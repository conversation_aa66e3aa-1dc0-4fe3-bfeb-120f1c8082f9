https://shell.segfault.net/#/dashboard

adm-FT2DnR9Sl7psikcXVODLyRYM

cat >>~/.ssh/config <<'__EOF__'
host degreewink
    User root
    HostName adm.segfault.net
    IdentityFile ~/.ssh/id_sf-adm-segfault-net
    SetEnv SECRET=FT2DnR9Sl7psikcXVODLyRYM
    LocalForward 5900 0:5900
__EOF__
chmod 600 ~/.ssh/config ~/.ssh/id_sf-adm-segfault-net
######################################################################
Thereafter use these commands:
--> ssh  degreewink
--> sftp degreewink
--> scp  degreewink:stuff.tar.gz ~/
--> sshfs -o reconnect degreewink:/sec ~/sec
----------------------------------------------------------------------
Token             : No See https://thc.org/segfault/token
Your workstation  : *************   (Mount Royal/Canada)
Reverse Port      : Type curl sf/port for reverse port.
Exit CryptoStorm  : **************  (Finland)
Exit Mullvad      : *************   (Helsinki/Finland)
Exit NordVPN      : **************  (Zurich/Switzerland)
TOR Proxy         : ************:9050
Shared storage    : /everyone/DegreeWink       (encrypted)
Your storage      : /sec                       (encrypted)
Your Onion WWW    : /onion                     (encrypted)
Your Web Page     : http://2xyr7jug4b5uhndzelsf7vgrxygttutc6h5mqzpwp7y6blk6owhxliqd.onion/degreewink/
SSH               : ssh -o "SetEnv SECRET=FT2DnR9Sl7psikcXVODLyRYM" \
                       <EMAIL>
SSH (TOR)         : torsocks ssh -o "SetEnv SECRET=FT2DnR9Sl7psikcXVODLyRYM" \
                       <EMAIL>
SSH (gsocket)     : gsocket -s NGExNzFhNMYm ssh -o "SetEnv SECRET=FT2DnR9Sl7psikcXVODLyRYM" \
                       <EMAIL>
SECRET            : FT2DnR9Sl7psikcXVODLyRYM <<<  WRITE THIS DOWN  <<<


https://salesfloor.atlassian.net/browse/CPD-1204


PHP8 migration:

https://salesfloor.atlassian.net/browse/CPD-1228
https://salesfloor.atlassian.net/browse/CPD-1227
https://salesfloor.atlassian.net/browse/CPD-1234

+14389262038

filter[search]=
&filter[user_id]=13
&filter[state]=
&filter[city]=
&per_page=11
&page=
&sf_locale=en_US
&id=13


./robo export:contacts shoppers-dev --full
./robo export:contacts shoppers-stg --full

https://docs.google.com/document/d/13H_Hr_EDl4zrY8pf7gblqrRC5ttqu80TBx2d8PFADbo/edit?usp=sharing



        $response = $I->doDirectGet($this->app, "customers/" . $id);
        $results = json_decode($response->getContent());

        $response = $I->doDirectGet($this->app, "customers/" . $id);
        $result = json_decode($response->getContent());

        $response = $I->doDirectPost($this->app, "customers", $params);
        $results = json_decode($response->getContent());

        $response = $I->doDirectPut($this->app, "customers/" . $results->ID, $params);
        $results = json_decode($response->getContent());

        $response = $I->doDirectDelete($this->app, "customers/" . $results->ID, $params);
        $results = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertEquals(404, $response->getStatusCode());
        $I->assertEquals(403, $response->getStatusCode());
        $I->assertEquals(204, $response->getStatusCode());
        $I->assertEquals(400, $response->getStatusCode());
        $I->assertEquals(401, $response->getStatusCode());

find . | grep 'Cest.php$' | xargs sed -E -i 's/^(class[ ]+[a-zA-Z]+Cest)$/\1 extends BaseApi/'
for i in $()

SF-30512-fix-sms-blacklist-namespace

ShopFeedCest


cd /Users/<USER>/workspace/platform/codeception/tests/api
for i in $(find .); do
    sed -E 's/^(class[ ]+[a-zA-Z]+Cest)$/\1 extends BaseApi/' $i
done

https://salesfloor.atlassian.net/browse/SF-30790

[26-Jan-2022 17:19:24 UTC] [2022-01-26 17:19:24] SF.widgets.INFO: Matched route "GET_virtual_appointment_virtualId". {"route_parameters":{"_controller":"virtual_appointment.controller:render","virtualId":"-MuH8
zjxA_nLQmYvQfgm","_route":"GET_virtual_appointment_virtualId"},"request_uri":"http://perrysport-widgets-qa06.salesfloor.net/virtual-appointment/-MuH8zjxA_nLQmYvQfgm?sf_locale=nl_NL&sf_locale=nl_NL&virtualId=-Mu
H8zjxA_nLQmYvQfgm"} []
[26-Jan-2022 17:19:24 UTC] [2022-01-26 17:19:24] SF.widgets.INFO: > GET /virtual-appointment/-MuH8zjxA_nLQmYvQfgm?virtualId=-MuH8zjxA_nLQmYvQfgm&sf_locale=nl_NL&sf_locale=nl_NL [] []
[26-Jan-2022 17:19:24 UTC] [2022-01-26 17:19:24] SF.widgets.CRITICAL: Twig_Error_Loader:
Template "retailers/perrysport/services/virtual_appointment/virtual-appointment.html" is not defined. (uncaught exception
) at /srv/www/sf_platform/current/vendor/twig/twig/lib/Twig/Loader/Chain.php line 129 {"exception":"[object] (Twig_Error_Loader(code: 0): Template \"retailers/perrysport/services/virtual_appointment/virtual-app
ointment.html\" is not defined. at /srv/www/sf_platform/current/vendor/twig/twig/lib/Twig/Loader/Chain.php:129)"} []
[26-Jan-2022 17:19:24 UTC] Template "retailers/perrysport/services/virtual_appointment/virtual-appointment.html" is not defined.
[26-Jan-2022 17:19:24 UTC] [2022-01-26 17:19:24] SF.widgets.INFO: < 500 [] []
[26-Jan-2022 17:19:25 UTC] [2022-01-26 17:19:25] SF.widgets.INFO: Matched route "GET_404". {"route_parameters":{"_controller":"page404.controller:render","_route":"GET_404"},"request_uri":"http://perrysport-wid
gets-qa06.salesfloor.net/404?sf_locale="} []
[26-Jan-2022 17:19:25 UTC] [2022-01-26 17:19:25] SF.widgets.INFO: > GET /404?sf_locale= [] []
[26-Jan-2022 17:19:25 UTC] [2022-01-26 17:19:25] SF.widgets.ERROR: Lang error. Non registered locale's display name for targetLangOrLocale=nl_NL inLangOrLocale=nl_NL [] []
[26-Jan-2022 17:19:25 UTC] [2022-01-26 17:19:25] SF.widgets.INFO: < 200 [] []
[26-Jan-2022 17:19:26 UTC] [2022-01-26 17:19:26] SF.widgets.INFO: Matched route "GET_cookies_render". {"route_parameters":{"_controller":"cookies.controller:renderCookies","_route":"GET_cookies_render"},"reques:


When a client sent a virtual appointment request (by text message) and the phone number does not exist in table ‘sf_customer’, a new row is inserted into table ‘sf_pre_customer'. After rep joining and sending text message, no customer found in 'sf_customer’ and does not know who is talking with.

However, if a client sent a virtual appointment request (by email) and the email does not exist in table ‘sf_customer’, a new row is inserted into this table ‘sf_customer'. After rep joining,  rep knows who is talking.


api/app/crons/ExportRequest.php


I'm a newbie for stock market, I just want to talk about symbol 'JWEL' (JOWELL GLOBAL LTD).

Please look at the chart of this symbol at 03 Jan, 2021, the price decreased from $24 to $8, reduced 2/3 in one day, and today it's $4.5.

It's normal if nothing happened behind it, however it looks like the company hired lot of persons who introduced this symbol to anyone and said the price will be increased by 5 or 6 times and the best time you buy is at the morning of 03th Jan, 2021, finally company got high benefit by selling the stocks and you lost money. those persons generally got around 10% commission.

The person I contacted through wechat did exactly what I mentioned above and I lost almost $8000, this guy disappeared next day, I know it's my bad to trust somebody and get cheated, but if the company did not support/encourage/pay for this behavior, it will never happen.

I hope you can do something to prevent it from happening again, especially for newcomer like me, it's really ugly.



"chat_request": 2092,
"chat_answer_rate_answers": 1482,


 "chat_request": 653,
  "chat_answer_rate_answers": 458,

  458 / 653 = 0.7013782542113323


'139', '114', '139'
'134', '99', '134'
'120', '97', '120'
'77', '55', '77'
'91', '51', '91'
'65', '42', '65'
'0', '0', '0'
'0', '0', '0'
'0', '0', '0'


0 + 0 + 91 + 0 + 139 + 134 + 77 + 65 + 120 = 626

0 + 0 + 51 + 0 + 114 + 99 + 55 + 42 + 97 = 458

SELECT m.id,m.user_id,m.phone_number,m.created_by_user_id,m.created_at,m.updated_at FROM sf_user_phone_number m WHERE m.`user_id` = '282' LIMIT 1



echo "xdebug.mode=debug\nxdebug.start_with_request=yes\nxdebug.client_host=host.docker.internal\nxdebug.client_port=9011\nxdebug.idekey=docker\n">> aa.txt

app/crons/InstallMaxmindDb.php

    SELECT u.ID,
       SUM(TIMESTAMPDIFF(SECOND,
            CASE WHEN ua.start_date < DATE_FORMAT('2021-07-04 00:00:00' ,'%Y-%m-01')
                THEN DATE_FORMAT('2021-07-04 00:00:00' ,'%Y-%m-01')
            ELSE ua.start_date END,
            CASE WHEN ua.end_date IS NOT NULL AND ua.end_date > DATE_FORMAT(DATE_ADD('2021-07-04 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')
                THEN DATE_FORMAT(DATE_ADD('2021-07-04 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')
            ELSE COALESCE(ua.end_date, DATE_FORMAT(DATE_ADD('2021-07-04 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')) END)
       ) AS time_active,
FROM wp_users u
LEFT JOIN sf_user_activity ua ON u.ID = ua.user_id
AND ua.type = 'active session'
AND ua.start_date < COALESCE(ua.end_date, DATE_FORMAT(DATE_ADD('2021-07-04 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01'))
AND ua.start_date < DATE_FORMAT(DATE_ADD('2021-07-04 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')
AND COALESCE(ua.end_date, DATE_FORMAT(DATE_ADD('2021-07-04 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')) >= DATE_FORMAT('2021-07-04 00:00:00' ,'%Y-%m-01')
WHERE u.user_login NOT LIKE 'salesfloor\_%'
GROUP BY u.ID
having time_active > 86400

    SELECT u.ID,
       SUM(TIMESTAMPDIFF(SECOND,
            CASE WHEN ua.start_date < DATE_FORMAT('2021-07-01 00:00:00' ,'%Y-%m-01')
                THEN DATE_FORMAT('2021-07-01 00:00:00' ,'%Y-%m-01')
            ELSE ua.start_date END,
            CASE WHEN ua.end_date IS NOT NULL AND ua.end_date > DATE_FORMAT(DATE_ADD('2021-07-01 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')
                THEN DATE_FORMAT(DATE_ADD('2021-07-01 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')
            ELSE COALESCE(ua.end_date, DATE_FORMAT(DATE_ADD('2021-07-01 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')) END)
       ) AS time_active,
FROM wp_users u
LEFT JOIN sf_user_activity ua ON u.ID = ua.user_id
AND ua.type = 'active session'
AND ua.start_date < COALESCE(ua.end_date, DATE_FORMAT(DATE_ADD('2021-07-01 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01'))
AND ua.start_date < DATE_FORMAT(DATE_ADD('2021-07-01 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')
AND COALESCE(ua.end_date, DATE_FORMAT(DATE_ADD('2021-07-01 00:00:00', INTERVAL 1 MONTH) ,'%Y-%m-01')) >= DATE_FORMAT('2021-07-04 00:00:00' ,'%Y-%m-01')
WHERE u.user_login NOT LIKE 'salesfloor\_%'
GROUP BY u.ID
having time_active > 86400



{
    "data":
    {
        "type": "associate_relationship",
        "attributes":
        {
            "primary":
            {
                "rep_id": "115",
                "first_name": "Adams",
                "last_name": "Martin",
                "store_name": "Bogus Plaza",
                "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_3.jpg,g_face,h_250,w_250/v0123456789/dev/tests/user_login_115"
            },
            "secondaries":
            [
                {
                    "rep_id": "13",
                    "first_name": "Eric",
                    "last_name": "Wagstaff",
                    "store_name": "Fake Mall",
                    "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_3.jpg,g_face,h_250,w_250/v0123456789/dev/tests/user_login_13"
                },
                {
                    "rep_id": "139",
                    "first_name": "Davis",
                    "last_name": "Taylor",
                    "store_name": "Fake Mall",
                    "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_3.jpg,g_face,h_250,w_250/v0123456789/dev/tests/user_login_139"
                }
            ]
        }
    }
}

{
  "data": {
    "type": "associate_relationship",
    "attributes": {
      "primary": {
        "store_name": "Bogus Plaza",
        "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_2.jpg,g_face,h_250,w_250/v1631632411/chicosdefaultrep.jpg"
      },
      "secondaries": [
        {
          "store_name": "Fake Mall",
          "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_5.jpg,g_face,h_250,w_250/v1631632411/chicosdefaultrep.jpg"
        },
        {
          "store_name": "Fake Mall",
          "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_5.jpg,g_face,h_250,w_250/v1631632411/chicosdefaultrep.jpg"
        }
      ]
    }
  }
}

SELECT d.user_id, d.endpoint_id, d.token FROM sf_devices d INNER JOIN wp_users u ON d.user_id = u.ID WHERE (u.type = 'rep' AND u.user_status AND u.selling_mode) AND (d.user_id IN (115))

https://holt.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID6138d4f5415511.83786543&meetingid=898&action=accept&customer=true&rep=lol56eff3d6ed8b9&version=2&sf_locale=en_US

"Click to accept:
https://holt.dev.salesfloor.net/s/VkidxBCe9qjzXNt6
Click to re-schedule:
https://holt.dev.salesfloor.net/s/P8XnwDo5GxBAp_yT
Click to cancel:
https://holt.dev.salesfloor.net/s/XhtarHRyZKlWHlkd"


./robo test:rest ServiceForms/AppointmentCest.php:testNewAppointmentWithWithEmailOn
./robo test:rest ServiceForms/AppointmentCest.php:testNewAppointmentWithWithTextOn

./robo test:rest ServiceForms/FinderCest.php:testRequestWithEmailOn
./robo test:rest ServiceForms/FinderCest.php:testRequestWithWithTextOn

./robo test:rest ServiceForms/QuestionCest.php:testRequestWithEmailOn
./robo test:rest ServiceForms/QuestionCest.php:testRequestWithWithTextOn

./robo test:functional Services/Salesfloor/CustomerService/CustomerServiceCest
./robo test:rest RetailerCustomer/RetailerCustomerAssociateRelationshipsCest.php
./robo test:rest RetailerCustomer/RetailerCustomerAssociateRelationshipsCest.php:testRetailerCustomerAssociateRelationshipsRepMode
./robo test:rest RetailerCustomer/RetailerCustomerAssociateRelationshipsCest.php:testRetailerCustomerAssociateRelationshipsTeamMode


./robo test:functional Exporter/Request/RequestExporterCest.php:testDailyExportYesterdayRequestsWithReplies

./robo casl:delete:customer holt-dev

php ./api/app/crons/ReadTextMessagingSendingQueue.php
php ./api/app/crons/CalculateDailyStats.php chicos-dev
php ./api/app/crons/CreateTransactionExports.php hbc-dev
php ./api/app/crons/SyncChatLogs.php hbc-dev

./robo test:functional Services/Salesfloor/CustomerService/CustomerServiceCest.php

 +
 +// For testing purposes, to be removed SF-29547
 +$configs['retailer.services.appointment.types'] = [
 +    [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
 +    [ 'type' => 'virtual', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
 +    [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
 +];
 +$configs['retailer.services.channel.text.enabled'] = true;
 +$configs['retailer.services.appointment_management.is_enabled'] = true;
 +$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
 +$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
 +$configs['retailer.services.appointment.all.is_enabled'] = true;
 +$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
 +
 +$configs['retailer.chat.option.virtual-appointment'] = true;

phani's branch conflicts
crontab: macys is not on master
test issues
do not contact 0 => 2



        {
            "name": "Listen for Xdebug",
            "type": "php",
            "request": "launch",
            "port": 9011,
            "pathMappings": {
                "/srv/www/sf_platform/current": "${workspaceRoot}/platform"
            },
            "xdebugSettings": {
                "max_data": 10240
            }
        },



virutal appointment:

mobile:  SF-29527-SF-29550-SF-29704-virtual-appointment


INSERT INTO `wordpress_saks`.`sf_events` (`type`, `date`, `source`, `uniq_id`, `user_id`, `customer_id`, `attributes`, `satisfied`, `event_id`, `acknowledged`, `store_id`)
VALUES ('111',
        '2021-01-01 00:00:00',
        '',
        'uniq_id',
        '1',
        '2',
        '',
        '0',
        '0',
        '0',
        '1003');


INSERT INTO `wordpress_saks`.`sf_events` (`type`, `date`, `source`, `uniq_id`, `user_id`, `customer_id`, `attributes`, `satisfied`, `event_id`, `acknowledged`, `store_id`)
VALUES ('112',
        '2021-01-01 01:00:00',
        '',
        'uniq_id1',
        '1',
        '2',
        '',
        '0',
        '0',
        '0',
        '1003');

SELECT m.id,
       m.panel_id,
       m.section_id,
       m.customer_id,
       m.label,
       m.is_label_bold,
       m.position,
       m.created_at,
       m.updated_at
FROM sf_retailer_customer_stats_panels m
WHERE (m.`customer_id` = '8499')
  AND (m.`section_id` IS NULL)


CREATE TABLE `sf_email_block_list` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL COMMENT 'Unsubscribed email address',
  `created_on_sendgrid` int(11) unsigned NOT NULL COMMENT 'Unix timestamp that email was added to sendgrid unsubscribes',
  `first_inserted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'First time row was added',
  `source` enum('Unsubscribed','Excluded') NOT NULL DEFAULT 'Unsubscribed' COMMENT 'type of reason why email in the list',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uq_email` (`email`),
  KEY `source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Local copy of Sendgrid unsubscribes';


www-data@c145e29e7bbf-local--dev: current$ ./robo test:api
 [Exec] Running ./tests.sh -l api in codeception
Codeception PHP Testing Framework v2.5.6
Powered by PHPUnit 7.5.20 by Sebastian Bergmann and contributors.
Running with seed:

dump.sql is out of date, regenerating...
Åmysqldump: Got error: 2002: Can't connect to local MySQL server through socket '/var/run/mysqld/mysqld.sock' (13) when trying to connect

In DatabaseDumpAutoUpdater.php line 192:

  Failed to export updated dump.sql via mysqldump


run [-o|--override OVERRIDE] [-e|--ext EXT] [--report] [--html [HTML]] [--xml [XML]] [--phpunit-xml [PHPUNIT-XML]] [--tap [TAP]] [--json [JSON]] [--colors] [--no-colors] [--silent] [--steps] [-d|--debug] [--coverage [COVERAGE]] [--coverage-html [COVERAGE-HTML]] [--coverage-xml [COVERAGE-XML]] [--coverage-text [COVERAGE-TEXT]] [--coverage-crap4j [COVERAGE-CRAP4J]] [--coverage-phpunit [COVERAGE-PHPUNIT]] [--no-exit] [-g|--group GROUP] [-s|--skip SKIP] [-x|--skip-group SKIP-GROUP] [--env ENV] [-f|--fail-fast] [--no-rebuild] [--seed SEED] [--] [<suite>] [<test>]

 [Exec]  Exit code 1  Time 04:30

mysqldump --defaults-extra-file= 'wordpress_tests' --skip-add-locks --skip-comments > /srv/www/sf_platform/current/codeception/tests/_support/../_data/dump.sql

mysqldump --defaults-extra-file=./my.cnf 'wordpress_tests' --skip-add-locks --skip-comments > dump.sql


UPDATE `tmp_new_20210303214833132431_sf_email_block_list` temp  JOIN `sf_email_block_list` original ON original.email = temp.email  SET temp.first_inserted_at = original.first_inserted_at


can_t_connect_to_local_my_sql_server_through_socket_var_run_mysqld_mysqld_sock
Can’t connect to local MySQL server through socket ‘/var/run/mysqld/mysqld.sock’

"https://api.sendgrid.com/v3/"




https://salesfloor.atlassian.net/browse/SF-29457?jql=labels%20%3D%20easy
https://salesfloor.atlassian.net/browse/SF-29448 => DB table bad data ?
https://salesfloor.atlassian.net/browse/SF-29408 => Improve fallback on instagram
https://salesfloor.atlassian.net/browse/SF-29308 => fix warning (not sure if it still apply) on shoppage
https://salesfloor.atlassian.net/browse/SF-29261 => psr12 + fix hook

sf-29448-imports-from-sendgrid-issues-with-first-issued-at-on-sf-email-block-list-table

SF-29448 Imports from Sendgrid: issues with 'first_issued_at' on sf_email_block_list table

LOAD DATA LOCAL INFILE

UPDATE `tmp_new_20210305193020c8e045_sf_email_block_list` temp  JOIN `sf_email_block_list` original ON original.email = temp.email  SET temp.first_inserted_at = original.first_inserted_at



docker-compose -f docker-compose.yml -f docker-compose-mac.yml -f docker-compose-mysql.yml config
docker-compose -f docker-compose.yml -f docker-compose-mac.yml -f docker-compose-mysql.yml start


https://gist.github.com/niw/e4313b9c14e968764a52375da41b4278
+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
git clone https://git.qemu.org/git/qemu.git
cd qemu
git checkout d0dddab40e -b wip/hvf
curl 'https://patchwork.kernel.org/series/418581/mbox/'|git am --3way
mkdir build
cd build
../configure --target-list=aarch64-softmmu --enable-cocoa
make -j 8


Download https://gist.github.com/niw/4f1f9bb572f40d406866f23b3127919b/raw/f546faea68f4149c06cca88fa67ace07a3758268/QEMU_EFI-cb438b9-edk2-stable202011-with-extra-resolutions.tar.gz
tar xzvf QEMU_EFI-*.tar.gz

dd if=/dev/zero of=pflash0.img bs=1m count=64
dd if=/dev/zero of=pflash1.img bs=1m count=64
dd if=QEMU_EFI.fd of=pflash0.img conv=notrunc
dd if=QEMU_VARS.fd of=pflash1.img conv=notrunc

./qemu-img create -f qcow2 /Users/<USER>/servers/vm/disks/ubuntu.qcow2 200G

Download ubuntu for ARM64:
https://ubuntu.com/download/server/arm

./qemu-system-aarch64 \
  -monitor stdio \
  -M virt,highmem=off \
  -accel hvf \
  -cpu cortex-a72 \
  -smp 6 \
  -m 8192 \
  -bios /Users/<USER>/servers/vm/QEMU_EFI.fd \
  -device virtio-gpu-pci \
  -display default,show-cursor=on \
  -device qemu-xhci \
  -device usb-kbd \
  -device usb-tablet \
  -net nic,model=virtio \
  -net user \
  -drive file=/Users/<USER>/servers/vm/disks/ubuntu.qcow2,if=virtio,cache=writethrough \
  -cdrom /Users/<USER>/servers/vm/disks/ubuntu-20.04.2-live-server-arm64.iso


  -netdev user,id=net0,net=********/8,host=******** \
  -device virtio-net,netdev=net0 \


  -net nic,model=virtio \
  -net user \

docker run --name=test-mysql -p 52000:3306  --env="MYSQL_ROOT_PASSWORD=root" mysql:5.6

siege -t 12s -c 22 https://saks.dev.salesfloor.net/administrator?sf_=&sf_storeid=0884&sf_associd=90000001&site_refer=salesfloor&sf_source_origin=storefront
siege -t 12s -c 50 https://saks.dev.salesfloor.net/administrator?sf_=&sf_storeid=0884&sf_associd=90000001&site_refer=salesfloor&sf_source_origin=storefront

https://saks.dev.salesfloor.net/shop?rep=salesfloor_test_rep0&sf_url=https://facebook.com&track_click=1

const SERVER_CLICK_PREFIX = 'SERVER_CLICK_';

        'SERVER_CLICK_TOP_PICKS',       // SF_EVENT_CLICK_TOP_PICKS Clicking of a Top Pick product
        'SERVER_CLICK_LATEST_ARRIVALS', // SF_EVENT_CLICK_LATEST_ARRIVALS Clicking of a Latest Arrivals / Deals product
        'SERVER_CLICK_RECOMMENDED',     // SF_EVENT_CLICK_RECOMMENDED Clicking of a Recommended Product
        'SERVER_CLICK_CHAT',  // SF_EVENT_CLICK_CHAT Click on a product in the chat (*Only customer, not the rep)
        'SERVER_CLICK_SHARE',  // SF_EVENT_CLICK_SHARE Click on a product in the email from a share
        'SERVER_CLICK_MESSAGE',  // SF_EVENT_CLICK_MESSAGE Click on a product in a private message (*Only customer, not the rep)
        'SERVER_CLICK_FACEBOOK',  // SF_EVENT_CLICK_FACEBOOK Click on a product on facebook from a share
        'SERVER_CLICK_TWITTER',  // SF_EVENT_CLICK_TWITTER Click on a product on twitter from a share
        'SERVER_CLICK_LINKEDIN',  // SF_EVENT_CLICK_LINKEDIN NOT USED at the moment
        'SERVER_CLICK_LOOKBOOK',  // SF_EVENT_CLICK_LOOKBOOK Click on a product in a lookbook page
        'SERVER_CLICK_CUSTOMER_REQUEST',  // SF_EVENT_CLICK_CUSTOMER_REQUEST At the moment, we use the message code for customer service message

sf-29308-fix-warning-on-shoppage
SF-29308 Fix warning on shoppage

http://peru.dev.salesfloor.net/reggie
"https://www.instagram.com/p/CMNIXT-BA3t/"

sf-29408-instagram-change-caching-logic-so-it-s-never-empty

SF-29408 Instagram : change caching logic so it's never empty


phpcs: Request workspace/configuration failed with message: Unable to locate phpcs. Please add phpcs to your global path or use composer dependency manager to install it in your project locally.



CREATE TABLE `sf_customer` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `localization` enum('en','fr') COLLATE utf8mb4_unicode_ci NOT NULL,
  `geo` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` float DEFAULT NULL,
  `longitude` float DEFAULT NULL,
  `comment` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subcribtion_flag` tinyint(1) NOT NULL,
  `sms_marketing_subscription_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Flag representing if the customer has subscribed to sms marketing',
  `first_name` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` varchar(5000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` enum('personal','corporate') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'personal',
  `retailer_customer_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `label_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `label_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `origin` enum('unknown','admin-crm-import','customer-importer-script','rep-address-book','rep-manual-creation','mobile-rep-device-import','customer-updater-script','mobile-rep-manual-creation','storefront-chat','storefront-appointment','storefront-personal-shopper','storefront-email','widget-chat','widget-appointment','widget-personal-shopper','widget-email','transaction','storefront-updates','customers2contacts','ci-transaction','rep-appointment','cancellation-follow-up-task') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'automated sub type',
  `locale` char(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unassigned_employee_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Populated with employee id if the user does not exist on crm import',
  `retailer_parent_customer_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Same as retailer_customer_id column; customer_id from sf_retailer_customers but 1-n relationship. Does not imply ownership, use retailer_customer_id',
  `entity_last_modified` datetime DEFAULT NULL COMMENT 'Last modified time for customer domain entity(include: customerEvents, customerAddress etc.  )',
  `entity_last_export` datetime DEFAULT NULL COMMENT 'Last export time for customer',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `email` (`user_id`,`email`),
  UNIQUE KEY `retailer_customer_id` (`retailer_customer_id`),
  UNIQUE KEY `user_id_2` (`user_id`,`retailer_parent_customer_id`),
  UNIQUE KEY `idx_uq_unassigned_employee_email` (`unassigned_employee_id`,`email`),
  KEY `email_2` (`email`),
  KEY `user_id` (`user_id`),
  KEY `origin` (`origin`),
  KEY `unassigned_employee_id` (`unassigned_employee_id`),
  KEY `retailer_parent_customer_id` (`retailer_parent_customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sf_retailer_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` enum('male','female','not known') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `first_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `zipcode` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postalcode` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `is_subscribed` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `employee_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'In rep mode, the employee id that owns this customer',
  `retailer_store_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'In team mode, retailer store id that owns this customer',
  `external_last_updated` datetime DEFAULT NULL COMMENT 'the timestamp when the record was last modified in the external system (UTC)',
  `locale` char(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `limited_visibility` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Flag to signal whether or not the contact information should be restricted',
  `is_subscribed_sms_marketing` tinyint(1) DEFAULT NULL COMMENT 'Flag representing if the customer has subscribed to sms marketing',
  PRIMARY KEY (`id`),
  UNIQUE KEY `customer_id` (`customer_id`),
  KEY `email` (`email`),
  KEY `first_name` (`first_name`),
  KEY `last_name` (`last_name`),
  KEY `phone` (`phone`),
  KEY `external_last_updated` (`external_last_updated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


sf_retailer_customers
sf_retailer_customer_stats_insights
sf_retailer_store

Attribute              CustomerUnsubscribe   RetailerCustomer
Base.php               Epic                  RetailerCustomerStatsInsights
CommonInterface.php    Exceptions            RetailerStore
Components             GenericImporter.php   RetailerTransaction
CsvMerger.php          Notifier.php          RetailerTransactionCancelsAndReturns
CsvSplitter.php        Product               S3Importer.php
Customer               RepOnboarding.php     Store.php
CustomerInsights       RepTransaction        Task
CustomerTags           Reps                  TextMessage

./robo test:api importer/retailerCustomerTransaction/ImportRetailerCustomerTransactionCest.php:testCoreUpdateEmailBlockList -- --debug

./robo test:api importer/retailerCustomerTransaction/ImportRetailerCustomerTransactionCest.php:testImportRetailerCustomerTransactionWithMoreTransactionTypes -- --debug

./robo test:api importer/retailerCustomerTransaction/ImportRetailerCustomerTransactionCest.php -- --debug
./robo test:functional importer/RepTransaction/RepTransactionImporterCest.php -- --debug

sf-29457-importer-date-field-should-handle-iso8601-properly-timezone
SF-29457 Importer "date" field should handle ISO8601 properly (timezone)


DELETE trx, trxd FROM tmp_new_36_sf_rep_transaction tempTrx
INNER JOIN sf_rep_transaction trx ON tempTrx.trx_id = trx.trx_id
INNER JOIN sf_rep_transaction_detail trxd ON tempTrx.trx_id = trxd.trx_id
WHERE tempTrx.trx_type = 'delete'


platform/codeception/tests/functional/Importer/RepTransaction/RepTransactionImporterCest.php

private/etc/my.cnf                                                               0.000001   mysqld
17:54:14  stat64            private/var/empty/.my.cnf

Command 'Rest Client: Generate Code Snippet' resulted in an error (Running the contributed command: 'rest-client.generate-codesnippet' failed.)

DROP TABLE `wordpress_saks`.`tmp_new_20210305192447f7a53a_sf_email_block_list`, `wordpress_saks`.`tmp_new_20210305192724c25404_sf_email_block_list`, `wordpress_saks`.`tmp_new_22_sf_rep_transaction`, `wordpress_saks`.`tmp_new_22_sf_rep_transaction_detail`, `wordpress_saks`.`tmp_new_23_sf_rep_transaction`, `wordpress_saks`.`tmp_new_23_sf_rep_transaction_detail`, `wordpress_saks`.`tmp_new_24_sf_rep_transaction`, `wordpress_saks`.`tmp_new_24_sf_rep_transaction_detail`, `wordpress_saks`.`tmp_new_25_sf_rep_transaction`;

++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
done https://salesfloor.atlassian.net/browse/SF-29411

 => You already worked on importer (should be easy to look at it) . I asked the reporter to add an example of bad data. It will be easier to do a fix + test.

joseph  2:14 PM
maybe : https://salesfloor.atlassian.net/browse/SF-29227
2:15
maybe : https://salesfloor.atlassian.net/browse/SF-29351
2:16
Those are not "easy" one ; but good to learn also

joseph  2:28 PM
Did you work with shopify in the past @Lei Yang ? (edited)

joseph  2:34 PM
Maybe this : https://salesfloor.atlassian.net/browse/SF-28921 (related to date and importer too)

shopify:
https://salesfloor.atlassian.net/browse/SF-26822

sf-29411-cosbar-product-column-validation-for-arrival-date-fails-with-what-appears-good-value

SF-29411 Cosbar: Product column validation for 'arrival-date' fails with what appears good value

"https://750fbc317ace4aec005c0449f67f96b7:<EMAIL>/admin/api/2020-10/products.json?limit=250"

sf-26822-shopify-collects-issue
SF-26822 Shopify Collects Issue

http://saks.widgets.dev.salesfloor.net/sf-dequeue/reps/salesfloor_test_rep0/events/
transaction-item?map[0]
[trx_id]=TRX1616094504507&map[0]
[product_id]=SKU16160945045071&map[0]
[trx_detail_total]=13&map[0]
[quantity]=4&cachebuster=1616094510&customer_id=y8angkjq1&fingerprint=96567836544217 HTTP/1.1

trx_id
product_id
quantity
sku
trx_detail_total

'trx_id'='TRX1616094504507',
'trx_detail_total'='13',
'product_id'='SKU16160945045071',
'quantity'='4',
'units'='each',
'trx_thread_id'='TRX1616094504507',

sf-29227-some-transactions-have-the-same-item-in-them-multiple-times-in-our-db-and-we-re-not-sure-why

SF-29227 Some transactions have the same item in them multiple times in our DB and we're not sure why

127.0.0.1 arnotts.api.dev.salesfloor.net
127.0.0.1 arnotts.dev.salesfloor.net
127.0.0.1 arnotts.widgets.dev.salesfloor.net
127.0.0.1 bash.api.dev.salesfloor.net
127.0.0.1 bash.dev.salesfloor.net
127.0.0.1 bash.widgets.dev.salesfloor.net
127.0.0.1 bbbaby.api.dev.salesfloor.net
127.0.0.1 bbbaby.dev.salesfloor.net
127.0.0.1 bbbaby.widgets.dev.salesfloor.net
127.0.0.1 benbridge.api.dev.salesfloor.net
127.0.0.1 benbridge.dev.salesfloor.net
127.0.0.1 benbridge.widgets.dev.salesfloor.net
127.0.0.1 bloom.api.dev.salesfloor.net
127.0.0.1 bloom.dev.salesfloor.net
127.0.0.1 bloom.widgets.dev.salesfloor.net
127.0.0.1 brownt.api.dev.salesfloor.net
127.0.0.1 brownt.dev.salesfloor.net
127.0.0.1 brownt.widgets.dev.salesfloor.net
127.0.0.1 buckle.api.dev.salesfloor.net
127.0.0.1 buckle.dev.salesfloor.net
127.0.0.1 buckle.widgets.dev.salesfloor.net
127.0.0.1 chicoca.api.dev.salesfloor.net
127.0.0.1 chicoca.dev.salesfloor.net
127.0.0.1 chicoca.widgets.dev.salesfloor.net
127.0.0.1 chicos.api.dev.salesfloor.net
127.0.0.1 chicos.dev.salesfloor.net
127.0.0.1 chicos.widgets.dev.salesfloor.net
127.0.0.1 cosbar.api.dev.salesfloor.net
127.0.0.1 cosbar.dev.salesfloor.net
127.0.0.1 cosbar.widgets.dev.salesfloor.net
127.0.0.1 cotr.api.dev.salesfloor.net
127.0.0.1 cotr.dev.salesfloor.net
127.0.0.1 cotr.widgets.dev.salesfloor.net
127.0.0.1 elguntors.api.dev.salesfloor.net
127.0.0.1 elguntors.dev.salesfloor.net
127.0.0.1 elguntors.widgets.dev.salesfloor.net
127.0.0.1 hbc.api.dev.salesfloor.net
127.0.0.1 hbc.dev.salesfloor.net
127.0.0.1 hbc.widgets.dev.salesfloor.net
127.0.0.1 holt.api.dev.salesfloor.net
127.0.0.1 holt.dev.salesfloor.net
127.0.0.1 holt.widgets.dev.salesfloor.net
127.0.0.1 jwas.api.dev.salesfloor.net
127.0.0.1 jwas.dev.salesfloor.net
127.0.0.1 jwas.widgets.dev.salesfloor.net
127.0.0.1 lilly.api.dev.salesfloor.net
127.0.0.1 lilly.dev.salesfloor.net
127.0.0.1 lilly.widgets.dev.salesfloor.net
127.0.0.1 pandora.api.dev.salesfloor.net
127.0.0.1 pandora.dev.salesfloor.net
127.0.0.1 pandora.widgets.dev.salesfloor.net
127.0.0.1 peru.api.dev.salesfloor.net
127.0.0.1 peru.dev.salesfloor.net
127.0.0.1 peru.widgets.dev.salesfloor.net
127.0.0.1 rtb.api.dev.salesfloor.net
127.0.0.1 rtb.dev.salesfloor.net
127.0.0.1 rtb.widgets.dev.salesfloor.net


platform/instance-webserver/src/salesfloor/wp-content/plugins/sf-contact-management/sf-contact-management.php
$result = Salesfloor_Contact_Management::get_customers( $args );

CREATE TABLE `sf_customer` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `name` varchar(60) DEFAULT NULL,
  `phone` varchar(30) DEFAULT NULL,
  `localization` enum('en','fr') NOT NULL,
  `geo` varchar(100) NOT NULL,
  `latitude` float DEFAULT NULL,
  `longitude` float DEFAULT NULL,
  `comment` varchar(500) NOT NULL,
  `subcribtion_flag` tinyint(1) NOT NULL,
  `sms_marketing_subscription_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Flag representing if the customer has subscribed to sms marketing',
  `first_name` varchar(60) DEFAULT NULL,
  `last_name` varchar(60) DEFAULT NULL,
  `note` varchar(5000) DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` enum('personal','corporate') NOT NULL DEFAULT 'personal',
  `retailer_customer_id` varchar(45) DEFAULT NULL,
  `label_email` varchar(255) DEFAULT NULL,
  `label_phone` varchar(255) DEFAULT NULL,
  `origin` enum('unknown','admin-crm-import','customer-importer-script','rep-address-book','rep-manual-creation','mobile-rep-device-import','customer-updater-script','mobile-rep-manual-creation','storefront-chat','storefront-appointment','storefront-personal-shopper','storefront-email','widget-chat','widget-appointment','widget-personal-shopper','widget-email','transaction','storefront-updates','customers2contacts','ci-transaction','rep-appointment','cancellation-follow-up-task') DEFAULT NULL COMMENT 'automated sub type',
  `locale` char(5) DEFAULT NULL,
  `unassigned_employee_id` varchar(32) DEFAULT NULL COMMENT 'Populated with employee id if the user does not exist on crm import',
  `retailer_parent_customer_id` varchar(45) DEFAULT NULL COMMENT 'Same as retailer_customer_id column; customer_id from sf_retailer_customers but 1-n relationship. Does not imply ownership, use retailer_customer_id',
  `entity_last_modified` datetime DEFAULT NULL COMMENT 'Last modified time for customer domain entity(include: customerEvents, customerAddress etc.  )',
  `entity_last_export` datetime DEFAULT NULL COMMENT 'Last export time for customer',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `email` (`user_id`,`email`),
  UNIQUE KEY `retailer_customer_id` (`retailer_customer_id`),
  UNIQUE KEY `user_id_2` (`user_id`,`retailer_parent_customer_id`),
  UNIQUE KEY `idx_uq_unassigned_employee_email` (`unassigned_employee_id`,`email`),
  KEY `email_2` (`email`),
  KEY `user_id` (`user_id`),
  KEY `origin` (`origin`),
  KEY `unassigned_employee_id` (`unassigned_employee_id`),
  KEY `retailer_parent_customer_id` (`retailer_parent_customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


127.0.0.1 arnotts.dev.salesfloor.net arnotts.widgets.dev.salesfloor.net arnotts.api.dev.salesfloor.net
127.0.0.1 bash.dev.salesfloor.net bash.widgets.dev.salesfloor.net bash.api.dev.salesfloor.net
127.0.0.1 bbbaby.dev.salesfloor.net bbbaby.widgets.dev.salesfloor.net bbbaby.api.dev.salesfloor.net
127.0.0.1 benbridge.dev.salesfloor.net benbridge.widgets.dev.salesfloor.net benbridge.api.dev.salesfloor.net
127.0.0.1 bloom.dev.salesfloor.net bloom.widgets.dev.salesfloor.net bloom.api.dev.salesfloor.net
127.0.0.1 brownt.dev.salesfloor.net brownt.widgets.dev.salesfloor.net brownt.api.dev.salesfloor.net
127.0.0.1 buckle.dev.salesfloor.net buckle.widgets.dev.salesfloor.net buckle.api.dev.salesfloor.net
127.0.0.1 chicoca.dev.salesfloor.net chicoca.widgets.dev.salesfloor.net chicoca.api.dev.salesfloor.net
127.0.0.1 chicos.dev.salesfloor.net chicos.widgets.dev.salesfloor.net chicos.api.dev.salesfloor.net
127.0.0.1 cosbar.dev.salesfloor.net cosbar.widgets.dev.salesfloor.net cosbar.api.dev.salesfloor.net
127.0.0.1 cotr.dev.salesfloor.net cotr.widgets.dev.salesfloor.net cotr.api.dev.salesfloor.net
127.0.0.1 elguntors.dev.salesfloor.net elguntors.widgets.dev.salesfloor.net elguntors.api.dev.salesfloor.net
127.0.0.1 hbc.dev.salesfloor.net hbc.widgets.dev.salesfloor.net hbc.api.dev.salesfloor.net
127.0.0.1 holt.dev.salesfloor.net holt.widgets.dev.salesfloor.net holt.api.dev.salesfloor.net
127.0.0.1 jwas.dev.salesfloor.net jwas.widgets.dev.salesfloor.net jwas.api.dev.salesfloor.net
127.0.0.1 lilly.dev.salesfloor.net lilly.widgets.dev.salesfloor.net lilly.api.dev.salesfloor.net
127.0.0.1 pandora.dev.salesfloor.net pandora.widgets.dev.salesfloor.net pandora.api.dev.salesfloor.net
127.0.0.1 peru.dev.salesfloor.net peru.widgets.dev.salesfloor.net peru.api.dev.salesfloor.net
127.0.0.1 rtb.dev.salesfloor.net rtb.widgets.dev.salesfloor.net rtb.api.dev.salesfloor.net
127.0.0.1 saks.dev.salesfloor.net saks.widgets.dev.salesfloor.net saks.api.dev.salesfloor.net
127.0.0.1 shoppers.dev.salesfloor.net shoppers.widgets.dev.salesfloor.net shoppers.api.dev.salesfloor.net
127.0.0.1 sjk.dev.salesfloor.net sjk.widgets.dev.salesfloor.net sjk.api.dev.salesfloor.net
127.0.0.1 soma.dev.salesfloor.net soma.widgets.dev.salesfloor.net soma.api.dev.salesfloor.net
127.0.0.1 sonoma.dev.salesfloor.net sonoma.widgets.dev.salesfloor.net sonoma.api.dev.salesfloor.net
127.0.0.1 want.dev.salesfloor.net want.widgets.dev.salesfloor.net want.api.dev.salesfloor.net
127.0.0.1 whbm.dev.salesfloor.net whbm.widgets.dev.salesfloor.net whbm.api.dev.salesfloor.net
127.0.0.1 whbmca.dev.salesfloor.net whbmca.widgets.dev.salesfloor.net whbmca.api.dev.salesfloor.net
127.0.0.1 wotr.dev.salesfloor.net wotr.widgets.dev.salesfloor.net wotr.api.dev.salesfloor.net

sf-29605-scrubbed-recipients-recipients-count-and-recipients-blockout-columns-of-sf-share-general-table-are-not-updating-as-expected

SF-29605 Scrubbed Recipients:: recipients_count and recipients_blockout columns of sf_share_general table are not updating as expected.
SF-29605 Scrubbed Recipients:: recipients_count and recipients_blockout columns of sf_share_general table are not updating as expected.


SELECT u.ID AS userId,
       u.employee_id AS userEmployeeId,

  (SELECT GROUP_CONCAT(um.meta_value SEPARATOR ' ')
   FROM wp_usermeta um
   WHERE um.user_id = c.user_id
     AND um.meta_key IN ('first_name',
                         'last_name')
   ORDER BY meta_key ASC) AS userName,

       u.user_email AS userEmail,
       u.store AS userStoreId,
       s.name AS userStoreName,
       u.user_status AS userStatus,
       c.retailer_customer_id AS contactCrmId,
       c.first_name AS contactFirstName,
       c.last_name AS contactLastName,
       c.email AS contactDefaultEmail,
       c.label_email AS contactDefaultEmailLabel,
       c.origin AS origin,

  (SELECT GROUP_CONCAT(CONCAT(CONCAT(cmemail.value, ';'), cmemail.label) SEPARATOR ',')
   FROM sf_customer_meta cmemail
   WHERE c.ID = cmemail.customer_id
     AND cmemail.type = 'email') AS contactAlternateEmails,

       c.phone AS contactDefaultPhone,
       c.label_phone AS contactDefaultPhoneLabel,

  (SELECT GROUP_CONCAT(CONCAT(CONCAT(cmphone.value, ';'), cmphone.label) SEPARATOR ',')
   FROM sf_customer_meta cmphone
   WHERE c.ID = cmphone.customer_id
     AND cmphone.type = 'phone') AS contactAlternatePhones,

       c.subcribtion_flag AS contactSalesfloorSubscription,
       c.sms_marketing_subscription_flag AS contactSalesfloorSubscriptionSms,
       c.created AS contactCreatedDate,
       c.last_modified AS contactModifiedDate,

  (SELECT csm.username
   FROM sf_customer_social_media csm,
                                 sf_social_media_network csmn
   WHERE c.ID = csm.customer_id
     AND csm.social_media_network_id=csmn.id
     AND csmn.name = 'facebook' ) AS facebook,

  (SELECT csm.username
   FROM sf_customer_social_media csm,
                                 sf_social_media_network csmn
   WHERE c.ID = csm.customer_id
     AND csm.social_media_network_id=csmn.id
     AND csmn.name = 'twitter' ) AS twitter,

  (SELECT csm.username
   FROM sf_customer_social_media csm,
                                 sf_social_media_network csmn
   WHERE c.ID = csm.customer_id
     AND csm.social_media_network_id=csmn.id
     AND csmn.name = 'instagram' ) AS instagram,

  (SELECT csm.username
   FROM sf_customer_social_media csm,
                                 sf_social_media_network csmn
   WHERE c.ID = csm.customer_id
     AND csm.social_media_network_id=csmn.id
     AND csmn.name = 'other' ) AS website,

       c.Id AS contactId,
       c.entity_last_modified,
       c.retailer_parent_customer_id,
       ss.retailer_store_id,

  ( SELECT GROUP_CONCAT(CONCAT_WS('#%^%#',scn.note, scn.created_at,IFNULL(scn.updated_at,'')) SEPARATOR '&@~@&')
   FROM sf_customer_notes scn
   WHERE c.ID = scn.customer_id ) AS concatContactNote,

  ( SELECT GROUP_CONCAT(sctag.retailer_tag_id SEPARATOR ';')
   FROM sf_customer_tags_relationships sctr
   INNER JOIN sf_customer_tags sctag ON sctr.tag_id = sctag.id
   WHERE c.ID = sctr.customer_id ) AS contactTags,

  ( SELECT GROUP_CONCAT(CONCAT_WS('#%^%#',sca.id,sca.address_line_1, IFNULL(sca.address_line_2,''),sca.postal_code,sca.state,sca.city,sca.country,sca.label,sca.is_default) SEPARATOR '&@~@&')
   FROM sf_customer_addresses sca
   WHERE c.ID = sca.customer_id ) AS concatContactAddress,

  ( SELECT CONCAT_WS('#%^%#', IFNULL(sce.year,''), IFNULL(sce.month,''), IFNULL(sce.day,''))
   FROM sf_customer_events sce
   WHERE c.ID = sce.customer_id
     AND sce.label = 'birthday' LIMIT 1 ) AS concatContactBirthday,

  ( SELECT CONCAT_WS('#%^%#', IFNULL(sce.year,''), IFNULL(sce.month,''), IFNULL(sce.day,''))
   FROM sf_customer_events sce
   WHERE c.ID = sce.customer_id
     AND sce.label = 'anniversary' LIMIT 1 ) AS concatContactAnniversary,

  ( SELECT CONCAT_WS('#%^%#', IFNULL(sce.year,''), IFNULL(sce.month,''), IFNULL(sce.day,''))
   FROM sf_customer_events sce
   WHERE c.ID = sce.customer_id
     AND sce.label = 'other' LIMIT 1 ) AS concatContactOtherEventDate,

       c.localization

FROM sf_customer c
INNER JOIN wp_users u ON c.user_id = u.ID
LEFT JOIN sf_store s ON s.store_id = u.store
LEFT JOIN sf_store ss ON u.store = ss.store_id
WHERE u.user_login NOT LIKE 'salesfloor\_%'
ORDER BY c.created ASC,
         c.id ASC

INSERT INTO `wordpress_chicos`.`sf_customer` (`user_id`, `email`, `name`, `phone`, `localization`, `geo`, `comment`, `subcribtion_flag`, `sms_marketing_subscription_flag`, `first_name`, `last_name`, `note`, `created`, `last_modified`, `type`, `retailer_customer_id`, `origin`, `locale`)
VALUES ('115', '<EMAIL>', 'JoJo Savard', '', 'en', '', '', '1', '0', 'JoJo', 'Savard', '', '2018-05-17 13:23:15', '2021-03-25 14:53:59', 'personal', '1', 'rep-manual-creation', 'en_US');





"User ID",
"User Employee ID",
"User Name",
"User Email",
"User Store ID",
"User Store Name",
"User Status",
"Contact CRM ID",
"Contact First Name",
"Contact Last Name",
"Contact Default Email",
"Contact Default Email Label",
"Contact Alternate Emails",
"Contact Default Phone",
"Contact Default Phone Label",
"Contact Alternate Phones",
"Contact Salesfloor Subscription",
"Contact Created",
"Contact Modified",
"Contact Deleted",
"Related CRM ID",
"Contact Record ID",
"User Retailer Store ID",
"Contact Notes",
"Contact Tags",
"Contact Addresses",
"Contact Facebook",
"Contact Twitter",
"Contact Instagram",
"Contact Website",
"Contact Birthday",
"Contact Anniversary",
"Contact Other Date",origin,"Text Message Subscription",
"Preferred Communication Language"

sf-29503-export-the-unsubscribe-and-preferred-values
SF-29503 Export the Unsubscribe and Preferred values

 SF-29503 Export the Unsubscribe and Preferred values

git cherry-pick ec334856c4
git cherry-pick 0e40c8898f

CREATE TABLE `sf_retailer_customer_stats_datapoints` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'Internal identifier for datapoint',
  `datapoint_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Datapoint identifier provided by retailer',
  `panel_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Panel identifier provided by retailer',
  `customer_id` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Customer identitier',
  `label` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Label for the datapoint',
  `type` enum('currency','number','date','plain_text','graph','product_recommendations','product_cart','product_wishlist','product_browsing') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Type of datapoint',
  `value` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Value for the datapoint',
  `color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Color of the datapoint (mainly for graphs)',
  `position` smallint(5) unsigned NOT NULL COMMENT 'Order for display',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`,`datapoint_id`),
  KEY `customer_id_2` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Customer stats datapoints';


SELECT m.id,m.panel_id,m.section_id,m.customer_id,m.label,m.is_label_bold,m.position,m.created_at,m.updated_at
FROM sf_retailer_customer_stats_panels m
WHERE (m.`customer_id` = '8499') AND (m.`section_id` IS NULL)

SELECT m.id,m.datapoint_id,m.customer_id,m.panel_id,m.label,m.type,m.value,m.color,m.position,m.created_at,m.updated_at
FROM sf_retailer_customer_stats_datapoints m
WHERE (m.`customer_id` = '8499') AND (m.`panel_id` = '1')

sf-29745-chs-brands-holt-incorrect-last-transaction-dates-for-all-contacts
SF-29745 CHS Brands + Holt - Incorrect Last Transaction dates for all contacts

sf-29756-chs-brands-when-an-all-customers-contact-s-state-is-a-space-the-checkbox-in-the-filter-is-on-the-left
SF-29756 CHS Brands - When an All Customers contact's State is a space, the checkbox in the filter is on the left

git cherry-pick cfff8691b1 (origin/SF-29035-Sendgrid-mass-email-sending-without-recipien
git cherry-pick 649a6a7ec6 SF-29035 Updated comment for SendgridProvider::currentKeyReceivedCampaignIsMinuteOldOrLess()
git cherry-pick 9faea3f8b6 SF-29035 Add error log timeout of 60s for mass duplicate email message
git cherry-pick 69e3a2f46b SF-29035 Test for SendCampaign recipient validation & validation put earlier in code

git cherry-pick e8e4fad175 SF-29035 Prevent SendGridProvider from sending an email with no recipients

ee23e1e057 Merge remote-tracking branch 'origin/master' into rel-1.135.0

SF-29809 User feed containing blank values for first or last name will clear these values for existing users
sf-29809-user-feed-containing-blank-values-for-first-or-last-name-will-clear-these-values-for-existing-users


SF-29811 SSO - Active SSO user can't onboard if other (inactive) SF user exists with same email address
sf-29811-sso-active-sso-user-can-t-onboard-if-other-inactive-sf-user-exists-with-same-email-address

[
  [
    'customer-social-media',
    'customer-social-media.network',
    'customer-address',
    'customer-event',
    'customer-to-customer-attribute',
    'customer-to-customer-attribute.customer-attribute',
    'customer-to-customer-attribute.customer-attribute.customer-attribute-panel',
    'customer-meta',
  ],
  [
    'customer' => [
      'user_id',
      'email',
      'name',
      'phone',
      'localization',
      'geo',
      'latitude',
      'longitude',
      'comment',
      'subcribtion_flag',
      'sms_marketing_subscription_flag',
      'first_name',
      'last_name',
      'note',
      'created',
      'last_modified',
      'type',
      'retailer_customer_id',
      'label_email',
      'label_phone',
      'origin',
      'locale',
      'unassigned_employee_id',
      'retailer_parent_customer_id',
      'entity_last_modified',
      'entity_last_export',
      'is_sms_blacklisted',
      'virtual_fields',
      'customer-social-media',
      'customer-address',
      'customer-event',
      'customer-to-customer-attribute',
      'customer-meta',
    ],
    'customer-social-media' => [
      'customer_id',
      'social_media_network_id',
      'username',
      'network',
    ],
    'network' => [
      'name',
      'url',
    ],
    'customer-address' => [
      'customer_id',
      'address_line_1',
      'address_line_2',
      'postal_code',
      'state',
      'city',
      'country',
      'label',
      'is_default',
    ],
    'customer-event' => [
      'customer_id',
      'label',
      'day',
      'month',
      'year',
    ],
    'customer-to-customer-attribute' => [
      'id',
      'customer_id',
      'attribute_id',
      'attribute_value',
      'created_at',
      'updated_at',
      'customer-attribute',
    ],
    'customer-attribute' => [
      'id',
      'attribute_id',
      'description',
      'attribute_type',
      'label',
      'position',
      'default_value',
      'place_holder',
      'is_required',
      'is_editable',
      'is_visible',
      'is_searchable',
      'is_filterable',
      'validation_rule',
      'panel_id',
      'customer-attribute-panel',
    ],
    'customer-attribute-panel' => [
      'id',
      'panel_id',
      'label',
      'position',
    ],
    'customer-meta' => [
      'id',
      'customer_id',
      'type',
      'value',
      'label',
      'position',
      'creation_date',
      'modification_date',
    ],
  ],
  [
    'ID',
    'user_id',
    'email',
    'name',
    'phone',
    'localization',
    'geo',
    'latitude',
    'longitude',
    'comment',
    'subcribtion_flag',
    'sms_marketing_subscription_flag',
    'first_name',
    'last_name',
    'note',
    'created',
    'last_modified',
    'type',
    'retailer_customer_id',
    'label_email',
    'label_phone',
    'origin',
    'locale',
    'unassigned_employee_id',
    'retailer_parent_customer_id',
    'entity_last_modified',
    'entity_last_export',
  ],
  [
    'size',
    'number',
  ],
  [
    'ID',
    'user_id',
    'email',
    'name',
    'phone',
    'localization',
    'geo',
    'latitude',
    'longitude',
    'comment',
    'subcribtion_flag',
    'sms_marketing_subscription_flag',
    'first_name',
    'last_name',
    'note',
    'created',
    'last_modified',
    'type',
    'retailer_customer_id',
    'label_email',
    'label_phone',
    'origin',
    'locale',
    'unassigned_employee_id',
    'retailer_parent_customer_id',
    'entity_last_modified',
    'entity_last_export',
  ],
  true,
]

sf-29754-be-3-fix-current-tests
SF-29754 [BE: 3] Fix current tests


epic:

https://salesfloor.atlassian.net/browse/SF-29527
  https://salesfloor.atlassian.net/browse/SF-29547
    SF-29549-customer-access-virtual-appt
  SF-29700-virtual-appointment-emails
  SF-29702-virtual-waiting-room-fe


SF-29699 BE
sf-29699-be

SF-29548: [BE: 2| FE: 1] Email/Text Notifications for Appointments incl. Virtual - F4
sf-29548-sf-29699-be


sf-29527-test
sf-29548-sf-29699-be

sf-29876-chicos-transaction-history-via-tasks-shows-same-date-amount-as-first-contact-viewed
SF-29876 Chicos - Transaction history via tasks shows same date/amount as first contact viewed

INSERT INTO `sf_product_shared` (`user_id`, `product_id`, `product_sku`, `created_at`, `updated_at`, `share_id`)

git

SF-29642-whbm-lookbook-preview-shows-incorrect-store-name-in-mobile-app

Issue Type	Key	Summary	Status	Remaining Estimate BE	Remaining Estimate FE	Story Points	Sprint
Story	SF-29692	[BE: | FE: ] Email/Text Notification Cleanup	To Do	?	?	?	Prioritized Product Backlog
Story	SF-29675	Create tracking params for virtual appointment	To Do	?	?	?	Prioritized Product Backlog
Story	SF-29558	Enable Configurations for Retailer Rollout	To Do	?	?	?	Prioritized Product Backlog
Story	SF-29555	[BE:  | FE: ] Virtual Appointment KPIs - F7	To Do	?	?	?	Prioritized Product Backlog
Story	SF-29551	[BE: 0 | FE: 5] Customer and Rep Can Join and Participate in a Virtual Appointment	To Do	0	5	5	Prioritized Product Backlog
Story	SF-29550	[BE: 1 | FE: 4] Rep Can Connect to the Virtual Appointment from the Mobile App - F6	Dev In Progress	1	3.5	5	Prioritized Product Backlog
Story	SF-29549	[BE: 3 | FE: 5] Customer Can Access the Virtual Appointment Waiting Room - F1/5	Dev In Progress	?	0	8	Sprint TCC 143
Story	SF-29548	[BE: 2| FE: 1] Email/Text Notifications for Appointments incl. Virtual - F4	Dev In Progress	?	0	3	Sprint TCC 143
Story	SF-29547	[BE: 5 | FE: 0.5] Customer/Rep Can Request/Book an Appointment of type Virtual - F2/3	Dev In Progress	?	0	5.5	Sprint TCC 143


SF-29692 SF-29675 SF-29558

platform/codeception/tests/rest/Customer/CustomerJsonApiCest.php

        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];
        $this->app['configs']['retailer.i18n.default_language_fallback'] = [
            'en' => 'en_US',
            'fr' => 'fr_CA',
        ];

'retailer.i18n.default_language_fallback'
$configs['retailer.i18n.locale.fallback'] = 'en_US'

`video_chat_sessions`,
`video_chat_duration`


CREATE TABLE `sf_user_daily_stats` (
  `user_id` bigint(20) unsigned NOT NULL,
  `date` date NOT NULL,
  `timezone` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `appointment_req` int(11) unsigned NOT NULL DEFAULT '0',
  `onboarding_end` int(11) unsigned NOT NULL DEFAULT '0',
  `user_add` int(11) unsigned NOT NULL DEFAULT '0',
  `chat_request` int(11) unsigned NOT NULL DEFAULT '0',
  `chat_answer` int(11) unsigned NOT NULL DEFAULT '0',
  `response_mail_sent` int(11) unsigned NOT NULL DEFAULT '0',
  `mail_sent` int(11) unsigned NOT NULL DEFAULT '0',
  `email_stats_open` int(11) unsigned NOT NULL DEFAULT '0',
  `email_stats_click` int(11) unsigned NOT NULL DEFAULT '0',
  `avg_init_resp_sum_times` int(11) unsigned NOT NULL DEFAULT '0',
  `avg_init_resp_num_responses` int(11) unsigned NOT NULL DEFAULT '0',
  `chat_answer_rate_requests` int(11) unsigned NOT NULL DEFAULT '0',
  `chat_answer_rate_answers` int(11) unsigned NOT NULL DEFAULT '0',
  `total_order_value` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `n_sales_transactions` int(11) unsigned NOT NULL DEFAULT '0',
  `feedback` int(11) unsigned NOT NULL DEFAULT '0',
  `profile_update` int(11) unsigned NOT NULL DEFAULT '0',
  `content_create` int(11) unsigned NOT NULL DEFAULT '0',
  `content_update` int(11) unsigned NOT NULL DEFAULT '0',
  `content_curate` int(11) unsigned NOT NULL DEFAULT '0',
  `ask_question_req` int(11) unsigned NOT NULL DEFAULT '0',
  `personal_shopper_req` int(11) unsigned NOT NULL DEFAULT '0',
  `live_session_start` int(11) unsigned NOT NULL DEFAULT '0',
  `live_session_end` int(11) unsigned NOT NULL DEFAULT '0',
  `product_update` int(11) unsigned NOT NULL DEFAULT '0',
  `deal_update` int(11) unsigned NOT NULL DEFAULT '0',
  `unsubscribe` int(11) unsigned NOT NULL DEFAULT '0',
  `new_sale` int(11) unsigned NOT NULL DEFAULT '0',
  `subscribe` int(11) unsigned NOT NULL DEFAULT '0',
  `retail_event` int(11) unsigned NOT NULL DEFAULT '0',
  `raise_concern` int(11) unsigned NOT NULL DEFAULT '0',
  `shopping_page` int(11) unsigned NOT NULL DEFAULT '0',
  `unique_visitor` int(11) unsigned NOT NULL DEFAULT '0',
  `page_hit` int(11) unsigned NOT NULL DEFAULT '0',
  `sale_duplicate` int(11) unsigned NOT NULL DEFAULT '0',
  `user_visit` int(11) unsigned NOT NULL DEFAULT '0',
  `com_ref` int(11) unsigned NOT NULL DEFAULT '0',
  `soc_ref` int(11) unsigned NOT NULL DEFAULT '0',
  `retail_hit` int(11) unsigned NOT NULL DEFAULT '0',
  `social_post` int(11) unsigned NOT NULL DEFAULT '0',
  `soc_share` int(11) unsigned NOT NULL DEFAULT '0',
  `moderate_lead` int(11) unsigned NOT NULL DEFAULT '0',
  `customer_card` int(11) unsigned NOT NULL DEFAULT '0',
  `help_useful` int(11) unsigned NOT NULL DEFAULT '0',
  `livesession_register` int(11) unsigned NOT NULL DEFAULT '0',
  `change_categories` int(11) unsigned NOT NULL DEFAULT '0',
  `chatsession_register` int(11) unsigned NOT NULL DEFAULT '0',
  `event_create` int(11) unsigned NOT NULL DEFAULT '0',
  `event_update` int(11) unsigned NOT NULL DEFAULT '0',
  `event_delete` int(11) unsigned NOT NULL DEFAULT '0',
  `event_subscribe` int(11) unsigned NOT NULL DEFAULT '0',
  `sidebar_view` int(11) unsigned NOT NULL DEFAULT '0',
  `sidebar_click` int(11) unsigned NOT NULL DEFAULT '0',
  `footer_view` int(11) unsigned NOT NULL DEFAULT '0',
  `footer_click` int(11) unsigned NOT NULL DEFAULT '0',
  `storefront_click` int(11) unsigned NOT NULL DEFAULT '0',
  `transactional_mail_sent` int(11) unsigned NOT NULL DEFAULT '0',
  `courtesy_mail_sent` int(11) unsigned NOT NULL DEFAULT '0',
  `service_total` int(11) unsigned NOT NULL DEFAULT '0',
  `traffic_total` int(11) unsigned NOT NULL DEFAULT '0',
  `content_total` int(11) unsigned NOT NULL DEFAULT '0',
  `number_seconds_available` int(11) unsigned NOT NULL DEFAULT '0',
  `total_return_value` decimal(12,4) NOT NULL DEFAULT '0.0000',
  `recommendation_chat` int(11) unsigned NOT NULL DEFAULT '0',
  `recommendation_compose_message` int(11) unsigned NOT NULL DEFAULT '0',
  `recommendation_share_email` int(11) unsigned NOT NULL DEFAULT '0',
  `recommendation_share_facebook` int(11) unsigned NOT NULL DEFAULT '0',
  `recommendation_share_twitter` int(11) unsigned NOT NULL DEFAULT '0',
  `recommendation_new_arrivals` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Total products recommendation in the new arrivals panel',
  `recommendation_top_picks` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Total products recommendation in the top picks panel',
  `click_top_picks` int(11) unsigned NOT NULL DEFAULT '0',
  `click_latest_arrivals` int(11) unsigned NOT NULL DEFAULT '0',
  `click_recommended` int(11) unsigned NOT NULL DEFAULT '0',
  `avg_selected_top_picks` float unsigned NOT NULL DEFAULT '0' COMMENT 'Average selected items for top picks panel',
  `avg_selected_new_arrivals` float unsigned NOT NULL DEFAULT '0' COMMENT 'Average selected items for new arrivals panel',
  `salesfloor_visits` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Calculated across system by Visits service with EventQueue. Max 1 visit/user/calendar day',
  `text_messages_outbound_api` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of texts sent via outbound-api',
  `text_messages_outbound_call` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of texts sent via outbound-call',
  `text_messages_outbound_reply` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of texts sent via outbound-reply',
  `text_messages_inbound` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of texts received via inbound',
  `recommendation_text_message` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_automated_created` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED Tasks created',
  `tasks_automated_resolved` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED Tasks resolved',
  `tasks_automated_dismissed` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED Tasks dismissed',
  `tasks_manual_created` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of MANUAL Tasks created',
  `tasks_manual_resolved` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of MANUAL Tasks resolved',
  `tasks_manual_dismissed` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of MANUAL Tasks dismissed',
  `chat_abandoned` int(11) NOT NULL,
  `chat_abandonment_time` int(11) NOT NULL,
  `chat_answer_time` int(11) NOT NULL,
  `chat_early_redirect` int(11) NOT NULL,
  `chat_auto_redirect` int(11) NOT NULL,
  `ask_question_req_email` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of question request by email',
  `ask_question_req_text` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of question request by text/sms',
  `appointment_req_email` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of appointment request by email',
  `appointment_req_text` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of appointment request by text/sms',
  `personal_shopper_req_email` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of personal shopper request by email',
  `personal_shopper_req_text` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of personal shopper request by text/sms',
  `library_share_attempts` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of share attempt events from Browse Library',
  `lookbook_create` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of Lookbooks created by a user on a particular date',
  `lookbook_update` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of Lookbooks updated by a user on a particular date',
  `received_chats_answered_by_other` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of chats received by a store that were ultimately answered by a different store',
  `request_email_sent` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of request email sent',
  `request_email_open` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of request email open',
  `request_email_click` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of request email click',
  `compose_email_sent` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of compose email sent',
  `compose_email_open` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of compose email open',
  `compose_email_click` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of compose email click',
  `share_email_sent` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of share email sent',
  `share_email_open` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of share email open',
  `share_email_click` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of share email click',
  `total_share_sent` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of share campaigns sent',
  `chat_abandon_0_29` int(11) NOT NULL DEFAULT '0',
  `chat_abandon_30_59` int(11) NOT NULL DEFAULT '0',
  `chat_abandon_60_89` int(11) NOT NULL DEFAULT '0',
  `chat_abandon_90_120` int(11) NOT NULL DEFAULT '0',
  `ask_question_req_chat_handoff` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of question request from chat handoff',
  `tasks_system_created` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-SYSTEM Tasks created',
  `tasks_system_resolved` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-SYSTEM Tasks resolved',
  `tasks_system_dismissed` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-SYSTEM Tasks dismissed',
  `tasks_followup_created` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-FOLLOWUP Tasks created',
  `tasks_followup_resolved` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-FOLLOWUP Tasks resolved',
  `tasks_followup_dismissed` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-FOLLOWUP Tasks dismissed',
  `tasks_corporate_created` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-CORPORATE Tasks created',
  `tasks_corporate_resolved` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-CORPORATE Tasks resolved',
  `tasks_corporate_dismissed` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of AUTOMATED-CORPORATE Tasks dismissed',
  `tasks_manual_resolved_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_manual_dismissed_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_automated_resolved_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_automated_dismissed_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_system_resolved_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_system_dismissed_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_followup_resolved_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_followup_dismissed_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_corporate_resolved_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `tasks_corporate_dismissed_sum_time` int(11) unsigned NOT NULL DEFAULT '0',
  `socialshop_post_created` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of creates made to the SocialShop feed',
  `socialshop_total_visit` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Total visitors to the SocialShop feed',
  `socialshop_product_click` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Clicks on products from the SocialShop feed',
  `socialshop_storefront_click` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of clicks on the ''view storefront'' button',
  `socialshop_sales_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of sales attributed to the SocialShop feed',
  `socialshop_sales_amount_total` float unsigned NOT NULL DEFAULT '0' COMMENT 'Sales in dollars attributed to the SocialShop feed',
  `tasks_corporate_deleted` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of automated-corporate Tasks deleted(delete by parent corporate task)',
  `ask_question_req_cs_email` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of question requests by email forwarded to customer support',
  `ask_question_req_cs_text` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of question requests by text/sms forwarded to customer support',
  `appointment_req_cs_email` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of appointment requests by email forwarded to customer support',
  `appointment_req_cs_text` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of appointment requests by text/sms forwarded to customer support',
  `personal_shopper_req_cs_email` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of personal shopper requests by email forwarded to customer support',
  `personal_shopper_req_cs_text` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'Number of personal shopper requests by text/sms forwarded to customer support',
  `scheduled_appointments` int(11) unsigned NOT NULL DEFAULT '0',
  `cancelled_appointment` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'count of Appointments whose scheduled date is within the reporting period whose status is Cancelled',
  `video_chat_sessions` int(11) NOT NULL DEFAULT '0',
  `video_chat_duration` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

  `virtual_appointment_sessions` int(11) NOT NULL DEFAULT '0',
  `virtual_appointment_duration` int(11) NOT NULL DEFAULT '0',


127.0.0.1 pharmaprix.dev.salesfloor.net pharmaprix.widgets.dev.salesfloor.net pharmaprix.api.dev.salesfloor.net

SF-29908 Mobile App::Add Appointment::User are not able to book appointment for customers from "All customers" tab.
SF-29908-mobile-app-add-appointment-user-are-not-able-to-book-appointment-for-customers-from-all-customers-tab


rm ci/docker/.docker/dnsmasq/dnsmasq.d/bin
rm ci/docker/.docker/dnsmasq/dnsmasq.d/dev
rm ci/docker/.docker/dnsmasq/dnsmasq.d/etc
rm ci/docker/.docker/dnsmasq/dnsmasq.d/home
rm ci/docker/.docker/dnsmasq/dnsmasq.d/lib
rm ci/docker/.docker/dnsmasq/dnsmasq.d/media
rm ci/docker/.docker/dnsmasq/dnsmasq.d/mnt
rm ci/docker/.docker/dnsmasq/dnsmasq.d/opt
rm ci/docker/.docker/dnsmasq/dnsmasq.d/proc
rm ci/docker/.docker/dnsmasq/dnsmasq.d/root
rm ci/docker/.docker/dnsmasq/dnsmasq.d/run
rm ci/docker/.docker/dnsmasq/dnsmasq.d/sbin
rm ci/docker/.docker/dnsmasq/dnsmasq.d/srv
rm ci/docker/.docker/dnsmasq/dnsmasq.d/sys
rm ci/docker/.docker/dnsmasq/dnsmasq.d/tmp
rm ci/docker/.docker/dnsmasq/dnsmasq.d/usr
rm ci/docker/.docker/dnsmasq/dnsmasq.d/var


120. $I->assertTrue(false,"sf.task.automated.cancelled_transaction_follow_up.enabled is not in the allowed list.") at tests/functional/Configs/ParserCest.php:242
119. $I->assertTrue(true,"sf.task.automated.transactions_distribution_by_stores.distance_radius is not in the allowed list.") at tests/functional/Configs/ParserCest.php:242
118. $I->assertTrue(true,"sf.task.automated.transactions_distribution_by_stores.max_per_owner is not in the allowed list.") at tests/functional/Configs/ParserCest.php:242
117. $I->assertTrue(true,"sf.task.automated.transactions_distribution_by_stores.category is not in the allowed list.") at tests/functional/Configs/ParserCest.php:242
116. $I->assertTrue(true,"sf.task.automated.transactions_distribution_by_stores.notifications_enabled is not in the allowed list.") at tests/functional/Configs/ParserCest.php:242
115. $I->assertTrue(true,"sf.task.automated.transactions_distribution_by_stores.emails_enabled is not in the allowed list.") at tests/functional/Configs/ParserCest.php:242


'sf.task.automated.cancelled_transaction_follow_up.emails_enabled',
'sf.task.automated.cancelled_transaction_follow_up.notifications_enabled',
'retailer.chat.option.video-chat',
"retailer.chat.option.video-chat.2way's"

CustomerInsights/CustomersToContactsCest.php:testCustomerToContactOnlyCustomerIdProvidedAndWithDeletedStatus

rm ci/docker/.docker/dnsmasq/dnsmasq.d/bin
rm ci/docker/.docker/dnsmasq/dnsmasq.d/dev
rm ci/docker/.docker/dnsmasq/dnsmasq.d/etc
rm ci/docker/.docker/dnsmasq/dnsmasq.d/home
rm ci/docker/.docker/dnsmasq/dnsmasq.d/lib
rm ci/docker/.docker/dnsmasq/dnsmasq.d/media
rm ci/docker/.docker/dnsmasq/dnsmasq.d/mnt
rm ci/docker/.docker/dnsmasq/dnsmasq.d/opt
rm ci/docker/.docker/dnsmasq/dnsmasq.d/proc
rm ci/docker/.docker/dnsmasq/dnsmasq.d/root
rm ci/docker/.docker/dnsmasq/dnsmasq.d/run
rm ci/docker/.docker/dnsmasq/dnsmasq.d/sbin
rm ci/docker/.docker/dnsmasq/dnsmasq.d/srv
rm ci/docker/.docker/dnsmasq/dnsmasq.d/sys
rm ci/docker/.docker/dnsmasq/dnsmasq.d/tmp
rm ci/docker/.docker/dnsmasq/dnsmasq.d/usr
rm ci/docker/.docker/dnsmasq/dnsmasq.d/var
rm codeception/tests/_data/dump.sql.old


SF-29686 SF-29852 [BE: 0.5] Book an Appointment Quick Links and Storefront Form

sf-29686-sf-29852-be-0-5-book-an-appointment-quick-links-and-storefront-form


DEVOPS-5078 SF-29955 HOLT - Update API endpoint on STG

DEVOPS-5078-SF-29955-holt-update-api-endpoint-on-stg

SF-29904 SDM PSA- SSO Update to match on UPN
SF-29904-sdm-psa-sso-update-to-match-on-upn

SF-29987 HBC - Wordpress vulnerabilities
SF-29987-hbc-wordpress-vulnerabilities


@devopspeople Just want to confirm that the following cron job scheduled at 06:10 everyday (from documentation)

UpdateProducts.php
10 6 * * *
Updates the product catalog based on data obtained fromthe retailer. Wrirtes to the db and to Algolia. Also refreshes autoselected products, sometimes.


SF-29999 Saks - Sale price not showing
SF-29999-saks-sale-price-not-showing


ID
2195234,2196535,2196594,2196596,2196734,2196814,2197433,2197440,2197649,3200186,5179182,5185144,5186122,5187330,


Noticed exception 'Salesfloor\Exceptions\GlobalException' with message 'InvalidArgumentException: Identifier "s3.client" is not defined. in /srv/www/sf_platform/current/vendor/pimple/pimple/lib/Pimple.php:78Stack trace:#0 /srv/www/sf_platform/current/providers/src/Image/ImageServiceProvider.php(29): Pimple->offsetGet('s3.client')#1 /srv/www/sf_platform/current/vendor/pimple/pimple/lib/Pimple.php(83): Salesfloor\Providers\Image\ImageServiceProvider->Salesfloor\Providers\Image\{closure}(Object(Silex\Application))#2 /srv/www/sf_platform/current/api/app/src/be/Controllers/Images/v1/Images.php(32): Pimple->offsetGet('service.image')#3 [internal function]: Salesfloor\API\Controllers\Images\v1\Images->upload(Object(Silex\Application))#4 /srv/www/sf_platform/current/vendor/symfony/http-kernel/HttpKernel.php(144): call_user_func_array(Array, Array)#5 /srv/www/sf_platform/current/vendor/symfony/http-kernel/HttpKernel.php(64): Symfony\Component\HttpKernel\HttpKernel->handleRaw(Object(Symfony\Component\HttpFoundation\Request), 1)#6 /srv/www/sf_platform/current/vendor/silex/silex/src/Silex/Application.php(586): Symfony\Component\HttpKernel\HttpKernel->handle(Object(Symfony\Component\HttpFoundation\Request), 1, true)#7 /srv/www/sf_platform/current/vendor/silex/silex/src/Silex/Application.php(563): Silex\Application->handle(Object(Symfony\Component\HttpFoundation\Request))#8 /srv/www/sf_platform/current/api/app/web/index.php(12): Silex\Application->run()#9 {main}' in /srv/www/sf_platform/current/api/app/src/be/errors.php:74

{
    "user":
    {
        "America/Montreal":
        {
            "users":
            [
                "115",
                ".....................",
                "1915"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/Montreal",
            "grouping": true,
            "stores":
            [
                "13"
            ]
        },
        "America/Edmonton":
        {
            "users":
            [
                "140",
                ".................",
                "1913"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/Edmonton",
            "grouping": true,
            "stores":
            [
                "1001"
            ]
        },
        "America/New_York":
        {
            "users":
            [
                "474",
                "...........................",
                "1820"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/New_York",
            "grouping": true,
            "stores":
            [
                "1002",
                "1003"
            ]
        }
    },
    "store":
    {
        "13":
        {
            "users":
            [
                "115",
                ".........",
                "1915"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/Montreal",
            "grouping": false
        },
        "1001":
        {
            "users":
            [
                "140",
                "........................................",
                "1913"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/Edmonton",
            "grouping": false
        },
        "1002":
        {
            "users":
            [
                "474",
                ".................",
                "1893"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/New_York",
            "grouping": false
        },
        "1003":
        {
            "users":
            [
                "325",
                "................",
                "1820"
            ],
            "date": "2021-06-24",
            "startDate": "2021-06-24 00:00:00",
            "endDate": "2021-06-24 23:59:59",
            "timezone": "America/New_York",
            "grouping": false
        }
    }
}


[
"getUniqueVisitorsCount",
"getOutstandingRequests",
"getScheduledAppointments",
"getCancelledAppointments",
"getSidebarStatus",
"getEmailStatsExploded",
"getAvgInitResp",
"getChatAnswerRate",
"getChatOutcomeStats",
"getChatsAnsweredByAnother",
"getProductRecommendationsByType",
"getAutoSelectedStats",
"getTextMessagingStats",
"getServicePerChannelStats",
"getCustomerSupportRequestPerChannelStats",
"getServiceFromChatHandoffStats",
"getTaskStatsByCreateTime",
"getTaskStatsByResolutionTime",
"getTaskStatsResolutionMetric",
"getTaskStatsByParentCorporateTask",
"getVideoChatStats",
"getTrxStats",
"getReturnStats",
"getSocialShopTrackingMetric"
]

sf_questions.user_id
sf_appointments.user_id
sf_personal_shopper.user_id
sf_stats_panel.user_id,
sf_event_log.action_id
sf_chat_abandoned.end_time

desc sf_questions;
desc sf_appointments;
desc sf_personal_shopper;
desc sf_stats_panel;
desc sf_event_log;
desc sf_chat_abandoned;

SF-29875 Chicos - KPIs incorrectly showing 0
SF-29875-chicos-kp-is-incorrectly-showing-0


'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'

rm -rf Emails-Backoffice-Storefront/src/css/7_themes/publisher/retailers/dxl/
rm -rf Emails-Backoffice-Storefront/src/templates/publisher/base/retailers/dxl/
rm -rf api/app/locale/dxl/
rm -rf api/app/src/templates/emails/publisher/dxl/
rm -rf api/app/src/templates/emails/retailers/dxl/
rm -rf configs/features/common/dxl/
rm -rf instance-webserver/src/salesfloor/css/sass/responsive/1_settings/retailers/dxl/
rm -rf instance-webserver/src/salesfloor/css/sass/responsive/8_themes/dxl/
rm -rf instance-webserver/src/salesfloor/img/retailers/dxl/
rm -rf instance-webserver/src/salesfloor/r/includes/dxl/
rm -rf instance-webserver/src/salesfloor/templates/mail/publisher/dxl/
rm -rf instance-webserver/src/salesfloor/templates/retailers/dxl/
rm -rf instance-webserver/src/salesfloor/twig/dxl/
rm -rf instance-webserver/src/salesfloor/wp-admin/includes/retailers/dxl/
rm -rf services/src/Importer/Components/Ingestor/File/Retailer/Dxl/
rm -rf services/src/Storefront/Menu/Dxl/
rm -rf widgets/app/locales/dxl/
rm -rf widgets/app/src/css/5_themes/dxl/
rm -rf widgets/app/src/fe/widget/configs/dxl/
rm -rf widgets/app/src/templates/retailers/dxl/
rm -rf widgets/app/src/templates/tests/retailers/dxl/
rm -rf widgets/app/web/img/retailers/dxl/


DEVOPS-5078-SF-29955-holt-update-api-endpoint-on-stg-1
DEVOPS-5078 SF-29955 HOLT - Update API endpoint on STG


SF-30052-SF-30057-be-3-update-mobile-to-use-image-uploader-aws
SF-30052-SF-30057-be-3-update-mobile-to-use-image-uploader-gcp

SF-30052 SF-30057 [BE:3] Update Mobile to Use Image Uploader


SF_29941_contact_customer_search_by_postal_code

SF-29941 Contact/Customer Search by Postal Code

SF-30066_buckle_ba_sh_j_was_text_message_is_sent_multiple_times_to_some_clients
SF-30066 Buckle & BA&SH & JWas - text message is sent multiple times to some clients

codeception/tests/rest/Images/UploaderCest.php

SF-29875 [TB: 2] Chicos - KPIs incorrectly showing 0
SF-29875_tb_2_chicos_kp_is_incorrectly_showing_0


ID: 1039
user_id: 115
name: shawn_test_lookbook 1
phone: +15145610765
sms_marketing_subscription_flag: 0
created: 2019-09-10 21:06:26

SF-27360-enhanced-cleanup-process-for-implied-email-text-consent-contacts
SF-27360 Enhanced Cleanup Process for Implied Email/Text Consent Contacts

arnottsq
brownt
buckle
default
holt
pharmaprix
shoppers

https://salesfloor.atlassian.net/browse/SF-29819
++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
platform/configs/Services/Configs.php@239
platform/configs/configs/common/calculated.php@81


SF-29498-SF-30185-do-not-contact-an-unsubscribed-contact-consent-status-doesn-t-change-even-if-customer-submits-any-customer-request-without-consent-selected-for-text-message


SF-29498 SF-30185 Do Not Contact: An Unsubscribed contact consent status doesn't change even if customer submits any customer request WITHOUT consent selected for text message


SF-29968 HBC - rep live chatted with client, SF has no record of sale, or chat

SF-29968-hbc-rep-live-chatted-with-client-sf-has-no-record-of-sale-or-chat






SF-30212-the-bucket-sf-dev-shoppers-public-and-sf-shoppers-public-should-be-uniform-access-aws
SF-30212-the-bucket-sf-dev-shoppers-public-and-sf-shoppers-public-should-be-uniform-access-gcp

SF-30212 The bucket "sf-dev-shoppers-public" and "sf-shoppers-public" should be uniform access


SF-29875 [TB: 2] Chicos - KPIs incorrectly showing 0

SF-29875-tb-2-chicos-kp-is-incorrectly-showing-0-2


Code Description
AJ Accounts Receivable Customer Account
AT Appropriation Number
BM Bill of Lading Number
9V Collect on Delivery (COD) Number
ON Dealer Order Number
DP Department Number
3Q Food and Drug Administration (FDA) Product Code
IK Invoice Number
MK Manifest Key Number
MJ Model Number
PM Part Number
PC Production Code
PO Purchase Order Number
RQ Purchase Request Number
RZ Return Authorization Number
SA Salesperson Number
SE Serial Number
ST Store Number
TN Transaction Reference Number





arnotts
bash
bbbaby
benbridge
bloom
brownt
buckle
chicoca
chicos
cosbar
cotr
dxl
elguntors
hbc
holt
humantouch
jwas
lilly
macys
pandora
peru
pharmaprix
rtb
saks
shiseido
shoppers
sjk
soma
tests
want
whbm
whbmca
wotr


rm -rf 1.txt
rm -rf 2.txt
rm -rf crons/
rm -rf file:/
rm -rf new.txt
rm -rf old.txt
rm -rf output.txt
rm -rf response.json

rm -rf file:/
rm -rf new.txt
rm -rf old.txt
rm -rf test_crondiff.php


SF-27360-enhanced-cleanup-process-for-implied-email-text-consent-contacts-2


SF-27360 Enhanced Cleanup Process for Implied Email/Text Consent Contacts


'backoffice',
'ci-transaction',
'corporate_email_unsub',
'corporate_sms_unsub',
'customers2contacts',
'email_block_list',
'get_my_updates',
'importer',
'mobile_reassign_customer',
'mobile',
'rep-appointment',
'sf_appointments',
'sf_personal_shopper',
'sf_questions',
'sms_opt_in',
'sms_opt_out',
'sms_opt',
'unknown',

SF-27360 Enhanced Cleanup Process for Implied Email/Text Consent Contacts


SF-29527-SF-30267-virtual-appointments-rep-s-profile-picture-is-not-shown-in-the-waiting-room

SF-29527 SF-30267 Virtual Appointments: Rep's profile picture is not shown in the Waiting room.

ID: 12
user_id:
email: <EMAIL>
name: Breana O'Kon
phone: +19526370740
localization: en
geo:
latitude:
longitude:
comment:
subcribtion_flag: 1
sms_marketing_subscription_flag: 0
first_name: Breana
last_name: O'Kon
note:
created: 2021-09-01 16:05:45
last_modified: 2021-09-01 16:05:46
type: corporate
retailer_customer_id:
label_email: home
label_phone: home
origin: customers2contacts
locale: en_US
unassigned_employee_id: 88017632
retailer_parent_customer_id:
entity_last_modified: 2021-09-01 16:05:46
entity_last_export:
contact_preference:


******************** 1. row *********************
                 ID: 1
        customer_id: 12
            user_id: 1
         event_type: In-Store
     event_duration: 60
               date: 2021-09-09 13:00:00
             status:
           location:
              notes: test
            uniq_id: SFID613799ae79c3c5.59820644
           timezone: America/New_York
           creation: 2021-09-07 16:56:14
  cust_meeting_link:
   rep_meeting_link:
            enddate: 2021-09-09 14:00:00
        rep_comment:
              phone: +15144321888
           store_id: 1003
             loc_id: 1
        category_id:
    sub_category_id:
            flagged:
      unattached_id:
         source_url: https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9
       source_title: Shop+with+Sally+Sellers+-+Chicos
             locale: en_US
            channel: email
status_notification:
             source: storefront
      reassigned_by:
    reassigned_date:
            version: 0
         updated_at: 2021-09-07 16:56:14

               ID: 1
        customer_id: 12
            user_id: 1
         event_type: In-Store
     event_duration: 60
               date: 2021-09-09 13:00:00
             status:
           location:
              notes: test
            uniq_id: SFID61379a38193e57.32858160
           timezone: America/New_York
           creation: 2021-09-07 16:58:32
  cust_meeting_link:
   rep_meeting_link:
            enddate: 2021-09-09 14:00:00
        rep_comment:
              phone: +15144321888
           store_id: 1003
             loc_id: 1
        category_id:
    sub_category_id:
            flagged:
      unattached_id:
         source_url: https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9
       source_title: Shop+with+Sally+Sellers+-+Chicos
             locale: en_US
            channel: text
status_notification:
             source: storefront
      reassigned_by:
    reassigned_date:
            version: 0
         updated_at: 2021-09-07 16:58:32


  kubectl -n qa06 exec pandora-qa06-sf-app-55bdf45464-dx88q -c web --stdin --tty -- /bin/bash
  kubectl -n qa06 exec pandora-qa06-sf-app-55bdf45464-dzht5 -c web --stdin --tty -- /bin/bash


platform/codeception/tests/rest/RetailerCustomer/RetailerCustomerAssociateRelationshipsCest.php
./robo test:rest RetailerCustomer/RetailerCustomerAssociateRelationshipsCest

SF-30306 SF-30309 [BE:3]Display Customer-Rep/Store Relationships on Profile

SF-30306-SF-30309-be-3-display-customer-rep-store-relationships-on-profile

SF-30332 All - A customer is able to submit a Contact Me request with a blacklisted SMS number

SF-30332-all-a-customer-is-able-to-submit-a-contact-me-request-with-a-blacklisted-sms-number

SF-30306-be-3-fe-4-display-customer-rep-store-relationships-on-profile

Sf 30306 be 3 fe 4 display customer rep store relationships on profile for GCP
SF-30306-be-3-fe-4-display-customer-rep-store-relationships-on-profile-gcp

SELECT DISTINCT m.product_id AS product_sku,
                pcm.storefront_slot,
                count(*) AS total
FROM sf_products m
INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id
AND pcm.category_id != 1
INNER JOIN sf_siteurl su ON su.product_id = m.product_id
INNER JOIN sf_images i ON i.product_id = m.product_id
LEFT JOIN sf_product p ON p.product_sku = m.product_id
AND p.user_id = 361
LEFT JOIN sf_deal d ON d.product_sku = m.product_id
AND d.user_id = 361
INNER JOIN sf_categories c ON pcm.category_id = c.category_id
INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id
INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id
AND rpsm.user_id = 361
WHERE (p.ID IS NULL)
  AND (d.ID IS NULL)
  AND (m.product_id <> '')
  AND (m.available = '1')
  AND (m.product_id NOT IN ('0400008024066',
                            '0400010154267',
                            '0400010208260',
                            '0400010163333',
                            '0400010089025',
                            '0400010012679',
                            '0400008035108',
                            '0400010238023',
                            '0400010247299',
                            '0400010045272',
                            '0400010112776',
                            '0400010042680',
                            '0400010206420',
                            '0400010174288'))
  AND (pcm.storefront_slot = 0) LIMIT 16


SF-30362-the-query-takes-100-db-cpu-for-more-than-1-hour
SF-30362 The query takes 100% DB CPU for more than 1 hour


SF-30306 SF-30377 [BE: 0.25] Add Retailer Store ID to the response

SF-30306-SF-30377-be-0-25-add-retailer-store-id-to-the-response


https://omiseplus.shiseido.co.jp/ja_JP/store-102013

SF-30360-shiseido-android-users-are-not-receiving-push-notifications-when-app-is-in-background
SF-30360-shiseido-android-users-are-not-receiving-push-notifications-when-app-is-in-background-2
SF-30360 Shiseido - Android users are not receiving push notifications when app is in background

https://chicos-qa04.salesfloor.net/js/moment.js
https://chicos.dev.salesfloor.net/js/moment.js

https://chicos-qa04.salesfloor.net/js/moment.js

https://chicos-stg.salesfloor.net/js/404

https://hbc-stg.salesfloor.net/js/moment.js


SF-30387-sendgrid-import-sendgrid-unsubscribes-php-script-is-not-importing-blocked-email-list-into-sf-db
SF-30387 Sendgrid::ImportSendgridUnsubscribes.php script is not importing blocked email list into SF DB.


SF-30384-all-active-users-weekly-summary-report-is-not-filtering-out-disabled-users
SF-30384 All – Active users WeeklySummaryReport is not filtering out disabled users

Amazon S3/eu-prod-brownt-eu-west-1-935077261142/outbound/dev/....

docker-compose logs -f web | while read line
do
   echo $line
   if echo $line | grep 'CONTAINER IS READY'
   then
      exit 0
   fi
done

mysql> show full processlist;
+-------+-----------+---------------------------+-----------+---------+------+--------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Id    | User      | Host                      | db        | Command | Time | State        | Info                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
+-------+-----------+---------------------------+-----------+---------+------+--------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
|    11 | root      | localhost                 | NULL      | Sleep   |   12 |              | NULL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|   162 | root      | localhost                 | NULL      | Sleep   |   24 |              | NULL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 15026 | wordpress | cloudsqlproxy~10.96.4.100 | wordpress | Sleep   |  315 |              | NULL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 18019 | wordpress | cloudsqlproxy~10.96.4.100 | wordpress | Sleep   | 4935 |              | NULL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 21893 | wordpress | cloudsqlproxy~10.96.4.100 | wordpress | Sleep   | 3293 |              | NULL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22655 | wordpress | cloudsqlproxy~*********   | wordpress | Query   |   50 | update       | INSERT INTO sf_deal (`user_id`, `from_user_id`, `position`, `product_sku`, `description`, `date`, `autoselected`) VALUES('336', '0', '3', '10100396', 'I recommend this product', '2021-10-11 17:54:16', '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| 22732 | wordpress | cloudsqlproxy~*********** | wordpress | Query   |    8 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
| 22733 | wordpress | cloudsqlproxy~10.96.1.32  | wordpress | Query   |    8 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
| 22734 | wordpress | cloudsqlproxy~**********  | wordpress | Query   |    3 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22735 | wordpress | cloudsqlproxy~10.96.12.14 | wordpress | Query   |    7 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
| 22736 | wordpress | cloudsqlproxy~10.96.12.14 | wordpress | Query   |   54 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot, count(*) as total FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (pcm.storefront_slot = 0) LIMIT 4                                                                                                                                                                                                                       |
| 22741 | wordpress | cloudsqlproxy~10.96.1.32  | wordpress | Query   |    0 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '3')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22742 | wordpress | cloudsqlproxy~10.96.7.141 | wordpress | Query   |    6 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22743 | wordpress | cloudsqlproxy~**********  | wordpress | Query   |    7 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
| 22977 | wordpress | cloudsqlproxy~*********** | wordpress | Query   |    5 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot, count(*) as total FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (pcm.storefront_slot = 0) LIMIT 4                                                                                                                                                                                                                       |
| 22978 | wordpress | cloudsqlproxy~*********** | wordpress | Query   |    9 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
| 22990 | wordpress | cloudsqlproxy~*********   | wordpress | Query   |    5 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 23666 | wordpress | cloudsqlproxy~*********   | wordpress | Query   |   31 | Sending data | SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot, count(*) as total FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (pcm.storefront_slot = 0) LIMIT 4                                                                                                                                                                                                                       |
57 rows in set (0.00 sec)


| 22655 | wordpress | cloudsqlproxy~*********   | wordpress | Query   |   50 | update       | INSERT INTO sf_deal (`user_id`, `from_user_id`, `position`, `product_sku`, `description`, `date`, `autoselected`) VALUES('336', '0', '3', '10100396', 'I recommend this product', '2021-10-11 17:54:16', '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| 22734 | wordpress | cloudsqlproxy~**********  | wordpress | Query   |    3 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22741 | wordpress | cloudsqlproxy~10.96.1.32  | wordpress | Query   |    0 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '3')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22742 | wordpress | cloudsqlproxy~10.96.7.141 | wordpress | Query   |    6 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 22990 | wordpress | cloudsqlproxy~*********   | wordpress | Query   |    5 | updating     | DELETE FROM sf_deal WHERE (`user_id` = '336') AND (`position` = '1')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |


SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot, count(*) as total FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (pcm.storefront_slot = 0) LIMIT 4                                                                                                                                                                                                                       |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot, count(*) as total FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (pcm.storefront_slot = 0) LIMIT 4                                                                                                                                                                                                                       |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100) ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4                                                                                                                                                                 |
SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot, count(*) as total FROM sf_products m INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1 INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 336 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 336 INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 336 WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (pcm.storefront_slot = 0) LIMIT 4                                                                                                                                                                                                                       |

{
"cat11199290", "cat11199292", "cat12269314", "cat14839307", "cat40036", "cat9799294", }

SF-30453 [ Prod ] - Macys high CPU
SF-30453-prod-macys-high-cpu


'ID', 'bigint(20) unsigned', 'NO', 'PRI', NULL, 'auto_increment'
'user_login', 'varchar(60)', 'NO', 'UNI', '', ''
'user_pass', 'varchar(64)', 'NO', '', '', ''
'user_nicename', 'varchar(50)', 'NO', 'MUL', '', ''
'user_email', 'varchar(100)', 'NO', '', '', ''
'user_url', 'varchar(100)', 'NO', '', '', ''
'user_registered', 'datetime', 'NO', 'MUL', '0000-00-00 00:00:00', ''
'user_activation_key', 'varchar(60)', 'NO', '', '', ''
'user_status', 'int(11)', 'NO', '', '0', ''
'user_alias', 'varchar(45)', 'YES', 'UNI', NULL, ''
'display_name', 'varchar(250)', 'NO', '', '', ''
'description', 'varchar(250)', 'YES', '', NULL, ''
'photo', 'varchar(100)', 'YES', '', NULL, ''
'last_login', 'datetime', 'YES', '', NULL, ''
'localization', 'enum(\'fr\',\'en\')', 'YES', '', NULL, ''
'feature', 'enum(\'deal\',\'8pack\',\'chat\',\'video\',\'blog\')', 'YES', '', NULL, ''
'status', 'enum(\'available\',\'unavailable\',\'vacation\')', 'YES', '', NULL, ''
'store', 'int(10) unsigned', 'NO', 'MUL', NULL, ''
'type', 'enum(\'rep\',\'store\')', 'YES', '', 'rep', ''
'commission_rate', 'decimal(5,2)', 'NO', '', '0.00', ''
'employee_id', 'varchar(45)', 'YES', 'MUL', NULL, ''
'group', 'int(11)', 'YES', '', '1', ''
'selling_mode', 'tinyint(1)', 'YES', '', '1', ''
'isPhoto', 'tinyint(1)', 'NO', 'MUL', '0', ''
'locked_at', 'datetime', 'YES', '', NULL, ''
'locale', 'char(5)', 'YES', '', NULL, ''
'creation_source', 'enum(\'invite\',\'feed\')', 'NO', '', 'invite', ''
'shop_feed', 'tinyint(1)', 'NO', '', '0', ''
'updated_by', 'bigint(20) unsigned', 'YES', '', NULL, ''
'updated_at', 'timestamp', 'YES', '', NULL, ''
'sso_auth', 'tinyint(1)', 'NO', '', '0', ''

/reps/{repId}/appointment-hours

CREATE TABLE `sf_rep_appointment_hours` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT 'The foreign key of table wp_users.',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

CREATE TABLE `sf_rep_appointment_hours_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sf_rep_appointment_hours_id` int(11) NOT NULL COMMENT 'The foreign key of table sf_rep_appointment_hours.',
  `day_of_week` int(11) NOT NULL COMMENT 'The days of week: 0-Monday, 1-Tuesday, ... 6-Sunday.',
  `start` time NOT NULL COMMENT 'The time when rep becomes available, e.g. 09:30:00',
  `end` time NOT NULL COMMENT 'The time when rep becomes unavailable, e.g. 18:30:00',
  PRIMARY KEY (`id`),
  KEY `sf_rep_appointment_hours_id` (`sf_rep_appointment_hours_id`,`day_of_week`),
  CONSTRAINT `sf_rep_appointment_hours_details_ibfk_1` FOREIGN KEY (`sf_rep_appointment_hours_id`) REFERENCES `sf_rep_appointment_hours` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

CREATE TABLE `sf_store_appointment_hours` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(10) NOT NULL COMMENT 'The foreign key of table sf_store',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint(20) unsigned NOT NULL COMMENT 'who created current row.',
  `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `updated_by` bigint(20) unsigned NOT NULL COMMENT 'who updated current row.',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

CREATE TABLE `sf_store_appointment_hours_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sf_store_appointment_hours_id` int(11) NOT NULL COMMENT 'The foreign key of table sf_store_appointment_hours',
  `day_of_week` int(11) unsigned NOT NULL COMMENT 'The days of week: 0-Monday, 1-Tuesday, ... 6-Sunday.',
  `start` time NOT NULL COMMENT 'The time when store becomes available, e.g. 09:30:00',
  `end` time NOT NULL COMMENT 'The time when store becomes unavailable, e.g. 18:30:00',
  PRIMARY KEY (`id`),
  KEY `sf_store_appointment_hours_id` (`sf_store_appointment_hours_id`,`day_of_week`),
  CONSTRAINT `sf_store_appointment_hours_details_ibfk_1` FOREIGN KEY (`sf_store_appointment_hours_id`) REFERENCES `sf_store_appointment_hours` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci


[
  'store_id' => 13,
  'enabled' => 1,
  'created_by' => 115,
]

 public function testCreateCustomerAppointmentTypeVirtual(RestTester $I)
    {
        $I->wantTo("Test create customer appointment of type virtual");

        $url = $this->app['configs']['salesfloor_storefront.host'] . '/sfadmin/appointment.php';

           'name' => 'test user',
            'email' => '<EMAIL>',
            'phone' => '',
            'country' => 'CA',
            'from' => 'storefront',
            'repname' => 'reggie',
            'contact_preference' => 'sms',
            'store' => '1003',
            'service' => 'virtual',
            'choosenDate' => date("m/d/Y", strtotime("+1 day")),
            'choosenTime' => '01:00 PM',
            'sms' => '5142345567',
            'timezone'  => 'America/Montreal',
            'usdoptlcdc' => $this->app['service.util']->qdcsrf(\Salesfloor\Services\GuzzleHttpClient::HEADER_INTERNAL_USER_AGENT), // pass csrf validation in wp stack
        ];
        $extraHeaders = ['User-Agent: ' . \Salesfloor\Services\GuzzleHttpClient::HEADER_INTERNAL_USER_AGENT];
// die();
        $this->doWpApiCurlPostWithFormData($I, $url, $user, $params, $extraHeaders);

        $I->seeInDatabase(
            'sf_pre_customer',
            [
                'ID' => 1,
                'name' => 'test user',
                'phone' => '+15142345567',
                'sms_marketing_subscription_flag' => 0,



testCreateCustomerAppointmentTypeVirtual


        both modified:   api/app/src/be/Managers/Reps.php
        both modified:   codeception/tests/functional/Mail/CustomerRequestEmail/AppointmentEmailCest.php
        both modified:   configs/configs/dev/chicos.php
        both modified:   instance-webserver/src/salesfloor/wp-content/plugins/sf-api/sf-api.php
        both modified:   models/src/Services/Appointment.php
        both modified:   services/src/Services/Appointments/Appointments.php
        both modified:   widgets/app/locales/generic/en_IE.yml
        both modified:   widgets/app/locales/generic/en_US.yml
        both modified:   widgets/app/src/fe/templates/chat/template.chatwindow.js
        both modified:   widgets/app/src/templates/services/chat/base.window-smb.html

SELECT DISTINCT m.product_id AS product_sku,
                pcm.storefront_slot,
                count(*) AS total
FROM sf_products m
INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id
AND pcm.category_id != 1
INNER JOIN sf_siteurl su ON su.product_id = m.product_id
INNER JOIN sf_images i ON i.product_id = m.product_id
LEFT JOIN
  (SELECT p.product_sku
   FROM sf_product p
   WHERE p.user_id = 1
   UNION SELECT d.product_sku
   FROM sf_deal d
   WHERE d.user_id = 1
   UNION SELECT '' AS product_sku
   UNION SELECT '0cb4b8b709e00325461bc0c4688be637' AS product_sku) selected ON m.product_id = selected.product_sku
INNER JOIN sf_categories c ON pcm.category_id = c.category_id
INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id
INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id
AND rpsm.user_id = 1
WHERE (m.available = "1")
  AND (selected.product_sku IS NULL)
  AND (pcm.storefront_slot = 0) LIMIT 8

SF-30306-SF-30309-be-3-display-customer-rep-store-relationships-on-profile

SF-30080 SDM - user is not receiving some emails from her clients
SF-30080-sdm-user-is-not-receiving-some-emails-from-her-clients


docker ps | grep sf_web | awk '{print $NF}'


'<EMAIL>',
'<EMAIL>',
'<EMAIL>',
'<EMAIL>',


 '<EMAIL>',
 '<EMAIL>',
 '<EMAIL>',

./api/app/src/be/Managers/Requests.php:83
./api/app/src/be/Managers/Messages/V1.php:79
./api/app/src/be/Managers/Services/V1/Appointments.php:196

SF-28270-holt-live-chat-answer-rate-aggregrate-calculation-3
SF-28270 Holt – Live Chat Answer Rate aggregrate calculation

SF-30511 BE - Mobile Onboarding - Connecting Twitter account during onboarding is slow and appears to fail even when it works

SF-30511-be-mobile-onboarding-connecting-twitter-account-during-onboarding-is-slow-and-appears-to-fail-even-when-it-works


select * from (
select count(*) as count , 'arnotts' as  retailer from wordpress_arnotts.sf_retailer_customer_stats union
select count(*) as count , 'bash' as  retailer from wordpress_bash.sf_retailer_customer_stats union
select count(*) as count , 'bbbaby' as  retailer from wordpress_bbbaby.sf_retailer_customer_stats union
select count(*) as count , 'benbridge' as  retailer from wordpress_benbridge.sf_retailer_customer_stats union
select count(*) as count , 'bloom' as  retailer from wordpress_bloom.sf_retailer_customer_stats union
select count(*) as count , 'brownt' as  retailer from wordpress_brownt.sf_retailer_customer_stats union
select count(*) as count , 'buckle' as  retailer from wordpress_buckle.sf_retailer_customer_stats union
select count(*) as count , 'cbanks' as  retailer from wordpress_cbanks.sf_retailer_customer_stats union
select count(*) as count , 'chicoca' as  retailer from wordpress_chicoca.sf_retailer_customer_stats union
select count(*) as count , 'chicos' as  retailer from wordpress_chicos.sf_retailer_customer_stats union
select count(*) as count , 'cosbar' as  retailer from wordpress_cosbar.sf_retailer_customer_stats union
select count(*) as count , 'cotr' as  retailer from wordpress_cotr.sf_retailer_customer_stats union
select count(*) as count , 'dxl' as  retailer from wordpress_dxl.sf_retailer_customer_stats union
select count(*) as count , 'elguntors' as  retailer from wordpress_elguntors.sf_retailer_customer_stats union
select count(*) as count , 'hbc' as  retailer from wordpress_hbc.sf_retailer_customer_stats union
select count(*) as count , 'holt' as  retailer from wordpress_holt.sf_retailer_customer_stats union
select count(*) as count , 'humantouch' as  retailer from wordpress_humantouch.sf_retailer_customer_stats union
select count(*) as count , 'jwas' as  retailer from wordpress_jwas.sf_retailer_customer_stats union
select count(*) as count , 'lilly' as  retailer from wordpress_lilly.sf_retailer_customer_stats union
select count(*) as count , 'macys' as  retailer from wordpress_macys.sf_retailer_customer_stats union
select count(*) as count , 'novartis' as  retailer from wordpress_novartis.sf_retailer_customer_stats union
select count(*) as count , 'pandora' as  retailer from wordpress_pandora.sf_retailer_customer_stats union
select count(*) as count , 'perrysport' as  retailer from wordpress_perrysport.sf_retailer_customer_stats union
select count(*) as count , 'peru' as  retailer from wordpress_peru.sf_retailer_customer_stats union
select count(*) as count , 'pharmaprix' as  retailer from wordpress_pharmaprix.sf_retailer_customer_stats union
select count(*) as count , 'rtb' as  retailer from wordpress_rtb.sf_retailer_customer_stats union
select count(*) as count , 'saks' as  retailer from wordpress_saks.sf_retailer_customer_stats union
select count(*) as count , 'shiseido' as  retailer from wordpress_shiseido.sf_retailer_customer_stats union
select count(*) as count , 'shoppers' as  retailer from wordpress_shoppers.sf_retailer_customer_stats union
select count(*) as count , 'sjk' as  retailer from wordpress_sjk.sf_retailer_customer_stats union
select count(*) as count , 'soma' as  retailer from wordpress_soma.sf_retailer_customer_stats union
-- select count(*) as count , 'tests' as  retailer from wordpress_tests.sf_retailer_customer_stats union
select count(*) as count , 'want' as  retailer from wordpress_want.sf_retailer_customer_stats union
select count(*) as count , 'whbm' as  retailer from wordpress_whbm.sf_retailer_customer_stats union
select count(*) as count , 'whbmca' as  retailer from wordpress_whbmca.sf_retailer_customer_stats union
select count(*) as count , 'wotr' as  retailer from wordpress_wotr.sf_retailer_customer_stats
) m


SF-30296 SSO - Mobile onboarding fails if user connects Twitter account during onboarding; can cause whitescreen lockup on iOS

SF-30296-sso-mobile-onboarding-fails-if-user-connects-twitter-account-during-onboarding-can-cause-whitescreen-lockup-on-i-os

SF-30511-be-mobile-onboarding-connecting-twitter-account-during-onboarding-is-slow-and-appears-to-fail-even-when-it-works

SF-26939 [Backend] Cleanup retailer stats old table


./robo test:functional Configs/ConfigCest.php | tee >> tests.txt
./robo test:functional Configs/ParserCest.php | tee >> tests.txt
./robo test:functional Tasks/NewRetailerTransactionFilteredCest.php | tee >> tests.txt
./robo test:functional Tasks/SoonToLapseCest.php | tee >> tests.txt
./robo test:functional Tasks/SoonToLapseSecondaryCest.php | tee >> tests.txt
./robo test:functional Tasks/TransactionMinCest.php | tee >> tests.txt
./robo test:rest Customer/CustomerApiCest.php | tee >> tests.txt

./robo test:functional Configs/ParserCest
./robo test:rest Customer/CustomerApiCest:testNotSubscribedCustomers


GET https://chicos.api.dev.salesfloor.net/retailer-customers?filter[search]=sds&filter[user_id]=115&filter[state]=&filter[city]=&per_page=13&page=&id=115 HTTP/1.1


SF-30114: Appointment Request Improvement
SF-30114-appointment-request-improvement

SF-30114-SF-30115-be-5-sales-associates-can-define-their-appointment-request-hours
SF-30114 SF-30115 [BE - 5] Sales associates can define their appointment request hours

SF-30114-SF-30115-be-5-sales-associates-can-define-their-appointment-request-hours-2


SF-30114 SF-30117 Display hours set by sales associates and managers in appointment request forms
SF-30114-SF-30117-display-hours-set-by-sales-associates-and-managers-in-appointment-request-forms

./robo generator:customers
./robo generator:retailer-customers

./robo ci:import:contacts
./robo ci:import:customers


./robo help {command}


 https://lilly-widgets-qa05.salesfloor.net/tests/desktop?sf_ip=***********

 https://pandora-widgets-qa06.salesfloor.net/tests/desktop?sf_ip=***********

 "An exception occurred while executing '

 SELECT m.ID
 ,m.user_id
 ,m.email
 ,m.name
 ,m.phone
 ,m.localization
 ,m.geo
 ,m.latitude
 ,m.longitude
 ,m.comment
 ,m.subcribtion_flag
 ,m.sms_marketing_subscription_flag
 ,m.first_name
 ,m.last_name
 ,m.note
 ,m.created
 ,m.last_modified
 ,m.type
 ,m.retailer_customer_id
 ,m.label_email
 ,m.label_phone
 ,m.origin
 ,m.locale
 ,m.unassigned_employee_id
 ,m.retailer_parent_customer_id
 ,m.entity_last_modified
 ,m.entity_last_export
 ,m.contact_preference
 , CONCAT(COALESCE(m.first_name ,''),COALESCE(m.last_name,''),COALESCE(m.email,''),COALESCE(m.phone,'')) AS custom_sort_concat, IF(SUBSTR(CONCAT(COALESCE(m.first_name, ''), COALESCE(m.last_name, ''), COALESCE(m.email, ''), COALESCE(m.phone, '')),1,1) < 'a',1,0 ) AS  custom_sort_special_char, ctrc.comment as matching, CASE WHEN rc.id IS NOT NULL THEN 1 ELSE 0 END as isRelatedRetailerCustomer FROM sf_customer m LEFT JOIN sf_customers_to_retailer_customers ctrc ON m.ID = ctrc.customer_id LEFT JOIN sf_retailer_customers rc ON ctrc.retailer_customer_id = rc.id WHERE (m.`user_id` = '115') AND (m.`groups` = '') AND (m.`ID` = '') AND (m.`subcribtion_flag` = '') AND (m.`sms_marketing_subscription_flag` = '') AND (m.`private` = '') AND (m.`corporate` = '') AND (m.`locale` = '') GROUP BY m.ID ORDER BY custom_sort_special_char,custom_sort_concat ASC LIMIT 12':

SQLSTATE[42S22]: Column not found: 1054 Unknown column 'm.groups' in 'where clause'"

SF-30713 BE: Handle Virtual Appointment Request with no email address (text message)

SF-30713-be-handle-virtual-appointment-request-with-no-email-address-text-message


INSERT INTO `sf_events`
(`type`,`source`,`uniq_id`,`user_id`,`customer_id`,`attributes`,`satisfied`,`event_id`,`store_id`)
VALUES (24,'sf_share_mail','SFID61dc7b173353c9.48191695',13,0,'5',0,0,13)

INSERT INTO `sf_mail_stats` (`event_uniq_id`) VALUES ('SFID61dc7b173353c9.48191695')

INSERT INTO sf_share_general (`user_id`, `share_type`, `recipient_count`, `recipient_blackout`, `subject`, `body`, `created_by`, `event_uniq_id`) VALUES('13', 'email', '5', '0', 'test', 'message', '13', 'SFID61dc7b173353c9.48191695')


gcloud pubsub topics publish projects/sf-dev-1b4e41f578/topics/lilly-share-an-update-dev --message="Message"

        $client = $this->getClient();
        $subscription = $client->subscription($queueUrl);
        $message = $subscription->pull();


https://chicos.dev.salesfloor.net/virtual-appointment/-Mt8wlHEllKodk61bO7-?sf_locale=en_US


{
  "success": true,
  "data": {
    "emails_sent": 1,
    "event_uniq_id": "SFID61ddb5397623e6.05209031",
    "emails_blackout": 0,
    "url": "http:\/\/lilly.dev.salesfloor.net\/reggie",
    "data": {
      "type": "share_general",
      "id": "2"
    }
  }
}


SF-30737 LP - Shared Update is being sent multiple times to some contacts

SF-30737-lp-shared-update-is-being-sent-multiple-times-to-some-contacts

...

 I now understand your question to be asking if there was an API error-
 such as an error with the body of your request.
 In that scenario, Sendgrid would not return a 250 response.
 Instead we would return a 4xx or 5xx response with an error message.

 A non-2xx level response would suggest that due to the error in the request,
 Sendgrid did not process the request, so NO mail was triggered.

 A 2xx level response suggests that the request was fully valid,
 and Sendgrid did attempt to deliver all of the mail.

        if ($withExtension) {


"outbound/dev/requests"
"DEV_CHICOS_request_2021-01-01_2021-10-10.csv

"sf-dev-chicos"

"outbound/dev/requests/DEV_CHICOS_request_2021-01-01_2021-10-10.csv"

'Appointment Request', '2948', '2021-03-02 19:39:17', 'https://chicos-widgets-stg.salesfloor.net/tests/desktop?sf_ip=***************', 'Bogus Plaza', '2021-03-02 19:39:17', 'In-Store', '2021-03-17 13:00:00', 'ce2e91d0-749d-11ec-b3a2-0242ac170064', ''
'Appointment Request', '3004', '2021-05-18 17:30:27', 'https://chicos-widgets-stg.salesfloor.net/tests/desktop?lang=fr', 'Fake Mall', '2021-05-18 17:30:27', 'In-Store', '2021-05-24 13:00:00', 'ce2ed33e-749d-11ec-b3a2-0242ac170064', ''
'Appointment Request', '3033', '2022-01-07 18:05:43', 'https://chicos.widgets.dev.salesfloor.net/tests/desktop?sf_ip=***********', 'Fake Mall', '2022-01-07 18:05:43', 'Virtual', '2022-01-08 02:00:00', 'ce2efbb0-749d-11ec-b3a2-0242ac170064', ''
'Appointment Request', '3034', '2022-01-07 18:06:47', 'https://chicos.widgets.dev.salesfloor.net/tests/desktop?sf_ip=***********', 'Fake Mall', '2022-01-07 18:06:47', 'Virtual', '2022-01-08 02:00:00', 'ce2efcb0-749d-11ec-b3a2-0242ac170064', ''
'Appointment Request', '3035', '2022-01-07 18:35:27', 'https://chicos.widgets.dev.salesfloor.net/tests/desktop?sf_ip=***********', 'Fake Mall', '2022-01-07 18:35:27', 'Virtual', '2022-01-08 02:00:00', 'ce2efdb4-749d-11ec-b3a2-0242ac170064', ''


m.request_id = q.ID AND m.request_type = 'contact_me'

SF-30683 [ Prod ] - HBC Query too much connections on DB

SF-30683-prod-hbc-query-too-much-connections-on-db



SF-30771 Virtual appointment - Rep user cannot enter and send message after customer joins meeting room.
SF-30771-virtual-appointment-rep-user-cannot-enter-and-send-message-after-customer-joins-meeting-room

SF-30771-virtual-appointment-rep-user-cannot-enter-and-send-message-after-customer-joins-meeting-room-2


SF-27285 Asset html page displays name of salesfloor_admin

SF-27285-asset-html-page-displays-name-of-salesfloor-admin


SF-30730 Fix (Go to green) codeception tests because of GCP-Master merge
SF-30730-fix-go-to-green-codeception-tests-because-of-gcp-master-merge



SF-29527 SF-30822 Virtual Appointment Release Config check
SF-29527-SF-30822-virtual-appointment-release-config-check

SF-30494 CHS brands - User cannot cancel appointment
SF-30494-chs-brands-user-cannot-cancel-appointment

'9886',
'13171',
'338'

http://chicos.dev.salesfloor.net/shop
?rep=lol56eff3d6ed8b9
&sf_url=https://www.chicos.com/store/category/warehouse+sale/cat20039275/?utm_source=marketing
&utm_medium=StyleConnect
&utm_campaign=01._19_21
&utm_content=warehouse
&event_source=email

<table width="100%" align="center" style="border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;">
<tr style="padding: 0; text-align: left; vertical-align: top" align="left">
<td align="center" class="asset-content" style="-moz-hyphens: auto; -webkit-hyphens: auto; border-collapse: collapse !important; color: #000000; font-family: Helvetica, Arial, Verdana, sans-serif !important; font-size: 13px; font-weight: normal; hyphens: auto; line-height: 1.4; margin: 0; padding: 0 0 20px; text-align: center; vertical-align: top; word-break: break-word" valign="top">
<a href="{SHOPPAGE}https%253A%252F%252Fwww.example.com%253Fv1%253D1%2526v2%253D2%2526v3%253D3">
<img src="any_image_link" style="border: 0px; width: 100%;" />
</a>
</td>
</tr>
</table>
<table width="100%" align="center" style="font-family:arial;font-size:smaller;border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;">
<tr style="padding: 0; text-align: left; vertical-align: top" align="left">
<td>
<p>sf_asset_footer</p>
</td>
</tr>
</table>

SF-30794 CHS Brands, Bloom - Some parameters in asset URL are lost when clicking through as a customer
SF-30794-chs-brands-bloom-some-parameters-in-asset-url-are-lost-when-clicking-through-as-a-customer

------------------------------------------------------------------------------
SF-30730-fix-go-to-green-codeception-tests-because-of-gcp-master-merge

SF-30174 [Spike] - Improve test speed (codeception) execution
SF-30174-spike-improve-test-speed-codeception-execution-epic

SF-30174 SF-30876 Mock up Elastic search component during testing
sf-30174-sf-30876-mock-up-elastic-search-component-during-testing

SF-30174 SF-30880 Use transaction between tests
SF-30174-sf-30880-use-transaction-between-tests



rm ./codeception/tests/rest/Reporting/BackOfficeSalesCest.php
rm ./codeception/tests/rest/IncomingMail/DuplicateMailCest.php
rm ./codeception/tests/rest/ServiceForms/FinderCest.php
rm ./codeception/tests/rest/ServiceForms/QuestionCest.php
rm ./codeception/tests/rest/ServiceForms/AppointmentCest.php
rm ./codeception/tests/rest/Assets/AssetsCest.php
rm ./codeception/tests/rest/Assets/CorporateAssetsCest.php
rm ./codeception/tests/rest/Share/ShareEmailCest.php
rm ./codeception/tests/rest/ShopFeed/ShopFeedCest.php
./codeception/tests/rest/WPJwtTokenAuth/WPJwtTokenAuthCest.php



rm AssetsCest.php
rm BackOfficeConnectionCest.php
rm CorporateAssetsCest.php
rm CustomerApiCest.php
rm DuplicateMailCest.php
rm ExportsAllcontactsCest.php
rm ExportsTextCest.php
rm IncomingMailCest.php
rm CorporateTasksCest.php
rm CustomerActivityFeedCest.php
rm CustomerApiAddressCest.php
rm CustomerBlockedValidationCest.php
rm CustomerJsonApiCest.php
rm CustomerMatchingCest.php
rm CustomerV1ApiCest.php
rm DashboardComponentCest.php
rm ErrorsCest.php
rm ExportsRequestCest.php
rm GetListCest.php
rm IncludeCest.php
rm InventoryLookupCest.php
rm UploaderCest.php


https://chicos.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID622262e33be585.78347695&meetingid=3038&action=accept&customer=true&rep=reggie&version=2&sf_locale=en_US

SF-29527 SF-30900 Virtual appointment SMS join link not sent to shopper if associate proposes new time before accepting request.

SF-29527-SF-30900-virtual-appointment-sms-join-link-not-sent-to-shopper-if-associate-proposes-new-time-before-accepting-request

SF-30174-SF-30902-use-database-transaction-between-tests-api-functional

SF-30174 SF-30902 Use database transaction between tests (api, functional)

php

php ./api/app/bin/check_virtual_appointment_configs.php prd
php ./api/app/bin/check_virtual_appointment_configs.php stg
php ./api/app/bin/check_virtual_appointment_configs.php qa04
php ./api/app/bin/check_virtual_appointment_configs.php qa05
php ./api/app/bin/check_virtual_appointment_configs.php qa06
php ./api/app/bin/check_virtual_appointment_configs.php dev

platform/codeception/tests/api/reps/BeginOnboardingCest.php

        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $response = $I->doDirectGet($this->app, "customers/" . $id);
        $result = json_decode($response->getContent());

        $response = $I->doDirectGet($this->app, "customers/" . $result->ID);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals('Contact creation failed. The data provided (email) matches another contact', $result->error);

SF-30926 Customers can't submit appointment request via sidebar widget, storefront or footer

SF-30926-customers-can-t-submit-appointment-request-via-sidebar-widget-storefront-or-footer

Request URL: https://soma-api-qa04.salesfloor.net/v2/retailer-customers/add-to-my-contacts
Request URL: https://soma-api-qa04.salesfloor.net/v2/retailer-customers/add-to-my-contacts
Request Method: POST

Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoianJlcDEiLCJzdWIiOiIyODMiLCJleHAiOjE2NDc5NjE4NzksImlhdCI6MTY0Nzk2MTI3OSwicmVmIjoxNjUyNzk5Njc5fQ.I0oAFFe0Alwa4NlCy5iH1qPnzDZH1bpmYlU-N3UltI0

{"data":{"type":"retailer-customer","attributes":{"customer_id":"000000008"}}}

SF-30934-add-customer-to-contacts-users-are-not-able-to-add-customers-with-only-phone-number-to-my-contacts
SF-30934 Add Customer to Contacts::Users are not able to add customers with only phone number to my contacts.


SF-30174 SF-30941 Fix bugs the tests found

SF-30946 CHICOS | Add a CI Customer to My Contacts for all Brands in QA06 environment for UAT
SF-30946-chicos-add-a-ci-customer-to-my-contacts-for-all-brands-in-qa06-environment-for-uat

$I->fakeElasticSearchForCustomerManager(...)
$I->fakeElasticSearchForCustomer(...)
$I->fakeElasticsearchByFilters(...)
$I->fakeElasticsearchByFields(...)
$I->fakeElasticSearchForCustomerV1(...)
$I->fakeElasticSearchForRetailerCustomerManager(...)


./codeception/tests/fixtures/importer/customer/attributes/when_add_customer_add_attribute_multi_select.csv

./robo ci:import:contacts --filename=./codeception/tests/fixtures/importer/customer/attributes/when_add_customer_add_attribute_multi_select.csv --no-delete chicos-dev

./robo ci:import:contacts --filename=./demo.csv --no-delete shoppers-qa06


[{"value": "oily","label": "Oily"},{"value": "dry","label": "Dry"}]



AC-10-be-3-enable-the-barcode-scanner-to-input-data-for-flagged-extended-contact-attributes

AC-101 Appointment Reminder text messages are sent repeatedly with sometimes differing links that don't always work

AC-101-appointment-reminder-text-messages-are-sent-repeatedly-with-sometimes-differing-links-that-don-t-always-work


AC-101
Appointment Reminder text messages are sent repeatedly with sometimes differing links that don't always work


https://benbridge-qa05.salesfloor.net/s/QkJrDEmZsiX6Ou1m
https://benbridge-qa05.salesfloor.net/s/QkJrDEmZsiX60u1m


sf_customer_attributes;
sf_customer_attributes_panel;
sf_customers_to_customer_attributes;
sf_customer;

delete from sf_customer_attributes;
delete from sf_customer_attributes_panel;
delete from sf_customers_to_customer_attributes;
delete from sf_customer;
delete from sf_product_variant_attributes;
delete from sf_product_variant_attributes_i18n;
delete from sf_retailer_customers_to_customer_attributes;
delete from sf_text_attachment;
delete FROM sf_unattached_customers;


AmazonSQS.php
Beanstalk.php
Facebook.php
Iron.php
MongoDb.php
MySql.php
Oci.php
PostgreSql.php
SqlSrv.php
Sqlite.php

AC-2-AC-150-search-and-filter-ea-user-is-not-able-to-search-attributes-of-type-single-select-multiple-select

AC 2 AC 150 search and filter ea user is not able to search attributes of type single select multiple select

AC-2 AC-158 Search and Filter EA::App does not return filter results in Contacts
AC-2-AC-158-search-and-filter-ea-app-does-not-return-filter-results-in-contacts


AC-165 User is not able to add a contact to "My Contacts" from "All Customers"
AC-165-user-is-not-able-to-add-a-contact-to-my-contacts-from-all-customers

AC-140 Chicos & SDM (likely all) - Primary & Secondary relationship exposure in All Customers includes inactive users

AC-140-chicos-sdm-likely-all-primary-secondary-relationship-exposure-in-all-customers-includes-inactive-users

AC-143 Appointment Reschedule::BO::User is not able to suggest new appointment time.

AC-143-appointment-reschedule-bo-user-is-not-able-to-suggest-new-appointment-time-2


{"attribute_id":"pco_pco1","attribute_value":"6085591199085509002"},{"attribute_id":"skin1_date","attribute_value":"2021-11-03"},{"attribute_id":"skin1_hydratest","attribute_value":"12"},{"attribute_id":"skin1_hydralevel","attribute_value":"hydralevel2"},{"attribute_id":"skin1_skintype","attribute_value":"skintype4"},{"attribute_id":"skin1_tzone","attribute_value":"["tzone2","tzone6"]"},{"attribute_id":"skin1_cheeks","attribute_value":"["cheeks4","cheeks5"]"},{"attribute_id":"skin1_eyes","attribute_value":"["eyes3","eyes4"]"},{"attribute_id":"skin1_neck","attribute_value":"["neck2","neck3"]"},{"attribute_id":"skin2_date","attribute_value":"2021-11-05"},{"attribute_id":"skin2_hydratest","attribute_value":"14"},{"attribute_id":"skin2_hydralevel","attribute_value":"hydralevel2"},{"attribute_id":"skin2_skintype","attribute_value":"skintype4"},{"attribute_id":"skin2_tzone","attribute_value":"["tzone7"]"},{"attribute_id":"skin2_cheeks","attribute_value":"["cheeks9"]"},{"attribute_id":"skin2_eyes","attribute_value":


AC-2 AC-181 Search and Filter::Es:Reindex script is not working.

AC-2-AC-181-search-and-filter-es-reindex-script-is-not-working

AC-185 Appointment Request - add new config to be able to set appointment duration (customer facing)
AC-185-appointment-request-add-new-config-to-be-able-to-set-appointment-duration-customer-facing


AC-187 Extended attributes are not transferred when adding a customer to "my contacts"
AC-187-extended-attributes-are-not-transferred-when-adding-a-customer-to-my-contacts

select contact_preference, count(contact_preference)
from sf_customer
group by contact_preference;

I would be looking at the data for the following retailers :
sdm

  NULL, '2817955'
  '', '52850'
  'email', '72163'
  'text', '42019'
  'phone', '14681'


macys

  NULL, '11737007'
  '', '1576694'
  'email', '9868'
  'text', '810'
  'phone', '56'


ben bridge

  NULL, '432190'
  '', '20321'
  'email', '686'
  'text', '788'
  'phone', '106'

bloom

  NULL,'1923360'
  '','156007'
  'email','20118'
  'text','319'
  'phone','15'

chicos

  NULL, '4794269'
  '', '4539'
  'email', '4083'
  'text', '211'
  'phone', '131'

pharmaprix

  NULL, '2126927'
  '', '3576'
  'email', '15955'
  'text', '9973'
  'phone', '5442'


AC-192 Product Attachments Not Being Attached to Text Messages
AC-192-product-attachments-not-being-attached-to-text-messages


AC-193 Shoppers::Export:contacts script is not generating contacts outbound file.
AC-193-shoppers-export-contacts-script-is-not-generating-contacts-outbound-file


https://chicos.api.dev.salesfloor.net/retailer-customers?page=0&per_page=11&filter[user_id]=115
https://chicos.api.dev.salesfloor.net/retailer-customers?page=0&per_page=11&filter[user_id]=115&id=13xxx

AC-211 PC - shared updates are not being sent to all subscribers
AC-211-pc-shared-updates-are-not-being-sent-to-all-subscribers

AC-214 SDM - All Customers Tab Not Populating
AC-214-sdm-all-customers-tab-not-populating

AC-208 Variant and Group not showing in the product filter
AC-208-variant-and-group-not-showing-in-the-product-filter


AC-217 BBJ, Bloom, Macy's, RTB - SMS being sent to wrong contacts
AC-217-bbj-bloom-macy-s-rtb-sms-being-sent-to-wrong-contacts

AC-230 Credo and Macy's - 'New Leads' notifications coming in with just the letter V

AC-230-credo-and-macy-s-new-leads-notifications-coming-in-with-just-the-letter-v


/tmp/translator_cache_dir/catalogue.en_US.be29aec3e037ad56180e85d033e5dfb086e2c796.php


-----------------------------
Dan Murphys

    "contact preference"  count
    NULL, '2159481'
    '', '1832412'
    'email', '164'

Arnotts
    "contact preference"  count
    NULL, '19417'
    'email', '3873'

Brown Thomas
    NULL, '35813'
    'email', '8706'
    'text', '5'
    'phone', '2'


DXL
    "contact preference"  count
    NULL, '3337932'
    'email', '1056'
    'text', '23'
    'phone', '2'

-----------------------------
Shiseido

    stores   date_overrides
    '87', '350'

    store_id  week_schedule
    '10003130', '7'
    '10003174', '7'
    '10003200', '7'
    '10003207', '7'
    '10003218', '7'
    '10003225', '7'
    '10003242', '7'
    '10003261', '7'
    '10003267', '7'
    '10003279', '7'
    '10003289', '7'
    '10003292', '7'
    '10003311', '7'
    '10003312', '7'
    '10003319', '7'
    '10003320', '7'
    '10003321', '7'
    '10003322', '7'
    '10003324', '7'
    '10003325', '7'
    '10003326', '7'
    '10003327', '7'
    '10003331', '7'
    '10003347', '7'
    '10003349', '7'
    '10003358', '7'
    '10003361', '7'
    '10003366', '7'
    '10003367', '7'
    '10003387', '7'
    '10003393', '7'
    '10003427', '7'
    '10003431', '7'
    '10003446', '7'
    '10003448', '7'
    '10003457', '7'
    '10003458', '7'
    '10003470', '7'
    '10003479', '7'
    '10003485', '7'
    '10003501', '7'
    '10003507', '7'
    '10003512', '7'
    '10003521', '7'
    '10003525', '7'
    '10003539', '7'
    '10003543', '7'
    '10003546', '7'
    '10003548', '7'
    '10003550', '7'
    '10003551', '7'
    '10003554', '7'
    '10003561', '7'
    '10003565', '1'
    '10003572', '7'
    '10003577', '7'
    '10003584', '7'
    '10003585', '7'
    '10003588', '7'
    '10003589', '7'
    '10003594', '7'
    '10003597', '7'
    '10003606', '7'
    '10003607', '7'
    '10003613', '7'
    '10003614', '7'
    '10003616', '7'
    '10003619', '7'
    '10003622', '7'
    '10003623', '7'
    '10003624', '7'
    '10003628', '7'
    '10003629', '7'
    '10003630', '7'
    '10003632', '7'
    '10003633', '7'
    '10003635', '7'
    '10003637', '7'
    '10003641', '7'
    '10003645', '7'
    '10003651', '7'
    '10003652', '7'
    '10003657', '7'
    '10003658', '7'
    '10003675', '7'
    '10003678', '7'
    '10003680', '7'


Peruvian

    stores   date_overrides
    '1', '0'

    store_id  week_schedule
    '11', '7'

CBK
    stores   date_overrides
    '0', '0'

    store_id  week_schedule
    {empty}

RTB
    stores   date_overrides
    '2', '0'

    store_id  week_schedule
    '1108', '7'
    '1193', '7'

Chico

    stores   date_overrides
    '36', '7'

    store_id  week_schedule
    '1050', '7'
    '1064', '7'
    '1081', '7'
    '1098', '7'
    '2475', '7'
    '2479', '7'
    '2536', '7'
    '2554', '7'
    '2557', '7'
    '2558', '7'
    '2568', '7'
    '2578', '7'
    '2596', '7'
    '2612', '7'
    '2661', '7'
    '2669', '7'
    '2670', '7'
    '2692', '7'
    '2699', '7'
    '2708', '7'
    '2714', '7'
    '2732', '7'
    '2747', '7'
    '2779', '7'
    '2784', '1'
    '2785', '7'
    '2790', '7'
    '2809', '7'
    '2838', '7'
    '2849', '7'
    '2857', '1'
    '2869', '7'
    '2881', '7'
    '2882', '7'
    '2903', '7'
    '2907', '7'

WHBM

    stores   date_overrides
    '27', '5'

    store_id  week_schedule
    '1811', '7'
    '1812', '7'
    '1815', '7'
    '1831', '7'
    '1835', '7'
    '1872', '7'
    '1897', '7'
    '1906', '7'
    '1919', '7'
    '1921', '7'
    '1950', '7'
    '1955', '7'
    '1962', '7'
    '1986', '7'
    '1999', '7'
    '2000', '7'
    '2002', '7'
    '2016', '7'
    '2029', '7'
    '2046', '7'
    '2088', '1'
    '2089', '7'
    '2090', '7'
    '2133', '7'
    '2166', '7'
    '2169', '7'
    '2222233', '7'

SOMA

    stores   date_overrides
    '26', '19'

    store_id  week_schedule
    '1012','7'
    '1015','7'
    '1042','7'
    '1051','7'
    '1075','7'
    '1109','7'
    '1115','7'
    '1116','7'
    '1124','7'
    '1125','7'
    '1126','7'
    '1143','7'
    '1146','7'
    '1162','7'
    '1170','7'
    '1187','7'
    '1205','7'
    '1217','7'
    '1218','7'
    '1230','7'
    '1239','7'
    '1250','7'
    '1255','7'
    '1270','7'
    '1275','7'
    '1283','7'

COTR

    stores   date_overrides
    '7', '4'

    store_id  week_schedule
    '1014', '7'
    '1045', '7'
    '1067', '7'
    '1068', '7'
    '1072', '7'
    '1090', '7'
    '1117', '7'

SDM

    stores   date_overrides
    '62', '21'

    store_id  week_schedule
    '1006', '7'
    '1017', '7'
    '1026', '7'
    '1042', '7'
    '9089', '7'
    '9091', '7'
    '9103', '7'
    '9106', '7'
    '9111', '7'
    '9123', '7'
    '9130', '7'
    '9142', '7'
    '9204', '7'
    '9205', '7'
    '9207', '7'
    '9244', '7'
    '9321', '7'
    '9338', '7'
    '9352', '7'
    '9376', '7'
    '9452', '1'
    '9500', '7'
    '9504', '1'
    '9536', '7'
    '9569', '7'
    '9577', '7'
    '9602', '7'
    '9641', '7'
    '9662', '7'
    '9698', '1'
    '9722', '7'
    '9747', '7'
    '9755', '7'
    '9776', '1'
    '9778', '1'
    '9787', '7'
    '9802', '7'
    '9815', '7'
    '9829', '7'
    '9840', '7'
    '9842', '7'
    '9872', '7'
    '9898', '7'
    '9916', '7'
    '9919', '7'
    '9923', '7'
    '9944', '7'
    '9954', '7'
    '9960', '7'
    '9972', '7'
    '9976', '7'
    '9986', '7'
    '9995', '1'
    '10011', '7'
    '10024', '7'
    '10037', '7'
    '10050', '7'
    '10057', '1'
    '10079', '7'
    '10086', '7'
    '10102', '7'
    '10111', '7'

PHX

    stores   date_overrides
    '4', '0'

    store_id  week_schedule
    '10000552', '1'
    '10000564', '7'
    '10000574', '1'
    '10000676', '7'



"/srv/www/sf_platform/current/configs/configs/dev/../../../.dev-env-id"

"/60b503a853629-chicos-dev.sf_customer/sf_customer/387704"

'sf_customer', 'CREATE TABLE `sf_customer` (\n  `ID` int(11) NOT NULL AUTO_INCREMENT,\n  `user_id` bigint(20) DEFAULT NULL,\n  `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `name` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `phone` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `localization` enum(\'en\',\'fr\') COLLATE utf8mb4_unicode_ci NOT NULL,\n  `geo` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,\n  `latitude` float DEFAULT NULL,\n  `longitude` float DEFAULT NULL,\n  `comment` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,\n  `subcribtion_flag` tinyint(1) NOT NULL,\n  `sms_marketing_subscription_flag` tinyint(1) NOT NULL DEFAULT \'0\' COMMENT \'Flag representing if the customer has subscribed to sms marketing\',\n  `first_name` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `last_name` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `note` varchar(5000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,\n  `last_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  `type` enum(\'personal\',\'corporate\') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT \'personal\',\n  `retailer_customer_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `label_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `label_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `origin` enum(\'unknown\',\'admin-crm-import\',\'customer-importer-script\',\'rep-address-book\',\'rep-manual-creation\',\'mobile-rep-device-import\',\'customer-updater-script\',\'mobile-rep-manual-creation\',\'storefront-chat\',\'storefront-appointment\',\'storefront-personal-shopper\',\'storefront-email\',\'widget-chat\',\'widget-appointment\',\'widget-personal-shopper\',\'widget-email\',\'transaction\',\'storefront-updates\',\'customers2contacts\',\'ci-transaction\',\'rep-appointment\',\'cancellation-follow-up-task\',\'rsvp-event\',\'api\',\'rep-add-to-contact\') COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `locale` char(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL,\n  `unassigned_employee_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT \'Populated with employee id if the user does not exist on crm import\',\n  `retailer_parent_customer_id` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT \'Same as retailer_customer_id column; customer_id from sf_retailer_customers but 1-n relationship. Does not imply ownership, use retailer_customer_id\',\n  `entity_last_modified` datetime DEFAULT NULL COMMENT \'Last modified time for customer domain entity(include: customerEvents, customerAddress etc.  )\',\n  `entity_last_export` datetime DEFAULT NULL COMMENT \'Last export time for customer\',\n  `contact_preference` enum(\'email\',\'text\',\'phone\') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT \'Preferred contact method, such as email, text, phone, etc.\',\n  PRIMARY KEY (`ID`),\n  UNIQUE KEY `email` (`user_id`,`email`),\n  UNIQUE KEY `retailer_customer_id` (`retailer_customer_id`),\n  UNIQUE KEY `user_id_2` (`user_id`,`retailer_parent_customer_id`),\n  UNIQUE KEY `idx_uq_unassigned_employee_email` (`unassigned_employee_id`,`email`),\n  KEY `email_2` (`email`),\n  KEY `user_id` (`user_id`),\n  KEY `origin` (`origin`),\n  KEY `unassigned_employee_id` (`unassigned_employee_id`),\n  KEY `retailer_parent_customer_id` (`retailer_parent_customer_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=387706 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci'


{
    "type": "rep",
    "ids":
    [
        "115"
    ],
    "filter":
    {
        "kpi-period": "MTD",
        "rep":
        [
            "115"
        ],
        "start-date": "2022-09-01 00:00:00",
        "end-date": "2022-09-08 23:59:59",
        "include-variance": true
    },
    "ranks":
    {
        "sales":
        {
            "value": 0,
            "rank": "2",
            "variance": 0,
            "old_rank": "2",
            "label": "Total Sales"
        },
        "response-time":
        {
            "value": 0,
            "rank": "2",
            "variance": 0,
            "old_rank": "2",
            "label": "Request Reply Time"
        },
        "requests-received":
        {
            "value": 0,
            "rank": "2",
            "variance": 0,
            "old_rank": "2",
            "label": "Total Requests"
        },
        "unique-visitors":
        {
            "value": 0,
            "rank": "2",
            "variance": 0,
            "old_rank": "2",
            "label": "Unique Visitors"
        },
        "chat-answer-rate":
        {
            "value": 0,
            "rank": "2",
            "variance": 0,
            "old_rank": "2",
            "label": "Live Chat Answer Rate"
        },
        "global":
        {
            "value": 8,
            "rank": "3",
            "variance": 0,
            "old_rank": "3",
            "label": "Total Ranking"
        }
    },
    "rank-out-of": 103,
    "kpis":
    {
        "salesfloor_visits_unique": 0,
        "salesfloor_storefront_visits_unique": 0,
        "outstanding_email_requests": 18,
        "outstanding_appointments": 3,
        "outstanding_personal_shopper": 0,
        "outstanding_total": 21,
        "socialshop_unique_visit": "0",
        "appointment_req": 0,
        "onboarding_end": 0,
        "user_add": 0,
        "chat_request": 0,
        "chat_answer": 0,
        "response_mail_sent": 0,
        "mail_sent": 0,
        "email_stats_open": 0,
        "email_stats_click": 0,
        "avg_init_resp_sum_times": 0,
        "avg_init_resp_num_responses": 0,
        "chat_answer_rate_requests": 0,
        "chat_answer_rate_answers": 0,
        "total_order_value": 0,
        "n_sales_transactions": 0,
        "feedback": 0,
        "profile_update": 0,
        "content_create": 0,
        "content_update": 0,
        "content_curate": 0,
        "ask_question_req": 0,
        "personal_shopper_req": 0,
        "live_session_start": 0,
        "live_session_end": 0,
        "product_update": 0,
        "deal_update": 0,
        "unsubscribe": 0,
        "new_sale": 0,
        "subscribe": 0,
        "retail_event": 0,
        "raise_concern": 0,
        "shopping_page": 0,
        "unique_visitor": 0,
        "page_hit": 0,
        "sale_duplicate": 0,
        "user_visit": 0,
        "com_ref": 0,
        "soc_ref": 0,
        "retail_hit": 0,
        "social_post": 0,
        "soc_share": 0,
        "moderate_lead": 0,
        "customer_card": 0,
        "help_useful": 0,
        "livesession_register": 0,
        "change_categories": 0,
        "chatsession_register": 0,
        "event_create": 0,
        "event_update": 0,
        "event_delete": 0,
        "event_subscribe": 0,
        "sidebar_view": 0,
        "sidebar_click": 0,
        "footer_view": 0,
        "footer_click": 0,
        "storefront_click": 0,
        "transactional_mail_sent": 0,
        "courtesy_mail_sent": 0,
        "service_total": 0,
        "traffic_total": 0,
        "content_total": 0,
        "number_seconds_available": 0,
        "total_return_value": 0,
        "recommendation_chat": 0,
        "recommendation_compose_message": 0,
        "recommendation_share_email": 0,
        "recommendation_share_facebook": 0,
        "recommendation_share_twitter": 0,
        "recommendation_new_arrivals": 0,
        "recommendation_top_picks": 0,
        "click_top_picks": 0,
        "click_latest_arrivals": 0,
        "click_recommended": 0,
        "avg_selected_top_picks": 0,
        "avg_selected_new_arrivals": 0,
        "salesfloor_visits": 0,
        "text_messages_outbound_api": 0,
        "text_messages_outbound_call": 0,
        "text_messages_outbound_reply": 0,
        "text_messages_inbound": 0,
        "recommendation_text_message": 0,
        "tasks_automated_created": 0,
        "tasks_automated_resolved": 0,
        "tasks_automated_dismissed": 0,
        "tasks_manual_created": 0,
        "tasks_manual_resolved": 0,
        "tasks_manual_dismissed": 0,
        "chat_abandoned": 0,
        "chat_abandonment_time": 0,
        "chat_answer_time": 0,
        "chat_early_redirect": 0,
        "chat_auto_redirect": 0,
        "ask_question_req_email": 0,
        "ask_question_req_text": 0,
        "appointment_req_email": 0,
        "appointment_req_text": 0,
        "personal_shopper_req_email": 0,
        "personal_shopper_req_text": 0,
        "library_share_attempts": 0,
        "lookbook_create": 0,
        "lookbook_update": 0,
        "received_chats_answered_by_other": 0,
        "request_email_sent": 0,
        "request_email_open": 0,
        "request_email_click": 0,
        "compose_email_sent": 0,
        "compose_email_open": 0,
        "compose_email_click": 0,
        "share_email_sent": 0,
        "share_email_open": 0,
        "share_email_click": 0,
        "total_share_sent": 0,
        "chat_abandon_0_29": 0,
        "chat_abandon_30_59": 0,
        "chat_abandon_60_89": 0,
        "chat_abandon_90_120": 0,
        "ask_question_req_chat_handoff": 0,
        "tasks_system_created": 0,
        "tasks_system_resolved": 0,
        "tasks_system_dismissed": 0,
        "tasks_followup_created": 0,
        "tasks_followup_resolved": 0,
        "tasks_followup_dismissed": 0,
        "tasks_corporate_created": 0,
        "tasks_corporate_resolved": 0,
        "tasks_corporate_dismissed": 0,
        "tasks_manual_resolved_sum_time": 0,
        "tasks_manual_dismissed_sum_time": 0,
        "tasks_automated_resolved_sum_time": 0,
        "tasks_automated_dismissed_sum_time": 0,
        "tasks_system_resolved_sum_time": 0,
        "tasks_system_dismissed_sum_time": 0,
        "tasks_followup_resolved_sum_time": 0,
        "tasks_followup_dismissed_sum_time": 0,
        "tasks_corporate_resolved_sum_time": 0,
        "tasks_corporate_dismissed_sum_time": 0,
        "socialshop_post_created": 0,
        "socialshop_total_visit": 0,
        "socialshop_product_click": 0,
        "socialshop_storefront_click": 0,
        "socialshop_sales_count": 0,
        "socialshop_sales_amount_total": 0,
        "tasks_corporate_deleted": 0,
        "ask_question_req_cs_email": 0,
        "ask_question_req_cs_text": 0,
        "appointment_req_cs_email": 0,
        "appointment_req_cs_text": 0,
        "personal_shopper_req_cs_email": 0,
        "personal_shopper_req_cs_text": 0,
        "scheduled_appointments": 0,
        "cancelled_appointment": 0,
        "video_chat_sessions": 0,
        "video_chat_duration": 0,
        "virtual_appointment_sessions": 0,
        "virtual_appointment_duration": 0,
        "avg_init_resp": "-",
        "avg_init_resp_raw": 0,
        "avg_order_value": 0,
        "chat_answer_rate": 0,
        "mail_click": 0,
        "mail_open": 0,
        "tasks_manual_resolved_avg_time": "-",
        "tasks_manual_resolved_avg_time_raw": 0,
        "tasks_manual_dismissed_avg_time": "-",
        "tasks_manual_dismissed_avg_time_raw": 0,
        "tasks_automated_resolved_avg_time": "-",
        "tasks_automated_resolved_avg_time_raw": 0,
        "tasks_automated_dismissed_avg_time": "-",
        "tasks_automated_dismissed_avg_time_raw": 0,
        "tasks_system_resolved_avg_time": "-",
        "tasks_system_resolved_avg_time_raw": 0,
        "tasks_system_dismissed_avg_time": "-",
        "tasks_system_dismissed_avg_time_raw": 0,
        "tasks_followup_resolved_avg_time": "-",
        "tasks_followup_resolved_avg_time_raw": 0,
        "tasks_followup_dismissed_avg_time": "-",
        "tasks_followup_dismissed_avg_time_raw": 0,
        "tasks_corporate_resolved_avg_time": "-",
        "tasks_corporate_resolved_avg_time_raw": 0,
        "tasks_corporate_dismissed_avg_time": "-",
        "tasks_corporate_dismissed_avg_time_raw": 0,
        "net_sales": 0,
        "n_recommendations": "0",
        "click_top_picks_rate": 0,
        "click_latest_arrivals_rate": 0,
        "click_recommended_rate": 0,
        "text_messages_sent": 0,
        "text_messages_received": 0,
        "service_exclude_live_chat_total": 0,
        "service_cs_total": 0
    }
}


user_add



SELECT sf_events.user_id,
       sf_events.type,
       COUNT(*) AS COUNT,
       SUM(if((sf_events.attributes+0)=0, 1, (sf_events.attributes+0))) AS total,
       COUNT(DISTINCT sf_events.uniq_id) AS uniq_id_count
FROM sf_events
WHERE (CONVERT_TZ(sf_events.date,'UTC', 'America/Montreal') >= '2022-09-07 00:00:00')
  AND (CONVERT_TZ(sf_events.date,'UTC', 'America/Montreal') <= '2022-09-07 23:59:59')
  AND (sf_events.type NOT IN (44,
                              45,
                              78,
                              79,
                              80,
                              81,
                              82,
                              83,
                              54,
                              55,
                              56,
                              57,
                              58,
                              70,
                              71,
                              76))
  AND (sf_events.user_id IN (115,
                             143,
                             142,
                             141,
                             139,
                             280,
                             281,
                             282,
                             283,
                             284,
                             285,
                             286,
                             287,
                             288,
                             289,
                             329,
                             331,
                             336,
                             536,
                             537,
                             538,
                             539,
                             548,
                             549,
                             560,
                             602,
                             603,
                             604,
                             606,
                             609,
                             610,
                             611,
                             635,
                             1014,
                             1027,
                             1029,
                             1031,
                             1033,
                             1039,
                             1041,
                             1043,
                             1045,
                             1046,
                             1051,
                             1052,
                             1053,
                             1055,
                             1057,
                             1059,
                             1063,
                             1091,
                             1104,
                             1113,
                             1120,
                             1123,
                             1125,
                             1129,
                             1131,
                             1133,
                             1134,
                             1135,
                             1136,
                             1137,
                             1138,
                             1139,
                             1140,
                             1141,
                             1143,
                             1145,
                             1149,
                             1151,
                             1152,
                             1153,
                             1154,
                             1155,
                             1156,
                             1157,
                             1158,
                             1159,
                             1160,
                             1162,
                             1164,
                             1165,
                             1166,
                             1167,
                             1168,
                             1169,
                             1182,
                             1242,
                             1273,
                             1315,
                             1351,
                             1400,
                             1401,
                             1456,
                             1476,
                             1477,
                             1478,
                             1479,
                             1532,
                             1533,
                             1534,
                             1708,
                             1709,
                             1716,
                             1810,
                             1816,
                             1821,
                             1843,
                             1844,
                             1853,
                             1867,
                             1870,
                             1898,
                             1899,
                             1901,
                             1902,
                             1903,
                             1904,
                             1912,
                             1914,
                             1915))
GROUP BY sf_events.type,
         sf_events.user_id



一年佳节是中秋，今夜清光不肯留。
莫道人间无此景，月明偏照故园愁


一年佳节又中秋，月色如银照客愁。
万里清光无处著，不知何事此身留。

天上人间一样秋，中元节后月如钩。
不知今夜清光好，只恐嫦娥也白头。

一年佳节又中秋，月色如银照伊容。
我知今夜清光好，无悔嫦娥也白头。


{
    "UCO":
    {
        "price": 32.14,
        "image": "http://storage.questrade.local/questrade/graph/UCO-30232435-01.png"
    },
    "DRIP":
    {
        "price": 15.43,
        "image": "http://storage.questrade.local/questrade/graph/DRIP-41337950-01.png"
    },
    "GME":
    {
        "price": 28.92,
        "image": "http://storage.questrade.local/questrade/graph/GME-19719-01.png"
    },
    "LABD":
    {
        "price": 18.7,
        "image": "http://storage.questrade.local/questrade/graph/LABD-31984585-01.png"
    },
    "FCX":
    {
        "price": 32.17,
        "image": "http://storage.questrade.local/questrade/graph/FCX-19329-01.png"
    },
    "SRTY":
    {
        "price": 50.72,
        "image": "http://storage.questrade.local/questrade/graph/SRTY-39925757-01.png"
    },
    "SOXS":
    {
        "price": 45.27,
        "image": "http://storage.questrade.local/questrade/graph/SOXS-41337960-01.png"
    },
    "SARK":
    {
        "price": 53.39,
        "image": "http://storage.questrade.local/questrade/graph/SARK-43620898-01.png"
    },
    "SQQQ":
    {
        "price": 41.84,
        "image": "http://storage.questrade.local/questrade/graph/SQQQ-39925756-01.png"
    },
    "FAZ":
    {
        "price": 21.33,
        "image": "http://storage.questrade.local/questrade/graph/FAZ-34856837-01.png"
    },
    "GDOT":
    {
        "price": 21.21,
        "image": "http://storage.questrade.local/questrade/graph/GDOT-20931-01.png"
    },
    "NEM":
    {
        "price": 43.17,
        "image": "http://storage.questrade.local/questrade/graph/NEM-29111-01.png"
    },
    "UPST":
    {
        "price": 27.53,
        "image": "http://storage.questrade.local/questrade/graph/UPST-33501967-01.png"
    },
    "DASH":
    {
        "price": 64.31,
        "image": "http://storage.questrade.local/questrade/graph/DASH-33418175-01.png"
    },
    "APP":
    {
        "price": 27.73,
        "image": "http://storage.questrade.local/questrade/graph/APP-35513422-01.png"
    },
    "COIN":
    {
        "price": 80.87,
        "image": "http://storage.questrade.local/questrade/graph/COIN-35476350-01.png"
    },
    "SYY":
    {
        "price": 83.89,
        "image": "http://storage.questrade.local/questrade/graph/SYY-37862-01.png"
    },
    "PATH":
    {
        "price": 14.64,
        "image": "http://storage.questrade.local/questrade/graph/PATH-35708037-01.png"
    },
    "MRVL":
    {
        "price": 49.74,
        "image": "http://storage.questrade.local/questrade/graph/MRVL-35708033-01.png"
    },
    "AMD":
    {
        "price": 85.45,
        "image": "http://storage.questrade.local/questrade/graph/AMD-6770-01.png"
    },
    "ITCI":
    {
        "price": 52,
        "image": "http://storage.questrade.local/questrade/graph/ITCI-5128858-01.png"
    }
}


"cURL error 52: Empty reply from server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api07.iq.questrade.com/v1/markets/candles/41894887?startTime=2022-09-09T20%3A31%3A00%2B00%3A00&endTime=2022-09-10T23%3A59%3A59%2B00%3A00&interval=HalfHour"


Error code : 1022 for symbol SQQQ (39925756)
Error code : 1022 for symbol AMD (6770)
Error code : 1022 for symbol GME (19719)
Error code : 1022 for symbol COIN (35476350)

     /** @var FilesystemAdapter $client */
        $client = $filesystemManager->disk('s3_local');




retailer.clienteling.mode
retailer.clienteling.customers.communication.blackout.is_enabled


COMMENT 'JS Fingerprint hashed to integer



{
    "chart":
    {
        "result":
        [
            {
                "meta":
                {
                    "currency": "USD",
                    "symbol": "APP",
                    "exchangeName": "NMS",
                    "instrumentType": "EQUITY",
                    "firstTradeDate": 1618493400,
                    "regularMarketTime": 1663272004,
                    "gmtoffset": -14400,
                    "timezone": "EDT",
                    "exchangeTimezoneName": "America/New_York",
                    "regularMarketPrice": 24.39,
                    "chartPreviousClose": 25.34,
                    "previousClose": 25.34,
                    "scale": 3,
                    "priceHint": 2,
                    "currentTradingPeriod":
                    {
                        "pre":
                        {
                            "timezone": "EDT",
                            "start": 1663228800,
                            "end": 1663248600,
                            "gmtoffset": -14400
                        },
                        "regular":
                        {
                            "timezone": "EDT",
                            "start": 1663248600,
                            "end": 1663272000,
                            "gmtoffset": -14400
                        },
                        "post":
                        {
                            "timezone": "EDT",
                            "start": 1663272000,
                            "end": 1663286400,
                            "gmtoffset": -14400
                        }
                    },
                    "tradingPeriods":
                    [
                        [
                            {
                                "timezone": "EDT",
                                "start": 1663248600,
                                "end": 1663272000,
                                "gmtoffset": -14400
                            }
                        ]
                    ],
                    "dataGranularity": "1m",
                    "range": "2m",
                    "validRanges":
                    [
                        "1d",
                        "5d",
                        "1mo",
                        "3mo",
                        "6mo",
                        "1y",
                        "2y",
                        "ytd",
                        "max"
                    ]
                },
                "timestamp":
                [
                    1663271880,
                    1663271940,
                    1663272000
                ],
                "indicators":
                {
                    "quote":
                    [
                        {
                            "open":
                            [
                                24.459999084472656,
                                24.405000686645508,
                                24.389999389648438
                            ],
                            "close":
                            [
                                24.405000686645508,
                                24.389999389648438,
                                24.389999389648438
                            ],
                            "high":
                            [
                                24.459999084472656,
                                24.420000076293945,
                                24.389999389648438
                            ],
                            "low":
                            [
                                24.3799991607666,
                                24.34000015258789,
                                24.389999389648438
                            ],
                            "volume":
                            [
                                0,
                                74508,
                                0
                            ]
                        }
                    ]
                }
            }
        ],
        "error": null
    }
}


{"chart":{"result":[{"meta":{"currency":"USD","symbol":"SRTY","exchangeName":"PCX","instrumentType":"ETF","firstTradeDate":1265898600,"regularMarketTime":1663272000,"gmtoffset":-14400,"timezone":"EDT","exchangeTimezoneName":"America\/New_York","regularMarketPrice":55.19,"chartPreviousClose":53.98,"previousClose":53.98,"scale":3,"priceHint":2,"currentTradingPeriod":{"pre":{"timezone":"EDT","end":1663248600,"start":1663228800,"gmtoffset":-14400},"regular":{"timezone":"EDT","end":1663272000,"start":1663248600,"gmtoffset":-14400},"post":{"timezone":"EDT","end":1663286400,"start":1663272000,"gmtoffset":-14400}},"tradingPeriods":[[{"timezone":"EDT","end":1663272000,"start":1663248600,"gmtoffset":-14400}]],"dataGranularity":"1m","range":"2m","validRanges":["1d","5d","1mo","3mo","6mo","1y","2y","5y","10y","ytd","max"]},"timestamp":[1663271880,1663271940,1663272000],"indicators":{"quote":[{"low":[55.150001525878906,55.17499923706055,55.189998626708984],"open":[55.150001525878906,55.17499923706055,55.189998626708984],"close":[55.220001220703125,55.189998626708984,55.189998626708984],"volume":[0,12716,0],"high":[55.22999954223633,55.25,55.189998626708984]}]}}],"error":null}}{"chart":{"result":[{"meta":{"currency":"USD","symbol":"GDOT","exchangeName":"NYQ","instrumentType":"EQUITY","firstTradeDate":1279805400,"regularMarketTime":1663272002,"gmtoffset":-14400,"timezone":"EDT","exchangeTimezoneName":"America\/New_York","regularMarketPrice":20.12,"chartPreviousClose":19.77,"previousClose":19.77,"scale":3,"priceHint":2,"currentTradingPeriod":{"pre":{"timezone":"EDT","start":1663228800,"end":1663248600,"gmtoffset":-14400},"regular":{"timezone":"EDT","start":1663248600,"end":1663272000,"gmtoffset":-14400},"post":{"timezone":"EDT","start":1663272000,"end":1663286400,"gmtoffset":-14400}},"tradingPeriods":[[{"timezone":"EDT","start":1663248600,"end":1663272000,"gmtoffset":-14400}]],"dataGranularity":"1m","range":"2m","validRanges":["1d","5d","1mo","3mo","6mo","1y","2y","5y","10y","ytd","max"]},"timestamp":[1663271880,1663271940,1663272000],"indicators":{"quote":[{"low":[20.06999969482422,20.06999969482422,20.1200008392334],"high":[20.09000015258789,20.1299991607666,20.1200008392334],"close":[20.06999969482422,20.1200008392334,20.1200008392334],"open":[20.079999923706055,20.079999923706055,20.1200008392334],"volume":[0,37612,0]}]}}],"error":null}}{"chart":{"result":[{"meta":{"currency":"USD","symbol":"PATH","exchangeName":"NYQ","instrumentType":"EQUITY","firstTradeDate":1619011800,"regularMarketTime":1663272002,"gmtoffset":-14400,"timezone":"EDT","exchangeTimezoneName":"America\/New_York","regularMarketPrice":15.17,"chartPreviousClose":14.33,"previousClose":14.33,"scale":3,"priceHint":2,"currentTradingPeriod":{"pre":{"timezone":"EDT","start":1663228800,"end":1663248600,"gmtoffset":-14400},"regular":{"timezone":"EDT","start":1663248600,"end":1663272000,"gmtoffset":-14400},"post":{"timezone":"EDT","start":1663

Good luck Karl, I will deeply miss working/days with you and wishing you all the best in the next step of your career!


            // $this->redisManager->hSet(
            //     TrackingService::KEY_TRACKING_RECENT_PRICE,
            //     $symbol->name,
            //     json_encode($result)
            // );


    # && echo "zend_extension=yasd\nyasd.debug_mode=remote\nyasd.remote_host=**********\nyasd.remote_port=9003\nyasd.open_extended_info=1" >> /etc/php/8.1/mods-available/yasd.ini \


        # && echo "\nxdebug.mode=debug\nxdebug.start_with_request=yes\nxdebug.client_host=**********\nxdebug.client_port=9003\nxdebug.log_level=0" >> /etc/php/8.1/mods-available/xdebug.ini



{
    "1": "| 🟢 [voluptas](http://127.0.0.1/graph/symbols/4827601) | 27.40 | [81.85](http://127.0.0.1/console/set-realtime-symbol/4827601)![](https://www.leiyang.ml/public/images/up.png) | **435.60**![](https://www.leiyang.ml/public/images/right-bottom.png) |"
}



2022-09-15 13:30:00.000000


SELECT dc.identifier
FROM sf_dashboard_component_mappings dcm
INNER JOIN sf_dashboard_components dc ON dcm.component_id = dc.id
WHERE (dcm.user_group = '1')
  AND (dcm.selling_mode = '1')
  AND (dcm.visibility = '1')
ORDER BY dcm.sequence ASC

store_id   week_schedule

stores  date_overrides


u.store = 1015

AC-390 The default value of config for 'Contacts Export' should be true for 3 tiers


    SELECT m.id,
       m.user_id,
       m.customer_id,
       m.task_category_id,
       m.status,
       m.type,
       m.details,
       m.resolution_note,
       m.resolution_date,
       m.reminder_date,
       m.last_reminder_date,
       m.created_at,
       m.updated_at,
       m.automated_type,
       m.parent_id,
       SUM(CASE WHEN m.status = 'unresolved' THEN 1 END) AS count_unresolved,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) = DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_due,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) < DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_overdue,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) > DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_upcoming,
       SUM(CASE WHEN m.status = 'resolved' THEN 1 END) AS count_resolved,
       SUM(CASE WHEN m.status = 'dismissed' THEN 1 END) AS count_dismissed,
       trx.trx_id AS rep_transaction_trx_id,
       rtrx.trx_thread_id AS retailer_transaction_thread_id,
       IF(u.locale IN ('ja_JP'), CONCAT(COALESCE(um2.meta_value, ''), ' ', COALESCE(um1.meta_value, '')), CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, ''))) AS repName,
       store.name AS storeName
FROM sf_task m
LEFT JOIN sf_task_rep_transaction trx ON m.id = trx.task_id
LEFT JOIN sf_task_retailer_transaction rtrx ON m.id = rtrx.task_id
INNER JOIN wp_users u ON m.user_id = u.ID
INNER JOIN sf_store store ON u.store = store.store_id
LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id
AND um1.meta_key = 'first_name'
LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id
AND um2.meta_key = 'last_name'
WHERE (m.`user_id` = '1')
  AND ((m.start_date IS NULL
        OR m.start_date <= now()))
GROUP BY user_id LIMIT 1


/
?

If user group == 1:
      /group-tasks/filters
If user group == 2: (it will return the filters for group/store tasks)
     /v2/managers/tasks/filters



/group-tasks/38/activities?


FbD2bT7/O4nsuK40tQVaJFXy1xiqA1eTlAQX53GBHEA=


/group-tasks/420799


"An exception occurred while executing 'SELECT m.id,m.store_id,m.title,m.details,m.start_date,m.reminder_date,m.auto_dismiss_date,m.status,m.customer_id,m.preferred_user_id,m.suggested_subject_line,m.Suggested_copy,m.created_at,m.updated_at, store.name AS storeName FROM sf_group_tasks m INNER JOIN sf_store store ON m.store_id = store_id.store_id WHERE (m.start_date IS NULL OR m.start_date <= now()) GROUP BY user_id LIMIT 1':

SQLSTATE[42S22]: Column not found: 1054 Unknown column 'store_id.store_id' in 'on clause'"


"An exception occurred while executing 'SELECT m.id,m.store_id,m.title,m.details,m.start_date,m.reminder_date,m.auto_dismiss_date,m.status,m.customer_id,m.preferred_user_id,m.suggested_subject_line,m.Suggested_copy,m.created_at,m.updated_at, IF(u.locale IN ('ja_JP'),
            CONCAT(COALESCE(um2.meta_value, ''), ' ', COALESCE(um1.meta_value, '')),
            CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, ''))) AS repName, store.name AS storeName FROM sf_group_tasks m LEFT JOIN wp_users u ON m.preferred_user_id = u.ID INNER JOIN sf_store store ON m.store_id = store_id.store_id LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id AND um1.meta_key = 'first_name' LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id AND um2.meta_key = 'last_name' WHERE (m.`store_id` = '1003') AND ((m.start_date IS NULL OR m.start_date <= now())) LIMIT 1':

SQLSTATE[42S22]: Column not found: 1054 Unknown column 'store_id.store_id' in 'on clause'"



SELECT m.id,
       m.user_id,
       m.customer_id,
       m.task_category_id,
       m.status,
       m.type,
       m.details,
       m.resolution_note,
       m.resolution_date,
       m.reminder_date,
       m.last_reminder_date,
       m.created_at,
       m.updated_at,
       m.automated_type,
       m.parent_id,
       SUM(CASE WHEN m.status = 'unresolved' THEN 1 END) AS count_unresolved,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) = DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_due,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) < DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_overdue,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) > DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_upcoming,
       SUM(CASE WHEN m.status = 'resolved' THEN 1 END) AS count_resolved,
       SUM(CASE WHEN m.status = 'dismissed' THEN 1 END) AS count_dismissed,
       trx.trx_id AS rep_transaction_trx_id,
       rtrx.trx_thread_id AS retailer_transaction_thread_id,
       IF(u.locale IN ('ja_JP'), CONCAT(COALESCE(um2.meta_value, ''), ' ', COALESCE(um1.meta_value, '')), CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, ''))) AS repName,
       store.name AS storeName
FROM sf_task m
LEFT JOIN sf_task_rep_transaction trx ON m.id = trx.task_id
LEFT JOIN sf_task_retailer_transaction rtrx ON m.id = rtrx.task_id
INNER JOIN wp_users u ON m.user_id = u.ID
INNER JOIN sf_store store ON u.store = store.store_id
LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id
AND um1.meta_key = 'first_name'
LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id
AND um2.meta_key = 'last_name'
WHERE (m.`user_id` = '1')
  AND ((m.start_date IS NULL
        OR m.start_date <= now()))
GROUP BY user_id LIMIT 1


"An exception occurred while executing 'SELECT m.id,m.store_id,m.title,m.details,m.start_date,m.reminder_date,m.auto_dismiss_date,m.status,m.customer_id,m.preferred_user_id,m.suggested_subject_line,m.Suggested_copy,m.created_at,m.updated_at, IF(u.locale IN ('ja_JP'),
            CONCAT(COALESCE(um2.meta_value, ''), ' ', COALESCE(um1.meta_value, '')),
            CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, ''))) AS repName, store.name AS storeName FROM sf_group_tasks m LEFT JOIN wp_users u ON m.preferred_user_id = u.ID INNER JOIN sf_store store ON m.store_id = sf_store.store_id LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id AND um1.meta_key = 'first_name' LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id AND um2.meta_key = 'last_name' WHERE (m.`store_id` = '1003') AND ((m.start_date IS NULL OR m.start_date <= now())) GROUP BY store_id LIMIT 1':

SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sf_store.store_id' in 'on clause'"

SELECT m.ID,
       m.user_login,
       m.user_nicename,
       m.user_email,
       m.user_status,
       m.display_name,
       m.user_alias,
       m.store,
       m.type,
       m.employee_id,
       m.group,
       m.selling_mode,
       m.isPhoto,
       m.locked_at,
       m.user_registered,
       m.locale,
       m.creation_source,
       m.shop_feed,
       m.updated_at,
       m.updated_by,
       m.sso_auth,
       first_name.meta_value AS first_name,
       last_name.meta_value AS last_name,
       COALESCE(TRIM(CONCAT(COALESCE(TRIM(first_name.meta_value), ''),' ', COALESCE(TRIM(last_name.meta_value), ''))), m.display_name) AS db_full_name,
       title.meta_value AS title,
       phone.meta_value AS phone,
       publish_phone.meta_value AS publish_phone,
       COALESCE(introduction.meta_value, '') AS introduction,
       COALESCE(GROUP_CONCAT(c.category_id), '') AS specialties,
       store.timezone AS store_timezone,
       locked.meta_value AS nb_locked_out,
       created_user.user_login AS created_user_name,
       updated_user.user_login AS updated_user_name,
       sf_store.store_id,
       sf_store.postal,
       sf_store.phone AS store_phone,
       sf_store.store_user_id,
       sf_store.retailer_store_id,
       sf_store.image_url AS store_image_url,
       sf_store.name AS store_name,
       sf_store.country,
       sf_store.region,
       sf_store.city,
       sf_store.address
FROM wp_users m
LEFT JOIN wp_usermeta first_name ON m.ID = first_name.user_id
AND first_name.meta_key = 'first_name'
LEFT JOIN wp_usermeta last_name ON m.ID = last_name.user_id
AND last_name.meta_key = 'last_name'
LEFT JOIN wp_usermeta title ON m.ID = title.user_id
AND title.meta_key = 'title'
LEFT JOIN wp_usermeta phone ON m.ID = phone.user_id
AND phone.meta_key = 'phone'
LEFT JOIN wp_usermeta publish_phone ON m.ID = publish_phone.user_id
AND publish_phone.meta_key = 'publish_phone'
LEFT JOIN wp_usermeta introduction ON introduction.user_id = m.ID
AND introduction.meta_key = 'rep_introduction'
LEFT JOIN sf_rep_product_selection_map MAP ON m.id = MAP.user_id
LEFT JOIN sf_categories c ON MAP.category_id = c.category_id
LEFT JOIN sf_store store ON m.store = store.store_id
LEFT JOIN wp_usermeta locked ON m.ID = locked.user_id
AND locked.meta_key = 'nb_locked_out'
LEFT JOIN sf_rep_onboarding rep_onboarding ON m.ID = rep_onboarding.wp_user_id
LEFT JOIN wp_users updated_user ON m.updated_by = updated_user.ID
LEFT JOIN sf_store sf_store ON m.store = sf_store.store_id
LEFT JOIN wp_users created_user ON rep_onboarding.created_by = created_user.ID
WHERE m.`ID` = '1'
GROUP BY m.id LIMIT 1 231018 16:03:43 79 Query


SELECT m.id,
       m.store_id,
       m.title,
       m.details,
       m.start_date,
       m.reminder_date,
       m.auto_dismiss_date,
       m.status,
       m.customer_id,
       m.preferred_user_id,
       m.suggested_subject_line,
       m.Suggested_copy,
       m.created_at,
       m.updated_at,
       IF(u.locale IN ('ja_JP'), CONCAT(COALESCE(um2.meta_value, ''), ' ', COALESCE(um1.meta_value, '')), CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, ''))) AS repName,
       store.name AS storeName
FROM sf_group_tasks m
LEFT JOIN wp_users u ON m.preferred_user_id = u.ID
INNER JOIN sf_store store ON m.store_id = store.store_id
LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id
AND um1.meta_key = 'first_name'
LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id
AND um2.meta_key = 'last_name'
WHERE (m.`store_id` = '1003')
  AND ((m.start_date IS NULL
        OR m.start_date <= now()))
GROUP BY store_id LIMIT 1


"m.id,m.user_id,m.customer_id,m.task_category_id,m.status,m.type,m.details,m.resolution_note,m.resolution_date,m.reminder_date,m.last_reminder_date,m.created_at,m.updated_at,m.automated_type,m.parent_id"


SELECT m.id,
       m.store_id,
       m.title,
       m.details,
       m.start_date,
       m.reminder_date,
       m.auto_dismiss_date,
       m.status,
       m.customer_id,
       m.preferred_user_id,
       m.suggested_subject_line,
       m.Suggested_copy,
       m.created_at,
       m.updated_at,
       SUM(CASE WHEN m.status = 'unresolved' THEN 1 END) AS count_unresolved,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) = DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_due,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) < DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_overdue,
       SUM(CASE WHEN m.status = 'unresolved'
           AND IF(m.reminder_date IS NOT NULL
                  AND DATE(m.reminder_date) > DATE(now())
                  AND m.status = "unresolved", TRUE, FALSE) = 1 THEN 1 END) AS count_upcoming,
       SUM(CASE WHEN m.status = 'resolved' THEN 1 END) AS count_resolved,
       SUM(CASE WHEN m.status = 'dismissed' THEN 1 END) AS count_dismissed,
       IF(u.locale IN ('ja_JP'), CONCAT(COALESCE(um2.meta_value, ''), ' ', COALESCE(um1.meta_value, '')), CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, ''))) AS repName,
       store.name AS storeName
FROM sf_group_tasks m
LEFT JOIN wp_users u ON m.preferred_user_id = u.ID
INNER JOIN sf_store store ON m.store_id = store.store_id
LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id
AND um1.meta_key = 'first_name'
LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id
AND um2.meta_key = 'last_name'
WHERE (m.`store_id` = '1003')
  AND ((m.start_date IS NULL
        OR m.start_date <= now()))
GROUP BY store_id LIMIT 1


"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoidGVzdHNfMTkwNjMiLCJzdWIiOjI1NSwiZXhwIjoxNjk3NjQ3MDA5LCJpYXQiOjE2OTc2NDY0MDksInJlZiI6MTcwMjQ4NDgwOX0.pIraw9FZQr60GJdOmmFW_QA6Z4_3W__Xp9Nf5d7T4CE"

{
    "unresolved":
    {
        "key": "unresolved",
        "name": "All Active",
        "query": "filter[status]=unresolved&sort=reminder_date",
        "count": "1",
        "type": "status"
    },
    "upcoming":
    {
        "key": "upcoming",
        "name": "Upcoming",
        "query": "filter[status]=unresolved&filter[is_upcoming]=1&sort=reminder_date",
        "count": 0,
        "type": "custom"
    },
    "due":
    {
        "key": "due",
        "name": "Due Today",
        "query": "filter[status]=unresolved&filter[is_due]=1&sort=reminder_date",
        "count": 0,
        "type": "custom"
    },
    "overdue":
    {
        "key": "overdue",
        "name": "Overdue",
        "query": "filter[status]=unresolved&filter[is_overdue]=1&sort=reminder_date",
        "count": "1",
        "type": "custom"
    },
    "dismissed":
    {
        "key": "dismissed",
        "name": "Dismissed",
        "query": "filter[status]=dismissed&",
        "count": 0,
        "type": "status"
    },
    "resolved":
    {
        "key": "resolved",
        "name": "Resolved",
        "query": "filter[status]=resolved&",
        "count": 0,
        "type": "status"
    }
}

https://chicos.api.dev.salesfloor.net/v2/managers/tasks/filters?filter%5Buser_ids%5D=1912%2C1810%2C1899%2C139%2C1315%2C1844%2C1853%2C1870%2C1532%2C1141%2C289%2C1113%2C1053%2C1104%2C1843%2C1140%2C286%2C288%2C536%2C610%2C284%2C537%2C611%2C285%2C539%2C1160%2C548%2C549%2C609%2C287%2C635%2C1915%2C1137%2C1903%2C1902%2C336%2C1898%2C1476%2C1478%2C1479%2C1867%2C1914%2C606%2C604%2C602%2C603%2C115%2C1125%2C1131%2C1134%2C1143%2C1149%2C1151%2C1904%2C1162%2C1145%2C1138%2C1401%2C1400%2C1139%2C1136%2C1135%2C1133%2C1129%2C1123%2C1091%2C1014%2C1029%2C1031%2C1033%2C1041%2C1043%2C1045%2C1051%2C1052%2C1057%2C1059%2C1120%2C1063%2C1055%2C1046%2C1039%2C1027%2C282%2C329%2C331%2C281%2C280%2C1901%2C1169%2C1273%2C1708%2C283%2C1716%2C1709%2C1242%2C1158%2C1168%2C1533%2C1534%2C1153%2C1156%2C1165%2C1816%2C560


{
    "unresolved":
    {
        "key": "unresolved",
        "name": "All Active",
        "query": "filter[status]=unresolved&sort=reminder_date&filter[preferred_user_id]=255",
        "count": "1",
        "type": "status"
    },
    "upcoming":
    {
        "key": "upcoming",
        "name": "Upcoming",
        "query": "filter[status]=unresolved&filter[is_upcoming]=1&sort=reminder_date&filter[preferred_user_id]=255",
        "count": 0,
        "type": "custom"
    },
    "due":
    {
        "key": "due",
        "name": "Due Today",
        "query": "filter[status]=unresolved&filter[is_due]=1&sort=reminder_date&filter[preferred_user_id]=255",
        "count": 0,
        "type": "custom"
    },
    "overdue":
    {
        "key": "overdue",
        "name": "Overdue",
        "query": "filter[status]=unresolved&filter[is_overdue]=1&sort=reminder_date&filter[preferred_user_id]=255",
        "count": "1",
        "type": "custom"
    },
    "dismissed":
    {
        "key": "dismissed",
        "name": "Dismissed",
        "query": "filter[status]=dismissed&filter[preferred_user_id]=255",
        "count": 0,
        "type": "status"
    },
    "resolved":
    {
        "key": "resolved",
        "name": "Resolved",
        "query": "filter[status]=resolved&filter[preferred_user_id]=255",
        "count": 0,
        "type": "status"
    }
}


SELECT m.ID,
       m.user_login,
       m.user_nicename,
       m.user_email,
       m.user_status,
       m.display_name,
       m.user_alias,
       m.store,
       m.type,
       m.employee_id,
       m.group,
       m.selling_mode,
       m.isPhoto,
       m.locked_at,
       m.user_registered,
       m.locale,
       m.creation_source,
       m.shop_feed,
       m.updated_at,
       m.updated_by,
       m.sso_auth,
       first_name.meta_value AS first_name,
       last_name.meta_value AS last_name,
       COALESCE(TRIM(CONCAT(COALESCE(TRIM(first_name.meta_value), ''),' ', COALESCE(TRIM(last_name.meta_value), ''))), m.display_name) AS db_full_name,
       title.meta_value AS title,
       phone.meta_value AS phone,
       publish_phone.meta_value AS publish_phone,
       COALESCE(introduction.meta_value, '') AS introduction,
       COALESCE(GROUP_CONCAT(c.category_id), '') AS specialties,
       store.timezone AS store_timezone,
       locked.meta_value AS nb_locked_out,
       created_user.user_login AS created_user_name,
       updated_user.user_login AS updated_user_name,
       sf_store.store_id,
       sf_store.postal,
       sf_store.phone AS store_phone,
       sf_store.store_user_id,
       sf_store.retailer_store_id,
       sf_store.image_url AS store_image_url,
       sf_store.name AS store_name,
       sf_store.country,
       sf_store.region,
       sf_store.city,
       sf_store.address
FROM wp_users m
LEFT JOIN wp_usermeta first_name ON m.ID = first_name.user_id
AND first_name.meta_key = 'first_name'
LEFT JOIN wp_usermeta last_name ON m.ID = last_name.user_id
AND last_name.meta_key = 'last_name'
LEFT JOIN wp_usermeta title ON m.ID = title.user_id
AND title.meta_key = 'title'
LEFT JOIN wp_usermeta phone ON m.ID = phone.user_id
AND phone.meta_key = 'phone'
LEFT JOIN wp_usermeta publish_phone ON m.ID = publish_phone.user_id
AND publish_phone.meta_key = 'publish_phone'
LEFT JOIN wp_usermeta introduction ON introduction.user_id = m.ID
AND introduction.meta_key = 'rep_introduction'
LEFT JOIN sf_rep_product_selection_map MAP ON m.id = MAP.user_id
LEFT JOIN sf_categories c ON MAP.category_id = c.category_id
LEFT JOIN sf_store store ON m.store = store.store_id
LEFT JOIN wp_usermeta locked ON m.ID = locked.user_id
AND locked.meta_key = 'nb_locked_out'
LEFT JOIN sf_rep_onboarding rep_onboarding ON m.ID = rep_onboarding.wp_user_id
LEFT JOIN wp_users updated_user ON m.updated_by = updated_user.ID
LEFT JOIN sf_store sf_store ON m.store = sf_store.store_id
LEFT JOIN wp_users created_user ON rep_onboarding.created_by = created_user.ID
WHERE m.`ID` = '1'
GROUP BY m.id LIMIT 1


SELECT m.id,
       m.store_id,
       m.title,
       m.details,
       m.start_date,
       m.reminder_date,
       m.auto_dismiss_date,
       m.status,
       m.customer_id,
       m.preferred_user_id,
       m.suggested_subject_line,
       m.Suggested_copy,
       m.created_at,
       m.updated_at,
       CONCAT(COALESCE(um1.meta_value, ''), ' ', COALESCE(um2.meta_value, '')) AS repName,
       COALESCE(store.name, '') AS storeName,
       trx.trx_id AS rep_transaction_trx_id,
       rtrx.trx_thread_id AS retailer_transaction_thread_id
FROM sf_group_tasks m
INNER JOIN wp_users u ON m.user_id = u.ID
LEFT JOIN sf_task_rep_transaction trx ON m.id = trx.task_id
LEFT JOIN sf_task_retailer_transaction rtrx ON m.id = rtrx.task_id
LEFT JOIN sf_store store ON u.store = store.store_id
LEFT JOIN wp_usermeta um1 ON u.ID = um1.user_id
AND um1.meta_key = 'first_name'
LEFT JOIN wp_usermeta um2 ON u.ID = um2.user_id
AND um2.meta_key = 'last_name'
WHERE (m.`status` = 'unresolved')
  AND (m.user_id = :userId)
ORDER BY reminder_date ASC LIMIT 10



{
    "meta":
    {
        "pages": 1,
        "total": 1
    },
    "links":
    {
        "self": "https://tests.api.dev.salesfloor.net/default/group-tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=product%2Cproduct_variant%2Casset&page%5Bnumber%5D=0&page%5Bsize%5D=10",
        "first": "https://tests.api.dev.salesfloor.net/default/group-tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=product%2Cproduct_variant%2Casset&page%5Bnumber%5D=0&page%5Bsize%5D=10",
        "prev": null,
        "next": null,
        "last": "https://tests.api.dev.salesfloor.net/default/group-tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=product%2Cproduct_variant%2Casset&page%5Bnumber%5D=0&page%5Bsize%5D=10"
    },
    "data":
    [
        {
            "type": "group-task",
            "id": "1",
            "attributes":
            {
                "store_id": "1003",
                "title": "Test Title",
                "details": "Test Detail",
                "start_date": "2023-04-01 00:00:23",
                "reminder_date": "2023-03-01 00:01:02",
                "auto_dismiss_date": "2024-01-01 00:01:02",
                "status": "unresolved",
                "customer_id": "1234",
                "preferred_user_id": "255",
                "suggested_subject_line": "Test Subject Line",
                "Suggested_copy": "Test Suggested Copy"
            },
            "relationships":
            {
                "product":
                {
                    "data":
                    [
                        {
                            "type": "product",
                            "id": "12001"
                        },
                        {
                            "type": "product",
                            "id": "12002"
                        }
                    ],
                    "links":
                    {
                        "self": "https://tests.api.dev.salesfloor.net/default/group-tasks/1/relationships/product",
                        "related": "https://tests.api.dev.salesfloor.net/default/group-tasks/1/product"
                    }
                },
                "product_variant":
                {
                    "data":
                    [
                        {
                            "type": "product",
                            "id": "sku01"
                        },
                        {
                            "type": "product",
                            "id": "sku02"
                        }
                    ],
                    "links":
                    {
                        "self": "https://tests.api.dev.salesfloor.net/default/group-tasks/1/relationships/product_variant",
                        "related": "https://tests.api.dev.salesfloor.net/default/group-tasks/1/product_variant"
                    }
                },
                "asset":
                {
                    "data":
                    [
                        {
                            "type": "asset",
                            "id": "1"
                        }
                    ],
                    "links":
                    {
                        "self": "https://tests.api.dev.salesfloor.net/default/group-tasks/1/relationships/asset",
                        "related": "https://tests.api.dev.salesfloor.net/default/group-tasks/1/asset"
                    }
                }
            },
            "links":
            {
                "self": "https://tests.api.dev.salesfloor.net/default/group-tasks/1"
            }
        }
    ],
    "included":
    [
        {
            "type": "product",
            "id": "12001",
            "attributes":
            {
                "product_id": "12001",
                "name": "name 01",
                "description": "description 01",
                "price": 10,
                "price_deal": 0,
                "price_old": 0,
                "vanity_data": "vanity",
                "deal_ratio": 1,
                "name2": "name 01",
                "available": 1,
                "deal_start_date": null,
                "deal_end_date": null,
                "gender": null,
                "min_age": null,
                "max_age": null,
                "retailer_sku": "",
                "arrival_date": null,
                "country": "CA",
                "brand": null,
                "img": "www.example1.com",
                "productUrl": "site.example1.com",
                "sku": "12001",
                "shortDescription": "description 01",
                "regularPrice": 10,
                "salePrice": 0,
                "oldPrice": 0,
                "SaleEndDate": "",
                "vanityData": "vanity",
                "thumbnailImage": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--IbCPBGXK--/f_auto/www.example1.com",
                "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--cR2zuQwe--/f_auto,q_90,w_250/www.example1.com",
                "ehf": 0,
                "additionalMedia":
                [
                    {
                        "mimeType": "image",
                        "thumbnailUrl": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--IbCPBGXK--/f_auto/www.example1.com",
                        "url": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--IbCPBGXK--/f_auto/www.example1.com",
                        "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--cR2zuQwe--/f_auto,q_90,w_250/www.example1.com"
                    }
                ]
            }
        },
        {
            "type": "product",
            "id": "12002",
            "attributes":
            {
                "product_id": "12002",
                "name": "name 02",
                "description": "description 02",
                "price": 20,
                "price_deal": 0,
                "price_old": 0,
                "vanity_data": "vanity",
                "deal_ratio": 1,
                "name2": "name 01",
                "available": 1,
                "deal_start_date": null,
                "deal_end_date": null,
                "gender": null,
                "min_age": null,
                "max_age": null,
                "retailer_sku": "",
                "arrival_date": null,
                "country": "CA",
                "brand": null,
                "img": "www.example2.com",
                "productUrl": "site.example2.com",
                "sku": "12002",
                "shortDescription": "description 02",
                "regularPrice": 20,
                "salePrice": 0,
                "oldPrice": 0,
                "SaleEndDate": "",
                "vanityData": "vanity",
                "thumbnailImage": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--iXGbO5Mr--/f_auto/www.example2.com",
                "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--BcME9Lfp--/f_auto,q_90,w_250/www.example2.com",
                "ehf": 0,
                "additionalMedia":
                [
                    {
                        "mimeType": "image",
                        "thumbnailUrl": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--iXGbO5Mr--/f_auto/www.example2.com",
                        "url": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--iXGbO5Mr--/f_auto/www.example2.com",
                        "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--BcME9Lfp--/f_auto,q_90,w_250/www.example2.com"
                    }
                ]
            }
        },
        {
            "type": "product",
            "id": "sku01",
            "attributes":
            {
                "product_id": "12001",
                "sku": "sku01",
                "name": "variant name 01",
                "description": "variant description 01",
                "price": "10.00",
                "available": "1",
                "product_url": "product_url_01",
                "image_url": "image_url_01",
                "price_deal": "5.00",
                "price_old": null,
                "deal_start_date": "0000-00-00",
                "deal_end_date": "0000-00-00",
                "brand": "brand",
                "is_default": "1",
                "arrival_date": "0000-00-00"
            }
        },
        {
            "type": "product",
            "id": "sku02",
            "attributes":
            {
                "product_id": "12002",
                "sku": "sku02",
                "name": "variant name 02",
                "description": "variant description 02",
                "price": "20.00",
                "available": "1",
                "product_url": "product_url_02",
                "image_url": "image_url_02",
                "price_deal": "5.20",
                "price_old": null,
                "deal_start_date": "0000-00-00",
                "deal_end_date": "0000-00-00",
                "brand": "brand",
                "is_default": "1",
                "arrival_date": "0000-00-00"
            }
        },
        {
            "type": "asset",
            "id": "1",
            "attributes":
            {
                "post_name": "testasset",
                "post_author": "1",
                "post_title": "test asset",
                "post_content": "<p>test content</p>",
                "post_status": "publish",
                "post_type": "sf-library",
                "post_mime_type": "",
                "post_date_gmt": "0000-00-00 00:00:00",
                "post_modified_gmt": "0000-00-00 00:00:00",
                "post_date": "0000-00-00 00:00:00",
                "post_modified": "0000-00-00 00:00:00",
                "label": null,
                "blurb": null,
                "image": null,
                "storefront_image": null,
                "facebook_image": null,
                "linkedin_image": null,
                "twitter_image": null,
                "facebook_type": null,
                "email": null,
                "storefront": null,
                "facebook": null,
                "linkedin": null,
                "twitter": null,
                "destination_url": null,
                "email_subject": null,
                "start_date": null,
                "end_date": null,
                "target_channel": "all",
                "virtual_fields":
                {
                    "label": "",
                    "permanentUrl": "https://tests.dev.salesfloor.net/?sf-library=testasset",
                    "assetType": "sf-library",
                    "status": "publish",
                    "feedIndex": "1",
                    "subject": "test asset",
                    "author": "Corporate",
                    "previewUrl": "/?sf-library=testasset",
                    "date": "-0001-11-30 00:00:00",
                    "startDate": "-",
                    "endDate": "-",
                    "startDatetime": null,
                    "endDatetime": null
                }
            }
        }
    ]
}


AC-256-salesfloor-modular-connect-phase-1-mvp-connect
AC-325-AC-342-backoffice-bluebox-modular-connect-tier1
AC-325-salesfloor-modular-connect-phase-2-configuration-effort
AC-352-appointment-hours-test-are-failing-because-of-the-daylight-time-saving
AC-389-allow-twig-in-email-templates_be
AC-390-the-default-value-of-config-for-contacts-export-should-be-true-for-3-tiers
AC-401-asset-html-page-displays-name-of-salesfloor-admin
AC-408-AC-501-codeception-test-improvement-phase-2
AC-408-AC-501-codeception-test-improvement-phase-2-new
AC-409-codeception-test-improvement-phase-1
AC-439_AC-522-new-added-favorites-does-not-appear-sorted-alphabetically-inside-contacts
AC-496-late-request-notification-email-is-crashing-for-multiple-retailers-production
AC-498-keep-email-sms-subscription-status-when-adding-a-customer-to-my-contacts-from-all-customers
AC-502-AC-503-move-the-tags-from-the-contact-overview-page-to-the-contact-info-page
AC-502-AC-512-be-support
AC-502-contact-management-ux-improvement-phase1-BE-platform
AC-513-Emails-for-the-Connect-2.0-API-not-translating-to-French
AC-518-all-customer-cannot-reschedule-or-cancel-an-appointment-by-text
AC-520-rtb-dan-m-inaccurate-kpi-reporting-for-share-emails-sent
AC-521-all-retailers-all-customers-are-not-in-alphabetic-order
AC-541-all-request-reply-time-kpi-is-calculated-incorrectly
AC-565-fix-broken-favorites-test-test-favorite-contacts-count-by-tags
FC-474-AC-603-be-update-mobile-app-to-hide-tasks-that-have-not-started-yet
PS-4031-AC-554-dxl-stg-uat-contact-not-saving-to-my-contacts-after-live-chat


https://tests.api.dev.salesfloor.net/default/group-tasks?filter[status]=unresolved&sort=reminder_date&include=product,product_variant,asset&page[number]=0&page[size]=10


/v2/group-tasks?filter[status]=unresolved&sort=reminder_date&page[number]=0&page[size]=3


{
    "unresolved":
    {
        "key": "unresolved",
        "name": "All Active",
        "query": "filter[status]=unresolved&sort=reminder_date",
        "count": "1",
        "type": "status"
    },
    "upcoming":
    {
        "key": "upcoming",
        "name": "Upcoming",
        "query": "filter[status]=unresolved&filter[is_upcoming]=1&sort=reminder_date",
        "count": 0,
        "type": "custom"
    },
    "due":
    {
        "key": "due",
        "name": "Due Today",
        "query": "filter[status]=unresolved&filter[is_due]=1&sort=reminder_date",
        "count": 0,
        "type": "custom"
    },
    "overdue":
    {
        "key": "overdue",
        "name": "Overdue",
        "query": "filter[status]=unresolved&filter[is_overdue]=1&sort=reminder_date",
        "count": "1",
        "type": "custom"
    },
    "dismissed":
    {
        "key": "dismissed",
        "name": "Dismissed",
        "query": "filter[status]=dismissed",
        "count": 0,
        "type": "status"
    },
    "resolved":
    {
        "key": "resolved",
        "name": "Resolved",
        "query": "filter[status]=resolved",
        "count": 0,
        "type": "status"
    }
}


SELECT m.id,
       m.store_id,
       m.title,
       m.details,
       m.start_date,
       m.reminder_date,
       m.auto_dismiss_date,
       m.status,
       m.customer_id,
       m.preferred_user_id,
       m.suggested_subject_line,
       m.suggested_copy,
       m.created_at,
       m.updated_at,
       TRUE AS is_due
FROM sf_group_tasks m
WHERE (m.`status` = 'unresolved')
  AND (m.store_id = 1003)
  AND (m.reminder_date IS NOT NULL
       AND m.status = 'unresolved'
       AND DATE(m.reminder_date) = DATE(now()))
ORDER BY reminder_date ASC LIMIT 10


 CPD-1189-Fix-codeception



{
    "track_total_hits": true,
    "query":
    {
        "bool":
        {
            "must":
            {
                "query_string":
                {
                    "fields":
                    [
                        "full_name^4",
                        "full_name.keyword^4",
                        "first_name^3",
                        "first_name.keyword^3",
                        "last_name^3",
                        "last_name.keyword^3",
                        "email^3",
                        "email.keyword^3",
                        "phone^2",
                        "phone.keyword^2",
                        "alternateEmail^2",
                        "alternateEmail.keyword^2",
                        "alternatePhone^2",
                        "alternatePhone.keyword^2",
                        "notes",
                        "notes.keyword",
                        "crm_id",
                        "crm_id.keyword",
                        "related_crm_id",
                        "related_crm_id.keyword",
                        "addresses.city^2",
                        "addresses.city.keyword^2",
                        "addresses.state^2",
                        "addresses.state.keyword^2",
                        "addresses.address_line_1",
                        "addresses.address_line_1.keyword",
                        "addresses.country",
                        "addresses.country.keyword",
                        "addresses.postal_code",
                        "addresses.postal_code.keyword"
                    ],
                    "query": "*mos*"
                }
            },
            "filter":
            {
                "bool":
                {
                    "must":
                    [
                        {
                            "term":
                            {
                                "user_id.keyword": "115"
                            }
                        },
                        {
                            "bool":
                            {
                                "must":
                                [],
                                "must_not":
                                [],
                                "should":
                                [
                                    {
                                        "term":
                                        {
                                            "addresses.state.keyword": "Pennsylvania"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "must_not":
                    [],
                    "should":
                    []
                }
            }
        }
    },
    "from": 0,
    "size": 10
}




[
    "transaction" => [
        [
            "trx_id" => "xxxx",
            "trx_total" => "100",
            "trx_type" => "sale",
            "trx_date" => "2020-01-02",
            "products" => [
                [
                    "product_id" => "1",
                    "sku" => 'xxxxx',
                    "name" => 'xxxxx',
                    "brand" => 'xxxxx',
                    "category" => 'xxxxx',
                ],
                [
                    "product_id" => "1",
                    "sku" => 'xxxxx',
                    "name" => 'xxxxx',
                    "brand" => 'xxxxx',
                    "category" => 'xxxxx',
                ]
            ]
        ]
    ]
]


{
    "bool":
    {
        "must":
        [
            "range":
            {
                "transactions.date":
                {
                    "gte": "2022-01-01",
                    "lt": "2024-01-01"
                }
            }

        ]
    }
}


services/src/Services/Appointments/Exceptions/AppointmentCancellationAlreadyCancelledException.php

real    0m32.204s
user    0m3.311s
sys     0m1.050s

real    0m19.230s
user    0m2.843s
sys     0m1.364s



{
    "query":
    {
        "bool":
        {
            "filter":
            {
                "bool":
                {
                    "must":
                    [
                        {
                            "term":
                            {
                                "tags.keyword": "1"
                            }
                        },
                        {
                            "term":
                            {
                                "favorite_contacts.keyword": "1"
                            }
                        },
                        {
                            "term":
                            {
                                "subcribtion_flag.keyword": 1
                            }
                        },
                        {
                            "bool":
                            {
                                "must":
                                [],
                                "must_not":
                                [],
                                "should":
                                [
                                    {
                                        "term":
                                        {
                                            "user_id.keyword": "1"
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool":
                            {
                                "must":
                                [],
                                "must_not":
                                [],
                                "should":
                                [
                                    {
                                        "term":
                                        {
                                            "addresses.city.keyword": "Montreal"
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool":
                            {
                                "must":
                                [],
                                "must_not":
                                [],
                                "should":
                                [
                                    {
                                        "term":
                                        {
                                            "addresses.state.keyword": "Montreal"
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "nested":
                            {
                                "path": "transactions",
                                "query":
                                {
                                    "bool":
                                    {
                                        "must":
                                        [
                                            {
                                                "range":
                                                {
                                                    "transactions.date":
                                                    {
                                                        "gte": "2020-06-01T00:00:00Z",
                                                        "lte": "2020-06-30T00:00:00Z"
                                                    }
                                                }
                                            },
                                            {
                                                "range":
                                                {
                                                    "transactions.total":
                                                    {
                                                        "gte": "200",
                                                        "lte": "300"
                                                    }
                                                }
                                            },
                                            {
                                                "terms":
                                                {
                                                    "transactions.products.brand.keyword":
                                                    [
                                                        "BRAND-20-2-1",
                                                        "BRAND-20-2-2"
                                                    ]
                                                }
                                            },
                                            {
                                                "terms":
                                                {
                                                    "transactions.products.category_id.keyword":
                                                    [
                                                        "category-id-20-2-1",
                                                        "category-id-20-2-2"
                                                    ]
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                    ],
                    "must_not":
                    [
                        {
                            "term":
                            {
                                "email.keyword": ""
                            }
                        }
                    ],
                    "should":
                    []
                }
            }
        }
    }
}



sf.sendgrid.queue_sends
boolean
false

--------------------------------------------------------
find all:
$r = $db->getMysqli()->query($query, MYSQLI_USE_RESULT);
while ($row = $r->fetch_assoc()) {
while ($row = $r->fetch_row()) {

CPD-1837 Improve the DB component used by importers

CPD-1837-improve-the-db-component-used-by-importers

CPD-1608 CPD-1612 Improve all tests for importers
CPD-1608-CPD-1612-improve-all-tests-for-importers


CPD-1968 Bloom - Transaction not Appearing on User's Dashboard & 30-day Report Email
CPD-1968-bloom-transaction-not-appearing-on-user-s-dashboard-30-day-report-email

CPD-1966 SF APP Prod: Unable to remove the second phone from the contact
CPD-1966-sf-app-prod-unable-to-remove-the-second-phone-from-the-contact

CPD-2003 Fabletics (maybe all) - Corporate Admin with no store assigned gets logged out of backoffice

CPD-2003-fabletics-maybe-all-corporate-admin-with-no-store-assigned-gets-logged-out-of-backoffice

CPD-1957 All - new Lookbooks aren't being created when attaching different variants of the same product ID
CPD-1957-all-new-lookbooks-aren-t-being-created-when-attaching-different-variants-of-the-same-product-id

CPD-2023 Create Corporate Task - Can't select specialties
CPD-2023-create-corporate-task-can-t-select-specialties



99 outbound
98 outbound
97 outbound
96 outbound
95 outbound
94 outbound
92 outbound
91 outbound
90 outbound
9 outbound
89 outbound
88 outbound
87 outbound
86 outbound
85 outbound
83 outbound
82 outbound
81 outbound
80 outbound
79 outbound
78 outbound
77 outbound
76 outbound
75 outbound
74 outbound
71 outbound
70 outbound
69 outbound
68 outbound
67 outbound
66 outbound
65 outbound
64 outbound
63 outbound
62 outbound
61 outbound
60 outbound
6 outbound
59 outbound
58 outbound
57 outbound
56 outbound
55 outbound
53 outbound
52 outbound
51 outbound
50 outbound
49 outbound
48 outbound
47 outbound
46 outbound
45 outbound
44 outbound
43 outbound
42 outbound
41 outbound
402 outbound
401 outbound
400 outbound
40 outbound
399 outbound
397 outbound
396 outbound
395 outbound
394 outbound
391 outbound
390 outbound
39 outbound
389 outbound
388 outbound
387 outbound
386 outbound
385 outbound
384 outbound
383 outbound
382 outbound
381 outbound
380 outbound
38 outbound
379 outbound
378 outbound
377 outbound
376 outbound
374 outbound
373 outbound
372 outbound
371 outbound
37 outbound
369 outbound
368 outbound
367 outbound
366 outbound
365 outbound
364 outbound
363 outbound
362 outbound
361 outbound
360 outbound
36 outbound
359 outbound
358 outbound
357 outbound
356 outbound
355 outbound
354 outbound
352 outbound
351 outbound
350 outbound
35 outbound
349 outbound
348 outbound
347 outbound
346 outbound
345 outbound
344 outbound
343 outbound
340 outbound
34 outbound
339 outbound
338 outbound
336 outbound
335 outbound
334 outbound
333 outbound
332 outbound
331 outbound
330 outbound
33 outbound
329 outbound
328 outbound
326 outbound
325 outbound
324 outbound
323 outbound
322 outbound
321 outbound
320 outbound
32 outbound
319 outbound
318 outbound
317 outbound
316 outbound
315 outbound
314 outbound
312 outbound
311 outbound
310 outbound
31 outbound
309 outbound
308 outbound
307 outbound
306 outbound
305 outbound
304 outbound
303 outbound
302 outbound
301 outbound
300 outbound
30 outbound
3 outbound
299 outbound
297 outbound
296 outbound
295 outbound
294 outbound
293 outbound
292 outbound
291 outbound
290 outbound
29 outbound
289 outbound
288 outbound
286 outbound
285 outbound
284 outbound
283 outbound
282 outbound
28 outbound
278 outbound
276 outbound
275 outbound
274 outbound
272 outbound
271 outbound
270 outbound
27 outbound
269 outbound
268 outbound
267 outbound
266 outbound
265 outbound
264 outbound
263 outbound
262 outbound
261 outbound
260 outbound
26 outbound
259 outbound
258 outbound
257 outbound
256 outbound
255 outbound
251 outbound
25 outbound
248 outbound
247 outbound
246 outbound
245 outbound
244 outbound
243 outbound
242 outbound
24 outbound
239 outbound
238 outbound
237 outbound
235 outbound
234 outbound
233 outbound
232 outbound
230 outbound
23 outbound
229 outbound
228 outbound
227 outbound
225 outbound
224 outbound
223 outbound
222 outbound
221 outbound
220 outbound
22 outbound
218 outbound
217 outbound
214 outbound
212 outbound
210 outbound
21 outbound
209 outbound
208 outbound
207 outbound
206 outbound
202 outbound
201 outbound
200 outbound
20 outbound
199 outbound
198 outbound
197 outbound
196 outbound
195 outbound
194 outbound
193 outbound
192 outbound
191 outbound
190 outbound
19 outbound
189 outbound
188 outbound
187 outbound
186 outbound
185 outbound
184 outbound
183 outbound
18 outbound
178 outbound
174 outbound
173 outbound
172 outbound
171 outbound
170 outbound
17 outbound
169 outbound
168 outbound
167 outbound
166 outbound
165 outbound
164 outbound
162 outbound
161 outbound
160 outbound
16 outbound
159 outbound
157 outbound
156 outbound
155 outbound
154 outbound
153 outbound
152 outbound
151 outbound
150 outbound
15 outbound
149 outbound
148 outbound
146 outbound
145 outbound
144 outbound
143 outbound
141 outbound
14 outbound
139 outbound
138 outbound
137 outbound
136 outbound
135 outbound
134 outbound
133 outbound
132 outbound
131 outbound
130 outbound
13 outbound
129 outbound
128 outbound
127 outbound
126 outbound
125 outbound
124 outbound
123 outbound
120 outbound
12 outbound
119 outbound
118 outbound
117 outbound
116 outbound
115 outbound
114 outbound
111 outbound
110 outbound
11 outbound
109 outbound
108 outbound
107 outbound
106 outbound
105 outbound
104 outbound
103 outbound
102 outbound
101 outbound
100 outbound


CPD-1608 CPD-1614 Clean up ES index and S3 bucket 'sf-tests' in _after

CPD-1608-CPD-1614-clean-up-es-index-and-s3-bucket-sf-tests-in-after

CPD-1608 CPD-1614 Clean up ES index and S3 bucket 'sf-tests' in _after

cpd-1608-cpd-1614-clean-up-es-index-and-s3-bucket-sf-tests-in-after

CPD-2054 Category ID should be a string
CPD-2054-category-id-should-be-a-string

CPD-2068 Chicos - corporate task with only 1 store selected was created for all stores

CPD-2068-chicos-corporate-task-with-only-1-store-selected-was-created-for-all-stores

CPD-2011 CPD-2015 Automatic task resolve on email or sms send

CPD-2011-CPD-2015-automatic-task-resolve-on-email-or-sms-send

CPD-2011 CPD-2050 Auto accept appointment requests - team mode
CPD-2011-CPD-2050-auto-accept-appointment-requests-team-mode

CPD-1608 CPD-2049 Improve all tests which related to Algolia search
CPD-1608-CPD-2049-improve-all-tests-which-related-to-algolia-search

CPD-1608 CPD-1820 Remove all tests in TestsController
CPD-1608-CPD-1820-remove-all-tests-in-tests-controller

CPD-2015 CPD-2156 Remove the cache when getting a task by id
CPD-2015-CPD-2156-remove-the-cache-when-getting-a-task-by-id

CPD-2015 CPD-2156 disable cache
CPD-2015-CPD-2156-disable-cache

"CPD-2204 All - tag filtering logic should be changed back to 'and'"
CPD-2204-all-tag-filtering-logic-should-be-changed-back-to-and

CPD-2256-filtering-tags-when-sharing-an-update-in-a-consistent-generic-way
CPD-2256 Filtering tags when sharing an update in a consistent/generic way

CPD-1608 CPD-2290 Fake class instead of codeception mockup: Elasticsearch
CPD-1608-CPD-2290-fake-class-instead-of-codeception-mockup-elasticsearch

CPD-1608 CPD-2325 Fake class instead of codeception mockup: MessageQueueClient
CPD-1608-CPD-2325-fake-class-instead-of-codeception-mockup-message-queue-client

CPD-1608 CPD-2396 Fake class instead of codeception mockup: Sendgrid and any others
CPD-1608-CPD-2396-fake-class-instead-of-codeception-mockup-sendgrid-and-any-others

CPD-2248 When Invalid data is inbounded for a Customer's Extended Attributes, the UI is breaking for some attributes

CPD-2248-when-invalid-data-is-inbounded-for-a-customer-s-extended-attributes-the-ui-is-breaking-for-some-attributes

CPD-2306 User Management list often loads incompletely on first load

CPD-2306-user-management-list-often-loads-incompletely-on-first-load


CPD-2452 Can not add appointment hours and date overrides

CPD-2452-can-not-add-appointment-hours-and-date-overrides

CPD-2457 Can not disable customer favourite

CPD-2457-can-not-disable-customer-favourite

CPD-2467 Can not claim any leads

CPD-2467-can-not-claim-any-leads

CPD-2469 All customers tab always returns empty when limited_visibility enabled
CPD-2469-all-customers-tab-always-returns-empty-when-limited-visibility-enabled

CPD-2476 Can not add to my contacts when the customer has no label
CPD-2476-can-not-add-to-my-contacts-when-the-customer-has-no-label

CPD-2477 Can not book an appointment with custom email/phone
CPD-2477-can-not-book-an-appointment-with-custom-email-phone

CPD-2483 only_full_group_by mode issue during the functional test

CPD-2483-only-full-group-by-mode-issue-during-the-functional-test

CPD-2490 Unable to send SMS appointment reminders
CPD-2490-unable-to-send-sms-appointment-reminders

CPD-2493 When processing incoming emails, customer activity feeds are not inserted into DB
CPD-2493-when-processing-incoming-emails-customer-activity-feeds-are-not-inserted-into-db

CPD-2498 Unable to switch default contact's address
CPD-2498-unable-to-switch-default-contact-s-address

CPD-2508 Fix insert failure in DB because of MySQL8 new flags

CPD-2508-fix-insert-failure-in-db-because-of-my-sql8-new-flags

CPD-2508-fix-insert-failure-in-db-because-of-my-sql8-new-flags-lookbook

CPD-2508 Fix insert failure in DB because of MySQL8 new flags lookbook

CPD-2508-fix-insert-failure-in-db-because-of-my-sql8-new-flags-text-attachment

CPD-2508 Fix insert failure in DB because of MySQL8 new flags text_attachment