<?php

namespace Salesfloor\Models;

use Carbon\Carbon;
use Salesfloor\Models\JsonApi\Relation;
use Salesfloor\Models\JsonApi\Join\RelationJoin;
use Salesfloor\Models\JsonApi\JoinType\OneToOne;
use Salesfloor\Models\Interfaces\JsonApiInterface;
use Salesfloor\Schemas\CustomerAttribute as CustomerAttributeSchema;
use Salesfloor\Schemas\CustomerAttributePanel as AttributePanelSchema;
use Salesfloor\Schemas\CustomerToCustomerAttribute as CustomerToCustomerAttributeSchema;

/**
 * Class CustomerAttribute
 *
 * @package Salesfloor\Models
 */
class CustomerToCustomerAttribute extends Base implements JsonApiInterface
{
    public const ID_FIELD = "id";

    /**
     * An id
     *
     * @var integer
     */
    public $id;

    /**
     * A customer id (contact)
     *
     * @var string
     */
    public $customer_id;

    /**
     * An attribute Id (id of customer_attributes table)
     *
     * @var string
     */
    public $attribute_id;

    /**
     * A value of attribute
     * This is dynamic type, could be int/decimal/string/json etc.
     * WESHOULD/COULD split to different columns/table in the future
     *
     * @var string
     */
    public $attribute_value;

    /**
     * A created_at
     *
     * @var datetime | string
     */
    public $created_at;

    /**
     * A updated_at
     *
     * @var datetime
     */
    public $updated_at;

    /**
     * @inheritdoc
     */
    public $fields = [
        'id'              => self::TYPE_PUBLIC,
        'customer_id'     => self::TYPE_PUBLIC,
        'attribute_id'    => self::TYPE_PUBLIC,
        'attribute_value' => self::TYPE_PUBLIC,
        'created_at'      => self::TYPE_PUBLIC,
        'updated_at'      => self::TYPE_PUBLIC,
    ];

    public function __construct()
    {
        parent::__construct();
        $this->created_at = Carbon::now('UTC')->toDateTimeString();
    }

    /**
     * Link model to Schema. Used by our 3rd party library (json-api)
     *
     * @return array
     */
    public function getSchemasProvider()
    {
        return [
            CustomerToCustomerAttribute::class => CustomerToCustomerAttributeSchema::class,
            CustomerAttribute::class           => CustomerAttributeSchema::class,
            CustomerAttributePanel::class      => AttributePanelSchema::class,
        ];
    }

    protected function prepareRelations()
    {
        $attribute = new Relation(
            new OneToOne(),
            "\Salesfloor\Models\CustomerAttribute",
            CustomerAttributeSchema::TYPE,
            new RelationJoin(
                'customer_attributes.manager',
                [
                    [
                        'source'      => 'attribute_id',
                        'destination' => 'id',
                    ],
                ]
            ),
            false
        );

        $this->relations = [
            CustomerAttributeSchema::TYPE   => $attribute,
        ];
    }
}
