<?php

namespace Salesfloor\Services\JsonApi;

use Neomerx\JsonApi\Contracts\Decoder\DecoderInterface;
use Neomerx\JsonApi\Contracts\Document\DocumentInterface;
use Neomerx\JsonApi\Factories\Factory;
use Salesfloor\Models\Base as BaseModel;
use Salesfloor\Schemas\Base as BaseSchema;
use Salesfloor\Services\JsonApi\Exceptions\JsonApiDecoderException;

/**
 * Class Decoder
 * This take json from the client, validate and parse it into a Resource that will be used by the application
 *
 * @package Salesfloor\Services\JsonApi
 */
class Decoder implements DecoderInterface
{
    private $method;

    private $schemas;

    // Used by SidePosting
    private $included;

    // May be Used by JSONAPI 1.2 (SidePosting)
    const KEYWORD_LID = 'lid';

    public function __construct($method, $schemas)
    {
        $this->method  = $method;
        $this->schemas = $schemas;
    }

    // https://github.com/InactiveProjects/limoncello/blob/develop/src/JsonApi/DocumentDecoder.php
    public function decode($data)
    {
        return $this->parseData($data);
    }

    private function parseData(array $data)
    {
        // first check if "data" is there (mandatory)
        $dataBlock = $this->getValue($data, DocumentInterface::KEYWORD_DATA, null);

        $this->parseIncluded($this->getValue($data, DocumentInterface::KEYWORD_INCLUDED, []));

        if (empty($dataBlock)) {
            // Delete can have empty body, in this case, resource is empty
            if ($this->isDelete()) {
                return null;
            }

            throw new JsonApiDecoderException("Data missing");
        }

        if ($this->isSingleResource($dataBlock)) {
            // This will throw an exception if not valid
            $this->validateSingleResource($dataBlock, true);

            return $this->parseSingleResource($dataBlock);
        } else {
            // Relationship
            // http://jsonapi.org/format/#crud-updating-relationships
            return $this->parseArrayResource($dataBlock);
        }
    }

    /**
     * This is mandatory to be able to link relationship to included section
     *
     * @param array $included
     *
     * @throws JsonApiDecoderException
     */
    private function parseIncluded(array $included)
    {
        // At the moment, included are only used by POST (create) and Patch (update)
        if (!$this->shouldParseRelationship()) {
            return;
        }

        foreach ($included as $include) {
            $type = $this->getValue($include, DocumentInterface::KEYWORD_TYPE, null);
            $id   = $this->getValue($include, DocumentInterface::KEYWORD_ID, null);
            $lid  = $this->getValue($include, self::KEYWORD_LID, null);

            // lid can be any value, but we need to make sure it doesn't conflict with an existing id
            // We use [$lid ?? $id] to store include into $this->included below.
            // It's only for relationships.
            if ($lid !== null) {
                $lid = 'l' . $lid;
            }

            if (empty($type)) {
                throw new JsonApiDecoderException("Missing type attributes");
            }
            if (empty($id) && empty($lid)) {
                throw new JsonApiDecoderException("id or lid are mandatory");
            }

            $this->included[$type][$lid ?? $id] = $include;
        }
    }

    private function parseArrayResource(array $data)
    {
        // TODO - Bulk operations
    }

    private function validateSingleResource(array $data, $isPrimary = true)
    {
        $id   = (int)$this->getValue($data, DocumentInterface::KEYWORD_ID);
        $type = $this->getValue($data, DocumentInterface::KEYWORD_TYPE);

        // TODO validate lid

        if (!$isPrimary) {
            // TODO
            // No validation on lid for now, since i can't really return it to the client for now.
            // We don't handle when relationship ID is passed only (n:m) relation.
        } else {
            // Main entity

            // Optional for insert (POST) - mandatory for update (PATCH)
            if ($this->isUpdate() || $this->isDelete()) {
                if ($id === null || is_integer($id) === false) {
                    throw new JsonApiDecoderException("id empty or not an integer");
                }
            } elseif ($this->isCreate()) {
                if ($id !== null) {
                    // Why you passed an id for create ?!
                }
            }
        }

        // Mandatory for single resource
        if (empty($type) || is_string($type) === false) {
            throw new JsonApiDecoderException("Type empty or not a string");
        }
    }

    /**
     * Parse single resource
     *
     * @param array $data
     *
     * @return JsonApiResource
     * @throws JsonApiDecoderException
     */
    private function parseSingleResource(array $data)
    {
        $id   = $this->getValue($data, DocumentInterface::KEYWORD_ID);
        $type = $this->getValue($data, DocumentInterface::KEYWORD_TYPE);

        $attributes    = $this->getValue($data, DocumentInterface::KEYWORD_ATTRIBUTES, []);
        $relationships = $this->getValue($data, DocumentInterface::KEYWORD_RELATIONSHIPS, []);
        $meta          = $this->getValue($data, DocumentInterface::KEYWORD_META, []);

        $lid = $this->getValue($data, 'lid', null); // JSONAPI 1.2 draft
        $virtualFields = $attributes['virtual_fields'] ?? [];

        $this->validateAttributes($attributes, $type);

        $relationshipResources = $this->parseRelationship($relationships);

        return new JsonApiResource($id, $type, $attributes, $relationshipResources, $meta, $lid, $virtualFields);
    }

    /**
     * Parse relationship (can be nested)
     *
     * @param array $relationships
     *
     * @return array
     * @throws JsonApiDecoderException
     */
    private function parseRelationship(array $relationships)
    {
        if (!$this->shouldParseRelationship()) {
            return [];
        }

        $relations = [];

        foreach ($relationships as $key => $relationship) {
            $data = $this->getValue($relationship, DocumentInterface::KEYWORD_DATA, []);
            if (!empty($data)) {
                // list of relation we need to create/update (side posting/patching)
                foreach ($data as $relData) {
                    $type = $this->getValue($relData, DocumentInterface::KEYWORD_TYPE, null);
                    $id   = $this->getValue($relData, DocumentInterface::KEYWORD_ID, null);
                    $lid  = $this->getValue($relData, self::KEYWORD_LID, null);

                    if ($lid !== null) {
                        $lid = 'l' . $lid;
                    }

                    $this->validateSingleResource($relData, false);

                    // TODO - If n:m relation, include is optional
                    $include = $this->included[$type][$lid ?? $id] ?? null;

                    if (empty($include)) {
                        throw new JsonApiDecoderException("Relationship include data missing");
                    }

                    $relations[$type][] = $this->parseSingleResource($include);
                }
            }
        }

        return $relations;
    }

    private function shouldParseRelationship(): bool
    {
        if ($this->isCreate() || $this->isUpdate()) {
            return true;
        }

        return false;
    }

    /**
     * This will validate and also cleanup reserved keyword if any
     *
     * @param $attributes
     * @param $type
     *
     * @throws Exceptions\JsonApiDecoderException
     */
    private function validateAttributes(&$attributes, $type)
    {
        $factory = new Factory();
        foreach ($this->schemas as $modelClass => $schemaClass) {
            /** @var BaseSchema $instance */
            $instance = new $schemaClass($factory);
            if ($type === $instance->getType()) {
                // First, validate that the attributes are good
                $instance->validateAttributes($attributes);

                // This must be done before the cleanUp, otherwise the "reserved" fields won't be copied
                $instance->cleanReservedKeywords($attributes);

                // This will check if attributes from client match model's fields (and are not protected)
                // It's more a cleanup than a validation that throw an exception
                /** @var BaseModel $model */
                $model = new $modelClass();
                $model->cleanAttributes($attributes);

                return;
            }
        }

        throw new JsonApiDecoderException("No match between type and schemas");
    }

    private function getValue(array $data, $key, $default = null)
    {
        return isset($data[$key]) ? $data[$key] : $default;
    }

    /**
     * Check if we have "id" or "type"
     *
     * POST (create) doesn't have and id
     * PATCH (update) have both
     *
     * @param array $data
     * @return bool
     */
    private function isSingleResource(array $data)
    {
        return isset($data[DocumentInterface::KEYWORD_ID]) || isset($data[DocumentInterface::KEYWORD_TYPE]);
    }

    private function isCreate()
    {
        return strtolower($this->method) === 'post';
    }

    private function isUpdate()
    {
        return strtolower($this->method) === 'patch';
    }

    private function isDelete()
    {
        return strtolower($this->method) === 'delete';
    }
}
