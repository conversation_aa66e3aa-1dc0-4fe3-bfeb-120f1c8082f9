#!/bin/sh

if [ $# -ne 0 ]; then
   exec "$@"
fi

# Enable Shell Debug
set -x

# Stop on errors
set -e

O_PLATFORM=yes
export O_USER O_PLATFORM

PROJROOT="/srv/www/sf_platform/current/"
PROGBASE="${PROJROOT}/vagrant/"
export PROJROOT PROJBASE
export LOG_SUFFIX="/"

# This is needed because of the hooks that are running in the container.
# see https://github.com/git/git/commit/8959555cee7ec045958f9b6dd62e541affb7e7d9
git config --global --add safe.directory /srv/www/sf_platform/current

if [ -e .git ]; then
  if [ -e hooks/docker ]; then
    chmod o+rx hooks/docker/*
    cp -Rfp hooks/docker/* .git/hooks/.
  fi
fi

if [ -n "${TELEPRESENCE_IP_ROUTE}" ]; then
  ip route add ${TELEPRESENCE_IP_ROUTE}
fi;

/usr/local/bin/entrypoint.sh &

[ -e '/usr/sbin/sshd' ] && mkdir -p /run/sshd && /usr/sbin/sshd

O_USER=www-data
printf '[>] Composer Install \n'
sudo -H -u "${O_USER}" composer install

sudo -H -u "${O_USER}" npm i

# InstallMaxmindDb.php doesn't work anymore but we don't need it since it's done (tmp) in the base image
# TODO Maxmind file is part of the base now, sadly not updated

# This cannot run twice
cd "${PROJROOT}/"
./robo db:install-missing-seeds || echo "\033[31m Error executing ./robo db:install-missing-seeds! \033[0m"

set +e
for mod in api instance-webserver widgets; do
  echo "[>] Provisioning module ${mod}"
  "${PROJROOT}/${mod}/build.sh"
  if [ "$?" != "0" ]; then
    echo -e "\n\n[WARNING] Provisioning of module ${mod} failed. Dev environment may not work properly\n\n"
  fi
done
set -e

# Mark this dev environment as using docker so Robo knows how to access it.
echo "docker" > .envtype

# Ensure to replace any npm ci hooks that would of overwite the git hooks
if [ -e .git ]; then
  if [ -e hooks/docker ]; then
    chmod o+rx hooks/docker/*
    cp -Rfp hooks/docker/* .git/hooks/.
  fi
fi

# DISABLE PHP XDebug unless X_DEBUG is set to true
if [ ! "X${X_DEBUG}" = "Xtrue" ]; then
   find /etc/php/. -type f -iname '*xdebug.ini' -exec sed -ri 's/^(.*)/; \1/g' {} \;
fi

X_DEBUG_STATUS=$(php -m -c | grep -i debug | wc -l)
if [ ${X_DEBUG_STATUS} -gt 0 ]; then
  echo -e "\n\n[WARNING] X DEBUG IS ENABLED. The http actions on the DEV environment may be slow\n\n"
fi;

printf 'CONTAINER IS READY\n'

