FROM ubuntu:jammy AS web

# Default IMAGE if not passed with a value inside the build stage
ENV DEBIAN_FRONTEND noninteractive

# Replace any other 7.3 reference in the file
ENV PHP_VERSION 8.3

# This is the first version that support php 8.3
ENV NEWRELIC_RELEASE *********

RUN	apt-get update && \
  apt-get install -y software-properties-common language-pack-en-base && \
  LC_ALL=en_US.UTF-8 add-apt-repository ppa:maxmind/ppa && \
  LC_ALL=en_US.UTF-8 add-apt-repository ppa:ondrej/php && \
  apt-get update && \
  apt-get install -y \
  libmaxminddb0 \
  libmaxminddb-dev \
  mmdb-bin \
  apache2 \
  apache2-utils \
  git \
  wget \
  curl \
  imagemagick \
  locales \
  php8.3 \
  php8.3-bcmath \
  php8.3-cli \
  php8.3-curl \
  # php8.3-geoip \ # Not supported anymore (TODO)
  php8.3-gd \
  php8.3-gnupg \
  php8.3-imap \
  php8.3-intl \
  php8.3-mbstring \
  php8.3-mysql \
  php8.3-xml \
  php8.3-zip \
  php8.3-fpm \
  php8.3-mailparse \
  logrotate \
  tar \
  tzdata \
  gzip \
  gettext-base \
  libxml2-dev \
  mysql-client \
  dnsutils \
  vim \
  bc \
  jq \
  cron \
  netcat && \
  apt-get remove -y software-properties-common \
  build-essential \
  gcc \
  cpp \
  # cpp-7 \ # Unable to locale package cpp-7, do we really need this ?
  g++ \
  build-essential \
  gnupg-utils \
  distro-info-data \
  gnupg \
  powermgmt-base \
  lsb-release \
  make

RUN wget -O composer-setup.php https://getcomposer.org/installer && \
  php composer-setup.php --version=2.6.5 --install-dir=/usr/local/bin && \
  mv -f /usr/local/bin/composer.phar /usr/local/bin/composer && \
  chmod +x /usr/local/bin/composer

# Install Supervisor.
RUN apt-get update && \
  apt-get install -y supervisor && \
  rm -rf /var/lib/apt/lists/*

RUN a2enmod headers && \
  a2enmod rewrite && \
  a2enmod expires

# Worker is preferred on high traffic webservers
RUN a2dismod mpm_prefork && \
  a2dismod mpm_worker && \
  a2enmod mpm_event && \
  a2enmod proxy && \
  a2enmod proxy_fcgi && \
  a2enmod substitute && \
  a2enmod ext_filter && \
  a2enconf php8.3-fpm

RUN	ln -snf /usr/sbin/php-fpm8.3 /usr/sbin/php-fpm

ARG OS_TYPE
RUN \
  if [ "${OS_TYPE}" = "linux" ]; then \
    curl -L https://download.newrelic.com/php_agent/archive/${NEWRELIC_RELEASE}/newrelic-php5-${NEWRELIC_RELEASE}-linux.tar.gz | tar -C /tmp -zx && \
    export NR_INSTALL_USE_CP_NOT_LN=1 && \
    export NR_INSTALL_SILENT=1 && \
    /tmp/newrelic-php5-*/newrelic-install install && \
    rm -rf /tmp/newrelic-php5-* /tmp/nrinstall*; \
  else \
    echo "Newrelic skipped"; \
  fi;

RUN apt-get update && \
  apt-get -o Dpkg::Options::="--force-confmiss" -y install php${PHP_VERSION}-dev && \
  cd /tmp && \
  composer require maxmind-db/reader && \
  cd "vendor/maxmind-db/reader/ext" && \
  phpize && \
  ./configure && \
  make clean && \
  make && \
  make install && \
  echo extension=maxminddb.so >> /etc/php/${PHP_VERSION}/mods-available/maxminddb.ini && \
  apt-get autoremove -y && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/* && \
  rm -fr /tmp/vendor && \
  cd /etc/php/${PHP_VERSION}/cli/conf.d && \
  ln -sf /etc/php/${PHP_VERSION}/mods-available/maxminddb.ini 20-maxminddb.ini && \
  cd /etc/php/${PHP_VERSION}/fpm/conf.d && \
  ln -sf /etc/php/${PHP_VERSION}/mods-available/maxminddb.ini 20-maxminddb.ini

# COPY --from=maxmind --chown=root:root /final /usr/share/GeoIP
COPY --chown=root:root ./GeoLite2-City.mmdb /usr/share/GeoIP/

# Use of iproute to allow routing through the telepresence container
# To allow communication inside kubernetes.
RUN apt-get update && \
    apt-get -y install mysql-client openssh-server openssh-client vim tcpdump net-tools libgnutls30 sudo fish && \
    apt-get -y install autoconf ruby-dev automake libffi7 libffi-dev netcat pv && \
    apt-get -y install php8.3-xdebug && \
    apt-get -y install iproute2 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install node 20 LTS (Supported until April 2026.
RUN apt-get update && \
    apt-get install -y ca-certificates curl gnupg && \
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_20.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
    apt-get update && \
    apt-get install -y nodejs && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*


RUN apt-get update && \
    apt-get install -y software-properties-common language-pack-en-base && \
    LC_ALL=en_US.UTF-8 && add-apt-repository ppa:maxmind/ppa && \
    apt-get update && \
    apt-get -y install libmaxminddb0 libmaxminddb-dev mmdb-bin && \
    apt-get remove -y software-properties-common language-pack-en-base && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# JSPM Installation fails on some installations for some reasons.
# https://geedew.com/What-does-unsafe-perm-in-npm-actually-do/
# https://www.vinayraghu.com/blog/npm-unsafe-perm
# RUN npm config set unsafe-perm true

USER root
WORKDIR /srv/www/sf_platform/current/

# Define default www-data userid for local
ARG UID=1000
ARG GID=1000

ENV UID=${UID:-1000}
ENV GID=${GID:-1000}

# Env value used in php-fpm must be set otherwise it will crash
ENV ENV=dev
ENV CUSTOMER=UNSET
ENV INFRA_CONFIG_PATH=UNSET
ENV OVERRIDES_PATH=UNSET

# X_DEBUG is in the .env file which docker compose loads
# Makefile also contains disable_xdebug or enable_xdebug command

ENV PHP_IDE_CONFIG=serverName=docker

RUN     sed -ri "s/:$GID:/:88888:/g" /etc/group
RUN     sed -ri "s/:$UID:/:88888:/g" /etc/passwd

RUN 	usermod -u $UID www-data && \
	groupmod -g $UID www-data && \
        chown $UID.$GID /var/www


RUN     sed -ri "s/20/9239829/g" /etc/passwd /etc/group

RUN	useradd -u 8888 -m -d /home/<USER>
        groupmod -g 8888 vagrant && \
        sed -ri "s/8888/$UID/g" /etc/passwd && \
        sed -ri "s/8888/$GID/g" /etc/group && \
        chown -R $UID.$GID /home/<USER>/

ADD dev/insecure_id_rsa /tmp/id_rsa
ADD dev/insecure_id_rsa.pub /tmp/id_rsa.pub

RUN mkdir -p /home/<USER>/.ssh/ \
    chmod 0700 /home/<USER>/.ssh/ && \
    cat /tmp/id_rsa.pub >> /home/<USER>/.ssh/authorized_keys \
        && cat /tmp/id_rsa.pub >> /home/<USER>/.ssh/id_rsa.pub \
        && cat /tmp/id_rsa >> /home/<USER>/.ssh/id_rsa \
        && chmod 0600 /home/<USER>/.ssh/id_rsa \
        && mkdir -p /root/.ssh/ \
        && chmod 0700 /root/.ssh/ \
        && cat /tmp/id_rsa >> /root/.ssh/id_rsa \
        && cat /tmp/id_rsa.pub >> /root/.ssh/id_rsa.pub \
        && rm -f /tmp/id_rsa* \
        && chmod 644 /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/id_rsa.pub /root/.ssh/id_rsa.pub \
    && chmod 400 /root/.ssh/id_rsa /home/<USER>/.ssh/id_rsa \
    && sed -ri '/^.*requiretty/ s/^(.*)//g' /etc/sudoers \
    && echo 'vagrant ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers \
    && echo 'www-data ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

COPY home/vagrant/ /home/<USER>/
RUN chown vagrant: /home/<USER>/.bashrc && \
    chmod 744 /home/<USER>/.bashrc


# #ssh
# RUN echo “root:training” | chpasswd
# RUN sed -i ‘s/prohibit-password/yes/’ /etc/ssh/sshd_config
# ADD ssh.tar /root/
# RUN chown -R root:root /root/.ssh;chmod -R 700 /root/.ssh
# RUN echo “StrictHostKeyChecking=no” >> /etc/ssh/ssh_config
# RUN mkdir /var/run/sshd
RUN	mkdir -p /logs/apache2 && \
	chmod u+rwx /logs/apache2
# End of WWW-DATA UID mods

# Cannot use jspm latest
# The latest jspm is not compatible with the nodejs version. jspm install was
# simply not functional.
RUN npm install jspm@0.16.55 -g
RUN npm install grunt -g

# Removing for now, since otherwise we can't make dev
#RUN npm install grunt-cli -g

RUN npm install gulp-cli -g
RUN npm install node-sass@9.0.0 -g
RUN npm install -g bower

RUN mkdir -p /srv/www/sf_platform/current/node_modules && \
     chown vagrant: /srv/www/sf_platform/current /srv/www/sf_platform/current/node_modules

COPY --chown=www-data ./certs/ /etc/ssl/certs/

# Copy apache & logrotate files
COPY	./etc/ /etc/
RUN	chmod 0644  /etc/logrotate.template/*


COPY	./entrypoint-dev.sh /usr/local/bin/entrypoint-dev.sh
COPY	./entrypoint.sh /usr/local/bin/entrypoint.sh
COPY	./deploy.sh /usr/local/bin/deploy.sh
COPY	./docker-healthcheck.sh /usr/local/bin/docker-healthcheck.sh

RUN	chmod +x /usr/local/bin/entrypoint-dev.sh \
                 /usr/local/bin/entrypoint.sh \
                 /usr/local/bin/docker-healthcheck.sh \
                 /usr/local/bin/deploy.sh

# Ensure a cron.csv file exist (mostly for local / testing)
# Next COPY statement would overwrite the cron.csv from the platform release.
RUN	ln -snf /srv/www/sf_platform /sf_platform
RUN	ln -snf /srv/www/sf_platform/current/ /vagrant_data

# Added current workdir for the robo command
ENV PATH=/srv/www/sf_platform/current/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

RUN mkdir -p /srv/www/sf_platform/current/node_modules /srv/www/sf_platform/current/node_modules/.bin /srv/www/sf_platform/current/node_modules/node-sass /srv/www/sf_platform/current/node_modules/node-sass/bin  && \
    chown vagrant: /srv/www/sf_platform/current /srv/www/sf_platform/current/node_modules /srv/www/sf_platform/current/node_modules/.bin /srv/www/sf_platform/current/node_modules/node-sass /srv/www/sf_platform/current/node_modules/node-sass/bin

COPY --chown=www-data ./certs/ /etc/ssl/certs/

RUN mkdir -p /var/www/.aws && \
    chown www-data: /var/www/.aws && \
    mkdir -p /opt/secrets/cloud-storage && \
    chown www-data: /opt/secrets/ /opt/secrets/cloud-storage

# Ensure to keep permissions
VOLUME [ "/var/www/.aws", "/opt/secrets/cloud-storage" ]

# Ensure a cron.csv file exist (mostly for local / testing)
# Next COPY statement would overwrite the cron.csv from the platform release.
RUN	ln -snf /srv/www/sf_platform /sf_platform
RUN	ln -snf /srv/www/sf_platform/current/ /vagrant_data

# Open port for httpd access
EXPOSE 80

# Added current workdir for the robo command
ENV PATH /srv/www/sf_platform/current/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# https://www.php.net/manual/en/install.fpm.configuration.php
ENV FPM_WWW_START_SERVERS=5 \
    FPM_WWW_MAX_CHILDREN=50 \
    FPM_WWW_MIN_SPARE_SERVERS=5 \
    FPM_WWW_MAX_SPARE_SERVERS=10 \
    FPM_WWW_MAX_REQUESTS=1000 \
    FPM_API_START_SERVERS=5 \
    FPM_API_MAX_CHILDREN=50 \
    FPM_API_MIN_SPARE_SERVERS=5 \
    FPM_API_MAX_SPARE_SERVERS=10 \
    FPM_API_MAX_REQUESTS=1000 \
    FPM_WIDGETS_START_SERVERS=5 \
    FPM_WIDGETS_MAX_CHILDREN=50 \
    FPM_WIDGETS_MIN_SPARE_SERVERS=5 \
    FPM_WIDGETS_MAX_SPARE_SERVERS=10 \
    FPM_WIDGETS_MAX_REQUESTS=1000 \
    # Extra log folder inside /logs/php-fpm/${PHP_FPM_LOG_SUFFIX_FOLDER}
    PHP_FPM_LOG_SUFFIX_FOLDER=logs \
    # The number of seconds after which an idle process will be killed. Used only when pm is set to ondemand.
    FPM_IDLE_TIMEOUT_IN_SEC=300

ENV OPCACHE_VALIDATE_TIMESTAMP=1
ENV OPCACHE_MEMORY=256

RUN mkdir -p /run/php

ENTRYPOINT ["/usr/local/bin/entrypoint-dev.sh"]
