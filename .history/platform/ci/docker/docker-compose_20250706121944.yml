version: "3.5"
services:
  traefik:
    image: traefik:v2.2.8
    # command: --web --docker --docker.domain=dev.salesfloor.net --logLevel=DEBUG
    command: -c /dev/null --web --docker --docker.domain=dev.salesfloor.net --logLevel=DEBUG --entryPoints="Name:https Address::443 TLS:/certs/apache-selfsigned.crt,/certs/apache-selfsigned.key" --entryPoints="Name:http Address::80 Redirect.EntryPoint:https"
    restart: unless-stopped
    networks:
      backend:
        ipv4_address: ************
        aliases:
          - traefik
          - tests.dev.salesfloor.net
          - tests.widgets.dev.salesfloor.net
      develop:
        ipv4_address: ************
        aliases:
          - traefik
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./.docker/traefik/traefik.toml:/traefik.toml
      - ./.docker/traefik/traefik.yml:/traefik.yml
      - ./certs/:/certs/:ro
    container_name: traefik-sf
    depends_on:
      - web

  # dnsmasq_host is used to resolve *.dev.salesfloor.net to 127.0.0.1 FROM THE HOST, not from containers
  # this dnsmasq_host has port 53 exposed on the host and the DNS resolver on the host must be configured
  # to use 127.0.0.1 as the nameserver to resolve *.dev.salesfloor.net (see ci/docker/dev/scripts/mac_setup.sh
  # for how that is setup on MacOS)
  #
  # The chain goes like this:
  # - Open https://saks.dev.salesfloor.net in your browser
  # - First the browser need to resolve the IP for saks.dev.salesfloor.net
  # - The DNS resolver on the host is configured to use 127.0.0.1 as the nameserver to resolve *.dev.salesfloor.net
  #   So it sends the DNS query to 127.0.0.1:53
  # - This dnsmasq_host container receives that DNS query (because its port 53 is exposed on the host)
  # - This dnsmasq_host container responds 127.0.0.1
  # - Now that the browser has an IP to connect to (127.0.0.1), it opens a connection on 127.0.0.1:443
  # - Since traefik has port 443 exposed on the host, it is the one receiving that connection.
  dnsmasq_host:
    build:
      context: .docker/dnsmasq/
    container_name: host-dns-server
    restart: unless-stopped
    privileged: true
    env_file:
      - .env
      - .env.local
    environment:
      OVERRIDE_SALESFLOOR_DNS:
    volumes:
      - ./.docker/dnsmasq/dnsmasq_host.d/:/etc/dnsmasq.ext/
    networks:
      backend:
        aliases:
          - dnsmasq-host
      develop:
        aliases:
          - dnsmasq-host

  # dnsmasq is used to resolve *.dev.salesfloor.net to ************ (traefik) from the containers
  # Other containers are configured to use it using the "dns: ..." config.
  # Note: *.dev.salesfloor.net should not be resolved to 127.0.0.1 from the containers because that
  # would mean the container would try to connect to itself. Especially in the "web" container,
  # connecting on 127.0.0.1:443 doesn't work because apache is only listening on 80 (apache is not
  # configured to handle HTTPS traffic because in kubernetes it should never receive HTTPS traffic).
  dnsmasq:
    build:
      context: .docker/dnsmasq/
    container_name: dns-server
    restart: unless-stopped
    privileged: true
    env_file:
      - .env
      - .env.local
    environment:
      OVERRIDE_SALESFLOOR_DNS:
    volumes:
       - ./.docker/dnsmasq/dnsmasq.d/:/etc/dnsmasq.ext/
    networks:
      backend:
        ipv4_address: ************
        aliases:
          - dnsmasq
      develop:
        ipv4_address: ************
        aliases:
          - dnsmasq

  web:
    build:
      context: .
      dockerfile: Dockerfile-lei
      args:
         UID: "${UID:?You must define UID variable for permissions handling, look at README}"
         GID: "${GID:?You must define GID variable for permissions handling, look at README}"
         ECR_IMAGE: ${ECR_IMAGE}
    extra_hosts:
        - 'host.docker.internal:host-gateway'
    # user: "${UID}:${GID}"
    # Allow to add telepresence routing
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
    restart: unless-stopped
    dns:
      - ************
      - ************

    depends_on:
      - dnsmasq

    links:
      - "dnsmasq:link-dnsmasq"

    env_file:
      - .env
      - .env.local
    environment:
      - OS_TYPE
    #  - AWS_MFA_SERIAL
    #  - AWS_DEFAULT_REGION
    #  - AWS_SESSION_TOKEN
    #  - AWS_ACCESS_KEY_ID
    #  - AWS_SECURITY_TOKEN

    volumes:
      - type: bind
        source: $HOME/.config/gcloud/application_default_credentials.json
        target: /var/www/.config/gcloud/application_default_credentials.json
      - ./entrypoint.sh:/usr/local/bin/entrypoint.sh:ro
      - nfsmount:/srv/www/sf_platform/current/:cached
      - ./logs/web/:/logs/:rw
      - mysql8-run:/var/run/mysqld
      #apache2/:rw

    #environment:
    #  - AWS_SECRET_ACCESS_KEY
    #  - AWS_MFA_SERIAL
    #  - AWS_DEFAULT_REGION
    #  - AWS_SESSION_TOKEN
    #  - AWS_ACCESS_KEY_ID
    #  - AWS_SECURITY_TOKEN

    networks:
      develop:
        aliases:
          - web
          - buckle.dev.salesfloor.net
          - chicos.dev.salesfloor.net
      backend:
        aliases:
          - web
          - buckle.dev.salesfloor.net
          - chicos.dev.salesfloor.net

  varnish:
    build:
      context: ../docker-varnish/
      dockerfile: Dockerfile
    ports:
      - "6081:6081"
    labels:
     - "traefik.enable=true"
     - "traefik.http.middlewares.https_redirect.redirectscheme.scheme=https"
     - "traefik.http.middlewares.https_redirect.redirectscheme.permanent=true"

     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_http_varnish.rule=HostRegexp(`dev.salesfloor.net`,`{subdomain:.+}.{subdomain:.+}.dev.salesfloor.net`,`{subdomain:.+}.dev.salesfloor.net`,`{subdomain:.+}.salesfloor.net`,`{subdomain:.+}.dev.salesfloor-ecom.net`)"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_http_varnish.entrypoints=http"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_http_varnish.priority=10"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_http_varnish.middlewares=https_redirect"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_http_varnish.service=${COMPOSE_PROJECT_NAME}_http_varnish"
     - "traefik.http.services.${COMPOSE_PROJECT_NAME}_http_varnish.loadbalancer.server.port=6081"

     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_https_varnish.rule=HostRegexp(`dev.salesfloor.net`,`{subdomain:.+}..{subdomain:.+}.dev.salesfloor.net`,`{subdomain:.+}.dev.salesfloor.net`,`{subdomain:.+}.salesfloor.net`,`{subdomain:.+}.dev.salesfloor-ecom.net`)"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_https_varnish.entrypoints=https"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_https_varnish.priority=10"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_https_varnish.tls=true"
     - "traefik.http.routers.${COMPOSE_PROJECT_NAME}_https_varnish.service=${COMPOSE_PROJECT_NAME}_https_varnish"
     - "traefik.http.services.${COMPOSE_PROJECT_NAME}_https_varnish.loadbalancer.server.port=6081"

    # Otherwise, you will get "web" could not be resolved to an IP from passthrough.vcl
    depends_on:
      - web

    # web container needs to access api container
    # but use a URL fed to it by browser on host
    # this will re-map the DNS to prevent localhost
    # note this doesn't work in Swarm or in ECS, only for local dev
    # external_links:
    #  - web:example.com
    restart: unless-stopped
    env_file:
      - .env
      - .env.local
    networks:
      - backend
      - develop

  redis:
    image: redis:6-alpine3.17
    ports:
     - "127.0.0.1:16379:6379"
    restart: unless-stopped
    networks:
      backend:
        ipv4_address: ************
        aliases:
          - redis
          - redis.salesfloor
          - stg-cache-v01.wzf9qc.0001.use1.cache.amazonaws.com
    dns:
      - ************
    env_file:
      - .env
      - .env.local

  cron-supervisor:
    image: ${COMPOSE_PROJECT_NAME}_web
    stop_grace_period: 1m
    # user: "${UID}:${GID}"
    dns:
      - ************
    env_file:
      - .env
      - .env.local
    environment:
      CRON_TYPE: supervisor
      CRON_HISTORY: "true"
      CUSTOMER: ${CUSTOMER-}
    volumes:
      - ./entrypoint.sh:/usr/local/bin/entrypoint.sh:ro
      - ./logs/cron/:/logs/:rw
      - ./scripts:/scripts:ro
    networks:
      - backend

# Example how to run a locally cron command
#  cron-run-sample:
#    image: ${COMPOSE_PROJECT_NAME}_web
#    command:
#       - "php"
#       - "DropOldTempTables.php"
#       - "${CUSTOMER}-${ENV}"
#    stop_grace_period: 1m
#    dns:
#      - ************
#    env_file:
#      - .env
#
#    environment:
#      CRON_NAME: ${CUSTOMER}-${ENV}
#      CRON_TYPE: cronmanual
#      CUSTOMER: ${CUSTOMER}
#      WORK_DIR: "/srv/www/sf_platform/current/api/app/crons"
#
#    volumes:
#      - ./entrypoint.sh:/usr/local/bin/entrypoint.sh:ro
#      - ./logs/cron/:/logs/:rw
#      - ./scripts:/scripts:ro
#    networks:
#      - backend

volumes:
  dynamodb:
    driver: "local"
  mysql:
    driver: "local"
  mysql-run:
    driver: "local"
  nfsmount:
    driver: "local"
  mysql8:
    driver: "local"
  mysql8-run:
    driver: "local"

networks:
    develop:
        name: ${COMPOSE_PROJECT_NAME}_develop
        ipam:
          config:
            - subnet: **********/16
    backend:
        name: ${COMPOSE_PROJECT_NAME}_develop_backend
        ipam:
          config:
            - subnet: **********/16
