# Generic configurations
COMPOSE_CONVERT_WINDOWS_PATHS=1
COMPOSE_HTTP_TIMEOUT=300
COMPOSE_PROJECT_NAME=sf

TMPDIR=/tmp/

# Environment configuration flag
ENV=dev

### APACHE ###################################################################
APACHE_RUN_USER=www-data
APACHE_RUN_GROUP=www-data

# In AWS/GCP This goes to /dev/null unless added as environment variables
# This help reducing Costs
APACHE_STDOUT_FILE=/dev/stdout

### PHPL ###################################################################
OPCACHE_ENABLED=1
OPCACHE_STRINGS_BUFFER=64
OPCACHE_MEMORY=512M

### VARNISH ###################################################################
VARNISH_PASSTHROUGH=true

### MYSQL ###################################################################

MYSQL_HOST="mysql"
MYSQL_DATABASE=wordpress
MYSQL_PORT=3306
MYSQL_HOST=mysql
MYSQL_USER=wordpress
MYSQL_PASSWORD=AvFAN4LpKl1EYH
MYSQL_ROOT_PASSWORD=123456

# Default PHP Memory Limit
PHP_MEMORY_LIMIT=256M

# Local ec2 instance id simulate variable
DOCKER=true
EC2_INSTANCE_ID=local
NEWRELIC_LICENCE_KEY=local

# To use Production Image instead of rebuilding it or override using env variables
# Note: This image has been added to ensure stability due to php7.4 packages being removed
# from the Debian Repositories due to EOL.
# We use the pre-built image from Build, that is being used for Lower and Production environments

ECR_IMAGE=lei-base

# If we use sf_base_web we might have faster setup, after initial build.
# But in some cases it give the following error:
# Error response from daemon: pull access denied for sf_base_web, repository does not exist or may require....
# 'docker login': denied: requested access to the resource is denied
# Commenting as this was mostly made to optimize the biuld process by using the latest built image from
# an internal GCP or AWS Repository
# ECR_IMAGE=sf_base_web

# DYNAMODB Endpoint
DYNAMO_ENDPOINT=http://dynamodb:8000

# Default to False
X_DEBUG=false

# On Development we use dynamic
# It should make it faster, but use more memory
FPM_MODE=dynamic
# dynamic
#ondemand

FPM_API_MAX_CHILDREN=50
FPM_API_MAX_SPARE_SERVERS=40
FPM_API_START_SERVERS=20
FPM_API_MAX_REQUESTS=1000
FPM_API_MIN_SPARE_SERVERS=20

# Required for GCP Pub/Sub
GOOGLE_APPLICATION_CREDENTIALS=/var/www/.config/gcloud/application_default_credentials.json

VARNISH_EXTRA_PARAMS=-p http_req_hdr_len=16k -p http_resp_hdr_len=64k -p http_resp_size=16k

# By default this is set to false, end-user should override in the
# .env.local
PLAYGROUND=false
PLAYGROUND_USER=${USER}

# Allow the Playground Services (k8s) ip cidr range via the telepresence container
# If playground is enabled
TELEPRESENCE_IP_ROUTE=**********/16 via ************
