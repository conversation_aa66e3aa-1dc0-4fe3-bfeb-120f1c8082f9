# Default IMAGE if not passed with a value inside the build stage
ARG ECR_IMAGE=sf_base_web

FROM ${ECR_IMAGE} AS BUILDER

# Use of iproute to allow routing through the telepresence container
# To allow communication inside kubernetes.

# JSPM Installation fails on some installations for some reasons.
# https://geedew.com/What-does-unsafe-perm-in-npm-actually-do/
# https://www.vinayraghu.com/blog/npm-unsafe-perm
# RUN npm config set unsafe-perm true

USER root
WORKDIR /srv/www/sf_platform/current/

# Define default www-data userid for local
ARG UID=1000
ARG GID=1000

ENV UID ${UID:-1000}
ENV GID ${GID:-1000}

# Env value used in php-fpm must be set otherwise it will crash
ENV ENV=dev
ENV CUSTOMER=UNSET
ENV INFRA_CONFIG_PATH=UNSET
ENV OVERRIDES_PATH=UNSET

# X_DEBUG is in the .env file which docker compose loads
# Makefile also contains disable_xdebug or enable_xdebug command

ENV PHP_IDE_CONFIG serverName=docker

RUN     sed -ri "s/:$GID:/:88888:/g" /etc/group
RUN     sed -ri "s/:$UID:/:88888:/g" /etc/passwd

RUN 	usermod -u $UID www-data && \
	groupmod -g $UID www-data && \
        chown $UID.$GID /var/www


RUN     sed -ri "s/20/9239829/g" /etc/passwd /etc/group

RUN	useradd -u 8888 -m -d /home/<USER>
        groupmod -g 8888 vagrant && \
        sed -ri "s/8888/$UID/g" /etc/passwd && \
        sed -ri "s/8888/$GID/g" /etc/group && \
        chown -R $UID.$GID /home/<USER>/

ADD dev/insecure_id_rsa /tmp/id_rsa
ADD dev/insecure_id_rsa.pub /tmp/id_rsa.pub

RUN mkdir -p /home/<USER>/.ssh/ \
    chmod 0700 /home/<USER>/.ssh/ && \
    cat /tmp/id_rsa.pub >> /home/<USER>/.ssh/authorized_keys \
        && cat /tmp/id_rsa.pub >> /home/<USER>/.ssh/id_rsa.pub \
        && cat /tmp/id_rsa >> /home/<USER>/.ssh/id_rsa \
        && chmod 0600 /home/<USER>/.ssh/id_rsa \
        && mkdir -p /root/.ssh/ \
        && chmod 0700 /root/.ssh/ \
        && cat /tmp/id_rsa >> /root/.ssh/id_rsa \
        && cat /tmp/id_rsa.pub >> /root/.ssh/id_rsa.pub \
        && rm -f /tmp/id_rsa* \
        && chmod 644 /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/id_rsa.pub /root/.ssh/id_rsa.pub \
    && chmod 400 /root/.ssh/id_rsa /home/<USER>/.ssh/id_rsa \
    && sed -ri '/^.*requiretty/ s/^(.*)//g' /etc/sudoers \
    && echo 'vagrant ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers \
    && echo 'www-data ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

COPY home/vagrant/ /home/<USER>/
RUN chown vagrant: /home/<USER>/.bashrc && \
    chmod 744 /home/<USER>/.bashrc


# #ssh
# RUN echo “root:training” | chpasswd
# RUN sed -i ‘s/prohibit-password/yes/’ /etc/ssh/sshd_config
# ADD ssh.tar /root/
# RUN chown -R root:root /root/.ssh;chmod -R 700 /root/.ssh
# RUN echo “StrictHostKeyChecking=no” >> /etc/ssh/ssh_config
# RUN mkdir /var/run/sshd
RUN	mkdir -p /logs/apache2 && \
	chmod u+rwx /logs/apache2
# End of WWW-DATA UID mods

# Cannot use jspm latest
# The latest jspm is not compatible with the nodejs version. jspm install was
# simply not functional.
RUN npm install jspm@0.16.55 -g
RUN npm install grunt -g

# Removing for now, since otherwise we can't make dev
#RUN npm install grunt-cli -g

RUN npm install gulp-cli -g
RUN npm install node-sass@9.0.0 -g
RUN npm install -g bower

RUN mkdir -p /srv/www/sf_platform/current/node_modules && \
     chown vagrant: /srv/www/sf_platform/current /srv/www/sf_platform/current/node_modules

COPY --chown=www-data ./certs/ /etc/ssl/certs/

# Copy apache & logrotate files
COPY	./etc/ /etc/
RUN	chmod 0644  /etc/logrotate.template/*


COPY	./entrypoint-dev.sh /usr/local/bin/entrypoint-dev.sh
COPY	./entrypoint.sh /usr/local/bin/entrypoint.sh
COPY	./deploy.sh /usr/local/bin/deploy.sh
COPY	./docker-healthcheck.sh /usr/local/bin/docker-healthcheck.sh

RUN	chmod +x /usr/local/bin/entrypoint-dev.sh \
                 /usr/local/bin/entrypoint.sh \
                 /usr/local/bin/docker-healthcheck.sh \
                 /usr/local/bin/deploy.sh

# Ensure a cron.csv file exist (mostly for local / testing)
# Next COPY statement would overwrite the cron.csv from the platform release.
RUN	ln -snf /srv/www/sf_platform /sf_platform
RUN	ln -snf /srv/www/sf_platform/current/ /vagrant_data

# Added current workdir for the robo command
ENV PATH /srv/www/sf_platform/current/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

RUN mkdir -p /srv/www/sf_platform/current/node_modules /srv/www/sf_platform/current/node_modules/.bin /srv/www/sf_platform/current/node_modules/node-sass /srv/www/sf_platform/current/node_modules/node-sass/bin  && \
    chown vagrant: /srv/www/sf_platform/current /srv/www/sf_platform/current/node_modules /srv/www/sf_platform/current/node_modules/.bin /srv/www/sf_platform/current/node_modules/node-sass /srv/www/sf_platform/current/node_modules/node-sass/bin

COPY --chown=www-data ./certs/ /etc/ssl/certs/

RUN mkdir -p /var/www/.aws && \
    chown www-data: /var/www/.aws && \
    mkdir -p /opt/secrets/cloud-storage && \
    chown www-data: /opt/secrets/ /opt/secrets/cloud-storage

# Ensure to keep permissions
VOLUME [ "/var/www/.aws", "/opt/secrets/cloud-storage" ]

# Ensure a cron.csv file exist (mostly for local / testing)
# Next COPY statement would overwrite the cron.csv from the platform release.
RUN	ln -snf /srv/www/sf_platform /sf_platform
RUN	ln -snf /srv/www/sf_platform/current/ /vagrant_data

# Open port for httpd access
EXPOSE 80

# Added current workdir for the robo command
ENV PATH /srv/www/sf_platform/current/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# https://www.php.net/manual/en/install.fpm.configuration.php
ENV FPM_WWW_START_SERVERS=5 \
    FPM_WWW_MAX_CHILDREN=50 \
    FPM_WWW_MIN_SPARE_SERVERS=5 \
    FPM_WWW_MAX_SPARE_SERVERS=10 \
    FPM_WWW_MAX_REQUESTS=1000 \
    FPM_API_START_SERVERS=5 \
    FPM_API_MAX_CHILDREN=50 \
    FPM_API_MIN_SPARE_SERVERS=5 \
    FPM_API_MAX_SPARE_SERVERS=10 \
    FPM_API_MAX_REQUESTS=1000 \
    FPM_WIDGETS_START_SERVERS=5 \
    FPM_WIDGETS_MAX_CHILDREN=50 \
    FPM_WIDGETS_MIN_SPARE_SERVERS=5 \
    FPM_WIDGETS_MAX_SPARE_SERVERS=10 \
    FPM_WIDGETS_MAX_REQUESTS=1000 \
    # Extra log folder inside /logs/php-fpm/${PHP_FPM_LOG_SUFFIX_FOLDER}
    PHP_FPM_LOG_SUFFIX_FOLDER=logs \
    # The number of seconds after which an idle process will be killed. Used only when pm is set to ondemand.
    FPM_IDLE_TIMEOUT_IN_SEC=300

ENV OPCACHE_VALIDATE_TIMESTAMP 1
ENV OPCACHE_MEMORY 256

RUN mkdir -p /run/php

ENTRYPOINT ["/usr/local/bin/entrypoint-dev.sh"]
