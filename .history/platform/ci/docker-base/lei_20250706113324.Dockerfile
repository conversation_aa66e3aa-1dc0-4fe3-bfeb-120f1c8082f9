FROM alpine:3.21.0 AS maxmind

# copy GeoIP data (from previous build steps)
COPY ./GeoLite2-City.mmdb /tmp

# I'm not sure why but the symlink with kaniko always ends up to be a duplicated file (2x 38MB layer)
RUN mkdir final && find /tmp -name '*.mmdb' -exec mv -t final {} +

# TODO: Timestamp of artifacts aren't preserved. This will give a different hash.
# Every build, will give you another layer/hash for this stage
# https://github.com/GoogleContainerTools/kaniko/issues/1894
RUN find final | xargs touch -t 200001010000.00

################################

FROM ubuntu:noble AS web

ENV DEBIAN_FRONTEND=noninteractive

# Replace any other 7.3 reference in the file
ENV PHP_VERSION=8.3

# Install node 20 LTS (Supported until April 2026.

RUN	apt-get update && apt-get upgrade -y && \
  apt-get install -y openssh-client \
  openssh-server \
  software-properties-common language-pack-en-base \
  net-tools \
  libgnutls30 \
  sudo \
  fish \
  autoconf \
  ruby-dev \
  automake \
  libffi7 \
  libffi-dev \
  netcat \
  pv \
  iproute2 \
  git \
  wget \
  curl \
  imagemagick \
  locales \
  logrotate \
  tar \
  tzdata \
  gzip \
  gettext-base \
  libxml2-dev \
  mysql-client \
  dnsutils \
  vim \
  bc \
  jq \
  cron \
  netcat && \
  gcc \
  cpp \
  # cpp-7 \ # Unable to locale package cpp-7, do we really need this ?
  g++ \
  build-essential \
  gnupg-utils \
  distro-info-data \
  gnupg \
  powermgmt-base \
  lsb-release \
  make

#   RUN LC_ALL=en_US.UTF-8 add-apt-repository ppa:maxmind/ppa && \
#   LC_ALL=en_US.UTF-8 add-apt-repository ppa:ondrej/php && \
#   apt-get install -y ca-certificates curl gnupg && \
#   mkdir -p /etc/apt/keyrings && \
#   curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
#   echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_20.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
#   apt-get update && \
#   apt-get install -y \
#   php8.3 \
#   php8.3-bcmath \
#   php8.3-cli \
#   php8.3-curl \
#   # php8.3-geoip \ # Not supported anymore (TODO)
#   php8.3-gd \
#   php8.3-gnupg \
#   php8.3-imap \
#   php8.3-intl \
#   php8.3-mbstring \
#   php8.3-mysql \
#   php8.3-xml \
#   php8.3-zip \
#   php8.3-fpm \
#   php8.3-mailparse \
#   php8.3-xdebug \
#   php8.3-dev \
#   nodejs \
#   libmaxminddb0 \
#   libmaxminddb-dev \
#   mmdb-bin \
#   apache2 \
#   apache2-utils \
#   supervisor

# RUN wget -O composer-setup.php https://getcomposer.org/installer && \
#   php composer-setup.php --version=2.6.5 --install-dir=/usr/local/bin && \
#   mv -f /usr/local/bin/composer.phar /usr/local/bin/composer && \
#   chmod +x /usr/local/bin/composer

# RUN a2enmod headers && \
#   a2enmod rewrite && \
#   a2enmod expires

# # Worker is preferred on high traffic webservers
# RUN a2dismod mpm_prefork && \
#   a2dismod mpm_worker && \
#   a2enmod mpm_event && \
#   a2enmod proxy && \
#   a2enmod proxy_fcgi && \
#   a2enmod substitute && \
#   a2enmod ext_filter && \
#   a2enconf php8.3-fpm

# RUN	ln -snf /usr/sbin/php-fpm8.3 /usr/sbin/php-fpm

# ARG OS_TYPE

# RUN cd /tmp && \
#   composer require maxmind-db/reader && \
#   cd "vendor/maxmind-db/reader/ext" && \
#   phpize && \
#   ./configure && \
#   make clean && \
#   make && \
#   make install && \
#   echo extension=maxminddb.so >> /etc/php/${PHP_VERSION}/mods-available/maxminddb.ini && \
#   apt-get autoremove -y && \
#   apt-get clean && \
#   rm -rf /var/lib/apt/lists/* && \
#   rm -fr /tmp/vendor && \
#   cd /etc/php/${PHP_VERSION}/cli/conf.d && \
#   ln -sf /etc/php/${PHP_VERSION}/mods-available/maxminddb.ini 20-maxminddb.ini && \
#   cd /etc/php/${PHP_VERSION}/fpm/conf.d && \
#   ln -sf /etc/php/${PHP_VERSION}/mods-available/maxminddb.ini 20-maxminddb.ini

# RUN apt-get autoremove -y && \
#   apt-get clean && \
#   rm -rf /var/lib/apt/lists/*


# COPY --from=maxmind --chown=root:root /final /usr/share/GeoIP
