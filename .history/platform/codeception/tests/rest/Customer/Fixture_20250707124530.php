<?php

namespace SF\rest\Customer;

use Codeception\Util\Fixtures;
use Salesfloor\Models\CustomerActivityFeed as CustomerActivityFeedModel;
use Salesfloor\Models\Customer;
use SF\BaseFixture;

class Fixture extends BaseFixture
{
    private static $value = [
        'customer8' => [
            'ID'         => 8,
            'user_id'    => 1,
            'email'      => '<EMAIL>',
            'first_name' => 'Jordan',
            'last_name'  => 'Mallette',
            'origin'     => Customer::ORIGIN_UNKNOWN,
            'subcribtion_flag' => 1,
        ],
        'customer9' => [
            'ID'         => 9,
            'user_id'    => 1,
            'email'      => '<EMAIL>',
            'first_name' => 'Jordan',
            'last_name'  => 'Someone',
            'origin'     => Customer::ORIGIN_UNKNOWN,
            'subcribtion_flag' => 2,
        ],
        'customer10-no-email' => [
            'ID'         => 10,
            'user_id'    => 1,
            'first_name' => 'Jordan',
            'last_name'  => 'Jones',
            'phone'      => '+15145556666',
            'origin'     => Customer::ORIGIN_UNKNOWN,
            'subcribtion_flag' => 0,
        ],
        'customer8-address-default' => [
            'id' => 1,
            'customer_id' => 8,
            'address_line_1' => '2691 Ave Van Horne',
            'address_line_2' => null,
            'postal_code' => 'H3S 1P6',
            'state' => 'QC',
            'city' => 'Montreal',
            'country' => 'Canada',
            'label' => 'home',
            'is_default' => 1,
        ],
        'customer8-address-2' => [
            'id' => 2,
            'customer_id' => 8,
            'address_line_1' => '212 US-20',
            'address_line_2' => null,
            'postal_code' => '68763',
            'state' => 'NE',
            'city' => 'O\' Néill',
            'country' => 'United States',
            'label' => 'home',
            'is_default' => 0,
        ],
        'customer9-address-1-default' => [
            'id' => 3,
            'customer_id' => 9,
            'address_line_1' => '212 some road',
            'address_line_2' => null,
            'postal_code' => '68763',
            'state' => ' ',
            'city' => 'Amsterdam',
            'country' => 'Germany',
            'label' => 'home',
            'is_default' => 1,
        ],
        'customer9-address-2-lowercase-state' => [
            'id' => 4,
            'customer_id' => 9,
            'address_line_1' => '2691 Ave Van Horne',
            'address_line_2' => null,
            'postal_code' => 'H3S 1P6',
            'state' => 'qc',
            'city' => 'Montreal',
            'country' => 'Canada',
            'label' => 'home',
            'is_default' => 1,
        ],
        'customer8-address-multi-same-state' => [
            'id' => 5,
            'customer_id' => 8,
            'address_line_1' => '1000 Ave Van Horne',
            'address_line_2' => null,
            'postal_code' => 'H3S 1P6',
            'state' => 'QC',
            'city' => 'Montreal',
            'country' => 'Canada',
            'label' => 'home',
            'is_default' => 1,
        ],
    ];

    public function customer()
    {
        // From API response
        /**
         *
            [
               {
                   "ID":2,
                  "user_id":1,
                  "first_name":"Joseph",
                  "name":"Joseph Mallette",
                  "last_name":"Mallette",
                  "email":"<EMAIL>",
                  "label_email":"work",
                  "phone":"+15148467733",
                  "label_phone":"home",
                  "type":"corporate",
                  "origin":"unknown",
                  "additionalEmails":[

               ],
                  "additionalPhones":[

               ],
                  "subcribtion_flag":0,
                  "groups":[
                     {
                         "user_id":"2",
                        "name":"Non-Subscribers",
                        "ID":"10000003",
                        "default":false,
                        "isSubscribers":false,
                        "isPrivate":false
                     }
                  ],
                  "isRelatedRetailerCustomer":0,
                  "isRelatedRetailerCustomerTransactions":0
               },
               {
                   "ID":1,
                  "user_id":1,
                  "first_name":"Joseph",
                  "name":"Joseph Mallette",
                  "last_name":"Mallette",
                  "email":"<EMAIL>",
                  "phone":"",
                  "type":"corporate",
                  "origin":"unknown",
                  "additionalEmails":[

               ],
                  "additionalPhones":[

               ],
                  "subcribtion_flag":0,
                  "groups":[
                     {
                         "user_id":"1",
                        "name":"Non-Subscribers",
                        "ID":"10000003",
                        "default":false,
                        "isSubscribers":false,
                        "isPrivate":false
                     }
                  ],
                  "isRelatedRetailerCustomer":0,
                  "isRelatedRetailerCustomerTransactions":0
               }
            ]
         */

        // customer1
        Fixtures::add(
            'customer1',
            [
                'ID'         => 1,
                'user_id'    => 1,
                'email'      => '<EMAIL>',
                'first_name' => 'Joseph',
                'last_name'  => 'Mallette',
                'origin'     => Customer::ORIGIN_UNKNOWN,
            ]
        );

        // customer2
        Fixtures::add(
            'customer2',
            [
                'ID'         => 2,
                'user_id'    => 1,
                'email'      => '<EMAIL>',
                'first_name' => 'Joseph',
                'last_name'  => 'Mallette',
                'origin'     => Customer::ORIGIN_UNKNOWN,
            ]
        );

        // customer 3 no-email-or-phone
        Fixtures::add(
            'customer-no-email-or-phone',
            [
                'ID'         => 3,
                'user_id'    => 1,
                'first_name' => 'Joseph',
                'last_name'  => 'Mallette',
                'origin'     => Customer::ORIGIN_UNKNOWN,
            ]
        );

        // Changed +n in email in fixtures below to match customer ID

        // customer 4 no-email
        Fixtures::add(
            'customer-no-email',
            [
                'ID'         => 4,
                'user_id'    => 1,
                'phone'      => '+15145556666',
                'first_name' => 'Joseph',
                'last_name'  => 'Mallette',
                'origin'     => Customer::ORIGIN_UNKNOWN,
            ]
        );

        // customer 5 no-phone
        Fixtures::add(
            'customer-no-phone',
            [
                'ID'         => 5,
                'user_id'    => 1,
                'email'      => '<EMAIL>',
                'first_name' => 'Joseph',
                'last_name'  => 'Mallette',
                'origin'     => Customer::ORIGIN_UNKNOWN,
            ]
        );

        // customer 6 subscriber
        Fixtures::add(
            'customer-subscriber',
            [
                'ID'               => 6,
                'user_id'          => 1,
                'email'            => '<EMAIL>',
                'first_name'       => 'Joseph',
                'last_name'        => 'Mallette',
                'origin'           => Customer::ORIGIN_UNKNOWN,
                'subcribtion_flag' => 1,
            ]
        );

        // customer 7 subscriber-no-email
        Fixtures::add(
            'customer-subscriber-no-email',
            [
                'ID'               => 7,
                'user_id'          => 1,
                'first_name'       => 'Joseph',
                'last_name'        => 'Mallette',
                'origin'           => Customer::ORIGIN_UNKNOWN,
                'subcribtion_flag' => 1,
            ]
        );

        // Add predefined above
        foreach (self::$value as $key => $value) {
            Fixtures::add($key, $value);
        }
    }

    public function customerAddress()
    {
        Fixtures::add(
            'CustomerAddress',
            [
                'sf_customer' => [
                    self::$value['customer8'],
                    self::$value['customer9'],
                ],
                'sf_customer_addresses' => [
                    self::$value['customer8-address-default'],
                    self::$value['customer8-address-2'],
                    self::$value['customer9-address-1-default'],
                    self::$value['customer9-address-2-lowercase-state'],
                    self::$value['customer8-address-multi-same-state'],
                ],
                'sf_customer_tags_relationships' => [
                    [
                        'customer_id' => 9,
                        'tag_id'      => 1,
                        'created_at'  => '2018-09-24 00:00:00',
                        'updated_at'  => '2018-09-24 00:00:00',
                    ],
                    [
                        'customer_id' => 9,
                        'tag_id'      => 2,
                        'created_at'  => '2018-09-24 00:00:00',
                        'updated_at'  => '2018-09-24 00:00:00',
                    ],
                    [
                        'customer_id' => 9,
                        'tag_id'      => 3,
                        'created_at'  => '2018-09-24 00:00:00',
                        'updated_at'  => '2018-09-24 00:00:00',
                    ],
                    [
                        'customer_id' => 9,
                        'tag_id'      => 4,
                        'created_at'  => '2018-09-24 00:00:00',
                        'updated_at'  => '2018-09-24 00:00:00',
                    ],
                    [
                        'customer_id' => 9,
                        'tag_id'      => 5,
                        'created_at'  => '2018-09-24 00:00:00',
                        'updated_at'  => '2018-09-24 00:00:00',
                    ],
                ],
                'sf_customer_tags' => [
                    [
                        'id'              => 1,
                        'retailer_tag_id' => 'B1',
                        'name'            => 'B1',
                        'status'          => 'active'
                    ],
                    [
                        'id'              => 2,
                        'retailer_tag_id' => 'B2',
                        'name'            => 'B2',
                        'status'          => 'active'
                    ],
                    [
                        'id'              => 3,
                        'retailer_tag_id' => 'B3',
                        'name'            => 'B3',
                        'status'          => 'active'
                    ],
                    [
                        'id'              => 4,
                        'retailer_tag_id' => 'B4',
                        'name'            => 'B4',
                        'status'          => 'active'
                    ],
                    [
                        'id'              => 5,
                        'retailer_tag_id' => 'B5',
                        'name'            => 'B5',
                        'status'          => 'active'
                    ],
                ],
            ]
        );
    }

    public function mandatoryFields()
    {
        Fixtures::add(
            'MandatoryFields',
            [
                'sf_customer' => [
                    self::$value['customer8'],
                    self::$value['customer9'],
                    self::$value['customer10-no-email'],
                ],
            ]
        );
    }

    public function unsubscribedCustomers()
    {
        Fixtures::add(
            'UnsubscribedCustomers',
            [
                'sf_customer' => [
                    self::$value['customer8'],
                    self::$value['customer9'],
                    self::$value['customer10-no-email'],
                ],
            ]
        );
    }

    public function customerMatching()
    {
        Fixtures::add('CustomerMatchingRCIOverMatching', [
            'sf_customer' => [
                [
                    'ID' => 3,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'JoJo',
                    'phone'                => '',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'retailer_customer_id' => '12345',
                    'first_name'           => 'Jo',
                    'last_name'            => 'Ma',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
            ],
            'sf_retailer_customers' => [
                [
                    'id' => 1,
                    'customer_id' => '12345',
                    'gender' => 'female',
                    'first_name' => 'Joseph',
                    'last_name' => 'Mallette',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '************',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'id' => 2,
                    'customer_id' => '1234511',
                    'gender' => 'female',
                    'first_name' => 'Joseph',
                    'last_name' => 'Mallette',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '************',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
            ],
        ]);

        Fixtures::add('CustomerMatchingRPCIOverMatching', [
            'sf_customer' => [
                [
                    'ID' => 3,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'JoJo',
                    'phone'                => '',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'retailer_parent_customer_id' => '12345',
                    'first_name'           => 'Jo',
                    'last_name'            => 'Ma',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10'
                ],
            ],
            'sf_retailer_customers' => [
                [
                    'id' => 1,
                    'customer_id' => '12345',
                    'gender' => 'female',
                    'first_name' => 'Joseph',
                    'last_name' => 'Mallette',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '************',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'id' => 2,
                    'customer_id' => '1234511',
                    'gender' => 'female',
                    'first_name' => 'Joseph',
                    'last_name' => 'Mallette',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '************',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
            ],
        ]);

        Fixtures::add('CustomerMatchingOnly', [
            'sf_customer' => [
                [
                    'ID' => 3,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'JoJo',
                    'phone'                => '345678',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'Jo',
                    'last_name'            => 'Ma',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
                [
                    'ID' => 4,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'JoJo',
                    'phone'                => '345678',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'Jo',
                    'last_name'            => 'Ma',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
                [
                    'ID' => 5,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'JoJo',
                    'phone'                => '345678',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'Jo',
                    'last_name'            => 'Ma',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                    'retailer_parent_customer_id' => 123455
                ],
            ],
            'sf_retailer_customers' => [
                [
                    'id' => 1004,
                    'customer_id'        => '123464',
                    'gender'             => 'female',
                    'first_name'         => 'Joseph',
                    'last_name'          => 'Mallette',
                    'email'              => '<EMAIL>',
                    'email_label'        => 'Personal',
                    'phone'              => '************',
                    'phone_label'        => 'Work',
                    'address_line1'      => '73975 Borer Mallll',
                    'address_line2'      => 'Juana Square',
                    'zipcode'            => '91266-7456',
                    'postalcode'         => '09302-0963',
                    'city'               => 'East Maryjaneview',
                    'state'              => 'California',
                    'country'            => 'Congo',
                    'longitude'          => '106.47575000',
                    'latitude'           => '42.32311300',
                    'is_subscribed'      => '0',
                    'limited_visibility' => 1,
                ],
                [
                    'id' => 1006,
                    'customer_id'        => '1231006',
                    'gender'             => 'female',
                    'first_name'         => 'Joseph',
                    'last_name'          => 'Mallette',
                    'email'              => '<EMAIL>',
                    'email_label'        => 'Personal',
                    'phone'              => '************',
                    'phone_label'        => 'Work',
                    'address_line1'      => '73975 Borer Mallll',
                    'address_line2'      => 'Juana Square',
                    'zipcode'            => '91266-7456',
                    'postalcode'         => '09302-0963',
                    'city'               => 'East Maryjaneview',
                    'state'              => 'California',
                    'country'            => 'Congo',
                    'longitude'          => '106.47575000',
                    'latitude'           => '42.32311300',
                    'is_subscribed'      => '0',
                    'limited_visibility' => 1,
                ],
                [
                    'id' => 2001,
                    'customer_id' => '12345',
                    'gender' => 'female',
                    'first_name' => 'Joseph',
                    'last_name' => 'Mallette',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '************',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'id' => 2002,
                    'customer_id'        => '123454',
                    'gender'             => 'female',
                    'first_name'         => 'Joseph',
                    'last_name'          => 'Mallette',
                    'email'              => '<EMAIL>',
                    'email_label'        => 'Personal',
                    'phone'              => '************',
                    'phone_label'        => 'Work',
                    'address_line1'      => '73975 Borer Mallll',
                    'address_line2'      => 'Juana Square',
                    'zipcode'            => '91266-7456',
                    'postalcode'         => '09302-0963',
                    'city'               => 'East Maryjaneview',
                    'state'              => 'California',
                    'country'            => 'Congo',
                    'longitude'          => '106.47575000',
                    'latitude'           => '42.32311300',
                    'is_subscribed'      => '0',
                    'limited_visibility' => 1,
                ],
                [
                    'id' => 2003,
                    'customer_id'        => '123455',
                    'gender'             => 'female',
                    'first_name'         => 'Joseph',
                    'last_name'          => 'Mallette',
                    'email'              => '<EMAIL>',
                    'email_label'        => 'Personal',
                    'phone'              => '************',
                    'phone_label'        => 'Work',
                    'address_line1'      => '73975 Borer Mallll',
                    'address_line2'      => 'Juana Square',
                    'zipcode'            => '91266-7456',
                    'postalcode'         => '09302-0963',
                    'city'               => 'East Maryjaneview',
                    'state'              => 'California',
                    'country'            => 'Congo',
                    'longitude'          => '106.47575000',
                    'latitude'           => '42.32311300',
                    'is_subscribed'      => '0',
                    'limited_visibility' => 1,
                ],
            ],
        ]);

        Fixtures::add(
            'MatchingWithLimitedVisibility',
            [
                'sf_customers_to_retailer_customers' => [
                    [
                        'customer_id'          => 3,
                        'retailer_customer_id' => 2001,
                        'comment'              => 'default_email: <EMAIL>',
                    ],
                    [
                        'customer_id'          => 3,
                        'retailer_customer_id' => 1004,
                        'comment'              => 'alternate_email: <EMAIL>',
                    ],
                    [
                        'customer_id'          => 4,
                        'retailer_customer_id' => 2002,
                        'comment'              => 'default_email: <EMAIL>',
                    ],
                    [
                        'customer_id'          => 5,
                        'retailer_customer_id' => 2003,
                        'comment'              => 'default_email: <EMAIL>',
                    ],
                    [
                        'customer_id'          => 5,
                        'retailer_customer_id' => 1006,
                        'comment'              => 'alternate_email: <EMAIL>',
                    ]
                ],
            ]
        );
    }

    public function customerMeta()
    {
        Fixtures::add('CustomerMeta', [
            'sf_customer_meta'           => [
                [
                    'id' => 1,
                    'customer_id'       => '1',
                    'type'              => 'email',
                    'value'             => '<EMAIL>',
                    'label'             => 'home',
                    'position'          => 0,
                ],
                [
                    'id' => 2,
                    'customer_id'       => '1',
                    'type'              => 'phone',
                    'value'             => '+15144321111',
                ],
            ],
        ]);
    }

    public function customerActivityFeed()
    {
        Fixtures::add('customer_activity_feed', [
            'sf_customer_activity_feed' => [
                [
                    'customer_id' =>  1,
                    'user_id' =>  1,
                    'type' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_CALL_ATTEMPTED,
                    'thread_id' =>  '',
                    'preview' =>  null,
                    'direction' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                    'status' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
                ],
                [
                    'customer_id' =>  1,
                    'user_id' =>  1,
                    'type' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_SHARE_EMAIL,
                    'thread_id' =>  '',
                    'preview' =>  null,
                    'direction' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                    'status' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_READ
                ],
                [
                    'customer_id' =>  1,
                    'user_id' =>  1,
                    'type' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_LOOKBOOK_PUBLISHED,
                    'thread_id' =>  '',
                    'preview' =>  'lookbook subtitle',
                    'direction' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_OUTBOUND,
                    'status' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
                ],
                [
                    'customer_id' =>  2,
                    'user_id' =>  1,
                    'type' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_SHARE_EMAIL,
                    'thread_id' =>  '',
                    'preview' =>  null,
                    'direction' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                    'status' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_READ
                ],
            ],
        ]);
    }

    public function customerSignature()
    {
        Fixtures::add('customer_signature_update', [
            'sf_customer'           => [
                [
                    'ID'               => 1001,
                    'user_id'          => 1,
                    'name'             => 'Joseph Mallette',
                    'email'            => '<EMAIL>',
                    'phone'            => '+15145555555',
                    'first_name'       => 'Joseph',
                    'last_name'        => 'Mallette',
                    'origin'           => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 0,
                    'sms_marketing_subscription_flag' => 0,
                ],
            ],
            'sf_customer_signature' => [
                [
                    'customer_id' => 1001,
                    'signature'   => 'what-ever-the-value',
                    'is_latest'   => '1',
                    'user_id'     => 1,
                    'from_user_id' => 1,
                ],
                [
                    'customer_id' => 1001,
                    'signature'   => 'what-ever-the-value-old-old',
                    'is_latest'   => '0',
                    'user_id'     => 1,
                    'from_user_id' => 1,
                ],
            ],
        ]);
    }

    public function customerBlockList()
    {
        Fixtures::add('customer_block_list', [
            'sf_email_block_list'           => [
                [
                    'email'  => '<EMAIL>',
                    'source' => 'Unsubscribed',
                    'created_on_sendgrid' => '1731543933',
                ],
            ],
            'sf_sms_block_list' => [
                [
                    'phone_number' => '+15145610744',
                    'user_id'      => 1,
                    'store_id'     => 1003,
                ],
            ],
        ]);
    }

    public function customerSmsMarketing()
    {
        Fixtures::add('CustomerSmsMarketing', [
            'sf_customer'           => [
                [
                    'ID'               => 5,
                    'user_id'          => 1,
                    'first_name'       => 'Joseph',
                    'last_name'        => 'Mallette',
                    'origin'           => Customer::ORIGIN_UNKNOWN,
                    'sms_marketing_subscription_flag' => 1,
                ],
                [
                    'ID'               => 6,
                    'user_id'          => 1,
                    'first_name'       => 'Joseph',
                    'last_name'        => 'Mallette',
                    'origin'           => Customer::ORIGIN_UNKNOWN,
                    'sms_marketing_subscription_flag' => 0,
                ],
            ],
        ]);
    }

    public function customerBlockListSetup()
    {
        Fixtures::add('CustomerBlockList', [
            'sf_email_block_list' => [
                [
                    'id' => 1,
                    'email'  => '<EMAIL>',
                    'source' => 'Unsubscribed',
                    'created_on_sendgrid' => '1618951147',
                ],
                [
                    'id' => 2,
                    'email'  => '<EMAIL>',
                    'source' => 'Unsubscribed',
                    'created_on_sendgrid' => '1618951147',
                ],
                [
                    'id' => 3,
                    'email'  => '<EMAIL>',
                    'source' => 'Unsubscribed',
                    'created_on_sendgrid' => '1618951147',
                ],
            ],
            'sf_sms_block_list' => [
                [
                    'id' => 1,
                    'user_id'  => '6',
                    'store_id' => '1003',
                    'phone_number' => '+15142345648'
                ],
                [
                    'id' => 2,
                    'user_id'  => '6',
                    'store_id' => '1003',
                    'phone_number' => '+15145545679'
                ],
            ],
            'sf_customer' => [
                [
                    'ID'               => 1001,
                    'user_id'          => 6,
                    'email'            => "<EMAIL>",
                    'phone'            => "+15142345748",
                    'first_name'       => 'Joe',
                    'last_name'        => 'Test',
                    'origin'           => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_MANUAL,
                    // See api/app/src/be/Managers/Client/Customers/Legacy.php::unsubscribeContactWhenOnBlockList() and
                    // models/src/Customer.php for constants; both of these must be 0:
                    'subcribtion_flag' => 0,
                    'sms_marketing_subscription_flag' => 0,
                ],
                [
                    'ID'               => 1005,
                    'user_id'          => 6,
                    'email'            => "<EMAIL>",
                    'phone'            => "+15142345749",
                    'first_name'       => 'Joe1',
                    'last_name'        => 'Test1',
                    'origin'           => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_MANUAL,
                    'subcribtion_flag' => 2,
                    'sms_marketing_subscription_flag' => 2,
                ],
            ],
        ]);
    }

    public function customersJsonapi()
    {
        Fixtures::add('CustomerProperties', [
            'sf_customer_meta'           => [
                [
                    'id' => 1,
                    'customer_id'       => '1',
                    'type'              => 'email',
                    'value'             => '<EMAIL>',
                    'label'             => 'home',
                    'position'          => 0,
                ],
            ],
            'sf_customer_addresses'    => [
                [
                    'id' => 1,
                    'customer_id'    => 1,
                    'address_line_1' => '2162 Elton Pl',
                    'postal_code'    => 'h4gT48',
                    'state'          => 'QC',
                    'city'           => 'Montreal',
                    'country'        => 'Canada',
                    'is_default'     => '1',
                    'label'          => 'home',
                ],
                [
                    'id' => 2,
                    'customer_id'    => 1,
                    'address_line_1' => '2132 xxx Pl',
                    'postal_code'    => 'h4rT58',
                    'state'          => 'QC',
                    'city'           => 'Montreal',
                    'country'        => 'Canada',
                    'is_default'     => '0',
                    'label'          => 'home',
                ],
            ],
            'sf_customer_events'       => [
                [
                    'id' => 1,
                    'customer_id' => 1,
                    'label'       => 'birthday',
                    'day'         => 1,
                    'month'       => 10,
                ],
                [
                    'id' => 2,
                    'customer_id' => 1,
                    'label'       => 'anniversary',
                    'day'         => 1,
                    'month'       => 1,
                ],
            ],
            'sf_customer_social_media' => [
                [
                    'id' => 1,
                    'customer_id'             => 1,
                    'social_media_network_id' => 1,
                    'username'                => 'testuser',
                ],
            ],
            'sf_customer_attributes' => [
                [
                    'id'             => '123',
                    'attribute_id'   => 'attr123',
                    'description' => 'customer\'s height',
                    'attribute_type'  => 'int',
                    'label' => 'Height',
                    'panel_id' => 1,
                    'validation_rule' => 'int',
                ],
                [
                    'id'             => '456',
                    'attribute_id'   => 'attr456',
                    'description' => 'customer\'s weight',
                    'attribute_type'  => 'int',
                    'label' => 'weight',
                    'panel_id' => 1,
                    'validation_rule' => 'int',
                ],
            ],
            'sf_customer_attributes_panel' => [
                [
                    'id' => 1,
                    'panel_id' => 'panel123',
                    'label' => 'test label',
                    'position' => 1,
                ],
            ],
            'sf_customers_to_customer_attributes' => [
                [
                    'id' => 1,
                    'customer_id' => 1,
                    'attribute_id' => 123, // 'id' of customer_attributes table
                    'attribute_value' => '180',
                ],
            ],
            'sf_retailer_customers_to_customer_attributes' => [
                [
                    'id' => 1,
                    'customer_id' => 12345,
                    'attribute_id' => 123, // 'id' of customer_attributes table
                    'attribute_value' => '160',
                ],
            ],
            'sf_retailer_customer_meta'           => [
                [
                    'id' => 1,
                    'customer_id'       => '12345',
                    'type'              => 'email',
                    'value'             => '<EMAIL>',
                    'position'          => 0,
                ],
            ],
        ]);

        Fixtures::add(
            'CustomerPropertiesWithMultipleAttribute',
            [
                'sf_customers_to_customer_attributes'          => [
                    [
                        'id' => 2,
                        'customer_id'     => 1,
                        'attribute_id'    => 456, // `id` of customer_attributes table
                        'attribute_value' => '80',
                    ],
                ],
                'sf_retailer_customers_to_customer_attributes' => [
                    [
                        'id' => 2,
                        'customer_id'     => 12345,
                        'attribute_id'    => 456, // `id` of customer_attributes table
                        'attribute_value' => '81',
                    ],
                ],
            ]
        );

        Fixtures::add(
            'CustomerPropertiesWithSearchableAttribute',
            [
                'sf_customer_attributes_panel' => [
                    [
                        'panel_id' => '2pac',
                        'label' => 'test2 label',
                        'position' => 2,
                    ],
                ],
                'sf_customer_attributes' => [
                    [
                        'id'             => '457',
                        'attribute_id'   => 'attr457',
                        'description' => 'Favourite Color',
                        'attribute_type'  => 'text',
                        'label' => 'Color',
                        'panel_id' => '2pac',
                        'is_searchable'   => 1,
                        'validation_rule' => 'alphanumeric',
                    ],
                    [
                        'id'             => '458',
                        'attribute_id'   => 'attr458',
                        'description' => 'Favorite Pets',
                        'attribute_type'  => 'single-select',
                        'label' => 'Pets',
                        'default_value' => '[{"value":"1","label":"Cat"},{"value":"2","label":"Tiger"}]',
                        'panel_id' => '2pac',
                        'is_searchable'   => 1,
                        'validation_rule' => null,
                    ],
                    [
                        'id'             => '459',
                        'attribute_id'   => 'attr459',
                        'description' => 'Computer Languages',
                        'attribute_type'  => 'multi-select',
                        'label' => 'Languages',
                        'default_value' => '[{"value":"1","label":"Python"},{"value":"2","label":"NodeJs"}]',
                        'panel_id' => '2pac',
                        'is_searchable'   => 1,
                        'validation_rule' => null,
                    ],
                    [
                        'id'             => '460',
                        'attribute_id'   => 'attr460',
                        'description' => 'Favorie Fruits',
                        'attribute_type'  => 'multi-select',
                        'label' => 'Fruits',
                        'default_value' => '[{"value":"p1","label":"Pear"},{"value":"o2","label":"Orange"}]',
                        'panel_id' => '2pac',
                        'is_searchable'   => 1,
                        'validation_rule' => null,
                    ],
                ],
            ]
        );
        Fixtures::add(
            'CustomerPropertiesWithSearchableAttributeData',
            [
                'sf_customers_to_customer_attributes'          => [
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 457, // `id` of customer_attributes table
                        'attribute_value' => 'Blue and Black',
                    ],
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 458, // `id` of customer_attributes table
                        'attribute_value' => '1',
                    ],
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 459, // `id` of customer_attributes table
                        'attribute_value' => '["1","2"]',
                    ],
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 460, // `id` of customer_attributes table
                        'attribute_value' => '["p1","o2"]',
                    ],
                ],
                'sf_retailer_customers_to_customer_attributes' => [
                    [
                        'customer_id'     => 12345,
                        'attribute_id'    => 457, // `id` of customer_attributes table
                        'attribute_value' => 'Green',
                    ],
                ],
            ]
        );

        Fixtures::add(
            'CustomerPropertiesWithFilterableAttribute',
            [
                'sf_customer_attributes_panel' => [
                    [
                        'panel_id' => '2pac',
                        'label' => 'test2 label',
                        'position' => 2,
                    ],
                ],
                'sf_customer_attributes' => [
                    [
                        'id'             => '457',
                        'attribute_id'   => 'colored',
                        'description' => 'Should Color',
                        'attribute_type'  => 'boolean',
                        'label' => 'Color',
                        'panel_id' => '2pac',
                        'is_filterable'   => 1,
                        'validation_rule' => 'numeric_only',
                    ],
                    [
                        'id'             => '458',
                        'attribute_id'   => 'attr458',
                        'description' => 'Favorite Pets',
                        'attribute_type'  => 'single-select',
                        'label' => 'Pets',
                        'default_value' => '[{"value":"1","label":"Cat"},{"value":"2","label":"Tiger"}]',
                        'panel_id' => '2pac',
                        'is_searchable'   => 1,
                        'validation_rule' => null,
                    ],
                    [
                        'id'             => '459',
                        'attribute_id'   => 'attr459',
                        'description' => 'Computer Languages',
                        'attribute_type'  => 'multi-select',
                        'label' => 'Languages',
                        'default_value' => '[{"value":"1","label":"Python"},{"value":"2","label":"NodeJs"}]',
                        'panel_id' => '2pac',
                        'is_searchable'   => 1,
                        'validation_rule' => null,
                    ],
                ],
            ]
        );
        Fixtures::add(
            'CustomerPropertiesWithFilterableAttributeData',
            [
                'sf_customers_to_customer_attributes' => [
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 457, // `id` of customer_attributes table
                        'attribute_value' => '0',
                    ],
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 458, // `id` of customer_attributes table
                        'attribute_value' => '1',
                    ],
                    [
                        'customer_id'     => 1,
                        'attribute_id'    => 459, // `id` of customer_attributes table
                        'attribute_value' => '["1","2"]',
                    ],
                ],
                'sf_retailer_customers_to_customer_attributes' => [
                    [
                        'customer_id'     => 12345,
                        'attribute_id'    => 457, // `id` of customer_attributes table
                        'attribute_value' => '0',
                    ],
                    [
                        'customer_id'     => 12345,
                        'attribute_id'    => 458, // `id` of customer_attributes table
                        'attribute_value' => '1',
                    ],
                    [
                        'customer_id'     => 12345,
                        'attribute_id'    => 459, // `id` of customer_attributes table
                        'attribute_value' => '["1","2"]',
                    ],
                ],
            ]
        );
    }

    public function customerAttributeSidePatchBodyStructure()
    {
        $body1 = [
            'data' => [
                'type' => 'customer',
                'id' => '1',
                'relationships' => [
                    'customer-to-customer-attribute' => [
                        'data' => [
                            [
                                'type' => 'customer-to-customer-attribute',
                                'id' => '1'
                            ]
                        ]
                    ]
                ]
            ],
            'included' => [
                [
                    'type' => 'customer-to-customer-attribute',
                    'id' => '1',
                    'attributes' => [
                        'customer_id' => '1',
                        'attribute_id' => '123',
                        'attribute_value' => 181
                    ]
                ]
            ]
        ];

        $body2 = <<<START
{
    "data": {
        "type": "customer",
        "id": "1",
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "1"
                    },
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "2"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "1",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "123",
                "attribute_value": 181
            }
        },
        {
            "type": "customer-to-customer-attribute",
            "id": "2",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "456",
                "attribute_value": 82
            }
        }
    ]
}
START;

        $postAndPatch = <<<START
{
  "data": {
    "type": "customer",
    "id": "1",
    "relationships": {
      "customer-to-customer-attribute": {
        "data": [
          {
            "type": "customer-to-customer-attribute",
            "id": "1"
          },
          {
            "type": "customer-to-customer-attribute",
            "lid": "123321"
          }
        ]
      }
    }
  },
  "included": [
    {
      "type": "customer-to-customer-attribute",
      "id": "1",
      "attributes": {
        "customer_id": "1",
        "attribute_id": "123",
        "attribute_value": 181
      }
    },
    {
      "type": "customer-to-customer-attribute",
      "lid": "123321",
      "attributes": {
        "customer_id": "1",
        "attribute_id": "456",
        "attribute_value": 82
      }
    }
  ]
}
START;
        $body3 = <<<START
{
    "data": {
        "type": "customer",
        "id": "1",
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "2"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "2",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "457",
                "attribute_value": "pink"
            }
        }
    ]
}
START;
        $body3Filter = <<<START
{
    "data": {
        "type": "customer",
        "id": "1",
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "2"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "2",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "457",
                "attribute_value": "1"
            }
        }
    ]
}
START;
        $post = <<<START
{
  "data": {
    "type": "customer",
    "id": "1",
    "relationships": {
      "customer-to-customer-attribute": {
        "data": [
          {
            "type": "customer-to-customer-attribute",
            "lid": "1"
          }
        ]
      }
    }
  },
  "included": [
    {
      "type": "customer-to-customer-attribute",
      "lid": "1",
      "attributes": {
        "customer_id": "1",
        "attribute_id": "457",
        "attribute_value": "brown"
      }
    }
  ]
}
START;
        $postFilter = <<<START
{
  "data": {
    "type": "customer",
    "id": "1",
    "relationships": {
      "customer-to-customer-attribute": {
        "data": [
          {
            "type": "customer-to-customer-attribute",
            "lid": "1"
          }
        ]
      }
    }
  },
  "included": [
    {
      "type": "customer-to-customer-attribute",
      "lid": "1",
      "attributes": {
        "customer_id": "1",
        "attribute_id": "457",
        "attribute_value": "1"
      }
    }
  ]
}
START;
        Fixtures::add('customer-with-customer-attribute-valid-single', json_decode($body1, true));
        Fixtures::add('customer-with-customer-attribute-valid-multiple', json_decode($body2, true));
        Fixtures::add('customer-with-customer-attribute-valid-multiple-post-and-patch', json_decode($postAndPatch, true));
        Fixtures::add('customer-with-customer-attribute-valid-single-searchable', json_decode($body3, true));
        Fixtures::add('customer-with-customer-attribute-valid-single-filterable', json_decode($body3Filter, true));
        Fixtures::add('customer-with-customer-attribute-searchable-post', json_decode($post, true));
        Fixtures::add('customer-with-customer-attribute-filterable-post', json_decode($postFilter, true));
    }

    public function customerAttributeSidePatchBodyStructureInvalid()
    {
        $body1 = <<<START
{
    "data": {
        "type": "customer",
        "id": "1",
        "attributes": {
            "user_id": "1",
            "email": "<EMAIL>"
        },
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "1"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "1",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "123",
                "attribute_value": "abc-invalid"
            }
        }
    ]
}
START;

        $body2 = <<<START
{
    "data": {
        "type": "customer",
        "id": "1",
        "attributes": {
            "user_id": "1",
            "email": "<EMAIL>"
        },
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "1"
                    },
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "2"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "1",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "123",
                "attribute_value": "abc-invalid"
            }
        },
        {
            "type": "customer-to-customer-attribute",
            "id": "2",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "456",
                "attribute_value": 82
            }
        }
    ]
}
START;

        $body3 = <<<START
{
    "data": {
        "type": "customer",
        "id": "1",
        "attributes": {
            "user_id": "1",
            "email": "<EMAIL>"
        },
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "1"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "1",
            "attributes": {
                "customer_id": "1",
                "attribute_id": "987654",
                "attribute_value": "whatever value"
            }
        }
    ]
}
START;
        Fixtures::add('customer-with-customer-attribute-invalid-single', json_decode($body1, true));
        Fixtures::add('customer-with-customer-attribute-invalid-multiple', json_decode($body2, true));
        Fixtures::add('customer-with-customer-attribute-invalid-id', json_decode($body3, true));
    }

    public function usersMyUpdates()
    {
        Fixtures::add('users_my_update', [
            'wp_users' => [
                [
                    'ID' => 1001,
                    'user_login' => 'reggie1001',
                    'user_pass' => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename' => 'reggie1001',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'reggie2',
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'employee_id' => '12345',
                    'group' => 1,
                    'store' => 1003,
                    'selling_mode' => 1,
                    'isPhoto' => 1
                ],
            ],
        ]);

        Fixtures::add('exist_customer_to_update', [
            'sf_customer' => [
                [
                    'id' => 3,
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'name'                            => 'Thomas test',
                    'phone'                           => '5147465555',
                    'subcribtion_flag'                => 0,
                    'sms_marketing_subscription_flag' => 0,
                    'locale'                          => 'en_US'
                ],
            ],
            'sf_customer_meta'           => [
                [
                    'customer_id'       => '3',
                    'type'              => 'email',
                    'value'             => '<EMAIL>',
                    'label'             => 'home',
                    'position'          => 0,
                ],
            ],
        ]);
    }

    public function getMyUpdatesFormData()
    {
        $data = $this->allMyUpdatesFormData();
        Fixtures::add('getMyUpdatesFormData', [
            $data[0],
            $data[1],
            $data[2],
            $data[3],
            $data[4],
            $data[5],
            $data[6],
            $data[7],
            $data[8],
            $data[9],
        ]);
    }

    private function allMyUpdatesFormData()
    {
        return [
            [ // no email and phone present.
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '',
                        'phone'   => '',
                        'name'    => 'Karl Sun',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedStatusCode' => 500,
                'expectedContent'    => []
            ],
            [ // email and phone present, will trigger update customer logic
                'data_purpose'       => 'test_exist_customer_to_update',
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '************',
                        'name'    => 'Karl1 Sun1',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl1 Sun1',
                    'first_name'                      => 'Karl1',
                    'last_name'                       => 'Sun1',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 1,
                    'sms_marketing_subscription_flag' => 1
                ],
                'seeInDatabase'      => [
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => 1,
                            'old_value' => 0,
                            'field_name' => 'subcribtion_flag'
                        ]
                    ],
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => '<EMAIL>',
                            'old_value' => '<EMAIL>',
                            'field_name' => 'email'
                        ]
                    ],
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => 1,
                            'old_value' => 0,
                            'field_name' => 'sms_marketing_subscription_flag'
                        ]
                    ],
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => '+15145131515',
                            'old_value' => '5147465555',
                            'field_name' => 'phone'
                        ]
                    ],
                ]
            ],
            [ // email and phone present, will trigger update customer logic and swap email
                'data_purpose'       => 'test_exist_customer_to_update',
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '************',
                        'name'    => 'Karl1 Sun1',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl1 Sun1',
                    'first_name'                      => 'Karl1',
                    'last_name'                       => 'Sun1',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 1,
                    'sms_marketing_subscription_flag' => 1
                ],
                'seeInDatabase'      => [
                    ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => 1, 'old_value' => 0, 'field_name' => 'subcribtion_flag']],
                    ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => '<EMAIL>', 'old_value' => '<EMAIL>', 'field_name' => 'email']],
                    ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => 1, 'old_value' => 0, 'field_name' => 'sms_marketing_subscription_flag']],
                    ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => '+15145131515', 'old_value' => '5147465555', 'field_name' => 'phone']],

                    ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => '<EMAIL>', 'old_value' => '<EMAIL>', 'field_name' => 'email', 'position' => 0]],
                ]
            ],
            [ // email and phone present, will trigger createNewCustomer logic
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '************',
                        'name'    => 'Karl1 Sun1',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedAction'     => 'createNewCustomer',
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl1 Sun1',
                    'first_name'                      => 'Karl1',
                    'last_name'                       => 'Sun1',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 1,
                    'sms_marketing_subscription_flag' => 1
                ],
                // 'seeInDatabase'      => [
                //     ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => 1, 'old_value' => null, 'field_name' => 'subcribtion_flag']],
                //     ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => '<EMAIL>', 'old_value' => null, 'field_name' => 'email']],
                //     ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => 1, 'old_value' => null, 'field_name' => 'sms_marketing_subscription_flag']],
                //     ['sf_customer_field_history', ['source' => 'get_my_updates', 'customer_id' => 3, 'new_value' => '+15145131515', 'old_value' => null, 'field_name' => 'phone']],
                // ]
            ],
            [
                // email and phone present, will trigger update customer logic,
                // test if a customer update a phone w/o change sms_marketing_subscription_flag,
                // The sf_customer_field_history will record record also
                'data_purpose'       => 'test_exist_customer_to_update',
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '************',
                        'name'    => 'Karl1 Sun1',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 0,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedStatusCode' => 200,
                'seeInDatabase'      => [
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => 1,
                            'old_value' => 0,
                            'field_name' => 'subcribtion_flag'
                        ]
                    ],
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => '<EMAIL>',
                            'old_value' => '<EMAIL>',
                            'field_name' => 'email'
                        ]
                    ],
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => 0,
                            'old_value' => 0,
                            'field_name' => 'sms_marketing_subscription_flag'
                        ]
                    ],
                    [
                        'sf_customer_field_history',
                        [
                            'source' => 'get_my_updates',
                            'customer_id' => 3,
                            'new_value' => '+15145131515',
                            'old_value' => '5147465555',
                            'field_name' => 'phone'
                        ]
                    ],
                ]
            ],
            [
                // email and phone present + user without notification
                // (for test with retailer.corporate-email.required = false), will trigger createNewCustomer logic
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1001,
                        'email'   => '<EMAIL>',
                        'phone'   => '************',
                        'name'    => 'Karl1 Sun1',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedAction'     => 'createNewCustomer',
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1001,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl1 Sun1',
                    'first_name'                      => 'Karl1',
                    'last_name'                       => 'Sun1',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 1,
                    'sms_marketing_subscription_flag' => 1
                ]
            ],
            [ // email only present, will trigger createNewCustomer logic
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '+15145131515',
                        'name'    => 'Karl2 Sun2',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 0,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedAction'     => 'createNewCustomer',
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl2 Sun2',
                    'first_name'                      => 'Karl2',
                    'last_name'                       => 'Sun2',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 0,
                    'sms_marketing_subscription_flag' => 1
                ]
            ],
            [ // phone only present, will trigger createNewCustomer logic
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '',
                        'phone'   => '+15145131515',
                        'name'    => 'Karl3 Sun3',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedAction'     => 'createNewCustomer',
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => null,
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl3 Sun3',
                    'first_name'                      => 'Karl3',
                    'last_name'                       => 'Sun3',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 0,
                    'sms_marketing_subscription_flag' => 1
                ]
            ],
            [ // empty name. will trigger createNewCustomer logic
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '+15145131515',
                        'name'    => '',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 1,
                        'receive_sms_updates'    => 1,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedAction'     => 'createNewCustomer',
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => '',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 1,
                    'sms_marketing_subscription_flag' => 1
                ]
            ],
            [ // empty subscription flags. will trigger createNewCustomer logic
                'data'               => [
                    'type'       => 'customer',
                    'attributes' => [
                        'user_id' => 1,
                        'email'   => '<EMAIL>',
                        'phone'   => '+15145131515',
                        'name'    => 'Karl6 Sun6',
                        'locale'  => 'en_US'
                    ],
                    'meta'       => [
                        'receive_communications' => 0,
                        'receive_sms_updates'    => 0,
                        'source_title'           => 'Title for Karl',
                        'source_url'             => 'http://someurl.com/'
                    ]
                ],
                'expectedAction'     => 'createNewCustomer',
                'expectedStatusCode' => 200,
                'expectedContent'    => [
                    'user_id'                         => 1,
                    'email'                           => '<EMAIL>',
                    'phone'                           => '+15145131515',
                    'name'                            => 'Karl6 Sun6',
                    'first_name'                      => 'Karl6',
                    'last_name'                       => 'Sun6',
                    'locale'                          => 'en_US',
                    'subcribtion_flag'                => 0,
                    'sms_marketing_subscription_flag' => 0
                ]
            ],
        ];
    }


    public function getAllSubscribeStatusData()
    {
        Fixtures::add('subscribedSpectrum', [
            'sf_customer' => [
                [
                    'ID'         => 100,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Jordan',
                    'last_name'  => 'Mallette',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 0,
                ],
                [
                    'ID'         => 101,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Jordan',
                    'last_name'  => 'Mallette',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 1,
                ],
                [
                    'ID'         => 102,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Jordan',
                    'last_name'  => 'Mallette',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 2,
                ],
            ]
        ]);
    }

    public function setupCustomerFavorites()
    {
        Fixtures::add('setupCustomerFavorites', [
            'sf_customer' => [
                [
                    'ID'         => 333,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Mike',
                    'last_name'  => 'Abbot',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 1,
                ],
                [
                    'ID'         => 444,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Bob',
                    'last_name'  => 'Russel',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 1,
                ],

            ],
            'sf_customer_favorites' => [
                [
                    "id" => 1,
                    'user_id' => 1,
                    'customer_id' => 444,
                    'is_favorite' => 1
                ],
            ],
            'sf_customer_tags_relationships' => [
                [
                    'customer_id' => 444,
                    'tag_id'      => 1,
                    'created_at'  => '2018-09-24 00:00:00',
                    'updated_at'  => '2018-09-24 00:00:00',
                ],
            ],
            'sf_customer_tags' => [
                [
                    'id'              => 1,
                    'retailer_tag_id' => 'green',
                    'name'            => 'green',
                    'status'          => 'active'
                ],
            ],
        ]);

        Fixtures::add('setupCustomerFavoritesForES', [
            'sf_customer' => [
                [
                    'ID'         => 333,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Mike',
                    'last_name'  => 'Abbot',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 1,
                ],
                [
                    'ID'         => 334,
                    'user_id'    => 1,
                    'email'      => '<EMAIL>',
                    'first_name' => 'Mikey',
                    'last_name'  => 'Abbottsville',
                    'origin'     => Customer::ORIGIN_UNKNOWN,
                    'subcribtion_flag' => 1,
                ],
            ],

        ]);
    }

    public function testGetCustomerObfuscated()
    {
        Fixtures::add('testGetCustomerObfuscated', [
            'sf_customer' => [
                [
                    'ID' => 3,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'JoJo',
                    'phone'                => '+15141231234',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'first_name'           => 'Jo',
                    'last_name'            => 'Ma',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
            ],
            'sf_customer_meta' => [
                [
                    'customer_id' => 3,
                    'type' => 'phone',
                    'value' => "+15141231234",
                    'label' => 'home',
                    'position' => 0,
                ],
                [
                    'customer_id' => 3,
                    'type' => 'email',
                    'value' => "<EMAIL>",
                    'label' => 'home',
                    'position' => 1,
                ]
            ],
            'sf_customer_addresses' => [
                [
                    'customer_id' => 3,
                    'address_line_1' => '1324 a long street name',
                    'address_line_2' => '134nd floor',
                    'postal_code' => 'H7W 4C7',
                    'state' => 'QC',
                    'city' => 'Montreal',
                    'country' => 'Canada',
                    'label' => 'home',
                ]
            ]
        ]);
    }

    public function testCustomerUpdateDuplicateEmailTeamMode()
    {
        Fixtures::add('testCustomerUpdateDuplicateEmailTeamMode', [
            'sf_customer' => [
                [
                    'ID' => 3,
                    'user_id' => 3001,
                    'email' => '<EMAIL>',
                    'name' => 'Johnny doe1',
                    'name' => 'JoJo',
                    'phone' => '',
                    'localization' => null,
                    'geo' => '',
                    'comment' => '',
                    'subcribtion_flag' => 1,
                    'first_name' => 'Jo',
                    'last_name' => 'Ma',
                ],
                [
                    'ID' => 4,
                    'user_id' => 3001,
                    'email' => '<EMAIL>',
                    'name' => 'Johnny doe2',
                    'phone' => '',
                    'localization' => null,
                    'geo' => '',
                    'comment' => '',
                    'subcribtion_flag' => 1,
                    'first_name' => 'Jo',
                    'last_name' => 'Ma',
                ],
            ],
            'sf_customer_meta' => [
                [
                    'customer_id' => 3,
                    'type' => 'email',
                    'value' => '<EMAIL>',
                    'label' => 'home',
                    'position' => 0,
                ],
                [
                    'customer_id' => 4,
                    'type' => 'email',
                    'value' => '<EMAIL>',
                    'label' => 'home',
                    'position' => 0,
                ]
            ]
        ]);
    }

    public function testCustomerUpdateDuplicateEmailRepMode()
    {
        Fixtures::add('testCustomerUpdateDuplicateEmailRepMode', [
            'sf_customer' => [
                [
                    'ID' => 3,
                    'user_id' => 1,
                    'email' => '<EMAIL>',
                    'name' => 'Johnny doe1',
                    'name' => 'JoJo',
                    'phone' => '',
                    'localization' => null,
                    'geo' => '',
                    'comment' => '',
                    'subcribtion_flag' => 1,
                    'first_name' => 'Jo',
                    'last_name' => 'Ma',
                ],
                [
                    'ID' => 4,
                    'user_id' => 1,
                    'email' => '<EMAIL>',
                    'name' => 'Johnny doe2',
                    'phone' => '',
                    'localization' => null,
                    'geo' => '',
                    'comment' => '',
                    'subcribtion_flag' => 1,
                    'first_name' => 'Jo',
                    'last_name' => 'Ma',
                ],
            ],
            'sf_customer_meta' => [
                [
                    'customer_id' => 3,
                    'type' => 'email',
                    'value' => '<EMAIL>',
                    'label' => 'home',
                    'position' => 0,
                ],
                [
                    'customer_id' => 4,
                    'type' => 'email',
                    'value' => '<EMAIL>',
                    'label' => 'home',
                    'position' => 0,
                ]
            ]
        ]);
    }
    public function advancedSearch()
    {
        Fixtures::add(
            'AdvancedSearchContacts',
            [
                'sf_customer' => [
                    [
                        'ID'         => 20,
                        'user_id'    => 1,
                        'email'      => '<EMAIL>',
                        'first_name' => 'First20name',
                        'last_name'  => 'Last20name',
                        'origin'     => Customer::ORIGIN_UNKNOWN,
                        'subcribtion_flag' => 1,
                        'phone'      => '+5144321820',
                        'created' => '2017-09-24 00:00:00',
                        'geo' => '',
                        'comment' => 'customer20 comment',
                    ],
                    [
                        'ID'         => 21,
                        'user_id'    => 1,
                        'email'      => '<EMAIL>',
                        'first_name' => 'First21name',
                        'last_name'  => 'Last21name',
                        'origin'     => Customer::ORIGIN_UNKNOWN,
                        'subcribtion_flag' => 1,
                        'phone'      => '+5144321821',
                        'created' => '2017-09-25 00:00:00',
                        'geo' => '',
                        'comment' => 'customer21 comment',
                    ]
                ],
                'sf_customer_addresses' => [
                    [
                        'customer_id' => 20,
                        'address_line_1' => '2691 Ave Van Horne',
                        'address_line_2' => null,
                        'postal_code' => 'H3S 1P6',
                        'state' => 'QC',
                        'city' => 'Montreal',
                        'country' => 'Canada',
                        'label' => 'home',
                        'is_default' => 1,
                    ],
                    [
                        'customer_id' => 21,
                        'address_line_1' => '212 US-20',
                        'address_line_2' => null,
                        'postal_code' => '68763',
                        'city' => 'Toronto',
                        'state' => 'ON',
                        'country' => 'Canada',
                        'label' => 'home',
                        'is_default' => 0,
                    ]
                ],
                'sf_customer_notes' => [
                    [
                        'customer_id' => 20,
                        'note'        => 'First21name Last21name note20 customer21 +5144321821-0021',
                        'created_at'   => '2018-09-24 00:00:00',
                        'updated_at'  => '2018-09-24 00:00:00',
                        'updated_by' => 1,
                    ],
                    [
                        'customer_id' => 21,
                        'note'        => 'First20name Last20name note21 customer20 +5144321820-0020',
                        'created_at'   => '2018-09-25 00:00:00',
                        'updated_at'  => '2018-09-25 00:00:00',
                        'updated_by' => 2,
                    ],
                ],
                'sf_customer_tags' => [
                    [
                        'id' => 1,
                        'retailer_tag_id' => '1',
                        'name' => 'tag1',
                        'status' => 'active'
                    ],
                    [
                        'id' => 2,
                        'retailer_tag_id' => '2',
                        'name' => 'tag2',
                        'status' => 'active'
                    ]
                ],
                'sf_customer_tags_relationships' => [
                    [
                        'tag_id' => 1,
                        'customer_id' => 20,
                        'created_by' => 1,
                    ],
                    [
                        'tag_id' => 2,
                        'customer_id' => 20,
                        'created_by' => 1,
                    ],
                ],
                'sf_customer_favorites' => [
                    [
                        'user_id' => 1,
                        'customer_id' => 20,
                        'is_favorite' => 1,
                    ],
                ],
                'sf_customer_activity_feed' => [
                    [
                        'customer_id' =>  20,
                        'user_id' =>  1,
                        'type' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_CALL_ATTEMPTED,
                        'thread_id' =>  '',
                        'preview' =>  null,
                        'direction' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                        'status' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD,
                        'created_at' => '2018-09-24 00:00:00',
                    ],
                    [
                        'customer_id' =>  21,
                        'user_id' =>  1,
                        'type' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_SHARE_EMAIL,
                        'thread_id' =>  '',
                        'preview' =>  null,
                        'direction' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                        'status' =>  CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_READ,
                        'created_at' => '2018-09-25 00:00:00',
                    ],
                ],
            ]
        );

        Fixtures::add(
            'AdvancedSearchRetailerCustomers',
            [
                'sf_retailer_customers' => [
                    [
                        'id' => 1,
                        'customer_id' => 'customer1id',
                        'gender' => 'female',
                        'first_name' => 'first1name',
                        'last_name' => 'last1name',
                        'email' => '<EMAIL>',
                        'email_label' => 'Personal',
                        'phone' => '************',
                        'phone_label' => 'Work',
                        'address_line1' => '73975 Borer Mallll',
                        'address_line2' => 'Juana Square',
                        'zipcode' => '91266-7456',
                        'postalcode' => '09302-0963',
                        'city' => 'East Maryjaneview',
                        'state' => 'California',
                        'country' => 'Congo',
                        'longitude' => '106.47575000',
                        'latitude' => '42.32311300',
                        'is_subscribed' => '0',
                        'limited_visibility' => '1',
                    ],
                    [
                        'id' => 2,
                        'customer_id' => 'customer2id',
                        'gender' => 'female',
                        'first_name' => 'first2name',
                        'last_name' => 'last2name',
                        'email' => '<EMAIL>',
                        'email_label' => 'Personal',
                        'phone' => '15144321222',
                        'phone_label' => 'Work',
                        'address_line1' => '73975 Borer Mallll',
                        'address_line2' => 'Juana Square',
                        'zipcode' => '91266-7456',
                        'postalcode' => '09302-0963',
                        'city' => 'East Maryjaneview',
                        'state' => 'NY',
                        'country' => 'Congo',
                        'longitude' => '106.47575000',
                        'latitude' => '42.32311300',
                        'is_subscribed' => '1',
                        'limited_visibility' => '0',
                        'is_subscribed_sms_marketing' => '1',
                    ],
                ],
                'sf_retailer_customer_meta'           => [
                    [
                        'customer_id'       => 'customer1id',
                        'type'              => 'email',
                        'value'             => '<EMAIL>',
                        'position'          => 0,
                        'label'             => 'Home',
                    ],
                ],
                'sf_retailer_customer_addresses'    => [
                    [
                        'customer_id'    => 'customer1id',
                        'address_line_1' => '2162 Elton Pl',
                        'postal_code'    => 'h4gT48',
                        'state'          => 'QC',
                        'city'           => 'Montreal',
                        'country'        => 'Canada',
                        'is_default'     => '1',
                        'label'          => 'Home',
                    ],
                    [
                        'customer_id'    => 'customer2id',
                        'address_line_1' => '2162 Elton Pl',
                        'postal_code'    => 'h4gT48',
                        'state'          => 'ON',
                        'city'           => 'Toronto',
                        'country'        => 'Canada',
                        'is_default'     => '1',
                        'label'          => 'Home',
                    ],
                ],
                'sf_retailer_customer_notes' => [
                    [
                        'customer_id'    => 'customer1id',
                        'position' => '1',
                        'note'    => 'customer1id note1 customer2id',
                    ],
                    [
                        'customer_id'    => 'customer2id',
                        'position' => '1',
                        'note'    => 'customer1id note2 customer2id',
                    ],
                ],
                'sf_customers_to_retailer_customers' => [
                    [
                        'customer_id' => '20',
                        'retailer_customer_id' => '1',
                    ],
                    [
                        'customer_id' => '21',
                        'retailer_customer_id' => '2',
                    ]
                ]
            ]
        );

        Fixtures::add(
            'AdvancedSearchTransactions',
            [
                'sf_rep_transaction' => [
                    [
                        'user_id' => '1',
                        'trx_id' => 'TRX20XXXXX1',
                        'trx_date' => '2020-02-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_apply_total' => '120.0000',
                        'trx_total' => '120.0000',
                        'status' => '1',
                        'received_date' => '2020-11-19 22:02:54',
                        'customer_name' => 'First20name Last20name',
                        'customer_email' => '<EMAIL>',
                        'ip' => '***********',
                        'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like ',
                        'acquisition' => 'shoppingLink',
                        'currency' => 'USD',
                        'attribution' => 'shoppingLink',
                        'customer_id' => '20',
                        'fingerprint' => '13453905751789',
                        'trx_thread_id' => 'TRX20XXXXX1',
                        'origin' => 'VariableSetting',
                    ],
                    [
                        'user_id' => '1',
                        'trx_id' => 'TRX20XXXXX2',
                        'trx_date' => '2020-06-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_apply_total' => '220.0000',
                        'trx_total' => '220.0000',
                        'status' => '1',
                        'received_date' => '2021-11-19 22:02:54',
                        'customer_name' => 'First20name Last20name',
                        'customer_email' => '<EMAIL>',
                        'ip' => '***********',
                        'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like ',
                        'acquisition' => 'shoppingLink',
                        'currency' => 'USD',
                        'attribution' => 'shoppingLink',
                        'customer_id' => '20',
                        'fingerprint' => '13453905751789',
                        'trx_thread_id' => 'TRX20XXXXX2',
                        'origin' => 'VariableSetting',
                    ],
                    [
                        'user_id' => '1',
                        'trx_id' => 'TRX21XXXXX1',
                        'trx_date' => '2021-02-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_apply_total' => '320.0000',
                        'trx_total' => '320.0000',
                        'status' => '1',
                        'received_date' => '2020-11-19 22:02:54',
                        'customer_name' => 'First21name Last21name',
                        'customer_email' => '<EMAIL>',
                        'ip' => '***********',
                        'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like ',
                        'acquisition' => 'shoppingLink',
                        'currency' => 'USD',
                        'attribution' => 'shoppingLink',
                        'customer_id' => '21',
                        'fingerprint' => '13453905751789',
                        'trx_thread_id' => 'TRX21-1',
                        'origin' => 'VariableSetting',
                    ],
                    [
                        'user_id' => '1',
                        'trx_id' => 'TRX21XXXXX2',
                        'trx_date' => '2021-06-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_apply_total' => '420.0000',
                        'trx_total' => '420.0000',
                        'status' => '1',
                        'received_date' => '2021-11-19 22:02:54',
                        'customer_name' => 'First21name Last21name',
                        'customer_email' => '<EMAIL>',
                        'ip' => '***********',
                        'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like ',
                        'acquisition' => 'shoppingLink',
                        'currency' => 'USD',
                        'attribution' => 'shoppingLink',
                        'customer_id' => '21',
                        'fingerprint' => '13453905751789',
                        'trx_thread_id' => 'TRX21XXXXX2',
                        'origin' => 'VariableSetting',
                    ],
                ],
                'sf_rep_transaction_detail' => [
                    [
                        'trx_id' => 'TRX20XXXXX1',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '60.0000',
                        'trx_detail_total' => '60.0000',
                        'product_id' => 'product1_trx20xxxxx1',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku1_trx20xxxxx1',
                        'trx_thread_id' => 'TRX20XXXXX1',
                    ],
                    [
                        'trx_id' => 'TRX20XXXXX1',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '60.0000',
                        'trx_detail_total' => '60.0000',
                        'product_id' => 'product1_trx20xxxxx1_extra',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku1_trx20xxxxx1_extra',
                        'trx_thread_id' => 'TRX20XXXXX1',
                    ],
                    [
                        'trx_id' => 'TRX20XXXXX1',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '60.0000',
                        'trx_detail_total' => '60.0000',
                        'product_id' => 'product2_trx20xxxxx1',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku2_trx20xxxxx1',
                        'trx_thread_id' => 'TRX20XXXXX1',
                    ],

                    [
                        'trx_id' => 'TRX20XXXXX2',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '110.0000',
                        'trx_detail_total' => '110.0000',
                        'product_id' => 'product1_trx20xxxxx2',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku1_trx20xxxxx2',
                        'trx_thread_id' => 'TRX20XXXXX2',
                    ],
                    [
                        'trx_id' => 'TRX20XXXXX2',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '110.0000',
                        'trx_detail_total' => '110.0000',
                        'product_id' => 'product2_trx20xxxxx2',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku2_trx20xxxxx2',
                        'trx_thread_id' => 'TRX20XXXXX2',
                    ],


                    [
                        'trx_id' => 'TRX21XXXXX1',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '60.0000',
                        'trx_detail_total' => '60.0000',
                        'product_id' => 'product1_trx21xxxxx1',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku1_trx21xxxxx1',
                        'trx_thread_id' => 'TRX21XXXXX1',
                    ],
                    [
                        'trx_id' => 'TRX21XXXXX1',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '60.0000',
                        'trx_detail_total' => '60.0000',
                        'product_id' => 'product2_trx21xxxxx1',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku2_trx21xxxxx1',
                        'trx_thread_id' => 'TRX21XXXXX1',
                    ],


                    [
                        'trx_id' => 'TRX21XXXXX2',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '110.0000',
                        'trx_detail_total' => '110.0000',
                        'product_id' => 'product1_trx21xxxxx2',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku1_trx21xxxxx2',
                        'trx_thread_id' => 'TRX21XXXXX2',
                    ],
                    [
                        'trx_id' => 'TRX21XXXXX2',
                        'trx_detail_id' => '',
                        'trx_detail_apply_total' => '110.0000',
                        'trx_detail_total' => '110.0000',
                        'product_id' => 'product2_trx21xxxxx2',
                        'quantity' => '1',
                        'units' => 'each',
                        'sku' => 'sku2_trx21xxxxx2',
                        'trx_thread_id' => 'TRX21XXXXX2',
                    ],
                ],
                'sf_product_variants' => [
                    [
                        'product_id' => 'product1_trx20xxxxx1',
                        'sku' => 'sku1_trx20xxxxx1',
                        'name' => 'product20-1-1',
                        'description' => 'product 20 1 1',
                        'price' => '60.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '60.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-20-1-1',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku1_trx20xxxxx1',
                    ],
                    [
                        'product_id' => 'product1_trx20xxxxx1_extra',
                        'sku' => 'sku1_trx20xxxxx1_extra',
                        'name' => 'product20-1-1-extra',
                        'description' => 'product 20 1 1 extra',
                        'price' => '60.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '60.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        // Same brand as product "product1_trx20xxxxx1"
                        'brand' => 'BRAND-20-1-1',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku1_trx20xxxxx1_extra',
                    ],
                    [
                        'product_id' => 'product2_trx20xxxxx1',
                        'sku' => 'sku2_trx20xxxxx1',
                        'name' => 'product20-1-2',
                        'description' => 'product 20 1 2',
                        'price' => '60.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '60.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-20-1-2',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku2_trx20xxxxx1',
                    ],

                    [
                        'product_id' => 'product1_trx20xxxxx2',
                        'sku' => 'sku1_trx20xxxxx2',
                        'name' => 'product20-2-1',
                        'description' => 'product 20 2 1',
                        'price' => '1100.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '1100.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-20-2-1',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku1_trx20xxxxx2',
                    ],
                    [
                        'product_id' => 'product2_trx20xxxxx2',
                        'sku' => 'sku2_trx20xxxxx2',
                        'name' => 'product20-2-2',
                        'description' => 'product 20 2 2',
                        'price' => '1100.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '1100.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-20-2-2',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku2_trx20xxxxx2',
                    ],

                    [
                        'product_id' => 'product1_trx21xxxxx1',
                        'sku' => 'sku1_trx21xxxxx1',
                        'name' => 'product21-1-1',
                        'description' => 'product 21 1 1',
                        'price' => '60.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '60.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-21-1-1',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku1_trx21xxxxx1',
                    ],
                    [
                        'product_id' => 'product2_trx21xxxxx1',
                        'sku' => 'sku2_trx21xxxxx1',
                        'name' => 'product21-1-2',
                        'description' => 'product 21 1 2',
                        'price' => '60.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '60.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-21-1-2',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku2_trx21xxxxx1',
                    ],

                    [
                        'product_id' => 'product1_trx21xxxxx2',
                        'sku' => 'sku1_trx21xxxxx2',
                        'name' => 'product21-2-1',
                        'description' => 'product 21 2 1',
                        'price' => '110.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '110.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-21-2-1',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku1_trx21xxxxx2',
                    ],
                    [
                        'product_id' => 'product2_trx21xxxxx2',
                        'sku' => 'sku2_trx21xxxxx2',
                        'name' => 'product21-2-2',
                        'description' => 'product 21 2 2',
                        'price' => '110.00',
                        'available' => '1',
                        'product_url' => 'https://stg-adm.stores.holtrenfrew.com/en/Products/Womens/Womens-Clothing/Coats/Puffer-Coats/49-WINTERS-Mayfair-Down-Puffer-Jacket/p/20000005001',
                        'image_url' => 'https://s7d9.scene7.com/is/image/HoltRenfrew1/m_4311270203_01',
                        'price_deal' => '110.00',
                        'deal_start_date' => '2020-09-05',
                        'deal_end_date' => '9999-12-31',
                        'brand' => 'BRAND-21-2-2',
                        'is_default' => '1',
                        'arrival_date' => '2018-12-20',
                        'gtin' => 'sku2_trx21xxxxx2',
                    ],
                ],
                'sf_categories' => [
                    [
                        'name' => 'Category20-1-1',
                        'category_id' => 'category-id-20-1-1',
                        'language' => 'en_CA',
                    ],
                    [
                        'name' => 'Category20-1-2',
                        'category_id' => 'category-id-20-1-2',
                        'language' => 'en_CA',
                    ],

                    [
                        'name' => 'Category20-2-1',
                        'category_id' => 'category-id-20-2-1',
                        'language' => 'en_CA',
                    ],
                    [
                        'name' => 'Category20-2-2',
                        'category_id' => 'category-id-20-2-2',
                        'language' => 'en_CA',
                    ],

                    [
                        'name' => 'Category21-1-1',
                        'category_id' => 'category-id-21-1-1',
                        'language' => 'en_CA',
                    ],
                    [
                        'name' => 'Category21-1-2',
                        'category_id' => 'category-id-21-1-2',
                        'language' => 'en_CA',
                    ],

                    [
                        'name' => 'Category21-2-1',
                        'category_id' => 'category-id-21-2-1',
                        'language' => 'en_CA',
                    ],
                    [
                        'name' => 'Category21-2-2',
                        'category_id' => 'category-id-21-2-2',
                        'language' => 'en_CA',
                    ],
                ],
                'sf_product_category_map' => [
                    [
                        'product_id' => 'product1_trx20xxxxx1',
                        'category_id' => 'category-id-20-1-1',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],
                    // Extra product has same category as 'product1_trx20xxxxx1'
                    [
                        'product_id' => 'product1_trx20xxxxx1_extra',
                        'category_id' => 'category-id-20-1-1',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],
                    [
                        'product_id' => 'product2_trx20xxxxx1',
                        'category_id' => 'category-id-20-1-2',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],

                    [
                        'product_id' => 'product1_trx20xxxxx2',
                        'category_id' => 'category-id-20-2-1',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],
                    [
                        'product_id' => 'product2_trx20xxxxx2',
                        'category_id' => 'category-id-20-2-2',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],

                    [
                        'product_id' => 'product1_trx21xxxxx1',
                        'category_id' => 'category-id-21-1-1',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],
                    [
                        'product_id' => 'product2_trx21xxxxx1',
                        'category_id' => 'category-id-21-1-2',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],

                    [
                        'product_id' => 'product1_trx21xxxxx2',
                        'category_id' => 'category-id-21-2-1',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],
                    [
                        'product_id' => 'product2_trx21xxxxx2',
                        'category_id' => 'category-id-21-2-2',
                        'brand' => null,
                        'storefront_slot' => 0,
                    ],
                ],
            ],
        );

        Fixtures::add(
            'AdvancedSearchRetailerTransactions',
            [
                'sf_retailer_transaction' => [
                    [
                        'customer_id' => 'customer1id',
                        'trx_id' => 'retailer_TRX20XXXXX1',
                        'trx_date' => '2022-02-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_total' => '1120.0000',
                        'currency' => 'USD',
                        'trx_thread_id' => 'retailer_TRX20XXXXX1',
                    ],
                    [
                        'customer_id' => 'customer1id',
                        'trx_id' => 'retailer_TRX20XXXXX2',
                        'trx_date' => '2022-06-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_total' => '1220.0000',
                        'currency' => 'USD',
                        'trx_thread_id' => 'retailer_TRX20XXXXX2',
                    ],
                    [
                        'customer_id' => 'customer2id',
                        'trx_id' => 'retailer_TRX21XXXXX1',
                        'trx_date' => '2023-02-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_total' => '1320.0000',
                        'currency' => 'USD',
                        'trx_thread_id' => 'retailer_TRX21-1',
                    ],
                    [
                        'customer_id' => 'customer2id',
                        'trx_id' => 'retailer_TRX21XXXXX2',
                        'trx_date' => '2023-06-19 22:02:54',
                        'trx_type' => 'sale',
                        'trx_total' => '1420.0000',
                        'currency' => 'USD',
                        'trx_thread_id' => 'retailer_TRX21XXXXX2',
                    ],
                ],
                'sf_retailer_transaction_details' => [
                    [
                        'trx_id' => 'retailer_TRX20XXXXX1',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '60.0000',
                        'product_id' => 'product1_trx20xxxxx1',
                        'product_quantity' => '1',
                        'sku' => 'sku1_trx20xxxxx1',
                        'trx_thread_id' => 'retailer_TRX20XXXXX1',
                    ],
                    [
                        'trx_id' => 'retailer_TRX20XXXXX1',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '60.0000',
                        'product_id' => 'product2_trx20xxxxx1',
                        'product_quantity' => '1',
                        'sku' => 'sku2_trx20xxxxx1',
                        'trx_thread_id' => 'retailer_TRX20XXXXX1',
                    ],

                    [
                        'trx_id' => 'retailer_TRX20XXXXX2',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '110.0000',
                        'product_id' => 'product1_trx20xxxxx2',
                        'product_quantity' => '1',
                        'sku' => 'sku1_trx20xxxxx2',
                        'trx_thread_id' => 'retailer_TRX20XXXXX2',
                    ],
                    [
                        'trx_id' => 'retailer_TRX20XXXXX2',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '110.0000',
                        'product_id' => 'product2_trx20xxxxx2',
                        'product_quantity' => '1',
                        'sku' => 'sku2_trx20xxxxx2',
                        'trx_thread_id' => 'retailer_TRX20XXXXX2',
                    ],
                    [
                        'trx_id' => 'retailer_TRX21XXXXX1',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '60.0000',
                        'product_id' => 'product1_trx21xxxxx1',
                        'product_quantity' => '1',
                        'sku' => 'sku1_trx21xxxxx1',
                        'trx_thread_id' => 'retailer_TRX21XXXXX1',
                    ],
                    [
                        'trx_id' => 'retailer_TRX21XXXXX1',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '60.0000',
                        'product_id' => 'product2_trx21xxxxx1',
                        'product_quantity' => '1',
                        'sku' => 'sku2_trx21xxxxx1',
                        'trx_thread_id' => 'retailer_TRX21XXXXX1',
                    ],
                    [
                        'trx_id' => 'retailer_TRX21XXXXX2',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '110.0000',
                        'product_id' => 'product1_trx21xxxxx2',
                        'product_quantity' => '1',
                        'sku' => 'sku1_trx21xxxxx2',
                        'trx_thread_id' => 'retailer_TRX21XXXXX2',
                    ],
                    [
                        'trx_id' => 'retailer_TRX21XXXXX2',
                        'trx_type' => 'Sale',
                        'trx_date' => '1970-01-01 00:00:00',
                        'product_price' => '110.0000',
                        'product_id' => 'product2_trx21xxxxx2',
                        'product_quantity' => '1',
                        'sku' => 'sku2_trx21xxxxx2',
                        'trx_thread_id' => 'retailer_TRX21XXXXX2',
                    ],
                ],
            ],
        );
    }

    public function addCustomerDataForPII()
    {
        Fixtures::add(
            'CustomerDataForPII',
            [
                'sf_customer' => [
                    [
                        'ID'         => 8,
                        'user_id'    => 1,
                        'email'      => '<EMAIL>',
                        'first_name' => 'Deborah',
                        'last_name'  => 'Green',
                        'origin'     => Customer::ORIGIN_UNKNOWN,
                        'subcribtion_flag' => 1,
                    ]
                ],
                'sf_retailer_customers' => [
                    [
                        'id' => 5,
                        'customer_id' => 'customer1id',
                        'gender' => 'female',
                        'first_name' => 'Marge',
                        'last_name' => 'Simpsons',
                        'email' => '<EMAIL>',
                        'email_label' => 'Personal',
                        'phone' => '************',
                        'phone_label' => 'Work',
                        'address_line1' => '73975 Borer Mallll',
                        'address_line2' => 'Juana Square',
                        'zipcode' => '91266-7456',
                        'postalcode' => '09302-0963',
                        'city' => 'East Maryjaneview',
                        'state' => 'California',
                        'country' => 'Congo',
                        'longitude' => '106.47575000',
                        'latitude' => '42.32311300',
                        'is_subscribed' => '0',
                        'limited_visibility' => '1',
                    ],
                ],
                'sf_customers_to_retailer_customers' => [
                    [
                        'customer_id' => '8',
                        'retailer_customer_id' => '5',
                    ],
                ]
            ]
        );
    }
}
