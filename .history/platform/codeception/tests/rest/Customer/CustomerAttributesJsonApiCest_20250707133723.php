<?php

declare(strict_types=1);

namespace SF\rest\Customer;

use Codeception\Util\Fixtures;
use Salesfloor\API\Managers\Client\Customers\Legacy;
use Salesfloor\API\Managers\Client\RetailerCustomers\Legacy as LegacyRetailerCustomer;
use Salesfloor\Models\CustomerAttribute;
use SF\rest\BaseRest;
use SF\RestTester;

class CustomerAttributesJsonApiCest extends BaseRest
{
    /** @group database_transaction */
    public function testOnlyAllowedCustomerAttributesWithIsSearchableEnabledIsReturned(RestTester $I)
    {
        $I->wantTo("Only allowed customer attributes that qualify as is_searchable is returned in response");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');

        $response = $I->doDirectGet($I, '/v2/customer-attributes?filter[is_searchable]=1');
        $searchableRuleTypes = (new CustomerAttribute())->searchableRuleTypes();
        $attribute_types = array_map(function ($data) {
            return $data->attributes->attribute_type;
        }, $response->data);
        $I->assertCount(0, array_diff($attribute_types, $searchableRuleTypes));
    }
    /** @group database_transaction */
    public function testOnlyAllowedCustomerAttributesWithIsFilterableEnabledIsReturned(RestTester $I)
    {
        $I->wantTo("Only allowed customer attributes that qualify as is_filterable is returned in response");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttribute');

        $response = $I->doDirectGet($I, '/v2/customer-attributes?filter[is_filterable]=1');
        $filterableRuleTypes = (new CustomerAttribute())->filterableRuleTypes();
        $attribute_types = array_map(function ($data) {
            return $data->attributes->attribute_type;
        }, $response->data);
        $I->assertCount(0, array_diff($attribute_types, $filterableRuleTypes));
    }

    /** @group database_transaction */
    public function testOnlyAllowedCustomerAttributesWithIsFilterableAndIsSearchableEnabledIsReturned(RestTester $I)
    {
        $I->wantTo("Only allowed customer attributes that  qualify as is_filterable and is_searchable is returned in response");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttribute');

        $response = $I->doDirectGet($I, '/v2/customer-attributes?filter[is_filterable]=1&filter[is_searchable]=1');
        $ca = new CustomerAttribute();
        $filterableRuleTypes = $ca->filterableRuleTypes();
        $searchableRuleTypes = $ca->searchableRuleTypes();
        $attribute_types = array_map(function ($data) {
            return $data->attributes->attribute_type;
        }, $response->data);
        $I->assertCount(0, array_diff($attribute_types, array_merge($filterableRuleTypes, $searchableRuleTypes)));
    }

    /** @group database_transactionaa */
    public function testUpdateCustomerAttributeValueValidSingle(RestTester $I)
    {
        $I->wantTo('Test update one customer attribute value to another valid attribute value');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-single');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;
        $expected = $requestData['included'];

        // the attribute value change from 180 => 181 in patch data
        foreach ($expected as $key => $values) {
            $I->assertEquals('customer-to-customer-attribute', $included[$key]->type);
            $I->assertEquals($values['attributes']['attribute_id'], $included[$key]->attributes->attribute_id);
            $I->assertEquals($values['attributes']['attribute_value'], $included[$key]->attributes->attribute_value);
            $I->assertEquals($values['attributes']['customer_id'], $included[$key]->attributes->customer_id);
        }
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueValidMultiplePatch(RestTester $I)
    {
        $I->wantTo('Test update one customer multiple attribute value to other valid multiple attribute values');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithMultipleAttribute');

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-multiple');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;

        $expected = $requestData['included'];
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected[0]['attributes']['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected[0]['attributes']['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected[0]['attributes']['customer_id'], $included[0]->attributes->customer_id);

        $I->assertEquals('customer-to-customer-attribute', $included[1]->type);
        $I->assertEquals($expected[1]['attributes']['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($expected[1]['attributes']['attribute_value'], $included[1]->attributes->attribute_value);
        $I->assertEquals($expected[1]['attributes']['customer_id'], $included[1]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueValidMultiplePostAndPatch(RestTester $I)
    {
        $I->wantTo('Test update one customer multiple attribute value to other valid multiple attribute values ( post + patch sametime)');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-multiple-post-and-patch');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;

        $expected = $requestData['included'];
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected[0]['attributes']['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected[0]['attributes']['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected[0]['attributes']['customer_id'], $included[0]->attributes->customer_id);

        $I->assertEquals('customer-to-customer-attribute', $included[1]->type);
        $I->assertEquals($expected[1]['attributes']['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($expected[1]['attributes']['attribute_value'], $included[1]->attributes->attribute_value);
        $I->assertEquals($expected[1]['attributes']['customer_id'], $included[1]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueInValidMultiple(RestTester $I)
    {
        $I->wantTo('Test update one customer multiple attribute value to other valid multiple attribute values');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithMultipleAttribute');

        $requestData = Fixtures::get('customer-with-customer-attribute-invalid-multiple');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;

        // sf_customers_to_customer_attributes fixture data
        $attributeFixture = Fixtures::get('CustomerProperties');
        $expected0 = $attributeFixture['sf_customers_to_customer_attributes'][0];
        $I->wantTo('this data keep as before from db fixture');
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected0['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected0['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected0['customer_id'], $included[0]->attributes->customer_id);

        $I->wantTo('this data be updated by request data');
        $expected1 = $requestData['included'][1]['attributes'];
        $I->assertEquals('customer-to-customer-attribute', $included[1]->type);
        $I->assertEquals($expected1['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($expected1['attribute_value'], $included[1]->attributes->attribute_value);
        $I->assertEquals($expected1['customer_id'], $included[1]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueInvalid(RestTester $I)
    {
        $I->wantTo('Test update one customer attribute value to an invalid attribute value');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', Fixtures::get('customer-with-customer-attribute-invalid-single'));

        $included = $response->included;

        // we need get expected from fixture data: sf_customers_to_customer_attributes
        $attributeFixture = Fixtures::get('CustomerProperties');
        $expected0 = $attributeFixture['sf_customers_to_customer_attributes'][0];

        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected0['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected0['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected0['customer_id'], $included[0]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeIdInvalid(RestTester $I)
    {
        $I->wantTo('Test update one customer attribute to an invalid attribute id');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', Fixtures::get('customer-with-customer-attribute-invalid-id'));

        $included = $response->included;

        // we need get expected from fixture data: sf_customers_to_customer_attributes
        $attributeFixture = Fixtures::get('CustomerProperties');
        $expected0 = $attributeFixture['sf_customers_to_customer_attributes'][0];

        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected0['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected0['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected0['customer_id'], $included[0]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testSearchCustomerWithExtendedContactAttributeIsSearchableEnabled(RestTester $I)
    {
        $I->wantTo("Search for a customer using an extended attribute marked as searchable");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttributeData');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $userId = 6;
        $userName = 'user5';

        // Text
        $this->setTokenInRequestHeader($I, $userId, $userName);
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=black');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Single select
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=cat');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Multi select
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=nodejs');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Multi select again, it's not a duplicated test, please keep it.
        // We had an issue that if there're more than one multi-select.
        // It's for function platform/api/app/src/be/Managers/Client/Base.php::cleanAttributesJson()
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=pear');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testFilterCustomerWithExtendedContactAttributeIsFilterableEnabled(RestTester $I)
    {
        $I->wantTo("Filter for a customer using an extended attribute marked as filterable");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttributeData');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        $manager->reindex();

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        // Boolean
        $response = $I->doDirectGet($I, '/v1/customers?filter[colored]=0');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Single select
        $response = $I->doDirectGet($I, '/v1/customers?filter[attr458]=1');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Multi select
        $response = $I->doDirectGet($I, '/v1/customers?filter[attr459]=1,2');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testSearchRetailerCustomerWithExtendedContactAttributeIsSearchableEnabled(RestTester $I)
    {
        $I->wantTo("Search for a retailer customer using an extended attribute marked as searchable");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttributeData');

        /** @var LegacyRetailerCustomer @manager */
        $manager = $this->app['retailer_customers.manager'];
        $manager->reindex();

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        $response = $I->doDirectGet($I, '/retailer-customers?filter[search]=green');

        $data = array_shift($response->data);
        $I->assertEquals('2001', $data->id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testFilterRetailerCustomerWithExtendedContactAttributeIsFilterableEnabled(RestTester $I)
    {
        $I->wantTo("Filter for a retailer customer using an extended attribute marked as filterable");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttributeData');

        /** @var LegacyRetailerCustomer @manager */
        $manager = $this->app['retailer_customers.manager'];
        $manager->reindex();

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        $response = $I->doDirectGet($I, '/retailer-customers?filter[colored]=0');

        $data = array_shift($response->data);
        $I->assertEquals('2001', $data->id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Single select
        $response = $I->doDirectGet($I, '/retailer-customers?filter[attr458]=1');
        $data = array_shift($response->data);
        $I->assertEquals('2001', $data->id);
        $I->assertEquals('<EMAIL>', $data->email);

        // Multi select
        $response = $I->doDirectGet($I, '/retailer-customers?filter[attr459]=1,2');
        $data = array_shift($response->data);
        $I->assertEquals('2001', $data->id);
        $I->assertEquals('<EMAIL>', $data->email);
    }


    /** @group database_transaction */
    public function testUpdatedCustomerAttributeValueIsSearchable(RestTester $I)
    {
        $I->wantTo('Test updated customer attribute value is reindexed and searchable on a search enabled type');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttributeData');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-single-searchable');
        $attributeId = $requestData['included'][0]['attributes']['attribute_id'];
        $data = $I->grabRowsFromDatabase('sf_customers_to_customer_attributes', null, ['attribute_id' => $attributeId]);

        $requestData["data"]["relationships"]["customer-to-customer-attribute"]["data"][0]["id"] = $data[0]["id"];
        $requestData["included"][0]["id"] = $data[0]["id"];

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        // initial search should return empty
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=pink');
        $I->assertEmpty($response->data);

        // simulate an update from mobile
        $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        // search again, reindex should have occurred
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=pink');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testUpdatedCustomerAttributeValueIsFilterable(RestTester $I)
    {
        $I->wantTo('Test updated customer attribute value is reindexed and filterable on a filter enabled type');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttributeData');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-single-filterable');

        $attributeId = $requestData['included'][0]['attributes']['attribute_id'];
        $data = $I->grabRowsFromDatabase('sf_customers_to_customer_attributes', null, ['attribute_id' => $attributeId]);
        $requestData["data"]["relationships"]["customer-to-customer-attribute"]["data"][0]["id"] = $data[0]["id"];
        $requestData["included"][0]["id"] = $data[0]["id"];

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        // initial search should return empty
        $response = $I->doDirectGet($I, '/v1/customers?filter[colored]=1');
        $I->assertEmpty($response->data);

        // simulate an update from mobile
        $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        // search again, reindex should have occurred
        $response = $I->doDirectGet($I, '/v1/customers?filter[colored]=1');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testCreateCustomerAttributeValueIsSearchable(RestTester $I)
    {
        $I->wantTo('Test create new customer attribute value is searchable');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $requestData = Fixtures::get('customer-with-customer-attribute-searchable-post');
        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        // initial search should return empty
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=brown');
        $I->assertEmpty($response->data);

        // simulate an update from mobile
        $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        // search again, reindex should have occurred
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=brown');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testCreateCustomerAttributeValueIsFilterable(RestTester $I)
    {
        $I->wantTo('Test create new customer attribute value is filterable');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithFilterableAttribute');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $requestData = Fixtures::get('customer-with-customer-attribute-filterable-post');
        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        // initial search should return empty
        $response = $I->doDirectGet($I, '/v1/customers?filter[colored]=1');
        $I->assertEmpty($response->data);

        // simulate an update from mobile
        $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        // search again, reindex should have occurred
        $response = $I->doDirectGet($I, '/v1/customers?filter[colored]=1');
        $data = array_shift($response->data);
        $I->assertEquals('1', $data->user_id);
        $I->assertEquals('<EMAIL>', $data->email);
    }

    /** @group database_transaction */
    public function testCannotSearchCustomerWithExtendedAttributeIfDisabled(RestTester $I)
    {
        $I->wantTo("Test cannot search customer with extended attribute if it's disabled");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttributeData');

        // populate elastic search indexes with customer data
        $this->app['configs']['retailer.modular_connect.extended_attributes.is_enabled'] = false;

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $userId = 6;
        $userName = 'user5';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        // Can not search extended attributes
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=black');
        $I->assertEmpty($response->data);

        // Still can search regular fields (name, email)
        $response = $I->doDirectGet($I, '/v1/customers?filter[search]=joseph');
        $I->assertCount(5, $response->data);
    }

    /** @group database_transaction */
    public function testCannotSearchRetailerCustomerWithExtendedAttributeIfDisabled(RestTester $I)
    {
        $I->wantTo("Test cannot search retailer customer with extended attribute if disabled");

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttribute');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithSearchableAttributeData');

        // populate elastic search indexes with retailer customer data
        $this->app['configs']['retailer.modular_connect.extended_attributes.is_enabled'] = false;

        /** @var LegacyRetailerCustomer @manager */
        $manager = $this->app['retailer_customers.manager'];
        $manager->reindex();

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, '/retailer-customers?filter[search]=green');
        $I->assertEmpty($response->data);

        $response = $I->doDirectGet($I, '/retailer-customers?filter[search]=joseph');
        $I->assertCount(5, $response->data);
    }
}
