"use strict";

var _excluded = ["page", "per_page"],
  _excluded2 = ["page", "per_page"];
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var s = Object.getOwnPropertySymbols(e); for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }
function _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.includes(n)) continue; t[n] = r[n]; } return t; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
angular.module('sfmobileApp').factory('contactService', ["$rootScope", "$resource", "$http", "$q", "localStorageService", "jsonapi", "apiService", "featuresEnabledService", "$routeParams", "CONST_CONTACTS", "CONST_TASKS", function ($rootScope, $resource, $http, $q, localStorageService, jsonapi, apiService, featuresEnabledService, $routeParams, CONST_CONTACTS, CONST_TASKS) {
  var searchStorageKeys = {
    contacts: CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA,
    customers: CONST_CONTACTS.LS_KEY_SEARCH_CUSTOMER_DATA
  };
  var consentType = CONST_CONTACTS.CONSENT_TYPE;
  var TASK_TYPE = CONST_TASKS.TASK_TYPE;
  var getCustomerFromEmail = function getCustomerFromEmail(_ref) {
    var email = _ref.email,
      user_id = _ref.user_id;
    var deferred = $q.defer();

    // Double decoding in BE needs double encode on the FE
    $http.get(apiService.getApiUrlV1("customers?filter[any_email]=".concat(encodeURIComponent(email), "&filter[user_id]=").concat(user_id))).then(function (_ref2) {
      var data = _ref2.data;
      return deferred.resolve(data);
    }, function (response) {
      return deferred.reject(response);
    });
    return deferred.promise;
  };

  // This is a custom api call not restful, (return the array itself), so use custom $http call and not $resource
  var getDuplicatePhonesCustomers = function getDuplicatePhonesCustomers(phones, success, error) {
    return $http.get(apiService.getApiUrl('customers-duplicates-by-phones?filter[phones]=' + phones)).then(function (response) {
      success(response.data);
    }), function (response) {
      error(response);
    };
  };

  // e.g: phone[]=5141231234,CA&phone[]=+15141231234,CA ... (+ and , are of course encoded)
  var getDuplicatePhonesCustomersV1 = function getDuplicatePhonesCustomersV1(phones) {
    phones = phones.map(function (phone) {
      return "phone[]=".concat(encodeURIComponent("".concat(phone.value, ",").concat(phone.country)));
    }).join('&');
    return $http.get(apiService.getApiUrlV1("customers-duplicates-by-phones?".concat(phones))).then(function (_ref3) {
      var data = _ref3.data;
      return data;
    });
  };
  var buildCategoryBrandFilter = function buildCategoryBrandFilter(key, value) {
    var encodedValue = value.map(function (val) {
      return encodeURIComponent(val);
    }).join(',');
    return "&filter[transaction][".concat(key, "]=").concat(encodedValue);
  };
  var buildSearchFilter = function buildSearchFilter(_ref4) {
    var search = _ref4.search;
    // single search
    if (typeof search === 'string' && search.length) {
      return "&filter[search]=".concat(encodeURIComponent(search));
    }
    // advanced search
    return Object.entries(search).reduce(function (filters, _ref5) {
      var _ref6 = _slicedToArray(_ref5, 2),
        key = _ref6[0],
        value = _ref6[1];
      if (value !== null && value !== void 0 && value.length) {
        return filters + "&filter[search][".concat(key, "]=").concat(encodeURIComponent(value));
      }
      return filters;
    }, '');
  };
  var buildTransactionDateFilter = function buildTransactionDateFilter(_ref7) {
    var _ref7$transaction_dat = _ref7.transaction_date,
      transaction_date = _ref7$transaction_dat === void 0 ? {} : _ref7$transaction_dat;
    return Object.entries(transaction_date).reduce(function (filters, _ref8) {
      var _ref9 = _slicedToArray(_ref8, 2),
        key = _ref9[0],
        value = _ref9[1];
      if (value) {
        return filters + "&filter[transaction][date][".concat(key, "]=").concat(value);
      }
      return filters;
    }, '');
  };
  var buildTransactionAmountFilter = function buildTransactionAmountFilter(_ref10) {
    var _ref10$transaction_am = _ref10.transaction_amount,
      transaction_amount = _ref10$transaction_am === void 0 ? {} : _ref10$transaction_am;
    return Object.entries(transaction_amount).reduce(function (filters, _ref11) {
      var _ref12 = _slicedToArray(_ref11, 2),
        key = _ref12[0],
        value = _ref12[1];
      if (value) {
        return filters + "&filter[transaction][total][".concat(key, "]=").concat(value);
      }
      return filters;
    }, '');
  };
  var buildAttributeFilters = function buildAttributeFilters(_ref13) {
    var _ref13$attributes = _ref13.attributes,
      attributes = _ref13$attributes === void 0 ? [] : _ref13$attributes;
    return attributes.reduce(function (filters, _ref14) {
      var attribute_id = _ref14.attribute_id,
        value_id = _ref14.value_id;
      return filters + "&filter[".concat(attribute_id, "]=").concat(value_id);
    }, '');
  };
  var isFilterValueValid = function isFilterValueValid(value) {
    return (value === null || value === void 0 ? void 0 : value.length) || typeof value === 'number' || _typeof(value) === 'object' && value && Object.keys(value).length > 0;
  };
  var buildFilters = function buildFilters(query) {
    return Object.entries(query).reduce(function (sanitizedFilters, _ref15) {
      var _ref16 = _slicedToArray(_ref15, 2),
        key = _ref16[0],
        value = _ref16[1];
      if (isFilterValueValid(value) && CONST_CONTACTS.ACCEPTED_API_FILTERS.includes(key)) {
        if (key === 'category_id' || key === 'brand') {
          return sanitizedFilters + buildCategoryBrandFilter(key, value);
        } else if (key === 'transaction_date') {
          return sanitizedFilters + buildTransactionDateFilter(query);
        } else if (key === 'transaction_amount') {
          return sanitizedFilters + buildTransactionAmountFilter(query);
        } else if (key === 'search') {
          return sanitizedFilters + buildSearchFilter(query);
        } else {
          return sanitizedFilters + "&filter[".concat(key, "]=").concat(value);
        }
      }
      return sanitizedFilters;
    }, buildAttributeFilters(query));
  };
  var getRetailerCustomers = function getRetailerCustomers(_ref17) {
    var _ref17$page = _ref17.page,
      page = _ref17$page === void 0 ? 0 : _ref17$page,
      per_page = _ref17.per_page,
      query = _objectWithoutProperties(_ref17, _excluded);
    return $resource(apiService.getApiUrl("retailer-customers?page=:page&per_page=:per_page".concat(buildFilters(query))), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    }).query({
      page: page || 0,
      per_page: per_page
    }).$promise;
  };
  var getSingleRetailerCustomer = function getSingleRetailerCustomer(id) {
    var user_id = $rootScope.currentUser.ID || localStorageService.get('sfId');
    var RetailerCustomerDetails = $resource(apiService.getApiUrl('retailer-customers/:id?include=related-customers,retailer-customer-tags,retailer-customer-stats-insights&filter[user_id]=:user_id'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return RetailerCustomerDetails.query({
      id: id,
      user_id: user_id
    }).$promise;
  };
  var sortByPosition = function sortByPosition(a, b) {
    var posA = parseInt(a.position);
    var posB = parseInt(b.position);
    if (posA === posB) {
      // Graphs need to be ordered by descending values if position is not set
      if (a.type === 'graph' && b.type === 'graph') {
        var valueA = parseFloat(a.value);
        var valueB = parseFloat(b.value);
        return valueB - valueA;
      }
      if (a.label < b.label) {
        return -1;
      }
      if (a.label > b.label) {
        return 1;
      }
      return 0;
    }
    return posA - posB;
  };
  var getAdditionalMetaData = function getAdditionalMetaData(customerData, type) {
    var customerMetaKey = "".concat(customerData.type, "-meta");
    return (customerData.getRelated(customerMetaKey) || []).map(jsonapi.getEntityData).filter(function (meta) {
      return meta["".concat(customerMetaKey, "_type")] === type;
    }).sort(sortByPosition);
  };
  var getSocials = function getSocials(customerData) {
    return (customerData.getRelated("".concat(customerData.type, "-social-media")) || []).map(function (social) {
      return angular.extend(jsonapi.getEntityData(social), {
        network: jsonapi.getEntityData(social.getRelated('network') || {})
      });
    });
  };
  var getFormattedTags = function getFormattedTags(customerData) {
    return (customerData.getRelated('customer-tag') || []).map(function (tag) {
      var rawTag = jsonapi.getEntityData(tag);
      return {
        id: rawTag.id,
        name: rawTag.name
      };
    });
  };
  var populatePanelsData = function populatePanelsData(panels) {
    return panels.map(function (panel) {
      var datapoints = panel.getRelated('retailer-customer-stats-datapoint');
      panel = jsonapi.getEntityData(panel);
      panel.datapoints = datapoints.map(function (datapoint) {
        var datapointData = jsonapi.getEntityData(datapoint);
        datapointData.type = datapointData['retailer-customer-stats-datapoint_type'];
        datapointData.product = jsonapi.getEntityData(datapoint.getRelated('product_variant') || datapoint.getRelated('product'));
        return datapointData;
      }).filter(function (_ref18) {
        var product = _ref18.product,
          type = _ref18.type;
        return !type.includes('product') || product;
      }).sort(sortByPosition);
      return panel;
    }).filter(function (_ref19) {
      var datapoints = _ref19.datapoints;
      return datapoints.length;
    }).sort(sortByPosition);
  };
  var getSingleRetailerCustomerV2 = function getSingleRetailerCustomerV2(id, retailerAttr) {
    var include = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];
    return jsonapi.get("/retailer-customers/".concat(id), {
      include: include
    }).then(function (customerData) {
      return angular.extend(jsonapi.getEntityData(customerData), {
        additionalEmails: getAdditionalMetaData(customerData, 'email'),
        additionalPhones: getAdditionalMetaData(customerData, 'phone'),
        panels: populatePanelsData(customerData.getRelated('retailer-customer-stats-panel') || []),
        sections: jsonapi.getCollectionData(customerData.getRelated('retailer-customer-stats-section') || []).sort(sortByPosition),
        attributePanels: populateCustomerAttributes(customerData, retailerAttr),
        events: (customerData.getRelated('retailer-customer-event') || []).map(jsonapi.getEntityData),
        socials: getSocials(customerData),
        addresses: (customerData.getRelated('retailer-customer-address') || []).map(jsonapi.getEntityData),
        customer_tags: getFormattedTags(customerData)
      });
    });
  };
  var getSingleRetailerCustomerWithAttributes = function getSingleRetailerCustomerWithAttributes(id, data) {
    return getSingleRetailerCustomerV2(id, data, ['customer-tag', 'retailer-customer-meta', 'retailer-customer-event', 'retailer-customer-address', 'retailer-customer-social-media', 'retailer-customer-social-media.network', 'retailer-customer-to-customer-attribute', 'retailer-customer-to-customer-attribute.customer-attribute', 'retailer-customer-to-customer-attribute.customer-attribute.customer-attribute-panel']);
  };

  //getSingleRetailerCustomerV2 is expecting 3 params - id, retailerAttributes, include
  var getSingleRetailerCustomerWithStats = function getSingleRetailerCustomerWithStats(id) {
    return getSingleRetailerCustomerV2(id, [], ['customer-tag', 'retailer-customer-meta', 'retailer-customer-address', 'retailer-customer-stats-section', 'retailer-customer-stats-panel.retailer-customer-stats-datapoint.product', 'retailer-customer-stats-panel.retailer-customer-stats-datapoint.product_variant']);
  };
  var getContactProductsV2 = function getContactProductsV2(id) {
    return jsonapi.get("/retailer-customers/".concat(id), {
      include: ['retailer-customer-stats-panel.retailer-customer-stats-datapoint.product', 'retailer-customer-stats-panel.retailer-customer-stats-datapoint.product_variant', 'retailer-customer-stats-section.retailer-customer-stats-panel.retailer-customer-stats-datapoint.product', 'retailer-customer-stats-section.retailer-customer-stats-panel.retailer-customer-stats-datapoint.product_variant']
    }).then(function (customerData) {
      return {
        panels: populatePanelsData(customerData.getRelated('retailer-customer-stats-panel')),
        sections: customerData.getRelated('retailer-customer-stats-section').map(function (section) {
          var sectionData = jsonapi.getEntityData(section);
          sectionData.panels = populatePanelsData(section.getRelated('retailer-customer-stats-panel') || []).map(function (panel) {
            panel.datapoints = panel.datapoints.filter(function (dataPoint) {
              return dataPoint.product;
            });
            return panel;
          }).filter(function (panel) {
            return panel.datapoints.length > 0;
          });
          return sectionData;
        })
      };
    });
  };
  var getRetailerCustomerSection = function getRetailerCustomerSection(id) {
    return jsonapi.get("/retailer-customers/stats/sections/".concat(id), {
      include: ['retailer-customer-stats-panel.retailer-customer-stats-datapoint.product', 'retailer-customer-stats-panel.retailer-customer-stats-datapoint.product_variant']
    }).then(function (section) {
      var sectionData = jsonapi.getEntityData(section);
      sectionData.panels = populatePanelsData(section.getRelated('retailer-customer-stats-panel') || []);
      return sectionData;
    });
  };

  //Conbine retailer attributes with attributes assigned to a contact/customer
  //For a new contact, there will not be a contactPanels as no attributes are assigned to him yet
  var combineAllAttributes = function combineAllAttributes(retailerAttr) {
    var contactPanels = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
    var contactsMapper = {};
    contactPanels.forEach(function (_ref20) {
      var panel_id = _ref20.panel_id,
        attributes = _ref20.attributes;
      contactsMapper[panel_id] = {};
      attributes.forEach(function (_ref21) {
        var id = _ref21.id,
          attribute_id = _ref21.attribute_id,
          attribute_value = _ref21.attribute_value;
        contactsMapper[panel_id][attribute_id] = {
          attribute_value: attribute_value,
          id: id
        };
      });
    });

    //for each retailer attribute - if this attribute is assigned to a contact - assign a value to it
    var displayedAttributes = retailerAttr.map(function (retailer) {
      if (contactsMapper[retailer.panel_id]) {
        retailer.attributes.forEach(function (attribute, index) {
          var attribute_id = attribute.attribute_id;
          //If contact doesn't have specific atribute, the attribute_value for this attr will be ''
          var _ref22 = contactsMapper[retailer.panel_id][attribute_id] || {},
            _ref22$attribute_valu = _ref22.attribute_value,
            attribute_value = _ref22$attribute_valu === void 0 ? '' : _ref22$attribute_valu;
          //If contact doesn't have specific atribute, the id for this attr will be null
          var _ref23 = contactsMapper[retailer.panel_id][attribute_id] || {},
            _ref23$id = _ref23.id,
            id = _ref23$id === void 0 ? null : _ref23$id;
          retailer.attributes[index] = _objectSpread(_objectSpread({}, retailer.attributes[index]), {}, {
            attribute_value: attribute_value,
            id: id,
            lid: attribute_id ? null : $rootScope.utils.getRandomLId() //If contact doesn't have specific atribute, the id for this attr will be null
          });
        });
      }
      return retailer;
    });
    return displayedAttributes;
  };
  var getAllRetailerAttributes = function getAllRetailerAttributes(contactId) {
    return jsonapi.get('/customer-attributes?include=customer-attribute-panel&sort=panel_id&page[size]=1000').then(function (retailerData) {
      var data = retailerData.data;
      var retailerAttributes = [];
      var attributesPanels = data.reduce(function (attrPanels, custAttr) {
        var rawPanels = custAttr.getRelated('customer-attribute-panel') || [];
        var panel = jsonapi.getEntityData(rawPanels);
        var attributes = jsonapi.getEntityData(custAttr);
        //The retailer attributes have different data from attributes that comming from customer attributes
        //Some data needs to be adjusted to make sure that the correct data is saved
        attributes.attribute_id = attributes.id; //the attribute_id that will be sent to the BE on save, should be the id of the attr from sf_customer_attributes table
        attributes.customer_id = contactId;
        attributes.lid = $rootScope.utils.getRandomLId();
        attributes.attribute_value = '';
        attributes.id = null;
        var attrData = angular.extend({}, panel, attributes);
        var _panel = panel,
          panel_id = _panel.panel_id;
        panel.attributes = [];
        if (panel_id in attrPanels) {
          panel = attrPanels[panel_id];
        } else {
          attrPanels[panel_id] = panel;
          panel.attributes = [];
        }
        panel.attributes.push(attrData);
        return attrPanels;
      }, {});
      attributesPanels = Object.keys(attributesPanels).forEach(function (key) {
        return retailerAttributes.push(attributesPanels[key]);
      });
      return retailerAttributes;
    });
  };
  var populateCustomerAttributes = function populateCustomerAttributes(customerData, retailerAttr) {
    var _customerData$type = customerData.type,
      type = _customerData$type === void 0 ? 'customer' : _customerData$type;
    var attrPanels = Object.values((customerData.getRelated("".concat(type, "-to-customer-attribute")) || []).reduce(function (attrPanels, custAttr) {
      var rawAttr = custAttr.getRelated('customer-attribute');
      if (!rawAttr) {
        return attrPanels;
      }
      var attrData = angular.extend({}, jsonapi.getEntityData(rawAttr), jsonapi.getEntityData(custAttr));
      var panel = jsonapi.getEntityData(rawAttr.getRelated('customer-attribute-panel'));
      var _panel2 = panel,
        panel_id = _panel2.panel_id;
      if (panel_id in attrPanels) {
        panel = attrPanels[panel_id];
      } else {
        attrPanels[panel_id] = panel;
        panel.attributes = [];
      }
      panel.attributes.push(attrData);
      return attrPanels;
    }, {}));
    attrPanels = combineAllAttributes(retailerAttr, attrPanels);
    attrPanels.forEach(function (_ref24) {
      var attributes = _ref24.attributes;
      return attributes.sort(sortByPosition);
    });
    attrPanels.sort(sortByPosition);
    return attrPanels;
  };
  var getSingleCustomerV2 = function getSingleCustomerV2(id, retailerAttr) {
    return jsonapi.get("/customers/".concat(id), {
      include: ['customer-tag', 'customer-meta', 'customer-event', 'customer-address', 'customer-social-media', 'customer-social-media.network', 'customer-to-customer-attribute', 'customer-to-customer-attribute.customer-attribute', 'customer-to-customer-attribute.customer-attribute.customer-attribute-panel']
    }).then(function (customerData) {
      return angular.extend(jsonapi.getEntityData(customerData), {
        additionalEmails: getAdditionalMetaData(customerData, 'email'),
        additionalPhones: getAdditionalMetaData(customerData, 'phone'),
        events: (customerData.getRelated('customer-event') || []).map(function (event) {
          return jsonapi.getEntityData(event);
        }),
        socials: getSocials(customerData),
        addresses: (customerData.getRelated('customer-address') || []).map(function (address) {
          return jsonapi.getEntityData(address);
        }),
        attributePanels: populateCustomerAttributes(customerData, retailerAttr),
        customer_tags: getFormattedTags(customerData)
      });
    });
  };
  var getCustomersLegacy = function getCustomersLegacy(_ref25) {
    var _ref25$page = _ref25.page,
      page = _ref25$page === void 0 ? 0 : _ref25$page,
      per_page = _ref25.per_page,
      query = _objectWithoutProperties(_ref25, _excluded2);
    return $resource(apiService.getApiUrlV1("customers?page=:page&per_page=:per_page&fields=count_tasks".concat(buildFilters(query))), {}, {
      query: {
        method: 'GET',
        cache: false,
        params: {},
        isArray: false
      }
    }).query({
      page: page || 0,
      per_page: per_page
    }).$promise;
  };
  var loadPageByUri = function loadPageByUri(uri, success, error) {
    return $http.get(apiService.getApiUrl(uri), {
      cache: false
    }).success(function (response) {
      if (angular.isFunction(success)) {
        success(response);
      }
    }).error(function (response) {
      if (angular.isFunction(error)) {
        error(response);
      }
    });
  };
  var getCustomers = function getCustomers(queryArgs) {
    var Customers = $resource(apiService.getApiUrl('customers?per_page=1000&filter[user_id]=:id&filter[search]=:search&filter[groups]=:group&filter[ID]=:in&filter[subcribtion_flag]=:subcribtion_flag&filter[only_store_contact]=:onlyStoreContact&is_array=true&fields=count_tasks'), {}, {
      query: {
        method: 'GET',
        cache: false,
        params: {},
        isArray: true
      }
    });
    return Customers.query(queryArgs).$promise;
  };
  var getAttributesFilters = function getAttributesFilters() {
    return jsonapi.get('customer-attributes', {
      filter: {
        is_filterable: 1
      }
    }).then(function (filters) {
      return filters.reduce(function (sanitizedFilters, filter) {
        var _jsonapi$getEntityDat = jsonapi.getEntityData(filter),
          label = _jsonapi$getEntityDat.label,
          attribute_id = _jsonapi$getEntityDat.attribute_id,
          is_filterable = _jsonapi$getEntityDat.is_filterable,
          default_value = _jsonapi$getEntityDat.default_value,
          type = _jsonapi$getEntityDat.attribute_type;
        if (is_filterable.toString() === '1' && ['boolean', 'multi-select', 'single-select'].includes(type)) {
          sanitizedFilters.push({
            type: type,
            label: label,
            value: default_value ? JSON.parse(default_value) : default_value,
            attribute_id: attribute_id
          });
        }
        return sanitizedFilters;
      }, []);
    });
  };
  var getContactsLocations = function getContactsLocations(userId) {
    return jsonapi.get("/reps/".concat(userId, "/customer-locations")).then(function (_ref26) {
      var locations = _ref26.data.attributes.locations;
      return locations;
    });
  };
  var getRetailerCustomersLocations = function getRetailerCustomersLocations() {
    return jsonapi.get('retailer-customer-locations').then(function (_ref27) {
      var locations = _ref27.data.attributes.locations;
      return locations;
    });
  };
  var getCustomerCount = function getCustomerCount(params) {
    return $http({
      url: apiService.getApiUrl("customers-countbytags?filter[customers]=subscribers".concat(buildFilters(params))),
      method: 'GET',
      params: params
    });
  };
  var getGroups = function getGroups() {
    var CustomerGroups = $resource(apiService.getApiUrl('customers-groups'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: true
      }
    });
    return CustomerGroups.query().$promise;
  };
  var getSingleCustomer = function getSingleCustomer(id) {
    var Customers = $resource(apiService.getApiUrl('customers/:id?include=related-retailer-customers,count-customer-notes,social-media,customer-tags,events,retailer-customer-stats-insights,addresses&fields=count_tasks'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return Customers.query({
      id: id
    }).$promise;
  };
  var checkBlockedCustomers = function checkBlockedCustomers(params) {
    return jsonapi.post(apiService.getApiUrlV2('customers/blocked'), params);
  };
  var addCustomerToContacts = function addCustomerToContacts(_ref28) {
    var customer_id = _ref28.customer_id;
    return jsonapi.post(apiService.getApiUrlV2('retailer-customers/add-to-my-contacts'), {
      type: 'retailer-customer',
      attributes: {
        customer_id: customer_id
      }
    }, {
      silenceLoader: true
    }).then(function (customer) {
      return jsonapi.getEntityData(customer).id;
    });
  };
  var saveCustomer = function saveCustomer(params) {
    var Customers = $resource(apiService.getApiUrl('customers/'), {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: false
      }
    });
    return Customers.query(params).$promise;
  };
  var createArrtibutesWithData = function createArrtibutesWithData(updatedPanel) {
    var allAttributesData = updatedPanel;
    var retailersData = localStorageService.get('sfCurrentCustomerV2');
    allAttributesData.type = 'customer';
    allAttributesData.id = retailersData.id;

    //API call should include all contact's attributes as one array (without panel division)
    var allAttributes = [];
    retailersData.attributePanels.forEach(function (panel) {
      if (panel.panel_id === allAttributesData.panel_id) {
        panel.attributes = allAttributesData.attributes;
      }
      panel.attributes.forEach(function (attr) {
        //We are sending only those attr that have attribute_value.
        //This is important, as a new contact might have required attributes in different panels. BE will not allow save if required is empty.
        if (attr.attribute_value) {
          //Convert all values of attribute types: int, boolean, and decimal, to a number (as reqested by the BE)
          if (['int', 'boolean', 'decimal'].includes(attr.attribute_type)) {
            attr.attribute_value = parseFloat(attr.attribute_value);
          }
          allAttributes.push(attr);
        }
      });
    });
    allAttributesData.attributes = allAttributes;
    var attr = jsonapi.createEntity(allAttributesData);
    attr.setRelated('customer-to-customer-attribute', attr.data.attributes.map(function (attribute) {
      return {
        type: 'customer-to-customer-attribute',
        id: attribute.id,
        attributes: {
          customer_id: attribute.customer_id,
          attribute_id: attribute.attribute_id,
          attribute_value: attribute.attribute_value,
          id: attribute.id
        }
      };
    }));
    return attr;
  };
  var updateCustomerAttributes = function updateCustomerAttributes(data, customerId) {
    var customerAttributes = createArrtibutesWithData(data);
    return jsonapi.patch("/customers/".concat(customerId), customerAttributes.getRaw());
  };
  var updateCustomer = function updateCustomer(queryArgs, params) {
    var Customer = $resource(apiService.getApiUrl('customers/:id'), {}, {
      query: {
        method: 'PUT',
        params: {},
        isArray: false
      }
    });
    return Customer.query(queryArgs, params).$promise;
  };
  var deleteCustomer = function deleteCustomer(queryArgs) {
    var Customer = $resource(apiService.getApiUrl('customers/:id'), {}, {
      query: {
        method: 'DELETE',
        params: {},
        isArray: false
      }
    });
    return Customer.query(queryArgs).$promise;
  };
  var updateCustomerFavorites = function updateCustomerFavorites(params) {
    var CustomerFavorites = $resource(apiService.getApiUrlV1('customer-favorites'), {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: false,
        silenceLoader: true
      }
    });
    return CustomerFavorites.query(params).$promise;
  };
  var bulkDelete = function bulkDelete(queryArgs, params) {
    var CustomerDelete = $resource(apiService.getApiUrl('customers-delete'), {}, {
      query: {
        method: 'PUT',
        params: {},
        isArray: false
      }
    });
    return CustomerDelete.query(queryArgs, params).$promise;
  };
  var getTransactionHistory = function getTransactionHistory(queryArgs) {
    var Transaction = $resource(apiService.getApiUrl('retailer-transactions?page=:page&per_page=:per_page&filter[customer_id]=:id&filter[trx_type]=:type'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return Transaction.query(queryArgs).$promise;
  };
  var getSingleTransaction = function getSingleTransaction(queryArgs) {
    var Transaction = $resource(apiService.getApiUrl('retailer-transactions?filter[trx_thread_id]=:threadId'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return Transaction.query(queryArgs).$promise;
  };
  var getSalesfloorTransactions = function getSalesfloorTransactions(queryArgs) {
    var Transactions = $resource(apiService.getApiUrl('transactions?filter[customer_id]=:id&filter[id]=:threadId&filter[trx_id]=:trxId&sort=-trx_date'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return Transactions.query(queryArgs).$promise;
  };

  /**************************
   *     EVENTS API CALLS
   **************************/

  /**
   * API call to create/update events
   */

  var saveContactEvents = function saveContactEvents(queryArgs, queryArray) {
    var CustomerEvents = $resource(apiService.getApiUrl('customers/:id/events'), {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: true
      }
    });
    return CustomerEvents.query(queryArgs, queryArray).$promise;
  };
  var getContactEvents = function getContactEvents(queryArgs) {
    var CustomerEvents = $resource(apiService.getApiUrl('customers/:id/events'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: true
      }
    });
    return CustomerEvents.query(queryArgs).$promise;
  };

  /***************************
  * SOCIAL MEDIA API CALLS
  **************************
   **
  * API call to create/update social medias
  **/
  var saveContactSocials = function saveContactSocials(queryArgs, queryArray) {
    var CustomerSocialMedia = $resource(apiService.getApiUrl('customers/:id/social-media'), {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: true
      }
    });
    return CustomerSocialMedia.query(queryArgs, queryArray).$promise;
  };

  /**
  * API call to get contact social medias data
  */
  var getContactSocials = function getContactSocials(queryArgs) {
    var CustomerSocialMedia = $resource(apiService.getApiUrl('customers/:id/social-media'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: true
      }
    });
    return CustomerSocialMedia.query(queryArgs).$promise;
  };

  /**
  * API call to get social medias IDs
  */
  var getSocialIds = function getSocialIds() {
    var SocialMediaNetwork = $resource(apiService.getApiUrl('social-media-network'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return SocialMediaNetwork.query().$promise;
  };

  /**************************
  * CONTACTS NOTES API CALLS
  **************************/
  /**
  * API call to get all notes listing for a specific contact (customer id)
  */
  var getContactNotesListing = function getContactNotesListing(queryArgs) {
    var CustomerNotes = $resource(apiService.getApiUrl('customer-notes/customer/:id?per_page=100'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return CustomerNotes.query(queryArgs).$promise;
  };

  /**
  * API call to create a note for specific contact
  */
  var createContactNote = function createContactNote(queryArgs) {
    var CustomerNotes = $resource(apiService.getApiUrl('customer-notes'), {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: false
      }
    });
    return CustomerNotes.query(queryArgs).$promise;
  };

  /**
  * API call to get a single contact note from specific contact (note id)
  */
  var getSingleContactNote = function getSingleContactNote(queryArgs) {
    var CustomerNotes = $resource(apiService.getApiUrl('customer-notes/:id'), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return CustomerNotes.query(queryArgs).$promise;
  };

  /**
  * API call to update a single contact note from specific contact (note id)
  */
  var updateContactNote = function updateContactNote(id, queryArgs) {
    var CustomerNotes = $resource(apiService.getApiUrl('customer-notes/:id'), {}, {
      query: {
        method: 'PUT',
        params: {},
        isArray: false
      }
    });
    return CustomerNotes.query(id, queryArgs).$promise;
  };

  /**
  * API call to delete a single contact note for specific contact (note id)
  */
  var deleteContactNote = function deleteContactNote(queryArgs) {
    var CustomerNotes = $resource(apiService.getApiUrl('customer-notes/:id'), {}, {
      query: {
        method: 'DELETE',
        params: {},
        isArray: false
      }
    });
    return CustomerNotes.query(queryArgs).$promise;
  };

  /**
  * Get all the contact addresses that belong to a customer id
  */
  var getContactAddresses = function getContactAddresses(queryArgs) {
    var CustomerAddresses = $resource(apiService.getApiUrl('customer-address/:id/list'));
    return CustomerAddresses.query(queryArgs).$promise;
  };
  var getContactAddress = function getContactAddress(contactId, addressId) {
    addressId = parseInt(addressId);
    return getContactAddresses({
      id: contactId
    }).then(function (addresses) {
      return addresses.find(function (_ref29) {
        var id = _ref29.id;
        return id === addressId;
      });
    });
  };

  /**
  * Get all contact tags
  */
  var getAllContactTags = function getAllContactTags() {
    var search = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
    var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 99999;
    var searchfilter = search ? "like(".concat(search, ")") : '';
    var CustomerTags = $resource(apiService.getApiUrl("customer-tags?filter[status]=active&filter[name]=".concat(searchfilter, "&per_page=").concat(size, "&sort=name")), {}, {
      query: {
        method: 'GET',
        params: {},
        isArray: false
      }
    });
    return CustomerTags.query().$promise;
  };
  var getProductCategoriesFilters = function getProductCategoriesFilters(_ref30) {
    var search = _ref30.search,
      size = _ref30.size;
    return $http.get(apiService.getApiUrlV2("product-categories?filter[name]=".concat(encodeURIComponent(search), "&page[number]=0&page[size]=").concat(size))).then(function (_ref31) {
      var data = _ref31.data.data;
      return data;
    });
  };
  var getProductBrandsFilters = function getProductBrandsFilters(_ref32) {
    var search = _ref32.search,
      size = _ref32.size;
    return $http.get(apiService.getApiUrlV2("product-brands?filter[name]=".concat(encodeURIComponent(search), "&page[number]=0&page[size]=").concat(size))).then(function (_ref33) {
      var data = _ref33.data.data;
      return data;
    });
  };

  /**
  * Create a new contact address
  */
  var createContactAddress = function createContactAddress(queryArgs) {
    var CustomerAddress = $resource(apiService.getApiUrl('customer-address'), {}, {
      query: {
        method: 'POST',
        params: {},
        isArray: false
      }
    });
    return CustomerAddress.query(queryArgs).$promise;
  };

  /**
  * Update contact address given a customer Id
  */
  var updateContactAddress = function updateContactAddress(queryArgs, copy) {
    var CustomerAddress = $resource(apiService.getApiUrl('customer-address/:id'), {
      id: '@id'
    }, {
      query: {
        method: 'PUT',
        params: {},
        isArray: false
      }
    });
    return CustomerAddress.query(queryArgs, copy).$promise;
  };

  /**
  * Delete contact address given a customer Id
  */
  var deleteContactAddress = function deleteContactAddress(queryArgs) {
    var CustomerAddress = $resource(apiService.getApiUrl('customer-address/:id'), {
      id: '@id'
    }, {
      query: {
        method: 'DELETE',
        params: {},
        isArray: false
      }
    });
    return CustomerAddress.query(queryArgs).$promise;
  };

  /**
  * Get contact tags given a customer Id
  */
  var getContactTags = function getContactTags(queryArgs) {
    var CustomerTags = $resource(apiService.getApiUrl('customer-tags/:id'), {
      id: '@id'
    }, {
      query: {
        method: 'GET',
        params: {},
        isArray: true
      }
    });
    return CustomerTags.query(queryArgs).$promise;
  };

  /**
  * Update contact tags for given a customer Id
  */
  var updateContactTags = function updateContactTags(queryArgs, state) {
    var CustomerTags = $resource(apiService.getApiUrl('customer-tags/:id'), {
      id: '@id'
    }, {
      query: {
        method: 'PUT',
        params: {},
        isArray: false
      }
    });
    return CustomerTags.query(queryArgs, state).$promise;
  };

  /**
  * Get retailer customer tags given a customer Id
  */
  var getRetailerCustomerContactTags = function getRetailerCustomerContactTags(queryArgs) {
    var RetailerCustomerTags = $resource(apiService.getApiUrl('retailer-customer-tags/:id'), {
      id: '@id'
    }, {
      query: {
        method: 'GET',
        params: {},
        isArray: true
      }
    });
    return RetailerCustomerTags.query(queryArgs).$promise;
  };
  var getSearchStorageKey = function getSearchStorageKey(mode) {
    return searchStorageKeys[mode];
  };
  var getSearchData = function getSearchData(mode) {
    return localStorageService.get(searchStorageKeys[mode]);
  };
  var setSearchData = function setSearchData(mode, searchData) {
    return localStorageService.set(searchStorageKeys[mode], searchData);
  };
  var clearSearchData = function clearSearchData(mode) {
    var modes = mode ? [mode] : Object.keys(searchStorageKeys);
    modes.forEach(function (mode) {
      return localStorageService.remove(searchStorageKeys[mode]);
    });
  };
  var clearSingleStorageKeyValue = function clearSingleStorageKeyValue(storageKey, valKey) {
    var storageKeyData = localStorageService.get(storageKey);
    if (storageKeyData && storageKeyData.hasOwnProperty(valKey)) {
      delete storageKeyData[valKey];
      localStorageService.set(storageKey, storageKeyData);
    }
  };
  var updateSingleStorageKeyValue = function updateSingleStorageKeyValue(storageKey, valKey, newValue) {
    var storageKeyData = localStorageService.get(storageKey);
    if (storageKeyData && storageKeyData.hasOwnProperty(valKey)) {
      localStorageService.set(storageKey, _objectSpread(_objectSpread({}, storageKeyData), {}, _defineProperty({}, valKey, newValue)));
    }
  };
  var isContactOrLinkedCustomer = function isContactOrLinkedCustomer() {
    return localStorageService.get(CONST_CONTACTS.LS_KEY_CURRENT_SEARCH_MODE) === 'contacts' || localStorageService.get(CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH);
  };
  var getSubscriptionText = function getSubscriptionText(subscriptionStatus) {
    return Object.values(consentType).find(function (type) {
      return String(subscriptionStatus) === type.value;
    });
  };
  var isFromContact = function isFromContact() {
    var sfCurrentSearchMode = localStorageService.get(CONST_CONTACTS.LS_KEY_CURRENT_SEARCH_MODE);
    // sfCurrentSearchMode is set in the contacts list.
    // $routeParams.type is set in tasks,
    // If coming from somewhere else (e.g. messages) and not from group tasks, "contacts" is assumed
    return sfCurrentSearchMode === 'contacts' || sfCurrentSearchMode === null && $routeParams.type !== TASK_TYPE.GROUP_TASK || !!localStorageService.get(CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH);
  };
  var getSubscriptionStatus = function getSubscriptionStatus(contact) {
    return isFromContact() ? (contact === null || contact === void 0 ? void 0 : contact.subscription_flag) || (contact === null || contact === void 0 ? void 0 : contact.subcribtion_flag) : contact === null || contact === void 0 ? void 0 : contact.is_subscribed;
  };
  var getSMSSubscriptionStatus = function getSMSSubscriptionStatus(contact) {
    return isFromContact() ? contact === null || contact === void 0 ? void 0 : contact.sms_marketing_subscription_flag : contact === null || contact === void 0 ? void 0 : contact.is_subscribed_sms_marketing;
  };
  var hasSubscriptionFlag = function hasSubscriptionFlag(contact) {
    return $rootScope.isNumeric(getSubscriptionStatus(contact));
  };
  var hasSMSSubscriptionFlag = function hasSMSSubscriptionFlag(contact) {
    return $rootScope.isNumeric(getSMSSubscriptionStatus(contact));
  };
  var subscriptionDefaultValue = function subscriptionDefaultValue() {
    return featuresEnabledService.isContactConsentRequired() ? CONST_CONTACTS.CONSENT_TYPE.SUBSCRIBED.value : CONST_CONTACTS.CONSENT_TYPE.ONE_TO_ONE.value;
  };
  var mergeContactsListData = function mergeContactsListData(_ref34) {
    var _ref34$currentData = _ref34.currentData,
      currentData = _ref34$currentData === void 0 ? [] : _ref34$currentData,
      _ref34$newData = _ref34.newData,
      newData = _ref34$newData === void 0 ? [] : _ref34$newData,
      _ref34$pageNumber = _ref34.pageNumber,
      pageNumber = _ref34$pageNumber === void 0 ? 0 : _ref34$pageNumber;
    if (pageNumber === 0 || pageNumber === null) {
      return newData;
    }
    return [].concat(_toConsumableArray(currentData), _toConsumableArray(newData));
  };
  var scrollToTheTopOfTheList = function scrollToTheTopOfTheList() {
    var pageNumber = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    if (pageNumber === 0 || pageNumber === null) {
      $rootScope.$broadcast('scrollToTheTop');
    }
  };
  return {
    getCustomerFromEmail: getCustomerFromEmail,
    getDuplicatePhonesCustomers: getDuplicatePhonesCustomers,
    getDuplicatePhonesCustomersV1: getDuplicatePhonesCustomersV1,
    getSingleRetailerCustomerV2: getSingleRetailerCustomerV2,
    getSingleRetailerCustomerWithAttributes: getSingleRetailerCustomerWithAttributes,
    getSingleRetailerCustomerWithStats: getSingleRetailerCustomerWithStats,
    getContactProductsV2: getContactProductsV2,
    getRetailerCustomerSection: getRetailerCustomerSection,
    getSingleRetailerCustomer: getSingleRetailerCustomer,
    getRetailerCustomers: getRetailerCustomers,
    getCustomersLegacy: getCustomersLegacy,
    getCustomers: getCustomers,
    getGroups: getGroups,
    checkBlockedCustomers: checkBlockedCustomers,
    saveCustomer: saveCustomer,
    updateCustomer: updateCustomer,
    deleteCustomer: deleteCustomer,
    updateCustomerFavorites: updateCustomerFavorites,
    bulkDelete: bulkDelete,
    getTransactionHistory: getTransactionHistory,
    getSingleTransaction: getSingleTransaction,
    getSalesfloorTransactions: getSalesfloorTransactions,
    saveContactEvents: saveContactEvents,
    getContactEvents: getContactEvents,
    saveContactSocials: saveContactSocials,
    getSocialIds: getSocialIds,
    getContactNotesListing: getContactNotesListing,
    createContactNote: createContactNote,
    getSingleContactNote: getSingleContactNote,
    updateContactNote: updateContactNote,
    deleteContactNote: deleteContactNote,
    getContactAddresses: getContactAddresses,
    getContactAddress: getContactAddress,
    getAllContactTags: getAllContactTags,
    createContactAddress: createContactAddress,
    updateContactAddress: updateContactAddress,
    deleteContactAddress: deleteContactAddress,
    getContactTags: getContactTags,
    updateContactTags: updateContactTags,
    getRetailerCustomerContactTags: getRetailerCustomerContactTags,
    getContactSocials: getContactSocials,
    getSingleCustomer: getSingleCustomer,
    getSingleCustomerV2: getSingleCustomerV2,
    loadPageByUri: loadPageByUri,
    getCustomerCount: getCustomerCount,
    getSearchStorageKey: getSearchStorageKey,
    getSearchData: getSearchData,
    setSearchData: setSearchData,
    clearSearchData: clearSearchData,
    updateCustomerAttributes: updateCustomerAttributes,
    getAllRetailerAttributes: getAllRetailerAttributes,
    getContactsLocations: getContactsLocations,
    getRetailerCustomersLocations: getRetailerCustomersLocations,
    getAttributesFilters: getAttributesFilters,
    addCustomerToContacts: addCustomerToContacts,
    clearSingleStorageKeyValue: clearSingleStorageKeyValue,
    updateSingleStorageKeyValue: updateSingleStorageKeyValue,
    isContactOrLinkedCustomer: isContactOrLinkedCustomer,
    getSubscriptionText: getSubscriptionText,
    isFromContact: isFromContact,
    getSubscriptionStatus: getSubscriptionStatus,
    getSMSSubscriptionStatus: getSMSSubscriptionStatus,
    hasSubscriptionFlag: hasSubscriptionFlag,
    hasSMSSubscriptionFlag: hasSMSSubscriptionFlag,
    subscriptionDefaultValue: subscriptionDefaultValue,
    getProductCategoriesFilters: getProductCategoriesFilters,
    getProductBrandsFilters: getProductBrandsFilters,
    mergeContactsListData: mergeContactsListData,
    scrollToTheTopOfTheList: scrollToTheTopOfTheList
  };
}]);
//# sourceMappingURL=contact.js.map
