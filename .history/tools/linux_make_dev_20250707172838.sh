#! /bin/bash

scriptFolder=$(dirname "${BASH_SOURCE[0]}")
cd "$scriptFolder/../platform/ci/docker" || exit 1

currentBranch=$(git branch --show-current)

# $1 is the first argument of the script, if it's empty, set it to string 'docker'
# bash shell string operation

docker=${1:-docker8}
git checkout "$docker"

# git checkout joseph
# git checkout master

# git checkout DEVOPS-9584-jenkins-job-codeception-run-migration-files-for-all-retailer-databases-but-we-do-not-need-them
# git checkout qa-test-config
# git checkout SF-30174-SF-30902-use-database-transaction-between-tests-api-functional

make stop

# Ensure a file exists, else docker would create a directory
touch service-account.json
gcloud config set project sf-dev-1b4e41f578 # magick command
gcloud kms decrypt --ciphertext-file=service-account.json.enc --plaintext-file=service-account.json --location global --keyring keyring-main --key development-key
# # Prevent current AWS Deployment to break
touch service-account.json

make dev

docker-compose logs -f web | while read -r line
do
   echo "$line"
   if echo "$line" | grep 'CONTAINER IS READY' > /dev/null
   then
      # Current we're in ../plateform/ci/docker
      git checkout "$currentBranch"
      cd ../../../tools || exit 1
      # ./linux_enable_xdebug.sh
      ./xdebug8.sh
      cd ../platform || exit 1

      container=$(docker ps | awk '$2 ~ /mysql/ {print $NF;}')
      docker exec -it "$container" mysql -uwordpress -pAvFAN4LpKl1EYH -e "set global max_allowed_packet=*********"
      break
   fi
done
