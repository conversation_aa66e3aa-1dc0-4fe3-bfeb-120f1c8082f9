## Get list of contacts/customers
### platform/api/app/src/be/Controllers/Customers/V1.php::getAll()
### platform/api/app/src/be/Managers/Client/Customers/Legacy.php::fetchAll()
### =============================================================
GET https://chicos.api.dev.salesfloor.net/v1/customers
    ?page=0
    &per_page=10
    &fields=count_tasks
    &filter[user_id]=115 HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-US,en;q=0.9
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjU3MDMzNDQ3LCJpYXQiOjE2NTcwMzI4NDcsInJlZiI6MTY2MTg3MTI0N30.E4Yxlp0uQh5bke05JLU8ki4n56GIwDnLDhRGH4Pszxs
content-type: application/json;charset=UTF-8
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"
sec-ch-ua-mobile: ?0
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36

## Get list of contacts and group the results
### platform/api/app/src/be/Controllers/Customers/V1.php::getAll()
### platform/api/app/src/be/Managers/Client/Customers/Legacy.php::fetchAll()
### =============================================================
GET https://holt.api.dev.salesfloor.net/v1/customers
    ?page=0
    &per_page=10
    &resultset-type=group_by_fields
    &filter[search]=aa
    &filter[user_id]=115 HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-US,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjY0ODExOTUxLCJpYXQiOjE2NjQ4MTEzNTEsInJlZiI6MTY2OTY0OTc1MX0.jqZa9e17P0ZOdGzxNhofGaAqGLu9aDxiNKgD4lVzJnM
content-type: application/json;charset=UTF-8
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"
sec-ch-ua-mobile: ?0
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36

## Get list of contacts without grouping the results
### platform/api/app/src/be/Controllers/Customers/V1.php::getAll()
### platform/api/app/src/be/Managers/Client/Customers/Legacy.php::fetchAll()
### =============================================================
GET https://holt.api.dev.salesfloor.net/v1/customers
    ?page=0
    &per_page=10
    &filter[search][first_name]=aa
    &filter[user_id]=115 HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-US,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjY0ODExOTUxLCJpYXQiOjE2NjQ4MTEzNTEsInJlZiI6MTY2OTY0OTc1MX0.jqZa9e17P0ZOdGzxNhofGaAqGLu9aDxiNKgD4lVzJnM
content-type: application/json;charset=UTF-8
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"
sec-ch-ua-mobile: ?0
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36

### Team mode(gnc)cd
GET https://gnc.api.dev.salesfloor.net/v1/customers
        ?page=0
        &per_page=7
        &fields=count_tasks
        &filter[search]=aa
        &filter[user_id]=115
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjc1MTA5MjYxLCJpYXQiOjE2NzUxMDg2NjEsInJlZiI6MTY3OTk0NzA2MX0.YrzNsAbSrSJX9rQDYYQrOES5u3-ZJveadNHB2MJpUxg
origin: http://localhost:9000

### Blocked before creation
### api/app/src/be/Controllers/CustomersBlocked.php::validateCustomersBlocked()
POST  https://holt.api.dev.salesfloor.net/v2/customers/blocked?sf_locale=en_US
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjY0ODExOTUxLCJpYXQiOjE2NjQ4MTEzNTEsInJlZiI6MTY2OTY0OTc1MX0.jqZa9e17P0ZOdGzxNhofGaAqGLu9aDxiNKgD4lVzJnM
content-length: 937
content-type: application/json
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36

{
    "data": {
        "type": "customers-blocked",
        "attributes": {
            "emails": [
                "<EMAIL>"
            ],
            "phones": [
                {
                    "value": "+15144321863",
                    "country": "CA"
                }
            ]
        }
    }
}

### Create a new contact/customer
### platform/api/app/src/be/Controllers/Customers/Legacy.php::create()
POST https://holt.api.dev.salesfloor.net/customers?sf_locale=en_US
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjY0ODExOTUxLCJpYXQiOjE2NjQ4MTEzNTEsInJlZiI6MTY2OTY0OTc1MX0.jqZa9e17P0ZOdGzxNhofGaAqGLu9aDxiNKgD4lVzJnM
content-length: 937
content-type: application/json
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36

{
  "contact_preference": "email",
  "subscriptionStatus": "0",
  "smsSubscriptionStatus": "0",
  "first_name": "Lei",
  "last_name": "Yang",
  "isSubscribed": false,
  "isSMSSubscribed": false,
  "isSMSUnsubscribed": false,
  "locale": "en_US",
  "email": "<EMAIL>",
  "phone": "+15144321863",
  "country": "CA",
  "additionalEmails": [],
  "additionalPhones": [],
  "user_id": 115,
  "subcribtion_flag": "0",
  "subscription_flag": "0",
  "sms_marketing_subscription_flag": "0",
  "note": "",
  "origin": "mobile-rep-manual-creation"
}

### team mode(gnc)
POST https://gnc.api.dev.salesfloor.net/customers
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjc1MTA5MjYxLCJpYXQiOjE2NzUxMDg2NjEsInJlZiI6MTY3OTk0NzA2MX0.YrzNsAbSrSJX9rQDYYQrOES5u3-ZJveadNHB2MJpUxg
content-type: application/json
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36

{
    "contact_preference": "email",
    "subscriptionStatus": "0",
    "smsSubscriptionStatus": "0",
    "first_name": "Lei",
    "last_name": "Yang",
    "isSubscribed": false,
    "isSMSSubscribed": false,
    "email": "<EMAIL>",
    "phone": "+15144321862",
    "country": "CA",
    "additionalEmails": [],
    "additionalPhones": [],
    "user_id": 115,
    "subcribtion_flag": "0",
    "subscription_flag": "0",
    "sms_marketing_subscription_flag": "0",
    "note": "",
    "origin": "mobile-rep-manual-creation"
}

### create multiple customers
POST https://gnc.api.dev.salesfloor.net/customers?per_page=-1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjc1MTA5MjYxLCJpYXQiOjE2NzUxMDg2NjEsInJlZiI6MTY3OTk0NzA2MX0.YrzNsAbSrSJX9rQDYYQrOES5u3-ZJveadNHB2MJpUxg
content-type: application/json
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36

[{
    "id": 7,
    "name": "Ben",
    "email": "<EMAIL>",
    "user_id": 115,
    "origin": "mobile-rep-device-import"
},
{
    "id": 1,
    "name": "Cedric",
    "email": "<EMAIL>",
    "user_id": 115,
    "origin": "mobile-rep-device-import"
},
{
    "id": 16,
    "name": "Cedric2",
    "email": "<EMAIL>",
    "user_id": 115,
    "origin": "mobile-rep-device-import"
}]

### Update customer
### platform/api/app/src/be/Controllers/Customers/V2.php::update()
### platform/controllers/src/Base/JsonApiController.php::update()
PATCH https://chicos.api.dev.salesfloor.net/v2/customers/386969
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjUzOTIzOTM1LCJpYXQiOjE2NTM5MjMzMzUsInJlZiI6MTY1ODc2MTczNX0.L5wseW6Z77l_TBTiGCGYhd5OGA13Tctt93ou7ZMhJug
content-length: 937
content-type: application/json
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36

{
    "data": {
        "panel_id": "2",
        "label": "Skin Analysis 1",
        "position": "2",
        "id": "386969",
        "type": "customer",
        "attributes": [
            {
                "panel_id": "1",
                "label": "PC Optimum 1",
                "position": "1",
                "id": "299",
                "type": "customer-attribute",
                "attribute_id": "1",
                "description": "PC Optimum 1",
                "attribute_type": "string",
                "default_value": "",
                "place_holder": "",
                "is_required": "0",
                "is_editable": "1",
                "is_visible": "1",
                "is_searchable": "1",
                "is_filterable": "1",
                "is_scannable": "0",
                "validation_rule": null,
                "customer_id": "386969",
                "lid": null,
                "attribute_value": "nnex"
            },
            {
                "panel_id": "2",
                "label": "Skin Concerns: T-Zone / Préoccupations: Zone T",
                "position": "6",
                "id": "295",
                "type": "customer-attribute",
                "attribute_id": "9",
                "description": "Skin Concerns: T-Zone / Préoccupations: Zone T",
                "attribute_type": "multi-select",
                "default_value": "[{\"value\":\"tzone1\",\"label\":\"Dehydration / Déshydratation\"},{\"value\":\"tzone2\",\"label\":\"Dryness / Sècheresse\"},{\"value\":\"tzone3\",\"label\":\"Oiliness / Excès de sébum\"},{\"value\":\"tzone4\",\"label\":\"Firmness / Fermeté\"},{\"value\":\"tzone5\",\"label\":\"Fine Lines / Ridules\"},{\"value\":\"tzone6\",\"label\":\"Deep Lines / Rides profondes\"},{\"value\":\"tzone7\",\"label\":\"Hyperpigmentation / Hyperpigmentation\"},{\"value\":\"tzone8\",\"label\":\"Redness / Rougeur\"},{\"value\":\"tzone9\",\"label\":\"Sensitivity / Sensibilité\"},{\"value\":\"tzone10\",\"label\":\"Blemishes / Imperfections\"},{\"value\":\"tzone11\",\"label\":\"Dullness / Teint Terne\"}]",
                "place_holder": "",
                "is_required": "0",
                "is_editable": "1",
                "is_visible": "1",
                "is_searchable": "1",
                "is_filterable": "1",
                "is_scannable": "0",
                "validation_rule": null,
                "customer_id": "386969",
                "lid": null,
                "attribute_value": "[\"tzone4\",\"tzone5\",\"tzone8\",\"tzone3\"]"
            },
            {
                "panel_id": "2",
                "label": "Skin Concerns: Eyes / Préoccupations: Yeux",
                "position": "8",
                "id": "296",
                "type": "customer-attribute",
                "attribute_id": "11",
                "description": "Skin Concerns: Eyes / Préoccupations: Yeux",
                "attribute_type": "multi-select",
                "default_value": "[{\"value\":\"eyes1\",\"label\":\"Dehydration / Déshydratation\"},{\"value\":\"eyes2\",\"label\":\"Dryness / Sècheresse\"},{\"value\":\"eyes3\",\"label\":\"Oiliness / Excès de sébum\"},{\"value\":\"eyes4\",\"label\":\"Firmness / Fermeté\"},{\"value\":\"eyes5\",\"label\":\"Fine Lines / Ridules\"},{\"value\":\"eyes6\",\"label\":\"Deep Lines / Rides profondes\"},{\"value\":\"eyes7\",\"label\":\"Hyperpigmentation / Hyperpigmentation\"},{\"value\":\"eyes8\",\"label\":\"Redness / Rougeur\"},{\"value\":\"eyes9\",\"label\":\"Sensitivity / Sensibilité\"},{\"value\":\"eyes10\",\"label\":\"Dark Circles / Cernes\"},{\"value\":\"eyes11\",\"label\":\"Puffiness / Bouffissures\"}]",
                "place_holder": "",
                "is_required": "0",
                "is_editable": "1",
                "is_visible": "1",
                "is_searchable": "1",
                "is_filterable": "1",
                "is_scannable": "0",
                "validation_rule": null,
                "customer_id": "386969",
                "lid": null,
                "attribute_value": "[\"eyes4\",\"eyes11\",\"eyes10\",\"eyes9\",\"eyes8\"]"
            },
            {
                "panel_id": "3",
                "label": "Skin Concerns: T-Zone / Préoccupations: Zone T",
                "position": "6",
                "id": "297",
                "type": "customer-attribute",
                "attribute_id": "19",
                "description": "Skin Concerns: T-Zone / Préoccupations: Zone T",
                "attribute_type": "multi-select",
                "default_value": "[{\"value\":\"tzone1\",\"label\":\"Dehydration / Déshydratation\"},{\"value\":\"tzone2\",\"label\":\"Dryness / Sècheresse\"},{\"value\":\"tzone3\",\"label\":\"Oiliness / Excès de sébum\"},{\"value\":\"tzone4\",\"label\":\"Firmness / Fermeté\"},{\"value\":\"tzone5\",\"label\":\"Fine Lines / Ridules\"},{\"value\":\"tzone6\",\"label\":\"Deep Lines / Rides profondes\"},{\"value\":\"tzone7\",\"label\":\"Hyperpigmentation / Hyperpigmentation\"},{\"value\":\"tzone8\",\"label\":\"Redness / Rougeur\"},{\"value\":\"tzone9\",\"label\":\"Sensitivity / Sensibilité\"},{\"value\":\"tzone10\",\"label\":\"Blemishes / Imperfections\"},{\"value\":\"tzone11\",\"label\":\"Dullness / Teint Terne\"}]",
                "place_holder": "",
                "is_required": "0",
                "is_editable": "1",
                "is_visible": "1",
                "is_searchable": "1",
                "is_filterable": "1",
                "is_scannable": "0",
                "validation_rule": null,
                "customer_id": "386969",
                "lid": null,
                "attribute_value": "[\"tzone4\",\"tzone8\",\"tzone11\"]"
            },
            {
                "panel_id": "3",
                "label": "Skin Concerns: Cheeks / Préoccupations: Joues",
                "position": "7",
                "id": "298",
                "type": "customer-attribute",
                "attribute_id": "20",
                "description": "Skin Concerns: Cheeks / Préoccupations: Joues",
                "attribute_type": "multi-select",
                "default_value": "[{\"value\":\"cheeks1\",\"label\":\"Dehydration / Déshydratation\"},{\"value\":\"cheeks2\",\"label\":\"Dryness / Sècheresse\"},{\"value\":\"cheeks3\",\"label\":\"Oiliness / Excès de sébum\"},{\"value\":\"cheeks4\",\"label\":\"Firmness / Fermeté\"},{\"value\":\"cheeks5\",\"label\":\"Fine Lines / Ridules\"},{\"value\":\"cheeks6\",\"label\":\"Deep Lines / Rides profondes\"},{\"value\":\"cheeks7\",\"label\":\"Hyperpigmentation / Hyperpigmentation\"},{\"value\":\"cheeks8\",\"label\":\"Redness / Rougeur\"},{\"value\":\"cheeks9\",\"label\":\"Sensitivity / Sensibilité\"},{\"value\":\"cheeks10\",\"label\":\"Blemishes / Imperfections\"},{\"value\":\"cheeks11\",\"label\":\"Dullness / Teint Terne\"}]",
                "place_holder": "",
                "is_required": "0",
                "is_editable": "1",
                "is_visible": "1",
                "is_searchable": "1",
                "is_filterable": "1",
                "is_scannable": "0",
                "validation_rule": null,
                "customer_id": "386969",
                "lid": null,
                "attribute_value": "[\"cheeks6\",\"cheeks3\"]"
            }
        ],
        "relationships": {
            "customer-to-customer-attribute": {
                "data": [
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "299"
                    },
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "295"
                    },
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "296"
                    },
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "297"
                    },
                    {
                        "type": "customer-to-customer-attribute",
                        "id": "298"
                    }
                ]
            }
        }
    },
    "included": [
        {
            "type": "customer-to-customer-attribute",
            "id": "299",
            "attributes": {
                "customer_id": "386969",
                "attribute_id": "1",
                "attribute_value": "nnex",
                "id": "299"
            }
        },
        {
            "type": "customer-to-customer-attribute",
            "id": "295",
            "attributes": {
                "customer_id": "386969",
                "attribute_id": "9",
                "attribute_value": "[\"tzone4\",\"tzone5\",\"tzone8\",\"tzone3\"]",
                "id": "295"
            }
        },
        {
            "type": "customer-to-customer-attribute",
            "id": "296",
            "attributes": {
                "customer_id": "386969",
                "attribute_id": "11",
                "attribute_value": "[\"eyes4\",\"eyes11\",\"eyes10\",\"eyes9\",\"eyes8\"]",
                "id": "296"
            }
        },
        {
            "type": "customer-to-customer-attribute",
            "id": "297",
            "attributes": {
                "customer_id": "386969",
                "attribute_id": "19",
                "attribute_value": "[\"tzone4\",\"tzone8\",\"tzone11\"]",
                "id": "297"
            }
        },
        {
            "type": "customer-to-customer-attribute",
            "id": "298",
            "attributes": {
                "customer_id": "386969",
                "attribute_id": "20",
                "attribute_value": "[\"cheeks6\",\"cheeks3\"]",
                "id": "298"
            }
        }
    ]
}

### Update contact in mobile
### platform/api/app/src/be/Controllers/Customers/Legacy.php::update()
PUT https://gnc.api.dev.salesfloor.net/customers/2
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjc0NjY1ODQxLCJpYXQiOjE2NzQ2NjUyNDEsInJlZiI6MTY3OTUwMzY0MX0.4pvS6X7C5g5GZIit25hAOQpSv7fQbTQas8cZDdqxMX8
content-type: application/json
origin: http://localhost:9000
referer: http://localhost:9000

{
    "user_id": 115,
    "email": "<EMAIL>",
    "name": "Lei Yang",
    "phone": "+15144321218",
    "localization": "en",
    "geo": "",
    "latitude": null,
    "longitude": null,
    "comment": "",
    "subcribtion_flag": "0",
    "sms_marketing_subscription_flag": "0",
    "first_name": "Lei1",
    "last_name": "Yang",
    "note": "",
    "created": "2023-01-25 16:56:22",
    "last_modified": "2023-01-25 16:56:28",
    "label_email": null,
    "label_phone": null,
    "retailer_customer_id": null,
    "origin": "mobile-rep-manual-creation",
    "locale": "en_US",
    "unassigned_employee_id": null,
    "retailer_parent_customer_id": null,
    "entity_last_modified": "2023-01-25 16:56:28",
    "entity_last_export": null,
    "contact_preference": "email",
    "customer_type": "corporate",
    "id": "2",
    "type": "customer",
    "is_sms_blacklisted": 0,
    "additionalEmails": [],
    "additionalPhones": [],
    "events": [],
    "socials": [],
    "addresses": [],
    "attributePanels": [],
    "subscription_flag": "0",
    "subscriptionStatus": "0",
    "smsSubscriptionStatus": "0",
    "isSubscribed": false,
    "isUnsubscribed": false,
    "isSMSSubscribed": false,
    "isSMSUnsubscribed": false,
    "hasSubscriptionFlag": true,
    "hasSMSSubscriptionFlag": true,
    "country": "CA"
}

### Delete a contact
POST https://gnc.dev.salesfloor.net/wp-admin/admin-ajax.php
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjc1MjY4OTc3LCJpYXQiOjE2NzUyNjgzNzcsInJlZiI6MTY4MDEwNjc3N30.RLDWiSjqM66OGr9PK_8EdK0pMaveOlT51O4Fe-uwGJ8
content-type: application/x-www-form-urlencoded; charset=UTF-8
cookie: dev_gnc_wordpress_test_cookie=WP+Cookie+check; _ga=GA1.2.1369564073.1675268365; _gid=GA1.2.1038804207.1675268365; dev_gnc_wordpress_sec_3ce968f549e93d9378cd2ce86181f331=reggie%7C1675279176%7Cb17adec5621c715c30c0005f4159d003; dev_gnc_sf_rep=reggie; dev_gnc_device_uuid=SF.7963504128.1675268376; dev_gnc_SF-TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjc1MjY4OTc3LCJpYXQiOjE2NzUyNjgzNzcsInJlZiI6MTY4MDEwNjc3N30.RLDWiSjqM66OGr9PK_8EdK0pMaveOlT51O4Fe-uwGJ8; dev_gnc_flashmsg=%5B%5D; cacheBustKey=1675268397015
origin: https://gnc.dev.salesfloor.net
referer: https://gnc.dev.salesfloor.net/backoffice/contacts

customer_id=4
&nonce=df60e546c7
&do=delete-customer
&action=sfhandlecontact

### Get count of customers by tag
### platform/api/app/src/be/Controllers/CustomerTags.php::getCustomerCountByTags()
### =============================================================
GET https://lilly.api.dev.salesfloor.net/customers-countbytags
    ?filter[customers]=subscribers
    &filter[mandatory_fields]=email
    &mandatory_fields=email
    &sf_locale=en_US HTTP/1.1
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjQxODQ0NjU3LCJpYXQiOjE2NDE4NDQwNTcsInJlZiI6MTY0NjY4MjQ1N30.2V8mSGC2znDqKbBQORAcYtj3n2nBm4kXnWHKtFBJnZ4

### Saks
GET https://saks.api.dev.salesfloor.net/customers-countbytags
    ?filter[customers]=subscribers
    &filter[mandatory_fields]=email
    &mandatory_fields=email
    &sf_locale=en_US HTTP/1.1
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMjc4IiwiZXhwIjoxNjY1MTU5NTg0LCJpYXQiOjE2NjUxNTg5ODQsInJlZiI6MTY2OTk5NzM4NH0.f_DIqm1ILQ2cw7-kGHAT5BX9380K_Ype6-pltxYgcBg

## Search by tags
### platform/api/app/src/be/Controllers/Customers/V1.php::getAll()
### =============================================================
GET https://chicos.api.dev.salesfloor.net/v1/customers?per_page=12
    &page=
    &filter[mandatory_fields]=
    &filter[user_id]=115
    &filter[search]=lei
    &filter[groups]=
    &filter[ID]=
    &filter[subcribtion_flag]=
    &filter[sms_marketing_subscription_flag]=
    &filter[private]=
    &filter[state]=
    &filter[city]=
    &filter[corporate]=
    &filter[tags]=2,1
    &fields=count_tasks
    &filter[locale]= HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-US,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjI2Mjg5NTkxLCJpYXQiOjE2MjYyODg5OTEsInJlZiI6MTYzMTEyNzM5MX0.NuIyfT2ZLg5EJes5ZJTgIj2B3JP3RF12MkGtlO9pbM8
content-type: application/json;charset=UTF-8
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"
sec-ch-ua-mobile: ?0
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36


### Filter by extended attributes
GET  https://chicos.api.dev.salesfloor.net/v1/customers?page=0
    &per_page=13
    &fields=count_tasks
    &filter[skin1_tzone]=tzone8
    &filter[user_id]=115 HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjUzOTIzOTM1LCJpYXQiOjE2NTM5MjMzMzUsInJlZiI6MTY1ODc2MTczNX0.L5wseW6Z77l_TBTiGCGYhd5OGA13Tctt93ou7ZMhJug
origin: http://localhost:9000
referer: http://localhost:9000/
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36

### Get contact overview
### platform/controllers/src/Base/LegacyController.php::getOne()
GET https://chicos.api.dev.salesfloor.net/customers/51?include=addresses,related-retailer-customers,count-customer-notes,social-media,customer-tags,events,retailer-customer-stats-insights&fields=count_tasks HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjgxNDg1Njk5LCJpYXQiOjE2ODE0ODUwOTksInJlZiI6MTY4NjMyMzQ5OX0.31DWFr0DOBj0S6KDIT1-v1ZPYHNp9pVGErwlKsZ_zpk
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Chromium";v="112", "Google Chrome";v="112", "Not:A-Brand";v="99"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

### Get contact information
### platform/controllers/src/Base/JsonApiController.php::getOne()
GET https://chicos.api.dev.salesfloor.net/v2/customers/51?include=customer-tag%2Ccustomer-meta%2Ccustomer-event%2Ccustomer-address%2Ccustomer-social-media%2Ccustomer-social-media.network%2Ccustomer-to-customer-attribute%2Ccustomer-to-customer-attribute.customer-attribute%2Ccustomer-to-customer-attribute.customer-attribute.customer-attribute-panel HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjgxNDg1Njk5LCJpYXQiOjE2ODE0ODUwOTksInJlZiI6MTY4NjMyMzQ5OX0.31DWFr0DOBj0S6KDIT1-v1ZPYHNp9pVGErwlKsZ_zpk
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Chromium";v="112", "Google Chrome";v="112", "Not:A-Brand";v="99"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36


curl --request GET \
  --url 'https://holt.api.dev.salesfloor.net/v1/customers?page=0&per_page=10&resultset-type=group_by_fields&filter%5Bsearch%5D=aa&filter%5Buser_id%5D=115' \
  --header 'accept: application/json, text/plain, */*' \
  --header 'accept-encoding: gzip, deflate, br' \
  --header 'accept-language: en-US,en;q=0.9' \
  --header 'authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjY0ODExOTUxLCJpYXQiOjE2NjQ4MTEzNTEsInJlZiI6MTY2OTY0OTc1MX0.jqZa9e17P0ZOdGzxNhofGaAqGLu9aDxiNKgD4lVzJnM' \
  --header 'content-type: application/json;charset=UTF-8' \
  --header 'origin: http://localhost:9000' \
  --header 'referer: http://localhost:9000/' \
  --header 'sec-ch-ua: "Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"' \
  --header 'sec-ch-ua-mobile: ?0' \
  --header 'sec-fetch-dest: empty' \
  --header 'sec-fetch-mode: cors' \
  --header 'sec-fetch-site: cross-site' \
  --header 'sf-app-id: Salesfloor Mobile App' \
  --header 'sf-app-version: dev' \
  --header 'sf-origin: mobile' \
  --header 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36'


### Update a contact
### platform/api/app/src/be/Controllers/Customers/Legacy.php::update()
PUT https://buckle.api.dev.salesfloor.net/customers/5540
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: en-GB,en;q=0.9
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNzE5NTE4OTg3LCJpYXQiOjE3MTk1MTgzODcsInJlZiI6MTcyNDM1Njc4N30.Z79sbYwi1BHDbi7Cs9hQKt_yqKRMsZcre3ZZpswUH3c
Cache-Control: no-cache
Content-Length: 3337
Content-Type: application/json
Origin: http://localhost:9000

{
    "user_id": 115,
    "email": "<EMAIL>",
    "name": "Denis Deo",
    "phone": "+12266061023",
    "localization": "en",
    "geo": "",
    "latitude": null,
    "longitude": null,
    "comment": "",
    "subcribtion_flag": "1",
    "sms_marketing_subscription_flag": "1",
    "first_name": "Denis",
    "last_name": "Deo",
    "note": "",
    "created": "2024-02-21 00:28:56",
    "last_modified": "2024-04-24 08:46:58",
    "label_email": null,
    "label_phone": null,
    "retailer_customer_id": null,
    "origin": "mobile-rep-manual-creation",
    "locale": "en_US",
    "unassigned_employee_id": null,
    "retailer_parent_customer_id": null,
    "entity_last_modified": "2024-04-24 08:46:58",
    "entity_last_export": "2024-04-24 00:00:00",
    "contact_preference": null,
    "customer_type": "personal",
    "id": "5540",
    "type": "customer",
    "is_sms_blacklisted": 0,
    "is_favorite_contact": true,
    "additionalEmails": [],
    "additionalPhones": [],
    "events": [],
    "socials": [],
    "addresses": [],
    "attributePanels": [],
    "customer_tags": [],
    "subscriptionStatus": "1",
    "smsSubscriptionStatus": "1",
    "isSubscribed": true,
    "isUnsubscribed": false,
    "isSMSSubscribed": true,
    "isSMSUnsubscribed": false,
    "hasSubscriptionFlag": true,
    "hasSMSSubscriptionFlag": true,
    "ID": 5540,
    "signature": "data:image/png;base64,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",
    "country": "CA",
    "subscription_flag": "0"
}


### Update a contact by removing an additional phone
### platform/api/app/src/be/Controllers/Customers/Legacy.php::update()
PUT https://holt.api.dev.salesfloor.net/customers/296?sf_locale=en_US
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: en-GB,en;q=0.9
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNzM2OTU5ODk0LCJpYXQiOjE3MzY5NTkyOTQsInJlZiI6MTc0MTc5NzY5NH0.RsbBFrLl1mMRNlmRfZS0WvgfRlN7vULobR2knSafcN8
Cache-Control: no-cache
Content-Length: 3337
Content-Type: application/json
Origin: http://localhost:9000

{
    "user_id": 115,
    "email": "<EMAIL>",
    "name": "Aaabb Abab",
    "phone": "+14389256742",
    "localization": "en",
    "geo": "",
    "latitude": null,
    "longitude": null,
    "comment": "",
    "subcribtion_flag": "2",
    "sms_marketing_subscription_flag": "2",
    "first_name": "Aaabb",
    "last_name": "Abab",
    "note": "",
    "created": "2021-07-15 21:56:23",
    "last_modified": "2025-01-15 16:48:17",
    "label_email": null,
    "label_phone": null,
    "retailer_customer_id": null,
    "origin": "mobile-rep-manual-creation",
    "locale": "en_US",
    "unassigned_employee_id": null,
    "retailer_parent_customer_id": null,
    "entity_last_modified": "2025-01-15 16:48:17",
    "entity_last_export": "0000-00-00 00:00:00",
    "contact_preference": null,
    "customer_type": "personal",
    "id": "296",
    "type": "customer",
    "is_sms_blacklisted": 1,
    "is_favorite_contact": false,
    "additionalEmails": [],
    "additionalPhones": [
        {
            "customer_id": "296",
            "value": "+15144321862",
            "label": "",
            "position": -1,
            "creation_date": "2025-01-15 16:47:04",
            "modification_date": null,
            "customer-meta_type": "phone",
            "id": "63",
            "type": "customer-meta",
            "phone": ""
        }
    ],
    "events": [],
    "socials": [],
    "addresses": [],
    "attributePanels": [],
    "customer_tags": [],
    "subscriptionStatus": "2",
    "smsSubscriptionStatus": "2",
    "isSubscribed": false,
    "isUnsubscribed": true,
    "isSMSSubscribed": false,
    "isSMSUnsubscribed": true,
    "hasSubscriptionFlag": true,
    "hasSMSSubscriptionFlag": true,
    "ID": 296,
    "country": "CA",
    "subscription_flag": "0"
}

### test
POST https://tests.api.dev.salesfloor.net/tests/customers-cron-import/hr_customers_import.csv
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-GB,en;q=0.9
authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoxLCJleHAiOjE3MzA0ODAwNjQsImlhdCI6MTczMDQ3OTQ2NCwicmVmIjoxNzM1MzE3ODY0fQ.hf3W7nbwsh7v3F7gM9fwDMHYcF9L8K5Tnw-v8hYgnsE

## Get list of contacts/customers
### platform/api/app/src/be/Controllers/Customers/V1.php::getAll()
### platform/api/app/src/be/Managers/Client/Customers/Legacy.php::fetchAll()
### =============================================================
GET https://chicos.api.dev.salesfloor.net/v1/customers
    ?page=0
    &per_page=10
    &fields=count_tasks
    &filter[user_id]=115
    &filter[sort]=last_contacted HTTP/1.1
accept: application/json, text/plain, */*
accept-encoding: gzip, deflate, br
accept-language: en-US,en;q=0.9
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoicmVnZ2llIiwic3ViIjoiMTE1IiwiZXhwIjoxNjU3MDMzNDQ3LCJpYXQiOjE2NTcwMzI4NDcsInJlZiI6MTY2MTg3MTI0N30.E4Yxlp0uQh5bke05JLU8ki4n56GIwDnLDhRGH4Pszxs
content-type: application/json;charset=UTF-8
origin: http://localhost:9000
referer: http://localhost:9000/
sec-ch-ua: "Google Chrome";v="89", "Chromium";v="89", ";Not A Brand";v="99"
sec-ch-ua-mobile: ?0
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: cross-site
sf-app-id: Salesfloor Mobile App
sf-app-version: dev
sf-origin: mobile
user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36

GET  https://buckle.api.dev.salesfloor.net/v2/customers/1
    ?include=customer-tag,customer-meta,customer-event,customer-address,customer-social-media,customer-social-media.network,customer-to-customer-attribute,customer-to-customer-attribute.customer-attribute,customer-to-customer-attribute.customer-attribute.customer-attribute-panel