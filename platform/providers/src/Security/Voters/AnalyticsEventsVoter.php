<?php

declare(strict_types=1);

namespace Salesfloor\Providers\Security\Voters;

use Salesfloor\Models\Base as BaseModel;
use Salesfloor\Models\Rep;
use Salesfloor\Models\AnalyticsEvents;
use Salesfloor\Services\GroupPermissionService;

class AnalyticsEventsVoter extends Base
{
    /**
     * @inheritdoc
     */
    protected function getInstanceType(): string
    {
        return AnalyticsEvents::class;
    }

    /**
     * @inheritdoc
     */
    protected function canView(Rep $user, BaseModel $model): bool
    {
        // Only Salesfloor admin users can view analytics events
        return $user->group > GroupPermissionService::CORP_ADMIN;
    }

    /**
     * @inheritdoc
     */
    protected function canCreate(Rep $user, BaseModel $model = null): bool
    {
        // Any authenticated user can create analytics events
        return true;
    }

    /**
     * @inheritdoc
     */
    protected function canUpdate(Rep $user, BaseModel $model): bool
    {
        // Analytics events should not be updated once created
        return false;
    }

    /**
     * @inheritdoc
     */
    protected function canDelete(Rep $user, BaseModel $model): bool
    {
        // Analytics events should not be deleted
        return false;
    }
}
