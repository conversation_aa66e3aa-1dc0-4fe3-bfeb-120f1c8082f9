<?php

declare(strict_types=1);

namespace Salesfloor\Providers\Security\Voters;

use Salesfloor\Models\Base as BaseModel;
use Salesfloor\Models\AI\Prompt;
use Salesfloor\Models\Rep;

class AIPromptVoter extends Base
{
    /**
     * @inheritdoc
     */
    protected function getInstanceType(): string
    {
        return Prompt::class;
    }

    protected function canView(Rep $user, BaseModel $model): bool
    {
        if (in_array($model->user_id, $this->getValidUsers($user))) {
            return true;
        }

        return false;
    }
}
