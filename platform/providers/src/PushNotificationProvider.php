<?php

/**
 * NotificationLateMessagesProvider Service Provider
 *
 * Copyright 2019 - Salesfloor
 */

namespace Salesfloor\Providers;

use Salesfloor\Services\PushNotifications\FCMBackend;
use Salesfloor\Services\PushNotifications\Service as PushNotificationService;
use Salesfloor\Services\PushNotifications\SnsBackend;
use Salesfloor\Services\PushNotifications\SnsConfig as PushNotificationConfig;
use GuzzleHttp\Client;
use Pimple\Container;
use Pimple\ServiceProviderInterface;
use Salesfloor\Services\Auth\TokenService;
use Symfony\Component\Cache\Adapter\ArrayAdapter;

class PushNotificationProvider implements ServiceProviderInterface
{
    /**
     * @inheritdoc
     * @return void
     */
    public function register(Container $app): void
    {
        $app['service.push'] = function ($app) {
            $configs = $app['configs'];
            $pushNotificationConfig = new PushNotificationConfig(
                $configs['env'],
                $configs['retailer.short_name']
            );

            // Ensure TokenService is available
            if (!isset($app['service.auth.token'])) {
                $memoryCache = new ArrayAdapter();
                $httpClient = new Client();
                $app['service.auth.token'] = new TokenService(
                    $app['logger'],
                    $memoryCache,
                    $httpClient
                );
            }

            if (
                isset($configs['service.pushnotification']) &&
                $configs['service.pushnotification'] === FCMBackend::FCM_PREFIX
            ) {
                $backend = new FCMBackend(
                    $configs['fcm.project_id'],
                    $configs['fcm.key'],
                    $configs['fcm.key.secret_file'],
                    $configs['fcm.version'],
                    $configs['fcm.verification.url'],
                    $pushNotificationConfig,
                    $app['logger'],
                    $app['devices.manager'],
                    $configs['firebase.service_account'] ?? null,
                    $app['service.auth.token']
                );
            } else {
                $backend = new SnsBackend(
                    $configs['sns.key'],
                    $configs['sns.secret'],
                    $configs['sns.region'],
                    $pushNotificationConfig,
                    $app['logger']
                );
            }

            $mysql = $app['mysql.importer'];

            return new PushNotificationService($backend, $mysql, $app);
        };
    }

    /**
     * @inheritdoc
     * @return void
     */
    public function boot(Container $app): void
    {
        // NOP
    }
}
