<?php

/**
 * NotificationLateMessagesProvider Service Provider
 *
 * Copyright 2019 - Salesfloor
 */

namespace Salesfloor\Providers;

use Salesfloor\Services\PushNotifications\FCMBackend;
use Salesfloor\Services\PushNotifications\Service as PushNotificationService;
use Salesfloor\Services\PushNotifications\SnsBackend;
use Salesfloor\Services\PushNotifications\SnsConfig as PushNotificationConfig;
use Pimple\Container;
use Pimple\ServiceProviderInterface;

class PushNotificationProvider implements ServiceProviderInterface
{
   /**
    * @inheritdoc
    */
    public function register(Container $app)
    {
        $app['service.push'] = function ($app) {
            $configs = $app['configs'];
            $pushNotificationConfig = new PushNotificationConfig(
                $configs['env'],
                $configs['retailer.short_name'],
            );

            if (isset($configs['service.pushnotification']) && $configs['service.pushnotification'] === FCMBackend::FCM_PREFIX) {
                $backend = new FCMBackend(
                    $configs['fcm.project_id'],
                    $configs['fcm.key'],
                    $configs['fcm.key.secret_file'],
                    $configs['fcm.version'],
                    $configs['fcm.verification.url'],
                    $pushNotificationConfig,
                    $app['logger'],
                    $app['devices.manager'],
                    $configs['firebase.service_account'] ?? null,
                );
            } else {
                $backend = new SnsBackend($configs['sns.key'], $configs['sns.secret'], $configs['sns.region'], $pushNotificationConfig, $app['logger']);
            }

            $mysql = $app['mysql.importer'];

            return new PushNotificationService($backend, $mysql, $app);
        };
    }

    /**
     * @inheritdoc
     */
    public function boot(Container $app)
    {
      // NOP
    }
}
