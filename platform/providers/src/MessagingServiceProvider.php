<?php

namespace Salesfloor\Providers;

use Salesfloor\Services\Messaging\Text\SendingQueue;
use Pi<PERSON>\Container;
use Pimple\ServiceProviderInterface;
use Salesfloor\Services\Proxy;
use Salesfloor\Services\Messaging\Text\Channel as TextChannel;
use Salesfloor\Services\Messaging\Email\Channel as EmailChannel;
use Salesfloor\Services\Messaging\Social\Channel as SocialChannel;
use Salesfloor\Services\Messaging\Recipient\RecipientService;
use Salesfloor\Services\Messaging\Text\TwilioProvider;

class MessagingServiceProvider implements ServiceProviderInterface
{
    /**
     * @inheritdoc
     */
    public function register(Container $app)
    {
        $app['service.messaging.text']   = function ($app) {
            return new Proxy(function () use ($app) {
                return new TextChannel($app);
            });
        };

        $app['service.messaging.text.sending_queue'] = function ($app) {
            return new SendingQueue($app['configs'], $app['logger'], $app['service.messagequeue']);
        };

        $app['service.messaging.email']  = function ($app) {
            return new EmailChannel($app);
        };
        $app['service.messaging.social'] = function ($app) {
            return new SocialChannel($app);
        };

        $app['service.messaging.text.twilio']   = function ($app) {
            return new Proxy(function () use ($app) {
                return new TwilioProvider($app);
            });
        };

        $app['service.messaging.recipient'] = function ($app) {
            // When loading twilio channel service we are calling this service in constructor
            // So if this constructor is called before boot ; userId will be empty.
            // This seems to only be the case during test mockup creation.
            return new Proxy(function () use ($app) {
                return new RecipientService($app);
            });
        };
    }

    /**
     * @inheritdoc
     */
    public function boot(Container $app)
    {
    }
}
