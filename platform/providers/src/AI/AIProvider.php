<?php

namespace Salesfloor\Providers\AI;

use Guz<PERSON>Http\Client;
use Pi<PERSON>\Container;
use <PERSON>mple\ServiceProviderInterface;
use Salesfloor\Providers\AI\Agent\CampaignAgentServiceProvider;
use Salesfloor\Providers\AI\Agent\ContentAnalyzerAgentServiceProvider;
use Salesfloor\Providers\AI\Agent\ContentRefinerAgentServiceProvider;
use Salesfloor\Providers\AI\Agent\RequestAnalyzerAgentServiceProvider;
use Salesfloor\Providers\AI\Agent\LanguageDetectionAgentServiceProvider;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\Auth\TokenService;
use Symfony\Component\Cache\Adapter\ArrayAdapter;

class AIProvider implements ServiceProviderInterface
{
    /**
     * @inheritdoc
     * @return void
     */
    public function register(Container $app): void
    {
        $app['service.ai.text'] = function ($app) {
            // Ensure TokenService is available
            if (!isset($app['service.auth.token'])) {
                $memoryCache = new ArrayAdapter();
                $httpClient = new Client();

                $app['service.auth.token'] = new TokenService(
                    $app['logger'],
                    $memoryCache,
                    $httpClient
                );
            }

            $httpClient = new Client();

            return new AIService(
                $app['configs'],
                $app['logger'],
                $app['service.auth.token'],
                $httpClient
            );
        };

        // Register AI agent service providers
        $app->register(new CampaignAgentServiceProvider());
        $app->register(new RequestAnalyzerAgentServiceProvider());
        $app->register(new ContentRefinerAgentServiceProvider());
        $app->register(new ContentAnalyzerAgentServiceProvider());
        $app->register(new LanguageDetectionAgentServiceProvider());
    }

    /**
     * @inheritdoc
     * @return void
     */
    public function boot(Container $app): void
    {
        // Nothing to do here
    }
}
