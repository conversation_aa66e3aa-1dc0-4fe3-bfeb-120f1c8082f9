<?php

namespace Salesfloor\Providers\AI\Agent;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\AI\Agent\CampaignAgent;

/**
 * Service provider for the CampaignAgent
 */
class CampaignAgentServiceProvider implements ServiceProviderInterface
{
    /**
     * Registers the CampaignAgent service in the application container
     *
     * @param Container $app The application container
     */
    public function register(Container $app): void
    {
        // Register the CampaignAgent as a service
        $app['service.ai.agent.campaign'] = function ($app) {
            return new CampaignAgent($app['service.ai.text'], $app['logger']);
        };
    }
}
