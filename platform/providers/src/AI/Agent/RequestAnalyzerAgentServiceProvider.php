<?php

namespace Salesfloor\Providers\AI\Agent;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\AI\Agent\RequestAnalyzerAgent;

/**
 * Service provider for the RequestAnalyzerAgent
 */
class RequestAnalyzerAgentServiceProvider implements ServiceProviderInterface
{
    /**
     * Registers the RequestAnalyzerAgent service in the application container
     *
     * @param Container $app The application container
     */
    public function register(Container $app): void
    {
        // Register the RequestAnalyzerAgent as a service
        $app['service.ai.agent.request_analyzer'] = function ($app) {
            return new RequestAnalyzerAgent(
                $app['service.ai.text'],
                $app['logger'],
                $app['service.ai.agent.campaign']
            );
        };
    }
}
