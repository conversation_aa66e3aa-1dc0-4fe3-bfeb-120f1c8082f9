<?php

namespace Salesfloor\Providers\AI\Agent;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\AI\Agent\ContentAnalyzerAgent;

/**
 * Service provider for the RequestAnalyzerAgent
 */
class ContentAnalyzerAgentServiceProvider implements ServiceProviderInterface
{
    /**
     * Registers the ContentAnalyzerAgent service in the application container
     *
     * @param Container $app The application container
     */
    public function register(Container $app): void
    {
        // Register the RequestAnalyzerAgent as a service
        $app['service.ai.agent.content_analyzer'] = function ($app) {
            return new ContentAnalyzerAgent(
                $app['service.ai.text'],
                $app['logger'],
                $app['service.ai.agent.content_refiner']
            );
        };
    }
}
