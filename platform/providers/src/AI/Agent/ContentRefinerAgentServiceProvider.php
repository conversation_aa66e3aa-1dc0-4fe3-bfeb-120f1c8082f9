<?php

namespace Salesfloor\Providers\AI\Agent;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\AI\Agent\ContentRefinerAgent;

/**
 * Service provider for the CampaignAgent
 */
class ContentRefinerAgentServiceProvider implements ServiceProviderInterface
{
    /**
     * Registers the CampaignAgent service in the application container
     *
     * @param Container $app The application container
     */
    public function register(Container $app): void
    {
        // Register the CampaignAgent as a service
        $app['service.ai.agent.content_refiner'] = function ($app) {
            return new ContentRefinerAgent(
                $app['service.ai.text'],
                $app['logger']
            );
        };
    }
}
