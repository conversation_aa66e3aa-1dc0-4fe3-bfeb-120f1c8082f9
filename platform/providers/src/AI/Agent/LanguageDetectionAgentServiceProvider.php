<?php

namespace Salesfloor\Providers\AI\Agent;

use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\AI\Agent\LanguageDetectionAgent;
use Salesfloor\Services\AI\Agent\RequestAnalyzerAgent;

/**
 * Service provider for the RequestAnalyzerAgent
 */
class LanguageDetectionAgentServiceProvider implements ServiceProviderInterface
{
    /**
     * Registers the LanguageDetectionAgent service in the application container
     *
     * @param Container $app The application container
     */
    public function register(Container $app): void
    {
        // Register the RequestAnalyzerAgent as a service
        $app['service.ai.agent.language_detector'] = function ($app) {
            return new LanguageDetectionAgent(
                $app['service.ai.text'],
                $app['logger']
            );
        };
    }
}
