<?php

namespace Salesfloor\Providers;

use GuzzleHttp\Client;
use Pi<PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\Auth\TokenService;
use Symfony\Component\Cache\Adapter\ArrayAdapter;

class AuthTokenProvider implements ServiceProviderInterface
{
    /**
     * @inheritdoc
     * @return void
     */
    public function register(Container $app): void
    {
        $app['service.auth.token'] = function ($app) {
            // Create a memory cache for storing tokens temporarily
            $memoryCache = new ArrayAdapter();

            $httpClient = new Client();

            return new TokenService($app['logger'], $memoryCache, $httpClient);
        };
    }

    /**
     * @inheritdoc
     * @return void
     */
    public function boot(Container $app): void
    {
        // Nothing to do here
    }
}
