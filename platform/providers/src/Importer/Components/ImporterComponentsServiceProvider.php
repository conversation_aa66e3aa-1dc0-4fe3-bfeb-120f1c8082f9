<?php

namespace Salesfloor\Providers\Importer\Components;

use Pimple\Container;
use Pimple\ServiceProviderInterface;
use Salesfloor\API\Managers\Import;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\NonPaginatedIterationStrategy;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\PaginatedIterationStrategy;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Product\DefaultFetcher as DefaultProductApiFetcher;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Product\MockFetcher as MockProductApiFetcher;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Shopify\RetailerCustomers as ShopifyCustomersFetcher;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Shopify\RetailerTransactions as ShopifyTransactionsFetcher;
use Salesfloor\Services\Importer\Components\Fetcher\Direct\DirectFetcherFactory;
use Salesfloor\Services\Importer\Components\Fetcher\S3\BaseS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\ProductFeedS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\RetailerCustomerS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\RetailerStatsS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\RetailerTransactionS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\S3FetcherFactory;
use Salesfloor\Services\Importer\Components\Fetcher\S3\TransactionS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\ValidationS3Fetcher;
use Salesfloor\Services\Importer\Components\Ingestor\Api\Generic\Product\DefaultIngestor as DefaultProductApiIngestor;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Shopify\RetailerCustomersGraphQLFetcher as ShopifyGraphQLCustomersFetcher;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Shopify\RetailerTransactionsGraphQLFetcher as ShopifyGraphQLTransactionsFetcher;
use Salesfloor\Services\Importer\Components\Ingestor\Api\Shopify\BaseRetailerCustomers as ShopifyCustomersIngestor;
use Salesfloor\Services\Importer\Components\Ingestor\Api\Shopify\BaseRetailerCustomersGraphQLIngestor as ShopifyGraphQLCustomersIngestor;
use Salesfloor\Services\Importer\Components\Ingestor\Api\Shopify\BaseRetailerTransactions as ShopifyTransactionsIngestor;
use Salesfloor\Services\Importer\Components\Ingestor\Api\Shopify\BaseRetailerTransactionsGraphQLIngestor as ShopifyGraphQLTransactionsIngestor;
use Salesfloor\Services\Importer\Components\Ingestor\File\CsvFileIngestor;
use Salesfloor\Services\Importer\Components\LastUpdatedDetector;
use Salesfloor\Services\Importer\Product\ProductCsvIngestor;
use Salesfloor\Services\Importer\RepTransaction\RepTransactionCsvIngestor;

/**
 * How to name your Importer Component services:
 *
 * importer.<pipeline-stage>.<data-source>.<resource>
 */
class ImporterComponentsServiceProvider implements ServiceProviderInterface
{
    /**
     * Registers services on the given app.
     *
     * This method should only be used to configure services and parameters.
     * It should not get services.
     */
    public function register(Container $app)
    {
        // Load this first - required by other components.
        $this->registerAdditionalComponents($app);

        $this->registerFetchers($app);
        $this->registerIngestors($app);
    }

    private function registerFetchers(Container $app)
    {
        $app['importer.fetcher.s3'] = function ($app) {
            return new S3FetcherFactory(BaseS3Fetcher::class, $app);
        };

        $app['importer.fetcher.validation'] = function ($app) {
            return new S3FetcherFactory(ValidationS3Fetcher::class, $app);
        };

        $app['importer.fetcher.s3.retailer_customer'] = function ($app) {
            return new S3FetcherFactory(RetailerCustomerS3Fetcher::class, $app);
        };

        $app['importer.fetcher.s3.retailer_transaction'] = function ($app) {
            return new S3FetcherFactory(RetailerTransactionS3Fetcher::class, $app);
        };

        $app['importer.fetcher.s3.retailer_stats'] = function ($app) {
            return new S3FetcherFactory(RetailerStatsS3Fetcher::class, $app);
        };

        $app['importer.fetcher.s3.product'] = function ($app) {
            return new S3FetcherFactory(ProductFeedS3Fetcher::class, $app);
        };

        $app['importer.fetcher.s3.transaction'] = function ($app) {
            return new S3FetcherFactory(TransactionS3Fetcher::class, $app);
        };

        $app['importer.fetcher.web.product.factory'] = function ($app) {
            return new DirectFetcherFactory($app['filemanager']);
        };

        $app['importer.fetcher.web.addresses'] = function ($app) {
            return new DirectFetcherFactory($app['filemanager']);
        };

        $app['importer.fetcher.api.product.mock'] = function ($app) {
            return function () use ($app) {
                return new MockProductApiFetcher($app['configs']);
            };
        };

        $app['importer.fetcher.api.product.default.paginated-iteration-strategy'] = function ($app) {
            return new PaginatedIterationStrategy(new \GuzzleHttp\Client());
        };

        $app['importer.fetcher.api.product.default.non-paginated-iteration-strategy'] = function ($app) {
            return new NonPaginatedIterationStrategy(new \GuzzleHttp\Client());
        };

        $app['importer.fetcher.api.product.default'] = function ($app) {
            return function () use ($app) {
                $configs = $app['configs'];
                if (empty($configs['importer.product.fetcher.api.is_paginated'])) {
                    $iterationStrategy = $app['importer.fetcher.api.product.default.non-paginated-iteration-strategy'];
                } else {
                    $iterationStrategy = $app['importer.fetcher.api.product.default.paginated-iteration-strategy'];
                    $iterationStrategy->setPerPage($configs['importer.product.fetcher.api.per_page']);
                    $iterationStrategy->setStartPage($configs['importer.product.fetcher.api.start_page']);
                }
                $iterationStrategy->setUrl($configs['importer.product.fetcher.api.url']);
                $iterationStrategy->authenticate($configs['importer.product.fetcher.api.credentials']);
                return new DefaultProductApiFetcher($iterationStrategy);
            };
        };

        $app['importer.fetcher.shopify.retailer_customer'] = function ($app) {
            return function () use ($app) {
                return new ShopifyCustomersFetcher(
                    $app['service.shopify'],
                    $app['importer.component.last_updated'],
                    $app['logger'],
                    $app['configs']
                );
            };
        };

        $app['importer.fetcher.shopify_graphql.retailer_customer'] = function ($app) {
            return function () use ($app) {
                return $this->getShopifyGraphQLCustomersFetcherClassByRetailer(
                    $app,
                    [
                        $app['service.shopify'],
                        $app['importer.component.last_updated'],
                        $app['logger'],
                        $app['configs'],
                    ]
                );
            };
        };

        $app['importer.fetcher.shopify.retailer_transaction'] = function ($app) {
            return function () use ($app) {
                return new ShopifyTransactionsFetcher(
                    $app['service.shopify'],
                    $app['importer.component.last_updated'],
                    $app['logger'],
                    $app['configs']
                );
            };
        };

        $app['importer.fetcher.shopify_graphql.retailer_transaction'] = function ($app) {
            return function () use ($app) {
                return new ShopifyGraphQLTransactionsFetcher(
                    $app['service.shopify'],
                    $app['importer.component.last_updated'],
                    $app['logger'],
                    $app['configs']
                );
            };
        };
    }

    private function registerIngestors(Container $app)
    {
        $app['importer.ingestor.csv'] = function ($app) {
            return function ($skipHeader = true) use ($app) {
                return new CsvFileIngestor($app['filemanager'], $skipHeader);
            };
        };


        $app['importer.' . Import::TYPE_CUSTOMER . '.ingestor'] = function ($app) {
            return function ($skipHeader = true) use ($app) {
                $args = [$app['filemanager'], $skipHeader];

                return $this->getIngestorClassByRetailer($app, Import::TYPE_CUSTOMER, $args);
            };
        };

        $app['importer.' . Import::TYPE_TRANSACTION . '.ingestor'] = function ($app) {
            return function ($skipHeader = true) use ($app) {
                $args = [$app['filemanager'], $skipHeader];

                return $this->getIngestorClassByRetailer($app, Import::TYPE_TRANSACTION, $args);
            };
        };

        $app['importer.ingestor.csv.products'] = function ($app) {
            return function ($skipHeader = true) use ($app) {
                $args = [$app['filemanager'], $skipHeader];
                return $this->getIngestorClassByRetailer($app, Import::TYPE_PRODUCT, $args, ProductCsvIngestor::class);
            };
        };

        $app['importer.ingestor.csv.transactions'] = function ($app) {
            return function ($skipHeader = true) use ($app) {
                $args = [$app['filemanager'], $skipHeader];

                return $this->getIngestorClassByRetailer(
                    $app,
                    Import::TYPE_REP_TRANSACTION,
                    $args,
                    RepTransactionCsvIngestor::class
                );
            };
        };

        $app['importer.ingestor.api.product.default'] = function ($app) {
            return function () use ($app) {
                return new DefaultProductApiIngestor($app['configs']);
            };
        };

        $app['importer.ingestor.api.product'] = function ($app) {
            return function () use ($app) {
                return $this->getApiIngestorClassByRetailer($app, [$app['configs']]);
            };
        };

        $app['importer.ingestor.shopify.retailer_customer'] = function ($app) {
            return function () use ($app) {
                return new ShopifyCustomersIngestor();
            };
        };

        $app['importer.ingestor.shopify_graphql.retailer_customer'] = function ($app) {
            return function () use ($app) {
                return $this->getShopifyGraphQLCustomersIngestorClassByRetailer($app, [$app['configs']]);
            };
        };

        $app['importer.ingestor.shopify.retailer_transaction'] = function ($app) {
            return function () use ($app) {
                return new ShopifyTransactionsIngestor();
            };
        };

        $app['importer.ingestor.shopify_graphql.retailer_transaction'] = function ($app) {
            return function () use ($app) {
                return $this->getShopifyGraphQLRetailerTransactionsIngestorClassByRetailer($app, [$app['configs']]);
            };
        };
    }

    /**
     * Get the appropriate ingestor class for the current retailer.
     *
     * If the retailer doesn't have an ingestor defined, use the default (as passed in $default)
     *
     * @param $app
     * @param $type string The import type used for this ingestor.
     * @param $args array The constructor arguments to pass.
     * @param string $default The class name to default to if no specific class found.
     * @return mixed
     */
    private function getIngestorClassByRetailer($app, $type, $args, $default = CsvFileIngestor::class)
    {
        $class = 'Salesfloor\\Services\\Importer\\Components\\Ingestor\\File\\Retailer\\'
            . ucfirst($app['configs']['retailer.short_name'])
            . '\\' . ucfirst($type) . 'Ingestor'
        ;

        if (!class_exists($class)) {
            $class = $default;
        }

        return new $class(...$args);
    }

    /**
     * Get the appropriate api ingestor class for the current retailer.
     *
     * If the retailer doesn't have an ingestor defined, use the default (as passed in $default)
     *
     * @param $app
     * @param $args array The constructor arguments to pass.
     * @param string $default The class name to default to if no specific class found.
     * @return mixed
     */
    private function getApiIngestorClassByRetailer($app, $args, $default = DefaultProductApiIngestor::class)
    {
        $class = 'Salesfloor\\Services\\Importer\\Components\\Ingestor\\Api\\Generic\\Product\\Retailer\\'
            . ucfirst($app['configs']['retailer.short_name'])
            . '\\' . 'ProductIngestor';

        if (!class_exists($class)) {
            $class = $default;
        }

        return new $class(...$args);
    }

    /**
     * Get the appropriate Shopify GraphQL ingestor class for the current retailer.
     * If the retailer doesn't have an ingestor defined, use the default (as passed in $default)
     *
     * @param $app
     * @param $args array The constructor arguments to pass.
     * @param string $default The class name to default to if no specific class found.
     * @return mixed
     */
    private function getShopifyGraphQLCustomersIngestorClassByRetailer($app, $args, $default = ShopifyGraphQLCustomersIngestor::class)
    {
        // ShopifyGraphQLCustomersIngestor
        // Salesfloor/Services/Importer/Components/Ingestor/Api/Retailer/{Retailer}/RetailerCustomerIngestor
        $class = 'Salesfloor\\Services\\Importer\\Components\\Ingestor\\Api\\Retailer\\'
            . ucfirst($app['configs']['retailer.short_name'])
            . '\\' . 'RetailerCustomerIngestor';

        if (!class_exists($class)) {
            $class = $default;
        }

        return new $class(...$args);
    }

    private function getShopifyGraphQLCustomersFetcherClassByRetailer($app, $args, $default = ShopifyGraphQLCustomersFetcher::class)
    {
        // ShopifyGraphQLCustomersFetcher(RetailerCustomersGraphQLFetcher)
        // Salesfloor/Services/Importer/Components/Fetcher/Api/Retailer/{Retailer}/RetailerCustomersGraphQLFetcher
        $class = 'Salesfloor\\Services\\Importer\\Components\\Fetcher\\Api\\Retailer\\'
            . ucfirst($app['configs']['retailer.short_name'])
            . '\\' . 'RetailerCustomersGraphQLFetcher';

        if (!class_exists($class)) {
            $class = $default;
        }

        return new $class(...$args);
    }

    /**
     * Get the appropriate Shopify GraphQL ingestor class for the current retailer.
     * If the retailer doesn't have an ingestor defined, use the default (as passed in $default)
     *
     * @param $app
     * @param $args array The constructor arguments to pass.
     * @param string $default The class name to default to if no specific class found.
     * @return object
     */
    private function getShopifyGraphQLRetailerTransactionsIngestorClassByRetailer($app, $args, $default = ShopifyGraphQLTransactionsIngestor::class)
    {
        // ShopifyGraphQLTransactionsIngestor
        // Salesfloor/Services/Importer/Components/Ingestor/Api/Retailer/{Retailer}/RetailerTransactionIngestor
        $class = 'Salesfloor\\Services\\Importer\\Components\\Ingestor\\Api\\Retailer\\'
            . ucfirst($app['configs']['retailer.short_name'])
            . '\\' . 'RetailerTransactionIngestor';

        if (!class_exists($class)) {
            $class = $default;
        }

        return new $class(...$args);
    }

    private function registerAdditionalComponents(Container $app)
    {
        $app['importer.component.last_updated'] = function ($app) {
            return new LastUpdatedDetector($app['repositories.mysql']);
        };
    }

    /**
     * Bootstraps the Container $app.
     *
     * This method is called after all services are registered
     * and should be used for "dynamic" configuration (whenever
     * a service must be requested).
     */
    public function boot(Container $app)
    {
        // TODO: Implement boot() method.
    }
}
