<?php

namespace Salesfloor\Providers\Tasks\Automated;

use Pimple\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use Salesfloor\Services\Tasks\Automated\Factory\RetailerCustomerEventMultiple;
use Salesfloor\Services\Tasks\Automated\NewRetailerTransactionFilteredMultiple;
use Salesfloor\Services\Tasks\Automated\RetailerTransactionEmployeeAssignedMultiple;

class AllAutomatedTasksServiceProvider implements ServiceProviderInterface
{
    /**
     * @inheritdoc
     */
    public function register(Container $app)
    {
        foreach (Task::$automated_types as $type) {
            $app[BaseScanner::SERVICE_BASE . $type] = function ($app) use ($type) {
                $className =  Task::convertAutomatedTypeToClassName($type);
                if (!class_exists($className)) {
                    throw new \Exception(sprintf('Class [%s] does not exist', $className));
                }
                return new $className($app);
            };
        }

        $app[BaseScanner::SERVICE_BASE . 'new_retailer_transaction_filtered_multiple'] = function ($app) {
            return new NewRetailerTransactionFilteredMultiple($app);
        };

        $app[BaseScanner::SERVICE_BASE . 'retailer_transaction_employee_assigned_multiple'] = function ($app) {
            return new RetailerTransactionEmployeeAssignedMultiple($app);
        };

        $app[BaseScanner::SERVICE_BASE . 'retailer_customer_event_multiple'] = function ($app) {
            return new RetailerCustomerEventMultiple($app);
        };
    }

    /**
     * @inheritdoc
     */
    public function boot(Container $app)
    {
        // NOP
    }
}
