# Default IMAGE if not passed with a value inside the build stage
ARG ECR_IMAGE=sf_base_web

FROM ${ECR_IMAGE} AS BUILDER

# # Use of iproute to allow routing through the telepresence container
# # To allow communication inside kubernetes.
# RUN apt-get update && \
#     apt-get -y install mysql-client openssh-server openssh-client vim tcpdump net-tools libgnutls30 sudo fish && \
#     apt-get -y install autoconf ruby-dev automake libffi7 libffi-dev netcat pv && \
#     apt-get -y install php8.3-xdebug && \
#     apt-get -y install iproute2

# # Install node 20 LTS (Supported until April 2026.
# RUN apt-get update && \
#     apt-get install -y ca-certificates curl gnupg && \
#     mkdir -p /etc/apt/keyrings && \
#     curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
#     echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_20.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
#     apt-get update && \
#     apt-get install -y nodejs

# RUN apt-get update && \
#     apt-get install -y software-properties-common language-pack-en-base && \
#     LC_ALL=en_US.UTF-8 && add-apt-repository ppa:maxmind/ppa && \
#     apt-get update && \
#     apt-get -y install libmaxminddb0 libmaxminddb-dev mmdb-bin && \
#     apt-get remove -y software-properties-common language-pack-en-base && \
#     apt-get autoremove -y

# JSPM Installation fails on some installations for some reasons.
# https://geedew.com/What-does-unsafe-perm-in-npm-actually-do/
# https://www.vinayraghu.com/blog/npm-unsafe-perm
# RUN npm config set unsafe-perm true

USER root
WORKDIR /srv/www/sf_platform/current/

# Env value used in php-fpm must be set otherwise it will crash
# ENV ENV=dev
# ENV CUSTOMER=UNSET
# ENV INFRA_CONFIG_PATH=UNSET
# ENV OVERRIDES_PATH=UNSET

# # X_DEBUG is in the .env file which docker compose loads
# # Makefile also contains disable_xdebug or enable_xdebug command

# ENV PHP_IDE_CONFIG serverName=docker

# # Define default www-data userid for local
# ARG UID=1000
# ARG GID=1000

# ENV UID ${UID:-1000}
# ENV GID ${GID:-1000}


# RUN     sed -ri "s/:$GID:/:88888:/g" /etc/group
# RUN     sed -ri "s/:$UID:/:88888:/g" /etc/passwd

# RUN 	usermod -u $UID www-data && \
# 	groupmod -g $GID www-data && \
#         chown $UID.$GID /var/www

# # RUN     sed -ri "s/20/9239829/g" /etc/passwd /etc/group

# RUN echo 'www-data ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# RUN	mkdir -p /logs/apache2 && \
# 	chmod u+rwx /logs/apache2
# End of WWW-DATA UID mods

# RUN echo \
# "xdebug.mode=debug\n"\
# "xdebug.start_with_request=yes\n"\
# "xdebug.client_host=host.docker.internal\n"\
# "xdebug.client_port=9011\n"\
# "xdebug.idekey=docker\n"\
# "xdebug.log_level=0\n"\
# >> /etc/php/${PHP_VERSION}/mods-available/xdebug.ini

# RUN mkdir -p /srv/www/sf_platform/current/node_modules && \
#      chown www-data: /srv/www/sf_platform/current /srv/www/sf_platform/current/node_modules

# COPY --chown=www-data ./certs/ /etc/ssl/certs/

# # Copy apache & logrotate files
# COPY	./etc/ /etc/
# RUN	chmod 0644  /etc/logrotate.template/*


# COPY	./entrypoint-dev.sh /usr/local/bin/entrypoint-dev.sh
# COPY	./entrypoint.sh /usr/local/bin/entrypoint.sh
# COPY	./deploy.sh /usr/local/bin/deploy.sh
# COPY	./docker-healthcheck.sh /usr/local/bin/docker-healthcheck.sh

# RUN	chmod +x /usr/local/bin/entrypoint-dev.sh \
#                  /usr/local/bin/entrypoint.sh \
#                  /usr/local/bin/docker-healthcheck.sh \
#                  /usr/local/bin/deploy.sh

# # Ensure a cron.csv file exist (mostly for local / testing)
# # Next COPY statement would overwrite the cron.csv from the platform release.
# RUN	ln -snf /srv/www/sf_platform /sf_platform
# RUN	ln -snf /srv/www/sf_platform/current/ /vagrant_data

# # Added current workdir for the robo command
# ENV PATH /srv/www/sf_platform/current/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# RUN mkdir -p /srv/www/sf_platform/current/node_modules /srv/www/sf_platform/current/node_modules/.bin /srv/www/sf_platform/current/node_modules/node-sass /srv/www/sf_platform/current/node_modules/node-sass/bin  && \
#     chown www-data: /srv/www/sf_platform/current /srv/www/sf_platform/current/node_modules /srv/www/sf_platform/current/node_modules/.bin /srv/www/sf_platform/current/node_modules/node-sass /srv/www/sf_platform/current/node_modules/node-sass/bin

# COPY --chown=www-data ./certs/ /etc/ssl/certs/

# RUN mkdir -p /var/www/.aws && \
#     chown www-data: /var/www/.aws && \
#     mkdir -p /opt/secrets/cloud-storage && \
#     chown www-data: /opt/secrets/ /opt/secrets/cloud-storage

# # Ensure to keep permissions
# VOLUME [ "/var/www/.aws", "/opt/secrets/cloud-storage" ]
# # Ensure a cron.csv file exist (mostly for local / testing)
# # Next COPY statement would overwrite the cron.csv from the platform release.
# RUN	ln -snf /srv/www/sf_platform /sf_platform
# RUN	ln -snf /srv/www/sf_platform/current/ /vagrant_data

# # Open port for httpd access
# EXPOSE 80

# # Added current workdir for the robo command
# ENV PATH /srv/www/sf_platform/current/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# # https://www.php.net/manual/en/install.fpm.configuration.php
# ENV FPM_WWW_START_SERVERS=5 \
#     FPM_WWW_MAX_CHILDREN=50 \
#     FPM_WWW_MIN_SPARE_SERVERS=5 \
#     FPM_WWW_MAX_SPARE_SERVERS=10 \
#     FPM_WWW_MAX_REQUESTS=1000 \
#     FPM_API_START_SERVERS=5 \
#     FPM_API_MAX_CHILDREN=50 \
#     FPM_API_MIN_SPARE_SERVERS=5 \
#     FPM_API_MAX_SPARE_SERVERS=10 \
#     FPM_API_MAX_REQUESTS=1000 \
#     FPM_WIDGETS_START_SERVERS=5 \
#     FPM_WIDGETS_MAX_CHILDREN=50 \
#     FPM_WIDGETS_MIN_SPARE_SERVERS=5 \
#     FPM_WIDGETS_MAX_SPARE_SERVERS=10 \
#     FPM_WIDGETS_MAX_REQUESTS=1000 \
#     # Extra log folder inside /logs/php-fpm/${PHP_FPM_LOG_SUFFIX_FOLDER}
#     PHP_FPM_LOG_SUFFIX_FOLDER=logs \
#     # The number of seconds after which an idle process will be killed. Used only when pm is set to ondemand.
#     FPM_IDLE_TIMEOUT_IN_SEC=300

# ENV OPCACHE_VALIDATE_TIMESTAMP 1
# ENV OPCACHE_MEMORY 256

# RUN mkdir -p /run/php

# ENTRYPOINT ["/usr/local/bin/entrypoint-dev.sh"]
CMD ["tail", "-f", "/dev/null"]
