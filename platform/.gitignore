.DS_Store
.vagrant
composer.phar
node_modules
GeoLiteCity.*
/.idea
*.swp
.vscode
onboarding-generator

/vendor

api/app/vendor
api/app/deploy_configs
api/composer.phar
api/node_modules
api/vagrant/xdebug.ini
api/doc/

widgets/app/vendor
widgets/app/tests/_output
widgets/node_modules
widgets/app/web/dist
widgets/app/web/css
widgets/app/web/js
widgets/app/web/fonts
widgets/tests/_output/*
widgets/app/web/vendor
widgets/app/web/locales
widgets/app/web/generated
widgets/tests/docs
widgets/app/src/fe/snippets/
widgets/vagrant/xdebug.ini
widgets/.jshintignore
!widgets/app/src/fe/snippets/snippet.js.hbs
!widgets/app/src/fe/snippets/snippet.cookie.js.hbs
!widgets/app/src/fe/snippets/snippet.localstorage.js.hbs
!widgets/app/vendor/fe/foundation/foundation.min.js
!widgets/app/vendor/fe/confirm-with-reveal/confirm-with-reveal.min.js

.vagrant
.DS_Store
.sass-cache
*.css.map
instance-webserver/configs/hosts
instance-webserver/src/salesfloor/node_modules/
instance-webserver/src/salesfloor/wp-content/plugins/advanced-post-types-order/
instance-webserver/src/salesfloor/wp-content/plugins/advanced-taxonomy-terms-order/
instance-webserver/src/salesfloor/wp-content/plugins/wordpress-importer/
instance-webserver/src/salesfloor/wp-content/themes/twentyfourteen-child/vendor/
instance-webserver/src/salesfloor/wp-includes/images/thumbnails/
instance-webserver/src/salesfloor/wp-content/uploads
instance-webserver/src/wp-includes/images/thumbnails/
instance-webserver/src/salesfloor/composer.phar
instance-webserver/src/salesfloor/wp-content/debug.log
instance-webserver/*.lock
instance-webserver/vagrant.log
instance-webserver/configs/ca-bundle.crt
instance-webserver/src/salesfloor/wp-content/debug.log
instance-webserver/src/salesfloor/vendor
instance-webserver/src/salesfloor/js/npm-debug.log
instance-webserver/src/salesfloor/npm-debug.log
instance-webserver/src/salesfloor/node_modules/
instance-webserver/src/salesfloor/css/dist/
instance-webserver/src/salesfloor/js/dist/
instance-webserver/src/salesfloor/css/responsive/
instance-webserver/src/salesfloor/css/customers_and_messages.css
instance-webserver/src/salesfloor/css/ben_assets2.css
instance-webserver/src/salesfloor/wp-admin/css/wp-admin.min.css
instance-webserver/src/salesfloor/js/nunjucksTemplates.js
instance-webserver/vagrant/xdebug.ini
!instance-webserver/composer.lock
instance-webserver/src/salesfloor/js/dist/main-services.min.js
instance-webserver/src/salesfloor/preview/

configs/configs/definition/features/descriptions-item.yaml
configs/configs/definition/features/descriptions-group.yaml


.dev-env-id
.envtype

sf-*.tar.gz
*.pyc
cron.csv
cron.json

c3.php

ci/docker/service-account.json

# related to gnupg, since I decided to put in repo since no devops was needed
services/gnupg/*
!services/gnupg/README.md

ci/docker/certs/apache-selfsigned.pem

# Seeds are now generated on the fly and we don't commit it anymore
vagrant/database/seeds/*.sql

.phpactor.json
