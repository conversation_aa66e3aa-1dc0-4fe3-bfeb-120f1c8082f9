<?php

scheduleCustomerInsights($crons);

$crons->addJob('send_kpi_emails', [
    'php' => 'SendWeeklyPerformanceEmails.php',
    'sched' => '0 12 * * Mon',
    'memory limit' => 512,
    'env' => ['SF_KPI_SENDNOW' => 1],
]);
$crons->addJob('ExportStoreActivitySummary', [
    'php' => 'ExportStoreActivitySummary.php',
    'sched' => '5 1 * * *',
]);
$crons->addJob('daily_outbound_chat_log', [
    'php' => 'ExportChatLogDaily.php',
    'sched' => '36 8 * * *',
]);
$crons->addJob('export_daily_store_liveChat_metrics', [
    'php' => 'ExportDailyStoreLiveChatMetrics.php',
    'sched' => '15 5 * * *',
]);
$crons->updateJob('export_customer_request', [
    'php' => 'ExportRequest.php',
    'sched' => '20 5 * * *',
]);
$crons->updateJob('automated_task_reminder', [
    'php' => 'SearchForAutomatedTasks.php',
    'sched' => '2 14 * * *',
    'scheduler' => 'k8s',
]);
$crons->addJob('daily_outbound_transactions', [
    'php' => 'CreateTransactionDailyExports.php',
    'sched' => '30 1 * * *',
]);
$crons->updateJob('catalog_update', [
    'prodSched' => '0 14 * * *',
]);
