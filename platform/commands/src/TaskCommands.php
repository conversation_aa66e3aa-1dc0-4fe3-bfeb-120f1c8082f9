<?php

namespace Salesfloor\Commands;

use Salesfloor\Models\Task as TaskModel;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use Salesfloor\Services\Tasks\Features\TaskAutoDismissProcessor;
use Salesfloor\Services\Util;
use Silex\Application;

/**
 * Tool to debug auto task generation
 */
class TaskCommands extends Base
{
    /**
     * Dry run the auto task generator
     *
     * @command task:dry-run chicos-dev
     *          --config-overwrite=prd                                      command run on dev env, get the sql by config from prd env
     *          --task-type=new_retailer_transaction_filtered
     *          --task-type=new_retailer_transaction_filtered_multiple
     *          --task-type=retailer_transaction_employee_assigned
     *          --task-type=retailer_transaction_employee_assigned_multiple
     *          --task-type=new_rep_transaction
     *          --task-type=retailer_customer_soon_to_lapse_filtered_multiple
     *          --task-type=transactions_distribution_by_stores
     *          --task-type=cancelled_transaction_follow_up
     *          --task-type=retailer_customer_stats_registry_followup
     * @param array[] $opts
     * @param string $retailer
     * @throws \ReflectionException
     */
    public function dryRun(string $retailer, array $opts = ['config-overwrite' => 'dev', 'task-type' => null])
    {
        $app = $this->getApplication($retailer);
        $configOverwrite = $opts['config-overwrite'];

        if ($app['configs']['env'] === 'prd') {
            $this->say("This task is only for use in not prd environments.");
            exit(5);
        }

        $this->say(sprintf("command in running in env %s, and mockup sql from env:%s ", $app['configs']['env'], $configOverwrite));

        $app['configs'] = $this->reloadEnviromentConfigs($app['configs']['retailer.name'], $configOverwrite);

        $taskType = $opts['task-type'];

        switch ($taskType) {
            case TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED:
            case TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE:
                //note: TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED: was not be used anymore
                $taskType = TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE;
                $this->scanNewRetailerTransactionFiltered($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED:
            case TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE:
                //note TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED: was not be used anymore
                $taskType = TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE;
                $this->scanRetailerTransactionEmployeeAssigned($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_NEW_REP_TRANSACTION:
                $this->scanNewRepTransaction($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED_MULTIPLE:
                //note: TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED: are not used anymore
                //note: TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_SECONDARY_FILTERED: are not used anymore
                $this->scanRetailerCustomerSoonToLapseFiltered($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_TRANSACTION_DISTRIBUTION_BY_STORES:
                $this->scanTransactionDistributionByStores($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_TRANSACTION_CANCELLED_FOLLOW_UP:
                $this->scanTransactionCancelledFollowUp($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT:
            case TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT_MULTIPLE:
                $taskType = TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT_MULTIPLE;
                $this->scanRetailerCustomerEvent($app, $taskType);
                break;
            case TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_FOLLOWUP:
                $taskType = TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_FOLLOWUP;
                $this->scanRetailerCustomerStatsRegistryFollowup($app, $taskType);
                break;
            default:
                $this->say("This task type is not support in debug tools yet");
                exit(5);
        }
    }

    /**
     * @link  https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1176862721/i.+CI+Sale+Transaction+Follow-Up+Primary+Associate+Store
     * @param Application $app
     * @param $taskType
     * @throws \ReflectionException
     */
    private function scanNewRetailerTransactionFiltered(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\NewRetailerTransactionFilteredMultiple $scanner */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        if (!in_array($taskType, TaskModel::$automated_types_multiple)) {
            // this section could be removed, keep here just in case we need single task in the future
            $this->yell('---- Query:ScanForTasks--------------');
            /** @var \Salesfloor\Services\Tasks\Automated\NewRetailerTransactionFiltered $scanner */
            $rawSql = $scanner->buildQueryScanForTasks();
            $this->say($rawSql);
            return;
        }

        // 1 fix rule query output
        $configDays = $this->getMethod($scanner, "getMultipleDaysBack")->invokeArgs($scanner, []);
        foreach ($configDays as $daysBack) {
            $instance = $scanner->getSingleTaskScanner();
            /** @var \Salesfloor\Services\Tasks\Automated\NewRetailerTransactionFiltered $instance */
            $instance->setDaysBack($daysBack);

            $this->yell("---- Query:ScanForTasks:FixRule:DaysBack:$daysBack--------------");
            $rawSql = $instance->buildQueryScanForTasks();
            $this->say($rawSql);
        }

        // 2 dynamic rule query output, a copy implement from processDynamicRule
        $rules = $scanner->getAllDynamicRuleSets();
        if (empty($rules)) {
            return;
        }

        foreach ($rules as $ruleKey => $ruleDetails) {
            $configDaysPerRule = $scanner->getMultipleDaysBackFromConfigPerRule($ruleKey);
            foreach ($configDaysPerRule as $daysBack) {
                $instance = $scanner->getSingleTaskScanner();
                $instance->setDaysBack($daysBack);
                $instance->bindRule($ruleDetails);

                $this->yell("---- Query:ScanForTasks:DynamicRule:DaysBack:$daysBack--------------");
                $rawSql = $instance->buildQueryScanForTasks();
                $this->say($rawSql);
            }
        }
    }

    /**
     * @link  https://salesfloor.atlassian.net/wiki/spaces/SB/pages/42975920134/Automated+Task+-+Retailer+Customer+Event
     * @param Application $app
     * @param $taskType
     * @throws \ReflectionException
     */
    private function scanRetailerCustomerEvent(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\Factory\RetailerCustomerEventMultiple $scanner */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];
        $rules = $scanner->getAllDynamicRuleSets();
        if (empty($rules)) {
            return;
        }

        foreach ($rules as $ruleKey => $ruleDetails) {
            $configDaysPerRule = $scanner->getMultipleDaysBackFromConfigPerRule($ruleKey);
            foreach ($configDaysPerRule as $daysBack) {
                $instance = $scanner->getSingleTaskScanner();
                $instance->setDaysBack($daysBack);
                $instance->bindRule($ruleDetails);

                $this->yell("---- Query:ScanForTasks:DynamicRule:$ruleKey:DaysBack:$daysBack--------------");
                $qb = $instance->buildQueryScanForTasks();
                $this->say(Util::getExecutableSQL($qb));
            }
        }
    }

    /**
     * @link  https://salesfloor.atlassian.net/wiki/spaces/SB/pages/2471231489/ii.+Retailer+transaction+employee+assigned+Task
     * @param Application $app
     * @param $taskType
     * @throws \ReflectionException
     */
    private function scanRetailerTransactionEmployeeAssigned(Application $app, $taskType)
    {
        // new_retailer_transaction_filtered
        /** @var \Salesfloor\Services\Tasks\Automated\RetailerTransactionEmployeeAssigned $service */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        $this->yell('----  NOTE: this debug tool DO NOT display the query of: createMissingCustomerIfNeeded --------------');
        $this->yell("----  Please check the function if config is off:'sf.task.automated.retailer_transaction_employee_assigned.matching'--------------");
        if (in_array($taskType, TaskModel::$automated_types_multiple)) {
            $configDays = $this->getMethod($scanner, "getMultipleDaysBack")->invokeArgs($scanner, []);
            foreach ($configDays as $daysBack) {
                $instance = $scanner->getSingleTaskScanner();
                $instance->setDaysBack($daysBack);

                $this->yell("---- Query:ScanForTasks:DaysBack:$daysBack--------------");

                $qb = $this->getMethod($instance, 'getQbScanForMatchingTasks')->invokeArgs($instance, []);
                $this->say(Util::getExecutableSQL($qb));
            }

            // 2 dynamic rule query output, a copy implement from processDynamicRule
            $rules = $scanner->getAllDynamicRuleSets();
            if (empty($rules)) {
                return;
            }

            foreach ($rules as $ruleKey => $ruleDetails) {
                $configDaysPerRule = $scanner->getMultipleDaysBackFromConfigPerRule($ruleKey);
                foreach ($configDaysPerRule as $daysBack) {
                    $instance = $scanner->getSingleTaskScanner();
                    $instance->setDaysBack($daysBack);
                    $instance->bindRule($ruleDetails);

                    $this->yell("---- Query:ScanForTasks:DynamicRule:$ruleKey:DaysBack:$daysBack--------------");
                    $qb = $this->getMethod($instance, 'getQbScanForMatchingTasks')->invokeArgs($instance, []);
                    $this->say(Util::getExecutableSQL($qb));
                }
            }
        } else {
            // this condition could be removed, keep here just in case we need single task in the future
            $this->yell('---- Query:ScanForTasks--------------');
            $qb = $scanner->buildQueryScanForTasks();
            $this->say(Util::getExecutableSQL($qb));
        }
    }

    /**
     * @link https://salesfloor.atlassian.net/wiki/spaces/SB/pages/2476245005/iii.+New+Rep+Transaction+Task
     * @param Application $app
     * @param $taskType
     */
    private function scanNewRepTransaction(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\NewRepTransaction $service */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        $this->yell('---- Query:ScanForTasks--------------');
        $rawSql = $scanner->buildQueryScanForTasks();
        $this->say($rawSql);
    }

    /**
     * @link https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1242431548/V.+CI+Sale+Transaction+Follow-Up+Random+Distribution+By+Stores
     * @param Application $app
     * @param $taskType
     * @throws \ReflectionException
     */
    private function scanTransactionDistributionByStores(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\TransactionsDistributionByStores $service */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        $this->yell('---- Query:getPilotStoreTaskQuery--------------');
        $qb = $this->getMethod($scanner, "getPilotStoreTaskQuery")->invokeArgs($scanner, []);
        $this->say(Util::getExecutableSQL($qb));

        $this->yell('---- Query:getOnlineStoreTaskQuery--------------');
        $qb = $this->getMethod($scanner, "getOnlineStoreTaskQuery")->invokeArgs($scanner, []);
        $this->say(Util::getExecutableSQL($qb));
    }

    /**
     * @link https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1647312897/iv.+CI+Soon+to+lapse
     * @param Application $app
     * @param $taskType
     * @throws \ReflectionException
     */
    private function scanRetailerCustomerSoonToLapseFiltered(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\Factory\RetailerCustomerSoonToLapseFilteredMultiple $scanner */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        if (in_array($taskType, TaskModel::$automated_types_multiple)) {
            $rules = $scanner->getAllDynamicRuleSets();
            if (empty($rules)) {
                $this->say("configuration key 'sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule' is required to retrieve the query");
                return;
            }

            foreach ($rules as $ruleKey => $ruleDetails) {
                $configDaysPerRule = $scanner->getMultipleDaysBackFromConfigPerRule($ruleKey);
                foreach ($configDaysPerRule as $daysBack) {
                    $instance = $scanner->getSingleTaskScanner();
                    $instance->setDaysBack($daysBack);
                    $instance->bindRule($ruleDetails);

                    $this->yell("---- Query:ScanForTasks:DynamicRule:DaysBack:$daysBack--------------");
                    $this->yell('---- Query:ScanForTasks:WhenTrxAndUserSameEmployeeId--------------');
                    $this->getMethod($instance, "setIsTrxAndUserSameEmployeeId")->invokeArgs($instance, [true]);
                    $query = $instance->buildQueryScanForTasks();
                    $this->say($query);

                    $this->yell('---- Query:ScanForTasks:WhenTrxAndUserDifferentEmployeeId--------------');
                    $this->getMethod($instance, "setIsTrxAndUserSameEmployeeId")->invokeArgs($instance, [false]);
                    $query = $instance->buildQueryScanForTasks();
                    $this->say($query);
                }
            }
        }
    }

    /**
     * @link https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1711046657/VI.+Cancel+Transaction+Follow+Up+Primary+Associate+Store
     * @param Application $app
     * @param $taskType
     */
    private function scanTransactionCancelledFollowUp(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\CancelledTransactionFollowUp $service */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        $this->yell('---- Query:ScanForTasks--------------');
        $qb = $scanner->buildQueryScanForTasks();
        $this->say(Util::getExecutableSQL($qb));
    }

    /**
     * Show the SQL query used to filter for tasks to auto-dismiss
     * @param string $retailer
     * @return void
     */
    public function taskShowAutoDismissSql(string $retailer)
    {
        $app = $this->getApplication($retailer);
        $logger = $app['logger'];

        /** @var TaskAutoDismissProcessor $autoExpireProcessor */
        $processor = $app['service.tasks.auto_dismiss.processor'];

        $logger->info('Dumping SQL queries for each rule');
        $rules = $processor->getConfiguredSettings();
        foreach ($rules as $settings) {
            $logger->info('---------------------');
            $qb = $processor->generateSQLForTaskAutoDismiss($settings);
            $logger->info(Util::getExecutableSQL($qb));
        }
    }

    /**
     * Process the auto-dismiss of non-corporate tasks
     * @command task:process-auto-dismiss chicos-dev
     *
     * @param string $retailer
     * @return void
     */
    public function taskProcessAutoDismiss(string $retailer)
    {
        $app = $this->getApplication($retailer);
        $logger = $app['logger'];

        /** @var TaskAutoDismissProcessor $autoExpireProcessor */
        $processor = $app['service.tasks.auto_dismiss.processor'];

        if ($processor->isFeatureEnabled()) {
            $logger->info('Task Auto-Dismiss Processing Start');
            $processor->process();
            $logger->info('Task Auto-Dismiss Processing Complete');
        } else {
            $logger->info('Task Auto-Dismiss is not enabled');
        }
    }

    /**
     * @link https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1711046657/VI.+Cancel+Transaction+Follow+Up+Primary+Associate+Store
     * @param Application $app
     * @param $taskType
     */
    private function scanRetailerCustomerStatsRegistryFollowup(Application $app, $taskType)
    {
        /** @var \Salesfloor\Services\Tasks\Automated\CancelledTransactionFollowUp $service */
        $scanner = $app[BaseScanner::SERVICE_BASE . $taskType];

        $this->yell('---- Query:ScanForTasks--------------');
        $qb = $scanner->buildQueryScanForTasks();
        $this->say(Util::getExecutableSQL($qb));
    }
}
