<?php

namespace Salesfloor\Commands\Cron;

use Silex\Application;
use Salesfloor\API\Managers\Import;
use Salesfloor\Models\Customer;
use Salesfloor\Models\RetailerTransactionAttribution as RetailerTransactionAttributionModel;
use Salesfloor\Services\CustomerInsights\Matching\MatchCustomersRetailerCustomerInterface;
use Salesfloor\Services\Exporter\RetailerTransactionAttribution\RetailerTransactionAttributionExporter;
use Salesfloor\Services\Importer\Components\Fetcher\S3\BaseS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\S3FetcherFactory;
use Salesfloor\Services\Importer\Exceptions\EmptyImportException;
use Salesfloor\Services\RetailerCustomerService;
use Salesfloor\Services\Salesfloor\Customer as CustomerService;
use Salesfloor\Services\DataProcessing\RetailerTransactionAttribution as RetailerTransactionAttributionService;

/**
 * Commands for running data imports.
 */
class CustomerInsightsCrons extends Base
{
    /**
     * Run matching for CI customers and contacts
     *
     * @singleton
     * @systemwide
     * @command ci:match
     */
    public function matchCustomers()
    {
        $job = function (Application $app) {
            /** @var MatchCustomersRetailerCustomerInterface $matcher */
            $matcher = $app['match-customer-retailercustomers'];
            $matcher->match();
        };

        $this->run($job);
    }

    /**
     * Run the contact importer for contact files generated from CI customer imports.
     *
     * This will pull from the inbound/{env}/c2c directory in S3, which is only populated
     * by CustomersToContacts.
     *
     * @singleton
     * @systemwide
     * @command ci:import:contacts
     * @param string $origin The origin to specify for imported contacts. Defaults to customers2contacts.
     * If using the S3 download, it can't be changed.
     * @param array $opts
     * @throws \Exception
     */
    public function importContacts($origin = Customer::ORIGIN_RETAILER_CUSTOMER_TO_CONTACT, $opts = ['filename' => null, 'no-delete' => false])
    {
        $filename = $opts['filename'];
        $noDelete = $opts['no-delete'];

        $ref = new \ReflectionClass(Customer::class);
        $constants = array_filter($ref->getConstants(), function ($constant) {
            if (strpos($constant, 'ORIGIN_') === 0) {
                return true;
            }
            return false;
        }, ARRAY_FILTER_USE_KEY);

        if (!in_array($origin, $constants)) {
            throw new \Exception("Origin [$origin] is not a valid enum constant from Customer model.");
        }

        $job = function (Application $app) use ($filename, $noDelete, $origin) {
            $getImporter = function () use ($app, $origin, $noDelete) {
                /** @var \Salesfloor\Services\Importer\Customer\CustomerImporter $importer */
                $importer = $app['customer.importer']();
                $importer->setImportOrigin($origin);
                $importer->setDeleteFileAfter(!$noDelete);

                return $importer;
            };

            if ($filename === null) {
                // Download c2c files from S3 until we've consumed everything available.
                // We use the default behavior of importing the oldest file first (by S3 LastModified date)
                // We'll keep doing this until we don't see any more files in the c2c path.

                // Only do this for C2C. We didn't have time to add an automatic s3 import for alternate origins.
                if ($origin !== Customer::ORIGIN_RETAILER_CUSTOMER_TO_CONTACT) {
                    throw new \Exception('S3 contact import is only for C2C origin');
                }

                try {
                    /** @var S3FetcherFactory $fetcherFactory */
                    $fetcherFactory = $app['importer.fetcher.s3'];
                    while (true) {
                        // This old importer doesn't download from S3, and I didn't have the time to refactor it
                        // into the new GenericImporter structure. Instead, we'll use the base fetcher to grab the
                        // files, and pass them into the importer after we download.
                        /** @var BaseS3Fetcher $fetcher */
                        $fetcher = $fetcherFactory->create(
                            'c2c',
                            null,
                            !$noDelete
                        );

                        // The order is important, since the fetcher is not part of the "process" core in this version
                        // we won't handle the delete (when there's no s3 file) properly.
                        $fetcher->prepare();

                        $importer = $getImporter();

                        // We don't need to force (that would trigger a backup) since we are using s3 fetcher
                        $importer->setFilename($fetcher->getStreamInfo());
                        $importer->process();

                        $fetcher->cleanup();
                        $fetcher->completed();
                    }
                } catch (EmptyImportException $e) {
                    $this->say('No more c2c files available in s3.');
                }
            } else {
                // File was specified, so only import that one.
                $importer = $getImporter();
                $importer->setFilename($filename);
                $importer->process();
            }

            // postHookAfterImporter
            if ($app['configs']['retailer.contacts.delete_unassociated_ci_imported.is_enabled']) {
                /** @var \Salesfloor\Services\Salesfloor\Customer $customerService */
                $customerService = $app['service.customer'];
                $customerService->deleteUnassociatedCiImportedCustomer();
            }
        };

        $this->run($job);
    }

    /**
     * Delete customer who is un-associate and origin from `customers2contacts`
     * @command ci:delete-unassociated-imported-contacts
     * @singleton
     * @throws \Exception
     */
    public function deleteUnassociatedCiImportedCustomer()
    {
        $job = function (Application $app) {
            if ($app['configs']['retailer.contacts.delete_unassociated_ci_imported.is_enabled'] !== true) {
                $app['logger']->error(
                    "cron should run for retailer with specific need with config of 'retailer.contacts.delete_unassociated_ci_imported.is_enabled'"
                );
                return;
            }

            /** @var \Salesfloor\Services\Salesfloor\Customer $customerService */
            $customerService = $app['service.customer'];
            $customerService->deleteUnassociatedCiImportedCustomer();
        };

        $this->run($job);
    }

    /**
     * Run the CI Retailer Customer Importer
     *
     * @singleton
     * @systemwide
     *
     * @command ci:import:customers
     *
     * @param array $opts
     * @option $filename The local file to import.
     * @option $no-delete If set, will not delete the file from the local machine after importing.
     * @throws \Exception
     */
    public function importCustomers($opts = ['filename' => null, 'no-delete' => false])
    {
        $this->doImporterJob('importer.retailer_customers', $opts);
    }


    /**
     * Delete customers who were not changed(created_at && updated_at) after the last successful import
     * @command ci:delete-retailer-customer-not-changed
     *          SF_ALLOW_DELETE_COUNT=2000 SF_ALLOW_DELETE_RATIO=0.5 ./robo ci:delete-retailer-customer-not-changed
     * @singleton
     * @throws \Exception
     */
    public function deleteRetailerCustomerNotChanged()
    {
        $allowDeleteCount = getenv("SF_ALLOW_DELETE_COUNT") ?: null;
        $allowDeleteRatio = getenv("SF_ALLOW_DELETE_RATIO") ?: null;


        $job = function (Application $app) use ($allowDeleteCount, $allowDeleteRatio) {
            if ($app['configs']['retailer.retailer_customers.delete_not_changed_recently.is_enabled'] !== true) {
                $app['logger']->error(
                    "cron should run for retailer with specific need with config of 'retailer.retailer_customers.delete_not_changed_recently.is_enabled'"
                );
                return;
            }

            $allowDeleteCount = $allowDeleteCount ?? $app['configs']['retailer.retailer_customers.delete_not_changed_recently.safety_guard']['allow_delete_count'];
            $allowDeleteRatio = $allowDeleteRatio ?? $app['configs']['retailer.retailer_customers.delete_not_changed_recently.safety_guard']['allow_delete_ratio'];

            /** @var Import $importManager */
            $importManager = $app['import.manager'];

            $lastImport = $importManager->getAll(
                ['type' => \Salesfloor\API\Managers\Import::TYPE_CUSTOMER, 'status' => Import::STATUS_COMPLETED],
                0,
                1,
                false,
                ['-id']
            );

            if (empty($lastImport[0]->started_at)) {
                $app['logger']->error(
                    "no importer is found with type of 'customer' and status of 'completed', exit script without deletion"
                );
                return;
            }

            $lastImportStartDatetime = $lastImport[0]->started_at;

            /** @var RetailerCustomerService $retailerCustomerService */
            $retailerCustomerService = $app['retailer.customer.service'];
            $retailerCustomerService->deleteRetailerCustomerNotChanged($lastImportStartDatetime, $allowDeleteCount, $allowDeleteRatio);
        };

        $this->run($job);
    }

    /**
     * Delete contacts who were don't have retailer customer associated with them
     * @command ci:delete-contact-with-orphan-retailer-customer
     *          SF_ALLOW_DELETE_COUNT=2000 SF_ALLOW_DELETE_RATIO=0.5 ./robo ci:delete-contact-with-orphan-retailer-customer retailer-{env}
     * @singleton
     * @throws \Exception
     */
    public function deleteContactWithOrphanRetailerCustomer()
    {
        $allowDeleteCount = getenv("SF_ALLOW_DELETE_COUNT") ?: null;
        $allowDeleteRatio = getenv("SF_ALLOW_DELETE_RATIO") ?: null;

        $job = function (Application $app) use ($allowDeleteCount, $allowDeleteRatio) {
            if ($app['configs']['retailer.retailer_customers.delete_not_changed_recently.is_enabled'] !== true) {
                $app['logger']->error(
                    "cron should run for retailer with specific need with config of 'retailer.retailer_customers.delete_not_changed_recently.is_enabled'"
                );
                return;
            }

            $allowDeleteCount = $allowDeleteCount ?? $app['configs']['retailer.retailer_customers.delete_contact_with_orphan_retailer_customer.safety_guard']['allow_delete_count'];
            $allowDeleteRatio = $allowDeleteRatio ?? $app['configs']['retailer.retailer_customers.delete_contact_with_orphan_retailer_customer.safety_guard']['allow_delete_ratio'];

            /** @var CustomerService $customerService */
            $customerService = $app['service.customer'];
            $customerService->deleteWithOrphanRetailerCustomer($allowDeleteCount, $allowDeleteRatio);
        };

        $this->run($job);
    }

    /**
     * Run the CI Transaction Importer
     *
     * @singleton
     * @systemwide
     *
     * @command ci:import:transactions
     *
     * @param array $opts
     * @option $filename The local file to import.
     * @option $no-delete If set, will not delete the file from the local machine after importing.
     * @throws \Exception
     */
    public function importTransactions($opts = ['filename' => null, 'no-delete' => false])
    {
        $opts['need-config-enabled'] = 'retailer.clienteling.enabled.transactions';
        $this->doImporterJob('importer.retailer_customers_transaction', $opts);
    }

    /**
     * Run the CI Stats Importer
     *
     * @singleton
     * @systemwide
     *
     * @command ci:import:stats
     *
     * @param array $opts
     * @option $filename The local file to import.
     * @option $no-delete If set, will not delete the file from the local machine after importing.
     * @throws \Exception
     */
    public function importStats($opts = ['filename' => null, 'no-delete' => false])
    {
        $opts['need-config-enabled'] = 'retailer.clienteling.enabled.customer_stats';
        $this->doImporterJob('importer.retailer_customers_stats_insights', $opts);
    }

    private function doImporterJob($importerIdentifier, $opts)
    {
        $filename = $opts['filename'];
        $noDelete = $opts['no-delete'];
        $needConfigEnabled = !empty($opts['need-config-enabled']) ? $opts['need-config-enabled'] : false;

        $job = function (Application $app) use ($importerIdentifier, $filename, $noDelete, $needConfigEnabled) {
            // Don't run a CI import if clienteling is off or the specific config is off.
            if (!$app['configs']['retailer.clienteling.mode'] || ($needConfigEnabled && !$app['configs'][$needConfigEnabled])) {
                return;
            }

            $importer = $app[$importerIdentifier];

            if (!empty($filename) && file_exists($filename)) {
                $importer->setFilename($filename, true);
            }

            if ($noDelete) {
                $importer->setDeleteFileAfter(false);
            }

            $importer->process();
        };

        $this->run($job);
    }

    /**
     * Calculate the retailer transaction attribution,
     * @singleton
     * @command ci:transaction-attribution:calculate
     * @param array $opts
     * @option start_date           The start date of the retailer transaction(by retailer transaction created timestamp,include this date)
     * @option end_date             The end date of the retailer transaction(by retailer transaction created timestamp,include this date)
     * @option attribute-types      The attribute types to calculate, separated by comma
     * @throws \Exception
     * example(dev env should run inside docker container):
     *   ./robo ci:transaction-attribution:calculate --start_date='20240601' --end_date='20240605' chicos-dev
     *   ./robo ci:transaction-attribution:calculate -s'20200101' -e'20350102' chicos-dev
     *   ./robo ci:transaction-attribution:calculate -s'20200101' -e'20350102' -t'email-click,email-open' chicos-dev
     */
    public function calculate(
        $opts = ['start_date|s' => null, 'end_date|e' => null, 'attribute-types|t' => null]
    ) {
        $startDate        = $opts['start_date'];
        $endDate          = $opts['end_date'];
        $attributionTypes = $opts['attribute-types'];

        if (empty($attributionTypes)) {
            $attributionTypes = RetailerTransactionAttributionModel::ATTRIBUTION_TYPES;
        } else {
            $attributionTypes = explode(',', $attributionTypes);
        }

        if (empty($startDate) || empty($endDate)) {
            // keep simple, default we calculate the attribution of yesterday,'-1' could switch to two configs if needed
            $startTime = gmdate('Y-m-d  00:00:00', strtotime('-1 day'));
            $endTime   = gmdate('Y-m-d  23:59:59', strtotime('-1 day'));
        } else {
            // Make sure we have a valid date format
            $startTime = gmdate("Y-m-d 00:00:00", strtotime($startDate));
            $endTime   = gmdate("Y-m-d 23:59:59", strtotime($endDate));
        }

        if (abs(strtotime($endTime) - strtotime($startTime)) > 31 * 24 * 3600) {
            $this->say('The input date range should be less than one month to avoid performance issue');
            return;
        }

        $job = function (Application $app) use ($startTime, $endTime, $attributionTypes) {
            if (!$app['configs']['retailer.clienteling.enabled.transactions']) {
                $this->say('For attribution calculation, retailer must have config retailer.clienteling.enabled.transactions to toggle the data import ');
                return;
            }

            if (!$app['configs']['retailer.clienteling.transactions.attribution.is_enabled']) {
                $this->say(
                    'For attribution calculation, retailer must have config retailer.clienteling.transactions.attribution.is_enabled to enable the calculation'
                );
                return;
            }

            /** @var RetailerTransactionAttributionService $retailerTransactionAttributionService */
            $retailerTransactionAttributionService = $app['service.data_processing.retailer_transaction_attribution'];

            $this->say(sprintf('Start calculate retailer transaction attribution which transaction imported from [%s] to [%s]....', $startTime, $endTime));

            // in ideal daily case, retailer should have only one date's transaction
            $transactionDates = $retailerTransactionAttributionService->calculate($startTime, $endTime, $attributionTypes);

            if (empty($transactionDates)) {
                $this->say('No validate dates(or too many dates) found to calculate retailer transaction attribution, script exit without calculation');
                return;
            }

            $this->say('Finished calculate retailer transaction attribution');
        };

        $this->run($job);
    }

    /**
     * Export retailer transaction attributions
     * Only exporting the full transaction attributions for now, could add $opts date range in the future
     *
     * @command ci:transaction-attribution:export chicos-dev
     * @singleton
     * @throws \Exception
     */
    public function exportTransactionAttribution()
    {
        $job = function (Application $app) {
            if (!$app['configs']['retailer.clienteling.transactions.attribution.is_enabled']) {
                $this->say(
                    'For attribution calculation exporter, retailer must have config retailer.clienteling.transactions.attribution.is_enabled to do the calculation'
                );
                return;
            }

            $allTransactionTypes = [
                [\Salesfloor\Models\RetailerTransaction::TYPE_SALE]
            ];

            $additionalTypes = $app['configs']['retailer.clienteling.transactions.attribution.addition_transaction_type'] ?? [];

            foreach ($additionalTypes as $types) {
                $allTransactionTypes[] = $types;
            }

            foreach ($allTransactionTypes as $transactionTypes) {
                /** @var RetailerTransactionAttributionExporter $exporter */
                $exporter = $app['exporter.retailer_transaction_attribution'];

                $exporter->setTransactionTypes($transactionTypes);

                $exporter->setEncryptionEnabled(false);
                $exporter->setBucket($exporter->getPrivateBucket());
                $app['logger']->info('Start upload all retailer_transaction_attribution file to backoffice access folder');
                $exporter->process();
                $app['logger']->info('Finished upload all retailer_transaction_attribution file to backoffice access folder');
            }

            // we use multiple loop to let the same type of exporters finished first
            foreach ($allTransactionTypes as $transactionTypes) {
                /** @var RetailerTransactionAttributionExporter $exporter */
                $exporter = $app['exporter.retailer_transaction_attribution'];

                $exporter->setTransactionTypes($transactionTypes);

                if ($app['configs']['exporter.transactions.attribution.daily.enabled']) {
                    $exporter->setBucket($exporter->getDefaultBucket());
                    $exporter->setEncryptionEnabled($app['configs']['exporter.transactions.attribution.outbound.encryption.enabled']);

                    $startDate = date('Y-m-d', strtotime($app['configs']['exporter.transactions.attribution.daily.start_days_before'] . ' days'));
                    $endDate   = date('Y-m-d', strtotime($app['configs']['exporter.transactions.attribution.daily.end_days_before'] . ' days'));

                    $app['logger']->info('Start upload incremental retailer_transaction_attribution file to ftp access folder');
                    $exporter->setExport(\Salesfloor\Services\Exporter\Base::EXPORT_TYPE_CSV);
                    $exporter->processByDateRange($startDate, $endDate);
                    $app['logger']->info('Finished upload incremental retailer_transaction_attribution file to ftp access folder');
                }

                if ($app['configs']['exporter.transactions.attribution.outbound.enabled']) {
                    $exporter->setBucket($exporter->getDefaultBucket());
                    $exporter->setEncryptionEnabled($app['configs']['exporter.transactions.attribution.outbound.encryption.enabled']);
                    $exporter->setExport(\Salesfloor\Services\Exporter\Base::EXPORT_TYPE_ZIP);
                    $exporter->setDayStart(null);
                    $exporter->setDayEnd(null);
                    // {bucket}/outbound/ folder usually only have range file, but here is an edge requirement for all data
                    $outboundPath = $exporter->getExportPathByRange();
                    $exporter->setFullExportPath($outboundPath);

                    $app['logger']->info('Start upload all retailer_transaction_attribution file to ftp access folder');
                    $exporter->process();
                    $app['logger']->info('Finished upload all retailer_transaction_attribution file to ftp access folder');
                }
            }
        };

        $this->run($job);
    }

    protected function getProcessName()
    {
        return 'CustomerInsights';
    }
}
