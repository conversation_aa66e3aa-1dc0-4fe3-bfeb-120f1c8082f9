<?php

namespace Salesfloor\Commands;

/**
 * Commands to manage Elasticsearch
 */
class ElasticsearchCommands extends Base
{
    private $availableReindex = [
        'customer' => 'reindex_customer',
        'contact'  => 'reindex_contact',
    ];

    /**
     * Reindex data to Elasticsearch
     *
     * @command es:reindex
     *
     * @param $retailer
     * @param $type
     */
    public function esReindex($retailer, $type = null)
    {
        // Reindex everything
        if (empty($type)) {
            foreach ($this->availableReindex as $index => $type) {
                $this->runBin($type, $retailer);
            }
        } else {
            if (isset($this->availableReindex[$type])) {
                $this->runBin($this->availableReindex[$type], $retailer);
            } else {
                $this->say(
                    sprintf(
                        "This is not an available reindex type [%s]. Possible values [%s]",
                        $type,
                        implode(',', array_keys($this->availableReindex))
                    )
                );
            }
        }
    }

    /**
     * We don't need to specify any retailer name when we do all.
     *
     * This is ONLY for dev
     */
    public function esReindexAll()
    {
        foreach ($this->getRetailers() as $retailer) {
            $this->esReindex("$retailer-dev");
        }
    }

    /**
     * Delete a specific index + reindex.
     * This will only work for DEV stack
     *
     * @param string      $retailer
     * @param string|null $type
     *
     * @throws \Salesfloor\Services\ElasticSearch\Exceptions\ElasticSearchException
     */
    public function esDelete($retailer, $type = null)
    {
        $app = $this->getApplication($retailer);

        if ($app['configs']['env'] !== 'dev') {
            $this->say("This task is only for use in dev environments.");
            exit(5);
        }

        /** @var \Salesfloor\Services\ElasticSearch\ElasticSearch $es */
        $es = $app['elasticsearch'];

        // We have an index "per className"
        $availableType = [
            'contact'  => 'sf_customer',
            'customer' => 'sf_retailer_customers',
        ];

        if (isset($availableType[$type])) {
            $alias = $availableType[$type];
            $indexes = $es->getIndexesFromAlias($alias);
            foreach ($indexes as $index) {
                $this->say("Deleting index $index");
                $es->deleteIndex($index);
            }
            $this->esReindex($retailer, $type);
        } else {
            $this->say(
                sprintf(
                    "This is not an available index name[%s]. Possible values [%s]",
                    $type,
                    implode(', ', array_keys($availableType))
                )
            );
        }
    }

    /**
     * Create index if missing (during deployment mostly)
     *
     * @param $retailer
     * @return void
     * @throws \Salesfloor\Services\ElasticSearch\Exceptions\ElasticSearchException
     */
    public function esSetup($retailer)
    {
        $app = $this->getApplication($retailer);

        /** @var \Salesfloor\Services\ElasticSearch\ElasticSearch $es */
        $es = $app['elasticsearch'];

        // We have an index "per className"
        $availableType = [
            'contact'  => 'sf_customer',
            'customer' => 'sf_retailer_customers',
        ];

        foreach ($availableType as $name => $table) {
            $indexExist = $es->indexExist($table);

            if (!$indexExist) {
                $this->say(
                    sprintf(
                        "This index for table [%s] is missing. We will try to index it",
                        $table,
                    )
                );

                $this->esReindex($retailer, $name);
            } else {
                $this->say(
                    sprintf(
                        "This index for table [%s] exist. We will run sanity check on it",
                        $table,
                    )
                );

                $setupProperly = $es->isSetupProperly($table);

                if (!$setupProperly) {
                    $app['logger']->critical(
                        sprintf(
                            "This index setup for table [%s] is not setup properly. (i.e: duplicate aliases, missing alias)",
                            $table,
                        )
                    );
                }
            }
        }
    }

    /**
     * Remove all indexes.
     * This will only work for tests-dev
     *
     * @throws \Salesfloor\Services\ElasticSearch\Exceptions\ElasticSearchException
     */
    public function esCleanupTestIndices()
    {
        $app = $this->getApplication('tests-dev');

        /** @var \Salesfloor\Services\ElasticSearch\ElasticSearch $es */
        $es = $app['elasticsearch'];

        $indices = $es->getIndices();
        foreach ($indices as $index => $aliases) {
            if (preg_match('/^sf-dev-\w+-tests-dev\.(sf_customer|sf_retailer_customers)/', $index)) {
                foreach ($aliases['aliases'] as $alias => $empty) {
                    $es->deleteAnyAlias($index, $alias);
                }
                $es->deleteAnyIndex($index);
            }
        }
    }
}
