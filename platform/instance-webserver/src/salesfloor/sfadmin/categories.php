<?php

require_once('../wp-load.php');
require_once('../wp-admin/includes/template.php');

use Salesfloor\API\Managers\Categories;
use Salesfloor\Models\RepOnboarding as RepOnboardingModel;

$user = wp_get_current_user();

header("Content-type: application/json");
nocache_headers();


// Initialize for Anonymous access.
$repSelection = array();

if (is_user_logged_in() )
{
    $rep_category = $wpdb->get_results("SELECT term_id FROM sf_rep_product_selection_map where user_id = " . $user->ID . " ;");

    $repSelection = array();
    $category_selection = "";

// Build the list of which dept are in the Rep selection.
    foreach($rep_category as $index => $obj)
    {
        // This will simplify the look-up
        $repSelection[$obj->term_id] = true;
        if ($category_selection == "")
        {
            $category_selection = $obj->term_id;
        }
        else
        {
            $category_selection .= "," . $obj->term_id;
        }
    }

}

// DIDIER: We assume that we have two level of category, one for the Dept and a set of Categories
// 		   for the dept. If we go more level we should refactor that as multiple call to handle
// 		   this. There is not a high value to do this at this stage.

// We did get a post, let's update the database with them
if ( $_SERVER['REQUEST_METHOD'] == 'POST' )  // Anonymous can only get the categories, they cannot post
{
	// compare the current db list with what was passed to us.

	$posted_cats = array();
	foreach ($_POST as $param => $value) {
		if ( 'on' === $value )
			$posted_cats[$param] = true;
	}

	$new_categories = array_diff_key($posted_cats, $repSelection);
	$del_categories = array_diff_key($repSelection, $posted_cats);

	if ( !empty($new_categories) || !empty($del_categories) ) {
		// check to see if they have already set categories, if they have raise a category change event
		include_once(get_stylesheet_directory().'/defines.php');
		// ;
		$result = $wpdb->get_var(
			$wpdb->prepare(
				'select steps_completed from sf_rep_onboarding where wp_user_id = %d',
				$user->ID
			)
		);
        if (!isset($result) || !$result || !($result & RepOnboardingModel::STEP_CATEGORIES)) {
            sf_record_onboarding_step(RepOnboardingModel::STEP_CATEGORIES);
        } else {
			// we might conceivably want to record the category changes here. but probably not, so we won't
			sf_add_tracker(SF_EVENT_CHANGE_CATEGORIES, null, null, $user->ID, null, null, null);
		}

		// insert new categories
		if ( !empty($new_categories) ) {
			$query = "INSERT INTO sf_rep_product_selection_map (term_taxonomy_id, user_id, term_id, category_id) VALUES ";
			$params = $values = array();
			foreach ($new_categories as $key => $unused) {
				$params[] = "(%s,%d,%s,%s)";
				array_push($values, $key, $user->ID, $key, $key);
			}
			$query .= implode(",", $params);
			$result = $wpdb->query($wpdb->prepare($query, $values));
		}

		if ( !empty($del_categories) ) {
			$query = "DELETE FROM sf_rep_product_selection_map WHERE user_id = $user->ID AND term_id IN ( ";
			$params = $values = array();
			foreach ($del_categories as $key => $unused) {
				$params[] = "%s";
				$values[] = $key;
			}
			$query .= implode(",", $params) . ")";
			$result = $wpdb->query($wpdb->prepare($query, $values));
		}
	}

  global $app;
  $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);
  $sfClient->insert("clear-specialties-cache", []);
  $sfClient->delete("reps/". $user->ID ."/products/refresh-autoselected", []);
  clear_storefront_cache();

	// post is put-only, return is ignored so leave now.
	echo '{ "done":"true "}';
	exit;
}

$limit_filter="";
if (isset($_REQUEST["onlyselection"]))
{
    $limit_filter=" and term_id in ($category_selection) ";
}

    $mysqlFlagUpdated = disableOnlyFullGroupBy($wpdb);
    $top_category_view = $wpdb->get_results($wpdb->prepare("SELECT term_id id, name, description as url, retailer_id as retailerId FROM wp_terms JOIN wp_term_taxonomy using (term_id) WHERE parent = %s $limit_filter GROUP BY id, name ORDER BY name", $app['configs']['products.root_category_id']));
    enableOnlyFullGroupBy($wpdb, $mysqlFlagUpdated);

$category_view = array();
if (!isset($_REQUEST["nosub"]))
{
    $category_view = $wpdb->get_results($wpdb->prepare("select wp_terms.term_id as id, parent_term as parent_id, wp_terms.name as name, retailer_id as retailerId, wp_term_taxonomy.description as url from (select term_id as parent_term, name as parent_name, parent, description as parent_description from wp_terms join wp_term_taxonomy using (term_id) where parent = %s) as new, wp_term_taxonomy, wp_terms where wp_term_taxonomy.parent=new.parent_term and wp_terms.term_id = wp_term_taxonomy.term_id order by parent_name, name;", $app['configs']['products.root_category_id']));
}


   // Merge the two category list as a hierarchy
   foreach($top_category_view as $index => &$parent_category)
   {
       // Create the placeholder for the sub categories
       $parent_category->subcategories = array();

       if (isset($repSelection[$parent_category->id]))
           $parent_category->selected = true;
       else
           $parent_category->selected = false;

       $parent_category->url=urlencode($parent_category->url);

       // Grab all subcategories until we reach the next one.
       while ($category_view[0]->parent_id === $parent_category->id)
       {
           $category_view[0]->url=urlencode($category_view[0]->url);
           // Remove from the list and add to the parent
           array_push($parent_category->subcategories, array_shift($category_view));
       }
   }

    /** @var $categoriesManager Categories */
    $categoriesManager = $app['categories.manager'];
    $top_category_view = $categoriesManager->filterSpecialtiesFromCategories($top_category_view, 'id');

   $return_result = array(
     "categories" => $top_category_view
   );

//    echo print_r($top_category_view, true);
//    echo wp_send_json_success($top_category_view);
    echo json_encode($return_result);
exit;
