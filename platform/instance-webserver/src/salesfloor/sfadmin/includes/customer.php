<?php

use Salesfloor\API\Managers\Client\Customers\Legacy;
use Salesfloor\API\Managers\CustomerFieldHistory;
use Salesfloor\Services\CustomerInsights\Matching\MatchCustomersRetailerCustomerInterface;
use Salesfloor\Services\Exceptions\PhoneNumberNotNormalizableException;
use Salesfloor\Services\NameSuggester;
use Salesfloor\Services\Multilang;
use Salesfloor\Models\Customer;

/**
 * Insert the customer + add event (tracker)
 * This customer will always be of type corporate
 *
 * MIGRATED
 * @see: \Salesfloor\API\Managers\Client\Customers\Legacy
 * NOTES: Verify if the events are tracked properly
 *
 * @param $input
 * @param $user
 * @param Boolean $forceSubscription Force an email subscription
 *
 * @return mixed
 */
function insertCustomer($input, $user, $forceSubscription = false)
{
    global $wpdb, $app;

    /** @var \Salesfloor\Services\Multilang $multilangService */
    $multilangService = $app['service.multilang'];

    $defaultRetailerLocale = $multilangService->getRetailerDefaultLocale();

    /** @var Salesfloor\Services\NameSuggester $nameSuggester */
    $nameSuggester = $app['name_suggester'];

    /** @var Legacy $customersManager */
    $customersManager = $app['customers.manager'];

    // remove space
    $input = array_map(function($item) {
        return trim($item);
    }, $input);

    $locale = isset($input['locale']) ? $input['locale'] : null;

    $emailSubFlag = (isset($input['autoSubscribe']) && $input['autoSubscribe'] == 'on') || $forceSubscription === true
        ? Customer::EMAIL_SUBSCRIPTION_STATUS_SUBSCRIBED
        : Customer::EMAIL_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;

    $smsSubFlag = null;
    if ((isset($input['autoSmsSubscribe']) && $input['autoSmsSubscribe'] == 'on')) {
        $smsSubFlag = Customer::SMS_SUBSCRIPTION_STATUS_SUBSCRIBED;
    }



    if (!empty($input['name'])) {
        $nameLocale = $locale ?? $defaultRetailerLocale;
        $names = $nameSuggester->parseName($input['name'], $nameLocale);

        if (empty($input['firstName'])) {
            $input['firstName'] = $names['given'];
        }

        if (empty($input['lastName'])) {
            $input['lastName'] = $names['family'];
        }
    }

    if (empty($input['origin'])) {
        $input['origin'] = \Salesfloor\Models\Customer::ORIGIN_UNKNOWN;
    }

    $phone = null;
    if (!empty($input['phone'])) {
        // TODO : centralize service form customer's phone normalized logic, ex: normalizePhone() or some other better place
        // $user->ID could still possible be '0' if no rep accept it yet
        if (empty($input['country']) && !empty($user->ID)) {
            $phone = getNormalizedPhone($input['phone'], $user->ID);
        } else {
            if ($app['normalization.phonenumbers']->validateAvailableCountry($input['country'])) {
                $phone = $app['normalization.phonenumbers']->normalize($input['phone'], $input['country']);
            }
        }
    }

    $email = empty($input['email']) ? null : $input['email'];

    // if email is empty, a contact shouldn't be subscriber
    if (empty($email)) {
        $emailSubFlag = Customer::EMAIL_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;
    }

    if (empty($phone) && empty($email)) {
        return null;
    }


    // SF-19311 - Tried to replace this with the manager, but there's a circular dependency between the customer
    // manager and the phone normalizer, so load_configs falls on its face. Until we fix our dependency hell,
    // we have to use the direct query here. SalesfloorAPIRepository also dies for incomprehensible reasons, probably
    // related to dependency mismanagement.

    // email will never be NULL but empty, so unique key will always limit to 1 client with phone number
    // https://wordpress.stackexchange.com/questions/143405/wpdb-wont-insert-null-into-table-column

    // Using $app is working, but i had some issue with a manager that doesn't exist in one injectDeps (RetailerCustomerRemapping)

    $data = [
        "user_id"          => $user->ID,  // Rep associated to this user
        "email"            => $email ?: null,
        "phone"            => $phone ?: null,
        "name"             => $input['name'] ?: null,
        "first_name"       => $input['firstName'] ?: null,
        "last_name"        => $input['lastName'] ?: null,
        "subcribtion_flag" => $emailSubFlag,
        "type"             => "corporate",
        "created"          => gmdate('Y-m-d H:i:s'),
        "origin"           => $input['origin'] ?: \Salesfloor\Models\Customer::ORIGIN_UNKNOWN,
        "locale"           => $locale,
        "contact_preference" => getContactPreferenceFromRequestContactPreference($input['contact_preference'] ?? ''),
        "geo" => '', // Can't be null,
        "localization" => 'en', // Can't be null
        "comment" => '', // Can't be null
    ];

    if (!is_null($smsSubFlag)) {
        $data['sms_marketing_subscription_flag'] = $smsSubFlag;
    }

    $customerModel = $customersManager->create($data);

    // This will check (already done before this function anyway) and also add alternate phone/email + add to ES
    $customersManager->checkAndSave($customerModel, !empty($input['country']) ? $input['country'] : null);

    $newCustomerId = $customerModel->getId();

    // Read it back to get the ID
    $customerDB = $wpdb->get_results($wpdb->prepare("select * from sf_customer where id=%s", $newCustomerId));

    //WESHOULD replace it with \Salesfloor\API\Managers\Client\Customers\V1::trackCustomerAddedEvent()
    sf_add_tracker(SF_EVENT_USER_ADD, "contact_management", null, $user->ID, null, 1, null);

    return $customerDB;
}

/**
 * A simple wrapper for service form customer's phone normalized logic in WordPress
 * @param string $phone
 * @param string $country
 * @param int $userId
 * @return string|null
 * @throws PhoneNumberNotNormalizableException
 */
function normalizePhone($phone, $country = '', $userId = null)
{
    global $app;

    if (empty($country) && !empty($userId)) {
        $normalizedPhone = getNormalizedPhone($phone, $userId);
    } else {
        if ($app['normalization.phonenumbers']->validateAvailableCountry($country)) {
            $normalizedPhone = $app['normalization.phonenumbers']->normalize($phone, $country);
        } else {
            throw new PhoneNumberNotNormalizableException("Missing country: $country for phone number: $phone normalization");
        }
    }

    return $normalizedPhone;
}

/**
 * Insert the customer + add event (tracker)
 * This customer will always be of type corporate
 *
 * @param $input
 * @param $user
 *
 * @return mixed
 */
function insertPreCustomer($input, $user)
{
    global $wpdb, $app;

    // remove space
    $input = array_map(function($item) {
        return trim($item);
    }, $input);

    $phone = null;
    if (!empty($input['phone'])) {
        // TODO : centralize service form customer's phone normalized logic, ex: normalizePhone() or some other better place
        if (empty($input['country']) && !empty($user->ID)) {
            $phone = getNormalizedPhone($input['phone'], $user->ID);
        } else {
            if ($app['normalization.phonenumbers']->validateAvailableCountry($input['country'])) {
                $phone = $app['normalization.phonenumbers']->normalize($input['phone'], $input['country']);
            }
        }
    }

    $email = empty($input['email']) ? null : $input['email'];

    $smsSubFlag = (isset($input['autoSmsSubscribe']) && $input['autoSmsSubscribe'] == 'on')
        ? Customer::SMS_SUBSCRIPTION_STATUS_SUBSCRIBED
        : Customer::SMS_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;

    if (empty($phone) && empty($email)) {
        return null;
    }

    if (!isset($input['locale'])) {
        $input['locale'] = null;
    }

    // Since now we send email/phone during pre_customer
    // even if the hidden input is invalid, since we don't want to block the submit in this case

    // The phone validation is done in the request itself.
    // If it's a sms request, throw exception
    // If it's a email request, just remove it

    // There's no backend validation for email. However, in the case of pre_customer, we should at least
    // Not store bad email in it.

    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $email = null;
    }

    $preCustomerModel = $app['pre_customers.manager']->create([
        'user_id'           => $user->ID,  // Rep associated to this user
        'email'             => $email ?: null,
        'phone'             => $phone ?: null,
        'name'              => $input['name'] ?: null,
        'origin'            => $input['origin'] ?: \Salesfloor\Models\Customer::ORIGIN_UNKNOWN,
        'source'            => $input['source'],
        'source_id'         => $input['source_id'],
        'locale'            => $input['locale'],
        'sms_marketing_subscription_flag' => $smsSubFlag,
    ]);

    $app['pre_customers.manager']->save($preCustomerModel);

    $newPreCustomerId = $preCustomerModel->getId();

    // Read it back to get the ID
    return $wpdb->get_results($wpdb->prepare("select * from sf_pre_customer where id=%s", $newPreCustomerId));
}

/**
 * Update the customer (phone/name/subscription)
 *
 * @param $input
 * @param Customer $current
 * @param Boolean $forceSubscription Force an email subscription
 */
function updateCustomer($input, $current, $forceSubscription = false)
{
    global $wpdb, $app;

    /** @var \Salesfloor\Services\Multilang $multilangService */
    $multilangService = $app['service.multilang'];

    /** @var Salesfloor\Services\NameSuggester $nameSuggester */
    $nameSuggester = $app['name_suggester'];

    /** @var Legacy $customersManager */
    $customersManager = $app['customers.manager'];

    $defaultRetailerLocale = $multilangService->getRetailerDefaultLocale();


    // remove space
    $input = array_map(function ($item) {
        return trim($item);
    }, $input);

    // We got an existing customer, set the name and the phone if the new one exist and are different than the old.
    // SF-1404, only update missing information.

    $newPhone        = $current->phone;
    $newName         = $current->name;
    $newFirstName    = $current->first_name;
    $newLastName     = $current->last_name;
    $newSubscription = $current->subcribtion_flag;
    $newSmsSubscription = $current->sms_marketing_subscription_flag;
    $locale          = $current->locale;

    if (!empty($input['phone'])) {
        try {
            $normalizedPhone = normalizePhone($input['phone'], $input['country'], $current->user_id);
        } catch (\Exception $e) {
            $app['logger']->warning(sprintf("normalize input phone:[%s] with country:[%s] Error [%s]", $input['phone'], $input['country'], $e->getMessage()));
            $normalizedPhone = null;
        }

        if (!empty($normalizedPhone) && $normalizedPhone !== $current->phone) {
            $newPhone = $normalizedPhone;
        }
    }

    if (!empty($input['name']) && $input['name'] !== $current->name) {
        $newName = $input['name'];

        $nameLocale = $locale ?? $defaultRetailerLocale;
        $names = $nameSuggester->parseName($input['name'], $nameLocale);

        $newFirstName = $names['given'];
        $newLastName  = $names['family'];
    }

    if (!empty($input['firstName']) && $input['firstName'] !== $current->first_name) {
        $newFirstName = $input['firstName'];
    }

    if (!empty($input['lastName']) && $input['lastName'] !== $current->last_name) {
        $newLastName = $input['lastName'];
    }

    $contactPreference = getContactPreferenceFromRequestContactPreference($input['contact_preference'] ?? '');
    if ((isset($input['autoSubscribe']) && $input['autoSubscribe'] == 'on') || $forceSubscription) {
        $newSubscription = Customer::EMAIL_SUBSCRIPTION_STATUS_SUBSCRIBED;
    }
    elseif (Customer::shouldUpdateSubscriptionStatus($contactPreference, [Customer::CONTACT_PREFERENCE_EMAIL])
        && $current->subcribtion_flag == Customer::EMAIL_SUBSCRIPTION_STATUS_UNSUBSCRIBED) {
        // When it's unsubscribed status and customer doesn't opt-in on the form, we need to set it as not subscribed.
        $newSubscription = Customer::EMAIL_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;
    }

    if (isset($input['autoSmsSubscribe']) && $input['autoSmsSubscribe'] == 'on') {
        $newSmsSubscription = Customer::SMS_SUBSCRIPTION_STATUS_SUBSCRIBED;
    }
    elseif (Customer::shouldUpdateSubscriptionStatus($contactPreference, [Customer::CONTACT_PREFERENCE_TEXT])
        && $current->sms_marketing_subscription_flag == Customer::SMS_SUBSCRIPTION_STATUS_UNSUBSCRIBED) {
        // When it's unsubscribed status and customer doesn't opt-in on the form, we need to set it as not subscribed.
        $newSmsSubscription = Customer::SMS_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;
    }

    // Don't use Wordpress because it's causing some issue with "null" value

    /** @var \Salesfloor\Models\Customer $customer */
    $customer = $customersManager->getOneOrNull([
        \Salesfloor\Models\Customer::ID_FIELD => $current->ID,
    ], null, false);

    if (empty($customer)) {
        // We can't update a customer that doesn't exist anymore
        return;
    }

    if (!empty($input['locale']) && $app['service.multilang']->isValidRetailerLocale($locale)) {
        $locale = $input['locale'];
    }

    $customer->name = $newName;
    $customer->phone = $newPhone;
    $customer->first_name = $newFirstName;
    $customer->last_name = $newLastName;
    $customer->subcribtion_flag = $newSubscription;
    $customer->sms_marketing_subscription_flag = $newSmsSubscription;
    $customer->locale = $locale;
    if ($customer->user_id == 0) {
        // Reqeust comes from side bar, we should update 'contact_preference'
        $customer->contact_preference = $contactPreference;
    }

    $customersManager->save($customer, true);
    $customersManager->index(['id' => $current->ID]);

    // PP-241 - Process matching rules
    /** @var MatchCustomersRetailerCustomerInterface $matchCustomerRetailerCustomerService */
    $matchCustomerRetailerCustomerService = $app['match-customer-retailercustomers'];
    if ($matchCustomerRetailerCustomerService->isEnabled()) {
        $matchCustomerRetailerCustomerService->addCustomerToQueue($current->ID);
    }
}

// SF-17529: de-unsubscribe contact following a new request
// This is necessary to be able to contact the customer by email
// Even if the subcribtion_flag = 0
//
// MIGRATED
// @see: \Salesfloor\Services\Mail\EmailBlockList::removeEmailsFromGlobalUnsubscribes
function removeEmailsFromGlobalUnsubscribes($email)
{
    global $app;
    $app['sf.mail.email_block_list']->removeEmailsFromGlobalUnsubscribes($email);
}

/**
 * Find customer by user object and customer email
 * @note this function will only find user by using $user->ID in params
 *       If we want to apply the function for store mode, the user need re-mapped to store user before function called
 *
 * MIGRATED
 * @see: \Salesfloor\API\Managers\Client\Customers\Legacy::findCustomerIds
 *
 * @param $user
 * @param $email
 * @return mixed
 */
function findCustomerFromUserAndEmail($user, $email)
{
    global $wpdb;

    return $wpdb->get_results(
        $wpdb->prepare(
            "SELECT c.* FROM sf_customer as c LEFT JOIN sf_customer_meta as cm ON cm.customer_id = c.ID WHERE c.user_id=%d AND (c.email=%s OR (cm.type = 'email' AND cm.value=%s))",
            $user->ID,
            $email,
            $email
        )
    );
}

/**
 * We can't do a match if there's more than 1 phone number. In this case
 * we will add it as a pre customer
 *
 * MIGRATED
 * @see: \Salesfloor\API\Managers\Client\Customers\Legacy::findCustomerIdsByUserAndPhone
 *
 * @param $user
 * @param $phone
 *
 * @return null
 */
function findCustomerFromUserAndPhone($user, $phone)
{
    global $wpdb;

    // Do not try to match when it's from the sidebar (user_id = 0)
    // This will result in a new lead (text) being assigned already to a customer_id
    // Since, when we claim a text request we don't create a new customer, the request will also have a customer_id
    // assign to the "generic" user_id = 0 forever.

    if (empty($user) || empty($user->ID)) {
        return null;
    }

    $results = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT c.* FROM sf_customer as c LEFT JOIN sf_customer_meta as cm ON cm.customer_id = c.ID WHERE c.user_id=%d AND (c.phone=%s OR (cm.type = 'phone' AND cm.value=%s))",
            $user->ID,
            $phone,
            $phone
        )
    );


    return !empty($results) && count($results) == 1 ? $results : null;
}

/**
 * MIGRATED
 * @see: \Salesfloor\API\Managers\Client\Customers\Legacy::beforeSave
 *
 * @param $phone
 * @param $userId
 *
 * @return string|null
 */
function getNormalizedPhone($phone, $userId)
{
    global $app;

    $normalizedPhone = null;

    if (!empty($phone)) {
        $country         = $app['normalization.phonenumbers']->getUserCountry($userId);
        $normalizedPhone = $app['normalization.phonenumbers']->normalize($phone, $country);
    }

    return $normalizedPhone;
}

/**
 * Add phone/email/subscription/sms_subscription flag into sf_customer_field_history table
 * A simple wrapper used in wordpress for store request only
 * @param \Salesfloor\Models\Customer $insertOrUpdateCustomer
 * @param \Salesfloor\Models\Customer $originalCustomer
 * @param string $source
 * @param int $sourceId
 * @throws Exception
 */
function logMailPhoneHistory($insertOrUpdateCustomer, $originalCustomer, $source, $sourceId)
{
    global $app;

    /** @var Legacy $customersManager */
    $customersManager = $app['customers.manager'];

    /** @var CustomerFieldHistory $customerFieldHistoryManager */
    $customerFieldHistoryManager = $app['customer_field_history.manager'];

    $customerFieldHistoryModel = $customersManager->createHistoryModel([
        'source'    => $source,
        'source_id' => $sourceId,
    ]);
    $customerFieldHistoryManager->logMailPhoneHistory($insertOrUpdateCustomer, $originalCustomer, $customerFieldHistoryModel, true);
}

/**
 * For the contact preference from reqeust, it's one of the values ('email', 'sms'),
 * When we store it into DB, it's one of the values ('email', 'text'),
 * This function convert the request values to database values.
 *
 * @param string $requestContactPreference
 * @return string
 */
function getContactPreferenceFromRequestContactPreference(string $requestContactPreference): ?string
{
    $mappings = [
        'email' => Customer::CONTACT_PREFERENCE_EMAIL,
        'sms' => Customer::CONTACT_PREFERENCE_TEXT,
    ];
    return $mappings[$requestContactPreference] ?? null;
}
