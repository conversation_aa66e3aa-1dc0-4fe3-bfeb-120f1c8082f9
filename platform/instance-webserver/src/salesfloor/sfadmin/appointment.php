<?php

use SF\Helper\Driver\Salesfloor;
use Salesfloor\Models\PreCustomer;
use Salesfloor\Models\CustomerActivityFeed;
use Salesfloor\Models\CustomerFieldHistory;
use Salesfloor\Models\Services\Appointment;
use Salesfloor\Services\Event as EventService;
use Salesfloor\Models\Services\ServiceInterface;
use Salesfloor\Services\PushNotifications\PublishQueue;

require_once('../wp-load.php');
require_once('includes/defines.php');
require_once('includes/email_template.php');
require_once('includes/push-notifications.php');
require_once('includes/stores.php');
require_once('includes/specialties.php');
require_once('includes/widget.php');
require_once('includes/customer.php');
require_once('includes/csrf.php');
require_once('includes/json.php');
require_once('includes/text.php');
require_once('../../../../models/src/CustomerActivityFeed.php');


/** @var \Salesfloor\Services\Sanitize\Sanitize $sanitizeService */
$sanitizeService = $app['service.sanitize'];
global $GET, $escIn, $escOut;
$escIn = $sanitizeService->getEscapeIn();
$escOut = $sanitizeService->getEscapeOut();
$repId = $storeId = null;

// Escape request variables
$GET  = $escIn->request($_GET);
$POST = $escIn->request($_POST);

/** @var EventService $eventService */
$eventService = $app['service.event'];

/** @var \Salesfloor\API\Managers\Blacklist\Sms $blacklistSmsManager */
$blacklistSmsManager = $app['blacklistSms.manager'];

/** @var \Salesfloor\API\Managers\EmailBlock\BlockedIncomingEmailAddresses $blacklistEmailManager */
$blacklistEmailManager = $app['blocked_incoming_email.manager'];

/** @var \Salesfloor\Services\Widget\Appointment $widgetService */
$widgetService = $app['service.widget.appointment']->setContext($GET, $POST);

/** @var \Salesfloor\Services\Services\Appointments\Appointments $appointmentsService */
$appointmentsService = $app['appointments.services'];

/** @var PublishQueue $publishQueueService */
$publishQueueService = $app['service.push.publish.queue'];

// If we get a link for a meeting event, this will be a Get with parameters and an magic value
// identifying the meeting.
/*
API:

    magicid=<uniq_id> for the meeting.

    -- Delete meeting from ID
        -- meetingid=<value>
        -- action=delete
    -- Accept meeting from ID
        -- meetingid=<value>
        -- action=accept
    -- Change meeting from ID
        -- meetingid=<value>
        -- action=change
        -- date=<value>
        -- time=<value>
        -- timezone=<value>
    -- Get meeting from ID
        -- meetingid
        ?? Should be logged ??
    --


*/

// When we are getting called by a link, we are getting a magic value and and a meeting ID, those should
// match a

/*
 * Simulate an event in 10 minutes.
 *
      SET time_zone='+00:00';
      insert into sf_appointments (customer_id, user_id, event_type, date, uniq_id, creation) values
                          (1, 5, 'meeting', ADDTIME(now(), '00:10'), FLOOR(RAND()*1000), now() );

    To test remove
        Sample test for remove
        http://192.168.0.102/sfadmin/bookappt.php?magicid=105&meetingid=1&action=delete

    To accept the meeting:
        http://192.168.0.102/sfadmin/bookappt.php?magicid=746&meetingid=3&action=accept

        http://192.168.0.102/sfadmin/bookappt.php?magicid=746&meetingid=3&action=change&date=2014-01-13&time=13:43
*/

/*
 * Those are to process the data coming from the Email
 * */

$user = getUserFromRepname($GET) ?? sf_get_userinfo($GET['rep']);
list(, $storeId) = getIdentifier($GET, $app['configs'], $user);

$storeId = (int) ($GET['from'] == 'sidebar' || $app['configs']['retailer.storepage_mode']) ? $storeId : $user->data->store;
$repId = (!$app['configs']['retailer.storepage_mode'] && $user) ? (int)$user->ID : null;


if (isset($GET["magicid"]) && isset($GET["meetingid"]))
{
    //$meeting = $wpdb->get_results("SELECT * FROM sf_appointments where uniq_id = '" . $GET["magicid"] . "' and ID = '" . $GET["meetingid"] . "';");
    $meeting = $wpdb->get_results(
        $wpdb->prepare("select
                              ID,
                              customer_id,
                              user_id,
                              unix_timestamp(date) as apt_date,
                              unix_timestamp(enddate) as end_date,
                              status,
                              event_type,
                              timezone,
                              cust_meeting_link,
                              uniq_id,
                              rep_meeting_link,
                              notes,
                              locale
                        from
                              sf_appointments
                        where
                              uniq_id = %s
                              and
                              ID = %s",
            $escIn->ssTrim($GET["magicid"]),
            $escIn->ssTrim($GET["meetingid"])
        )
    );

    if (!isset($meeting[0]))
    {
        echo _("This meeting does not exist.");
        exit;
    }

    // don't waste customer's time if rep is no longer active
    $user_id = $meeting[0]->user_id;
    $user = get_user_by('id', $user_id);
    if ($user->user_status != '1') {
      header('Location: /404');
      exit;
    }
    /** @var Appointment $request */
    $request = $app['appointments.manager']->getByUniqId($GET["magicid"]);

    //
    // We have a match for the meeting referenced by the call back. Now we can look at the action
    switch($GET["action"])
    {
        case 'cancel':

            if ($request->channel == ServiceInterface::CHANNEL_TEXT) {
                // Send an SMS to the customer
                $appointmentsService->cancelFromCustomer($request);
            }

            renderCancelConfirmDisplay($meeting[0]->ID, $GET["rep"], $meeting[0]);
            break;

        case 'accept':
            $customer = false;
            $rep = $GET["rep"]; // We get the rep login from the params

            if ($meeting[0]->apt_date < time() )
            {
                // Check if the meeting is still in the future,
                // present the form to reschedule if not
                renderRescheduleForm($meeting[0], __("This meeting is expired, please set a new date for it.") );
                exit;
            }

            $wpdb->get_results("update sf_appointments set status = " . STATUS_ACCEPTED . ", version = version + 1, updated_at = '" . gmdate('Y-m-d H:i:s') . "' where ID= " . $meeting[0]->ID . ";");
            // Retrieve appointment model
            $request = $app['appointments.manager']->getById($meeting[0]->ID);
            if ($appointmentsService->pushVirtualInFirebase($request) === null) {
                $appointmentsService->updateVirtualInFirebase($request);
            }

            // We determine which email to send depending on if there was a reschedule and, if so, by whom
            if(isset($GET["customer"]) && $GET["customer"] && $GET["customer"] != "false"){
                if($GET["customer"] == "true") {

                    if ($request->channel == ServiceInterface::CHANNEL_TEXT) {
                        // Send an SMS to the customer
                        $appointmentsService->acceptFromCustomer($request);
                    }

                    // Case where its a reschedule accepted by the customer
                    send_email(TEMPLATE_APT_ACCEPT_RESC_BY_CUST_TO_REP, $meeting[0]->ID, 'appointment', false);

                    // Dont_send is set to true, and since this customer message was assigned to the wront "from_type", we never
                    // add a row in sf_message. Afaik, it's doing nothing we need anymore
                    // Since i fixed the type ( so accept appt can be part of the "answer" of the reply time metric), it's adding
                    // an extra row in sf_message, that we never saw before with not useful information
                    // TEMPLATE_APT_ACCEPT_RESC_BY_REP_TO_REP was already removed from initial commit
                    //send_email(TEMPLATE_APT_ACCEPT_RESC_BY_CUST_TO_CUST, $meeting[0]->ID, 'appointment', false, null, true);

                    $customer = true;
                } else {
                    // Case where its a reschedule accepted by the rep
                        send_email(TEMPLATE_APT_ACCEPT_RESC_BY_REP_TO_CUST, $meeting[0]->ID, 'appointment', true, null, !$appointmentsService->isConsented($request->channel, $request->customer_id));
                }
            } else {
                // Case when there was no reschedule
                if ($request->channel == ServiceInterface::CHANNEL_TEXT) {
                    // Send an SMS to the customer
                    $appointmentsService->acceptFromRep($request);
                } else {
                    // Send an email to the customer
                    send_email(TEMPLATE_APT_ACCEPT_BY_REP_TO_CUST, $meeting[0]->ID, 'appointment', true, null, !$appointmentsService->isConsented($request->channel, $request->customer_id));
                }

                // Add a message "You accepted the appointment"
                send_email(TEMPLATE_APT_ACCEPT_BY_REP_TO_REP, $meeting[0]->ID, 'appointment', false, null, true);
            }

            renderAcceptDisplay($customer, $rep, $meeting[0]);
            break;

        case 'change':
            // Ok the Rep click on the change link and we need to present the form populated with the proper information.
            // magicid={$meeting[0]->uniq_id}&meetingid={$meeting[0]->ID}&action=change

            // TODO: This check for a valid user but do not check for this specific rep, we should probably reject the request if this is not the proper RepID.
            if (! isset($GET["rep"]) && ! is_user_logged_in() )
            {
                auth_redirect();
                exit;
            }

            // Diplay the form populated with the meeting information
            $repId = $user->ID;
            $storeId = $user->store;
            renderRescheduleForm($meeting[0]);
            break;
        default:
            // Return the meeting information
            echo "This meeting is set for " . $meeting[0]->date . "<br>\n";
            echo "Customer is : " . $meeting[0]->customer_id . "<br>\n";
            echo "Rep is : " . $meeting[0]->user_id . "<br>\n";
            echo "Event type : " . $meeting[0]->event_type . "<br>\n";
            echo "Last status : " . $meeting[0]->status . "<br>\n";
            echo "Location: " . $meeting[0]->location . "<br>\n";
            echo "Notes: " . $meeting[0]->notes . "<br>\n";
            echo "The time zone use for this is " . $meeting[0]->timezone . "<br>\n";
            break;
    }

    exit;
}



/*
 * If we are in the case where the cancel was confirmed, we send the corresponding
 * emails to the rep and the customer to inform them and we update the database.
 */
if (isset ($POST["cancelConfirmed"]) && isset($POST["meetingId"])){
    if (!check_qdcsrf($POST['usdoptlcdc'], $_SERVER['HTTP_USER_AGENT'])) {
        header('HTTP/1.0 400 Bad Request');
        exit;
    }
    $meetingId = $escIn->ssTrim($POST["meetingId"]);
    $select = $wpdb->prepare("SELECT a.user_id, c.email, c.subcribtion_flag FROM sf_appointments a JOIN sf_customer c ON a.customer_id = c.ID WHERE a.ID = %d", $meetingId);
    $apt = $wpdb->get_row($select, ARRAY_A);

    $wpdb->get_results($wpdb->prepare("update sf_appointments set status = " . STATUS_CANCELLED .", version = version + 1, updated_at = '" . gmdate('Y-m-d H:i:s') . "' where ID=%s",$meetingId));

    $appointment = $app['appointments.manager']->getById($meetingId);
    $appointmentsService->deleteVirtualInFirebase($appointment);
    // We send the cancel email to the rep and the customer
    send_email(TEMPLATE_CANCEL_TO_REP, $meetingId, 'appointment', false);

    // we may need the customer consent
    $outOfOfficeSent = false;
    if ($app['configs']['retailer.services.appointment_management.notify_without_consent'] || $apt['subcribtion_flag'] != \Salesfloor\Models\Customer::EMAIL_SUBSCRIPTION_STATUS_UNSUBSCRIBED) {
        $outOfOfficeSent = sendOutOfOfficeEmailIfApplicable($apt['user_id'], $apt['email']);
    }
    if (!$outOfOfficeSent || $app['configs']['autoresponder.send_normal_response_too']) {
        send_email(TEMPLATE_CANCEL_TO_CUST, $meetingId, 'appointment',false, null, true);
    }



    if (!empty($POST['repname'])) {
        // we can't use $apt['user_id'] here because it's the store user ID
        // under store mode, we need the actual rep ID here to send push
        // notification
        $user = sf_get_userinfo($POST['repname']);
        $message = 'pn_cancel_appointment_request';
        $inapp = [
            'event_action' => 'cancel_appointment',
            'request_id' => 'book_appointment_' . $meetingId,
            'alertBox' => 'false'
        ];
        $publishQueueService->publishToReps([$user->ID], $message, $inapp);
    }

    echo "{}\n";
    exit;
}



/*
 * We are coming from a post then this mean we have the form being populated by either the customer or from
 * the Rep responding to the customer for a appointment.
 * */

if (isset($POST["service"]) && isset($POST["choosenDate"]) && isset($POST["choosenTime"]))
{
    /** @var \Salesfloor\API\Managers\Client\Customers\Legacy $customersManager */
    $customersManager = $app['customers.manager'];

    if (!check_qdcsrf($POST['usdoptlcdc'], $_SERVER['HTTP_USER_AGENT'])) {
        header('HTTP/1.0 400 Bad Request');
        exit;
    }

    // We have parameter which are coming from the appointment page, Let's set the meeting in the database
    //
    $result = "{}";

    $serviceType = $POST["service"];
    $selectedSpecialty = sanitize_selected_specialty($POST["selectedSpecialty"]);

    $timeZone = $POST["timezone"];
    try {
        $apt_tz = new DateTimeZone($timeZone);
        if (!$apt_tz) {
            error_log('Could not get DateTimeZone from >'. $timeZone);
            renderJSONInvalid("Invalid: TimeZone.");
        }
    } catch (Exception $e) {
        error_log('Could not get DateTimeZone from >'. $timeZone);
        error_log('Exception >'. $e);
        renderJSONInvalid("Invalid: TimeZone.");
    }

    $apt_utctz = new DateTimeZone('UTC');
    try {
        $apt_st = new DateTime($POST["choosenDate"] . " " . $POST["choosenTime"], $apt_tz);
    } catch (Exception $e) {
        renderJSONInvalid("Invalid: Start Date.");
    }
    // $apt_st->setTimezone($apt_tz);

    $apt_st->setTimezone($apt_utctz);

    $duration = $app['configs']['retailer.services.appointment.duration'];
    // Just a dummy fix, this should not be happening... If outside of the limit, set to 30 min.
    if ($duration < 0 || $duration > 300) {
        $duration = 30;
    }

    try {
        $durationInSec = $duration * 60;
        $apt_end_time = date('h:i A', strtotime($POST['choosenTime']) + $durationInSec);
        $apt_end = new DateTime($POST["choosenDate"] . " " . $apt_end_time, $apt_tz);
    } catch (Exception $e) {
        renderJSONInvalid("Invalid: End Date");
    }
    // $apt_end->setTimezone($apt_tz);
    $apt_end->setTimezone($apt_utctz);

    if ($apt_st->format('U') < time()) {
        renderJSONInvalid("Invalid: Start Date.");
    }

    $customerName = '';
    if (isset($POST["name"])) {
        $customerName = $escIn->str($escIn->ssTrim($POST["name"]));

        $customerName = filter_var($customerName, FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW);
        if ($customerName === false) {
            renderJSONInvalid("Invalid: Name");
        }
    }

    $customerMail = '';
    if ( !empty($POST["email"]) ) {
        $customerMail = $escIn->ssTrim($POST["email"]);
        if ( !filter_var($customerMail, FILTER_VALIDATE_EMAIL) ) {
            renderJSONInvalid("Invalid: Email");
        }
    }

    $extraInfo = "";
    if ( isset($POST["extraInfo"])) {
        $extraInfo = $escIn->str($escIn->ssTrim($POST["extraInfo"]));
    }

    if (isset($POST["context"])) {
        $origin = $POST["context"] === 'storefront'
            ? \Salesfloor\Models\Customer::ORIGIN_STOREFRONT_APPOINTMENT
            : \Salesfloor\Models\Customer::ORIGIN_WIDGET_APPOINTMENT;
    } else {
        $origin = \Salesfloor\Models\Customer::ORIGIN_UNKNOWN;
    }

    /**
     * Some retailer $configs['messaging.text.enabled'] = false;
     * There is no "contact_preference" in request and 'email' is default value.
     */
    $contactPreference = 'email';
    if (isset($POST["contact_preference"])) {
        $contactPreference = mb_strtolower($escIn->ssTrim($POST["contact_preference"]));
    }
    $isSmsRequest = $contactPreference === 'sms';

    if ($isSmsRequest) {
        $customerPhone = $escIn->ssTrim(filter_var($POST["sms"],FILTER_UNSAFE_RAW,FILTER_FLAG_STRIP_LOW));
    } else {
        $customerPhone = $escIn->ssTrim(filter_var($POST["phone"],FILTER_UNSAFE_RAW,FILTER_FLAG_STRIP_LOW));
    }

    // If we are call from a reschedule, then we have a meeting ID value.
    $meetingID = $escIn->ssTrim($POST["meetingID"]);

    $customerCountry = $escIn->ssTrim($POST["country"]);

    $foaas_dot_com = true;
    $storeID = 0; // we will not insert store 0
    if ( isset($POST["store"]) ) {
        // $storeID may be a store identifier here, see :~platform/widgets/app/web/js/widgets/widget.sidebar.js:getStoreId
        // This means that it can be an int or a string
        $storeID = $POST["store"];
        /** @var \Salesfloor\API\Managers\Stores $storeManager */
        $storeManager = $app['stores.manager'];
        $storeByIdOrIdentifier = $storeManager->getStoreByIdOrIdentifier($POST["store"]);
        if (empty($storeByIdOrIdentifier)) {
            renderJSONInvalid("invalid store");
        } else {
            $storeID = $storeByIdOrIdentifier->store_id;
        }
        $foaas_dot_com = false;
    }
    $repId = is_numeric($POST['user_id']) ? (int) $POST['user_id'] : $repId;
    // Booking appointment limit validation
    if (
       $widgetService->getSource('booking_validation') !== 'retailer'
        && !$app['service.appointment_hours']->isBookingAvailableForTimeSlot((int)$storeID, $apt_st, $repId)
    ) {
        $locale = wp_get_current_user()->locale ?? null;
        $message = $app['appointments.manager']->getTranslatedMessage('api_exception_appointment_time_not_available', $locale);

        http_response_code(406);
        renderJSONInvalid(json_encode(['status' => 'timeslot_not_available', 'message' => $message]));
    }

    $customerLocale = $app['service.multilang']->sanitizeLocaleByStore(
        isset($POST['sf_locale']) ? $POST['sf_locale'] : null,
        $storeID
    );

    $currentTime = new DateTime('NOW');
    // $currentTime->setTimezone($apt_tz);
    $currentTime->setTimezone($apt_utctz);

    // Team mode never assigns to a specific rep when request is submitted. Always store user.
    if ($widgetService->isTeamMode()) {
        // Anonymous rep requests.
        $user     = new  stdClass();
        $user->ID = 0;

        $requestStoreId = $widgetService->getTeamModeStoreId();
        if (!empty($requestStoreId)) {
            $user = getStoreFromIdentifierOrFromId($requestStoreId);
        }
    } else {
        $repname = $POST['repname'];

        // When a request come from the sidebar/retailer api, the repname is the storeID (Sometimes (multibrand not))
        // $user and $user->ID will be always empty is this case
        if ($POST['from'] !== 'sidebar' && $POST['from'] !== 'retailer') {
            $user = ctype_digit($repname) ? sf_get_userinfo(null, $repname) : sf_get_userinfo($repname);
        }
    }

    if (empty($user))
    {
        $user = new stdClass();
        $user->ID = 0;
    }

    $appt_user = $user;

    // SF-1223 and discussion with Ben, if the customer pick a different store for the appointment than the initial store, we should set the apt in it.
    if (($storeID > 0) && ($user->type === "store") && ($user->store !== $storeID))
    {
        // We have a store ID different than the store for that user and that user is a store, then we create the request
        // to the corresponding store user.
        $store_user_id = $wpdb->get_var("select store_user_id from sf_store where store_id = " . $storeID);

        if (!empty($store_user_id))
        {
            // Set the user as the matching store ID.
            $user = get_user_by('id', $store_user_id);
        }
    }

    $sourceUrl   = $escIn->str($escIn->ssTrim($POST["source_url"]));
    $sourceTitle = $escIn->str($escIn->ssTrim($POST["source_title"]));

    $autoSubscribe = 'off';
    if (isset($POST['autoSubscribe']) && in_array($POST['autoSubscribe'], ['on', 'off'])) {
        $autoSubscribe = $POST['autoSubscribe'];
    }
    $autoSmsSubscribe = 'off';
    if (isset($POST['autoSmsSubscribe']) && in_array($POST['autoSmsSubscribe'], ['on', 'off'])) {
        $autoSmsSubscribe = $POST['autoSmsSubscribe'];
    }

    // Get a random value
    // DIDIER: Need to confirm that we do not have an other meeting with this value
    $magicID = uniqid("SFID",true);

    // We need to find the customer ID or create a new entry for this customer.
    // DIDIER: Ideally we should be able to create a cookie here to track this customer.

    if ($result == "{}")
    {
        $customerDB = null;
        switch ($contactPreference) {
            case 'email':
                if (!empty($customerMail)) {
                    $customerDB = findCustomerFromUserAndEmail($user, $customerMail);
                }
                break;
            case 'sms':
                if (!empty($customerPhone)) {
                    $customerPhone = normalizeCustomerPhone($customerPhone, $customerCountry, $isSmsRequest);
                    $customerDB = findCustomerFromUserAndPhone($user, $customerPhone);
                }
                break;
        }
        $current = empty($customerDB) ? null : reset($customerDB);

        // We use the same form to update the time, but we don't want to insert/edit in this case
        if (empty($meetingID)) {
            if (empty($current)) {
                $originalCustomer = null;
                $customerDB = insertCustomer([
                    'name'    => $customerName,
                    'country' => $customerCountry,
                    'phone'   => $customerPhone,
                    'email'   => $customerMail,
                    'origin'  => $origin,
                    'locale'  => $customerLocale,
                    'contact_preference' => $contactPreference,
                    'autoSubscribe' => $autoSubscribe,
                    'autoSmsSubscribe' => $autoSmsSubscribe,
                ], $user);
                $current    = $customerDB[0];
                $customerId = $current->ID;
            } else {
                $customerId = $current->ID;
                $originalCustomer = $customersManager->getById($current->ID);
                updateCustomer(
                    [
                        'name'   => $customerName,
                        'country' => $customerCountry,
                        'phone'  => $customerPhone,
                        'locale' => $customerLocale,
                        'contact_preference' => $contactPreference,
                        'autoSubscribe' => $autoSubscribe,
                        'autoSmsSubscribe' => $autoSmsSubscribe,
                    ],
                    $current
                );
            }
            $insertOrUpdateCustomer = empty($customerId) ? null : $customersManager->getById($customerId);
        }

        // #SF-28204 If not blocked allow
        if ($blacklistEmailManager->isBlocked($customerMail)) {
            $eventService->trackEvent(
                $eventService->getEvent(EventService::SF_EVENT_APPOINTMENT_EMAIL_BLOCKED),
                'appointment',
                !empty($customerId) ? $customerId : null,
                !empty($user) ? $user->ID : null,
                null,
                null,
                null
            );
        } elseif ($blacklistSmsManager->isBlocked($customerPhone)) {
            $eventService->trackEvent(
                $eventService->getEvent(EventService::SF_EVENT_APPOINTMENT_SMS_BLOCKED),
                'appointment',
                !empty($customerId) ? $customerId : null,
                !empty($user) ? $user->ID : null,
                null,
                null,
                null
            );
        } else {
            // Clean the Event Type
            $serviceType = Appointment::getEventTypeByMeetingType($serviceType) ?? 'invalid-' . $serviceType;

            // if they have an invalid service type it is because they cheated.
            if (('invalid' === substr($serviceType, 0, 7)) ||
                (!SF_LIVE_SERVICE_VIDEO && Appointment::EVENT_TYPE_VIDEO === $serviceType) ||
                (!SF_LIVE_SERVICE_CHAT && Appointment::EVENT_TYPE_CHAT === $serviceType)) {
                $errmsg = "BOOK APT: Invalid type received, failing >" . $serviceType;
                syslog(LOG_ERR, $errmsg);
                echo $errmsg;
                die();
            }

            // Update the database with the new appointment.

            // If we have a meeting ID, we change it instead of creating a new one
            if (isset($meetingID) && $meetingID) {
                // Get old appointment to have access to the version number since $wpdb->update doesn't support increments...
                /** @var Appointment $oldAppt */
                $oldAppt = $app['appointments.manager']->getById($meetingID);

                 if(isset($POST['customer']) && $POST['customer'] == 'true') {
                    $status = '';
                    $note_col = 'notes';
                } else {
                    $status = 'pending';
                    $note_col = 'rep_comment';
                }
                $wpdb->update(
                    "sf_appointments",
                    array( // Data
                        "event_duration" => $duration,
                        "date" => $apt_st->format('Y-m-d H:i'),
                        "status" => $status,
                        $note_col => $extraInfo,
                        "timezone" => $timeZone,
                        "enddate" => $apt_end->format('Y-m-d H:i'),
                        "version" => $oldAppt->version + 1,
                        "updated_at" => gmdate('Y-m-d H:i'),
                    ),
                    array( // Data where
                        "ID" => $meetingID,
                    ),
                    array( // Data format
                        "%d",
                        "%s",
                        "%s",
                        "%s",
                        "%s",
                        "%s",
                        "%d",
                        "%s",
                    ),
                    array( // Data where format
                        "%d"
                    )
                );
                $app['appointments.manager']->resetNotify($meetingID);

                /** @var Appointment $request */
                $request = $app['appointments.manager']->getById($meetingID);
                $appointmentsService->updateVirtualInFirebase($request);

                // We determine if it is a reschedule for the rep or the customer
                // We then send the emails accordingly to the rep and the customer
                if (isset($POST["customer"]) && $POST["customer"] == "true") {
                    if ($request->channel == ServiceInterface::CHANNEL_TEXT) {
                        // New time from the customer, so need to add this new time in the text thread
                        $appointmentsService->newTimeFromCustomer($request);
                    }

                    send_email(TEMPLATE_APT_RESC_CUST, $meetingID, 'appointment', false);
                    if (!$appointmentsService->isConsented($request->channel, $request->customer_id)){
                        sendOutOfOfficeEmailIfApplicable($user->ID, $customerMail);
                    }

                    $message = 'pn_modify_appointment_request';
                    $inapp = [
                        'event_action' => 'modify_appointment',
                        'request_id' => 'book_appointment_' . $meetingID,
                        'alertBox' => 'false'
                    ];
                    $publishQueueService->publishToReps([$user->ID], $message, $inapp);
                } else if (isset($appt_user->ID) && $appt_user->ID) {
                    // SF-13935 The global `$foaas_dot_com` is related to a different bug (SF-5188)
                    //          and seems to be the root of the current bug. Since it's definitely a hack
                    //          and it's being used to define the user name when building the email template
                    //          (sfadmin/includes/email_template.php::513), looks like we have no options
                    //          but keeping the approach
                    $foaas_dot_com = false;

                    if ($request->channel == ServiceInterface::CHANNEL_TEXT) {
                        $appointmentsService->newTimeFromRep($request);
                    } else {
                        // This will send a email to the customer AND add to sf_message
                         send_email(TEMPLATE_NEW_APPOINTMENT_TO_CUSTOMER, $meetingID, 'appointment', true, $appt_user->ID, !$appointmentsService->isConsented($request->channel, $request->customer_id));
                    }

                } else {
                        send_email(TEMPLATE_NEW_APPOINTMENT_TO_CUSTOMER, $meetingID, 'appointment', true, null, !$appointmentsService->isConsented($request->channel, $request->customer_id));
                }
            }
            else
            {
                if (empty($storeID) && !empty($POST['store_lead'])) {
                    $storeLead = $POST['store_lead'];

                    $store = $app['stores.manager']->getStoreByIdOrIdentifier($storeLead);

                    if (!empty($store)) {
                        $storeID = $storeLead;
                    } else {
                        // It seems that it's always a id now (team/rep/multibrand)
                        // Keep fallback just in case
                        $name = str_replace('-', ' ', $storeLead);
                        $storeID = $wpdb->get_var(
                            $wpdb->prepare(
                                "select store_id from sf_store where name=%s",
                                $name
                            )
                        );
                    }
                }
                // Detect the channel based on input - could get an exception if invalid input
                $channel = getChannelFromServiceRequest($POST);

                $source = $widgetService->getSource($magicID);

                $loc = sf_get_location_from_request();
                $appt_data = array(
                    "user_id" => $user->ID,
                    "event_type" => $serviceType,
                    "event_duration" => $duration,
                    "date" => $apt_st->format('Y-m-d H:i'),
                    "status" => '',
                    "notes" => $extraInfo,
                    "uniq_id" => $magicID,
                    "timezone" => $timeZone,
                    "creation" => $currentTime->format('Y-m-d H:i:s'),
                    "enddate" => $apt_end->format('Y-m-d H:i'),
                    "phone" => $customerPhone,
                    "source_url" => $sourceUrl,
                    "source_title" => $sourceTitle,
                    "locale" => $customerLocale,
                    "channel" => $channel,
                    "source" => $source,
                    "version" => 0,
                    "updated_at" => $currentTime->format('Y-m-d H:i:s'),
                    "location" => '', // Can't be null
                );

                // some IP addresses are problematic.  skip them.  wpdb->insert can't do NULL.
                if (isset($loc[0]->id)) {
                    $appt_data['loc_id'] = $loc[0]->id;
                }
                // wp can't insert nulls
                if ($storeID) {
                    $appt_data['store_id'] = $storeID;
                }
                if ($customerId) {
                    $appt_data['customer_id'] = $customerId;
                }
                if (isset($POST['customer_id'])) {
                    $unattached_id = $POST['customer_id'];

                    // SF-16318 Pass the fingerprint to be used when logging unattached customers
                    // Will be used to eventually create an event in the event queue
                    sf_insert_unattached($unattached_id, (!empty($POST['fingerprint']) ? $POST['fingerprint'] : null));

                    $appt_data['unattached_id'] = $unattached_id;
                }
                add_selected_specialty_to_db_insert($selectedSpecialty, $appt_data);

                // Create the request itself based on data
                /** @var \Salesfloor\Models\Services\Appointment $request */
                $request = $appointmentsService->createRequest($appt_data);

                $meeting_id = $request->getId();

                // TODO: This would be good to track also reschedule workflows.
                sf_add_tracker(SF_EVENT_BOOKAPT, null, $customerId, $user->ID, null, intval($meeting_id), $magicID);

                if (!empty($customerId)) {
                   // Customer Event Tracking: Appointment Request
                    $customerActivityFeedService = $app['service.customer_activity_feed'];

                    $customerActivityFeedService->trackActivity(
                        $customerId,
                        $appt_data['user_id'],
                        CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST,
                        $meeting_id,
                        $appt_data['notes'],
                        CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                        CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD,
                        true
                    );
                }

                // we can't put logMailPhoneHistory logic in insertCustomer()/updateCustomer()
                // because source_id is need, but insertCustomer()/updateCustomer() is call before data adding into appointment/personal_shopper/question table
                if (!empty($insertOrUpdateCustomer)) {
                    logMailPhoneHistory($insertOrUpdateCustomer, $originalCustomer, CustomerFieldHistory::SOURCE_STORE_REQUEST_APPOINTMENT, $meeting_id);
                }

                if ($isSmsRequest) {
                    if (empty($customerId)) {
                        insertPreCustomer([
                            'name' => $customerName,
                            'country' => $customerCountry,
                            'email' => $customerMail,
                            'phone' => $customerPhone,
                            'origin' => $origin,
                            'source' => \Salesfloor\API\Managers\PreCustomers::SOURCE_APPOINTMENT,
                            'source_id' => $meeting_id,
                            'locale' => $customerLocale,
                            'autoSmsSubscribe' => $autoSmsSubscribe,
                        ], $user);
                    }

                    // SF-19580 - Send text confirmation if preference is for SMS.
                    // This is a attempt to move some logic out of wordpress
                    $appointmentsService->initRequest($request, $customerLocale);
                } else {
                    $outOfOfficeSent = false;
                    if ($isConsented = $appointmentsService->isConsented($request->channel, $request->customer_id)){
                        $outOfOfficeSent = sendOutOfOfficeEmailIfApplicable($user->ID, $customerMail);
                    }
                    if (!$outOfOfficeSent || $app['configs']['autoresponder.send_normal_response_too']) {
                        send_email(TEMPLATE_REQUEST_SUCCESS, $meeting_id, 'appointment', false, null, !$isConsented);
                    }
                }

                $messageContent = send_email(TEMPLATE_NEW_APPOINTMENT_TO_REP, $meeting_id, 'appointment', false);

                // Message is translated inside the push notification service because we need to translate according to the user's language
                $message = 'pn_new_appointment_request';
                $request_id = 'book_appointment_' . $meeting_id;
                $inapp = [
                    'event_action' => 'new_request',
                    'request_id' => $request_id,
                    'alertBox' => 'false'
                ];
                if ($app['configs']['retailer.storepage_mode']) {
                    $publishQueueService->publishToStore($storeID, $message, $inapp);
                } else {
                    $publishQueueService->publishToReps([$user->ID], $message, $inapp);
                }

                if (!$app['configs']['retailer.storepage_mode'] && isset($POST["store_lead"]) && $POST["store_lead"] != "" && $user->ID === 0) {
                    $publishQueueService->publishNewLead($storeID, $selectedSpecialty, $magicID);
                }

                if (
                    $app['configs']['retailer.storepage_mode']
                    && $app['configs']['retailer.services.appointment.auto-accept.is_enabled']
                ) {
                    $sfApi = new \SFAPI_API_Salesfloor();
                    $sfApi->acceptRequestAction($meeting_id, $magicID, $user->ID);
                }
            }
        }
    }
    // We need to return an empty JSON to please the call
    echo $result . "\n";
    exit;
}

/**
 * Insert Custom Hours
 * Override Date variables
 */
function insertAppointmentHours($app)
{
    global $repId, $storeId;

    $appointmentHours = $appointmentDateOverrides = [];

    if (!empty($storeId)) {
        $appointmentHours = $app['service.appointment_hours']->getTimeslotsForUI($storeId, $repId);
        $appointmentDateOverrides = $app['service.appointment_hours']->getDateOverridesForUI($storeId, $repId);
    }

    $script = "<script type='text/javascript'>
        let appointmentHours = ". json_encode($appointmentHours) . ";
        let appointmentDateOverrides = " . json_encode($appointmentDateOverrides) . ";
    </script>";

    return $script;
}

/**
 * End
 */

$storeText = "";
$stores = array();

// If we are called from the Shopping Page, then we have the value in our parameter.
if (!$widgetService->isFormSubmission() && $widgetService->isRepAttached())
{
    $user = get_user_from_identifier_list($widgetService->getPossibleUserIdentifierFromRepname());

    if (isset($user->ID))
    {
        $helpPhoneText = '';

        // We need to get the rep's phone number
//      $repPhone = $wpdb->get_results("select * from wp_usermeta where meta_key = 'phone' AND user_id = " . $user->ID . ";");
//      $publishPhone = $wpdb->get_results("select * from wp_usermeta where meta_key = 'publish_phone' AND user_id = " . $user->ID . ";");

        $repPhone = get_usermeta($user->ID, 'phone');
        $publishPhone = get_usermeta($user->ID, 'publish_phone');

        // If the phone number isn't present or the rep doesn't want it displayed, we don't add the text
        if($repPhone != '' && $publishPhone != '' && $publishPhone != '0'){
            $helpPhoneText = '<span class="rep-phone">For immediate help, please call me at ' . esc_html($repPhone) . '</span>';
        }

        if (isset($user->store))
        {
            $store = $wpdb->get_var(
              $wpdb->prepare(
                  "select name from sf_store where store_id=%d",
                  $user->store
              )
            );

            if (! empty($store))
            {
                $storeText = esc_html($store);
            }
        }

        // Need to add the param in the hidden input field.
        renderCustomerForm($GET['repname'], $helpPhoneText, $storeText, $user->store);
        exit;
    }
}


/*
 *
 * This display the Form that need to be processed,
 *
 * */

// DIDIER: Need to replace strings with the WordPress Localization information.
renderCustomerForm(null, null, "", $GET['repname']);
$fullHeight = true;
if(isset($GET['appointmentnewtime'])){
    $appointmentnewtime = htmlspecialchars($GET['appointmentnewtime']);
}

// this renders invalids json it's appropriately named lololololol
function renderJSONInvalid($msg) {
    header("Content-type: application/json");
    error_log('Error setting appointment>'.$msg);
    echo $msg;
    die;
}

function renderCustomerForm($rep = null, $phone = null, $storeText = "", $store_id = null)
{
    global $wpdb, $app, $GET, $escIn, $escOut;

    // The name of the variable is important because it's used down the include path in javascripts.php
    $script = insertAppointmentHours($app);

    $is_store_user = false;
    $user = getUserFromRepname($GET);
    $store_user = get_store_for_user($user);
    if (isset($store_user->ID) && $store_user->ID) {
        $is_store_user = true;
    }

    // $is_store_user is used for 2 things
    // - carousel =>  /reps/X vs /stores/X
    // - different translation, i'm not 100% if it's related to the mode of the retailer (team / vs rep)
    if ($GET['from'] == 'sidebar') {
        // Always set to true when you are redirected from sidebar since it's always a store
        $is_store_user = true;
    } elseif ($GET['from'] == 'landing') {
        $is_store_user = true;
    } elseif ($GET['from'] == 'footer' && $app['configs']['retailer.storepage_mode']) {
        // For team mode, this variable is used for the carousel and we need /stores/X in this case
        // Afaik, bru/tru works on both reps/X or stores/X have the same output
        $is_store_user = true;
    }

    $userFullName = $user->first_name . " " . $user->last_name;

    $stores = get_stores_for_user($user, $GET['from']);

    /** @var $lang \Salesfloor\Services\Lang $lang */
    $lang = $app['service.lang'];
    $locale = $user && $user->store_obj ? $user->store_obj->locale : null;
    $lang->init([\Salesfloor\Services\Lang::SCOPE_WP_SERVICE_APPOINTMENT], $locale);

    $source_url = (isset($GET['source_url']) ? $escIn->ssTrim($GET['source_url']) : '');
    $source_title = (isset($GET['source_title']) && $GET['source_title'] ? $escIn->ssTrim($GET['source_title']) : $source_url);

    list($storeIdentifier, $storeId) = getIdentifier($GET, $app['configs'], $user);

    $store_dropdown = user_should_select_own_store($user, $GET['from']) ? render_store_dropdown($user, $GET['repname'], $GET['from'], $storeId,$lang->getLocale()) : '';
    $categoryId = null;
    if (!empty($GET['category'])) {
        $categoryId = stripslashes($GET['category_id']);
    }
    $backUrl = (isset($GET['from']) && ($GET['from'] == 'sidebar' || $GET['from'] == 'footer' )) ? $source_url : get_home_url() . '/'.$escOut->urlPart($GET['repname']);
    $csrfToken = qdcsrf($_SERVER['HTTP_USER_AGENT']);
    $storeHour = $app['stores.manager']->getStoreAppointmentHours();

    $isService = true;
    global $app;
    $internalFrom = 'service';
    include(file_to_include_default_to_generic('../r/includes/', '/' . $lang->getLocale() . '/head.php'));

    $textMessageIsEnabled = displayTemplateServiceWithTextChannel($user, $store_id);
    $showOptionalPhoneInput = !$app['configs']['retailer.services.hide_optional_phone_input'];
?>

    <body class="service-page <?= (isset($fullHeight) && $fullHeight) ? "fullheight": ""  ?> version2-0">

      <div class="global-services__wrapper">
        <div class="page global-services global-services--is-appointment fn-services-global-wrapper">
            <div class="service-ctn book-appointment-ctn global-services__wrapper js-service-ctn fn-result-message">
                <div class="global-services__header global-services--is-smb <?= ($GET['from'] != 'sidebar') ? "global-services__header--is-rep" : "" ?>">
                  <div class="global-services__title-wrapper">
                  <?php if(!isset($GET['embed']) || $GET['embed'] !== 'landing') : ?>
                      <div class="service-navigation-ctn global-services__title__container">
                        <a href="<?= $escOut->url($backUrl) ?>" class="back-trigger proper-icon-angle-left fn-navigation-service-back global-services__title__link">
                          <span class="global-services__title__link--is-visible"><?= $escOut->str($lang->get('appointment_back')); ?></span>
                        </a>
                      </div>
                    <?php endif; ?>
                    <h1 id="AtAppointmentTitle" class="global-services__title"><?= $escOut->str($app['sf.services']->getAppointmentLabel('service-appointment')) ?>
                      <span class="global-services__sub-title"><?= $escOut->strUnsafe($lang->get('appointment_mention')); ?></span>
                    </h1>
                  </div>

                  <div class="global-services__header__container">
                    <?php if ($app['configs']['retailer.services.logo']): ?>
                        <div class="global-services__logo-ctn">
                            <img class="global-services__logo-ctn__img" src="<?= $escOut->attr($lang->get('appointment_logo_url')); ?>">
                        </div>
                    <?php else: ?>
                        <div class="global-services__carousel <?= (isset($GET['embed']) && $GET['embed'] === 'landing') ? 'global-services__carousel--is-landing' : '' ?>">
                          <?= widgetCarousel(SF_SERVICE_WIDGET_HOST, $storeIdentifier, $GET['from'], $is_store_user, $GET['version'], $lang->getLocale())?>
                        </div>
                    <?php endif; ?>
                    <?php if(($GET['from'] === 'sidebar') && ($app['configs']['retailer.services.findarep.is_enabled']) && (!isset($GET['embed']) || $GET['embed'] !== 'landing')): ?>
                      <div class="global-services__findarep__wrapper js-findrep-link">
                        <p class="global-services__findarep"><?= $escOut->strUnsafe($lang->get('appointment_find_a_rep')); ?></p>
                      </div>
                    <?php endif; ?>
                  </div>
                </div>

                <div class="global-services__table <?php if($GET['from'] === 'storefront'): ?>global-services__table--is-storefront-context<?php elseif($GET['from'] === 'footer'): ?>global-services__table--is-footer-context<?php endif; ?><?php if(isset($GET['embed']) && $GET['embed'] === 'landing'): ?>global-services__table--is-landing-context<?php endif; ?>">
                    <div class="global-services__table__row global-services__table__row--is-form">
                    <form title="<?= $escOut->strUnsafe($lang->get('appointment_form_title')); ?>" class="default-form fn-date global-services__form fn-services-form" data-context="<?= $escOut->strUnsafe($GET['from']) ?>">
                        <?php if($lang->get('appointment_mention_top')): ?>
                            <div class="global-services__mention global-services__mention--is-top">
                                <?= $escOut->strUnsafe($lang->get('appointment_mention_top')); ?>
                            </div>
                        <?php endif; ?>
                        <fieldset class="global-services__fieldset">
                            <legend><?= $lang->get('appointment_form_title'); ?></legend>
                          <div class="content-ctn fn-content-wrapper">
                            <?php if(stores_is_a_concept($user)) : ?>
                              <script>
                                  var store_hours = [];
                              <?php
                                  foreach($stores as $store) {
                                      if ($store->hours) {
                                          echo "store_hours[" . $store->store_id . "] = " . $store->hours . ";\n";
                                      }
                                  }
                              ?>
                              </script>
                              <?php if($store_dropdown) { ?>
                                  <div class="form-elmnt-wrapper global-services__form__element global-services__form__element--is-store global-services__form--is-smb" tabindex="0">
                                      <label class="global-services__form__title"><?= $escOut->strUnsafe($lang->get('appointment_label_store_dropdown')); ?></label>
                                      <div class="service-section-ctn">
                                          <?= $store_dropdown ?>
                                      </div>
                                  </div>
                              <?php } else {
                                  $store = reset($stores);
                                  $location = getStoreLocation($store->store_id, $lang->getLocale());
                              ?>
                              <div class="form-elmnt-wrapper global-services__form__element global-services__form__element--is-store">
                                  <label class="global-services__form__title global-services__form__title--is-store proper-icon-location"><?= $escOut->strUnsafe($lang->get('appointment_label_store_location')); ?></label>
                                  <div class="service-section-ctn">
                                      <p class="location-text global-services__location__text global-services__location__text--is-store"><?= $location ?></p>
                                  </div>
                              </div>
                              <?php } ?>

                              <input id="store" name="store" type="hidden" value="<?= $escOut->attr($store_id) ?>">
                              <input id="store_lead" name="store_lead" type="hidden" value="<?= $escOut->htmlEncodeAttr($store_id) ?>">
                              <input type="hidden" name="sf_locale" id="sf_locale" value="<?= $escOut->attr(isset($GET['sf_locale'])?$GET['sf_locale'] : ''); ?>">
                              <?php if (isset($GET['customer_id'])) : ?>
                                  <input id="customer_id" name="customer_id" type="hidden" value="<?= $escOut->htmlEncodeAttr($GET['customer_id']) ?>">
                                  <script>
                                   sf_widget.utils.dataStorage.set({name: 'sf_wdt_customer_id', value: <?= $escOut->json($GET['customer_id']) ?>, expires: <?= $app['configs']['retailer.sale_cookie_expire'] ?>});
                                  </script>
                              <?php endif; ?>
                            <?php endif; ?>

                  <div class="form-elmnt-wrapper global-services__form__element">
                    <label id="appointment-type-radiogroup" class="global-services__form__title global-services__form__title--is-type"><?= $escOut->strUnsafe($lang->get('appointment_label_type')); ?></label>
                    <div class="service-section-ctn services-selector-ctn fn-service">
                      <ul role="radiogroup" aria-labelledby="appointment-type-radiogroup" class="fn-service-list global-services__list">
                        <?php
                            $types = $app['configs']['retailer.services.appointment.types'];
                            $piiObfuscateIsEnabled = $app['configs']['retailer.pii.obfuscate.is_enabled'];
                            $typesFiltered = array_filter($types, function($item) use($piiObfuscateIsEnabled) {
                                return $piiObfuscateIsEnabled ? $item['type'] !== 'phone' : true;
                            });
                            $position = array_column($typesFiltered, 'position');
                            array_multisort($position, SORT_NUMERIC, $typesFiltered);

                            for ($i = 0; $i < count($typesFiltered); $i++) {
                                $type = $typesFiltered[$i]['type'];
                                $isEnabled = $typesFiltered[$i]['is_enabled'];
                                $isDefault = $typesFiltered[$i]['is_default'];
                                $position = $typesFiltered[$i]['position'];
                                $useAlternateLabel = $typesFiltered[$i]['use_alternate_label'];

                                $labelString = 'appointment_label_type_option_' . $type . ($useAlternateLabel ? '_alternate' : '');
                                $label = $type === 'chat' ? str_replace('<br>', ' ', $app['sf.services']->getChatLabel()) : $lang->get($labelString);

                                $inputAttributes = $isDefault ? 'checked required' : ($isEnabled ? '' : 'disabled');

                                echo '<li role="radio" aria-labelledby="' . $type . '-appointment-service-label" class="global-services__list__item' . ($isEnabled ? '' : ' global-services__list__item--is-disabled') . '" tabindex="0">
                                        <input type="radio" name="service" id="' . $type . 'Service" class="fn-trigger-service selectable-type-2 global-services__input" value="' . $type . '" ' . $inputAttributes . '>
                                        <label id="' . $type . '-appointment-service-label" for="' . $type . 'Service" class="global-services__label">
                                            <span class="global-services__label__text">' . $label . '</span>
                                        </label>
                                    </li>';
                            }
                        ?>
                      </ul>
                    </div>
                  </div>
                                    <?= render_specialty_dropdown($user, $lang->get('appointment_label_specialty_dropdown'), $lang->get('appointment_placeholder_specialty_dropdown'), $lang->get('appointment_placeholder_all_specialty_dropdown'), $lang->get('appointment_label_required'), $categoryId, $lang->getLocale()) ?>
                                    <?= $escOut->strUnsafe($lang->get('appointment_label_datetime')); ?>
                                    <div class="service-section-ctn global-services__form__element">
                                        <div class="date-selector-ctn fn-date-selector">
                                            <div class="form-elmnt-wrapper global-services__element__wrapper--is-date-time">
                                                <div class="form-field-ctn global-services__element--is-left global-services__form--is-smb" tabindex="0">
                                                    <div class="horizontal-label-ctn global-services--is-smb">
                                                      <?= $escOut->strUnsafe($lang->get('appointment_label_date_input')); ?>
                                                    </div>
                                                    <div class="global-services__date__icon <?= $escOut->attr($lang->get('appointment_class_icon_calendar')); ?>">
                                                        <input type="text" name="choosenDatePlaceholder" class="fn-date-selector-calendar fn-calendar-i18n global-services__input needsclick"
                                                               data-endtime="<?= $escOut->attr($storeHour['close']) ?>"
                                                               id="choosenDatePlaceholder" value="" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_date_input')); ?>" data-locale="<?= $escOut->attr($lang->getLocale()) ?>" required readonly>
                                                    </div>
                                                    <input type="hidden" name="choosenDate" class="fn-date-selector-calendar-choosen" id="choosenDate" value="">
                                                </div>

                                                <div class="form-field-ctn global-services__element--is-right global-services__form--is-smb" tabindex="0">
                                                    <div class="horizontal-label-ctn global-services--is-smb">
                                                      <?= $escOut->strUnsafe($lang->get('appointment_label_time_input')); ?>
                                                    </div>
                                                    <label class="select-wrapper proper-icon-arrow-down icon-arrow-down global-services__label">
                                                        <select name="choosenTime" class="fn-time-placeholder fn-time-start global-services__select"
                                                                data-starttime="<?= $escOut->attr($storeHour['open']) ?>"  data-endtime="<?= $escOut->attr($storeHour['close']) ?>"
                                                                id="choosenTime" required>
                                                            <option value="" disabled selected><?= $escOut->str($lang->get('appointment_placeholder_time_input')); ?></option>
                                                        </select>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="service-section-ctn">
                                        <div class="form-elmnt-wrapper global-services__form__element global-services__form--is-smb js-mobile-focus-ctn" tabindex="0">
                                            <?= $escOut->strUnsafe($lang->get('appointment_label_name_input')); ?>
                                            <div class="form-field-ctn">
                                                <input type="text" name="name" id="name" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_name_input')); ?>" class="global-services__input js-mobile-focus">
                                            </div>
                                        </div>

                                        <?php if ($textMessageIsEnabled) : ?>
                                            <div class="global-services__form__element global-services__form__switch global-services__form--is-smb">
                                                <div class="global-services__switch__title"><?= $escOut->str($lang->get('appointment_switch_title')); ?></div>
                                                <div class="global-services__switch__options">
                                                    <input class="global-services__switch__radio" type="radio" name="contact_preference" id="contactEmail" value="email" checked>
                                                    <label class="global-services__switch__label" for="contactEmail"><?= $escOut->str($lang->get('appointment_switch_email_label')); ?></label>

                                                    <input class="global-services__switch__radio" type="radio" name="contact_preference" id="contactTextMessage" value="sms">
                                                    <label class="global-services__switch__label" for="contactTextMessage"><?= $escOut->str($lang->get('appointment_switch_text_label')); ?></label>
                                                </div>
                                            </div>

                                            <div class="global-services__form__element global-services__form__element--is-autosubscribe-value global-services__form__element--is-hidden global-services__form--is-smb js-contact-text-message js-mobile-focus-ctn" data-countries-available="<?= $escOut->jsonAttr($app['configs']['retailer.countries.text.available']) ?>" data-countries-preferred="<?= $escOut->jsonAttr($app['configs']['retailer.countries.text.preferred']) ?>" data-countries-preferred-threshold="<?= $escOut->jsonAttr($app['configs']['retailer.countries.preferred.threshold']) ?>">
                                                <?= $escOut->strUnsafe($lang->get('appointment_label_sms_input')); ?>
                                                <input type="text" class="sms global-services__input fn-question-phone-input js-form-element js-mobile-focus" name="sms" id="sms" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_phone_input')); ?>" value="<?= $escOut->htmlEncodeAttr($customer['phone'] ?? '') ?>" aria-labelledby="appointmentFormSmsInputLabel">
                                            </div>
                                        <?php endif; ?>
                                        <input type="hidden" name="country" id="country" value="">

                                        <div class="form-elmnt-wrapper global-services__form__element global-services__form__element--is-autosubscribe-value global-services__form--is-smb js-contact-email js-mobile-focus-ctn" tabindex="0">
                                            <?= $escOut->strUnsafe($lang->get('appointment_label_email_input')); ?>
                                            <div class="form-field-ctn">
                                                <input type="email" class="email global-services__input js-form-element js-mobile-focus" name="email" id="email" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_email_input')); ?>" required>
                                            </div>
                                        </div>

                                        <?php if ($app['configs']['retailer.services.casl']) : ?>
                                            <div class="form-field-ctn autosubscribe-ctn global-services__autosubscribe global-services__autosubscribe--is-casl">
                                                <a href="#casl" class="casl__trigger proper-icon-arrow-down fn-casl-trigger"  data-accepted="<?= $escOut->attr($lang->get("appointment_casl_v1_result_message")); ?>"><?= $escOut->strUnsafe($lang->get('appointment_casl_v1_trigger')); ?></a>
                                                <div class="casl__content-wrapper fn-casl-content">
                                                    <p class="casl__text"><?= $escOut->strUnsafe($lang->get("appointment_casl_v1_text_1")); ?></p>
                                                    <p class="casl__text"><?= $escOut->strUnsafe($lang->get("appointment_casl_v1_text_2")); ?></p>
                                                    <p class="casl__text casl__text--is-minor"><?= $escOut->strUnsafe($lang->get("appointment_casl_v1_text_3")); ?></p>
                                                    <p class="casl__text casl__text--is-minor"><?= $escOut->strUnsafe($lang->get("appointment_casl_v1_text_4", 1, ['%date%' => date('Y')])); ?></p>
                                                    <div class="casl__option-wrapper">
                                                        <input type="checkbox" name="autoSubscribe" id="casl" class="fn-autosubscribe-checkbox global-services__checkbox casl__checkbox">
                                                        <label for="casl" class="global-services__checkbox__text casl__label">
                                                          <?= $escOut->strUnsafe($lang->get('appointment_label_casl_v1_checkbox')); ?>
                                                        </label>
                                                    </div>
                                                    <div class="casl__btn-wrapper">
                                                      <button type="button" class="btn btn-retailer-type-1 global-services__button global-services__button--is-casl-send fn-casl-btn-continue" disabled><?= $escOut->str($lang->get("appointment_button_continue")); ?></button>
                                                      <button type="button" class="btn btn-edit-type-4 global-services__button global-services__button--is-casl-cancel fn-casl-btn-cancel"><?= $escOut->str($lang->get("appointment_button_cancel")); ?></button>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php else : ?>
                                            <div class="form-field-ctn autosubscribe-ctn global-services__autosubscribe js-contact-email">
                                                <?php if($app['configs']['retailer.services.subscription.enabled']) : ?>
                                                    <input type="checkbox" name="autoSubscribe" id="autoSubscribe" class="fn-autosubscribe-checkbox global-services__checkbox">
                                                    <label for="autoSubscribe" class="global-services__checkbox__text" data-tooltip="html-content">
                                                        <?= $escOut->strUnsafe($lang->get('appointment_label_signin_checkbox', 1, ['brand' => $app['configs']['retailer.brand_name'], 'associate_noun' => $app['configs']['retailer.associate']])); ?>
                                                    </label>

                                                  <?php if ($app['configs']['retailer.services.policy']) : ?>
                                                    <p class="global-services__autosubscribe__policy">
                                                      <?= $escOut->strUnsafe($lang->get('appointment_policy')); ?>
                                                    </p>
                                                  <?php endif; ?>
                                                <?php endif;?>
                                                <?php if($app['configs']['retailer.services.termconditions.enabled']) : ?>
                                                  <input type="checkbox" name="autoTermConditions" id="autoTermConditions" class="fn-autosubscribe-checkbox global-services__checkbox">
                                                  <label for="autoTermConditions" class="global-services__checkbox__text" data-tooltip="html-content">
                                                      <?= $escOut->strUnsafe($lang->get('appointment_label_termconditions_checkbox', 1, ['brand' => $app['configs']['retailer.brand_name'], 'associate_noun' => $app['configs']['retailer.associate']])); ?>
                                                      <?= $escOut->strUnsafe($lang->get('appointment_label_terms_and_conditions_full_text')); ?>
                                                  </label>
                                                <?php endif;?>
                                            </div>
                                            <div class="form-field-ctn autosubscribe-ctn global-services__autosubscribe global-services__form__element--is-hidden js-contact-text-message">
                                                <?php if($app['configs']['retailer.services.channel.text.enabled']) : ?>
                                                    <input type="checkbox" name="autoSmsSubscribe" id="autoSmsSubscribe" class="fn-autosubscribe-checkbox global-services__checkbox" aria-labelledby="autoSmsSubscribeLabel">
                                                    <label for="autoSmsSubscribe" class="global-services__checkbox__text" data-tooltip="html-content" id="autoSmsSubscribeLabel">
                                                        <?= $escOut->strUnsafe($lang->get('appointment_label_sms_checkbox', 1, ['brand' => $app['configs']['retailer.brand_name'], 'associate_noun' => $app['configs']['retailer.associate']])); ?>
                                                    </label>

                                                  <?php if ($app['configs']['retailer.services.policy']) : ?>
                                                    <p class="global-services__autosubscribe__policy">
                                                      <?= $escOut->strUnsafe($lang->get('appointment_policy')); ?>
                                                    </p>
                                                  <?php endif; ?>
                                                  <?php if($app['configs']['retailer.services.termconditions.enabled']) : ?>
                                                    <input type="checkbox" name="autoSmsTermConditions" id="autoSmsTermConditions" class="fn-autosubscribe-checkbox global-services__checkbox">
                                                    <label for="autoSmsTermConditions" class="global-services__checkbox__text" data-tooltip="html-content">
                                                        <?= $escOut->strUnsafe($lang->get('appointment_label_termconditions_sms_checkbox', 1, ['brand' => $app['configs']['retailer.brand_name'], 'associate_noun' => $app['configs']['retailer.associate']])); ?>
                                                    </label>
                                                  <?php endif;?>
                                                <?php endif;?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="form-elmnt-wrapper global-services__form__element global-services__form--is-smb js-mobile-focus-ctn <?= $showOptionalPhoneInput ? 'js-general-phone' : 'global-services__form__element--is-hidden js-hide-general-phone' ?>" data-countries-available="<?= $escOut->jsonAttr($app['configs']['retailer.countries.application.available']) ?>" data-countries-preferred="<?= $escOut->jsonAttr($app['configs']['retailer.countries.application.preferred']) ?>">
                                            <?= $escOut->strUnsafe($lang->get('appointment_label_phone_input')); ?>
                                            <div class="form-field-ctn">
                                                <input type="text" name="phone" id="phone" class="phone medium global-services__input js-form-element js-mobile-focus" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_phone_input')); ?>" aria-labelledby="appointmentFormPhoneInputLabel">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-elmnt-wrapper global-services__form--is-textarea global-services__form--is-smb js-mobile-focus-ctn" tabindex="0">
                                        <?= $escOut->strUnsafe(($is_store_user) ? $lang->get('appointment_label_message_textarea_storemode') : $lang->get('appointment_label_message_textarea_repmode')); ?>
                                        <div class="service-section-ctn">
                                            <div class="form-field-ctn">
                                                <textarea name="extraInfo" id="extraInfo" class="global-services__textarea js-mobile-focus"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <footer class="fn-show-footer global-services__footer">
                                    <div class="btn-bar btn-bar-type-6 global-services__footer__container">
                                        <input type="hidden" name="usdoptlcdc" value="<?= $escOut->attr($csrfToken) ?>">
                                        <input type="hidden" class="fn-timezone" name="timezone" id="timezone" value="">
                                        <input type="hidden" name="repname" id="repname" value="<?= $escOut->htmlEncodeAttr($rep) ?>">
                                        <input type="hidden" name="user_id" id="user_id" value="<?= $escOut->attr($user->ID) ?>">
                                        <input type="hidden" name="source_url" id="source_url" value="<?= $escOut->attr($source_url) ?>">
                                        <input type="hidden" name="source_title" id="source_title" value="<?= $escOut->attr($source_title) ?>">
                                        <input type="hidden" name="from" id="from" value="<?= $escOut->attr(isset($GET['from'])?$GET['from'] : 'sidebar'); ?>">
                                        <div class="error-message icon-close hidden" id="appointment-form-error-message"></div>
                                        <button type="submit" class="btn btn-retailer-type-1 fn-trigger-save js-form-btn global-services__button" tabindex="0" disabled><?= $escOut->strUnsafe($lang->get('appointment_button')); ?></button>
                                   </div>
                                    <?php if($app['configs']['retailer.services.casl.v2']) : ?>
                                        <p class="global-services__autosubscribe__policy"><?= $escOut->strUnsafe($lang->get('appointment_casl_v2_mention')); ?></p>
                                        <p class="global-services__autosubscribe__address"><?= $escOut->strUnsafe($lang->get('appointment_casl_v2_address')); ?></p>
                                    <?php endif; ?>

                                    <?php if($app['configs']['retailer.services.casl.v3']) : ?>
                                        <div class="global-services__mention global-services__mention--is-bottom">
                                            <span class="js-casl-v3-email">
                                                <?= $escOut->strUnsafe($lang->get('appointment_casl_v3_email')); ?>
                                            </span>
                                            <span class="global-services__form__element--is-hidden js-casl-v3-text-message">
                                                <?= $escOut->strUnsafe($lang->get('appointment_casl_v3_text_message')); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </footer>
                            </fieldset>

                            <?php
                                $serviceReferer = ($GET['from'] === 'storefront') || ($GET['from'] === 'footer') || ($GET['from'] === 'landing') ? $GET['from'] : 'sidebar';
                                $isFromSidebar = $serviceReferer == 'sidebar';
                            ?>

                            <input id="js-service-referer" type="hidden" name="context" value="<?= $escOut->htmlEncodeAttr($serviceReferer) ?>">
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Select date and time template -->
        <script id="default-date-time-template" type="text/x-handlebars-template">
            <div class="form-field-ctn">
                <input type="text" name="choosenDatePlaceholder" class="fn-date-selector-calendar fn-calendar-i18n needsclick"
                       data-endtime="<?= $escOut->attr($storeHour['close']) ?>"
                       id="choosenDatePlaceholder" value="" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_phone_input')); ?>" data-locale="<?= $escOut->attr($lang->getLocale()) ?>" required readonly>
                <input type="hidden" name="choosenDate" class="fn-date-selector-calendar-choosen" id="choosenDate" value="">
            </div>
            <div class="form-field-ctn">
                <label class="select-wrapper proper-icon-arrow-down icon-arrow-down">
                    <select name="choosenTime" class="fn-time-placeholder fn-time-start"
                            data-starttime="<?= $escOut->attr($storeHour['open']) ?>"  data-endtime="<?= $escOut->attr($storeHour['close']) ?>"
                            id="choosenTime" required>
                        <option value="" disabled selected><?= $escOut->strUnsafe($lang->get('appointment_placeholder_time_input')); ?></option>
                    </select>
                </label>
            </div>
        </script>

        <!-- result message template -->
        <script id="result-message-template" type="text/x-handlebars-template">
            <div class="message global-services__validation">
              <div class="global-services__validation__container">
                <div class="global-services__validation__message proper-icon-checkmark">
                  <span class="global-services__validation__title"><?= $escOut->str($lang->get('appointment_result_message_thanks')); ?></span>
                  <span class="global-services__validation__text"><?= $escOut->str($lang->get('appointment_result_message_response')); ?></span>
                </div>
              </div>
            </div>
        </script>

        <script id="message-box-template" type="text/x-handlebars-template">
            <div class="message-box message-box-{{type}} fn-message-box">
                <div class="message-box-wrapper">
                    <p><strong>{{domain}}</strong> {{message}}</p>
                    <a href="#" class="message-box-close fn-trigger-message-box-close"><i class="icon-close"></i></a>
                </div>
            </div>
        </script>

        <div id="modal-generic-ctn" class="modal-layout-2"></div>
    </body>
</html>
<?php

}

// TODO: Should merge with the function above.

function renderRescheduleForm($meetingObj, $message = null)
{
    global $app, $GET, $escIn, $escOut;

    // The name of the variable is important because it's used down the include path in javascripts.php
    $script = insertAppointmentHours($app);

    // If we come to this link from the rep, we get his login from the cookies
    // SF-20875 - This form is used by reps and customers. We must translate by the preference of the current
    // viewer.
    if (!isset($GET["rep"])){
        $user = wp_get_current_user();
        $rep = $user->user_login;
        $locale = $user->locale;
        $customerReschedule = "<input type='hidden' name='customer' id='customer_reschedule' value = 'false'/>";
    } else {
        $rep = $GET["rep"];
        // We create an hidden input to show it is a reschedule coming from the customer
        $customerReschedule = "<input type='hidden' name='customer' id='customer_reschedule' value = 'true'/>";
        $locale = $meetingObj->locale;
    }

    /** @var \Salesfloor\API\Managers\Services\Appointments $appointmentManager */
    $appointmentManager = $app['appointments.manager'];
    // This is the same form but we do have element which are already defined...
    // Let prepare the field for the content that need to be stored...
    $meetingType = $appointmentManager->getTranslatedTypeByTypeId(
            $meetingObj->event_type,
            $locale
    );

    $meetingValue = Appointment::getMeetingTypeByEventType($meetingObj->event_type)
        ?? Appointment::MEETTING_TYPE_VIDEO;

    // Grab the customer information
    global $wpdb, $app;

    /** @var Appointment $request */
    $request = $app['appointments.manager']->getByUniqId($meetingObj->uniq_id);

    // At the moment, only text request goes in the pre_customer table
    $customerName = $customerEmail = $customerPhone = null;
    if (empty($meetingObj->customer_id) && $request->channel == ServiceInterface::CHANNEL_TEXT) {
        /** @var PreCustomer $preCustomer */
        $preCustomer = $app['pre_customers.manager']->fetchBySource(ServiceInterface::TYPE_APPOINTMENT, $meetingObj->ID);

        // Only with pre_customer information, if there's no linked customer
        if (!empty($preCustomer)) {
            $customerName  = $preCustomer->name;
            $customerEmail = $preCustomer->email;
            $customerPhone = $preCustomer->phone;
        }
    } else {
        $customerDB = $wpdb->get_results("select * from sf_customer where ID = '" . $meetingObj->customer_id . "';");

        /** @var Salesfloor\Services\NameSuggester $nameSuggester */
        $nameSuggester = $app['name_suggester'];

        $customerName = $nameSuggester->formatName('customer_name_reschedule_appt', $app['configs']['retailer.i18n.default_locale'], $customerDB[0]->first_name, $customerDB[0]->last_name);
        $customerEmail = htmlentities($customerDB[0]->email);
        $customerPhone = htmlentities($customerDB[0]->phone);
    }

    /*
     *
         $meeting = $wpdb->get_results("select
    ID,
    customer_id,
    user_id,
    unix_timestamp(date) as apt_date,
    unix_timestamp(enddate) as end_date,
    status,
    timezone,
    cust_meeting_link,
    uniq_id,
    rep_meeting_link,
    notes
    from sf_appointments where uniq_id = '" . $GET["magicid"] . "' and ID = '" . $GET["meetingid"] . "';");
    */

    $meetingNote = $meetingObj->notes;
    $meetingID = $meetingObj->ID;

    $apt_tz = new DateTimeZone($meetingObj->timezone);

    $apt_epoch = $meetingObj->apt_date;

    $apt = new DateTime("@$apt_epoch", $apt_tz);
    $apt->setTimezone($apt_tz);

    $apt_epoch = $meetingObj->end_date;

    $apt_end = new DateTime("@$apt_epoch", $apt_tz);
    $apt_end->setTimezone($apt_tz);

    $jsonRep = json_encode($rep);
    if ($message !== null)
    {
        $jsonMessage = json_encode($message);
        // We have a message to send to the user, at this stage this is only if the meeting is in the pase so
        // the warning is implicit

        $extraMessage =<<<Texte
        <script>
            var Salesfloor = window.Salesfloor || {};
            Salesfloor.Environement = {
                message : {
                    type: "warning",
                    text: $jsonMessage
                },
                repname : $jsonRep
            };
        </script>
Texte;
    }
    else
    {
        $extraMessage =<<<Texte
        <script>
            var Salesfloor = window.Salesfloor || {};
            Salesfloor.Environement = {
                repname : $jsonRep
            };
        </script>
Texte;

    }

/*
service:chat
choosenDate:2014-1-23
choosenDuration:9:00 am
choosenTime:8:30 am
timezone:America/New_York
name:Didier Tremblay
email:<EMAIL>
phone:************
extraInfo:Here a comment
*/

    ?>

<?php
global $app;

$user = sf_get_userinfo($GET['rep']);

/** @var $lang \Salesfloor\Services\Lang $lang */
$lang = $app['service.lang'];
$lang->init([\Salesfloor\Services\Lang::SCOPE_WP_SERVICE_APPOINTMENT], $locale);

$storeIdentifier = $GET['rep'];
$csrfToken = qdcsrf($_SERVER['HTTP_USER_AGENT']);

if ($GET['from'] == 'sidebar') {
    list($storeIdentifier, $storeId) = get_store_identifier($storeIdentifier);
}

$isService = true;
global $app;
$internalFrom = 'service';

$meetingHumanStart = $app['service.multilang']->getLocalizedDate($apt, $locale);
$storeHour = $app['stores.manager']->getStoreAppointmentHours();

include(file_to_include_default_to_generic('../r/includes/', '/' . $lang->getLocale() . '/head.php'));
?>

  <body class="<?= (isset($fullHeight) && $fullHeight) ? "fullheight": ""  ?> version2-0">
    <?= $extraMessage ?>
    <div class="global-services__wrapper global-services__wrapper--is-new-time fn-result-message">
        <div class="global-services__header global-services--is-smb <?= ($GET['from'] != 'sidebar') ? "global-services__header--is-rep" : "" ?>">
          <div class="global-services__title-wrapper">
            <?php if(!$GET['embed'] || $GET['embed'] !== 'landing') : ?>
              <div class="service-navigation-ctn global-services__title__container">
                <a href="<?= $escOut->url(get_home_url() . '/'.$escOut->urlPart($GET['rep'])); ?>" class="back-trigger proper-icon-angle-left fn-navigation-service-back global-services__title__link">
                  <span class="global-services__title__link--is-visible"><?= $escOut->str($lang->get('appointment_back')); ?></span>
                </a>
              </div>
            <?php endif; ?>
            <h1 class="global-services__title"><?= $escOut->str($lang->get('appointment_datetime_new_title')); ?>
              <span class="global-services__sub-title"><?= $escOut->str($lang->get('appointment_new_title')); ?></span>
            </h1>
            </h1>
          </div>
        </div>

      <div class="global-services__table global-services__table--is-storefront-context">
        <div class="global-services__table__row global-services__table__row--is-form">
            <form id="reschedule-form" title="<?= $lang->get('appointment_reschedule_form_title'); ?>" class="default-form fn-date global-services__form">
              <fieldset class="global-services__fieldset">
                <legend><?= $lang->get('appointment_reschedule_form_title'); ?></legend>
                <div>
                  <div class="global-services__form__element global-services__form__element--is-new-time">
                    <div>
                      <h1 class="global-services__title global-services--is-smb"><?= $escOut->str($lang->get('appointment_section_title')); ?>
                        <?php if (trim($customerName)) : ?>
                            <p class="global-services__form__text">
                                <?= $escOut->str($customerName) ?>
                            </p>
                        <?php endif; ?>
                    </div><!--
                    --><div>
                        <?php if (trim($customerPhone)) : ?>
                            <p class="global-services__form__text">
                                <?= $escOut->str($customerPhone) ?>
                            </p>
                        <?php endif; ?>
                    </div><!--
                    --><div>
                        <?php if (trim($customerEmail)) : ?>
                            <p class="global-services__form__text">
                                <?= $escOut->str($customerEmail) ?>
                            </p>
                        <?php endif; ?>
                    </div><!--
                    --><div>
                      <p class="global-services__form__text">
                        <?= $escOut->str($lang->get('appointment_meeting_type', 1, ['type' => htmlspecialchars($meetingType)])); ?>
                      </p>
                    </div><!--
                    --><div>
                      <p class="global-services__form__text global-services__form__text--is-date"><?= $escOut->str($meetingHumanStart) ?></p>
                    </div>
                  </div>

                  <div class="global-services__form__element">
                    <div class="form-elmnt-wrapper fn-date-selector">
                        <div class="form-field-ctn global-services__element--is-left global-services__form--is-smb" tabindex="0">
                          <div class="horizontal-label-ctn global-services--is-smb">
                            <?= $escOut->strUnsafe($lang->get('appointment_label_date_input')); ?>
                          </div>
                            <div class="global-services__date__icon  <?= $escOut->attr($lang->get('appointment_class_icon_calendar')); ?>">
                                <input type="text" name="choosenDatePlaceholder" class="fn-date-selector-calendar fn-calendar-i18n global-services__input needsclick"
                                       data-endtime="<?= $escOut->attr($storeHour['close']) ?>"
                                       id="choosenDatePlaceholder" value="" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_date_input')); ?>" data-locale="<?= $escOut->attr($lang->getLocale()) ?>" required readonly>
                            </div>
                            <input type="hidden" name="choosenDate" class="fn-date-selector-calendar-choosen" id="choosenDate" value="">
                        </div>

                        <div class="form-field-ctn global-services__element--is-right global-services__form--is-smb" tabindex="0">
                          <div class="horizontal-label-ctn global-services--is-smb">
                            <?= $escOut->strUnsafe($lang->get('appointment_label_time_input')); ?>
                          </div>
                            <label class="select-wrapper proper-icon-arrow-down icon-arrow-down global-services__label">
                                <select name="choosenTime" class="fn-time-placeholder fn-time-start global-services__select"
                                        data-starttime="<?= $escOut->attr($storeHour['open']) ?>"  data-endtime="<?= $escOut->attr($storeHour['close']) ?>"
                                        id="choosenTime" required>
                                    <option value="" disabled selected><?= $escOut->str($lang->get('appointment_placeholder_time_input')); ?></option>
                                </select>
                            </label>
                        </div>
                    </div>
                  </div>

                   <div class="form-elmnt-wrapper global-services__form--is-textarea global-services__form--is-smb">
                      <?= $escOut->strUnsafe($lang->get('appointment_label_new_time_textarea')); ?>
                      <div class="service-section-ctn">
                        <div class="form-field-ctn global-services__textarea__container">
                          <textarea name="extraInfo" id="extraInfo" class="global-services__textarea" placeholder="<?= $escOut->attr($lang->get('appointment_placeholder_comments_textarea')); ?>"></textarea>
                            <?=$customerReschedule?>
                        </div>
                      </div>
                    </div>
                </div>
                <footer class="fn-show-footer global-services__footer">
                  <div class="btn-bar btn-bar-type-6 global-services__footer__container">
                    <input type="hidden" name="usdoptlcdc" value="<?= $escOut->attr($csrfToken) ?>">
                    <input type="hidden" class="fn-timezone" name="timezone" id="timezone" value="">
                    <input type="hidden" name="name" id="name" placeholder="Name" value="<?= $escOut->attr($customerName) ?>">
                    <input type="hidden" class="email" name="email" id="email" value="<?= $escOut->attr($customerEmail) ?>">
                    <input type="hidden" name="phone" id="phone" class="phone medium" value="<?= $escOut->attr($customerPhone) ?>">
                    <input type="hidden" name="repname" id="repname" value="<?= $escOut->attr($rep) ?>">
                    <input type="hidden" name="meetingID" id="meetingID" value="<?= $escOut->attr($meetingID) ?>">
                    <input type="hidden" name="service" value="<?= $escOut->attr($meetingValue) ?>">
                    <div class="error-message icon-close hidden" id="appointment-form-error-message"></div>
                    <button type="submit" class="btn btn-retailer-type-1 fn-trigger-save global-services__button"><?= $escOut->str($lang->get('appointment_button_new_time')); ?></button>
                 </div>
                </footer>
              </fieldset>
            </form>
          </div>
        </div>
      </div>

      <!-- result message template -->
      <script id="result-message-template" type="text/x-handlebars-template">
        <div class="message global-services__validation">
          <div class="global-services__validation__container">
            <div class="global-services__validation__message proper-icon-checkmark">
              <span class="global-services__validation__title"><?= $escOut->str($lang->get('appointment_result_message_thanks')); ?></span>
              <span class="global-services__validation__text"><?= $escOut->str($lang->get('appointment_result_message_response')); ?></span>
            </div>
          </div>
        </div>
      </script>

      <script id="message-box-template" type="text/x-handlebars-template">
        <div class="message-box message-box-{{type}} fn-message-box">
          <div class="message-box-wrapper">
            <p><strong>{{domain}}</strong> {{message}}</p>
            <a href="#" class="message-box-close fn-trigger-message-box-close"><i class="icon-close"></i></a>
          </div>
        </div>
      </script>

    <div id="modal-generic-ctn" class="modal-layout-2"></div>
  </body>
</html>
<?php

}

/**
 * Function to render the display to inform the customer that the appointment
 * was successfully accepted.
 * @param Boolean $customer
 * @param String $rep
 */
function renderAcceptDisplay($customer, $rep, $meeting)
{
global $GET, $escIn, $escOut;
$storeIdentifier = $GET['rep'];

if ($GET['from'] == 'sidebar') {
    list($storeIdentifier, $storeId) = get_store_identifier($storeIdentifier);
}

$isService = true;
$internalFrom = 'service';
global $app;

/** @var PublishQueue $publishQueueService */
$publishQueueService = $app['service.push.publish.queue'];

$user = sf_get_userinfo($rep);

$locale = $meeting->locale;
if (!$customer) {
    $locale = $user->locale;
}

/** @var $lang \Salesfloor\Services\Lang $lang */
$lang = $app['service.lang'];
$lang->init([\Salesfloor\Services\Lang::SCOPE_WP_SERVICE_APPOINTMENT], $locale);

include(file_to_include_default_to_generic('../r/includes/', '/' . $lang->getLocale() . '/head.php'));
?>

  <body class="service-page <?= (isset($fullHeight) && $fullHeight) ? "fullheight": ""  ?> version2-0">
    <div class="global-services__wrapper">
        <div class="global-services__header global-services--is-smb <?= ($GET['from'] != 'sidebar') ? "global-services__header--is-rep" : "" ?>">
          <div class="global-services__title-wrapper">
            <h1 class="global-services__title"><?= $app['sf.services']->getAppointmentLabel() ?></h1>
          </div>
        </div>

      <div class="global-services__table global-services__table--is-storefront-context">
        <div class="global-services__table__row">
          <h2 class="global-services__baseline-title">
            <?= ($customer) ? $lang->get('appointment_accepted_message_storefront') : $lang->get('appointment_accepted_message_backoffice'); ?>
          </h2>
        </div>
      </div>
    </div>
    <script>
      window.onload = function() {
        setTimeout( function(){
          <?php
            // If we're coming from a customer email, we redirect to the rep page, else to the rep's backoffice
            if($customer){
              if ($app['configs']['retailer.modular_connect.storefront.is_enabled']) {
                echo "window.location = '/" . $lang->getLocale() . "/" . $escOut->urlPart($rep) . "';";
              } else {
                echo "window.location = '" . $app['configs']['retailer.url'] . "';";
              }
            } else {
              echo "window.location = '/backoffice';";
            }
          ?>
        }, 3000);
      }
    </script>
  </body>
</html>
<?php

    if ($customer) {
        $message = 'pn_accept_appointment_request';
        $inapp = [
            'event_action' => 'accept_appointment',
            'request_id' => 'book_appointment_' . $meeting->ID,
            'alertBox' => 'false'
        ];
        $publishQueueService->publishToReps([$user->ID], $message, $inapp);
    }
}

/**
 * Function to render the display where the customer can confirm that he/she
 * wishes to cancel the appointment.
 * @param String $meetingId
 * @param String $rep
 */
function renderCancelConfirmDisplay($meetingId, $rep, $meetingObj)
{
global $app, $GET, $escIn, $escOut;

// The name of the variable is important because it's used down the include path in javascripts.php
$script = insertAppointmentHours($app);

$storeIdentifier = $GET['rep'];

if ($GET['from'] == 'sidebar') {
    list($storeIdentifier, $storeId) = get_store_identifier($storeIdentifier);
}

global $wpdb, $app;

/** @var Appointment $request */
$request = $app['appointments.manager']->getByUniqId($meetingObj->uniq_id);

$customerName = $customerEmail = $customerPhone = null;
if (empty($meetingObj->customer_id) && $request->channel == ServiceInterface::CHANNEL_TEXT) {
    /** @var PreCustomer $preCustomer */
    $preCustomer = $app['pre_customers.manager']->fetchBySource(
        ServiceInterface::TYPE_APPOINTMENT,
        $meetingObj->ID
    );

    // Only with pre_customer information, if there's no linked customer
    if (!empty($preCustomer)) {
        $customerName  = $preCustomer->name;
        $customerEmail = $preCustomer->email;
        $customerPhone = $preCustomer->phone;
    }
} else {
    $customerDB = $wpdb->get_results("select * from sf_customer where ID = '".$meetingObj->customer_id."';");

    /** @var Salesfloor\Services\NameSuggester $nameSuggester */
    $nameSuggester = $app['name_suggester'];

    $customerName = $nameSuggester->formatName('customer_name_cancel_confirm_appt', $app['configs']['retailer.i18n.default_locale'], $customerDB[0]->first_name, $customerDB[0]->last_name);
    $customerEmail = htmlentities($customerDB[0]->email);
    $customerPhone = htmlentities($customerDB[0]->phone);
}


/** @var \Salesfloor\API\Managers\Services\Appointments $appointmentManager */
$appointmentManager = $app['appointments.manager'];
// This is the same form but we do have element which are already defined...
// Let prepare the field for the content that need to be stored...
$meetingType = $appointmentManager->getTranslatedTypeByTypeId(
    $meetingObj->event_type,
    $meetingObj->locale
);

$apt_tz = new DateTimeZone($meetingObj->timezone);
$apt_epoch = $meetingObj->apt_date;
$apt = new DateTime("@$apt_epoch", $apt_tz);
$apt->setTimezone($apt_tz);

/** @var \Salesfloor\Services\Multilang $multilang */
$multilang = $app['service.multilang'];

$meetingStartDay = $multilang->getLocalizedDate(
        $apt,
        $meetingObj->locale,
        IntlDateFormatter::LONG,
        IntlDateFormatter::NONE
);
$meetingStartTime = $multilang->getLocalizedDate(
    $apt,
    $meetingObj->locale,
    IntlDateFormatter::NONE,
    IntlDateFormatter::LONG
);

$isService = true;
$internalFrom = 'service';
$csrfToken = qdcsrf($_SERVER['HTTP_USER_AGENT']);
global $app;
$user = sf_get_userinfo($rep);

/** @var $lang \Salesfloor\Services\Lang $lang */
$lang = $app['service.lang'];
$locale = $user && $user->store_obj ? $user->store_obj->locale : null;
$lang->init([\Salesfloor\Services\Lang::SCOPE_WP_SERVICE_APPOINTMENT], $locale);

include(file_to_include_default_to_generic('../r/includes/', '/' . $lang->getLocale() . '/head.php'));
?>
  <body class="service-page <?= (isset($fullHeight) && $fullHeight) ? "fullheight": ""  ?> version2-0">
    <div class="global-services__wrapper global-services__wrapper--is-cancel-apt fn-result-message">
        <div class="global-services__header global-services--is-smb <?= ($GET['from'] != 'sidebar') ? "global-services__header--is-rep" : "" ?>">
          <div class="global-services__title-wrapper">
            <?php if(!$GET['embed'] || $GET['embed'] !== 'landing') : ?>
              <div class="service-navigation-ctn global-services__title__container">
                <a href="<?= $escOut->url(get_home_url() . '/'.$escOut->urlPart($GET['rep'])); ?>" class="back-trigger proper-icon-angle-left fn-navigation-service-back global-services__title__link">
                  <span class="global-services__title__link--is-visible"><?= $escOut->str($lang->get('appointment_back')); ?></span>
                </a>
              </div>
            <?php endif; ?>
            <h1 class="global-services__title"><?= $escOut->str($lang->get('appointment_cancel_title')); ?>
              <span class="global-services__sub-title"><?= $escOut->str($lang->get('appointment_cancel_mention')); ?></span>
            </h1>
            </h1>
          </div>
        </div>

      <div class="global-services__table global-services__table--is-storefront-context">
        <div class="global-services__table__row global-services__table__row--is-form">
          <form title="<?= $lang->get('appointment_cancel_form_title'); ?>" class="default-form fn-date global-services__form">
            <fieldset class="global-services__fieldset">
                <legend><?= $lang->get('apppintment_cancel_form_title'); ?></legend>
              <div>
                <div class="global-services__form__element global-services__form__element--is-new-time">
                  <div>
                    <h1 class="global-services__title global-services--is-smb"><?= $escOut->strUnsafe($lang->get('appointment_section_title')); ?>
                    <?php if (trim($customerName)) : ?>
                        <p class="global-services__form__text">
                            <?= $escOut->htmlEncode($customerName) ?>
                        </p>
                    <?php endif; ?>
                  </div><!--
                  --><div>
                      <?php if (trim($customerPhone)) : ?>
                        <p class="global-services__form__text">
                            <?= $escOut->htmlEncode($customerPhone) ?>
                        </p>
                      <?php endif; ?>
                  </div><!--
                  --><div>
                      <?php if (trim($customerEmail)) : ?>
                        <p class="global-services__form__text">
                            <?= $escOut->htmlEncode($customerEmail) ?>
                        </p>
                      <?php endif; ?>
                  </div><!--
                  --><div>
                    <p class="global-services__form__text">
                        <?= $escOut->str($lang->get('appointment_meeting_type', 1, ['type' => htmlspecialchars($meetingType)])); ?>
                    </p>
                  </div><!--
                  --><div>
                    <div class="global-services__element--is-left global-services__form--is-smb">
                      <h4 class="global-services__form__title"><?= $escOut->str($lang->get('appointment_label_details_date')); ?></h4>
                      <p class="global-services__form__fake-input"><?= $escOut->htmlEncode($meetingStartDay) ?></p>
                    </div><!--
                    --><div class="global-services__element--is-right global-services__form--is-smb">
                      <h4 class="global-services__form__title"><?= $escOut->str($lang->get('appointment_label_details_time')); ?></h4>
                      <p class="global-services__form__fake-input"><?= $escOut->htmlEncode($meetingStartTime) ?></p>
                    </div>
                  </div>
                </div>
              </div>
              <footer class="fn-show-footer global-services__footer">
                <div class="btn-bar btn-bar-type-6 global-services__footer__container">
                  <input type="hidden" name="usdoptlcdc" value="<?= $escOut->attr($csrfToken) ?>">
                  <input type='hidden' name='cancelConfirmed' id='cancel_confirmed' value='true'>
                  <input type='hidden' name='meetingId' id='meeting_Id' value="<?= $escOut->attr($meetingId) ?>">
                  <input type='hidden' name='customer' id='customer_reschedule' value='true'>
                  <input type="hidden" name="repname" id="repname" value="<?= $escOut->htmlEncodeAttr($rep) ?>">
                  <div class="error-message icon-close hidden" id="appointment-form-error-message"></div>
                  <button type="submit" class="btn btn-retailer-type-1 fn-trigger-save global-services__button "><?= $escOut->str($lang->get('appointment_button_cancel_apt')); ?></button>
               </div>
              </footer>
            </fieldset>
          </form>
        </div>
      </div>
    </div>

    <!-- result message template -->
    <script id="result-message-template" type="text/x-handlebars-template">
      <h4 class="message"><?= $escOut->str($lang->get('appointment_result_message_cancel')); ?></h4>
    </script>
    <div id="modal-generic-ctn" class="modal-layout-2"></div>
  </body>
</html>
<?php

}


exit;
