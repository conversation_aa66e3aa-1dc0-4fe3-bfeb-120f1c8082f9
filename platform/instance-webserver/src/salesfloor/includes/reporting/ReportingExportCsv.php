<?php

require_once __DIR__ . '/ReportingEngine.php';
require_once __DIR__ . '/ReportingHelpersUsers.php';
// require_once __DIR__ . '/../../wp-config.php';

use Salesfloor\Reporting\Helpers\ReportingHelpersValueFormatter as Helper;
use Salesfloor\Services\Exporter\Sidebar\ContextualWidgetMetricsCsvTemplate;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Reporting\ChatMetrics;
use Salesfloor\Traits\ChatAggregateMetricOptions;

class ReportingExportCsv extends ReportingEngine
{
    use ChatAggregateMetricOptions;

    // SF-20413 output the CSV file in batches to reduce memory usage.
    // Larger batches means less db roundtrips, but more memory use.
    const USER_BATCH_SIZE = 200;

    protected $showAdminColumns = false;
    protected $showCorpColumns = false;
    protected $format = 'csv';
    protected $nullAttribution = 'N/A';
    protected $notApplicable = 'N/A';

    private $wroteHttpHeader = false;
    private $wroteCsvHeader = false;
    private $userBatch;

    const DATASET_PLACEHOLDER = '';

    public function run($db, $app, $queryNewData = true)
    {
        $ret = '';
        $this->wroteCsvHeader = false;

        $type = empty($this->requestsType[0]) ? '' : $this->requestsType[0];

        $users = ReportingHelpersUsers::getAllUsers($db, $type, true);
        foreach (array_chunk($users, self::USER_BATCH_SIZE) as $this->userBatch) {
            $this->users = array_map(function ($u) { return $u['ID']; }, $this->userBatch);
            $ret .= parent::run($db, $app, $queryNewData);
            $this->workingInBatches = true;

            // If we're getting a CSV for stores, the user list is ignored by the
            // reporting engine. Looping over users has the effect of repeating
            // the same report for every batch. To avoid that, break out of this
            // loop if we're producing a report with store data.
            foreach ($this->requestsType as $t) {
                if (strpos($t, '-store-') || $t == 'download-sidebar-metrics' || $t == \ReportingEngine::ENGINE_CONTEXTUAL_WIDGET) {
                    break 2;
                }
            }
        }

        return $ret;
    }

    /**
     * @param $userData
     * @param $columns
     * @return string
     * @deprecated use \ReportingHelpersValueFormat::getTotal
     */
    private function getTotal($userData, $columns)
    {
        $total = 0;

        foreach ($columns as $column) {
            if (isset($userData[$column])) {
                $total += intval($userData[$column]);
            }
        }

        return strval($total);
    }

    /**
     * @param $userData
     * @param $column
     * @return string
     * @deprecated use \ReportingHelpersValueFormat::getDate
     */
    private function getDate($userData, $column)
    {
        if (isset($userData[$column])) {
            return $userData[$column];
        } else {
            return "0000-00-00 00:00:00";
        }
    }

    /**
     * @param $userData
     * @param $column
     * @return string
     * @deprecated use \ReportingHelpersValueFormat::getValue
     */
    private function getValue($userData, $column)
    {
        if (isset($userData[$column])) {
            return strval(intval($userData[$column]));
        } else {
            return "0";
        }
    }

    /**
     * From input ($userDate), calculate a ratio based on $numerator/$denomitator
     *
     * Of course, return 0 if $denominator is empty
     *
     * @param $userData     input from daily stats
     * @param $numerator    value used for the $numerator
     * @param $denominator  value used for the $denominator
     *
     * @return string
     * @deprecated use \ReportingHelpersValueFormat::getRateValue
     */
    public function getRateValue($userData, $numerator, $denominator)
    {
        if (!empty($userData[$denominator])) {
            $numerator = !empty($userData[$numerator]) ? $userData[$numerator] : 0;
            return $this->getFloat($numerator / $userData[$denominator], 2, 100);
        } else {
            return "0";
        }
    }

    /**
     * Get the rate from the data directly not via input. Used to calculate the total
     *
     * @param $numerator
     * @param $denominator
     *
     * @return string
     * @deprecated use \ReportingHelpersValueFormat::getRate
     */
    public function getRate($numerator, $denominator)
    {
        if (!empty($denominator)) {
            return $this->getFloat($numerator/$denominator, 2, 100);
        } else {
            return "0";
        }
    }

    /**
     * Based on a list of columns, add them all together. Used to sum data to a total
     *
     * @param       $userData   input from daily_stats
     * @param array $column     list of columns you want to add together
     *
     * @return mixed
     * @deprecated use \ReportingHelpersValueFormat::addValue
     */
    public function addValue($userData, array $column = [])
    {
        return array_reduce($column, function($acc, $item) use ($userData) {
            if (!empty($userData[$item])) {
                return $acc + (int)$userData[$item];
            } else {
                return $acc;
            }
        }, 0);
    }

    /**
     * Function attempts to find the $column in the $userData provided.
     * Then passes to getFloat() to convert to a string version of a
     * float by applying the $multiplier and rounding to $decimals digits
     * @param  array   $userData   Array representing a row of data
     * @param  string  $column     The name of the column we want returned
     * @param  integer $decimals   Number of decimals to allow in rounding
     * @param  integer $multiplier Number to multiply the result before rounding
     * @return string              String representation of computed float
     * @deprecated use \ReportingHelpersValueFormat::getFloatValue
     */
    private function getFloatValue($userData, $column, $decimals = 2, $multiplier = 1)
    {
        $value = 0;
        if (isset($userData[$column])) {
            $value = $userData[$column];
        }
        return $this->getFloat($value, $decimals, $multiplier);
    }

    /**
     * Function converts the given $value to a string version of a
     * float by applying the $multiplier and rounding to $decimals digits
     * @param  number  $value      Value to be multiplied and rounded
     * @param  integer $decimals   Number of decimals to allow in rounding
     * @param  integer $multiplier Number to multiply the result before rounding
     * @return string              String representation of computed float
     * @deprecated use \ReportingHelpersValueFormat::getFloat
     */
    private function getFloat($value, $decimals = 2, $multiplier = 1)
    {
        $float = floatval($value) * (is_numeric($multiplier) ? $multiplier : 1);
        if (isset($decimals) && is_numeric($decimals)) {
            $float = number_format($float, $decimals, ".", ",");
        }
        return strval($float);
    }

    /**
     * @param $userData
     * @param $column
     * @return string
     * @deprecated use \ReportingHelpersValueFormat::getStrValue
     */
    private function getStrValue ($userData, $column) {
        if (isset($userData[$column])) {
            return strval($userData[$column]);
        }

        return "";
    }

    /**
     * This is only for a specific column, since the logic is different than the rest
     * @param $userDate
     */
    private function calculateCorrespondencePreference($userData)
    {
        $totalQuestionEmail        = $userData['ask_question_req_email'] ?: 0;
        $totalQuestionText         = $userData['ask_question_req_text'] ?: 0;
        $totalAppointmentEmail     = $userData['appointment_req_email'] ?: 0;
        $totalAppointmentText      = $userData['appointment_req_text'] ?: 0;
        $totalPersonalShopperEmail = $userData['personal_shopper_req_email'] ?: 0;
        $totalPersonalShopperText  = $userData['personal_shopper_req_text'] ?: 0;

        $totalEmail = $totalQuestionEmail + $totalAppointmentEmail + $totalPersonalShopperEmail;
        $totalText  = $totalQuestionText + $totalAppointmentText + $totalPersonalShopperText;

        $total = $totalEmail + $totalText;

        $ratioEmail = ($total == 0) ? 0 : ($totalEmail / $total) * 100;
        $totalText  = ($total == 0) ? 0 : ($totalText / $total) * 100;

        // Since we want to get exactly 100% , don't round it for both
        $formattedRatioEmail = number_format($ratioEmail, 2);

        // Don't do this trick if you don't have any request, otherwise, you'll get 100 %
        $formattedRatioText = $totalText ? 100 - $formattedRatioEmail : "0.00";

        return $formattedRatioEmail . ' % (Email) / ' . $formattedRatioText . ' % (Text message)';
    }

    /**
     * @param array $userData
     * @return string
     */
    private function calculatePercentageOfRequestsForwardedToCustomerSupport($userData)
    {
        $totalRequestsExcludeLiveChat = $this->getTotalServiceExcludeLiveChat($userData);
        $totalCustomerSupportRequests = $this->getTotalCsService($userData);

        $customerSupportRequestsRatio = ($totalRequestsExcludeLiveChat == 0) ? 0 : ($totalCustomerSupportRequests / $totalRequestsExcludeLiveChat) * 100;

        $formattedRatio = number_format($customerSupportRequestsRatio);

        return $formattedRatio . '%';
    }

    /**
     * @param array $kpiData
     * @return int $total
     */
    private function getTotalServiceExcludeLiveChat($kpiData)
    {
        $totalQuestionEmail        = $kpiData['ask_question_req_email'] ?: 0;
        $totalQuestionText         = $kpiData['ask_question_req_text'] ?: 0;
        $totalAppointmentEmail     = $kpiData['appointment_req_email'] ?: 0;
        $totalAppointmentText      = $kpiData['appointment_req_text'] ?: 0;
        $totalPersonalShopperEmail = $kpiData['personal_shopper_req_email'] ?: 0;
        $totalPersonalShopperText  = $kpiData['personal_shopper_req_text'] ?: 0;

        $totalEmail = $totalQuestionEmail + $totalAppointmentEmail + $totalPersonalShopperEmail;
        $totalText  = $totalQuestionText + $totalAppointmentText + $totalPersonalShopperText;

        $total = $totalEmail + $totalText;

        return $total;
    }

    /**
     * @param array $kpiData
     * @return int $totalCs
     */
    private function getTotalCsService($kpiData)
    {
        $totalQuestionCsEmail        = $kpiData['ask_question_req_cs_email'] ?: 0;
        $totalQuestionCsText         = $kpiData['ask_question_req_cs_text'] ?: 0;
        $totalAppointmentCsEmail     = $kpiData['appointment_req_cs_email'] ?: 0;
        $totalAppointmentCsText      = $kpiData['appointment_req_cs_text'] ?: 0;
        $totalPersonalShopperCsEmail = $kpiData['personal_shopper_req_cs_email'] ?: 0;
        $totalPersonalShopperCsText  = $kpiData['personal_shopper_req_cs_text'] ?: 0;

        $totalCsEmail = $totalQuestionCsEmail + $totalAppointmentCsEmail + $totalPersonalShopperCsEmail;
        $totalCsText  = $totalQuestionCsText + $totalAppointmentCsText + $totalPersonalShopperCsText;

        $totalCs = $totalCsEmail + $totalCsText;

        return $totalCs;
    }

    private function initMarketingDataset(array $userData, $type = 'user'): array
    {
        $configs = $this->app['configs'];
        $dataset = [];

        if (!$this->isMultibrand && $type == 'store') {
            $dataset[] = static::DATASET_PLACEHOLDER;
        }

        // SF-19789 - Display "-" and not "0" if value is missing
        if ($type == 'store') {
            $dataset[] = $this->getValue($userData, 'outstanding_total'); // Current outstanding leads.
            $dataset[] = $this->getValue($userData, 'sidebar_status'); // Current status of the sidebar for the store.
        }
        $dataset[] = $this->getValue($userData, 'service_total'); // Total Requests
        $dataset[] = Helper::getDurationStr($userData, 'avg_init_resp'); // AVG Init Resp

        $dataset[] = $this->getValue($userData, 'appointment_req'); // Appt Req
        $dataset[] = $this->getValue($userData, 'ask_question_req'); // Ask & Answer Req
        $dataset[] = $this->getValue($userData, 'ask_question_req_chat_handoff'); // Ask & Answer Req
        if ($configs['retailer.has_personal_shopper']) {
            $dataset[] = $this->getValue($userData, 'personal_shopper_req'); // P Shopper Req
        } elseif ($type == 'store') {
            $dataset[] = static::DATASET_PLACEHOLDER;
        }

        // SF-19136 - correspondence preference
        $dataset[] = $this->calculateCorrespondencePreference($userData);

        $dataset[] = $this->getValue($userData, 'chat_request'); // Live Chat Requests
        $dataset[] = $this->getValue($userData, 'chat_answer'); // Live Chat Answers
        $dataset[] = $this->getValue($userData, 'content_total'); // Content Updates

        // PP-165 - Add Lookbook metrics in Store and Associate Activity report
        $dataset[] = $this->getValue($userData, 'lookbook_create'); // Lookbooks created
        $dataset[] = $this->getValue($userData, 'lookbook_update'); // Lookbooks updated

        if ($this->showAdminColumns) {
            $dataset[] = $this->getTotal($userData, ['product_update', 'deal_update']); // Product Updates
            $dataset[] = $this->getTotal($userData, ['event_create', 'event_update']); // Event Updates
            $dataset[] = $this->getTotal($userData, ['content_create', 'content_update']); // Post Updates
        }
        // SF-17372 Reporting for Text Messages
        // Show sent / received number if retailer is enabled
        if ($configs['messaging.text.enabled']) {
            $dataset[] = $this->getValue($userData, 'text_messages_sent'); // Text Messages sent
            $dataset[] = $this->getValue($userData, 'text_messages_received'); // Text Messages received
        } elseif ($type == 'store') {
            $dataset[] = static::DATASET_PLACEHOLDER;
            $dataset[] = static::DATASET_PLACEHOLDER;
        }

        // SF-21171 - New email section
        $dataset[] = $this->getValue($userData, 'total_share_sent');
        $dataset[] = $this->getValue($userData, 'share_email_sent');
        $dataset[] = $this->getRateValue($userData, 'share_email_open', 'share_email_sent') . '%';
        $dataset[] = $this->getValue($userData, 'share_email_click');
        $dataset[] = $this->getRateValue($userData, 'share_email_click', 'share_email_sent') . '%';

        $dataset[] = $this->getValue($userData, 'compose_email_sent');
        $dataset[] = $this->getRateValue($userData, 'compose_email_open', 'compose_email_sent') . '%';
        $dataset[] = $this->getValue($userData, 'compose_email_click');
        $dataset[] = $this->getRateValue($userData, 'compose_email_click', 'compose_email_sent') . '%';

        $dataset[] = $this->getValue($userData, 'request_email_sent');
        $dataset[] = $this->getRateValue($userData, 'request_email_open', 'request_email_sent') . '%';
        $dataset[] = $this->getValue($userData, 'request_email_click');
        $dataset[] = $this->getRateValue($userData, 'request_email_click', 'request_email_sent') . '%';

        $totalEmailSent = $this->addValue($userData, ['request_email_sent', 'compose_email_sent', 'share_email_sent', 'mail_sent']);
        $dataset[] = $totalEmailSent;
        $dataset[] = $this->getRate($this->addValue($userData, ['share_email_open', 'compose_email_open', 'request_email_open', 'mail_open']), $totalEmailSent) . '%';
        $totalEmailClick = $this->addValue($userData, ['share_email_click', 'compose_email_click', 'request_email_click', 'mail_click']);
        $dataset[] = $totalEmailClick;
        $dataset[] = $this->getRate($totalEmailClick, $totalEmailSent) . '%';

        ///// End of new email section ////

        $dataset[] = $this->getValue($userData, 'social_post'); // Social Posts
        $dataset[] = $this->getValue($userData, 'soc_ref'); // Clicks
        $dataset[] = $this->getValue($userData, 'traffic_total'); // Storefront Visits

        if ($this->showAdminColumns) {
            $dataset[] = $this->getValue($userData, 'unique_visitor'); // Unique Visitors
            $dataset[] = $this->getValue($userData, 'com_ref'); // Retailer Ref Shoppers
        }
        $dataset[] = $this->getValue($userData, 'user_add'); // Contacts Added
        $dataset[] = $this->getFloatValue($userData, 'avg_order_value', 2); // Average Order Val
        $dataset[] = $this->getValue($userData, 'n_sales_transactions'); // # Trans
        $dataset[] = $this->getValue($userData, 'n_recommendations'); // Total number of recommendations
        $dataset[] = $this->getFloatValue($userData, 'click_recommended_rate'); // Recommended CTR (against traffic_total / Storefront visits )
        if ($this->showAdminColumns) {
            $dataset[] = $this->getFloatValue($userData, 'click_top_picks_rate'); // Top Picks CTR (against traffic_total / Storefront visits )
            if ($configs['retailer.has_latest_arrivals']) {
                $dataset[] = $this->getFloatValue($userData, 'click_latest_arrivals_rate'); // Latest Arrivals CTR (against traffic_total / Storefront visits )
            } elseif ($type == 'store') {
                $dataset[] = static::DATASET_PLACEHOLDER;
            }
            $dataset[] = $this->getFloatValue($userData, 'avg_selected_top_picks');
            $dataset[] = $this->getFloatValue($userData, 'avg_selected_new_arrivals');
        }

        // SF-17601 Metrics For Tasks: Show to Corp + SF Admins
        $dataset[] = $this->getValue($userData, 'tasks_automated_created');
        $dataset[] = $this->getValue($userData, 'tasks_automated_resolved');
        $dataset[] = $this->getValue($userData, 'tasks_automated_dismissed');
        $dataset[] = $this->getValue($userData, 'tasks_manual_created');
        $dataset[] = $this->getValue($userData, 'tasks_manual_resolved');
        $dataset[] = $this->getValue($userData, 'tasks_manual_dismissed');

        // PP-126 Show Library Share attempts
        $dataset[] = $this->getValue($userData, 'library_share_attempts');

        // SF-23464 Metrics For Tasks Enhancement
        $dataset[] = $this->getValue($userData, 'tasks_system_created');
        $dataset[] = $this->getValue($userData, 'tasks_system_resolved');
        $dataset[] = $this->getValue($userData, 'tasks_system_dismissed');
        $dataset[] = $this->getValue($userData, 'tasks_followup_created');
        $dataset[] = $this->getValue($userData, 'tasks_followup_resolved');
        $dataset[] = $this->getValue($userData, 'tasks_followup_dismissed');
        $dataset[] = $this->getValue($userData, 'tasks_corporate_created');
        $dataset[] = $this->getValue($userData, 'tasks_corporate_resolved');
        $dataset[] = $this->getValue($userData, 'tasks_corporate_dismissed');

        $dataset[] = Helper::getDurationStr($userData, 'tasks_manual_resolved_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_manual_dismissed_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_automated_resolved_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_automated_dismissed_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_system_resolved_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_system_dismissed_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_followup_resolved_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_followup_dismissed_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_corporate_resolved_avg_time');
        $dataset[] = Helper::getDurationStr($userData, 'tasks_corporate_dismissed_avg_time');

        // SF-23763 SocialShop tracking
        $dataset[] = $this->getValue($userData, 'socialshop_post_created');
        $dataset[] = $this->getValue($userData, 'socialshop_total_visit');
        $dataset[] = $this->getValue($userData, 'socialshop_unique_visit');
        $dataset[] = $this->getValue($userData, 'socialshop_product_click');
        $dataset[] = $this->getValue($userData, 'socialshop_storefront_click');
        $dataset[] = $this->getValue($userData, 'socialshop_sales_count');
        $dataset[] = $this->getFloatValue($userData, 'socialshop_sales_amount_total');

        $dataset[] = $this->getValue($userData, 'tasks_corporate_deleted');

        $dataset[] = $this->getValue($userData, 'avg_init_resp_raw'); // AVG Init Resp of seconds

        $dataset[] = $this->calculatePercentageOfRequestsForwardedToCustomerSupport($userData);
        return $dataset;
    }

    /**
     * NOTE : Don't add any new columns in this function, new columns should add in buildMarketingReviseDataSet()
     * @param $data
     * @param $userId
     * @param string $type
     * @return array
     */
    private function buildMarketingDataset($data, $userId, $type = 'user')
    {
        $marketingKey = 'marketing' . ($type === 'store' ? '-store' : '');
        $identifier   = $type . '-' . $userId;

        $userData = $data[$marketingKey][$identifier] ?? [];
        $dataset = $this->initMarketingDataset($userData, $type);
        return $dataset;
    }

    private function buildMarketingReviseDataSet($data, $userId, $type = 'user')
    {
        $marketingKey = 'marketing' . ($type === 'store' ? '-store' : '');
        $identifier   = $type . '-' . $userId;
        $userData = $data[$marketingKey][$identifier] ?? [];
        $dataset = [];

        $dataset[] = $this->getValue($userData, 'scheduled_appointments');
        $dataset[] = $this->getValue($userData, 'cancelled_appointment');

        $dataset[] = $this->getValue($userData, 'video_chat_sessions');
        $dataset[] = Helper::getDurationStr($userData, 'video_chat_duration');

        $dataset[] = $this->getValue($userData, 'virtual_appointment_sessions');
        $dataset[] = Helper::getDurationStr($userData, 'virtual_appointment_duration');

        $dataset[] = $this->getValue($userData, 'grouped_products_shared_email_total');
        $dataset[] = $this->getValue($userData, 'grouped_products_shared_email_avg_products');
        $dataset[] = $this->getValue($userData, 'grouped_products_shared_email_total_products');
        $dataset[] = $this->getValue($userData, 'grouped_products_shared_sms_total');
        $dataset[] = $this->getValue($userData, 'grouped_products_shared_sms_avg_products');
        $dataset[] = $this->getValue($userData, 'grouped_products_shared_sms_total_products');

        // Group tasks;
        $dataset[] = $this->getValue($userData, 'group_tasks_created');
        $dataset[] = $this->getValue($userData, 'group_tasks_resolved');
        $dataset[] = $this->getValue($userData, 'group_tasks_dismissed');
        $dataset[] = $this->getValue($userData, 'group_tasks_resolved_sum_time');
        $dataset[] = $this->getValue($userData, 'group_tasks_dismissed_sum_time');
        return $dataset;
    }

    private function buildSalesDataset($data, $userId)
    {
        $dataset = [];

        $dataset[] = isset($data['id']) ? strval($data['id']) : '';
        $dataset[] = $this->getDate($data, 'utcdate');
        $dataset[] = isset($data['total']) ? strval(floatval($data['total'])) : '0';
        // SF-17158 Expose transaction report to CORP admins and hide certain columns
        if ($this->showAdminColumns) {
            $dataset[] = isset($data['apply']) ? strval(floatval($data['apply'])) : '0';
        }
        $dataset[] = isset($data['origin']) ? $data['origin'] : '';
        if ($this->showAdminColumns) {
            $dataset[] = isset($data['products']) ? strval($data['products']) : '';
            $dataset[] = isset($data['quantities']) ? strval($data['quantities']) : '';
            $dataset[] = isset($data['names']) ? strval($data['names']) : '';
            $dataset[] = isset($data['acquisition']) ? strval($data['acquisition']) : '';
        }
        $dataset[] = isset($data['attribution']) ? strval($data['attribution']) : $this->nullAttribution;
        $dataset[] = isset($data['customer_name']) ? strval($data['customer_name']) : $this->nullAttribution;
        $dataset[] = isset($data['customer_email']) ? strval($data['customer_email']) : $this->nullAttribution;
        $dataset[] = isset($data['customer_id']) ? strval($data['customer_id']) : $this->nullAttribution;
        $dataset[] = $this->getDate($data, 'received_date');
        $dataset[] = isset($data['trx_type']) ? $data['trx_type'] : '';

        return $dataset;
    }

    private function buildSalesTotalDataset($data, $userId, $type = 'user')
    {
        if ($this->showAdminColumns) {
            $dataset = ["0", "0"];
        } else {
            $dataset = ["0"];
        }
        $salesKey = 'sales_total' . ($type === 'store' ? '_store' : '');
        $identifier   = $type . '-' . $userId;

        if (isset($data[$salesKey][$identifier])) {
            $userData = $data[$salesKey][$identifier];
            $dataset = [$this->getFloatValue($userData, 'total', 2)];
            if ($this->showAdminColumns) {
                $dataset[] = $this->getFloatValue($userData, 'tot_comm', 2);
            }
        }

        return $dataset;
    }

    private function buildSalesDetailsDataset($data, $userId)
    {
        $dataset = [];

        $dataset[] = isset($data['trx_id']) ? strval($data['trx_id']) : '';
        $dataset[] = $this->getDate($data, 'date');
        $dataset[] = isset($data['product_id']) ? strval($data['product_id']) : '';
        $dataset[] = isset($data['name']) ? strval($data['name']) : '';
        $dataset[] = $this->getValue($data, 'quantity');
        $dataset[] = $this->getFloatValue($data, 'trx_detail_total');
        if ($this->showAdminColumns) {
            $dataset[] = $this->getFloatValue($data, 'trx_detail_apply_total');
            $dataset[] = $data['origin'];
            $dataset[] = $data['acquisition'];
        }
        $dataset[] = isset($data['attribution']) ? strval($data['attribution']) : $this->nullAttribution;
        $dataset[] = isset($data['customer_id']) ? strval($data['customer_id']) : $this->nullAttribution;
        $dataset[] = $this->getDate($data, 'received_date');
        $dataset[] = isset($data['trx_type']) ? $data['trx_type'] : '';
        $dataset[] = Helper::getStrValue($data, 'variant_id');

        return $dataset;
    }

    private function buildCustomersDataset($data, $userId)
    {
        $dataset = [];

        $dataset[] = isset($data['email']) ? strval($data['email']) : '';
        $dataset[] = isset($data['first_name']) ? strval($data['first_name']) : '';
        $dataset[] = isset($data['last_name']) ? strval($data['last_name']) : '';
        $dataset[] = isset($data['subcribtion_flag']) ? intval($data['subcribtion_flag']) : '0';

        return $dataset;
    }

    private function buildDownloadUserDataset($data, $userId)
    {
        $dataset = [];

        $getTotalPhoneNumber = function($data) {
            $active = (int)$data[ReportingProcessorUsers::FIELD_ACTIVE_PHONE_NUMBER]; // 0 - 1
            $nbDeleted = (int)$data[ReportingProcessorUsers::FIELD_DUPLICATE_PHONE_NUMBER];

            return $active + $nbDeleted;
        };

        $dataset[] = isset($data['selling_mode']) ? strval($data['selling_mode']) : '';
        $dataset[] = isset($data['user_status']) ? strval($data['user_status']) : '';
        $dataset[] = isset($data['time_active']) ? intval($data['time_active']) >= REPORT_DOWNLOADUSER_THRESHOLD_ACTIVE ? 'true' : 'false' : 'false';
        $dataset[] = isset($data['time_active']) ? floor(intval($data['time_active']) / 3600) : '0';
        $dataset[] = isset($data['user_registered']) ? strval($data['user_registered']) : '';

        // Add total of phone number (sms) the rep has. Current + past
        $dataset[] = $getTotalPhoneNumber($data);

        return $dataset;
    }

    private function buildChatUserMetricsDataset($data, $userId)
    {
        $dataset = [];

        $numAnswered = isset($data['chat_answer']) ? intval($data['chat_answer']) : 0;
        $numRequests = isset($data['chat_request']) ? intval($data['chat_request']) : 0;
        $abandoned = $data['chat_abandoned'] ?? 0;
        $numberSecondsAvailable = isset($data['number_seconds_available']) ? intval($data['number_seconds_available']) : 0;

        $dataset[] = max($numRequests - $abandoned - $numAnswered, 0);
        $dataset[] = $numRequests;
        $dataset[] = $numAnswered;
        $denom = $numRequests - $abandoned;
        $dataset[] = $denom <= 0 ? 0 : round(($numAnswered / $denom) * 100);
        $dataset[] = round($numberSecondsAvailable / 60);
        $dataset[] = $this->calculateAverageUserAvailability($this->app['service.util'], [
            'numberSecondsAvailable' => $numberSecondsAvailable,
            'startDate' => $this->dateFrom,
            'endDate' => $this->dateTo,
        ]);
        // PP-345 Chats received but answered by another
        $dataset[] = $this->getValue($data, 'received_chats_answered_by_other');

        // Chat security
        $dataset[] = $this->getValue($data, 'flagged_chat_conversations');

        // Abandons
        $dataset[] = $abandoned;

        if ($this->showAdminColumns) {
            // Avg answer time
            $dataset[] = $data['chat_answer'] == 0 ? 0 : $data['chat_answer_time'] / $data['chat_answer'];

        }

        return $dataset;
    }

    /**
     * SF-16958 Access Widget CTR From Reporting
     * Function returns the complete data set array for the Sidebar Metrics report
     * Each entry represents a day and their equivalent views / clicks / ctr and would
     * become a single row in the exported CSV
     *
     * Contrary to other build***Dataset functions, this one is not tied to $userId, and
     * handles ALL the data and not just row by row
     * header columns list at: \ReportingExportCsv::buildDownloadSidebarMetricsHeader
     * @param  array $rows All row (date) data from sf_elb_stats
     * @return array       Formatted / manipulated data for all the information rows in the CSV
     */
    private function buildDownloadSidebarMetricsDataset($rows)
    {
        $completeDataSet = [];

        if ($this->showCorpColumns) {
            // This will guarantee csv will always show the latest date's stats in descending order.
            // We shouldn't need this in prod for now, but in the future, if we decide to stop populating
            // sf_elb_stats table, array_merge_recursive function from the SidebarStatsAggregator will put the new stats at the end
            // of the array and we need to sort the returned array.
            if (!empty($rows) && count($rows) > 2) {
                usort($rows, function ($a, $b) {
                    return strcmp($b['date'], $a['date']);
                });
            }

            foreach ($rows as $row) {
                $dataset = [];

                // Insert data into the dataset in the appropriate order
                $dataset[] = isset($row['date']) ? $row['date'] : $this->notApplicable;

                $totalUniqueSidebarViews = $row['sidebar_mobile_unique_view'] + $row['sidebar_desktop_unique_view'];
                $dataset[] = $totalUniqueSidebarViews;
                $TotalUniqueSidebarClicks = $row['sidebar_mobile_unique_click'] + $row['sidebar_desktop_unique_click'];
                $dataset[] = $TotalUniqueSidebarClicks;
                $totalClickThroughRate = ($totalUniqueSidebarViews > 0) ? $this->getFloat(
                    $TotalUniqueSidebarClicks / $totalUniqueSidebarViews,
                    2,
                    100
                ) : $TotalUniqueSidebarClicks;
                $dataset[] = $totalClickThroughRate . '%';

                $dataset[] = $row['sidebar_mobile_unique_view'];
                $dataset[] = $row['sidebar_mobile_unique_click'];
                $dataset[] = $row['sidebar_mobile_unique_minimize'];
                $dataset[] = $row['sidebar_mobile_unique_maximize'];
                $dataset[] = $row['sidebar_mobile_unique_tagline_minimize'];

                $dataset[] = $row['sidebar_mobile_total_view'];
                $dataset[] = $row['sidebar_mobile_total_click'];
                $dataset[] = $row['sidebar_mobile_total_minimize'];
                $dataset[] = $row['sidebar_mobile_total_maximize'];
                $dataset[] = $row['sidebar_mobile_total_tagline_minimize'];

                $dataset[] = $row['sidebar_desktop_unique_view'];
                $dataset[] = $row['sidebar_desktop_unique_click'];
                $dataset[] = $row['sidebar_desktop_unique_minimize'];
                $dataset[] = $row['sidebar_desktop_unique_maximize'];

                $dataset[] = $row['sidebar_desktop_total_view'];
                $dataset[] = $row['sidebar_desktop_total_click'];
                $dataset[] = $row['sidebar_desktop_total_minimize'];
                $dataset[] = $row['sidebar_desktop_total_maximize'];

                $dataset[] = empty($row['sidebar_desktop_total_view']) ? 0 : Helper::getFloat(
                    $row['sidebar_desktop_total_click'] / $row['sidebar_desktop_total_view'],
                    2,
                    100
                ) . '%';

                $dataset[] = empty($row['sidebar_desktop_unique_view']) ? 0 : Helper::getFloat(
                    $row['sidebar_desktop_unique_click'] / $row['sidebar_desktop_unique_view'],
                    2,
                    100
                ) . '%';

                $dataset[] = empty($row['sidebar_mobile_total_view']) ? 0 : Helper::getFloat(
                    $row['sidebar_mobile_total_click'] / $row['sidebar_mobile_total_view'],
                    2,
                    100
                ) . '%';

                $dataset[] = empty($row['sidebar_mobile_unique_view']) ? 0 : Helper::getFloat(
                    $row['sidebar_mobile_unique_click'] / $row['sidebar_mobile_unique_view'],
                    2,
                    100
                ) . '%';

                if (empty($row['sidebar_desktop_total_view'] + $row['sidebar_mobile_total_view'])) {
                    $dataset[] = 0;
                } else {
                    $dataset[] = Helper::getFloat(
                        ($row['sidebar_desktop_total_click'] + $row['sidebar_mobile_total_click']) / ($row['sidebar_desktop_total_view'] + $row['sidebar_mobile_total_view']),
                        2,
                        100
                    ) . '%';
                }

                $completeDataSet[] = $dataset;
            }
        }

        return $completeDataSet;
    }

    /**
     * Access Contextual Widget From Reporting
     *
     * Function returns the complete data set array for the Contextual Metrics report
     * Each entry represents a day and their equivalent views / clicks / ctr and would become a single row in the exported CSV
     *
     * Contrary to other build***Dataset functions, this one is not tied to $userId, and
     * handles ALL the data and not just row by row
     * header columns list at: \ReportingExportCsv::buildDownloadContextualWidgetMetricsHeader
     * @param array $rows All row (date) data
     * @return array       Formatted / manipulated data for all the information rows in the CSV
     */
    private function buildDownloadContextualWidgetMetricsDataset($rows)
    {
        return  ContextualWidgetMetricsCsvTemplate::buildCsvDataset($rows, $this->showCorpColumns);
    }

    /**
     * SF-16958 Access Widget CTR From Reporting
     * Function returns the array representing all of the Column Names for
     * the SidebarMetrics CSV Export, in the requested order
     * @return array Ordered columns for SidebarMetrics Export
     * TODO : Refactor by moving headers to SidebarMetricsExporter()
     */
    private function buildDownloadSidebarMetricsHeader()
    {
        $headers = [];

        // we don't actually need showCorpColumns by now, just use this property to keep role more clear
        if ($this->showCorpColumns) {
            // csv columns could split to 3 section: 1 sidebar kpi section, 2 contextual widgets section, 3 sidebar + Contextual widgets

            $extraHeaders = [
                'Date',
                'Total Unique Sidebar Views',
                'Total Unique Sidebar Clicks',
                'Total Unique Click-Through Rate',

                'Unique Mobile Sidebar Views',
                'Unique Mobile Sidebar Clicks',
                'Unique Mobile Sidebar Minimize',
                'Unique Mobile Sidebar Maximize',
                'Unique Mobile Sidebar Tagline Minimize',

                'Mobile Sidebar Views',
                'Mobile Sidebar Clicks',
                'Mobile Sidebar Minimize',
                'Mobile Sidebar Maximize',
                'Mobile Sidebar Tagline Minimize',

                'Unique Desktop Sidebar Views',
                'Unique Desktop Sidebar Clicks',
                'Unique Desktop Sidebar Minimize',
                'Unique Desktop Sidebar Maximize',

                'Desktop Sidebar Views',
                'Desktop Sidebar Clicks',
                'Desktop Sidebar Minimize',
                'Desktop Sidebar Maximize',

                'Click-Through Rate Desktop',
                'Unique Click-Through Rate Desktop',
                'Click-Through Rate Mobile',
                'Unique Click-Through Rate Mobile',
                'Click-Through Rate Total',
            ];

            $headers = array_merge($headers, $extraHeaders);
        }

        return $headers;
    }

    private function buildDownloadContextualWidgetMetricsHeader()
    {
        return ContextualWidgetMetricsCsvTemplate::buildCsvHeader($this->showCorpColumns);
    }

    private function buildChatStoreMetricsDataset($data, $store)
    {
        $configs = $this->app['configs'];
        $dataset = [];
        $handle = function ($key) use ($data) {
            return (int)($data[$key] ?? 0);
        };

        $numAnswered = $handle('chat_answer');
        $numRequests = $handle('chat_request');
        $numberSecondsAvailable = $handle('number_seconds_available');
        $abandoned = $handle('chat_abandoned');
        if (!empty($configs['retailer.brand'])) {
            $dataset[] = $configs['retailer.brand_name'];
        }

        $dataset[] = max($numRequests - $abandoned - $numAnswered, 0);
        $dataset[] = $numRequests;
        $dataset[] = $numAnswered;
        $denom = $numRequests - $abandoned;
        $dataset[] = $denom <= 0 ? 0  : round(($numAnswered / $denom) * 100);
        $dataset[] = round($numberSecondsAvailable / 60);
        $dataset[] = $this->calculateAverageStoreAvailability($this->app['service.util'], [
            'numberSecondsAvailable' => $numberSecondsAvailable,
            'startDate' => $this->dateFrom,
            'endDate' => $this->dateTo,
        ]);
        // PP-345 Chats received but answered by another
        $dataset[] = $this->getValue($data, 'received_chats_answered_by_other');

        $dataset[] = $this->getValue($data, 'flagged_chat_conversations');

        // Abandons - the total number of abandons.
        $dataset[] = $abandoned;
        // duration before abandon
        $dataset[] = $handle(ChatMetrics::METRIC_ABANDON_0_29);
        $dataset[] = $handle(ChatMetrics::METRIC_ABANDON_30_59);
        $dataset[] = $handle(ChatMetrics::METRIC_ABANDON_60_89);
        $dataset[] = $handle(ChatMetrics::METRIC_ABANDON_90_120);

        if ($this->showAdminColumns) {
            // Avg answer time
            $dataset[] = $data['chat_answer'] == 0 ? 0 : $data['chat_answer_time'] / $data['chat_answer'];
        }
        return $dataset;
    }

    private function getNewRow($user, $item = null)
    {
        $userDataset = [];

        $userDataset[] = $user['first_name'] . " " . $user['last_name'];
        if ($this->showAdminColumns) {
            $userDataset[] = $user['ID'];
        }
        $userDataset[] = (isset($user['user_status']) ? ($user['user_status'] ? 'Active' : 'Disabled') : $this->notApplicable);
        $userDataset[] = $user['user_email'];
        $userDataset[] = $user['employee_id'];
        $userDataset[] = $user['user_login'];
        $userDataset[] = $user['group'];
        $userDataset[] = $user['shame_type'];

        if(!is_null($item)){
            $userDataset[] = $item['store_id'];
            $userDataset[] = $item['retailer_store_id'];
            $userDataset[] = $item['store_name'];
        }else{
            $userDataset[] = $user['store_id'];
            $userDataset[] = $user['retailer_store_id'];
            $userDataset[] = $user['name'];
        }

        if ($this->isMultibrand) {
          $userDataset[] = $this->getStrValue($user, 'brand');
        }

        // Only add those field when you export associated information
        if (in_array('get-users', $this->requestsType)) {
            $userDataset[] = $this->app['configs']['salesfloor_storefront.host'] . '/' . ($user['user_alias'] ?: $user['user_login']);
            $userDataset[] = $this->app['cloudinary']->getAvatar($user['user_login'], $this->app['configs']['retailer.avatar_transform']);
            $userDataset[] = $user['rep_introduction'];
            $userDataset[] = $user['phone'];
            $userDataset[] = $user['sms_number'];
            $userDataset[] = $user['specialties'];
        }

        return $userDataset;
    }

    private function getNewStoreRow($store)
    {
        $storeDataset = [];

        $storeDataset[] = $store['name'];
        $storeDataset[] = $store['store_id'];
        $storeDataset[] = $store['retailer_store_id'];

        if ($this->isMultibrand) {
          $storeDataset[] = $this->getStrValue($store, 'brand');
        }

        return $storeDataset;
    }

    private function buildDataset($data, $users)
    {
        $dataset = [];

        foreach ($users as $user) {
            if (in_array('get-users', $this->requestsType)) {
                $dataset[] = $this->getNewRow($user);
            } else if (in_array('get-sales', $this->requestsType)) {
                if (isset($data['sales'])) {
                    foreach ($data['sales'] as $item) {
                        if ($item['user_id'] == $user['ID']) {
                            $userDataset = array_merge($this->getNewRow($user, $item), $this->buildSalesDataset($item, $user['ID']));
                            $dataset[] = $userDataset;
                        }
                    }
                }
            } else if (in_array('get-sales-details', $this->requestsType)) {
                if (isset($data['sales_details'])) {
                    foreach ($data['sales_details'] as $item) {
                        if ($item['user_id'] == $user['ID']) {
                            $userDataset = array_merge($this->getNewRow($user, $item), $this->buildSalesDetailsDataset($item, $user['ID']));
                            $dataset[] = $userDataset;
                        }
                    }
                }
            } else if (in_array('get-customers', $this->requestsType)) {
                if (isset($data['customers'])) {
                    foreach ($data['customers'] as $item) {
                        if ($item['user_id'] == $user['ID']) {
                            $userDataset = array_merge($this->getNewRow($user), $this->buildCustomersDataset($item, $user['ID']));
                            $dataset[] = $userDataset;
                        }
                    }
                }
            } else if (in_array('download-users', $this->requestsType)) {
                if (isset($data['users'])) {
                    foreach ($data['users'] as $item) {
                        if ($item['ID'] == $user['ID']) {
                            $userDataset = array_merge($this->getNewRow($user), $this->buildDownloadUserDataset($item, $user['ID']));
                            $dataset[] = $userDataset;
                        }
                    }
                }
            } else {
                $userDataset = $this->getNewRow($user);

                foreach ($this->requestsType as $requestType) {
                    switch ($requestType) {
                        case 'get-marketing':
                            $userDataset = array_merge($userDataset, $this->buildMarketingDataset($data, $user['ID']));
                            break;
                        case 'get-marketing-revise':
                            $userDataset = array_merge($userDataset, $this->buildMarketingReviseDataSet($data, $user['ID']));
                            break;
                        case 'get-sales':
                            $userDataset = array_merge($userDataset, $this->buildSalesDataset($data, $user['ID']));
                            break;
                        case 'get-sales-total':
                            $userDataset = array_merge($userDataset, $this->buildSalesTotalDataset($data, $user['ID']));
                            break;
                        case 'get-sales-details':
                            break;
                        case 'get-chat-user-metrics':
                            // This reports / buildDataset doesn't handle data extraction. Do it before
                            $dataUser = $data['chat']["user-{$user['ID']}"] ?? [];
                            $userDataset = array_merge($userDataset, $this->buildChatUserMetricsDataset($dataUser, $user['ID']));
                    }
                }

                $dataset[] = $userDataset;
            }
        }

        return $dataset;
    }

    private function buildDatasetForStore($data, $stores)
    {
        $dataset = [];
        foreach ($stores as $store) {
            $storeDataset = $this->getNewStoreRow($store);
            foreach($this->requestsType as $requestType) {
                switch ($requestType) {
                    case 'get-store-marketing':
                        $storeDataset = array_merge($storeDataset, $this->buildMarketingDataset($data, $store['store_id'], 'store'));
                        break;
                    case 'get-store-marketing-revise':
                        $storeDataset = array_merge($storeDataset, $this->buildMarketingReviseDataSet($data, $store['store_id'], 'store'));
                        break;
                    case 'get-store-sales-total':
                        $storeDataset = array_merge($storeDataset, $this->buildSalesTotalDataset($data, $store['store_id'], 'store'));
                        break;
                    case 'get-chat-store-metrics':
                        $dataStore = $data['chat-store']["store-{$store['store_id']}"] ?? [];
                        $storeDataset = array_merge($storeDataset, $this->buildChatStoreMetricsDataset($dataStore, $store['store_id'], 'store'));
                        break;
                }
            }
            $dataset[] = $storeDataset;
        }
        return $dataset;
    }

    protected function outputData($data, $db)
    {
        if (!is_array($data)) {
            return $data;
        }

        $this->evaluatePermissions();
        if (array_key_exists('chat-store', $data) || array_key_exists('marketing-store', $data)) {
            $stores = ReportingHelpersUsers::getAllStores($db);
            $data   = $this->buildDatasetForStore($data, $stores);

        } else if (in_array('download-sidebar-metrics', $this->requestsType)) {
            // SF-16958 Access Widget CTR From Reporting
            // Dataset does not rely on User / Store, so it is built in standalone function
            $data = $this->buildDownloadSidebarMetricsDataset($data['users']);
        } elseif (in_array(\ReportingEngine::ENGINE_CONTEXTUAL_WIDGET, $this->requestsType)) {
            $data = $this->buildDownloadContextualWidgetMetricsDataset($data['users']);
        } elseif ($this->userBatch) {
            $data = $this->buildDataset($data, $this->userBatch);
        } else {
            $type = empty($this->requestsType[0]) ? '' : $this->requestsType[0];

            $users  = ReportingHelpersUsers::getAllUsers($db, $type, true);
            $data   = $this->buildDataset($data, $users);
        }

        $formattedData = "";

        if (!$this->wroteCsvHeader) {
            $formattedData .= $this->ouputDataHeader();
            $formattedData .= "\n";
            $this->wroteCsvHeader = true;
        }

        foreach ($data as $line) {
            for ($i = 0; $i < sizeof($line); $i++) {
                $formattedData .= "\"" . str_replace('"', '""', $line[$i]) . "\"";
                if ($i + 1 < sizeof($line)) {
                    $formattedData .= ",";
                }
            }
            $formattedData .= "\n";
        }

        return $formattedData;
    }

    protected function setDocumentHeader($app, $contentType = 'text/csv', $filename = null)
    {
        if ($this->wroteHttpHeader) {
            return;
        }

        $this->evaluatePermissions();
        if (!$this->returnCompleteData) {
            header("Content-type: $contentType");
            header('Content-disposition: attachment;filename=' . (empty($filename) ? $this->getCSVFilename($app['configs']) : $filename));
        }

        $this->wroteHttpHeader = true;
    }

    /**
     * Evaluates and sets any necessary Permissions for the file
     * @return
     */
    protected function evaluatePermissions()
    {
        $this->showAdminColumns = Perms::userHasPermission($this->currentUser, 'show-admin-cols');
        $this->showCorpColumns  = Perms::userHasPermission($this->currentUser, 'show-corp-admin-cols');
    }

    /**
     * Generate Appropriate Filename for the Export
     * @param  array $overrideConfigs Array of system / retailer information to use instead of saved one
     * @param  boolean $includeDates  Whether or not to include dates in the filename
     * @param  boolean $includeFormat Whether or not to include the extension in the filename
     * @return string             Filename for export
     */
    public function getCSVFilename($overrideConfigs = [], $includeDates = true, $includeFormat = true)
    {
        $configs = $this->app['configs'];

        $retailerShort = 'RETAILER';
        $env = 'ENV';

        if (!empty($overrideConfigs)) {
            $configs = $overrideConfigs;
        }
        if (!empty($configs['retailer.short_name'])) {
            $retailerShort = $configs['retailer.short_name'];
        }
        if (!empty($configs['env'])) {
            $env = $configs['env'];
        }

        $this->evaluatePermissions();
        $retailer  = strtoupper($retailerShort);
        $dateFrom  = !empty($this->dateFrom) ? date('Y-m-d', strtotime($this->dateFrom)) : '';
        $dateTo    = !empty($this->dateTo) ? date('Y-m-d', strtotime($this->dateTo)) : '';
        $format    = '.csv';
        $userGroup = ($this->currentUser->group == Perms::SF_ADMIN ? 'SF' : 'CORP');
        $showEnv   = strtoupper(($env != 'prd' ? $env . '_' : ''));

        $nameNoExt = $showEnv . $retailer . '_' . $userGroup . '_';

        if (in_array('download-users', $this->requestsType)) {
            $nameNoExt .= 'Salesfloor_AllUserExport' . date('Ymd') . '_' . date('MY', strtotime($this->dateFrom));
        } else if (in_array('download-sidebar-metrics', $this->requestsType)) {
            $nameNoExt .= 'Salesfloor_SidebarMetrics';
        } elseif (in_array(\ReportingEngine::ENGINE_CONTEXTUAL_WIDGET, $this->requestsType)) {
            $nameNoExt .= 'Salesfloor_ContextualWidgetMetrics';
        } else {
            if (in_array('get-marketing', $this->requestsType)) {
                $nameNoExt .= 'ActivitySummary_Associates';
            } elseif (in_array('get-store-marketing', $this->requestsType)) {
                $nameNoExt .= 'ActivitySummary_Stores';
            } elseif (in_array('get-sales', $this->requestsType)) {
                $nameNoExt .= 'Transactions';
            } elseif (in_array('get-sales-details', $this->requestsType)) {
                $nameNoExt .= 'TransactionDetails';
            } elseif (in_array('get-chat-user-metrics', $this->requestsType)) {
                $nameNoExt .= 'LiveChatMetrics_Associates';
            } elseif (in_array('get-chat-store-metrics', $this->requestsType)) {
                $nameNoExt .= 'LiveChatMetrics_Stores';
            } elseif (in_array('get-users', $this->requestsType)) {
                $nameNoExt .= 'Associates';
            } elseif (in_array('get-chat-log', $this->requestsType)) {
                $nameNoExt .= 'YearlyChatLog';
            } else {
                $nameNoExt .= 'Export';
            }

            if ($includeDates) {
                $nameNoExt .= '_' . $dateFrom . '_' . $dateTo;
            }
        }

        return $nameNoExt . ($includeFormat ? $format : '');
    }

    /**
     * Rewrite a csv file to contain only the specified columns.
     *
     * Some retailers consume our CSV exports automatically. It's important for them
     * that we don't change the set of columns in these files without warning. This
     * function makes coordination easier: we can keep the set of exported columns
     * fixed until they are ready to accept new columns.
     *
     * @param $fileName string the CSV to potentially rewrite
     * @param $header string the list of desired columns, in a format understood by
     *        `xsv select`. If falsy, the CSV file is left unchanged.
     *
     * This function uses [xsv](https://github.com/BurntSushi/xsv) to rewrite the
     * csv file. Fortunately, xsv accepts a copy of our CSV header row as a spec
     * of which columns to select. This makes it very simple to define the list
     * of columns to export: just copy the first line of the file the retailer is
     * set on using.
     */
    public static function selectColumns($fileName, $header)
    {
        if (!$header) {
            return;
        }
        $srcHeader  = '"' . implode('","', array_keys(  $header)) . '"';
        $destHeader = '"' . implode('","', array_values($header)) . '"';

        $xsv = __DIR__ . '/../../../../../api/app/bin/xsv';
        $tmpFile = tempnam('/tmp', 'selectColumns');
        $cmd = "$xsv select" .
            " " . escapeshellarg($srcHeader) .
            " " . escapeshellarg($fileName) .
            " | sed 1c" . escapeshellarg($destHeader) .
            " > " . escapeshellarg($tmpFile);
        exec($cmd, $output, $status);
        if ($status != 0) {
            throw new \Exception("Non-zero exit status $status for command $cmd: " .
                implode("\n", $output));
        }
        if (!rename($tmpFile, $fileName)) {
            throw new \Exception("Failed to rename '$tmpFile' to '$fileName'.");
        }
    }

    private function getHeadersArray()
    {
        $configs = $this->app['configs'];
        $headers = [];

        if ($this->type === 'user') {
            $headers[] = 'User';
            if ($this->showAdminColumns) {
                $headers[] = 'User ID';
            }
            $headers[] = 'Current Status';
            $headers[] = 'Email';
            $headers[] = 'Retailer ID';
            $headers[] = 'User Login';
            $headers[] = 'Permission';
            $headers[] = 'Type';
            $headers[] = 'Store ID';
            $headers[] = 'Retailer Store ID';
            $headers[] = 'Location';

        } else {
            $headers[] = 'Store';
            $headers[] = 'Store ID';
            $headers[] = 'Retailer Store ID';
        }

        if ($this->isMultibrand) {
            $headers[] = 'Brand';
        }

        foreach ($this->requestsType as $requestType) {
            switch ($requestType) {
                case 'get-users':
                    $headers[] = 'Storefront URL';
                    $headers[] = 'Image URL';
                    $headers[] = 'About Me';
                    $headers[] = 'Phone Number';
                    $headers[] = 'SMS Number';
                    $headers[] = 'Specialties';
                    break;
                case 'get-store-marketing':
                    // we still need display Brand to keep column alignment for 'get-store-marketing' report, see SF-23416
                    if (!$this->isMultibrand) {
                        $headers[] = 'Brand';
                    }
                    $headers[] = 'Outstanding Requests (current)';
                    $headers[] = 'Sidebar Widget Status';
                    //!!!!!! Be careful: no break here
                case 'get-marketing':
                    $headers[] = 'Total Requests';
                    $headers[] = 'Request Reply Time';
                     // Appointment Requests
                    $headers[] = $this->app['sf.services']->getAppointmentLabel() . 's';
                    // Contact Me / Ask Answer Requests
                    $headers[] = ucwords($this->app['sf.services']->getEmailMeLabel('reports')) . ' Requests ';
                    // Email Me Requests from chat handoff
                    $headers[] = $this->app['sf.services']->getEmailMeRequestChatHandoffLabel();
                    if ($configs['retailer.has_personal_shopper']) {
                        // Personal Shopper Requests
                        $headers[] = $this->app['sf.services']->getPersonalShopperLabel() . ' Requests ';
                    } elseif ($requestType == 'get-store-marketing') {
                        $headers[] = $this->app['sf.services']->getPersonalShopperLabel() . ' Requests ';
                    }

                    $headers[] = 'Email/Text Correspondence Preference';

                    $headers[] = $this->app['sf.services']->getChatLabel() . ' Requests'; // Live Chat Requests
                    $headers[] = $this->app['sf.services']->getChatLabel() . ' Answers'; // Live Chat Answers
                    $headers[] = 'Content Updates';
                    // PP-165 - Add Lookbook metrics in Store and Associate Activity report
                    $headers[] = 'Lookbooks Added';
                    $headers[] = 'Lookbook Updates';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Product Updates';
                        $headers[] = 'Event Updates';
                        $headers[] = 'Post Updates';
                    }
                    // SF-17372 Reporting for Text Messages
                    // Show sent / received number if retailer is enabled
                    if ($configs['messaging.text.enabled']) {
                        $headers[] = 'Text Messages Sent';
                        $headers[] = 'Text Messages Received';
                    } elseif ($requestType == 'get-store-marketing') {
                        $headers[] = 'Text Messages Sent';
                        $headers[] = 'Text Messages Received';
                    }

                    // SF-21171 - New email section header
                    $headers[] = 'Email Campaigns';
                    $headers[] = 'Campaign Emails Sent';
                    $headers[] = 'Campaign Emails Sent (Open Rate)';
                    $headers[] = 'Campaign Emails Sent (Clicks)';
                    $headers[] = 'Campaign Emails Sent (Click Rate)';
                    $headers[] = 'Compose Emails Sent';
                    $headers[] = 'Compose Emails Sent (Open Rate)';
                    $headers[] = 'Compose Emails Sent (Clicks)';
                    $headers[] = 'Compose Emails Sent (Click Rate)';
                    $headers[] = 'Request Reply Emails Sent';
                    $headers[] = 'Request Reply Emails Sent (Open Rate)';
                    $headers[] = 'Request Reply Emails Sent (Clicks)';
                    $headers[] = 'Request Reply Emails Sent (Click Rate)';
                    $headers[] = 'Total Emails Sent';
                    $headers[] = 'Total Emails Sent (Open Rate)';
                    $headers[] = 'Total Emails Sent (Clicks)';
                    $headers[] = 'Total Emails Sent (Click Rate)';

                    $headers[] = 'Social Posts';
                    $headers[] = 'Social Clicks';
                    $headers[] = 'Storefront Visits';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Unique Storefront Visitors';
                        $headers[] = 'Retailer Referred Shoppers';
                    }
                    $headers[] = 'Contacts Added';
                    $headers[] = 'Average Order Value';
                    $headers[] = 'Number of Transactions';
                    $headers[] = 'Number of Recommendations';
                    $headers[] = 'Recommendations CTR (Storefront)';
                    if ($this->showAdminColumns) {
                        $headers[] = RETAILER_LABEL_KPI_TOP_PICKS . ' CTR';
                        if ($configs['retailer.has_kpi_deals']) {
                            $headers[] = RETAILER_LABEL_KPI_DEALS . ' CTR';
                        } elseif ($requestType == 'get-store-marketing') {
                            $headers[] = 'New Arrivals CTR';
                        }
                        $headers[] = 'Average selected Top Picks';

                        if ($configs['retailers.name'] === "toysRus" || $configs['retailers.name'] === "babiesRus") {
                            $headers[] = 'Average selected Deals';
                        } else {
                            $headers[] = 'Average selected New Arrivals';
                        }
                    }
                    // SF-17601 Metrics For Tasks: Show to Corp + SF Admins
                    $headers[] = 'Automated Tasks Created';
                    $headers[] = 'Automated Tasks Resolved';
                    $headers[] = 'Automated Tasks Dismissed';
                    $headers[] = 'Manual Tasks Created';
                    $headers[] = 'Manual Tasks Resolved';
                    $headers[] = 'Manual Tasks Dismissed';
                    $headers[] = 'Share to External App (Attempts)';

                    // SF-23464 Metrics For Tasks Enhancement
                    $headers[] = 'System Tasks Created';
                    $headers[] = 'System Tasks Resolved';
                    $headers[] = 'System Tasks Dismissed';

                    $headers[] = 'Follow-Up Tasks Created';
                    $headers[] = 'Follow-Up Tasks Resolved';
                    $headers[] = 'Follow-Up Tasks Dismissed';

                    $headers[] = 'Corporate Tasks Created';
                    $headers[] = 'Corporate Tasks Resolved';
                    $headers[] = 'Corporate Tasks Dismissed';

                    // SF-23466/SF-23467
                    $headers[] = 'Manual Task Resolve Time';
                    $headers[] = 'Manual Task Dismiss Time';

                    $headers[] = 'Automated Task Resolve Time';
                    $headers[] = 'Automated Task Dismiss Time';

                    $headers[] = 'System Task Resolve Time';
                    $headers[] = 'System Task Dismiss Time';

                    $headers[] = 'Follow-up Task Resolve Time';
                    $headers[] = 'Follow-up Task Dismiss Time';

                    $headers[] = 'Corporate Task Resolve Time';
                    $headers[] = 'Corporate Task Dismiss Time';

                    $headers[] = 'SocialShop posts created';
                    $headers[] = 'SocialShop total visit';
                    $headers[] = 'SocialShop unique visit';
                    $headers[] = 'SocialShop product click';
                    $headers[] = 'SocialShop storefront click';
                    $headers[] = 'SocialShop sales count';
                    $headers[] = 'SocialShop sales amount';

                    $headers[] = 'Corporate Tasks Deleted';

                    $headers[] = 'Request Reply Time (Seconds)';
                    $headers[] = '% of Forwarded Requests';
                    break;
                case 'get-marketing-revise':
                case 'get-store-marketing-revise':
                    $headers[] = 'Scheduled Appointments';
                    $headers[] = 'Cancelled Appointments';

                    $headers[] = 'Video Chat Sessions';
                    $headers[] = 'Video Chat Duration';

                    $headers[] = 'Virtual Appointment Sessions';
                    $headers[] = 'Virtual Appointment Duration';

                    $headers[] = 'Number of Grouped Product Links shared - Email';
                    $headers[] = 'Average number of products per Grouped Product Link - Email';
                    $headers[] = 'Total number of products shared in Grouped Product Links - Email';
                    $headers[] = 'Number of Grouped Product Links shared - SMS';
                    $headers[] = 'Average number of products per Grouped Product Link - SMS';
                    $headers[] = 'Total number of products shared in Grouped Product Links - SMS';

                    // Group Tasks
                    $headers[] = 'Group Tasks Created';
                    $headers[] = 'Group Tasks Resolved';
                    $headers[] = 'Group Tasks Dismissed';
                    $headers[] = 'Group Tasks Resolve Time';
                    $headers[] = 'Group Tasks Dismiss Time';
                    break;
                case 'get-sales':
                    // SF-17158 Expose transaction report to CORP admins and hide certain columns
                    $headers[] = 'Transaction ID';
                    $headers[] = 'Date';
                    $headers[] = 'Total';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Commission';
                    }
                    $headers[] = 'Source';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Products';
                        $headers[] = 'Quantities';
                        $headers[] = 'Names';
                        $headers[] = 'Acquisition Channel';
                    }
                    $headers[] = 'Attribution';
                    $headers[] = 'Customer Name';
                    $headers[] = 'Customer Email';
                    $headers[] = 'Contact Record ID';
                    $headers[] = 'Date (Store\'s local time)';
                    $headers[] = 'Transaction Type';
                    break;
                case 'get-store-sales-total':
                case 'get-sales-total':
                    $headers[] = 'Sales';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Commissions';
                    }
                    break;
                case 'get-sales-details':
                    $headers[] = 'Transaction ID';
                    $headers[] = 'Date';
                    $headers[] = 'Product ID';
                    $headers[] = 'Product Name';
                    $headers[] = 'Quantity';
                    $headers[] = 'Unit Price';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Commission';
                        $headers[] = 'Source';
                        $headers[] = 'Acquisition Channel';
                    }
                    $headers[] = 'Attribution';
                    $headers[] = 'Contact Record ID';
                    $headers[] = 'Date (Store\'s local time)';
                    $headers[] = 'Transaction Type';
                    $headers[] = 'Variant ID';
                    break;
                case 'get-customers':
                    $headers[] = 'Customer Email';
                    $headers[] = 'Customer First Name';
                    $headers[] = 'Customer Last Name';
                    $headers[] = 'Subscribed';
                    break;
                case 'download-users':
                    $headers[] = 'Current Selling Mode';
                    $headers[] = 'Status';
                    $headers[] = 'Active in the Month';
                    $headers[] = 'Active Hours in the Month';
                    $headers[] = 'Date Created';
                    $headers[] = 'Active Text Message Numbers in the Month';
                    break;
                case 'get-chat-store-metrics':
                    $headers[] = 'Number of Chats Missed';
                    $headers[] = 'Number of Chats Received';
                    $headers[] = 'Number of Chats Answered';
                    $headers[] = 'Chat Answer Rate (%)';
                    $headers[] = 'Number of Minutes Available for Chat';
                    $headers[] = 'Chat Availability Rate (%)';
                    $headers[] = 'Chats Received Answered By Another';
                    $headers[] = 'Flagged Chat Sessions';
                    $headers[] = 'Number of Chats Abandoned';
                    $headers[] = 'Chats Abandoned: 0-29s';
                    $headers[] = 'Chats Abandoned: 30-59s';
                    $headers[] = 'Chats Abandoned: 60-89s';
                    $headers[] = 'Chats Abandoned: 90-120s';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Avg. Answer Time';
                    }
                    break;
                case 'get-chat-user-metrics':
                    $headers[] = 'Number of Chats Missed';
                    $headers[] = 'Number of Chats Received';
                    $headers[] = 'Number of Chats Answered';
                    $headers[] = 'Chat Answer Rate (%)';
                    $headers[] = 'Number of Minutes Available for Chat';
                    $headers[] = 'Chat Availability Rate (%)';
                    $headers[] = 'Chats Received Answered By Another';
                    $headers[] = 'Flagged Chat Sessions';
                    $headers[] = 'Number of Chats Abandoned';
                    if ($this->showAdminColumns) {
                        $headers[] = 'Avg. Answer Time';
                    }
                    break;
            }
        }
        return $headers;
    }

    private function ouputDataHeader()
    {
        if (in_array('download-sidebar-metrics', $this->requestsType)) {
            // SF-16958 Access Widget CTR From Reporting
            // Dataset does not rely on User / Store, so headers are built in standalone function
            $headers = $this->buildDownloadSidebarMetricsHeader();
        } elseif (in_array(\ReportingEngine::ENGINE_CONTEXTUAL_WIDGET, $this->requestsType)) {
            $headers = $this->buildDownloadContextualWidgetMetricsHeader();
        } else {
            $headers = $this->getHeadersArray();
        }
        $headerString = '"' . implode('","', $headers) . '"';

        return $headerString;
    }

    protected function isGroupingEnabled()
    {
        return true;
    }

    /**
     * Generate HTML version of Export for development purposes
     * If needed, call from outputData()
     * @param  array   $data    Array of results (not yet csv)
     * @param  integer $maxRows Max number of rows (other than header) to show
     * @return string           String HTML code representation of export information
     */
    protected function getHtmlTableResults($data, $maxRows = 0)
    {
        $info = "<pre>
        <br><strong>Request Types</strong><br>
        " . print_r($this->requestsType, true) . "
        <br><strong>Date Details: </strong>" . $this->dateFrom . " - " . $this->dateTo . "
        <br><strong>Filename:</strong> " . $this->getCSVFilename() . "
        </pre><br>
        ";

        $table = $info . "<table style=\"border: 1px solid black\">
        <thead>
            <tr>";
        foreach (explode(",", $this->ouputDataHeader()) as $key => $header) {
            $table .= "
                <td style=\"border: 1px solid black\">$header</td>";
        }
        $table .= "
            </tr>
        </thead>
        <tbody>";
        $rows = 0;
        foreach($data as $row) {
            $table .= "
            <tr>";
            foreach ($row as $key => $value) {
                $table .= "
                <td style=\"border: 1px solid black\"> $value" . ($key == 0 ? " (Row $rows)" : '');
            }
            $table .= "
            </tr>";
            $rows++;
            if (!empty($maxRows) && $rows > $maxRows) {
                break;
            }
        }

        $table .= "
        </tbody>
        </table>";

        return $table;
    }
}
