<?php

use Salesfloor\Services\MySQLRepository;

require_once __DIR__ . '/ReportingProcessor.php';

class ReportingProcessorSalesDetails extends ReportingProcessor
{
    protected function internalProcess(MySQLRepository $db)
    {
        $retailerHasSkus = $this->retailerHasSkus($db);
        $qb = $this->prepareQueryBuilder($db, true, $retailerHasSkus);
        $cancelsAndReturns = $db->executeCustomQuery($qb);

        $qb = $this->prepareQueryBuilder($db, false, $retailerHasSkus);
        $purchases = $db->executeCustomQuery($qb);

        return array_merge($cancelsAndReturns, $purchases);
    }

    /**
     * Run the following test to ensure that the query builder works correctly:
     *
     *    ./robo test:functional Exporter/Transaction/TransactionExporterCest.php
     *
     * @param MySQLRepository $db
     * @param boolean $cancelsAndReturns
     * @param boolean $retailerHasSkus
     * @return void
     */
    private function prepareQueryBuilder($db, $cancelsAndReturns = false, $retailerHasSkus = false)
    {
        $queryBuilder = $db->getQueryBuilder();

        $queryBuilder->select(
            's.user_id',
            's.trx_id',
            'trx_detail_id',
            'trx_detail_total * u.`commission_rate` / 100 as trx_detail_apply_total',
            'trx_detail_total',
            'd.product_id',
            'd.quantity',
            's.trx_date AS date',
            's.trx_type',
            's.origin',
            's.acquisition',
            's.attribution',
            's.customer_id',
            's.store_id',
            'store.name AS store_name',
            'store.retailer_store_id',
            "CONCAT(DATE_FORMAT(" . $this->getDateField('s.trx_date', 'store.timezone') . ", '%b %d, %Y %H:%i:%s')" .
            ", ' (UTC', TIME_FORMAT(TIMEDIFF(s.trx_date, CONVERT_TZ(s.trx_date, store.timezone, 'UTC')), '%H:%i'), ')') AS received_date"
        );
        if ($retailerHasSkus) {
            $queryBuilder->addSelect('COALESCE(p.name, p2.name, "") AS name');
            $queryBuilder->addSelect("CASE WHEN d.sku IS NULL OR d.sku = '' THEN d.product_id ELSE d.sku END AS `variant_id`");
        } else {
            $queryBuilder->addSelect('COALESCE(p.name, "") AS name');
            $queryBuilder->addSelect('d.product_id AS `variant_id`');
        }
        $queryBuilder->from('wp_users', 'u');
        $queryBuilder->leftJoin('u', 'sf_rep_transaction', 's', 'u.ID = s.user_id');

        // We probably don't need to include on the join trx_detail_id when not cancel/return but let's keep it.
        $joinCondition = $cancelsAndReturns
            ? 's.trx_id = d.trx_id AND d.trx_detail_id = s.trx_date'
            : 's.trx_id = d.trx_id AND (d.trx_detail_id = "" OR d.trx_detail_id IS NULL)';

        $queryBuilder->leftJoin('s', 'sf_rep_transaction_detail', 'd', $joinCondition);
        $queryBuilder->leftJoin('d', 'sf_products', 'p', 'd.product_id = p.product_id');
        $queryBuilder->leftJoin('s', 'sf_store', 'store', 's.store_id = store.store_id');

        if ($retailerHasSkus) {
            $queryBuilder->leftJoin('d', 'sf_products', 'p2', 'd.product_id = p2.retailer_sku');
        }

        // Use extra join on store, since the store in the transaction could be different than the current one.
        $queryBuilder->innerJoin('u', 'sf_store', 'currentStore', 'u.store = currentStore.store_id');

        $queryBuilder->where($queryBuilder->expr()->gte($this->getDateField('trx_date', 'currentStore.timezone'), $queryBuilder->expr()->literal($this->dateFrom)));
        $queryBuilder->andWhere($queryBuilder->expr()->lte($this->getDateField('trx_date', 'currentStore.timezone'), $queryBuilder->expr()->literal($this->dateTo)));
        $queryBuilder->andWhere('s.user_id != 0');

        $queryBuilder = $this->applyUsersFiltering(
            $queryBuilder,
            's',
            false,
            'u',
            'user_id',
            'store_id',
            's'
        );

        if ($this->transactionId) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('s.trx_id', $queryBuilder->expr()->literal($this->transactionId)));
        }
        $queryBuilder->orderBy('trx_date', 'DESC');
        $queryBuilder->addOrderBy('trx_id');
        // Make sure the rows ordered properly and the content of .CSV file never changes (the order of the lines).
        // This is important for the tests when capturing the content of the .CSV file as a snapshot and
        // then comparing it with the content of the file when the test is running.
        $queryBuilder->addOrderBy('d.product_id');

        if ($cancelsAndReturns) {
            $queryBuilder->andWhere("s.trx_type IN (:cancelAndReturn)");
            $queryBuilder->groupBy('s.trx_id, d.trx_detail_id, d.product_id, d.sku, d.trx_detail_total');

            $queryBuilder->setParameters(
                [
                    'cancelAndReturn' => [\Salesfloor\Models\Transaction::TRX_TYPE_CANCEL, \Salesfloor\Models\Transaction::TRX_TYPE_RETURN],
                ],
                [
                    'cancelAndReturn' => \Doctrine\DBAL\Connection::PARAM_STR_ARRAY,
                ]
            );
        } else {
            $queryBuilder->andWhere("s.trx_type = :sale");
            $queryBuilder->groupBy('s.trx_id, d.product_id, d.sku, d.trx_detail_total');

            $queryBuilder->setParameters([
                'sale' => Salesfloor\Models\Transaction::TRX_TYPE_SALE,
            ]);
        }

        $queryBuilder->leftJoin('s', 'sf_rep_transaction_remove', 'cancels', 's.trx_id = cancels.trx_id');
        $queryBuilder->andWhere('cancels.trx_id IS NULL');

        return $queryBuilder;
    }
}
