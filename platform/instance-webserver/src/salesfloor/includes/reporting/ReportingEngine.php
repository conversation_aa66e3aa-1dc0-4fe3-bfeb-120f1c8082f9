<?php

require_once __DIR__ . '/ReportingHelpersBackfill.php';
require_once __DIR__ . '/ReportingProcessor.php';
require_once __DIR__ . '/ReportingProcessorCommission.php';
require_once __DIR__ . '/ReportingProcessorMarketing.php';
require_once __DIR__ . '/ReportingProcessorSales.php';
require_once __DIR__ . '/ReportingProcessorSalesDetails.php';
require_once __DIR__ . '/ReportingProcessorSalesTotal.php';
require_once __DIR__ . '/ReportingProcessorCustomers.php';
require_once __DIR__ . '/ReportingProcessorUsers.php';
require_once __DIR__ . '/ReportingProcessorChatUserMetrics.php';
require_once __DIR__ . '/ReportingProcessorChatStoreMetrics.php';
require_once __DIR__ . '/ReportingProcessorSidebarMetrics.php';
require_once __DIR__ . '/ReportingProcessorSidebarMetricsTotal.php';

if (!defined('API_STACK')) {
    require_once __DIR__ . '/../../../../../vendor/autoload.php';
}

class ReportingEngine
{
    // note : try to use this const instead of hardcode string
    const ENGINE_MARKET = 'get-marketing';
    const ENGINE_SALES_TOTAL = 'get-sales-total';
    const ENGINE_CHAT_STORE = 'get-chat-store-metrics';
    const ENGINE_SIDEBAR = 'download-sidebar-metrics';

    const ENGINE_CONTEXTUAL_WIDGET = 'download-contextual-widget-metrics';
    const ENGINE_GET_SIDEBAR_TOTAL = 'get-sidebar-metrics-total';

    const ENGINE_DOWNLOAD_USER = 'download-users';

    protected $requestsType;
    protected $users;
    protected $stores;
    protected $timezone;
    protected $dateFrom;
    protected $dateTo;
    protected $transactionId;
    protected $isMultibrand;
    protected $returnCompleteData = false;
    // Determines where to fetch results from 'backfill-only' | 'query-only' | 'combine'
    protected $fetchResultsFrom;
    protected $type  = 'user';
    protected $currentUser;
    protected $app;
    protected $data;
    protected $format = 'json';
    protected $streamDownload = false;
    protected $brand;
    protected $workingInBatches = false;

    /**
     * Only this groups of metrics need to be calculated, if the array is not empty.
     * @var array $onlyThisMetrics
     */
    protected $onlyThisMetrics;

    /**
     * This is a flag to ignore users/stores during kpi request in form/UI.
     * @var array $excludeInactive
     */
    protected $excludeInactive;
    /**
     * @var false|mixed
     */
    private mixed $forceGrouping;

    public function __construct(
        $requestsType,
        $users,
        $timezone,
        $dateFrom,
        $dateTo,
        $transactionId,
        $returnCompleteData = false,
        $forceGrouping = false,
        $stores = [],
        $fetchResultsFrom = '',
        $brand = null,
        array $onlyThisMetrics = [],
        $excludeInactive = []
    ) {
        list($this->requestsType, $canStream) = $this->getRequestType($requestsType);

        $this->brand = $brand;
        $this->users = $users;
        $this->timezone = $timezone;
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
        $this->transactionId = $transactionId;
        $this->isMultibrand = false;
        $this->returnCompleteData = $returnCompleteData;
        $this->forceGrouping = $forceGrouping;
        $this->stores = $stores;
        $this->fetchResultsFrom = ReportingHelpersBackfill::validateFetchResultsFrom($fetchResultsFrom);
        $this->currentUser = $this->getCurrentUser();
        $this->onlyThisMetrics = $onlyThisMetrics;
        $this->evaluateStreamDownload($canStream);
        $this->excludeInactive = $excludeInactive;
    }

    private function getRequestType($requestsType)
    {
        $canStream = true;

        // SF-15079 For HR weekly transactions cron we don't want to zip reports because they want a specific format
        // Could be probably better implemented but we need to fix it asap and this solution was less risky
        foreach ($requestsType as &$rType) {
            if ($rType == 'get-sales-details-unzip') {
                $rType = 'get-sales-details';
                $canStream = false;
            }
        }

        return [$requestsType, $canStream];
    }

    public function run($db, $app, $queryNewData = true)
    {
        set_time_limit(0); // Remove timeouts

        $data = [];
        $this->app = $app;
        if ($this->streamDownload) {
            $this->streamDownloadFromStorage();
        } else {
            $this->setDocumentHeader($app);
            // Can now call this run function more than once with the same object
            // In the case of a multiple call it will not re-query the information
            // It will just reprocess the store data for the CSV creation
            // Used by the CreateTransactionExports Cron to just reshuffle the
            //     results for CORP vs SF admin CSVs
            if ($queryNewData || !isset($this->data) || $this->workingInBatches) {
                $this->data = $this->process($db, $this->returnCompleteData);
            }
            if ($this->returnCompleteData) {
                return $this->outputData($this->data, $db);
            }
            echo $this->outputData($this->data, $db);
        }

    }

    /**
     * @deprecated Obsolete, we do not support multi-brand anymore, $isMultibrand should always be false
     * @param $isMultibrand
     */
    public function setIsMultibrand ($isMultibrand) {
        $this->isMultibrand = $isMultibrand;
    }

    protected function outputData($data, $db)
    {
        return $data;
    }

    protected function setDocumentHeader ($app)
    {
    }

    protected function isGroupingEnabled()
    {
        return false;
    }

    protected function process($db)
    {
        // why ?! if report engine is call from robo directly, global $app set to null when this function called from $this->run()
        // a patch to set app back if this happen
        global $app;
        if (empty($app)) {
            $app = $this->app ;
        }
        try {

            $data = [];

            foreach ($this->requestsType as $requestType) {
                $key = null;

                switch ($requestType) {
                    case 'get-marketing':
                        $key = 'marketing';
                        // SF-14452 Requesting complete data grouped by user when run by CalculateDailyStats cron
                        if ($this->returnCompleteData) {
                            $report = new ReportingProcessorMarketing($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->forceGrouping, $this->returnCompleteData, $this->stores, $this->fetchResultsFrom, $this->app['configs']['retailer.storepage_mode'], $this->brand);
                            break;
                        }
                        $report = new ReportingProcessorMarketing($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, $this->fetchResultsFrom, $this->app['configs']['retailer.storepage_mode'], $this->brand);
                        break;
                    case 'get-marketing-revise':
                        // 'get-marketing-revise' must be called after 'get-marketing' to add some new columns after fix columns, used only in ReportingExportCsv
                        break;
                    case 'get-store-marketing':
                        $key = 'marketing-store';
                        $this->setType('store');
                        if ($this->returnCompleteData) {
                            $report = new ReportingProcessorMarketing([], $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->forceGrouping, $this->returnCompleteData, $this->stores, 'backfill-only', $this->app['configs']['retailer.storepage_mode']);
                            break;
                        }
                        // Running in store mode, will not filter by specific users, only stores
                        $report = new ReportingProcessorMarketing([], $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'backfill-only', $this->app['configs']['retailer.storepage_mode']);
                        // The resulting values come only from the backfill because these numbers are precalculated
                        break;
                    case 'get-store-marketing-revise':
                        // 'get-store-marketing-revise' must be called after 'get-store-marketing' to add some new columns after fix columns, used only in ReportingExportCsv
                        break;
                    case 'get-sales-total':
                        $key = 'sales_total';
                        $report = new ReportingProcessorSalesTotal($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-store-sales-total':
                        $key = 'sales_total_store';
                        $this->setType('store');
                        // Running in store mode, will not filter by specific users, only stores
                        $report = new ReportingProcessorSalesTotal([], $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-sales':
                        $key = 'sales';
                        $report = new ReportingProcessorSales($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), $this->returnCompleteData, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-commission':
                        $key = 'commission';
                        $report = new ReportingProcessorCommission($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-sales-details':
                        $key = 'sales_details';
                        $report = new ReportingProcessorSalesDetails($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), $this->returnCompleteData, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-customers':
                        $key = 'customers';
                        $report = new ReportingProcessorCustomers($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'download-users':
                        $key = 'users';
                        $report = new ReportingProcessorUsers($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-chat-user-metrics':
                        $key = 'chat';
                        if ($this->returnCompleteData) {
                            $report = new ReportingProcessorChatUserMetrics($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->forceGrouping, $this->returnCompleteData, $this->stores, $this->fetchResultsFrom, $this->app['configs']['retailer.storepage_mode']);
                            break;
                        }
                        $report = new ReportingProcessorChatUserMetrics($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, $this->fetchResultsFrom, $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case 'get-chat-store-metrics':
                        $key = 'chat-store';
                        $this->setType('store');
                        if ($this->returnCompleteData) {
                            $report = new ReportingProcessorChatStoreMetrics($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->forceGrouping, $this->returnCompleteData, $this->stores, $this->fetchResultsFrom, $this->app['configs']['retailer.storepage_mode']);
                            break;
                        }
                        $report = new ReportingProcessorChatStoreMetrics($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, $this->fetchResultsFrom, $this->app['configs']['retailer.storepage_mode']);
                        break;
                    // SF-16958 Access Widget CTR From Reporting
                    // New reportingType for CTR. Export only. Stream CSV from S3
                    case 'download-sidebar-metrics':
                    case self::ENGINE_CONTEXTUAL_WIDGET:
                        $key = 'users';
                        $report = new ReportingProcessorSidebarMetrics($this->users, $this->timezone, $this->dateFrom, $this->dateTo, $this->transactionId, $this->isGroupingEnabled(), false, $this->stores, 'query-only', $this->app['configs']['retailer.storepage_mode']);
                        break;
                    case self::ENGINE_GET_SIDEBAR_TOTAL:
                        $key    = 'sidebar-metrics-total';
                        //  Note: ReportingProcessorSidebarMetricsTotal is a global KPI aggregator,
                        //        Not per store nor users, and data per date is aggregated by UTC timezone
                        $report = new
                        ReportingProcessorSidebarMetricsTotal(
                            $this->users,
                            $this->timezone,
                            $this->dateFrom,
                            $this->dateTo,
                            $this->transactionId,
                            $this->isGroupingEnabled(),
                            false,
                            $this->stores,
                            'query-only',
                            $this->app['configs']['retailer.storepage_mode']
                        );
                        break;
                }


                if ($key) {
                    $report
                        ->setApp($app)
                        ->setType($this->type)
                        ->setMetricsToProcess($this->onlyThisMetrics)
                        ->setExcludeInactive($this->excludeInactive)
                    ;

                    $data[$key . '_dates'] = $report->getBackfillDates();
                    $data[$key] = $report->process($db);
                }
            }

        } catch (\Exception $e) {
            return $e->getMessage();
        }

        return $data;
    }

    /**
     * Set the type to be passed to the ReportingProcessorXXXXX
     * Only needs to be set for those that are store, since user is default
     * @param string $type User / Store
     */
    protected function setType($type = 'user')
    {
        if (in_array($type, ReportingProcessor::getAggregateTypes())) {
            $this->type       = $type;
        }
    }

    /**
     * Get current user information
     * Can fail because this code is run from cronjob, so we don't have the current wordpress user
     * @return Object currentUser
     */
    protected function getCurrentUser()
    {
        $currentUser = null;
        try {
            if (function_exists('wp_get_current_user')) {
                $currentUser = wp_get_current_user();
                if (!empty($currentUser->data)) {
                    $currentUser = $currentUser->data;
                }
            }
        } catch (\Exception $e) {
            error_log($e->getMessage());
        }

        return $currentUser;
    }

    /**
     * Function will create mock user
     * To be used by the crons that call this class
     * @param integer $group Integer signifying LEVEL of group
     */
    public function setCurrentUserGroup($group = 5)
    {
        $this->currentUser = (object) ['group' => $group];
    }

    public function getCSVFilename($overrideApp = [], $includeDates = true, $includeFormat = true)
    {
        return '';
    }

    /**
     * Updates the $this->streamDownload value
     * Evaluates to true if requestType matches one of the allowed download types
     *  + AND we are not coming from the CRON (ONLY cron requests complete data)
     *  + We are requesting a csv format report i.e. ReportingExportCsv
     * @return
     */
    protected function evaluateStreamDownload($canStream = true)
    {
        $typesToDownload = ['get-sales', 'get-sales-details', 'download-sidebar-metrics', self::ENGINE_CONTEXTUAL_WIDGET, 'get-chat-log'];
        $matchingTypes   = array_intersect($typesToDownload, $this->requestsType);
        if (count($matchingTypes) && $this->returnCompleteData == false && $this->format == 'csv' && $canStream) {
            $this->streamDownload = true;
        }
    }

    /**
     * Streams the requested file instead of downloading from Storage (s3)
     * Echoes it out to the client with the appropriate headers to force download
     *
     * Will first attempt to stream a ZIP with the given name, and then fallback
     * to attempt to stream a CSV. Majority of the files in S3 Exports are .zip,
     * and new ones such as SidebardMetrics are in CSV
     * @return
     */
    protected function streamDownloadFromStorage()
    {
        // File name, without extension
        $fileNameNoExt = $this->getCSVFilename($this->app['configs'], false, false);

        // Use Client to download file
        $cloudstorageClient = $this->app['cloudstorage.client'];

        $cloudstorageClient->registerStreamWrapper();

        $extensions = ['.zip', '.csv'];

        // Try to open ZIP stream first, otherwise fallback to CSV stream
        foreach ($extensions as $extension) {
            if ($this->app['configs']['exporter.private_bucket_duplicated.is_enabled'] == true) {
                $bucket = $this->app['configs']['exporter.private.bucket'];
            } else {
                $bucket = $this->app['configs']['s3.bucket'];
            }
            $s3AccessString = 's3://' . $bucket . '/' . $this->app['configs']['s3.exports.prefix'] . $fileNameNoExt . $extension;
            // Open a stream in read-only mode
            if ($stream = fopen($s3AccessString, 'r')) {
                $this->setDocumentHeader($this->app, 'application/octet-stream', $fileNameNoExt . $extension);
                while (!feof($stream)) {
                    // Chunk stream into 1024 bytes
                    echo fread($stream, 1024);
                }
                fclose($stream);
                break;
            }
        }

        exit;
    }
}
