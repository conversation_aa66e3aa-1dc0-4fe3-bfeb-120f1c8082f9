<?php

use Salesfloor\API\Managers\Categories;

class RepSpecialties {
    private static $availableSpecialties;
    private static $repSpecialties;
    private static $specialtyRepIds;

    public static function getAvailableSpecialties($locale = null) {
        global $wpdb, $app;
    
        $specialties = null;

        $mysqlFlagUpdated = disableOnlyFullGroupBy($wpdb);
            
        if (!isset(static::$availableSpecialties) && $locale) {
            $specialties = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT wp_terms.term_id AS id,
                            CASE WHEN wp_terms_i18n.name IS NOT NULL then wp_terms_i18n.name ELSE wp_terms.name END AS name 
                    FROM wp_terms 
                    JOIN wp_term_taxonomy ON wp_term_taxonomy.term_id = wp_terms.term_id
                    LEFT JOIN wp_terms_i18n ON wp_terms_i18n.term_id = wp_terms.term_id AND wp_terms_i18n.locale = '%s'
                    WHERE parent = %s GROUP BY wp_terms.name ORDER BY wp_terms.name",
                    [$locale, $app['configs']['products.root_category_id']]
                ),
                ARRAY_A
            );
        } elseif(!isset(static::$availableSpecialties)) {
            $specialties = $wpdb->get_results($wpdb->prepare(
                "SELECT term_id id, name FROM wp_terms JOIN wp_term_taxonomy using (term_id) WHERE parent = %s GROUP BY name ORDER BY name",
                $app['configs']['products.root_category_id']
            ), ARRAY_A);
        }

        enableOnlyFullGroupBy($wpdb, $mysqlFlagUpdated);

        if (!$specialties) {
            $specialties = [];
        }

        /** @var $categoriesManager Categories */
        $categoriesManager = $app['categories.manager'];
        static::$availableSpecialties = $categoriesManager->filterSpecialtiesFromCategories($specialties,'id');

        return static::$availableSpecialties;
    }

    public static function getRepSpecialties($repId, $withName = false, $locale = null) {
        global $wpdb;

        if(!isset(static::$repSpecialties)) {
            static::$repSpecialties = array();
        }
    
        if(!isset(static::$repSpecialties[$repId]) && $locale) {
            static::$repSpecialties[$repId] = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT sf_rep_product_selection_map.term_id AS id,
                            CASE WHEN wp_terms_i18n.name IS NOT NULL then wp_terms_i18n.name ELSE wp_terms.name END AS name
                    FROM sf_rep_product_selection_map
                    JOIN wp_terms USING (term_id)
                    LEFT JOIN wp_terms_i18n ON wp_terms_i18n.term_id = sf_rep_product_selection_map.term_id AND wp_terms_i18n.locale = '%s'
                    WHERE user_id = %d",
                    [$locale, $repId]
                ),
                ARRAY_A
            );
        } elseif(!isset(static::$repSpecialties[$repId])) {
            static::$repSpecialties[$repId] = $wpdb->get_results($wpdb->prepare(
                "SELECT term_id id, name FROM sf_rep_product_selection_map
                JOIN wp_terms USING (term_id)
                WHERE user_id = %d",
                $repId
            ), ARRAY_A);
        }

        if($withName) {
            $ret = static::$repSpecialties[$repId];
        } else {
            $ret = array();
            foreach(static::$repSpecialties[$repId] as $s) {
                $ret[] = $s['id'];
            }
        }

        return $ret;
    }

    public static function getSpecialtyRepIds($specialty, $store = null) {
        global $wpdb;

        if(!isset(static::$specialtyRepIds)) {
            static::$specialtyRepIds = array();
        }
        if(!isset(static::$specialtyRepIds[$specialty])) {
            if($store) {
                $query = $wpdb->prepare(
                    "SELECT user_id FROM sf_rep_product_selection_map m
                    JOIN wp_users u ON m.user_id = u.ID
                    WHERE term_id = %s AND store = %d",
                    [$specialty, $store]);
            } else {
                $query = $wpdb->prepare(
                    "SELECT user_id FROM sf_rep_product_selection_map WHERE term_id = %s",
                    $specialty);
            }
            static::$specialtyRepIds[$specialty] = $wpdb->get_col($query);
        }

        return static::$specialtyRepIds[$specialty];
    }

    public static function shouldFilterNewLeadsBySpecialty() {
        global $app;

        return $app['configs']['retailer.filter_new_leads_by_specialty'];
    }
};
