<?php

require_once ABSPATH . 'sfadmin/includes/sf-template.php';
require_once ABSPATH . 'includes/publisher/Shareable.php';
require_once ABSPATH . 'includes/publisher/Template.php';
require_once ABSPATH . 'includes/publisher/Post.php';
require_once ABSPATH . 'includes/publisher/PostFromPublisher.php';
require_once ABSPATH . 'includes/publisher/Product.php';
require_once ABSPATH . 'includes/publisher/ImageAsset.php';
require_once ABSPATH . 'includes/publisher/MixedAttachments.php';
require_once ABSPATH . 'includes/publisher/MixedAsset.php';
require_once ABSPATH . 'includes/publisher/Hello.php';
require_once ABSPATH . 'includes/publisher/FacebookClient.php';
require_once ABSPATH . 'includes/publisher/LinkedInClient.php';
require_once ABSPATH . 'includes/publisher/TwitterClient.php';
require_once ABSPATH . 'includes/publisher/InstagramClient.php';
require_once ABSPATH . 'includes/publisher/PinterestClient.php';
require_once ABSPATH . 'includes/UrlShortener.php';
require_once ABSPATH . 'includes/publisher/GroupedProducts.php';

use Carbon\Carbon;
use Salesfloor\Models\CustomerTag;
use Salesfloor\Services\Tasks\Tasks;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Query\QueryBuilder;
use Salesfloor\API\Managers\Transactions;
use Salesfloor\Services\Mail\EmailBlockList;
use Salesfloor\Services\Mail\Client as MailClient;
use Salesfloor\Models\RetailerCustomerCommunication;
use Salesfloor\Services\RetailerCustomerNotificationBlackout;

class Publisher
{

    const PER_BATCH = 249;

    /**
     * @param      $user
     * @param      $caption
     * @param      $recipients
     * @param null $assetData
     * @param null $assetType
     * @param null $url
     * @param null $locale
     *
     * @return array|bool|mixed
     */
    public static function publish(
        $user,
        $caption,
        $recipients,
        $assetData = null,
        $assetType = null,
        $url = null,
        $locale = null,
        $templateData = null,
        $sanitizeAssetData = null,
        $uniqueId = null,
        $favoriteContacts = null,
        $filters = null
    ) {
        global $app, $wpdb;
        $ret = array();

        // SF-15106 Strip Tags to avoid XSS attack in Publisher Posts
        // Only $caption needs to be stripped because all the other fields are already stripped within $assetData / DB
        // Additional: Extra remove XSS has been applied on $caption and $assetData['email_subject']
        $caption = $app['service.sanitize']->removeXss($caption);
        $assetData['email_subject'] = $app['service.sanitize']->removeXss($assetData['email_subject']);

        // If i18n is enabled and we don't know the locale, we'll use the store default locale of the rep
        if ($app['configs']['retailer.i18n.is_enabled'] && empty($locale) && isset($user->store)) {
            $locale = $app['service.multilang']->getStoreDefaultLocale($user->store);
        }

        // This is the asset before the template way
        $asset = static::newShareable($user, $caption, $assetData, $assetType, $url, $locale);
        /**
         * HACK
         * Since the logic - exception of how to handle all the social media is really not clear, keep it as is for now
         *
         * Handle only the email with the new way
         */
        $newAsset = null;
        if (!empty($templateData)) {
            // New way with the Template structure
            $assetType = Template::class;
            if (!empty($assetData['email_subject'] && empty($templateData['email_subject']))) {
                $templateData['email_subject'] = $assetData['email_subject'];
            }
            $assetData = $templateData;
            $newAsset = static::newShareable($user, $caption, $assetData, $assetType, $url, $locale);
        }

        if (!$asset) {
            return false;
        }
        if (isset($recipients['social']) && count($recipients['social']) > 0) {
            $ret = static::shareToSocialNetworks($user, $recipients['social'], $asset);
        }

        if (isset($recipients['email'])) {
            $emailCustomerTagIds = [];
            if (!empty($recipients['email_customer_tag_ids'])) {
                $emailCustomerTagIds = $recipients['email_customer_tag_ids'];
            }
            // We need $asset, $caption, $assetData, $assetType to be able to send emails later
            // Those info allow to generate the email Obj in the readQueue method of MassMailer class
            list($emailsSentCount, $eventUniqId, $totalBlackoutEmails) = static::sendEmail(
                $user,
                $recipients['email'],
                $newAsset ?: $asset,
                $caption,
                $assetData,
                $assetType,
                $emailCustomerTagIds,
                $locale,
                // Have to reverse order of last 2 params to be safe,
                // b/c sendEmail() has no null value on the final argument $uniqueId:
                $favoriteContacts,
                $uniqueId,
                $filters
            );
            $ret['emails_sent']   = $emailsSentCount;
            $ret['event_uniq_id'] = $eventUniqId;
            $ret['emails_blackout'] = $totalBlackoutEmails;
        }

        if ($sanitizeAssetData !== null) {
            if ($sanitizeAssetData['taskId'] !== null) {
                /** @var Tasks $tasksService */
                $tasksService = $app['service.tasks'];
                $tasksService->autoResolve((int)$sanitizeAssetData['taskId'], __METHOD__);
            }

            if ($sanitizeAssetData['groupTaskId'] !== null) {
                /** @var GroupTasks groupTaskService */
                $groupTaskService = $app['service.group-tasks'];
                $groupTaskService->autoResolve((int)$sanitizeAssetData['groupTaskId'], (int)$user->ID);
            }
        }

        if (isset($recipients['storefront']) && is_a($asset, 'Post')) {
            $ret['storefront'] = static::postToStorefront($user, $asset);
        }

        if (is_a($asset, 'Post') && count($ret) > 0) {
            $asset->setPublished();
        }

        $ret['url'] = $asset->getUrl();
        $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);
        $sfClient->insert("clear-nags-cache", []);

        // SF-15174 Track Product recommendations on Share / Publish
        static::trackProductRecommendation(
            $user,
            $assetType,
            $newAsset ?? $asset,
            $recipients,
            // Always count share an update as one recommendation
            1
        );

        return $ret;
    }

    public static function newShareable($user, $caption, $assetData, $assetType, $url, $locale)
    {
        // Before, assetData was an ID (only), so get_post() would work as-is
        // However, we modified assetData to include the subject so it can be used later on.
        // I don't want to touch the rest, so i'm doing a patch here. (╯°□°）╯ ┻━┻
        $getPostIdFromArray = function ($assetData) {
            if (is_numeric($assetData)) {
                return $assetData;
            } elseif (!empty($assetData['id']) && is_numeric($assetData['id'])) {
                return $assetData['id'];
            } elseif (!empty($assetData['asset']) && is_numeric($assetData['asset'])) {
                return $assetData['asset'];
            } else {
                // Default behaviour, it's possible you get weird behaviour
                // because get_post cast to int() if not a Post (entity) or object (creation).
                return $assetData;
            }
        };
        switch ($assetType) {
            case 'post':
                return new Post($user, $caption, get_post($getPostIdFromArray($assetData)), $locale); // quirk: this writes a new "sf_share" object to the db
                break;
            case 'post-from-publisher':
                return new PostFromPublisher($user, $caption, get_post($getPostIdFromArray($assetData)), $locale); // IS_A Post, same quirk.
                break;
            case 'product':
                return new Product($user, $caption, $assetData, $locale);
                break;
            case 'groupedProducts':
                return new GroupedProducts($user, $caption, $assetData, $locale);
            case 'image':
                return new ImageAsset($user, $caption, $assetData, $locale);
                break;
            case 'hello':
                return new Hello($user, $caption);
                break;
            case MixedAttachments::class:
                return new MixedAttachments($user, $caption, $assetData, $locale);
            case MixedAsset::class:
                return new MixedAsset($user, $caption, $assetData, $locale);
            case Template::class:
                return new Template($user, $caption, $assetData, $locale);
            case null:
                return new Shareable($user, $caption, $url, $locale);
                break;
            default:
                error_log('BUG: Publisher::publish: bad $assetType "' . $assetType . '". It must be either "product", "post", "post-from-publisher", "template", "image", "hello", "' . MixedAttachments::class . '", or null.');
                return false;
        }
    }

    /**
     * @param $user
     * @param $customers
     * @param $asset
     * @param null $caption
     * @param null $assetData
     * @param null $assetType
     * @param null $emailCustomerTagIds
     * @param null $locale
     * @return array of ["how many emails sent", "uniq_id of sf_events"]
     */
    private static function sendEmail(
        $user,
        $customers,
        $asset,
        $caption = null,
        $assetData = null,
        $assetType = null,
        $emailCustomerTagIds = null,
        $locale = null,
        $favoriteContacts = null,
        $uniqueId = null,
        $filters = null
    ) {
        global $wpdb, $app;

        list($store, $storeUser, $effectiveUser) = static::getStoreDependentFields($user);
        $groupTaskId = $assetData['groupTaskId'] ?? null;

        /**
         * Process (share) a list of addresses.
         * The reason we bulk it, it's because we can't put more than 100k in memory. So new we use a generator to
         * do it by bulk.
         *
         * @param array $addresses
         * @param int $totalEmailSent
         * @param $totalBlackoutEmails
         *
         * @return array|mixed
         */
        $processBulk = function (
            $addresses,
            &$totalEmailSent,
            &$totalBlackoutEmails
        ) use (
            $app,
            $user,
            $locale,
            $asset,
            $assetData,
            $assetType,
            $caption,
            $effectiveUser,
            $uniqueId,
            $groupTaskId
        ) {
            // SF-17047 Sendgrid CASL Unsub Sync
            // Strip blocked emails and send a rep notification email when applicable
            /** @var EmailBlockList $emailBlockList */
            $emailBlockList = $app['sf.mail.email_block_list'];
            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - email block list get Valid Emails And Trigger Notification",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession()
            ));
            $addresses = $emailBlockList->getValidEmailsAndTriggerNotification($addresses, $user->ID);
            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - email block list get Valid Emails And Trigger Notification complete- data: %s",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession(),
                print_r($addresses, true)
            ));

            /**
             * For email share, we'll track the contact's matching primary/secondary retailer customers' last timestamp of received email, and
             * drop the contact if the contact's matched retailer customer has received share email during the blackout
             * period(default is the last 7 days if blackout feature is enabled)
             *
             * @var RetailerCustomerNotificationBlackout $retailerCustomerBlackoutService
             */
            $retailerCustomerBlackoutService = $app['service.retailer.customer.blackout'];
            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - customer black out",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession()
            ));
            list($addresses, $blackoutRecipients) = $retailerCustomerBlackoutService
                ->processBlackout($addresses, $user->ID, RetailerCustomerCommunication::SOURCE_EMAIL);
            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - process bulk - customer black out complete- data: %s",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession(),
                print_r($addresses, true)
            ));
            $totalEmailSent += count($addresses);
            $totalBlackoutEmails += count($blackoutRecipients);

            // Returning null here will make event_uniq_id in sf_share_general empty
            if (empty($addresses)) {
                return null;
            }

            // We need to update the asset with the destination. it's use when you get the proxied url of a product
            $asset->setDestination(Shareable::DESTINATION_SHARE);

            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - prepare email template",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession()
            ));
            $emailObj = static::prepareEmailTemplate($user, $asset, $locale);


            if (!empty($assetData['email_subject'])) {
                $emailObj->set_dictionary('TITLE', $assetData['email_subject']);
            }

            $replyEmail = sf_get_email_address($effectiveUser);
            if ($groupTaskId !== null) {
                $replyEmail = sprintf('gt-%d-%s', $groupTaskId, $replyEmail);
            }

            $fromEmail = sf_get_email_address($effectiveUser, RETAILER_EMAIL_MODE);
            $fromName = static::getFromName($user);
            $trackingUrlPrefix = get_home_url() . "/sfadmin/tracker.php?id=";

            $emailTemplate = $asset->getMailTemplate();

            $locale = $app['service.lang']->getLocaleForOutboundEmails(
                $app['stores.manager'],
                $user->store,
                null,
                $locale
            );

            if (!empty($locale)) {
                $emailTemplate = $app['service.lang']->addLocaleToEmailTemplate($emailTemplate, $locale);
            }
            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - prepare email template complete- data: %s",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession(),
                print_r($emailTemplate, true)
            ));
            // Since we bulk, we need to keep a count of how much email we sent (save in sf_events)
            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - add messages to queue",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession()
            ));
            $eventId = static::addToQueue(
                $app,
                $caption,
                $assetData,
                $assetType,
                $emailTemplate,
                $fromName,
                $fromEmail,
                $trackingUrlPrefix,
                $user->id,
                $addresses,
                $replyEmail,
                $locale,
                $uniqueId
            );

            //@todo remove debug code below
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s -  process bulk - add messages to queue - data: %s",
                $user->ID,
                $user->employee_id,
                \Salesfloor\Services\DebugTools::getSession(),
                $eventId
            ));
            return $eventId;
        };

        $bulkAddress = [];
        $totalEmailSent = 0;
        $totalBlackoutEmails = 0;
        $eventId = $uniqueId ?? null; // In case we're using PrepareShareQueue

        //@todo remove debug code below
        addDebug('SF-30217', sprintf(
            "userId: %d - empId: %d - session: %s - process email addresses in bulk",
            $user->ID,
            $user->employee_id,
            \Salesfloor\Services\DebugTools::getSession()
        ));
        foreach (
            static::getEmailAddresses(
                $user,
                $customers,
                $emailCustomerTagIds,
                $locale,
                $favoriteContacts,
                $filters
            ) as $address
        ) {
            // Random bulk size that we know will fit in memory
            if (count($bulkAddress) >= 100 * self::PER_BATCH) {
                // For sf-share, only one valid(not empty) eventId need to be save, no matter which Bulk it came from
                $processBulk($bulkAddress, $totalEmailSent, $totalBlackoutEmails);
                $bulkAddress = [];
            }

            $bulkAddress[] = $address;
        }

        // Do we have some leftover addresses to process
        if (!empty($bulkAddress)) {
            $eventId = $processBulk($bulkAddress, $totalEmailSent, $totalBlackoutEmails);
        }

        if ((bool)$eventId) {
            $eventType = SF_EVENT_MAIL_SENT;
            $eventAttribute = $totalEmailSent;

            if ($assetType == 'groupedProducts') {
                $eventType = SF_EVENT_STYLED_LINK_SHARED_EMAIL;
                $eventAttribute = $asset->getTotalProducts();
            }

            sf_add_tracker(
                $eventType,
                "sf_share_mail",
                null,
                $effectiveUser->ID,
                null,
                $eventAttribute,
                $eventId
            );
            $wpdb->insert('sf_mail_stats', array("event_uniq_id" => $eventId), array("%s"));
        }
        return [$totalEmailSent, $eventId, $totalBlackoutEmails];
    }

    /**
     * This function has been added from PrepareShare to isolate any WP calls to this class
     * @param  int $userId
     * @return WP_User|bool
     */
    public static function getWordPressUser(int $userId)
    {
        return get_user_by('id', $userId);
    }

    private static function addToQueue(
        $app,
        $caption,
        $assetData,
        $assetType,
        $emailTemplate,
        $fromName,
        $fromEmail,
        $trackingUrlPrefix,
        $userId,
        $addresses,
        $replyEmail,
        $locale,
        $uniqueId
    ) {
        /** @var \Salesfloor\Services\MassMailer $mailer */
        $mailer = $app['service.massmailer'];
        $mailer->setUniqueId($uniqueId ?? uniqid('SFID', true)); // uniqueId is updated to avoid duplicates

        // @TODO Splitting into batches should be moved into the Mail/Client and the appropriate providers
        // That way each item in the queue represents one "burst"/"campaign", and the email provider that
        // will handle it will decide how to batch/split the requests appropriately
        // Can be done when code is refactored to store objects such as the email/template within S3
        $iterations = ceil(count($addresses) / self::PER_BATCH);
        $index = 0;
        for ($i = 0; $i < $iterations; $i++) {
            $eventId = $mailer->addToQueue(
                $caption,
                $assetData,
                $assetType,
                $emailTemplate,
                $fromName,
                $fromEmail,
                $trackingUrlPrefix,
                $userId,
                array_slice($addresses, $index, self::PER_BATCH),
                $replyEmail,
                // SF-16900 Add "mail_categories" to track Email Performance
                [MailClient::CATEGORY_PUBLISHER],
                $locale
            );
            $index += self::PER_BATCH;
        }

        return $eventId;
    }

    private static function getFromName($user)
    {
        global $app;
        $brandName = preg_replace('/[\'"]/', '', $app['configs']['retailer.brand_name']);

        $storeName = "";
        if ($user->store && $user->store != "" && $user->store != "0") {
            $store     = $app['stores.manager']->getStoreByUserId($user->id);
            $storeName = $store->name;
        }

        $repName = sf_get_rep_display_name($user);
        $isTeamMode = $app['configs']['retailer.storepage_mode'];
        $senderFormatKey = ($isTeamMode ? 'team' : 'rep') . '_mode_publisher';

        return $app['name_suggester']->formatSender($senderFormatKey, $app['locale'], $repName, $brandName, $storeName);
    }

    /**
     * returns list($store, $storeUser, $effectiveUser, $repTitle) for given $user.
     *
     * when called again for the same user, cached results are returned.
     */
    private static function getStoreDependentFields($user)
    {
        static $resultCache = array();

        if (!isset($resultCache[$user->ID])) {
            $store = sf_get_store_object_for_user($user);
            $storeUser = sf_get_store_for_user($user);
            $effectiveUser = $storeUser ? $storeUser : $user;
            $resultCache[$user->ID] = [$store, $storeUser, $effectiveUser];
        }

        return $resultCache[$user->ID];
    }

    private static function getEmailAddresses(
        $user,
        $customers,
        $emailCustomerTagIds = null,
        $locale = null,
        $favoriteContacts = null,
        $filters = null
    ) {
        global $wpdb, $app;

        if (isset($customers) && ($customers == 'all' || $customers == 'subscribers' || is_array($customers))) {
            if (!empty($emailCustomerTagIds) && !is_array($emailCustomerTagIds)) {
                $emailCustomerTagIds = explode(',', $emailCustomerTagIds);
            }

            // Do not rely on ES for filtering, this will not return all results (capped to 10k) and
            // ES is not the source of truth. Use ES only for full text search feature.

            // Must be static, because we are in a static context (╯°□°）╯ ┻━┻
            yield from static::getEmailAddressesForShare(
                $user,
                $customers,
                $locale,
                $emailCustomerTagIds,
                (bool)$favoriteContacts,
                $filters
            );
        }

        return [];
    }

    private static function getEmailAddressesForShare(
        $user,
        $customers,
        $locale,
        array $tagIds,
        bool $favorites,
        ?array $filters
    ) {
        global $app;

        // This is a protection to make sure we don't share to rep we aren't supposed too.
        if (empty($user) || empty($user->ID)) {
            return [];
        }

        list($store, $storeUser) = static::getStoreDependentFields($user);

        /** @var QueryBuilder $qB */
        $qB = $app['repositories.mysql']->getQueryBuilder();

        $mysqlFlagUpdated = $app['repositories.mysql']->disableOnlyFullGroupBy();

        $qB
            ->select(
                'c.email as email',
                'c.first_name as first_name',
                'c.last_name as last_name'
            )
            ->from('sf_customer', 'c')
            ->where(
                $qB->expr()->in('c.user_id', array_filter([
                    $user->ID,
                    $storeUser->ID ?? null,
                ]))
            )
        ;

        // This is legacy, but since this function is for share, why this is not hardcoded ?
        if ($customers === 'subscribers') {
            $qB->andWhere(
                $qB->expr()->eq('c.subcribtion_flag', 1)
            );
        }

        if (is_array($customers)) {
            $customerIds = static::sanitizeCustomerIds($customers);
            if (empty($customerIds)) {
                return;
            }

            $qB->andWhere(
                $qB->expr()->in(
                    'c.ID',
                    $customerIds
                )
            );
        }

        if (!empty($locale) && $app['service.multilang']->isI18N()) {
            $qB->andWhere(
                $qB->expr()->eq('c.locale', $qB->expr()->literal($locale))
            );
        }

        if ($favorites) {
            $qB->innerJoin(
                'c',
                'sf_customer_favorites',
                'cf',
                $qB->expr()->andX(
                    $qB->expr()->eq(
                        'c.ID',
                        'cf.customer_id'
                    ),
                    $qB->expr()->eq(
                        'cf.is_favorite',
                        1
                    )
                )
            );
        }

        // $tagIds looks like:
        // {
        //     "ids": "8,9,10,11,12,13,14,1,2,3,4,5,6,7",
        //     "filter": "or"
        // }
        if (!empty($tagIds)) {
            $filter = $tagIds[CustomerTag::FILTER_KEY] ?? CustomerTag::FILTER_AND;
            $tagIds = $app['service.util']->convertTagIdsToRealArray($tagIds['ids']);
            $numTags = count($tagIds);
            $qB->innerJoin(
                'c',
                'sf_customer_tags_relationships',
                'ctr',
                $qB->expr()->andX(
                    $qB->expr()->eq(
                        'c.ID',
                        'ctr.customer_id'
                    ),
                    $qB->expr()->in(
                        'ctr.tag_id',
                        $tagIds
                    )
                )
            );
            if ($numTags > 1 && $filter === CustomerTag::FILTER_AND) {
                $qB->groupBy('c.email');
                $qB->having(
                    $qB->expr()->eq(
                        'COUNT(DISTINCT ctr.tag_id)',
                        count($tagIds)
                    )
                );
            }
        }

        if ($filters !== null) {
            foreach ($filters as $filterKey => $filterDetails) {
                switch ($filterKey) {
                    case 'transaction':
                        self::applyTransactionFilters($qB, $filterDetails);
                        break;
                    case 'address':
                        self::applyAddressFilters($qB, $filterDetails);
                        break;
                }
            }
            $qB->groupBy('c.email');
        }

        $stmt = $qB->execute();

        $app['repositories.mysql']->enableOnlyFullGroupBy($mysqlFlagUpdated);

        while ($row = $stmt->fetch()) {
            yield $row;
        }
    }

    private static function applyTransactionFilters(QueryBuilder $qb, array $filterDetails)
    {
        $qb->leftJoin('c', 'sf_rep_transaction', 'rep_t', 'c.ID = rep_t.customer_id and rep_t.trx_type = :sale')
            ->leftJoin('rep_t', 'sf_rep_transaction_detail', 'rep_d', 'rep_t.trx_id = rep_d.trx_id')
            ->leftJoin('c', 'sf_customers_to_retailer_customers', 'ctrc', 'c.ID = ctrc.customer_id')
            ->leftJoin('ctrc', 'sf_retailer_customers', 'rc', 'ctrc.retailer_customer_id = rc.id')
            ->leftJoin(
                'rc',
                'sf_retailer_transaction',
                'retailer_t',
                'rc.customer_id = retailer_t.customer_id and retailer_t.trx_type = :sale'
            )->leftJoin(
                'retailer_t',
                'sf_retailer_transaction_details',
                'retailer_d',
                'retailer_t.trx_id = retailer_d.trx_id'
            )->setParameter('sale', Transactions::TRANSACTION_TYPE_SALE);

        foreach ($filterDetails as $transactionKey => $transactionFilters) {
            switch ($transactionKey) {
                case 'total':
                    self::applyTotalFilters($qb, $transactionFilters);
                    break;
                case 'date':
                    self::applyDateFilters($qb, $transactionFilters);
                    break;
                case 'brand':
                    self::applyBrandFilter($qb, $transactionFilters);
                    break;
                case 'category_id':
                    self::applyCategoryIdFilter($qb, $transactionFilters);
                    break;
            }
        }
    }

    private static function applyAddressFilters(QueryBuilder $qb, array $filterDetails)
    {
        $qb->leftJoin('c', 'sf_customer_addresses', 'a', 'c.ID = a.customer_id');
        foreach ($filterDetails as $addressKey => $addressFilters) {
            $addresses = explode(',', $addressFilters);
            switch ($addressKey) {
                case 'state':
                    $qb->andWhere('a.state in (:states)')
                        ->setParameter('states', $addresses, ArrayParameterType::STRING);
                    break;
                case 'city':
                    $qb->andWhere('a.city in (:cities)')
                        ->setParameter('cities', $addresses, ArrayParameterType::STRING);
                    break;
            }
        }
    }

    private static function applyTotalFilters(QueryBuilder $qb, array $transactionFilters)
    {
        if (!empty($transactionFilters['from'])) {
            $qb->andWhere('rep_t.trx_total >= :from or retailer_t.trx_total >= :from')
                ->setParameter('from', $transactionFilters['from']);
        }
        if (!empty($transactionFilters['to'])) {
            $qb->andWhere('rep_t.trx_total <= :to or retailer_t.trx_total <= :to')
                ->setParameter('to', $transactionFilters['to']);
        }
    }

    private static function applyDateFilters(QueryBuilder $qb, array $transactionFilters)
    {
        if (!empty($transactionFilters['start'])) {
            $start = Carbon::parse($transactionFilters['start'], 'UTC');
            $qb->andWhere('rep_t.trx_date >= :start or retailer_t.trx_date >= :start')
                ->setParameter('start', $start->startOfDay()->toDateTimeString());
        }
        if (!empty($transactionFilters['end'])) {
            $end = Carbon::parse($transactionFilters['end'], 'UTC');
            $qb->andWhere('rep_t.trx_date <= :end or retailer_t.trx_date <= :end')
                ->setParameter('end', $end->endOfDay()->toDateTimeString());
        }
    }

    private static function applyBrandFilter(QueryBuilder $qb, string $brands)
    {
        global $app;
        if (!empty($brands)) {
            if ($app['configs']['products.expanded_variants.enabled']) {
                $qb->leftJoin('rep_d', 'sf_product_variants', 'rep_p', 'rep_p.sku = rep_d.sku ');
                $qb->leftJoin('retailer_d', 'sf_product_variants', 'retailer_p', 'retailer_p.sku = retailer_d.sku ');
            } else {
                $qb->leftJoin('rep_d', 'sf_products', 'rep_p', 'rep_p.product_id = rep_d.product_id');
                $qb->leftJoin(
                    'retailer_d',
                    'sf_products',
                    'retailer_p',
                    'retailer_p.product_id = retailer_d.product_id'
                );
            }
            $qb->andWhere('rep_p.brand in (:brands) or retailer_p.brand in (:brands)')
            ->setParameter(
                'brands',
                explode(',', $brands),
                ArrayParameterType::STRING
            );
        }
    }

    private static function applyCategoryIdFilter(QueryBuilder $qb, string $categoryIds)
    {
        if (!empty($categoryIds)) {
            $qb->leftJoin(
                'rep_d',
                'sf_product_category_map',
                'rep_pc',
                'rep_d.product_id = rep_pc.product_id'
            )->leftJoin(
                'retailer_d',
                'sf_product_category_map',
                'retailer_pc',
                'retailer_d.product_id = retailer_pc.product_id'
            )
            ->andWhere('rep_pc.category_id in (:category_ids) or retailer_pc.category_id in (:category_ids)')
            ->setParameter(
                'category_ids',
                explode(',', $categoryIds),
                ArrayParameterType::STRING
            );
        }
    }

    private static function sanitizeCustomerIds($customers)
    {
        $customerIds = array();
        foreach ($customers as $c) {
            if (ctype_digit("$c")) {
                $customerIds[] = $c;
            }
        }
        return $customerIds;
    }

    public static function prepareEmailTemplate($user, $asset, $locale)
    {
        global $app;

        // We need to pass locale from the constructor because some general variable (UNSUBSCRIBE_EXTERNAL_LINK)
        // are set in this step. We could, in theory, do it manually after; but i preferred to do it in the "base"
        // since it's generic in all templates.
        $emailObj = new Salesfloor_Template($user, null, $locale);
        $emailObj->set_dictionary('LOCALE', $locale);
        $emailObj->set_dictionary('TITLE', $asset->getEmailSubject());

        $caption   = $asset->getCaption();

        if ((get_class($asset) == 'GroupedProducts')) {
            $introText = $caption;
        } else if (empty($caption)) {
            $introText = str_replace("\n", "<br>\n", $asset->getEmailIntro());
        } else {
            $introText = \Salesfloor\Services\Util::formatTextForEmail($caption);
        }

        $emailObj->set_dictionary('TEXT', $introText);
        $emailObj->set_dictionary('PREVIEWTEXT', $asset->getEmailIntro());

        list($store, $storeUser, $effectiveUser) = static::getStoreDependentFields($user);
        if ($store && isset($store->name)) {
            $emailObj->set_dictionary('STORENAME', $store->name);
        }

        $emailObj->set_dictionary('EMAIL', sf_get_email_address($effectiveUser, RETAILER_EMAIL_MODE));
        $emailObj->set_dictionary('USERLOGIN', $effectiveUser->user_login);
        $emailObj->set_dictionary('AVATAR', sf_get_rep_image($user));

        if (is_a($asset, 'Product')) {
            $emailObj->set_dictionary('URL', $asset->getUrl(true));
            $emailObj->set_dictionary('PRODUCT_ROWS', sf_render_products($asset->mockPostData(), $effectiveUser, false, $user, $locale));
        } else if (get_class($asset) == 'GroupedProducts') {
            $emailObj->groupedProductsOn = true;
            $emailObj->set_dictionary('groupedProductsList', $asset->getFormattedProducts());
            $emailObj->set_dictionary('GROUPED_PRODUCTS_LINK', $asset->getGroupedProductsLink($user));
            $emailObj->set_dictionary('mailTitleMessage', 'retailer.email.compose.base_curation.title_update');
            $emailObj->set_dictionary('photos', [['src' => $asset->imageAsset->getImage()]]);
            $emailObj->set_dictionary('CONTENT_IS_HTML',  \Salesfloor\Services\Util::containsHtml($introText));

        } else if (is_a($asset, 'Post')) {
            if ($asset->isProductPost()) {
                $emailObj->set_dictionary('PRODUCT_ROWS', sf_render_products($asset->getBasePostData(), $effectiveUser, false, $user, $locale));
            } else {
                $emailObj->set_dictionary('CONTENT', $asset->getContent());
            }
        } else if (is_a($asset, 'ImageAsset')) {
            $emailObj->set_dictionary('CONTENT', $asset->getContent());
        } else if (is_a($asset, MixedAttachments::class)) {
            /** @var MixedAttachments $asset */
            $emailObj->set_dictionary('URL', $asset->getUrl());
            $emailObj->set_dictionary('REP_UPLOADED_PHOTO', $asset->imageAsset->getContent());
            $emailObj->set_dictionary('ASSET_CONTENT', '');

            $productRows = '';
            if (!empty($asset->product)) {
                $productRows = sf_render_products($asset->product->mockPostData(), $effectiveUser, false, $user, $locale);
            } else if (!empty($asset->post)) {
                $productRows = sf_render_products(json_decode($asset->post->getContent(), true), $effectiveUser, false, $user, $locale);
            }
            $emailObj->set_dictionary('PRODUCT_ROWS', $productRows);
        } else if (is_a($asset, MixedAsset::class)) {
            /** @var MixedAsset $asset */
            $emailObj->set_dictionary('URL', $asset->getUrl());
            $emailObj->set_dictionary('REP_UPLOADED_PHOTO', '');
            $emailObj->set_dictionary('ASSET_CONTENT', $asset->asset->isProductPost()
                ? sf_render_products($asset->asset->getBasePostData(), $effectiveUser, false, $user, $locale)
                : $asset->asset->getContent());

            $productRows = '';
            if (!empty($asset->product)) {
                $productRows = sf_render_products($asset->product->mockPostData(), $effectiveUser, false, $user, $locale);
            } else if (!empty($asset->post)) {
                $productRows = sf_render_products(json_decode($asset->post->getContent(), true), $effectiveUser, false, $user, $locale);
            }
            $emailObj->set_dictionary('PRODUCT_ROWS', $productRows);
        } else if (is_a($asset, Template::class)) {
            /** @var Template $asset */
            // {URL} => This seems to be only for the hello_world template

            // This is the only obj i have access when i render the template, so i will add alternate template to it
            $emailObj->alternateTemplate = $asset->getAlternateTemplate();
        } else {
            $emailObj->set_dictionary('CONTENT', '');
            $emailObj->set_dictionary('ACTION', str_replace('%7BREPPAGE%7D', '{REPPAGE}', stripDoctype((new Salesfloor_Template($user))->render('call_to_action', $locale))));
        }

        return $emailObj;
    }

    private static function shareToSocialNetworks($user, $socialNetworks, $asset)
    {
        foreach ($socialNetworks as $socnet) {
            $clazz = false;
            switch ($socnet) {
                case 'facebook':
                    $clazz = 'FacebookClient';
                    break;
                case 'linkedin':
                    $clazz = 'LinkedInClient';
                    break;
                case 'twitter':
                    $clazz = 'TwitterClient';
                    break;
                case 'instagram':
                    $clazz = 'InstagramClient';
                    break;
                case 'pinterest':
                    $clazz = 'PinterestClient';
                    break;
            }
            if ($clazz) {
                try {
                    $client = new $clazz($user);
                    $ret['result'] = $client->share($asset);
                    $ret[$socnet] = true;
                    sf_add_tracker(SF_EVENT_SOCIAL_POST, $socnet, null, $user->ID, null, 1, null);
                } catch (Exception $e) {
                    // WEMAY: provide the user with an explanation; $e has an error message.
                    // Is it useful to the user though? Assuming it isn't, for now.
                    error_log($e);
                    $ret[$socnet] = false;
                }
            }
        }

        return $ret;
    }

    private static function postToStorefront($user, Post $post)
    {
        global $wpdb;

        list($store, $storeUser, $effectiveUser) = static::getStoreDependentFields($user);
        $relativeLink = $post->getRelativeUrl();

        // Delete if the item exist so we can add it after without creating duplicate
        $current_content = $wpdb->get_results(
            $wpdb->prepare(
                "delete from sf_content where url = '%s' and  user_id = %d;",
                array(
                    $relativelink,
                    $effectiveUser->ID,
                )
            )
        );

        // We need to get the previous content out of the way first.
        $current_content = $wpdb->get_results(
            $wpdb->prepare(
                "select ID from sf_content where user_id = %d and position != -1 order by position asc;",
                array(
                    $effectiveUser->ID,
                )
            )
        );

        // From this list, increase the position for each element,
        // All element after the third are set to -1 since they are overflow
        // TODO: Here this is a multiple query which are not atomic meaning several simulaneouse event will cause
        // TODO  unpredicted result. This should not break the overall functionality but might bw improved.

        $new_position = 1;
        foreach ($current_content as $element) {
            $wpdb->get_results(
                $wpdb->prepare(
                    "update sf_content set position = %d where ID = %d;",
                    array(
                        ($new_position < 4) ? $new_position++ : -1,
                        $element->ID
                    )
                )
            );
        }

        $apt_utctz = new DateTimeZone('UTC');
        $currentTime = new DateTime('NOW');
        $currentTime->setTimezone($apt_utctz);

        // We have shifted all the elements. The position 0 is now available for us.
        $basePost = $post->getBasePost();
        $wpdb->replace(
            "sf_content",
            array( // Data
                "user_id"      => $effectiveUser->ID,
                "from_user_id" => $user->ID,
                "post_id"      => $basePost->ID,
                "type"         => 'page',
                "image"        => $post->getImage(),
                "description"  => $post->getDescription(),
                "url"          => $post->getUrl(),
                "position"     => 0,
                "date"         => $currentTime->format('Y-m-d H:i'),
                "title"        => $post->getTitle(),
            ),
            array( // Data format
                "%d",
                "%d",
                "%d",
                "%s",
                "%s",
                "%s",
                "%s",
                "%d",
                "%s",
                "%s"
            )
        );

        return true;
    }

    /**
     * Function will trigger POST to the API for tracking a recommendation
     * of a product through Share / Publish SF-15174
     * @param  object  $user                  User object
     * @param  string  $assetType             Asset type received in the publish function (post / product / hello / image/ ...)
     * @param  object  $asset                 Asset object. Will be interpreted if type is Post / Product
     * @param  array   $recipients            Array of recipients details. Specifies where the share will be published
     * @param  integer $numberRecommendations Number of recommendations when publishing by email
     * @return boolean                        True if a tracking event was triggered and successfully tracked
     */
    private static function trackProductRecommendation($user, $assetType, $asset, $recipients, $numberRecommendations = 1)
    {
        global $app;
        // Validate necessary inputs
        if (empty($asset) || empty($recipients) || empty($assetType)) {
            return false;
        }
        $products = [];
        $channels = [];

        // Decide on the channels that were used based on the $recipients
        if (isset($recipients['email'])) {
            $channels[] = 'share-email';
        }
        if (!empty($recipients['social'])) {
            if (in_array('twitter', $recipients['social'])) {
                $channels[] = 'share-twitter';
            }
            if (in_array('facebook', $recipients['social'])) {
                $channels[] = 'share-facebook';
            }
        }

        if (empty($channels)) {
            return false;
        }

        // Decipher the product SKU / ID depending on the assetType (asset will be a Post or a Product)
        switch ($assetType) {
            case 'post':
            case 'post-from-publisher':
                // We overwrite assetType, but asset it still the old way. No needs to handle it differently
            case 'Template':
                if (is_a($asset, 'Post')) {
                    $postContent = $asset->getBasePostData();
                    //@todo remove debug code below
                    addDebug('SF-30217', sprintf(
                        "userId: %d - empId: %d - session: %s - decipher product sku/id from template - data: %s",
                        $user->ID,
                        $user->employee_id,
                        \Salesfloor\Services\DebugTools::getSession(),
                        print_r($postContent, true)
                    ));
                    if (!empty($postContent['objects']) && !empty($postContent['type']) && in_array($postContent['type'], ['product', 'products'])) {
                        foreach ($postContent['objects'] as $object) {
                            if (!empty($object['type']) && $object['type'] == 'product' && !empty($object['id'])) {
                                // Get variant sku first, if it is empty, we'll get product Id
                                if (!empty($object['variantSku'])) {
                                    $products[] = $object['variantSku'];
                                } elseif (!empty($object['id'])) {
                                    $products[] = $object['id'];
                                }
                            }
                        }
                    }
                } else {
                    //@todo remove debug code below
                    addDebug('SF-30217', sprintf(
                        "userId: %d - empId: %d - session: %s - decipher product sku/id from AlternateTemplate ",
                        $user->ID,
                        $user->employee_id,
                        \Salesfloor\Services\DebugTools::getSession()
                    ));
                    $templateProducts = $asset->getAlternateTemplate()->getProducts();

                    //@todo remove debug code below
                    addDebug('SF-30217', sprintf(
                        "userId: %d - empId: %d - session: %s - decipher product sku/id from AlternateTemplate retrieved - data: %s",
                        $user->ID,
                        $user->employee_id,
                        \Salesfloor\Services\DebugTools::getSession(),
                        print_r($templateProducts, true)
                    ));

                    foreach ($templateProducts as $templateProduct) {
                        if (!empty($templateProduct['variantSku'])) {
                            $products[] = $templateProduct['variantSku'];
                        } elseif (!empty($templateProduct['sku'])) {
                            $products[] = $templateProduct['sku'];
                        }
                    }
                }
                break;
            case 'product':
                //@todo remove debug code below
                addDebug('SF-30217', sprintf(
                    "userId: %d - empId: %d - session: %s - decipher product sku/id from product",
                    $user->ID,
                    $user->employee_id,
                    \Salesfloor\Services\DebugTools::getSession()
                ));
                if (is_a($asset, 'Product')) {
                    // Get variant sku first
                    $productSku = $asset->getVariantSku();
                    // Not a variant, use the parent product_id
                    if (empty($productSku)) {
                        $productSku = $asset->getSku();
                    }

                    // If we've found a sku/product_id
                    if (!empty($productSku)) {
                        $products[] = $productSku;
                    }
                }
                break;
        }

        // If we have successfully deciphered / found products, attempt to send a POST to the API to track the recommendations
        if (!empty($products)) {
            $products = array_unique($products);
            $postData = [
                'user_id'           => $user->ID,
                'channels'          => $channels,
                'product_id'        => $products,
                'n_recommendations' => $numberRecommendations
            ];

            try {
                $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);
                //@todo remove debug code below
                addDebug('SF-30217', sprintf(
                    "userId: %d - empId: %d - session: %s - begin insert tracking recommendations - data: %s",
                    $user->ID,
                    $user->employee_id,
                    \Salesfloor\Services\DebugTools::getSession(),
                    print_r($postData, true)
                ));
                $sfClient->insertJSON("recommendations/track", $postData);
                return true;
            } catch (\Exception $e) {
                error_log('Error Tracking Recommendation: ' . $e->getMessage());
            }
        }

        return false;
    }
}
