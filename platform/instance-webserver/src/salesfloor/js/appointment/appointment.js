/*
 * Salesfloor Inc. Property
 * Created on : Jan 14, 2014, 10:00:44 AM
 * Author     : <PERSON>
 */
define([
  'jquery',
  'moment/moment-with-locales.min',
  'jstimezone',
  'mobiledetect',
  'conditionizr',
  'handlebars',
  'ui',
  'inscription/inscription',
  'utils',
  'services/services',
], function($, moment, jstz, MobileDetect, conditionizr, Handlebars, ui, inscription, utils, Services) {
  var Salesfloor = window.Salesfloor || {};

  Salesfloor.Appointment = {
    externalIframeControl: function() {
      $.modal.close();
    },

    iframeControl: function(ctn) {
      var $this = $('body').find('#bookAnAppointment').contents();
      if ($(ctn).height() <= 600) {
        $this.find('.content-ctn').addClass('is-mobile');
      }

      $this.find('.fn-trigger-close').on('click', function() {
        $.modal.close();
      });
    },

    bookAppointment: function(options) {
      var _options = {
        $ctn: $('.fn-appointment'),
        trigger: '.fn-appointment-trigger',
        triggerClose: '.fn-trigger-close',
        iframeUrl: '',
        iframeHeight: Salesfloor.Conf.iframes.appointment.height,
        iframeWidth: '486',
        iframeTop: '30',
        auto: false
      };

      $.extend(_options, options);

      if (_options.$ctn.length === 0) {
        return false;
      }

      var source = window.top;
      var entryURL = '/sfadmin/appointment.php?from=storefront' +
        '&sf_locale=' + utils.getQueryParam('sf_locale') +
        '&source_url=' + encodeURIComponent(source.location) +
        '&source_title=' + encodeURIComponent(source.document.title);

      if (Salesfloor !== undefined && Salesfloor.Environement !== undefined && Salesfloor.Environement.repname !== undefined) {
        // Add the Rep information if we have it
        entryURL += '&repname=' + Salesfloor.Environement.repname;
      } else {
        entryURL = 'iframe/appointment.php';
      }
      
      _options.iframeUrl = entryURL;
      var iframeHeight = _options.iframeHeight;
      var iframeWidth = Salesfloor.Conf.serviceVersion === 2 ? '386' : _options.iframeWidth;
      var position = [_options.iframeTop + 'px'];
      var version = Salesfloor.Conf.serviceVersion;

      var render = function() {
        var md = new MobileDetect(window.navigator.userAgent);

        if (md.phone() || md.tablet()) {
          window.location = '/sfadmin/appointment.php?repname=' + Salesfloor.Environement.repname + '&from=storefront&version=' + version;
        } else {
          return new ui.modal({
            external: true,
            $template: '<iframe id="bookAnAppointment" class="box box-type-4" src="' + _options.iframeUrl + '&version=' + version + '" height="' + iframeHeight + '" width="' + iframeWidth + '" onload="Salesfloor.Appointment.iframeControl(this)" scrolling="no" seamless>',
            position: position,
            minWidth: null
          });
        }
      };

      _options.$ctn.each(function() {
        if (_options.auto && !sessionStorage.getItem('appointment')) {
          sessionStorage.setItem('appointment', '1');
          return render();
        } else {
          $(this).find(_options.trigger).each(function() {
            $(this).on('click', function(e) {
              e.preventDefault();
              return render();
            });
          });
        }
      });
    },

    selectDate: function(options) {
      var _options = {
        $ctn: $('.fn-date'),
        content: '#modal-generic-ctn',
        form: '.fn-date-form',
        trigger: '.fn-date-trigger',
        resultTemplate: $('#result-message-template'),
        resultTarget: '.fn-result-message',
        dateSelectorTarget: '.fn-date-selector',
        calendarInputI18n: '.fn-calendar-i18n',
        calendarInput: '.fn-date-selector-calendar',
        calendarInputValue: '.fn-date-selector-calendar-choosen',
        timePlaceholder: '.fn-time-placeholder',
        timeStartClass: 'fn-time-start',
        timeEndClass: 'fn-time-end',
        datePlaceholder: '.fn-date-placeholder',
        dateInfoPlaceholder: '.fn-date-info-placeholder',
        dateChosen: '.fn-choosen-date',
        timeChoosen: '.fn-choosen-time',
        durationChoosen: '.fn-choosen-duration',
        timepickerFormat: 'g:i A',
        timezonePlaceholder: '.fn-timezone',
        triggerSave: '.fn-trigger-save',
        triggerClose: '.fn-trigger-close',
        $serviceCtn: $('.fn-service'),
        serviceList: '.fn-service-list',
        $storeLocation: $('.fn-service-store-location'),
        serviceTrigger: '.fn-trigger-service',
        serviceTriggerIcon: '.fn-trigger-service-icon',
        serviceAnimateContent: '.fn-service-animate',
        serviceAnimateElement: '.fn-service-animate-element',
        checkboxAutoSubscribe: '.fn-autosubscribe-checkbox',
        loader: '.fn-date-selector-loader',
        $selectedSpecialty: '#selectedSpecialty',
        $choosenDate: '#choosenDate',
        errorClass: '.icon-close'
      };

      /**
       * Retrieve locale from dom.
       *
       * @param $ctn
       */
      var getLocaleFromCtn = function ($ctn) {
        if ($ctn.find(_options.calendarInputI18n).data('locale')) {
          return $ctn.find(_options.calendarInputI18n).data('locale').replace('_', '-').toLocaleLowerCase();
        }
      };

      // get the default message from the time slots dropdown
      var setTimeMessage = $('#choosenTime > option').html();

      $.extend(_options, options);

      if (_options.$ctn.length === 0) {
        return false;
      }

      var $ctn = _options.$ctn;
      var dateChosen;
      var timeChoosen;
      var durationChoosen;
      var formatedDateChoosen;
      var md = new MobileDetect(window.navigator.userAgent);
      var tz = jstz.determine();
      var origins = {};

      // configure moment with locales
      moment.locale(getLocaleFromCtn($ctn));

      $(window).on('message', function(event) {
        var data = services.returnProperParsing(event.originalEvent.data);
        //On storefront, when appointment service is clicked, load the updated DateOverrides
        //to prevent error message when there is limitation of appointments per timeslot.
        if (data.action === 'reloadAppointmentDateOverrides' && isStoreAppointmentHoursEnabled) {
          clearDatePicker();
          getDateOverrides();
        }

        if (data.action === 'storefrontHandShake' || data.action === 'landingHandShake') {
          origins = {
            parent: event.originalEvent.origin,
            source: event.originalEvent.source,
            container: data.container
          };

          // If we need to hack from other domain and there is a race condition
          // we will have to setInterval handshake and here call to stop it once done
          var response = JSON.stringify({
            action: 'stopIntervalCheck',
            service: 'appointment',
            nav: true
          });
          origins.source.postMessage(response, origins.parent);

          //handle iframe height changes
          services.setIframeCommunication(origins, 'appointment');
        }
      });

      var mandatoryFields = {};

      var services = new Services({
        el: $('.fn-date'),
        btnProp: $('.js-form-btn'),
        mandatoryFields: mandatoryFields,
        locale: utils.getQueryParam('sf_locale')
      });

      var resultMessage = function($ctn, $source, target) {
        //Disable SCHEDULE button
        $ctn.find(_options.trigger).addClass('is-disabled');

        var source = $source.html();
        var template = Handlebars.compile(source);
        var result = template();
        var rep = $('#repname').val();

        setTimeout(function() {
          if (origins.parent && origins.container === 'landing') {
            var data = JSON.stringify({
              action: 'resetView'
            });
            origins.source.postMessage(data, '*');
          } else if (md.phone() || md.tablet()) {
            if (Salesfloor.Environement !== undefined && Salesfloor.Environement.chat && Salesfloor.Environement.chat === true) {
              close();
              return false;
            }

            var sourceUrl = utils.getQueryParam('source_url');

            if (sourceUrl) {
              window.location.assign(sourceUrl);
            }
            return false;
          } else if (origins.parent) {
            var data = JSON.stringify({
              action: 'modalClose',
              container: origins.container
            });
            origins.source.postMessage(data, '*');
            window.location.reload();
          } else if (window.parent.Salesfloor !== undefined && window.parent.Salesfloor.Environement !== undefined && window.parent.Salesfloor.Environement.context !== undefined && window.parent.Salesfloor.Environement.context === 'reppage') {
            window.parent.Salesfloor.Appointment.externalIframeControl();
            close(); // close after resultMessage when sent from sidebar2/footer on desktop
          } else if ($('#customer_reschedule').length > 0) {
            var customer = $('#customer_reschedule').val();

            // If the rep accessed this page, we redirect to his backoffice page
            if (customer === 'true') {
              window.location = '/' + rep;
            } else {
              window.close();
            }
          }
        }, 3000);

        var finishedAction = JSON.stringify({
          action: 'scrollToTop',
        });

        if (origins.source) {
          origins.source.postMessage(finishedAction, origins.parent);
        }

        return $ctn.parents(target).html(result);
      };

      var togglePhoneState = function (element) {
        $('#' + element).toggleClass('valid').toggleClass('error');
      };

      var registerAppointment = function($ctn, appointment, data){
        var  apiURL = '/sfadmin/appointment.php';
          //GA tracking click events submit appointment form
          gtag('event', 'click', {
            'event_category': 'bookappointment_submit',
            'event_label': 'send btn was clicked',
          }); 

        utils.registerAcquisition('book-appointment');
        // SF-16318 Log visit touch points to new sf_event_log
        utils.eventQueue.pushServiceEvent('APPOINTMENT');
        appointment = appointment + '&fingerprint=' + window.sf_widget.utils.dataStorage.get(window.sf_widget.utils.eventQueue.fingerprintKey);
        
        services.disableForm();

        return $.ajax({
          type: 'POST',
          url: apiURL,
          dataType: 'json',
          data: appointment
        }).done(function(response) {
          if (response.errors) {
            services.enableForm();

            var errors = response.errors;
            if (Number(errors.code) === 101) {
              // Invalid phone number
              var element = $('#contactTextMessage').is(':checked') ? 'sms' : 'phone';

              togglePhoneState(element);
              $('#' + element).val('').focus();

              // Force flag to be reset
              $('#' + element).intlTelInput('setNumber', '');
              validateForm();
            }
          } else {
            $(_options.triggerSave).prop('disabled', false);

            //Thank you message
            if ($('select[name=store]').length > 0) {
              var storeName = $('select[name=store] option:selected').val();

              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_sidebar_store', value: storeName, expires: Salesfloor.Conf.saleCookieExpire });
              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking', value: 'true', expires: Salesfloor.Conf.saleCookieExpire });
              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_store', value: storeName, expires: Salesfloor.Conf.saleCookieExpire });
              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_rep', value: '', expires: 'remove '});
            } else {
              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking', value: 'true', expires: Salesfloor.Conf.saleCookieExpire });
              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_rep', value: window.rep_name, expires: Salesfloor.Conf.saleCookieExpire });
              window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_store', value: '', expires: 'remove' });
            }

            services.clearForm();
            resultMessage($ctn, _options.resultTemplate, _options.resultTarget);
          }
        }).statusCode({
          400: function () { alert('Page expired; please reload'); },
          406: function(e) {
            var errorMessage = JSON.parse(e.responseText || '{}').message;
              if (!errorMessage) {
                return;
              }

              $(_options.errorClass).html(errorMessage).removeClass('hidden').css('color', 'red');
              // clear calendar and timeslots by calling the clearDatePicker method
              // do a get request in order reload dynamic dates into the calendar
              // disbale the send request button
              // resize the iframe
              clearDatePicker();
              getDateOverrides();
              $(_options.triggerSave).prop('disabled', true);
              services.manageIframeHeight();
              errorOccured = true;
            }
        }).fail(function() {
          //Thank you message
          if ($('select[name=store]').length > 0) {
            var storeName = $('select[name=store] option:selected').val();

            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_sidebar_store', value: storeName, expires: Salesfloor.Conf.saleCookieExpire });
            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking', value: 'true', expires: Salesfloor.Conf.saleCookieExpire });
            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_store', value: storeName, expires: Salesfloor.Conf.saleCookieExpire });
            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_rep', value: '', expires: 'remove '});
          } else {
            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking', value: 'true', expires: Salesfloor.Conf.saleCookieExpire });
            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_rep', value: window.rep_name, expires: Salesfloor.Conf.saleCookieExpire });
            window.sf_widget.utils.dataStorage.set({ name: 'sf_wdt_tracking_store', value: '', expires: 'remove' });
          }

          services.enableForm();
          console.warn('Can\'t push appointment');
        });
      };

      var getDateOverrides = function() {
        var storeId = $('#store').val();
        var userId = $('#user_id').val();
        var apiUrl = Salesfloor.Conf.restApi + '/v2/public/stores/' + storeId + '/store-appointment-hours-overrides?repId=' + userId;
        $.ajax({
          type: 'GET',
          url: apiUrl,
          dataType: 'json'
        }).done(function (response) {
          storeAppointmentDateOverrides = response;
        }).fail(function(e) {
        })
      }

      var defineSelectedDate = function($ctn, day, month, year, date) {
        var fancyDateFormat = getRegionalFancyDateFormat($ctn);

        formatedDateChoosen = moment([year, month, day]).format(fancyDateFormat);
        dateChosen = year + '-' + (month + 1) + '-' + day;

        return {
          newDate: $ctn.find(_options.calendarInput).val(formatedDateChoosen),
          newTimes: setTimepicker($ctn, date)
        };
      };

      // SF-30114 Feature - define store appointment hours (configs['retailer.store_appointment_hours.is_enabled'] = true)
      var isStoreAppointmentHoursEnabled = Salesfloor.Conf.storeAppointmentHoursIsEnabled;
      var storeAppointmentHoursData;
      var storeAppointmentDateOverrides;
      var storeAppointmentHourTimeSlots = [];
      var lastTimeSlot;
      var timeSlots;
      var dateOverrideFlag;
      var errorOccured = false;
      
      if (isStoreAppointmentHoursEnabled) {
        storeAppointmentHoursData = appointmentHours;
        storeAppointmentDateOverrides = appointmentDateOverrides;
      }

      // this function clears calendar and timeslots
      var clearDatePicker = function() {
        // clear calendar
        $('.fn-date-selector-calendar').val('')
        // remove all the options from the dropdown accept the first
        // set the first option to setTime message
        $('#choosenTime > option').not(':first').remove();
        $('#choosenTime > option').html(setTimeMessage);
      }

      var setStartEndTime = function(timeSlots) {
        storeAppointmentHourTimeSlots = [];
        timeSlots && timeSlots.forEach(function(timeslot) {
          storeAppointmentHourTimeSlots.push({
            startHour: parseInt(moment(timeslot.start_time,'HH:mm:ss').hour()),
            startMinute: parseInt(moment(timeslot.start_time,'HH:mm:ss').minutes()),
            endHour: parseInt(moment(timeslot.end_time,'HH:mm:ss').hour()),
            endMinute: parseInt(moment(timeslot.end_time,'HH:mm:ss').minutes())
          });
        });
      };

      var findDayOfTheWeek = function(date) {
        return moment(new Date(date)).locale('en').format('dddd').toLowerCase();
      };

      var findStartEndTime = function(date) {
        dateOverrideFlag = false;
        if (storeAppointmentDateOverrides.length > 0) {
          for (var j = 0; j < storeAppointmentDateOverrides.length; j++) {
            if (storeAppointmentDateOverrides[j].attributes.date === moment(new Date(date)).format('YYYY-MM-DD')) {
              dateOverrideFlag = true;
              timeSlots = storeAppointmentDateOverrides[j].attributes.timeslots;
              setStartEndTime(timeSlots);
              break;
            }
          }
        }

        if (!dateOverrideFlag) {
          for (var i = 0; i < storeAppointmentHoursData.length; i++) {
            if (storeAppointmentHoursData[i].attributes.day_of_week === findDayOfTheWeek(date)) {
              timeSlots = storeAppointmentHoursData[i].attributes.timeslots;
              setStartEndTime(timeSlots);
              break;
            }
          }
        };
      };

      var getApptLeadTime = function() {
        var leadTimeConfig = Salesfloor.Conf.appointmentLeadTime;
        var apptType = $('.fn-service-list').find('input[name=service]:checked').val();
        // Reschedule form
        if (!apptType) {
          apptType = $('#reschedule-form input[name=service]').val();
        }
        if (leadTimeConfig.hasOwnProperty(apptType)) {
            return leadTimeConfig[apptType];
        }
      };

      var setTimepicker = function($ctn, date) {
        if (storeAppointmentHoursData) {
          findStartEndTime(date);
          // hides the error message generated because of 406 error code
          // resize the iframe
          if (errorOccured) {
            $('#appointment-form-error-message').toggleClass('hidden');
            services.manageIframeHeight();
            errorOccured = false;
          }
        }
        var timeOptions = [];
        var locale = getLocaleFromCtn($ctn);
        var fancyTimeFormat = getRegionalFancyTimeFormat($ctn);
        
        // Foreach input
        $ctn.find(_options.timePlaceholder).each(function() {
          //If selection already made, set the input values
          if (timeChoosen !== undefined && durationChoosen !== undefined) {
            if ($(this).hasClass(_options.timeStartClass)) {
              $(this).val(timeChoosen);
            }
          }

          // startTime is allow with 0.5 as slot, so 7.5, 8, 8.5 are possible values
          var buildTimepicker = function($element, startTime, endTime, endMinutes) {
            var j = 0;
            var timeList = document.createDocumentFragment();
            // check if output 30 minutes first
            if (Math.ceil(startTime) - startTime === 0.5) {
              timeOptions.push(moment().set('hour', startTime).set('m', 0).add(j, 'hours').add(30, 'm').locale(locale).format(fancyTimeFormat));
              startTime = Math.ceil(startTime);
            }
            // creates additional loop, if endTime is x.5 (ex: 22:30)
            if (Math.ceil(endTime) - endTime === 0.5) {
              endTime = Math.ceil(endTime);
            }
           
            for (var i = startTime; i < endTime; i++) {
              timeOptions.push(moment().set('hour', startTime).set('m', 0).add(j, 'hours').format(fancyTimeFormat));
              timeOptions.push(moment().set('hour', startTime).set('m', 0).add(j, 'hours').add(30, 'm').format(fancyTimeFormat));
              j++;
            }
            // add the last time slot if there is no endMinutes
            !endMinutes && timeOptions.push(moment().set('hour', startTime).set('m', 0).add(j, 'hours').locale(locale).format(fancyTimeFormat));
            
            // when current date selected is equal to today
            // compare todayEarliest with the timeoptions
            // remove the timeoptions lesser than todayEarliest
            // for lang = en
            // converting timeoptions from 12hr format to 24hr format
            if (isStoreAppointmentHoursEnabled && (date === moment().format(validationFormat) || date === nextAvailableDay)) {
              timeOptions = timeOptions.filter(function(timeOption) {
                return moment(timeOption,'hh:mm A').format('HH:mm') >= moment(todayEarliest,'hh:mm A').format('HH:mm')
              });
            }
            $.each(timeOptions, function(index, value) {
              var node = document.createElement('option');
              $(node).attr('value', value).html(value);
              timeList.appendChild(node);
            });

            $element.html('').append(timeList);
          };

          //Set the specific variables to build the timepicker
          var start = '';
          var end = '';
          var leadTime = getApptLeadTime();
          var validationFormat = getRegionalValidationDateFormat($ctn);
          var nextAvailableDay;
          var todayEarliest;

          nextAvailableDay = moment().add(leadTime, 'minutes').format(validationFormat);
          todayEarliest = parseInt(moment().add(leadTime, 'minutes').format('HH'));

          //To match the day handle of datepicker, if time now 8:45, todayEarliest = 12:00 (8+4) and it should be 13:00
          if (parseInt(moment().format('mm')) >= 30) {
            todayEarliest ++;
          } else {
            todayEarliest += 0.5;
          }
          start = $(this).data('starttime') || parseInt(moment().set('hour', 0).format('HH'));
          end = $(this).data('endtime') || parseInt(moment().set('hour', 23).format('HH'));
          if (date && (date === moment().format(validationFormat) || date === nextAvailableDay)) {
            if (end < todayEarliest) {
            // today isn't available, calendar will disable today, time picker will set to next day
            } else {
              start = start > todayEarliest ? start : todayEarliest;
            }
          }
          !isStoreAppointmentHoursEnabled && buildTimepicker($(this), start, end);

          if (isStoreAppointmentHoursEnabled) {
            for (var i = 0; i < storeAppointmentHourTimeSlots.length; i++) {
              start = storeAppointmentHourTimeSlots[i].startHour;
              end = storeAppointmentHourTimeSlots[i].endHour;
              // this below condition is for startMinutes/endMinutes == 30 (ex: 22:30)
              // add .5 to the start/end time
              if (storeAppointmentHourTimeSlots[i].endMinute === 30) {
                end += 0.5;
              }
              if (storeAppointmentHourTimeSlots[i].startMinute === 30) {
                start += 0.5;
              }
              buildTimepicker($(this), start, end, storeAppointmentHourTimeSlots[i].endMinute);
            };
          }
        });
      };

      /**
       * Retrieve `fancyDateFormat` from current jqueryui locale.
       *
       * @param {object} $ctn
       * @returns {string}
       */
      var getRegionalFancyDateFormat = function ($ctn) {
        return $ctn.find(_options.calendarInputI18n).datepicker('option', 'fancyDateFormat') || 'ddd MMM D';
      };

      /**
       * Retrieve `validationDateFormat` from current jqueryui locale.
       *
       * @param {object} $ctn
       * @returns {string}
       */
      var getRegionalValidationDateFormat = function ($ctn) {
        return $ctn.find(_options.calendarInputI18n).datepicker('option', 'validationDateFormat') || 'MM/DD/YYYY';
      };

      /**
       * Retrieve `fancyTimeFormat` from current jqueryui locale.
       *
       * @param {object} $ctn
       * @returns {string}
       */
      var getRegionalFancyTimeFormat = function ($ctn) {
        var format = getLocaleFromCtn($ctn) === 'en-us' ? 'hh:mm A' : 'HH:mm';

        if (getLocaleFromCtn($ctn) === 'en-in') {
          format = 'hh:mm A';
        }

        return $ctn.find(_options.calendarInputI18n).datepicker('option', 'fancyTimeFormat') || format;
      };

      /**
       * Update regional settings for a given jqueryui datepicker.
       *
       * @param {object} $ctn
       */
      var applyLocaleToDatePicker = function ($ctn) {
        var locale = getLocaleFromCtn($ctn);
        var $datepicker = $ctn.find(_options.calendarInputI18n);

        // update current settings with regional settings
        var regional = $.extend(
          $datepicker.datepicker('option', 'all'),
          $.datepicker.regional[locale]
        );

        $ctn.find(_options.calendarInputI18n).datepicker('option', regional);
      };

      var footerShadow = function() {
        if ($(window).scrollTop() + $(window).height() === $(document).height()) {
          $('.js-service-footer').removeClass('global-services__navigation-wrapper--is-hover');
        } else {
          $('.js-service-footer').addClass('global-services__navigation-wrapper--is-hover');
        }
      };

      var updateSpecialtiesDropdown = function () {
        var $selectedSpecialty = $('#selectedSpecialty');

        if ($selectedSpecialty.find(':selected').is(':disabled')) {
          $selectedSpecialty.addClass('global-services__select--is-ghost-text');
        } else {
          $selectedSpecialty.removeClass('global-services__select--is-ghost-text');
        }
      };

      var process = function($ctn) {
        var platform = new utils.browserDetection();
        if(platform.chrome || platform.firefox || platform.safari) {
          $('.js-findrep-link').show();
        }
        //Set default Timepicker only if StoreAppointmentHours feature is enabled
        if (!isStoreAppointmentHoursEnabled) {
          setTimepicker($ctn, dateChosen);
        }

        //Set default calendar
        var defaultDate = null;
        if (dateChosen !== undefined) {
          var dateSplitted = dateChosen.split('-');
          defaultDate = new Date(dateSplitted[0], dateSplitted[1] - 1, dateSplitted[2]);
        }

        applyLocaleToDatePicker($ctn);

        $ctn.find(_options.calendarInput).each(function () {

          var minDate = 0;
          if ($(this).data('endtime') || storeAppointmentHoursData.length > 0) {
            var endHour = $(this).data('endtime');
            if (isStoreAppointmentHoursEnabled) {
              findStartEndTime(moment());
              lastTimeSlot = storeAppointmentHourTimeSlots.pop();
              endHour = lastTimeSlot && lastTimeSlot.endHour;
            }
            // todayEarliestTime take current time + leadTime. It will go pass the 23, bigger then endDate when making appointment.
            var leadTime = getApptLeadTime();
            var todayEarliestTime = parseInt(moment().add(leadTime, 'minutes').format('HH'));

            if (isStoreAppointmentHoursEnabled && lastTimeSlot && lastTimeSlot.endMinute === 30) {
              endHour += 0.5;
            }
            //On setTimepicker, start time might get changed as additional 30 min/1hour will be added to it.
            //(if time now 8:45, leadTime = 4, todayEarliestTime = 12:00 and start would be 13:00)
            //we need to make sure that the new start/todayEarliestTime will not be after end time
            if (parseInt(moment().format('mm')) >= 30) {
              todayEarliestTime ++;
            } else {
              todayEarliestTime += 0.5;
            }
            //if the todayEarliestTime is after the end time, we need to disable the day
            if (endHour < todayEarliestTime) {
              minDate = 1;
            }
          }
          $(this).datepicker({
            prevText: "&laquo;",
            nextText: "&raquo;",
            minDate: minDate,
            maxDate: "+3m",
            showOtherMonths: true,
            defaultDate: defaultDate,
            beforeShow: function (input) {
              var $input = $(input);

              // since the input is cleared after the options update
              // we'll set it back programmatically
              var valueBeforeRegionalUpdate = $input.val();

              applyLocaleToDatePicker($ctn);

              $input.val(valueBeforeRegionalUpdate);
            },
            beforeShowDay: function(date) {
              var leadTime = getApptLeadTime();
              var dateFromLeadTime = moment().add(leadTime, 'm');
              var validationFormat = getRegionalValidationDateFormat($ctn);

              if (leadTime > 0 && dateFromLeadTime.diff(moment(date), 'days') > 0) {
                return [false, ''];
              }

              var nextAvailableDay = dateFromLeadTime.format(validationFormat);
              var nextAvailableHour = parseInt(dateFromLeadTime.format('HH'));

              if (!isStoreAppointmentHoursEnabled) {
                return [true, ''];
              } else {
                for (var j = 0; j < storeAppointmentDateOverrides.length; j++) {
                  if (storeAppointmentDateOverrides[j].attributes.date === moment(date).format('YYYY-MM-DD')) {
                    return [storeAppointmentDateOverrides[j].attributes.is_available === '1', ''];
                  }
                }
                  var dayOfTheWeek = findDayOfTheWeek(date);
                for (var i = 0; i < storeAppointmentHoursData.length; i++) {
                  if (dayOfTheWeek === storeAppointmentHoursData[i].attributes.day_of_week) {
                    var isAvailable = storeAppointmentHoursData[i].attributes.is_available === '1';
                    
                    if (moment(date).format(validationFormat) === nextAvailableDay) {
                      var availableSlots = storeAppointmentHoursData[i].attributes.timeslots.filter(function (timeslot) {
                        // test if the nextAvailableHour is not past the last available end slot
                        return nextAvailableHour < parseInt(moment(timeslot.end_time,'HH:mm:ss').hour())
                      });
                      return [isAvailable && availableSlots.length > 0, ''];
                    }
                    return [storeAppointmentHoursData[i].attributes.is_available === '1',''];
                  }
                }
              } 
            },
            onSelect: function (date, obj) {
              $ctn.find(_options.calendarInputValue).val(date);

              validateForm(true);

              $(this).valid();
              defineSelectedDate($ctn, obj.selectedDay, obj.selectedMonth, obj.selectedYear, date);
            }
          });
        });

        //Set default current date
        $ctn.find(_options.datePlaceholder).html(function() {
          if (dateChosen !== undefined) {
            var dateSplitted = dateChosen.split('-');

            formatedDateChoosen = moment(new Date(dateSplitted[0], dateSplitted[1] - 1, dateSplitted[2]))
              .format(getRegionalFancyDateFormat($ctn));
            return formatedDateChoosen;
          }

          formatedDateChoosen = moment().format(getRegionalFancyDateFormat($ctn));

          return formatedDateChoosen;
        });

        dateChosen = dateChosen !== undefined ? dateChosen : moment().format('YYYY-M-DD');

        footerShadow();

        $(window).on('scroll', function() {
          footerShadow();
        });

        $(document).ready(function() {
          updateSpecialtiesDropdown();
        });

        var md = new MobileDetect(window.navigator.userAgent);
        var event = ((md.phone() || md.tablet()) && (md.os() == 'iOS')) ? 'touchend' : 'click';

        if (md.phone() || md.tablet() || ($(window).height() && $(window).height() <= 667 && $(window).width() && $(window).width() <= 375)) {
          services.bindMobileFocus();
        }

        $('.fn-navigation-service-back').on('click', function(e) {
          e.preventDefault();

          // SF-13745 - Make sure there is no unencoded ampersand in the url
          // That would mean we got more than what we needed from the split
          var sourceUrl = location.search.split('source_url=')[1];
          sourceUrl = sourceUrl && sourceUrl.split('&')[0];

          if (sourceUrl) {
            sourceUrl = (sourceUrl.indexOf('%2F')) ? decodeURIComponent(sourceUrl) : sourceUrl;
            window.location.assign(sourceUrl);
          } else {
            window.history.go(-1);
          }
        });

        $('.js-findarep-trigger').on('click', function(e) {
          e.preventDefault();
          window.sf_widget.widgets.findstore.open({
            page: 'findrep',
            storeId: $('#store').val()
          });
        });
      };

      var render = function() {
        return {
          render: null,
          init: process($(_options.dateSelectorTarget))
        };
      };

      // Init select time part
      if ($ctn.length) {
        render()
      }

      //Set the focus on init to avoid IE11 bug on iframe
      $ctn.find('input[type=radio]').focus();

      //Timezone
      //Set default current timeZone
      $ctn.find(_options.timezonePlaceholder).val(tz.name());

      $ctn.on('form.validated', function(e) {
        e.preventDefault();
        $(_options.triggerSave).prop("disabled", true);
        services.clearUnwantedFormData($ctn);
        registerAppointment($ctn, $(this).serialize(), $(this).serializeArray()).always(function() {
          $(_options.triggerSave).prop("disabled", false);
        });
      });

      $ctn.find(_options.triggerClose).on('click', function() {
        if (md.phone() || md.tablet()) {
          window.history.back();
        } else {
          window.close();

          if (Salesfloor.Environement !== undefined && Salesfloor.Environement.repname !== undefined) {
            window.location = '/' + Salesfloor.Environement.repname;
          }
          $('body').trigger('window.close');
        }
      });

      //Service animation
      //IE8 hack to trigger the change event
      new utils.browserDetection();
      conditionizr.on('ie8', function() {
        $ctn.find(_options.$serviceCtn).on('click', _options.serviceTriggerIcon, function() {
          $ctn.find(_options.$serviceCtn).find(_options.serviceTrigger).removeClass('is-checked');
          $(this).parent().prev(_options.serviceTrigger).trigger('change').addClass('is-checked');
        });
      });

      var $phone     = $ctn.find('#phone');
      var $store     = $ctn.find('#store');
      var $email     = $ctn.find('#email');
      var $sms       = $ctn.find('#sms');
      var $specialty = $ctn.find('#selectedSpecialty');

      var $phoneLabel = $ctn.find('.js-phone-label-element');
      var labelTextRequired = $phoneLabel.data('required-text');
      var labelTextNotRequired = $phoneLabel.data('not-required-text');

      var phoneOption = function($inputPhone, required) {
        if ($inputPhone.val() === '') {
          $ctn.find('.js-form-btn').prop('disabled', true);
        }
        
        if (required) {
          $phoneLabel.html(labelTextRequired);
          $inputPhone.rules('add', {
            required : true,
            intlTelInput: true
          });
        } else {
          $phoneLabel.html(labelTextNotRequired);
          $inputPhone.rules('remove', 'required');
          $inputPhone.rules('add', {
            required : false
          });
        }
      };

      var isPhoneOptionSelected = function () {
        return $ctn.find('#phoneService').prop('checked');
      };

      var isTextMessageSelected = function () {
        return $ctn.find('#contactTextMessage').is(':checked');
      };

      var validateForm = function (dateIsFilled) {
        if (isTextMessageSelected()) {
          mandatoryFields.sms = utils.validatePhone($sms);
          delete mandatoryFields.email;
          delete mandatoryFields.phone;
        } else {
          mandatoryFields.email = !!utils.validateEmail($email.val());
          delete mandatoryFields.sms;

          if (!!$phone.length) {
            mandatoryFields.phone = utils.validatePhone($phone) || !isPhoneOptionSelected() && !$phone.val().length;
          }
        }
        
        if (Salesfloor.Conf.consentIsRequired) {
          mandatoryFields.consent = $('#' + (!isTextMessageSelected() ? 'autoSubscribe' : 'autoSmsSubscribe')).is(':checked');
        }

        if (Salesfloor.Conf.termIsEnabled && Salesfloor.Conf.termIsRequired) {
          mandatoryFields.termconditions = $('#' + (!isTextMessageSelected() ? 'autoTermConditions' : 'autoSmsTermConditions')).is(':checked');
        }

        if (!!$specialty.find(':selected').length) {
          mandatoryFields.specialty = $specialty.find(':selected').val() !== 'Select' && $specialty.find(':selected').is(':not(:disabled)');
        }

        mandatoryFields.date = dateIsFilled || !!mandatoryFields.date;

        services.manageProp(mandatoryFields);
      };
      
      var isSmsPreferenceSelected = function () {
        return $ctn.find('input[name=contact_preference]:checked').val() === 'sms';
      };

      //validating the chosen date for phones and tablets
      $ctn.find(_options.$choosenDate).on('change', function() {
        validateForm(!!$(this).val().trim());
      });

      $ctn.find(_options.$selectedSpecialty).on('change', function() {
        validateForm();
        updateSpecialtiesDropdown();
      });

      // Initial check
      // HR have phone first selected for example
      if (isPhoneOptionSelected()) {
        phoneOption($phone, true);

        if (!isSmsPreferenceSelected()) {
          // Show phone input even if $configs['retailer.services.hide_optional_phone_input'] is true
          // Because appointment type selected is Phone and preference is not SMS
          // SF-26491
          $('.js-hide-general-phone').removeClass('global-services__form__element--is-hidden');
        }
      } else {
        phoneOption($phone, false);
      }
      validateForm();

      //options switcher
      $ctn.find(_options.$serviceCtn).on('change', _options.serviceTrigger, function() {
        var position = $(this).parent().position();
        var $animateCtn = $(this).parents(_options.$serviceCtn).find(_options.serviceAnimateContent);

        $animateCtn.find(_options.serviceAnimateElement).animate({
          left: position.left + 35
        });

        if (isPhoneOptionSelected() || isSmsPreferenceSelected()) {
          if (isSmsPreferenceSelected()) {
            $phone.rules('remove', 'required');
            $phone.rules('add', {
              required: false
            });
          } else {
            phoneOption($phone, true);
          }
        } else {
          phoneOption($phone, false);

          if ($phone.parent('div').hasClass('hint')) {
            $phone.unwrap();
          }
        }

        validateForm();

        if ($(this).val() === 'store') {
          $store.rules('add',{
            required: true
          });
          _options.$storeLocation.show('fast');
        } else {
          $store.rules('remove', 'required');
          $store.rules('add', {
            required: false
          });

          if ($store.hasClass('error')) {
            $store.removeClass('error');
          }
          _options.$storeLocation.hide('fast');
        }

        // Toggle phone input depending on which appointment type is selected
        // Only applies if $configs['retailer.services.hide_optional_phone_input'] is true
        // SF-26491
        $('.js-hide-general-phone').toggleClass('global-services__form__element--is-hidden', !isPhoneOptionSelected() || isSmsPreferenceSelected());
        // Refresh time slots after changes to Appointment type
        clearDatePicker();
        process($(_options.dateSelectorTarget));
      });

      $ctn.on('input', '.js-form-element', function() {
        var inputVal = $(this).val();
        var inputId = $(this).attr('id');

        if (inputVal === '') {
          if ($(this).hasClass('error')) {
            $(this).attr('placeholder', '');
          }
        }

        validateForm();
      });

      /**
       * Default option for the intlTelInput
       *
       * @type {{autoPlaceholder: string, geoIpLookup: geoIpLookup, initialCountry: string}}
       */
      var intlTelInputOptions = {
        autoPlaceholder: 'aggressive',
        geoIpLookup: function (callback) {
          $.get('/sfadmin/locate.php?store_id=' + $('#store').val()).always(function (resp) {
            resp = services.returnProperParsing(resp);
            var countryCode = resp && resp.country ? resp.country : '';
            callback(countryCode);
          });
        },
        initialCountry: 'auto'
      };

      /**
       * Based on the type (sms/phone), return appropriate preferred and available countries
       *
       * @param type
       * @returns {{preferredCountries: Array, onlyCountries: Array}}
       */
      var getCountries = function (type) {
        var $selector;
        switch (type) {
          case 'sms':
            $selector = $('.global-services__form--is-smb .sms').closest('.global-services__form--is-smb');
            break;
          case 'phone':
          default:
            $selector = $('.global-services__form--is-smb .phone').closest('.global-services__form--is-smb');
            break;
        }

        var preferred = $selector.data('countries-preferred');
        var available = $selector.data('countries-available');
        var threshold = $selector.data('countries-preferred-threshold');

        // Threshold is used so we don't get duplicate when listing of country is too small
        return {
          preferredCountries: preferred && (!threshold || (available || []).length > threshold) ? preferred : [],
          onlyCountries: available || []
        };
      };

      if (typeof Object.assign != 'function') {
        Object.assign = function(target) {
          'use strict';
          if (target == null) {
            throw new TypeError('Cannot convert undefined or null to object');
          }

          target = Object(target);
          for (var index = 1; index < arguments.length; index++) {
            var source = arguments[index];
            if (source != null) {
              for (var key in source) {
                if (Object.prototype.hasOwnProperty.call(source, key)) {
                  target[key] = source[key];
                }
              }
            }
          }
          return target;
        };
      }

      var forceCountry = function(type) {
        // Since we have 2 phones number in the same form, we need to force to the phone value, otherwise
        // it could use the country from the previous (#sms) modified one
        var country = $('#' + type).intlTelInput('getSelectedCountryData');
        $('#country').val((country && country.iso2 || '').toUpperCase());
      };

      $('.global-services__form--is-smb .phone').intlTelInput(Object.assign(intlTelInputOptions, getCountries('phone')));
      $('.global-services__form--is-smb .sms').intlTelInput(Object.assign(intlTelInputOptions, getCountries('sms')));

      $('.phone, .sms').on('countrychange', function() {
        var element = $('#contactTextMessage').is(':checked') ? 'sms' : 'phone';
        forceCountry(element);
        validateForm();
      });

      var updateFormRules = function (isEmail) {
        $phone.rules('add', { intlTelInput: isEmail });
        $email.rules('add', { email: isEmail });
        $sms.rules('add', { intlTelInput: !isEmail });
      };

      $ctn.find('#contactEmail').on('click', function () {
          if ($(this).is(':checked')) {
            $('.js-contact-email').removeClass('global-services__form__element--is-hidden').find('#email').prop('required', true);
            $('.js-contact-text-message').addClass('global-services__form__element--is-hidden').find('#sms').prop('required', false);
            $('.js-general-phone').removeClass('global-services__form__element--is-hidden').find('#phone').prop('required', isPhoneOptionSelected());

            $('.js-casl-v3-email').removeClass('global-services__form__element--is-hidden');
            $('.js-casl-v3-text-message').addClass('global-services__form__element--is-hidden');

            // Toggle general phone input depending of which appointment type is selected
            // This only applies if $configs['retailer.services.hide_optional_phone_input'] is true
            $('.js-hide-general-phone').toggleClass('global-services__form__element--is-hidden', !isPhoneOptionSelected()).find('#phone').prop('required', isPhoneOptionSelected());

            validateForm();

            $(this).find('fn-content-wrapper').trigger('change');

            if (isPhoneOptionSelected()) {
              $phone.rules('add', 'required');
            } else {
              $phone.rules('remove', 'required');
              $phone.rules('add', {
                required: false
              });

              // We need to remove it only if you don't have the appointment type phone
              $phoneLabel.html(labelTextNotRequired);
            }

            updateFormRules(true);
            forceCountry('phone');
          }
      });

      $ctn.find('#contactTextMessage').on('click', function () {
          if ($(this).is(':checked')) {
            $('.js-contact-email').addClass('global-services__form__element--is-hidden').find('#email').prop('required', false);
            $('.js-contact-text-message').removeClass('global-services__form__element--is-hidden').find('#sms').prop('required', true);

            // Hide general input if preference selected is SMS
            $('.js-general-phone, .js-hide-general-phone').addClass('global-services__form__element--is-hidden').find('#phone').prop('required', false);

            $('.js-casl-v3-email').addClass('global-services__form__element--is-hidden');
            $('.js-casl-v3-text-message').removeClass('global-services__form__element--is-hidden');

            // We need to force "required" on phone when you want to be contacted via sms
            $phoneLabel.html(labelTextRequired);

            validateForm();

            $(this).find('fn-content-wrapper').trigger('change');

            // When we switch to Text, the phone is not required/mandatory anymore
            // I'm don't know why remove it not enough, but if not specified, required true it set later (not sure where)
            $phone.rules('remove', 'required');
            $phone.rules('add', {
              required: false
            });

            updateFormRules(false);
            forceCountry('sms');
          }
      });

      $ctn.find('#autoSubscribe').on('change', function() {
        validateForm();
      });

      $ctn.find('#autoSmsSubscribe').on('change', function() {
        validateForm();
      });

      $ctn.find('#autoTermConditions').on('change', function() {
        validateForm();
      });

      $ctn.find('#autoSmsTermConditions').on('change', function() {
        validateForm();
      });

      //display the message box if any
      if ((Salesfloor.Environement !== undefined) && (Salesfloor.Environement.message !== undefined)) {
        var $resultMessage = {
          type: Salesfloor.Environement.message.type,
          message: Salesfloor.Environement.message.text
        };

        new ui.messageBanner({
          $ctn: $('.fn-date'),
          positionTop:'0',
          positionLeft:'0',
          $data: $resultMessage
        });
      }
    }
  };

  return Salesfloor.Appointment;
});
