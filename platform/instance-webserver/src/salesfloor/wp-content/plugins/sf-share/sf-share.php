<?php

use Salesfloor\Services\Share\ShareEmail;
use Salesfloor\Services\Util;
use Salesfloor\Services\ShopFeed;
use Salesfloor\Models\CustomerTag;
use Salesfloor\Services\PrepareShare;
use Salesfloor\Services\Sanitize\Sanitize;
use Salesfloor\Services\Sanitize\PlainTextFormatter;

require_once ABSPATH . 'includes/PostFilter.php';
require_once ABSPATH . 'includes/publisher/Publisher.php';
require_once ABSPATH . 'includes/publisher/MixedAttachments.php';
require_once ABSPATH . 'includes/publisher/MixedAsset.php';
require_once ABSPATH . 'includes/publisher/Template.php';
require_once ABSPATH . 'sfadmin/publisher/post-functions.php';

add_action('init', 'sf_share_init', 1);
function sf_share_init()
{
    // keep this around while we still have posts of type sf-share.
    // WESHOULD port sf-share posts to type sf-library.

    $labels = array(
        'name'               => 'Share',
        'singular_name'      => 'Share',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Share',
        'edit_item'          => 'Edit Share',
        'new_item'           => 'New Share',
        'all_items'          => 'All Share',
        'view_item'          => 'View Share',
        'search_items'       => 'Search Share',
        'not_found'          => 'No share found',
        'not_found_in_trash' => 'No share found in Trash',
        'parent_item_colon'  => '',
        'menu_name'          => 'Share'
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'sf-share'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => null,
        'supports'           => array('title', 'editor', 'tags'),
        'taxonomies'          => array('post_tag'),
    );

    register_post_type('sf-share', $args);
    register_taxonomy_for_object_type('post-tag', 'sf-share');
}


class Salesfloor_Share
{
    public const EMAIL = 'email';
    public const SOCIAL = 'social';

    /** @var \Salesfloor\API\Managers\Reps $repsManager */
    protected $repsManager;

    /** @var \Salesfloor\API\Managers\Products\ProductShared $productShareManager */
    protected $productShareManager;

    /** @var \Salesfloor\Configs\Configs $configs */
    protected $configs;

    /** @var \Salesfloor\API\Managers\ProductVariants $productVariantManager */
    protected $productVariantManager;

    /** @var PrepareShare */
    protected $prepareShare;

    /** @var ShareEmail */
    protected $shareEmail;

    public function __construct($type = null)
    {
        require_once(ABSPATH . '/sfadmin/includes/sf-template.php');

        // Register AJAX handler for sending share
        add_action('wp_ajax_sfsendshare', function ($data = array(), $magic = \false) {
            return $this->ajax_send_share($data, $magic);
        });

        global $app;
        $this->repsManager           = $app['reps.manager'];
        $this->productShareManager   = $app['product_shared.manager'];
        $this->productVariantManager = $app['product_variants.manager'];
        $this->configs = $app['configs'];
        $this->prepareShare = $app['services.share.prepare'];
        $this->shareEmail = $app['service.share_email'];
    }

    /**
     * Share page on AJAX request.
     * The mobile app still uses this.
     */
    public function ajax_send_share($data = array(), $magic = false)
    {
        global $user, $app;

        /** @var Sanitize $sanitizeService */
        $sanitizeService = $app['service.sanitize'];

        $locale = null;

        if ($magic === 'more magic' && count($data) > 0) {
            $_POST = $data;
        }
        if (!is_user_logged_in()) {
            wp_send_json_error();
        }
        $user = wp_get_current_user();
        if (!$user) {
            wp_send_json_error();
        }

        // remove after debugging - SF-30217
        $debug_session = \Salesfloor\Services\DebugTools::getSession();
        $debug_start_time = -hrtime(true);
        addDebug('SF-30217', sprintf(
            "userId: %d - empId: %d - session: %s - STEP 1 - send share start - data: %s",
            $user->ID,
            $user->employee_id,
            $debug_session,
            print_r($data, true)
        ));
        // Check that we've submitted page's URL
        if (!isset($_POST['url'])) {
            wp_send_json_error();
            exit;
        }

        $recipients = $this->getRecipients();

        addDebug('SF-30217', sprintf(
            "userId: %d - empId: %d - session: %s - STEP 2 - get recipients - data: %s",
            $user->ID,
            $user->employee_id,
            $debug_session,
            print_r($recipients, true)
        ));

        if (!empty($_POST['templateLocale'])) {
            $locale = $_POST['templateLocale'];
        }

        // Prepare data for group task.
        $groupTaskId = $_POST['group_task_id'] ?? null;
        if (!empty($groupTaskId)) {
            if (isset($_POST['asset']) && is_array($_POST['asset'])) {
                $asset = $_POST['asset'];
                $_POST['asset']['feedIndex'] = $asset['feedIndex'] ?? $asset["virtual_fields"]["feedIndex"] ?? null;
                $_POST['asset']['id'] = $asset['id'] ?? $asset['feedIndex'];
            }
        }

        $asset = $asset_type = null;
        if (isset($_POST['asset']) && isset($_POST['asset']['feedIndex']) && preg_match('/^\d+$/', $_POST['asset']['feedIndex'])) {
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 3 - check asset feedIndex has products - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                print_r($_POST['asset'], true)
            ));
            if ($this->hasProducts() || $this->hasProduct()) {
                $asset = array(
                    'asset'      => $_POST['asset']['feedIndex'],
                    'product'    => $this->hasProduct() ? $this->handleProduct($_POST) : null,
                    'post'       => $this->hasProducts() ? $this->handleProducts($data, $locale, true, true)[1] : null,
                );
                $asset_type = MixedAsset::class;
                addDebug('SF-30217', sprintf(
                    "userId: %d - empId: %d - session: %s - STEP 4 - check asset feedIndex has products completed - data: %s",
                    $user->ID,
                    $user->employee_id,
                    $debug_session,
                    print_r($asset, true)
                ));
            } else {
                // asset only
                $asset = [
                    'asset'   => $_POST['asset']['feedIndex'],
                    'product' => null,
                    'post'    => null,

                ];
                $asset_type = 'post';
                addDebug('SF-30217', sprintf(
                    "userId: %d - empId: %d - session: %s - STEP 5 - check asset feedIndex has asset only completed - data: %s",
                    $user->ID,
                    $user->employee_id,
                    $debug_session,
                    print_r($asset, true)
                ));
            }
        } elseif (isset($_POST['groupedProductsOn']) && $_POST['groupedProductsOn'] == true) {
            $asset_type = 'groupedProducts';
            $asset = array(
                'imageAsset' => $this->hasImageAssetPhoto() ? $this->handleImageAsset($data) : null,
                'products'   => $this->hasProducts() ? $this->handleProducts($data, $locale, true, true)[1] : null,
            );
        } elseif ($this->hasImageAssetPhoto() && ($this->hasProduct() || $this->hasProducts())) {
            $asset = array(
                'imageAsset' => $this->handleImageAsset($data),
                'product'    => $this->hasProduct()  ? $this->handleProduct($_POST) : null,
                'post'       => $this->hasProducts() ? $this->handleProducts($data, $locale, true, true)[1] : null,
            );
            $asset_type = MixedAttachments::class;
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 6 - check has image asset and product completed - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                print_r($asset, true)
            ));
        } else if ($this->hasProduct()) {
            $asset = $this->handleProduct($_POST);
            $asset_type = 'product';
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 7 - check has single product only completed - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                print_r($asset, true)
            ));
        } else if ($this->hasImageAssetPhoto()) {
            $asset_type = 'image';
            $asset = $this->handleImageAsset($data);
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 8 - check has image asset only completed - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                print_r($asset, true)
            ));
        } else if ($_POST['products']) {
            list($asset, $created) = $this->handleProducts($data, $locale, true, true);
            $asset_type = 'post';

            $asset["id"] = $created["id"];
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 9 - check has products completed - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                print_r($asset, true)
            ));
        }

        // Sanitize subject and body
        $text = stripslashes($_POST['text']); // strip slashes added from magic quotes from the BO
        $text = $sanitizeService->removeXss($text);
        $isHtml       = (isset($_POST['isHtml']) && ($_POST['isHtml'] === true || $_POST['isHtml'] === 'true'));
        $isEmailShare = (isset($_POST['email'])  && ($_POST['email']  === true || $_POST['email']  === 'true'));
        if ($isEmailShare && !$isHtml) {
            // if plaintext email encode special chars
            $_POST['text'] = htmlspecialchars($text);
        } else {
            // pre tag will be used to display spacing and htmlentities
            $_POST['text'] = $text;
        }
        $_POST['email_subject'] = $sanitizeService->removeXss($_POST['email_subject']);

        $caption      = $_POST['text'];
        $url          = $_POST['url'];

        // SF-21473 - Select template manually, not based on the content you sent
        $templateData = null;
        if (!empty($_POST['templateEmail'])) {
            $templateData = $this->shareEmail->sanitizeAssetData($_POST);
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 10 - check has template email completed - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                print_r($templateData, true)
            ));
        }

        $sanitizeAssetData = $this->shareEmail->sanitizeAssetData($_POST);
        $userModel         = $app['reps.manager']->getById($user->ID);
        addDebug('SF-30217', sprintf(
            "userId: %d - empId: %d - session: %s - STEP 11 - sanitize asset data completed - data: %s",
            $user->ID,
            $user->employee_id,
            $debug_session,
            print_r([
                'debug-user' => $userModel,
                'debug-asset' => $sanitizeAssetData
            ], true)
        ));
        /** @var ShopFeed $shopFeedService */
        $shopFeedService = $app['service.shop_feed'];

        // As requested - Only for instagram :O  ̿ ̿̿'̿̿\̵͇̿̿\=(•̪●)=/̵͇̿̿/'̿̿
        // The instagram share is only done in mobile and is not linked to this code anyway !
        // So even if you cancel, it will still be populated
        // Don't populate the feed if it's not activated
        if (in_array('instagram', $recipients['social']) && $shopFeedService->isEnabledForRep($user->ID)) {

            // It is possible from the BO to send the share to multiple networks
            // due to this an email from the WYSIWYG can also be sent to socialshop
            // socialshop does not allow HTML thus we must convert to plaintext if HTML is detected
            $socialShareAssetData = $sanitizeAssetData;
            /** @var PlainTextFormatter $plainTextFormatter */
            $plainTextFormatter = $app['service.plaintext.formatter'];
            if (Util::containsHtml($socialShareAssetData['comment'])) {
                $socialShareAssetData['comment'] = $plainTextFormatter->htmlToPlainText($socialShareAssetData['comment']);
            }
            // Populate shop feed tables based on input
            $shopFeedService->addShopFeedPost(
                $userModel,
                // We already sanitize input for the alternate email template. Reused that
                $shopFeedService->preparePostFromShare($socialShareAssetData)
            );
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - STEP 12 - feed service addShopFeedPost completed - data: %s",
                $user->ID,
                $user->employee_id,
                $debug_session,
                json_encode($userModel)
            ));
        }

        if (isset($_POST['email']) && $_POST['email'] == 'true') {
            $asset['email_subject'] = PostFilter::filter(stripslashes($_POST['email_subject']));
        }

        $isEmail = (!empty($recipients['email']) || !empty($recipients['email_customer_tag_ids']));
        $type = $isEmail ? self::EMAIL : self::SOCIAL;

        $favoriteContacts = isset($_POST['favorite_contacts']) ? intval($_POST['favorite_contacts']) : null;
        $groupedProductsOn = $_POST['groupedProductsOn'] ?? false;
        $filters = $_POST['filters'] ?? null;

        $message = [
            'uniqueId' => uniqid('SFID', true),
            'userId' => $user->ID,
            'caption' => $caption,
            'recipients' => $recipients,
            'assetData' => $asset,
            'assetType' => $asset_type,
            'url' => $url,
            'locale' => $locale,
            'templateData' => $templateData,
            'sanitizeAssetData' => $sanitizeAssetData,
            'favoriteContacts' => $favoriteContacts,
            'groupedProductsOn' => $groupedProductsOn,
            'filters' => $filters,
        ];
        $response = $this->prepareShare->prepareMessage($type, $message);
        $debug_end_time = ($debug_start_time += hrtime(true)) / 1e+9; // nanoseconds to seconds
        addDebug('SF-30217', sprintf(
            "userId: %d - empId: %d - session: %s - STEP 18 - share send completed time: %s(secs)",
            $user->ID,
            $user->employee_id,
            $debug_session,
            $debug_end_time
        ));
        wp_send_json($response);

        return array('success' => 'maybe?');
    }

    /**
     * Get Recipients from $_POST
     * We handle several type of recipients:
     *      email recipients, (filter by subscribers or email_customer_tag_ids)
     *      social share, ex: twitter, linkedin, instagram etc.
     *      storefront share
     * @return array
     */
    private function getRecipients()
    {
        $recipients = ['social' => []];
        if (isset($_POST['email']) && $_POST['email'] == 'true') {
            $recipients['email'] = 'subscribers';
        }

        if (!empty($_POST['email_customer_tag_ids'])) {
            $recipients['email_customer_tag_ids'] = [
                'ids' => $_POST['email_customer_tag_ids'],
                // Default is 'and'.
                CustomerTag::FILTER_KEY => $_POST['email_customer_tag_op'] ?? CustomerTag::FILTER_AND,
            ];
        }

        if (isset($_POST['twitter']) && $_POST['twitter'] == 'true') {
            $recipients['social'][] = 'twitter';
        }
        if (isset($_POST['linkedin']) && $_POST['linkedin'] == 'true') {
            $recipients['social'][] = 'linkedin';
        }
        if (isset($_POST['facebook']) && $_POST['facebook'] == 'true') {
            $recipients['social'][] = 'facebook';
        }
        if (isset($_POST['instagram']) && $_POST['instagram'] == 'true') {
            $recipients['social'][] = 'instagram';
        }
        if (isset($_POST['pinterest']) && $_POST['pinterest'] == 'true') {
            $recipients['social'][] = 'pinterest';
        }
        if (isset($_POST['frontpage']) && $_POST['frontpage'] == 'true') {
            $recipients['storefront'] = true;
        }

        return $recipients;
    }

    /**
     * TODO - Do we still need to create a post for each share ?!
     * The url created doesn't include the rep name, there's no htaccess for this route
     * Go to rep.php => 404
     *
     * @param $data
     * @param null $locale
     * @param bool $createPost
     * @param bool $enablePostMetaCreateFrom
     * @return array
     */
    private function handleProducts($data, $locale = null, $createPost = true, $enablePostMetaCreateFrom = false)
    {
        global $app, $user;

        if ($app['configs']['retailer.i18n.is_enabled'] && empty($locale) && isset($user->store)) {
            $locale = $app['service.multilang']->getStoreDefaultLocale($user->store);
        }

        //@todo remove after debug - SF-30217
        addDebug('SF-30217', sprintf(
            "userId: %d - empId: %d - session: %s - handle products - data: %s",
            $user->ID,
            $user->employee_id,
            session_id(),
            print_r($data['products'], true)
        ));

        $nProducts = count($data['products']);

        $subject = $data['email_subject'] ?? null;
        if ($subject === null) {
            $subject = $app['translator']->transChoice(
                'retailer.email.subject.email_subject_share_with_products_no_photos',
                $nProducts,
                [
                    '%repName%'    => sf_get_rep_display_name($user),
                    '%nbProducts%' => $nProducts
                ],
                null,
                $locale
            );
        }

        $post_data = array(
            "type"    => 'products',
            "objects" => array(),
            "title"   => $subject,
            "message" => $_POST['text']
        );

        foreach ($data['products'] as $product) {
            $variantIdByPost = $this->getVariantIdByPost($product);
            $post_data['objects'][] = array(
                "type"    => "product",
                "id"      => $this->sanitizeProductId($product),
                'variantSku' => !empty($variantIdByPost) ? stripslashes($variantIdByPost) : null,
                'image' => $product['img'] ?? $product['image_url'],
                "message" => "",
            );
        }

        if ($createPost === true) {
            $created = create_post(json_encode($post_data), $enablePostMetaCreateFrom, 'mobile_share', $locale);
            //@todo remove after debug - SF-30217
            addDebug('SF-30217', sprintf(
                "userId: %d - empId: %d - session: %s - handle products added to wp_post - data: %s",
                $user->ID,
                $user->employee_id,
                session_id(),
                print_r($created, true)
            ));
        } else {
            $created = false;
        }

        return array($post_data, $created);
    }

    /**
     * We also retrieve variant_id as fallback, Because:
     * 1. If some product/variant is selected by pass pdp model, no selectedVariant be sent from front-end, ex: share product/store front
     * 2. Front-end might remove selectedVariant in the future to reduce network traffic and unify data
     * @param array $postProduct
     * @return null|string
     */
    private function getVariantIdByPost($postProduct)
    {
        // Prioritize the variant_id field.
        if (!empty($postProduct['variant_id'])) {
            return $postProduct['variant_id'];
        }

        // Keep this logic because front-end send only this data when select search product and go into details panel
        if (!empty($postProduct['selectedVariant']['sku'])) {
            return $postProduct['selectedVariant']['sku'];
        }

        // For share an update
        if (!empty($postProduct['variants']['sku'])) {
            return $postProduct['variants']['sku'];
        }

        // This is for group task.
        // 'product_id'/'sku' are set by FE,
        // There’s ‘product_id’, then the 'sku' is real sku.
        if (!empty($postProduct['product_id']) && !empty($postProduct['sku'])) {
            return $postProduct['sku'];
        }

        return null;
    }

    private function handleProduct(array $requestData)
    {
        $product = $requestData['product'];
        $img = $product['selectedVariant']['image_url'] ?? $product['img'];

        if (!$img) {
            $img = $product['thumbnailImage'];
        }
        if (!$img) {
            $img = $product['additionalMedia'][0]['url'];
        }

        $productUrl = $product['selectedVariant']['product_url'] ?? $product['productUrl'];
        return array(
            'sku' => $this->sanitizeProductId($product),
            'variantSku' => stripslashes($this->getVariantIdByPost($product)),
            'url' => stripslashes($productUrl),
            'image' => stripslashes($img),
            'name' => stripslashes($product['name']),
            'description' => stripslashes(
                !empty($requestData['text']) ? $requestData['text'] : $product['shortDescription']
            ),
        );
    }

    private function sanitizeProductId($product)
    {
        if (isset($product['product_ID']) && !empty($product['product_ID'])) {
            $id = stripslashes($product['product_ID']);
        } elseif (isset($product['product_id']) && !empty($product['product_id'])) {
            $id = stripslashes($product['product_id']);
        } elseif (isset($product['sku']) && !empty($product['sku'])) {
            $id = stripslashes($product['sku']);
        } else {
            $id = 'epic_' . stripslashes($product['id']);
        }

        return $id;
    }

    private function handleImageAsset($data)
    {
        return array('image' => $_POST['attachment']);
    }

    private function hasProduct()
    {
        return isset($_POST['product']) && (isset($_POST['product']['sku']) || (isset($_POST['product']['type']) && $_POST['product']['type'] == 'epic'));
    }

    private function hasProducts()
    {
        return isset($_POST['products']) && !empty($_POST['products']);
    }

    private function hasImageAssetPhoto()
    {
        return isset($_POST['attachment']) && !empty($_POST['attachment']);
    }
}

// This is to execute the sharing module so calling this way have the creator to instanciate callbacks.
$salefloor_share = new Salesfloor_Share;
