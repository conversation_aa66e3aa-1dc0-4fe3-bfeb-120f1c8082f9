<?php
/**
 * Salesfloor specific functions
 */

include_once('defines.php');

require_once ABSPATH . 'includes/PostFilter.php';
require_once ABSPATH . 'includes/RepSpecialties.php';
require_once ABSPATH . 'includes/ProductAPIClient.php';
require_once ABSPATH . 'includes/storefront-panels/ProductPanel.php';
require_once ABSPATH . '../../../vendor/autoload.php';


use Salesfloor\Models\Event;
use Salesfloor\Models\UnattachedCustomer;
use \Salesfloor\Services\Event as EventService;
use Salesfloor\Models\RepOnboarding as RepOnboardingModel;

$content_width = 600; // Change Default width to 600 for SF elements

class Salesfloor_Options
{

    private $options = array();

    /**
     * Initialize class.
     * @access public
     */
    public function __construct()
    {
        // check cache server, if its there get from there

        // no cache. load from wpdb
        global $wpdb;
        $opts = $wpdb->get_results("select option_name, option_value from wp_options where option_name like 'sf_options_%'");
        // remove sf_options_ then split on |
        foreach ($opts as $key => $value) {
            $this->options[substr($value->option_name,11)] = $value->option_value;
        }
    }

    /**
     * Get an option if it is set
     * @access public
     * @param path string: option to check
     */

    public function option($path = null) {
        if (!$path) {
            return null;
        }
        if (isset($this->options[$path])) {
            return $this->options[$path];
        } else {
            return null;
        }
    }
} //END Class
$sf_options = new Salesfloor_Options;


function sf_remove_twentyfourteen_scripts()
{
    global $pagenow;
    //error_log('pagenow >'. $pagenow);
    // if (// we are editing an old post
    //  ( 'index.php' !== $pagenow ) ||
    //  ( 'post.php' == $pagenow && isset($_GET['post']) && 'sf-content' == get_post_type($_GET['post']) )  ||
    //  ( isset($_GET['p']) && 'sf-content' == get_post_type($_GET['p']) )  || // in preview mode, the url looks like ?p=581
    //  ( isset($_GET['sf-content']) && 'sf-content' == get_post_type($_GET['sf-content']) )  ||  // in content view, the url looks like ?sf-content={POST_ID}
    //  ( 'post-new.php' == $pagenow && isset($_GET['post_type']) && 'sf-content' == $_GET['post_type'] ) ||
    //  ( 'sf-content' == get_post_type( get_the_ID() ))
    // ) {
        // Load our main stylesheet.
        wp_deregister_style( 'twentyfourteen-style' );
        // Load the Internet Explorer specific stylesheet.
        wp_deregister_style( 'twentyfourteen-ie', get_template_directory_uri() . '/css/ie.css', array( 'twentyfourteen-style', 'genericons' ), '20131205' );
    // }
}

// force this to run after the native twentyfourteen does its thing
add_action( 'wp_enqueue_scripts', 'sf_remove_twentyfourteen_scripts', 99 );

// trigger the strong password enforcement for all users:
//add_filter( 'slt_fsp_caps_check', __return_empty_array() );

function sf_check_password_strength($password)
{
    global $app;
    $policy = $app['security.policy.password'];
    return $policy->validatePassword($password);
}

add_action( 'user_profile_update_errors', 'sf_validateProfileUpdate', 10, 3 );
add_action('validate_password_reset', 'sf_validatePasswordReset', 10, 2 );

/**
 * validate profile update
 *
 * <AUTHOR> Sexton <<EMAIL>>
 * @param   WP_Error $errors
 * @param   boolean $update
 * @param   object $user raw user object not a WP_User
 */
function sf_validateProfileUpdate( WP_Error &$errors, $update, &$user ) {
    return sf_validateComplexPassword( $errors );
}


/**
 * validate password reset
 *
 * <AUTHOR> Sexton <<EMAIL>>
 * @param   WP_Error $errors
 * @param   stdClass $userData
 * @return  WP_Error
 */
function sf_validatePasswordReset( $errors, $userData ) {
    return sf_validateComplexPassword( $errors );
}

/**
 * validate complex password
 *
 * <AUTHOR> Sexton <<EMAIL>>
 * @param   WP_Error $errors
 * @param   stdClass $userData
 * @return  WP_Error
 */
function sf_validateComplexPassword( $errors ) {
    $password = ( isset( $_POST[ 'pass1' ] ) && trim( $_POST[ 'pass1' ] ) ) ? $_POST[ 'pass1' ] : null;

    // no password or already has password error
    if ( empty( $password ) || ( $errors->get_error_data( 'pass' ) ) ) {
        return $errors;
    }

    $isAllowedPassword = sf_check_password_strength($password);
    if (!$isAllowedPassword) {
        $errors->add('passwordstrength', _("Your password is not strong enough."));
    }

    return $errors;
}

function sf_custom_content_init()
{
    $labels = array(
        'name'             => 'Content',
        'singular_name'   => 'Content',
        'add_new'           => 'Add New',
        'add_new_item'     => 'Add New Content',
        'edit_item'       => 'Edit Content',
        'new_item'         => 'New Content',
        'all_items'       => 'All Content',
        'view_item'       => 'View Content',
        'search_items'     => 'Search Content',
        'not_found'       => 'No content found',
        'not_found_in_trash' => 'No content found in Trash',
        'parent_item_colon'  => '',
        'menu_name'       => 'Content'
    );
    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'           => true,
        'show_in_menu'     => true,
        'query_var'       => true,
        'rewrite'           => array( 'slug' => 'sf-content' ),
        'capability_type'   => 'post',
        'has_archive'       => true,
        'hierarchical'     => false,
        'menu_position'   => null,
        'supports'         => array( 'title', 'editor', 'tags' ),
        'taxonomies'         => array( 'post_tag'),
    );
    register_post_type( 'sf-content', $args );
    register_taxonomy_for_object_type( 'post-tag', 'sf-content' );
}
add_action( 'init', 'sf_custom_content_init', 1 );
add_action('admin_init', 'set_user_metaboxes');
add_action('admin_init', 'sf_redirect_onboarding');

function set_user_metaboxes($user_id=NULL)
{
    // These are the metakeys we will need to update
    $meta_key['order'] = 'meta-box-order_post';
    // So this can be used without hooking into user_register
    if ( ! $user_id) {
        $user_id = get_current_user_id();
    }
    // Set the default order if it has not been set yet
    if ( ! get_user_meta( $user_id, $meta_key['order'], true) ) {
        $meta_value = array(
            'side' => 'sf_rep_email_sectionid,sf_rep_share_sectionid,sf_rep_feature_sectionid,tagsdiv-post_tag',
            'normal' => 'submitdiv',
            'advanced' => '',
        );
        update_user_meta( $user_id, $meta_key['order'], $meta_value );
    }
}

function sf_redirect_onboarding()
{
    // this is only called in admin_init which means the user has been validated as an admin user
    global $wpdb;
    $user_id = get_current_user_id();
    // don't prepare, we trust the user id from WP.
    $page_id = $wpdb->get_var('select page from sf_rep_onboarding where wp_user_id = '. $user_id);
    if (isset($page_id) && null !== $page_id) {
        if ($page_id < 2) {
            // page 0 is not setup, page 1 is password created. page 2 is account setup (email &c)
            // so page 0 has no wp_user, we can assume if we are here it is page 1
            header("Location: /setup/account");
            exit;
        }
    }
}

// Create a thumbnail if a post is published or updated
add_action( 'generate_thumbnail', 'sf_create_thumbnail' );
function sf_create_thumbnail($post_id) {
    $meta_image = get_post_meta($post_id, 'sf_product_image_url');

    // If we are still editing no save.
    $post_info = get_post($post_id);

    if ($post_info->ping_status !== 'closed')
        return;

    if (!empty($meta_image))
        return; // Do not create thumbnail for element which has associated images.

    $permalink = get_permalink( $post_id );
    $now = time();
    if ( defined('SF_PRODUCTION') && (SF_PRODUCTION == true))
    {
        $file = "/tmp/$now-$post_id.js";
        $destination = "/var/www/wp-includes/images/thumbnails/";
    }
    else
    {
        $file = "$now-$post_id.js";
        $destination = "../wp-includes/images/thumbnails/";
    }
    // Keeping to trace the amount of regeneration that is happening.
    // create the js file
    $fh = fopen($file, 'w');
    fwrite($fh,"
page = require('webpage').create();
page.clipRect = { top: 0, left: 0, width: 400, height: 600 };
page.open('$permalink&headless', function() {
    page.render('$destination$post_id.png');
    phantom.exit();
});");
    fclose($fh);
    // make sure phantomjs is in the path
    if ( defined('SF_PRODUCTION') && (SF_PRODUCTION == true))
    {
        // Call the wrapper to ensure process being terminated if required to avoid getting the library stuck
        $cmd = SF_SHELL_DIR . "/launch_monitor_phantom.sh $file &";
        // Disabled for the moment.
        // $cmd = "cp '{$destination}thumbnail.png'  '$destination$post_id.png'";
    }
    else
    {
        $cmd = "phantomjs $file";
    }
    $response = exec($cmd);
    unlink($file);
}
// Create a thumbnail if a post is published or updated
add_action( 'schedule_thumbnail', 'sf_schedule_thumbnail' );
function sf_schedule_thumbnail($post_id) {
    // ignore autosave
    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) return;
    // Protection to avoid getting something in the post_id with a ; permitting someone from escaping in our shell and do nasty.
    if (filter_var($post_id, FILTER_VALIDATE_INT) == false) return;
    // Should copy the default place holder and then set the schedule for the future
    // Waiting the real code, calling the generator
    sf_create_thumbnail($post_id);
}

/**/
/**/
/* Salesfloor utility function, to prevent code rewrite adding functions here which are shared by multiple screens. */
/**/
/**/
/*
 * Return an array of the Rep categories with a boolean if them has this categories in his profile or not.
 */
class Salesfloor_Categories {
    /**
     * @var
     */
    protected $user_id;
    protected $username;
    // Keep them even if we create multiple time the object this is the same list anyway.
    protected static $repSelection = [];
    protected static $category_view = null;

    /**
     * Initialize class.
     *
     * @access public
     */
    public function __construct($user, $username) {
        global $wpdb, $app;
        $this->user_id = $user;
        $this->username = $username;
        // Populate the Rep Selection is not defined.
        if (Salesfloor_Categories::$repSelection === []) {
            $rep_category = $wpdb->get_results($wpdb->prepare("SELECT term_id FROM sf_rep_product_selection_map where user_id = %d", $this->user_id));
            // Build the list of which dept are in the Rep selection.
            foreach($rep_category as $index => $obj) {
                // This will simplify the look-up
                Salesfloor_Categories::$repSelection[] = $obj->term_id;
            }
        }
        // Get the Category if this is not already defined.
        if (Salesfloor_Categories::$category_view == null)
        {
            $results = $wpdb->get_results("
                SELECT
                    c.term_id as termid,
                    p.term_id as parent_id,
                    c.name as name,
                    p.name as parent_name,
                    pt.description as parent_url,
                    ct.description as url
                from wp_terms p
                left join wp_term_taxonomy pt on p.term_id = pt.term_id
                left join wp_term_taxonomy ct on ct.parent = p.term_id
                left join wp_terms c on c.term_id = ct.term_id
                where pt.parent = {$app['configs']['products.root_category_id']} AND c.term_id IS NOT NULL
                order by TRIM(p.name), TRIM(c.name);
            ");

            // Let's keep the above sql to reduce break change
            // and below logic trigger by config which only HBC has value
            if (!empty($app['configs']['retailer.specialties.appending'])) {
                // TODO : we don't handle multi-lang in BACKOFFICE, take the default:'en_US' by now
                $defaultLocale        = $app['configs']['retailer.i18n.default_locale'];
                $specialtiesAppending = $app['configs']['retailer.specialties.appending'][$defaultLocale];
                foreach ($specialtiesAppending as $category) {
                    $categoryObject = new stdClass();

                    $categoryObject->termid      = '';
                    $categoryObject->parent_id   = $category['id'];
                    $categoryObject->name        = '';
                    $categoryObject->parent_name = $category['name'];
                    $categoryObject->parent_url  = '';
                    $categoryObject->url         = '';

                    $results[] = $categoryObject;
                }
            }

            Salesfloor_Categories::$category_view = $results;
        }
    }
    public function listDepartmentNames($repSelect = true, $subcategory = false)
    {
        $list = '';
        foreach(Salesfloor_Categories::$category_view as $category)
        {

        }
    }

    /**
     * This method return categories for a rep
     * @param  integer $userId user id
     * @return array           Array of categories
     */
    public function getRepCategories($userId)
    {
        global $wpdb;
        $categories = $wpdb->get_results($wpdb->prepare(
            "SELECT c.category_id, c.name FROM sf_categories c
             INNER JOIN sf_rep_product_selection_map rpsm ON c.category_id = rpsm.category_id
             WHERE rpsm.user_id = %d", $userId));

        return $categories;
    }

    public function displayDepartments($repSelect = true, $subcategory=false, $link=false, $list=false)
    {
        $parent_id = null;
        $skip_child = false;
        $new_parent_added = false;
        $element = ( $list ? '' : "<ul>" );
        // cannot have links and list, it gets too messy
        if ($link) $list = false;
        foreach(Salesfloor_Categories::$category_view as $category)
        {
            // Process parent if this is a new node.
            if ($parent_id != $category->parent_id)
            {
                // if (Salesfloor_Categories::$repSelection[$category->parent_id] != $repSelect)
                if ($repSelect && !in_array($category->parent_id, Salesfloor_Categories::$repSelection)) {
                    // We do skip the one which are not a rep selection in the first pass
                    continue;
                }

                if ($parent_id != null)
                {
                    if ($subcategory == true && !$skip_child)
                    {
                        // If there is a sub category it need closure too.
                        $element .= ( $list ? '' : "</ul>\n" );
                    }
                    // If we are coming from a loop this mean we need to close the <li> from the previous parent
                    $element .= ( $list ? ', ' : "</li>\n" );
                }
                $parent_id = $category->parent_id;
                // When we are here this mean we are doing a new node.
                // We build the Department with our information.
                $new_parent_added = true;
                $skip_child = false;
                if ($link == true)
                {
                    $subNav = ($subcategory == true && $category->name) ? 'class="fn-main-navigation-trigger-first-level icon-angle-right"' : "";
                                        $element .= '<li><a href="' . get_home_url() . '/shop?rep=' . $this->username . '&sf_url=' . urlencode('http://www.toysrus.ca/category/index.jsp?categoryId=' . $category->parent_id) . '" '.$subNav.'>' . $category->parent_name . '</a>';
                }
                else
                {
                    $element .= ( $list ? '' : '<li>' ) . $category->parent_name;
                }
                // Here we have the start of the <li>... for the parent dept. We need to insert the sub elements.
            }
            // We may or not need to display a sub category, some page only need depts.
            if ($subcategory == true && $category->name)
            {
                if ($new_parent_added)
                {
                    $element .= ( $list ? ', ' : '<ul class="subNav-n">' );
                    $new_parent_added = false;
                                        $element .= '<li><a class="visible-xs visible-sm fn-main-navigation-trigger-back trigger-back icon-angle-left">Back</a></li>';
                }
                if ($link == true)
                {
                    $element .= '<li><a href="' . get_home_url() . '/shop?rep=' . $this->username . '&sf_url=' . urlencode('http://www.toysrus.ca/category/index.jsp?categoryId=' . $category->termid) . '">' . $category->name . '</a></li>' . "\n";
                }
                else
                {
                    $element .= ( $list ? '' : '<li>' ) . $category->name . ( $list ? ', ' : ('</li>' . "\n") );
                }
            } else if ($subcategory == true) {
                $skip_child = true;
            }
        }

        if ($parent_id != null) // Skip all display if there is nothing to show.
        {
            if ($subcategory == true)
            {
                // If there is a sub category it need closure too.
                $element .= ( $list ? '' : "</ul>\n" );
            }
            // Print our results
            $element .= ( $list ? '' : ( "</li>\n</ul>\n" ) );
            if ($list) {
                $element = trim($element);
                $element = rtrim($element,',');
                $element = preg_replace('/(, ){2,}/', ', ', $element);
            }
            echo $element;
        }
    }
}
define('SF_EVENT_FEEDBACK', 1);
define('SF_EVENT_PROFILE', 2);
define('SF_EVENT_CREATE', 3);
define('SF_EVENT_UPDATE', 4);
define('SF_EVENT_CURATE', 5);
define('SF_EVENT_ASKQUESTION', 6);
define('SF_EVENT_BOOKAPT', 7);
define('SF_EVENT_FINDER', 8);
define('SF_EVENT_LIVESESSION_START', 9);
define('SF_EVENT_LIVESESSION_END', 10);   // NOTE: This value is copied in sf_express for completion event in node.
define('SF_EVENT_PRODUCT', 11);
define('SF_EVENT_DEAL', 12);
define('SF_EVENT_CONTENT', 13);
define('SF_EVENT_UNSUBSCRIBE', 14);
define('SF_EVENT_NEWSALE', 15);
define('SF_EVENT_SUBSCRIBE', 16);
define('SF_EVENT_RETAILER', 17);
define('SF_EVENT_CONCERN', 18);
define('SF_EVENT_PAGE_LIVE', 19);
define('SF_EVENT_NEW_SHOPPING', 20); // Customer went on the shopping page, we add tracking in case we sell something...
define('SF_EVENT_NEW_USER_HIT', 21); // This duplicates the GA information, however this do not appear heavy to account at this stage.
define('SF_EVENT_RETURNING_USER_HIT', 22); // TODO: After MVP this implies rolling tables and aggregate older time reports.
define('SF_EVENT_SALE_DUPLICATE', 23); // Should not happen but track event where we are receiving same sale event.
define('SF_EVENT_MAIL_SENT', 24); // SEnd mail event.
define('SF_EVENT_MAIL_OPEN', 25); // The pixel for mail open tracking has been called.
define('SF_EVENT_MAIL_CLICK', 26); // A link in the email has been used.
define('SF_EVENT_USER_ADD', 27); // A new contact have been add
define('SF_EVENT_USER_VISIT', 28); // A user visit our site in a new browser session.
define('SF_EVENT_COM_REF', 29); // Being hit from a referal of the retailer
define('SF_EVENT_SOC_REF', 30); // Being hit from a referal of a social post
define('SF_EVENT_RETAIL_HIT', 31); // Number of hit on the retailer from the shopping page.
define('SF_EVENT_SOCIAL_POST', 32); // Post has been done on the rep social network.
define('SF_EVENT_SOCIAL_SHARE', 33); // TODO: Should be someone share a social post from Reggie...
define('SF_EVENT_MODERATE_LEAD', 34); // whenever a rep marks a lead as unusable
define('SF_EVENT_CUSTOMER_CARD', 35); // A customer use the send postcard feature.
define('SF_EVENT_LIVESESSION_REGISTER', 37); // Triggered when a rep gets a request and a meeting is scheduled with zoom
define('SF_EVENT_CHANGE_CATEGORIES', 38); // rep changes their category list
define('SF_EVENT_CHATSESSION_REGISTER', 39); // Triggered when a rep gets and responds to a chat request
define('SF_EVENT_STOREEVENT_CREATE', 40); // Triggered when a rep create a new store event
define('SF_EVENT_STOREEVENT_UPDATE', 41); // Triggered when a rep update a store event
define('SF_EVENT_STOREEVENT_DELETE', 42); // Triggered when a rep delete a store event
define('SF_EVENT_STOREEVENT_SUBSCRIBE', 43); // Triggered when a rep delete a store event
define('SF_EVENT_CHAT_REQUEST', 44);
define('SF_EVENT_CHAT_ANSWER', 45);
define('SF_EVENT_SIDEBAR_VIEW', 46);
define('SF_EVENT_SIDEBAR_CLICK', 47);
define('SF_EVENT_FOOTER_VIEW', 48);
define('SF_EVENT_FOOTER_CLICK', 49);
define('SF_EVENT_STOREFRONT_CLICK', 50);
define('SF_EVENT_TRANSACTIONAL_MAIL_SENT', 51); // emails sent by the app (e.g. "thanks for your request!")
define('SF_EVENT_RESPONSE_MAIL_SENT', 52); // emails sent by the rep from the message center in response to a customer request
define('SF_EVENT_COURTESY_MAIL_SENT', 53); // emails sent by the rep from the message center that aren't in response to a request (rep's own initiative)
// SF-15128 Track Recommendations of products through different channels
define('SF_EVENT_RECOMMENDATION_chat', 54); // A recommendation event. Results of sharing a product during chat
define('SF_EVENT_RECOMMENDATION_compose_message', 55); // A recommendation event. Results of sharing a product while composing a message
// Share product recommendation events come from Share An Update OR from Publisher on backoffice
define('SF_EVENT_RECOMMENDATION_share_email', 56); // A recommendation event. Results of sharing a product while sharing through email
define('SF_EVENT_RECOMMENDATION_share_facebook', 57); // A recommendation event. Results of sharing a product while sharing through facebook
define('SF_EVENT_RECOMMENDATION_share_twitter', 58); // A recommendation event. Results of sharing a product while sharing through twitter
// SF-15188 Track clicks of product grids (recommended)
define('SF_EVENT_CLICK_top_picks', 59); // Clicking of a Top Pick product
define('SF_EVENT_CLICK_latest_arrivals', 60); // Clicking of a Latest Arrivals / Deals product
define('SF_EVENT_CLICK_recommended', 61); // Clicking of a Recommended Product

// SF-16088 Track clicks of product (recommended)
define('SF_EVENT_CLICK_CHAT', 62);  // Click on a product in the chat (*Only customer, not the rep)
define('SF_EVENT_CLICK_SHARE', 63);  // Click on a product in the email from a share
define('SF_EVENT_CLICK_MESSAGE', 64);  // Click on a product in a private message (*Only customer, not the rep)
define('SF_EVENT_CLICK_FACEBOOK', 65);  // Click on a product on facebook from a share
define('SF_EVENT_CLICK_TWITTER', 66);  // Click on a product on twitter from a share
define('SF_EVENT_CLICK_LINKEDIN', 67);  // NOT USED at the moment
define('SF_EVENT_CLICK_LOOKBOOK', 68);  // Click on a product in a lookbook page
define('SF_EVENT_CLICK_CUSTOMER_REQUEST', 69);  // At the moment, we use the message code for customer service message

// SF-16087 Track recommendation from backoffice/mobile for those 2 panels (top_pick / new_arrivals)
define('SF_EVENT_RECOMMENDATION_TOP_PICKS', 70);
define('SF_EVENT_RECOMMENDATION_NEW_ARRIVALS', 71);

// SF-18099 Automated Storefront Tasks (Nag revamp)
// Events mark that rep has already been reminded about Share An Update / Update Products
define('SF_EVENT_TASK_SHARE_UPDATE', 72);
define('SF_EVENT_TASK_UPDATE_STOREFRONT_PRODUCTS', 73);
define('SF_EVENT_TASK_NEW_CUSTOMER_PURCHASE', 74);
define('SF_EVENT_TASK_SOON_TO_LAPSE', 75);

define('SF_EVENT_RECOMMENDATION_TEXT_MESSAGE', 76); // A recommendation event. Results of attaching a product when texting
define('SF_EVENT_CLICK_TEXT_PRODUCT', 77);          // Clicking of a text message attached product

// SF-17601 Metrics For Tasks
define('SF_EVENT_TASK_AUTOMATED_CREATED',   EventService::SF_EVENT_TASK_AUTOMATED_CREATED);
define('SF_EVENT_TASK_AUTOMATED_RESOLVED',  EventService::SF_EVENT_TASK_AUTOMATED_RESOLVED);
define('SF_EVENT_TASK_AUTOMATED_DISMISSED', EventService::SF_EVENT_TASK_AUTOMATED_DISMISSED);
define('SF_EVENT_TASK_MANUAL_CREATED',      EventService::SF_EVENT_TASK_MANUAL_CREATED);
define('SF_EVENT_TASK_MANUAL_RESOLVED',     EventService::SF_EVENT_TASK_MANUAL_RESOLVED);
define('SF_EVENT_TASK_MANUAL_DISMISSED',    EventService::SF_EVENT_TASK_MANUAL_DISMISSED);

// PP-125
define('SF_EVENT_CHAT_FLAGGED_INAPPROPRIATE', EventService::SF_EVENT_CHAT_FLAGGED_INAPPROPRIATE);

// PP-126 Track share attempts
define('SF_EVENT_LIBRARY_SHARE_ATTEMPT', EventService::SF_EVENT_LIBRARY_SHARE_ATTEMPT);

// PP-165 Lookbook Create / Update metrics
define('SF_EVENT_LOOKBOOK_CREATE', EventService::SF_EVENT_LOOKBOOK_CREATE);
define('SF_EVENT_LOOKBOOK_UPDATE', EventService::SF_EVENT_LOOKBOOK_UPDATE);

define('SF_EVENT_STYLED_LINK_SHARED_EMAIL', EventService::SF_EVENT_STYLED_LINK_SHARED_EMAIL);
define('SF_EVENT_STYLED_LINK_SHARED_SMS', EventService::SF_EVENT_STYLED_LINK_SHARED_SMS);

/**
 *  This add an entry in the sf_event database for reporting of specific salesfloor event.  The global reporting
 *  of those events will be hand off to Google but we still need to populate some pages eg. activity feed with the
 *  list of event that have been done. Also this page
 *
 * MIGRATED
 * @see \Salesfloor\Services\Event::trackEvent
 *
 * @param null $event_type  ; Type of event that need to be posted
 * @param null $source      ; Text field, origine of the event
 * @param null $customer_id   ; Customer ID
 * @param null $user_id    ; Rep ID, if rep can be derived from session this is not required
 * @param null $relatedEvent  ; Event associate to the current event eg(satisfaction for the appointment X).
 * @param null $attributes  ; Object/Structure specific to the event
 *
 * return <html code with google tracker for this element>
 */
function sf_add_tracker( $event_type, $source = null, $customer_id = null, $user_id = null, $relatedEvent = null, $attributes = null, $uniq_id=null)
{
    assert('isset($event_type)', "Require a valid event type.");
    global $wpdb;
    // We received an event, let's update the database.
    // Find the user from the session if not set already
    $this_user_id = get_current_user_id();
    if (! isset($user_id)) {
        $user_id = $this_user_id;
    }

    if ($this_user_id != $user_id && $attributes == null) {
        $attributes = $this_user_id;
    }

    if ($q = $wpdb->prepare('SELECT store FROM wp_users WHERE ID = %d', [$user_id])) {
        $store_id = $wpdb->get_var($q);
    } else {
        // I don't think this is ever executed, but I don't really know,
        // so here's a fallback default value.
        $store_id = 0;
    }

    // Clear the rep page for events which alter it
    switch($event_type)
    {
        case SF_EVENT_PROFILE:
        case SF_EVENT_CREATE:
        case SF_EVENT_UPDATE:
        case SF_EVENT_CURATE:
        case SF_EVENT_PRODUCT:
        case SF_EVENT_DEAL:
        case SF_EVENT_CONTENT:
        case SF_EVENT_NEWSALE:
        case SF_EVENT_SUBSCRIBE:
        case SF_EVENT_CHANGE_CATEGORIES:
        case SF_EVENT_STOREEVENT_CREATE:
        case SF_EVENT_STOREEVENT_UPDATE:
        case SF_EVENT_STOREEVENT_DELETE:
        case SF_EVENT_USER_ADD:
        case EventService::SF_EVENT_FAV_PRODUCT:
            $current_user = wp_get_current_user();
            $store_user = get_store_for_user($current_user);
            break;
    }

    // TODO : Aggregate insert statement for performances...
    if ($uniq_id == null)
        $uniq_id = uniqid("SFID",true);
    // The user email do not exist, let create an entry for this customer
    $wpdb->insert(  "sf_events",
        array( // Data
            "type" => $event_type,
            "source" => $source ?? '', // Can't be null
            "uniq_id" => $uniq_id,
            "user_id" => $user_id ?? 0,  // Rep associated to this user (e.g: type 30 won't have any, optional)
            "customer_id" => $customer_id ?? 0,  // Can't be null
            "attributes" => mb_substr(json_encode($attributes), 0, Event::DB_ATTRIBUTES_MAX_LENGTH),
            "satisfied" => false,
            "event_id" => $relatedEvent ?? 0, // Can't be null
            "store_id" => $store_id,
        ),
        array( // Data format
            "%d",
            "%s",
            "%s",
            "%d",
            "%d",
            "%s",
            "%d",
            "%d",
            "%d",
        ) );
    // Update the related event if present
        // This is use for both customer satisfaction and to denote where a sales have been completed to make the token consumed.
    if (isset($relatedEvent) && $event_type!=SF_EVENT_FEEDBACK)
    {
        $wpdb->get_results(
            $wpdb->prepare(
                "update sf_events set satisfied = satisfied+1 where ID=%d;",
                $relatedEvent
            ));
    }

    return $uniq_id;
}
/**
 * Add a tracker event for that page hit.
 * Alternate tracking code, right now this might be easier to get data from ourselves... Need to evaluate both solutions
 *
 * @param null $user_id
 * @return string
 */
function sf_track_hit($user_id = null)
{
    global $app;

    // ignore non-get requests
    if ( 'GET' !== $_SERVER['REQUEST_METHOD'] ) {
        return;
    }
    if ($user_id !== null)
    {
        if (! isset($_SERVER['HTTP_REFERER']) )
            $_SERVER['HTTP_REFERER'] = '';

        if (! isset($_COOKIE["SFU"]))
        {
            // No Tracking session found, let's track a new event for this and take the ID as our cookie value for futur references.
            // TODO: With that link with the appointements and other function we could match a user by finding the SFU cookie.
            $tracking_info = sf_add_tracker(SF_EVENT_NEW_USER_HIT, $_SERVER["HTTP_REFERER"], null, $user_id->ID, null, $_SERVER["SCRIPT_NAME"]);
            setCookieValue("SFU", $tracking_info, time() + (60*60*24*30));
        }
        else
        {
            // TODO: We might need to look up to validate that it is a valid tracking ID, however this might not create any issue to not do it.
            $tracking_info = $_COOKIE["SFU"];
            sf_add_tracker(SF_EVENT_RETURNING_USER_HIT, $_SERVER["HTTP_REFERER"], null, $user_id->ID, null, $_SERVER["SCRIPT_NAME"], $tracking_info);
        }
        // We need to keep track of visit, so if we do have a visit tracking cookie we are happy and do nothing.
        // Otherwise we set a visit tracking cookie and register an event for that.
        if ((! isset($_COOKIE["SFV"])) || ($_COOKIE["SFV"] !== $tracking_info))
        {
            // No delay this is a session cookie that should clear after the session is done.
            setCookieValue("SFV", $tracking_info, 0);
            sf_add_tracker(SF_EVENT_USER_VISIT, $_SERVER["HTTP_REFERER"], null, $user_id->ID, null, $_SERVER["SCRIPT_NAME"], $tracking_info);
        }
        if (isset($_SERVER["HTTP_REFERER"]))
        {
            if (strstr($_SERVER["HTTP_REFERER"], RETAILER_NAME))
            {
                // If are coming here from a retailer page, account it as from the .COM referrer.
                sf_add_tracker(SF_EVENT_COM_REF,$_SERVER["HTTP_REFERER"]);
            }
            elseif ( strstr($_SERVER['HTTP_REFERER'], RETAILER_SITE))
            {
                sf_add_tracker(SF_EVENT_COM_REF,$_SERVER["HTTP_REFERER"]);
            }
            elseif( SF_SERVICE_WIDGET_HOST && strstr($_SERVER['HTTP_REFERER'], SF_SERVICE_WIDGET_HOST))
            {
                sf_add_tracker(SF_EVENT_COM_REF,$_SERVER["HTTP_REFERER"]);
            }
            elseif (!strstr($_SERVER["HTTP_REFERER"], $_SERVER["SERVER_NAME"]) && $_SERVER["HTTP_REFERER"] != "")
            {
                // If we are coming form a source which is not us nor the retailer, then account as Social Click.
                sf_add_tracker(SF_EVENT_SOC_REF,$_SERVER["HTTP_REFERER"]);
            }
        }
    }
}

/**
 * This is just to make the warning goes away, not even sure if those cookie are still used (probably not)
 *
 * @param $name
 * @param $value
 * @param $duration
 */
function setCookieValue($name, $value, $duration)
{
    global $app;

    // Path is always "/", always secure and always samesite none
    // Since this is not related to auth, it's fine to put it to none even if not mandatory
    setcookie(
        $app['configs']['cookie_prefix'] . $name,
        $value,
        [
            'expires' => $duration,
            'path'     => '/',
            // Don't use 'retailer.cookie_domain' since this value is not properly set.
            // PR Open for months SF-22692 (╯°□°）╯ ┻━┻
            'domain'   => $app['configs']['wordpress.cookie_domain'],
            'httponly' => false,
            'secure'   => true,
            'samesite' => 'none',
        ]
    );
}

/**
 * Return the html/script code to include the google analytic in a page. This is up to the invoker to echo or not the returned code.
 *
 * @return string
 */
function sf_get_analytic_html()
{
    if (!defined('SF_GOOGLE_ANALYTICS_UID') || empty($uid = SF_GOOGLE_ANALYTICS_UID)) {
        return '';
    }

    $userId = get_current_user_id();
    $gaUserId = '';

    // Use new GA feature to track user over multiple device
    // https://support.google.com/analytics/answer/6205850#overview
    if (!empty($userId)) {
        $gaUserId = ", { 'userId': {$userId} }";
    }

    return <<<MARKER
<!-- Google Analytics -->
<script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
        (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
    ga('create', '{$uid}', 'auto'{$gaUserId});
    ga('send', 'pageview');
</script>
MARKER;
}

/**
 * Return the script tag to include FastClick and handle the issue where the mobile keyboard covers the input fields
 *
 * @return string
 */
function sf_get_input_scroll_into_view_html()
{
    return <<<MARKER
<script src="/js/vendor/mobile-detect/mobile-detect.min.js"></script>
<script src="/js/vendor/fastclick/lib/fastclick.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    if (!window.MobileDetect) {
      return;
    }

    var md = new window.MobileDetect(window.navigator.userAgent);

    if (!md.mobile()) {
      //console.log('not mobile, skip tap treatment');
      return;
    }

    // attach fastclick to remove tap event delay

    if (window.FastClick) {
      window.FastClick.attach(document.body);
    }

    // scroll the focused input element into visible area not covered by the
    // keyboard

    function scrollIntoView(event) {
      setTimeout(function () {
        event.target.scrollIntoView();
      }, 300);
    }

    var nodeList = document.querySelectorAll('.js-mobile-focus');
    Array.prototype.forEach.call(nodeList, function (elem) {
      elem.addEventListener('focus', scrollIntoView);
    });
  });
</script>
MARKER;
}

/*
 * message is used for the rep page notification
 * email if the message that need to be added in the Email for this event.
 *
 * */
// Profile Events
// From SF-234
/*
 *
** Performance Summary calling out top 5-10 selling reps per retailer and most Happy Customers
** Salesfloor suggested profile updates
*** Add product to Featured Product or 8-pack (send if Reggie Rep has not updated within 14 days)
*** Create content and Publish to Rep Page (send if Reggie Rep has not updated within 14 days)
*** Send an email to customers (send if Reggie Rep has not sent email within 14 days)
*** Get New Leads (if Reggie Rep has not accessed Lead Generation tool in SF within 14 days)
*** Add Deals (send if Reggie Rep has not updated within 14 days)
*** Combine to single email if more than one of these applies in given 2-wk period.
 *
 * */
// TODO: Notify rep if no proudct update since 14 days
// Last modified product : select unix_timestamp(date) as udate from sf_product where user_id=5 order by date desc limit 1;
// Last modified deal : select unix_timestamp(date) as udate from sf_deal where user_id=5 order by date desc limit 1;
// Last published content : select unix_timestamp(date) as udate from sf_content where user_id=5 order by date desc limit 1;
// Last created content :  select unix_timestamp(post_date) as udate from wp_posts where post_author = 5 and post_status in ('publish', 'scheduled') order by post_date desc limit 1;
// Last Email sent : select unix_timestamp(date) as udate from sf_messages where user_id=5 and from_type='user' order by date desc limit 1;
// TODO: Lead generation when this will be produced.
// Notify event are meant for the Backoffice page
// The mailnotify events are email notification, the mail keep track of which notification have been sent already to avoid redundant mail.
function sf_get_notification_to_send($user,$ignore_customer_requests = false)
{
    global $sf_options, $store_user, $wpdb, $app;
    $notificatiosSent = get_user_meta($user->ID, "notifications", true);
    // In case we did not have a value yet.
    if ($notificatiosSent == false)
        $notificatiosSent = 0;
    $newNotificationMask = $notificatiosSent;
    // Return a JSON for all the activity that are due for that Rep
    // This will be use for the BackOffice notification and the mail_processor for event trigger
    // TODO: This might be worth to create as a JSON in the Template folder...
    // Prep the Array
    // NOTE: The link do not contain the wordpress since there will be a homeurl appended to it which adds that.

    $notifs = array();

    if ($app['configs']['retailer.notifications_create_description']) {
        $notifs["onboarding_description"] = array(
            "category" => "Complete Profile",
            "title" => "Create a personalized description",
            "message" => _("You can create a personalized description to help engage your customers."),
            "query" => "select steps_completed from sf_rep_onboarding where wp_user_id = %d;",
            "js_trigger" => 'fn-trigger-description-open',
            "mask" => RepOnboardingModel::STEP_ABOUT_ME,
            "link" => "#",
            "label" => _('GET STARTED')
        );
    }

    if ($app['configs']['retailer.social_networks']) {
        $notifs["onboarding_social"] = array(
            "category" => "Complete Profile",
            "title" => "Link to your social networks",
            "message" => _("You can link Salesfloor directly to your social networks! You will be able to send updates and alerts directly from Salesfloor!"),
            "callback" => function() {
                global $user;
                return (count(array_filter(SocialNetworkSessions::getInstance($user->ID)->getConnectionStatus())) == 0);
            },
            "js_trigger" => 'fn-modal-opener-trigger',  // always
            "mask" => RepOnboardingModel::STEP_SOCIAL,
            "modal_link" => "/setup/connect-social?headless=1",
            "link" => "#",
            "label" => _('GET STARTED')
        );
    }

    if ($app['configs']['retailer.onboarding.step.add_contacts']) {
      $notifs['onboarding_emails'] = array(
        "category" => "Complete Profile",
        "title" => "Import your emails",
        "message" => _("You can import email addresses so you can send them updates and alerts directly from Salesfloor!"),
        "query" => "select steps_completed from sf_rep_onboarding where wp_user_id = %d;",
        "js_trigger" => 'fn-import-trigger',  // always
        "mask" => RepOnboardingModel::STEP_EMAIL,
        "link" => "#",
        "label" => _('GET STARTED')
      );
    }

    $notifs = array(
        "onboarding_picture" => array(
            "category" => "Complete Profile",
            "title" => "Upload a picture",
            "message" => _("You can save a picture of yourself to help engage your customers."),
            "query" => "select steps_completed from sf_rep_onboarding where wp_user_id = %d;",
            "js_trigger" => 'fn-trigger-image-open',
            "mask" => RepOnboardingModel::STEP_UPLOAD_PIC,
            "link" => "#",
            "label" => _('GET STARTED')
        )
    ) + $notifs;

    if (!$ignore_customer_requests) {
        $notif_requests = [];

        if ($app['configs']['retailer.backoffice.pii.is_visible']) {
            $notif_requests = array(
                /*  Define the unanswered store request here so it is always first */
                'unanswered_requests' => array(
                    "category" => 'Customer Relations',
                    "title" => _("IMPORTANT!"),
                    "message" => _('You have unanswered customer request(s)'),
                    "label" => _('SEE REQUESTS'),
                    "query" =>  "
                        SELECT `request_id`, `request_type`
                        FROM `sf_messages`
                        WHERE `id` IN (
                            SELECT MAX(`id`)
                            FROM `sf_messages`
                            WHERE `request_id` != 0
                                AND `owner_id` = %d
                                AND `type` != 'private'
                            GROUP BY `request_type`, `request_id`
                        )
                            AND `from_type` = 'customer'
                            AND `status` != 'resolved'
                            AND `message` != 2
                            AND `message` != 4
                            AND `date` <= DATE_SUB(NOW(), INTERVAL 2 HOUR)"
                ),
                'unanswered_messages' => array(
                    "category" => 'Customer Relations',
                    "title" => _("IMPORTANT!"),
                    "message" => _('You have unanswered message(s)'),
                    "label" => _('SEE MESSAGES'),
                    "query" =>  "
                        SELECT `thread_id`
                        FROM `sf_messages`
                        WHERE `id` IN (
                            SELECT MAX(`id`)
                            FROM `sf_messages`
                            WHERE (`request_id` = 0 or `request_id` is null)
                                AND `owner_id` = %d
                            GROUP BY `thread_id`
                        )
                            AND `from_type` = 'customer'
                            AND `category` = 'inbox'
                            AND `status` = 'unread'
                            AND `date` <= date_sub(now(), INTERVAL 2 HOUR)"
                )
            );
        }
        $notifs = $notif_requests + $notifs;
    }


    if ( 0 !== $sf_options->option('notifications|products|'.RETAILER_NAME)) {
        if ($store_user) {
            $title = _("Update your storefront's products");
            $message = _("Make sure you keep up to date with interesting products for your customers.");
        } else {
            $title = _("Update your products");
            $message = _("Make sure you keep up to date with interesting products for your customers.");
        }
        $notifs["product"] = array(
            "category" => "Product Updates",
            "title" => $title,
            "message" => $message,
            "query" => "select unix_timestamp(max(date)) as udate from sf_product where user_id=%d;",
            "trigger" => 14*24*3600,  // 14 days
            "mask" => 0x01,
            "link" => "#",
            "js_trigger" => "fn-update-trigger-product-edit",
            "click_target" => "",
            "label" => _('EDIT PRODUCTS')
        );
    }
    if ( '0' !== $sf_options->option('notifications|deals|'.RETAILER_NAME) && $app['configs']['retailer.notifications_deals_current']) {
        $notifs["deal"] = array(
            "category" => "Deal Updates",
            "title" => "Keep your deals current",
            "message" => _("Your last deal update was more than 14 days ago. Make sure that you have the latest and hottest deals for your customers."),
            "query" => "select unix_timestamp(max(date)) as udate from sf_deal where user_id=%d;",
            "trigger" => 14*24*3600,  // 14 days
            "mask" => 0x02,
            "link" => "#",
            "js_trigger" => "fn-update-trigger-deals-edit",
            "click_target" => "",
            "label" => _('EDIT DEALS')
        );
    }
  if ( '0' !== $sf_options->option('notifications|content|'.RETAILER_NAME) && $app['configs']['retailer.notifications_posts_current']) {
        $notifs["content"] = array(
            "category" => "Your Posts",
            "title" => "Keep your posts current",
            "message" => _("Your last new post was more than 14 days ago. You should make sure to keep adding new content and sharing it with your customers."),
            "query" => "select unix_timestamp(max(date)) as udate from sf_content where user_id=%d;",
            "trigger" => 14*24*3600,  // 14 days
            "mask" => 0x04,
            "link" => "#",
            "js_trigger" => "fn-update-trigger-posts-edit",
            "click_target" => "",
            "label" => _('SHARE POSTS')
        );
    }
    if ( '1' === $sf_options->option('notifications|email|'.RETAILER_NAME) && $app['configs']['retailer.backoffice.pii.is_visible']) {
        $notifs["email"] = array(
            "category" => "Customer Emails",
            "title" => "Talk to your leads",
            "message" => _("The last time you sent an email to a customer was more than 14 days ago. Keep your customers engaged by emailing them deals, updates, and posts."),
            "query" => "select unix_timestamp(max(date)) as udate from sf_messages where user_id=%d and from_type='user';",
            "trigger" => 14*24*3600,  // 14 days
            "mask" => 0x08,
            "link" => "/backoffice/store-request-center#/messages/compose",
            "label" => _('SEND EMAIL')
        );
    }
    if ( '1' === $sf_options->option('notifications|leads|'.RETAILER_NAME) && !$store_user && $app['configs']['retailer.backoffice.pii.is_visible']) {
        $notifs["lead"] = array(
            "category" => "Claim leads",
            "title" => "",
            "message" => "",
            "query" => "",
            "trigger" => 3*3600,  // 3 hours
            "mask" => 0x10,
            "link" => "/backoffice/new-leads",
            "label" => _('CLAIM LEADS')
        );
    }
    if ( '0' !== $sf_options->option('notifications|share|'.RETAILER_NAME) && $app['configs']['retailer.notifications_send_update'] && $app['configs']['retailer.backoffice.pii.is_visible']) {
        if ($store_user) {
            $message = _("You can send customers updates about products  and promotions from your store.");
            $query = "select unix_timestamp(max(date)) as udate from sf_events where type = " . SF_EVENT_CURATE . " and user_id in (" .
                        "select id from wp_users where type = 'rep' and store = (select store_id from sf_store where store_user_id = %d))";
        } else {
            $message = _("You can send customers updates about products  and promotions.");
            $query = "select unix_timestamp(max(date)) as udate from sf_events where type = " . SF_EVENT_CURATE . " and user_id = %d;";
        }
        $notifs["share"] = array(
            "category" => "Share Content",
            "title" => "Send updates to customers",
            "message" => $message,
            "query" => $query,
            "trigger" => 14*24*3600,  // 14 days,
            "js_trigger" => "fn-update-trigger-share",
            "mask" => 0x40,
            "link" => "#",
            "label" => _('GET STARTED')
        );
    }
    if ( '0' !== $sf_options->option('notifications|events|'.RETAILER_NAME) && $app['configs']['retailer.notifications_create_event']) {
        if (RETAILER_STOREPAGE_MODE) {
            $notifs["create"] = array(
                "category" => "Create Events",
                "title" => "Add an event",
                "message" => _("Make sure to add events to your storefront so that customers can see what’s going on."),
                "query" => "select unix_timestamp(max(last_update)) as udate from sf_store_events where store_user_id = %d;",
                "trigger" => 30*24*3600,  // 30 days
                "mask" => 0x80,
                "js_trigger" => "fn-update-trigger-event-add",
                "label" => _('GET STARTED')
            );
        }
    }
    if(RETAILER_REPS_HAVE_SPECIALTIES) {
        $notifs["onboarding_categories"] = array(
            "category" => "Complete Profile",
            "title" => "Choose categories",
            "message" => _("Tell us which categories in which you are an expert so we can send customers who need help with those categories to you."),
            "query" => "select steps_completed from sf_rep_onboarding where wp_user_id = %d;",
            "js_trigger" => 'fn-trigger-open',
            "mask" => RepOnboardingModel::STEP_CATEGORIES,
            "link" => "#",
            "label" => _('GET STARTED')
        );
    }

    if ($app['configs']['retailer.backoffice.pii.is_visible']) {
        // get messages
        $notifs["unanswered_requests"]["link"] = "/backoffice/store-request-center#/storeRequests/unresolved";
        $notifs["unanswered_messages"]["link"] = "/backoffice/store-request-center#messages/inbox";
    }

    $count = 0;

    foreach($notifs as $index => $item)
    {
        if ($store_user) {
            $user_id = $store_user->ID;
        } else {
            $user_id = $user->ID;
        }
        $onboarding = ( strpos($index,'onboarding_') === 0 );
        if ( isset($item["query"]) && ($item["query"] !== "") && !$onboarding ) {
            if ( 0 === strpos($index, 'unanswered_')) {
                // aww hell no
            //  $unanswered = $wpdb->get_results($wpdb->prepare(
            //      $item["query"],
            //      $user_id
            //  ));
                $unanswered = [];

                $count = count($unanswered);
                switch ($count) {
                    case 0:
                        // nothing unanswered
                        $notifs[$index]["notify"] = false;
                        unset($notifs[$index]);
                        break;
                    case 1:
                        $notifs[$index]["notify"] = true;
                        // link directly to the object
                        if ( 'unanswered_requests' == $index ) {
                            $notifs[$index]['link'] .= '/thread' . '/' . $unanswered[0]->request_type . '_' . $unanswered[0]->request_id;
                        } else {
                            $notifs[$index]['link'] .= '/thread' . '/' . $unanswered[0]->thread_id;
                        }
                        break;
                    default:
                        $notifs[$index]["notify"] = true;
                        break;
                }
                continue;
            }

            $record_date = $wpdb->get_row(
                $wpdb->prepare(
                    $item["query"],
                    $user_id
                ))->udate;
            unset($notifs[$index]["query"]);
            $notifs[$index]["elapse_days"] = (int)(( time() - $record_date ) / (24*3600)*100)/100;
            $notifs[$index]["date"] = $record_date;
            if ( ( time() - $record_date ) < $item["trigger"]) {
                // This is still in the valid delay, do not raise concerns.
                $notifs[$index]["notify"] = false;
                $notifs[$index]["mail_notify"] = false;
                unset($notifs[$index]);
                // Clear notification which are now clear to permit mail trigger again in the future.
                if ( $item["mask"] ) {
                    $newNotificationMask = $newNotificationMask & (~ $item["mask"]);
                }
            } else {
                // We have a notification for this one.
                $notifs[$index]["notify"] = true;
                $count++;
                // This was not sent yet, add it
                if ( $item["mask"] && ( ($notificatiosSent & $item["mask"]) != $item["mask"]) ) {
                    $notifs[$index]["mail_notify"] = true;
                }
            }
        } elseif(isset($item['callback'])) {
            $notifs[$index]["notify"] = $item['callback']();
        } else if ($onboarding) {
            //error_log('doing >'.$index);
            $result = $wpdb->get_var(
                $wpdb->prepare(
                    $item["query"],
                    $user->ID
                )
            );
            $notifs[$index]["mail_notify"] = false;
            if ($result === null) {
                // this rep didn't go through onboarding. ignore them.
                unset($notifs[$index]);
            } else if (!$result) {
                $notifs[$index]["notify"] = true;
            } else {
                $masked = $result & $item['mask'];
                if ($masked == $item['mask'])
                    unset($notifs[$index]);
                else {
                    $notifs[$index]["notify"] = true;
                    $notifs[$index]["mail_notify"] = false;
                }
            }
        } else {
            // Invalid delete it.
            unset($notifs[$index]);
        }
        // If we are inside the interval we remove the entry
    }

    // Update the notification sent if this change.
    if ($newNotificationMask != $notificatiosSent) {
        update_user_meta($user->ID, "notifications", $newNotificationMask);
    }

    if ( 0 == count($notifs) ) {
        // create default notifications
        $notifs = array();

        if ($store_user) {
            $title = _("Update your storefront's products");
            $message = _("Make sure you keep up to date with interesting products for your customers.");
        } else {
            $title = _("Update your rep page's products");
            $message = _("Make sure you keep up to date with interesting products for your customers.");
        }
        $notifs["product"] = array(
            "category" => "Product Updates",
            "title" => $title,
            "message" => $message,
            "link" => "#",
            "js_trigger" => "fn-update-trigger-product-edit",
            "click_target" => "",
            "notify" => true,
            "mail_notify" => false,
            "label" => _('EDIT PRODUCTS')
        );

        if ($app['configs']['retailer.notifications_send_update'] && $app['configs']['retailer.backoffice.pii.is_visible']) {
            if ($store_user) {
                $message = _("You can send customers updates about products  and promotions from your store.");
            } else {
                $message = _("You can send customers updates about products  and promotions.");
            }
            $notifs["share"] = array(
                "category" => "Share Content",
                "title" => "Send updates to customers",
                "message" => $message,
                "js_trigger" => "fn-update-trigger-share",
                "link" => "#",
                "notify" => true,
                "mail_notify" => false,
                "label" => _('GET STARTED')
            );
        }

    }

    return $notifs;
}
add_action( 'init', 'sf_filter_add_filters', 5 );
function sf_filter_add_filters() {

    add_filter('content_save_pre','sf_filter_content_action',1);
    add_filter('title_save_pre','sf_filter_content_action',1);
    add_filter('the_content','sf_filter_content_action',1);
    add_filter('the_title','sf_filter_content_action',1);
    // do custom calls for custom pages here
    // post parametes we care about:
    // we use both $_REQUEST and $_POST
    // we don't use $_GET for this kind of thing
    $param_names = ['firstname','lastname','description','fname','lname','desc','url', 'newusername','data','message','title','note'];
    foreach(array_keys($_POST) as $key)
    {
        if (in_array(strtolower($key), $param_names))
        {
            $_POST[$key] = sf_filter_content_action($_POST[$key]);
            if (array_key_exists($key,$_REQUEST)) {
                $_REQUEST[$key] = $_POST[$key];
            }
        }
    }
}
function sf_filter_content_action($text)
{
    if ( (array)$text === $text )
    {
        // for some json requests each post parameter is actually an array. This is where we wrap that.
        if ( isset($text['data']) )
        {
            return sf_filter_content_action($text['data']);
        }
    }
    else
    {
        $content = trim($text);
        if (!$content) return '';
        $words = [];

        $filter_file = file_to_include_default_to_generic(SF_TEMPLATES_DIR . '/retailers', SF_WORD_FILTER_LIST);
        // bring in the curated list of words, line by line.
        $lines = file( $filter_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES );

        foreach ($lines as $line)
        {
            // split each line on comma, ignoring any empty words.
            foreach ( preg_split( "/,/", $line, null, PREG_SPLIT_NO_EMPTY ) as $word )
            {
                $word = trim($word);
                // add case-insensitive and unicode parameters to regex for word
                array_push($words, "/\b$word\b/iu");
            }
        }
        $lines = file( file_to_include_default_to_generic(SF_TEMPLATES_DIR . '/retailers', SF_STRING_FILTER_LIST), FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES );

        foreach ($lines as $line)
        {
            if ( '' === trim($line) ) continue;
            // treat these as not requiring word boundaries.
            array_push($words, "/\b$line\b/iu");
        }
        mb_internal_encoding('UTF-8');

        $content = preg_replace($words,'',$content);
        return trim($content);
    }
}

function is_valid_ip($ip, $allow_private_or_reserved = false)
{
    return $allow_private_or_reserved ?
        filter_var($ip, FILTER_VALIDATE_IP) !== false :
        ( filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false ) && ( !preg_match('/^127\./', $ip) );
}

function sf_get_location_from_request()
{

    // TODO: this should probably be stored in an LRU cache because I don't want to make yet another DB query on every single request.

    global $wpdb, $app;
    if (defined('SF_PRODUCTION')) {
        // Right. Cloudflare.  Makes life difficult
        // We check for CF-Connecting-IP first.
        if (array_key_exists('HTTP_CF_CONNECTING_IP', $_SERVER)) {
            $ip = trim($_SERVER['HTTP_CF_CONNECTING_IP']);
            if (is_valid_ip($ip)) {
                $req_ip = $ip;
            }
        // then we check for X_FORWARDED_FOR
        } else if (array_key_exists('HTTP_X_FORWARDED_FOR', $_SERVER)) {
            // then we check to see if *that* is a comma separated list
            if (strpos($_SERVER['HTTP_X_FORWARDED_FOR'], ',')) {
                $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                // we've gone through multiple proxies, but not cloudflare
                // take the left-most non-private IP
                foreach ($ips as $idx => $ip) {
                    $ip = trim($ip);
                    if (is_valid_ip($ip)) {
                        $req_ip = $ip;
                        break;
                    }
                }
            } else {
                $ip = trim($_SERVER['HTTP_X_FORWARDED_FOR']);
                if (is_valid_ip($ip)) {
                    $req_ip = $ip;
                }
            }
        } else {
            $ip = trim($_SERVER["REMOTE_ADDR"]);
            if (is_valid_ip($ip)) {
                $req_ip = $ip;
            }
        }
    }   else {
        $rnd = rand(5,32);
        //error_log('using random>'.$rnd);
        $req_ip = long2ip((float)floor(ip2long($_SERVER['REMOTE_ADDR'])/(0.06*$rnd))) ;
        //error_log('req_ip'."\n".$req_ip);
    }
    if (!$req_ip) {
        // If we have no IP address, return an empty object.
        array();
        $loc[0] = new StdClass();
        return $loc;
    }
    // we don't know if an IP always maps to the same location
    // TODO: if we use a local DB then we can store DB timestamp and store the IP in an ip_location_timestamp table
    /** @var \Salesfloor\Services\GeoIP $geoipService */
    $geoipService = $app['geoip'];
    $geoip = $geoipService->getCityLocation($req_ip);
    if (!isset($geoip['location']['latitude']) || !isset($geoip['location']['longitude'])) {
        // If we have no lat or long, return an empty object.
        $loc = [];
        $loc[0] = new StdClass();
        return $loc;
    }
    //if (!SF_PRODUCTION) error_log('geoip'."\n".print_r($geoip,true));
    // get the location if we've seen this IP before.
    $loc = $wpdb->get_results( $wpdb->prepare('SELECT id, latitude, longitude, country_code, region, city, postal_code, area_code FROM geoip_location WHERE ROUND(latitude,4) = %f AND ROUND(longitude,4) = %f', round($geoip['latitude'], 4), round($geoip['longitude'], 4) ));
    if (!isset($loc) || sizeof($loc) == 0) {
        $wpdb->insert('geoip_location',array(
                'country_code'  => $geoip['country']['iso_code'],
                'region'        => '', // Not available anymore
                'city'          => utf8_encode( $geoip['city']['names']['en'] ), // @TODO: Do we want to use another language than EN?
                'postal_code'   => $geoip['postal']['code'],
                'latitude'      => round($geoip['location']['latitude'], 4),
                'longitude'     => round($geoip['location']['longitude'], 4),
                'area_code'     => !empty($geoip['subdivisions'][0]['iso_code']) ? $geoip['subdivisions'][0]['iso_code'] : ''
            ), array(
                '%s','%s','%s','%s','%f','%f','%d'
            )
        );
        $loc = array();
        $loc[0] = new StdClass();
        $loc[0]->id             = $wpdb->insert_id;
        $loc[0]->country_code   = $geoip['country']['iso_code'];
        $loc[0]->region         = ''; // Not available anymore
        $loc[0]->city           = utf8_encode( $geoip['city']['names']['en'] );
        $loc[0]->postal_code    = $geoip['postal']['code'];
        $loc[0]->latitude       = $geoip['location']['latitude'];
        $loc[0]->longitude      = $geoip['location']['longitude'];
        $loc[0]->area_code      = !empty($geoip['subdivisions'][0]['iso_code']) ? $geoip['subdivisions'][0]['iso_code'] : '';

        //error_log('insert_query>'.$wpdb->last_query);
    }
    return $loc;
}

function sf_get_geoip_location_by_id($id) {
    if ( !isset($id) || !$id || !absint($id) )
        return;
    $id = absint($id);
    global $wpdb;
    $lat_long = $wpdb->get_results( $wpdb->prepare(
        "       SELECT latitude, longitude, country_code, region, city, postal_code, area_code
        FROM geoip_location
        WHERE id = %d"
        , $id
    ));
    if (isset($lat_long[0]))
        return $lat_long[0];
    else
        return;
}

function sf_time_ago($dateStr)
{
    global $app;
    $time = strtotime($dateStr);
    $util = new Salesfloor\Services\Util($app);
    return $util->timeAgo($time);
}

function sf_single_quote_string($a)
{
    if ( strlen($a) == 0 )
    {
        $a = "''";
    }
    else
    {
        if ( substr($a,0,1) != "'" )
            $a = "'" . $a;
        if ( substr($a,-1) != "'" )
            $a .= "'";
    }
    return $a;
}

/**
 * Function will call the appropriate managers to insert an $unattached_id,
 * also known as the customer_id on the browser, into the sf_unattached_customers table
 *
 * This is NOT associating a rep and the customer (unattached_id) yet. That is the function
 * of sf_claim_customer.
 * @param string $unattached_id Generated "customer_id" from customers browser
 * @param null $fingerprint Generated on customers browser (created from customer_id)
 */
function sf_insert_unattached($unattached_id, $fingerprint = null)
{
    global $app;

    // Get Existing unattached customer, Cache FALSE
    $unattachedCustomer = $app['unattached_customers.manager']->getOneOrNull(['id' => $unattached_id], null, false);

    if (empty($unattachedCustomer)) {
        // Source is not needed, though this is from a request, since it is not yet claimed
        $unattachedCustomer = $app['unattached_customers.manager']->create([
            'user_id'     => 0,
            'fingerprint' => $fingerprint,
            'source'      => UnattachedCustomer::SOURCE_REQUEST,
        ]);

        // $unattached_id (or customer_id) is inserted through preInsertId because
        // It maps to the ID column, which cannot be present to use the
        // INSERT functionality as this is a new row
        $unattachedCustomer->setPreInsertId($unattached_id);
        $app['unattached_customers.manager']->save($unattachedCustomer);
    } else {
        // We must change the source to request because we have a patch to not delete tracking cookie
        // when you call the stack widgets and you're not tracked yet (pending request)
        $unattachedCustomer->source = UnattachedCustomer::SOURCE_REQUEST;
        $app['unattached_customers.manager']->save($unattachedCustomer);
    }
}

/**
 * Function will call the appropriate managers to claim a customer ($unattachedId)
 * Since this is in WP, it will claim it with REQUEST as the source
 *
 * @param $repId SF Rep Id (in what format, who knows?!)
 * @param $unattachedId Browser generated identifier (customer_id) for session
 */
function sf_claim_customer($repId, $unattachedId) {
    global $app;

    $unattachedCustomer = $app['unattached_customers.manager']->associateCustomerAndRep($unattachedId, $repId, null, UnattachedCustomer::SOURCE_REQUEST);


    // Event logging is based on fingerprint, only log if present
    if (!empty($unattachedCustomer->fingerprint)) {
        $eventQueue = $app['sf.event.queue'];
        $eventQueue->push([
            'action' => 'SERVER_ASSOCIATE_CUSTOMER',
            'action_id' => $repId,
            'fingerprint' => $unattachedCustomer->fingerprint
        ]);
    }
}

/**
 * Retrieves the leads or the leads count for a particular user.
 *
 * @param $user_id The ID of the user we wish to retrieve leads for
 * @param null $type Lead filter
 * @param unknown $unused_cruft
 * @param int $start_id The Lead ID to start from (for pagination)
 * @param int $count The max number of records to return (For pagination)
 * @param bool $count_only Whether or not we want just the count - - Count only will remove the  limit
 *
 * @return array|bool|void An array if succesful, false on failure, void on error
 */
function sf_get_leads($user_id, $type = null, $unused_cruft = null, $start_id = 0, $count = 15, $count_only = false)
{
    global $wpdb_slave, $app;

    $user = get_user_by('id', $user_id);
    if (!$user)
        return false;

    $user = $user->data;

    if (!is_numeric($start_id)) {
        error_log("cannot get leads with non-numeric start_id");
        return false;
    }

    if (!intval($count)) {
        error_log('and they say that it is not numeric>' . $count);
        $count = 15;
    }

    $question_type = " AND 1=0\n";
    $appt_type = " AND 1=0\n";
    $product_type = " AND 1=0\n";

    if ($type) {
        // This comes from a drop-down, no multi-select
        switch ($type) {
            case 'question':
                $question_type = "AND 1=1";
                break;
            case 'product':
                $product_type = "AND 1=1";
                break;
            case 'appt_phone':
                $appt_type = "AND a.event_type='Phone'";
                break;
            case 'appt_chat':
                $appt_type = "AND a.event_type='Chat'";
                break;
            case 'appt_store':
                $appt_type = "AND a.event_type='In-Store'";
                break;
            case 'appt_video':
                $appt_type = "AND a.event_type='Video Conference'";
                break;

            default:
                $question_type = " AND 1=1";
                $appt_type = " AND 1=1";
                $product_type = " AND 1=1";
                break;
        }
    } else {
        $question_type = " AND 1=1";
        $appt_type = " AND 1=1";
        $product_type = " AND 1=1";
    }

    $params = array();
    $specialty_clause = '';
    $specialty_clause_q = '';
    $specialty_clause_p = '';
    $specialty_clause_a = '';
    if (RepSpecialties::shouldFilterNewLeadsBySpecialty()) {
        $specialties = RepSpecialties::getRepSpecialties($user_id);
        if ($app['configs']['retailer.short_name'] == 'nm') {
            // Home, Kids, and Gifts should be routed to all reps. See https://salesfloor.atlassian.net/browse/SF-9441
            $specialties = array_merge($specialties, ['8cf04a9734132302f96da8e113e80c', '32436b4b2a0f6ec1908cd44d5abb4b', '67204426ac9de2ade3533836fe933d']);
        }
        if ($specialties) {
            $specialty_clause_q = " AND (q.category_id IS NULL OR q.category_id IN(" . implode(",", array_fill(0, count($specialties), "%s")) . "))";
            $specialty_clause_p = " AND (p.category_id IS NULL OR p.category_id IN(" . implode(",", array_fill(0, count($specialties), "%s")) . "))";
            $specialty_clause_a = " AND (a.category_id IS NULL OR a.category_id IN(" . implode(",", array_fill(0, count($specialties), "%s")) . "))";
            $params = array_merge($params, $specialties, $specialties, $specialties);
        }
    }

    $outerSelection = '*';
    $limit = "LIMIT $count";

    if (true === $count_only) {
        $outerSelection = "count(*) as 'count'";
        $limit = null;
    }

    $subQueries = [
        "SELECT
            q.uniq_id request_id,
            'question' type, 'all' category,
            e.id event_id, e.date event_date,
            c.name customer_name,
            c.email customer_email,
            q.channel channel,
            q.phone customer_phone,
            q.chat_handoff chat_handoff,
            null appt_type, null appt_date, null appt_datetime, null appt_tz, q.question notes, null store_id,
            null store_name, null store_city, null store_region,
            null sub_category, null min, null max,
            gl.region, gl.city, gl.country_code, q.unattached_id, q.category_id, cat.name AS category_name,
            pc.name pre_customer_name,
            pc.email pre_customer_email,
            pc.phone pre_customer_phone
        FROM sf_questions q
        JOIN sf_events e ON e.uniq_id = q.uniq_id
        LEFT JOIN sf_customer c ON c.ID = e.customer_id
        LEFT JOIN sf_pre_customer pc ON pc.source = 'sf_questions' AND pc.source_id = q.ID
        LEFT OUTER JOIN geoip_location gl ON gl.id = q.loc_id
        LEFT JOIN sf_categories cat ON q.category_id = cat.category_id
        WHERE q.user_id = 0
        AND   e.id > $start_id
        AND q.store_id = $user->store
        $question_type
        $specialty_clause_q",
    ];

    if ($app['configs']['retailer.modular_connect.appointment_request.is_enabled']) {
        $subQueries[] =
            "SELECT
                a.uniq_id request_id,
                'appointment' type, 'all' category,
                e.id event_id, e.date event_date,
                c.name customer_name,
                c.email customer_email,
                a.channel channel,
                a.phone customer_phone,
                null chat_handoff,
                a.event_type appt_type, DATE_FORMAT( CONVERT_TZ( a.date,'UTC', a.timezone ), '%%b %%e at %%l:%%i %%p') appt_date, a.date appt_datetime,  a.timezone appt_tz, a.notes, a.store_id,
                s.name store_name, s.city store_city, s.region store_region,
                null sub_category, null min, null max,
                gl.region, gl.city, gl.country_code, a.unattached_id, a.category_id, cat.name AS category_name,
                pc.name pre_customer_name,
                pc.email pre_customer_email,
                pc.phone pre_customer_phone
            FROM sf_appointments a
            JOIN sf_events e ON e.uniq_id = a.uniq_id
            LEFT JOIN sf_customer c ON c.ID = e.customer_id
            LEFT JOIN sf_pre_customer pc ON pc.source = 'sf_appointments' AND pc.source_id = a.ID
            LEFT OUTER JOIN sf_store s ON s.store_id = a.store_id
            LEFT OUTER JOIN geoip_location gl ON gl.id = a.loc_id
            LEFT JOIN sf_categories cat ON a.category_id = cat.category_id
            WHERE a.user_id = 0
            AND   e.id > $start_id
            AND a.store_id = $user->store
            $appt_type
            $specialty_clause_a";
    }

    if ($app['configs']['retailer.has_personal_shopper']) {
        $subQueries[] =
            "SELECT
                p.uniq_id request_id,
                'product' type, p.category category,
                e.id event_id, e.date event_date,
                c.name customer_name,
                c.email customer_email,
                p.channel channel,
                p.phone customer_phone,
                null chat_handoff,
                null appt_type, null appt_date, null appt_datetime, null appt_tz, p.notes, null store_id,
                null store_name, null store_city, null store_region,
                p.sub_category, IF(p.min_budget=-1,null,p.min_budget) AS min, IF(p.max_budget=-1,null,p.max_budget) AS max,
                gl.region, gl.city, gl.country_code, p.unattached_id, p.category_id, cat.name AS category_name,
                pc.name pre_customer_name,
                pc.email pre_customer_email,
                pc.phone pre_customer_phone
            FROM sf_personal_shopper p
            JOIN sf_events e ON e.uniq_id = p.uniq_id
            LEFT JOIN sf_customer c ON c.ID = e.customer_id
            LEFT JOIN sf_pre_customer pc ON pc.source = 'sf_personal_shopper' AND pc.source_id = p.ID
            LEFT OUTER JOIN geoip_location gl ON gl.id = p.loc_id
            LEFT JOIN sf_categories cat ON p.category_id = cat.category_id
            WHERE p.user_id = 0
            AND   e.id > $start_id
            AND p.store_id = $user->store
            $product_type
            $specialty_clause_p";
    }

    $unionAll = implode(' UNION ALL ', $subQueries);
    $query = "SELECT $outerSelection FROM ($unionAll) events ORDER BY event_id DESC $limit";

    $query = $wpdb_slave->prepare($query, $params);
    $results = $wpdb_slave->get_results($query);

    if (is_array($results))
    {
        if (true === $count_only) {
            return $results;
        } else {
            return array_slice($results, 0, $count);
        }
    }
    else
    {
        error_log("can't get leads, maybe none?");
        error_log(print_r($query,true));
        error_log($wpdb_slave->last_query);
        error_log($wpdb_slave->last_error);
        return;
    }
}

/**
 * Fetch from the retailer the product and return an structure wit the information.
 *
 * TODO: OnBoarding, this need to be adapted for the retailer...
 *
 * @param $sku_list
 * @return array|mixed
 *
[stdClass Object
(
[Brand] => BestBuyCanada
[currentPage] => 1
[total] => 1
[totalPages] => 1
[pageSize] => 32
[products] => Array
(
[0] => stdClass Object
(
[sku] => 10288549
[name] => PlayStation Vita Borderlands 2 Bundle
[regularPrice] => 199.99
[salePrice] => 199.99
[shortDescription] => Pick up one of the most acclaimed games of the year in this PS Vita Borderlands 2 Bundle. Along with a downloadable copy of the game and 8GB memory card, receive a black WiFi-enabled handheld and six add-ons.
[productType] =>
[thumbnailImage] => /multimedia/Products/150x150/102/10288/10288549.jpg
[customerRating] => 4
[customerRatingCount] => 1
[customerReviewCount] => 0
[productUrl] => /en-CA/product/playstation-playstation-vita-borderlands-2-bundle-3000275/10288549.aspx?path=bf26ef101928fc087dd5e8b993e3d5caen02
[isAdvertised] =>
[isClearance] =>
[isInStoreOnly] =>
[isOnlineOnly] =>
[isPreorderable] =>
[isVisible] => 1
[hasPromotion] =>
[ehf] => 0.4
[currentRegion] => QC
[hideSavings] =>
[isPriceEndsLabel] =>
[priceUnit] =>
[saleEndDate] =>
)

)

[paths] => Array
(
)

[facets] =>
[LastSearchDate] => 2014-05-30T10:05:27
[relatedQueries] =>
[relatedCategories] =>
[selectedFacets] =>
[resources] =>
[redirectUrl] =>
[promotions] =>
)
]

 *
 */


/**
 * Retrieve a web page using curl and return the content.
 *
 * TODO: Can we limit the size that we will fetch to protect us?
 *
 * @param $url
 * @return mixed
 */

$curl_data_string = "";
$curl_data_size = 0;

// WESHOULD consider eliminating this obviously broken code
function sf_fetch_web_page($url)
{
    // Initialize curl
    $ch = curl_init();
    global $curl_data_string;
    global $curl_data_size;
    $curl_data_string = "";
    $curl_data_size = 0;

    // toys r us chokes without a real user agent
    // onboarding TODO:  decide if we do desktop/mobile/fake
    $userAgent = 'Mozilla/5.0 (Windows NT 5.1; rv:31.0) Gecko/20100101 Firefox/31.0';// 'curl';
    curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

    // Call the API to get the product.
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_AUTOREFERER, true);
    curl_setopt($ch, CURLINFO_HEADER_OUT, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_COOKIESESSION, true);
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, 'sf_curl_write_function');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    $output = curl_exec($ch);
    curl_close($ch);

    // $curl_data_string is set in the sf_curl_write_function
    return $curl_data_string;
}

// WESHOULD consider eliminating this obviously broken code
/*
 * Return the number of bytes actually taken care of. If that amount differs from the amount passed to your function,
 * it'll signal an error to the library. This will abort the transfer and return CURLE_WRITE_ERROR.
 */
function sf_curl_write_function($handle, $data) {
    global $curl_data_string, $curl_data_size;
    $curl_data_string .= $data;
    $curl_data_size += strlen($data);
    if ($curl_data_size > CURL_MAX_DATA_LENGTH) {
        return 0;
    }
    else
        return strlen($data);
}

/**
 * Returns the URL to the help section of the app.
 *
 * @return string URL
 */
function sf_bo_get_help_link_url() {
    global $app;
    return $app['configs']['content.help_link_url'];
}

/**
 * Returns an anchor linking to the Help section of the application.
 *
 * @param string $label The label to use in the anchor
 * @param array $attributes A list of attributes include with the anchor tag (An href attribute will be overwritten).
 * @return string The anchor tag
 */
function sf_bo_link_to_help($label = "Help Center", $attrs = null) {
    if (!is_array($attrs)) {
        $attrs = [];
    }
    $attrs['href']   = sf_bo_get_help_link_url();
    $attrs['target'] = '_blank';

    $attrstr = implode(" ", array_map(function ($key, $value) {
        $escValue = htmlspecialchars($value, ENT_COMPAT);
        return "$key=\"$escValue\"";
    }, array_keys($attrs), array_values($attrs)));

    return "<a $attrstr>$label</a>";
}

/*
 * @internal
 * Called by onboarding pages to record that the user actually did something in the step rather than skipping.
 * Also called by profile update functions to record that the user performed the skipped step later.
 */
function sf_record_onboarding_step($step) {
    global $app, $wpdb;
    $step = $step;
    $no_onboarding = false;
    $user_id = get_current_user_id();
    if (!$user_id)
        return;
    $steps = $wpdb->get_var( $wpdb->prepare( "SELECT steps_completed FROM sf_rep_onboarding WHERE wp_user_id = %d", $user_id ) );
    if ( null === $steps )
        $no_onboarding = true;
    else if ( !isset($steps) )
        $steps = 0;

    $new_steps = $steps | $step;
    if ($no_onboarding) {
        // this user was created via a method other than onboarding.
        // it shouldn't happen but we might craft some by hand for some reason.
        // We can ignore them for the purposes of getting them to *finish* onboarding.
    } else {
        if ($new_steps !== $steps)
            $update = $wpdb->update(
                'sf_rep_onboarding',                    //table
                array('steps_completed' => $new_steps), // new data
                array('wp_user_id'=>$user_id),          // where
                array( '%d'),                           // new data format
                array( '%d')                            // where format
            );
    }
    $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);
    $sfClient->insert("clear-nags-cache", []);
}

/*
 * This is a kludge that exists only because one physical store,
 * e.g. "West Edmonton", can have multiple brands (TRU and BRU), but
 * the sf_store table isn't setup that way, plus we have store
 * users, and the wp_users table doesn't even know about brands.
 * It's dirty and we should refactor to allow multiple brands
 * per store. Possibly even multiple brands per rep. And --I
 * know I'm asking for a lot here-- we should elminate store users
 * entirely, but that seems like a big one.
 */
function sf_get_store_user_id_from_store_user_name($username) {
    global $wpdb, $app;

    $brandCond = '';
    $params = array($username);
    if($app['configs']['retailer.brand'] !== NULL) {
        $brandCond = 'AND brand = %s';
        $params[] = $app['configs']['retailer.brand'];
    }

    return $wpdb->get_var($wpdb->prepare('SELECT u.id
        FROM wp_users u
        JOIN sf_store s ON u.store = s.store_id
        WHERE u.user_nicename = %s ' . $brandCond,
        $params));
}

/**
 * Find the user using the username and fallback with the second identity that they could have build when personalize their URL
 *
 * @param $username
 * @return bool|WP_User
 */
function sf_get_userinfo($username = null, $userid = null, $user = null)
{
    global $wpdb, $app;

    $username = trim($username);

    if (!empty($username)) {
        // SOOPA KLOODGE: multiple store users can have the same name
        // (i.e. nicename), so we bypass fetch-by-user-login and
        // do a quick kludgey lookup for the brand-aware store user first.
        // THIS SHOULD BE REFACTORED: WESHOULD allow multiple brands per store
        // (i.e. an sf_store_brand table), and multiple brands per rep
        // (i.e. an sf_rep_brand table).
        $user_id = sf_get_store_user_id_from_store_user_name($username);
        if(!$user_id) {

            $user_id = sf_get_store_user_id_from_store_user_name(str_replace('-', '', $username));

            // This is for multibrand (e.g: "fake-mall") .. otherwise will use get by user_login and fake-mall is not
            // necessarily the good brand. This will fix at least this store without breaking anything else (probably)
            if (!$user_id) {
                $user_id = sf_get_store_user_id_from_store_user_name(str_replace('-', ' ', $username));
            }

            if (!$user_id) {
                // 95% user case is login = rep, so avoid call to check alias if login matches
                $user = get_user_by('login', $username);
                if (empty($user)) {
                    $user_id = $wpdb->get_var($wpdb->prepare('SELECT id FROM wp_users WHERE user_alias = %s',$username));

                    if (empty($user_id)) {
                        // Try as a last resort in history via user_login
                        /** @var \Salesfloor\Models\Rep $rep */
                        $rep = $app['reps.manager']->getRemappedUserLogin($username);
                        if (!empty($rep)) {
                            // Keep the same structure using get_user_by()
                            $user_id = $rep->getId();
                        }
                    }
                }
            }
        }
        if(!$user) {
            $user = get_user_by('id',$user_id);
        }
    } else if ($userid) {
        $user = get_userdata($userid);
    } else if (! $user || ! isset($user->ID)) {
        return null;
    }

    // hell, why not. let's add store info to the user object
    // WESHOULD : remove or change this section since store i18n data is needed too
    if ($user) {
        $store = $wpdb->get_row($wpdb->prepare(
'SELECT s.store_id, s.name, latitude, longitude, country, region, city, store_user_id, brand, s.locale
FROM sf_store s
JOIN wp_users u ON (u.store = s.store_id)
WHERE u.ID = %d',$user->ID));
        if ($store && !is_wp_error($store)) {
            $storeLocales = $app['stores.manager']->getActiveLocales($user->store);
            if (!empty($storeLocales)) {
                $store->locales = $storeLocales;
            }

            $user->store_obj = $store;
        }
    }

    return $user;
}

function sf_get_rep_slug ($user_id,$get_store=true)
{
    global $app;

    $user = get_user_by('id',$user_id);
    if (!$user) {
        return;
    }
    $store_user = get_store_for_user($user);
    if ($store_user&&$get_store) {
        $user_id = $store_user->ID;
    }
    global $wpdb;
    $slug = $wpdb->get_var($wpdb->prepare('SELECT IF (user_alias IS null,user_login, user_alias) AS slug FROM wp_users WHERE ID = %d',$user_id));
    if (!$slug || is_wp_error($slug)) {
        return;
    } else {
        if ($app['configs']['retailer.brand'] && $app['configs']['retailer.brand'] != "") {
            $slug = str_replace("-" . $app['configs']['retailer.brand'], "", $slug);
        }
        return $slug;
    }
}

// This assumes that there is a store/rep with the exact same nicename
// in the other brand. Kloooooooodgey!
function sf_get_this_url_for_different_brand($different_brand) {
    global $app;

    if(empty($app['configs']['retailer.brand2host'])) {
        $newHost = $_SERVER['HTTP_HOST'];
    } else {
        $newHost = $app['configs']['retailer.brand2host'][$different_brand];
    }

    $slug = $_SERVER['REQUEST_URI'];
    if ($app['configs']['retailer.brand'] !== NULL) {
        $slug = str_replace("-" . $app['configs']['retailer.brand'], "", $slug);
    }

    return 'http' . ($_SERVER['HTTPS'] ? 's' : '') . '://' . $newHost . $slug;
}

class sf_repage_components
{
    /**
     * @var
     */
    protected $state;
    protected $user;

    // Internal variable to return element which were already fetch
    protected $introduction;
    protected $location;
    protected $products;
    protected $looks;
    protected $deals;
    protected $contents;
    protected $category_selection;

    protected $default_products = array();

    /**
     * Initialize class.
     *
     * @access public
     */
    public function __construct($user) {
        $this->user = $user;
        // Take default if no value exist...
        if (defined('RETAILER_DEFAULT_PRODUCT_ARRAY') && !empty(RETAILER_DEFAULT_PRODUCT_ARRAY)) {
            $prod_array = explode(',', RETAILER_DEFAULT_PRODUCT_ARRAY);
            foreach ($prod_array as $key => $sku) {
                $prod = new stdClass();
                $prod->product_sku = $sku;
                array_push($this->default_products, $prod);
            }
        }
    }

    public function rep_introduction()
    {
        // Return element if already defined
        if (! isset($this->introduction)) {
            $this->introduction = html_entity_decode(get_user_meta($this->user->ID, 'rep_introduction', true));
            $this->introduction = preg_replace('/\%0D\%0A/', '<br>', $this->introduction);

            // If the rep has no introduction, we get the default one
            if ($this->introduction == '') {
                $this->introduction = sf_get_default_rep_intro($this->user);
            }
        }

        return $this->introduction;
    }

    private function get_panel($table)
    {
        global $wpdb, $app, $keepPagination;

        $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);

        $user_id = $this->user->ID;

          $filters = [
            "kludge" => "relationship-aware",
            "scalar" => [],
            "rel" => [
              ["rep", $user_id],
              ["type", $this->user->type],
              ["specialties", (int) $app['configs']['retailer.specialties.is_enabled']],
            ],
          ];

          if ($app['configs']['retailer.brand']) {
            $filters['rel'][] = ["brand", $app['configs']['retailer.brand']];
          }
                $nbProducts = 1;
                $sort = null;
                switch($table) {
                case "sf_product":
                    $type = "reps/" . $user_id . "/products/top-picks";
                    $nbProducts = $app['configs']['retailer.num_top_picks'];
                    $typeCache = 'top-picks';
                    $filters = [];
                    break;
                case "sf_recommendations":
                    $type = "recommendations";
                    $nbProducts = $app['configs']['retailer.trending_recommendations.max'];
                    $filters = [
                        'user_id' => "$user_id",
                    ];
                    $sort = '-n_recommendations';
                    $typeCache = $type;
                    break;
                case "sf_deal":
                    if(defined('DEALS_PAGE')) {
                        $nbProducts = 16;
                    } else {
                        $nbProducts = $app['configs']['retailer.num_deals'];
                    }
                    if ($app['configs']['retailer.default_secondary_products'] == 'LATEST_ARRIVALS') {
                        $type = "reps/" . $user_id . "/products/new-arrivals";
                        $typeCache = 'new-arrivals';
                        $filters = [];
                    } else {
                        $type = "reps/" . $user_id . "/products/specials";
                        $typeCache = 'specials';
                        $filters = [];
                    }

                    break;
                default:
                    throw new \Exception("No panel at table \"$table\"", 500);
                }

            // SF-18611: we need the total number of products for trending recocommendations
            // that's why we need the pagination.
            // Note that all parameters of the query are importants and has to be the same as they are in storefront
            // if we don't want discrepency between BO and storefront
            $keepPagination = true;
            $fetched = $sfClient->select([], $type, $filters, 0, $nbProducts, $sort);
            $fetched = reset($fetched);

            // Set the number of products recommended
            if ($table == 'sf_recommendations') {
                $this->setNumberRecommendations(isset($fetched['pagination']['count']) ? $fetched['pagination']['count'] : 0);
            }

            // Unset the pagination to display product panels
            if (isset($fetched['pagination'])) {
                unset($fetched['pagination']);
            }

        return \Salesfloor\Services\Util::arrayToObject($fetched);
    }

    public function setNumberRecommendations($num)
    {
        $this->numberRecommendations = $num;
    }

    public function getNumberRecommendations()
    {
        return $this->numberRecommendations;
    }

    public function get_looks($n = null) {
        if (!isset($this->looks)) {
            global $app;

            $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);

        try {
            $fetched = $sfClient->select(null, 'reps/' . $this->user->ID . '/my-looks', [], 0, $app['configs']['retailer.num_looks']);
        } catch(\Exception $e) {
            $fetched = [];
        }

            $panel_structure = [];
            foreach($fetched as $look) {
                $obj = new StdClass();
                foreach($look as $k => $v) {
                    $obj->$k = $v;
                }
                $panel_structure[] = $obj;
            }

            $this->looks = $panel_structure;
        }

        if (isset($n)) {
            return array_slice($this->looks, 0, $n);
        }

        return $this->looks;
    }

    public function get_products($n = null) {
        // Return element if already defined
        if (!isset($this->products)) {
            $this->products = $this->get_panel("sf_product");
        }

                if(isset($n)) {
                    return array_slice($this->products, 0, $n);
                }

        return $this->products;
    }

    public function get_recommendations($n = null) {
        // Return element if already defined
        if (!isset($this->recommendations)) {
            $this->recommendations = $this->get_panel("sf_recommendations");
        }

        if(isset($n)) {
            return array_slice($this->recommendations, 0, $n);
        }

        return $this->recommendations;
    }

    public function get_deals($n = null) {
        // Return element if already defined
        if (!isset($this->deals)) {
            $this->deals = $this->get_panel("sf_deal");
        }

                if(isset($n)) {
                    return array_slice($this->deals, 0, $n);
                }

        return $this->deals;
    }

    public function getDynamicContent()
    {
        global $app;

        $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);

        return $sfClient->get('reps/' . $this->user->ID . '/storefront');
    }

    public function get_contents()
    {
        global $app;

        // Return element if already defined
        if (! isset($this->contents))
        {
            global $wpdb;

            if ($this->get_category_selection() !== "")
                $contents = array_merge(
                    $wpdb->get_results("SELECT * FROM sf_content where user_id = " . $this->user->ID . " and position > -1 order by position asc;"),
//                    $wpdb->get_results("SELECT * FROM sf_content where user_id = " . $this->user->ID . " and position = -1 order by date asc limit 4;"),
                    $wpdb->get_results("SELECT * FROM sf_content where user_id = 0 and department in (" . $this->get_category_selection() . ") and position > -1 order by mandatory desc limit " . RETAILER_NUM_POSTS . ";"),
                    $wpdb->get_results("SELECT * FROM sf_content where user_id = 0 order by position asc limit " . RETAILER_NUM_POSTS . ";"));
            else
                $contents = array_merge($wpdb->get_results("SELECT * FROM sf_content where user_id = " . $this->user->ID . " and position > -1 order by position asc;"),
//                    $wpdb->get_results("SELECT * FROM sf_content where user_id = " . $this->user->ID . " and position = -1 order by date asc limit 4;"),
                    $wpdb->get_results("SELECT * FROM sf_content where user_id = 0 and position > -1 order by position asc;"));

        }

        if (! isset($this->contents)) {
            $this->contents = array();
        }

        if (isset($contents)) {
            foreach ($contents as $content) {
                if ($content->url && $content->url != "0" && $content->url != "&feed=json") {
                    if(preg_match('/^https?:\/\//', $content->url)) {
                        // it doesn't matter whether this link goes to an external website (e.g. reddit.com)
                        // or if it's already a shoppage link; the shoppage can handle those cases.
                        $content->url = get_shoppage($this->user->user_login, $content->url);
                    }
                    $this->contents[$content->position] = $content;
                }
            }
        }
        //var_dump($contents);exit;
        // Fill with something is nothing else has been found.
        for ($i=0;$i<RETAILER_NUM_POSTS;$i++)
        {
            if (! isset($this->contents[$i]) || ($this->contents[$i]->type == 'page' && $this->contents[$i]->ID == 0) )
            {
                $this->contents[$i]= new stdClass(); // Create the object on the fly.
                $this->contents[$i]->position = $i + 1;
                $this->contents[$i]->post_id = $i;
                $this->contents[$i]->type = 'rss';
                if (defined('RETAILER_DEFAULT_FEED_URL') && RETAILER_DEFAULT_FEED_URL) {
                    $default_feeds = explode('|', RETAILER_DEFAULT_FEED_URL);
                    if (isset($default_feeds[$i])) {
                        switch ($default_feeds[$i]) {
                            default:
                                $this->contents[$i]->url = $default_feeds[$i];
                        }
                    } else {
                        $this->contents[$i]->url = $default_feeds[0];
                    }
                }
            }
        }

        // echo "\n<!--\n"; print_r($this->contents); echo "\n -->\n";

        return $this->contents;
    }

    /**
     * @return string
     */
    public function get_category_selection()
    {
        // Return element if already defined
        if (! isset($this->category_selection))
        {
            // Get the Category for this Rep
            global $wpdb;
            $sf_category_selection = $wpdb->get_results("SELECT * FROM sf_rep_product_selection_map where user_id = " . $this->user->ID . ";");

            $this->category_selection = "";

            foreach($sf_category_selection as $index => $item)
            {
                if ($this->category_selection == "")
                {
                    $this->category_selection = "'" . $item->term_id . "'";
                }
                else
                {
                    $this->category_selection .= "," . "'" . $item->term_id . "'";
                }
            }
        }

        return $this->category_selection;
    }

    /**
     * @return string
     */
    public function get_location()
    {
        // Return element if already defined
        if (! isset($this->location))
        {
            // Grab rep store location
            global $wpdb;
            $storeName = $wpdb->get_var("SELECT name FROM sf_store WHERE store_id IN (SELECT store from wp_users WHERE ID = " . $this->user->ID . ");");
            $storeCity = $wpdb->get_var("SELECT city FROM sf_store WHERE store_id IN (SELECT store from wp_users WHERE ID = " . $this->user->ID . ");");
            $storeRegion = $wpdb->get_var("SELECT region FROM sf_store WHERE store_id IN (SELECT store from wp_users WHERE ID = " . $this->user->ID . ");");
            $this->location = $storeCity == null ? $storeName : $storeCity . ', ' . $storeRegion;
        }

        return $this->location;
    }


}

function clear_storefront_cache () {
  global $app, $user;
  $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);
  $sfClient->insert("clear-storefront-cache", []);
  if ($app['configs']['retailer.trending_recommendations']) {
      clearTrendingRecommendationCache($user->ID);
  }
}

function clearTrendingRecommendationCache($userId)
{
    global $app;

    $sfClient = new Salesfloor\Services\SalesfloorAPIRepository($app['configs']);
    $sfClient->delete("recommendations/cache/$userId", []);
}

/*
 * Deny normal users access to the dashboard
 */

add_action( 'init', 'blockusers_init' );
function blockusers_init() {
    global $pagenow;
    if ( 'index.php' == $pagenow && is_admin() && ! current_user_can( 'administrator' ) && ! ( defined( 'DOING_AJAX' ) && DOING_AJAX ) ) {
        wp_redirect( '/backoffice' );
        exit;
    }
}

function sf_get_store_status($id = null) {
    $id = intval($id);
    if (!$id) {
        return 0;
    }
    global $wpdb;
    $res = $wpdb->get_var(
        $wpdb->prepare(
       "SELECT      s.user_status
        FROM        sf_session s
        JOIN        wp_users u ON (u.ID = s.user_id)
        JOIN        wp_users su ON (u.store = su.store)
        WHERE       su.ID = %d
        AND         last_access > now() - 180*60
        ORDER BY    user_status DESC, last_access DESC
        LIMIT       1",
        $id
    ));
    if (!$res || is_wp_error($res)) {
        return 0;
    } else {
        return $res;
    }

}

function sf_get_rep_status($id = null) {
    $id = intval($id);
    if (!$id) {
        return 0;
    }
    global $wpdb;
    $res = $wpdb->get_var(
        $wpdb->prepare(
       "SELECT      user_status
        FROM        sf_session
        WHERE       user_id = %d
        AND         last_access > now() - 180*60
        ORDER BY    user_status DESC, last_access DESC
        LIMIT       1",
        $id
    ));
    if (!$res || is_wp_error($res)) {
        return 0;
    } else {
        return $res;
    }

}

/**
 * Try to include the specified file otherwise replace the retailer with the generic keyword and try this file.
 *
 * @param $prefix
 * @param $retailer
 * @param $target
 */
function file_to_include_default_to_generic($prefix, $target)
{
    global $app;

    $pathfile = $prefix . '/' . RETAILER_NAME . '/' . $target;

    if (file_exists( $pathfile )) {
        return  $pathfile;
    } else {
        $pathfile = $prefix . DIRECTORY_SEPARATOR . "generic" . DIRECTORY_SEPARATOR . $target;
        $pathfile = preg_replace('/\//', '/', $pathfile); // Just to remove too much /
        if (file_exists( $pathfile)) {
            return $pathfile;
        }
        // WESHOULD cause a crash (500) so that we fix this shit instead of just complaining endlessly.
        error_log("file \"$target\" doesn't exist in \"$prefix\".");
    }
}

/**
 * Return the user structure matching the store that is associated to this Rep if the store has a store page.
 * Return NULL otherwise.
 *
 * @param $user
 * @return bool|null|WP_User
 */

// alias
function sf_get_store_for_user($user) {
    return get_store_for_user($user);
}

function get_store_for_user($user)
{
// Get the store user_id associated to this rep if exit;
    $store_user = null;
    global $app, $wpdb;

    if (isset($user->store_obj->store_user_id))
        return get_user_by( 'id', $user->store_obj->store_user_id );

    // Grap the user ID for that store if it exist
    if ($app['configs']['retailer.brand'] !== NULL) {
      $store_user_id = $wpdb->get_var(
        $wpdb->prepare("SELECT b.store_user_id FROM sf_store s JOIN sf_store b ON s.store_id = %d AND s.name = b.name AND b.brand = %s",
        $user->store, $app['configs']['retailer.brand']
      ));
    } else {
      $store_user_id = $wpdb->get_var(
        $wpdb->prepare("select store_user_id from sf_store where store_id=%d;",
        $user->store
      )
    );
    }

    if ($store_user_id != null)
    {
        $store_user = get_user_by( 'id', $store_user_id );

        if ($store_user->data->type == 'store') {
            return $store_user;
        }
    }

    return null;
}

function sf_get_store_object_for_user($user)
{
    if (!$user || ! isset($user->ID)) {
        return null;
    }
    global $wpdb;
    $store = $wpdb->get_results("select * from sf_store where store_id = (select store from wp_users where ID = $user->ID)");
    return $store;
}

function get_rep_onboarding_object_for_user($user)
{
    if (!$user || ! isset($user->ID)) {
        return null;
    }
    global $wpdb;
    $repOnboarding = $wpdb->get_row('select * from sf_rep_onboarding where wp_user_id = '.$user->ID);
    if (empty($repOnboarding)) {
        return null;
    }

    return $repOnboarding;
}

function sf_get_store_user_by_store_id($id) {
    $store_user = null;
    global $wpdb;
    $qry = $wpdb->prepare("select store_user_id from sf_store where store_id=%d;",$id);
    $store_user_id = $wpdb->get_var($qry);
    if ($store_user_id != null) {
        $store_user = get_user_by( 'id', $store_user_id );
        if ($store_user->data->type != 'store') {
            $store_user = null;
        }
    }
    return $store_user;
}

function sf_get_store_user_by_identifier($identifier) {
    $store_user = null;
    global $app;

    /** @var \Salesfloor\API\Managers\Stores $storeManager */
    $storeManager = $app['stores.manager'];
    try {
        /** @var \Salesfloor\Models\Store $store */
        $store = $storeManager->getOne(['sf_identifier' => $identifier], ['store_user_id']);
    } catch (\Exception $e) {
        return null;
    }

    $store_user_id = $store->store_user_id;
    if ($store_user_id != null) {
        $store_user = get_user_by( 'id', $store_user_id );
        if ($store_user->data->type != 'store') {
            $store_user = null;
        }
    }
    return $store_user;
}

function sf_get_store_display_name($store_id) {
    global $wpdb;
    $name = $wpdb->get_var($wpdb->prepare("select name from sf_store where store_id = %d",$store_id));
    return $name;
}

function sf_get_store_by_id($store_id) {
    global $wpdb;
    $name = $wpdb->get_row($wpdb->prepare("select * from sf_store where store_id = %d",$store_id));
    return $name;
}

function sf_get_rep_display_name($user)
{
    global $app;

    $first = trim($user->first_name);
    $last = trim($user->last_name);
    if ($first && !$last) {
        return $first;
    } elseif (!$first && $last) {
        return $last;
    }  elseif (!$first && !$last) {
        return "";
    }

    $templateKey = 'rep_display_name';
    if($app['configs']['retailer.storepage_mode'] && $user->type === 'rep') {
        $templateKey = 'team_mode_rep_display_name';
    }
    return $app['name_suggester']->formatName($templateKey, $app['locale'], $first, $last);
}

function sf_get_rep_storefront_display_name($user) {
    global $app;

    $first = trim($user->first_name);
    if($first && $app['configs']['retailer.short_name'] !== 'bru') {
        return $first;
    }/*else if($first && $app['configs']['retailer.short_name'] !== 'nm'){
        return $first;
    }*/

    return sf_get_rep_display_name($user);
}

function sf_get_next_mail_send_time($store_id) {
    $fmt = "Y-m-d H:i:s";
    global $wpdb;
    $store = $wpdb->get_results(
        "select store_id, store_user_id, timezone, hours, name
        from sf_store
        where store_id = $store_id"
    );

    if (!defined('SF_STORE_NOTIFICATION_QUIET_HOURS')) {
        $start_offset = 2;
        $end_offset = 0;
    } else {
        $quiet_times = json_decode(SF_STORE_NOTIFICATION_QUIET_HOURS);
        $start_offset = isset($quiet_times['START']) ? $quiet_times['START'] : $start;
        $end_offset = isset($quiet_times['END']) ? $quiet_times['END'] : $end;
    }

    // reset to default
    date_default_timezone_set('America/Montreal');
    // we may need to convert from this tz to store tz
    // we are less concerned with reps because customers
    // conceptualize appropriate reaction time from store
    if ( isset($store[0]->timezone)) {
        @date_default_timezone_set($store[0]->timezone);
    }
    $there = new DateTime();
    $tomorrow = new DateTime('tomorrow');
    // get the day of the week
    $dow = strtolower($there->format('l'));
    $tmw_dow = strtolower($tomorrow->format('l'));
    if ( isset($store[0]->hours) ) {
        $hours = json_decode($store[0]->hours);
        if ( isset($hours->$dow) && isset($hours->$tmw_dow) ) {
            $opening = explode(':', $hours->$dow->am);
            $closing = explode(':', $hours->$dow->pm);
            $tmw_opening = explode(':', $hours->$tmw_dow->am);
        } elseif ( isset($hours->$dow) ) {
            $opening = explode(':', $hours->$dow->am);
            $closing = explode(':', $hours->$dow->pm);
            $tmw_opening = array(8+$start_offset,0);
        } elseif ( isset($hours->$tmw_dow) ) {
            $opening = array(8+$start_offset,0);
            $closing = array(22+$end_offset,0);
            $tmw_opening = explode(':', $hours->$tmw_dow->am);
        } else {
            $opening = array(8+$start_offset,0);
            $closing = array(22+$end_offset,0);
            $tmw_opening = array(8+$start_offset,0);
        }

        for ($i=0; $i < 2; $i++) {
            $opening[$i] = intval($opening[$i]);
            $closing[$i] = intval($closing[$i]);
            $tmw_opening[$i] = intval($tmw_opening[$i]);
        }

        if ( 2 !== count($closing) || 2 !== count($opening) || 2 !== count($tmw_opening) ) {
            error_log('bad_format>'.print_r($bad_format,true));
            error_log('opening>'.print_r($opening,true));
            error_log('closing>'.print_r($closing,true));
            error_log('tmw_opening>'.print_r($tmw_opening,true));
            error_log("Store hours are not formatted correctly, not delaying send messages for store >" . print_r($store,true) );
            return;
        }
    } else {
        $opening = array(8+$start_offset,0);
        $closing = array(22+$end_offset,0);
        $tmw_opening = array(8+$start_offset,0);
    }

    $open = new DateTime();
    $open->setTime($opening[0],$opening[1],0);
    $close = new DateTime();
    $close->setTime($closing[0],$closing[1],0);
    $tmw_open = new DateTime('tomorrow');
    $tmw_open->setTime($tmw_opening[0],$tmw_opening[1],0);

    // send at is always UTC.  So switch back
    $utc = new DateTimeZone('UTC');
    if ( $there < $open ) {
        $open->setTimezone($utc);
        $send_at = $open->format($fmt);
    } elseif ( $close < $there ) {
        $tmw_open->setTimezone($utc);
        $send_at = $tmw_open->format($fmt);
    }

    return $send_at;
}

// Moved to: api/app/src/be/Managers/Reps.php:1576
function sf_get_reps_to_notify($store_id = null) {
    global $wpdb;
    // notifications default to true
    $rep_query = "
select  u.ID, u.user_email, fn.meta_value first_name, ln.meta_value last_name, if(em.meta_value = '1' or em.meta_value is null,1,0) notify
from    wp_users u
left outer join wp_usermeta em on ( em.user_id = u.id and em.meta_key = 'mail_notifications_salesfloor' )
join    wp_usermeta fn on ( fn.user_id = u.id and fn.meta_key = 'first_name' )
join    wp_usermeta ln on ( ln.user_id = u.id and ln.meta_key = 'last_name' ) and u.type = 'rep'
where u.user_status = 1 AND u.selling_mode = 1
";
    if ($store_id) {
        $rep_query .= " AND u.store = $store_id";
    }

    $rep_query .= " GROUP BY u.user_email";

    $reps = $wpdb->get_results($rep_query);

    return $reps;
}

function sf_get_rep_image($user = null) {
  global $app;

  return $app['cloudinary']->getAvatar(($user ? $user->user_login : null), $app['configs']['retailer.avatar_transform']);
}

function stripDoctype($template) {
    list($head, $tail) = preg_split("/<body[^>]*>/", $template, 2);
    if(!isset($tail)) {
        return $template;
    }
    $cutoff = strrpos($tail, '</body>');
    if($cutoff === false) {
        return $template;
    }

    return substr($tail, 0, $cutoff);
}

function sf_get_look($id)
{
    global $wpdb;

    $query = "
        SELECT *
        FROM sf_looks
        WHERE `id` = %d";

    $results = $wpdb->get_results($wpdb->prepare($query, $id));

    return (array) $results[0];
}

/**
 * TODO :WESHOULD: centralize this function and ~/instance-webserver/src/salesfloor/wp-content/themes/twentyfourteen-child/functions.php::sf_render_products,
 * Also move into \Salesfloor\Services\Mail\Templates\Includes\ProductEmailTemplate for future refactoring in the future
 */
function sf_render_products($data, $user, $tmpl = false, $origUser = null, $locale = null) {
    global $app;

    /** @var \Salesfloor\Services\Messaging\Email\MailProductHelper $mailProductHelper */
    $mailProductHelper = $app['service.messaging.mail_product_helper'];

    /** @var \Salesfloor\API\Managers\Products $productsManager */
    $productsManager = $app['product_variants.manager'];

    /** @var \Salesfloor\API\Managers\ProductVariants $productVariantManager */
    $productVariantManager = $app['product_variants.manager'];

    $email_object = new Salesfloor_Template($origUser ? $origUser : $user);
    $product_template = stripDoctype($email_object->render('product_row', $locale));
    $comment_template = stripDoctype($email_object->render('product_row_comment', $locale));

    $find = [
        '{SHOP_PAGE}',
        '%7BSHOP_PAGE%7D',
        '{PRODUCT_TITLE}',
        '{PRODUCT_IMAGE}',
        '%7BPRODUCT_IMAGE%7D',
        '{PRODUCT_DESCRIPTION}',
        '{COMMENT}',
        '{PRICE}',
        '{PRICE20}',
        '{PRICE30}',
        '{IMMU_DISCOUNT_START}',
        '{IMMU_DISCOUNT_END}',
        '{ORIGINAL_PRICE}',
        '{OLD_PRICE}',
        '{DESIGNER_NAME}',
        '{VARIANT_BRAND}',
        '{SHOW_PRODUCT_ON_SALE_START}',
        '{SHOW_PRODUCT_ON_SALE_END}',
        '{SHOW_PRODUCT_NORMAL_PRICE_START}',
        '{SHOW_PRODUCT_NORMAL_PRICE_END}',
        '{IS_EPICS_START}',
        '{IS_EPICS_END}',
        '{SHOW_PRODUCT_OLD_PRICE_START}',
        '{SHOW_PRODUCT_OLD_PRICE_END}'
    ];
    $products = "";
    foreach($data['objects'] as $object) {
        $product_data = [];
        if ($app['configs']['products.expanded_variants.enabled'] === true) {
            if(!empty($object['variantSku'])){
                $product_data = $mailProductHelper->getMailVariants([$object['variantSku']], $locale);
            }else{
                if (strpos($object['id'], 'epic_') === 0) {
                    $object['id'] = str_replace('epic_', '', $object['id']);
                }
                // Add extra check to make sure we don't load "first element" (bad legacy code) when filter is empty
                if (!empty($object['id'])) {
                    $defaultVariant = $productVariantManager->getOneOrNull(['product_id' => $object['id'], 'is_default' => 1]);
                    if (!empty($defaultVariant) && !empty($defaultVariant->sku)) {
                        $product_data = $mailProductHelper->getMailVariants([$defaultVariant->sku], $locale);
                    }
                }
            }
            $product_data = !empty($product_data) ? reset($product_data) : $product_data;
        } else {
            if (strpos($object['id'], 'epic_') === 0) {
                $id = str_replace('epic_', '', $object['id']);
                $product_data = sf_get_look($id);
            } else {
                $product_data = $mailProductHelper->getMailProduct([$object['id']], $locale);
                $product_data = !empty($product_data) ? reset($product_data) : $product_data;
            }
        }

        // For an unknown reason, the product information received is not in the DB anymore. Skip it.
        if (empty($product_data)) {
            $app['logger']->error(
                sprintf(
                    "During sf_render_products(), we skip a product because we can't find it in the DB [%s]",
                    json_encode($object)
                )
            );
            continue;
        }

        if (isset($product_data['productUrl'])) {
            $shoppage = $app['configs']['salesfloor_storefront.host'] . "/shop?rep=" . $user->user_login . "&sf_url=" . urlencode($product_data['productUrl']) . "&track_click=share&product_id=" . $product_data['sku'];
        } else {
            $shoppage = '#';
        }
        if (isset($product_data['regularPrice'])) {
            $price = $product_data['regularPrice'];
        } else {
            $price = '';
        }
        $price20 = 0;
        $price30 = 0;
        $commentStart = '<!--';
        $commentEnd = '-->';
        if (isset($product_data['discount']) && $product_data['discount']['customer'] > 0) {
            $price20 = $product_data['discount']['customer'];
            $price30 = $product_data['discount']['consultant'];
            $commentStart = '';
            $commentEnd = '';
        }

        $designerName = '';
        if(!empty($product_data['vanityData'])) {
            $vanityData = json_decode($product_data['vanityData'], true);
            if(!empty($vanityData['brand_name'])) {
                $designerName = trim($vanityData['brand_name']);
            }
        }
        if($designerName == '' && !empty($product_data['name2'])) {
            $designerName = $product_data['name2'];
        }
        if (isset($product_data['brand']) && !empty($product_data['brand'])) {
          // WESHOULD fix this at the source (at the json_encode in the product api)
          // but that would be too much of a change to test in the time we have now
            $designerName = str_replace("\\'", "'", $product_data['brand']);
        }

        if(isset($product_data['salePrice']) && $product_data['salePrice'] && $product_data['salePrice'] !== $product_data['regularPrice']) {
            $original_price = $price;
            $old_price      = $product_data['oldPrice'];
            $price          = $product_data['salePrice'];
        } else {
            $original_price = '';
            $old_price = '';
        }
        $product_image = isset($product_data['additionalMedia'][0]['url']) ? $product_data['additionalMedia'][0]['url'] : $product_data['picture'];
        if (strpos($product_image, 'cloudinary') === false) {
            $product_image = $app['cloudinary']->getProductThumbnail($product_image);
        }
        $replace = [
            $shoppage,
            $shoppage,
            isset($product_data['name']) ? htmlentities($product_data['name']) : $product_data['title'],
            $product_image,
            $product_image,
            isset($product_data['shortDescription']) ? $product_data['shortDescription'] : $product_data['description'],
            (isset($object['message']) ? htmlentities(PostFilter::filter($object['message'])) : ''),
            $app['service.util']->formatPrice($price, null, $locale),
            $app['service.util']->formatPrice($price20, null, $locale),
            $app['service.util']->formatPrice($price30, null, $locale),
            $commentStart,
            $commentEnd,
            $app['service.util']->formatPrice($original_price, null, $locale),
            $app['service.util']->formatPrice($old_price, null, $locale),
            $designerName,
            $designerName,
            !empty($original_price)?'': '<!--',
            !empty($original_price)?'': '-->',
            empty($original_price)?'': '<!--',
            empty($original_price)?'': '-->',
            !empty($price)?'':'<!--',
            !empty($price)?'':'-->',
            empty($original_price) || !empty($old_price)?'': '<!--',
            empty($original_price) || !empty($old_price)?'': '-->'
        ];
        $products .= str_replace($find, $replace, $product_template);
        if(isset($object['message']) && $object['message']) {
            $products .= str_replace('{COMMENT}', str_replace("\n", "<br>\n", htmlentities(PostFilter::filter($object['message']))), $comment_template);
        }
    }

    if($tmpl) {
        return $email_object->render($tmpl);
    }
    return $products;
}

function sf_get_fundamental_post($post) {
    $data = json_decode($post->post_content, true);
    if(is_null($data)) {
        return $post;
    }
    if($data['type'] != 'asset') {
        return $post;
    }
    $embedded_post = get_post($data['objects'][0]['id']);
    return sf_get_fundamental_post($embedded_post);
}

function sf_render_created_content($post, $user, $tmpl = false, $origUser = null) {
    $data = json_decode($post->post_content, true);
    if(null == $data) {
        // content is retailer-provided html; don't filter it.
        return $post->post_content;
    }
    if(0 == count($data['objects'])) {
        return '';
    }

    $type = $data['objects'][0]['type'];
    if('asset' == $type) {
        $fundamental_post = sf_get_fundamental_post($post);
        return sf_render_created_content($fundamental_post, $user, $tmpl, $origUser);
    } else if(0 == strpos($type, 'product')) {
        return sf_render_products($data, $user, $tmpl, $origUser);
    }
    return '';
}

function sf_render_content_creation_preview($post, $user)
{
    $data = json_decode($post->post_content, true);
    if(null == $data) {
        // content is retailer-provided html; don't filter it.
        return $post->post_content;
    }
    if(0 == count($data['objects'])) {
        $data['objects'][0] = array('type' => 'null');
    }

    $type = $data['objects'][0]['type'];
    $email_obj = new Salesfloor_Template($user);
    $email_obj->set_dictionary("TITLE", PostFilter::filter($data['title']));
    $email_obj->set_dictionary("TEXT", str_replace("\n", "<br>\n", PostFilter::filter($data['message'])));
    $email_obj->set_dictionary("USER", sf_get_rep_display_name($user));
    $tmpl = 'curation_mail';
    if('asset' == $type) {
        $tmpl = 'curation_asset_mail';

        $fundamental_post = sf_get_fundamental_post($post);

        $content = sf_render_created_content($fundamental_post, $user);
        $email_obj->set_dictionary("CONTENT", $content);
    } else if(0 === strpos($type, 'product')) {
        $products = sf_render_products($data, $user);
        $email_obj->set_dictionary("PRODUCT_ROWS", $products);
    } else {
        $email_obj->set_dictionary("PRODUCT_ROWS", '');
    }

    return $email_obj->render($tmpl);
}

function get_shoppage($repname, $url) {
    global $app;
    return 'http://' . $app['configs']['retailer.host'] . '/shop?rep=' . urlencode($repname) . '&sf_url=' . urlencode($url);
}

function sf_is_ambassador($user) {
    $stores = sf_get_store_object_for_user($user);
    if(!$stores) {
        return false;
    }
    $store = reset($stores);
    if(!$store) {
        return false;
    }

    return $store->shame_type == 'ambassador';
}

function sf_get_default_rep_intro($user) {
    global $app;
    $default_introduction = file_get_contents(file_to_include_default_to_generic(
        SF_TEMPLATES_DIR . '/retailers', 'introduction_template.txt'));

    return $default_introduction;
}

/**
 * @deprecated Use \Salesfloor\API\Managers\Reps::getPublicId instead
 */
function sf_get_rep_public_id($user, $strip_brand_suffix_if_store = false) {
    $ret = empty($user->user_alias) ? $user->user_login : $user->user_alias;

    if($strip_brand_suffix_if_store && $user->type === "store") {
        $ret = strip_brand_suffix($ret);
    }

    return $ret;
}

/**
 * @deprecated  Use \Salesfloor\API\Managers\Reps::stripBrandSuffix instead
 */
function strip_brand_suffix($str) {
    global $app;

    if($app['configs']['retailer.brand']) {
        $offset = -1 * (strlen($app['configs']['retailer.brand']) + 1);
        if(substr($str, $offset) === "-" . $app['configs']['retailer.brand']) {
            return substr($str, 0, $offset);
        }
    }

    return $str;
}

/**
 * @deprecated  Use \Salesfloor\API\Managers\Reps::getInstrumentedEmail instead
 */
function sf_get_email_address($user, $emailMode = null) {
    global $app;

    if($emailMode == SF_EMAIL_MODE_REP ||
        ($emailMode == SF_EMAIL_MODE_RETAILER &&
        strpos($user->user_email, $app['configs']['retailer.email_domain']) !== false)
    ) {
        return $user->user_email;
    }

    return sf_get_rep_public_id($user, true) . '@' . str_replace(['http://', 'https://'], '', $app['configs']['salesfloor_storefront.host']);
}

function sf_get_store_or_rep_email(WP_User $user, $emailMode = null) {
    if(RETAILER_STOREPAGE_MODE && $user->store) {
        if($storeUser = get_store_for_user($user)) {
            $user = $storeUser;
        }
    }

    return sf_get_email_address($user, $emailMode);
}

function store_key_2_store_name($store_key) {
    return implode(' ', array_map('ucfirst', explode('-', $store_key)));
}

function sf_get_store_name_for_chat($user)
{
    return sf_get_store_identifier_from_user($user);
}

// SF-14114: as services work with store user name and not id in team
// mode, this method allows to make a distinction between our store
// identifier on chat and on footer. We should probably move to only
// id but it requires more work to ensure that services is compatible
// to different versions. There is a separate ticket for this.
function sf_get_store_name_for_footer($user)
{
    return sf_get_store_identifier_from_user($user);
}

function sf_get_store_identifier_from_user($user)
{
    global $app;

    if($store = sf_get_store_object_for_user($user)) {
        $store = reset($store);
        return $store->sf_identifier;
    }

    return null;
}

function sf_get_js_rep_name($user) {
    $ret = $user->user_login;

    if($user->type === "store") {
        $ret = strip_brand_suffix($ret);
    }

    return $ret;
}

function is_email_recipient_allowed($user, $address, $verifyRepEligibility = false) {
    global $app;

    // don't send to our own domain
    list($unused_localpart, $domain) = explode('@', $address);
    if($domain == $app['configs']['retailer.host']) {
        return false;
    }
    if(is_array($app['configs']['retailer.brand2host']) && in_array($domain, $app['configs']['retailer.brand2host'])) {
        return false;
    }

    // SF-21808: Limit the email notifications sent to (selling-mode) users when 'Corp Email Required' config is False
    if ($verifyRepEligibility && !$app['reps.manager']->isEligibleForEmailNotification($user->ID)) {
        return false;
    }

    return true;
}

function sf_get_suggested_username($firstName, $lastName, $login, $template)
{
    global $app;
    global $wpdb;

    if($app['configs']['onboarding.choose_own_username']) {
        switch ($template) {
            case SF_USERNAME_TMPL_FI_LN:
                $lower = strtolower( substr($firstName,0,1) . $lastName);
                break;
            case SF_USERNAME_TMPL_FN_LI:
                $lower = strtolower($firstName . substr($lastName,0,1) );
                break;
            case SF_USERNAME_TMPL_FN_LN:
                $lower = strtolower($firstName . $lastName);
                break;
            case SF_USERNAME_TMPL_NONE:
                return '';
                break;
            default:
                $lower = strtolower($firstName . $lastName);
                break;
        }

        $lower = str_replace(' ', '', $lower);
    }
    else {
        $lower = $login;
    }

    if (empty($lower)) {
        return $lower;
    }

    // check to see if first_name + last_name is already taken as a wp username
    try {
        $taken = $wpdb->query($wpdb->prepare("
            SELECT
                id
            FROM
                wp_users
            WHERE
                user_login = '%s'
            OR  user_alias = '%s'
            UNION

            SELECT
            	`name`
            FROM
            	sf_store
            WHERE
            	name = '%s'
            ", $lower, $lower, $lower ));
    }
    catch (Exception $e) {
        var_dump($e->getMessage());
        exit;
    }

    if (!$taken) {
        $suggested = $lower;
    }
    else {

        // Generate an new alias
        try {
            $loginNameList = [];

            $query = function ($field) use ($wpdb, $lower) {
                return $wpdb->get_results("
                SELECT
                    $field as alias
                FROM
                    wp_users
                WHERE
                    user_login like '%".$lower."%' OR user_alias like '%".$lower."%'
                ");
            };

            $results = array_filter(array_merge(
                $query('user_alias'),
                $query('user_login'),
            ), function($item) {
                return $item->alias;
            });

            foreach ($results as $result) {
                $loginNameList[strtolower($result->alias)] = true;
            }

            $suggested = generateNewAlias($loginNameList, $lower);

        }
        catch (Exception $e) {
            var_dump($e->getMessage());
            exit;
        }
    }

    return $suggested;

}

function generateNewAlias($loginNameList, $originalAlias, $alias = null, $index = 0) {
    if (is_null($alias)) {
        $alias = $originalAlias;
    }

    if (isset($loginNameList[$alias])) {
        $index++;
        $alias = $originalAlias . $index;
        return generateNewAlias($loginNameList, $originalAlias, $alias, $index);
    } else {
       return $alias;
    }
}

function sf_get_alias_suggestion($user)
{
    global $app;
    if($app['configs']['onboarding.alias_match_username']) {
        return $user->user_login;
    }

    return sf_get_suggested_username($user->first_name, $user->last_name, $user->user_login, SF_ALIAS_TMPL);
}

function sf_update_alias($user_id, $alias)
{
    global $wpdb;
    $wpdb->update('wp_users', array('user_alias' => $alias), array('ID' => $user_id), array('%s'), array('%d'));
}

function sf_get_number_pending_mobile_items($user)
{
    global $salesStorePage;
    global $current_user;
    $current_user = $user;

    $messages = $salesStorePage->getMessagesApi([
        'filter' => [
            'type' => 'message',
            'category' => 'inbox',
            'status' => 'unread'
        ]
    ]);

    $storeRequests = $salesStorePage->getMessagesApi([
        'filter' => [
            'type' => 'storeRequest',
            'status' => 'unread'
        ]
    ]);

    $leads = sf_get_leads($current_user->ID, null, null, 0, 1000);
    return intval($messages['totalNumber']) + intval($storeRequests['totalNumber'] - 1) + count($leads);
}

function render_look_panel($params) {
    global $user, $app;

    $panel_name  = isset($params['panel_name'])  ? $params['panel_name']  : "";
    $panel_id    = isset($params['panel_id'])    ? $params['panel_id']    : "";
    $looks       = isset($params['looks'])       ? $params['looks']       : [];
    $offset      = isset($params['offset'])      ? $params['offset']      : 0;
    $classes     = isset($params['classes'])     ? $params['classes']     : "";
    $escape_html = isset($params['escape_html']) ? $params['escape_html'] : false;
    $mode        = isset($params['mode'])        ? $params['mode']        : "rep";

    include ABSPATH . 'r/includes/generic/look_panel.php';
}

function render_product_panel($params) {
  global $user, $app;

  $panel_name  = isset($params['panel_name'])  ? $params['panel_name']  : "";
  $panel_id    = isset($params['panel_id'])    ? $params['panel_id']    : "";
  $products    = isset($params['products'])    ? $params['products']    : [];
  $offset      = isset($params['offset'])      ? $params['offset']      : 0;
  $classes     = isset($params['classes'])     ? $params['classes']     : "";
  $escape_html = isset($params['escape_html']) ? $params['escape_html'] : false;
  $mode        = isset($params['mode'])        ? $params['mode']        : "rep";
  $template    = isset($params['template'])    ? $params['template']    : "product";
  $storeName   = isset($params['store_name'])  ? $params['store_name']  : null;
  $isTrending  = isset($params['isTrending'])  ? $params['isTrending']  : false;

  // @TODO-CONFIGS Clean up concept of $env
  $prerendererEnv = [
    'user_login'               => $user->user_login,
    'RETAILER_STOREPAGE_MODE'  => RETAILER_STOREPAGE_MODE,
    'RETAILER_NAME'            => RETAILER_NAME,
    'storefrontUrl'            => '',
    'firstName'                => sf_get_rep_storefront_display_name($user),
    'cloudinary'               => $app['cloudinary'],
    'thumbnail'                => $app['configs']['storefront.use_thumbnails'],
    'avatar_transform'         => $app['configs']['retailer.avatar_transform'],
    'storeName'                => $storeName,
    'retailer.url'             => $app['configs']['retailer.url'],
    'products.defaultcomments' => $app['products.defaultcomments'],
    'locale'                   => $app['locale'],
    'utilService'              => $app['service.util']
  ];

  $products = \Salesfloor\Services\Util::objectToArray($products);

  $prerenderedProducts = array();
  for ($i = 0; $i < count($products); ++$i) {
    $prerenderedProducts[] = Salesfloor\Models\ProductPrerenderer::getDataItem($products[$i], $i, $prerendererEnv, $panel_id);
  }

  include file_to_include_default_to_generic(ABSPATH . 'r/includes/', 'product_panel.php');
}

function getDefaultLocaleFromStoreId($id)
{
    global $wpdb;
    $locale = $wpdb->get_var($wpdb->prepare("SELECT locale FROM sf_store WHERE store_id = %d", $id));

    return $locale ?: null;
}

function getDefaultLocaleFromRepId($id)
{
    global $wpdb;
    $locale = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT locale FROM sf_store s
             INNER JOIN wp_users u ON u.store = s.store_id
             WHERE u.ID = %d",
            $id
        )
    );

    return $locale ?: null;
}

function mb_ucfirst($string, $forceRestToLowerase = false)
{
    $first = mb_strtoupper(mb_substr($string, 0, 1));
    $rest  = mb_substr($string, 1);

    if ($forceRestToLowerase) {
        $rest = mb_strtolower($rest);
    }

    return $first . $rest;
}

function getLocaleFromUserOrStore($user, $storeId = null)
{
    global $wpdb, $app;

    $locale = $wpdb->get_var(
        $wpdb->prepare(
            "
            SELECT 	COALESCE (u.locale, s.locale)
			FROM 	wp_users u
			LEFT JOIN sf_store s ON s.store_id = u.store
			WHERE 	u.ID = %d
			",
            $user->ID
        )
    );

    if (empty($locale) && !empty($storeId)) {
        $locale = $wpdb->get_var(
            $wpdb->prepare(
                "
            SELECT 	s.locale
			FROM 	sf_store s
			WHERE 	s.store_id = %d
			",
                $storeId
            )
        );
    }

    return !empty($locale) ? $locale : $app['configs']['retailer.i18n.default_locale'];
}

/**
 * During a request, find the channel to used based on information from POST
 *
 * @param $input            $_POST
 * @return null|string      email|text , the channel
 * @throws Exception        If invalid input
 */
function getChannelFromServiceRequest($input)
{
    $availableChannel = [
        'email',
        'text'
    ];

    $default = 'email';

    if (empty($input['contact_preference'])) {
        return $default;
    }

    $current = null;
    if ($input['contact_preference'] == 'sms') {
        if (!empty($input['sms'])) {
            $current = 'text';
        } else {
            throw new Exception("Field sms is empty");
        }
    } elseif ($input['contact_preference'] == 'email') {
        if (!empty($input['email'])) {
            $current = 'email';
        } else {
            throw new Exception("Field email is empty");
        }
    }

    // To be sure previous retailer that doesn't have the new code don't have "empty" as the channel
    if (empty($current)) {
        $current = $default;
    }

    if (in_array($current, $availableChannel)) {
        return $current;
    }

    // This should never happen since we have a default
    throw new Exception("Invalid input. Can't find desired channel");
}

// SF-21663 - Hotfix to block potential second order injection.
// https://blog.ripstech.com/2018/wordpress-file-delete-to-code-execution/
add_filter( 'wp_update_attachment_metadata', 'rips_unlink_tempfix' );

function rips_unlink_tempfix( $data ) {
    if( isset($data['thumb']) ) {
        $data['thumb'] = basename($data['thumb']);
    }

    return $data;
}
