<?php

if (isset($pageTitle) && $pageTitle !== '') {
    $pageTitle = htmlspecialchars($pageTitle);
} elseif (isset($userFullName) && trim($userFullName)) {
    $pageTitle = htmlspecialchars("$userFullName | Credo Beauty");
} else {
    $pageTitle = "Clean Beauty | Credo Beauty Natural Makeup & Organic Skincare Store";
}
$pageDescription = htmlspecialchars("Shop clean, nontoxic beauty & skin care products at Credo, and get free shipping over $50 + free samples with every purchase. Believe in better beauty.");
$ogImage = "https://cdn.salesfloor.net/salesfloor-assets/credobeauty/logo_2025.png";
$antiqueIECompatibilitySnippet = false;
$favicon = "https://cdn.salesfloor.net/salesfloor-assets/credobeauty/favicon_2023.ico";

require ABSPATH . 'r/includes/generic/common_head.php';
