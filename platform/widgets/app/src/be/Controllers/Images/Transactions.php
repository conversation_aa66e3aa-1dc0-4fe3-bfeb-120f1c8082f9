<?php

  /**
   * Transactions Controller
   *
   * Copyright 2014 - Salesfloor
   */

  namespace Salesfloor\Widgets\Controllers\Images;

  use Predis\Collection\Iterator\Keyspace;
  use Salesfloor\Models\Transaction;
  use Salesfloor\Services\Obfuscation\PIIObfuscation;
  use Salesfloor\Services\Security\Cryptography\GnuPrivacyGuard;
  use Silex\Application;
  use Salesfloor\Services\EventQueue;
  use Salesfloor\Services\Lang;

class Transactions extends \Salesfloor\Widgets\Controllers\Images
{
    const SLACK_SALES_CHANNEL = '#sales';
    /**
     * @inheritDoc
     */
    protected function processTransaction(Application $app, $userId, $parameters, $user = null)
    {
        $parameters['user_id'] = $userId;

        $token = $app['request']->cookies->get($app['configs']['cookie_prefix'] . 'sf_wdt_customer_token');
        if (isset($token)) {
            $customerInfo = unserialize($app['predis']->get($token));
            $parameters['customer_name'] = $customerInfo['name'];
            $parameters['customer_email'] = $customerInfo['email'];
        }

      // SF-15575 Track store_id in new transactions
        if (isset($user) && !empty($user->store) && empty($parameters['store_id'])) {
            $parameters['store_id'] = $user->store;
        }

        $parameters['ip'] = $app['geoip']->getIp($app['request']);
        $parameters['user_agent'] = $app['request']->headers->get('User-Agent');

        if (isset($parameters['temp_trx'])) {
            $key = $app['configs']['retailers.short'] . ':Transaction:' . $parameters['customer_id'];

            unset($parameters['temp_trx']);

            $app['predis']->set($key, serialize($parameters));

            return;
        }

        if (isset($parameters['verified_trx'])) {
            $key = $app['configs']['retailers.short'] . ':Transaction:' . $parameters['customer_id'];
            $trxId = $parameters['verified_trx'];

            $this->processTransactionItems($app, $userId, $user, $parameters['customer_id'], $trxId);

            $parameters = unserialize($app['predis']->get($key));
            $parameters['trx_id'] = $trxId;
        }

        if (isset($parameters['customer_id'])) {
            unset($parameters['customer_id']);
        }

        // In team mode, $user is usually null; we still want to track those sales
        $isDeactivatedUser = !empty($user) && $user->user_status == 0;

        // PP-271 - Don't record transactions for users that are deactivated
        if ($isDeactivatedUser) {
            $parameters['user_id'] = 0;
            $parameters['deactivated_user_id'] = $userId;
        }

        try {
          // Since getOne keeps hitting the cache, multiple duplicate transactions are getting saved
          // Ex: 1st Send --> Query trx_id 1 --> 0 Found --> Create trx_id 1
          // 2nd Send (Duplicate from JS) --> Query trx_id 1 (From Cache) --> 0 Found --> Create trx_id 1
          // Both local Repo Manager and API Repo Manager are told to not look in cache
            $fromCache = false;
            $transaction = $this->manager->getOne(
                [
                'id' => $parameters['trx_id'],
                'cache' => $fromCache          // Tell API Repository Manager to not look in cache
                ],
                null,
                $fromCache                       // Tell local Repository Manager to not look in cache
            );
        } catch (\Exception $e) {
            // The transaction will be skipped if missing transaction id.
            if (empty($parameters['trx_id'])) {
                $message = sprintf(
                    "Transaction is missing transaction id and is skipped with parameters [%s]",
                    print_r($parameters, true)
                );

                $app['logger']->critical($message);

                return;
            }

            foreach (['customer_name', 'customer_email'] as $k) {
                if (isset($parameters[$k])) {
                    $parameters[$k] = html_entity_decode($parameters[$k]);
                }
            }

            // when retailer send us the encrypted customer_name and customer_email, we'll decrypt it.
            // Plain text of data will be like: {"customer_name":"John Doe","customer_email":"<EMAIL>"}
            // Data will be encrypted by OpenPGP and then base64 encoded
            if ($app['configs']['security.pii.crypto.enabled']) {
                $this->decryptCustomerInfo($app, $parameters);
            }

            if (isset($parameters['customer_info'])) {
                unset($parameters['customer_info']);
            }

            //Here trx_type from the snippet will be like 'VariableSetting', 'callback-script', etc
            //They are not real transaction type, but means where the transactions are from, so
            //we'll map them to the origin column, and since they are all sales, we'll map their type as 'sale'
            $parameters['origin'] = $parameters['trx_type'];
            $parameters['trx_type'] = Transaction::TRX_TYPE_SALE;
            //since there's trx_thread_id column in tr
            if (empty($parameters['trx_thread_id'])) {
                $parameters['trx_thread_id'] = $parameters['trx_id'];
            }

            $transactionId = parent::processTransaction($app, $userId, $parameters);

            // PP-271 - Don't report a sale for inactive users.
            if ($isDeactivatedUser) {
                return;
            }

            // Uniquely identify the debugging info when multiple requests are sent
            $debuggerId = uniqid();

            $app['logger']->error('SF-transaction-follow-up-' . $debuggerId . ': Rep transaction has been created');

            if ($app['configs']['retailer.notification_on_sale']) {
                $this->sendNotification($app, $user, $parameters);
                $this->sendEmail($app, $user, $parameters, $debuggerId);
            }

            $app['logger']->error('SF-transaction-follow-up-' . $debuggerId . ': Rep transaction email and notification has been sent');

            // SF-16318 Track the transaction as part of the new event-queue
            $fingerprint = isset($parameters['fingerprint']) ? $parameters['fingerprint'] : null;
            $this->trackTransactionInEventQueue($app, $transactionId, $fingerprint);

            $app['logger']->error('SF-transaction-follow-up-' . $debuggerId . ': Rep transaction event has been recorded in event log table');

            // PP-8 Attempt to create customer and associate to transaction
            $this->trackTransactionInTransactionCustomerQueue($app, $transactionId);

            $app['logger']->error('SF-transaction-follow-up-' . $debuggerId . ': Rep transaction has been pushed to the transaction customer queue');
        }
    }

    /**
     * SF-16318 Track a transaction in the new event queue with a valid transaction
     * @param  Application $app           Current instance of Application
     * @param  integer     $transactionId The actual "id" value of the transaction row just created
     * @param  string      $fingerprint   Finger print
     * @return bool
     */
    private function trackTransactionInEventQueue(Application $app, $transactionId, $fingerprint = null)
    {
        // SF-20763: we use the fingerprint from the HTTP request (passed as an argument in this method)
        // and use the cookie as a fallback
        if (empty($fingerprint)) {
            $fingerprint = $app['request']->cookies->get($app['configs']['cookie_prefix'] . EventQueue::FINGERPRINT_COOKIE_POSTFIX);
        }

        if (!empty($fingerprint)) {
            $app['sf.event.queue']->push([
            'action' => 'SERVER_RECORD_TRANSACTION',
            'action_id' => $transactionId,
            'fingerprint' => $fingerprint
            ]);
        }
        return true;
    }

    /**
     * PP-8 Post-sale contact creation
     * Push a message containing the newly created $transactionId to the TransactionCustomer queue
     * When popped off the queue (by the ReadTransactionCustomerQueue cron), will attempt to associate
     * the given transaction to an existing customer, or create one to associate
     * @param Application $app
     * @param $transactionId
     * @return bool
     */
    private function trackTransactionInTransactionCustomerQueue(Application $app, $transactionId)
    {
        $app['service.data_processing.transaction_customer']->push(intval($transactionId));
        return true;
    }

    private function processTransactionItems($app, $userId, $user, $customerId, $trxId)
    {
        $keySearch = $app['configs']['retailers.short'] . ':TransactionItem:' . $customerId . ':*';

        $parameters = [];

        // Don't use keys in production, but scan instead
        foreach (new Keyspace($app['predis'], $keySearch) as $key) {
            $parameters[] = array_merge(unserialize($app['predis']->get($key)), ['trx_id' => $trxId]);
        };

        $app['transaction_items.controller']->processTransaction($app, $userId, $parameters, $user);
    }

    /**
     * Send an email
     *
     * @param Application $app An instance of an application
     * @param Object $user An instance of a user
     * @param array $parameters An array of parameters
     * @param string $debuggerId Uniquely identify the debugging info when multiple requests are sent
     */
    protected function sendEmail(Application $app, $user, $parameters, $debuggerId)
    {
        if (isset($user->user_email) && isset($user->ID)) {
            $locale = $this->multilangService->getLocaleFromUserOrStore($user->ID);
            $notAvailable = $app['translator']->trans('not_available', [], null, $locale);
            $customerName  = isset($parameters['customer_name']) ? $parameters['customer_name'] : $notAvailable;
            $customerEmail = isset($parameters['customer_email']) ? $parameters['customer_email'] : $notAvailable;
            $customerPhone = isset($parameters['customer_phone']) ? $parameters['customer_phone'] : $notAvailable;
            $transactionTotal = isset($parameters['trx_total']) && is_numeric($parameters['trx_total']) ?
                    number_format((float)$parameters['trx_total'], 2) : $parameters['trx_total'];

            // SF-21808: Limit the email notifications sent to (selling-mode) users when Corp Email config is No
            if (!$app['reps.manager']->isEligibleForEmailNotification($user->ID)) {
                $notificationService = $app['service.notification-system']();
                $notificationService->setTitle('title.sale');
                $notificationService->setBodyListInfo('transaction.id', $parameters['trx_id']);
                $notificationService->setBodyListInfo('transaction.total', $transactionTotal);
                $notificationService->setCustomerName($customerName);
                $notificationService->setCustomerEmail($customerEmail);
                $notificationService->setCustomerPhone($customerPhone);

                $app['messages.manager']->createNotificationSystemMessage(
                    $user->ID,
                    $notificationService->getTitle($locale),
                    $notificationService->getBodyList($locale)
                );
                return;
            }

            $templatePath = sprintf(
                'emails%snew_sale.html',
                $app['configs']['retailer.i18n.is_enabled'] ? "/$locale/" : '/'
            );

            $template = $app['twig']->render($templatePath, [
                'trx_id' => $parameters['trx_id'],
                'current_year' => date('Y'),
            ]);

            $isAppLoginPrimary = $app['configs']['retailer.backoffice_primary_login_through_app.enabled'];
            if ($isAppLoginPrimary) {
                $backofficeLink = $app['configs']['salesfloor_storefront.host'] . '/app';
            } else {
                $backofficeLink = $app['configs']['salesfloor_storefront.host'] . '/backoffice';
            }

            $replaceVars = [
                '{RETAILER_IDSTR}'  => $app['configs']['retailers.current'],
                '{BACKOFFICEPAGE}'  => $backofficeLink,
                '{USER_FIRST_NAME}' => isset($user->name) ? $user->name : $user->display_name,
                '{TOTAL}'           => $transactionTotal,
                '{CUSTOMER_NAME}'   => $customerName,
                '{CUSTOMER_EMAIL}'  => $this->getPIIData($app['service.obfuscation.pii'], $customerEmail, PIIObfuscation::OBFUSCATION_TYPE_EMAIL),
                '{CUSTOMER_PHONE}'  => $this->getPIIData($app['service.obfuscation.pii'], $customerPhone, PIIObfuscation::OBFUSCATION_TYPE_PHONE),
                '{RETAILER_NAME}'   => $app['configs']['retailers.name'],
                '{RETAILER_PRETTY_NAME}'    => $app['configs']['retailer.pretty_name'],
                '{UNSUBSCRIBE_LINK}'        => '[GLOBAL_UNSUBSCRIBE]',
            ];

            // TODO : Use the new templating system
            $template = str_replace(array_keys($replaceVars), array_values($replaceVars), $template);

            $message = [
                'email' => $user->user_email,
                'subject' => $app['translator']->trans('new_sale_subject', [], null, $locale),
                'sender_email' => $app['configs']['retailer.emails.no_reply_address'],
                'sender_name' => 'Salesfloor',
                'html_message' => $template
            ];

            // SF-16900 Add "mail_categories" to track Email Performance
            $message[$app['sf.mail.client']::PARAMS_KEY_CATEGORIES] = [$app['sf.mail.client']::CATEGORY_TRANSACTIONAL];

            // Bypass queue for now, try to figure out if it's impacting email not sent
            if ($app['configs']['retailer.transaction.use_mail_relay_queue']) {
                $app['sf.mail.client']->sendRelay($message);
            } else {
                $app['sf.mail.client']->sendRelayNow($message);
            }

            $app['logger']->error('SF-transaction-follow-up-' . $debuggerId . ': Email has been sent');

            // Log sale in slack
            $title = sprintf(
                "[%s] [%s] new sale from [%s]",
                $app['configs']['retailers.short'],
                $app['configs']['env'],
                $user->user_login
            );

            $message = sprintf(
                "User id [%s][%s] sale total [%s] on date [%s] GMT",
                $user->ID,
                $user->user_login,
                isset($parameters['trx_total']) ? $transactionTotal : '-',
                gmdate('Y-m-d H:i:s')
            );

            $app['service.slack']->sendMessageToChannel(self::SLACK_SALES_CHANNEL, $title, $message, null, ':moneybag:');

            $app['logger']->error('SF-transaction-follow-up-' . $debuggerId . ': Sales notification has been sent to Slack');
        }
    }

    /**
     * Send a push notification
     *
     * @param Application $app An instance of an application
     * @param Object $user An instance of a user
     * @param array $parameters An array of parameters
     */
    protected function sendNotification(Application $app, $user, $parameters)
    {
        $url = $app['configs']['salesfloor_storefront.addr'] . '/api/pushNotification/firebase/publish';
        $jsonString = json_encode(array(
        "reps" => array(
          $user->user_login => true
        ),
        // Most push notification code is in wordpress, along with the translation strings.
        // It is being left here to avoid having to maintain push notification strings in two places.
        "message" => 'pn_you_made_a_sale_with_customer',
        "inapp" => [],
        ));
        $ch = curl_init();
        $timeout = 0; // Set 0 for no timeout.
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonString);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($jsonString),
        'Host: ' . $app['configs']['salesfloor_storefront.host'],
        ]);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        $result = curl_exec($ch);
        curl_close($ch);
    }

    /**
     * @inheritDoc
     */
    protected function getParameters(Application $app)
    {
        $parameters = [
        'trx_id'          => $app['request']->get('trx_id'),
        'trx_date'        => gmdate("Y-m-d H:i:s"), // now, in UTC. We don't trust the client to pass in an honest date.
        'trx_apply_total' => static::sanitizeMoneyStr($app['request']->get('trx_total')),
        'trx_total'       => static::sanitizeMoneyStr($app['request']->get('trx_total')),
        'customer_name'   => $app['request']->get('customer_name'),
        'customer_email'  => $app['request']->get('customer_email'),
        'trx_type'        => $app['request']->get('type', 'script-callback'),
        'acquisition'     => $app['request']->get('acquisition', ''),
        'fingerprint'     => $app['request']->get('fingerprint', ''),
        ];

        if ($app['request']->get('customer_name', null)) {
            $parameters['customer_name'] = $app['request']->get('customer_name', null);
        }

        if ($app['request']->get('customer_email', null)) {
            $parameters['customer_email'] = $app['request']->get('customer_email', null);
        }

        if ($app['request']->get('customer_info', null)) {
            $parameters['customer_info'] = $app['request']->get('customer_info', null);
        }

        if ($app['request']->get('customer_id', null)) {
            $parameters['customer_id'] = $app['request']->get('customer_id', null);
        }

        if ($app['request']->get('temp_trx', null)) {
            $parameters['temp_trx'] = $app['request']->get('temp_trx', null);
        }

        if ($app['request']->get('verified_trx', null)) {
            $parameters['verified_trx'] = $app['request']->get('verified_trx', null);
        }

      // SF-15575 Get currency for new transactions, fallback to retailer default
        $currency = strtoupper($app['request']->get('currency', ''));
        if (!in_array($currency, $app['configs']['retailer.allowed_currencies'])) {
            $title = $app['configs']['retailers.short'] . ': bad currency for trx ' . $parameters['trx_id'];
          // Including $title in the $message since you cannot search by title in Slack
            $message = "$title | Currency is '$currency', expected one of " . implode(", ", $app['configs']['retailer.allowed_currencies']);
            $currency = $app['configs']['retailer.default_currency'];
            $message .= "\n Setting to default: $currency";
            $app['service.slack']->sendMessageToChannel(self::SLACK_SALES_CHANNEL, $title, $message, null, ':exclamation:');
        }
        $parameters['currency'] = $currency;

        return $parameters;
    }

    /**
     * Decrypt customer info such as customer name, customer email, etc, from the encrypted
     * customer_info parameter sent from the retailer.
     *
     * @param Application $app
     * @param array $parameters
     */
    private function decryptCustomerInfo(Application $app, &$parameters)
    {
        // We'll keep this for now when deploy this feature and remove it after Retailer send us only
        // customer_info parameter
        if (!isset($parameters['customer_info'])) {
            return;
        }

        /** @var GnuPrivacyGuard $gpg */
        $gpg = $app['security.crypto.gpg'];

        $decodeSuccess = false;
        $customer_name = '';
        $customer_email = '';
        if (!empty($parameters['customer_info'])) {
            try {
                $privateKey = $gpg->getPrivateKey('inbound', GnuPrivacyGuard::SPECIFICITY_PII_TRANSACTION);
                $customerInfo = $gpg->decrypt(base64_decode($parameters['customer_info']), $privateKey);

                if ($customerInfo) {
                    $customer = json_decode(trim($customerInfo), true);
                    if ($customer) {
                        $decodeSuccess  = true;
                        $customer_name  = $customer['customer_name'];
                        $customer_email = $customer['customer_email'];
                    }
                }
            } catch (\Exception $e) {
                $app['logger']->error('Rep transaction customer info decryption failed because: ' . $e->getMessage());
            }

            if (!$decodeSuccess) {
                $failureManager = $app['rep_transaction_decryption_failure.manager'];
                $model = $failureManager->create([
                    'trx_id' => $parameters['trx_id'],
                    'encrypted_customer_info' => $parameters['customer_info'],
                    'processed' => null
                ]);
                $failureManager->save($model);
            }
        }
        $parameters['customer_name'] = $customer_name;
        $parameters['customer_email'] = $customer_email;
    }

    /**
     * When email/phone is sent to the rep, we must obfuscate data if config is enabled.
     *
     * Would love to move this logic to a service but widgets stack is minimal atm.
     *
     * @param PIIObfuscation $obfuscationService
     * @param string $field
     * @param string $fieldType
     *
     * @return string|null
     * @throws \Salesfloor\Services\Obfuscation\ObfuscationException
     */
    private function getPIIData(PIIObfuscation $obfuscationService, string $field, string $fieldType)
    {
        if (!$this->configs['retailer.pii.obfuscate.is_enabled']) {
            return $field;
        }

        return $obfuscationService->obfuscate($fieldType, $field);
    }
}
