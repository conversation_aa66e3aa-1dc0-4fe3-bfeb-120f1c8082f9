/**
 * Salesfloor Main Widget code
 * @description We link all widgets together using postmessages
 * <AUTHOR>
 * @namespace sf_widget
 *
 */

(function() {
  var methods = {
    /**
     * Extend options, load plugins, load initial iframes test
     */
    init : function(){
      var self = this;
      // setup configs
      var configs = sf_widget.cb.objectKeys(sf_widget_configs);
      var lastConfig = configs[configs.length - 1];
      var retailerConfigs =  sf_widget_configs[lastConfig]; // get last property of sf_widget_configs;
      sf_widget.options = sf_widget.utils.deepExtend(defaults, retailerConfigs || {});

      // Variable for tracking whether the request for stand down was on this current page
      // Used to ensure that the variable to stand down again (safety measure) is not cleared until next page
      sf_widget.standDownOnCurrentPage = false;

      // Variable for tracking whether the widget(footer) forced in the sf_rep|store
      // GET parameter after page load by altering the history
      sf_widget.trackingUrlAddedAfterLoad = false;

      if (retailerConfigs.applyRetailerCustomJs && typeof retailerConfigs.applyRetailerCustomJs === 'function') {
        retailerConfigs.applyRetailerCustomJs();
      }

      this.triggerSidebarTrackingEvent();

      // Handle messaging between widget & mainscreen,
      // cb means crossbrowser, fixing ie9
      window[sf_widget.cb.addEventListener()](sf_widget.cb.message(), function(event){
        methods.handleWidgetsResponse(event);
      }, false);

      // When using crossdomain cookie, we pass cookies through a widget(iframe) on the right domain
      // this slow down initial showing speed
      if(!sf_widget.options.crossDomain){
        sf_widget.utils.dataStorage.init(sf_widget.utils.dataStorage.getInitialData());
      }

      this.loadWidgets({preload: true});


      if (!window.__is_new_widget) {
        sf_widget.loadedWidgets = 0;
        this.insertWidgetHead();
      } else {
          sf_widget.loadedWidgets = sf_widget.options.widgets.length;
      }

      if (sf_widget.options.crossDomain) {
        if (!window.__is_new_widget) {
          this.insertScript('cookie');
        }
      }
      this.queryRepStatus();
      // Once rep status (async) has been determined (when applicable), we init the widgets (self.onDomReady())
      sf_widget.utils.domReady(window, function () {
        var intervalId = setInterval(function () {
          if (window.sf_widget && sf_widget.queriedRepStatus) {
            clearInterval(intervalId);
            self.onDomReady(lastConfig);
          }
        }, 50);
      });
    },

    triggerSidebarTrackingEvent: function() {

      document.addEventListener('sidebar_mobile_displayed', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_MOBILE_VIEW');
      });

      document.addEventListener('sidebar_desktop_displayed', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_DESKTOP_VIEW');
      });

      document.addEventListener('sidebar_desktop_clicked', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_DESKTOP_CLICK');
      });

      document.addEventListener('sidebar_mobile_clicked', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_MOBILE_CLICK');
      });

      document.addEventListener('sidebar_mobile_minimize', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_MOBILE_MINIMIZE');
      });

      document.addEventListener('sidebar_mobile_tagline_minimize', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_MOBILE_TAGLINE_MINIMIZE');
      });

      document.addEventListener('sidebar_desktop_minimize', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_DESKTOP_MINIMIZE');
      });

      document.addEventListener('sidebar_mobile_maximize', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_MOBILE_MAXIMIZE');
      });

      document.addEventListener('sidebar_desktop_maximize', function () {
        sf_widget.utils.eventQueue.push('SIDEBAR_DESKTOP_MAXIMIZE');
      });
    },
    /**
     * SF-16431 SF-16750 Query for Rep user_status, set globals once answer is received OR deemed unnecessary (store context)
     */
    queryRepStatus : function() {
      // If rep detected in the URL, query the API for rep status
      var repDetected = sf_widget.utils.getQueryParam('sf_rep', sf_widget.options.writeHistoryType);
      if (repDetected) {
        sf_widget.utils.JSONP(
          sf_widget.options.salesfloor_api + "/public/reps/" + repDetected,
          {
            errorCallback : function() {
              sf_widget.queriedRepStatus = 1;
              sf_widget.repStatus = 0;
              return;
            }
          },
          function(data) {
            sf_widget.queriedRepStatus = 1;
            if (data.hasOwnProperty("error") || !data.user_login) {
               sf_widget.repStatus = 0;
               return;
            }
            // Assume OK repStatus (will also work for failed JSONP call)
            sf_widget.repStatus = 1;
            sf_widget.utils.dataStorage.set({name: 'sf_wdt_rep_status',
              value: JSON.stringify({
                'user_login': data.user_login,
                'employee_id': data.employee_id,
                'tracking_rep': repDetected,
                'store_id': data.store_data && data.store_data.store_id,
                'retailer_store_id': data.store_data && data.store_data.retailer_store_id
              })});
            if (data && data.ID && data.user_status === "0") {
              sf_widget.repStatus = 0;
            }
          });
      } else {
        // Rep is not present in the URL, mark everything as "OK"
        sf_widget.queriedRepStatus = 1;
        sf_widget.repStatus = 1;
      }
    },
    /**
     * Functions to be executed once DOM Ready has been detected
     */
    onDomReady : function (lastConfig) {
      sf_widget.ready = true;
      sf_widget.utils.dataStorage.processQueue();
      // Primarily looks at IE8, but custom rules are definable per config
      if(!methods.shouldEnableWidget()){
        return;
      }

      sf_widget.utils.dataStorage.loadDefaultSessions();
      this.setCookieUniqueId();

      // custom rules are definable per config
      if (methods.shouldInitWidget(lastConfig)) {
        this.initWidgets();
        // Retailer have sometimes widget they want us to replace
        if (sf_widget.options.removeOnShow) {
          sf_widget.utils.hideWebsiteNodes(sf_widget.options.removeOnShow);
        }
      }

      // SF-16318 Check for fingerprint cookie
      sf_widget.utils.eventQueue.checkFingerprint();
      sf_widget.utils.eventQueue.processAsyncEvents();

      // TODO: we should not registerContextualService in every page
      //       as a temporary solution, keep function call but disable the event inside
      // NOTE: remove this function call is risky because maybe retailer don't even have this function call on there page if they made mistake
      // SF-24466 Register available contextual service buttons
      sf_widget.utils.registerContextualService();


      // SF-18368 Remove tracking cookies
      sf_widget.utils.deleteTrackingCookiesIfNeeded();

      // Fetch Tracking Employee ID now that datastorage should be ready
      sf_widget.utils.getTrackingEmployeeId();
    },
    initWidgets : function(){
      // set cookie store when available
      sf_widget.utils.resetTracking();
      sf_widget.utils.setStore();
      sf_widget.utils.setRep(false);
      sf_widget.utils.setIp();

      sf_widget.utils.setWidgetRepresentant();
      // Execute functions before DOM ready
      this.loadWidgets();
    },
    setAcquisition : function(options){
      sf_widget.utils.dataStorage.setAcquisition(options.source);
    },
    setSalesCookie : function(){
      // used to track transaction
      sf_widget.utils.dataStorage.set({
        name: 'sf_wdt_tracking',
        value: "true",
        expires:sf_widget.options.saleCookieDuration
      });
    },
    /*
     * Always hide the sidebar on IE for Ann Taylor
     */
    annSidebarException: function () {
      if (sf_widget.options.retailer === 'ann' &&
          sf_widget.utils.dataStorage.get('sf_wdt_tracking') !== 'true' &&
          !sf_widget.utils.testFooterQueryString() &&
          sf_widget.utils.detectIE()
         ) {
        return true;
      }

      return false;
    },
    /**
     * Check if any widgets should be rendered at all (we do not render on ie8 for example)
     */
    shouldEnableWidget : function(retailer){

      if (this.annSidebarException()) {
        return false;
      }

      // our code is not tested on ie8 & we must execute only in ie9 plus
      if (/(MSIE\ [0-8]\.\d+)/.test(navigator.userAgent)) {
        return false;
      }

      if((navigator.platform.match(/(Win32|Win16|Win64|WinCE|Windows)/i)) && (navigator.userAgent.indexOf('Safari') !== -1 && navigator.userAgent.indexOf('Chrome') === -1)){ // disabled on safari windows
        return false;
      }

      // check if we are in private mode that cannot use cookies
      try {
        localStorage.setItem("2",2);
      } catch (exception) {
        return false;
      }

      return true;
    },
    /**
     * Check if any widgets should be rendered at all (we do not render on ie7 for example)
     */
    shouldInitWidget : function(retailer){
      var render = true;
      // config setup in retailer config
      if (window.hideWidgets === true) {
        return false;
      }

      if(sf_widget_configs[retailer].rules.shouldRenderWidgets){
        render = sf_widget_configs[retailer].rules.shouldRenderWidgets();
      }

      return render;
    },
    /**
     * Handle post message response from all widgets
     * @param {object} event Post message event, contain all actions sent from widgets
     */
    handleWidgetsResponse : function(event){
      var config = sf_widget_configs[sf_widget.options.retailer];

      // Also ignore any non-HTTPS request, as we can't trust those either.
      var getProtectRegEx = function (key) {
        return new RegExp('^https:\/\/' + key.replace('//', '').replace(/\./g, '\\.').replace(/\/+$/, ''), 'i');
      };

      var widgetsMatcher = getProtectRegEx(config.salesfloor_site);
      var storefrontMatcher = getProtectRegEx(config.salesfloor_storefront);
      var origin = event.origin.replace(/\/+$/, '');

      // If neither safe origin matches, ignore the postmessage.
      if (!origin.match(widgetsMatcher) && !origin.match(storefrontMatcher)) {
        return false;
      }

      var data;
      // must be careful we could get entangle with retailer messages
      if(event && event.data){
        try {
          data = JSON.parse(event.data);
        } catch (e) {
          return false;
        }
      }
      // automatically call a method under any loaded widget
      if(data && data.action){
        if (data.action === 'changeLocation') {
          var url = data.url;
          if (!/^https:\/\//.test(url)) {
            url = 'https:' + url;
          }
          if (!(widgetsMatcher.test(url) || storefrontMatcher.test(url))) {
            // If the URL is not from the same domain, we we do not do anything
            return false;
          }
        }

        if (data.action === 'dispatchSalesfloorEvent') {
          this.dispatchSalesfloorEvent(data);
        }

        if (data.action === 'dispatchSidebarTrackingEvent') {
          this.dispatchSidebarTrackingEvent(data);
        }
        if (data.action === 'dispatchSidebarMobileClickTrackingEvent') {
          this.dispatchSidebarMobileClickTrackingEvent(data);
        }

        if (data.widget && data.target !== 'template') {
          try{
            sf_widget.widgets[data.widget][data.action](data);
          } catch(e) {
            if(data.widget === "automat") {
              console.warn("Attempting to invoke " + data.action + " action on the legacy automat widget. If you're seeing this but weird issues in webchat, you need to deploy the new webchat version.");
            } else {
              console.error("Error attempting to invoke " + data.action + " action on the " + data.widget + " widget.", e);
            }
          }

        // Call utils functionnality
        } else if(data.utils){
          sf_widget.utils[data.utils][data.action](data);
        }

      // can call directly another iframe without going throught retailer widgets
      } else if (data && data.widgetId) {
        if (!document.getElementById(data.widgetId) || !document.getElementById(data.widgetId).contentWindow) { return; }
        document.getElementById(data.widgetId).contentWindow.postMessage(event.data, '*');
      }
    },
    /**
     * Dispatch an event to the main window document
     *
     * Contains an exception for IE
     */
    dispatchEvent: function (eventName) {
      var event;

      if (typeof(Event) === 'function') {
        event = new Event(eventName);
      } else {
        event = document.createEvent('Event');
        event.initEvent(eventName, true, true);
      }

      document.dispatchEvent(event);
    },
    /**
     * Dispatch an event on the main window document. This allows retailers to see these events and
     * execute code in the main context.
     *
     * Events must be whitelisted to be thrown.
     *
     * @param data
     */
    dispatchSalesfloorEvent: function (data) {
      if (!data.eventName) {
        return;
      }

      // Only allow whitelisted events.
      // Since these events are happening on the retailer's site: Salesfloor events should be prefixed where possible.
      var allowedEvents = ['sidebar_displayed', 'sidebar_clicked', 'store_resource_not_found', 'sf_footer_reloaded'];
      if (allowedEvents.indexOf(data.eventName) === -1) {
        return;
      }

      this.dispatchEvent(data.eventName);
    },

    /**
     * Dispatch sidebar tracking events
     *
     * Events must be whitelisted to be thrown.
     *
     * @param data
     */
    dispatchSidebarTrackingEvent: function (data) {
      if (!data.eventName) {
        return;
      }

      // Only allow whitelisted events.
      var allowedEvents = [
          'sidebar_mobile_displayed',
          'sidebar_desktop_displayed',
          'sidebar_desktop_clicked',
          'sidebar_mobile_clicked',
          'sidebar_mobile_minimize',
          'sidebar_mobile_tagline_minimize',
          'sidebar_desktop_minimize',
          'sidebar_mobile_maximize',
          'sidebar_desktop_maximize'
      ];
      if (allowedEvents.indexOf(data.eventName) === -1) {
        return;
      }

      this.dispatchEvent(data.eventName);
    },

    /**
     * Store sidebar mobile click event in the cookie
     *
     *
     * @param data
     */
    dispatchSidebarMobileClickTrackingEvent: function (data) {
      if (!data.eventName) {
        return;
      }

      sf_widget.utils.eventQueue.setAsyncEvents(data.eventName);

    },

    /**
     * Set unique id cookie so we can track customer over time
     */
    setCookieUniqueId : function (options) {
        var currentUniqueId;
        if (options && options.data_storage_method === 'cookie') {
          currentUniqueId = sf_widget.utils.dataStorage.fetch('sf_wdt_customer_id');
        } else {
          currentUniqueId = sf_widget.utils.dataStorage.get('sf_wdt_customer_id');
        }

        var radix = 36;
        if (!currentUniqueId) {
          currentUniqueId = Math.random().toString(radix).substr(2, 9);
        }

        sf_widget.utils.dataStorage.set({
          name:'sf_wdt_customer_id',
          value:currentUniqueId,
          expires: sf_widget.options.saleCookieDuration
        });

        // Ensure that fingerprint (calculated from customer_id) is set at the same time as customer_id
        var fingerprint = parseInt(currentUniqueId, radix);
        sf_widget.utils.dataStorage.set({
          name: sf_widget.utils.eventQueue.fingerprintKey,
          value: fingerprint,
          expires: sf_widget.options.saleCookieDuration
        });
        return fingerprint;
    },
    /**
     * Insert widget in head document
     */
    insertWidgetHead : function(){
      for(var i=0; i<sf_widget.options.widgets.length; i++) {
        this.insertScript(sf_widget.options.widgets[i]);
      }
    },
    /**
     * Insert script
     */
    insertScript : function(name) {
      var s = document.createElement("script");
      s.type = "text/javascript";
      s.src = sf_widget.options.salesfloor_site+"/js/widgets/widget."+name+".js";
      document.getElementsByTagName('head')[0].appendChild(s);
    },
    /**
     * Load widgets based on options provided with script tag put on retailer website, default with sidebar & footer
     * preload option execute functions before dom ready \
     * Example use: adding visibility hidden on some element in the retailer page
     */
   	loadWidgets : function(options) {
      var widget;
      // widgets are already loaded
      if (options && options.widget) {
        widget = sf_widget.widgets[options.widget];
        if(widget && widget.load){ widget.load(); }
        // widgets are not already loaded
      } else if(sf_widget.options.widgets && sf_widget.options.widgets.length > 0){
        for(var i=0; i<sf_widget.options.widgets.length; i++) {
          var name = sf_widget.options.widgets[i];
          widget = sf_widget.widgets[name];
          // PRELOAD
          if(options && options.preload){
            if(sf_widget.preload && sf_widget.preload[name]){ sf_widget.preload[name].init(); }
          // LOAD
          }else{
            if(widget && widget.load){ 
              widget.load();
            }
          }
        }
      }
    },
    reEvaluateWidgetRules : function() {
      var visibleWidgetList = [['sidebar', '#sf-widget-companion'], ['footer', '#sf-footer-companion']];
      for (var i = 0; i < visibleWidgetList.length; i++) {
        if (sf_widget.widgets[visibleWidgetList[i][0]]) {
          if (!window.sf_widget.rules.shouldEnable(visibleWidgetList[i][0], sf_widget.options.retailer)) {
            window.sf_widget.utils.hideWebsiteNodes(visibleWidgetList[i][1]);
          } else {
            window.sf_widget.utils.showWebsiteNodes(visibleWidgetList[i][1]);
          }
        }
      }
    }
  };
  /**
   * Make load plugins avaiable to widgets outside of sf_widget scope
   */
  sf_widget.loadPlugins = function () {
    methods.loadPlugins();
  };

  // Default widget options
  var defaults = {
    // sidebar widget
    widgetHeight: 147,
    widgetWidth: 300,
    sidebarMobileHeight: 33,
    sidebarMobileBottom: 0,
    sidebarMobileLeft: 0,
    sidebarClosedHeight: 33,

    sidebar2Height: 255,
    sidebar2Width: 186,
    sidebar2WidthMobile: 340,
    sidebar2WidthSmall: 304,

    widgetBottom: 0,
    widgetleft: 0,
    cookieSessionRefresh: 30000,
    cacheCookieDuration: 1800000, // 30 mins
    saleCookieDuration: 1209600000, // 14 Days
    fingerprintCookieDuration: 8640000000, // 100 Days
    crossDomain: false,
    removeOnShow: "", // querySelectorAll on nodes in the retailer website

    // footer widget
    footerHeight: 95,
    footerWidth: "100%",
    footerbottom: 0,
    forceFooter: false,
    footerleft: 0,
    footerSpacingElements: "body", // querySelectorAll on nodes in the retailer website
    footer: {
      media: {
        desktop: {
          position: {
            bottom: 0
          }
        },
        mobile: {
          position: {
            bottom: 0
          }
        }
      }
    },

    widgets: ['sidebar','footer'],

    pageSpacingBottom: "60px",
    pageSpacingType: "margin",
    // menu widget
    menuWidth: "100%",
    menuHeight: 30,
    // notification widget
    notificationWidth: 385,
    notificationHeight: 0,
    // overrided in the retailer website
    salesfloor_site: "http://elguntors.widgets.dev.salesfloor.net/",
    salesfloor_api: "http://elguntors.api.dev.salesfloor.net/",
    salesfloor_storefront: "http://elguntors.dev.salesfloor.net",
    keyPrefix: "dev_elguntors_",

    // SF-17891 Since a retailer can serve HTTP pages (e.g.: products) and HTTPS pages (e.g.: checkout)
    //          at same time, we might need to force cookies as non-secure in order to allow expiration
    //          refreshing while customers are browsing through the HTTP pages.
    secureOnlyCookies: true,

    data_storage_method: "cookie",
    mode: 'rep',

    // method of tracking sales: appendImage or fetchApi
    sales_tracking_method: 'appendImage',

    // method of reading cookies from objects or fetch cookie(raw string parsed to key/value)
    cookie_read_method: 'get',

    logoReplaceSelector: ".hdrLogo > a",

    retailerCookie: ""
  };

  // Init all the things
  window.sf_widget = window.sf_widget || {};
  window.sf_widget.widgets = window.sf_widget.widgets || {};
  window.sf_widget.widgets.base = methods;
  // SF-19008 - Prevent the sidebar from breaking if
  // widget.js is called twice from the retailer's ecommerce
  if (window.sf_widget.loadedWidgets > 0) {
    return;
  }

  methods.init();
}());
