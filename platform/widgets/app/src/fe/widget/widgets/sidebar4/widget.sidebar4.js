// This is the parent frame.
// It does NOT have $.ajax
// This is the widget code running on the parent frame of the automat widget.
// To get a summary of how events will be passed in from Webchat, see the webchat source
// at `types/SalesfloorFrameApi.ts`
(function () {
  var connect2WidgetId = "sf-connect2-companion";
  var POST_MESSAGE_SOURCE = "salesfloorPlatform";
  window.sf_widget = window.sf_widget || {};
  window.sf_widget.widgets = window.sf_widget.widgets || {};

  sf_widget.widgets.sidebar4 = {
    widgetId: connect2WidgetId,
    isOpen: false,

    load: function () {
      if (
        !(
          sf_widget.rules.shouldEnable("footer", sf_widget.options.retailer) ||
          sf_widget.rules.shouldEnable("sidebar", sf_widget.options.retailer)
        )
      ) {
        return;
      }
      if (this.checkDisabled()) {
        return;
      }
      this.footerStateCheck();
      this.addIntegrationIdEventListener();
      if (sf_widget.utils.dataStorage.fetch("sf_wdt_sidebar_store")) {
        sf_widget.sf_store_widget = sf_widget.utils.dataStorage.fetch(
          "sf_wdt_sidebar_store"
        );
      }
      if (!sf_widget.sf_store_widget) {
        this.getStoreData(window.connect2StorefrontStoreId);
      } else {
        this.append();
      }
    },
    getStoreData: function (storeId) {
      var self = this;
      var data = {};
      if (storeId) {
        data["filter[store]"] = storeId;
        data["include-has-sms"] = true;
      } else {
        data["filter[ip]"] = sf_widget.utils.getIp() || "current";
        data["filter[locale]"] = sf_widget.utils.getLocale() || "";
        data["include-has-sms"] = true;
      }

      sf_widget.utils.JSONP(
        sf_widget.options.salesfloor_api + "/stores",
        data,
        function (data) {
          if (data && data[0]) {
            self.setCurrentStore(data[0].sf_identifier);
            self.setCurrentLocale(data[0].locale);

            self.append();
          }
        }
      );
    },
    append: function () {
      var widgetMode = window.sf_sidebar_widget_mode || "popup";
      var innerWidth = window.innerWidth;
      var innerHeight = window.innerHeight;
      var templateParams = {
        widgetMode: widgetMode,
        innerWidth: innerWidth,
        innerHeight: innerHeight,
        parentPageTitle: encodeURIComponent(document.title),
        parentPageUrl: encodeURIComponent(document.location.href),
      };

      if(sf_widget.options.sidebarExternalElementAdjustments){
        sf_widget.utils.adjustExternalElements(sf_widget.options.sidebarExternalElementAdjustments);
      }

      sf_widget.utils.appendWidget({
        css: null,
        widget: "sidebar4",
        content: sf_widget.widgets.sidebar4.content.getTemplate(templateParams),
        iframeId: connect2WidgetId,
        container: "body",
        sf_store_widget: sf_widget.sf_store_widget,
      });
    },
    afterRender: function () {
      var sessionToken = this.getFirebaseSessionToken();
      if (sessionToken) {
        var chatRoom = this.getLatestChatRoom();
        if (chatRoom) {
          this.sendSessionTokenToIframe(sessionToken);
        } else {
          this.removeFirebaseSessionToken();
        }      
      }
      var frame = document.getElementById(connect2WidgetId);
      setTimeout(function () {
        frame.dataset.ready = true;
        console.debug("SF sidebar4 is ready.");
      }, 600); // adding some delay to make sure the iframe is ready
      this.addWindowResizeEventListener();
      this.addScrollEvent();
      this.addBeforeUnloadEventListener();
    },
    throttle: function (func, wait) {
      var time = Date.now();
      var timer;
      return function () {
        var self = this;
        var args = arguments;
        if (time + wait - Date.now() < 0) {
          func.apply(self, args);
          time = Date.now();
        } else if (!timer) {
          timer = setTimeout(function () {
            func.apply(self, args);
            time = Date.now();
            timer = null;
          }, wait - (Date.now() - time));
        }
      };
    },
    debounce: function (func, wait) {
      var timer;
      return function (event) {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(func, wait, event);
      };
    },
    addWindowResizeEventListener: function () {
      var self = this;
      window.addEventListener(
        "resize",
        function() {
          if (sf_sidebar_widget_mode === "fullpage") {
            document.getElementById(connect2WidgetId).style = "border:0;width:0;height:0;z-index:100000;max-width:unset;width:" + window.innerWidth + "px;height:" + window.innerHeight + "px;position:fixed;bottom:0;right:unset;left:0;top:unset;";
          } else {
            return self.debounce(function () {
              self.sendPostMessageToTemplate({
                action: "onWindowResize",
                source: POST_MESSAGE_SOURCE,
                payload: {
                  width: window.innerWidth,
                  height: window.innerHeight,
                },
              });
            }, 300);
        }
        }
      );
    },
    addScrollEvent: function () {
      var self = this;
      window.addEventListener(
        "scroll",
        self.throttle(function () {
          var mediaType = sf_widget.widgets.sidebar.getMobileData().media;
          var scrollThreshold = (sf_widget.options.webchatLureScrollToMinimizeThreshold && 
            sf_widget.options.webchatLureScrollToMinimizeThreshold[mediaType]) || 0.5;
          var status = self.calculateOnScrollProgress() > scrollThreshold ? "closed" : "open";
          self.sendPostMessageToTemplate({
            action: "OnUserScroll",
            source: POST_MESSAGE_SOURCE,
            payload: {
              status: status,
            },
          });
        }, 250)
      );
    },
    /**
     * This function is invoked to set an event listener for the 'beforeunload' event.
     */
    addBeforeUnloadEventListener: function () {
      var self = this;
      if (window.sf_sidebar_widget_mode === "fullpage") {
        window.addEventListener("beforeunload", function (event) {
          event.preventDefault();
          event.returnValue = "";
          self.sendPostMessageToTemplate({
            action: "BeforeUnload",
            source: POST_MESSAGE_SOURCE,
            payload: {
              startUnload: true
            },
          });
        });
      }
    },
    scrollData: function () {
      var body = window.document.body;
      var html = window.document.documentElement;
      var height = Math.max(
        body.scrollHeight,
        body.offsetHeight,
        html.clientHeight,
        html.scrollHeight,
        html.offsetHeight
      );

      return {
        windowHeight: window.innerHeight,
        scrollPosition:
          window.pageYOffset || html.scrollTop || body.scrollTop || 0,
        documentScrollHeight: height,
      };
    },
    calculateOnScrollProgress: function () {
      var scrollData = this.scrollData();
      var percent =
        (scrollData.scrollPosition /
          (scrollData.documentScrollHeight - scrollData.windowHeight)) *
        100;

      return percent;
    },
    setLurePosition: function (data) {
      if (data.payload) {
        var position = data.payload.position || "left";
        var c2Container = document.getElementById(
          "sf-widget-companion-wrapper"
        );
        var c2Iframe = document.getElementById(connect2WidgetId);

        if (c2Container && c2Iframe) {
          var prevPos = position === "left" ? "right" : "left";
          c2Container.style.removeProperty(prevPos);
          c2Iframe.style.removeProperty(prevPos);
          c2Container.style.setProperty(position, "0");
          c2Iframe.style.setProperty(position, "0");
        }
      }

      this.acknowledge(data);
    },

    /**
     * This method is invoked to re-initialized dataStorage with a fresh storage data copy from iframe.
     * @param {*} data
     */
    refreshPlatformDataStorage: function (data) {
      if (data.payload) {
        this.reinitializeDataStorage(data.payload.data);
      }

      this.acknowledge(data);
    },

    /**
     * This function is invoked to filter out the cookies that doesn't belong to the current environment.
     * For example, if you are on dev. Then cookies started with qa04 prefix should be filtered out.
     * Also, cookies for different retailers should be ignored.
     * @param {*} cookies
     * @returns
     */
    filterCookiesByEnvironment: function (cookies) {
      var cookiesArray = cookies.split(';');
      var filteredCookies = [];

      for (var i = 0; i < cookiesArray.length; i++) {
        var cookie = cookiesArray[i].trim();
        if (cookie.indexOf(sf_widget.options.keyPrefix + "sf_wdt") === 0 || cookie.indexOf("sf_wdt") === -1) {
          filteredCookies.push(cookie);
        }
      }

      return filteredCookies.join('; ');
    },

    /**
     *
     * @param {*} data
     */
    reinitializeDataStorage: function (data) {
      if (data) {
        // TODO: add scenario for local storage
        var cookies = this.filterCookiesByEnvironment(document.cookie + '; ' + data);
        sf_widget.utils.dataStorage.init(cookies);
      }
    },

    /**
     * This method is invoked to create a new firebase session.
     * Note that Webchat is the one that actually sends the request to Typhoon to create a new session.
     * Platform here is to send a message to ask Webchat to perform the action.
     *
     * @param { string } entrypointId The entrypoint Id you want to fire once the session is created.
     * @param { Object } conversationData - Params for the contextual widget.
     * @param { string | null } conversationData.specialtyId
     * @param { string | null } conversationData.retailerStoreId
     */
    createFirebaseChatSession: function (entrypointId, conversationData) {
      this.sendPostMessageToTemplate({
        action: "createSession",
        source: POST_MESSAGE_SOURCE,
        payload: {
          entrypointId:
            entrypointId || sf_widget.options.webchat.defaultConvEntrypoint,
          specialtyId: conversationData.specialtyId || undefined,
          retailerStoreId: conversationData.retailerStoreId || undefined,
        },
      });
    },

    /**
     * This function is invoked to clear the firebase session token from data storage. 
     * Chat room and conversation will also be removed as well.
     */
    removeFirebaseSessionToken: function (data) {
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_session_token",
        value: "",
        expires: "remove",
      });

      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_room",
        value: "",
        expires: "remove",
      });

      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_conversation",
        value: "",
        expires: "remove",
      });

      if (data) {
        this.acknowledge(data);
      }
    },

    /**
     * This function is invoked to save the firebase session token to data storage.
     *
     * @param { Object } session The session token.
     */
    setFirebaseSessionToken: function (session) {
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_session_token",
        value: JSON.stringify(session),
        expires: new Date(session.expiryTime).valueOf() - Date.now(),
      });
    },

    /**
     * This function is invoked to get the existing firebase session from data storage (cookie / local storage).
     *
     * @returns { Object | null } The existing Firebase session object (JWT token) if found, otherwise null.
     * The session object has the following fields:
     * - accessToken (string): The access token associated with the session.
     * - expiryTime (Date): The expiry time of the session.
     * - sessionName (string): The name of the session.
     * - firebaseUrl (string): The URL of the Firebase database.
     */
    getFirebaseSessionToken: function () {
      try {
        var session = sf_widget.utils.dataStorage.fetch(
          "sf_wdt_chat_session_token"
        );
        if (session) {
          session = JSON.parse(decodeURIComponent(session));

          if (session.expiryTime && (new Date(session.expiryTime) - Date.now() > 0)) {
            return session;
          }

          console.warn("The existing SF fb session token is expired already.");
          this.removeFirebaseSessionToken();
          return null;
        } else {
          this.removeFirebaseSessionToken();
        }
      } catch (err) {
        console.warn(
          "Platform fails to load the existing firbase session token.",
          err
        );
      }

      return null;
    },

    /**
     * This function is invoked to store the URL of the current chat room in data storage.
     *
     * @param { string } room The URL of the firebase chat room.
     * @param { string } expiryTime The expiry time of the chat room.
     */
    setCurrentChatRoom: function (room, expiryTime) {
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_room",
        value: room,
        expires: new Date(expiryTime).valueOf() - Date.now(),
      });
    },

    /**
     * This function is invoked to get the URL of the current firebase chat room from data storage (cookie / local storage).
     * If there is a chat room chain, then this should be the latest(tail) one.
     *
     * @returns { string } The URL of the latest firebase chat room.
     */
    getLatestChatRoom: function () {
      try {
        var chatRoom = sf_widget.utils.dataStorage.fetch("sf_wdt_chat_room");
        if (chatRoom) {
          chatRoom = decodeURIComponent(chatRoom);
          return chatRoom;
        }
      } catch (err) {
        console.warn("Platform fails to get the latest chat room.", err);
      }

      return null;
    },

    /**
     * This function is invoked to insert/update the chat conversation stored in data storage.
     *
     * @param { string } entrypointId The entrypoint Id of the conversation.
     * @param { Object } conversationData - Params for the contextual widget.
     * @param { string | null } conversationData.specialtyId
     * @param { string | null } conversationData.retailerStoreId
     * @param { number } convoStartTime The start time of the conversation in milliseconds.
     * @param { string } initialChatRoom The URL of the initial chat room for the conversation.
     */
    setChatConversation: function (
      entrypointId,
      conversationData,
      convoStartTime,
      initialChatRoom
    ) {
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_conversation",
        value: JSON.stringify({
          entrypointId: encodeURIComponent(entrypointId), // encoding the entrypoint id here because entrypoint id could contain 'space', we need to encode the spaces before adding it to the data storage.
          specialtyId: conversationData.specialtyId || undefined, // what happen if specialtyId is undefined? 
          retailerStoreId: conversationData.retailerStoreId || undefined,
          convoStartTime: convoStartTime,
          initialChatRoom: initialChatRoom,
        }),
        expires: sf_widget.options.webchat.conversationTimeout,
      });
    },

    /**
     * This function is invoked to get the existing chat conversation from data storage (cookie / local storage).
     *
     * @returns { Object | null } The existing chat conversation object if found, otherwise null.
     * The conversation object has the following fields:
     * - entrypointId (string): The entrypoint ID of the conversation.
     * - convoStartTime (number): The start time of the conversation in milliseconds.
     * - initialChatRoom (string): The URL of the first firebase chat room associatd with the conversation.
     */
    getExistingConversation: function () {
      try {
        var conversation = sf_widget.utils.dataStorage.fetch(
          "sf_wdt_chat_conversation"
        );
        if (conversation) {
          conversation = JSON.parse(decodeURIComponent(conversation));

          return {
            entrypointId: decodeURIComponent(conversation.entrypointId), // entrypointId is pre-encoded once before because of the 'spaces' within the id
            specialtyId: conversation.specialtyId || undefined,
            retailerStoreId: conversation.retailerStoreId || undefined,
            convoStartTime: conversation.convoStartTime,
            initialChatRoom: conversation.initialChatRoom,
          };
        }
      } catch (err) {
        console.warn(
          "Platform fails to load the existing chat conversation.",
          err
        );
      }

      return null;
    },

    /**
     * This API function is invoked (from Webchat) to save the firebase chat room session to data storage.
     *
     * @param {*} data
     */
    persistSession: function (data) {
      this.setFirebaseSessionToken(
        data.payload.session
      );

      if (data.payload.isNewSession) {
        var initialChatRoom = data.payload.session.firebaseUrl;
        this.setCurrentChatRoom(initialChatRoom, data.payload.expiryTime);
        this.createChatConversation(
          data.payload.entrypointId ||
          sf_widget.options.webchat.defaultConvEntrypoint,
          {
            specialtyId: data.payload.specialtyId,
            retailerStoreId: data.payload.retailerStoreId
          },
          initialChatRoom
        );
      }

      this.acknowledge(data);
    },

    /**
     * This API function is invoked (from Webchat) to save the firebase chat room to data storage.
     *
     * @param {*} data
     */
    persistChatRoom: function (data) {
      var latestChatRoomPath = data.payload.latestChatRoomPath;
      if (latestChatRoomPath) {
        this.setCurrentChatRoom(latestChatRoomPath, data.payload.expiryTime);
      }

      this.acknowledge(data);
    },

    /**
     * This function is invoked to start a new chat conversation.
     *
     * @param { string | null } entrypointId The entrypoint id of the new conversation.
     * @param { Object | null } conversationData - Params for the contextual widget.
     * @param { string | null } conversationData.specialtyId
     * @param { string | null } conversationData.retailerStoreId 
     * @param { string | null } initialChatRoom The URL of the chat room for this new conversation.
     */
    createChatConversation: function (
      entrypointId,
      conversationData,
      initialChatRoom
    ) {
      var entrypoint =
        entrypointId || sf_widget.options.webchat.defaultConvEntrypoint;
      var chatRoomPath = initialChatRoom || this.getLatestChatRoom();
      // The convoStartTime will be updated by updateConversationStartTime calling from Webchat.
      var convoStartTime = new Date().getTime();
      var trackingRep = sf_widget.utils.dataStorage.fetch("sf_wdt_tracking_rep");
      var associateData = {};
      conversationData = conversationData || {};

      sf_widget.utils.JSONP(
        sf_widget.options.salesfloor_api + "/public/reps/" + trackingRep,
        {
          errorCallback : function() {
            return;
          }
        },
        function(data) {
          associateData = data;
          return associateData;
        });

      this.setChatConversation(entrypoint, conversationData, convoStartTime, chatRoomPath);
      this.sendPostMessageToTemplate({
        action: "setEntrypoint",
        source: POST_MESSAGE_SOURCE,
        payload: {
          id: entrypoint,
          convoStartTime: convoStartTime,
          initialChatRoom: chatRoomPath,
          specialtyId: conversationData.specialtyId || undefined,
          retailerStoreId: conversationData.retailerStoreId || undefined,
          payload: {
            footerRep:
              sf_widget.utils.dataStorage.fetch("sf_wdt_footer_rep") ||
              undefined,
            specialtyId: conversationData.specialtyId || undefined,
            retailerStoreId: conversationData.retailerStoreId || undefined,
            associate_data: {
              id: associateData.ID || undefined,
              first_name: associateData.first_name || undefined,
              last_name: associateData.last_name || undefined,
              username: associateData.user_login || undefined,
              store: associateData.store_data || undefined,
              avatarUrl: associateData.avatarUrl || undefined,
            },
            associate_first_name: associateData.first_name || undefined,
            associate_full_name: associateData.display_name || undefined,
            associate_id: associateData.ID || undefined,
            associate_last_name: associateData.last_name || undefined,
          },
          mergePayloads: true,
          fireOnExistingSession: true,
        },
      });
    },

    /**
     * This API function is invoked (from Webchat) to renew/extend the existing chat conversation.
     *
     * @param {*} data
     */
    renewChatConversation: function (data) {
      var conversation = this.getExistingConversation();
      if (conversation) {
        this.setChatConversation(
          conversation.entrypointId,
          {
            specialtyId: conversation.specialtyId,
            retailerStoreId: conversation.retailerStoreId
          },
          conversation.convoStartTime,
          conversation.initialChatRoom
        );
        this.sendChatConversationToIframe(conversation);
      } else {
        // This is because you have the chat window open and idle for more than 30 mins.
        // The existing conversation is already expired,
        // so we will start a new conversation at this point.
        // Question: what if the user is at the storefront page? Shall we still invoke the default entrypoint???
        this.createChatConversation();
      }

      this.acknowledge(data);
    },

    /**
     * This API function is invoked (from Webchat) to end the current chat and start a new conversation with the provided entrypoint id.
     *
     * @param {*} data
     */
    restartChatConversation: function (data) {
      var payload = data.payload;
      if (payload) {
        this.createChatConversation(payload.entrypointId);
      }
      this.acknowledge(data);
    },

    /**
     * This API function is invoked (from Webchat) to remove the sf_wdt_chat_conversation from data storage in order to end the current conversation.
     */
    endChatConversation: function () {
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_chat_conversation",
        value: "",
        expires: "remove",
      });
    },

    /**
     * This function is invoked to send the firebase session token to Webchat for returning users.
     * Most likely, the session token should come from data storage.
     *
     * @param {*} session
     */
    sendSessionTokenToIframe: function (session) {
      if (session) {
        this.sendPostMessageToTemplate({
          action: "sendSession",
          source: POST_MESSAGE_SOURCE,
          payload: {
            session: session,
          },
        });
      } else {
        console.warn("Platform: there is no session token to be sent!");
      }
    },

    /**
     * This function is invoked to send the chat converation information to Webchat for the returning users.
     * Most likely, the converation object should come from data storage.
     *
     * @param {*} conversation
     */
    sendChatConversationToIframe: function (conversation) {
      if (conversation) {
        this.sendPostMessageToTemplate({
          action: "sendConversation",
          source: POST_MESSAGE_SOURCE,
          payload: {
            conversation: conversation,
          },
        });
      } else {
        console.warn("Platform: there is no conversation to be sent!");
      }
    },

    /**
     * This function is invoked to update the conversation start time in the data storage.
     * @param {*} data
     */
    updateConversationStartTime: function (data) {
      var conversation = this.getExistingConversation();
      if (conversation) {
        conversation.convoStartTime = data.payload.timestamp;
        this.setChatConversation(
          conversation.entrypointId,
          {
            specialtyId: conversation.specialtyId,
            retailerStoreId: conversation.retailerStoreId
          },
          conversation.convoStartTime,
          conversation.initialChatRoom
        );
        this.sendChatConversationToIframe(conversation);
      }
      this.acknowledge(data);
    },

    isPageException: function (host) {
      var exceptions = sf_widget.options.sf_rep_exceptions;
      if (exceptions === undefined) {
        return;
      }
      for (var i = 0; i < exceptions.length; ++i) {
        var reg = new RegExp(exceptions[i]);
        if (host.search(reg) === 0) {
          return true;
        }
      }
      return false;
    },

    needsFragmentInUrl: function (fragment, cookie) {
      var urlHasFragment = fragment.test(window.location.href),
        hasCookie = sf_widget.utils.dataStorage.fetch(cookie),
        isException = this.isPageException(window.location.hostname);
      return !urlHasFragment && hasCookie && !isException;
    },

    needsSfStoreUrl: function () {
      return this.needsFragmentInUrl(/sf_store/, "sf_wdt_footer_store");
    },

    needsSfRepUrl: function () {
      return this.needsFragmentInUrl(/sf_rep/, "sf_wdt_footer_rep");
    },

    addSfStoreUrl: function (param) {
      if (window.history.replaceState) {
        history.replaceState(
          null,
          null,
          window.location +
            param +
            "sf_store=" +
            sf_widget.utils.dataStorage.fetch("sf_wdt_footer_store")
        );
      }
    },

    addSfRepUrl: function (param) {
      if (window.history.replaceState) {
        history.replaceState(
          null,
          null,
          window.location +
            param +
            "sf_rep=" +
            sf_widget.utils.dataStorage.fetch("sf_wdt_footer_rep")
        );
      }
    },

    /**
     * if you are in a shopping page, it add sf_store to the url in case you share the page
     */
    replaceHistory: function () {
      var param = sf_widget.options.writeHistoryType === "hash" ? "#" : "";
      param += window.location.search ? "&" : "?";
      if (history) {
        if (this.needsSfStoreUrl()) {
          this.addSfStoreUrl(param);
        }
        if (this.needsSfRepUrl()) {
          this.addSfRepUrl(param);
        }
      }
    },

    footerStateCheck: function () {
      // if you are on storefront, then we should 
      //  1. clear the footer cookies
      //  2. remove the footer state fragment from the Url
      if (window.sf_sidebar_widget_mode === "storefront") {
        sf_widget.utils.dataStorage.set({
          name: "sf_wdt_footer_rep",
          value: "",
          expires: "remove",
        });
        sf_widget.utils.dataStorage.set({
          name:'sf_wdt_footer_rep', 
          expires:"session"
        });

        sf_widget.utils.dataStorage.set({
          name: "sf_wdt_footer_store",
          value: "",
          expires: "remove",
        });
        sf_widget.utils.dataStorage.set({
          name:'sf_wdt_footer_store', 
          expires:"session"
        });
        var curURL = new URL(window.location.href);
        if (curURL.searchParams.has("sf_rep") || curURL.searchParams.has("sf_store")) {
          curURL.searchParams.delete("sf_rep");
          curURL.searchParams.delete("sf_store");
          var updatedUrl = curURL.toString();
          window.history.replaceState({}, document.title, updatedUrl);
          window.location.replace(updatedUrl);
          location.reload();
        }

      } else {
        this.replaceHistory();
      }
    },

    /**
     * Remove widget completely from retailer website
     */
    remove: function () {
      var widget = document.getElementById("sf-widget-companion-wrapper");
      widget && widget.parentNode && widget.parentNode.removeChild(widget);
    },

    /**
     * This function is invoked to launch the chat window popup (Webchat).
     *
     * @param { 'fullpage' | 'popup' | 'storefront' | 'contextual' } webchat mode
     * @param { boolean } isChatWindowOpenedBefore True if the chat window popup is launched before.
     * @param { string } entrypointId The id of the entrypoint. If the request comes from Webchat lure, then this should be empty. If the request comes from special cases like storefront entrypoint, then it should have value.
     * @param { Object | null } conversationData - Params for the contextual widget.
     * @param { string | null } conversationData.specialtyId
     * @param { string | null } conversationData.retailerStoreId 
    */
    launchChatWindow: function (
      mode,
      isChatWindowOpenedBefore,
      entrypointId,
      conversationData
    ) {
      var isLure = mode === "popup";
      // open the Webchat popup window if the launch window request is not from the Webchat lure.
      if (!isLure) {
        this.openChat();
      }

      conversationData = conversationData || {};
      var sessionToken = this.getFirebaseSessionToken();
      if (sessionToken) {
        if (isLure && !isChatWindowOpenedBefore) {
          this.sendSessionTokenToIframe(sessionToken);
        }

        existingConv = this.getExistingConversation();
        // lite mode will trigger a new conversation everytime
        if (!existingConv || mode === "fullpage") {
          this.createChatConversation(entrypointId, conversationData);
        } else {
          if (!isLure && (sf_widget.utils.dataStorage.fetch('sf_wdt_livechat_status') === "connecting" || sf_widget.utils.dataStorage.fetch('sf_wdt_livechat_status') === "connected")) {
            this.sendSessionTokenToIframe(sessionToken);
            this.sendChatConversationToIframe(existingConv);
            return;
          }
          if (entrypointId) {
            if (existingConv.entrypointId !== entrypointId) {
              this.createChatConversation(entrypointId, conversationData);
            } else {
              this.sendSessionTokenToIframe(sessionToken);
              this.sendChatConversationToIframe(existingConv);
            }
          } else {
            this.sendChatConversationToIframe(existingConv);
          }
        }
      } else {
        this.createFirebaseChatSession(entrypointId, conversationData);
      }
    },

    setOpenState: function (data) {
      var payload = data.payload;
      if (payload) {
        if (payload.chat === "opened") {
          this.isOpen = true;
          this.reinitializeDataStorage(payload.dataStorage);
          this.launchChatWindow(payload.mode, payload.hasChatWindowOpened);
        } else {
          this.isOpen = false;
          if (payload.endConversation) {
            window.dispatchEvent(new Event("sf_connect_2_end_conversation"));
            this.endChatConversation();
          }
        }
      }

      this.acknowledge(data);
    },

    setSelectedStore: function (data) {
      var payload = data.payload;
      if (payload) {
        this.setCurrentStore(payload.sf_identifier);
        this.setCurrentLocale(payload.locale);
      }
      this.acknowledge(data);
    },

    setLivechatStatus: function (data) {
      var payload = data.payload;
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_livechat_status",
        value: payload.toString(),
        expires: sf_widget.options.saleCookieDuration,
      });
      this.acknowledge(data);
    },

    // Sends a "Got-It!" message to the Webchat widget, so that webchat can know which events are being acknowledged and which ignored"
    acknowledge: function (data) {
      this.sendPostMessageToTemplate({
        action: "acknowledgeEvent",
        source: POST_MESSAGE_SOURCE,
        payload: {
          eventId: data.id,
        },
      });
    },

    /**
     * This function is invoked to post message to the Webchat widget in the iframe.
     * For what actions we can use, please refer to the SalesfloorFrameApi.ts file in the Webchat project for more information.
     *
     * @param {*} data
     */
    sendPostMessageToTemplate: function (data) {
      var jsonData = JSON.stringify(data);
      document
        .getElementById(connect2WidgetId)
        .contentWindow.postMessage(jsonData, "*");
    },

    openChat: function () {
      this.sendPostMessageToTemplate({
        action: "setChatOpenState",
        source: POST_MESSAGE_SOURCE,
        payload: {
          isOpen: true,
        },
      });
    },
    setIFrameStyles: function (data) {
      document.getElementById(connect2WidgetId).style = data.payload;
      this.acknowledge(data);
    },

    trackChatActivity: function (args) {
      try {
        fetch(
          sf_widget.options.salesfloor_api +
            "/customer-activity-feed/track-activity",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json;charset=UTF-8",
            },
            body: JSON.stringify(args),
            cache: "reload",
          }
        );
      } catch (error) {
        console.error("Error:", error);
      }
    },

    setEmailCookie: function (email) {
      localStorage.setItem('sf_wdt_customer_email', email);
    },

    pushNotification: function (reps, message, inapp) {
      try {
        inapp = inapp || false;
        var data = JSON.stringify({reps: reps, message: message, inapp: inapp});

        fetch(sf_widget.options.salesfloor_storefront + '/api/pushNotification/firebase/publish', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8'
          },
          body: data
        });
      } catch (error) {
        console.error("Failed to push chat message notification:", error);
      }
    },

    // This function is invoked by Webchat when end user send a message during a live chat session
    trackAction: function (data) {
      var payload = data.payload;
      if (payload && payload.data && payload.data.type === "Text") {
        var chatRoomID = this.getFirebaseSessionToken()
          .firebaseUrl.split("/")
          .at(-1);
        var agentDataCookie =
          sf_widget.utils.dataStorage.fetch("sf_wdt_agent_data");
        if (agentDataCookie) {
          try {
            var agentData = JSON.parse(agentDataCookie);

            // send message push notification
            var repName = agentData.userLogin,
                rep = {},
                message = payload.data.text;
            rep[repName] = true;
            this.pushNotification(rep, message, {disabled: "true", alertBox: "false", event_action: "new_chat_message"});

            // log the chat activity
            this.trackChatActivity({
              customerIdOrEmail: localStorage.getItem('sf_wdt_customer_email'),
              userIdOrName: repName,
              preview: message,
              chatId: chatRoomID,
              type: 7,
              direction: "inbound",
              status: "read",
            });
          } catch (e) {
            console.error(
              "Failed to parse and track activity.",
              agentDataCookie,
              e
            );
          }
        }
      }

      this.acknowledge(data);
    },

    setAffiliateLink: function (data) {
      try {
        fetch(sf_widget.options.salesfloor_site + "/affiliate-link/" + data.payload.rep).then(function (data) {
          if (data.url) {
            var link = document.createElement("img");
            link.setAttribute("src", data.url);
            link.setAttribute(
              "style",
              "position:absolute;top:20px; width:1px; height:1px;"
            );
            link.setAttribute("height", "1");
            link.setAttribute("width", "1");
            link.setAttribute("data-name", "affiliate-link");
            document.body.appendChild(link);
          }
        });
      } catch (error) {
        console.error(error);
      }
    },

    openRepStorefrontPage: function (data) {
      var payload = data.payload;
      if (payload) {
        this.endChatConversation();

        // remove footer rep
        sf_widget.utils.dataStorage.set({
          name: "sf_wdt_footer_rep",
          value: "",
          expires: "remove",
        });
        sf_widget.utils.dataStorage.set({
          name:'sf_wdt_footer_rep', 
          expires:"session"
        });

        var curURL = new URL(window.location.href);
        if (curURL.searchParams.has("sf_rep")) {
          curURL.searchParams.delete("sf_rep");
          var updatedUrl = curURL.toString();
          window.history.replaceState({}, document.title, updatedUrl);
          window.location.replace(updatedUrl);
        }

        window.open(payload.url, "_top");
      }
      this.acknowledge(data);
    },

    initiateSalesTracking: function (data) {
      var salesTrackingData = data.payload.data;
      if (salesTrackingData.channel === "chat-trigger") {
        if (sf_widget.options.mode === "rep") {
          // We don't need to do the tracking for rep mode when you initiate live chat,
          // do it when a rep answer the call
          this.acknowledge(data);
          return;
        }

        salesTrackingData.channel = "chat";
      }

      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_tracking",
        value: "true",
        expires: sf_widget.options.saleCookieDuration,
      });

      if (sf_widget.options.mode === "store") {
        // team mode
        sf_widget.utils.dataStorage.set({
          name: "sf_wdt_tracking_store",
          value: salesTrackingData.store,
          expires: sf_widget.options.saleCookieDuration,
        });
        window.sf_widget.utils.dataStorage.set({
          name: "sf_wdt_tracking_rep",
          value: "",
          expires: "remove",
        });
      } else {
        // rep mode
        if (salesTrackingData.rep) {
          // To a specific rep
          sf_widget.utils.dataStorage.set({
            name: "sf_wdt_tracking_rep",
            value: salesTrackingData.rep,
            expires: sf_widget.options.saleCookieDuration,
          });
        } else {
          // New lead to the store
          sf_widget.utils.dataStorage.set({
            name: "sf_wdt_tracking_rep",
            value: "",
            expires: "remove",
          });
        }
        window.sf_widget.utils.dataStorage.set({
          name: "sf_wdt_tracking_store",
          value: "",
          expires: "remove",
        });
      }

      // Track email
      if (data.payload.data.email) {
        this.setEmailCookie(data.payload.data.email);
      }

      // Track Activity
      switch (data.payload.data.channel) {
        case "chat":
          var chatRoomID = this.getFirebaseSessionToken()
            .firebaseUrl.split("/")
            .at(-1);
          this.trackChatActivity({
            customerIdOrEmail:
              data.payload.data.email ||
              localStorage.getItem('sf_wdt_customer_email'),
            userIdOrName: data.payload.data.rep,
            chatId: chatRoomID,
            type: 7,
            direction: "inbound",
            status: "read",
          });
          break;
        case "appointment":
          window.sf_widget.utils.eventQueue.push(
            (data.payload.data.source + "_APPOINTMENT").toUpperCase()
          );
          break;
        case "contact-me":
          var event = (data.payload.data.source + "_EMAIL_ME").toUpperCase();
          if (data.payload.data.chat_handoff) {
            event = event + "_MISSED_CHAT";
          }
          window.sf_widget.utils.eventQueue.push(event);
          break;
      }

      sf_widget.utils.dataStorage.setAcquisition(
        salesTrackingData.source + "-" + salesTrackingData.channel
      );

      this.acknowledge(data);
    },
    setAgentData: function (data) {
      var agentData = data.payload;

      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_agent_data",
        value: JSON.stringify(agentData),
        expires: 1000 * 60 * 30, // 30 minutes
      });

      this.acknowledge(data);
    },
    sendSidebarTrackingEvent: function (data) {
      var payload = data.payload;
      if (payload) {
        sf_widget.widgets.base.dispatchSidebarTrackingEvent(payload);
      }
      this.acknowledge(data);
    },
    addIntegrationIdEventListener: function () {
      var self = this;

      window.addEventListener(
        "message",
        function (event) {
          if (event.data.action === "sendIntegrationId") {
            self.verifyIntegrationId(event.data.payload.integrationId);
          }
        },
        false
      );
    },
    verifyIntegrationId: function (integrationId) {
      /**
       * If we switch the bot instance (integrationID) for an environment
       * Platform needs to detect if the bot integration id stored in the cookie
       * is the same as the id the current environment is using.
       * If its different, then we clear the right cookies
       * https://salesfloor.atlassian.net/browse/RD-2194
       */
        var sessionToken = this.getFirebaseSessionToken();
  
        sessionName = sessionToken && sessionToken.sessionName;
        var sameIntegrationId = sessionName && sessionName.startsWith(integrationId);
  
        if (sessionName && !sameIntegrationId) {
          sf_widget.utils.dataStorage.set({
            name: "sf_wdt_chat_conversation",
            value: "",
            expires: "remove",
          });
          sf_widget.utils.dataStorage.set({
            name: "sf_wdt_chat_room",
            value: "",
            expires: "remove",
          });
          sf_widget.utils.dataStorage.set({
            name: "sf_wdt_chat_session_token",
            value: "",
            expires: "remove",
          });
        } else if (sessionToken && sameIntegrationId) {
          this.sendSessionTokenToIframe(sessionToken);
        }
      },
    unsupportedEvent: function (data) {
      this.acknowledge(data);
      // Only necessary if we want to add support for Webchat protocol behaviours
      //console.warn("Unsupported Connect V2 Chat Event", data);
    },
    setCurrentLocale: function (locale) {
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_sidebar_store_locale",
        value: locale,
        expires: sf_widget.options.cacheCookieDuration,
      });
    },
    setCurrentStore: function (id) {
      sf_widget.sf_store_widget = id;
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_sidebar_store",
        value: id,
        expires: sf_widget.options.cacheCookieDuration,
      });
    },
    isTower: function () {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    sendAddSourcePostMessage: function () {
      this.sendPostMessageToTemplate({
        action: "addSource",
        source_url: window.location.toString(),
        source_title: window.document.title,
      });
    },
    isWidgetLoaded: function () {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    manageDisplayOnScroll: function () {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    manageMinimizeMobileOnScroll: function () {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    isScrollAtTheBottom: function () {
      var scrollData = this.scrollData();

      return scrollData.scrollPosition + scrollData.windowHeight === scrollData.documentScrollHeight ? false : true;
    },
    getCurrentWidgetDimension: function (widget, attribute) {
      var widgetStyle = window.getComputedStyle(widget);

      return widgetStyle.getPropertyValue(attribute);
    },
    toggleDisplayOnScroll: function () {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    // Add bottom spacing if on mobile safari to prevent ios menu bar from conflicting with sidebar widget
    calculateBottomPosition: function () {
      //regex to check user agent if browser is safari and not chrome
      var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      if (
        isSafari &&
        sf_widget.options.sidebar.media.mobile.position.bottom < 20
      ) {
        sf_widget.options.sidebar.media.mobile.position.bottom =
          sf_widget.options.sidebar.media.mobile.position.bottom + 20;
        return sf_widget.options.sidebar.media.mobile.position.bottom;
      }
      return sf_widget.options.sidebar.media.mobile.position.bottom;
    },
    throttleEvent: function (method, wait) {
      var time = Date.now();
      var self = this;
      return function () {
        if (time + wait - Date.now() < 0) {
          method.call(self);
          time = Date.now();
        }
      };
    },
    checkDisabled: function () {
      if (
        sf_widget.utils.dataStorage.fetch("sf_wdt_sidebar_state") === "disabled"
      ) {
        return true;
      }
    },
    disable: function () {
      var $widget = document.getElementById("sf-widget-companion-wrapper");
      if ($widget) {
        $widget.parentNode.removeChild($widget);
      }
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_sidebar_state",
        expires: "session",
      });
      sf_widget.utils.dataStorage.set({
        name: "sf_wdt_sidebar_state",
        value: "disabled",
        expires: sf_widget.options.sidebarMinimizeDuration,
      });
    },
    checkWindowWidth: function () {
      if (sf_widget.utils.dataStorage.fetch("sf_wdt_sidebar_state") !== "open") {
        if (
          sf_widget.options.sidebarMinimizeSmallScreen &&
          !sf_widget.widgets.sidebar.isTower()
        ) {
          if (
            window.innerWidth < sf_widget.options.screenInnerWidthBreakPoint
          ) {
            if (!sf_widget.utils.dataStorage.fetch("sf_wdt_sidebar_state")) {
              this.setWidgetState({ state: "closeFull" });
            }
          }
        }
      }
    },
    getAnimateInView: function () {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    /**
     * set iframe height
     */
    setIframeHeight: function (data) {
      var height = data || window.innerHeight;
      this.sendPostMessageToTemplate({
        action: "onWindowResize",
        source: POST_MESSAGE_SOURCE,
        payload: {
          width: window.innerWidth,
          height: height,
        },
      });
    },
    /**
     * set iframe width
     */
    setIframeWidth: function (data) {
      console.warn("Method not implemented or relevant in Connect 2.0");
      return false;
    },
    /**
     * Get given store, or the nearest store based on user ip
     */
    showFromNearestStore: function (
      currentLocale,
      successCallback,
      errorCallback
    ) {
      var self = this,
        ip = sf_widget.utils.getIp(),
        url = sf_widget.options.salesfloor_api + "/stores",
        data = {
          "filter[ip]": ip || "current",
          "filter[locale]": currentLocale,
          "include-has-sms": true,
          errorCallback: function () {
            sf_widget.utils.showWebsiteNodes(sf_widget.options.removeOnShow);
            if (errorCallback) {
              errorCallback(new Error());
            }
          },
        };

      if (sf_widget.options.sidebar.findNearbyStore) {
        data["filter[origin]"] = "chat_availability";
      }

      sf_widget.utils.JSONP(
        url,
        data,
        // callback
        function (data) {
          // check if retailer want the widget
          if (data) {
            // if config is enabled store list of closest stores
            if (sf_widget.options.sidebar.findNearbyStore) {
              self.setNearestStores(
                data.map(function (data) {
                  return data.sf_identifier;
                })
              );
            }
            data = data[0];
          }

          if (data && data.name) {
            // Set returned nearest store with the matching current locale
            self.setStore(
              self.getStoreId(data),
              true,
              self.getStoreLocale(data)
            );
            // exit the function once its determined that a near store exists
            if (successCallback) {
              successCallback(true);
              return;
            }
            self.append();
            // If call fails or no store available (NO JSONP error handling)
          } else {
            // clear selected store (if any): it doesn't exist.
            self.setStore(null, true, null);
            if (successCallback) {
              successCallback(false);
              return;
            }
            sf_widget.utils.showWebsiteNodes(sf_widget.options.removeOnShow);
          }
        }
      );
    },
    /**
     * Set widget state close change the way it's render at initial load.
     * This is handled with a query string passed to twig in the widget
     */
    setWidgetState: function (data, duration) {
      if (duration) {
        if (
          sf_widget.utils.dataStorage.fetch("sf_wdt_sidebar_state") !== data.state
        ) {
          sf_widget.utils.dataStorage.set({
            name: "sf_wdt_sidebar_state",
            value: "closeFull",
            expires: duration,
          });
        }
      } else {
        sf_widget.utils.dataStorage.set({
          name: "sf_wdt_sidebar_state",
          value: data.state,
          expires: "session",
        });
      }
    },
    getMobileData: function (options) {
      var settings = options || {};
      var windowWidth =
          window.innerWidth ||
          document.documentElement.clientWidth ||
          document.body.clientWidth,
        data = {
          height: sf_widget.options.widgetHeight,
          media: "desktop",
        };

      if (sf_widget.utils.dataStorage.fetch("sf_wdt_sidebar_state") === "close") {
        data.height = sf_widget.options.sidebarClosedHeight;
      }
      // mobile view
      if (windowWidth < 767) {
        data.height = settings.stateOverride !== "open" ? sf_widget.options.sidebarMobileHeight : sf_widget.options.widgetHeight;
        data.media = "mobile";
      }

      return data;
    },
    setStore: function (id, justCaching, locale) {
      var previousStore = sf_widget.utils.dataStorage.fetch(
        "sf_wdt_sidebar_store"
      );
      var previousLocale = sf_widget.utils.dataStorage.fetch(
        "sf_wdt_sidebar_store_locale"
      );

      var duration = justCaching ? sf_widget.options.cacheCookieDuration : sf_widget.options.saleCookieDuration;

      if (previousStore === null || previousStore !== id) {
        this.setCurrentStore(id, duration);
        if (id) {
          sf_widget.sf_store_widget = id;
        }
      }

      if (previousLocale !== locale) {
        this.setCurrentLocale(locale, duration);
      }
    },
    setNearestStores: function (nearestStores) {
      if (nearestStores && nearestStores.length) {
        sf_widget.nearestStores = nearestStores;
      }
    },
    chooseStore: function (data) {
      var storeId = this.getStoreId(data.store);
      var storeLocale = this.getStoreLocale(data.store);
      this.setStore(null, true, null);
      this.setStore(storeId, false, storeLocale);
      this.fullReload();
      this.forceDisplay();
    },
    getStoreId: function (store) {
      return store.sf_identifier;
    },
    getStoreLocale: function (store) {
      return store.locale;
    },
    fullReload: function () {
      this.remove();
      this.append({ stateOverride: "open" });
    },
    changeLocation: function (data) {
      window.location.href = data.url;
    },
    forceDisplay: function () {
      var widget = document.querySelector("#sf-widget-companion-wrapper");
      widget.style.display = "block";
    },
    content: {
      getIframeUrl: function (queryParams) {
        queryParams = queryParams || {};
        var agentData =
          sf_widget.utils.dataStorage.fetch("sf_wdt_agent_data") || "{}";

        return (
          sf_widget.options.salesfloor_site +
          "/stores/" +
          sf_widget.sf_store_widget +
          "/widgets/sidebar4" +
          "?media=" +
          sf_widget.widgets.sidebar.getMobileData().media +
          "&customer_id=" +
          (sf_widget.utils.dataStorage.fetch("sf_wdt_customer_id") || "") +
          "&agent=" +
          encodeURIComponent(agentData) +
          "&footer_rep=" +
          (sf_widget.utils.dataStorage.fetch("sf_wdt_footer_rep") || "") +
          "&fingerprint=" +
          (sf_widget.utils.dataStorage.fetch("sf_wdt_fingerprint") || "") +
          "&sf_ip=" +
          (sf_widget.utils.getIp() || "current") +
          "&sf_locale=" +
          (sf_widget.utils.getLocale() || "") +
          Object.entries(queryParams)
            .map(function (e) {
              return "&" + e[0] + "=" + e[1];
            })
            .join("")
        );
      },
      getTemplate: function (queryParams) {
        queryParams = queryParams || {};
        var iframeTitle = "Salesfloor Widget";
        var zIndex = sf_widget.options.webchat.zIndex || 99999999;
        var customStyle = sf_widget.options.webchat.customStyle || "";
        return (
          '<div id="sf-widget-companion-wrapper" style="left:0px;bottom:0px;position:fixed;z-index:' + 
          zIndex + 
          ';display:block;vertical-align:middle;zoom:1;user-select:none;transform:translate3d(0,0,0);' + 
          customStyle +
          '">' +
          '<iframe id="' +
          connect2WidgetId +
          '" src="' +
          this.getIframeUrl(queryParams) +
          '" width="0" height="0" style="border:none;overflow:hidden;display:block;visibility:hidden;" scrolling="yes" seamless allowTransparency="true" frameBorder="0" title="' +
          iframeTitle +
          '">' +
          "<!--iframecontent-->" +
          "</iframe>" +
          "</div>"
        );
      },
    },
  };

  sf_widget.loadedWidgets = sf_widget.loadedWidgets + 1;
})();
