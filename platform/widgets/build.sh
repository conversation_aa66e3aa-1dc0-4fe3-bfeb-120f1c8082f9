#!/bin/bash

CURRENT_FOLDER=$(dirname "$(readlink -f "$0")")
NODE_MODULE_PATH="node_modules"

# ./build.sh $ACTION
# Argument:
# ACTION:
#   Possible values: "", "clean", "force_build"

# Helper script
. ${CURRENT_FOLDER}/../bin/bash_helper.sh ${CURRENT_FOLDER} "${1}"

set -e
# Widget build script
# run the following commands from the widgets directory
MARKER="node_modules/.valid-1"
if [ ! -e "${MARKER}" ]; then
  rm -Rf node_modules/.valid*
  npm ci
  # Running JSPM Install as its possible the vendor/fe folder is deleted
  # or the install has not ran properly.
  jspm install
  npm run build
  touch "${MARKER}"
else
  echo "Widgets already provisioned"
fi
