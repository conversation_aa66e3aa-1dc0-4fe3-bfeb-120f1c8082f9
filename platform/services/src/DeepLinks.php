<?php

namespace Salesfloor\Services;

use Salesfloor\Services\Tasks\Automated\CancelledTransactionFollowUp;
use Salesfloor\Services\Tasks\Automated\NagShareUpdate;
use Salesfloor\Services\Tasks\Automated\NagUpdateStorefrontProducts;
use Salesfloor\Services\Tasks\Automated\NagOnboardingUpdateAboutMe;
use Salesfloor\Services\Tasks\Automated\NagOnboardingUploadProfilePic;
use Salesfloor\Services\Tasks\Automated\NagOnboardingConnectSocialMedia;
use Salesfloor\Services\Tasks\Automated\NagOnboardingAddContacts;

class DeepLinks
{
    private $mobileBuilds;
    private $storefrontUrl;
    private $branchBaseUrl;
    private $retailerId;
    private $enabled;
    private $isAppLoginPrimary;
    private $appUrl;

    public function __construct(\Salesfloor\Configs\Configs $configs, MobileBuilds $mobileBuilds)
    {
        $this->mobileBuilds = $mobileBuilds;
        $this->storefrontUrl = str_replace('http://', 'https://', $configs['salesfloor_storefront.host']);
        $this->branchBaseUrl = $configs['branch.base_url'];
        $this->retailerId = $configs['retailers.current.id'];
        $this->enabled = $configs['deep_links_enabled'];

        $this->isAppLoginPrimary = $configs['retailer.backoffice_primary_login_through_app.enabled'];
        $this->appUrl = $this->storefrontUrl . '/app';
    }

    public function generate($deepLinkID, $requestType = '', $messageID = '', $eventUniqId = null, $params = null)
    {
        $requestID = $requestType . "_" . $messageID;
        $deepLinkParams = [];

        switch ($deepLinkID) {
            case 'VIEW_REQUEST_LINK':
            case 'REPLY_REQUEST_LINK':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice/store-request-center#/storeRequests/unresolved/thread/$requestID",
                    '$deeplink_path' => "/store-requests/id/$requestID"
                ]);
                break;
            case 'VIEW_MESSAGE_LINK':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice/store-request-center#/messages/inbox/thread/$messageID",
                    '$deeplink_path' => "/store-messages/id/$messageID"
                ]);
                break;
            case 'REP_ACCEPT_LINK':
                $deepLinkParams = [
                    '$desktop_url'   => "$this->storefrontUrl/sfadmin/appointment.php?magicid={$eventUniqId}" .
                    "&meetingid=$messageID&action=accept&customer=false" .
                    "&rep={USERLOGIN}&version={SERVICE_VERSION}",
                    '$deeplink_path' => "/store-requests/id/$requestID"
                ];
                break;
            case 'REP_RESCHEDULE_LINK':
                $deepLinkParams = [
                    '$desktop_url'   => "$this->storefrontUrl/sfadmin/appointment.php?magicid={$eventUniqId}" .
                    "&meetingid=$messageID&action=change&version={SERVICE_VERSION}",
                    '$deeplink_path' => "/store-requests/new-time/id/$requestID"
                ];
                break;
            case 'BACKOFFICEPAGE':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice",
                    '$deeplink_path' => '/'
                ]);
                break;
            case 'NOTIFICATIONS_URL':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice/store-request-center#/storeRequests/unresolved",
                    '$deeplink_path' => '/store-requests'
                ]);
                break;
            case 'EMAIL_LINK':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice/store-request-center#/messages/compose?target={CUSTOMER_EMAIL_ENC}",
                    '$deeplink_path' => '/store-messages/compose?email={CUSTOMER_EMAIL_ENC}'
                ]);
                break;
            case 'ONBOARDING_LINK':
                $token = $this->getOnboardingToken($params);
                $deepLinkParams = [
                    '$desktop_url'   => "$this->storefrontUrl/setup?token=$token",
                    '$deeplink_path' => 'rep-onboarding/create-user?token=' . $token
                ];
                break;
            case 'ONBOARDING_ACTIVATE_ACCOUNT_LINK':
                $token = $this->getOnboardingToken($params);
                $deepLinkParams = [
                    '$desktop_url'   => "$this->storefrontUrl/setup/activate-account?token=$token",
                    '$deeplink_path' => 'rep-onboarding/create-user?token=' . $token
                ];
                break;
            case 'TASKREMINDER':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice",
                    '$deeplink_path' => '/tasks/edit/' . $messageID,
                ]);
                break;
            case 'GROUP_TASK_REMINDER':
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice",
                    '$deeplink_path' => '/v2/group-tasks/' . $messageID,
                ]);
                break;
            case NagShareUpdate::DEEPLINK_ACTION:
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice",
                    '$deeplink_path' => '/share',
                ]);
                break;
            case NagUpdateStorefrontProducts::DEEPLINK_ACTION:
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice",
                    '$deeplink_path' => '/storefront',
                ]);
                break;
            case NagOnboardingUpdateAboutMe::DEEPLINK_ACTION:
            case NagOnboardingUploadProfilePic::DEEPLINK_ACTION:
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice",
                    '$deeplink_path' => '/settings/index',
                ]);
                break;
            case NagOnboardingConnectSocialMedia::DEEPLINK_ACTION:
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice/settings",
                    '$deeplink_path' => '/settings/index',
                ]);
                break;
            case NagOnboardingAddContacts::DEEPLINK_ACTION:
                $deepLinkParams = $this->decideBackofficeLinkLocation([
                    '$desktop_url'   => "$this->storefrontUrl/backoffice/contacts",
                    '$deeplink_path' => '/contacts',
                ]);
                break;
            default:
                break;
        }

        if (!$this->enabled) {
            return $deepLinkParams['$desktop_url'];
        }

        // Download pages to redirect the user when they don't have the app installed
        // This overrides the iOS and Android fallback URLs set in the branch web dashboard
        $deepLinkParams['$ios_url'] = $this->mobileBuilds->getIosDownloadPage();
        $deepLinkParams['$android_url'] = $this->mobileBuilds->getAndroidDownloadPage();

        // Page to redirect the user when they leave the app with the iOS top-right button
        if ($this->mobileBuilds->isIosStoreApp()) {
            $deepLinkParams['$ios_has_app_url'] = "$this->storefrontUrl/ios_deeplink_fallback/ios_fallback.html";
        }

        // Our deeplinks are sent via sendgrid, but we don't do anything sendgrid-specific.
        // WESHOULD maybe not need this?
        $deepLinkParams['$3p'] = "e_sg";

        // Because the query params contain some {variables} for template
        // replacement we must decode them, otherwise the replacement fails.

        $deepLinkParams['$deeplink_path'] = $this->addDefaultParams($deepLinkParams['$deeplink_path']);

        $query = str_replace(['%7B', '%7D'], ['{', '}'], http_build_query($deepLinkParams));

        return $this->branchBaseUrl . '?' . $query;
    }

    /**
     * Decide to redirect to backoffice location or redirect to /app
     * @param array $linkDetails
     * @return array decided link details
     */
    private function decideBackofficeLinkLocation($linkDetails)
    {
        if ($this->isAppLoginPrimary) {
            return [
                '$desktop_url'   => $this->appUrl,
                '$deeplink_path' => '/app'
            ];
        }
        return $linkDetails;
    }

    private function addDefaultParams($path)
    {
        $path .= strpos($path, '?') ? '&' : '?';

        $path .= 'retailerId=' . $this->retailerId;

        return $path;
    }

    private function getOnboardingToken($params)
    {
        $token = $params['token'];
        if (!$token) {
            throw new \Exception(
                "BUG: Missing token in onboarding params for deep links. Have "
                . json_encode($params)
            );
        }

        return $token;
    }
}
