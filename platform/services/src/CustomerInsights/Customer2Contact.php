<?php

/**
 * Customer Update
 *
 * This import is for <PERSON> Rosen but could be use by other retailers
 *
 * Prepare and commit a mass import / update
 * of customers via csv file.
 */

namespace Salesfloor\Services\CustomerInsights;

use Salesfloor\API\Managers\Client\Customers\V1 as CustomerManager;
use Salesfloor\API\Managers\CustomersToRetailerCustomers;
use Salesfloor\Models\Customer;
use Salesfloor\Models\CustomerAddress;
use Salesfloor\Models\CustomerEvent;
use Salesfloor\Models\CustomerMeta;
use Salesfloor\Models\CustomerSocialMedia;
use Salesfloor\Models\CustomerTagsRelationships;
use Salesfloor\Models\CustomerToCustomerAttribute;
use Salesfloor\Models\RetailerCustomer;
use Salesfloor\Services\CustomerInsights\Matching\BaseMatchCustomersRetailerCustomers;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Services\NormalizationPhoneNumber;
use Silex\Application;

class Customer2Contact
{
    /** @var Application */
    protected $app;

    /** @var MySQLRepository $repo */
    protected $repo;

    /** @var CustomerManager $customersManager */
    protected $customersManager;

    /** @var BaseMatchCustomersRetailerCustomers $matchCustomersRetailerCustomers */
    protected $matchCustomersRetailerCustomers;

    /** @var \Salesfloor\API\Managers\CustomerMeta $customerMetaManager */
    private $customerMetaManager;

    /** @var NormalizationPhoneNumber $phoneNormalization */
    private $phoneNormalization;

    private $logger;

    public function __construct(Application $app)
    {
        $this->app = $app;

        $this->repo = $app['repositories.mysql'];
        $this->customersManager = $app['customers_v1.manager'];
        $this->matchCustomersRetailerCustomers = $app['match-customer-retailercustomers'];
        $this->customerMetaManager = $app['customersmeta.manager'];
        $this->phoneNormalization = $app['normalization.phonenumbers'];
        $this->logger = $app['logger'];
    }

    /**
     * Create a full contact from a retailer customer (+ including the linked customer data records)
     *
     * Note: Retailer data should be validated before calling this function (e.g: phone number not matching the right
     * format and preventing the creation of the contact)
     *
     * @param int $userId
     * @param RetailerCustomer $retailerCustomer
     * @param $customerOrigin
     * @param string $matchOrigin
     *
     * @return Customer
     * @throws \Throwable
     */
    public function createCustomerFromRetailerCustomer(
        int $userId,
        RetailerCustomer $retailerCustomer,
        $customerOrigin,
        string $matchOrigin = ''
    ): Customer {
        try {
            // Use transactions to prevent creation of duplicate entries
            $this->repo->beginTransaction();

            // Copy the customer data from the retailer customer
            $customerData = [
                'user_id'                     => $userId,
                'email'                       => $retailerCustomer->email,
                'name'                        => $retailerCustomer->first_name . ' ' . $retailerCustomer->last_name,
                'phone'                       => $retailerCustomer->phone,
                'localization'                => null, // Not sure how this should be filled
                'geo'                         => null,
                'latitude'                    => null,
                'longitude'                   => null,
                'comment'                     => null,
                'subcribtion_flag'            =>
                    $this->getSubscriptionStatusFromRetailerCustomer($retailerCustomer->is_subscribed),
                'sms_marketing_subscription_flag' =>
                    $this->getSubscriptionStatusFromRetailerCustomer($retailerCustomer->is_subscribed_sms_marketing),
                'first_name'                  => $retailerCustomer->first_name,
                'last_name'                   => $retailerCustomer->last_name,
                'note'                        => '',
                'created'                     => gmdate('Y-m-d H:i:s'),
                'last_modified'               => null,
                'type'                        => Customer::TYPE_PERSONAL,
                'retailer_customer_id'        => null,
                'label_email'                 => $retailerCustomer->email_label,
                'label_phone'                 => $retailerCustomer->phone_label,
                'origin'                      => $customerOrigin,
                'locale'                      => $retailerCustomer->locale,
                'unassigned_employee_id'      => null,
                'retailer_parent_customer_id' => null,
                'entity_last_modified'        => null,
                'entity_last_export'          => null,
            ];

            /** @var Customer $customer */
            $customer = $this->customersManager->create($customerData);

            if ($customerOrigin == Customer::ORIGIN_RETAILER_CI_TRANSACTION) {
                $userId = null;
            } elseif ($customerOrigin !== Customer::ORIGIN_RETAILER_REP_APPOINTMENT) {
                $customerOrigin = Customer::ORIGIN_RETAILER_CUSTOMER_TO_CONTACT;
            }
            $historyContext = $this->customersManager->buildHistoryContext(null, $customerOrigin, $userId);

            // Create the customer
            $this->customersManager->checkAndSave(
                $customer,
                null,
                [],
                false,
                $historyContext,
                false
            );

            // Import all other tables
            $this->loadCustomerDataToContact($retailerCustomer, $customer);

            // Link the customer to the contact
            if (!empty($matchOrigin)) {
                $this->matchCustomersRetailerCustomers->createMatch(
                    $customer->getId(),
                    $retailerCustomer->getId(),
                    CustomersToRetailerCustomers::MATCH_MANUAL . ':' . $matchOrigin
                );
            }

            $this->repo->commit();
            return $customer;
        } catch (\Throwable $t) {
            $this->repo->rollback();
            throw $t;
        }
    }

    /**
     * Copy the retailer customer linked data to a contact
     *
     * @param RetailerCustomer $retailerCustomer
     * @param Customer $customer
     */
    protected function loadCustomerDataToContact(RetailerCustomer $retailerCustomer, Customer $customer)
    {
        $toLoad = [];

        $customerAddress = new CustomerAddress();
        $toLoad[]        = [
            'source' => 'sf_retailer_customer_addresses',
            'dest'   => 'sf_customer_addresses',
            'fields' => array_keys($customerAddress->fields),
        ];

        $customerEvent = new CustomerEvent();
        $toLoad[]      = [
            'source' => 'sf_retailer_customer_events',
            'dest'   => 'sf_customer_events',
            'fields' => array_keys($customerEvent->fields),
        ];

        $customerMeta = new CustomerMeta();
        $toLoad[]     = [
            'source' => 'sf_retailer_customer_meta',
            'dest'   => 'sf_customer_meta',
            'fields' => array_keys($customerMeta->fields),
            'callback' => function () use ($customer) {
                $phoneMetas = $this->customerMetaManager->getAll(
                    [
                        'customer_id' => $customer->getId(),
                        'type' => 'phone'
                    ],
                    0,
                    -1
                );

                /** @var CustomerMeta $meta */
                foreach ($phoneMetas as $meta) {
                    // Reset value, so if more than 1 phone numbers, we don't reuse previous phone number.
                    $normalizedPhone = null;

                    $phone = $meta->value;
                    try {
                        // Let's normalize them since they aren't in CI data.
                        $normalizedPhone = $this->phoneNormalization->normalizeWithGuess($phone, !empty($customer->user_id) ? $customer->user_id : null);
                    } catch (\Exception $e) {
                        $this->logger->error($e);
                        continue;
                    } finally {
                        $this->logger->debug(
                            sprintf(
                                "Normalization of phone number [%s] for customer id [%s] completed. Result [%s]",
                                $meta->value,
                                $customer->getId(),
                                $normalizedPhone
                            )
                        );

                        // Can't normalize it ; delete it
                        if (empty($normalizedPhone)) {
                            $this->customerMetaManager->delete($meta->getId());
                        } else {
                            $meta->value = $normalizedPhone;
                            $this->customerMetaManager->save($meta);
                        }
                    }
                }
            },
        ];

        $customerSocialMedia = new CustomerSocialMedia();
        $toLoad[]            = [
            'source' => 'sf_retailer_customer_social_media',
            'dest'   => 'sf_customer_social_media',
            'fields' => array_keys($customerSocialMedia->fields),
        ];

        $customerTagsRelationships = new CustomerTagsRelationships();
        $toLoad[]                  = [
            'source' => 'sf_retailer_customer_tags_relationships',
            'dest'   => 'sf_customer_tags_relationships',
            'fields' => array_keys($customerTagsRelationships->fields),
        ];

        $customerToCustomerAttribute = new CustomerToCustomerAttribute();
        $toLoad[]  = [
            'source' => 'sf_retailer_customers_to_customer_attributes',
            'dest'   => 'sf_customers_to_customer_attributes',
            'fields' => array_keys($customerToCustomerAttribute->fields),
        ];

        foreach ($toLoad as $loadData) {
            $fieldsToLoad = [];
            $valuesToLoad = [];
            foreach ($loadData['fields'] as $field) {
                if ($field === 'ID' || $field === 'id') {
                    continue;
                }

                // Default the value to import to the source field name
                $value = $field;

                // In the customer tables the customer ID link uses the PK instead of the retailer customer ID
                if ($field === 'customer_id') {
                    $value = $customer->getId();
                }

                // Customer processing because the fields are sometime slightly different
                // Fields should be in the order of the dest
                switch ($loadData['dest']) {
                    case 'sf_customer_meta':
                        if ($field === 'creation_date') {
                            $value = 'created_at';
                        } elseif ($field === 'modification_date') {
                            $value = 'updated_at';
                        }
                        break;
                }

                // We can add some custom processing here later (e.g: replace the created/update dates etc.)
                // Any literal value should contain enclosing quotes (e.g: "my_value")
                $fieldsToLoad[] = $field;
                $valuesToLoad[] = $value;
            }

            $q = 'INSERT INTO ' . $loadData['dest'] . ' (' . implode(', ', $fieldsToLoad) . ')
                 SELECT ' . implode(', ', $valuesToLoad) . '
                 FROM ' . $loadData['source'] . '
                 WHERE customer_id = "' . $retailerCustomer->customer_id . '"';

            $this->repo->executeRawQuery($q);

            if (isset($loadData['callback'])) {
                $loadData['callback']();
            }
        }
    }

    /**
     *  The subscription status  of retailer customers can be null, when adding it to the contact list,
     *  We have to check it and transform it to the default value if it's null.
     */
    protected function getSubscriptionStatusFromRetailerCustomer(?int $subscriptionStatus): int
    {
        return $subscriptionStatus ??
            (int)$this->app['configs']['retailer.add_customer_to_my_contacts.default_subscription_status'];
    }
}
