<?php

namespace Salesfloor\Services\Algolia\Indexer;

use Algolia\AlgoliaSearch\Exceptions\BadRequestException;
use Algolia\AlgoliaSearch\SearchClient;
use Algolia\AlgoliaSearch\SearchIndex;
use AlgoliaSearch\Client;
use AlgoliaSearch\Index;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Result;
use Exception;
use Psr\Log\LoggerInterface;
use Salesfloor\API\Managers\ProductVariants;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\Algolia\Exceptions\AlgoliaIndexerCategoryException;
use Salesfloor\Services\Algolia\Exceptions\AlgoliaIndexerException;
use Salesfloor\Services\CDN\CloudinaryImage;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Services\TablesDiff;

abstract class Base
{
    /**
     * If the structure change too much, we could move those field in each version
     */

    /**
     * Those delimiter used by group_concat, so we don't need to loop over each product.
     * - variants
     * - retailer_ids (Only used if extended variant is disabled)
     */


    const SEPARATOR_CATEGORIES_PROPERTIES   = '####';
    const SEPARATOR_CATEGORIES_DIVISON      = '-=-=-=-';
    const SEPARATOR_CATEGORIES_PARENT_CHILD = '@@@@';

    const SEPARATOR_CATEGORY_MAP_DIVISION = '####';


    const SEPARATOR_VARIANTS_PROPERTIES          = '####';
    const SEPARATOR_VARIANTS_DIVISON             = '@@@@';
    const SEPARATOR_VARIANTS_ATTRIBUTES          = '-=-=-=-';
    const SEPARATOR_VARIANTS_ATTRIBUTES_DIVISION = '&|&|&';

    const SEPARATOR_PRODUCT_RETAILER_IDS = '####';

    const FIELD_PRODUCT_ID  = 'product_id';
    const FIELD_AVAILABLE   = 'available';
    const FIELD_NAME        = 'name';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_BRAND       = 'brand';

    const FIELD_PRICE         = 'price';
    const FIELD_SALE_PRICE    = 'sale_price';
    const FIELD_OLD_PRICE     = 'old_price';
    const FIELD_REGULAR_PRICE = 'regular_price';

    const FIELD_PRODUCT_URL      = 'product_url';
    const FIELD_IMAGE_URL        = 'image_url';
    const FIELD_PUBLICATION_DATE = 'publication_date';
    const FIELD_DEAL_START_DATE  = 'deal_start_date';
    const FIELD_DEAL_END_DATE    = 'deal_end_date';
    const FIELD_RETAILER_SKU     = 'retailer_sku';
    const FIELD_PRICE_RANGE      = 'price_range';
    const FIELD_ON_SALE          = 'on_sale';
    const FIELD_DEFAULT_SKU      = 'default_sku';

    // This is to make new indexer backward compatible (v2 is not)
    const FIELD_CATEGORY_ID = 'category_id';
    const FIELD_CATEGORY    = 'category';

    // This is used in the v2 (Not ready)
    const FIELD_CATEGORIES      = 'categories';
    const FIELD_CATEGORY_CHILD  = 'child';
    const FIELD_CATEGORY_PARENT = 'parent';

    // Dynamic field
    const FIELD_DYNAMIC_THUMBNAIL_URL = 'thumbnail_url';

    const MYSQL_CATEGORY_ID = 'category_id';
    const MYSQL_CATEGORY_PARENT_ID = 'parent_id';
    const MYSQL_CATEGORY_NAME = 'category_name';

    /////////////////////////////////////////
    //
    // Variant Section
    //

    const FIELD_VARIANTS = 'variants';

    // Those field are specific to variant and can't be share with generic product field
    const FIELD_VARIANT_IS_DEFAULT = 'is_default';
    const FIELD_VARIANT_SKU        = 'sku';
    const FIELD_VARIANT_GTIN       = 'gtin';
    const FIELD_VARIANT_COUNT      = 'variant_count'; // Prefix with variant, in case we have another count
    const FIELD_PRIORITY_VARIANT_COUNT = 'priority_variant_count';

    const FIELD_VARIANT_ATTRIBUTES      = 'attributes';
    const FIELD_VARIANT_ATTRIBUTE_KEY   = 'key';
    const FIELD_VARIANT_ATTRIBUTE_VALUE = 'value';
    const FIELD_VARIANT_ATTRIBUTE_GROUP = 'group';

    const ALGOLIA_ATTRIBUTE_VALUE = 'value';
    const ALGOLIA_ATTRIBUTE_GROUP = 'group';

    /**
     * Those section are duplicate between default product + variant.
     * Since the name is the same, we prefix it with "variant_" and remove it later when we process it
     */
    const MYSQL_VARIANT_PRICE         = 'variant_price';
    const MYSQL_VARIANT_SALE_PRICE    = 'variant_sale_price';
    const MYSQL_VARIANT_OLD_PRICE     = 'variant_old_price';
    const MYSQL_VARIANT_REGULAR_PRICE = 'variant_regular_price';

    const MYSQL_VARIANT_NAME             = 'variant_name';
    const MYSQL_VARIANT_DESCRIPTION      = 'variant_description';
    const MYSQL_VARIANT_AVAILABLE        = 'variant_available';
    const MYSQL_VARIANT_PRODUCT_URL      = 'variant_product_url';
    const MYSQL_VARIANT_IMAGE_URL        = 'variant_image_url';
    const MYSQL_VARIANT_DEAL_START_DATE  = 'variant_deal_start_date';
    const MYSQL_VARIANT_DEAL_END_DATE    = 'variant_deal_end_date';
    const MYSQL_VARIANT_PUBLICATION_DATE = 'variant_publication_date';
    const MYSQL_VARIANT_BRAND            = 'variant_brand';

    const ALGOLIA_MAX_SIZE = '102400';

    // This is a extra buffer size we will remove when reaching limit for safety.
    const ALGOLIA_MAX_SIZE_BUFFER = 100;

    /**
     * Group concat default limit is 1024. This is not enough for us. Bump it.
     * If we reach the max size of packet ( 4194304 ) we will need to make multiple queries for variants
     */
    const MAX_LENGTH_GROUP_CONCAT = 102400;

    const MAX_LEVEL_RECURSION_CATEGORIES = 10; // Currently we are at 4

    /** @var Configs $configs */
    protected $configs;
    /** @var LoggerInterface $logger */
    protected $logger;
    /** @var LoggerInterface $successLogger */
    protected $successLogger;
    /** @var MySQLRepository $repository */
    protected $repository;
    /** @var boolean $multibrand */
    protected $multibrand;
    /** @var string $brand */
    protected $brand;
    /** @var string $env */
    protected $env;
    /** @var string $algoliaIndexName */
    protected $algoliaIndexName;
    /** @var array $priceRanges */
    protected $priceRanges;

    /** @var ProductVariants $productVariantsManager */
    protected $productVariantsManager;

    /**
     * @var CloudinaryImage $cloudinaryImage
     */
    protected $cloudinaryImage;

    /**
     * Variant + dynamic attributes are only know after being indexed when full reindex.
     * If it's an update() (partial), we will query MySQL to get all the attributes name
     *
     * @var array $dynamicFacets
     */
    protected $dynamicFacets = [];

    /**
     * Store in memory (memoize) to speed up performance
     *
     * @var array $storedCategoriesPerProduct
     */
    private $storedCategoriesPerProduct = [];

    /**
     * Store in memory categoryTree custom structure.
     * Let's move to recursive logic since we have now 4 level of categories.
     *
     * @var array $storeCategoriesTree
     */
    protected $storedCategoriesTree = [];

    /**
     * Store in momery (memoize) to speed up performance
     *
     * @var array $storedVariantAttributes
     */
    private $storedVariantAttributes = [];

    /**
     * Store in memory (memoize) to speed up performance
     *
     * @var array $storedRetailerIds
     */
    private $storedRetailerIds = [];

    /** @var integer $algoliaBulkSize */
    private $algoliaBulkSize; // 100kb
    /** @var bool $isNewRetailer */
    private $isNewRetailer;

    /** @var SearchClient $algolia */
    private $algolia;

    /**
     * Base constructor.
     *
     * @param Configs $configs
     * @param LoggerInterface $logger
     * @param LoggerInterface $success
     * @param MySQLRepository $repository
     *
     * @param ProductVariants $productVariantsManager
     * @throws Exception
     */
    public function __construct(
        Configs $configs,
        LoggerInterface $logger,
        LoggerInterface $success,
        MySQLRepository $repository,
        ProductVariants $productVariantsManager,
        CloudinaryImage $cloudinaryImage
    ) {
        $this->configs                = $configs;
        $this->logger                 = $logger;
        $this->repository             = $repository;
        $this->successLogger          = $success;
        $this->cloudinaryImage        = $cloudinaryImage;
        $this->multibrand             = $this->configs['multibrand.is_active'];
        $this->brand                  = $this->configs['retailer.brand'];
        $this->env                    = $this->configs['env'];
        $this->isNewRetailer          = $this->configs['algolia.newretailer'];
        $this->algoliaIndexName       = $this->getIndexName(true);
        $this->priceRanges            = $this->configs['algolia.filters.prices.options'];
        $this->algoliaBulkSize        = $this->configs['algolia.recordsbybatch'];
        $this->productVariantsManager = $productVariantsManager;

        $this->algolia = SearchClient::create(
            $this->configs['algolia.admin.app_id'],
            $this->configs['algolia.admin.api_key']
        );
    }

    /**
     * Algolia index cron job update needs to be run based on the following rules:
     * - If it's prod we do it. We always want index up to date.
     * - Otherwise we check if we skip the update on purpose (used by unit tests)
     * - We generate/update index only for new retailers. For retailers that are already in production, we don't
     *   generate index in dev, stg, qa, we use the production index for our tests.
     *
     * Copied from previous AlgoliaIndexer
     *
     * @return boolean
     */
    public function indexNeedsToBeUpdated()
    {
        return ($this->env == 'prd' || $this->isNewRetailer);
    }

    /**
     * Reindex all the product data via a temporary index
     *
     * @throws \AlgoliaSearch\AlgoliaException
     */
    public function reindex()
    {
        // Generate a temporary index name
        $tempIndexName = $this->getTempIndexName();
        // Init the temporary index
        $newIndex = $this->algolia->initIndex($tempIndexName);
        // Init the current index
        $currentIndex = $this->algolia->initIndex($this->algoliaIndexName);

        $this->prepareIndex($newIndex);
        $total = $this->resetIndex($newIndex);

        // Since attributes on variant are dynamic (i don't know what they are until we process them)
        // We need to update the structure after the import with those new processed field
        $this->updateDynamicFacets($newIndex);

        $this->algolia->moveIndex($tempIndexName, $this->getIndexName())->wait();

        // Get current index' settings
        $currentIndexSettings = $currentIndex->getSettings();
        // Check if the index has no virtual replicas
        if (empty($currentIndexSettings['replicas'])) {
            // Create the virtual replicas
            $currentIndex->setSettings([
                'replicas' => [
                    'virtual(' . $this->getIndexName() . '_price_asc)',
                    'virtual(' . $this->getIndexName() . '_price_desc)',
                    'virtual(' . $this->getIndexName() . '_arrival_date_asc)',
                    'virtual(' . $this->getIndexName() . '_arrival_date_desc)',
                ]
            ]);
            $this->prepareVirtualReplicas($currentIndex);
        }

        $this->successLogger->info(
            sprintf(
                "Algolia reindex completed. Total record uploaded %s",
                $total
            )
        );
    }

    /**
     * Update index based on sf_table_diff
     *
     * @throws \AlgoliaSearch\AlgoliaException
     */
    public function update()
    {
        $currentIndex = $this->algolia->initIndex($this->algoliaIndexName);

        $this->prepareIndex($currentIndex);

        $this->updateIndex($currentIndex);

        // When we update, we need to extract the properties from database, because
        // we only a part of the data, so if it's based only on data we processed we will miss some information
        if ($this->isExtendedVariantEnabled()) {
            // Get all attributes name from DB since we are updating only a small subset
            array_map(function ($item) {
                $this->addToDynamicFacet(implode('.', [
                    implode('_', array_filter([
                        static::FIELD_VARIANTS,
                        $this->isI18nEnabled() ? "{locale}" : null,
                    ])),
                    static::FIELD_VARIANT_ATTRIBUTES,
                    $item,
                    static::ALGOLIA_ATTRIBUTE_GROUP,
                ]));
            }, $this->productVariantsManager->getAttributeNames());

            $this->updateDynamicFacets($currentIndex);
        }
    }

    /**
     * General settings for algolia
     *
     * @param SearchIndex $index
     * @return void
     */
    public function prepareIndex(SearchIndex $index)
    {
        /**
         * This was copied from previous AlgoliaIndexer
         */

        $settings = [
            // Keep only results with the lowest number of typos, e.g. if
            // there was an exact match, don't return any results with
            // typos.
            'typoTolerance'                    => 'min',
            // Remove very common words from the query ('the', 'a', ...).
            'removeStopWords'                  => true,
            // Attributes searchable via a free form text search, in order
            // of importance.
            'searchableAttributes'             => $this->getAttributes(),
            // Order of attributes for faceting is of no importance, unlike
            // attributesToIndex.
            'attributesForFaceting'            => $this->getFacets(),
            "customRanking"                    => ["desc(available)", "desc(publication_date)"],
            // Consider the plural and singular form of a word an exact
            // match rather than say a 1 character typo. For instance,
            // 'handbag' and 'handbags' would count as an exact match.
            'ignorePlurals'                    => true,
            // The max facet values to display. Set higher than the default
            // 100 because certain retailers will, for instance, have a
            // large amount of possible values for the brand (name2).
            'maxValuesPerFacet'                => 100000,
            // We only want exact matches to return results for SKU search,
            // disable typo tolerance.
            'disableTypoToleranceOnAttributes' => ['retailer_sku', 'product_id'],
            'ranking'                          => [
                'typo',
                'filters',
                'attribute',
                'proximity',
                'exact',
                'custom'
            ],
        ];

        // This is mandatory, otherwise, you will get a lot of duplicate (for each sku)
        if ($this->isExtendedVariantEnabled()) {
            $settings = array_merge($settings, [
                'distinct'             => 1,
                'attributeForDistinct' => static::FIELD_PRODUCT_ID,
                'disableTypoToleranceOnAttributes' => ['retailer_sku', 'product_id', 'variants.sku'],
            ]);
        }

        $index->setSettings($settings);
    }

    /**
     * Virtual replicas settings
     *
     * @param SearchIndex $index
     * @return void
     */
    public function prepareVirtualReplicas(SearchIndex $index)
    {
        $replica_price_asc = $this->algolia->initIndex($index->getIndexName() . '_price_asc');
        $replica_price_asc->setSettings(["customRanking" => ["asc(price)"], 'relevancyStrictness' => $this->configs['algolia.relevancy_strictness']]);

        $replica_price_desc = $this->algolia->initIndex($index->getIndexName() . '_price_desc');
        $replica_price_desc->setSettings(["customRanking" => ["desc(price)"], 'relevancyStrictness' => $this->configs['algolia.relevancy_strictness']]);

        $replica_arrival_date_asc = $this->algolia->initIndex($index->getIndexName() . '_arrival_date_asc');
        $replica_arrival_date_asc->setSettings(["customRanking" => ["asc(publication_date)"], 'relevancyStrictness' => $this->configs['algolia.relevancy_strictness']]);

        $replica_arrival_date_desc = $this->algolia->initIndex($index->getIndexName() . '_arrival_date_desc');
        $replica_arrival_date_desc->setSettings(["customRanking" => ["desc(publication_date)"], 'relevancyStrictness' => $this->configs['algolia.relevancy_strictness']]);
    }

    /**
     * Generic function used to add to attributes/facets
     *
     * If we want to customized facet via the onlyFilter() we will need to split attributes/facets to own function
     *
     * @param array  $attributes Array of all attributes
     * @param string $attribute  Specific attribute you want to append
     * @param bool   $ordered    If false, will prefix the attribute with "unordered()". Don't use that for facet
     */
    public function addSettingToAttributes(&$attributes, $attribute, $ordered = true)
    {
        array_push(
            $attributes,
            ($ordered ? $attribute : $this->addUnorderedAttribute($attribute))
        );
    }

    /**
     * Add attributes/facets for each locale too
     *
     * @param array  $attributes
     * @param string $attribute
     * @param bool   $ordered
     */
    protected function addI18nSettingToAttributes(array &$attributes, $attribute, $ordered = true)
    {
        $this->addSettingToAttributes($attributes, $attribute, $ordered);

        if ($this->configs['retailer.i18n.is_enabled']) {
            foreach ($this->configs['sf.i18n.locales'] as $locale) {
                $i18nAttribute = $attribute . '_' . $locale;
                $this->addSettingToAttributes($attributes, $i18nAttribute, $ordered);
            }
        }
    }

    /**
     * Reused the attributes section but remove any "ordered/unordered()" from it before sending it to mobile
     * Those "restrictSearchableAttributes" seems to be used only during querying not filtering
     *
     * @return array
     */
    public function getSearchableAttributes()
    {
        return array_map(function ($item) {
            $matches  = [];
            $hasMatch = preg_match("/\((.*)\)/", $item, $matches);

            if ($hasMatch) {
                return $matches[1];
            } else {
                return $item;
            }
        }, $this->getAttributes());
    }

    /**
     * Get desired algolia index name for product.
     *
     * indices versions:
     *     no version :    product index without variant feature
     *     V1:             product index with variant feature
     *
     * @param bool $write
     *
     * @return string
     */
    public function getIndexName($write = false)
    {
        $getEnv = function () use ($write) {
            $algoliaEnv = $this->env;

            if ($algoliaEnv !== 'prd' && !$this->isNewRetailer && !$write) {
                $algoliaEnv = 'prd';
            }

            return $algoliaEnv;
        };

        return implode('_', array_filter([
            $this->configs['retailers.current'],
            $this->brand,
            $this->isExtendedVariantEnabled() ? 'V1' : '',
            $getEnv(),
        ]));
    }

    /**
     * Copy index named $src to $dest.
     *
     * This is typically only done when setting up a retailer's account.
     */
    public function copyIndex($src, $dest)
    {
        $this->algolia->copyIndex($src, $dest);
    }

    /**
     * Delete the given Algolia Index.
     *
     * This is typically only done when shutting down a retailer's account.
     */
    public function deleteIndex($indexName)
    {
        return $this->algolia->initIndex($indexName)->delete();
    }


    /**
     * Get thumbnail url for product image
     *
     * @param $url
     * @param $width
     *
     * @return string|string[]|null
     */
    protected function getThumbnailUrl($url, $width)
    {
        // Cloudinary can't proxy images with URLS longer than 255 characters
        // https://support.cloudinary.com/hc/en-us/articles/*********-Why-do-I-get-a-public-ID-too-long-error-when-trying-to-fetch-from-a-remote-URL-
        if (strlen($url) > 255) {
            return $url;
        }

        return $this->cloudinaryImage->getProxyUrl($url, [
            "width"        => $width,
            "sign_url"     => true,
            "fetch_format" => "auto",
            "quality"      => 90,
        ]);
    }

    /**
     * Since multibrand product are splitted by brand, we need to filter when we are in multibrand mode
     *
     * @param QueryBuilder $qB
     * @param              $alias
     *
     * @return string
     */
    protected function getBrandRestriction(QueryBuilder $qB, $alias)
    {
        if ($this->multibrand && !empty($this->brand)) {
            return " AND $alias.brand = " . $qB->expr()->literal($this->brand);
        }

        return '';
    }

    /**
     * When attribute is unordered (e.g: description)
     *
     * @param $attribute
     *
     * @return string
     */
    protected function addUnorderedAttribute($attribute)
    {
        return "unordered($attribute)";
    }

    /**
     * Populate the attributes array that will be send to algolia
     *
     * @return mixed
     */
    abstract protected function getAttributes();

    /**
     * Populate the facets array that will be send to algolia
     *
     * @return mixed
     */
    abstract protected function getFacets();

    /**
     * Take one row (product/variant) - sanitize/modified it - return array ready for aloglia
     *
     * @param array $product
     *
     * @return mixed
     */
    abstract protected function processProduct(array $product);

    /**
     * Copied from previous AlgoliaIndexer
     *
     * @param $price
     *
     * @return int|string|null
     */
    protected function getPriceRanges($price)
    {
        $priceRange = null;

        foreach ($this->priceRanges as $range => $val) {
            if (strpos($range, '-')) {
                $minMax   = explode('-', $range);
                $minPrice = $minMax[0];
                $maxPrice = $minMax[1];
            } else {
                $minPrice = str_replace('>', '', $range);
                $maxPrice = -1;
            }

            if (($maxPrice > -1 && $price >= $minPrice && $price < $maxPrice) || ($maxPrice == -1 && $price >= $minPrice)) {
                $priceRange = $range;
                break;
            }
        }

        return $priceRange;
    }

    /**
     * This is not perfect, because it's including bracket and comma, but it's helpful to know if we are close to the
     * limit 100kb.
     *
     * Seems like this function return 20-25% bigger size than the real size (returned by algolia)
     *
     * @param array $current
     *
     * @return int
     */
    protected function getSizeOfArray(array $current)
    {
        return mb_strlen(json_encode($current, JSON_NUMERIC_CHECK), '8bit');
    }

    /**
     * Add special field to the dynamic list of facets
     *
     * @param $field
     */
    protected function addToDynamicFacet($field)
    {
        if (!in_array($field, $this->dynamicFacets)) {
            $this->dynamicFacets[] = $field;
        }
    }

    protected function isExtendedVariantEnabled()
    {
        return (bool)$this->configs['products.expanded_variants.enabled'];
    }

    protected function isI18nEnabled()
    {
        return (bool)$this->configs['retailer.i18n.is_enabled'];
    }

    /**
     * Since we loop "per product" via bulk() function, we MUST have one entity per row.
     *
     * This let you the flexibility to change it in child class if necesary.
     *
     * @param QueryBuilder $queryBuilder
     */
    protected function addGroupBy(QueryBuilder $queryBuilder)
    {
        if (!$this->isExtendedVariantEnabled()) {
            $queryBuilder->groupBy('p.product_id');
        } else {
            // If you use one query (with multiple group concat) you MUST use group by
            // In some case, distinct is quicker, but i managed to make it both ok performance-wise
            $queryBuilder->groupBy('pv.sku');
        }
    }

    /**
     * Add variant information if enabled.
     *
     * This is as the v2 since we store 'variant' and not product.
     * We don't need a group concat on variant, but a new row.
     *
     * @param QueryBuilder $queryBuilder
     */
    protected function addVariants(QueryBuilder $queryBuilder)
    {
        if (!$this->isExtendedVariantEnabled()) {
            return;
        }

        /**
         * We can't use a group concat anymore, because if we want to use the core (getProducts)
         * that loop over each product, we need to change the query to make a new row for each variant
         */

        $selectVariant = sprintf(
            "pv.sku as %s, pv.name as %s, pv.description as %s, pv.available as %s, pv.product_url %s,
             pv.image_url as %s, pv.price as %s, pv.price_deal as %s, pv.price_old as %s,
             pv.deal_start_date as %s, pv.deal_end_date as %s,
             DATE_FORMAT(pv.arrival_date, '%%Y%%m%%d') as %s, pv.gtin as %s, pv.brand as %s",
            static::FIELD_VARIANT_SKU,
            static::MYSQL_VARIANT_NAME,
            static::MYSQL_VARIANT_DESCRIPTION,
            static::MYSQL_VARIANT_AVAILABLE,
            static::MYSQL_VARIANT_PRODUCT_URL,
            static::MYSQL_VARIANT_IMAGE_URL,
            static::MYSQL_VARIANT_PRICE,
            static::MYSQL_VARIANT_SALE_PRICE,
            static::MYSQL_VARIANT_OLD_PRICE,
            static::MYSQL_VARIANT_DEAL_START_DATE,
            static::MYSQL_VARIANT_DEAL_END_DATE,
            static::MYSQL_VARIANT_PUBLICATION_DATE, // Keep same structure as product
            static::FIELD_VARIANT_GTIN, // Since this is specific for variant (No needs of the prefix hack)
            static::MYSQL_VARIANT_BRAND
        );

        $queryBuilder
            ->addSelect($selectVariant)
            ->leftJoin('p', 'sf_product_variants', 'pv', 'p.product_id = pv.product_id')
            // Only index if variant is available (Not only default product)
            ->andWhere($queryBuilder->expr()->eq('pv.available', 1));

        // Save also the total number of variant for this product
        $queryBuilder
            ->addSelect("count(pv_count.sku) as :variant_count")
            ->leftJoin('p', 'sf_product_variants', 'pv_count', $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('p.product_id', 'pv_count.product_id')
                // Count even if not available, since we display all of them
            ))
            ->setParameter(
                'variant_count',
                self::FIELD_VARIANT_COUNT
            );

        // At the moment, we never use it "with attributes" but the initial goal was to do everything
        // in one query. Sadly, it was too slow in dev and it wasn't acceptable

        // The attributes are now populated in preprocessProduct()

        if ($this->configs['products.expanded_variants.priority_badge.enabled'] === true) {
            $this->addPriorityVariantCount($queryBuilder);
        }
    }

    /**
     * Check how many distinct attribute set for each product, front-end need this value in product list UI
     *
     * For example: product_xxx     -> variant_1 -> color:red ,     size: small
     *                              -> variant_2 -> color:red ,     size: large
     *                              -> variant_3 -> color:green ,   size: x-large
     *
     * priority_variant_count(position:1 as default) is 2
     *
     * @note All product variant must have same order of attribute, this is not perfect and should refactoring later
     * @param QueryBuilder $queryBuilder
     */
    protected function addPriorityVariantCount(QueryBuilder $queryBuilder)
    {
        $attrCountTableAlias = 'temp_product_attributes_count';
        $subQuery            = $this->repository->getQueryBuilder();

        $subQuery
            ->select([
                "COUNT(DISTINCT(IF(attr2.position = :priority_variant_count_position, attr2.value, null))) AS :priority_variant_count",
                'pv2.product_id'
            ])
            ->from('sf_product_variants', 'pv2')
            ->leftJoin('pv2', 'sf_product_variant_attributes', 'attr2', 'pv2.sku = attr2.sku')
            ->where('pv2.available = 1')
            ->groupBy('product_id');

        $queryBuilder
            ->leftJoin('p', sprintf('(%s)', $subQuery->getSQL()), $attrCountTableAlias, "p.product_id = $attrCountTableAlias.product_id")
            ->addSelect("$attrCountTableAlias.priority_variant_count AS :priority_variant_count")
            ->setParameter(
                'priority_variant_count_position',
                $this->configs['products.expanded_variants.priority_badge.position'] ?? 1,
                ParameterType::INTEGER
            )
            ->setParameter(
                'priority_variant_count',
                self::FIELD_PRIORITY_VARIANT_COUNT
            );
    }

    /**
     * This is a special check for group concat to know if we reached the limit. We don't really need it anymore,
     * since we split variant per record, but keep it just in case we have edge-case.
     *
     * @param $name
     * @param $value
     *
     * @return bool
     */
    protected function isGroupConcatFieldTooBig($name, $value)
    {
        // group_concat doesn't care about multi-byte
        // If you see this message in slack, you will probably need to increase MAX_LENGTH_GROUP_CONCAT size
        if (strlen($value) >= self::MAX_LENGTH_GROUP_CONCAT) {
            $this->logger->critical(
                sprintf(
                    "This field [%s] with value [%s] is equal to max length group concat [%s]. We will drop this field :(",
                    $name,
                    $value,
                    self::MAX_LENGTH_GROUP_CONCAT
                )
            );
            return true;
        }

        return false;
    }

    /**
     * Decided if the product or the variant is on sale
     *
     * @param      $product
     * @param bool $isVariant
     *
     * @return bool
     */
    protected function isOnSale($product, $isVariant = false)
    {
        $salePrice    = $isVariant ? $product[static::MYSQL_VARIANT_SALE_PRICE] : $product[static::FIELD_SALE_PRICE];
        $currentPrice = $isVariant ? $product[static::MYSQL_VARIANT_PRICE] : $product[static::FIELD_PRICE];

        $isOnSale = false;
        if (!empty($salePrice) && $salePrice < $currentPrice) {
            $isOnSale = true;
        }

        // Because we want 0-1 stored in algolia
        return (int)$isOnSale;
    }

    /**
     * Get the display price for a product/variant
     *
     * @param      $product
     * @param bool $isVariant
     *
     * @return mixed
     */
    protected function getDisplayPrice($product, $isVariant = false)
    {
        if ($this->isOnSale($product, $isVariant)) {
            if ($isVariant) {
                return $product[static::MYSQL_VARIANT_SALE_PRICE];
            } else {
                return $product[static::FIELD_SALE_PRICE];
            }
        } else {
            if ($isVariant) {
                return $product[static::MYSQL_VARIANT_PRICE];
            } else {
                return $product[static::FIELD_PRICE];
            }
        }
    }

    /**
     * Keep it as separated function in case we want to do it in the future in only one query
     *
     * @param QueryBuilder $queryBuilder
     */
    private function addRetailerIds(QueryBuilder $queryBuilder)
    {
        $selectRetailerIds = sprintf(
            "group_concat(DISTINCT pri.retailer_id SEPARATOR '%s') as %s",
            static::SEPARATOR_PRODUCT_RETAILER_IDS,
            static::FIELD_RETAILER_SKU
        );

        $queryBuilder
            ->addSelect($selectRetailerIds)
            ->leftJoin(
                'p',
                'sf_product_retailer_ids',
                'pri',
                'p.product_id = pri.product_id'
            );
    }

    /**
     * Generic function to loop over each product
     *
     * @param string $type
     *
     * @return \Generator
     */
    private function getProducts($type = null)
    {
        // Because of group_concat (Default is 1024)
        // Variant can get pretty high, if it get's too high and we have performance issue
        // we may need to split them in multiple queries
        // This value, can't go higher than 'max_allowed_packet'
        $this->repository->executeRawQuery(
            sprintf(
                "SET SESSION group_concat_max_len = %s;",
                self::MAX_LENGTH_GROUP_CONCAT
            )
        );

        /** @var QueryBuilder $qB */
        $qB = $this->getProductQueryBuilder();

        $mysqlFlagUpdated = $this->repository->disableOnlyFullGroupBy();

        switch ($type) {
            case TablesDiff::ACTION_INSERT:
            case TablesDiff::ACTION_UPDATE:
                $qB
                    ->innerJoin(
                        'p',
                        'sf_table_diff',
                        'diff',
                        'p.product_id = diff.product_id AND diff.type = :type'
                    )
                    ->setParameter('type', $type);
                break;
            case TablesDiff::ACTION_DELETE:
                $qB = $this->repository->getQueryBuilder();
                $qB
                    ->select('product_id')
                    ->from('sf_table_diff')
                    ->where($qB->expr()->eq('type', $qB->expr()->literal(TablesDiff::ACTION_DELETE)));
                break;
        }

        $stmt = $qB->execute();

        $this->repository->enableOnlyFullGroupBy($mysqlFlagUpdated);

        while ($row = $stmt->fetch()) {
            yield $row;
        }
    }

    /**
     * General query to get product information
     *
     * @return QueryBuilder
     */
    private function getProductQueryBuilder()
    {
        /** @var QueryBuilder $queryBuilder */
        $queryBuilder = $this->repository->getQueryBuilder();

        $queryBuilder
            ->select(
                'p.product_id as :product_id',
                'p.name as :name',
                'p.name2 as :brand',
                'p.description as :description',
                'p.price as :price',
                'p.price_deal as :price_deal',
                'p.price_old as :price_old',
                'p.deal_start_date as :start_date',
                'p.deal_end_date as :end_date',
                'p.available as :available',
                'p.retailer_sku as :default_sku',
                'DATE_FORMAT(p.arrival_date, \'%Y%m%d\')  as :arrival_date',
                'img.url as :image_url',
                'siteurl.url as :product_url'
            )
            ->from('sf_products', 'p')
            ->innerJoin('p', 'sf_images', 'img', 'p.product_id = img.product_id')
            ->innerJoin(
                'p',
                'sf_siteurl',
                'siteurl',
                'p.product_id = siteurl.product_id'
            )
            // If we don't filter product in the wrong brand now, the preProcessProduct() won't find the category linked
            // to it, since we only gather category for the current brand
            // No need to look at i18n table, because it's only to get proper subset of products.
            ->innerJoin(
                'p',
                'sf_product_category_map',
                'cm',
                'p.product_id = cm.product_id' . $this->getBrandRestriction($queryBuilder, 'cm')
            )
            ->innerJoin(
                'cm',
                'sf_categories',
                'c',
                'cm.category_id = c.category_id' . $this->getBrandRestriction($queryBuilder, 'c')
            )
            ->setParameters([
                'product_id'   => static::FIELD_PRODUCT_ID,
                'name'         => static::FIELD_NAME,
                'brand'        => static::FIELD_BRAND,
                'description'  => static::FIELD_DESCRIPTION,
                'price'        => static::FIELD_PRICE,
                'price_deal'   => static::FIELD_SALE_PRICE,
                'price_old'    => static::FIELD_OLD_PRICE,
                'start_date'   => static::FIELD_DEAL_START_DATE,
                'end_date'     => static::FIELD_DEAL_END_DATE,
                'available'    => static::FIELD_AVAILABLE,
                'arrival_date' => static::FIELD_PUBLICATION_DATE,
                'image_url'    => static::FIELD_IMAGE_URL,
                'product_url'  => static::FIELD_PRODUCT_URL,
                'default_sku'  => static::FIELD_DEFAULT_SKU,
            ])
            ->where($queryBuilder->expr()->eq('p.available', 1))
            ->orderBy('p.product_id', 'ASC'); // This is not necessary but useful during test

        $this->updateQueryBuilder($queryBuilder);

        $this->addGroupBy($queryBuilder);

        $this->getMainProductsI18n($queryBuilder);

        return $queryBuilder;
    }

    private function addToQueryBuilderI18n(callable $function)
    {
        if ($this->isI18nEnabled()) {
            foreach ($this->configs['sf.i18n.locales'] as $locale) {
                if ($locale != $this->configs['retailer.i18n.default_locale']) {
                    $function($locale);
                }
            }
        }
    }

    /**
     * Update main default query based on variable/config
     *
     * @param QueryBuilder $queryBuilder
     */
    private function updateQueryBuilder(QueryBuilder $queryBuilder)
    {
        if ($this->isExtendedVariantEnabled()) {
            // Not with the attributes, because of the distinct + group_concat
            $this->addVariants($queryBuilder);
        }
    }

    /**
     * Update QueryBuilder with data from sf_X_i18n so we can add them in algolia
     *
     * @param QueryBuilder $queryBuilder
     *
     * @return QueryBuilder
     */
    private function getMainProductsI18n(QueryBuilder $queryBuilder)
    {
        if (!$this->isI18nEnabled()) {
            return;
        }

        foreach ($this->configs['sf.i18n.locales'] as $locale) {
            // I'm not sure what's the best since we always return with locale structure
            // Keep this structure for now until if find something better (will do the merge later)
            if ($locale != $this->configs['retailer.i18n.default_locale']) {
                $productAliasTable  = 'sf_products_' . $locale;
                $imagesAliasTable   = 'sf_images_' . $locale;
                $siteurlsAliasTable = 'sf_siteurl_' . $locale;

                // We clean up products when they are not in all locale
                // No need to support the "fallback" to main language afaik

                $queryBuilder->addSelect($productAliasTable . '.name as name_' . $locale);
                $queryBuilder->addSelect($productAliasTable . '.description as description_' . $locale);
                $queryBuilder->addSelect($imagesAliasTable . '.url as image_url_' . $locale);
                $queryBuilder->addSelect($siteurlsAliasTable . '.url as product_url_' . $locale);

                $queryBuilder->innerJoin(
                    'p',
                    'sf_products_i18n',
                    $productAliasTable,
                    "$productAliasTable.product_id = p.product_id AND $productAliasTable.locale='$locale'"
                );
                $queryBuilder->innerJoin(
                    'img',
                    'sf_images_i18n',
                    $imagesAliasTable,
                    "$imagesAliasTable.product_id = p.product_id AND $imagesAliasTable.locale='$locale'"
                );
                $queryBuilder->innerJoin(
                    'siteurl',
                    'sf_siteurl_i18n',
                    $siteurlsAliasTable,
                    "$siteurlsAliasTable.product_id = p.product_id AND $siteurlsAliasTable.locale='$locale'"
                );

                if ($this->isExtendedVariantEnabled()) {
                    $variantAliasTable = 'sf_product_variants_' . $locale;

                    $handleLocale = function ($field, $as) use ($variantAliasTable, $locale) {
                        return sprintf(
                            "%s.%s as %s",
                            $variantAliasTable,
                            $field,
                            $as . "_" . $locale
                        );
                    };

                    /**
                     * Afaik, inner join i18n table is fine because we delete product that are not translated
                     * in all language. (for now). If needed, we could left join + coalesce to get default value
                     */
                    $queryBuilder
                        // SKU is not a i18n, but we use this later on to populate data
                        ->addSelect($handleLocale('sku', static::FIELD_VARIANT_SKU))
                        ->addSelect($handleLocale('name', static::MYSQL_VARIANT_NAME))
                        ->addSelect($handleLocale('description', static::MYSQL_VARIANT_DESCRIPTION))
                        ->addSelect($handleLocale('image_url', static::MYSQL_VARIANT_IMAGE_URL))
                        ->addSelect($handleLocale('product_url', static::MYSQL_VARIANT_PRODUCT_URL))
                        ->innerJoin(
                            'p',
                            'sf_product_variants_i18n',
                            $variantAliasTable,
                            $queryBuilder->expr()->andX(
                                $queryBuilder->expr()->eq('pv.sku', "$variantAliasTable.sku"),
                                $queryBuilder->expr()->eq(
                                    "$variantAliasTable.locale",
                                    $queryBuilder->expr()->literal($locale)
                                )
                            )
                        );
                }
            }
        }

        return $queryBuilder;
    }

    /**
     * Update index based on sf_table_diff content
     *
     * @param SearchIndex $index
     * @return void
     */
    private function updateIndex(SearchIndex $index)
    {
        $this->logger->info(
            sprintf(
                "Update index [%s] in progress ...",
                $index->getIndexName()
            )
        );

        $totalInsert = $this->addInsertedProduct($index);
        $totalUpdate = $this->addUpdatedProduct($index);
        $totalDelete = $this->removeDeletedProduct($index);

        $this->successLogger->info(
            sprintf(
                "Algolia update index [%s] completed. Inserted [%s] Updated [%s] Deleted [%d]",
                $this->algoliaIndexName,
                $totalInsert,
                $totalUpdate,
                $totalDelete
            )
        );
    }

    /**

     *
     * @param array $obj
     * @param int $position
     * @param bool $last
     *
     * @return array
     */

    /**
     * Trying to "resizeDown" the object we try to save in algolia since it has a 100kb limitation per obj and 10kb
     * max avg. Currently, avg is around 2.5kb for macys.
     *
     * The logic is:
     * - based on the estimate, resize down description (main & variant)
     * - if estimate not provided, cut in half description (main & variant)
     * - if on the last attempt still not working, we remove the object from bulk obj
     *
     * @param array $current        The current object that failed to insert based on algolia exception
     * @param int|null $estimate    The size we need to remove based on algolia exception
     *
     * @return array
     */
    private function resizeDown(array $current, ?int $estimate): array
    {
        // The idea behind buffer is to handle the "..." we will add.
        if (!empty($estimate)) {
            $buffer = self::ALGOLIA_MAX_SIZE_BUFFER;
            $estimate += $buffer;
        }

        $resize = function ($field) use ($estimate) {
            $newField = $field;
            if (!empty($field)) {
                $length = mb_strlen($field);
                $newField = mb_substr($field, 0, $estimate ? -$estimate : intdiv($length, 2));

                if (!empty($newField)) {
                    $newField .= '...';
                }

                $this->logger->debug(
                    sprintf(
                        "We resized down the field [%s] from [%s] to [%s]",
                        $field,
                        $length,
                        mb_strlen($newField)
                    )
                );
            }

            return $newField;
        };

        // Most of the time, the culprit of the "too big" exception is the description. (e.g: macys)
        if (!empty($current[self::FIELD_DESCRIPTION])) {
            $current[self::FIELD_DESCRIPTION] = $resize($current[self::FIELD_DESCRIPTION]);
        }

        if (!empty($current[self::FIELD_VARIANTS][self::FIELD_DESCRIPTION])) {
            $current[self::FIELD_VARIANTS][self::FIELD_DESCRIPTION] = $resize($current[self::FIELD_VARIANTS][self::FIELD_DESCRIPTION]);
        }

        return $current;
    }

    /**
     * Generic function to process list of data (products) by bulk
     *
     * @param \Generator $products
     * @param Index      $index
     * @param callable   $process
     * @param callable   $action
     *
     * @return int
     */
    private function bulk(\Generator $products, SearchIndex $index, callable $process, callable $action)
    {
        // Nb of object inserted in algolia
        $total = 0;
        // Array of records that will be saved into algolia in bulk
        $obj = [];

        // Max attempt before throwing exception on this bulk
        // If we have more than 4 products too big in this bulk, we won't have enough attempt to clean them and it will crash.
        $maxRetry = 12;

        // It's important to use $obj as reference here, otherwise it will always be empty.
        $executeAction = function ($retry = 1) use ($action, &$obj, &$executeAction, $maxRetry) {
            try {
                if ($retry > $maxRetry) {
                    $this->logger->error(
                        sprintf(
                            "You have reached the limit of retry of [%s] for bulk obj [%s]. This bulk will not be part of algolia.",
                            $maxRetry,
                            print_r($obj, true)
                        )
                    );

                    throw new AlgoliaIndexerException("Algolia indexer reached the max amount of retries for this bulk");
                }

                return $this->retry($action, $obj);
            } catch (BadRequestException $e) {
                $this->logger->error($e);

                $matches = [];

                preg_match('/.* position (\d+) .* size\=(\d+)\/(\d+) .*/', $e->getMessage(), $matches);

                $position = isset($matches[1]) ? (int)$matches[1] : null;
                $size = isset($matches[2]) ? (int)$matches[2] : null;
                $limit = isset($matches[3]) ? (int)$matches[3] : null;

                if (isset($position) && is_numeric($position)) {
                    $current = $obj[$position] ?? null;
                    if (!empty($current)) {
                        if ($retry % 3 != 0) {
                            $obj[$position] = $this->resizeDown($current, ($size && $limit) ? ($size - $limit) : null);
                        } else {
                            array_splice($obj, $position, 1);
                        }

                        return $executeAction(++$retry);
                    } else {
                        $this->logger->error(
                            sprintf(
                                "The position [%s] isn't part of the obj. Length [%s]. This should not happen",
                                $position,
                                count($obj)
                            )
                        );
                    }
                }

                // Force rethrow
                throw $e;
            } catch (Exception $e) {
                // It's not clear why, but something algolia crash because of malformed JSON.
                $this->logger->error(
                    sprintf(
                        "Algolia bulk failed [%s] with obj [%s]",
                        $e->getMessage(),
                        // Don't json_encode it because at the moment, most of the error is "malformed json"
                        print_r($obj, true)
                    )
                );

                $this->logger->error($e);

                throw $e;
            }
        };

        foreach ($products as $product) {
            $current = $process($product);

            // Object is not part of algolia on update/delete, skip
            if (empty($current)) {
                continue;
            }

            $current = $this->adjustRecordSize($current);

            $obj[] = $current;

            // Send X objects at the same time to algolia
            if (count($obj) >= $this->algoliaBulkSize) {
                $total += count($obj);
                $executeAction();
                $obj = [];
            }
        }

        // Process any leftover products
        $executeAction();

        $total += count($obj);

        return $total;
    }

    private function adjustRecordSize($current)
    {
        // Don't do the extra check for size if it's not an array (product)
        if (empty($current) || !is_array($current)) {
            return $current;
        }

        // This is an attempt to previous algolia to skip a bulk insert
        $size = $this->getSizeOfArray($current);

        if ($size > static::ALGOLIA_MAX_SIZE) {
            $this->logger->error(
                sprintf(
                    "We reached Algolia max size [%s] on this product [%s] with size [%s bytes]",
                    static::ALGOLIA_MAX_SIZE,
                    print_r($current, true),
                    $size
                )
            );
            $current = $this->resizeDown($current, $size - static::ALGOLIA_MAX_SIZE);
        }

        return $current;
    }

    /**
     * Retry a callable X times
     *
     * @param callable $function
     *
     * @return mixed
     */
    private function retry(callable $function, ...$params)
    {
        $nbAttempt = 0;
        $maxAttempt = 3;

        for (; $nbAttempt < $maxAttempt; $nbAttempt++) {
            try {
                return $function(...$params);
            } catch (Exception $e) {
                $this->logger->info(
                    sprintf(
                        "Algolia retry attempt [%s] on [%s]",
                        $nbAttempt,
                        $maxAttempt
                    )
                );
                $this->logger->error($e);
            }
        }

        // Try one last time without try/catch - don't silence exception
        return $function(...$params);
    }

    /**
     * Update algolia with deleted products (based on diff)
     *
     * @param SearchIndex $index
     *
     * @return int|void
     */
    private function removeDeletedProduct(SearchIndex $index)
    {
        return $this->bulk($this->getProducts(TablesDiff::ACTION_DELETE), $index, function ($product) use ($index) {
            // When we delete, we only return the product_id
            $objectId = $this->getObjectIdFromProductId($index, $product[static::FIELD_PRODUCT_ID]);

            if (empty($objectId)) {
                $this->logger->critical(
                    sprintf(
                        "This diff on product [%s] says it was deleted but it's no in algolia. Skip",
                        $product[static::FIELD_PRODUCT_ID]
                    )
                );
                return null;
            } else {
                return $objectId;
            }
        }, function ($products) use ($index) {
            $this->deleteProducts($index, $products);
        });
    }

    /**
     * Update algolia with inserted products (based on diff)
     *
     * @param SearchIndex $index
     *
     * @return int|void
     */
    private function addInsertedProduct(SearchIndex $index)
    {
        return $this->bulk($this->getProducts(TablesDiff::ACTION_INSERT), $index, function ($product) {
            return $this->preprocessProduct($product);
        }, function ($products) use ($index) {
            $this->indexProducts($index, $products);
        });
    }

    /**
     * Update algolia with updated products (based on diff)
     *
     * @param SearchIndex $index
     *
     * @return int|void
     */
    private function addUpdatedProduct(SearchIndex $index)
    {
        return $this->bulk($this->getProducts(TablesDiff::ACTION_UPDATE), $index, function ($product) use ($index) {
            $current = $this->preprocessProduct($product);

            $objectId = $this->getObjectIdFromProductId($index, $current[static::FIELD_PRODUCT_ID]);

            if (empty($objectId)) {
                $this->logger->critical(
                    sprintf(
                        "This diff on product [%s] says it was updated but it's no in algolia. Skip",
                        $current[static::FIELD_PRODUCT_ID]
                    )
                );
                return null;
            } else {
                $current['objectID'] = $objectId;
            }

            return $current;
        }, function ($products) use ($index) {
            $this->updateProducts($index, $products);
        });
    }

    /**
     * Copied from previous AlgoliaIndexer
     *
     * @param SearchIndex $index
     * @param       $productId
     *
     * @return |null
     * @throws \AlgoliaSearch\AlgoliaException
     */
    private function getObjectIdFromProductId(SearchIndex $index, $productId)
    {
        $res = $index->search($productId, ['hitsPerPage' => 1]);

        if (!empty($res) && isset($res['hits'][0]['objectID'])) {
            return $res['hits'][0]['objectID'];
        }

        return null;
    }

    /**
     * Loop over each product and add it to the temporary index by bulk
     *
     * @param \AlgoliaSearch\SearchIndex $index
     *
     * @return int
     */
    private function resetIndex(SearchIndex $index)
    {
        $this->logger->info(
            sprintf(
                "Reset index [%s] using tmp index [%s] in progress ...",
                $this->algoliaIndexName,
                $index->getIndexName()
            )
        );

        $total = $this->bulk($this->getProducts(), $index, function ($product) {
            return $this->preprocessProduct($product);
        }, function ($products) use ($index) {
            $this->indexProducts($index, $products);
        });

        $this->logger->info(
            sprintf(
                "Reset index [%s] using tmp index [%s] is completed. [%s] product added",
                $this->algoliaIndexName,
                $index->getIndexName(),
                $total
            )
        );

        return $total;
    }

    /**
     * Since i can't make a nice one big performant query (in dev), we decided to memoize data that needed
     * group_concat.
     *
     * At the beginning, i made a config to use "oneQuery" for everything VS "oneQuery" only for main data + extra
     * query. Memoizing data seems the best compromise here at the moment.
     *
     * The result (array) should always be the same (one query or not) so processProduct know what to expect.
     *
     * Since code is centralized here, i decided to skip to create custom model and keep an array for now.
     *
     * @param $product
     *
     * @return mixed
     * @throws AlgoliaIndexerCategoryException
     */
    private function preProcessProduct($product)
    {
        $mysqlFlagUpdated = $this->repository->disableOnlyFullGroupBy();

        // Category/attribute needs to support multilang, so i can't assign it to a specific field. Using array_merge instead
        $this->getCategories($product);

        if ($this->isExtendedVariantEnabled()) {
            $this->getVariantAttributes($product);
        } else {
            $this->getRetailerIds($product);
        }

        $this->repository->enableOnlyFullGroupBy($mysqlFlagUpdated);

        // processProduct expect a specific structure (because it was using group_concat before)
        // previous function keep the same logic and are parsed during processProduct()

        return $this->processProduct($product);
    }

    /**
     * Get the categories data needed to populate $this->storedCategoriesTree
     *
     * @return array
     */
    private function getCategoriesData(): array
    {
        $qb = $this->repository->getQueryBuilder();

        $select = sprintf(
            'ct.category_id as %s, ct.parent_id as %s, cat.name as %s',
            self::MYSQL_CATEGORY_ID,
            self::MYSQL_CATEGORY_PARENT_ID,
            self::MYSQL_CATEGORY_NAME,
        );

        $qb
            ->select($select)
            ->from('sf_category_tree', 'ct')
            ->leftJoin('ct', 'sf_categories', 'cat', 'ct.category_id = cat.category_id')
            ->orderBy('parent_id');


        $this->addToQueryBuilderI18n(function ($locale) use ($qb) {
            $categoriesAliasTable  = 'sf_categories_1_' . $locale;

            $select = sprintf(
                "%s.name as %s",
                $categoriesAliasTable,
                self::MYSQL_CATEGORY_NAME . '_' . $locale,
            );

            $qb
                ->addSelect($select)
                ->leftJoin(
                    'cat',
                    'sf_categories_i18n',
                    $categoriesAliasTable,
                    "$categoriesAliasTable.category_id = cat.category_id AND $categoriesAliasTable.locale='$locale'"
                );
        });

        return $qb
            ->executeQuery()
            ->fetchAllAssociativeIndexed();
    }

    private function populateStoredCategoriesTree()
    {
        $this->logger->info("Populate CategoriesTree - start");

        $categories = $this->getCategoriesData();
        $tree = [];

        // In the past, it was easier to populate the tree, because we only had 2 levels and
        // , in theory, all products were supposed to be targetting a level 2 (leaf).
        // Now, a product can be linked to any levels.
        // For this reason, all categories will have their path populated. (Not only leaf)
        foreach ($categories as $catId => $cat) {
            $tree[$catId][$catId] = array_merge(
                [self::MYSQL_CATEGORY_ID => $catId],
                $cat,
            );

            $parentId = $cat['parent_id'];
            $level = 0;

            // The idea is to populate the tree path until you reach "1" (legacy) or null (top level)
            while ($parentId != $this->configs['products.root_category_id'] && !empty($parentId)) {
                // Safety net in case we have a bug in the tree table that would cause circular logic.
                if (isset($tree[$catId][$parentId])) {
                    $this->logger->error(
                        sprintf(
                            "While processing cat ID [%s], we reached a circular reference with parent ID [%s]. Path [%s]",
                            $catId,
                            $parentId,
                            json_encode($tree[$catId]),
                        )
                    );
                    break;
                }
                // Safety net in case we have too much nested categories. (L4 for now)
                // We remove 1 from the max level because of the leaf itself.
                if ($level >= (self::MAX_LEVEL_RECURSION_CATEGORIES - 1)) {
                    $this->logger->error(
                        sprintf(
                            "While processing cat ID [%s], we reached the max nested threshold of [%s]. Path [%s]",
                            $catId,
                            self::MAX_LEVEL_RECURSION_CATEGORIES,
                            json_encode($tree[$catId]),
                        )
                    );
                    break;
                }
                $current = $categories[$parentId] ?? null;

                if (empty($current)) {
                    $this->logger->error(
                        sprintf(
                            "While processing cat ID [%s], we reach a parent ID [%s] that doesn't exist",
                            $catId,
                            $parentId
                        )
                    );
                    break;
                }

                $tree[$catId][$parentId] = array_merge(
                    [self::MYSQL_CATEGORY_ID => $parentId],
                    $current,
                );

                $parentId = $current['parent_id'];
                $level++;
            }
        }

        $this->storedCategoriesTree = $tree;

        $this->logger->info("Populate CategoriesTree - end");
    }

    private function populateStoredCategoriesPerPerProduct()
    {
        $this->logger->info("Populate CategoriesPerProduct - start");

        $getCategoriesPerProduct = function () {
            $qb = $this->repository->getQueryBuilder();

            // Order is important because of fetchAllAssocIndexed()
            $selectCategories = sprintf(
                "pcm.product_id, group_concat(DISTINCT pcm.category_id SEPARATOR '%s') as %s",
                self::SEPARATOR_CATEGORY_MAP_DIVISION,
                self::FIELD_CATEGORIES,
            );

            return $qb
                ->select($selectCategories)
                ->from('sf_product_category_map', 'pcm')
                ->innerJoin('pcm', 'sf_products', 'p', 'p.product_id = pcm.product_id')
                ->where(
                    $qb->expr()->eq('p.available', 1)
                )
                ->groupBy('pcm.product_id')
                ->executeQuery()
                ->fetchAllAssociativeIndexed();
        };

        $this->storedCategoriesPerProduct = $getCategoriesPerProduct();

        $this->logger->info("Populate CategoriesPerProduct - end");
    }

    /**
     * @param array $product
     *
     * @return mixed
     * @throws AlgoliaIndexerCategoryException
     */
    private function getCategories(array &$product)
    {
        $productId = $product[static::FIELD_PRODUCT_ID];

        $this->logger->debug(
            sprintf(
                "Processing category for product [%s]",
                $productId
            )
        );

        if (empty($this->storedCategoriesTree)) {
            $this->populateStoredCategoriesTree();
        }

        if (empty($this->storedCategoriesPerProduct)) {
            $this->populateStoredCategoriesPerPerProduct();
        }

        if (!isset($this->storedCategoriesPerProduct[$productId])) {
            throw new AlgoliaIndexerCategoryException(sprintf(
                "Missing category for product id [%s]",
                $productId
            ));
        } else {
            $product = array_merge($product, $this->storedCategoriesPerProduct[$productId]);
        }
    }

    private function getVariantAttributes(array &$product)
    {
        $sku = $product[static::FIELD_VARIANT_SKU];
        $this->logger->debug(
            sprintf(
                "Processing variant attributes for product [%s] and variant [%s]",
                $product[static::FIELD_PRODUCT_ID],
                $sku
            )
        );

        if (empty($this->storedVariantAttributes)) {
            // Populate

            $this->logger->debug("Prepopulate variant attributes ...");

            /** @var QueryBuilder $queryBuilder */
            $queryBuilder = $this->repository->getQueryBuilder();

            $this->addVariantAttributes($queryBuilder);

            $attributes = $queryBuilder->executeQuery();

            foreach ($attributes->iterateAssociative() as $attribute) {
                // if this takes too much memory, store it in redis !
                $processingSku = $attribute[static::FIELD_VARIANT_SKU];
                unset($attribute[static::FIELD_VARIANT_SKU]); // This will leave all the locale/field
                $this->storedVariantAttributes[$processingSku] = $attribute;
            }

            $this->logger->debug("Prepopulate variant attributes completed");
        }

        if (isset($this->storedVariantAttributes[$sku])) {
            $product = array_merge($product, $this->storedVariantAttributes[$sku]);
        }
    }

    private function addVariantAttributes(QueryBuilder $queryBuilder)
    {
        $selectVariantAttributes = sprintf(
            "group_concat(DISTINCT concat_ws('%s', pva.name, pva.value, pva.group, pva.position) ORDER BY pva.position SEPARATOR '%s') as %s",
            static::SEPARATOR_VARIANTS_ATTRIBUTES,
            static::SEPARATOR_VARIANTS_ATTRIBUTES_DIVISION,
            static::FIELD_VARIANT_ATTRIBUTES
        );

        $queryBuilder
            ->select(
                'pv.sku',
                $selectVariantAttributes
            )
            ->from('sf_product_variants', 'pv')
            ->leftJoin('pv', 'sf_product_variant_attributes', 'pva', 'pv.sku = pva.sku')
            ->groupBy('pva.sku')
            ->orderBy('pva.position'); // this is useful for test (otherwise i don't know which one will be first

        // Add link to i18n table
        $this->addToQueryBuilderI18n(function ($locale) use ($queryBuilder) {
            $variantAttributesAlias = 'sf_product_variant_attributes_' . $locale;

            $selectVariantAttributes = sprintf(
                "group_concat(DISTINCT concat_ws('%s', %s.name, %s.value, %s.group, %s.position) ORDER BY %s.position SEPARATOR '%s') as %s",
                static::SEPARATOR_VARIANTS_ATTRIBUTES,
                $variantAttributesAlias,
                $variantAttributesAlias,
                $variantAttributesAlias,
                $variantAttributesAlias,
                $variantAttributesAlias,
                static::SEPARATOR_VARIANTS_ATTRIBUTES_DIVISION,
                static::FIELD_VARIANT_ATTRIBUTES . '_' . $locale
            );

            $queryBuilder
                ->addSelect($selectVariantAttributes)
                ->leftJoin(
                    'pva',
                    'sf_product_variant_attributes_i18n',
                    $variantAttributesAlias,
                    "$variantAttributesAlias.sku = pva.sku AND $variantAttributesAlias.locale='$locale'"
                );
        });
    }

    /**
     * Get for a product all retailer ids (sku). This should be use only if extended variant isn't used.
     *
     * @param array $product
     */
    private function getRetailerIds(array &$product)
    {
        $productId = $product[static::FIELD_PRODUCT_ID];

        $this->logger->debug(
            sprintf(
                "Processing retailer ids for product [%s]",
                $productId
            )
        );

        if (empty($this->storedRetailerIds)) {
            // Populate

            /** @var QueryBuilder $queryBuilder */
            $queryBuilder = $this->repository->getQueryBuilder();

            $queryBuilder
                ->select('p.product_id as :product_id')
                ->from('sf_products', 'p')
                ->groupBy('p.product_id')
                ->setParameters([
                    'product_id' => static::FIELD_PRODUCT_ID,
                ]);

            $this->addRetailerIds($queryBuilder);

            $retailerIds = $queryBuilder->execute();

            foreach ($retailerIds as $retailerId) {
                // if this takes too much memory, store it in redis !
                $this->storedRetailerIds[$retailerId[static::FIELD_PRODUCT_ID]] =
                    $retailerId[static::FIELD_RETAILER_SKU];
            }
        }

        if (isset($this->storedRetailerIds[$productId])) {
            $product[static::FIELD_RETAILER_SKU] = $this->storedRetailerIds[$productId];
        }
    }

    /**
     * Add products to algolia by bulk
     *
     * @param SearchIndex $index
     * @param array $products
     */
    private function indexProducts(SearchIndex $index, array $products)
    {
        $this->logger->debug(
            sprintf(
                "Indexing [%s] products to algolia",
                count($products)
            )
        );

        $index->saveObjects($products, [
            'autoGenerateObjectIDIfNotExist' => true,
        ]);
    }

    /**
     * Update products in algolia by bulk
     *
     * @param SearchIndex $index
     * @param array $products
     */
    private function updateProducts(SearchIndex $index, array $products)
    {
        $this->logger->debug(
            sprintf(
                "Updating [%s] products to algolia",
                count($products)
            )
        );

        $index->saveObjects($products, [
            'autoGenerateObjectIDIfNotExist' => true,
        ]);
    }

    /**
     * Delete products in algolia by bulk
     *
     * @param SearchIndex $index
     * @param array $objectIds
     */
    private function deleteProducts(SearchIndex $index, array $objectIds)
    {
        $this->logger->debug(
            sprintf(
                "Deleting [%s] products to algolia",
                count($objectIds)
            )
        );

        $index->deleteObjects($objectIds);
    }

    /**
     * Get algolia temporary index name
     *
     * @return string
     */
    private function getTempIndexName()
    {
        return implode('_', [
            $this->algoliaIndexName,
            uniqid(),
            $this->env,
        ]);
    }

    /**
     * Since algolia limit is 20kb, this is a workaround so we don't get exception.
     * Since we are using bulk insert, it's hard to just loop try/catch over.
     *
     * @param array $current
     *
     * @return bool
     */
    private function isRecordTooBig(array $current)
    {
        // This is an attempt to previous algolia to skip a bulk insert
        $size = $this->getSizeOfArray($current);

        if ($size > static::ALGOLIA_MAX_SIZE) {
            $this->logger->error(
                sprintf(
                    "We reached Algolia max size [%s] on this product [%s] with size [%s bytes]",
                    static::ALGOLIA_MAX_SIZE,
                    print_r($current, true),
                    $size
                )
            );

            return true;
        }

        return false;
    }

    /**
     * After indexing, we need to update algolia settings with new dynamic facets
     *
     * @param SearchIndex $index
     *
     * @throws \AlgoliaSearch\AlgoliaException
     */
    private function updateDynamicFacets(SearchIndex $index)
    {
        $dynamicFacets = [];
        foreach ($this->dynamicFacets as $dynamic) {
            if (strpos($dynamic, '{locale}') !== false) {
                // It's a localized field
                foreach ($this->configs['sf.i18n.locales'] as $locale) {
                    $dynamicFacets[] = str_replace('{locale}', $locale, $dynamic);
                };
            } else {
                $dynamicFacets[] = $dynamic;
            }
        }

        $this->logger->info(
            sprintf("Adding new facets to index [%s]", json_encode($dynamicFacets))
        );

        $index->setSettings(['attributesForFaceting' => array_merge($this->getFacets(), $dynamicFacets)]);
    }
}
