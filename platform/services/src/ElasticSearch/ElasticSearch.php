<?php

declare(strict_types=1);

namespace Salesfloor\Services\ElasticSearch;

use DateTime;
use Monolog\Logger;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Middleware;
use Guz<PERSON><PERSON>ttp\HandlerStack;
use Guz<PERSON><PERSON>ttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Salesfloor\Services\Util;
use Aws\Signature\SignatureV4;
use GuzzleHttp\RetryMiddleware;
use Salesfloor\Configs\Configs;
use Aws\Credentials\Credentials;
use Salesfloor\Models\CustomerTag;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use Salesfloor\Services\ElasticSearch\Exceptions\ElasticSearchException;

/**
 * Class ElasticSearch
 *
 * @package Salesfloor\Services\ElasticSearch
 *
 * All requests to ElasticSearch must be authenticated. To use it from your workstation,
 * (e.g. to access Kibana in your web browser), you can use the aws-es-kibana proxy to
 * sign all your requests (https://github.com/santthosh/aws-es-kibana):
 *
 *  1. authenticate to AWS (see
 *  https://salesfloor.atlassian.net/wiki/display/DEV/Using+AWS+from+the+command+line+with+MFA)
 *  2. run ./node_modules/aws-es-kibana/index.js <es-endpoint>
 *  3. visit http://127.0.0.1:9200/_plugin/kibana/ in your browser
 *
 * e.g.
 *
 *  $ awl 123123
 *  $ cd platform
 *  $ ./node_modules/aws-es-kibana/index.js search-sf-dev-oxni43tntzeqysjanabwkw2dc4.us-east-1.es.amazonaws.com
 *
 * AWS ES doesn't let us give access to whole groups at a time; users need to
 * be granted access individually. If you want access to AWS ES for your AWS
 * IAM user, ask ppp@ for it.
 */
class ElasticSearch extends Base
{
    public const KEYWORD_BOOL = "bool";
    public const KEYWORD_SHOULD = "should";
    public const KEYWORD_TERM = "term";

    private const RETRY_ATTEMPT = 5;

    private const CONCURRENT_REQUEST = 10;

    private $region;

    /** @var Client $client */
    protected $client;

    /** @var Alias $alias */
    private $alias;

    /** @var Index $index */
    private $index;

    public const WILDCARD_NONE   = 0;
    public const WILDCARD_PREFIX = 1;
    public const WILDCARD_SUFFIX = 2;
    public const WILDCARD_BOTH   = 3;

    // name of filed type for keyword filter
    // elasticSearch can give any name to "keyword" type, and here salesfloor just give 'keyword' name to 'keyword' type
    public const FIELD_TYPE_KEYWORD = 'keyword';

    public const DSL_AND_OPERATOR   = 'AND';
    public const DSL_OR_OPERATOR    = 'OR';
    public const DSL_FUZZY_OPERATOR = '~';

    /**
     * ElasticSearch constructor.
     *
     * @param Configs $configs
     * @param Logger $logger
     * @throws ElasticSearchException
     */
    public function __construct(Configs $configs, Logger $logger)
    {
        parent::__construct($configs, $logger);

        $this->region = $this->configs['amazon.elasticsearch.region'];

        if ($this->configs['service.elasticsearch'] == self::PROVIDER_GCP) {
            $this->setGcpElasticSearch();
        } else {
            $this->setAwsElasticSearch();
        }

        $this->alias = new Alias($this->client, $configs, $logger);
        $this->index = new Index($this->client, $configs, $logger);
    }

    /**
     * Based on the config, create a listener on before so i can sign the call to AWS Elasticsearch service
     *
     */
    public function setGcpElasticSearch()
    {
        // Create a handler stack that has all of the default middlewares attached
        $handler = HandlerStack::create();
        // Push the handler onto the handler stack
        $handler->push(Middleware::mapRequest(function (RequestInterface $request) {
            $this->logger->debug(sprintf("Calling ES url [%s] [%s]", $request->getUri(), $request->getMethod()));

            $username = $this->configs['gcp.elasticsearch.username'];
            $password = $this->configs['gcp.elasticsearch.password'];

            // Notice that we have to return a request object
            return $request->withHeader('Authorization', 'Basic ' . base64_encode($username . ':' . $password));
        }));

        // https://gist.github.com/christeredvartsen/7776e40a0102a571c35a9fc892164a8c
        $handler->push(Middleware::retry(function (int $retries, RequestInterface $request, ResponseInterface $response = null) {

            $shouldRetry = $retries < self::RETRY_ATTEMPT &&
                (empty($response) || $response->getStatusCode() === 400 || $response->getStatusCode() === 429 || $response->getStatusCode() >= 500);

            if ($shouldRetry) {
                // 400 is for when the index is getting snapshot and 429 is too many request
                // 50X could be because of a timeout.
                $this->logger->error(
                    sprintf(
                        "Retry on [%s] with code [%s] and response [%s]",
                        $request->getUri(),
                        $response?->getStatusCode(),
                        substr($response?->getBody()->getContents() ?? '', 0, 200) . "..."
                    )
                );
            }

            return $shouldRetry;
        }, function (int $retries, ResponseInterface $response = null) {
            if (empty($response) || !$response->hasHeader('Retry-After')) {
                return RetryMiddleware::exponentialDelay($retries);
            }

            $retryAfter = $response->getHeaderLine('Retry-After');

            if (!is_numeric($retryAfter)) {
                $retryAfter = (new DateTime($retryAfter))->getTimestamp() - time();
            }

            return (int) $retryAfter * 1000;
        }));

        // Inject the handler into the client
        $this->client = new Client(['handler' => $handler]);
    }


    /**
     * Based on the config, create a listener on before so i can sign the call to AWS Elasticsearch service
     *
     */
    public function setAwsElasticSearch()
    {
        // Create a handler stack that has all of the default middlewares attached
        $handler = HandlerStack::create();
        // Push the handler onto the handler stack
        $handler->push(Middleware::mapRequest(function (RequestInterface $request) {
            $signer = new SignatureV4('es', $this->region);
            $this->logger->debug(sprintf("Calling AWS ES url [%s] [%s]", $request->getUri(), $request->getMethod()));

            // create new requests with previous value so i can pass it to signRequest()
            // it needs to be specific type that implement EntityEnclosingRequestInterface
            $newRequest = new Request($request->getMethod(), $request->getUri(), $request->getHeaders());

            $body = $request->getBody();
            // Don't attach body if there's no body, we need this payload when we sign the request
            if ($body) {
                $newRequest->setBody($body);
            }

            $credentials = new Credentials($this->configs['s3.key'], $this->configs['s3.secret']);

            // It need : Guzzle\Http\Message\RequestInterface
            $signer->signRequest($newRequest, $credentials);

            return $request->withHeader('x-amz-date', $newRequest->getHeader('x-amz-date')->__toString())
                            ->withHeader('authorization', $newRequest->getHeader('authorization')->__toString())
                            ->withHeader('host', $newRequest->getHeader('host')->__toString());
            // Notice that we have to return a request object
            // return $request;
        }));

        // Inject the handler into the client
        $this->client = new Client(['handler' => $handler]);
    }

    /**
     * At the moment, the re-index will not delete content from destination if missing from source
     * TODO: Better scenario would be to create a alias X for es index and switch it when you re-index
     *
     * @param $source
     * @param $destination
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function reIndex($source, $destination)
    {
        if (empty($source) || empty($destination)) {
            throw new ElasticSearchException(sprintf("Source [%s] or Destination [%s] are missing", $source, $destination));
        }

        $response = $this->client->post($this->getRootUrl() . '_reindex', [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body'    => json_encode([
                'source'    => [
                    'index' => $this->getIndexFormatted($source),
                ],
                'dest'      => [
                    'index'        => $this->getIndexFormatted($destination),
                    // Setting version_type to external will cause Elasticsearch to preserve the version from the source,
                    // create any documents that are missing, and update any documents that have an older version
                    // in the destination index than they do in the source index:
                    'version_type' => 'external',
                ],
                // By default version conflicts abort the _reindex process but you can just count them by settings "conflicts": "proceed" in the request body.
                'conflicts' => 'proceed',
            ]),
        ]);

        return $this->returnResponse($response);
    }

    public function updateSettings(string $index, array $settings)
    {
        return $this->index->update($index . '/_settings', $settings);
    }

    /**
     * Create an index
     *
     * @param $index
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function createIndex($index, $body = null)
    {
        if (empty($index)) {
            throw new ElasticSearchException("Index name is missing");
        }

        $response = $this->client->put($this->getUrl($index), [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => empty($body) ? "{}" : json_encode($body),
        ]);

        return $this->returnResponse($response);
    }

    /**
     * Delete an index ; you can't use alias anymore
     *
     * @param $index
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function deleteIndex($index)
    {
        if (empty($index)) {
            throw new ElasticSearchException("Index name is missing");
        }

        try {
            $response = $this->client->delete($this->getUrl($index));
        } catch (RequestException $e) {
            if ($e->getCode() === 404) {
                return null;
            }

            // this is temp to debug test
            throw new \Exception(sprintf(
                "Delete failed on url [%s] with message [%s]",
                $this->getUrl($index),
                $e->getMessage()
            ));
        }

        return $this->returnResponse($response);
    }

    /**
     * Check if index exist
     *
     * @param $index
     *
     * @return bool True if exist, false otherwise
     * @throws ElasticSearchException
     */
    public function indexExist($index)
    {
        if (empty($index)) {
            throw new ElasticSearchException("Index name is missing");
        }

        try {
            $response = $this->client->head($this->getUrl($index));
        } catch (ClientException $e) {
            if ($e->getCode() == 404) {
                return false;
            }

            throw $e;
        }

        return $response->getStatusCode() === 200;
    }

    // TODO : support mapping modification
    // At the moment, AWS/ES create default mapping based on when we send to them
    // No difference between searchable and stored
    // https://www.elastic.co/guide/en/elasticsearch/reference/current/mapping-store.html

    /**
     * Add/Update specific entity (id) in ES
     *
     * Keep in mind that in the futur, type will be removed
     * https://github.com/elastic/elasticsearch/issues/15613
     *
     * The _source field needs to be enabled for this feature to work
     *
     * @param $index
     * @param $data
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function index($index, $data)
    {
        // ALWAYS use same structure (id lowercase)
        if (!$data['id']) {
            throw new ElasticSearchException("Id is missing");
        } else {
            // Clean input
            $id = $data['id'];
            unset($data['id']);
        }

        $url = $this->getUrl($index, '_bulk', null, true);

        $execute = function () use ($data, $url) {
            $response = $this->client->put($url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'body'    => json_encode($data),
            ]);

            return $this->returnResponse($response);
        };

        return $this->retry($execute);
    }

    /**
     * Return one entity from ES
     *
     * @param $index
     * @param $id
     *
     * @return ?array
     * @throws ElasticSearchException
     */
    public function get($index, $id)
    {
        try {
            $response = $this->client->get($this->getUrl($index, '_doc', $id));
        } catch (ClientException $e) {
            if ($e->getCode() == 404) {
                return null;
            }

            throw $e;
        }

        return $this->returnResponse($response);
    }

    /**
     * Delete one entity from ES
     *
     * @param $index
     * @param $id
     *
     * @return array
     * @throws ElasticSearchException
     */
    public function delete($index, $id)
    {
        if (empty($id)) {
            throw new ElasticSearchException("Id is missing");
        }

        try {
            // Don't crash if the data is not in ES
            $response = $this->client->delete($this->getUrl($index, '_doc', $id));
        } catch (RequestException $e) {
            if ($e->getCode() === 404) {
                $this->logger->warning(sprintf("You are trying to delete something that doesn't exist Index [%s] Id [%s]", $index, $id));

                return null;
            }

            throw $e;
        }

        return $this->returnResponse($response);
    }

    /**
     * bulk (insert/delete) data to ES
     *
     * @param $index
     * @param $action
     * @param array $data
     *
     * @return void Since this is done in parallel, do not try to merge the response.
     * @throws ElasticSearchException
     */
    public function bulk($index, $action, array $data): void
    {
        // You don't have anything to process
        if (empty($data)) {
            return;
        }

        $request = function () use ($data, $index, $action) {
            // 1000 was the original bulk size and will keep it as it for now.
            $chunks = array_chunk($data, 1000);
            foreach ($chunks as $chunk) {
                $this->logger->debug(
                    sprintf(
                        "ES: Bulk on index [%s] and action [%s] with %s entries",
                        $index,
                        $action,
                        count($chunk)
                    )
                );

                $action = strtolower($action);

                $possibleAction = ['index', 'delete'];

                if (!in_array($action, $possibleAction)) {
                    throw new ElasticSearchException("Bulk action invalid [%s]", $action);
                }

                $body = [];

                foreach ($chunk as $value) {
                    switch ($action) {
                        case 'index':
                            // We need an array an it need to have an id
                            if (!is_array($value) || !isset($value['id'])) {
                                continue 2;
                            }

                            $body[] = json_encode(
                                [
                                    "index" => [
                                        '_index' => $this->getIndexFormatted($index),
                                        '_id'    => $value['id'],
                                    ],
                                ]
                            );
                            // We don't need it in the body and as primary id
                            unset($value['id']);
                            $body[] = json_encode($value);

                            break;
                        case 'delete':
                            $id = null;
                            // Support [ X, Y, Z, ...]
                            // Support [ ['id' => x, ...], ['id' => Y, ... ], ... ]
                            if (is_array($value)) {
                                $id = !empty($value['id']) ? $value['id'] : null;
                            } else {
                                $id = $value;
                            }

                            if (empty($id)) {
                                continue 2;
                            }

                            $body[] = json_encode(
                                [
                                    "delete" => [
                                        '_index' => $this->getIndexFormatted($index),
                                        '_id'    => $id,
                                    ],
                                ]
                            );
                            break;
                    }
                }

                // If you don't have any body, don't send anything to ES
                if (empty($body)) {
                    return;
                }

                $url = $this->getUrl($index, '_bulk', null, true);

                // https://www.elastic.co/guide/en/elasticsearch/reference/current/docs-bulk.html
                $formattedData = join("\n", $body) . "\n";

                yield new Request('POST', $url, [
                    'Content-Type' => 'application/x-ndjson',
                ], $formattedData);
            }
        };

        $pool = new Pool($this->client, $request(), [
            'concurrency' => self::CONCURRENT_REQUEST,
            'fulfilled' => function (Response $response, $indexPromise) {
                $body = $this->returnResponse($response);

                $this->logger->debug(
                    sprintf(
                        "ES: Bulk operation promise [%s] completed with status code [%s] and response [%s]",
                        $indexPromise,
                        $response->getStatusCode(),
                        substr(json_encode($body), 0, 1000) . "..."
                    )
                );
            },
            'rejected' => function (mixed $reason, $indexPromise) {
                $message = substr(var_export($reason, true), 0, 2000);
                if ($reason instanceof \Throwable) {
                    $message = $reason->getMessage();
                }

                $type = gettype($reason);
                $objectInfo = $type === 'object' ? $reason::class : $type;

                $this->logger->error(
                    sprintf(
                        "ES: Bulk operation promise [%s] of type [%s] failed with message [%s].",
                        $indexPromise,
                        $objectInfo,
                        $message
                    )
                );

                // Don't silence exception, This is called after the X retry from the middleware
                throw $reason;

                // I'm getting rid of the "split bulk". I tried with 100k contact and it took 4sec.
                // I prefer to rely on the retry than on the splitting.
            },
        ]);

        $promise = $pool->promise();
        $promise->wait();

        // There's no "return" of response anymore, since those are process in parallel.
    }

    /**
     * Get documents by ID(s)
     *
     * @param $index
     * @param array $ids
     *
     * @return array
     * @throws ElasticSearchException
     */
    public function mget($index, array $ids): array
    {
        // You don't have anything to process
        if (empty($ids)) {
            return [];
        }

        $response = $this->client->get($this->getUrl($index, '_mget'), [
            'headers' => [
                'content-type' => "application/json",
            ],
            'body'    => json_encode([
                'ids' => $ids,
            ]),
        ]);

        return $this->returnResponse($response);
    }

    /**
     * By default all field are text and keyword
     * https://www.elastic.co/blog/strings-are-dead-long-live-strings
     *
     * "If you need to index structured content such as email addresses, hostnames, status codes, or tags,
     * it is likely that you should rather use a keyword field. codes, or tags, it is likely that you should rather use
     * a keyword field." https://www.elastic.co/guide/en/elasticsearch/reference/master/keyword.html
     *
     * "If you need to index full text content such as email bodies or product descriptions,
     * it is likely that you should rather use a text field."
     * https://www.elastic.co/guide/en/elasticsearch/reference/master/text.html
     *
     * @param $index
     * @param $input
     * @param $wildCard
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function search($index, $input, $page, $perPage, $wildCard = true)
    {
        $response = $this->client->get($this->getUrl($index, '_search'), [
            'headers' => [
                'content-type' => "application/json",
            ],
            'body'    => json_encode([
                "query" => [
                    "query_string" => [
                        // https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-query-string-query.html
                        // Defaults to the index.query.default_field index setting, which has a default value of *.
                        // We don't need to specify by default which fields since we search on all of them.
                        "query"      => (true === $wildCard ? "*$input*" : $input),
                    ],
                ],
                "from"  => $this->page2offset($page, $perPage),
                "size"  => $perPage,
            ]),
        ]);

        return $this->returnResponse($response);
    }

    /**
     * INFO: The initial idea was to query ES when full text search was needed (input) and when it was ONLY filtering
     * to use the DB as the main source (since we don't need ES full text search). However, this is not the case anymore.
     * I don't know if it's for performance reason, or just because it was "available".
     *
     * WARNING: Since ES 7+ don't expect "should" be to consider for a match in a filter context.
     *
     * Searches or counts by the 3 types of filters with the option to return the desired fields. By default it will return all fields.
     * A note on filters: the filters array must be build in a very particular fashion. This is, the array might contain up to 3
     * associative entries:
     *
     * - 'must'
     * - 'must_not'
     * - 'should'
     *
     * Each entry must then store each filter as an independent associative array. For example:
     *
     * $filter = [
     *      'must' => [
     *          ['tag' => 1],
     *          ['tag' => 2],
     *          ['subcribtion_flag' => 1]
     *      ],
     *      'should' => [ // See previous warning
     *          ['user_id' => 115],
     *          ['user_id' => 338]
     *      ]
     * ];
     *
     * Do not include 'term', this element will be added automagically by a call to 'populateFilters()'.
     *
     * @param string        $index The name of the ES index we want to work on
     * @param Query    $filters  containing any of the 3 filter declarations: 'must', 'must_not' and/or 'should'
     * @param int|null      $page (optional) A page offset for pagination - discarded if $countOnly is set to true
     * @param int|null      $perPage (optional) The number of records per page  - discarded if $countOnly is set to true
     * @param string|null   $fields (optional) An array with the name of the fields we wish to retrieve from ES
     *                      - discarded if $countOnly is set to true
     * @param bool|false    $countOnly (optional) Whether we want to hit the '_search' or '_count' end point in ES
     *                      - i.e. get the full record set or just the count
     *
     * @return array
     */
    public function searchByFilters(
        $index,
        Query $searchQuery,
        $page = null,
        $perPage = null,
        $fields = null,
        $countOnly = false
    ) {
        $jsonRequestBody = [
            'query' => [
                'bool' => [
                    'filter' => $this->populateFilters(
                        $searchQuery->esFilters,
                        static::FIELD_TYPE_KEYWORD
                    ),
                ],
            ],
        ];

        if (!$countOnly) {
            $jsonRequestBody['track_total_hits'] = true;
        }

        // Append pagination and wanted fields to non-count-only queries...
        if (false === $countOnly) {
            // Setup full pagination
            if (null !== $page && null !== $perPage) {
                $jsonRequestBody['from'] = $this->page2offset($page, $perPage);
                $jsonRequestBody['size'] = $perPage;
            }

            // Retrieve all fields if no fields have been defined
            if (null === $fields) {
                $fields = ['*'];
            }

            $jsonRequestBody['_source'] = $fields;
        }

        if ($searchQuery->isSortable()) {
            $jsonRequestBody['sort'] = [
                [ $searchQuery->esSort => $searchQuery->order],
            ];
        }

        // Execute...
        $response = $this->client->get($this->getUrl($index, ($countOnly ? '_count' : "_search")), [
            'headers' => [
                'content-type' => "application/json",
            ],
            'json'    => $jsonRequestBody,
        ]);

        return $this->returnResponse($response);
    }

    /**
     * Search by field using "match phrase" query
     * https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-match-query-phrase.html
     *
     * Seems like in this case, we don't need to specify both fields (e.g: email & email.keyword)
     *
     * At the moment, wildcard feature is disabled since we are using match (multi_match)
     * If we want to support those feature we must use query_string (More error prone)
     * https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-match-query.html
     * https://stackoverflow.com/questions/37689935/whats-the-difference-between-query-string-and-multi-match
     *
     * @param $index
     * @param $searchQuery
     * @param $fields
     * @param $page
     * @param $perPage
     * @param $filters
     * @param $wildCard
     *
     * @return array
     * @throws ElasticSearchException
     */
    public function searchByFields(
        $index,
        Query $searchQuery,
        $page,
        $perPage,
        $wildCard = self::WILDCARD_BOTH
    ) {
        // ES have a limit for size (index.max_result_window) (Default 10000)
        // https://www.elastic.co/guide/en/elasticsearch/reference/current/search-request-from-size.html

        $body = [
            // Since v7, total is capped at 10k (default), to be able to display the exact value, we need to
            // pass this params to each call. In theory, we should rely on _count, but this is not implemented.
            // See getSearchResultHitCount() if we want to get rid of this params.
            "track_total_hits" => true,
            "query" => [
                "bool" => [
                    "must"   => [
                        "query_string" => $searchQuery->getQueryString($wildCard),
                    ],
                    "filter" => $this->populateFilters($searchQuery->esFilters),
                ],
            ],
            "from"  => $this->page2offset($page, $perPage),
            "size"  => $perPage,
        ];
        if ($searchQuery->isSortable()) {
            $body['sort'] = [
                [ $searchQuery->esSort => $searchQuery->order],
            ];
        }

        $payload = [
            'headers' => [
                'content-type' => "application/json",
            ],
            'body'    => json_encode($body),
        ];

        $response = $this->client->get($this->getUrl($index, '_search'), $payload);

        return $this->returnResponse($response);
    }

    /**
     * Adapter for encapsulate array which contains keys must/mustNot/should into ElasticSearch API format by:
     * Map key value from must/mustNot/should to must/must_not/should, add fieldType('keyword'), encapsulate into term/terms array
     *
     * @param array  $filters
     * @param string $originalFieldType Force it to "keyword" because filter would always target the keyword type
     *
     * @return array
     * @link https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-bool-query.html
     * @link https://www.elastic.co/guide/en/elasticsearch/guide/current/combining-filters.html
     */
    private function populateFilters(array $filters, $originalFieldType = self::FIELD_TYPE_KEYWORD)
    {
        // filters need to be split 3 in category : must (AND), must_not, should (OR)
        $formatFilter = function ($filters) use ($originalFieldType) {
            $newFilters = [];

            // TODO : this should be cleaned up and enforce "keyword" on filters
            $fieldType = "";
            if (!empty($originalFieldType)) {
                $fieldType = ".$originalFieldType";
            }

            $addTags = function ($key, $value) use (&$newFilters) {
                $newKey = $key;
                // default is 'and'
                $filter = $value[CustomerTag::FILTER_KEY] ?? CustomerTag::FILTER_AND;
                unset($value[CustomerTag::FILTER_KEY]);

                $newFilters[] = match ($filter) {
                    CustomerTag::FILTER_OR => [
                        'terms' => [
                            $newKey => $value,
                        ],
                    ],
                    default => [
                        'terms_set' => [
                            $newKey => [
                                'terms' => $value,
                                'minimum_should_match_script' => [
                                    'source' => 'params.num_terms'
                                ]
                            ],
                        ],
                    ],
                };
            };

            $addFilterToArray = function ($key, $value) use (&$newFilters, $fieldType) {
                if (!is_array($value)) {
                    $value = [$value];
                }

                $newKey = $key;

                // Only append fieldtype if not already part of the key
                // this is only a failsafe in case field type was already included.
                if (!empty($fieldType) && strpos($key, $fieldType) === false) {
                    $newKey = $key . $fieldType;
                }

                foreach ($value as $val) {
                    $newFilters[] = [
                        self::KEYWORD_TERM => [
                            $newKey => $val,
                        ]
                    ];
                }
            };

            foreach ($filters as $filter) {
                foreach ($filter as $key => $value) {
                    if ($key === 'nested') {
                        $newFilters[] = [
                            $key => $value,
                        ];
                        continue;
                    }

                    // If we have a custom scenario, don't format it
                    // Fixme: this is not scalable. Currently we only have one specific scenario for bool keywords
                    // being different from default implementation. Elasticsearch has different varieties of filters
                    // and current implementation provides limited avenues to scale them up.
                    // Following documentation provides different ways to create complex compound queries
                    // https://www.elastic.co/guide/en/elasticsearch/reference/6.8/compound-queries.html

                    if ($key === self::KEYWORD_BOOL) {
                        $newFilters[] = $this->populateFilters($value, $originalFieldType);
                        continue;
                    }

                    if ($key === 'tags') {
                        $addTags($key, $value);
                        continue;
                    }

                    // TODO: I'm not sure if the next statement is still true. In theory, i would prefer to use
                    // terms + array than multiple term.

                    // We can't use "terms" because otherwise, it won't be a perfect match
                    // https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-terms-query.html
                    $addFilterToArray($key, $value);
                }
            };

            return $newFilters;
        };

        $must    = !empty($filters['must']) ? $formatFilter($filters['must']) : [];
        $mustNot = !empty($filters['mustNot']) ? $formatFilter($filters['mustNot']) : [];
        $should  = !empty($filters['should']) ? $formatFilter($filters['should']) : [];

        return [
            "bool" => [
                "must"     => $must,
                "must_not" => $mustNot,
                "should"   => $should,
            ],
        ];
    }

    private function page2offset($page, $perPage)
    {
        return $page * $perPage;
    }

    /**
     * Use during reindexation. Don't use "reIndex" but switch alias instead
     *
     * @param $index
     * @param $alias
     *
     * @throws ElasticSearchException
     */
    public function switchAlias($index, $alias)
    {
        $this->logger->debug(
            sprintf(
                "ES: switchAlias() - index [%s] with alias [%s]",
                $index,
                $alias
            )
        );

        if (!$this->alias->exist($alias)) {
            $this->logger->debug(
                sprintf(
                    "ES: switchAlias() - alias [%s] is not used, no need to switch",
                    $alias
                )
            );

            // Just create it them (usually, the initial script)
            $this->alias->add($index, $alias);
            // If there's an index for it, you won't be able to create the alias
        } else {
            // Most of the time, it will be a switch
            // Find the source(s), the previous indexes that have the alias. It will be removed.
            $sources = $this->alias->get($alias);

            $this->alias->switchAlias($index, $alias);

            $this->logger->debug(
                sprintf(
                    "ES: switchAlias() - Completed. Will delete older index [%s]",
                    json_encode($sources)
                )
            );

            // You can delete source (switch completed)
            if ($this->index->exist($alias)) {
                // Because of the race condition, it's possible that some "duplicate" index are not deleted with this
                // script. We have another script that delete old index (preprod only for now)
                // This may not be needed, since having duplicate aliases during bulk, will give you an exception.
                array_map(function ($source) {
                    $response = $this->index->delete($source);

                    $this->logger->debug(
                        sprintf(
                            "ES: switchAlias() - Deleting [%s] with response [%s]",
                            $source,
                            json_encode($response)
                        )
                    );
                }, $sources);
            } else {
                throw new ElasticSearchException("Looks like the switch alias failed. Delete source was not done for safety");
            }
        }
    }

    /**
     * Get all indexes from an alias. This is used atm to delete the index from this alias.
     *
     * @param string $alias
     * @return array
     *
     * @throws ElasticSearchException
     */
    public function getIndexesFromAlias(string $alias)
    {
        return $this->alias->get($alias);
    }

    /**
     * Get the statistics of an index.
     *
     * @param $index The index to stat.
     * @param $stat  The specific statistics to acquire.
     *
     * @return array
     */
    public function stat($index, $stat = null)
    {
        $r = $this->client->get($this->getUrl($index, '_stats', $stat));
        return $this->returnResponse($r);
    }

    /**
     * Check if index is setup properly (one alias and valid one)
     *
     * @param $index
     * @return bool
     * @throws ElasticSearchException
     */
    public function isSetupProperly($index): bool
    {
        try {
            $indexes = $this->alias->get($index);
        } catch (ClientException $e) {
            $this->logger->error(
                sprintf(
                    "This index [%s] alias setup is wrong. Exception [%s]",
                    $index,
                    $e->getMessage()
                )
            );

            if ($e->getCode() === 404) {
                return false;
            }

            throw $e;
        }

        $validCount = count($indexes) === 1;

        //e.g: joseph-chicos-dev.sf_customer-2022-03-28-19-52-52
        return $validCount && preg_match('/' . $index .  '-(\d{4})-(\d{2})-(\d{2})-(\d{2})-(\d{2})-(\d{2})$/', $indexes[0]);
    }

    /**
     * Get all indices
     *
     * @return array
     */
    public function getIndices()
    {
        $response = $this->client->get($this->getRootUrl() . '_alias');
        return $this->returnResponse($response);
    }

    /**
     * Delete any index which was created in current server.
     *
     * @param $index
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function deleteAnyIndex($index)
    {
        if (empty($index)) {
            throw new ElasticSearchException("Index name is missing");
        }

        try {
            $requestUrl = $this->getRootUrl() . $index;
            $response = $this->client->delete($requestUrl);
        } catch (RequestException $e) {
            if ($e->getCode() === 404) {
                return null;
            }

            throw new ElasticSearchException(sprintf(
                "Delete failed on url [%s] with message [%s]",
                $requestUrl,
                $e->getMessage()
            ));
        }

        return $this->returnResponse($response);
    }

    /**
     * Delete any alias in an index which was created in current server.
     *
     * @param $index
     * @param $alias
     *
     * @return string
     * @throws ElasticSearchException
     */
    public function deleteAnyAlias($index, $alias)
    {
        if (empty($index)) {
            throw new ElasticSearchException("Index name is missing");
        }

        try {
            $requestUrl = sprintf('%s%s/_alias/%s', $this->getRootUrl(), $index, $alias);
            $response = $this->client->delete($requestUrl);
        } catch (RequestException $e) {
            if ($e->getCode() === 404) {
                return null;
            }

            // this is temp to debug test
            throw new \Exception(sprintf(
                "Delete failed on url [%s] with message [%s]",
                $requestUrl,
                $e->getMessage()
            ));
        }

        return $this->returnResponse($response);
    }
}
