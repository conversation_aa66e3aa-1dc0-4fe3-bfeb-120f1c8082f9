<?php

namespace Salesfloor\Services\ElasticSearch;

use Carbon\Carbon;
use Salesfloor\Services\Util;
use Salesfloor\Models\CustomerTag;
use Salesfloor\Models\CustomerAttribute;
use Salesfloor\Services\Salesfloor\Reps;
use Salesfloor\API\Managers\CustomerAttributes;

class ContactQuery extends Query
{
    /** @var Reps */
    private $reps;

    public function __construct(array $i18nLocales, CustomerAttributes $customerAttributes, $reps)
    {
        parent::__construct($i18nLocales, $customerAttributes);
        $this->reps = $reps;
    }

    protected const ADVANCED_SEARCH_KEYS = [
        'first_name' => ['first_name'],
        'last_name' => ['last_name'],
        'email' => ['email', 'email.keyword'],
        'phone' => ['phone'],
        'city' => ['addresses.city'],
        'state' => ['addresses.state'],
        'notes' => ['notes', 'notes.keyword'],
    ];

    public function shouldElasticsearch(): bool
    {
        if ($this->isSearchable()) {
            return true;
        }

        if ($this->isFilterForSqlOnly()) {
            return false;
        }

        if ($this->isFilterForElasticsearchOnly() && $this->isFilterable()) {
            return true;
        }

        if ($this->isSortable()) {
            return true;
        }

        return false;
    }

    public function buildElasticsearchFilterByTransaction(array $transactionFilters): array
    {
        $dateStart = $this->parseDate($transactionFilters['date']['start'] ?? null);
        $dateEnd = $this->parseDate($transactionFilters['date']['end'] ?? null);

        $dateRange = [];
        if ($dateStart !== null || $dateEnd !== null) {
            $conditions = [];
            if ($dateStart !== null) {
                $conditions['gte'] = $dateStart->startOfDay()->toIso8601ZuluString();
            }
            if ($dateEnd !== null) {
                $conditions['lte'] = $dateEnd->endOfDay()->toIso8601ZuluString();
            }
            $dateRange = [
                'range' => [
                    'transactions.date' => $conditions,
                ],
            ];
        }

        $totalFrom = $transactionFilters['total']['from'] ?? null;
        $totalTo = $transactionFilters['total']['to'] ?? null;

        $totalRange = [];
        if ($totalFrom !== null || $totalTo !== null) {
            $conditions = [];
            if ($totalFrom !== null) {
                $conditions['gte'] = $totalFrom;
            }
            if ($totalTo !== null) {
                $conditions['lte'] = $totalTo;
            }
            $totalRange = [
                'range' => [
                    'transactions.total' => $conditions,
                ],
            ];
        }

        $products = [];
        $brand = $transactionFilters['brand'] ?? null;
        if ($brand !== null) {
            $products[] = [
                'terms' => [
                    'transactions.products.brand' => explode(',', $brand),
                ]
            ];
        }

        $categoryId = $transactionFilters['category_id'] ?? null;
        if ($categoryId !== null) {
            $products[] = [
                'terms' => [
                    'transactions.products.category_id' => explode(',', $categoryId),
                ]
            ];
        }

        if (count($products) > 0) {
            $products = [
                'nested' => [
                    'path' => 'transactions.products',
                    'query' => [
                        'bool' => [
                            'must' => $products,
                        ],
                    ],
                ],
            ];
        }

        return array_values(array_filter([
            $dateRange,
            $totalRange,
            $products,
        ]));
    }

    /**
     * @return array
     */
    public function getFields(): array
    {
        // include searchable extended attributes
        $attributes = $this->customerAttributes->getSearchableAttributes();

        return array_merge([
            ['name' => 'full_name', 'boost' => 4],
            ['name' => 'first_name', 'boost' => 3],
            ['name' => 'last_name', 'boost' => 3],
            ['name' => 'email', 'boost' => 3],
            ['name' => 'phone', 'boost' => 2],
            ['name' => 'alternateEmail', 'boost' => 2],
            ['name' => 'alternatePhone', 'boost' => 2],
            'notes',
            'crm_id',
            'related_crm_id',
            ['name' => 'addresses.city', 'boost' => 2],
            ['name' => 'addresses.state', 'boost' => 2],
            'addresses.address_line_1',
            'addresses.country',
            'addresses.postal_code',
        ], $this->extractExtendedAttributeIds($attributes));
    }

    /**
     * Also check that any_email filter is not present in filters. any_email is a special filter and elasticsearch
     * doesn't know how to handle it. Why do we use it? Who knows. It could be `any` reason. `Any`how we need to make
     * an exception for any_email or scary things will happen.
     *
     * @return bool
     */
    public function checkAnyEmailSpecialFilterIsPresent(): bool
    {
        return !empty($this->filters['any_email']);
    }

    /**
     * Get current RepId logged on the app
     *
     * @return int|null
     */
    protected function getCurrentRepId()
    {
        if ($loggedUser = $this->reps->getLoggedInUser()) {
            return $loggedUser->getId();
        }
        return null;
    }

    /**
     * Convert filter from QS to ES filters.
     *
     * Do not append ".keyword" since it's done on the other service since when filtering we
     * don't want to use the text type, but the keyword type.
     *
     */
    protected function cleanFiltersForElasticSearch()
    {
        $filterableAttributes = Util::keyBy(
            $this->customerAttributes->getFilterableAttributes(),
            'attribute_id'
        );
        $availableFilters = array_merge([
            'user_id',          // used by private/corporate/only_store_contact and rep mode
            'complex_query',    // used by store mode search (user_id=X||user_id=Y)
            'subcribtion_flag',
            'sms_marketing_subscription_flag',
            'tags',             // used by filter section in mobile (ids only)
            'mandatory_fields', // used by lookbook, share, sms section in mobile (email or phone)
            'city',
            'state',
            'country',
            'postal_code',
            'favorite_contacts',
            'transaction',
        ], array_keys($filterableAttributes));

        // For favorites customers to work correctly
        // we must remove the user_id from the filters
        // because the same customer can be a favorite for multiple users
        if (isset($this->filters['favorite_contacts'])) {
            $availableFilters = array_diff($availableFilters, ['user_id']);
        }

        $must = $mustNot = $should = [];

        $mysqlQuoteList = '`';

        $tagsFilter = $this->filters[CustomerTag::FILTER_FIELD] ?? CustomerTag::FILTER_AND;
        foreach ($this->filters as $key => $value) {
            // Note value may be 0 which is technically empty
            if (!in_array($key, $availableFilters) || is_null($value) || $value === '') {
                continue;
            }

            if (isset($filterableAttributes[$key])) {
                // let's hold the actual eca key since its dynamic we can build the query based on its type
                $ecaKey = $key;
                $key = $filterableAttributes[$key]['attribute_type'];
            }

            switch ($key) {
                    // Special case (need to use a or in filtering)
                case 'complex_query':
                    $splitQuery = explode('||', $value);
                    $terms = [];
                    foreach ($splitQuery as $complexValue) {
                        $splitValue =  [];
                        preg_match('/\.?([^.]*)=(.*)/', $complexValue, $splitValue);
                        if (count($splitValue) != 3) {
                            // weird complex query, not supported
                            continue;
                        }

                        // Remove all "`" (Mysql quote), because ES won't process those filters
                        $field = trim($splitValue[1], $mysqlQuoteList);
                        $terms[] = [
                            $field => $splitValue[2]
                        ];
                    }

                    // We cannot rely on should since ES v7+ since minimum_should_match is 0 when
                    // you have another MUST type. It must be wrap in another BOOL with no MUST because in this case
                    // the minimum_should_match is 1.
                    $must[] = [
                        ElasticSearch::KEYWORD_BOOL => [
                            ElasticSearch::KEYWORD_SHOULD => $terms
                        ]
                    ];
                    break;
                case 'tags':
                    $must[] = [
                        $key => [
                            CustomerTag::FILTER_KEY => $tagsFilter
                        ] + explode(',', $value),
                    ];
                    break;
                case 'state':
                case 'city':
                    // I moved the city to the same logic as state, because I don't see any reasons not to support it
                    // from the beginning.

                    // We can't use only "should", because it's possible we have other condition in should.
                    // If you have more than one conditions,
                    // you will need only one to include this record in the result.
                    // This is most of the time, not the expected result.
                    // Doing a should within a must seems to be the proper way, even if using the "filter" type.

                    // We still can have an edge case where you have a city with the same name
                    // in different province/state.
                    // There's no way to say i want to search only for city X within state Y.
                    // UI doesn't support it at the moment
                    $field = (function () use ($key) {
                        switch ($key) {
                            case 'state':
                            case 'city':
                                return "addresses.$key";
                            default:
                                return $key;
                        }
                    })();

                    $parameters = explode(",", $value);

                    $terms = [];
                    foreach ($parameters as $param) {
                        $terms[] = [$field => $param];
                    }

                    // See higher why we are using must+should
                    $must[] = [
                        ElasticSearch::KEYWORD_BOOL => [
                            ElasticSearch::KEYWORD_SHOULD => $terms
                        ]
                    ];
                    break;
                case 'mandatory_fields':
                    // @TODO: Found this in an unused call of the mobile app &filter[email]=^null
                    // (would it work to also filter nulls?)
                    // Get the mandatory fields array
                    $mandatoryFields = explode(',', $value);

                    if (!empty($mandatoryFields)) {
                        foreach ($mandatoryFields as $mandatoryField) {
                            // For each mandatory field provided compare the `field.keyword` value with an empty string
                            // This however does not filter null values
                            $mustNot[] = [
                                $mandatoryField => '',
                            ];
                        }
                    }
                    break;
                case 'subcribtion_flag':
                case 'sms_marketing_subscription_flag':
                    if (strpos($value, '^') === 0) {
                        $mustNot[] = [
                            $key => substr($value, 1),
                        ];
                    } else {
                        $must[] = [
                            $key => explode(',', $value),
                        ];
                    }
                    break;
                    // -- handle Extended Attributes --
                    // The boolean type should use an exact match where the "filter" type will be more performant
                    // but Acs want an AND operation coupled with the possibility of Yes and No being searched
                    // at the same time.
                    // As well as for both single select and multiselect: if one or more sub-options are selected,
                    // records when any of the selected options are filtered
                case CustomerAttribute::ATTRIBUTE_TYPE_BOOLEAN:
                case CustomerAttribute::ATTRIBUTE_TYPE_SINGLE_SELECT:
                case CustomerAttribute::ATTRIBUTE_TYPE_MULTI_SELECT:
                    $field = $ecaKey;
                    $parameters = explode(",", $value);
                    $selectTypes = [
                        CustomerAttribute::ATTRIBUTE_TYPE_SINGLE_SELECT,
                        CustomerAttribute::ATTRIBUTE_TYPE_MULTI_SELECT
                    ];
                    if (in_array($key, $selectTypes, true)) {
                        $options = Util::keyBy(
                            json_decode($filterableAttributes[$field]['default_value'], true),
                            'value'
                        );
                        $parameters = array_map(function ($param) use ($options) {
                            return $options[$param]['label'];
                        }, $parameters);
                    }
                    $terms = [];
                    foreach ($parameters as $param) {
                        $terms[] = [$field => $param];
                    }

                    // See higher why we are using must+should
                    $must[] = [
                        ElasticSearch::KEYWORD_BOOL => [
                            ElasticSearch::KEYWORD_SHOULD => $terms
                        ]
                    ];
                    break;
                case 'favorite_contacts':
                    $must[] = [
                        $key => $this->getCurrentRepId(),
                    ];
                    break;
                case 'transaction':
                    $queries = $this->buildElasticsearchFilterByTransaction($this->filters[$key]);
                    if (!empty($queries)) {
                        $must[] = [
                            'nested' => [
                                'path' => 'transactions',
                                'query' => [
                                    'bool' => [
                                        'must' => $queries,
                                    ]
                                ]
                            ]
                        ];
                    }
                    break;
                default:
                    $must[] = [
                        $key => $value
                    ];
                    break;
            }
        }

        $this->esFilters = [
            'must' => $must,
            'mustNot' => $mustNot,
            'should' => $should,
        ];
    }

    private function parseDate(?string $date): ?Carbon
    {
        return $date === null ? null : Carbon::parse($date, 'UTC');
    }

    private function isFilterForSqlOnly(): bool
    {
        // NOTE (important): If id is already provided for filters, then we don't need to do search
        // since the result set is already filtered to specific set of rows we want to retrieve.
        // We also need support 'ID' which is correct db column
        // and 'id' which is from legacy code but still recognized be sql
        if (!empty($this->filters['ID']) || !empty($this->filters['id'])) {
            return true;
        }

        $sqlOnlyFilters = array_keys($this->specialFilters);

        foreach ($sqlOnlyFilters as $filter) {
            if (!empty($this->filters[$filter])) {
                return true;
            }
        }
        return false;
    }

    private function isFilterForElasticsearchOnly()
    {
        $filterableAttributes = $this->customerAttributes->getFilterableAttributes();
        $elasticsearchOnlyFilters = array_merge([
            'city',
            'state',
            'country',
            'postal_code',
        ], $this->extractExtendedAttributeIds($filterableAttributes));

        foreach ($elasticsearchOnlyFilters as $filter) {
            if (isset($this->filters[$filter]) && $this->filters[$filter] != '') {
                return true;
            }
        }

        if (isset($this->filters['transaction']) && is_array($this->filters['transaction'])) {
            $transactionFilters = $this->filters['transaction'];
            $filterKeys = [
                'total',
                'date',
                'brand',
                'category_id',
            ];
            foreach ($filterKeys as $filterKey) {
                if (isset($transactionFilters[$filterKey]) && $transactionFilters[$filterKey] != '') {
                    return true;
                }
            }
        }
        return false;
    }
}
