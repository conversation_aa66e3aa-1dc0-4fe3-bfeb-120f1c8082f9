<?php

namespace Salesfloor\Services\Auth;

use Google\Auth\ApplicationDefaultCredentials;
use Google\Auth\CredentialsLoader;
use Google\Auth\FetchAuthTokenCache;
use GuzzleHttp\Client;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\Auth\Exceptions\AuthTokenException;

class TokenService
{
    private const GOOGLE_OAUTH2_SCOPES = [
        'https://www.googleapis.com/auth/cloud-platform',
        'https://www.googleapis.com/auth/firebase.messaging',
        'https://www.googleapis.com/auth/datastore',
        'https://www.googleapis.com/auth/firebase',
    ];

    private LoggerInterface $logger;
    private CacheItemPoolInterface $memoryCache;
    private Client $httpClient;

    public function __construct(
        LoggerInterface $logger,
        CacheItemPoolInterface $memoryCache,
        ?Client $httpClient = null
    ) {
        $this->logger = $logger;
        $this->memoryCache = $memoryCache;
        $this->httpClient = $httpClient ?? new Client();
    }

    /**
     * Get a Google access token using either a service account or application default credentials
     *
     * @param array|null $serviceAccount Service account credentials as an array (from JSON)
     * @param array|null $scopes Optional custom scopes to override the default ones
     * @return string The access token
     * @throws AuthTokenException If unable to retrieve a token
     */
    public function getGoogleAccessToken(
        ?array $serviceAccount = null,
        ?array $scopes = null
    ): string {
        $oauth2Scopes = $scopes ?? self::GOOGLE_OAUTH2_SCOPES;

        try {
            if (!empty($serviceAccount)) {
                $creds = CredentialsLoader::makeCredentials(
                    $oauth2Scopes,
                    $serviceAccount
                );

                $credentials = new FetchAuthTokenCache(
                    $creds,
                    [],
                    $this->memoryCache
                );
            } else {
                $credentials = ApplicationDefaultCredentials::getCredentials(
                    $oauth2Scopes,
                    null,
                    [],
                    $this->memoryCache
                );
            }

            $accessToken =
                $credentials->fetchAuthToken()['access_token'] ?? null;

            if (is_null($accessToken)) {
                $this->logger->error('Failed to get a new Google Access Token');
                throw new AuthTokenException(
                    'Error: Google Access token is empty!'
                );
            }

            return $accessToken;
        } catch (\Exception $e) {
            $this->logger->error(
                'Error getting Google access token: ' . $e->getMessage()
            );
            throw new AuthTokenException(
                'Failed to retrieve Google access token: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Get an access token for a generic OAuth2 service
     *
     * @param string $clientId The OAuth2 client ID
     * @param string $clientSecret The OAuth2 client secret
     * @param string $tokenUrl The URL to request tokens from
     * @param array $additionalParams Additional parameters to include in the token request
     * @return string The access token
     * @throws AuthTokenException If unable to retrieve a token
     */
    public function getOAuth2AccessToken(
        string $clientId,
        string $clientSecret,
        string $tokenUrl,
        array $additionalParams = []
    ): string {
        try {
            $params = array_merge(
                [
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret,
                    'grant_type' => 'client_credentials',
                ],
                $additionalParams
            );

            $response = $this->httpClient->request('POST', $tokenUrl, [
                'form_params' => $params,
                'http_errors' => false,
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                $this->logger->error(
                    "Failed to get OAuth2 Access Token. HTTP Code: $statusCode, Response: $body"
                );
                throw new AuthTokenException(
                    'Error: Failed to get OAuth2 Access Token'
                );
            }

            $data = json_decode($body, true);
            if (!isset($data['access_token'])) {
                $this->logger->error(
                    'OAuth2 response did not contain an access token'
                );
                throw new AuthTokenException(
                    'Error: OAuth2 response did not contain an access token'
                );
            }

            return $data['access_token'];
        } catch (\Exception $e) {
            $this->logger->error(
                'Error getting OAuth2 access token: ' . $e->getMessage()
            );
            throw new AuthTokenException(
                'Failed to retrieve OAuth2 access token: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Get an API key-based authentication header value
     *
     * @param string $apiKey The API key
     * @param string $headerPrefix Optional prefix for the header value (e.g., "Bearer ")
     * @return string The header value
     */
    public function getApiKeyHeader(
        string $apiKey,
        string $headerPrefix = 'Bearer '
    ): string {
        return $headerPrefix . $apiKey;
    }
}
