<?php

namespace Salesfloor\Services\Tasks;

use DateInterval;
use DateTime;
use IntlDateFormatter;
use Doctrine\DBAL\Query\QueryBuilder;
use Psr\Log\LoggerInterface;
use Salesfloor\API\Controllers\Messages;
use Salesfloor\API\Managers\Client\Customers\Legacy as Customers;
use Salesfloor\API\Managers\Reps;
use Salesfloor\API\Managers\Task\Tasks as TasksManager;
use Salesfloor\Configs\Configs;
use Salesfloor\Models\Task as TaskModel;
use Salesfloor\Models\Task;
use Salesfloor\Services\NotificationSystem;
use Salesfloor\Services\SalesfloorAPIRepository;
use Salesfloor\Services\Util;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\DeepLinks;
use Salesfloor\Services\MobileBuilds;
use Salesfloor\Services\Mail\Client as MailClient;
use Salesfloor\Services\Tasks\Automated\BaseScanner as BaseAutomatedTaskScanner;
use Silex\Application;

/**
 * Class Service Tasks
 *
 * Contains common functionality used across Tasks in relation to other SF models as well
 *
 * - Used by the cron (ReminderTask) to do some extra stuff related to tasks
 * - Used by the SearchForAutomatedTasks.php cron to detect / create automated tasks
 *
 * @package Salesfloor\Services\Tasks
 */
class Tasks
{
    /** @var Application $app */
    public $app;

    /** @var TasksManager $tasksManager */
    public $tasksManager;

    /** @var Reps $repsManager */
    public $repsManager;

    /** @var Messages $messagesManager */
    public $messagesManager;

    /** @var LoggerInterface $logger */
    public $logger;
    /** @var  MailClient $emailService */
    public $emailService;
    /** @var  Configs $configs */
    public $configs;
    /** @var  Customers $customersManager */
    public $customersManager;
    /** @var  SalesfloorAPIRepository $salesfloorApi */
    public $salesfloorApi;
    /** @var  MobileBuilds $mobileBuildService */
    public $mobileBuildService;

    public $isTeamMode; // true = store mode , false = rep mode

    public $eventService;

     /** @var NotificationSystem $notificationService */
    public $notificationService;

    public $twig;
    public $translator;

    const NOTIFICATION_MESSAGE_EXPIRED_TASK_CUSTOMER = 'pn_task_reminder_for_customer';
    const NOTIFICATION_MESSAGE_EXPIRED_TASK          = 'pn_task_reminder';

    const RESTRICT_BY_BRAND = true;

    /** @var Multilang $multilang */
    protected $multilang;

    public function __construct($app)
    {
        $this->app                 = $app;
        $this->tasksManager        = $app['tasks.manager'];
        $this->logger              = $app['logger'];
        $this->emailService        = $app['sf.mail.client'];
        $this->configs             = $app['configs'];
        $this->twig                = $app['twig'];
        $this->customersManager    = $app['customers.manager'];
        $this->salesfloorApi       = $app['repositories.salesfloor_api'];
        $this->mobileBuildService  = $app['mobile-builds.service'];
        $this->isTeamMode          = $app['configs']['retailer.storepage_mode'];
        $this->eventService        = $app['service.event'];
        $this->repsManager         = $app['reps.manager'];
        $this->translator          = $app['translator'];
        $this->notificationService = $app['service.notification-system'];
        $this->multilang           = $app['service.multilang'];
        $this->messagesManager     = $app['messages.manager'];
    }

    /**
     * Process all the tasks that have reach the "due date" + send email + send notification
     *
     * @throws \Exception
     */
    public function reminder()
    {
        // Since we use generator, we don't know yet how much data we have
        $total = 0;

        // At the moment, if the user have X different tasks that have the same reminder_date, they will
        // receive different/multiple email/notification
        foreach ($this->getReminderTasks() as $task) {
            $this->logger->debug(
                sprintf(
                    "Processing task [%d] reminder_date [%s] from Rep [%d]",
                    $task['id'],
                    $task['reminder_date'],
                    $task['userId']
                )
            );

            // If both method failed, we will try again (since we don't use reminderDate)
            // If it's only one (method), it's enough for us. We will consider it done and won't do it again.
            // Success is also represented by a communication method not being allowed by configs. This is acceptable
            $success = false;

            // Create structured $user / $customer arrays that are expected by the Send Email / Notification functions
            list($user, $customer) = $this->splitArrayIntoUserAndCustomer($task);

            try {
                $this->sendEmailTaskReminder($task, $user, $customer);
                $success = true;
            } catch (\Exception $e) {
                $this->logger->error(sprintf("Task [%d] reminder via email failed [%s]", $task['id'], $e->getMessage()));
            }

            try {
                $this->sendNotificationTaskReminder($task, $user, $customer);
                $success = true;
            } catch (\Exception $e) {
                $this->logger->error(sprintf("Task [%d] reminder via notification failed [%s]", $task['id'], $e->getMessage()));
            }

            if ($success) {
                // If both methods of communication (email / notif) are not allowed by configs, we will still reach this
                // point. This is accepted as the result of bad configuration and not system flaw.
                $this->updateLastReminderDate($task);
                $total++;
            } else {
                $this->logger->error(sprintf("Task [%d] reminder failed for both method. We will try again later", $task['id']));
            }
        }

        $this->logger->info(sprintf("Processed %d due/expired tasks", $total));

        return $total;
    }


    /**
     * Process all the tasks that have reach the "auto dismiss date"
     *
     * @throws \Exception
     */
    public function autoDismiss()
    {
        //Here we use raw query, query builder doesn't work well with update and inner join
        //https://stackoverflow.com/questions/15293502/doctrine-query-builder-not-working-with-update-and-inner-join
        $mysqlRepository = $this->tasksManager->getRepository();

        $query = <<<SQL
        UPDATE sf_task t
                  INNER JOIN wp_users u ON t.user_id = u.ID
                  INNER JOIN sf_store s ON u.store = s.store_id
                  INNER JOIN sf_corporate_tasks sct on  sct.id = t.parent_id
                  SET  t.status = :dismissed, t.resolution_note = :note, t.updated_at = :currentTime
        WHERE t.status NOT IN (:finishedStatus) AND sct.is_deleted != 1 AND sct.auto_dismiss_time IS NOT NULL
        AND CONVERT_TZ(sct.auto_dismiss_time, s.timezone, 'UTC') <= :currentTime
SQL;

        $count = $mysqlRepository->executeUpdate(
            $query,
            [
                'currentTime'    => gmdate('Y-m-d H:i:s'),
                'dismissed'      => TaskModel::STATUS_DISMISSED,
                'note'           => TaskModel::AUTO_DISMISS_NOTE,
                'finishedStatus' => [TaskModel::STATUS_RESOLVED, TaskModel::STATUS_DISMISSED],
            ],
            ['finishedStatus' => \Doctrine\DBAL\Connection::PARAM_STR_ARRAY]
        );
        return $count;
    }

    /**
     * Generator that gets all unresolved tasks for a user
     *
     * @param int $userId The user for which to get all unresolved tasks
     * @see also getUnresolvedTasksForUser
     */
    public function yieldUnresolvedTasksForUser(int $userId): iterable
    {
        $qy = "SELECT id, user_id, customer_id, details, `type`, created_at, status FROM sf_task"
            . " WHERE status = '" . TaskModel::STATUS_UNRESOLVED . "' AND user_id = :userId";

        $stmt = $this->tasksManager->getRepository()->executeQuery($qy, ['userId' => $userId]);

        while ($row = $stmt->fetch()) {
            $task = new Task();
            $this->tasksManager->setFields(
                $task,
                [
                    "id" => $row['id'],
                    "user_id" => $row['user_id'],
                    "customer_id" => $row['customer_id'],
                    "type" => $row['type'],
                    "details" => $row['details'],
                    "status" => $row['status'],
                    "created_at" => $row['created_at'],
                ]
            );
            yield $task;
        }
    }

    /**
     * Get all unresolved tasks for a user.
     *
     * @param int $userId The user for which to get all unresolved tasks
     * @return array List of Task models with the id, user_id, customer_id, status, and type fields filled out.
     * @see also yieldUnresolvedTasksForUser
     */
    public function getUnresolvedTasksForUser(int $userId): array
    {
        return iterator_to_array($this->yieldUnresolvedTasksForUser($userId));
    }

    /**
     * Dismiss all unresolved tasks assigned to the given user
     *
     * @param int $userId The users whose unresolved tasks to dismiss
     * @param string $note The note to set on all the tasks this dismisses
     * @param string $source The source to set on all the tasks this dismisses
     * @return int The number of dismissed tasks
     */
    public function dismissUnresolvedTasksForUser(int $userId, string $note, string $source)
    {
        $db = $this->tasksManager->getRepository();

        // To make sure that the list of tasks to dismiss and the set of tasks
        // that actually get updated are the same, we run the first two queries
        // in the same transaction.
        $db->beginTransaction();

        $tasksToDismiss = $this->getUnresolvedTasksForUser($userId);

        $qy = "UPDATE sf_task"
            . " SET status = '" . TaskModel::STATUS_DISMISSED . "'"
            . "     , resolution_date = NOW()"
            . "     , resolution_note = :note"
            . "     , updated_at = NOW()"
            . " WHERE status = '" . TaskModel::STATUS_UNRESOLVED  . "' AND user_id = :userId";

        $nUpdated = $db->executeUpdate(
            $qy,
            [ 'userId' => $userId, 'note' => $note ]
        );

        // It doesn't matter so much whether the tracking queries work or not,
        // and since there can be an arbitrarily large number of them (depending
        // on the number of tasks dismissed), let's commit the transaction now
        // and do the tracking afterwards.
        $db->commit();

        foreach ($tasksToDismiss as $originalTask) {
            $updatedTask = clone $originalTask;
            $updatedTask->status = TaskModel::STATUS_DISMISSED;
            $this->recordUpdateEvent($updatedTask, $originalTask, $source);
        }

        return $nUpdated;
    }

    /**
     * Keep track of the last time the cron ran on this task, so if we want, we could run the cron multiple time with
     * any impact (multiple email) being sent
     *
     * @param array $task
     *
     * @throws \Exception
     */
    private function updateLastReminderDate(array $task)
    {
        /** @var Task $task */
        $taskModel = $this->tasksManager->getOneOrNull([
            'id' => $task['id']
        ]);

        if (empty($taskModel)) {
            throw new \Exception(sprintf("The task [%d] you want to process doesn't exist anymore", $task['id']));
        }

        $taskModel->last_reminder_date = Util::now();

        $this->tasksManager->save($taskModel);
    }

    /**
     * Function validates whether the $task + $user + $customer arrays that are used to
     * Send Email / Notifications for reminders contain all the necessary fields. If a field
     * is missing an Exception will be thrown.
     * @param  array     $task     Array of task information
     * @param  array     $user     Array of user information
     * @param  array     $customer Array of customer information
     * @throws Exception
     */
    private function validateTaskUserCustomerFields(array $task, array $user, array $customer)
    {
        $required = [
            'task'     => ['id', 'details', 'reminder_date', 'customer_id', 'type'],
            'user'     => ['ID', 'user_email', 'user_login', 'firstname', 'timezone', 'store'],
            'customer' => ['ID', 'first_name', 'last_name', 'name', 'email', 'phone'],
        ];
        foreach ($required as $variable => $fields) {
            // In the case of customer array, either we have it or we dont
            if ($variable === 'customer' && empty($$variable)) {
                continue;
            }
            foreach ($fields as $field) {
                if (!in_array($field, array_keys($$variable))) {
                    throw new \Exception("Missing required field '$field' from the '$variable' variable for sending Task Reminder");
                }
            }
        }
    }

    /**
     * Returns the value of the requested config $key, or throws an exception if undefined
     * @param $key
     * @return mixed
     * @throws \Exception
     */
    private function checkConfig($key)
    {
        if (!$this->configs->offsetExists($key)) {
            throw new \Exception('Requested config key does not exist: ' . $key);
        }
        return $this->configs[$key];
    }

    /**
     * Send email to the rep with task information
     *
     * @param array $task     Task info
     * @param array $user     User info
     * @param array $customer Customer info
     * @return bool True if Email sent, False if Email sending disabled by config
     * @throws \Exception
     */
    public function sendEmailTaskReminder(array $task, array $user, array $customer)
    {
        // Check for required fields
        $this->validateTaskUserCustomerFields($task, $user, $customer);

        $permissionKey = 'sf.task.' . mb_strtolower($task['type']) . '.' . BaseAutomatedTaskScanner::CONFIG_KEY_EMAILS_ENABLED;
        if (!$this->checkConfig($permissionKey)) {
            return false;
        }

        // If the task is associated to a customer, we have a different layout. Used raw twig template in nunjucks
        $customerName = '';

        $subject = "[Salesfloor] Task Reminder";
        if (!empty($task['customer_id'])) {
            if (empty($customer)) {
                // TODO : If we pass null to the filter, we get some weird behaviour (getAll will return all even if no match)
                $customer = $this->customersManager->getOneOrNull([
                    'ID' => $task['customer_id'],
                ]);
                if (!empty($customer)) {
                    $customer = $customer->toArray(true);
                }
            }

            if (!empty($customer)) {
                $customerName = $customer['name'] ?: $customer['first_name'] . ' ' . $customer['last_name'];

                if (!empty(trim($customerName))) {
                    $subject .= ' for ' . $customerName;
                }
            }
        }

        // Detect the locale of the rep
        $locale = $this->multilang->getLocaleFromUserOrStore($user['ID'], null);

        // SF-21808 - A user is not elligible if corporate email is required for the retailer
        // And the User has selling mode ON
        if (!$this->repsManager->isEligibleForEmailNotification($user['ID'])) {
            $reminderDate = $this->multilang->getLocalizedDate(new \DateTime($task['reminder_date']), $locale, IntlDateFormatter::LONG, IntlDateFormatter::LONG);
            // Prepare the notification message

            $notificationService = $this->notificationService;
            $notificationService = $notificationService();
            $notificationService->setTitle('title.task.reminder');
            $notificationService->setBodyListInfo('task.due-date', $reminderDate);
            $notificationService->setCustomerName($customerName);
            $notificationService->setCustomerEmail($customer['email']);
            $notificationService->setCustomerPhone($customer['phone']);
            $notificationService->setBodyListInfo('task.detail', str_replace("\n", "<br>", $task['details']));

            // Send the notification message
            $this->messagesManager->createNotificationSystemMessage(
                $user['ID'],
                $notificationService->getTitle($locale),
                $notificationService->getBodyList($locale)
            );

            return;
        }


        $template = $this->twig->render($this->multilang->addLocaleToEmailTemplate('emails/common/reminder_task.html', $locale), [
            'customer' => $customer,
            'customer_name_is_empty' => empty(trim($customerName))
        ]);

        // Convert date to the proper timezone (the one from the store or Montreal if empty)
        // WESHOULD display proper information in email (EST) for example so they have all the information
        $user['timezone'] = $user['timezone'] ?: 'America/Montreal';

        $template = str_replace([
            '{USER_FIRST_NAME}',
            '{VIEW_TASK_LINK}',
            '{RETAILER_PRETTY_NAME}',
            '{CUSTOMER_NAME}',
            '{EXPIRATION_DATE}',
            '{TASK_DETAIL}',
            '{BACKOFFICEPAGE}',
            '{RETAILER_IDSTR}',
        ], [
            $user['firstname'],
            $this->getTaskDeepLink($task),
            $this->configs['retailer.pretty_name'],
            $customerName,
            Util::getLocaleTime('%A %e %B %Y, %I:%M %p', Util::getTimezoneTime($task['reminder_date'], $user['timezone'])), // Try to display locale based time
            $task['details'],
            $this->configs['salesfloor_storefront.host'] . '/backoffice',
            $this->configs['retailer.idstr'],
        ], $template);

        $message = [
            'email'                => $user['user_email'],
            'subject'              => $subject,
            'sender_email'         => $this->configs['retailer.emails.no_reply_address'],
            'sender_name'          => $this->configs['retailer.emails.no_reply_name'],
            'html_message'         => $template,
            'track_clicks_in_html' => false,
        ];

        $message[MailClient::PARAMS_KEY_CATEGORIES] = [MailClient::CATEGORY_TRANSACTIONAL];

        $this->emailService->sendRelay($message);
        return true;
    }

    /**
     * Get deeplink url or desktop url
     *
     * @param $task
     *
     * @return mixed|string
     */
    private function getTaskDeepLink($task)
    {
        $deepLinks = new DeepLinks($this->configs, new MobileBuilds($this->configs, $this->app['cloudstorage.factory']->getClient('mobile_s3_bucket')));

        return $deepLinks->generate('TASKREMINDER', '', $task['id']);
    }

    /**
     * Send a notification to the rep
     *
     * @param array $task
     * @param array $user
     * @param array $customer
     * @return bool True if Notification sent, False if notification sending disabled by config
     */
    public function sendNotificationTaskReminder(array $task, array $user, array $customer)
    {
        // Check for required fields
        $this->validateTaskUserCustomerFields($task, $user, $customer);

        $permissionKey = 'sf.task.' . mb_strtolower($task['type']) . '.' . BaseAutomatedTaskScanner::CONFIG_KEY_NOTIFICATIONS_ENABLED;
        if (!$this->checkConfig($permissionKey)) {
            return false;
        }
        $notification = [
            'inapp' => [
                'alertBox'     => 'false',
                'event_action' => 'task_reminder',
                'task_id'      => $task['id'],
                'force_inapp'  => 'true',
                'sound'        => 'true',
            ],
        ];

        // Detect the locale of the rep
        $locale = $this->multilang->getLocaleFromUserOrStore($user['ID'], null);

        // Notification message is a bit different if it's associated to a customer
        $getNotificationMessage = function () use ($customer, $locale) {
            if (isset($customer['name']) ? trim($customer['name']) : false) {
                return $this->translator->trans(self::NOTIFICATION_MESSAGE_EXPIRED_TASK_CUSTOMER, ['%name%' => $customer['name']], null, $locale);
            } else {
                return $this->translator->trans(self::NOTIFICATION_MESSAGE_EXPIRED_TASK, [], null, $locale);
            }
        };

        // TODO : Stop calling backoffice and create a service for that
        $pushUrl = 'pushNotification/firebase/publish';
        if ($this->isTeamMode) {
            $pushUrl                 .= '/store';
            $notification['store']   = $user['store'];
            // Same prefix as text messaging
            $notification['message'] = $getNotificationMessage();
        } else {
            $notification['reps']    = [$user['user_login'] => true];
            $notification['message'] = $getNotificationMessage();
        }

        $this->salesfloorApi->insertJSON($pushUrl, $notification);
        return true;
    }

    /**
     * Get all tasks that need to be reminded
     *
     * Since we don't want to get all in memory, use generator
     *
     * @return \Generator
     */
    private function getReminderTasks()
    {
        // Get all tasks that need to be reminded. GetAll() doesn't support gt, use qB
        /** @var QueryBuilder $qB */
        $qB = $this->tasksManager->getRepository()->getQueryBuilder();

        // Force second to be 00 so we never double the task reminder
        $now = gmdate('Y-m-d H:i:00');

        // Use configs in case in the future we want to change the frequency of the cron
        $interval = "PT" . $this->configs['sf.task.reminder.cron'] . 'M';

        $date = new DateTime($now, new \DateTimeZone('UTC'));
        $date->add(new DateInterval($interval));
        $nextExecutionDate = $date->format('Y-m-d H:i:00');

        $this->logger->debug(
            sprintf(
                "Will process the task from [%s] to [%s]",
                $now,
                $nextExecutionDate
            )
        );

        // All the fields coming out of this query will be in one array, we have to make sure that there
        // is no name contention. Such as the id / ID fields, task.type vs customer.type, etc...
        // Do not to use alias.* in the select statement. Check the expected fields in validateTaskUserCustomerFields
        $qB
            ->select([
                't.id, t.type, t.reminder_date, t.details, t.customer_id, t.last_reminder_date',
                'w.ID as userId, w.user_login, w.user_email',
                'c.ID as customerId, c.name as customerName, c.first_name as customerFirstName, c.last_name as customerLastName',
                'c.email as customerEmail, c.phone as customerPhone',
                // What should be the value if there's not firstname. Use user_alias for now
                'COALESCE(um.meta_value, w.user_alias) as userFirstName',
                's.timezone as timezone, w.store'
            ])
            ->from('sf_task', 't')
            ->innerJoin('t', 'wp_users', 'w', "t.user_id = w.ID")
            ->leftJoin('w', 'sf_store', 's', 's.store_id = w.store')
            ->leftJoin('t', 'sf_customer', 'c', "c.ID = t.customer_id")
            ->leftJoin('w', 'wp_usermeta', 'um', "w.ID = um.user_id AND um.meta_key = 'first_name'")
            ->where($qB->expr()->andX(
                $qB->expr()->eq('t.status', $qB->expr()->literal(TaskModel::STATUS_UNRESOLVED)),
                $qB->expr()->lt('t.reminder_date', $qB->expr()->literal($nextExecutionDate)),
                $qB->expr()->orX(
                    // New task (Not reminded yet)
                    $qB->expr()->isNull('t.last_reminder_date'),
                    // Old task (Reminded)
                    // gte() is important now to cover the edge case where last_reminder_date happen in the first second
                    $qB->expr()->gte('t.reminder_date', sprintf('t.last_reminder_date + INTERVAL %d MINUTE', $this->configs['sf.task.reminder.cron']))
                )
            ));

        $stmt = $qB->execute();

        while ($row = $stmt->fetch()) {
            yield $row;
        }
    }

    /**
     * Function attempts to split the $fields array into a valid $user and $customer array
     * All important fields for Email / Notification generation are included
     * @param  array  $fields Generic array with User and Customer data
     * @return array          With first entry representing $user and second $customer
     */
    public function splitArrayIntoUserAndCustomer(array $fields)
    {
        // Create structured $user / $customer arrays that are expected by
        // sendEmailTaskReminder & sendNotificationTaskReminder
        $user = [
            'ID' => (isset($fields['userId']) ? $fields['userId'] : null),
            'user_login' => (isset($fields['user_login']) ? $fields['user_login'] : null),
            'user_email' => (isset($fields['user_email']) ? $fields['user_email'] : null),
            'firstname' => (isset($fields['userFirstName']) ? $fields['userFirstName'] : null),
            'timezone' => (isset($fields['timezone']) ? $fields['timezone'] : null),
            'store' => (isset($fields['store']) ? $fields['store'] : null),
        ];

        if (!empty($fields['customerId'])) {
            $customer = [
                'ID' => (isset($fields['customerId']) ? $fields['customerId'] : null),
                'first_name' => (isset($fields['customerFirstName']) ? $fields['customerFirstName'] : null),
                'last_name' => (isset($fields['customerLastName']) ? $fields['customerLastName'] : null),
                'name' => (isset($fields['customerName']) ? $fields['customerName'] : null),
                'email' => (isset($fields['customerEmail']) ? $fields['customerEmail'] : null),
                'phone' => (isset($fields['customerPhone']) ? $fields['customerPhone'] : null),
            ];
        } else {
            $customer = [];
        }
        return [$user, $customer];
    }

    /**
     * Function will return the appropriate User ID for the given context.
     * In rep mode, the inputted $userId will be returned unaltered
     * In team mode, the inputted $userId will be translated to the store user's
     * @param  integer $userId Id of the user (usually logged in)
     * @return integer         Id of the user to use in this context
     */
    public function getUserIdForContext($userId)
    {
        return $this->repsManager->getUserIdForContext($this->isTeamMode, $userId);
    }

    /**
     * Record an appropriate event for a newly created task
     * @param  TaskModel $task   Newly created task
     * @param  string    $source Calling method / file details
     * @return boolean           True if event was recorded, false otherwise
     */
    public function recordCreationEvent(TaskModel $task, $source = '')
    {
        return $this->recordUpdateEvent($task, null, $source);
    }


    /**
     * Record an approriate event for an updated task (also for newly created ones)
     * @param  TaskModel      $updatedTask  Newly Updated task (or created)
     * @param  TaskModel|null $originalTask Previous version of task (when updating)
     * @param  string         $source       Calling method / file details
     * @return boolean                      True if event was recorded, false otherwise
     */
    public function recordUpdateEvent(TaskModel $updatedTask, TaskModel $originalTask = null, $source = '')
    {
        if (empty($originalTask) && empty($updatedTask)) {
            $this->logger->error('Received empty original and updated Task');
            return false;
        } elseif (empty($originalTask) && ($updatedTask->status !== TaskModel::STATUS_UNRESOLVED)) {
            $this->logger->error('Create Task scenario with incorrect status');
            return false;
        } elseif (!empty($originalTask) && !empty($updatedTask) && (($originalTask->getId() !== $updatedTask->getId()) || ($originalTask->status === $updatedTask->status) || ($updatedTask->status === TaskModel::STATUS_UNRESOLVED))) {
            return false;
        }

        try {
            $this->eventService->trackEvent(
                $this->getTaskEventType($updatedTask),
                (!empty($source) ? $source : ''),
                $updatedTask->customer_id,
                $updatedTask->user_id,
                null,
                intval($updatedTask->getId())
            );
        } catch (\Exception $recordEventException) {
            $this->logger->error('Error saving Task Related Event: ' . $recordEventException->getMessage());
            return false;
        }
        return true;
    }

    /**
     * Get the EventType to be used when tracking create/update Tasks
     * @param  TaskModel $task Newly updated / created task
     * @return string          Event Type to use
     * @throws Exception       When $task status does not matche possible list
     */
    public function getTaskEventType(TaskModel $task)
    {
        switch ($task->status) {
            case TaskModel::STATUS_UNRESOLVED:
                $action = 'CREATED';
                break;
            case TaskModel::STATUS_RESOLVED:
                $action = 'RESOLVED';
                break;
            case TaskModel::STATUS_DISMISSED:
                $action = 'DISMISSED';
                break;
            default:
                throw new \Exception('Cannot generate Task Event Name because of invalid status: ' . $task->status);
                break;
        }
        return 'SF_EVENT_TASK_' . strtoupper($task->type) . '_' . $action;
    }

    public function autoResolve(int $taskId, string $source): void
    {
        if (!$this->configs['retailer.tasks.auto-resolve.is_enabled']) {
            return;
        }

        /** @var Task $originalTask */
        $originalTask = $this->tasksManager->getById($taskId);
        if ($originalTask !== null && $originalTask->status !== Task::STATUS_RESOLVED) {
            $resolvedTask = clone $originalTask;
            $resolvedTask->status = Task::STATUS_RESOLVED;
            $this->tasksManager->save($resolvedTask);
            $this->tasksManager->invalidateClassCache();
            $this->recordUpdateEvent($resolvedTask, $originalTask, $source);
        }
    }
}
