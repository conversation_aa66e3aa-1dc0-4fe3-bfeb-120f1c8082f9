<?php

namespace Salesfloor\Services\Tasks\Automated\Factory;

use Salesfloor\Services\Tasks\Automated\AutomatedTaskException;
use Salesfloor\Services\Tasks\Automated\RetailerCustomerSoonToLapseFiltered;
use Silex\Application;

class RetailerCustomerSoonToLapseFilteredMultiple extends FactoryMultiple
{
    const TASK_TYPE_PRETTY = 'Retailer Customer Soon To Lapse Multiple - Filtered';

    protected $app;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        $this->dynamicRuleSets = $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'];
    }

    /**
     * Retrieves an instance of the RetailerCustomerSoonToLapseFiltered Task
     *
     * @return RetailerCustomerSoonToLapseFiltered
     * @throws AutomatedTaskException
     */
    public function getSingleTaskScanner()
    {
        return new RetailerCustomerSoonToLapseFiltered($this->app);
    }
}
