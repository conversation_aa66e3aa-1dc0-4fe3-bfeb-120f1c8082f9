<?php

namespace Salesfloor\Services\Tasks\Automated;

use Doctrine\DBAL\Query\QueryBuilder;
use Salesfloor\API\Managers\TaskRetailerTransactions;
use Salesfloor\Models\Task as TaskModel;
use Silex\Application;

/**
 * Abstract Base Class that sets the structure and additional shared functionality
 * for RetailerTransaction based Automated Task scenarios
 * Class BaseRetailerTransaction
 * @package Salesfloor\Services\Tasks\Automated
 */
abstract class BaseRetailerTransaction extends BaseTransaction
{
    const TRX_TYPE_SALE = 'Sale';

    const TRANSLATION_DETAILS_KEY = '';

    protected $daysBack = null;

    /** @var int maximum number of tasks that a rep could receive */
    protected $maxPerOwnerThreshold ;

    /** @var string  translation key for task description, so it could change by different days back */
    protected $dynamic_translation_details_key;

    /** @var TaskRetailerTransactions $managerTaskRetailerTransactions */
    protected $taskRetailerTransactionsManager;

    public function loadDependencies(Application $app)
    {
        parent::loadDependencies($app);

        $this->taskRetailerTransactionsManager = $app['task_retailer_transactions.manager'];

        $maxPerOwnerThreshold = $this->getConfig(static::CONFIG_KEY_MAX_PER_OWNER);
        $this->maxPerOwnerThreshold = $maxPerOwnerThreshold;
    }

    /**
     * Validate configs needed in case by case extending class
     * PP-61 All Retailer Transaction (clienteling) based tasks should also check that clienteling mode is enabled
     * before deciding to look for clienteling (retailer_transaction) data that could create tasks
     * @return bool
     */
    public function validateConfigs()
    {
        return $this->configs['retailer.clienteling.mode'];
    }

    /**
     * Override the number of days back to check
     * @param $daysBack integer
     * @return $this
     */
    public function setDaysBack($daysBack)
    {
        $this->daysBack = $daysBack;

        return $this;
    }

    /**
     * Get Days back to look for transactions
     * @return integer days back to look for transactions
     */
    public function getDaysBack()
    {
        // Use parent's config
        if (is_null($this->daysBack)) {
            return parent::getDaysBack();
        }

        return $this->daysBack;
    }

    public function buildQueryScanForTasks()
    {
        $qb = $this->getQueryBuilder();
        // Execution: Retailer Transaction -> Retailer Customer -> SF Customer To Retailer Customer Mapping -> SF Customer -> SF User(Rep) (and additional details tables)
        $qb->select('user.ID as userId')
            ->from('sf_retailer_transaction', 'trx')
            ->innerJoin('trx', 'sf_retailer_customers', 'retailerCustomer', 'trx.customer_id = retailerCustomer.customer_id')
            ->innerJoin('retailerCustomer', 'sf_customers_to_retailer_customers', 'customerMapping', 'retailerCustomer.id = customerMapping.retailer_customer_id')
            // This mapping table does not function properly as some rows may represent NOW invalid matches based on the JSON value in the `comment` field
            ->innerJoin('customerMapping', 'sf_customer', 'customer', 'customerMapping.customer_id = customer.ID')
            ->innerJoin('customer', 'wp_users', 'user', 'customer.user_id = user.ID')
            ->innerJoin('user', 'sf_store', 'store', 'user.store = store.store_id')
            ->where('trx.trx_type = "' . static::TRX_TYPE_SALE . '"');

        $this->restrictUsersToActiveSellers($qb, 'user');

        $this->addFilterCustomerSubscriptionCondition($qb, 'customer');
        $this->addFilterRetailerCustomerSubscriptionCondition($qb, 'retailerCustomer');

        // When team mode, must convert all User (or storeUser) IDs to the first related storeUserId
        $userTable = 'user';
        if ($this->isTeamMode) {
            $userTable = 'storeUser';
            // Tasks are associated to the FIRST store user (in the case of team multibrand)
            $qb->select('storeUser.ID as userId')
                ->innerJoin('store', 'wp_users', 'storeUser', 'store.store_user_id = storeUser.ID AND storeUser.`type` = "' . static::USER_TYPE_STORE . '"')
                // Restrict CUSTOMERs to STORE Customers
                ->andWhere('customer.user_id = storeUser.ID');

            if (!empty($this->brand)) {
                $qb->andWhere('store.brand = "' . $this->brand . '"');
            }
        } else {
            $qb->andWhere('user.`type` = "' . static::USER_TYPE_REP . '"');
        }

        // Add extra user / customer information to the results that is used to build the Task Reminder Email
        $qb->addSelect([
            "user.store as userStoreId",
            "trx.trx_thread_id as threadId",
            "$userTable.user_email, $userTable.user_login, $userTable.store",
            "customer.ID as customerId, customer.name as customerName, customer.first_name as customerFirstName, customer.last_name as customerLastName",
            "customer.email as customerEmail, customer.phone as customerPhone",
            "retailerCustomer.customer_id as retailer_customer_id",
            "COALESCE(um.meta_value, $userTable.user_alias) as userFirstName",
            "trx.trx_total as transaction_total, trx.id as transaction_id, retailerCustomer.id as retailer_row_identifier",
            "store.timezone as timezone, trx.trx_date as transaction_date",
        ])
            ->leftJoin($userTable, 'wp_usermeta', 'um', "$userTable.ID = um.user_id AND um.meta_key = 'first_name'");

        $this->addQuerySelectTopTransaction($qb);

        $this->restrictQueryToRetailerAssignedContact($qb);

        $this->addInnerQueryGroupBy($qb);

        // Add other restrictions depending on extended classes
        $this->addQueryRestrictions($qb);
        $this->addQueryOrderBy($qb);
        $this->addMinTransactionCondition($qb);

        // Ensure that for any given retailerCustomer on a day, only one task will be created
        // To get the actual max transaction, we have to wrap this query as a subquery and do additional
        // querying.
        $query = sprintf($this->getQueryWrapper(), $qb->getSQL());

        return $query;
    }

    /**
     * Function scans for BaseRetailerTransaction scenarios. Every row of the result represents a Task to create
     * Calls extended classes for possible query modifications / post query filtering of the results
     * i.e. Add extra sorting / Filter to top 5 transactions
     * @return array
     */
    public function processScanForTasks()
    {
        $query = $this->buildQueryScanForTasks();

        $results = $this->repositories->executeRawQuery($query);
        return $results->fetchAll();
    }

    /**
     * Allows for extending classes to add an order by condition before execution
     * @param QueryBuilder $qb
     */
    public function addQueryOrderBy(QueryBuilder $qb)
    {
        return;
    }

    /**
     * For each customer, we'll select the top transaction based on the criteria, for example:
     * transaction_total, transaction_date, etc.
     * @param QueryBuilder $qb
     */
    public function addQuerySelectTopTransaction(QueryBuilder $qb)
    {
        $qb->addSelect('MAX(trx.trx_total) as max_transaction_total');
    }

    /**
     * Add group by to the inner query
     * @param QueryBuilder $qb
     */
    protected function addInnerQueryGroupBy(QueryBuilder $qb)
    {
        $qb->groupBy('retailer_customer_id');
    }

    /**
     * In order to ensure that the task is linked to the most valuable transaction for a customer,
     * we have to wrap the query and self join. By querying for the highest value transaction in the
     * inner query, we know what the highest value for that day for that customer is.
     *
     * @return string
     */
    protected function getQueryWrapper()
    {
        $restrictions = $this->getWrapperRestrictions();

        $userSortField = $this->getTopTransactionUserSortField();

        $wrapperCondition = $this->getWrapperJoinConditions();

        return <<<SQL
select 
rt.trx_total as transaction_total, customerId, rt.trx_thread_id as threadId,
userId, userStoreId, user_email, user_login, store, data.retailer_customer_id
from (%s) as data
inner join sf_retailer_transaction rt 
  ON rt.customer_id = data.retailer_customer_id $wrapperCondition
$restrictions 
order by {$userSortField} asc, transaction_total desc
SQL;
    }

    /**
     * Add additional restrictions to the wrapper query.
     * This where ensures we're only looking at transactions for the correct day (since a customer
     * may have spent the same amount on different days).
     * @return string
     * @return string
     */
    protected function getWrapperRestrictions()
    {
        return 'where DATE(rt.trx_date) = ' . $this->calculateDaysBackDateSql();
    }

    /**
     * Adding extra table join condition on the wrapper to filter the top transactions for
     * each customer based on the criteria, for example: transaction_total, transaction_date, etc.
     *
     * @return string
     */
    protected function getWrapperJoinConditions()
    {
        return ' AND rt.trx_total = data.max_transaction_total';
    }

    /**
     * Functions below are used by specific extending classes
     */

    /**
     * Ensure that the transaction happened in the HOME store
     * Requires the trx and store aliases in the $qb query
     * @param QueryBuilder $qb
     */
    public function restrictQueryToHomeStoreTransactionsOnly(QueryBuilder $qb)
    {
        $qb->andWhere('trx.location = store.retailer_store_id');
    }

    /**
     * Ensure tasks for a customer are only given to users that were assigned the customer by
     * the retailer via the CI importer.
     *
     * When creating tasks, checking for the existence of retailer_customer_id on a contact
     * is sufficient to determine if the contact is the "assigned" version. CI imports only
     * create contacts when an assignment is possible. Matching will associate existing
     * contacts to the CI customer, but will not update the retailer_customer_id in the
     * customer table.
     *
     * @see https://salesfloor.atlassian.net/browse/SF-22014
     *
     * @param QueryBuilder $qb
     */
    public function restrictQueryToRetailerAssignedContact(QueryBuilder $qb)
    {
        $qb->andWhere('customer.retailer_customer_id IS NOT NULL');
    }

    /**
     * We want to ensure that we're sorting transactions by task owner, as it simplifies some logic
     * for the task filtering. In team mode, it should be the store id, in rep the rep user id.
     * @return string
     */
    protected function getTopTransactionUserSortField()
    {
        if ($this->isTeamMode) {
            return 'userStoreId';
        } else {
            return 'userId';
        }
    }

    /**
     * Keep only the highest X transactions. Keep in mind that we assume the tasks are already sorted (via MySQL)
     *
     * @param array $tasks  List of tasks from processScanForTasks()
     * @param string $type  The field used for grouping (userId or store)
     *
     * @return array
     */
    protected function filterTasksTopTransactions(array $tasks, string $type)
    {
        if (empty($tasks)) {
            return $tasks;
        }

        $filtered = $topTrxs = [];
        foreach ($tasks as $task) {
            $groupByField = $task[$type];
            if (!array_key_exists($groupByField, $topTrxs)) {
                $topTrxs[$groupByField] = [];
            }
            if (count($topTrxs[$groupByField]) < $this->maxPerOwnerThreshold) {
                $topTrxs[$groupByField][] = $task['transaction_total'];
                $filtered[] = $task;
            }
        }

        return $filtered;
    }

    /**
     * Filter possible task list to TOP X (default 5) transactions per store
     * Only works in conjunction with orderQueryTopTransactionsPerStore()
     * @param array $tasks
     * @return array
     */
    public function filterTasksTopTransactionsPerStore(array $tasks)
    {
        return $this->filterTasksTopTransactions($tasks, 'store');
    }

    /**
     * Filter possible task list to TOP X transactions per user.
     * Only works in conjunction with orderQueryTopTransactionsPerUser()
     *
     * @param array $tasks
     * @return array Tasks filtered by top transactions
     */
    public function filterTasksTopTransactionsPerUser($tasks)
    {
        return $this->filterTasksTopTransactions($tasks, 'userId');
    }

    protected function afterTaskCreate(TaskModel $task, $taskInfo = [])
    {
        $model = $this->taskRetailerTransactionsManager->create([
            'task_id' => $task->id,
            'trx_thread_id' => $taskInfo['threadId']
        ]);

        $this->taskRetailerTransactionsManager->save($model);
    }

    /**
     * In this specific case we will be filtering to TOP X (default 5) transactions per store
     * This is facilitated by the ORDER BY that is happening above in the addQueryOrderBy()
     * @inheritdoc
     */
    protected function applyTopTransactionsFilterByMode($tasks)
    {
        if ($this->isTeamMode) {
            return $this->filterTasksTopTransactionsPerStore($tasks);
        } else {
            return $this->filterTasksTopTransactionsPerUser($tasks);
        }
    }

    protected function setMaxPerOwnerThreshold($maxPerOwner)
    {
        $this->maxPerOwnerThreshold = $maxPerOwner;
    }

    /**
     * @inheritdoc
     */
    public function getTaskDetails($plainText = true, $locale = null)
    {
        return $this->translationService->trans($this->getTaskDetailsKey(), [], null, $locale);
    }

    public function getTaskDetailsKey(): string
    {
        return static::TRANSLATION_DETAILS_KEY;
    }

    public function setDynamicTaskDetailsKey($key)
    {
        $this->dynamic_translation_details_key = $key;
    }

    public function getDynamicTaskDetailsKey()
    {
        $key = $this->dynamic_translation_details_key;
        if (empty($key)) {
            $key = static::TRANSLATION_DETAILS_KEY;
        }
        return $key;
    }

    /**
     * @inheritdoc
     */
    public function processFilterScannedTasks(array $tasks)
    {
        return $this->applyTopTransactionsFilterByMode($tasks);
    }
}
