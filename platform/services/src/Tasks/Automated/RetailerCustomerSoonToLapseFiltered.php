<?php

namespace Salesfloor\Services\Tasks\Automated;

use Doctrine\DBAL\Query\QueryBuilder;
use Salesfloor\Models\Task;

/**
 * PP-29 Soon To Lapse Customer
 * Limited to the TOP 5 (configurable) retailer transactions for a given X Days back (configurable)
 * Transaction must have happened in the HOME store of the given retailer customer
 */
class RetailerCustomerSoonToLapseFiltered extends BaseRetailerTransaction
{
    const TASK_TYPE = Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED;
    const TASK_TYPE_PRETTY = 'Retailer Customer Soon To Lapse - Filtered';

    const SUCCESS_EVENT_TYPE = 'SF_EVENT_TASK_SOON_TO_LAPSE';

    const TRANSLATION_DETAILS_KEY = 'api_automated_tasks_detail_retailer_customer_soon_to_lapse_filtered';

    /**
     * A status flag that switch sql when scanning task by condition if employee Id in transaction match retailer_customer->customer->rep's employeeId
     * @var bool
     */
    private $shouldTrxAndUserSameEmployeeId = false;

    public function processScanForTasks()
    {
        if ($this->isSecondaryEmployeeAssignEnabled() == true) {
            $matchedTasks   = $this->processScanForEmployeeIdMatchTasks();
            $noMatchedTasks = $this->processScanForEmployeeIdNoMatchTasks();

            $tasks = $this->mergeSecondaryEmployeeAssignTasks($matchedTasks, $noMatchedTasks);
        } else {
            $tasks = parent::processScanForTasks();
        }
        return $tasks;
    }

    private function mergeSecondaryEmployeeAssignTasks($matchedTasks, $noMatchedTasks)
    {
        $allTasks = array_merge($matchedTasks, $noMatchedTasks);

        // prevention code make sure that one threadId could generate only one task
        $uniqueTasks = [];
        foreach ($allTasks as $task) {
            if (empty($uniqueTasks[$task['threadId']])) {
                $uniqueTasks[$task['threadId']] = $task;
            }
        }

        return array_values($uniqueTasks);
    }

    /**
     * Query to get all new possible tasks that:
     * the employee id in transaction equal with the value that transaction->retailer_customer->customer->rep's employee id
     * @return array
     */
    private function processScanForEmployeeIdMatchTasks()
    {
        $this->setIsTrxAndUserSameEmployeeId(true);
        $matchedTasks = parent::processScanForTasks();
        return $matchedTasks;
    }

    /**
     * Query to get all new possible tasks that:
     * the employee id in transaction not equal with the value that transaction->retailer_customer->customer->rep's employee id
     * @return array
     */
    private function processScanForEmployeeIdNoMatchTasks()
    {
        $this->setIsTrxAndUserSameEmployeeId(false);
        $noMatchedTasks = parent::processScanForTasks();
        return $noMatchedTasks;
    }

    /**
     * Will restrict to home store only transactions
     * @inheritdoc
     */
    public function addQueryRestrictions(QueryBuilder $qb)
    {
        parent::addQueryRestrictions($qb);

        if ($this->isSecondaryEmployeeAssignEnabled()) {
            $qb->andWhere($this->shouldTrxAndUserSameEmployeeIdExpr($qb));
        }

        $qb->andWhere('DATE(trx.trx_date) >= ' . $this->calculateDaysBackDateSql());

        if ($this->isTeamMode) {
            $this->restrictQueryToHomeStoreTransactionsOnly($qb);
        }
    }

    /**
     * @inheritDoc
     */
    protected function addMinTransactionCondition(QueryBuilder $qB)
    {
        // This soon to lapse doesn't have the restriction on the min value of the transaction
    }

    /**
     * Find transactions in range, but only add it to the result set
     * if the last transaction for the customer was X days ago.
     * If any transactions happened after, don't use it, as the customer
     * is still active and not "soon to lapse".
     * @return string
     */
    protected function getWrapperRestrictions()
    {
        $days = $this->calculateDaysBackDateSql();
        return <<<SQL
        group by customerId
HAVING DATE(MAX(rt.trx_date)) = {$days}
SQL;
    }

    public function addQuerySelectTopTransaction(QueryBuilder $qb)
    {
        $qb->addSelect('MAX(trx.trx_date) as latest_transaction_date');
    }

    protected function getWrapperJoinConditions()
    {
        return sprintf(" AND rt.trx_date = data.latest_transaction_date AND rt.trx_type = '%s'", static::TRX_TYPE_SALE);
    }

    /**
     * @inheritdoc
     */
    public function getTaskDetails($plainText = true, $locale = null)
    {
        return $this->translationService->trans(
            static::TRANSLATION_DETAILS_KEY,
            ['%daysSearchBack%' => $this->getConfig(static::CONFIG_KEY_DAYS_SEARCH_BACK)],
            null,
            $locale
        );
    }

    private function setIsTrxAndUserSameEmployeeId($shouldTrxAndUserSameEmployeeId)
    {
        $this->shouldTrxAndUserSameEmployeeId = $shouldTrxAndUserSameEmployeeId;
    }

    /**
     * Check if customer has linked to an rep by Primary Employee or Secondary Employee
     * {@inheritdoc}
     */
    public function restrictQueryToRetailerAssignedContact(QueryBuilder $qb)
    {
        if ($this->isSecondaryEmployeeAssignEnabled()) {
            $qb->andWhere('customer.retailer_parent_customer_id IS NOT NULL');
        } else {
            $qb->andWhere('customer.retailer_customer_id IS NOT NULL');
        }
    }

    /**
     * if true then assign tasks based on Secondary Employee of transaction related customer, instead of Primary Employee(default setting)
     * this config default value is false
     * @return boolean
     */
    private function isSecondaryEmployeeAssignEnabled()
    {
        return $this->configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled'];
    }

    /**
     * this function only apply when assign task with Secondary Employee(sf_customer.retailer_parent_customer_id)
     * According the class shouldTrxAndUserSameEmployeeId, return different dbal expression
     * @param $qb
     * @return mixed
     */
    private function shouldTrxAndUserSameEmployeeIdExpr($qb)
    {
        if ($this->isSecondaryEmployeeAssignEnabled() !== true) {
            $this->logger->error('shouldTrxAndUserSameEmployeeIdExpr is called only when task creation according secondary employee');
        }

        // note: no special logic for team mode since store_user should not have any employee_id
        if ($this->shouldTrxAndUserSameEmployeeId == true) {
            return $qb->expr()->andX(
                $qb->expr()->isNotNull('trx.employee_id'),
                $qb->expr()->eq('trx.employee_id', ' user.employee_id')
            );
        } else {
            return $qb->expr()->orX(
                $qb->expr()->isNull('trx.employee_id'),
                $qb->expr()->neq('trx.employee_id', ' user.employee_id')
            );
        }
    }
}
