<?php

namespace Salesfloor\Services\Tasks\Features;

use Salesfloor\Configs\Configs;
use Salesfloor\Models\Task as TaskModel;
use Salesfloor\API\Managers\Task\Tasks as TasksManager;
use Salesfloor\Services\Tasks\Tasks as TaskService;
use Monolog\Logger;
use Salesfloor\Services\MySQLRepository;
use Silex\Application;

class TaskAutoDismissProcessor
{
    const HAPPEN_AFTER_REMINDER = 'reminder';
    const HAPPEN_AFTER_CREATION = 'creation';

    const VALID_HAPPEN_AFTER_VALUES = [
        self::HAPPEN_AFTER_REMINDER,
        self::HAPPEN_AFTER_CREATION
    ];

    const DEFAULT_AUTOMATED_TYPES = [
        TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE,
        TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED,

        TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_EVENT,
        TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_FOLLOWUP,

        TaskModel::AUTOMATED_TYPE_TRANSACTION_DISTRIBUTION_BY_STORES,
        TaskModel::AUTOMATED_TYPE_TRANSACTION_CANCELLED_FOLLOW_UP,

        TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION,
        TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED,
        TaskModel::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE,

        TaskModel::AUTOMATED_TYPE_NEW_REP_TRANSACTION,
        TaskModel::AUTOMATED_TYPE_REP_TRANSACTION_IMPORTED,

        TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_IMPORTED,
        TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED,
        TaskModel::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE,
    ];

    const DEFAULT_DAYS_AFTER_EVENT = 7;

    /** @var  Logger $logger */
    protected $logger;

    /** @var Configs */
    protected $configs;

    /** @var TasksManager */
    protected $tasksManager;

    /** @var  MySQLRepository $repositories */
    protected $repositories;

    /** @var TaskService $taskService */
    protected $taskService;

    public function __construct(Application $app)
    {
        $this->loadDependencies($app);
    }

    public function loadDependencies(Application $app)
    {
        $this->logger       = $app['logger'];
        $this->configs      = $app['configs'];
        $this->tasksManager = $app['tasks.manager'];
        $this->repositories = $app['repositories.mysql'];
        $this->taskService  = $app['service.tasks'];
    }

    /**
     * Is the auto-dismissal feature enabled?
     *
     * @return boolean
     */
    public function isFeatureEnabled()
    {
        return $this->configs['sf.task.auto_dismiss.enabled'];
    }

    /**
     * Get the settings from the configs
     *
     * @return array
     */
    public function getConfiguredSettings()
    {
        return $this->configs['sf.task.auto_dismiss.settings'];
    }

    /**
     * Generate SQL to fetch the task IDs to be dismissed
     *
     * @param array $params
     * @option automated_types      The automated types to filter for
     * @option happen_after_event   The event we begin counting the days from for the dismissal
     * @option days_after_event     Days after the event to dismiss task
     * @return QueryBuilder
     */
    public function generateSQLForTaskAutoDismiss(array $params)
    {
        $daysAfterEvent     = !empty($params['days_after_event']) ? intval($params['days_after_event']) : self::DEFAULT_DAYS_AFTER_EVENT;
        $automatedTypes     = !empty($params['automated_types']) ? $params['automated_types'] : [];
        $happenAfterEvent   = self::HAPPEN_AFTER_REMINDER;
        if (!empty($params['happen_after_event'])) {
            if (in_array($params['happen_after_event'], self::VALID_HAPPEN_AFTER_VALUES)) {
                $happenAfterEvent = $params['happen_after_event'];
            }
        }

        $qb = $this->repositories->getQueryBuilder();
        $qb->select('m.id')
            ->from(TasksManager::TABLE_NAME, 'm')
            ->where("m.status = :unresolved_status")
                ->setParameter('unresolved_status', TaskModel::STATUS_UNRESOLVED)
            ->andWhere("m.automated_type != :corp_type")
                ->setParameter('corp_type', TaskModel::AUTOMATED_TYPE_RETAILER_CORPORATE)
            ->andWhere('m.automated_type IN (:automated_types)')
                ->setParameter('automated_types', $automatedTypes, \Doctrine\DBAL\Connection::PARAM_STR_ARRAY)
        ;

        switch ($happenAfterEvent) {
            case self::HAPPEN_AFTER_CREATION:
                $qb->andWhere("m.created_at < now() - interval :days_after_event DAY");
                $qb->setParameter('days_after_event', $daysAfterEvent);
                break;
            case self::HAPPEN_AFTER_REMINDER:
            default:
                $qb->andWhere("m.reminder_date < now() - interval :days_after_event DAY");
                $qb->setParameter('days_after_event', $daysAfterEvent);
        }

        return $qb;
    }

    /**
     * Iterate over the task ids to be dismissed
     *
     * @param array $params same as generateSQLForTaskAutoDismiss()
     * @yields $taskId
     */
    public function iterateTaskIdsToDismiss(array $params)
    {
        $qb = $this->generateSQLForTaskAutoDismiss($params);
        $stmt = $qb->execute();
        while ($row = $stmt->fetch()) {
            yield $row['id'];
        }
    }

    /**
     * Process tasks to be dismissed
     *
     * @return false|void
     * @throws \Salesfloor\API\Exceptions\Manager\MissingRequiredFieldException
     */
    public function process()
    {
        if (!$this->isFeatureEnabled()) {
            return false;
        }
        foreach ($this->getConfiguredSettings() as $settings) {
            $this->logger->info(sprintf("Processing auto-dismiss tasks %s\n", \json_encode($settings)));
            foreach ($this->iterateTaskIdsToDismiss($settings) as $taskId) {
                $this->logger->info(sprintf('Dismissing task %s', $taskId));
                try {
                    $task = $this->tasksManager->getOneOrNull(['id' => $taskId], null, false);
                    if (empty($task)) {
                        continue;
                    }
                    $originalTask = clone $task;
                    $task->status = TaskModel::STATUS_DISMISSED;
                    $task->resolution_note = TaskModel::AUTO_DISMISS_NOTE;
                    $this->tasksManager->save($task);

                    // Update KPI
                    $this->taskService->recordUpdateEvent($task, $originalTask, __METHOD__);
                } catch (\Exception $e) {
                    $this->logger->critical($e->getMessage());
                }
            }

            $this->tasksManager->invalidateClassCache();
        }
    }
}
