<?php

namespace Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Product;

use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\PaginatedIterationStrategy;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\NonPaginatedIterationStrategy;

class DefaultFetcher extends Base
{
    protected $configs;

    protected $iterationStrategy;

    public function __construct($configs)
    {
        $this->configs = $configs;
        if (empty($this->configs['importer.product.fetcher.api.is_paginated'])) {
            $this->iterationStrategy = new NonPaginatedIterationStrategy($configs);
        } else {
            $this->iterationStrategy = new PaginatedIterationStrategy($configs);
            $this->iterationStrategy->setPerPage($this->configs['importer.product.fetcher.api.per_page']);
            $this->iterationStrategy->setStartPage($this->configs['importer.product.fetcher.api.start_page']);
        }
        $this->iterationStrategy->setUrl($this->configs['importer.product.fetcher.api.url']);
        $this->iterationStrategy->authenticate($this->configs['importer.product.fetcher.api.credentials']);
    }

    public function getStreamInfo()
    {
        if (!empty($this->iterationStrategy)) {
            do {
                yield $this->iterationStrategy->getNext();
            } while (!$this->iterationStrategy->isEnd());
        }
    }
}
