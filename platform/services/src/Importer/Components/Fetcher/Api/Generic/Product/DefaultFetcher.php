<?php

namespace Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Product;

use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\Base as BaseIterationStrategy;

/**
 * Run the following test to confirm the functionality of this class:
 *
 *   ./robo test:functional Importer/Components/Fetcher/Api/DefaultProductApiCest.php
 */
class DefaultFetcher extends Base
{
    protected $iterationStrategy;

    public function __construct(BaseIterationStrategy $iterationStrategy)
    {
        $this->iterationStrategy = $iterationStrategy;
    }

    public function getStreamInfo()
    {
        if (!empty($this->iterationStrategy)) {
            do {
                yield $this->iterationStrategy->getNext();
            } while (!$this->iterationStrategy->isEnd());
        }
    }
}
