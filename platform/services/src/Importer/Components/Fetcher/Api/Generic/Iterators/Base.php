<?php

namespace Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators;

class Base
{
    protected $configs;
    protected $queryUrl = '';
    protected $isEnd = false;
    protected $auth = [];

    public function __construct($configs)
    {
        $this->configs = $configs;
    }

    public function setUrl($url)
    {
        $this->queryUrl = $url;
    }

    public function authenticate($auth = null)
    {
        $this->auth = $auth;
    }

    public function getNext()
    {
        return;
    }

    /**
     * Allow the iterater the ability to end looping
     * However it can be left up to the ingestor to decide when to end
     *  since it is responsible for decoding
     * @return bool
     */
    public function isEnd()
    {
        return $this->isEnd;
    }
}
