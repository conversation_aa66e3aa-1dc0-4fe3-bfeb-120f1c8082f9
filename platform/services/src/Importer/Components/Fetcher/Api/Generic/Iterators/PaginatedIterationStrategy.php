<?php

namespace Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators;

use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\Base as BaseIterationStrategy;

class PaginatedIterationStrategy extends BaseIterationStrategy
{
    protected $startPage = 1;
    protected $page = null;
    protected $perPage = 10;

    public function setPerPage($size)
    {
        $this->perPage = $size;
    }

    public function setStartPage($startPage)
    {
        $this->startPage = $startPage;
    }

    public function getNext()
    {

        // Calculate pagination values
        if (empty($this->page)) {
            $this->page = $this->startPage;
        } else {
            ++$this->page;
        }
        $from = $this->perPage * ($this->page - 1); // starts at 0
        $to = $this->perPage * $this->page;

        // Replace vars in url template
        $url = $this->queryUrl;
        $url = str_replace('{{pageNum}}', $this->page, $url);
        $url = str_replace('{{perPage}}', $this->perPage, $url);
        $url = str_replace('{{from}}', $from, $url);
        $url = str_replace('{{to}}', $to, $url);

        // GET result
        $res = $this->guzzleHttpClient->get($url);

        if ($res->getStatusCode() == 200) {
            return strval($res->getBody());
        } else {
            // Kill iteration when fails to retrieve
            $this->isEnd = true;
            return null;
        }
    }
}
