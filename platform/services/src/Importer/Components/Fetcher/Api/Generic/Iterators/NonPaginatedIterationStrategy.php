<?php

namespace Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators;

use Salesfloor\Services\Importer\Components\Fetcher\Api\Generic\Iterators\Base as BaseIterationStrategy;

class NonPaginatedIterationStrategy extends BaseIterationStrategy
{
    /** @var \GuzzleHttp\Client $guzzleHttpClient */
    protected $guzzleHttpClient;

    public function __construct($configs)
    {
        parent::__construct($configs);
        $this->guzzleHttpClient = new \GuzzleHttp\Client();
    }

    public function getNext()
    {
        // Only execute once
        $this->isEnd = true;

        // GET result
        $res = $this->guzzleHttpClient->get($this->queryUrl, $this->auth ?? []);
        if ($res->getStatusCode() == 200) {
            return strval($res->getBody());
        }
    }
}
