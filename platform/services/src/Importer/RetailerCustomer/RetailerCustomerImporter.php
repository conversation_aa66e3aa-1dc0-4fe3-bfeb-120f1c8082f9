<?php

namespace Salesfloor\Services\Importer\RetailerCustomer;

use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;
use Exception;
use PDO;
use Salesfloor\Models\Customer;
use Salesfloor\Services\Util;
use Salesfloor\API\Managers\Import;
use Salesfloor\API\Managers\RetailerCustomerEvents;
use Salesfloor\API\Managers\RetailerCustomersToCustomerAttributes;
use Salesfloor\Models\CustomerMeta;
use Salesfloor\Models\RetailerCustomer;
use Salesfloor\Models\SocialMediaNetwork;
use Salesfloor\Services\CustomerInsights\Matching\BaseMatchCustomersRetailerCustomers;
use Salesfloor\Services\ElasticSearch\ElasticSearch;
use Salesfloor\Services\ElasticSearch\Exceptions\ElasticSearchException;
use Salesfloor\Services\ExtendedInsertQueryBuilder;
use Salesfloor\Services\Importer\Components\Reporting\ValidationSummaryReportWriter;
use Salesfloor\Services\Importer\Components\Validation\LogMessage;
use Salesfloor\Services\Importer\CustomerInsights\BaseRetailerRowGenerator;
use Salesfloor\Services\Importer\Exceptions\ImportException;
use Salesfloor\API\Managers\Client\RetailerCustomers\Legacy as Customers;
use Salesfloor\API\Managers\SocialMediaNetwork as SocialMediaNetworkManager;
use Salesfloor\Services\Importer\S3Importer;
use Salesfloor\API\Managers\CustomerAttributes;
use Salesfloor\Models\FeedScannerJob as FeedScannerJobModel;
use Salesfloor\Services\Importer\Components\Reporting\ImporterReportInterface;

/**
 * Import Retailer Customers using the generic format csv file.
 *
 * This should have no relation to Ann clienteling importers until Ann is
 * updated to use this generic format.
 */
class RetailerCustomerImporter extends S3Importer implements ImporterReportInterface
{
    protected $feedType = FeedScannerJobModel::FEED_TYPE_CUSTOMER;

    const RETAILER_CUSTOMER_TABLE = 'sf_retailer_customers';
    const RETAILER_CUSTOMER_META_TABLE = 'sf_retailer_customer_meta';
    const RETAILER_CUSTOMER_TAGS_RELATIONSHIPS = 'sf_retailer_customer_tags_relationships';
    const RETAILER_CUSTOMER_ADDRESSES = 'sf_retailer_customer_addresses';
    const RETAILER_CUSTOMER_EVENTS = 'sf_retailer_customer_events';
    const RETAILER_CUSTOMER_SOCIAL_MEDIA = 'sf_retailer_customer_social_media';
    const CUSTOMERS_TO_RETAILER_CUSTOMERS = 'sf_customers_to_retailer_customers';
    const RETAILER_CUSTOMERS_TO_CUSTOMER_ATTRIBUTES_TABLE = 'sf_retailer_customers_to_customer_attributes';

    // This must be the same as the contact importer, otherwise the status won't match
    const STATUS_NEW      = 'new';
    const STATUS_MODIFIED = 'modified';
    const STATUS_DELETED  = 'deleted';

    /** @var int $expectedColumnsInFile expected number of columns in the import file */
    const TOTAL_EXPECTED_COLUMNS_IN_FILE = 28;

    /**
     * If you decide to change this, or use a config. Please update convertTags() function in c2c.php
     *
     * @var string $tagDelimiter delimiter for tags in the tag column
     */
    protected $tagDelimiter = ';';

    protected $labelDelimiter = [';'];          // delimiter between value;label
    protected $alternateDelimiter = [','];      // delimiter between alternate value;label,value;label

    // attribute id list from sf_attributes table, to check if attribute id if valid
    private $attributeIds = null;

    // attribute ids mapper, key: attribute_id of retailer' side, value: id(PK) of sf_customer_attributes table
    private $attributeIdsMapper = [] ;

    // Not all the import support recover (default=false)
    // If you support recovery, the base will recover from the hash any previous (non-completed) import
    // At the moment, the only step that is "recover" is the import (since it's the one that take the most time)
    // We could also try to recover the "moveToMySQL" step, but this is not done yet
    protected $supportRecovery = true;

    // If true, importer will add/update/delete ElasticSearch index
    protected $isEnabledElasticSearch = true;

    // Use during validation (csv) to keep a hash of all unique key so we don't process rows 2 times (with same unique key)
    protected $duplicate = [];

    /**
     * This is not a constant, as this should be changeable by subclasses.
     * If they change it, it provides an easy way to remap columns.
     *
     * @var array
     */
    protected $columnMapping = [
        'id'                       => 0,
        'firstName'                => 1,
        'lastName'                 => 2,
        'emailDefault'             => 3,
        'emailAlternates'          => 4,
        'phoneDefault'             => 5,
        'phoneAlternates'          => 6,
        'status'                   => 7,
        'retailerStoreId'          => 8,
        'employeeId'               => 9,
        'tags'                     => 10,
        'secondary-employee-ids'   => 11,
        'secondary-store-ids'      => 12,
        'sf-contact-record-id'     => 13,
        'addresses'                => 14,
        'facebook'                 => 15,
        'twitter'                  => 16,
        'instagram'                => 17,
        'website'                  => 18,
        'birthday'                 => 19,
        'anniversary'              => 20,
        'misc-date'                => 21,
        'crm-last-processed'       => 22,
        'sf-email-subscription'    => 23,
        'preferred-language'       => 24,
        'attributes'               => 25,
        'limited-visibility'       => 26,
        'sf-sms-subscription'      => 27,
        'preferred-contact-method' => 28,
    ];

    /**
     * Some retailers don't provide certain data. Flag it as skipped.
     *
     * @var array
     */
    protected $skippedFields = [];

    /** @var  ElasticSearch $elasticsearch */
    protected $elasticsearch;

    /** @var Customers $retailerCustomersManager */
    protected $retailerCustomersManager;

    /** @var SocialMediaNetworkManager $socialMediaNetworkManager */
    protected $socialMediaNetworkManager;

    /** @var CustomerAttributes $attributesManager */
    private $attributesManager;

    protected $tagIds;

    /** @var CustomersToContacts $customers2contacts */
    private $customers2contacts;

    /** @var bool $shouldCreateContacts */
    protected $shouldCreateContacts;

    /** @var callable $insertBuilder */
    protected $insertBuilder;

    /** @var $customerLoader BaseRetailerRowGenerator */
    protected $customerLoader;

    /** @var BaseMatchCustomersRetailerCustomers $matchContactCustomer */
    private $matchContactCustomer;

    /** @var RetailerCustomersToCustomerAttributes $retailerCustomersToCustomerAttributesManager */
    private $retailerCustomersToCustomerAttributesManager;

    /**
     * List of files generated by customer to contactsß
     *
     * @var array
     */
    private $c2cFiles;
    /**
     * If true, will upload C2C files to s3.
     *
     * @var bool
     */
    private $uploadCustomerToContactFiles = true;

    // Detect if there are any inserted or updated rows to do matching with
    private $insertedOrUpdatedRowCount = 0;

    public function getImportType()
    {
        return Import::TYPE_CUSTOMER;
    }

    public function loadDependencies($app)
    {
        $this->elasticsearch             = $app['elasticsearch'];
        $this->retailerCustomersManager  = $app['retailer_customers.manager'];
        $this->socialMediaNetworkManager = $app['social_media_network.manager'];
        $this->attributesManager         = $app['customer_attributes.manager'];
        $this->customers2contacts        = $app['importer.customers_to_contacts'];
        $this->shouldCreateContacts      = $app['configs']['retailer.clienteling.enabled.create_contact'];
        $this->insertBuilder             = $app['service.insert_query_builder'];
        $this->matchContactCustomer      = $app['match-customer-retailercustomers'];

        $this->retailerCustomersToCustomerAttributesManager = $app['retailer_customers_to_customer_attributes.manager'];

        $this->isEncryptedFile = $app['configs']['sf.import_ci_customers.encrypted'];
        $this->customerLoader  = $app[$app['configs']['importer.' . Import::TYPE_CUSTOMER . '.loader']];

        // add report by default
        $this->reportService->addReportWriter(new ValidationSummaryReportWriter($app['configs']));

        $this->loadTagIds();
    }

    /**
     * @return ExtendedInsertQueryBuilder
     */
    private function getInsertBuilder()
    {
        $b = $this->insertBuilder;
        return $b();
    }

    /**
     * Get the mapping for the customers file into the CRM importer.
     *
     * Note that it is expected that for store mode, store users must have an Employee ID
     * equal to the retailer store id.
     *
     * Fields that may be needed for preprocessing can also be added here. C2C
     * is aware of the fields that need to be translated to a CRM file, and will
     * ignore extra fields.
     *
     * @return array
     */
    public function getCustomerToCrmContactColumnMapping()
    {
        $isStoreMode = $this->configs['retailer.storepage_mode'];

        return [
            CustomersToContacts::COL_EMPLOYEE_ID                   => $isStoreMode
                ? $this->getColumnIndex('retailerStoreId')
                : $this->getColumnIndex('employeeId'),
            CustomersToContacts::COL_FIRST_NAME                    => $this->getColumnIndex('firstName'),
            CustomersToContacts::COL_LAST_NAME                     => $this->getColumnIndex('lastName'),
            CustomersToContacts::COL_CUSTOMER_ID                   => $this->getColumnIndex('id'),
            CustomersToContacts::COL_CUSTOMER_EMAIL                => $this->getColumnIndex('emailDefault'),
            CustomersToContacts::COL_PHONE                         => $this->getColumnIndex('phoneDefault'),
            CustomersToContacts::COL_ALT_EMAIL                     => $this->getColumnIndex('emailAlternates'),
            CustomersToContacts::COL_ALT_PHONE                     => $this->getColumnIndex('phoneAlternates'),
            CustomersToContacts::COL_RETAILER_TAG_IDS              => $this->getColumnIndex('tags'),
            // SF-22828 - Add fields for contact syncing.
            CustomersToContacts::COL_SECONDARY_CUSTOMER_ID         => null, // Placeholder, set in C2C if needed.
            CustomersToContacts::COL_SF_CONTACT_RECORD_ID          => null, // Placeholder, set in C2C if needed.
            CustomersToContacts::COL_ADDRESSES                     => $this->getColumnIndex('addresses'),
            CustomersToContacts::COL_FACEBOOK                      => $this->getColumnIndex('facebook'),
            CustomersToContacts::COL_TWITTER                       => $this->getColumnIndex('twitter'),
            CustomersToContacts::COL_INSTAGRAM                     => $this->getColumnIndex('instagram'),
            CustomersToContacts::COL_WEBSITE                       => $this->getColumnIndex('website'),
            CustomersToContacts::COL_BIRTHDAY                      => $this->getColumnIndex('birthday'),
            CustomersToContacts::COL_ANNIVERSARY                   => $this->getColumnIndex('anniversary'),
            CustomersToContacts::COL_MISC_DATE                     => $this->getColumnIndex('misc-date'),
            CustomersToContacts::COL_CRM_LAST_PROCESSED            => $this->getColumnIndex('crm-last-processed'),
            CustomersToContacts::COL_STATUS                        => $this->getColumnIndex('status'),
            CustomersToContacts::COL_LOCALE                        => $this->getColumnIndex('preferred-language'),
            CustomersToContacts::COL_SUBSCRIPTION                  => $this->getColumnIndex('sf-email-subscription'),
            CustomersToContacts::COL_ATTRIBUTES                    => $this->getColumnIndex('attributes'),
            CustomersToContacts::COL_SMS_MARKETING_SUBSCRIPTION    => $this->getColumnIndex('sf-sms-subscription'),
            CustomersToContacts::COL_PREFERRED_CONTACT_METHOD      => $this->getColumnIndex('preferred-contact-method'),

            // Additional metadata fields for transformations in C2C
            CustomersToContacts::META_COL_SECONDARY_ASSIGNMENT_IDS => $isStoreMode
                ? $this->getColumnIndex('secondary-store-ids')
                : $this->getColumnIndex('secondary-employee-ids'),
            // We set this as a meta field so contact id is guaranteed to be null unless we actually have something
            // to set.
            CustomersToContacts::META_COL_CONTACT_RECORD_IDS       => $this->getColumnIndex('sf-contact-record-id'),
        ];
    }

    public function getOriginalTableName()
    {
        return [
            self::RETAILER_CUSTOMER_TABLE      => [
                // 'id',                // Auto generated by DB
                'customer_id',
                'gender',
                'first_name',
                'last_name',
                'email',
                'email_label',
                'phone',
                'phone_label',
                'address_line1',
                'address_line2',
                'zipcode',
                'postalcode',
                'city',
                'state',
                'country',
                'longitude',
                'latitude',
                'is_subscribed', // sf-email-subscription
                // 'created_at',
                // 'updated_at',
                'employee_id',
                'retailer_store_id',
                'external_last_updated',
                'locale', // preferred-language
                'limited_visibility',
                'is_subscribed_sms_marketing', // sf-sms-subscription
                'contact_preference', // preferred-contact-method
                // Some field added at the end manually
                'status',
                'position',
            ],
            self::RETAILER_CUSTOMER_META_TABLE           => [
                // 'id'
                'customer_id',
                'type',
                'label',
                'position',
                'value',
                // 'created_at',
                // 'updated_at',
            ],
            self::RETAILER_CUSTOMER_TAGS_RELATIONSHIPS   => [
                // `id`,
                'tag_id',
                'customer_id',
                // 'created_by',
                // 'created_at',
                // 'updated_at',
            ],
            self::RETAILER_CUSTOMER_ADDRESSES => [
                // `id`,
                'customer_id',
                'address_line_1',
                'address_line_2',
                'postal_code',
                'state',
                'city',
                'country',
                'label',
                'is_default',
                // 'created_at',
                // 'updated_at',
            ],
            self::RETAILER_CUSTOMER_EVENTS => [
                // `id`,
                'customer_id',
                'label',
                'day',
                'month',
                'year',
                // 'created_at',
                // 'updated_at',
            ],
            self::RETAILER_CUSTOMER_SOCIAL_MEDIA => [
                // `id`,
                'customer_id',
                'social_media_network_id',
                'username',
                // 'created_at',
                // 'updated_at',
            ],
            self::RETAILER_CUSTOMERS_TO_CUSTOMER_ATTRIBUTES_TABLE => [
                // `id`,
                'customer_id',
                'attribute_id',
                'attribute_value',
                // 'created_at',
                // 'updated_at',
            ],
        ];
    }

    public function setEnabledElasticSearch($es)
    {
        $this->isEnabledElasticSearch = $es;
        return $this;
    }

    public function beforeProcessAllDataBlock()
    {
        // Since the buffer copy the destination table, and status column can't be store anywhere, i create one
        $this->updateRetailerCustomerTableForImport();
    }

    public function import()
    {
        $new    = [];     // List of all customers that have new, update or empty status
        $delete = [];     // LIst of all customers that have the status "delete"

        // This is the size we will persist the data to DB when one of the 2 arrays reach
        $maxSizeArrayBulk = $this->configs['sf.import_ci_customers.bulk_size'];

        foreach ($this->getRetailerCustomer() as $customer) {
            $status = mb_strtolower($customer['status']); // #sanitizeStatus also used in CustomerToContacts

            if (empty($customer['status']) || in_array($status, [self::STATUS_NEW, self::STATUS_MODIFIED])) {
                $new[] = $customer;
            } elseif ($status == self::STATUS_DELETED) {
                $delete[] = $customer;
            } else {
                $this->log(sprintf("This customer doesn't have a valid status"));
            }

            if (count($delete) >= $maxSizeArrayBulk || count($new) >= $maxSizeArrayBulk) {
                $this->processBulk($new, $delete);
                $new = $delete = []; // reset bulk memory
            }
        }

        // We also need to process it at the end, in case we don't reach the maxSizeArrayBulk and reach the end of the foreach
        $this->processBulk($new, $delete);

        $this->updateTagRelationships();
        $this->updateAddresses();
        $this->updateEvents();
        $this->updateSocialMedia();

        if ($this->insertedOrUpdatedRowCount == 0) {
            $this->logger->info("Import completed. Will skip the full match since no rows were inserted/modified.");
        } else {
            // The problem with running the matching every X hour, is we have concurrent execution.
            // In this case, we have often for some retailer, locks and the import fail.
            // By doing it at the end, we shouldn't have this scenario.
            // Now we just hope that we don't have a contact importer running at the same time.
            $this->logger->info("Import completed. Will do the full match now");

            // Matching on all rows is too slow for some retailer. (e.g: gnc +60M customers and +27M retailer customers)
            $lastModified = $this->importModel->started_at;

            $this->matchContactCustomer->match(
                "sf_retailer_customers.created_at >= '$lastModified' OR
                        sf_retailer_customers.updated_at >= '$lastModified'",
            );
        }

        // Move the c2c at the end to have lower risk of having duplicate
        if ($this->shouldCreateContacts) {
            $this->createContacts();
        }
    }

    /**
     * @return array
     */
    public function getCustomerToContactFiles(): array
    {
        return $this->c2cFiles;
    }

    /**
     * This should not change based on the retailer, so we put it in the base.
     * This will take both alternate field and extract data from it based on delimiter and add it to the buffer
     *
     * @param $retailerCustomerId
     * @param $alternateMeta
     * @param $defaultValue
     * @param $type
     *
     * @throws ImportException
     */
    protected function appendMeta($retailerCustomerId, $alternateMeta, $defaultValue, $type)
    {
        $position           = 0;
        $alternateDelimiter = $this->detectDelimiter($alternateMeta, $this->alternateDelimiter);
        $alternates         = $this->extractField($alternateMeta, $alternateDelimiter);

        // Delete null value
        $alternates = array_filter($alternates);

        foreach ($alternates as $key => &$alternate) {
            $labelDelimiter = $this->detectDelimiter($alternate, $this->labelDelimiter);

            list($newValue, $newLabel) = array_map(function ($item) {
                return $this->normalize($item);
            }, array_pad($this->extractField($alternate, $labelDelimiter), 2, null));

            if (!empty($newValue) && $newValue != $defaultValue) {
                $this->buffer->append(
                    self::RETAILER_CUSTOMER_META_TABLE,
                    [
                        null,
                        $retailerCustomerId,
                        $type,
                        $newLabel,
                        $position++,
                        $newValue,
                        null,
                        null,
                    ]
                );
            }
        }
    }

    private function updateRetailerCustomerTableForImport()
    {
        // Since we copy the table header we are missing 2 column. Status / position
        $table = $this->buffer->getStagingTable(self::RETAILER_CUSTOMER_TABLE);

        $mysqlConnection = $this->mysql->getMysqli();

        // We must drop the customer_id unicity check otherwise we won't be able to process delete+insert in the same file
        // We will keep the unicity check in php when 2waysync is on.
        // We must keep an index however for performance reason.
        $q = <<<MYSQL
        ALTER TABLE $table
        DROP INDEX customer_id
MYSQL;

        $mysqlConnection->query($q);

        // Always add them at the end
        $q = "ALTER TABLE `$table`
            ADD COLUMN `status` varchar(45) NOT NULL,
            ADD COLUMN `position` int(11) NOT NULL AFTER `status`,
            ADD INDEX (status),
            ADD INDEX (customer_id)
            ";

        $mysqlConnection->query($q);
    }

    /**
     * The Process new/modified/delete.
     *
     * @param $new
     * @param $delete
     *
     * @throws Exception
     */
    private function processBulk($new, $delete)
    {
        $this->repositories->beginTransaction();
        $deleteIds = [];

        try {
            $lastDelete = $lastNew = 0;
            $deleted    = $updated = $inserted = 0;
            if (!empty($delete)) {
                // We need to get these before deleting, otherwise we can't get the ID of the row because it's gone.
                $deleteIds = $this->getRetailerCustomerIdFromCustomerId(array_column($delete, 'customer_id'));
                $this->deleteBulkRetailerCustomer($delete);
                $lastDelete = end($delete)['id'];

                $deleted = count($delete);
            }

            if (!empty($new)) {
                $statement = $this->insertBulkRetailerCustomer($new);
                $lastNew   = end($new)['id'];

                $totalNew = count($new);
                $updated  = $statement->rowCount() - $totalNew;
                $inserted = $totalNew - $updated;

                $this->insertedOrUpdatedRowCount += $totalNew;
            }

            $this->updateInserted($inserted);
            $this->updateUpdated($updated);
            $this->updateDeleted($deleted);

            $last = max($lastDelete, $lastNew);
            if ($last) {
                // We store the id of the temporary table (main table), so when we will resume, we can do
                // where id > X
                $this->importModel->last_processed_id = $last;
                $this->saveImportModel();
            }

            $this->repositories->commit();
        } catch (Exception $e) {
            $this->repositories->rollback();

            throw $e;
        }

        $this->addToElasticSearch(array_column($new, 'customer_id'));
        $this->deleteFromElasticSearch($deleteIds);
    }

    /**
     * Reindex retailer customers in elasticsearch.
     *
     * @param array $addIds an array of retailer customer ids (customer_id field) to reindex.
     */
    private function addToElasticSearch(array $addIds)
    {
        if (empty($addIds)) {
            return;
        }

        try {
            if ($this->isEnabledElasticSearch) {
                $this->retailerCustomersManager->index(['id' => $this->getRetailerCustomerIdFromCustomerId($addIds)], true);
            }
        } catch (ElasticSearchException $e) {
            $this->error(
                "Failed to reindex retailer customers in elasticsearch: " . $e->getMessage() . $e->getTraceAsString()
            );
        }
    }

    /**
     * Delete retailer customers from elasticsearch.
     *
     * @param array $deleteIds an array of primary key ids (id field from sf_retailer_customers) to delete from ES.
     */
    private function deleteFromElasticSearch(array $deleteIds)
    {
        if (empty($deleteIds)) {
            return;
        }

        try {
            if ($this->isEnabledElasticSearch) {
                $this->retailerCustomersManager
                    ->deleteByIdsFromElasticSearch($deleteIds);
            }
        } catch (ElasticSearchException $e) {
            $this->error(
                "Failed to delete retailer customers from elasticsearch: " . $e->getMessage() . $e->getTraceAsString()
            );
        }
    }

    /**
     * The problem with updating the matching: we don't have access to the new id. We only have the customer_id
     *
     * @param $customerIds
     * @return array
     */
    protected function getRetailerCustomerIdFromCustomerId($customerIds)
    {
        /** @var QueryBuilder $qB */
        $qB = $this->repositories->getQueryBuilder();

        $qB
            ->select('cr.id as id')
            ->from(self::RETAILER_CUSTOMER_TABLE, 'cr')
            ->where(
                "cr.customer_id IN (:customerId)"
            )
            ->setParameter('customerId', array_unique($customerIds), Connection::PARAM_STR_ARRAY);

        $results = $qB->execute()->fetchAll();

        return array_column($results, 'id');
    }

    private function deleteBulkRetailerCustomer($customers)
    {
        if (empty($customers)) {
            return;
        }

        // We need to delete relationship first because we are relying on the parent table (join) to get the ids
        $this->deleteCustomerRelationship($customers);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_TABLE, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_META_TABLE, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_TAGS_RELATIONSHIPS, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_ADDRESSES, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_SOCIAL_MEDIA, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_EVENTS, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);

        $this->repositories->deleteMany(self::RETAILER_CUSTOMERS_TO_CUSTOMER_ATTRIBUTES_TABLE, [
            'customer_id' => array_column($customers, 'customer_id'),
        ]);
    }

    /**
     * Delete all entries based on a list of customer id (CI).
     * Table sf_customers_to_retailer_customers doesn't rely on "customer_id" (CI) but on our internal ID.
     *
     * @param array $customers
     *
     * @return void
     */
    private function deleteCustomerRelationship(array $customers)
    {
        // DBAL doesn't support delete with join.
        $sql = sprintf(
            "DELETE ctrc FROM %s ctrc
    INNER JOIN %s rc on rc.id = ctrc.retailer_customer_id
    where rc.customer_id in (:ids)",
            self::CUSTOMERS_TO_RETAILER_CUSTOMERS,
            self::RETAILER_CUSTOMER_TABLE,
        );

        $this->repositories->executeQuery($sql, [
            'ids' => array_column($customers, 'customer_id'),
        ], [
            'ids' => ArrayParameterType::STRING,
        ]);
    }

    private function insertBulkRetailerCustomer($customers)
    {
        // Since it's not possible to do 2 insert on different table at the same time just split it in 2
        $insertBuilder = $this->getInsertBuilder();

        $desiredFields = [
            'customer_id',
            'first_name',
            'last_name',
            'email',
            'email_label',
            'phone',
            'phone_label',
            'employee_id',
            'retailer_store_id',
            'created_at',
            'external_last_updated',
            'locale',
            'is_subscribed',
            'limited_visibility',
            'is_subscribed_sms_marketing',
            'contact_preference',
        ];

        $insertBuilder
            ->setTable(self::RETAILER_CUSTOMER_TABLE)
            ->addInsertFields($desiredFields)
        ;

        foreach ($customers as $customer) {
            $values = $insertBuilder->extract($desiredFields, $customer);
            $insertBuilder->addValuesSet($values);
            $this->commitTableCustomerAttributes($customer);
        }

        // START populating sf_retailer_customer_meta table.
        $bulkCustomerIds = array_column($customers, 'customer_id');
        // Delete all meta associated to these customer_id (retailer) since we are not the system of record.
        $this->repositories->deleteMany(self::RETAILER_CUSTOMER_META_TABLE, [
            'customer_id' => $bulkCustomerIds,
        ]);

        $qB = $this->repositories->getQueryBuilder();
        $qB
            ->select('cmi.customer_id, cmi.type, cmi.label, cmi.position, cmi.value, cmi.created_at')
            ->from($this->getRealTmpTableName(self::RETAILER_CUSTOMER_META_TABLE), 'cmi')
            ->where($qB->expr()->in('cmi.customer_id', ':customerIds'))
            ->setParameter('customerIds', $bulkCustomerIds, Connection::PARAM_STR_ARRAY);
        $results = $qB->execute()->fetchAll(PDO::FETCH_NUM);

        $this->repositories->insertMany(self::RETAILER_CUSTOMER_META_TABLE, [
            'customer_id',
            'type',
            'label',
            'position',
            'value',
            'created_at', // we never update meta, only customer itself (retailer)
        ], $results);
        // END populating sf_retailer_customer_meta table.

        $addOnDuplicateUpdateFields = [
            'first_name' => true,
            'last_name' => true,
            'email' => true,
            'email_label' => true,
            'phone' => true,
            'phone_label' => true,
            'locale' => true,
            'is_subscribed' => true,
            'limited_visibility' => true,
            'is_subscribed_sms_marketing' => true,
            'contact_preference' => true,
            'external_last_updated' => true,
            'updated_at' => gmdate('Y-m-d H:i:s')
        ];

        //ex: ['is_subscribed'] for credobeauty only
        $addOnDuplicateUpdateFieldsExcluded = $this->configs['sf.import_ci_customers.update_fields_excluded'];

        if (!empty($addOnDuplicateUpdateFieldsExcluded)) {
            $addOnDuplicateUpdateFields = array_diff_key($addOnDuplicateUpdateFields, array_flip($addOnDuplicateUpdateFieldsExcluded));
        }

        $insertBuilder->addOnDuplicateUpdateFields($addOnDuplicateUpdateFields);

        $insert = ($insertBuilder->prepare());

        return $this->repositories->executeQuery($insert['query'], $insert['parameters']);
    }

    /**
     * @return \Generator
     */
    private function getRetailerCustomer()
    {
        yield from $this->customerLoader->getRetailerCustomer($this);
    }

    private function updateTagRelationships()
    {
        $tblTags = self::RETAILER_CUSTOMER_TAGS_RELATIONSHIPS;
        $stgTags = $this->buffer->getStagingTable($tblTags);
        $stgCustomers = $this->buffer->getStagingTable(self::RETAILER_CUSTOMER_TABLE);

        // Use ctime from already-existing tags (if the tag is new, the ctime
        // defaults to NOW(); otherwise, the previously-recorded ctime is
        // copied from the existing data)
        $q = "UPDATE $stgTags s" .
            " JOIN $tblTags t ON s.customer_id = t.customer_id AND s.tag_id = t.tag_id" .
            " SET s.created_at = t.created_at";
        $this->queryOrThrow($q);

        // Clear the tag relationships for the customers we're importing
        // (we join on the customers table rather than the tags table so
        //  that this also works for a customer with zero tags)
        $q = "DELETE FROM t USING $tblTags t" .
            " JOIN $stgCustomers c ON t.customer_id = c.customer_id";
        $this->queryOrThrow($q);

        $q = "INSERT IGNORE INTO $tblTags (tag_id, customer_id, created_by, created_at, updated_at)" .
            " SELECT tag_id, customer_id, created_by, created_at, NOW()" .
            " FROM $stgTags";
        $this->queryOrThrow($q);
    }

    private function updateAddresses()
    {
        $tblAddresses = self::RETAILER_CUSTOMER_ADDRESSES;
        $stgAddresses = $this->buffer->getStagingTable($tblAddresses);
        $stgCustomers = $this->buffer->getStagingTable(self::RETAILER_CUSTOMER_TABLE);

        // Clear the addresses for the customers we're importing
        // (we join on the customers table rather than the addresses table so
        //  that this also works for a customer with zero addresses)
        $q = "DELETE FROM t USING $tblAddresses t" .
            " JOIN $stgCustomers c ON t.customer_id = c.customer_id";
        $this->queryOrThrow($q);

        $q = "INSERT IGNORE INTO $tblAddresses (customer_id, address_line_1, address_line_2, postal_code, state, city, country, label, is_default, created_at, updated_at)" .
            " SELECT customer_id, address_line_1, address_line_2, postal_code, state, city, country, label, is_default, created_at, NOW()" .
            " FROM $stgAddresses";
        $this->queryOrThrow($q);
    }

    private function updateEvents()
    {
        $tblEvents = self::RETAILER_CUSTOMER_EVENTS;
        $stgEvents = $this->buffer->getStagingTable($tblEvents);
        $stgCustomers = $this->buffer->getStagingTable(self::RETAILER_CUSTOMER_TABLE);

        // Clear the events for the customers we're importing
        // (we join on the customers table rather than the events table so
        //  that this also works for a customer with zero events)
        $q = "DELETE FROM t USING $tblEvents t" .
            " JOIN $stgCustomers c ON t.customer_id = c.customer_id";
        $this->queryOrThrow($q);

        $q = "INSERT IGNORE INTO $tblEvents (customer_id, label, `day`, `month`, `year`, created_at, updated_at)" .
            " SELECT customer_id, label, `day`, `month`, `year`, created_at, NOW()" .
            " FROM $stgEvents";
        $this->queryOrThrow($q);
    }

    private function updateSocialMedia()
    {
        $tblSocialMedia = self::RETAILER_CUSTOMER_SOCIAL_MEDIA;
        $stgSocialMedia = $this->buffer->getStagingTable(self::RETAILER_CUSTOMER_SOCIAL_MEDIA);
        $stgCustomers = $this->buffer->getStagingTable(self::RETAILER_CUSTOMER_TABLE);

        // Clear the events for the customers we're importing
        // (we join on the customers table rather than the events table so
        //  that this also works for a customer with zero events)
        $q = "DELETE FROM t USING $tblSocialMedia t" .
            " JOIN $stgCustomers c ON t.customer_id = c.customer_id";
        $this->queryOrThrow($q);

        $q = "INSERT IGNORE INTO $tblSocialMedia (customer_id, social_media_network_id, username, created_at, updated_at)" .
            " SELECT customer_id, social_media_network_id, username, created_at, NOW()" .
            " FROM $stgSocialMedia";
        $this->queryOrThrow($q);
    }

    /**
     * Validate the row csv against the generic format.
     *
     * Once we have more experience with onboarding CI retailers, some of these
     * may be suited to be configurable. Realistically, if a retailer is different,
     * it's probably VERY different, so this whole method will be replaced for
     * the retailer.
     *
     * @param array $row
     * @param int $mode If 'import', will stop after first validation failure, and ignore warnings. If 'validate',
     * will run all validation rules, returning a full list of failures including warnings
     * @return true|array
     * @throws ImportException
     */
    protected function validateAndClean(array &$row, $mode = self::VALIDATION_MODE_IMPORT)
    {
        $row = Util::cleanUpRowEncoding($row);

        // SF-22828 - Pad to expected column size. This will allow us to gracefully handle retailers that were
        // onboarded to CI before new columns were added - columns that the retailer doesn't pass will be
        // set to null, and guarantees a row length.
        // Ideally, we'd be looking at the header row, instead of numeric indexes, but that ship sailed long ago.
        $row = array_pad($row, static::TOTAL_EXPECTED_COLUMNS_IN_FILE, null);

        $id = $this->getRowData('id', $row);

        $validations = [
            //CustomersToContacts::COL_EMPLOYEE_ID
            function (&$row, &$warnings) use ($id) {
                if (trim($id) === '') {
                    $messageText = 'No retailer customer id provided.';

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('id_invalid');
                    $newMessage->level = LogMessage::LEVEL_ERROR;
                    $newMessage->message = $messageText;
                    $newMessage->value = $id;
                    $newMessage->field = $this->getColumnIndex('id');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    return $messageText;
                }
                return true;
            },
            // CustomersToContacts::COL_EMPLOYEE_ID
            function (&$row, &$warnings) use ($id) {
                if (isset($this->duplicate[$id])) {
                    $messageText = "Duplicate row will be skipped.";

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('id_duplicate');
                    $newMessage->level = LogMessage::LEVEL_ERROR;
                    $newMessage->message = $messageText;
                    $newMessage->value = $id;
                    $newMessage->field = $this->getColumnIndex('id');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);


                    return $messageText;
                }
                return true;
            },
            //CustomersToContacts::COL_CUSTOMER_EMAIL
            function (&$row, &$warnings) {
                $labelMaxLen = 255;
                $emailMaxLen = 255;

                $emailDefault = $this->getRowData('emailDefault', $row);
                $defaultEmailDelimiter = $this->detectDelimiter($emailDefault, $this->labelDelimiter);
                list($defaultEmailValue, $defaultEmailLabel) = array_map(function ($item) {
                    return $this->normalize($item);
                }, array_pad($this->extractField($emailDefault, $defaultEmailDelimiter), 2, null));


                if (!filter_var($defaultEmailValue, FILTER_VALIDATE_EMAIL)) {
                    if (!empty($defaultEmailValue)) {
                        $messageText = "Invalid email address.";

                        $newMessage = new LogMessage();
                        $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                        $newMessage->setCode('emailDefault_invalid');
                        $newMessage->level = LogMessage::LEVEL_WARNING;
                        $newMessage->message = $messageText;
                        $newMessage->value = $defaultEmailValue;
                        $newMessage->field = $this->getColumnIndex('emailDefault');
                        $newMessage->row = &$row;
                        $this->reportService->addLogMessage($newMessage);

                        $warnings[] = $messageText;
                    }
                    $row[$this->getColumnIndex('emailDefault')] = null;
                }

                if (!empty($defaultEmailValue) && is_string($defaultEmailValue) && mb_strlen($defaultEmailValue) > $emailMaxLen) {
                    $messageText = sprintf("This email address exceeds [%s], it will be truncated.", $emailMaxLen);
                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('emailDefault_value_maxlength');
                    $newMessage->level = LogMessage::LEVEL_WARNING;
                    $newMessage->message = $messageText;
                    $newMessage->value = $defaultEmailValue;
                    $newMessage->field = $this->getColumnIndex('emailDefault');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);
                }

                if (!empty($defaultEmailLabel) && is_string($defaultEmailLabel) && mb_strlen($defaultEmailLabel) > $labelMaxLen) {
                    $messageText = sprintf("This email label exceeds [%s], it will be truncated.", $labelMaxLen);
                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('emailDefault_lable_maxlength');
                    $newMessage->level = LogMessage::LEVEL_WARNING;
                    $newMessage->message = $messageText;
                    $newMessage->value = $defaultEmailLabel;
                    $newMessage->field = $this->getColumnIndex('emailDefault');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);
                }

                return true;
            },
            //CustomersToContacts::COL_ADDRESSES
            function (&$row, &$warnings) {
                $addresses = $this->getRowData('addresses', $row);

                if (empty($addresses)) {
                    return true;
                }

                // Validate addresses rule:
                //     1. if is a valid json format
                //     2. json_decode(123456) is a valid json format and return 123456, but still is invalid
                $decodeAddresses = json_decode($addresses, true);
                if (($decodeAddresses === null && json_last_error() !== JSON_ERROR_NONE) || !is_array($decodeAddresses)) {
                    $messageText = "Addresses is not a valid json structure.";

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('addresses_invalid');
                    $newMessage->level = LogMessage::LEVEL_WARNING;
                    $newMessage->message = $messageText;
                    $newMessage->value = $addresses;
                    $newMessage->field = $this->getColumnIndex('addresses');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    $warnings[] = $messageText;

                    $this->updateRow('addresses', null, $row);
                } elseif (is_array($decodeAddresses) && !empty($decodeAddresses)) {
                    // If is array and is not empty
                    // This section will only add messages for the feed validation
                    // Will NOT affect validation result

                    // filter empty values
                    $decodeAddresses = array_filter($decodeAddresses);

                    // if is not list of addresses make it so
                    if (!array_key_exists(0, $decodeAddresses)) {
                        $decodeAddresses = [$decodeAddresses];
                    }

                    $addressAttributes = [
                        "address_line_1",
                        "address_line_2",
                        "postal_code",
                        "state",
                        "city",
                        "country",
                        "label",
                        "is_default",
                    ];
                    foreach ($decodeAddresses as $address) {
                        if (!empty($address)) {
                            // check that all attributes exists
                            foreach ($addressAttributes as $attr) {
                                if (!array_key_exists($attr, $address)) {
                                    $messageText = "Address objects should contain the [" . $attr . "] attribute.";
                                    $newMessage = new LogMessage();
                                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                                    $newMessage->setCode('addresses_missing_' . $attr);
                                    $newMessage->level = LogMessage::LEVEL_WARNING;
                                    $newMessage->message = $messageText;
                                    $newMessage->value = $addresses;
                                    $newMessage->field = $this->getColumnIndex('addresses');
                                    $newMessage->row = &$row;
                                    $this->reportService->addLogMessage($newMessage);

                                    $warnings[] = $messageText;
                                }
                            }
                        }
                    }
                }

                return true;
            },
            // Birthday, Anniversary, Misc-date
            function (&$row, &$warnings) {
                foreach (['birthday', 'anniversary', 'misc-date'] as $event) {
                    $eventData = $originalData = $this->getRowData($event, $row);

                    // Validate events - it must be a json structure
                    if (empty($eventData)) {
                        $eventData = null;
                    } else {
                        $decoded = json_decode($eventData, true);
                        if (($decoded === null || json_last_error() !== JSON_ERROR_NONE)) {
                            // Invalid JSON
                            $messageText = sprintf("Event is not a valid json structure [%s].", $event);

                            $newMessage = new LogMessage();
                            $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                            $newMessage->setCode($event . '_invalid');
                            $newMessage->level = LogMessage::LEVEL_WARNING;
                            $newMessage->message = $messageText;
                            $newMessage->value = $originalData;
                            $newMessage->field = $this->getColumnIndex($event);
                            $newMessage->row = &$row;
                            $this->reportService->addLogMessage($newMessage);

                            $warnings[] = $messageText;
                            $eventData = null;
                        } else {
                            // Process / Sanitize date in JSON format
                            $eventData = $decoded;

                            // Sanitize json data
                            $invalid = false;
                            $year   = !empty($eventData['year'])     ? intval($eventData['year'])    : null;
                            $month  = !empty($eventData['month'])    ? intval($eventData['month'])   : null;
                            $day    = !empty($eventData['date'])     ? intval($eventData['date'])    : null;

                            // There exists an inconsistency where it is sometime labeled "date" and other times "day"
                            if (is_null($day) && !empty($eventData['day'])) {
                                $day = intval($eventData['day']);
                            }

                            // Month is required - year or day can be null
                            if (!empty($month)) {
                                $eventData = [
                                    'year'  => $year,
                                    'month' => $month,
                                    'date'  => $day,
                                ];

                                // Validate the calendar date / period makes sense
                                $checkData = [
                                    'year'  => !empty($eventData['year']) ? $eventData['year'] : 2000 /* leap year*/,
                                    'month' => $eventData['month'],
                                    'date'  => !empty($eventData['date']) ? $eventData['date'] : 1,
                                ];
                                if (!checkdate($checkData['month'], $checkData['date'], $checkData['year'])) {
                                    $invalid = true;

                                    $messageText = 'Value does not appear to be a valid date.';
                                    $newMessage = new LogMessage();
                                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                                    $newMessage->setCode($event . '_checkdate');
                                    $newMessage->level = LogMessage::LEVEL_WARNING;
                                    $newMessage->message = $messageText;
                                    $newMessage->value = $originalData;
                                    $newMessage->field = $this->getColumnIndex($event);
                                    $newMessage->row = &$row;
                                    $this->reportService->addLogMessage($newMessage);
                                }
                            } else {
                                $invalid = true;

                                $messageText = 'A month is a required for every date.';
                                $newMessage = new LogMessage();
                                $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                                $newMessage->setCode($event . '_required_month');
                                $newMessage->level = LogMessage::LEVEL_WARNING;
                                $newMessage->message = $messageText;
                                $newMessage->value = $originalData;
                                $newMessage->field = $this->getColumnIndex($event);
                                $newMessage->row = &$row;
                                $this->reportService->addLogMessage($newMessage);
                            }

                            if ($invalid) {
                                $warnings[] = sprintf("Event is not a valid json structure [%s].", $event);
                            }
                        }
                    }

                    // Update the row with the final values
                    $this->updateRow($event, $eventData, $row);
                }
                return true;
            },
            // CustomersToContacts::COL_CRM_LAST_PROCESSED
            function (&$row, &$warnings) {
                $crmLastProcessedDate = trim($this->getRowData('crm-last-processed', $row) ?? '');
                $crmLastProcessedDateTime = strtotime($crmLastProcessedDate);
                $messageText = $code = null;
                if ($this->isCustomerSyncEnabled() && empty($crmLastProcessedDate)) {
                    $messageText = "crm-last-processed is required and cannot be empty when two-way-sync is enabled";
                    $code = "required";
                } elseif (!empty($crmLastProcessedDate)) {
                    if (preg_match('/^0{4}-0{2}-0{2}(.*)?$/', $crmLastProcessedDate) || $crmLastProcessedDateTime === false) {
                        $messageText = "crm-last-processed has to be a valid date and cannot set to 0000-00-00";
                        $code = "invalid";
                    }
                }
                if ($messageText) {
                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('crm_last_processed_' . $code);
                    $newMessage->level = LogMessage::LEVEL_ERROR;
                    $newMessage->message = $messageText;
                    $newMessage->value = $crmLastProcessedDate;
                    $newMessage->field = $this->getColumnIndex('crm-last-processed');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    return $messageText;
                }
                $crmLastProcessedDate = $crmLastProcessedDateTime ? gmdate('Y-m-d H:i:s', $crmLastProcessedDateTime) : null;
                $this->updateRow('crm-last-processed', $crmLastProcessedDate, $row);
                return true;
            },
            // Social networks
            function (&$row, &$warnings) {
                $logInvalidUsername = function ($input, $network, $regex = null) use (&$warnings, &$row) {
                    $messageText = sprintf("Input for [%s] doesn't match username regex.", $network);
                    $comment = null;
                    if (!empty($regex)) {
                        $comment = sprintf('The regex the value must match is [%s].', $regex);
                        $messageText .= ' ' . $comment;
                    }

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_COLUMN_NOTICE;
                    $newMessage->setCode($network . '_invalid');
                    $newMessage->level = LogMessage::LEVEL_NOTICE;
                    $newMessage->message = $messageText;
                    $newMessage->comment = $comment;
                    $newMessage->value = $input;
                    $newMessage->field = $this->getColumnIndex($network);
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    $warnings[] = $messageText;
                };
                $logInvalidWebsite = function ($input, $network) use (&$warnings) {
                    $warnings[] = sprintf("Input [%s] for [%s] doesn't match username regex", $input, $network);
                };


                $validateNetwork = function (&$input, $regex) {
                    if (filter_var($input, FILTER_VALIDATE_URL)) {
                        $parsedUrl = parse_url($input);
                        $path      = trim($parsedUrl['path'], '/');

                        // Overwrite the network if url, because we are supposed to save in DB only the username.
                        // The MySQL comment is wrong, it shouldn't be an url.
                        $input = $path;

                        return preg_match($regex, $path);
                    } else {
                        return preg_match($regex, $input);
                    }
                };

                $facebook = $this->getRowData('facebook', $row);
                $regex = '/^[a-z\d.]{5,}$/i';
                if (!$validateNetwork($facebook, $regex)) {
                    if (!empty($facebook)) {
                        $logInvalidUsername($facebook, 'facebook', $regex);
                    }
                    $facebook = null;
                }

                $twitter = $this->getRowData('twitter', $row);
                $regex = '/^[A-Za-z0-9_]{1,15}$/i';
                if (!$validateNetwork($twitter, $regex)) {
                    if (!empty($twitter)) {
                        $logInvalidUsername($twitter, 'twitter', $regex);
                    }
                    $twitter = null;
                }

                $instagram = $this->getRowData('instagram', $row);
                $regex = '/^[a-zA-Z0-9._]+$/i';
                if (!$validateNetwork($instagram, $regex)) {
                    if (!empty($instagram)) {
                        $logInvalidUsername($instagram, 'instagram', $regex);
                    }
                    $instagram = null;
                }

                $website = $this->getRowData('website', $row);
                if (!filter_var($website, FILTER_VALIDATE_URL)) {
                    if (!empty($website)) {
                        $logInvalidWebsite($website, 'other');
                    }
                    $website = null;
                }

                // Update the row with the final values
                $this->updateRow('facebook', $facebook, $row);
                $this->updateRow('twitter', $twitter, $row);
                $this->updateRow('instagram', $instagram, $row);
                $this->updateRow('website', $website, $row);

                return true;
            },
            // Status
            function (&$row, &$warnings) {
                $status = $this->getRowData('status', $row);
                if (empty($status)) {
                    $this->updateRow('status', 'new', $row);
                }

                $status = strtolower($this->getRowData('status', $row));

                // Validate statuses (Empty is considered as new), still valid
                if (!empty($status) && !in_array($status, [self::STATUS_NEW, self::STATUS_MODIFIED, self::STATUS_DELETED])) {
                    $messageText = "Invalid customer status.";

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('status_invalid');
                    $newMessage->level = LogMessage::LEVEL_ERROR;
                    $newMessage->message = $messageText;
                    $newMessage->value = $status;
                    $newMessage->field = $this->getColumnIndex('status');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    return $messageText;
                }
                return true;
            },
            // Email subscription
            function (&$row, &$warnings) {
                $isSubscribed = $this->getRowData('sf-email-subscription', $row);

                // Convert subscription column to 1-0 since we use a tinyint in DB
                $normalizedSubscription = $this->normalize($isSubscribed);

                // I can't force a value here, because if empty, i don't want to overwrite the value during update
                // except if sync is on.
                $subscription = "";

                // This is the same logic as in customer (contact) importer
                if (in_array($normalizedSubscription, ['yes', 'oui', '1'])) {
                    $subscription = RetailerCustomer::EMAIL_SUBSCRIPTION_STATUS_SUBSCRIBED;
                } elseif (in_array($normalizedSubscription, ['no', 'non', '0'])) {
                    $subscription = RetailerCustomer::EMAIL_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;
                } elseif (in_array($normalizedSubscription, ['2'])) {
                    $subscription = RetailerCustomer::EMAIL_SUBSCRIPTION_STATUS_UNSUBSCRIBED;
                } elseif (!empty($normalizedSubscription)) {
                    $messageText = "Invalid email marketing subscription flag value.";

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('sf_email_subscription_invalid');
                    $newMessage->level = LogMessage::LEVEL_WARNING;
                    $newMessage->message = $messageText;
                    $newMessage->value = $isSubscribed;
                    $newMessage->field = $this->getColumnIndex('sf-email-subscription');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    $warnings[] = $messageText;
                }

                $this->updateRow('sf-email-subscription', $subscription, $row);
                return true;
            },
            // Limited visibility
            function (&$row, &$warnings) {
                $limitedVisibility = $this->getRowData('limited-visibility', $row);

                // Convert limited column to 1-0 since we use a tinyint in DB
                $normalizedLimitedVisibility = $this->normalize($limitedVisibility);

                // Any empty value will be converted to 0 (the default value)
                $limitedVisibilityFlag = 0;

                if (in_array($normalizedLimitedVisibility, ['1'])) {
                    $limitedVisibilityFlag = 1;
                } elseif (!empty($normalizedLimitedVisibility)) {
                    $messageText = "Invalid limited visibility flag.";

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('limited_visibility_invalid');
                    $newMessage->level = LogMessage::LEVEL_WARNING;
                    $newMessage->message = $messageText;
                    $newMessage->value = $limitedVisibility;
                    $newMessage->field = $this->getColumnIndex('limited-visibility');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    $warnings[] = $messageText;
                }

                $this->updateRow('limited-visibility', $limitedVisibilityFlag, $row);
                return true;
            },
            // SMS Subscription
            function (&$row, &$warnings) {
                $isSubscribed = $this->getRowData('sf-sms-subscription', $row);

                // Convert subscription column to 1-0 since we use a tinyint in DB
                $normalizedSubscription = $this->normalize($isSubscribed);

                // I can't force a value here, because if empty, i don't want to overwrite the value during update
                // except if sync is on.
                $subscription = "";

                // This is the same logic as in customer (contact) importer
                if (in_array($normalizedSubscription, ['yes', 'oui', '1'])) {
                    $subscription = RetailerCustomer::EMAIL_SUBSCRIPTION_STATUS_SUBSCRIBED;
                } elseif (in_array($normalizedSubscription, ['no', 'non', '0'])) {
                    $subscription = RetailerCustomer::EMAIL_SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;
                } elseif (in_array($normalizedSubscription, ['2'])) {
                    $subscription = RetailerCustomer::EMAIL_SUBSCRIPTION_STATUS_UNSUBSCRIBED;
                } elseif (!empty($normalizedSubscription)) {
                    $messageText = "Invalid SMS marketing subscription flag.";

                    $newMessage = new LogMessage();
                    $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                    $newMessage->setCode('sf_sms_subscription_invalid');
                    $newMessage->level = LogMessage::LEVEL_WARNING;
                    $newMessage->message = $messageText;
                    $newMessage->value = $isSubscribed;
                    $newMessage->field = $this->getColumnIndex('sf-sms-subscription');
                    $newMessage->row = &$row;
                    $this->reportService->addLogMessage($newMessage);

                    $warnings[] = $messageText;
                }

                $this->updateRow('sf-sms-subscription', $subscription, $row);
                return true;
            },
            // Preferred contact method
            function (&$row, &$warnings) {
                $contactPreference = $this->getRowData('preferred-contact-method', $row);

                $normalizedContactPreference = $this->normalize($contactPreference);

                $preference = RetailerCustomer::CONTACT_PREFERENCE_NO_PREFERENCE;

                if (!empty($normalizedContactPreference)) {
                    if (!in_array($normalizedContactPreference, RetailerCustomer::CONTACT_PREFERENCES)) {
                        $messageText = "Invalid preferred contact method.";

                        $newMessage = new LogMessage();
                        $newMessage->type = LogMessage::TYPE_INVALID_COLUMN;
                        $newMessage->setCode('preferred_contact_method_invalid');
                        $newMessage->level = LogMessage::LEVEL_WARNING;
                        $newMessage->message = $messageText;
                        $newMessage->value = $contactPreference;
                        $newMessage->field = $this->getColumnIndex('preferred-contact-method');
                        $newMessage->row = &$row;
                        $this->reportService->addLogMessage($newMessage);

                        $warnings[] = $messageText;
                    } elseif ($normalizedContactPreference == RetailerCustomer::CONTACT_PREFERENCE_NONE) {
                        $preference = RetailerCustomer::CONTACT_PREFERENCE_NO_PREFERENCE;
                    } else {
                        $preference = $normalizedContactPreference;
                    }
                }

                $this->updateRow('preferred-contact-method', $preference, $row);
                return true;
            }
        ];

        $locale = $this->getRowData('preferred-language', $row);
        if (!empty($locale)) {
            // This will return null or a valid (format) locale (Not necessary a valid locale for the future store)
            $this->updateRow('preferred-language', $this->multilang->normalizeLocale($locale), $row);
        }

        $validationFailures = $this->applyValidationAndCleaningRules(
            $row,
            $validations,
            $mode
        );

        if (empty($validationFailures)) {
            // We want to relax the logic when 2way sync is off because we want to have similar behaviour
            // in CI vs CRM import. If means, that if it's on, we will use the duplicate check.
            if ($this->isCustomerSyncEnabled()) {
                $this->duplicate[$id] = true;
            }
        }

        return empty($validationFailures) ? true : $validationFailures;
    }

    private function updateRow($label, $value, &$row)
    {
        return $row[$this->getColumnIndex($label)] = $value;
    }

    protected function isSkippedField($label)
    {
        return !in_array($label, array_keys($this->columnMapping));
    }

    protected function getRowData($label, $row)
    {
        if ($this->isSkippedField($label)) {
            return null;
        }

        return $row[$this->getColumnIndex($label)] ?? null;
    }

    protected function getColumnIndex($label)
    {
        return $this->columnMapping[$label];
    }

    /**
     * Add customer data to the append buffer.
     *
     * @param $row
     * @param $position
     * @throws ImportException
     */
    protected function appendRow($row, $position)
    {
        $customerId = $this->getRowData('id', $row);

        // Clean first name and last name as needed when data contains backslash, non-utf-8 characters
        // TODO: Should we do that for all column ? What are the performance impact of this ?
        $firstName = $this->util->cleanUpValue($this->getRowData('firstName', $row));
        $lastName = $this->util->cleanUpValue($this->getRowData('lastName', $row));

        $emailDefault = $this->getRowData('emailDefault', $row);
        $emailAlternates = $this->getRowData('emailAlternates', $row);
        $phoneDefault = $this->getRowData('phoneDefault', $row);
        $phoneAlternates = $this->getRowData('phoneAlternates', $row);
        $status = $this->getRowData('status', $row);
        $retailerStoreId = $this->getRowData('retailerStoreId', $row);
        $employeeId = $this->getRowData('employeeId', $row);
        $customerTagIds = $this->getRowData('tags', $row);
        $addresses = $this->getRowData('addresses', $row);
        $birthday = $this->getRowData('birthday', $row);
        $anniversary = $this->getRowData('anniversary', $row);
        $miscDate = $this->getRowData('misc-date', $row);
        $facebook = $this->getRowData('facebook', $row);
        $twitter = $this->getRowData('twitter', $row);
        $instagram = $this->getRowData('instagram', $row);
        $website = $this->getRowData('website', $row);
        $externalLastUpdated = $this->getRowData('crm-last-processed', $row);
        $subscription = $this->getRowData('sf-email-subscription', $row);
        $locale = $this->getRowData('preferred-language', $row) ?? null;
        $attributes = $this->getRowData('attributes', $row);
        $limitedVisibility = $this->getRowData('limited-visibility', $row);
        $smsSubscription = $this->getRowData('sf-sms-subscription', $row);
        $contactPreference = $this->getRowData('preferred-contact-method', $row);

        $defaultEmailDelimiter = $this->detectDelimiter($emailDefault, $this->labelDelimiter);
        $defaultPhoneDelimiter = $this->detectDelimiter($phoneDefault, $this->labelDelimiter);

        list($defaultEmailValue, $defaultEmailLabel) = array_map(function ($item) {
            return $this->normalize($item);
        }, array_pad($this->extractField($emailDefault, $defaultEmailDelimiter), 2, null));

        list($defaultPhoneValue, $defaultPhoneLabel) = array_map(function ($item) {
            return $this->normalize($item);
        }, array_pad($this->extractField($phoneDefault, $defaultPhoneDelimiter), 2, null));

        // First table with the status
        $this->buffer->append(
            self::RETAILER_CUSTOMER_TABLE,
            [
                null,   // id
                $customerId,
                null,   // gender
                $firstName,
                $lastName,
                $defaultEmailValue ?: null,
                $defaultEmailLabel ?: null,
                $defaultPhoneValue ?: null,
                $defaultPhoneLabel ?: null,
                null,   // addressLine1
                null,   // addressLine2
                null,   // zipcode
                null,   // postalcode
                null,   // city
                null,   // state
                null,   // country
                null,   // longitude
                null,   // latitude
                $subscription === '' ? null : $subscription, // possible value are null, 0, 1
                null,   // created_at
                null,   // updated_at
                $employeeId ?: null,
                $retailerStoreId ?: null,
                $externalLastUpdated ?: null,
                $locale ?: null,
                $limitedVisibility === '' ? 0 : $limitedVisibility,
                // Moved at the end, since we want to be backward compatible if we decide to rollback code but not DB.
                $smsSubscription === '' ? null : $smsSubscription, // possible value are null, 0, 1
                $contactPreference,
                // Extra field added - used during import step itself
                $status ?: null,
                $position,
            ]
        );

        // Generic function that will populate the meta file
        if (!empty($phoneAlternates)) {
            $this->appendMeta($customerId, $phoneAlternates, $defaultPhoneValue, CustomerMeta::TYPE_PHONE);
        }

        if (!empty($emailAlternates)) {
            $this->appendMeta($customerId, $emailAlternates, $defaultEmailValue, CustomerMeta::TYPE_EMAIL);
        }

        if (!empty($attributes)) {
            $this->appendAttributes($customerId, $attributes);
        }

        $customerTagIdList = str_getcsv($customerTagIds, $this->tagDelimiter);

        if ($customerTagIdList) {
            $this->bufferAppendCustomerTags($customerTagIdList, $customerId);
        }

        // Addresses
        if (!empty($addresses)) {
            // $addresses have been checked if valid json in function:validateAndClean
            $addresses = json_decode($addresses, true);
            $this->bufferAppendCustomerAddresses($addresses, $customerId);
        }

        // Events
        if ($birthday) {
            $this->bufferAppendCustomerEvent(RetailerCustomerEvents::CUSTOMER_EVENTS_LABEL_BIRTHDAY, $birthday, $customerId);
        }
        if ($anniversary) {
            $this->bufferAppendCustomerEvent(RetailerCustomerEvents::CUSTOMER_EVENTS_LABEL_ANNIVERSARY, $anniversary, $customerId);
        }
        if ($miscDate) {
            $this->bufferAppendCustomerEvent(RetailerCustomerEvents::CUSTOMER_EVENTS_LABEL_OTHER, $miscDate, $customerId);
        }

        // Social Media
        if ($facebook) {
            $this->bufferAppendCustomerSocialMedia('facebook', $facebook, $customerId);
        }
        if ($twitter) {
            $this->bufferAppendCustomerSocialMedia('twitter', $twitter, $customerId);
        }
        if ($instagram) {
            $this->bufferAppendCustomerSocialMedia('instagram', $instagram, $customerId);
        }
        if ($website) {
            $this->bufferAppendCustomerSocialMedia('other', $website, $customerId);
        }
    }

    /**
     * Extract attribute data and put into buffer
     * WESHOULD: Try to make this function share with Customer Importer:appendAttributes() because the logic is similar
     * @param $customerId
     * @param $rawAttributes
     */
    private function appendAttributes($customerId, $rawAttributes)
    {
        if (empty($rawAttributes)) {
            return;
        }

        $attributes = json_decode($rawAttributes, true);
        if ($attributes === null || json_last_error() !== JSON_ERROR_NONE) {
            $this->log(
                sprintf(
                    "The attribute column is not a valid json structure: [%s]",
                    $attributes
                )
            );
            $attributes = null;
            return;
        }

        if (!isset($this->attributeIds)) {
            $allAttributes      = $this->attributesManager->getAll([], 0, -1, false);

            foreach ($allAttributes as $attribute) {
                // id from retailer site -> id of our side
                $this->attributeIdsMapper[$attribute->attribute_id] = $attribute->id;
            }

            $this->attributeIds = array_keys($this->attributeIdsMapper);
        }

        $violations = [];
        $errorLog = [];
        foreach ($attributes as $attribute) {
            if (!in_array($attribute['attribute_id'], $this->attributeIds)) {
                $errorLog[] = sprintf("attribute_id(retailer' site) [%s] does not exist ", $attribute['attribute_id']);
            } else {
                // 0. if attribute_value is null, it will be skipped by considering as invalid
                // 1. if attribute_value is empty, still put into buffer so can delete it from real table later step
                // 2. if attribute_id exist, will do logic to update later step
                if (!isset($attribute['attribute_value']) || $this->attributesManager->validateAttributeByRetailerId($attribute['attribute_id'], $attribute['attribute_value'], $violations) === false) {
                    $errorLog[] = sprintf(
                        "Invalid attribute. attribute_id(retailer' site) [%s] with value : [%s] ",
                        $attribute['attribute_id'],
                        $attribute['attribute_value']
                    );
                    continue;
                }

                // this attribute_id  customer-side id, we need map this to our id
                $this->buffer->append(
                    self::RETAILER_CUSTOMERS_TO_CUSTOMER_ATTRIBUTES_TABLE,
                    [
                        null,
                        $customerId,
                        $this->attributeIdsMapper[$attribute['attribute_id']],
                        // json_encode to save an array to plain text
                        is_array($attribute['attribute_value']) ? json_encode($attribute['attribute_value']) : $attribute['attribute_value'],
                    ]
                );
            }
        }

        if (!empty($errorLog)) {
            $this->log(implode(";", $errorLog));
        }
    }


    /**
     * Move customer attribute from tmp table to real table
     * The CI Customer importer replaces all Extended Attribute data for the target Retailer Customer record, no matter Two-Way sync on/off
     *     if the field is empty, existing attributes will be removed
     *     if the field is not empty, all attributes will be overwritten
     * @param $customer
     * @throws ImportException
     */
    private function commitTableCustomerAttributes($customer)
    {
        $qB = $this->repositories->getQueryBuilder();

        $qB
            ->select('rca.*')
            ->from($this->getRealTmpTableName(self::RETAILER_CUSTOMERS_TO_CUSTOMER_ATTRIBUTES_TABLE), 'rca')
            ->where($qB->expr()->eq('rca.customer_id', $qB->expr()->literal($customer['customer_id'])));

        $results = $qB->execute()->fetchAll();

        $this->retailerCustomersToCustomerAttributesManager->deleteMany(['customer_id' => $customer['customer_id']]);

        foreach ($results as $result) {
            if (isset($result['attribute_value']) && trim($result['attribute_value']) !== '') {
                $insertBuilder = $this->getInsertBuilder();

                $insertBuilder
                    ->setTable(self::RETAILER_CUSTOMERS_TO_CUSTOMER_ATTRIBUTES_TABLE)
                    ->addInsertFields(['customer_id', 'attribute_id', 'attribute_value'])
                    ->addValuesSet([
                        'customer_id'     => $result['customer_id'],
                        'attribute_id'    => $result['attribute_id'],
                        'attribute_value' => $result['attribute_value'],
                    ]);
                $insertBuilder->addOnDuplicateUpdateFields(['attribute_value' => true]);

                $insert = ($insertBuilder->prepare());

                $this->repositories->executeQuery($insert['query'], $insert['parameters']);
            }
        }
    }

    /**
     * Appends retailer customer tags to buffer
     * @param $customerTagIds
     * @param $customerId
     */
    private function bufferAppendCustomerTags($customerTagIds, $customerId)
    {
        $customerTagIds = array_map('strtolower', array_map('trim', $customerTagIds));
        foreach ($customerTagIds as $tagId) {
            if (isset($this->tagIds[$tagId])) {
                $tag = $this->tagIds[$tagId];
                $this->buffer->append(
                    self::RETAILER_CUSTOMER_TAGS_RELATIONSHIPS,
                    [
                        null,        // id
                        $tag,        // Customer Tag ID
                        $customerId, // Customer ID
                        0,           // created_by
                        null,        // created_at
                        null,        // updated_at
                    ]
                );
            }
        }
    }

    /**
     * Appends retailer customer addresses to buffer
     *
     * @param array|null $customerAddresses
     * @param int $customerId
     */
    private function bufferAppendCustomerAddresses(?array $customerAddresses, $customerId)
    {
        if (empty($customerAddresses)) {
            return;
        }

        // Do we have 1 or more addresses ?!?
        if (!is_array($customerAddresses[0])) {
            $customerAddresses = [$customerAddresses];
        }

        foreach ($customerAddresses as $customerAddress) {
            // TODO: All of those will generate notice if missing from json. We should add validation or use fallback
            $this->buffer->append(
                self::RETAILER_CUSTOMER_ADDRESSES,
                [
                    null,        // id
                    $customerId, // Customer ID
                    $customerAddress['address_line_1'],
                    $customerAddress['address_line_2'] ?? null,
                    $customerAddress['postal_code'],
                    $customerAddress['state'],
                    $customerAddress['city'],
                    $customerAddress['country'],
                    $customerAddress['label'],
                    $customerAddress['is_default'],
                    null,        // created_at
                    null,        // updated_at
                ]
            );
        }
    }

    /**
     * Appends retailer customer events to buffer
     *
     * @param array $customerEvent
     * @param int $customerId
     */
    private function bufferAppendCustomerEvent($label, $customerEvent, $customerId)
    {
        if (!empty($customerEvent)) {
            $this->buffer->append(
                self::RETAILER_CUSTOMER_EVENTS,
                [
                    null,        // id
                    $customerId, // Customer ID
                    $label,
                    $customerEvent['date'],
                    $customerEvent['month'],
                    $customerEvent['year'],
                    null,        // created_at
                    null,        // updated_at
                ]
            );
        }
    }

    /**
     * Appends retailer customer social media to buffer
     *
     * @param string $networkName
     * @param string $username
     * @param int $customerId
     */
    private function bufferAppendCustomerSocialMedia($networkName, $username, $customerId)
    {
        /** @var SocialMediaNetwork $network */
        $network = $this->socialMediaNetworkManager->getOneOrNull([
            'name' => $networkName,
        ]);

        if (!empty($network) && !empty($username)) {
            $this->buffer->append(
                self::RETAILER_CUSTOMER_SOCIAL_MEDIA,
                [
                    null,        // id
                    $customerId, // Customer ID
                    $network->getId(), // Social Media Network Id
                    $username,
                    null,        // created_at
                    null,        // updated_at
                ]
            );
        }
    }

    /**
     * Preload the possible tags for this retailer.
     */
    private function loadTagIds()
    {
        $this->tagIds = [];

        $qB = $this->repositories->getQueryBuilder();
        $qB->select('id', 'retailer_tag_id')->from('sf_customer_tags');
        $results = $qB->execute()->fetchAll();

        foreach ($results as $row) {
            $this->tagIds[strtolower($row['retailer_tag_id'])] = $row['id'];
        }
    }

    public function createContacts()
    {
        // note : this is for getCrmCustomerRows from retailer customer file, c2c files is always set delimiter to ','
        if (!empty($this->configs['importer.customer.ingestor.csv.delimiter'])) {
            $delimiter = $this->configs['importer.customer.ingestor.csv.delimiter'];
        } else {
            $delimiter = $this->mainDelimiter;
        }
        $this->customers2contacts->setCsvDelimiter($delimiter);

        // Set the parent of c2c
        $this->customers2contacts->setImportParent($this->importModel);

        $ingestor = $this->getIngestor();

        $this->c2cFiles = $this->customers2contacts->generateFromCustomerFile(
            $ingestor,
            $this->getCustomerToCrmContactColumnMapping(),
            $this->uploadCustomerToContactFiles,
            !($this->deleteFileAfter)
        );
    }

    public function setUploadCustomerToContactFiles(bool $flag)
    {
        $this->uploadCustomerToContactFiles = $flag;
        return $this;
    }

    /**
     * Check if sync between retailer-salesfloor is enabled. Based on one config at the moment
     *
     * @return bool
     */
    private function isCustomerSyncEnabled()
    {
        return $this->configs['retailer.clienteling.customer.sync'] === true;
    }

    /**
     * Get order of columns
     * @return array
     */
    public function getFieldToColumnKeyOrderedMap()
    {
        // map position to key
        $columnIndexToKey = array_flip($this->columnMapping);

        //ex:
        // [0 => 'id']
        // [1 => 'firstName']
        // [2 => 'lastName']

        // make sure keys are ordered based on index
        ksort($columnIndexToKey);

        // Pad any unspecified column keys so that position is consistent
        $result = [];
        $maxPos = max($this->columnMapping);
        for ($i = 0; $i <= $maxPos; ++$i) {
            if (array_key_exists($i, $columnIndexToKey)) {
                $result[$i] = $columnIndexToKey[$i];
            } else {
                $result[$i] = ""; // pad empty indexes, should never happen but its possible with the configs this way.
            }
        }

        return $result;
    }

    /*
     * See docblock in ImporterReportInterface
     */
    public function getColumnAliases()
    {
        // note: default alias will be the first in list
        return [
            'id'                        => ['id'],
            'firstName'                 => ['first-name', 'firstName'],
            'lastName'                  => ['last-name', 'lastName'],
            'emailDefault'              => ['email-default', 'emailDefault'],
            'emailAlternates'           => ['email-alternates', 'emailAlternates'],
            'phoneDefault'              => ['phone-default', 'phoneDefault'],
            'phoneAlternates'           => ['phone-alternates', 'phoneAlternates'],
            'status'                    => ['status'],
            'retailerStoreId'           => ['store-id', 'store', 'retailerStoreId'],
            'employeeId'                => ['employee-id', 'employeeId'],
            'tags'                      => ['tags', 'tag-id'],
            'secondary-employee-ids'    => ['secondary-employee-ids'],
            'secondary-store-ids'       => ['secondary-store-ids'],
            'sf-contact-record-id'      => ['sf-contact-record-id', 'sf-contact-record-ids'],
            'addresses'                 => ['addresses'],
            'facebook'                  => ['facebook'],
            'twitter'                   => ['twitter'],
            'instagram'                 => ['instagram'],
            'website'                   => ['website'],
            'birthday'                  => ['birthday'],
            'anniversary'               => ['anniversary'],
            'misc-date'                 => ['misc-date'],
            'crm-last-processed'        => ['crm-last-processed'],
            'sf-email-subscription'     => ['sf-email-subscription'],
            'preferred-language'        => ['preferred-language'],
            'attributes'                => ['attributes', 'extended-attributes'],
            'limited-visibility'        => ['limited-visibility'],
            'sf-sms-subscription'       => ['sf-sms-subscription'],
            'preferred-contact-method'  => ['preferred-contact-method']
        ];
    }
}
