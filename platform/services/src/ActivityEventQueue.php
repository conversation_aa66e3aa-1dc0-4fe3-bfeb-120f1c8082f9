<?php

namespace Salesfloor\Services;

use Salesfloor\API\Managers\Events;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\MessageQueue\MessageQueueInterface;

class ActivityEventQueue extends EventQueue
{
    // List of Actions. ALWAYS APPEND TO BOTTOM
    private $activityActions = [
        'USER_ACTIVITY_TRACKING',
        'CHAT_ANSWER_RATE_TRACKING'
    ];

    private $userActivity;

    private $eventManager;

    private $activityActionIds = [
        'start-user-chat',
        'stop-user-chat',
        'track-chat-request-event'
    ];


    /**
     * Construct the EventQueue Instance and initialize needed variables
     * @param Configs $configs Configs instance
     */
    public function __construct(UserActivity $userActivity, Events $eventsManager, MessageQueueInterface $messageQueue, Configs $configs, $attributionService)
    {
        $this->userActivity = $userActivity;
        $this->eventManager = $eventsManager;
        parent::__construct($messageQueue, $configs, $attributionService);
    }


    /**
     * Function validates the received event, throwing exception if any required
     * fields are missing or contain invalid data (action received not in list)
     * Once validated, a newly structure event is created with generated timestamp,
     * and retailer / environment information. Once ready it is returned in json
     * @param array $params Original Event Array
     * @return string         JSON representation of the validated Event
     * @throws \Exception
     */
    public function serializeSend(array $params)
    {
        $params['action'] = strtoupper($params['action']);
        // Build final structure for event
        $event = [];
        if (!in_array($params['action'], $this->activityActions)) {
            return parent::serializeSend($params);
        }

        foreach (['action', 'action_id', 'type', 'user_ids', 'timestamp'] as $expectedField) {
            $event[$expectedField] = (isset($params[$expectedField]) ? $params[$expectedField] : null);
        }

        $strPayload = json_encode($event);
        if ($strPayload === null) {
            throw new \Exception("Unable to queue up event, failure to json encode " . var_export($event, true));
        }
        return $strPayload;
    }


    /**
     * Function un-encodes the event string (json) received from the queue polling
     * It validates that the string is in the expected structure and contains the
     * minimum required fields for saving a valid event
     * @param  string $json String JSON representation of the event in the queue
     * @return array        Valid event array decoded from json input
     */
    public function unserializeSend($json)
    {
        $payload = json_decode($json, true);
        if (!isset($payload)) {
            throw new \Exception("Bad payload; it's not JSON: $json");
        }

        if (!in_array($payload['action'], $this->activityActions)) {
            return parent::unserializeSend($json);
        }

        foreach (['action', 'action_id', 'timestamp'] as $k) {
            if (!isset($payload[$k])) {
                throw new \Exception("Bad payload; it has no '$k' property: $json");
            }
        }
        if (!in_array($payload['action'], $this->activityActions)) {
            throw new \Exception("POP Event: Action {$payload['action']} is not valid. Skipping");
        }

        foreach (['type', 'user_ids'] as $k) {
            if (!isset($payload[$k])) {
                throw new \Exception("Bad payload; it has no '$k' property: $json");
            }
        }
        if ($payload['action'] === 'CHAT_ANSWER_RATE_TRACKING') {
            if (!isset($payload['uniq_id'])) {
                throw new \Exception("Bad payload; it has no 'uniq_id' property: $json");
            }
        }

        return $payload;
    }



    /**
     * Function saves an event (usually received from queue polling / pop) into
     * the database table "sf_events" and "sf_useractivity" using a DBAL insert
     * @param  MysqlRepository $mysqlRepository Repository instance used for writing events
     * @param  array           $event           Array representation of event to log
     * @return bool                             TRUE when successfully saved, FALSE on error
     */
    public function saveEvent(MysqlRepository $mysqlRepository, array $event)
    {
        try {
            if (!in_array($event['action'], $this->activityActions)) {
                return parent::saveEvent($mysqlRepository, $event);
            }
            switch ($event['action_id']) {
                case 'start-user-chat':
                    foreach ($event['user_ids'] as $userId) {
                        $this->userActivity->startUserChatActivity($userId, $event['type'], date('Y-m-d H:i:s', $event['timestamp']));
                    }
                    break;

                case 'stop-user-chat':
                    foreach ($event['user_ids'] as $userId) {
                        $this->userActivity->stopUserChatActivity($userId, $event['type'], date('Y-m-d H:i:s', $event['timestamp']));
                    }

                    break;
                case 'track-chat-request-event':
                    $params = [
                        'uniq_id' => $event['uniq_id'],
                        'type' => $event['type'],
                        'customer_id' => $event['customer_id'],
                        'attributes' => $event['attributes'],
                        'source' => $event['source'],
                        'satisfied' => $event['satisfied'],
                        'event_id' => $event['event_id'],
                        'date' => date('Y-m-d H:i:s', $event['timestamp'])
                    ];
                    foreach ($event['user_ids'] as $userId) {
                        $params['user_id'] = $userId;
                        $this->eventManager->saveEvent($params);
                    }
                    break;
                default:
                    // We don't process the request any further, but we make sure its taken out of queue.
                    error_log(sprintf('Unknown Activity action_id provided. Expected one of %s; Got %s', implode(', ', $this->activityActionIds), $event['action_id']));
            }
        } catch (\Exception $insertException) {
            error_log('Error Inserting Event: ' . json_encode($event) . ' - With Error: ' . $insertException->getMessage());
            return false;
        }
        return true;
    }



    /**
     * Function will call any other services / functions that MAY act upon the results
     * of a just saved event.
     * Actions could include Processing Attribution when recording a transaction, updating
     * additional meta data tables when performing complex actions, etc...
     * @param  array  $savedEvent Data that was returned from unserializeSend and successfully saved
     *                                Minimum array structure ['action' => 'SOME_ACTION', 'fingerprint' => 1234, 'timestamp' => 5678]
     */
    public function postProcessEvent(array $savedEvent)
    {
        if (!in_array($savedEvent['action'], $this->activityActions)) {
             parent::postProcessEvent($savedEvent);
        }
    }
}
