<?php

namespace Salesfloor\Services\Mail\Templates\Internal;

use Salesfloor\API\Managers\Reps;
use Salesfloor\Services\Mail\Exceptions\InvalidEmailInputException;
use Salesfloor\Services\Mail\Exceptions\InvalidEmailTemplateContextException;
use Silex\Application;

class NotificationsEmailTemplate extends AbstractInternalTemplate
{
    /** @var Reps */
    protected $repsManager;

    /** @var string  */
    protected $templateName = 'notifications';

    /** @var array  */
    protected $variables = ['global'];

    /**
     * Load dependencies
     *
     * @param Application $app
     */
    protected function loadDependencies($app)
    {
        $this->repsManager = $app['reps.manager'];
    }

    /**
     * @param array $context
     *
     * @throws InvalidEmailTemplateContextException
     * @throws InvalidEmailInputException
     */
    public function setCustomEmailContext($context = [])
    {
        list($userId) = $this->extractParametersFromContext($context);

        $user = $this->repsManager->getOne(['ID' => $userId]);

        $this->email->setFromEmail($this->configs['retailer.emails.no_reply_address']);
        $this->email->setSubject($this->translator->trans('Reminder: Customer requests'));

        $this->email->addToEmails($user->user_email);
    }

    /**
     * @param array $context
     *
     * @throws InvalidEmailTemplateContextException
     */
    public function setCustomEmailTemplateContext($context = [])
    {
        list($userId) = $this->extractParametersFromContext($context);

        $user = $this->repsManager->getOne(['ID' => $userId]);

        $message = $this->getMessage();

        $this->emailTemplate->addDynamicVariables([
            'NOTIFICATIONS' => $message,
            'USER' => html_entity_decode($user->display_name),
            'USER_FIRST_NAME' => $user->first_name,
            'USER_LAST_NAME' => $user->last_name,
        ]);
    }

    /**
     * @param array $context
     *
     * @return array
     * @throws InvalidEmailTemplateContextException
     */
    public function extractParametersFromContext($context = [])
    {
        if (empty($context['userId'])) {
            throw new InvalidEmailTemplateContextException('Missing userId param');
        }

        return [$context['userId']];
    }

    /**
     * Get the message content
     *
     * @return string
     */
    private function getMessage()
    {
        $message = "<ul>";
        $message .= "<li>" . $this->translator->trans('This is a reminder that there are new customer requests that have not been answered yet.') .
                    "<br>" . $this->translator->trans('Please take a moment to respond to the customer(s).') . "</li>";
        $message .= "<a href=\"{HOMEURL}" . "/backoffice/store-request-center#/storeRequests/unresolved" . "\" class=\"style_finder_button\">" . $this->translator->trans('VIEW REQUESTS') . "</a>";
        $message .= "<div style=\"clear:both\"></div>"; // For the button style we need to reset the flow
        $message .= "</ul>";

        return $message;
    }
}
