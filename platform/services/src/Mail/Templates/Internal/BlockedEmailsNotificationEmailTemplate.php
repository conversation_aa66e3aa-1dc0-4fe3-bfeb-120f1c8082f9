<?php

declare(strict_types=1);

namespace Salesfloor\Services\Mail\Templates\Internal;

use Salesfloor\API\Managers\Reps;
use Salesfloor\Services\Mail\Exceptions\InvalidEmailTemplateContextException;

class BlockedEmailsNotificationEmailTemplate extends AbstractInternalTemplate
{
    protected $templateName = 'blocked_emails_notification';
    protected $variables = ['global'];

    /** @var Reps $repsManager */
    private $repsManager;

    protected function loadDependencies($app)
    {
        $this->repsManager = $app['reps.manager'];
    }

    public function setCustomEmailContext($context = [])
    {
        list($userId) = $this->extractParametersFromContext($context);

        $user = $this->repsManager->getOne(['ID' => $userId], null, false);

        $this->email->setFromEmail($this->configs['retailer.emails.no_reply_address']);
        $this->email->setFromName($this->configs['retailer.emails.no_reply_name']);
        $this->email->setSubject($this->translator->trans('email_subject_blocked_contact_notification'));

        $this->email->addToEmails($user->user_email);
    }

    public function setCustomEmailTemplateContext($context = [])
    {
        list(, $invalidEmails) = $this->extractParametersFromContext($context);

        $headers = [
            $this->translator->trans('email_blocked_contact_column_firstname'),
            $this->translator->trans('email_blocked_contact_column_lastname'),
            $this->translator->trans('email_blocked_contact_column_email'),
        ];

        $this->emailTemplate->addDynamicVariables([
            'BO_CONTACTS_LINK' => $this->configs['salesfloor_storefront.host'] . '/backoffice/contacts',
            'RETAILER_ID'      => $this->configs['mobile.retailer-id'],
            // /!\ These two variables need to have the name formatted this way (legacy) because the template won't
            // accept a different format without rewriting the sub components
            'tableHeaders'     => $headers,
            'tableData'        => $invalidEmails,
        ]);
    }

    public function extractParametersFromContext($context = [])
    {
        if (empty($context['userId'])) {
            throw new InvalidEmailTemplateContextException('Missing userId param');
        }

        if (empty($context['invalidEmails'])) {
            throw new InvalidEmailTemplateContextException('Missing invalidEmails param');
        }

        return [$context['userId'], $context['invalidEmails']];
    }
}
