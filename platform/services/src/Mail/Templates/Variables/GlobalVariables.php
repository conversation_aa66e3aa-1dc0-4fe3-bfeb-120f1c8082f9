<?php

namespace Salesfloor\Services\Mail\Templates\Variables;

use Carbon\Carbon;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\Storefront;
use Salesfloor\Services\Salesfloor\Reps as RepsService;

/**
 * Loaded in \Salesfloor\Services\Mail\Templates\AbstractBase if this variable set is required
 */
class GlobalVariables implements VariablesInterface
{
    // TODO - Add DOC here

    /**
     * @var $services
     */
    private $services;

    /**
     * @var $configs
     */
    private $configs;

    /**
     * @var $deepLinks
     */
    private $deepLinks;

    /**
     * @var RepsService $repsService
     */
    private $repsService;

    /**
     * @var $repsManager
     */
    private $repsManager;

    /**
     * @var $storesManager
     */
    private $storesManager;

    /**
     * @var $translator
     */
    private $translator;

    /**
     * @var Storefront
     */
    private $storefront;

    /** @var Multilang */
    private $multilang;

    public function __construct($configs, $translator, $services, $deepLinks, $repsService, $repsManager, $storesManager, $storefront, $multilang)
    {
        $this->services      = $services;
        $this->configs       = $configs;
        $this->deepLinks     = $deepLinks;
        $this->repsService   = $repsService;
        $this->repsManager   = $repsManager;
        $this->storesManager = $storesManager;
        $this->translator    = $translator;
        $this->storefront    = $storefront;
        $this->multilang     = $multilang;
    }

    public function getVariables($context = [])
    {
        if (!empty($context['userId'])) {
            $currentUser = $this->repsManager->getOneOrNull(['ID' => $context['userId']]);
        } else {
            $currentUser = $this->repsService->getLoggedInUser();
        }

        $store     = $currentUser?->store ? $this->storesManager->getOneOrNull(['store_id' => $currentUser->store]) : null;
        $userLogin = $currentUser->user_login;
        $baseUrlWithLocale = $this->storefront->getBaseUrlWithLocale();
        $variables = [
            'ACTION'                  => '',
            'APPOINTMENT_LABEL'       => $this->services->getRequestLabel('book_appointment', false),
            'APPOINTMENT_LINK'        => $baseUrlWithLocale . "/$userLogin?appointment=1",
            'ASK_LINK'                => $baseUrlWithLocale . "/$userLogin?ask=1",
            'AVATAR'                  => $this->repsService->getAvatar($currentUser ? $currentUser->getId() : null),
            'BACKOFFICEPAGE'          => $this->deepLinks->generate('BACKOFFICEPAGE'),
            'CHAT_LABEL'              => $this->services->getChatLabel(),
            'CONTACT_US_EMAIL'        => $this->configs['retailer.email.contact_us'],
            'CURRENT_YEAR'            => Carbon::now()->year,
            'CUSTOMER_NAME'           => "a customer",
            'CUSTOMER_PHONE'          => '',
            'DESCRIPTION'             => 'MISSING DESCRIPTION',
            'EMAIL'                   => $currentUser->user_email,
            'EMAIL_LINK'              => [
                'string'       => $this->deepLinks->generate('EMAIL_LINK'),
                'dependencies' => ['CUSTOMER_EMAIL_ENC', 'CUSTOMER_EMAIL_ENCRYPTED']
            ],
            'FINDER_LABEL'            => $this->services->getRequestLabel('personal_shopper', false),
            'HAPPY_CUSTOMER_LINK'     => [
                'format'       => $baseUrlWithLocale . "/$userLogin?feedback=1&magicid=%s&email=%s",
                'placeholders' => ['EVENT_MAGIC_ID', 'CUSTOMER_EMAIL_ENC', 'CUSTOMER_EMAIL_ENCRYPTED']
            ],
            'HOMEURL'                 => $baseUrlWithLocale,
            'HOST'                    => 'www.salesfloor.net',
            'LIVE_SERVICE_LINK'       => $baseUrlWithLocale . "/$userLogin?live_service=1",
            'MAILCHIMP_ADDRESS_FIELD' => '[CLIENTS.ADDRESS]',
            'NOTIFICATIONS_URL'       => $this->deepLinks->generate('NOTIFICATIONS_URL'),
            'PHONE'                   => $this->getRepPhone(),
            'POSTCARD'                => [
                'format'       => $this->storefront->getBaseUrl() . "/r/sf-postcard.php?repname=$userLogin&sku=%s",
                'placeholders' => ['PRODUCT_SKU']
            ],
            'POWEREDBY'               => $this->storefront->getBaseUrl() . "/img/salesfloor_poweredby.png",
            'PREVIEWTEXT'             => '',
            'QUESTION_LABEL'          => $this->services->getRequestLabel('contact_me', false),
            'REPORT_CONCERN_LINK'     => $baseUrlWithLocale . "/$userLogin?report=1",
            'REPPAGE'                 => $this->repsService->getStoreFrontUrl($currentUser ? $currentUser->getId() : null),
            'REPTITLE'                => $this->repsService->getRepTitle($currentUser),
            'RETAILER'                => $this->translator->trans("retailer.name"),
            'RETAILERLOGO'            => $this->storefront->getBaseUrl() . "/img/retailers/" . $this->configs['retailer.name'] . "/" . $this->configs['retailer.name'] . ".png",
            'RETAILERLOGOSMALL'       => $this->storefront->getBaseUrl() . "/img/retailers/" . $this->configs['retailer.name'] . "/" . $this->configs['retailer.name'] . "_p.png",
            'RETAILER_BRAND_NAME'     => $this->translator->trans("retailer.name"),
            'RETAILER_IDSTR'          => $this->configs['retailer.idstr'],
            'RETAILER_PRETTY_NAME'    => $this->multilang->getLocalizedRetailerName(),
            'RETAILERURL'             => $this->storefront->getBaseUrl() . "/shop?rep={$currentUser->user_login}&sf_url=" . urlencode($this->configs['retailer.url']),
            'SHOPPAGE'                => $this->storefront->getBaseUrl() . "/shop?rep=$userLogin&sf_url=",
            'SHOPPER_LINK'            => $baseUrlWithLocale . "/$userLogin?shopper=1",
            'STORE'                   => $this->getStoreLocation($store),
            'STORE_ADDRESS'           => $this->getStoreAddress($store),
            'STORE_ADDRESS_LINE_ONE'    => $store ? $store->address : "",
            'STORE_CITY'              => $store ? $store->city    : "",
            'STORE_REGION'            => $store ? $store->region  : "",
            'STORE_COUNTRY'           => $store ? $store->country : "",
            'STORE_POSTAL'            => $store ? $store->postal  : "",
            'STORE_PHONE'             => $store ? $store->phone   : "",
            'STORE_LOCATION_STREET'   => empty($store->street) ? '' : $store->street,
            'STORE_LOCATION_CITY'     => empty($store->city) ? '' : $store->city,
            'STORE_LOCATION_REGION'   => empty($store->region) ? '' : $store->region,
            'STORE_NAME'              => $store->name,
            // In old sf-template.php this variable is define twice, so it was a bit confusing. The latest one is the good one afaik
            // Sendgrid is appending to the email template if placeholder is missing.
            'UNSUBSCRIBE_LINK'        => "[GLOBAL_UNSUBSCRIBE]",
            'URL'                     => $this->repsService->getStoreFrontUrl($currentUser ? $currentUser->getId() : null),
            'USER'                    => $this->repsService->getDisplayName($currentUser ? $currentUser->getId() : null),
            'USERLOGIN'               => $currentUser->user_login,
            'USER_FIRST_NAME'         => html_entity_decode($currentUser->first_name),
            // Global variable doesn't have access to mail client (sf.mail.client), I will use
            // the ML generic function instead here.
            'UNSUBSCRIBE_EXTERNAL_LINK' => [
                'string'       => $this->multilang->getConfigValue('retailer.unsubscribe_link', $context['locale'] ?? null),
                'dependencies' => ['CUSTOMER_EMAIL_ENC', 'CUSTOMER_EMAIL_ENCRYPTED']
            ],
            // Virtual appointment
            'JOIN_MEETING_LINK' =>
                $this->storefront->getJoinMeetingUrl($context['virtualId'] ?? '', $context['locale'] ?? ''),
        ];

        return $variables;
    }

    private function getRepPhone()
    {
        $currentUser = $this->repsService->getLoggedInUser(['phone', 'publish_phone']);

        if (isset($currentUser->publish_phone) && isset($currentUser->phone) && $currentUser->publish_phone == 1) {
            return $currentUser->phone;
        }

        return '';
    }

    private function getStoreLocation($store)
    {
        $location = '';

        if (!empty($store->city)) {
            $location .= $store->city . ', ';
        }

        $location .= $store->region;

        return $location;
    }

    private function getStoreAddress($store)
    {
        return 'Store: ' . $store->name . ', ' . $store->address . ', ' .  $store->city  . ' ' .  $store->region . ' ' .  $store->postal;
    }
}
