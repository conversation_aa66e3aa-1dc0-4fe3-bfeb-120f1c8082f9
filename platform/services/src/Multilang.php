<?php

namespace Salesfloor\Services;

use DateTime;
use IntlDateFormatter;
use Salesfloor\Exceptions\GlobalException;
use Salesfloor\Services\Cache\GenericCache;
use Silex\Application;
use Symfony\Component\Translation\Translator;
use Symfony\Component\Translation\Loader\YamlFileLoader;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\DBAL\Query\QueryBuilder;
use Psr\Log\LoggerInterface;
use Salesfloor\API\Managers\Stores;

/**
 * Provides general Multilang functionality.
 *
 * Translation is handled by the silex translator, but this service contains the code to initialize it.
 */
class Multilang
{
    const LOCALE_ALL = 'ALL';
    const CACHING_TRANSLATION_PATH = 'cache_translation_filename';

    /** @var string $keySfLocale Query parameter key used to retrieve a locale */
    public $keySfLocale = 'sf_locale';

    /** @var string $keySourceUrl Query parameter key used to retrieve a locale */
    public $keySourceUrl = 'source_url';

    /** @var \Salesfloor\Configs\Configs $configs */
    private $configs;

    /** @var \Monolog\Logger $logger */
    private $logger;

    /** @var Translator */
    private $translator;

    /** @var string $locale */
    private $locale;

    /** @var string $fallbackLocale */
    private $fallbackLocale;

    /** @var GenericCache $cache */
    private $cache;

    /** @var  MySQLRepository $repositories */
    protected $repositories;

    /**
     * Multilang constructor.
     * @param $configs
     * @param LoggerInterface         $logger
     * @param Translator $translator
     * @param MySQLRepository $repositories
     */
    public function __construct(
        $configs,
        LoggerInterface $logger,
        $translator, // TODO
        MySQLRepository $repositories,
        GenericCache $cache
    ) {
        $this->configs      = $configs;
        $this->logger       = $logger;
        $this->translator   = $translator;
        $this->repositories = $repositories;
        $this->cache        = $cache;

        $this->fallbackLocale = $this->configs['retailer.i18n.default_locale'];
        $this->locale         = $this->fallbackLocale;
    }

    public function getLocale()
    {
        return $this->locale;
    }

    /**
     * This method is used to force the locale
     * For unit tests for instance
     * @param string $locale locale
     * @return mixed
     */
    public function setLocale($locale)
    {
        // This could be done in a custom listener and set onKernelRequest.
        // However, symfony doc also suggest to set locale directly on the translator service.
        $this->translator->setLocale($locale);

        return $this->locale = $locale;
    }

    public function getCurrentRequestLocale()
    {
        return $this->getLocale();
    }

    /**
     * Set in app['locale'] the proper locale or the default one
     */
    public function initLocale(Application $app)
    {
        $locale = $this->detectLocale(null, true);

        if (empty($locale)) {
            // If we fail to detect the locale properly, use the default.
            $locale = $this->configs['retailer.i18n.default_locale'];
        }

        $app['locale'] = $locale;

        // Update service, in case we don't use $app['locale'] but the service itself
        $this->setLocale($locale);
    }

    /**
     * Initialize the Translator service with the default files.
     */
    public function initDefaultTranslator()
    {
        $this->translator->addLoader('yaml', new YamlFileLoader());

        $pathsPerLanguage = $this->cache->wrap(self::CACHING_TRANSLATION_PATH, function () {
            return $this->getTranslationPaths();
        });

        // Twig date extension have a specific domain for loading date/time
        $getDomain = function ($path) {
            $filename = basename($path);
            switch ($filename) {
                case 'strings_time.yml':
                    return 'date';
                default:
                    return null;
            }
        };

        foreach ($pathsPerLanguage as $language => $paths) {
            foreach ($paths as $path) {
                $this->translator->addResource('yaml', $path, $language, $getDomain($path));
            }
        }
    }

    public function getLocalizedRetailerName($locale = null)
    {
        if ($locale === null) {
            $locale = $this->getLocale();
        }

        return $this->translator->trans('retailer.name', [], null, $locale);
    }

    /**
     * Check if the given locale is valid for the current retailer.
     * @param $locale
     * @return boolean
     */
    public function isValidRetailerLocale($locale)
    {
        return in_array($locale, $this->configs['sf.i18n.locales']);
    }

    public function getRetailerLocales()
    {
        return $this->configs['sf.i18n.locales'];
    }

    /**
     * Determine if i18n is active on this retailer.
     *
     * @deprecated This is not recommended.
     * We want to move towards all retailers having locales set, even if it's just one.
     * @return bool
     */
    public function isI18N()
    {
        return $this->configs['retailer.i18n.is_enabled'];
    }

    /**
     * Get the retailer base fallback default locale
     * @return string
     */
    public function getRetailerDefaultLocale()
    {
        return $this->configs['retailer.i18n.default_locale'];
    }

    /**
     * Check if a local is the default locale for the retailer
     *
     * @param string $locale
     *
     * @return string
     */
    public function isRetailerDefaultLocale($locale)
    {
        return $locale === $this->configs['retailer.i18n.default_locale'];
    }

    /**
     * Checks if the locale is valid for the retailer.
     * If it isn't, returns the retailer default locale.
     *
     * Using this method guarantees you will receive a locale valid for the retailer.
     *
     * @param $requestedLocale string
     * @return string
     */
    public function sanitizeLocale($requestedLocale)
    {
        $locale = $this->getRetailerDefaultLocale();

        if ($this->isI18N()) {
            if ($this->isValidRetailerLocale($requestedLocale)) {
                $locale = $requestedLocale;
            }
        }

        return $locale;
    }

    /**
     * Checks if the locale is valid for the store.
     * If it isn't, returns the retailer store locale.
     *
     * Using this method guarantees you will receive a locale valid for the store.
     *
     * @param string $requestLocale
     * @param int $storeId
     * @return string
     */
    public function sanitizeLocaleByStore($requestLocale, $storeId)
    {
        // No multilang? Return default for retailer.
        if (!$this->isI18N()) {
            return $this->getRetailerDefaultLocale();
        }

        $locales = $this->getActiveLocales($storeId);
        // If requested is valid? return it
        if (isset($locales[$requestLocale])) {
            return $locales[$requestLocale]['locale'];
        }

        // Find the default for the store
        foreach ($locales as $locale) {
            if ($locale['is_default']) {
                return $locale['locale'];
            }
        }

        // Final fallback - use the retailer lang.
        // Very unlikely, and probably indicates a misconfiguration.
        return $this->getRetailerDefaultLocale();
    }

    /**
     * Get the default locale for a given store.
     *
     * @param int $storeId
     * @return string
     */
    public function getStoreDefaultLocale($storeId)
    {
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return null;
        }

        // If for any reason the storeId is empty, don't process the query otherwise (filters are not processed if empty)
        if (empty($storeId)) {
            return null;
        }

        $locale = null;

        $qb = $this->repositories->getQueryBuilder();
        $qb->select(['locale', 'is_default', 'store_id'])
            ->from('sf_store_locale')
            ->where('store_id = :storeId')
            ->andWhere('is_default = 1')
            ->setParameter('storeId', $storeId);
        $results = $qb->execute()->fetchAll();

        if (!empty($results[0]['locale'])) {
            $locale = $results[0]['locale'];
        }

        return $locale;
    }

    /**
     * Get a localized date string.
     *
     * This doesn't support custom format. See how painful it is to support that in blog link below
     * For example:
     *  - day/month but not the year
     *  - hour:minute timezone but not the second
     *
     * @param DateTime  $date       Date you want to format
     * @param string    $locale     Locale you want to use for format (e.g: en_US)
     * @param int       $dateType   Date format (e.g.: -1 = none, 0 = FULL, 3 = short)
     * @param int       $timeType   Time format (same as dateType)
     *
     * @return string
     * @link https://blog.ksimka.com/a-long-journey-to-formatting-a-date-without-a-year-internationally-with-php/
     * @link https://bugs.php.net/bug.php?id=70377
     */
    public function getLocalizedDate(DateTime $date, $locale, $dateType = IntlDateFormatter::FULL, $timeType = IntlDateFormatter::LONG)
    {
        $intlDate = new IntlDateFormatter(
            $locale,
            $dateType,
            $timeType,
            $date->getTimezone()
        );

        $pattern= $intlDate->getPattern();

        return $intlDate->format($date);
    }

    /**
     * Sanitize  variables for translation by adding character % around keys
     * @param array  $variables  Array of variables
     *
     * @return array
     */
    public function sanitizeTranslationVariables(array $variables)
    {
        $sanitizedVariables = [];
        $transChar = '%';

        array_walk($variables, function ($value, $key) use (&$sanitizedVariables, $transChar) {
            $pattern = "/^$transChar.*$transChar$/";
            if (!preg_match($pattern, $key)) {
                $key = $transChar . $key . $transChar;
            }

            $sanitizedVariables[$key] = $value;
        });

        return $sanitizedVariables;
    }

    /**
     * Sanitize the amount for translation
     * @param  integer $amount Amount
     * @return integer         Sanitized amount
     */
    public function sanitizeAmount($amount)
    {
        // interpret an empty/false/negative $amount as a none case match
        if (!$amount || $amount < 0) {
            $amount = 0;
        }

        // interpret an invalid $amount as a single case
        if (!is_numeric($amount)) {
            $amount = 1;
        }

        return $amount;
    }

    /**
     * Get list of available locales for a retailer and corresponding urls
     *
     * @param string $urlPattern eg: http://stores.saks.com/{locale}/rep
     * @return void|array
     */
    public function getRetailerAvailableLocaleUrls($urlPattern, $replacement = '{locale}')
    {
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return [];
        }

        $availableLocales = [];

        foreach ($this->configs['sf.i18n.locales'] as $locale) {
            $availableLocales[] = [
                'code' => $locale,
                'name' => $this->getLocaleDisplayName($locale, $locale),
                'url'  => $this->buildLanguageSwitchItemUrl($urlPattern, $locale, $replacement),
            ];
        }

        return $availableLocales;
    }

    public function buildLanguageSwitchItemUrl($urlPattern, $locale, $replacement = '{locale}')
    {
        return preg_replace("/$replacement/", $locale, "$urlPattern");
    }

    /**
     * Get the locale under a specific xx_XX format
     *
     * @param $locale
     *
     * @return string|null
     */
    public function normalizeLocale($locale)
    {
        // look if it's a good format
        if (!preg_match('/[a-z]{2}[_|-][a-z]{2}/i', $locale)) {
            return null;
        }

        $locale = str_replace('-', '_', $locale);

        $splittedLocale = explode('_', $locale);

        return implode('_', [
           strtolower($splittedLocale[0]),
           strtoupper($splittedLocale[1])
        ]);
    }

    // ---------- BOF function from Lang service ---------- //

    /**
     * Function attempts to determine the Locale in order of precedence:
     *  - Locale of given $user
     *  - Locale of given $user->store
     *  - Locale of given $storeId (may be different from $user->store)
     *  - Locale of default config retailer.i18n.default_locale
     *
     * May be a useful concept to bring to services
     * @param int|null $userId
     * @param int|null $storeId
     * @return string  Locale
     */
    public function getLocaleFromUserOrStore($userId, $storeId = null)
    {
        /** @var QueryBuilder $qb */
        $qb = $this->repositories->getQueryBuilder();
        $qb->select('COALESCE (u.locale, sl.locale) as locale')
            ->from('wp_users', 'u')
            ->leftJoin('u', 'sf_store_locale', 'sl', 'u.store = sl.store_id AND sl.is_default = 1')
            ->where('u.ID = :userId')
            ->setParameter('userId', $userId);

        $results = $qb->execute($qb->getSql())->fetch();

        if (empty($results['locale']) && !empty($storeId)) {
            $qb = $this->repositories->getQueryBuilder();
            $qb->select('sl.locale')
                ->from('sf_store_locale', 'sl')
                ->where('sl.store_id = :storeId')
                ->andWhere($qb->expr()->eq('sl.is_default', 1))
                ->setParameter('storeId', $storeId);
            $results = $qb->execute($qb->getSql())->fetch();
        }

        $locale = isset($results['locale']) ? $results['locale'] : null;

        return !empty($locale) ? $locale : $this->configs['retailer.i18n.default_locale'];
    }

    /**
     * This is used by both WP and widget stack, to detect the locale. On the widget stack,
     * we do it in 2 step because we don't want to call the api when it's not necessary
     *
     * @param null $localeStore
     * @param bool $onlyRequest
     *
     * @param Request|null $request
     * @return bool|null|string
     */
    public function detectLocale($localeStore = null, $onlyRequest = false, Request $request = null)
    {
        // Do not try to detect locale, when the i18n module is not enabled
        // fallback locale is the default locale for the retailer. At the moment, it's en_US for everyone
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return $this->fallbackLocale;
        }

        if (($requestLocale = $this->retrieveLocaleFromRequest($request)) || $onlyRequest) {
            return $requestLocale;
        } elseif ($storeLocale = $this->sanitizeAndValidateLocale($localeStore)) {
            return $storeLocale;
        } elseif ($this->configs['retailer.multi_domains.is_enabled'] == true && !empty($this->configs['retailer.multi_domains.locale'])) {
            return $this->configs['retailer.multi_domains.locale'];
        } elseif ($serverLocale = $this->retrieveLocaleFromServer()) {
            return $serverLocale;
        } else {
            return $this->fallbackLocale;
        }
    }


    /**
     * Returns the locale display name in a given language.
     *
     * @param string $targetLangOrLocale Preferred ISO8859 format, e.g.: `fr_CA`.
     *                                   It accepts different formats such as `fr-ca`, `fr-CA`, `fr_ca`.
     *                                   It accepts ISO639-1 language codes, e.g.: `fr`, `en`
     *
     * @param string $inLangOrLocale     Preferred ISO639-1 language codes, e.g.: `fr`, `en`
     *                                   It accepts ISO8859 format, e.g.: `fr_CA`.
     *                                   It accepts different formats such as `fr-ca`, `fr-CA`, `fr_ca`.
     * @return string
     */
    public function getLocaleDisplayName($targetLangOrLocale, $inLangOrLocale)
    {
        $targetLocale = $this->sanitizeAndValidateLocale($targetLangOrLocale);
        $inLang       = $this->getLanguageFromLocale($inLangOrLocale);

        // invalid targetLocale
        if (empty($targetLocale)) {
            $this->logger->error("Lang error. Invalid locale when retrieving display name for targetLangOrLocale={$targetLangOrLocale} inLangOrLocale={$inLangOrLocale}");
            return $targetLangOrLocale;
        }

        // non-registered locale display name
        if (empty($this->configs['retailer.i18n.locale_display_name']["{$targetLocale}_{$inLang}"])) {
            $this->logger->error("Lang error. Non registered locale's display name for targetLangOrLocale={$targetLangOrLocale} inLangOrLocale={$inLangOrLocale}");
            return $targetLangOrLocale;
        }

        return $this->configs['retailer.i18n.locale_display_name']["{$targetLocale}_{$inLang}"];
    }

    /**
     * Sanitize and validate a given locale. To be considered a valid locale is has to be
     * properly configured into the retailer's configs.
     *
     * @param string $locale
     *
     * @return string|null ISO8859 format, e.g.: `fr_CA`
     */
    public function sanitizeAndValidateLocale($locale)
    {
        if (!$locale) {
            return null;
        }

        if (strlen($locale) === 2) {
            $locale = $this->getLocaleFromLanguage($locale);
        }

        $locale = filter_var($locale, FILTER_UNSAFE_RAW);
        $locale = trim($locale);
        $locale = str_replace('-', '_', $locale);
        $locale = preg_replace_callback('/_.{2}$/', function ($match) {
            // en_us >> en_US
            return strtoupper($match[0]);
        }, $locale);

        // try a perfect match
        if (in_array($locale, $this->configs['sf.i18n.locales'])) {
            return $locale;
        }

        // Special call to support i18n
        if (strtoupper($locale) == self::LOCALE_ALL) {
            return self::LOCALE_ALL;
        }

        // try a match using locale's language code
        // we might don't have the correct locale but we can show
        // the right language at least
        $locale = $this->resolveLanguageMatch($locale);
        if (in_array($locale, $this->configs['sf.i18n.locales'])) {
            return $locale;
        }

        return null;
    }

    /**
     * Return a language fallback locale from a given language code. The language fallback locale
     * has to be properly configured into the retailer's configs.
     *
     * @param string $lang ISO639 format, e.g.: `fr`, `en`
     *
     * @return null|string
     */
    public function getLocaleFromLanguage($lang)
    {
        $lang = filter_var($lang);
        $lang = trim($lang);
        $lang = strtolower($lang);

        return isset($this->configs['retailer.i18n.default_language_fallback'][$lang])
            ? $this->configs['retailer.i18n.default_language_fallback'][$lang]
            : null;
    }

    public function isLocaleAll($locale = null)
    {
        // TODO : Do we want to return all local (new structure) even when i18n is not enabled ?!?
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return false;
        }

        return !empty($locale) ? $locale == self::LOCALE_ALL : $this->locale == self::LOCALE_ALL;
    }

    /**
     * Try to retrieve the locale value from:
     * 1. Query parameter `sf_locale`
     * 2. Query parameter `sf_locale` inside query parameter `source_url`
     *
     * @param Request|null $request
     * @return null|string
     */
    public function retrieveLocaleFromRequest(Request $request = null)
    {
        // The Symfony request should be the primary source of truth.
        // Especially since in some contexts (like tests or subrequests), $_REQUEST is not reliable and may be empty.
        if ($request !== null) {
            $locale = $this->sanitizeAndValidateLocale($request->get($this->keySfLocale));
            if ($locale) {
                return $locale;
            }
        }

        // from `locale` query parameter
        if (isset($_REQUEST[$this->keySfLocale])) {
            $locale = isset($_REQUEST[$this->keySfLocale])
                ? $this->sanitizeAndValidateLocale($_REQUEST[$this->keySfLocale])
                : null;

            if ($locale) {
                return $locale;
            }
        }

        // from `source_url` query parameter
        if (isset($_REQUEST[$this->keySourceUrl])) {
            $parsedUrl   = parse_url($_REQUEST[$this->keySourceUrl]);
            $queryString = isset($parsedUrl['query']) ? $parsedUrl['query'] : '';
            $pathString = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
            $locale = null;

            parse_str($queryString, $query);

            if (isset($query[$this->keySfLocale])) { // From sf_locale parameter
                $retrievedLocale = $query[$this->keySfLocale];
            } else { // From locale in route
                $retrievedLocale = $this->getLocaleFromRoute($pathString);
            }

            $locale = $this->sanitizeAndValidateLocale($retrievedLocale);

            if ($locale) {
                return $locale;
            }
        }

        return null;
    }

    /**
     * Return a locale based on the route given (url rewrite)
     *
     * @param string $route ISO639 format, e.g.: `fr`, `en`
     *
     * @return null|string
     */
    protected function getLocaleFromRoute($route)
    {
        if ($this->configs['sf.i18n.locales']) {
            foreach ($this->configs['sf.i18n.locales'] as $value) {
                if (stripos($route, $value) !== false) {
                    return $value;
                }
            }
        }

        return null;
    }

    /**
     * Try to retrieve the locale value from $_SERVER
     * 1. Check `HTTP_REFERER`
     * 2. Check `HTTP_ACCEPT_LANGUAGE`.
     *
     * @return null|string
     */
    protected function retrieveLocaleFromServer()
    {
        if (isset($_SERVER['HTTP_REFERER'])) {
            $parsedUrl   = parse_url($_SERVER['HTTP_REFERER']);
            $queryString = isset($parsedUrl['query']) ? $parsedUrl['query'] : '';

            parse_str($queryString, $query);

            // from `source_url` query parameter
            if (isset($query[$this->keySourceUrl])) {
                $parsedUrl   = parse_url($query[$this->keySourceUrl]);
                $queryString = isset($parsedUrl['query']) ? $parsedUrl['query'] : '';
                $pathString = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
                $locale = null;

                parse_str($queryString, $query);

                if (isset($query[$this->keySfLocale])) { // From sf_locale parameter
                    $retrievedLocale = $query[$this->keySfLocale];
                } else { // From locale in route
                    $retrievedLocale = $this->getLocaleFromRoute($pathString);
                }

                $locale = $this->sanitizeAndValidateLocale($retrievedLocale);

                if ($locale) {
                    return $locale;
                }
            }
        }

        foreach (array_keys($this->parseHttpAcceptLanguage()) as $locale) {
            if ($locale = $this->sanitizeAndValidateLocale($locale)) {
                return $locale;
            }
        }

        return null;
    }

    /**
     * @param string $locale Preferred ISO8859 format, e.g.: `fr_CA`.
     *                       It accepts different formats such as `fr-ca`, `fr-CA`, `fr_ca`.
     *                       It accepts ISO639-1 language codes (`fr`, `en`) which will be converted
     *
     * @return string ISO639 language code, e.g.: `fr`, `en`
     */
    protected function getLanguageFromLocale($locale)
    {
        return strtolower(preg_replace('/.{3}$/', '', $locale));
    }

    /**
     * Parse the server variable `HTTP_ACCEPT_LANGUAGE`, which stores the current browser's
     * list of preferred locales/languages. They are sorted by an index.
     *
     * @return array List of preferred locales/languages.
     *               e.g: ['fr_CA' => 1, 'fr' => '0.8', 'en' => '0.6', 'en_CA' => 0.5]
     */
    protected function parseHttpAcceptLanguage()
    {
        $httpLanguages = isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) ? $_SERVER['HTTP_ACCEPT_LANGUAGE'] : null;
        $languages     = [];

        if (empty($httpLanguages)) {
            return $languages;
        }

        $accepted = preg_split('/,\s*/', $httpLanguages);

        foreach ($accepted as $accept) {
            $match = null;

            $result = preg_match('/^([a-z]{1,8}(?:[-_][a-z]{1,8})*)(?:;\s*q=(0(?:\.[0-9]{1,3})?|1(?:\.0{1,3})?))?$/i', $accept, $match);

            if ($result < 1) {
                continue;
            }

            if (isset($match[2]) === true) {
                $quality = (float)$match[2];
            } else {
                $quality = 1.0;
            }

            $countries = explode('-', $match[1]);
            $region    = array_shift($countries);
            $country2  = explode('_', $region);
            $region    = array_shift($country2);

            foreach ($countries as $country) {
                $languages[$region . '_' . strtoupper($country)] = $quality;
            }

            foreach ($country2 as $country) {
                $languages[$region . '_' . strtoupper($country)] = $quality;
            }

            if ((isset($languages[$region]) === false) || ($languages[$region] < $quality)) {
                $languages[$region] = $quality;
            }
        }

        return $languages;
    }

    protected function resolveLanguageMatch($locale)
    {
        $lang = $this->getLanguageFromLocale($locale);

        return $this->getLocaleFromLanguage($lang);
    }

    /**
     * Add the locale prefix to the email template if needed
     * @param string $path   Path of the email template
     * @param string $locale Locale
     *
     * @return string  Email template i18n path
     */
    public function addLocaleToEmailTemplate($path, $locale)
    {
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return $path;
        }

        $templateFileName = basename($path);
        $templateDirectories = dirname($path);

        $i18nPath = "$templateDirectories/$locale/$templateFileName";

        return $i18nPath;
    }

    /**
     * Get locale parameter for outbound email communication
     * This method allow to centralize the logic we want to define the correct locale
     *
     * Priorities:
     * 1) Forced locale (locale selectedd by the Rep)
     * 2) Event locale (locale from the original request of the customer)
     * 3) Locale from the store
     *
     * We should probably move this method to new Emailing service once it will be ready
     * The dependency $storesManager cannot be injected in constructor because managers have service.lang
     * dependency, so it's circular. That is why it's in the method
     *
     * @param  Stores  $storesManager  Stores manager
     * @param  integer $storeId        Store identifier
     * @param  string  $eventLocale    Event locale
     * @param  string  $locale         Forced locale
     * @return string                  Locale
     */
    public function getLocaleForOutboundEmails($storesManager, $storeId, $eventLocale = null, $locale = null)
    {
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return null;
        }

        // SF-19542: reps can choose/force the language for outbound communication
        if (!empty($locale)) {
            return $locale;
        }

        // Locale from the original request
        if (!empty($eventLocale)) {
            return $eventLocale;
        }

        // SF-19579: Detect the default locale of the store and use it as default locale for emails
        return $storesManager->getDefaultLocale($storeId);
    }

    // ---------- EOF function from Lang service ---------- //

    // ---------- BOF function from:  Manager/Store ---------- //
    /**
     * WESHOULD : Move this function into an intermediate layer, so manager/multilang could both use these function
     * Return list of active locales for a store
     * @param  integer $storeId Store id
     * @return array                 List of locales
     */
    public function getActiveLocales($storeId)
    {
        if (!$this->configs['retailer.i18n.is_enabled']) {
            return [];
        }

        $locales = [];

        $qb = $this->repositories->getQueryBuilder();
        $qb->select(['locale', 'is_default', 'store_id'])
            ->from('sf_store_locale')
            ->where('store_id = :storeId')
            ->setParameter('storeId', $storeId);
        $results = $qb->execute()->fetchAll();

        foreach ($results as $result) {
            $locales[$result['locale']] = [
                'locale'     => $result['locale'],
                'is_default' => $result['is_default'],
            ];
        }

        return $locales;
    }
    // ---------- EOF function from:  Manager/Store ---------- //

    /**
     * This can be used to extract some config value based on locale.
     * At the moment, it's used in the email stack to get the unsubscribe link per stack/locale
     *
     * @param       $configName The name of the config. e.g: "retailer.unsubscribe_link"
     * @param null  $locale     The value of the locale. e.g: "fr_CA"
     * @param false $strict     If true, will throw exception if locale match doesn't exist or config is empty.
     *                          For example, if it's part of the core. Not a feature.
     * @param bool  $fallback   if true, will use defaultLocale value (if possible)
     *
     * @return mixed|string|null
     * @throws GlobalException
     */
    public function getConfigValue($configName, $locale = null, $strict = false, $fallback = true): ?string
    {
        // This will throw InvalidArgumentException if it doesn't exist.
        $configValue = $this->configs[$configName];

        if ($strict && empty($configValue)) {
            throw new GlobalException(
                sprintf(
                    "getConfigValue(): This config [%s] is empty",
                    $configName
                )
            );
        }

        $defaultLocale = $this->configs['retailer.i18n.default_locale'];

        if (is_array($configValue) && !empty($configValue)) {
            if ($locale && $configValue[$locale]) {
                return $configValue[$locale];
            } elseif ($fallback && $configValue[$defaultLocale]) {
                return $configValue[$defaultLocale];
            } elseif ($strict) {
                throw new GlobalException(
                    sprintf(
                        "getConfigValue(): The config [%s] with locale [%s] doesn't exist.",
                        $configName,
                        $locale
                    )
                );
            } else {
                return null;
            }
        } elseif (is_string($configValue)) {
            return $configValue;
        } else {
            return null;
        }
    }

    public function getAsianLocales()
    {
        return ['ja_JP'];
    }

    public function isAsianLocale($locale)
    {
        if (empty($locale)) {
            $locale = $this->getRetailerDefaultLocale();
        }

        return in_array(strtolower($locale), array_map('strtolower', $this->getAsianLocales()));
    }

    /**
     * This is called during boot() to warm up the cache.
     * To make sure it's working on all stack (api/widgets/wp) we load all locales in cache even if not needed.
     * Performance doesn't seems to be affected since it's cached.
     *
     * @return array[]
     */
    public function prepareWarmUpCache(): array
    {
        return [
            self::CACHING_TRANSLATION_PATH => $this->getTranslationPaths()
        ];
    }

    /**
     * Find all paths of the yaml (translation file)
     *
     * @return array
     */
    private function getTranslationPaths(): array
    {
        $paths = [];

        // This is to load yml resources from retailer locale and the fallback locale
        $locales = array_unique(array_merge($this->configs['sf.i18n.locales'], [$this->configs['retailer.i18n.locale.fallback']]));

        // Since those paths are cached, we need to make sure that api/widgets/wp get all the one needed,
        // and since we don't know which one will be called first (api/widgets/wp),
        // we need to make sure that all locales are loaded in all scenarios.
        // In summary, we don't rely on the PATH_LOCALES constant anymore.

        // It's unclear why widgets need API locale (shared services ?!), but to limit the risk of a bug,
        // we will keep the logic.

        // Also, if it would affect performance, we could have different key per "source" (api/widgets/wp) but based
        // on my test it was minimal.
        $apiLocalePath = PATH_PLATFORM . '/api/app/locale';
        $widgetLocalePath = PATH_PLATFORM . '/widgets/app/locales';

        $retailer = $this->configs['retailers.current'];

        $getPath = function ($localePath, $language) use ($retailer) {
            //////////////////
            /// Generic (global) (api)
            ///
            $functionGeneric = function () use ($localePath, $language) {
                $path = join('/', [
                    $localePath,
                    'generic',
                    $language,
                    'strings_*.yml'
                ]);

                // Find all files in the given language path.
                return glob($path);
            };

            //////////////////
            /// Retailer specific per locale (saks/en_US.yml)
            /// Since this service is also used in widgets, let's make sure it handles "generic/en_US.yml"

            $functionRetailerLanguageSpecific = function () use ($localePath, $language, $retailer) {
                $paths = [];

                foreach (['generic', $retailer] as $current) {
                    $path = join('/', [
                        $localePath,
                        $current,
                        $language . '.yml',
                    ]);

                    if (file_exists($path)) {
                        $paths[] = $path;
                    }
                }

                return $paths;
            };

            //////////////////
            /// Area-specific strings per retailer (en_US.yml)
            ///

            $functionRetailerAreaSpecific = function () use ($localePath, $retailer, $language) {
                $path = join('/', [
                    $localePath,
                    $retailer,
                    $language,
                    'strings_*.yml',
                ]);

                return glob($path);
            };

            return array_merge(
                $functionGeneric(),
                $functionRetailerLanguageSpecific(),
                $functionRetailerAreaSpecific(),
            );
        };

        foreach ($locales as $language) {
            $paths[$language] = array_merge(
                $getPath($apiLocalePath, $language),
                $getPath($widgetLocalePath, $language),
            );
        }

        return $paths;
    }
}
