<?php

declare(strict_types=1);

namespace Salesfloor\Services;

use Salesfloor\Services\Queue\SidebarEventQueue;
use Silex\Application;
use Salesfloor\Services\EventQueue as EventQueueService;
use Salesfloor\Services\Queue\SidebarEventQueue as SidebarEventQueueService;
use Salesfloor\Models\SidebarEventLog as SidebarEventLogModel;

/**
 * This service class is used to aggregate sidebar stats from sf_elb_stats
 * and sf_sidebar_event_log(_yyyymmdd) into daily stats and save the aggregated stats in
 * table sf_sidebar_daily_stats table.
 *
 * Class SidebarStatsAggregator
 * @package Salesfloor\Services
 */
class SidebarStatsAggregator
{
    const SIDEBAR_DAILY_STATS_TABLE = 'sf_sidebar_daily_stats';

    public static $sidebarDailyStatsColumns = [
        'sidebar_mobile_total_view'              => true,
        'sidebar_mobile_total_click'             => true,
        'sidebar_mobile_total_minimize'          => true,
        'sidebar_mobile_total_maximize'          => true,
        'sidebar_mobile_total_tagline_minimize'  => true,
        'sidebar_desktop_total_view'             => true,
        'sidebar_desktop_total_click'            => true,
        'sidebar_desktop_total_minimize'         => true,
        'sidebar_desktop_total_maximize'         => true,
        'sidebar_mobile_unique_view'             => true,
        'sidebar_mobile_unique_click'            => true,
        'sidebar_mobile_unique_minimize'         => true,
        'sidebar_mobile_unique_maximize'         => true,
        'sidebar_mobile_unique_tagline_minimize' => true,
        'sidebar_desktop_unique_view'            => true,
        'sidebar_desktop_unique_click'           => true,
        'sidebar_desktop_unique_minimize'        => true,
        'sidebar_desktop_unique_maximize'        => true,

        'contextual_widget_desktop_view'         => true,
        'contextual_widget_desktop_unique_view'  => true,
        'contextual_widget_desktop_click'        => true,
        'contextual_widget_desktop_unique_click' => true,
        'contextual_widget_mobile_view'          => true,
        'contextual_widget_mobile_unique_view'   => true,
        'contextual_widget_mobile_click'         => true,
        'contextual_widget_mobile_unique_click'  => true,
    ];

    /** @var \Salesfloor\Services\MySQLRepository $mysql*/
    protected $repositories;

    /** @var ExtendedInsertQueryBuilder $insertBuilder */
    protected $insertBuilder;

    /** @var \Salesfloor\Configs\Configs $configs */
    protected $configs;

    public function __construct(Application $app)
    {
        $this->repositories     = $app['repositories.mysql'];
        $this->configs          = $app['configs'];
        $this->insertBuilder    = $app['service.insert_query_builder']();
    }

    /**
     * Aggregate ELB stats and new sidebar stats into sf_sidebar_daily_stats table
     *
     * @param string $date
     * @throws \Exception
     */
    public function aggregate($date): void
    {
        $results = $this->getSidebarAndContextualWidgetMetrics($date);

        if (!empty($results)) {
            $this->insertSidebarStatsToDailyStatsTable($results);
        }
    }

    /**
     * Insert daily sidebar stats into sf_sidebar_daily_stats table
     *
     * @param array $stats
     * @throws \Exception
     */
    public function insertSidebarStatsToDailyStatsTable($stats)
    {
        $insertBuilder = $this->insertBuilder;

        $insertFields = array_merge(['date'], array_keys(self::$sidebarDailyStatsColumns));

        $insertBuilder
            ->setTable(self::SIDEBAR_DAILY_STATS_TABLE)
            ->addInsertFields($insertFields)
        ;

        foreach ($stats as $stat) {
            $values = $insertBuilder->extract($insertFields, $stat);
            $insertBuilder->addValuesSet($values);
        }

        $insertBuilder->addOnDuplicateUpdateFields(self::$sidebarDailyStatsColumns);

        $insert = ($insertBuilder->prepare());

        $this->repositories->executeQuery($insert['query'], $insert['parameters']);
    }

    /**
     * Static function to check if sf_sidebar_daily_stats Table is empty
     * @param MySQLRepository $repo
     * @return bool
     */
    public static function sidebarDailyStatsTableIsEmpty(MySQLRepository $repo): bool
    {
        $queryBuilder = $repo->getQueryBuilder();

        $queryBuilder->select('COUNT(*) AS count')
            ->from(self::SIDEBAR_DAILY_STATS_TABLE, 's');

        $results = $repo->executeCustomQuery($queryBuilder);

        $count = isset($results[0]['count']) ? intval($results[0]['count']) : 0;

        if ($count === 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Get all the stats from sf_sidebar_daily_stats Table
     *
     * This function is used to in ReportingProcessorSidebarMetrics to fetch all the daily sidebar stats
     * and merge with all the old sidebar daily stats, then generate csv file.
     * @param MySQLRepository $repo
     * @param array $columns
     * @param null $from
     * @param null $to
     * @return array
     */
    public static function getAllSidebarDailyStats(MySQLRepository $repo, $columns = [], $from = null, $to = null): array
    {
        $queryBuilder = $repo->getQueryBuilder();

        $queryBuilder->select((empty($columns) ? 's.*' : $columns))
            ->from(self::SIDEBAR_DAILY_STATS_TABLE, 's')
            ->orderBy('s.date', 'DESC');

        if (!empty($from) && !empty($to)) {
            $queryBuilder->andWhere($queryBuilder->expr()->gte('s.date', $queryBuilder->expr()->literal($from)));
            $queryBuilder->andWhere($queryBuilder->expr()->lte('s.date', $queryBuilder->expr()->literal($to)));
        }

        $results = $repo->executeCustomQuery($queryBuilder);

        return $results;
    }

    /**
     * Get new sidebar metrics from sf_sidebar_event_log(_yyyymmdd) table and sf_elb_stats table
     *
     * @param string $date
     * @return array
     */
    public function getSidebarAndContextualWidgetMetrics($date)
    {
        $sidebarMobileView = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_MOBILE_VIEW, ['sidebar_mobile_total_view' => 'total', 'sidebar_mobile_unique_view' => 'uniqueCount'], $date);
        $sidebarMobileClick = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_MOBILE_CLICK, ['sidebar_mobile_total_click' => 'total', 'sidebar_mobile_unique_click' => 'uniqueCount'], $date);
        $sidebarMobileMinimize = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_MOBILE_MINIMIZE, ['sidebar_mobile_total_minimize' => 'total', 'sidebar_mobile_unique_minimize' => 'uniqueCount'], $date);
        $sidebarMobileMaximize = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_MOBILE_MAXIMIZE, ['sidebar_mobile_total_maximize' => 'total', 'sidebar_mobile_unique_maximize' => 'uniqueCount'], $date);
        $sidebarMobileTaglineMinimize = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_MOBILE_TAGLINE_MINIMIZE, ['sidebar_mobile_total_tagline_minimize' => 'total', 'sidebar_mobile_unique_tagline_minimize' => 'uniqueCount'], $date);

        $sidebarDesktopView = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_DESKTOP_VIEW, ['sidebar_desktop_total_view' => 'total', 'sidebar_desktop_unique_view' => 'uniqueCount'], $date);
        $sidebarDesktopClick = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_DESKTOP_CLICK, ['sidebar_desktop_total_click' => 'total', 'sidebar_desktop_unique_click' => 'uniqueCount'], $date);
        $sidebarDesktopMinimize = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_DESKTOP_MINIMIZE, ['sidebar_desktop_total_minimize' => 'total', 'sidebar_desktop_unique_minimize' => 'uniqueCount'], $date);
        $sidebarDesktopMaximize = self::getSidebarStatsByEvent($this->repositories, EventQueueService::SF_EVENT_NEW_SIDEBAR_DESKTOP_MAXIMIZE, ['sidebar_desktop_total_maximize' => 'total', 'sidebar_desktop_unique_maximize' => 'uniqueCount'], $date);

        $contextualDesktopView = self::getSidebarStatsByEvent(
            $this->repositories,
            SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
            ['contextual_widget_desktop_view' => 'total', 'contextual_widget_desktop_unique_view' => 'uniqueCount'],
            $date
        );

        $contextualDesktopClick = self::getSidebarStatsByEvent(
            $this->repositories,
            SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
            ['contextual_widget_desktop_click' => 'total', 'contextual_widget_desktop_unique_click' => 'uniqueCount'],
            $date
        );

        $contextualMobileView = self::getSidebarStatsByEvent(
            $this->repositories,
            SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
            ['contextual_widget_mobile_view' => 'total', 'contextual_widget_mobile_unique_view' => 'uniqueCount'],
            $date
        );

        $contextualMobileClick = self::getSidebarStatsByEvent(
            $this->repositories,
            SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
            ['contextual_widget_mobile_click' => 'total', 'contextual_widget_mobile_unique_click' => 'uniqueCount'],
            $date
        );

        $results = array_merge_recursive(
            $sidebarMobileView,
            $sidebarMobileClick,
            $sidebarMobileMinimize,
            $sidebarMobileMaximize,
            $sidebarMobileTaglineMinimize,
            $sidebarDesktopView,
            $sidebarDesktopClick,
            $sidebarDesktopMinimize,
            $sidebarDesktopMaximize,
            $contextualMobileView,
            $contextualMobileClick,
            $contextualDesktopView,
            $contextualDesktopClick
        );

        if (empty($results) && !empty($date)) {
            $results[$date] = ['date' => $date];
        }

        $results = self::backFillSidebarMatrix($results, array_keys(self::$sidebarDailyStatsColumns));

        return $results;
    }

    /**
     * Transform data into proper format
     *
     * @param array $results
     * @param bool $associateMapping if the mapping is associate array or array of values
     * @param array $arrayMappings
     * @return array $data
     */
    public static function transformData($results, $associateMapping, $arrayMappings): array
    {
        $data = [];

        foreach ($results as $result) {
            if ($associateMapping) {
                foreach ($arrayMappings as $key => $map) {
                    $data[$result['date']][$key] = $result[$map];
                }
            } else {
                $data[$result['date']]['date'] = $result['date'];
                foreach ($arrayMappings as $map) {
                    $data[$result['date']][$map] = $result[$map];
                }
            }
        }

        return $data;
    }

    /**
     * Get sidebar stats query builder based
     *
     * @param \Salesfloor\Services\MySQLRepository $db
     * @param string $event
     * @param array $arrayMappings
     * @param string $date
     * @return array
     */
    public static function getSidebarStatsByEvent($db, $event, $arrayMappings, $date): array
    {
        /** @var \Doctrine\DBAL\Query\QueryBuilder $queryBuilder */
        $queryBuilder = $db->getQueryBuilder();

        $queryBuilder->select(
            'DATE(se.created_at) AS date',
            'se.action',
            'COUNT(DISTINCT se.fingerprint) AS uniqueCount',
            'COUNT(*) AS total'
        );

        // Don't use cast DATE(X) in where in mysql for performance reason (index won't be used)
        $date = new \DateTime($date);
        $dateFrom = $date->format('Y-m-d 00:00:00');
        $dateTo = $date->format('Y-m-d 23:59:59');

        $table = SidebarEventLogModel::getEventTable($date);
        $queryBuilder->from($table, 'se');
        $queryBuilder->where('se.action = :event')
            ->andWhere('se.created_at BETWEEN :dateFrom AND :dateTo')
            // Afaik, this is not critical/needed to the query but keep it for safety.
            ->groupBy('DATE(se.created_at)')
            ->orderBy('date', 'DESC')
            ->setParameters(
                [
                    'event' => $event,
                    'dateFrom' => $dateFrom,
                    'dateTo' => $dateTo,
                ]
            );

        // Composite index is on action,action_id,created_at.
        // Now, some actions (e.g: clicks) will have an action_id and not null.
        // If action_id because too dynamic, we could also do where (x is null or x is not null) but bit slower.
        $queryBuilder
            ->andWhere(
                $queryBuilder->expr()->or(
                    $queryBuilder->expr()->isNull('se.action_id'),
                    $queryBuilder->expr()->in('se.action_id', array_values(SidebarEventQueue::SF_EVENT_CONTEXTUAL_WIDGET_SUB_ACTION)),
                )
            );

        $results = $db->executeCustomQuery($queryBuilder);

        return self::transformData($results, true, $arrayMappings);
    }

    /**
     * Back fill default 0 value for the missing matrix in new sidebar stats to
     * get the full set of stats
     *
     * @param $newSidebarStats
     * @param array $columns
     * @return array
     */
    public static function backFillSidebarMatrix($newSidebarStats, $columns): array
    {
        $data = [];

        foreach ($newSidebarStats as $key => $values) {
            $stat = [];
            $stat['date'] = $key;
            foreach ($columns as $column) {
                $stat[$column] = isset($values[$column]) ? $values[$column] : 0;
            }
            $data[] = $stat;
        }

        return $data;
    }
}
