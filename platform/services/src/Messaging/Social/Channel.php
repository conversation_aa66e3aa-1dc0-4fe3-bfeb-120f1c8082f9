<?php

namespace Salesfloor\Services\Messaging\Social;

use Salesfloor\Models\Services\ServiceInterface;
use Salesfloor\Services\Messaging\BaseChannel;

class Channel extends BaseChannel
{
    const CHANNEL_CONFIG = 'messaging.social';
    const CHANNEL_NAME = 'social';

    protected $allProvidersArray = [
        FakeProvider::PROVIDER_NAME => 'Salesfloor\Services\Messaging\Social\FakeProvider',
    ];
    /**
     * @var string
     */
    private $extra;

    public function loadDependencies($app)
    {
        // Load dependencies related to this Messaging Channel
        $this->extra = 'social';
    }

    public function sendMessage(
        $userId,
        array $to,
        $body = '',
        array $attachments = [],
        $sendingUserId = null,
        bool $isToMany = false,
        bool $isLastMessage = true,
        $addToDB = true,
        ServiceInterface $request = null
    ) {
        return true;
    }

    public function validateSendMessage($userId, array $to, $body = '', array $attachments = [], bool $skipBlockList = false)
    {
    }

    public function validateUserPermission($userId)
    {
    }

    public function enableUser($userId, $actionByUserId)
    {
    }

    public function disableUser($userId, $actionByUserId)
    {
    }

    public function sendNoReplyMessage(array $to, $body = '', $addToDB = false, ServiceInterface $request = null)
    {
        // TODO: Implement sendNoReplyMessage() method.
    }
}
