<?php

namespace Salesfloor\Services\Messaging\Text;

use Salesfloor\Models\Messaging\Text\Attachment;
use Salesfloor\Models\Store;
use Salesfloor\Services\Messaging\Exceptions\CustomerOptedOutException;
use Twilio\Jwt\AccessToken;
use Twi<PERSON>\Jwt\Grants\VideoGrant;
use <PERSON>wi<PERSON>\Rest\Client as TwilioClient;
use Twi<PERSON>\Twiml;
use Salesfloor\Services\Messaging\BaseChannel;
use Salesfloor\Services\Messaging\Exceptions\MessagingException;
use Salesfloor\Models\Messaging\Text\Message as MessageModel;
use Salesfloor\Models\PhoneNumberDetails as PhoneNumberDetailsModel;
use Salesfloor\API\Managers\UserPhoneNumbers;

class TwilioProvider extends BaseProvider
{
    const PROVIDER_NAME = 'twilio';

    // Friendly Names of Twilio configured 'applications'
    // An application represents a set of configurations that can be applied to a number when receiving text / call
    const APPLICATION_MESSAGING_ONLY = 'Messaging';
    const APPLICATION_NOREPLY_RESPONDER = 'NoReply';

    const INCOMING_PHONE_NUMBER_TYPE_NO_REPLY = 'no_reply';
    const INCOMING_PHONE_NUMBER_TYPE_ASSOCIATE = 'associate';

    // Keys to check for specific values in INBOUND request from Twilio
    const INBOUND_KEY_PHONE_USER = 'To';
    const INBOUND_KEY_PHONE_CUSTOMER = 'From';
    const INBOUND_KEY_MESSAGE_ID = 'MessageSid';
    const INBOUND_KEY_BODY = 'Body';
    const INBOUND_KEY_NUM_MEDIA = 'NumMedia';
    const INBOUND_KEY_MEDIA_TYPE = 'MediaContentType';
    const INBOUND_KEY_MEDIA_URL = 'MediaUrl';
    const INBOUND_KEY_STATUS = 'SmsStatus';
    const INBOUND_VALUE_DIRECTION = MessageModel::DIRECTION_INBOUND;
    // Which of the above keys are required in valid INBOUND requests
    protected $inboundRequiredFields = [
        self::INBOUND_KEY_PHONE_USER,
        self::INBOUND_KEY_PHONE_CUSTOMER,
        self::INBOUND_KEY_MESSAGE_ID,
    ];

    // Keys to check for specific values in OUTBOUND confirmation arrays from Twilio
    const OUTBOUND_KEY_PHONE_USER = 'from';
    const OUTBOUND_KEY_PHONE_CUSTOMER = 'to';
    const OUTBOUND_KEY_MESSAGE_ID = 'sid';
    const OUTBOUND_KEY_BODY = 'body';
    const OUTBOUND_KEY_NUM_MEDIA = 'numMedia';
    const OUTBOUND_KEY_MEDIA_TYPE = 'mediaContentType';
    const OUTBOUND_KEY_MEDIA_URL = 'mediaUrl';
    const OUTBOUND_KEY_DIRECTION = 'direction';
    const OUTBOUND_KEY_STATUS = 'status';

    // https://www.twilio.com/docs/api/errors
    const TWILIO_ERROR_UNSUBSCRIBED_RECIPIENT = 21610; // Attempt to send to unsubscribed recipient
    const TWILIO_ERROR_INVALID_MOBILE_NUMBER  = 21614; // 'To' number is not a valid mobile number
    const TWILIO_ERROR_INVALID_PHONE_NUMBER   = 21211; // 'To' number is not a valid phone number
    const TWILIO_ERROR_REGION_NOT_ENABLED     = 21408; // Permission to send an SMS has not been enabled for the region

    const VIDEO_ROOM_TYPE = 'group-small';

    public $optInKeywords  = ['START', 'YES', 'UNSTOP'];
    public $optOutKeywords = ['STOP', 'STOPALL', 'UNSUBSCRIBE', 'CANCEL', 'END', 'QUIT'];

    // Which of the above keys are required in valid OUTBOUND confirmations
    protected $outboundRequiredFields = [
        self::OUTBOUND_KEY_PHONE_USER,
        self::OUTBOUND_KEY_PHONE_CUSTOMER,
        self::OUTBOUND_KEY_MESSAGE_ID,
    ];

    // Identification / Authorization keys for twilio account in context
    protected $accountSID;
    protected $apiKeyId;
    protected $apiKeySecret;

    /** @var TwilioClient $client Instance of the TwilioClient (API SDK) in usage */
    protected $client;

    // List the used Twilio API Resources. Key is official name used in api, value is "pretty" version for output
    protected $apiResourceNames = [
        'accounts',
        'applications',
        'incomingPhoneNumbers',
        'addresses',
    ];
    private $utils;

    /** @var \Salesfloor\Services\Sanitize\Sanitize $sanitizeService */
    private $sanitizeService;

    /** @var UserPhoneNumbers $userPhoneNumbersManager */
    private $userPhoneNumbersManager;
    /**
     * @inheritdoc
     */
    public function loadDependencies($app)
    {
        $this->accountSID   = $this->getConfig('account_sid');
        $this->apiKeyId     = $this->getConfig('api_key_id');
        $this->apiKeySecret = $this->getConfig('api_key_secret');
        $this->utils        = $app['service.util'];
        $this->sanitizeService          = $app['service.sanitize'];
        $this->userPhoneNumbersManager  = $app['user_phone_numbers.manager'];

        // This will crash normally because serviceProvider is called in injectDeps of rep.
        // To bypass this, we wrap the ServiceProvider in a proxy, that doesn't call the construct on assignment operation.
        $this->setClient($this->apiKeyId, $this->apiKeySecret, $this->accountSID);
    }

    // NOTE: Support the account/auth-token credentials for the
    //       master account (needed for subaccount creation).
    //
    // subaccounts should use the API key id and secret as
    // username and password, and provide the account's SID.
    // The master client should use the master account's SID
    // and auth token as username and password, and provide
    // an empty accountSID parameter.
    //
    public function setClient($username, $password, $accountSID = null)
    {
        $this->client = new TwilioClient(
            $username,
            $password,
            $accountSID
        );
        return $this;
    }

    /**
     * @inheritdoc
     */
    public function getClient()
    {
        return $this->client;
    }

    /**
     * @inheritdoc
     */
    public function getAvailableNumbersForStore(Store $store)
    {
        $attemptLog = [];
        $searches = [];
        // Setting used to find available numbers
        $requiredParams = [
            'smsEnabled' => true, // Allows SMS Texting
            'mmsEnabled' => true, // Allows MMS Texting (images)
            // Since we set up the address for each subaccount, we don't exclude address that requires address setup
            'excludeAllAddressRequired' => false,
            'distance' => $this->configs['twilio.available.phone.numbers.distance'], // Distance in miles accept for some of the below number searches
        ];

        // Best case to worst case phone matching scenarios
        if (!empty($store->phone)) {
            preg_match('/(\d{3})[^0-9]*\d{3}[^0-9]*\d{4}$/', $store->phone, $matches);
            if (isset($matches[1])) {
                $searches['Area Code'] = ['areaCode' => $matches[1]];
            }
        }
        if (!empty($store->postal)) {
            $searches['Postal Code']           = ['inPostalCode' => $store->postal];
            $searches['Postal Code No Spaces'] = ['inPostalCode' => str_replace(' ', '', $store->postal)];
        }
        if (!empty($store->phone)) {
            $searches['Store Phone Number'] = ['nearNumber' => $store->phone];
        }
        if (!empty($store->latitude) && !empty($store->longitude)) {
            $searches['Latitude & Longitude'] = ['nearLatLong' => $store->latitude . ',' . $store->longitude];
        }
        if (!empty($store->region)) {
            $searches['Region'] = ['inRegion' => $store->region];
        }

        foreach ($searches as $searchType => $params) {
            try {
                // Keep information about the attempts that will show on complete failure (0 numbers found)
                $attemptLog[$searchType] = ['search_params' => $params, 'error' => null];
                // When store is disabled, we put a prefix in front of the country
                $numbers = $this->client->availablePhoneNumbers($store->extractCountry())->local->read(array_merge($requiredParams, $params));
                if (!empty($numbers)) {
                    return $numbers;
                }
            } catch (\Exception $fetchAvailableNumbersException) {
                $attemptLog[$searchType]['error'] = $fetchAvailableNumbersException->getMessage();
            }
        }
        // @TODO Should also create alert to pager-duty or something if we could not find anything?
        $this->throwMessagingException('Unable to locate available numbers for Store: "'
            . $store->name . '" ID: ' . $store->store_id . ' - Attempt Details: ' . json_encode($attemptLog));
    }

    /**
     * @inheritdoc
     */
    public function getAvailableNumbersForRetailer()
    {
        $numbers = [];
        // Setting used to find available numbers
        $requiredParams = [
            'smsEnabled' => true, // Allows SMS Texting
            'mmsEnabled' => true, // Allows MMS Texting (images)
            'VoiceEnabled' => true, // Allows Voice
            // Since we set up the address for each subaccount, we don't exclude phone numbers that requires address setup
            'excludeAllAddressRequired' => false,
        ];

        $numbers = $this->client->availablePhoneNumbers($this->configs['retailer.country.code'])->local->read($requiredParams);

        if (!empty($numbers)) {
            return $numbers;
        }

        // @TODO Should also create alert to pager-duty or something if we could not find anything?
        $this->throwMessagingException('Unable to locate available numbers for retailer country code: "' . $this->configs['retailer.country.code']);
    }

    /**
     * @inheritdoc
     */
    public function reserveNumber($numberObject, $userId, $applicationName = null)
    {
        $applicationName = $this->getDefaultApplicationName($applicationName);

        // Purchase the requested number
        $config = $this->getConfigForNumber($numberObject->phoneNumber, $userId, $applicationName);
        $reserved = $this->client->incomingPhoneNumbers->create($config);

        $phoneNumber = $reserved->phoneNumber;

        if (!empty($phoneNumber) && $this->shouldEnableA2P10DLC()) {
            // retrieve the phone number instance because sometime unknown reason(twilio side) $reserved is not a PhoneNumberInstance
            $phoneNumberInstance = $this->getPhoneNumberInstance($reserved->phoneNumber);

            $messageServiceSId = $this->getMessagingServiceSidByNumberFriendlyName($this->getNumbersFriendlyName($userId));

            $this->enableA2P10DLC($phoneNumberInstance->sid, $messageServiceSId);
        }
        return $phoneNumber;
    }

    /**
     * Get messaging service sid by number friendly name
     *
     * There are two types of number friendly name should link to different message service
     * a sf-{retailer}-{env}:{rep_id}    - associate number                -> associate message service
     * b sf-{retailer}-{env}:no-reply    - application no reply number     -> no reply message service
     *
     * @param string $friendlyName
     * @return mixed
     */
    public function getMessagingServiceSidByNumberFriendlyName($friendlyName)
    {
        $phoneNumberType = $this->getPhoneNumberTypeByFriendlyName($friendlyName);

        if ($phoneNumberType == static::INCOMING_PHONE_NUMBER_TYPE_NO_REPLY) {
            $messageServiceSid = $this->configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'];
        } else { //  $phoneNumberType == static::INCOMING_PHONE_NUMBER_TYPE_ASSOCIATE
            $messageServiceSid = $this->configs['retailer.services.channel.text.a2p10dlc.message_service.associate'];
        }

        return $messageServiceSid;
    }

    /**
     * Get phone number type by friendly name
     *
     * There are two types which could know by friendly name
     * a sf-{retailer}-{env}:{rep_id}   - associate number
     * b sf-{retailer}-{env}:no-reply   - application no reply number
     *
     * @param $friendlyName
     * @return string
     */
    public function getPhoneNumberTypeByFriendlyName($friendlyName)
    {
        $lowercaseFriendlyName = strtolower($friendlyName);
        if (strpos($lowercaseFriendlyName, 'no-reply') !== false || strpos($lowercaseFriendlyName, 'noreply') !== false) {
            $phoneNumberType = static::INCOMING_PHONE_NUMBER_TYPE_NO_REPLY;
        } else {
            $phoneNumberType = static::INCOMING_PHONE_NUMBER_TYPE_ASSOCIATE;
        }

        return $phoneNumberType;
    }

    /**
     * Check if retailer need to enable A2P 10DLC as regulation
     * @return bool
     */
    public function shouldEnableA2P10DLC(): bool
    {
        return $this->configs['retailer.services.channel.text.a2p10dlc.is_required'] && !empty($this->configs['retailer.services.channel.text.a2p10dlc.message_service.associate']) && !empty($this->configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply']);
    }

    /**
     * @param $phoneNumberSid string        The phone number sid, format ex: 'PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'
     * @param $messageServiceSid string     messaging service sid, format ex: 'MGXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'
     * refer: https://www.twilio.com/docs/messaging/services/api/phonenumber-resource#create-a-phonenumber-resource-add-a-phone-number-to-a-messaging-service
     */
    public function enableA2P10DLC($phoneNumberSid, $messageServiceSid)
    {
        try {
            $phone_number = $this->client->messaging->v1->services($messageServiceSid)
                ->phoneNumbers
                ->create($phoneNumberSid);

            return $phoneNumberSid;
        } catch (\Exception $e) {
            // silence this error by now, because we could still give phone number to user let user continue onboarding
            // and A2P 10DLC could be enabled later either from twilio console or a cron job
            $this->logger->error('Error enabling A2P 10DLC for phone number: ' . $phoneNumberSid . ' - ' . $e->getMessage());
        }
    }

    /**
     * Get all phone numbers by messaging service sid
     * refer https://www.twilio.com/docs/messaging/api/phonenumber-resource#read-multiple-phonenumber-resources
     * @param $serviceSid
     * @return array
     */
    private function getAllNumbersByMessagingServiceId($serviceSid)
    {
        $numbers = [];

        // as twilio document, a messaging service multiple phone numbers amount is less than 1000 records so no worries about pagination
        $phoneNumbers = $this->client->messaging->v1->services($serviceSid)
            ->phoneNumbers
            ->read();

        foreach ($phoneNumbers as $phoneNumber) {
            $numbers[] = $phoneNumber->phoneNumber;
        }

        return $numbers;
    }

    /**
     * Update a list of Phone Numbers to a Messaging Service
     * refer: https://www.twilio.com/docs/messaging/services/api/phonenumber-resource#create-a-phonenumber-resource-add-a-phone-number-to-a-messaging-service
     * @param $messagingServiceSid  string the message service sid
     * @param $messageServiceType   string the message service type, should be one of the following: 'associate' or 'no_reply' in salesfloor application
     * @return array
     */
    public function updatePhoneNumbersMessageService($messagingServiceSid, $messageServiceType)
    {
        $failedPhones = [];
        // $incomingNumbers format ex:  [ [phoneNumber1 => ['sid1' => sid, 'friendlyName' => friendlyName]]...]
        $incomingNumbers = [];

        $numbersExisted = $this->getAllNumbersByMessagingServiceId($messagingServiceSid);

        $pagesIncomingPhoneNumber = $this->client->incomingPhoneNumbers->stream();

        foreach ($pagesIncomingPhoneNumber as $incomingPhoneNumber) {
            $numberType = $this->getPhoneNumberTypeByFriendlyName($incomingPhoneNumber->friendlyName);

            if ($numberType == $messageServiceType) {
                $incomingNumbers[$incomingPhoneNumber->phoneNumber] = [
                    'sid'          => $incomingPhoneNumber->sid,
                    'friendlyName' => $incomingPhoneNumber->friendlyName,
                ];
            }
        }

        foreach ($incomingNumbers as $incomingNumber => $numberProperties) {
            if (!in_array($incomingNumber, $numbersExisted)) {
                try {
                    $this->enableA2P10DLC($numberProperties['sid'], $messagingServiceSid);
                } catch (\Exception $e) {
                    $failedPhones[$incomingNumber] = $numberProperties;
                    $this->logger->error('Error enabling A2P 10DLC for phone number: ' . $numberProperties['sid'] . ' - ' . $e->getMessage());
                }
            }
        }

        return $failedPhones;
    }

    /**
     * Create a new Twilio Video Access Token
     *
     * This is not related to messaging we should move that to own twilio service.
     *
     * @param string $roomName The name of the room for the video chat
     * @param string $name     The name of the user
     * @return string          The access token
     */
    public function createVideoToken($roomName, $name)
    {
        // Create access token
        $token = new AccessToken($this->accountSID, $this->apiKeyId, $this->apiKeySecret, 3600, $name);

        // Create video grant
        $videoGrant = new VideoGrant();
        $videoGrant->setRoom($roomName);

        // Add grant to token
        $token->addGrant($videoGrant);

        // render token to string
        return $token->toJWT();
    }

    /**
     * Create a new Twilio Video Room
     *
     * This is not related to messaging we should move that to own twilio service.
     *
     * @param string $roomName The name of the room for the video chat
     * @return string          The room Id
     */
    public function createVideoRoom($roomName)
    {
        $room = $this->client->video->v1->rooms
            ->create([
                'type' => self::VIDEO_ROOM_TYPE,
                'uniqueName' => $roomName
            ]);

        return $room->sid;
    }

    /**
     * Get the Twilio Number's resource ID (SID) for the given phone number
     * @param  string $number Phone number in E.164 format i.e. '+15142345678'
     * @return string         SID of given $number
     */
    private function getPhoneNumberSID($number)
    {
        return $this->getClientResource('incomingPhoneNumbers', ['phoneNumber' => $number], true, false)->sid;
    }

    /**
     * Get the Twilio Number's resource for the given phone number
     * @param string $number Phone number in E.164 format i.e. '+15142345678'
     * @throws MessagingException
     */
    public function getPhoneNumberInstance($phoneNumber)
    {
        return $this->getClientResource('incomingPhoneNumbers', ['phoneNumber' => $phoneNumber], true, false);
    }

    /**
     * Get the Twilio Application's resource ID (SID) for the given friendlyName
     * @param  string $friendlyName Friendly Name used at application creation time i.e. 'Messaging'
     * @return string               SID of given $friendlyName
     */
    private function getApplicationSID($friendlyName)
    {
        return $this->getClientResource('applications', ['friendlyName' => $friendlyName], true, false)->sid;
    }

    /**
     * Get the Twilio Account address resource ID (SID) for the given friendlyName
     * @param  string $friendlyName Friendly Name used at address resource creation time i.e. 'saks-dev-address'
     * @return string               SID of given $friendlyName
     */
    private function getAccountAddressSID($friendlyName)
    {
        return $this->getClientResource('addresses', ['friendlyName' => $friendlyName], true, false)->sid;
    }

    /**
     * Function attempts to find a given Client (twilio API) resource by the type $apiResourceName
     * and the received $filter. It will take into account whether or not the search must be strict
     * (Strictness implies that a success can
     * @param  string $apiResourceName Name of the resource type i.e. 'applications' / 'incomingPhoneNumbers'
     * @param  array  $filter          Array of filters to apply to this resource search
     * @param  bool   $strictFind      Whether or not an error should be thrown if more than 1 resource are found
     * @param  bool   $useApi          Whether or not to filter request by ->api
     * @throws MessagingException      When no resource is found or more than
     * @return mixed                   Object representation of resource found
     */
    private function getClientResource($apiResourceName, $filter, $strictFind = true, $useApi = false)
    {
        $client = & $this->client;

        // When requested to, the resource search will take place a layer deeper in the Client, within the "api" context
        // Most resources can be found without this layer. Subaccounts however must use master key and this layer
        if ($useApi) {
            $client = $client->api;
        }

        if (!in_array($apiResourceName, $this->apiResourceNames)) {
            $this->throwMessagingException('Invalid API resource ' . $apiResourceName . ' requested');
        }
        $resources = $client->$apiResourceName->read($filter);
        if (!empty($resources) && (count($resources) === 1 || !$strictFind)) {
            return $resources[0];
        }
        $this->throwMessagingException('Could not determine single ' . $apiResourceName . ' resource for filter ' . json_encode($filter));
    }

    /**
     * @inheritdoc
     */
    public function releaseNumber($number, $userId)
    {
        $returnedNumber = $this->client
            ->incomingPhoneNumbers($this->getPhoneNumberSID($number))
            ->delete();
        return $number;
    }

    /**
     * Returns array to be used as configuration values for a Twilio Phone Number
     * @param  string $number Phone number in E.164 format
     * @param  int    $userId User (rep) that phone number belongs to
     * @return array          Array of configuration values
     */
    private function getConfigForNumber($number, $userId, $applicationName = null)
    {
        $applicationName = $this->getDefaultApplicationName($applicationName);
        $applicationSid = $this->getApplicationSID($applicationName);
        $addressSid     = $this->getAccountAddressSID($this->getAccountAddressFriendlyName());
        return [
            'phoneNumber'         => $number,
            'friendlyName'        => $this->getNumbersFriendlyName($userId),
            'smsApplicationSid'   => $applicationSid,
            'voiceApplicationSid' => $applicationSid,
            'addressSid'          => $addressSid,
        ];
    }

    /**
     * Generate a string to be used as a "friendlyName" for a given twilio number
     * Represents retailer / stack context as well as User (rep) that owns the #
     * i.e. Number for User 425 on Saks Dev -> 'sf-saks-dev:425'
     * @param  int    $userId User (rep) that phone number belongs to
     * @return string         Representing current context and User owning #
     */
    public function getNumbersFriendlyName($userId)
    {
        return $this->getRetailerStackName() . ':' . $userId;
    }

    /**
     * Generate account address resource friendly name
     * @return string account address resource friendly name
     */
    public function getAccountAddressFriendlyName()
    {
        return $this->getRetailerStackName() . ':address';
    }

    /**
     * @inheritdoc
     */
    public function updateNumber($number, $userId, $applicationName = null)
    {
        $this->logger->info("Updating Number $number  - $userId with latest configurations");
        $applicationName = $this->getDefaultApplicationName($applicationName);
        $config = $this->getConfigForNumber($number, $userId, $applicationName);
        $updated = $this->client
            ->incomingPhoneNumbers($this->getPhoneNumberSID($number))
            ->update($config);
        return $updated->toArray();
    }

    /**
     * Get the Twilio subaccount for the current retailer / stack
     * context (i.e. saks-dev).
     *
     * @return null|\Twilio\Rest\Api\V2010\AccountInstance
     */
    public function getSubAccount()
    {
        $friendlyName = $this->getRetailerStackName();

        $this->logger->info('Try yo get sub account ' . $friendlyName);

        $accountList = $this->client->api->accounts->read(["friendlyName" => $friendlyName]);

        if (!empty($accountList)) {
            if (count($accountList) > 1) {
                $this->logger->warning('will use the first sub account if more then one sub account exist :' . $friendlyName);
            } else {
                $this->logger->info('sub account already exist:' . $friendlyName);
            }
            return $accountList[0];
        }

        $this->logger->info('sub account could not found:' . $friendlyName);
        return null;
    }

    /**
     * Creates the Twilio subaccount for the current retailer / stack
     * context (i.e. saks-dev).
     *
     * @return object Twilio Response account object
     */
    public function createSubAccount()
    {
        $friendlyName = $this->getRetailerStackName();

        $this->logger->info('Creating new subaccount ' . $friendlyName);

        return $this->client->api->accounts->create([
            'FriendlyName' => $friendlyName,
        ]);
    }

    /**
     * Updates the Twilio sub account's applications for the current retailer / stack context (i.e. saks-dev)
     */
    public function createOrUpdateSubAccountApplication()
    {
        $friendlyName = $this->getRetailerStackName();

        $this->logger->info('Updating subaccount ' . $friendlyName . ' applications');

        $messagingApplication = $this->createOrUpdateMessagingApplication();

        $accountAddress = $this->createOrUpdateAccountAddress();
        $noreplyApplication = $this->createOrUpdateNoReplyApplication();
    }

    /**
     * Close the given subaccount. This only works if the service is
     * running with master account credentials.
     *
     * See https://www.twilio.com/docs/iam/api/subaccounts#closing-subaccounts
     */
    public function closeSubaccount(string $subaccountSid)
    {
        return $this->client->api->v2010->accounts($subaccountSid)->update([
            "status" => "closed"
        ]);
    }

    /**
     * Creates / Updates the main 'Messaging' application used to configure Phone Numbers
     * for the current reatiler / stack context (i.e. saks-dev)
     * @return object Twilio Response application object
     */
    private function createOrUpdateMessagingApplication()
    {
        try {
            $existingApplicationSid = $this->getApplicationSID($this->configs['messaging.text.application.messaging']);
            $alreadyCreated = true;
        } catch (MessagingException $applicationSidException) {
            $alreadyCreated = false;
        }
        $config = $this->getConfigForApplication();

        if ($alreadyCreated) {
            $this->logger->info('Updating Application "' . $this->configs['messaging.text.application.messaging'] . '"');
            $application = $this->client->applications($existingApplicationSid)->update($config);
        } else {
            $this->logger->info('Creating new Application "' . $this->configs['messaging.text.application.messaging'] . '"');
            $application = $this->client->applications->create($this->configs['messaging.text.application.messaging'], $config);
        }
        $this->logger->info('Application SID: ' . $application->sid);
        $this->logger->info('SMS Callback by ' . $application->smsMethod . ': ' . $application->smsUrl);
        $this->logger->info('Voice Callback by ' . $application->voiceMethod . ': ' . $application->voiceUrl);
        return $application;
    }

    /**
     * Creates / Updates the address resource required by some country regulation when provisioning phone number
     * for the current reatiler / stack context (i.e. saks-dev)
     * @return object Twilio Address Resource object
     */
    private function createOrUpdateAccountAddress()
    {
        try {
            $existingAccountAddressSid = $this->getAccountAddressSID($this->getAccountAddressFriendlyName());
            $alreadyCreated = true;
        } catch (MessagingException $addressSidException) {
            $alreadyCreated = false;
        }

        if ($alreadyCreated) {
            $this->logger->info('Updating Account Address "' . $this->getAccountAddressFriendlyName() . '"');
            $config = $this->getConfigForAddress(false);
            $address = $this->client->addresses($existingAccountAddressSid)->update($config);
        } else {
            $this->logger->info('Creating new Address "' . $this->getAccountAddressFriendlyName() . '"');
            $config = $this->getConfigForAddress(true);
            $address = $this->client->addresses->create(...array_values($config));
        }
        $this->logger->info('Account Address SID: ' . $address->sid);

        return $address;
    }

    /**
     * Updates existing Twilio phone numbers with address
     * for the current reatiler / stack context (i.e. saks-dev)
     */
    public function updateExistingPhoneNumbersAddress()
    {
        $phoneNumbers = $this->userPhoneNumbersManager->getAll([], 0, -1, false);

        $failedUpdatedPhones = [];

        foreach ($phoneNumbers as $phone) {
            if (!empty($phone->phone_number) && !empty($phone->user_id)) {
                try {
                    $this->updateNumber($phone->phone_number, $phone->user_id);
                    $this->logger->info('Updated existing Phone numbers "' . $phone->phone_number . '"');
                } catch (\Exception $e) {
                    $errorMessage = sprintf(
                        "Failed updating existing Phone numbers[%s]. The error message is: %s, and failure backtrace is: %s",
                        $phone->phone_number,
                        $e->getMessage(),
                        $e->getTraceAsString()
                    );
                    $failedUpdatedPhones[] = ['number' => $phone->phone_number, 'error' => $errorMessage];
                    $this->logger->error($errorMessage);
                }
            }
        }

        return $failedUpdatedPhones;
    }

    /**
     * Creates or Updates the NoReply application, which provides autoresponders for
     * numbers that are unmonitored by a rep. Generally, these are numbers used for
     * general retailer messages and confirmations. Since they are unmonitored
     *
     * @return mixed
     */
    private function createOrUpdateNoReplyApplication()
    {
        try {
            $existingApplicationSid = $this->getApplicationSID($this->configs['messaging.text.application.noreply']);
            $alreadyCreated = true;
        } catch (MessagingException $applicationSidException) {
            $alreadyCreated = false;
        }
        $config = $this->getConfigForApplication('noreply');

        if ($alreadyCreated) {
            $this->logger->info('Updating Application "' . $this->configs['messaging.text.application.noreply'] . '"');
            $application = $this->client->applications($existingApplicationSid)->update($config);
        } else {
            $this->logger->info('Creating new Application "' . $this->configs['messaging.text.application.noreply'] . '"');
            $application = $this->client->applications->create($this->configs['messaging.text.application.noreply'], $config);
        }

        // Reserve NOREPLY number for new NOREPLY application
        $this->generateReserveAndSaveNumberForNoReply();

        $this->logger->info('Application SID: ' . $application->sid);
        $this->logger->info('SMS Callback by ' . $application->smsMethod . ': ' . $application->smsUrl);
        $this->logger->info('Voice Callback by ' . $application->voiceMethod . ': ' . $application->voiceUrl);
        return $application;
    }

    /**
     * Reserve a phone number via the SMS provider, save it into database.
     * This number will be used to send no reply SMS message
     */
    public function generateReserveAndSaveNumberForNoReply()
    {
        if (
            $this->configs['retailer.services.channel.text.enabled']
            && $this->configs['retailer.services.channel.text.generate_reserve_and_save_number_for_noreply']
        ) {
            $allNoReplyPhoneNumbers = $this->getIncomingPhoneNumberForNoReply();

            if (!empty($allNoReplyPhoneNumbers)) {
                $twilioPhoneNumber = $allNoReplyPhoneNumbers[0]->phoneNumber;
                //TODO : other no-reply phone numberS could be removed here
            } else {
                $twilioPhoneNumber = $this->generateAndReserveNumberForNoReply();
            }

            $this->wpOptions->createOrUpdateNoReplyPhoneNumber($twilioPhoneNumber);
        }
    }

    /**
     * Return all incomingPhoneNumbers of no-reply in a generator
     *
     * @return \Twilio\Rest\Api\V2010\Account\IncomingPhoneNumberInstance[]
     */
    public function getIncomingPhoneNumberForNoReply()
    {
        $friendlyName = $this->getRetailerStackName() . ':' . static::SMS_PROVIDER_NO_REPLY_KEY;
        $noReplyList  = $this->client->incomingPhoneNumbers->read(["friendlyName" => $friendlyName]);

        return $noReplyList;
    }

    /**
     * Generates and returns the array of configs needed to create / update an Application for Twilio Numbers
     * @return array Configuration field and values
     */
    private function getConfigForApplication($suffix = '')
    {
        // URL at the base of callbacks for Twilio. Usually REST API URL
        $apiUrl   = $this->restApiHost;
        // Override URL base with configured URL (i.e. dev testing with ngrok)
        // NOT ALLOWED IN PRODUCTION
        if (!empty($this->getConfig('override_callback_host')) && strtoupper($this->stack) !== 'PROD') {
            $apiUrl = $this->getConfig('override_callback_host');
        }
        $baseUrl  = $apiUrl . BaseChannel::MESSAGING_BASE_URL_PUBLIC . '/' . Channel::CHANNEL_NAME . '/' . static::PROVIDER_NAME;

        if (!empty($suffix)) {
            $baseUrl .= '/' .  $suffix;
        }

        $voiceUrl = $baseUrl . '/voice';
        $smsUrl   = $baseUrl . '/sms';
        $method   = 'POST';

        return [
            // Used functionalities
            'voiceUrl' => $voiceUrl,
            'voiceMethod' => $method,
            'smsUrl' => $smsUrl,
            'smsMethod' => $method,
            'smsFallbackUrl' => $smsUrl . '/fallback',
            'smsFallbackMethod' => $method,
            'smsStatusCallback' => $smsUrl . '/status',
            // Unused functionalities
            'voiceFallbackUrl' => null,
            'voiceFallbackMethod' => null,
            'statusCallback' => null,
            'statusCallbackMethod' => null,
            'voiceCallerIdLookup' => false,
        ];
    }

    /**
     * Generates and returns the array of configs needed to create / update a Twilio address resource
     *
     * The function used to create and update address resource in Twilio library is badly written and
     * the way to pass arguments are different:
     *
     * create($customerName, $street, $city, $region, $postalCode, $isoCountry, $options = array())
     *
     * update($options = array())
     *
     * @param bool $create to know if the config is used to create or update the address
     *
     * @return array $config Configuration field and values
     */
    private function getConfigForAddress($create = false)
    {
        $config = [
            'customerName' => $this->configs['retailer.hq_address.customerName'],
            'street'       => $this->configs['retailer.hq_address.street'],
            'city'         => $this->configs['retailer.hq_address.city'],
            'region'       => $this->configs['retailer.hq_address.region'],
            'postalCode'   => $this->configs['retailer.hq_address.postalCode'],
            'isoCountry'   => $this->configs['retailer.hq_address.isoCountry'],
            'friendlyName' => $this->getAccountAddressFriendlyName(),
        ];

        if ($create) {
            $options = [
                'friendlyName' => [
                    'friendlyName' => $this->getAccountAddressFriendlyName(),
                ],
            ];
            $config = array_values(array_merge($config, $options));
        } else {
            $config = array_merge($config, ['friendlyName' => $this->getAccountAddressFriendlyName()]);
        }

        return $config;
    }

    /**
     * Function that returns a TwiML response. This response tells Twilio
     * to reply back to a message with the a given $body and $attachments
     * FOR TESTING
     * @param  string $body        Body of the received message
     * @param  array  $attachments Array with url in each sub array
     * @return object              TwiML response object
     */
    private function prepareMessageResponse($body = '', array $attachments = [])
    {
        $response = new Twiml();
        $message = $response->message();
        if (!empty($body)) {
            $message->body($body);
        }
        foreach ($attachments as $attachment) {
            $message->media($attachment['url']);
        }
        return $response;
    }

    /**
     * @inheritdoc
     */
    public function sanitizeInboundRequestData(array $data)
    {
        return $this->sanitizeData($data, true);
    }

    /**
     * @inheritdoc
     */
    public function sanitizeOutboundRequestData(array $data)
    {
        return $this->sanitizeData($data, false);
    }

    /**
     * Build body content based on attachment type.
     *
     * @param $attachment
     *
     * @return string
     */
    protected function buildBodyWhenOneAttachment($attachment)
    {
        $type = $attachment['type'];

        switch ($type) {
            case Attachment::TYPE_PRODUCT_VARIANT:
            case Attachment::TYPE_PRODUCT:
                return $this->buildBodyWhenAttachedProduct($attachment);
            case Attachment::TYPE_ASSET:
                return $this->buildBodyWhenAttachedAsset($attachment);
        }
    }

    protected function buildBodyWhenAttachedProduct($attachment)
    {
        $name = $attachment['name'];
        $regularPrice = $attachment['regular_price'];
        $specialPrice = $attachment['sale_price'];
        $linkUrl = $attachment['link_url'];
        $locale = $attachment['locale'];

        $body = $this->createBodyWhenAttachedProduct($name);

        if ($this->configs['products.is-price-displayed']) {
            if ($this->hasSalePrice($specialPrice, $regularPrice)) {
                $body = $this->addToBodyWhenAttachedProduct($body, [
                    $this->translator->trans("api_label_price_original", ['%price%' => $this->utils->formatPrice($regularPrice, null, $locale)], null, $locale),
                    $this->translator->trans("api_label_price_sale", ['%price%' => $this->utils->formatPrice($specialPrice, null, $locale)], null, $locale),
                    '',
                ]);
            } else {
                $body = $this->addToBodyWhenAttachedProduct($body, [
                    $this->translator->trans("api_label_price", ['%price%' => $this->utils->formatPrice($regularPrice, null, $locale)], null, $locale),
                    '',
                ]);
            }
        }

        $body = $this->addToBodyWhenAttachedProduct($body, [
            $linkUrl
        ]);

        return implode("\n", $body);
    }

    /**
     * Define if the product has a sale price
     * @param  integer  $specialPrice Special price
     * @param  integer  $regularPrice Regular price
     * @return boolean
     */
    private function hasSalePrice($specialPrice, $regularPrice)
    {
        return $specialPrice && $specialPrice < $regularPrice;
    }

    /**
     * Create minium body with product name
     * @param  string $name Product name
     * @return array        Basic body
     */
    private function createBodyWhenAttachedProduct($name)
    {
        return [
            $name,
            '',
        ];
    }

    /**
     * Add elements to the body. Can be price, links...
     * @param array $body  Body
     * @param array $items Items to add to the body
     * @return array       The body with new items
     */
    private function addToBodyWhenAttachedProduct(array $body, array $items)
    {
        return array_merge($body, $items);
    }

    protected function buildBodyWhenAttachedAsset($attachment)
    {
        $body = [
            $attachment['link_url'],
        ];

        return implode("\n", $body);
    }

    /**
     * Sanitize $data depending on the $isInbound boolean. Function knows which keys
     * to check to find specific values as well as which fields are required.
     * If inbound TRUE, will have attachments interpreted
     * If inbound FALSE, will do basic sanitization
     * @param  array  $data      Data received, request data when inbound, response data when outbound
     * @param  bool   $isInbound Whether message is inbound or outbound
     * @return array             Array of restructure data that is accepted by functions such
     *                                 as sendMessage()
     */
    private function sanitizeData(array $data, $isInbound)
    {
        $prefix = ($isInbound ? 'inbound' : 'outbound');
        $constant = strtoupper($prefix);
        $requiredFields = $prefix . 'RequiredFields';
        // Check that inbound array has minimum required fields
        $missingKeys = [];
        foreach ($this->$requiredFields as $key) {
            if (!isset($data[$key])) {
                $missingKeys[] = $key;
            }
        }
        if (!empty($missingKeys)) {
            $this->throwMessagingException(ucwords($prefix) . ' ' . Channel::CHANNEL_NAME . ' array is missing keys ' . json_encode($missingKeys));
        }
        // When interpreting the data returned from Twilio (from inbound requests as well as the response for outbound)
        // To determine which type of inbound / outbound-{api|call|reply} requires some magic
        // When inbound we use the 'inbound' value, when outbound we grab the value for the OUTBOUND_KEY_DIRECTION
        // from the response returned by Twilio.
        $direction = ($isInbound ? self::INBOUND_VALUE_DIRECTION : $data[self::OUTBOUND_KEY_DIRECTION]);
        // Return data in agreed upon structure
        if ($isInbound) {
            return [
                'userPhoneNumber' => $data[self::INBOUND_KEY_PHONE_USER],
                'customerPhoneNumber' => $data[self::INBOUND_KEY_PHONE_CUSTOMER],
                'providerMessageId' => $data[self::INBOUND_KEY_MESSAGE_ID],
                'body' => $this->sanitizeService->removeXss(!empty($data[self::INBOUND_KEY_BODY]) ? $data[self::INBOUND_KEY_BODY] : null),
                'direction' => $direction,
                'status' => $data[self::INBOUND_KEY_STATUS],
                'attachments' => $this->getAttachments($data, $constant)
            ];
        } else {
            return [
                'userPhoneNumber' => $data[self::OUTBOUND_KEY_PHONE_USER],
                'customerPhoneNumber' => $data[self::OUTBOUND_KEY_PHONE_CUSTOMER],
                'providerMessageId' => $data[self::OUTBOUND_KEY_MESSAGE_ID],
                'body' => $this->sanitizeService->removeXss(!empty($data[self::OUTBOUND_KEY_BODY]) ? $data[self::OUTBOUND_KEY_BODY] : null),
                'direction' => $direction,
                'status' => $data[self::OUTBOUND_KEY_STATUS],
                'attachments' => $this->getAttachments($data, $constant)
            ];
        }
    }

    /**
     * @inheritdoc
     */
    public function getAttachments(array $data, $constant)
    {
        $attachments = [];
        if ($data[constant('self::' . $constant . '_KEY_NUM_MEDIA')] > 0) {
            for ($i = 0; $i < $data[constant('self::' . $constant . '_KEY_NUM_MEDIA')]; $i++) {
                $typeKey = constant('self::' . $constant . '_KEY_MEDIA_TYPE') . $i;
                $urlKey = constant('self::' . $constant . '_KEY_MEDIA_URL') . $i;
                if (!empty($data[$typeKey]) && !empty($data[$urlKey])) {
                    $attachments[] = [
                        'type' => 'url',
                        'url' => $data[$urlKey],
                        'file' => null,
                        'mimeType' => $data[$typeKey],
                    ];
                }
            }
        }
        return $attachments;
    }

    /**
     * @inheritdoc
     */
    public function sendMessage($fromNumber, $toNumber, $body = '', array $attachments = [])
    {
        $message = ['from' => $fromNumber];
        if (count($attachments) === 1) {
            $message['body'] = $this->buildBodyWhenOneAttachment(reset($attachments));
        } elseif (!empty($body)) {
            $message['body'] = $body;
        }

        $urls = [];
        foreach ($attachments as $attachment) {
            $urls[] = $attachment['url'];
        }
        if (!empty($urls)) {
            $message['mediaUrl'] = $urls;
        }

        try {
            $response =  $this->client->messages->create($toNumber, $message);
        } catch (\Exception $exception) {
            if ($exception->getCode() === self::TWILIO_ERROR_UNSUBSCRIBED_RECIPIENT) {
                throw new CustomerOptedOutException();
            }
            // Keep the code since that's the best way to know the twilio exception
            throw new MessagingException($exception->getMessage(), $exception->getCode());
        }

        // Debug only and should be removed when ticket is done.
        // https://salesfloor.atlassian.net/browse/AC-217
        if (!empty($body)) {
            $this->logger->error('AC217', $response->toArray());
        }
        return $response->toArray();
    }

    /**
     * @inheritdoc
     */
    public function replyWithDictatedMessage(array $sentences, $locale = null)
    {
        $response = new Twiml();
        foreach ($sentences as $sentence) {
            if (is_string($sentence) && !empty($sentence)) {
                $response->say($sentence, $this->getVoiceParameters($locale));
            }
        }
        return $response;
    }

    /**
     * @inheritdoc
     */
    public function replyWithBlankResponse()
    {
        return new Twiml();
    }

    /**
     * Return the parameters to pass to Twilio when using the "say" verb to speak back
     * @param   $locale    To set the locale for the service to read a voice message
     * @return array Describing which voice to use to speak the phrases passed
     */

    private function getVoiceParameters($locale = null)
    {
        $params = ['voice' => 'woman'];

        if (!empty($locale)) {
            $params['language'] = $this->sanitizeLocaleForVocalMessage($locale);
        }

        return $params;
    }

    /**
     * Twilio is expecting a specific format for vocal message locale
     *
     * should be: en, fr, en-us...
     *
     * We need to convert locale (en_CA) into Twilio format (en-ca)
     *
     * @param  [type] $locale [description]
     * @return [type]         [description]
     */
    private function sanitizeLocaleForVocalMessage($locale)
    {
        return strtolower(str_replace('_', '-', $locale));
    }

    public function getOptInKeywords()
    {
        return $this->optInKeywords;
    }

    public function getOptOutKeywords()
    {
        return $this->optOutKeywords;
    }

    protected function getDefaultApplicationName($applicationName)
    {
        if (null === $applicationName) {
            return $this->configs['messaging.text.application.messaging'];
        }

        return $applicationName;
    }

    /**
     * Lookup the phone number's carrier type using third party service
     * NOTE: ref https://www.twilio.com/docs/lookup/v1-api
     * 1. when a phone number is detected as landline/voip by Twilio, the customer might could still get message for some carriers/provider
     * 2. when a phone number is not detected not as landline/voip by Twilio, in real word it still could be a landline number,
     *    just Twilio could not detect it so return as null so it is unknown in our side
     * @param $phoneNumber
     * @return bool
     */
    public function lookupPhoneNumberCarrierType($phoneNumber)
    {
        $number = $this->client->lookups->v1->phoneNumbers($phoneNumber)
            ->fetch(['type' => 'carrier']);

        $carrierType = $number->carrier['type'] ?? PhoneNumberDetailsModel::CARRIER_TYPE_UNKNOWN;

        $possibleCarrierType = [
            PhoneNumberDetailsModel::CARRIER_TYPE_MOBILE,
            PhoneNumberDetailsModel::CARRIER_TYPE_LANDLINE,
            PhoneNumberDetailsModel::CARRIER_TYPE_VOIP,
            PhoneNumberDetailsModel::CARRIER_TYPE_UNKNOWN,
        ];

        if (!in_array($carrierType, $possibleCarrierType)) {
            $carrierType = PhoneNumberDetailsModel::CARRIER_TYPE_UNKNOWN;
        }

        return $carrierType;
    }
}
