<?php

namespace Salesfloor\Services\Messaging\Text;

use malkusch\lock\exception\MutexException;
use malkusch\lock\mutex\PredisMutex;
use Salesfloor\API\Exceptions\Manager\MissingRequiredFieldException;
use Salesfloor\API\Managers\Client\Customers\Legacy;
use Salesfloor\API\Managers\CustomerMeta;
use Salesfloor\API\Managers\Messaging\Text\Messages;
use Salesfloor\API\Managers\Messaging\Text\Threads;
use Salesfloor\API\Managers\PhoneNumberDetails as PhoneNumberDetailsManager;
use Salesfloor\Models\PhoneNumberDetails as PhoneNumberDetailsModel;
use Salesfloor\API\Managers\PreCustomers;
use Salesfloor\API\Managers\Services\Appointments;
use Salesfloor\API\Managers\Services\PersonalShopper;
use Salesfloor\API\Managers\Services\Questions;
use Salesfloor\API\Managers\SmsBlock\SmsBlockList;
use Salesfloor\API\Managers\UserPhoneNumbers;
use Salesfloor\Exceptions\GlobalException;
use Salesfloor\Models\Base;
use Salesfloor\Models\Customer;
use Salesfloor\Models\CustomerFieldHistory;
use Salesfloor\Models\Messaging\Text\Message;
use Salesfloor\Models\Messaging\Text\Thread;
use Salesfloor\Models\PreCustomer;
use Salesfloor\Models\Rep;
use Salesfloor\Models\Services\Appointment;
use Salesfloor\Models\Services\PersonalShopper as PersonalShopperModel;
use Salesfloor\Models\Services\Question;
use Salesfloor\Models\Services\ServiceInterface;
use Salesfloor\Models\UserPhoneNumber;
use Salesfloor\Services\AutoResponder;
use Salesfloor\Services\Messaging\BaseChannel;
use Salesfloor\Models\Customer as CustomerModel;
use Salesfloor\Models\Messaging\Text\Message as MessageModel;
use Salesfloor\Services\Messaging\Exceptions\CustomerOptedOutException;
use Salesfloor\Services\Messaging\Exceptions\MessagingException;
use Salesfloor\Services\Messaging\Exceptions\TextEnableQueueException;
use Salesfloor\Services\Messaging\Exceptions\TextSendingBlockException;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Models\CustomerActivityFeed;
use Salesfloor\Services\SalesfloorAPIRepository;
use Salesfloor\Services\Event as EventService;
use Salesfloor\Services\Messaging\Recipient\RecipientService;

class Channel extends BaseChannel
{
    const CHANNEL_CONFIG = 'messaging.text';
    const CHANNEL_NAME = 'text';

    const NOTIFICATION_MESSAGE_NEW_TEXT_MESSAGE = 'pn_you_got_new_text_message';

    // Seconds range for waiting for reply from AWS SQS for Text Enable Queue
    const QUEUE_MIN_WAIT = 0;
    const QUEUE_MAX_WAIT = 20;

    // Key names used in the Messages passed in / out of the AWS SQS service
    const MSG_KEY_USER_ID      = 'user_id';
    const MSG_KEY_ACTION_BY    = 'action_by_user_id';
    const MSG_KEY_ENABLE       = 'enable';
    const MSG_KEY_RETAILER     = 'retailer';
    const MSG_KEY_STACK        = 'stack';
    const MSG_KEY_REQUESTED_AT = 'requested_at';

    const RETAILER_SHORT_BRU = 'bru';
    const RETAILER_SHORT_TRU = 'tru';

    const array MSG_NOT_SUPPORTED_PHONE_CARRIER_TYPES = [
        PhoneNumberDetailsModel::CARRIER_TYPE_LANDLINE,
        PhoneNumberDetailsModel::CARRIER_TYPE_VOIP,
    ];

    // Cache key for prevent spam for no-reply messages
    const CACHE_NO_REPLY_ALREADY_SENT_KEY = 'messaging-no-reply';
    const CACHE_NO_REPLY_ALREADY_SENT_TTL = 86400; // 24h

    private $queueId;
    private $queue;

    // Dead Letter Queue Settings
    private $deadLetterQueueId;
    // 14 is the max days by AWS limits
    private $deadLetterRetentionDays = 14;
    private $numberReceivesBeforeDeadLetter = 5;

    private $waitTimeSeconds = 20;
    private $lastQueueUrl;
    private $lastReceiptHandle;
    // Return to queue and hide message for # Seconds if failed to save it
    private $hideMessage = 5;

    /** @var TwilioProvider $provider */
    protected $provider;

    // List of all Providers. First will be used as default
    protected $allProvidersArray = [
        TwilioProvider::PROVIDER_NAME => 'Salesfloor\Services\Messaging\Text\TwilioProvider',
    ];

    /** @var \Salesfloor\API\Managers\Messages\Legacy $sfMessagesManager */
    protected $sfMessagesManager;

    protected $customerActivityFeedService;

    /** @var MySQLRepository Repository */
    protected $repository;

    /** @var Translator $translator */
    protected $translator;
    protected $locale;
    protected $cache;

    /** @var Multilang $multilangService */
    private $multilangService;

    /** @var Threads $textThreadsManager */
    private $textThreadsManager;

    /** @var Messages $textMessagesManager */
    private $textMessagesManager;

    /** @var PhoneNumberDetailsManager $phoneNumberDetailsManager */
    private $phoneNumberDetailsManager;

    /** @var Legacy $customersManager */
    private $customersManager;

    /** @var PreCustomers $precustomerManager */
    private $precustomerManager;

    /** @var UserPhoneNumbers $userPhoneNumbersManager */
    private $userPhoneNumbersManager;
    private $textAttachmentsManager;

    /** @var SmsBlockList */
    private $smsBlockListManager;

    /** @var AutoResponder $autoresponder */
    private $autoresponder;

    /** @var SalesfloorAPIRepository $salesfloorApi */
    private $salesfloorApi;
    private $retailerPrettyName;
    private $isTeamMode;

    /** @var Questions $questionsManager */
    private $questionsManager;

    /** @var Appointments $appointmentsManager */
    private $appointmentsManager;

    /** @var PersonalShopper $personalShopperManager */
    private $personalShopperManager;

    /** @var callable $notificationSystemFactory */
    private $notificationSystemFactory;

    /** @var SendingQueue  $sendingQueue */
    private $sendingQueue;

    /** @var \Salesfloor\API\Managers\Blacklist\Sms $blacklistSmsManager */
    private $blacklistSmsManager;

    /** @var EventService $eventService */
    private $eventService;

    /** @var RecipientService $recipientService  */
    private $recipientService;
    /**
     * Destructor that can try to ensure that halfway read/handled
     *  messages are sent back into the events queue for reprocessing
     */
    public function __destruct()
    {
        if (!empty($this->lastQueueUrl) && !empty($this->lastReceiptHandle)) {
            $this->queue->changeMessageVisibility($this->lastQueueUrl, $this->lastReceiptHandle, $this->hideMessage);
        }
    }

    /**
     * @inheritdoc
     */
    public function loadDependencies($app)
    {
        $this->repsManager = $app['reps.manager'];
        $this->precustomerManager = $app['pre_customers.manager'];
        $this->customersManager = $app['customers.manager'];
        $this->userPhoneNumbersManager = $app['user_phone_numbers.manager'];
        $this->textThreadsManager = $app['messaging.text.threads.manager'];
        $this->textMessagesManager = $app['messaging.text.messages.manager'];
        $this->textAttachmentsManager = $app['messaging.text.attachments.manager'];
        $this->smsBlockListManager = $app['sms_block_list.manager'];
        $this->phoneNumberDetailsManager = $app['phone_number_details.manager'];

        $this->autoresponder = $app['service.autoresponder'];
        $this->salesfloorApi = $app['repositories.salesfloor_api'];

        // Enable Disable SMS by Associate
        $this->queue = $app['service.messagequeue'];
        $this->queueId = $app['configs']['queue.text_messaging_enable.full_name'];
        $this->deadLetterQueueId = $app['configs']['queue.text_messaging_enable.dead_letter_name'];
        $this->updateQueueIdsForMultibrand();

        $this->retailerPrettyName = $app['configs']['retailer.pretty_name'];
        $this->isTeamMode = $app['configs']['retailer.storepage_mode'];

        // Notify Associate When Text Messaging is Enabled
        $this->sfMessagesManager = $app['messages.manager'];

        $this->repository = $app['repositories.mysql'];
        $this->locale     = $app['locale'];
        $this->translator = $app['translator'];
        $this->cache      = $app['predis'];

        $this->customerActivityFeedService = $app['service.customer_activity_feed'];

        $this->multilangService = $app['service.multilang'];

        $this->questionsManager       = $app['questions.manager'];
        $this->appointmentsManager    = $app['appointments.manager'];
        $this->personalShopperManager = $app['personal_shopper.manager'];

        $this->notificationSystemFactory = $app['service.notification-system'];
        $this->sendingQueue              = $app['service.messaging.text.sending_queue'];

        $this->blacklistSmsManager  = $app['blacklistSms.manager'];
        $this->eventService         = $app['service.event'];

        $this->recipientService = $app['service.messaging.recipient'];
    }

    /**
     * In the case of multibrand ensure that the bru is picked instead of tru
     * as the "retailer short name" within the queue naming structure
     */
    public function updateQueueIdsForMultibrand()
    {
        if ($this->retailer === static::RETAILER_SHORT_TRU) {
            $this->queueId = str_replace($this->queueId, static::RETAILER_SHORT_TRU, static::RETAILER_SHORT_BRU);
            $this->deadLetterQueueId = str_replace($this->deadLetterQueueId, static::RETAILER_SHORT_TRU, static::RETAILER_SHORT_BRU);
        }
    }

    /**
     * @inheritdoc
     *
     * @throws CustomerOptedOutException
     */
    public function sendMessage(
        $userId,
        array $to,
        $body = '',
        array $attachments = [],
        $sendingUserId = null,
        bool $isToMany = false,
        bool $isLastMessage = true,
        $addToDB = true,
        ServiceInterface $request = null
    ) {
        $userPhoneNumberObject = $this->getUserPhoneNumberObject($userId);

        if (empty($userPhoneNumberObject)) {
            $this->throwMessagingException('Rep ' . $userId . ' does not have ' . static::CHANNEL_NAME . ' messaging permission');
        }

        try {
            $this->sendOptOutMessageIfEligible($userPhoneNumberObject->phone_number, $to, $userId, $request);

            if ($addToDB) {
                $providerResponse = $this->postToProviderAndSaveOnDb(
                    $userPhoneNumberObject->phone_number,
                    $to,
                    $body,
                    $attachments,
                    $sendingUserId,
                    $request
                );
            } else {
                // todo: check it later.
                $providerResponse = $this->postToProvider(
                    $userPhoneNumberObject->phone_number,
                    $to,
                    $body,
                    $attachments
                );
            }

            // Send STOP message for multi-recipient messages
            if ($isToMany && $isLastMessage) {
                $this->sendStopMessageAfterLastMessage($userPhoneNumberObject->phone_number, $to, $userId);
            }
        } catch (CustomerOptedOutException $customerOptedOutException) {
            $this->handleOptOut($userPhoneNumberObject->phone_number, $to['customer_phone_number']);
            throw $customerOptedOutException;
        }

        // Track outbound text message here
        $customerId = $this->recipientService->getCustomerId($to);
        if ($customerId !== null) {
            if (empty($body) && !empty($attachments)) {
                $body = '[Attachment]';
            }

            $this->customerActivityFeedService->trackActivity(
                $customerId,
                $userId,
                CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD,
                $providerResponse->text_thread_id,
                $body,
                CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_OUTBOUND,
                CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ,
                true
            );
        }

        return $providerResponse;
    }

    public function sendNoReplyMessage(array $to, $body = '', $addToDB = false, ServiceInterface $request = null)
    {
        $fromNumber = $this->wpOptions->getNoReplyPhoneNumber();

        try {
            // Sometimes (e.g: appointment), we would like to add it as a message for more information
            // Most of the time, we don't care because it's adding no value
            if ($addToDB) {
                $providerResponse = $this->postToProviderAndSaveOnDb($fromNumber, $to, $body, [], null, $request);
            } else {
                $providerResponse = $this->postToProvider($fromNumber, $to, $body, []);
            }
        } catch (CustomerOptedOutException $customerOptedOutException) {
            $this->handleOptOut($fromNumber, $to['customer_phone_number']);
            throw $customerOptedOutException;
        }

        return $providerResponse;
    }

    /**
     * Post a message to provider and persist
     *
     * @param         $userPhoneNumber
     * @param array   $to
     * @param string  $body
     * @param array   $attachments
     * @param integer $sendingUserId
     *
     * @return mixed
     */
    protected function postToProviderAndSaveOnDb($userPhoneNumber, array $to, $body = '', array $attachments = [], $sendingUserId = null, ServiceInterface $service = null)
    {
        $data = $this->postToProvider($userPhoneNumber, $to, $body, $attachments);

        // Only for contact, we store customer_id.
        $customerId = $this->recipientService->getCustomerId($to);
        if (!empty($data)) {
            $message = $this->addMessageToThread(
                $data['userPhoneNumber'],
                $data['customerPhoneNumber'],
                $data['providerMessageId'],
                $data['direction'],
                $data['status'],
                $customerId,
                $data['body'],
                (empty($data['attachments']) ? $attachments : $data['attachments']),
                !(empty($sendingUserId)) ? $sendingUserId : null,
                (!empty($service)) ? $service->getSource() : null,
                (!empty($service)) ? $service->getId() : null
            );
            return $message;
        }
        return false;
    }

    protected function postToProvider($userPhoneNumber, array $to, $body = '', array $attachments = [])
    {
        $providerResponse = $this->provider->sendMessage(
            $userPhoneNumber,
            $to['customer_phone_number'],
            $body,
            $attachments
        );
        return $this->provider->sanitizeOutboundRequestData($providerResponse);
    }

    /**
     * @inheritdoc
     */
    public function validateSendMessage($userId, array $to, $body = '', array $attachments = [], bool $skipBlockList = false)
    {
        // Rep and retailer permissions should already have been checked in validatePermissions()
        // Additional validation that $body / $attachments are valid
        if (empty($body) && empty($attachments)) {
            $this->throwMessagingException('Message must contain a body or an array of attachments');
        }
        if (isset($body) && !is_string($body)) {
            $this->throwMessagingException('Body is not a string');
        }

        if (!$skipBlockList && $this->smsBlockListManager->isBlocked($to['customer_phone_number'], $userId)) {
            throw new TextSendingBlockException('Rep ' . $userId . ' is blocked to send ' . static::CHANNEL_NAME . ' message to ' . $to['customer_phone_number']);
        }

        return true;
    }

    /**
     * @inheritdoc
     */
    public function validateUserPermission($userId)
    {
        $userPhoneNumberObject = $this->getUserPhoneNumberObject($userId);
        if (empty($userPhoneNumberObject)) {
            $this->throwMessagingException('Rep ' . $userId . ' does not have ' . static::CHANNEL_NAME . ' messaging permission');
        }
        return true;
    }


    /**
     * Return the UserPhoneNumber object for the requested $userId
     * If no UserPhoneNumber row exists for the user, a null will be returned
     *
     * @param  int $userId ID of the user to get number for
     *
     * @return UserPhoneNumber|Base|null
     */
    public function getUserPhoneNumberObject($userId)
    {
        return $this->userPhoneNumbersManager->getOneOrNull(['user_id' => $userId], null, false);
    }

    /**
     * @inheritdoc
     */
    public function enableUser($userId, $actionByUserId)
    {
        try {
            $this->validateRetailerPermission();
            $userPhoneNumberObject = $this->getUserPhoneNumberObject($userId);
            if (empty($userPhoneNumberObject)) {
                /**@var \Salesfloor\Models\Store $store */
                $store = $this->getStoreByRep($userId);
                if (empty($store)) {
                    $this->logger->critical('Error Enabling ' . self::CHANNEL_NAME . ' Messaging for User ' . $userId . '. No related store');
                    return false;
                }

                if ($this->configs['retailer.storepage_mode'] == true) {
                    $storePhoneCount = $this->userPhoneNumbersManager->countByStoreId($store->store_id);
                    if ($storePhoneCount >= 1) {
                        $this->logger->warning(
                            sprintf(
                                "You are trying to enable SMS on a store mode, which the store: [%s] already have phone number,'. Skipping",
                                $store->store_id
                            )
                        );

                        // i must return true, otherwise, we won't remove it from the queue.
                        return true;
                    }
                }

                // Safety net, so we don't create phone on a disabled rep.

                // I wanted to use getById(), but i see there's a fallback with user_login. (╯°□°）╯ ┻━┻
                // Keep in mind that if the userId is deleted in DB, message will never be remove from the queue
                // because of "Not found" exception.

                /** @var Rep $rep */
                $rep = $this->getRep($userId);

                if (!$rep->user_status) {
                    $this->logger->warning(
                        sprintf(
                            "You are trying to enable SMS on a disabled rep [%s]. Skipping",
                            $userId
                        )
                    );
                    // i must return true, otherwise, we won't remove it from the queue.
                    return true;
                }

                // Number is in E.164 (i.e. "+1") format, ex '+14388002173'
                $number = $this->provider->generateAndReserveNumberForUser($userId, $store);
                // Create UserPhoneNumber
                $this->userPhoneNumbersManager->createAndSave($userId, $actionByUserId, $number);
                $this->logger->info('Enabled User ' . $userId . ' for ' . self::CHANNEL_NAME . ' messaging - Assigned Phone #: ' . $number);

                // SF-17885 Notify Associate When Text Messaging is Enabled
                $this->notifyUserOnEnable($userId, $number);
            }
        } catch (\Exception $enableUserException) {
            $this->logger->critical('Error Enabling ' . self::CHANNEL_NAME . ' Messaging for User ' . $userId . '. With Message: ' . $enableUserException->getMessage());
            return false;
        }
        return true;
    }

    /**
     * @inheritdoc
     */
    public function disableUser($userId, $actionByUserId)
    {
        try {
            /** @var MySQLRepository $repo */
            $repo = $this->userPhoneNumbersManager->getRepository();
            $repo->beginTransaction();

            $userPhoneNumberObject = $this->getUserPhoneNumberObject($userId);

            if (!empty($userPhoneNumberObject)) {
                $this->logger->info('Disabled User ' . $userId . ' for ' . self::CHANNEL_NAME . ' messaging - Released Phone #: ' .
                    $userPhoneNumberObject->phone_number . ' from ' . $this->provider->getProviderName() . ' account');

                $this->userPhoneNumbersManager->delete($userId, $actionByUserId);

                // Don't release the number from Twilio. This will be handled in a cron job that will
                // release the number after a 7 day grace period.
            }

            $repo->commit();
        } catch (\Exception $disableUserException) {
            $repo->rollback();
            $this->logger->critical('Error Disabling ' . self::CHANNEL_NAME . ' Messaging for User ' . $userId . '. With Message: ' . $disableUserException->getMessage());
            return false;
        }
        return true;
    }



    /**
     * Updates the settings for the number with the given channel provider
     * (Uses twilio to reconfigure the Phone Number settings such as "friendlyName" or callback urls)
     * @param  int   $userId ID of the user to update number settings
     * @return array         Array of new settings for phone number
     */
    public function updateNumberForUser($userId)
    {
        $userPhoneNumber = $this->getUserPhoneNumberObject($userId);
        if (empty($userPhoneNumber)) {
            return [];
        }
        return $this->provider->updateNumber($userPhoneNumber->phone_number, $userId);
    }


    /**
     * MESSAGE HANDLING BELOW
     */

    /**
     * Handle an inbound SMS Request. Should log event, record message to appropriate
     * tables, and send any necessary notifications
     * @param  string $provider    The provider to use to handle request i.e. 'twilio'
     * @param  array  $requestData Raw array of POSTed request data
     * @return bool                True on successful processing of message
     */
    public function handleSmsRequest($provider, array $requestData)
    {
        $this->setProvider($provider);
        $data = $this->provider->sanitizeInboundRequestData($requestData);

        // #SF-28204 If not blocked allow
        if ($this->blacklistSmsManager->isBlocked($data['customerPhoneNumber'])) {
            $this->eventService->trackEvent(
                $this->eventService->getEvent(EventService::SF_EVENT_CHANNEL_SMS_BLOCKED),
                'sms',
                null,
                null,
                null,
                null,
                null
            );
        } else {
            /** @var MessageModel $message */
            $message = $this->addMessageToThread(
                $data['userPhoneNumber'],
                $data['customerPhoneNumber'],
                $data['providerMessageId'],
                $data['direction'],
                $data['status'],
                null, // $customerId, unknown in the case of inbound requests
                $data['body'],
                $data['attachments']
            );

            if ($this->provider->isOptOutMessage($data['body'])) {
                $this->handleOptOut($data['userPhoneNumber'], $data['customerPhoneNumber']);
            }

            if ($this->provider->isOptInMessage($data['body'])) {
                $this->handleOptIn($data['userPhoneNumber'], $data['customerPhoneNumber']);
            }

            // Auto response message
            // Find rep number by looking a the thread and sending_user_id
            $userArray = $this->textThreadsManager->getUserArrayFromThreadId($message->text_thread_id);

            // Send the automatic response by sms
            $this->autoresponder->sendAutoResponder($userArray['ID'], $data['customerPhoneNumber'], AutoResponder::TYPE_PHONE);

            // Send the push notification
            $this->sendPushNotificationForMessage($message);
        }

        // Pass blank response back to provider to mark that no further action is needed on their part
        return $this->provider->replyWithBlankResponse();
    }

    /**
     * Handle an inbound SMS No reply Request.
     * @param  string $provider    The provider to use to handle request i.e. 'twilio'
     * @param  array  $requestData Raw array of POSTed request data
     * @return bool                True on successful processing of message
     */
    public function handleSmsNoReplyRequest($provider, array $requestData)
    {
        $this->setProvider($provider);
        $data = $this->provider->sanitizeInboundRequestData($requestData);

        $customerPhoneNumber = $data['customerPhoneNumber'];
        // This is coming from the NoReply number, NOT THE ORIGINAL REP'S NUMBER
        $noReplyPhoneNumber = $data['userPhoneNumber'];

        if ($this->hasNoReplyMessageAlreadySent($customerPhoneNumber)) {
            $this->logger->info(
                sprintf(
                    "Customer [$customerPhoneNumber] has already received an no-reply message",
                    $customerPhoneNumber
                )
            );
            // You can't return an empty response, just return an empty array for now
            // We don't need to throw an exception when there's already a no-reply sms sent
            return [];
        }

        // There are no threads for noreply messages. Take the best guess of the pre/customer and use that locale.
        // If nothing is found, use whatever the default is for the retailer.
        $locale = null;
        /** @var Customer $customer */
        $customers = $this->customersManager->getAll(
            ['phone' => $customerPhoneNumber],
            0,
            1,
            true,
            '-ID' // Sort by ID, as there's a known bug where created will be 0000-00-00 00:00:00. WP stack issue.
        );

        // It's likely the customer won't exist yet, so we'll have to check precustomer.
        // Find the precustomer that matches this phone number, and use their locale.
        if (empty($customers)) {
            /** @var array $precustomer */
            $precustomer = $this->precustomerManager->getAll(
                ['phone' => $customerPhoneNumber],
                0,
                1,
                true,
                '-created_at'
            );

            // If we have results but it should be 1
            if (count($precustomer) > 0) {
                $precustomer = array_shift($precustomer);
                $locale  = $precustomer->locale;
            }
        } else {
            $customer = array_shift($customers);
            $locale = $customer->locale;
        }

        $response = $this->sendBaseRetailerNoReplyMessage(
            ['customer_phone_number' => $customerPhoneNumber],
            $this->translator->trans(
                'api_messaging_sms_no_reply_message',
                [
                    '%retailername%' => $this->configs['retailer.pretty_name'],
                ],
                null,
                $locale
            )
        );

        $this->recordNoReplyMessageSent($customerPhoneNumber);

        return $response;
    }

    /**
     * Verify if a no reply automatic message has been already sent to customer
     * We want to limit cost and avoid spam
     * @param  string  $number Phone number
     * @return boolean
     */
    protected function hasNoReplyMessageAlreadySent($number)
    {
        return $this->cache->exists($this->getNoReplyMessageSentCacheKey($number));
    }

    /**
     * Save in cache (redis) the fact that a customer already received a no reply automatic message
     *
     * @param  string  $number Phone number
     */
    protected function recordNoReplyMessageSent($number)
    {
        $this->cache->set($this->getNoReplyMessageSentCacheKey($number), 'true');
        $this->cache->expire($this->getNoReplyMessageSentCacheKey($number), static::CACHE_NO_REPLY_ALREADY_SENT_TTL);
    }

    /**
     * Get the cache (redis) key for knowing if a customer already received a no reply automatic message
     * @param  string  $number Phone number
     * @return string          Cache key
     */
    protected function getNoReplyMessageSentCacheKey($number)
    {
        $prefix = $this->configs['retailer.name'] . ':' . $this->configs['env'] ;
        return $prefix . ':' . static::CACHE_NO_REPLY_ALREADY_SENT_KEY . ':' . $number;
    }

    /**
     * Send a push notification to the mobile app based on the given $message
     * Will only work for INBOUND REP MODE messages.
     * Rep Outbound  - No Notification, only one person is replying
     * Team Inbound  - No Notification, badge will update automatically
     * Team Outbound - No Notification, polling will update the thread instead
     * @param  MessageModel $message Model representing the last message to get added to the thread
     */
    public function sendPushNotificationForMessage(MessageModel $message)
    {
        if ($message->direction !== MessageModel::DIRECTION_INBOUND || $this->isTeamMode) {
            return;
        }
        $notification = [
            'inapp'   => [
                'sound'        => 'true',
                'alertBox'     => 'false',
                'thread_id'    => $message->text_thread_id,
                'force_inapp'  => 'true',
                'text_message' => $message->body,
                'event_action' => 'new_text_message',
            ],
        ];

        try {
            $userArray = $this->textThreadsManager->getUserArrayFromThreadId($message->text_thread_id);
        } catch (\Exception $queryException) {
            // Push notifications are not critical, so log error and move forward
            $this->logger->critical('Error Querying For User Array From ThreadId (' . self::CHANNEL_NAME . '): ' . $queryException->getMessage());
            $userArray = [];
        }
        if (empty($userArray)) {
            return;
        }

        $locale = $this->multilangService->getLocaleFromUserOrStore($userArray['ID'], $userArray['store']);

        // Will always hit the else for now. In the future store will handle in this way
        $pushUrl = 'pushNotification/firebase/publish';
        if ($this->isTeamMode) {
            $pushUrl .= '/store';
            $notification['store'] = $userArray['store'];
            $notification['message'] = sprintf(
                '%s - %s',
                $this->translator->trans('pn_team_mode_prefix', [], null, $locale),
                $this->translator->trans(self::NOTIFICATION_MESSAGE_NEW_TEXT_MESSAGE, [], null, $locale)
            );
        } else {
            $notification['reps'] = [$userArray['user_login'] => true];
            $notification['message'] = $this->translator->trans(self::NOTIFICATION_MESSAGE_NEW_TEXT_MESSAGE, [], null, $locale);
        }

        try {
            $this->salesfloorApi->insertJSON($pushUrl, $notification);
        } catch (\Exception $firebasePublishException) {
            // Push notifications are not critical, so log error and move forward
            $this->logger->error($this->getChannelName() . ': ' . $firebasePublishException->getMessage());
        }
    }

    /**
     * Add message to new or existing thread. Thread will be identified by the $userPhoneNumber
     * and the $customerPhoneNumber. Attachments will be saved in with the URL provided by the provider
     *
     * Warning: TODO: This is not in a transaction. We would need to refactor parent function to be able to do it.
     *
     * @param  string       $userPhoneNumber     User (rep) phone number in E.164 format
     * @param  string       $customerPhoneNumber From phone number in E.164 format, valid customer or not
     * @param  string       $providerMessageId   Message ID from provider
     * @param  string       $direction           Direction of message 'inbound', 'outbound-api', 'outbound-call', 'outbound-reply'
     * @param  string       $status              Status of the message 'accepted', 'receiving', 'received', ...
     * @param  int          $customerId          OPTIONAL ID of the customer associated
     * @param  string       $body                Body text of message
     * @param  array        $attachments         Array of arrays attachments with 'url'=> '...' entries in each top level entry
     * @param  integer      $sendingUserId       Id of the rep that sent the message. In team mode this is going to identify who sent the message unlike the userId linked the thread
     * @param  string       $source              The request source where this message is created
     * @param  int          $sourceId            The ID of the request which has created this message
     *
     * @return MessageModel                      Saved message model on success
     */
    public function addMessageToThread($userPhoneNumber, $customerPhoneNumber, $providerMessageId, $direction, $status, $customerId = null, $body = null, $attachments = [], $sendingUserId = null, $source = null, $sourceId = null)
    {
        /** @var Thread $thread */
        // Attempt to identify message thread. Create one if none yet
        $thread = $this->textThreadsManager->findOrCreate(
            $userPhoneNumber,
            $customerPhoneNumber,
            ($direction === MessageModel::DIRECTION_INBOUND ? false : true), // isRead
            $customerId
        );

        // Add message to thread
        $message = $this->textMessagesManager->createAndSave(
            $thread->id,
            $providerMessageId,
            $direction,
            $status,
            $body,
            $sendingUserId,
            $source,
            $sourceId
        );

        // Try to match customer if the thread does not have an up to date customer ID
        if (empty($thread->customer_id)) {
            $this->linkThreadToCustomer($thread, $customerPhoneNumber);
        }

        // When you create a "request sms message", you don't
        // If it's a no reply (no sendingUserId), we don't want to update the status
        if (empty($source) && empty($sourceId) && !empty($sendingUserId)) {
            $this->updateRequestStatus($thread, $sendingUserId);
        }

        // Add attachments to message in thread (if any)
        if (!empty($attachments)) {
            $this->textAttachmentsManager->createAndSaveMultiple($message, $attachments);
        }

        if (empty($body) && !empty($attachments)) {
            $body = '[Attachment]';
        }

        // Track inbound text message here
        if (!empty($thread->customer_id)) {
            $this->customerActivityFeedService->trackActivity(
                $thread->customer_id,
                $thread->user_id,
                CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD,
                $thread->id,
                $body,
                CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
                CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD,
                true
            );
        }

        return $message;
    }

    /**
     * Link a customer to the thread if we get only one match
     *
     * @param Thread $thread
     * @param string $customerPhoneNumber
     *
     * @throws MissingRequiredFieldException
     */
    protected function linkThreadToCustomer(Thread $thread, $customerPhoneNumber)
    {
        // Find the customer associated to the rep using the phone number
        /** @var Customer[] $matchingCustomers */
        $matchingCustomers = $this->customersManager->getAll([
            'user_id' => $thread->user_id,
            'phone' => $customerPhoneNumber,
        ], 0, 10);

        $countMatchingCustomers = count($matchingCustomers);
        if ($countMatchingCustomers === 1) {
            // Match the customers if there is only one match
            // Update the customer id in the thread
            $thread->customer_id = $matchingCustomers[0]->ID;
            $this->textThreadsManager->save($thread);
        } elseif ($countMatchingCustomers === 0) {
            // Find the customers associated to the rep using the alternative phone number
            $qb = $this->repository->getQueryBuilder();
            $qb->select(['cm.customer_id']);
            $qb->from(CustomerMeta::TABLE_NAME, 'cm');
            $qb->join('cm', 'sf_customer', 'c', $qb->expr()->andX(
                $qb->expr()->eq('cm.customer_id', 'c.id'),
                $qb->expr()->eq('c.user_id', $thread->user_id)
            ));
            $qb->where($qb->expr()->eq('cm.type', $qb->expr()->literal(CustomerMeta::TYPE_PHONE)));
            $qb->andWhere($qb->expr()->eq('cm.value', $qb->expr()->literal($customerPhoneNumber)));
            $qb->groupBy('cm.customer_id');

            $matchingCustomersAlt = $qb->execute()->fetchAll();

            $countMatchingCustomersAlt = count($matchingCustomersAlt);
            if ($countMatchingCustomersAlt === 1) {
                // Match the customer if there is only one match
                // Update the customer id in the thread
                $thread->customer_id = $matchingCustomersAlt[0]['customer_id'];
                $this->textThreadsManager->save($thread);
            } elseif ($countMatchingCustomersAlt > 1) {
                $this->logger->info('You have ' . $countMatchingCustomersAlt . ' customer with phone ' . $customerPhoneNumber . ' for rep ' . $thread->user_id . '. We can\'t link it');
            }
        } else {
            $this->logger->info('You have ' . $countMatchingCustomers . ' customer with phone ' . $customerPhoneNumber . ' for rep ' . $thread->user_id . '. We can\'t link it');
        }
    }

    /**
     * Handle Fallbacks for inbound SMS Requests
     * For now spits back output that is unused
     * @param  string $provider    Provider name to use to process
     * @param  array  $requestData Data received from request (usually POST)
     * @return string              Output about function and receive $requestData
     */
    public function handleSmsFallbackRequest($provider, array $requestData)
    {
        $this->setProvider($provider);
        $requestData = $this->provider->sanitizeInboundRequestData($requestData);
        return __FUNCTION__ . 'Received ' . json_encode($requestData);
    }

    /**
     * Handle Status for inbound SMS Requests
     * For now spits back output that is unused
     * @param  string $provider    Provider name to use to process
     * @param  array  $requestData Data received from request (usually POST)
     * @return string              Output about function and receive $requestData
     */
    public function handleSmsStatusRequest($provider, array $requestData)
    {
        $this->setProvider($provider);
        $requestData = $this->provider->sanitizeInboundRequestData($requestData);
        return __FUNCTION__ . 'Received ' . json_encode($requestData);
    }

    /**
     * Handle an inbound Voice Requests
     * @param  string $provider    Provider name to use to process
     * @param  array  $requestData Data received from request (usually POST)
     */
    public function handleVoiceRequest($provider, array $requestData)
    {
        $this->setProvider($provider);
        return $this->provider->replyWithDictatedMessage(
            [
                $this->translator->trans('api_messaging_vocal_automatic_message')
            ],
            $this->locale
        );
    }

    /**
     * Handle an inbound Voice Requests
     * @param  string $provider    Provider name to use to process
     * @param  array  $requestData Data received from request (usually POST)
     */
    public function handleVoiceNoReplyRequest($provider, array $requestData)
    {
        $this->setProvider($provider);

        $callerPhoneNumber = $requestData['Caller'];

        if ($this->hasNoReplyMessageAlreadySent($callerPhoneNumber)) {
            return false;
        }

        $response = $this->provider->replyWithDictatedMessage(
            [
                $this->translator->trans('api_messaging_vocal_no_reply_message', [
                    '%retailername%' => $this->configs['retailer.pretty_name'],
                ])
            ],
            $this->locale
        );

        $this->recordNoReplyMessageSent($callerPhoneNumber);

        return $response;
    }

    /**
     * Returns an array that represent the thread with id $threadId
     * A 'messages' subarray of visible messages is included
     * Each message has an 'attachments' subarray that represents visible attachments
     * @param  integer  $threadId      Id of the thread to get messages for
     * @param  integer  $lastMessageId Id of the last visible message, used to offset the query instead of pagination
     * @param  integer $page           Page (pagination)
     * @param  integer $perPage        Max number of results to display
     * @return array                   Array of results
     */
    public function getCompiledMessagesForThread($threadId, $lastMessageId = null, $page = 0, $perPage = 50)
    {
        /** @var Thread $thread */
        $thread = $this->textThreadsManager->getOneOrNull(['id' => $threadId]);
        if (empty($thread)) {
            return [];
        }
        // Threads will have a customer_id if started by a Rep
        // But if started by inbound message we are not (/ cannot always) identifying the associated customer
        if (!empty($thread->customer_id)) {
            /** @var Customer $customer */
            $customer = $this->customersManager->getOneOrNull([CustomerModel::ID_FIELD => $thread->customer_id]);
            $locale = $customer->locale;
        } else {
            // Try to get the pre-customer to get the language
            /** @var PreCustomer $preCustomer */
            $preCustomer = $this->precustomerManager->getOneOrNull([
                'phone' => $thread->customer_phone_number,
            ]);
            $locale = $preCustomer->locale;
        }
        $messageResults = $this->textMessagesManager->getVisibleMessagesInThread($thread->id, $lastMessageId, $page, $perPage);
        if (empty($messageResults)) {
            return [];
        }
        $messageResults = array_reverse($messageResults);

        $threadWithMessages = $thread->toArray(true);
        $messages = [];
        $messageIds = [];
        // Reverse order of messages
        foreach ($messageResults as $result) {
            $messages[$result['id']] = $result;
            $messages[$result['id']]['attachments'] = [];
            $messageIds[] = $result['id'];
        }

        $attachments = $this->textAttachmentsManager->getVisibleAttachmentsForMessageIds($messageIds);
        foreach ($attachments as $attachment) {
            if (isset($messages[$attachment['text_message_id']])) {
                $messages[$attachment['text_message_id']]['attachments'][] = $attachment;
            }
        }

        // Additional fields to return in results
        $threadWithMessages['customer'] = $customer ?? [];
        $threadWithMessages['text_thread_id'] = $thread->id;
        $threadWithMessages['locale'] = $locale;
        $threadWithMessages['messages'] = $messages;

        return $threadWithMessages;
    }

    public function getQueueId()
    {
        return $this->queueId;
    }

    /**
     * Function fetches the needed Enable queue URL and if not found it creates
     * the queue with the appropriate attributes
     * @return string  Enable queue URL to be used to push / pop messages to / from
     */
    private function getQueueUrl()
    {
        // Get the related "Events" queue URL. Create it if necessary
        $queueUrl = $this->queue->getQueueUrl($this->queueId);
        if (!$queueUrl) {
            $queueUrl = $this->queue->createQueue($this->queueId, $this->buildQueueAttributes());
        }
        return $queueUrl;
    }


    /**
     * Push several messages to the SQS Text Enable Queue
     * Messages are used to act on the Users permission to text message
     * All messages will have the same action apply ($enable TRUE / FALSE)
     * @param  array   $userIds        Array of User IDs to enable / disable
     * @param  integer $actionByUserId Admin User ID that is performing this action
     * @param  boolean $enable         True -> Enable Text Messaging, False -> Disable
     */
    public function pushMultiple(array $userIds, $actionByUserId, $enable = true)
    {
        $returns = [];
        foreach ($userIds as $userId) {
            $returns[] = $this->push($userId, $actionByUserId, $enable);
        }
        return $returns;
    }

    /**
     * Push a message to the SQS Text Enable Queue
     * Messages are used to act on the Users permission to text message
     * @param  integer $userId         User ID to enable / disable
     * @param  integer $actionByUserId Admin User ID that is performing this action
     * @param  boolean $enable         True -> Enable Text Messaging, False -> Disable
     */
    public function push($userId, $actionByUserId, $enable = true)
    {
        $payload = $this->serializeSend($userId, $actionByUserId, $enable);
        $queueUrl = $this->getQueueUrl();
        return $this->queue->sendMessage($queueUrl, $payload);
    }

    /**
     * Get Queue object for sending text message
     *
     * @return SendingQueue
     */
    public function getSendingQueue()
    {
        return $this->sendingQueue;
    }

    /**
     * Function puts together a JSON payload string that consists of a formatted
     * array of data that can be used to enable or disable a user's text ability
     * @param  integer $userId         User ID to enable / disable
     * @param  integer $actionByUserId Admin User ID that is performing this action
     * @param  boolean $enable         True -> Enable Text Messaging, False -> Disable
     * @return string                  JSON String to push as messages to SQS Queue
     */
    public function serializeSend($userId, $actionByUserId, $enable = true)
    {
        $message = [
            self::MSG_KEY_USER_ID => $userId,
            self::MSG_KEY_ACTION_BY => $actionByUserId,
            self::MSG_KEY_ENABLE => $enable,
            self::MSG_KEY_RETAILER => $this->retailer,
            self::MSG_KEY_STACK => $this->stack,
            self::MSG_KEY_REQUESTED_AT => gmdate('U'),
        ];

        $strPayload = json_encode($message);
        if ($strPayload === null) {
            throw new \Exception("Unable to queue up 'Text Enable' change message, failure to json encode " . var_export($message, true));
        }
        return $strPayload;
    }

    /**
     * Function will validate a requested polling length time (in seconds).
     * If it is valid (0-20) it will be returned, otherwise the class default is returned.
     * @param  integer $requestedTime Number of seconds you would like message polling to stay open
     * @return integer                Validated number of seconds that will be used in polling
     */
    public function getWaitTimeSeconds($requestedTime = null)
    {
        if (is_numeric($requestedTime) && $requestedTime >= self::QUEUE_MIN_WAIT && $requestedTime <= self::QUEUE_MAX_WAIT) {
            return $requestedTime;
        }
        return $this->waitTimeSeconds;
    }

    /**
     * Pop off and process a message from the SQS Text Enable Queue
     * A message should consist of a body that can be interpreted into
     * a finite Enable or Disable a specific user id from text messaging
     * @param  integer    $waitTimeSeconds Number of seconds to keep request to SQS open
     * @return array|bool                  Array of the data that was received if successfully process
     *                                           False if no message was received
     */
    public function pop($waitTimeSeconds = null)
    {
        $this->lastQueueUrl = $this->getQueueUrl();
        $response = $this->queue->getMessages($this->lastQueueUrl, $this->getWaitTimeSeconds($waitTimeSeconds));

        $messages = $response->getPath('Messages');
        // Expecting 1 message only
        if (isset($messages[0])) {
            // Save the last message handle just in case the execution is suddenly terminated
            // Can be used by the __destruct method to push this message back into the queue
            $this->lastReceiptHandle = $messages[0]['ReceiptHandle'];
            try {
                $data = $this->unserializeSend($messages[0]['Body']);
            } catch (\Exception $unserializeException) {
                $this->queue->deleteMessage($this->lastQueueUrl, $this->lastReceiptHandle);
                $this->lastReceiptHandle = null;
                // Returning true so that the Read Queue loop continues
                return true;
            }
            $method = ($data[self::MSG_KEY_ENABLE] ? 'enableUser' : 'disableUser');
            $ok = $this->$method($data[self::MSG_KEY_USER_ID], $data[self::MSG_KEY_ACTION_BY]);
            if ($ok) {
                // After processing emails correctly we delete the message (acknowledge)
                $this->queue->deleteMessage($this->lastQueueUrl, $this->lastReceiptHandle);
                $this->lastReceiptHandle = null;
                return $data;
            }
            // In case of saving error, we change visibility timeout to allow reprocessing of message later
            // If message has been read a max number of times (numberReceivesBeforeDeadLetter) and returns
            //     to the queue it will be pushed automatically by AWS to the "Dead Letter" queue
            $this->queue->changeMessageVisibility($this->lastQueueUrl, $this->lastReceiptHandle, $this->hideMessage);
            $this->lastReceiptHandle = null;

            // Also throw specific exception that should be caught by the calling function (ReadEventQueue.php)
            // It will know how to handle it (exit the script, restart by supervisor)
            throw new TextEnableQueueException('TextEnableQueue: Possible Database Error', 500);
        }
        return false;
    }

    /**
     * Deciphers the received message body from SQS, which is a JSON string
     * into an array and checks that the required fields are present
     * @param  string $json Message body, should be string in json format
     * @return array        Array created from decoding $json
     */
    public function unserializeSend($json)
    {
        $payload = json_decode($json, true);
        if (!isset($payload)) {
            throw new \Exception("Bad payload; it's not JSON: $json");
        }
        foreach ([self::MSG_KEY_USER_ID, self::MSG_KEY_ACTION_BY, self::MSG_KEY_ENABLE] as $k) {
            if (!isset($payload[$k])) {
                throw new \Exception("Bad payload; it has no '$k' property: $json");
            }
        }
        return $payload;
    }

    /**
     * Function returns the attributes array that is necessary to build the event queue
     * @return array Containing the attributes for the event queue
     */
    private function buildQueueAttributes()
    {
        // Policy for the "Dead Letter" queue. Must specify: where to send "dead" messages
        // And after how many reads (receives) a message / letter can be deamed "dead"
        $redrivePolicy = [
            'deadLetterTargetArn' => $this->queue->getQueueArn($this->getDeadLetterQueueUrl()),
            'maxReceiveCount' => $this->numberReceivesBeforeDeadLetter
        ];
        return [
            'ReceiveMessageWaitTimeSeconds' => $this->waitTimeSeconds,
            'RedrivePolicy' => json_encode($redrivePolicy)
        ];
    }

    /**
     * Function fetches the needed dead letter queue URL and if not found
     * it creates the dead letter queue with the appropriate attributes.
     * @return string Dead Letter queue URL that failed messages fallback to
     */
    private function getDeadLetterQueueUrl()
    {
        $deadLetterQueueUrl = $this->queue->getQueueUrl($this->deadLetterQueueId);
        if (!$deadLetterQueueUrl) {
            $deadLetterQueueUrl = $this->queue->createQueue($this->deadLetterQueueId, $this->buildDeadLetterQueueAttributes());
        }
        return $deadLetterQueueUrl;
    }

    /**
     * Function returns the attributes array that is necessary to build the dead letter event queue
     * @return array Containing the attributes for the dead letterevent queue
     */
    private function buildDeadLetterQueueAttributes()
    {
        return [
            'MessageRetentionPeriod' => ($this->deadLetterRetentionDays * 24 * 60 * 60), // Number of days a message is held in the queue
            'ReceiveMessageWaitTimeSeconds' => 20 // Max # Seconds that a getMessage request can "poll" / wait to get a result
        ];
    }

    /**
     * Function checks whether a given $userId can be enabled for text messaging based
     * on basic rules. For now the rules are that a user must be
     * - Type = Rep
     * - Selling Mode = On
     * - User Status = On
     * - Not a salesfloor_... user
     * @param  integer $userId Id of the user to check if can be enabled
     * @return bool            True if can be enabled, false otherwise
     */
    public function userCanBeEnabled($userId)
    {
        $user = $this->repsManager->getOneOrNull([
            'ID' => $userId,
            'type' => ($this->isTeamMode ? 'store' : 'rep'),
            'user_status' => 1,
            'selling_mode' => 1
        ]);
        // Must be valid user and have a relation to a store
        if (empty($user) || intval($user->store) === 0) {
            return false;
        }

        $salesfloor = 'salesfloor_';
        if (substr($user->user_login, 0, strlen($salesfloor)) === $salesfloor) {
            return false;
        }
        return true;
    }

    /**
     * Function will return the appropriate User ID for the given context.
     * In rep mode, the inputted $userId will be returned unaltered
     * In team mode, the inputted $userId will be translated to the store user's
     * @param  integer $userId Id of the user (usually logged in)
     * @return integer         Id of the user to use in this context
     */
    public function getUserIdForContext($userId)
    {
        return $this->repsManager->getUserIdForContext($this->isTeamMode, $userId);
    }

    /**
     * Send opt-out message if there's no thread for a given $userPhoneNumber - $customerPhoneNumber pair
     *
     * @param string                $userPhoneNumber
     * @param array                 $to
     * @param int                   $userId
     * @param ServiceInterface|null $request
     *
     * @throws \Exception
     */
    protected function sendOptOutMessageIfEligible($userPhoneNumber, $to, $userId, ServiceInterface $request = null)
    {
        $sendOptOutMessageIfEligible = function () use ($userPhoneNumber, $to, $userId, $request) {
            if (!$this->needSendOptOutMessage($userPhoneNumber, $to)) {
                return;
            }

            // Just in case we already have a request (Never happen at the moment)
            if (!empty($request) && !empty($request->locale)) {
                $locale = $request->locale;
            } else {
                // The opt out message shouldn't be related to the locale of the rep

                /** @var Thread $thread */
                $thread = $this->textThreadsManager->getThreadFromUserCustomer($userId, $userPhoneNumber, $to);

                // default locale
                $locale = empty($userId) ? $this->multilangService->getRetailerDefaultLocale() : $this->getDefaultLocaleFromStore($userId);

                // Thread is mandatory
                if (!empty($thread)) {
                    $locale = $this->getLocaleFromThread($thread, $userId);
                } else {
                    // Default when no thread created yet
                    $customerId = $this->recipientService->getCustomerId($to);
                    if ($customerId !== null) {
                        /** @var Customer $customer */
                        $customer = $this->customersManager->getById($customerId);
                        if (!empty($customer) && !empty($customer->locale)) {
                            $locale = $customer->locale;
                        }
                    }
                }
            }

            if ($this->isTeamMode) {
                $body = $this->translator->trans('api_messaging_optout_team', [
                    '%retailerName%' => $this->configs['retailer.pretty_name'],
                ], null, $locale);
            } else {
                $firstName = $this->translator->trans('api_messaging_someone');
                $lastName = '';

                /** @var Rep $user */
                $user = $this->repsManager->getOneOrNull(['id' => $userId]);
                if (!empty($user)) {
                    if (!empty($user->first_name)) {
                        $firstName = $user->first_name;
                    }
                    if (!empty($user->last_name)) {
                        $lastName = $user->last_name;
                    }
                }

                $storeName = $this->repsManager->getStoreNameFromUser($userId);
                if (!empty($storeName)) {
                    $storeName = sprintf(' (%s)', $storeName);
                }
                $body = $this->translator->trans('api_messaging_optout_rep', [
                    '%retailerName%' => $this->configs['retailer.pretty_name'],
                    '%firstName%' => $firstName,
                    '%lastName%' => $lastName,
                    '%storeName%' => $storeName,
                ], null, $locale);
            }

            $this->postToProviderAndSaveOnDb($userPhoneNumber, $to, $body);
        };

        try {
            // Generate a Mutex key for the rep/customer pair
            $mutexKey = 'mutex_send_opt_out_message_' . preg_replace('/[^0-9_]/', '', $userPhoneNumber . '_' . $to['customer_phone_number']);

            // Create the mutex with a timeout of 10s (because some message with an attachment can take more that 3s - the default value - to send)
            $mutex = new PredisMutex([$this->cache], $mutexKey, 10);
            $mutex->synchronized($sendOptOutMessageIfEligible);
        } catch (MutexException $e) {
            $this->logger->error($e);
            // Unable to create the mutex --> default behavior
            $sendOptOutMessageIfEligible();
        }
    }

    /**
     * Send a STOP message to the recipient
     * This is used to re-send a notification to the customer when he's receiving a marketing sms.
     *
     * @param string $userPhoneNumber
     * @param array  $to
     * @param $userId
     */
    public function sendStopMessageAfterLastMessage(string $userPhoneNumber, array $to, $userId)
    {
        if ($this->configs['retailer.storepage_mode']) {  // for team mode.
            $body = $this->translator->trans('api_message_stop_instruction_teammode', [
                '%retailerName%' => $this->retailerPrettyName
            ]);
        } else { // for rep mode.
            $firstName = $this->translator->trans('api_messaging_someone');

            /** @var Rep $user */
            $user = $this->repsManager->getOneOrNull(['id' => $userId]);
            if (!empty($user) && !empty($user->first_name)) {
                $firstName = $user->first_name;
            }
            $body = $this->translator->trans('api_message_stop_instruction_repmode', [
                '%firstName%' => $firstName,
                '%retailerName%' => $this->retailerPrettyName
            ]);
        }

        try {
            // Delay the sending of this message since it's supposed to be linked to the last one (we don't want it to
            // be receive before another message of the queue) - We can change this number later if we need to.
            sleep(10);

            // Send automated message with STOP instruction.
            $this->postToProvider($userPhoneNumber, $to, $body);
        } catch (\Exception $e) {
            $this->logger->error($e);
        }
    }

    /**
     * Get best guess for the locale used to send opt out message to the customer
     *
     * @param Thread $thread
     * @param        $userId
     *
     * @return string
     */
    private function getLocaleFromThread(Thread $thread, $userId)
    {
        // Priority
        // Locale of last request (if any)
        // Locale of the customer (if any)
        // Default locale of the store

        $locale = $this->getLocaleFromTextThreadHistory($thread) ?: $this->getCustomerLocaleFromThread($thread) ?: $this->getDefaultLocaleFromStore($userId);

        // Just in case
        if (empty($locale)) {
            $this->logger->error(
                sprintf(
                    "I can't find any default locale on the store for rep [%d]",
                    $userId
                )
            );
            $locale = $this->configs['retailer.i18n.default_locale'];
        }

        return $locale;
    }

    /**
     * Get default locale from the store (sf_store_locale)
     *
     * @param $userId
     *
     * @return string
     */
    private function getDefaultLocaleFromStore($userId)
    {
        /** @var Rep $rep */
        $rep = $this->getRep($userId);
        return $this->multilangService->getStoreDefaultLocale($rep->store);
    }

    /**
     * Get locale of the customer (from the thread, not the phone number)
     *
     * @param Thread $thread
     *
     * @return null|string
     */
    private function getCustomerLocaleFromThread(Thread $thread)
    {
        $customerId = $thread->customer_id;

        if (empty($customerId)) {
            return null;
        }

        /** @var Customer $customer */
        $customer = $this->customersManager->getById($customerId);

        if (empty($customer) || empty($customer->locale)) {
            return null;
        }

        return $customer->locale;
    }

    /**
     * Get the locale from the thread history using the request made by the customer.
     *
     * This will use the latest request.
     *
     * @param Thread $thread
     *
     * @return null
     * @throws \Exception
     */
    private function getLocaleFromTextThreadHistory(Thread $thread)
    {
        // You can't find the locale, if there's no thread created yet
        if (empty($thread)) {
            return null;
        }

        /** @var Thread $thread */
        $messages = $this->textMessagesManager->getAll(
            [
                'text_thread_id' => $thread->getId(),
                'source'         => '^null',
                'source_id'      => '^null',
            ],
            0,
            1,
            false,
            [
                '-created_at',
            ]
        );

        if (!empty($messages)) {
            /** @var Message $firstRequestMessage */
            $firstRequestMessage = $messages[0];

            $request = $this->textMessagesManager->getRequestFromMessage($firstRequestMessage);
            if (!empty($request) && !empty($request->locale)) {
                return $request->locale;
            }
        }

        // There's no previous "request message", so i can't get the locale on this thread
        return null;
    }

    /**
     * Check if you need to sent the optout text message
     *
     * @param $userPhoneNumber
     * @param $to
     *
     * @return bool
     */
    private function needSendOptOutMessage($userPhoneNumber, $to)
    {
        $filters = [
            'user_phone_number'     => $userPhoneNumber,
            'customer_phone_number' => $to['customer_phone_number'],
        ];

        // NB: The functionality of sending a "how to unsub" message is only being triggered by REP initiated threads / messages
        // In the future, once we expect to have CUSTOMER initiated chains, they will also need to trigger this "how to" message
        $threads = $this->textThreadsManager->getAll($filters, 0, -1, false);

        if (!empty($threads)) {
            // The thread are not only rep initiated anymore, it can come from a request (customer) but created manually
            $newRequest = true;
            foreach ($threads as $thread) {
                /** @var Thread $thread */
                if (!$this->textThreadsManager->isThreadInOptOutState($thread)) {
                    $newRequest = false;
                }
            }

            return $newRequest;
        }

        return true;
    }

    /**
     * Handle sms opt in action
     *
     * @param $userPhoneNumber
     * @param $customerPhoneNumber
     * @throws GlobalException
     * @throws \Exception
     */
    protected function handleOptIn($userPhoneNumber, $customerPhoneNumber)
    {
        /** @var Thread $thread */
        $thread = $this->textThreadsManager->getOneOrNull([
            'user_phone_number'        => $userPhoneNumber,
            'customer_phone_number'    => $customerPhoneNumber,
            Thread::ATTR_IS_SUBSCRIBED => false,
        ], null, false);

        if (!$thread) {
            return;
        }

        $this->textThreadsManager->markSubscribed($thread->id);
        $this->smsBlockListManager->unblock($customerPhoneNumber, $thread->user_id);

        // Set contacts with given default phone as Not Subscribed for sms marketing status
        if (!empty($customerPhoneNumber) && !empty($thread->user_id)) {
            $this->setCustomerSmsMarketingAsNotSubscribed($customerPhoneNumber, $thread->user_id);
        }
    }

    /**
     * Set contacts with given default phone as Not Subscribed for sms marketing status
     *
     * @param $phone
     * @param $userId
     * @throws \Exception
     */
    protected function setCustomerSmsMarketingAsNotSubscribed($phone, $userId)
    {
        $customers = $this->customersManager->getAll(['user_id' => $userId, 'phone' => $phone], 0, -1, false);

        foreach ($customers as $customer) {
            if ($customer->sms_marketing_subscription_flag != Customer::SMS_SUBSCRIPTION_STATUS_UNSUBSCRIBED) {
                continue;
            }
            $this->customersManager->smsMarketingNotSubscribe($customer->ID, CustomerFieldHistory::SOURCE_SMS_OPT_IN);
        }
    }

    /**
     * Handle the customer marketing sms opt-out process
     * Number is added to block list for contacts, every related thead set to Unsubscribed, and all Contacts with that number are downgraded to Non-Subscriber
     *
     * @param string $userPhoneNumber
     * @param string $customerPhoneNumber
     *
     * @throws \Exception
     */
    protected function handleOptOut($userPhoneNumber, $customerPhoneNumber)
    {
        /** @var Thread[] $threads */
        $threads = $this->textThreadsManager->getAll([
            'user_phone_number'        => $userPhoneNumber,
            'customer_phone_number'    => $customerPhoneNumber,
        ], 0, -1);

        //block phone for all threads and change contacts smsMarketingUnsubscribe flag
        foreach ($threads as $thread) {
            if ($thread->{Thread::ATTR_IS_SUBSCRIBED} == true) {
                $this->textThreadsManager->markUnsubscribed($thread->id);
            }
        }

        // beside all threads, block phone for all reps(the rep-contact might even not have thread yet) and change contacts smsMarketingUnsubscribe flag
        // the contact already in block list will be skipped
        // the contact already disable sms_marketing_subscription_flag will be skipped
        $this->smsBlockListManager->blockAllContactsByPhone($customerPhoneNumber, CustomerFieldHistory::SOURCE_SMS_OPT_OUT);
    }

    /**
     * SF-17885 Notify Associate When Text Messaging is Enabled
     * Function inserts a message into the user's SF Mailbox (sf_messages)
     * with the details of the phone number assigned to the user (store_user)
     *
     * @param  integer $userId          User that has just been enabled for text messaging
     * @param  string  $userPhoneNumber User (rep) phone number in E.164 format
     */
    public function notifyUserOnEnable($userId, $userPhoneNumber)
    {
        try {
            // Acquire rep locale, and translate message based on their preference.
            $rep = $this->getRep($userId);

            $messageText = $this->translator->trans(
                'api_messaging_sms_user_activation_complete',
                [ '%userPhoneNumber%' => $userPhoneNumber ],
                null,
                $rep->locale
            );

            $messageParameters = [
                'user_id'       => $userId,
                'owner_id'      => $userId,
                'customer_id'   => (-1),
                'from_type'     => 'user',
                'from_email'    => $this->configs['retailer.emails.no_reply_address'],
                'from_name'     => 'Salesfloor',
                'request_id'    => 0,
                'type'          => 'message',
                'status'        => 'unread',
                'date'          => gmdate('Y-m-d H:i:s'),
                'category'      => 'inbox',
                'last_category' => null,
                'message'       => $messageText,
                'thread_id'     => null, // Can be null, value will be updated with the id later.
                'title'         => $messageText = $this->translator->trans(
                    'api_messaging_sms_user_activation_subject',
                    [],
                    null,
                    $rep->locale
                ),
            ];
            $message = $this->sfMessagesManager->create($messageParameters);
            $this->sfMessagesManager->save($message);

            // Ensure that the thread_id is the same as the ID of the message
            $message->thread_id = $message->ID;
            $this->sfMessagesManager->save($message);
        } catch (\Exception $savingMessageException) {
            $this->logger->critical('Error Inserting "Text Messaging Enabled" SF Message for User ' . $userId . ': ' . $savingMessageException->getMessage());
        }
    }

    /**
     * Update status of all requests that are still in "new" state from the same thread.
     *
     * @param Thread  $thread       Text message thread entity
     * @param integer $userId       Rep/User id
     *
     * @throws MessagingException
     */
    private function updateRequestStatus(Thread $thread, int $userId): void
    {
        $currentThreadId = $thread->getId();

        $this->logger->debug(
            sprintf(
                "Updating Request status (By creating a fake message), if needed, from text thread [%s]",
                $currentThreadId
            )
        );

        // Get all requests linked in this thread, this is not perfect because we loop over each of them everytimes
        // Sadly, the logic (of is it's a new request) is not done directly in a MySQL query here.
        // If we find some performance issue, we could refactor all of this in one query instead
        // (e.g.: Give me all request that are "new state" linked to this sms thread
        $messagesWithRequest = $this->textMessagesManager->getAll(
            [
                'text_thread_id' => $currentThreadId,
                'source'         => '^null',
                'source_id'      => '^null',
            ],
            0,
            -1,
            false,
            [
                '-created_at',
            ]
        );

        /** @var Message $message */
        foreach ($messagesWithRequest as $message) {
            // $message should always have a source && source_id if getAll() is working as expected
            $request = $this->textMessagesManager->getRequestFromMessage($message);

            if (empty($request)) {
                $this->logger->error(
                    sprintf(
                        "This sms message is linked to request type [%s] and request Id [%s] but it's not found. This should not happen.",
                        $message->source,
                        $message->source_id
                    )
                );
                continue;
            }

            // Add a new temp message to this request only if it's a new request
            // Since we never add a customer SMS to the thread, once we append a new message, it will never be
            // in new state again.
            if ($this->isNewRequest($request)) {
                $this->createMessageInRequestThread($request, $userId);
            }
        }
    }

    /**
     * Add a message in sf_messages in the same thread.
     * For request "message" a thread is using request_type and request_id.
     *
     * @param ServiceInterface $request The request itself (question,appointment,personalShopper)
     * @param integer          $userId  The rep/user ID
     *
     * @throws MessagingException
     */
    private function createMessageInRequestThread(ServiceInterface $request, int $userId): void
    {
        /** @var Rep $rep */
        $rep = $this->repsManager->getById($userId);

        if (empty($rep)) {
            throw new MessagingException(
                sprintf(
                    "Can't get locale if userId is not valid [%s]",
                    $userId
                )
            );
        }

        // I can't use the notification system, because it's used only for one specific reason
        // (when no corporate email address, you add a message)

        // This doesn't seems to be displayed, so hardcode a generic title for now
        $title = 'system-message';

        $message = $this->translator->trans(
            'retailer.sms.request.message',
            [],
            null,
            $rep->locale
        );

        $this->sfMessagesManager->addMessageToRequestOrThread(
            $rep,
            $request,
            null,
            true,
            $title,
            $message
        );
    }

    /**
     * Check if the request itself is in "new" status.
     * Sadly, we don't really have a status column for the request. We deduct the state by the type of
     * messages and his content. This is painful and coming from legacy.
     *
     * @param ServiceInterface $request
     *
     * @return bool
     * @throws MessagingException
     */
    private function isNewRequest(ServiceInterface $request): bool
    {
        if ($request instanceof Question) {
            $manager = $this->questionsManager;
        } elseif ($request instanceof Appointment) {
            $manager = $this->appointmentsManager;
        } elseif ($request instanceof PersonalShopperModel) {
            $manager = $this->personalShopperManager;
        } else {
            throw new MessagingException(
                sprintf(
                    "Invalid request type [%s]",
                    get_class($request)
                )
            );
        }

        return $manager->isStatusNew($request);
    }

    /**
     * Check what the phone number carrier type
     * mobile, landline, voip, etc
     *
     * @param $phoneNumber
     * @return bool
     */
    public function lookupPhoneNumberCarrierType($phoneNumber)
    {
        return $this->provider->lookupPhoneNumberCarrierType($phoneNumber);
    }

    /**
     * Lookup and save phone number carrier types
     * Will check DB and if not found will query external service
     * Will update DB with any new phone number carrier types discovered
     *
     * @param array $phoneNumbers [phoneNumber, ...]
     * @return array [phoneNumber => carrierType]
     */
    protected function lookupAndSavePhoneNumberCarrierTypes(array $phoneNumbers): array
    {
        // Check what is stored in db
        $phoneNumberCarrierTypes = $this->phoneNumberDetailsManager->getPhoneNumberCarrierTypes($phoneNumbers);
        $newPhoneNumberCarrierTypes = [];
        foreach ($phoneNumbers as $phoneNumber) {
            if (empty($phoneNumberCarrierTypes[$phoneNumber])) {
                // Fetch the value form external service
                $phoneNumberType = $this->lookupPhoneNumberCarrierType($phoneNumber);
                $phoneNumberCarrierTypes[$phoneNumber] = $phoneNumberType;
                $newPhoneNumberCarrierTypes[$phoneNumber] = $phoneNumberType;
            }
        }

        // Set newly discovered phone carrier types
        if (!empty($newPhoneNumberCarrierTypes)) {
            $this->phoneNumberDetailsManager->savePhoneNumberCarrierTypesInBulk($newPhoneNumberCarrierTypes);
        }

        return $phoneNumberCarrierTypes;
    }

    /**
     * Does carrier type support text messaging?
     *
     * @param string $carrierType
     * @return bool|null
     */
    protected function doesCarrierTypeSupportText(string $carrierType): bool|null
    {
        // NOTE:
        // 1. when a phone number is detected as landline/voip by Twilio, the customer might/could still get message for some carriers/provider
        // 2. when a phone number is not detected as landline/voip by Twilio, in real word it still could be a landline number,
        //    Twilio might not detect it so it is unknown in our side

        $result = null; // unknown carrier type will be null

        if ($carrierType === PhoneNumberDetailsModel::CARRIER_TYPE_MOBILE) {
            $result = true;
        } elseif (in_array($carrierType, self::MSG_NOT_SUPPORTED_PHONE_CARRIER_TYPES)) {
            $result = false;
        }

        return $result;
    }

    /**
     * Check the phone number supports texting
     * Note: results may vary in real life, Twilio detection is not perfect
     *
     * @param string $phoneNumber
     * @return bool|null
     */
    public function checkPhoneNumberSupportsText(string $phoneNumber): bool|null
    {
        $phoneNumberCarrierTypes = $this->lookupAndSavePhoneNumberCarrierTypes([$phoneNumber]);
        $carrierType = $phoneNumberCarrierTypes[$phoneNumber];
        return $this->doesCarrierTypeSupportText($carrierType);
    }

    /**
     * Check if multiple numbers support text
     * Note: results may vary in real life, Twilio detection is not perfect
     *
     * @param array $phoneNumbers
     * @return array [phoneNumber => (bool|null)supportsTexting]
     */
    public function checkPhoneNumbersSupportsText(array $phoneNumbers): array
    {
        $phoneCarrierTypes = $this->lookupAndSavePhoneNumberCarrierTypes($phoneNumbers);

        $results = [];
        foreach ($phoneCarrierTypes as $phoneNumber => $carrierType) {
            $results[$phoneNumber] = $this->doesCarrierTypeSupportText($carrierType);
        }

        return $results;
    }
}
