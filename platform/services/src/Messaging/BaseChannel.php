<?php

namespace Salesfloor\Services\Messaging;

use Monolog\Logger;
use Salesfloor\API\Managers\Reps;
use Salesfloor\API\Managers\Stores;
use Salesfloor\API\Managers\WpOptions;
use Salesfloor\Configs\Configs;
use Salesfloor\Models\Services\ServiceInterface;
use Salesfloor\Services\Messaging\Exceptions\CustomerOptedOutException;
use Salesfloor\Services\Messaging\Exceptions\MessagingException;

/**
 * Base ChannelInterface used to specify the required functionality
 * for any newly implemented Messaging Channel (i.e. Text / Social / Email)
 */
abstract class BaseChannel implements ChannelInterface
{
    const CONFIG_BASE = 'messaging';
    const CHANNEL_NAME = null;

    const MESSAGING_BASE_URL_PUBLIC    = '/messaging/public';
    const MESSAGING_BASE_URL_PROTECTED = '/messaging/protected';
    const MESSAGING_BASE_URL_PRIVATE   = '/messaging/private';

    protected $configs;

    /** @var Logger $logger */
    protected $logger;

    // Array containing instances of each of the possible providers
    protected $allProviders;
    // Pointer to the provider in use. Can be changed with setProvider
    protected $provider;

    protected $retailer;
    protected $stack;

    /** @var \Symfony\Component\Translation\Translator */
    protected $translator;
    /** @var Reps $repsManager */
    protected $repsManager;
    /** @var Stores $storesManager */
    protected $storesManager;
    /** @var WpOptions $wpOptions */
    protected $wpOptions;
    protected $allProvidersArray = [];

    public function __construct($app)
    {
        $this->loadBaseDependencies($app);
        $this->initializeProviders($app);
        $this->pickFirstProvider();
    }

    /**
     * Load the dependencies & store configs needed for all channels
     * Ex: Additional managers needed for Text channel (ThreadsManager)
     * @param  \Silex\Application $app Instance of application
     */
    public function loadBaseDependencies($app)
    {
        $this->configs = $app['configs'];
        $this->logger  = $app['logger'];
        $this->repsManager = $app['reps.manager'];
        $this->storesManager = $app['stores.manager'];
        $this->translator = $app['translator'];

        $this->stack = $app['configs']['env'];
        $this->retailer = $app['configs']['retailers.short'];
        $this->wpOptions = $app['wp_options.manager'];

        // Load the dependcies for the given channel (text/email/social)
        $this->loadDependencies($app);
    }

    /**
     * @inheritdoc
     */
    public function initializeProviders($app)
    {
        foreach ($this->allProvidersArray as $name => $provider) {
            $this->allProviders[$name] = $app[$provider];
        }
    }

    /**
     * @inheritdoc
     */
    public function pickFirstProvider()
    {
        $allProviderNames = array_keys($this->allProvidersArray);
        $this->setProvider($allProviderNames[0]);
    }

    /**
     * @inheritdoc
     */
    public function setProvider($provider)
    {
        if (!isset($this->allProviders[$provider])) {
            $this->throwMessagingException('Invalid provider ' . $provider . ' requested');
        }
        $this->provider = & $this->allProviders[$provider];
        return $this;
    }

    /**
     * @inheritdoc
     */
    public function getProvider()
    {
        return $this->provider;
    }

    /**
     * Returns the channel name for the given channel
     * i.e. Messaging/Text/Channel -> 'text'
     * @return string Name of channel in use
     */
    public function getChannelName()
    {
        return static::CHANNEL_NAME;
    }


    /**
     * Begins the process of sending a message through any channel.
     * Validates base rules and then forwards the message to the given
     * channel to do the actual processing / sending of the message.
     *
     * @param  int             $userId        ID of the rep that is sending the message
     * @param  array           $to            Array containing details of the recipient
     *                                        i.e. phone number / email / contact_id / etc
     * @param  string          $body          Content of the message to send
     * @param  array           $attachments   Array containg the details of the attachments
     *                                        i.e. URL of image, (future) asset / product details
     * @param  integer         $sendingUserId Id of the rep sending the outbound message. In teammode
     *                                        this is going to be used to show which rep sent the message
     * @param boolean          $isToMany      Flag to know if the message is being sent to more than one recipient event
     *                                        if the function takes only one of them as a $to param
     * @param boolean          $isLastMessage Flag to know if the current message is the last one of a series (e.g: 1 text
     *                                        message + 3 attachments, the last one will be the 3rd attachment)
     * @param boolean          $addToDB       In some case, we don't want to add to DB (Notification from services)
     * @param ServiceInterface $request       If the text message is coming from a service
     *
     * @return object|bool On success object (model) representing the message
     *                     Usually Models/Messaging/{Channel}/Message
     *
     * @throws CustomerOptedOutException
     */
    public function sendBaseMessage(
        $userId,
        array $to,
        $body = '',
        array $attachments = [],
        $sendingUserId = null,
        bool $isToMany = false,
        bool $isLastMessage = true,
        $addToDB = true,
        ServiceInterface $request = null
    ) {
        try {
            $message = $this->sendBaseMessageRaw($userId, $to, $body, $attachments, $sendingUserId, $isToMany, $isLastMessage, $addToDB, $request);
        } catch (CustomerOptedOutException $customerOptedOutException) {
            $this->logger->error('Error Sending Message: ' . $customerOptedOutException->getMessage());
            throw $customerOptedOutException;
        } catch (MessagingException $sendMessageException) {
            $this->logger->error('Error Sending Message: ' . $sendMessageException->getMessage());
            return false;
        } catch (\Exception $genericException) {
            $this->logger->error('Error: ' . $genericException->getMessage());
            return false;
        }

        // Debug only and should be removed when ticket is done.
        // https://salesfloor.atlassian.net/browse/AC-217
        if (!empty($body)) {
            $this->logger->error('AC217', [$to, $body]);
        }
        return $message;
    }

    /**
     * This is the same as sendBaseMessage().
     * Sadly, by catching the exception and always considering false, i don't have any way to know what's
     * the source if the error.
     *
     * I could change the main function, but i'm afraid of side effect (exception vs false).
     *
     * See sendBaseMessage for params description.
     *
     * @param                       $userId
     * @param array                 $to
     * @param string                $body
     * @param array                 $attachments
     * @param null                  $sendingUserId
     * @param bool                  $isToMany
     * @param bool                  $isLastMessage
     * @param bool                  $addToDB
     * @param ServiceInterface|null $request
     *
     * @return object
     * @throws MessagingException
     */
    public function sendBaseMessageRaw(
        $userId,
        array $to,
        $body = '',
        array $attachments = [],
        $sendingUserId = null,
        bool $isToMany = false,
        bool $isLastMessage = true,
        $addToDB = true,
        ServiceInterface $request = null
    ) {
        $this->validateBaseSendMessage($userId, $to, $body, $attachments, $this->shouldBypass($request));
        return $this->sendMessage($userId, $to, $body, $attachments, $sendingUserId, $isToMany, $isLastMessage, $addToDB, $request);
    }

    /**
     * Begin the process of sending a message that cannot be replied to.
     *
     * This should be used for confirmation messages from the general system, rather than a specific rep.
     *
     * @param  array           $to      Array containing details of the recipient
     *                                  i.e. phone number / email / contact_id / etc
     * @param  string          $body    Content of the message to send
     * @param  integer         $sendingUserId Id of the rep sending the outbound message. In teammode
     *                                this is going to be used to show which rep sent the message
     * @param boolean          $addToDB If true, will add the message sent to the DB
     * @param ServiceInterface $request If set, will add source/source_id to the text message
     *
     * @return object
     *
     * @throws CustomerOptedOutException
     */
    public function sendBaseRetailerNoReplyMessage(array $to, $body = '', $addToDB = false, ServiceInterface $request = null)
    {
        try {
            $this->validateBaseSendNoReplyMessage($to, $body);
            $message = $this->sendNoReplyMessage($to, $body, $addToDB, $request);
        } catch (CustomerOptedOutException $customerOptedOutException) {
            $this->logger->error('Error Sending Message: ' . $customerOptedOutException->getMessage());
            throw $customerOptedOutException;
        } catch (MessagingException $sendMessageException) {
            $this->logger->error('Error Sending Message: ' . $sendMessageException->getMessage());
            return false;
        } catch (\Exception $genericException) {
            $this->logger->error('Error: ' . $genericException->getMessage());
            return false;
        }

        return $message;
    }

    /**
     * Get the Rep (user) oject represented by the $userId
     * @param  int    $userId ID of the rep to fetch
     * @return object         Models\Rep object
     */
    public function getRep($userId)
    {
        if (is_numeric($userId)) {
            $rep = $this->repsManager->getOne(['ID' => $userId], null, false);
        } else {
            $rep = $this->repsManager->getOne(['user_login' => $userId], null, false);
        }
        return $rep;
    }



    /**
     * Get the Store oject represented by the $storeId
     * @param  int    $storeId ID of the store to fetch
     * @return object         Models\Store object
     */
    public function getStore($storeId)
    {
        return $this->storesManager->getOneOrNull(['store_id' => $storeId]);
    }

    /**
     * Get the Store oject associated to the given $rep
     * $rep can represent a Models\Rep object or an ID
     * @param  mixed  $rep Rep object model or Rep ID value
     * @return object      Models\Store object
     */
    public function getStoreByRep($rep)
    {
        if (is_numeric($rep)) {
            $rep = $this->getRep($rep);
        }
        return $this->getStore($rep->store);
    }

    /**
     * Validate the contents against rules for the ALL Channels
     * @param  int     $userId      ID of the rep that is sending the message
     * @param  array   $to          Array containing details of the recipient
     *                              i.e. phone number / email / contact_id / etc
     * @param  string  $body        Content of the message to send
     * @param  array   $attachments Array containg the details of the attachments
     *                              i.e. URL of image, (future) asset / product details
     * @param bool $skipBlockList check if to validate against a list of blocked items
     * @throws MessagingException   Exception if validation fails
     * @return bool                 True if validation passes
     */
    public function validateBaseSendMessage($userId, array $to, $body = '', array $attachments = [], bool $skipBlockList = false)
    {
        // Check that text messaging is enabled for the retailer + user/rep
        $this->validatePermissions($userId, $to);
        $rep = $this->getRep($userId);
        if (empty($rep)) {
            $this->throwMessagingException('Invalid Rep ID Received');
        }
        return $this->validateSendMessage($userId, $to, $body, $attachments, $skipBlockList);
    }

    public function validateBaseSendNoReplyMessage(array $to, $body = '')
    {
        $this->validateRetailerPermission();
        return $this->validateSendMessage(null, $to, $body, []);
    }

    /**
     * Function will validate that Retailer + User have appropriate permissions
     * to send messages via the given channel
     * @param  int   $userId ID of the rep that is sending the message
     * @param  array $to     Array containing details of the recipient
     *                       i.e. phone number / email / contact_id / etc
     * @return bool          True on passed validations
     */
    public function validatePermissions($userId, array $to)
    {
        return $this->validateRetailerPermission() && $this->validateUserPermission($userId);
    }

    /**
     * Function to throw a new MessagingException
     * @param string $message Descriptive message for the error
     * @thows MessagingException
     */
    public function throwMessagingException($message)
    {
        throw new MessagingException($message);
    }

    /**
     * Check that retailer has permission to send via the given channel
     * @return bool True when retailer is allowing channel sending
     */
    public function validateRetailerPermission()
    {
        if (!$this->getRetailerChannelPermission()) {
            $this->throwMessagingException('Retailer does not support ' . static::CHANNEL_NAME . ' messaging');
        }
        return true;
    }

    /**
     * Return the value of the config for the permission for the retailer to
     * use the given channel. i.e. Fetches the 'messaging.text.enabled' config
     * for saks dev in the appropriate configs
     * @return mixed Value found in the messaging.{channel}.enabled config
     */
    public function getRetailerChannelPermission()
    {
        return $this->getConfig('enabled');
    }

    /**
     * Generic function that fetches the value of messaging channels config
     * with the given $key representing the ending of the config name
     * i.e. $key -> checks 'messaging.{channel}.{$key}''
     * @param  string $key String to append to 'messaging.{channel}.' to compose
     *                     full config key name to look for
     * @return mixed       Value of the requested config
     */
    public function getConfig($key)
    {
        return self::getSpecificConfig($this->configs, static::CHANNEL_NAME, $key);
    }

    /**
     * Function returns the value of the config compiled by the parameters received
     * @param  Configs $configs  Pimple object representing all configurations
     * @param  string  $channel  Name of the channel in use i.e. 'text'
     * @param  string  $key      Final part of the config being searched for i.e. 'enabled'
     * @param  string  $provider OPTIONAL provider name i.e. 'twilio'. When provided
     *                           config will look like 'messaging.{channel}.{provider}.{key}''
     * @return mixed             Value of the requested config
     */
    public static function getSpecificConfig(Configs $configs, $channel, $key, $provider = null)
    {
        $configKey = implode('.', array_filter([static::CONFIG_BASE, $channel, $provider, $key]));
        return ($configs->offsetExists($configKey) ? $configs[$configKey] : null);
    }

    /**
     * Determine if to bypass blockedList
     * @param ServiceInterface|null $request
     * @return bool
     */
    private function shouldBypass(?ServiceInterface $request): bool
    {
        if (!$request) {
            return false;
        }
        return  $request->getBypassBlockList();
    }
}
