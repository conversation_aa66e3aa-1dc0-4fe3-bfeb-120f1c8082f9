<?php

declare(strict_types=1);

namespace Salesfloor\Services\IncomingMail;

use Monolog\Logger;
use Salesfloor\Models\Rep;
use Salesfloor\Services\Util;
use Salesfloor\Services\Proxy;
use Salesfloor\Models\Message;
use Salesfloor\Configs\Configs;
use Salesfloor\Models\Customer;
use Salesfloor\Services\DeepLinks;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\GroupTasks;
use Salesfloor\Services\Image\Image;
use Salesfloor\Models\RejectedEmail;
use Doctrine\DBAL\ArrayParameterType;
use Salesfloor\Services\MobileBuilds;
use Salesfloor\Services\EmailThreads;
use Salesfloor\Services\AutoResponder;
use Salesfloor\Services\NameSuggester;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Services\Sanitize\Sanitize;
use Salesfloor\Exceptions\GlobalException;
use Salesfloor\Services\Mail\EmailBlockList;
use Symfony\Component\Translation\Translator;
use Salesfloor\API\Managers\RejectedEmails;
use Symfony\Component\HttpFoundation\Request;
use Salesfloor\Services\CustomerActivityFeed;
use Salesfloor\Models\Services\ServiceInterface;
use Salesfloor\Services\Event as EventService;
use Salesfloor\Services\IncomingMail\Validator;
use Salesfloor\API\Managers\Reps as RepsManager;
use Symfony\Component\HttpFoundation\HeaderBag;
use Salesfloor\Services\Mail\EmailTemplateFactory;
use Salesfloor\Models\Virtuals\Images\ImageUpload;
use Salesfloor\Services\Moderation\ImageModeration;
use Salesfloor\API\Managers\Stores as StoreManager;
use Salesfloor\Services\IncomingMail\InboundMessage;
use Salesfloor\Services\Proxy as AutoresponderProxy;
use Salesfloor\Services\PushNotifications\PublishQueue;
use Salesfloor\Services\CloudStorage\CloudStorageFactory;
use Salesfloor\Services\IncomingMail\InboundDomainService;
use Salesfloor\Services\Moderation\Moderator\HiveModerator;
use Salesfloor\API\Managers\EmailAttachments\EmailAttachments;
use Salesfloor\API\Managers\Messages\Legacy as MessagesManager;
use Salesfloor\Services\PushNotifications\Service as PushService;
use Salesfloor\API\Managers\Services\Questions as QuestionsManager;
use Salesfloor\API\Managers\Client\Customers\V1 as CustomersManager;
use Salesfloor\Services\IncomingMail\Exceptions\NoRepFoundException;
use Salesfloor\Models\CustomerActivityFeed as CustomerActivityFeedModel;
use Salesfloor\API\Managers\EmailBlock\BlockedIncomingEmailAddresses;
use Salesfloor\Services\IncomingMail\Exceptions\InvalidEmailException;
use Salesfloor\Services\IncomingMail\Exceptions\BlockedEmailException;
use Salesfloor\API\Managers\Services\Appointments as AppointmentManager;
use Salesfloor\Services\IncomingMail\Exceptions\DuplicateEmailException;
use Salesfloor\Services\IncomingMail\Exceptions\CustomerNotFoundException;
use Salesfloor\Services\IncomingMail\Exceptions\InboundEmailParserException;
use Salesfloor\Services\IncomingMail\Exceptions\NoreplyNotificationException;
use Salesfloor\API\Managers\Services\PersonalShopper as PersonalShopperManager;
use Salesfloor\Services\IncomingMail\Exceptions\SpamScoreLimitReachedException;
use Salesfloor\Services\Mail\Exceptions\InvalidEmailTemplateContextException;

/**
 * The InboundParser is the utlimate service that processes
 * all the mails that customers/users are sending to the Webhook mail
 * The service will filter, validate, XSS, logging or dropping the email.
 *
 */
class InboundEmailParser extends Parser
{
    const SPAM_SCORE_LIMIT = 5;

    const NEW_MESSAGE_EVENT = 'new_message';

    /** The name of field to contain the image URL */
    const IMAGE_URL_FIELD = 'cdn';

    /** @var InboundMessage */
    private $inboundMessage;

    /** @var Rep */
    private $rep;

    /** @var Customer */
    private $customer;

    /** @var Deeplinks */
    protected $deepLinks;


    public function __construct(
        protected Configs $configs,
        protected Logger $logger,
        protected NameSuggester $nameSuggester,
        protected Multilang $multilangService,
        protected EventService $eventsService,
        protected BlockedIncomingEmailAddresses $blockedIncomingEmailAddresses,
        protected Validator $validator,
        protected PublishQueue $publishQueueService,
        protected PushService $pushService,
        protected Sanitize $sanitizer,
        protected RejectedEmails $rejectedEmail,
        protected CustomersManager $customersManager,
        protected EmailBlockList $emailBlockList,
        protected CustomerActivityFeed $customerActivityFeed,
        protected CloudStorageFactory $cloudStorageFactory,
        protected Translator $translator,
        protected EmailThreads $emailThreads,
        protected MessagesManager $messagesManager,
        protected AppointmentManager $appointmentManager,
        protected QuestionsManager $questionsManager,
        protected PersonalShopperManager $personalShopperManager,
        protected RepsManager $repsManager,
        protected StoreManager $storeManager,
        protected MySQLRepository $repository,
        protected AutoresponderProxy $autoResponder,
        protected EmailTemplateFactory $emailTemplateFactory,
        protected GroupTasks $serviceGroupTasks,
        protected EmailAttachments $emailAttachmentsManager,
        protected Image $imageService,
        protected ImageModeration $imageModeration,
        protected InboundDomainService $inboundDomainService,
        protected Proxy $repsService,
    ) {
        $mobileBuilds = new MobileBuilds($this->configs, $this->cloudStorageFactory->getClient('mobile_s3_bucket'));
        $this->deepLinks = new DeepLinks($this->configs, $mobileBuilds);
    }


    /**
     * Sendgrid/Mandrill webhook process logic
     *
     * The format of the Sendgrid webhook payload
     * https://docs.sendgrid.com/for-developers/parsing-email/setting-up-the-inbound-parse-webhook
     *
     * @param Request $request
     * @param array $parameters
     * @return void
     */
    public function processIncomingEmail(Request $request): void
    {
        $params = $request->request->all();
        $headers = $request->headers;

        $incomingEmailEvents = $this->parseWebhookRequest(headers: $headers, request: $params);

        foreach ($incomingEmailEvents as $incomingEmailEvent) {
            try {
                $incomingEmailEvent = $this->parseWebhookAttachments($incomingEmailEvent, $request);
                $inboundMessage = $this->parseIncomingEmailEvent($incomingEmailEvent);
                $messageUID = $this->process($inboundMessage);
                $this->logger->info(sprintf(
                    "Incoming email with Message-Id: %s from %s to %s was successfully processed!",
                    $messageUID ?? 'NA',
                    $inboundMessage->getSenderEmail(),
                    $inboundMessage->getTo()
                ));
            } catch (InboundEmailParserException $e) {
                $this->logger->error("IncomingEmailParser: " . $e->getMessage());
                continue;
            } catch (InvalidEmailTemplateContextException $e) {
                $this->logger->error("IncomingEmailParser: " . $e->getMessage());
                continue;
            }
        }
    }

    /**
     * Parses the incoming email event
     * that was passed to us by third-party Email provider (ie:Sendgrid)
     * (to, from, title, subject, etc.)
     *
     * @param array $email
     * @return InboundMessage $message
     */
    public function parseIncomingEmailEvent(array $emailEvent): InboundMessage
    {
        $inboundMessage = new InboundMessage(
            $this->configs,
            $this->sanitizer,
            $this->emailThreads,
            $this->repsManager,
            $this->translator,
            $this->repository,
            $this->storeManager,
            $this->messagesManager,
            $this->serviceGroupTasks
        );

        return $inboundMessage->init($emailEvent);
    }

    /**
     * Overloading the parent function to remove stripslashes
     * Can probably work with it, but let's just optimize it here
     *
     * @param string $events
     * @return array
     */
    public function formatOutput(string $events): array
    {
        if (empty($events)) {
            return [];
        }
        // Mandrill events might be URL encoded
        $sub = substr($events, 0, 100);
        if (urlencode(urldecode($sub)) === $sub) {
            $events = urldecode($events);
        }
        $results = json_decode($events, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error('InboundEmailParser Invalid JSON ' . json_last_error_msg());
        }
        return (array)$results;
    }

    /**
     * Transform the request content to the expected array format for the webhook.
     *
     * @param array $request Content of $_REQUEST, usually
     * @param HeaderBag $headers Headers of the request. Used to differentiate provider.
     * @return array|null
     * @throws InboundEmailParserException
     */
    public function parseWebhookRequest(array $request, HeaderBag $headers): ?array
    {
        if (!$headers->has('user-agent')) {
            throw new InboundEmailParserException('User-Agent header is missing!');
        }
        $userAgent = $headers->get('user-agent');

        if (strpos($userAgent, static::NAME_USER_AGENT_MANDRILL) !== false) {
            if (isset($request['mandrill_events'])) {
                return $this->formatOutput($request['mandrill_events']);
            }
        } elseif (strpos($userAgent, static::NAME_USER_AGENT_SENDGRID) !== false) {
            return $this->parseSendgridRequestToMandrillFormat($request);
        }

        return null;
    }

    /**
     * Process the incoming email
     *
     * @param array $incomingEmail
     * @return string|null Message-Id
     */
    protected function process(InboundMessage $inboundMessage): ?string
    {
        $this->init($inboundMessage);

        $this->dropDuplicateEmail();
        $this->dropInvalidEmail();
        $this->dropBlockedSender();
        $this->dropNoreplyNotifications();
        $this->dropHighScoreSpam();
        $this->dropOrCreateCustomer();

        switch ($this->inboundMessage->getMessageCategory()) {
            case InboundMessage::TO_CUSTOMER:
                $this->deliverMessageToCustomer();
                break;
            case InboundMessage::TO_GROUP_TASKS:
                $this->deliverMessageForGroupTask($this->rep);
                break;
            default:
                $this->deliverMessageToRep($this->rep);
        }
        // at this point we assume the message was delivered
        // and we want to make sure the Rep can reply back
        // to customer
        $this->subscribeCustomerToReplies();

        // Register Message UID to prevent Message duplicates
        return $this->registerMessageUID();
    }
    /**
     * Initialize the Rep and Customer objects
     * from the input data
     * @param InboundMessage
     *
     * @return void
     */
    private function init(InboundMessage $inboundMessage): void
    {
        $this->inboundMessage = $inboundMessage;

        $to = $this->inboundMessage->getTo();
        $from = $this->inboundMessage->getSenderEmail();

        if ($this->inboundMessage->isCustomerDestination($to)) {
            $customerId = $this->inboundMessage->getCustomerFromAddress($to);
            if (empty($customerId)) {
                throw new InboundEmailParserException('CustomerId is missing');
            }
            try {
                $this->rep = $this->getRep($from);
                $this->customer = $this->customersManager->getOne(['id' => $customerId]);
            } catch (\Exception $e) {
                throw new CustomerNotFoundException('Customer not found');
            }
        } else {
            $this->rep = $this->getRep($to);
            $this->customer = $this->getCustomer($from, $this->rep);
        }
    }

    /**
     * Check for Storefront origin domain
     * We need to identify which domains are ours
     * to check for possible spams / special notifications
     *
     * incoming_mail.php line 136
     *
     * @param string $email
     * @return boolean
     */
    private function isOurDomain(string $email): bool
    {
        if (!$email) {
            return false;
        }
        $storeFrontHost = str_replace(
            ['http://', 'https://'],
            '',
            $this->configs['salesfloor_storefront.host']
        );
        [, $addressHost] = explode('@', $email);

        return $addressHost == $storeFrontHost;
    }

    /**
     * Is the email valid in regards to dkim/spf
     * incoming_mail.php line 146
     * @param array $email
     * @return void
     */
    private function dropInvalidEmail(): void
    {
        // Check for the dkim/spf if the
        // email originates from Salesfloor
        $email = $this->inboundMessage->getSenderEmail();
        if (!$this->isOurDomain($email)) {
            return;
        }
        $emailEvent = $this->inboundMessage->getOriginalEmailEvent();

        $dkim = $emailEvent['msg']['dkim'] ?? false;
        if ($dkim && (!$dkim['signed'] || !$dkim['valid'])) {
            throw new InvalidEmailException();
        }

        $spf = $emailEvent['msg']['spf'] ?? false;
        if ($spf && $spf['result'] != 'pass') {
            throw new InvalidEmailException();
        }
    }

    /**
     * check if email is a duplicate
     *
     * @param array $incomingEmail
     * @throws DuplicateEmailException
     * @return void
     */
    private function dropDuplicateEmail(): void
    {
        if ($this->validator->isDuplicate($this->inboundMessage->getOriginalEmailEvent())) {
            throw new DuplicateEmailException();
        }
    }

    /**
     * Is incoming email inside the block list
     *
     * @param array $email
     * @return void
     * @throws BlockedEmailException
     */
    private function dropBlockedSender(): void
    {
        $fromAddress = $this->inboundMessage->getSenderEmail();

        if (!$this->blockedIncomingEmailAddresses->isBlocked($fromAddress)) {
            return;
        }

        $this->eventsService->trackEvent(
            $this->eventsService->getEvent(EventService::SF_EVENT_CHANNEL_MAIL_BLOCKED),
            'finder',
            null,
            null,
            null,
            null,
            null
        );
        throw new BlockedEmailException("This email address is in incoming block list");
    }



    /**
     * Check for the message for noreply address
     *
     * @param array $email
     * @return void
     *
     * @throws NoreplyNotificationException
     */
    private function dropNoreplyNotifications(): void
    {
        $to = $this->inboundMessage->getTo();

        // SF-17902 - Drop email noreply@ and notifications@
        if (in_array(Util::extractLocalPartEmail($to), ['noreply', 'notifications'])) {
            throw new NoreplyNotificationException();
        }
    }

    /**
     * If the spam score is too high just drop the email
     * Spam score is being attached to the email headers by Sendgrid
     *
     * @return void
     */
    private function dropHighScoreSpam()
    {
        if (
            !$this->isRequestOrThreadOrLookbook()
            && $this->inboundMessage->getSpamScore() >= self::SPAM_SCORE_LIMIT
        ) {
            throw new SpamScoreLimitReachedException(sprintf(
                'Spam score too high! Details: %s',
                $this->inboundMessage
            ));
        }
        return;
    }

    /**
     * Drop request if the customer is not found
     * Logic from incoming_mail.php Line #430
     * Only accept messages from addresses that are already in the database.
     * This includes alternate emails, and customers that belong to other users.

     * @param string $fromEmail
     * @return void
     *
     * incoming_mail.php line 476
     * See tag _unless_thread_request_or_lookbook
     *
     * @throws CustomerNotFoundException
     */
    private function dropOrCreateCustomer(): void
    {
        if ($this->customer) {
            return;
        }
        // Skip this check for GroupTasks as it uses sf_retailer_customers
        if ($this->inboundMessage->getMessageCategory() === InboundMessage::TO_GROUP_TASKS) {
            return;
        }
        // the config was renamed and we must keep backward compatibile for now
        $isRejectEmailEnabled = $this->configs['unknown_incoming_email.tracking_and_reject.is_enabled'] ??
            $this->configs['reject_email_tracking.enabled'];
        if (
            $this->isRequestOrThreadOrLookbook()
            || $this->existsAsCustomer()
            || $isRejectEmailEnabled === false
        ) {
            $this->addCustomerIfNotExists();
        } else {
            // Log the rejected email for later review
            $this->trackingAndRejectEmail(RejectedEmail::REJECTED_UNKNOWN_CUSTOMER);
            throw new CustomerNotFoundException('The customer was not found');
        }
    }


    /**
     * This scenario is not usual, but customers may reply
     * to a thread from other emails.
     *
     * @return void
     */
    private function addCustomerIfNotExists()
    {
        if ($this->customer) {
            return;
        }
        $locale = $this->multilangService->getLocaleFromUserOrStore((int)$this->rep->getId(), null);
        $customerType = $this->configs['retailer.storepage_mode'] ?
            Customer::TYPE_CORPORATE : Customer::TYPE_PERSONAL;

        $customer = $this->customersManager->createNewCustomer([
            'userId' => $this->rep->getId(),
            'name' => $this->inboundMessage->getSenderName(),
            'email' => $this->inboundMessage->getSenderEmail(),
            'phone' => null,
            'localization' => $locale,
            'origin' => Customer::ORIGIN_UNKNOWN,
            'type' => $customerType,
        ]);
        $this->customer = $customer;
    }

    /**
     * Todo: when message is being send from Rep to Customer
     * the customer_id is -1 in the sf_messages
     * Read the commend at sf-messages:1942
     *
     * @return void
     */
    protected function deliverMessageToCustomer(): void
    {
        $payload = [
            'from_type'         => Message::FROM_TYPE_USER,
            'type'              => 'message',
            'status'            => 'read',
            'category'          => 'sent',
            'user_id'           => $this->rep->getId(),
            'customer_ids'      => [$this->customer->getId()],
            'from_name'         => $this->inboundMessage->getSenderName(),
            'from_email'        => $this->inboundMessage->getSenderEmail(),
            'title'             => $this->inboundMessage->getSubject(),
            'message'           => $this->inboundMessage->getFilteredContent(),
        ];
        if ($this->inboundMessage->getRequest()->getId()) {
            $payload['request_id'] = $this->inboundMessage->getRequest()->getId();
            $payload['request_type'] = $this->inboundMessage->getRequest()->getType();
        } else {
            $payload['thread_id'] = $this->inboundMessage->getThreadId();
        }
        // Replicates the sf-messages.php line #587
        if (
            $this->customer->subcribtion_flag == -1
            || $this->customer->subcribtion_flag == Customer::SUBSCRIPTION_STATUS_UNSUBSCRIBED
        ) {
            return;
        }
        $scrubbed = $this->emailBlockList->scrubEmailsArray([$this->customer->email], $payload);
        if (empty($scrubbed['valid'])) {
            return;
        }
        // Replicates the sf_messages line #1068
        $message = $this->messagesManager->addMessageToRequestOrThread(
            $this->rep,
            $this->inboundMessage->getRequest(),
            $this->customer,
            true,
            $this->inboundMessage->getSubject(),
            $this->inboundMessage->getFilteredContent(),
            null,
            $this->inboundMessage->getThreadId()
        );

        $locale = $this->multilangService->getLocaleFromUserOrStore((int)$this->rep->getId(), null);
        $this->sendEmail($message, $locale);
    }

    /**
     * Save message into sf_messages
     *
     * @param Rep $rep
     * @param integer $customerId
     * @param integer|null $threadId
     * @param [type] $request
     * @param string $message
     * @param string $subject
     * @return int
     */
    protected function saveAndTrackMessage(InboundMessage $inboundMessage): int
    {
        if (!$this->customer) {
            throw new \Exception(sprintf('Customer was not found. Details: %s', $inboundMessage));
        }
        $message = $this->messagesManager->addMessageToRequestOrThread(
            $this->rep,
            $inboundMessage->getRequest(),
            $this->customer,
            $inboundMessage->getMessageCategory() !== InboundMessage::TO_REP, // false when the message is for Rep
            $inboundMessage->getSubject(),
            $inboundMessage->getFilteredContent(),
            null,
            $inboundMessage->getThreadId(),
            $inboundMessage->getUploadedFiles(self::IMAGE_URL_FIELD), // field from ImageUpload
        );

        $threadId = $message->thread_id ?? null;
        $requestType = $inboundMessage->getRequest()->getType();
        $activityType = $this->getActivityType($requestType);
        $eventThreadId = ($activityType == CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD) ?
            $threadId : $inboundMessage->getRequest()->getId();

        $this->customerActivityFeed->trackActivity(
            $this->customer->getId(),
            $activityType,
            $eventThreadId,
            $inboundMessage->getFilteredContent(),
            CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );
        return (int) $threadId;
    }

    /**
     * Send email from the Email Template
     *
     * @param InboundMessage $inboundMessage
     * @return void
     */
    protected function sendEmailNotification(InboundMessage $inboundMessage): void
    {
        $replyEmail = $this->emailThreads->proxyCustomerAddress(
            $this->customer?->getId(),
            $this->inboundMessage->getRequest()->getType(),
            $this->inboundMessage->getRequest()->getId(),
            $inboundMessage->getThreadId()
        );

        $context = [
            'fromName' => $inboundMessage->getSenderName(),
            'fromEmail' => $replyEmail,
            'subject' => $inboundMessage->getSubject(),
            'text' => $inboundMessage->getTextContent(),
            'html' => $inboundMessage->getFilteredContent(),
            'to' => $this->rep->user_email,
            'replyEmail' => $replyEmail,
            'request' => $inboundMessage->getRequest(),
            'threadId' => $inboundMessage->getThreadId(),
            'deepLinks' => $this->deepLinks,
            'message' => $inboundMessage->getFilteredContent(),
            'userId' => $this->rep->getId(),
            'rep' => $this->rep,
        ];
        $template = $this->emailTemplateFactory->build('NewMessage', $context, true);
        $template->sendEmail();
    }

    /**
     * Get Rep associtated with the communication
     *
     * @param string|null $email
     * @return Rep $rep|null
     */
    private function getRep(?string $email = null): ?Rep
    {
        if ($this->rep) {
            return $this->rep;
        }
        if ($email) {
            return $this->findRep($email);
        }
    }

    /**
     * The message is destined for Rep
     *
     * @param Rep $rep
     * @return void
     */
    protected function deliverMessageToRep(Rep $rep): void
    {
        $this->uploadAndModerateAttachments($this->inboundMessage);

        $threadId = $this->saveAndTrackMessage($this->inboundMessage);

        $this->trackAttachments($this->inboundMessage->getUploadedFiles(), $threadId);

        if ($this->isNotifiable($this->rep)) {
            $this->sendEmailNotification($this->inboundMessage);
        }

        // if a new thread or new request
        if ($threadId) {
            $message = 'pn_you_received_new_customer_email';
            $inapp = [
                'event_action' => self::NEW_MESSAGE_EVENT,
                'request_id' => $threadId,
                'alertBox' => 'false'
            ];
            if ($rep->type === RepsManager::USER_TYPE_REP) {
                $this->publishQueueService->publishToReps([$rep->getId()], $message, $inapp);
            } else {
                $this->pushService->publishToStoreRepsLocalized($rep->getId(), $message, [], $inapp);
            }
        }
        $this->autoResponder->sendAutoResponder($rep->getId(), $this->inboundMessage->getTo(), AutoResponder::TYPE_EMAIL);
    }


    /**
     * When the message is related to a GroupTask
     * call the service to deliver it to Rep
     * @param Rep $rep
     * @return void
     */
    protected function deliverMessageForGroupTask(Rep $rep): void
    {
        $groupTaskId = $this->inboundMessage->getGroupTaskId();

        $qb = $this->repository->getQueryBuilder();
        $qb->select('count(*) as total')
            ->from('sf_group_tasks', 'gt')
            ->where('gt.id = :groupTaskId')
            ->setParameter('groupTaskId', $groupTaskId)
            ->setMaxResults(1);

        $groupTask = $this->repository->executeCustomQuery($qb);
        if (empty($groupTask[0]['total'])) {
            $this->trackingAndRejectEmail(RejectedEmail::REJECTED_UNKNOWN_GROUP_TASK);
            throw new GlobalException('The groupTask was not found');
        }
        $qb->select('count(*) as total')
            ->from('sf_retailer_customers', 'rc')
            ->where('rc.email = :email')
            ->setParameter('email', $this->inboundMessage->getSenderEmail())
            ->setMaxResults(1);

        $retailerCustomer = $this->repository->executeCustomQuery($qb);
        if (empty($retailerCustomer[0]['total'])) {
            $this->trackingAndRejectEmail(RejectedEmail::REJECTED_UNKNOWN_RETAILER_CUSTOMER);
            throw new CustomerNotFoundException('The customer was not found');
        }
        $this->serviceGroupTasks->processReplyEmailFromCustomer(
            $groupTaskId,
            (int)$rep->getId(),
            $this->inboundMessage->getSubject(),
            $this->inboundMessage->getFilteredContent()
        );
    }

    /**
     * Get the Rep information based on the email address
     * Please note that $to should be parsed by unproxyAddress first
     *
     * @param string $email
     * @return Rep $rep|null
     *
     * @throws NoRepFoundException
     */
    private function findRep(string $email): ?Rep
    {
        // Search by Rep email
        $rep = $this->repsManager->getOneOrNull(['user_email' => $email]);
        if ($rep) {
            return $rep;
        }
        $username = explode('@', $email)[0];

        // Search by Reps login
        $rep = $this->repsManager->getOneOrNull(['user_login' => $username]);
        if ($rep) {
            return $rep;
        }

        // Search by Reps Alias
        $rep = $this->repsManager->getOneOrNull(['user_alias' => $username]);
        if (!$rep) {
            throw new NoRepFoundException('No rep was found for ' . $email);
        }
        return $rep;
    }

    /**
     * Not a request or a Email thread or Lookbook
     *
     * @return boolean
     */
    private function isRequestOrThreadOrLookbook(): bool
    {
        return (
            $this->inboundMessage->getRequest()->getId()
            || $this->inboundMessage->getThreadId()
            || $this->inboundMessage->getIsLookbookEmail()
        );
    }

    /**
     * Add email to rejected database for reporting
     *
     * @param string $reason
     * @return void
     */
    private function trackingAndRejectEmail(string $reason): void
    {
        $data = $this->rejectedEmail->create([
            'from_email' => $this->inboundMessage->getSenderEmail(),
            'to_email'   => $this->inboundMessage->getTo(),
            'subject'    => $this->inboundMessage->getSubject(),
            'spam_score' => $this->inboundMessage->getSpamScore(),
            'message'    => $this->inboundMessage->getHtmlContent(),
            'reject_reason' => $reason,
        ]);

        $this->rejectedEmail->save($data);
    }


    /**
     * Get the type for the Activity Tracking
     *
     * @param string|null $requestType
     * @return int
     */
    private function getActivityType(?string $requestType): int
    {
        switch ($requestType) {
            case ServiceInterface::TYPE_PERSONAL_SHOPPER:
                return CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST;
            case ServiceInterface::TYPE_QUESTION:
                return CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST;
            case ServiceInterface::TYPE_APPOINTMENT:
                return CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST;
            default:
                return CustomerActivityFeedModel::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD;
        }
    }

    /**
     * Send email to the customer
     *
     * @param Message $message
     * @param string|null $locale
     * @return void
     */
    private function sendEmail(Message $message, string $locale): void
    {
        $messageId = $message->thread_id ?? null;
        $context = [
            'messageId' => $messageId,
            'locale' => $locale,
            'userId' => $this->rep->getId(),
        ];

        $this->emailTemplateFactory
            ->build('Publisher\\Messages', $context)
            ->sendEmail();
    }

    /**
     * Get customer ID from sf_customer
     * This is migrated from incoming_mail.php line 340
     *
     * @param string|null $fromEmail
     * @param Rep|null $rep
     *
     * @return Customer|null $customer
     */
    private function getCustomer(?string $fromEmail = null, ?Rep $rep = null): ?Customer
    {
        if ($this->customer) {
            return $this->customer;
        }
        if (!($fromEmail && $rep)) {
            return null;
        }
        $qb = $this->repository->getQueryBuilder();

        $getStoreUser = function () use ($rep) {
            $storeUser = $this->configs['retailer.storepage_mode'] ?
                $this->repsManager->getUserIdFromContext($rep->ID) : null;

            /**
             * Legacy code returning -1 when store user is undefined.
             * incoming_mail.php Line 465; $storeUserId = -1;
             */
            return $storeUser ?? -1;
        };
        // Get the first match
        $qb->select('ID')
            ->from('sf_customer', 'c')
            ->where('c.email = :email')
            ->andWhere('c.user_id IN (:userIds)')
            ->setParameter('email', $fromEmail)
            ->setParameter('userIds', [$rep->ID, $getStoreUser()], ArrayParameterType::STRING)
            ->setMaxResults(1)
        ;
        $customer = $this->repository->executeCustomQuery($qb);

        $getCustomerMatch = function ($customerId) {
            return $this->customersManager->getOne(['id' => $customerId]);
        };
        // Get customer as an object
        if ($customer) {
            return $getCustomerMatch($customer[0]['ID']);
        }
        // Look in anonymous un-assigned users
        $qb->select('ID')
            ->from('sf_customer', 'c')
            ->where('email = :email')
            ->andWhere('user_id = 0')
            ->setParameter('email', $fromEmail)
        ;
        $customer = $this->repository->executeCustomQuery($qb);

        return $customer ? $getCustomerMatch($customer[0]['ID']) : null;
    }

    /**
     * Automatically subscribe the Customer to
     * 1-to-1 communications so the Rep can reply back
     * when customer is fully unsubscribed
     * Added by PS-5161
     *
     * @return bool
     */
    private function subscribeCustomerToReplies(): bool
    {
        if (!$this->customer) {
            return false;
        }

        return $this->customersManager->allowEmailCommunicationForCustomer($this->customer->getId());
    }

    /**
     * Only accept messages from addresses that are already in the database.
     * This includes alternate emails, and customers that belong to other users.
     *
     * @return bool
     */
    private function existsAsCustomer(): bool
    {
        $fromEmail = $this->inboundMessage->getSenderEmail();
        if (!$fromEmail) {
            return false;
        }
        $qb = $this->repository->getQueryBuilder();
        $qb->select('COUNT(*) as total')
            ->from('sf_customer', 'c')
            ->where('c.email = :email')
            ->setParameter('email', $fromEmail)
            ->setMaxResults(1);
        $result = $this->repository->executeCustomQuery($qb);

        if ($result[0]['total']) {
            return true;
        }

        $qb = $this->repository->getQueryBuilder();
        $qb->select('COUNT(*) as total')
            ->from('sf_customer_meta', 'cm')
            ->where('cm.type = "email"')
            ->andWhere('value = :email')
            ->setParameter('email', $fromEmail)
            ->setMaxResults(1);
        $result = $this->repository->executeCustomQuery($qb);

        if ($result[0]['total']) {
            return true;
        }

        return false;
    }

    /**
     * When Rep receives a message inside the Hub
     * this will decide if the Rep will receive an email notification
     * @param Rep $rep
     * @return bool
     */
    private function isNotifiable(Rep $rep): bool
    {
        try {
            // Don't send notification if Rep is disabled
            if ($rep->user_status != 1) {
                return false;
            }
            // Don't send email notification for Lookbook
            if ($this->inboundMessage->getIsLookbookEmail()) {
                return false;
            }
            // or if not eligible for email notification
            if (!$this->repsManager->isEligibleForEmailNotification((int)$rep->getId())) {
                return false;
            }
            // don't send to our own domain
            [, $domain] = explode('@', strtolower($rep->user_email));
            if ($domain === $this->configs['retailer.host']) {
                return false;
            }
            if ($this->inboundDomainService->hasDomain($domain)) {
                return false;
            }
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    "An error occurred inside %s  and the notification message was suppressed. Error %s",
                    __METHOD__,
                    $e->getMessage()
                )
            );
            // We must assume that the email is not
            // notifiable in case of any erorrs.
            return false;
        }
        return true;
    }

    /**
     * Get the attachments sent by customer
     *
     * @param array $incomingEmailEvent
     * @param Request $request
     *
     * @return array $results
     */
    private function parseWebhookAttachments(array $incomingEmailEvent, Request $request)
    {
        $params = $request->request->all();

        $incomingEmailEvent['attachments'] = [
            'count' => $params['attachments'] ?? 0,
            'info' => $params['attachment-info'] ?? [],
            'files' => $request->files,
        ];

        return $incomingEmailEvent;
    }

    /**
     * Moderate and upload attachments received from the Customers
     * The number of attachments passed to the Webhook
     * is controlled by the max_file_uploads in the PHP ini
     *
     * @param \Salesfloor\Services\IncomingMail\InboundMessage $inboundMessage
     * @return void
     */
    private function uploadAndModerateAttachments(InboundMessage $inboundMessage)
    {
        $attachmentFiles = $inboundMessage->getAttachmentFiles()->all();
        if (!$attachmentFiles) {
            return;
        }
        $uploadedFiles = [];
        foreach ($attachmentFiles as $attachmentFile) {
            if (!$attachmentFile instanceof \SplFileInfo) {
                continue;
            }
            $filePath = $attachmentFile->getPathname();
            // The Sengrid is causing some of the input file to come empty.
            // The structure is there, but the filepath is empty.
            if (!$filePath) {
                $this->logger->error(sprintf("Attachment file has empty path. File: %s", print_r($attachmentFile, true)));
                continue;
            }

            if ($this->configs['retailer.moderation.image.is_enabled']) {
                $moderationModel = $this->imageModeration->moderate(
                    $filePath,
                    (int)$this->rep->getId(),
                    Image::ORIGIN_EMAIL,
                    null,
                    ['imageFormat' => HiveModerator::IMAGE_BINARY_FORMAT]
                );
                if ($moderationModel->is_moderator_flagged) {
                    continue;
                }
            }

            $imageUpload = new ImageUpload();
            $imageUpload->setFields([
                'origin' => Image::ORIGIN_EMAIL,
                'source' => $filePath,
            ]);
            $result = $this->imageService->upload($imageUpload);

            $uploadedFiles[] = $result;
        }
        $inboundMessage->setUploadedFiles($uploadedFiles);
    }

    /**
     * Save the attachments to sf_email_attachments table
     *
     * @param array $imageUploads
     * @param int $threadId
     * @return void
     */
    private function trackAttachments(array $imageUploads, int $threadId)
    {
        foreach ($imageUploads as $imageUpload) {
            $model = $this->emailAttachmentsManager->create([
                'url' => $imageUpload->destination,
                'thread_id' => $threadId,
                'customer_id' => $this->customer->getId(),
                'user_id' => $this->rep->getId(),
            ]);
            $this->emailAttachmentsManager->save($model);
        }
    }

    /**
     * Add Message UID to Redis for later duplicates checks
     *
     * @return string|null
     */
    private function registerMessageUID(): ?string
    {
        return $this->validator->registerMessageUID(
            $this->inboundMessage->getOriginalEmailEvent()
        );
    }
}
