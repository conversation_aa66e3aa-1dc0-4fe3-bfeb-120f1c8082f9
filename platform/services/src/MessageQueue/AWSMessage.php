<?php

declare(strict_types=1);

namespace Salesfloor\Services\MessageQueue;

use Aws\Result;
use Guzzle\Service\Resource\Model;
use Salesfloor\Services\Exceptions\NotImplementedException;

/*
 * The AWSMessage might be used using array access example
 * $messages['Messages'] or $messages->getPath['Messages']
 * we have to support ArrayAccess and getPath logic
 */
class AWSMessage implements \ArrayAccess, MessageInterface
{
    /**
     * @var Model $data
     *
     * Example of $data field
     * (
     * [structure:protected] =>
     * [data:protected] => Array
     *    (
     *         [ResponseMetadata] => Array
     *            (
     *                [RequestId] => 9419d00d-392e-5378-873a-87afd68f480a
     *            )
     *
     *          [Messages] => Array
     *            (
     *               [0] => Array
     *                   (
     *                      [MessageId] => ce0436a3-034c-45cb-84f6-6af66c49373b
     *                      [ReceiptHandle] => AQEBB63P5vA/F7Nrl0wTkO5Dy4ZamL6uXDrar53mgaF1cynHF441Mmx0pmJQ2bkGmKlotekPQPboNAfC0sL5JniF8ohJSrm8RkziVrNEHSvqLE2wJjmWNk7E4azkqYzZYnmvFbiXjn0Uoo/8R3LI3NSCy9I+jgoXiq/MUMnH/7Lf33eneXXlzCEhCf7ai6bpvX1YkjgX1TmNwANxa5h2dAOrsjdwgXBCa7fp/5Mqe95Q1bD4ZQn3+45hIWvuDaqLl1R3+EbTQe4u0AR+8Wq6gyQVoMqFQnLlzDTrwbNbQxDjc4cSVp09JtrBS9SgZ3sFfde+SSojzhWnVEfBgkjSodX7ZzVECYL1u/ycrVjt2a9GHzCdtyEsFHBO2bAveLtBElbeEhFWky9D68spLYvj5j3gcw==
     *                      [MD5OfBody] => f44037dfa55aa88acd6da70134312602
     *                      [Body] => {"action":"SIDEBAR_MOBILE_VIEW","fingerprint":"aa3sfwa3aafa33f"}
     *                  )
     *            )
     *   )
     * )
     */
    private $data;

    public function __construct(Result $data)
    {
        $this->data = $data;
    }

    /**
     * @param string $path
     *
     * @return array
     */
    public function getPath($path)
    {
        return $this->data->getPath($path);
    }

    /**
     * Array Access to update an element
     * not used for now, we do not expect
     * to update elements from the AWS Response
     *
     * @param mixed $key
     * @param mixed $value
     * @throws NotImplementedException
     */
    public function offsetSet($key, $value): void
    {
        // not implemented, do not allow override of data ..
        /*
         * Example:
             * if (is_null($key)) {
             *   $this->storage[] = $value;
             * } else {
             *    $this->storage[$key] = $value;
         * }
         */
        throw new NotImplementedException('Not implemented, messages are coming from 3rd party services and should be modified');
    }

    /**
     * Array Access to remove element
     * not used for now, we do not expect
     * to update elements from the AWS Response
     *
     * @param mixed $key
     * @throws NotImplementedException
     */
    public function offsetUnset($key): void
    {
        throw new NotImplementedException('Not implemented, messages are coming from 3rd party services and should be modified');
    }

    /*
     * Array Access offsetExists function
     */
    public function offsetExists($key): bool
    {
        return isset($this->data[$key]);
    }

    /*
    * Array Access and return object
    */
    public function offsetGet($key): mixed
    {
        return $this->data[$key];
    }
}
