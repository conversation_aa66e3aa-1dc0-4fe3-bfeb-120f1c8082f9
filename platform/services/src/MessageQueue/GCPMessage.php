<?php

namespace Salesfloor\Services\MessageQueue;

use Salesfloor\Services\Exceptions\NotImplementedException;

/**
 * GCPMessages class to be compatible with current SQS AWS Messages get Functionalities to limit code changes.
 *
 * It might be used using array access example
 * $messages['Messages'] or $messages->getPath['Messages']
 * we have to support ArrayAccess and getPath logic
 */
class GCPMessage implements \ArrayAccess, MessageInterface
{
    /** @var array $data */
    private $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function getPath($path)
    {
        switch ($path) {
            case 'ReceiptHandle':
            case 'receiptHandle':
                if (isset($this->data[0])) {
                    return $this->data;
                }
                return false;
            case 'body':
            case 'Body':
                if (isset($this->data[0])) {
                    // we return an array of 1 element always;
                    return $this->data[0]->getCustomData();
                }
                return;
            case 'Messages':
                if (isset($this->data[0])) {
                    // we return an array of 1 element always;
                    return [$this->data[0]->getCustomData()];
                }
                break;
            case 'Attributes':
                if (isset($this->data[0])) {
                    return $this->data[0]->attributes();
                }
                break;
        }
        return [];
    }

    /**
     * Array Access to update an element
     * not used for now, we do not expect
     * to update elements from the GCP Response
     *
     * @param mixed $key
     * @param mixed $value
     * @throws NotImplementedException
     */
    public function offsetSet($key, $value): void
    {
    // not implemented, do not allow override of data ..
    /*
     * Example:
         * if (is_null($key)) {
         *   $this->storage[] = $value;
         * } else {
         *    $this->storage[$key] = $value;
     * }
     */
        throw new NotImplementedException('Not implemented, messages are coming from 3rd party services and should be modified');
    }

    /**
     * Array Access to remove element
     * not used for now, we do not expect
     * to update elements from the AWS Response
     *
     * @param mixed $key
     * @throws NotImplementedException
     */
    public function offsetUnset($key): void
    {
        throw new NotImplementedException('Not implemented, messages are coming from 3rd party services and should be modified');
    }

    public function offsetExists($key): bool
    {
        switch ($key) {
            case 'Messages':
            case 'Body':
            case 'body':
            case 'ReceiptHandle':
            case 'receiptHandle':
            case 'Attributes':
                return true;
        }

        // TODO: Should we log if we try to use another
        return false;
    }

    public function offsetGet($key): mixed
    {
        return $this->getPath($key);
    }
}
