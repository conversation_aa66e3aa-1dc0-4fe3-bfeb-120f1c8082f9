<?php

declare(strict_types=1);

namespace Salesfloor\Services\MessageQueue;

interface MessageInterface
{
     /**
     * Get the message property
     *
     * @param string $xmlPath An XML Path to gather ie: Messages/0/X
     *                        though we only support 1st level ie getPath('Messages')
     *                        since currently the App did not uses more complex patterns
     *
     * @return array Resulting Object
     */
    public function getPath($xmlPath);

    /**
     * Get the data property.
     * Right now, it's used to get the data from the message for test purpose only.
     */
    public function getData();
}
