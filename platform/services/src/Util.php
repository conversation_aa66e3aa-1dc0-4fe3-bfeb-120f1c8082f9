<?php

namespace Salesfloor\Services;

use DateInterval;
use DatePeriod;
use Silex\Application;
use Salesfloor\Services\Sanitize\Sanitize as SanitizeService;
use Doctrine\DBAL\Query\QueryBuilder;

class Util
{
    private $app;

    /**
     * The counter for how many debug files have been generated
     * This is to be used to add a number in the filename to indicate sequence of generation and progression of events
     * @var int
     */
    private static $debugFileSequenceCounter = 0;

    public function __construct($app)
    {
        $this->app = $app;
    }

    public static function uuid()
    {
        return sprintf(
            '%04x%04x%04x%04x%04x%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    public static function getRandomFilename($prefix = '')
    {
        return "/tmp/$prefix" . Util::uuid();
    }

    /**
     * Limit image to max file size
     *
     * @param $imagePath path to image
     * @param $maxFileSize maximum file size
     * @return resource
     * @throws \Exception
     */
    public function limitImageFileSize($imagePath, $maxFileSize)
    {
        // Make sure to have a buffer
        $maxFileSize = $maxFileSize * 0.75;

        // Get values from the file
        list($width, $height) = getimagesize($imagePath);
        $fileSize = filesize($imagePath);
        $imageTypeCode = exif_imagetype($imagePath);

        // Create image from file
        switch ($imageTypeCode) {
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($imagePath);
                break;
            default:
                throw new \Exception('Invalid Image Format');
        }

        // If larger than max file size -> resize
        if ($fileSize > $maxFileSize) {
            // Calculate ratios and area
            $widthRatio = $width / $height;
            $sizeRatio  = $maxFileSize / $fileSize;
            $fileArea   = $width * $height;

            // Calculate new dimensions assuming the file size scales linearly with area
            // It does for the most part but amount of colors also plays a role
            $newArea   = $sizeRatio * $fileArea;
            $newHeight = round(sqrt($newArea / $widthRatio));
            $newWidth  = round($newHeight * $widthRatio);

            // Scale image
            return imagescale($image, $newWidth, $newHeight);
        }

        return $image;
    }

    /**
     * Convert filesize() to byte notation
     * Ex:
     * formatBytes(123456) -> 120.56 KB
     * formatBytes(1234567) -> 1.18 MB
     * @param $size
     * @param int $precision
     * @return string
     */
    public static function formatBytes($size, $precision = 2)
    {
        $base = log($size, 1024);
        $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }

    /**
     * Function to normalize importer column names used to detect missing columns
     * @param $name
     * @return string
     */
    public static function normalizeColumnName($name)
    {
        return strtolower(str_replace(['-', '_', ' '], '', $name));
    }

    /**
     * Convert column number to column letter
     * credit: https://stackoverflow.com/questions/3302857/algorithm-to-get-the-excel-like-column-name-of-a-number
     *
     * EX:
     *      0 => 'A'
     *      1 => 'B'
     *      2 => 'C'
     *      ...
     *      26 => 'AA'
     *      27 => 'AB'
     *      ...
     *      1000 => 'ALM'
     *
     * @param $n    column-offset [0, 1, ...] (not to be confused with the column-number [1, 2, ...])
     * @return      string
     */
    public static function excelColumnLetter($n)
    {
        for ($r = ""; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr(($n % 26) + 0x41) . $r;
        }
        return $r;
    }

    /**
     * Decompose fullname into firstname and lastname using the first space as a separator
     * What happened if the firstname is composed of 2 names? eg: is Jean Pierre
     *
     * @param  string $fullname Fullname
     * @return array            Array contening firstname and lastname
     */
    public function decomposeFullname($fullname, $locale = null)
    {
        /** @var Salesfloor\Services\NameSuggester $nameSuggester */
        $nameSuggester = $this->app['name_suggester'];

        $names = $nameSuggester->parseName($fullname, $locale);

        return [$names['given'], $names['family']];
    }

    /**
     * Get initial of a name
     * @param  string $name A name
     * @return string       Initial of a name
     */
    public static function getInitial($name)
    {
        if (empty($name)) {
            return '';
        }

        $nameLen = mb_strlen($name);
        return mb_strtoupper(mb_substr($name, 0, 1)) . ($nameLen > 1 ? '.' : '');
    }

    public static function timeAgo($ptime)
    {
        $estimateTime = time() - $ptime;

        if ($estimateTime < 1) {
            return 'less than 1 second ago';
        }

        $condition = [
            12 * 30 * 24 * 60 * 60 => 'year',
            30 * 24 * 60 * 60 => 'month',
            24 * 60 * 60 => 'day',
            60 * 60 => 'hour',
            60 => 'minute',
            1 => 'second',
        ];

        foreach ($condition as $secs => $str) {
            $d = $estimateTime / $secs;
            if ($d >= 1) {
                $r = round($d);
                return $r . ' ' . $str . ($r > 1 ? 's' : '') . ' ago';
            }
        }
    }

    /**
     * Convert UTC datetime to local time based on timezone
     *
     * @param  string $utc UTC datetime string format (Y-m-d H:i:s)
     * @param  string $timeZone Time Zone string
     *
     * @return string[] Array structure that contains local datetime info
     * @throws \Exception
     */
    public function convertUtcToLocal(string $utc, string $timeZone, array $format = ['Y-m-d', 'H:i:s']): array
    {
        $dt = new \DateTime($utc, new \DateTimeZone('UTC'));

        $tz = new \DateTimeZone($timeZone);

        $dt->setTimezone($tz);

        $localDateTime = [
            $dt->format($format[0]),
            $dt->format($format[1]),
        ];

        return $localDateTime;
    }

    /**
     * Convert locale datetime to utc time based on timezone
     */
    public static function convertLocalToUtc(string $localeTime, string $localeTimeZone, array $format = ['Y-m-d', 'H:i:s']): array
    {
        if (empty($localeTimeZone)) {
            $localeTimeZone = 'UTC';
        }
        $dt = new \DateTime($localeTime, new \DateTimeZone($localeTimeZone));

        $tz = new \DateTimeZone('UTC');

        $dt->setTimezone($tz);

        $utcDateTime = [
            $dt->format($format[0]),
            $dt->format($format[1]),
        ];

        return $utcDateTime;
    }

    /**
     * Check if timezone is a string or db field
     * @param $timezoneField
     */
    public static function isTimezoneIdentifiers($timezoneField)
    {
        if (empty($timezoneField) || strtoupper($timezoneField) == 'UTC') {
            return true;
        }

        //  to be simple,  only check if the format: 'xxx/yyy'
        if (preg_match('/^[a-zA-Z]+\/[a-zA-Z_]+$/', $timezoneField)) {
            return true;
        }

        return false;
    }

    /**
     * Return true if current date is within range
     * If date is null it will be considered open-ended
     * thus will return true if within the other end of the range
     * [current, null, end]     current <= end
     * [current, start, null]   start <= current
     * [current, start, end]    start <= current <= end
     * @param $currentDatetime  string of current date
     * @param $startDate        string of start date for range
     * @param $endDate          string of end date for range
     * @return bool             is current time in range
     */
    public static function dateInRange($currentDatetime, $startDate, $endDate)
    {
        $startTs = null;
        if ($startDate != null) {
            $startTs = strtotime($startDate);
        }
        $endTs = null;
        if ($endDate != null) {
            $endTs = strtotime($endDate);
        }
        $currentTs  = strtotime($currentDatetime);

        $afterStart = true;
        if ($startTs != null) {
            $afterStart = $startTs <= $currentTs;
        }

        $beforeEnd = true;
        if ($endTs != null) {
            $beforeEnd = $currentTs <= $endTs;
        }

        // Check that user date is between start & end
        return $afterStart && $beforeEnd;
    }

    /**
     * Get working days (Monday-Friday (5))
     *
     * @link    https://stackoverflow.com/questions/336127/calculate-business-days
     *
     * @param  string $startDate start date
     * @param  string $endDate   end date
     * @param array $holidayDays e.g '*-12-25', '*-01-01', '2013-12-23'
     *
     * @return integer           number of days
     */
    public function getWorkingDays($startDate, $endDate, array $holidayDays = [])
    {
        // The params should have 00:00:00 / 23:59:59.
        // Since we only want ot know the number of days between them, time doesn't matter.
        $start = date('Y-m-d', strtotime($startDate));
        $end = date('Y-m-d', strtotime($endDate));

        $workingDays = [1, 2, 3, 4, 5]; # date format = N (1 = Monday, ...)

        $from = new \DateTimeImmutable($start);
        $to = new \DateTimeImmutable($end);

        // Adding one day because start/end date can be equal.
        $newTo = $to->modify('+1 day');

        $interval = new DateInterval('P1D');
        $periods = new DatePeriod($from, $interval, $newTo);

        $days = 0;
        foreach ($periods as $period) {
            if (!in_array($period->format('N'), $workingDays)) {
                continue;
            }

            if (in_array($period->format('Y-m-d'), $holidayDays)) {
                continue;
            }

            if (in_array($period->format('*-m-d'), $holidayDays)) {
                continue;
            }

            $days++;
        }
        return $days;
    }

    public static function archiveDir($directory, $archiveName = null)
    {
        $i = strrpos($directory, '/');
        $super = substr($directory, 0, $i);
        $sub = substr($directory, $i + 1);
        if (isset($archiveName)) {
            $tarLeaf = $archiveName;
        } else {
            $tarLeaf = $sub . '.tar.xz';
        }
        if (preg_match("/['\\\\]/", $tarLeaf)) {
            throw new \Exception("what are you doing");
        }
        $tarPath = "/tmp/$tarLeaf";
        exec("cd '$super' && tar cJf '$tarPath' '$sub'");

        return $tarPath;
    }

    public function camelCase($str)
    {
        return ucfirst(strtolower($str));
    }

    public static function geoDistance(
        $latitudeFrom,
        $longitudeFrom,
        $latitudeTo,
        $longitudeTo,
        $earthRadius = 6371
    ) {
        // convert from degrees to radians
        $latFrom = deg2rad($latitudeFrom);
        $lonFrom = deg2rad($longitudeFrom);
        $latTo = deg2rad($latitudeTo);
        $lonTo = deg2rad($longitudeTo);

        $lonDelta = $lonTo - $lonFrom;
        $a = pow(cos($latTo) * sin($lonDelta), 2) +
        pow(cos($latFrom) * sin($latTo) - sin($latFrom) * cos($latTo) * cos($lonDelta), 2);
        $b = sin($latFrom) * sin($latTo) + cos($latFrom) * cos($latTo) * cos($lonDelta);

        $angle = atan2(sqrt($a), $b);
        return $angle * $earthRadius;
    }

    public static function arrayToObject($array)
    {
        if (!is_array($array)) {
            throw new \Exception("Argument is not an array");
        }

        return json_decode(json_encode($array), false);
    }

    public static function objectToArray($obj)
    {
        return json_decode(json_encode($obj), true);
    }

    public function diffObject($objReference, $objCompared, $diff = [])
    {
        $objReference = self::objectToArray($objReference);
        $objCompared = self::objectToArray($objCompared);

        foreach ($objReference as $key => $value) {
            $resultDiff = $this->compareProperty($objReference, $objCompared, $key, $diff);
            if ($resultDiff !== true) {
                $diff[$key] = $resultDiff;
            }
        }

        return $diff;
    }

    public function compareProperty($objReference, $objCompared, $key, $diff)
    {
        if (is_array($objReference[$key])) {
            return $this->diffObject($objReference[$key], $objCompared[$key], $diff[$key]);
        } elseif (!isset($objCompared[$key]) || $objReference[$key] != $objCompared[$key]) {
            return $objReference[$key] . ' => ' . (isset($objCompared[$key]) ? $objCompared[$key] : 'undefined');
        } else {
            return true;
        }
    }

    public function getDuplicateInArray($array)
    {
        $arrayUnique = array_unique($array);
        return array_diff_assoc($array, $arrayUnique);
    }


    public static function slugify($slug)
    {
        // Remove HTML tags
        $slug = preg_replace('/<(.*?)>/u', '', $slug);

        // Remove inner-word punctuation.
        $slug = preg_replace('/[\'"‘’“”]/u', '', $slug);

        // Make it lowercase
        $slug = mb_strtolower($slug, 'UTF-8');

        // Get the "words".  Split on anything that is not a unicode letter or number.
        // Periods are OK too.
        preg_match_all('/[\p{L}\p{N}\.]+/u', $slug, $words);
        $words = array_filter($words[0]);
        $slug = implode('-', $words);

        return $slug;
    }

    /**
     * Decodes any %## encoding in the given string.
     *
     * @note : rawurldecode :     RFC 3986, will keep symbols ('+')
     *         urldecode    :     Default decode setting, symbols ('+') are decoded to a space character.
     *
     * @param array|object $data
     * @param bool $rawurldecode
     * @return array|mixed
     */
    public static function urlDecodeDeep($data, $rawurldecode = false)
    {
        $function = $rawurldecode === false ? 'urldecode' : 'rawurldecode';
        return self::mapDeep($data, $function);
    }

    /**
     * This function is convenient when encoding a string to be used in a query part of a URL, as a convenient way to
     * pass variables to the next page
     *
     * @param array|object $data
     */
    public function urlEncodeDeep($data)
    {
        return self::mapDeep($data, 'urlencode');
    }

    /**
     * https://core.trac.wordpress.org/browser/tags/4.7.3/src/wp-includes/formatting.php#L4306
     *
     * @param array|object $value
     * @param $function             This can be for example urlencode|urldecode
     *
     * @return array|mixed
     */
    private static function mapDeep($value, $function)
    {
        // This support array AND object
        if (is_array($value)) {
            foreach ($value as $index => $item) {
                $value[$index] = self::mapDeep($item, $function);
            }
        } elseif (is_object($value)) {
            $object_vars = get_object_vars($value);
            foreach ($object_vars as $property_name => $property_value) {
                $value->$property_name = self::mapDeep($property_value, $function);
            }
        } else {
            $value = call_user_func($function, $value);
        }

        return $value;
    }

    /**
     * Function transforms a given simple array into a new array where each
     * entry is now protected by the $quoteCharacter on both ends
     * EX: INPUT  -> $array = ["v1", "v2"], $quoteCharacter = "'" (single quote)
     *     OUTPUT -> ["'v1'", "'v2'"]
     *
     * Useful for functions such as $queryBuilder->expr()->in($field, $values)
     * which does not know if your values should be treated as numbers or
     * protected strings within the SQL query's IN expression
     *
     * @return array Array with each value protected by $quoteCharacter
     */
    public static function quoteValuesInArray(array $array, $quoteCharacter = "'")
    {
        $quoted = [];
        foreach ($array as $value) {
            $quoted[] = $quoteCharacter . $value . $quoteCharacter;
        }
        return $quoted;
    }

    /**
     * Replace parameters into sql
     * Used for dumping queries and also for generating cache keys
     * @param QueryBuilder $qb
     * @return string
     */
    public static function getExecutableSQL($qb)
    {
        // Replace params into query
        $strSql = $qb->getSQL();
        $queryParams = $qb->getParameters();

        foreach ($queryParams as $key => $value) {
            $strBuild = null;
            switch (true) { // compare true to case value
                case is_array($value):
                    // keep order of ids consistent for cache key generation
                    sort($value);

                    $tempArr = [];
                    foreach ($value as $subValue) {
                        $tempArr[] = "'" . addslashes($subValue) . "'";
                    }
                    $strBuild = join(', ', $tempArr);
                    break;

                case is_numeric($value):
                    $strBuild = addslashes($value);
                    break;

                case is_string($value):
                    $strBuild = "'" . addslashes($value) . "'";
                    break;

                case is_null($value):
                    $strBuild = 'NULL';
                    break;

                default:
                    // NOP
            }

            if (!is_null($strBuild)) {
                $strSql = str_replace(':' . $key, $strBuild, $strSql);
            }
        }

        return $strSql;
    }

    /**
     * This method allows to extract a property from an array of objects or arrays
     * @param  Array  $array     Array of objects or arrays
     * @param  String $property  A property name (key for an array)
     * @return Array             Array of values
     */
    public function getPropertiesFromArray($array, $property)
    {
        return array_map(function ($item) use ($property) {
            $value = null;
            if (is_array($item) && isset($item[$property])) {
                $value = $item[$property];
            } elseif (isset($item->{$property})) {
                $value = $item->{$property};
            }

            return $value;
        }, $array);
    }

    public function ucfirstLowercase($str)
    {
        return ucfirst(mb_strtolower($str ?? ''));
    }

    /**
     * Convert a parsed url back to string.
     *
     * @link http://php.net/manual/en/function.parse-url.php#106731
     *
     * @param array $parsedUrl Url parsed by `parse_url()`
     *
     * @return string
     */
    public function unparseUrl($parsedUrl)
    {
        $scheme   = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] . '://' : '';
        $host     = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
        $port     = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';
        $user     = isset($parsedUrl['user']) ? $parsedUrl['user'] : '';
        $pass     = isset($parsedUrl['pass']) ? ':' . $parsedUrl['pass'] : '';
        $pass     = ($user || $pass) ? "$pass@" : '';
        $path     = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
        $query    = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
        $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';

        return "$scheme$user$pass$host$port$path$query$fragment";
    }

    /**
     * Extract the local part of the email. E.g: <EMAIL> will return noreply
     * @param $email
     *
     * @return bool
     */
    public static function extractLocalPartEmail($email)
    {
        $parts = explode('@', $email);

        if (count($parts) !== 2) {
            return false;
        }

        return mb_strtolower(trim($parts[0]));
    }

    /**
     * Use default locale to display current time
     *
     * @param string $format E.g: "%A %e %B %Y, %T" will return "Tuesday 25 July 2017, 21:08:19" or "‌mardi 25 juillet
     *                       2017, 21:08:06"
     * @param null   $date
     * @param string $timezone  Timezone used for this timestamp/date
     *
     * @deprecated Use Multilang::getLocalizedDate. It uses the intl extension. Fiddling with the default timezones
     * still has issues with translating the names of days and months.
     *
     * @return string
     * @throws \Exception
     */
    public static function getLocaleTime($format, $date = null, $timezone = null)
    {
        // This doesn't support multilang, without touching setlocale()
        // We may need to using intl

        // This is the only way (Afaik) to make strftime to print the correct timezone information
        if (!empty($timezone)) {
            $currentTimezone = date_default_timezone_get();
            date_default_timezone_set($timezone);
        }


        if ($date instanceof \DateTime) {
            $timestamp = $date->getTimestamp();
        } else {
            if (is_integer($date)) {
                $timestamp = $date;
            } else {
                if (is_null($date)) {
                    $timestamp = time();
                } else {
                    $timestamp = strtotime($date);
                }
            }
        }

        if (false === $timestamp) {
            throw new \Exception("Date is not a valid format");
        }

        $newDate = strftime($format, $timestamp);

        if (!empty($timezone)) {
            date_default_timezone_set($currentTimezone);
        }

        return $newDate;
    }

    /**
     * Convert UTC time to specific timezone
     *
     * @param        $date
     * @param        $timezone
     * @param string $format
     *
     * @return string
     */
    public static function getTimezoneTime($date, $timezone, $format = 'Y-m-d H:i:s')
    {
        $date = new \DateTime($date);
        $date->setTimezone(new \DateTimeZone($timezone));

        return $date->format($format);
    }

    /**
     * Check if a date is a valid
     *
     * @param  string  $strDt         Date in string format
     * @param  string  $strDateformat Date format to verify
     * @param  string  $strTimezone   Timezone
     * @return boolean
     */
    public static function isValidDate($strDt, $strDateformat = 'Y-m-d H:i:s', $strTimezone = 'UTC')
    {
        $date = \DateTime::createFromFormat($strDateformat, $strDt, new \DateTimeZone($strTimezone));
        $lastErrors = \DateTime::getLastErrors();
        return $date && $lastErrors["warning_count"] == 0 && $lastErrors["error_count"] == 0;
    }

    /**
     * Return basic date for of NOW
     *
     * @param string $format
     * @param bool   $utc
     *
     * @return false|string
     */
    public static function now($format = 'Y-m-d H:i:s', $utc = true)
    {
        return $utc ? gmdate($format) : date($format);
    }

    /**
     * Return basic date for of Yesterday
     *
     * @param string $format
     *
     * @return false|string
     */
    public static function yesterday($format = 'Y-m-d H:i:s')
    {
        $date = new \DateTime();
        $date->add(\DateInterval::createFromDateString('yesterday'));

        return $date->format($format);
    }

    /**
     * Register a process at $label, unless there's already one running.
     *
     * This reads/writes the process id to a PID file at /var/run/$label.pid.
     * If that file exists and it contains the PID of a currently-running process,
     * false is returned. Otherwise, the current PID is written to that file,
     * and true is returned.
     *
     * @param string $label The label for the process
     * @param Application $app The silex app container, for looking up stack name.
     * @return string the newly-created pidfile, or false if there's
     *  already a process with this label running.
     * @throws \Exception
     * @deprecated Use ProcessManagementService instead
     */
    public static function registerSingletonProcess($label, $app = null)
    {
        if ($app !== null) {
            $stack = $app['configs']['env'];
            $retailerShort = $app['configs']['retailers.short'];
            $label .= "-$retailerShort-$stack";
        }

        $pidFile = "/tmp/$label.pid";
        if (file_exists($pidFile)) {
            $pid = file_get_contents($pidFile);
            if (!$pid) {
                throw new \Exception("Failed to read pidfile $pidFile");
            }

            // Find out if there really exists a process with $pid by sending
            // it signal 0. From kill(2), "If sig is 0, then no signal is sent,
            // but error checking is still performed; this can be used to check
            // for the existence of a process ID or process group ID."
            if (posix_kill(trim($pid), 0)) {
                return false;
            }
        }
        $ok = file_put_contents($pidFile, getmypid() . "\n");
        if (!$ok) {
            throw new \Exception("Failed to write pid to $pidFile");
        }

        return $pidFile;
    }

    /**
     * Given a standard locale code, extracts the two-character country code.
     *
     * @param $localeCode string A five-character locale
     * @return string
     */
    public function extractCountry($localeCode)
    {
        $match = [];
        $found = preg_match('/(.*)_(.*)/', $localeCode, $match);

        if ($found) {
            return $match[2];
        }

        return $localeCode;
    }

    public function getClientIp()
    {
        $ipaddress = null;

        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        }

        /* There could be multiple IP's in referer
        * Example: GET /locate/country?ip=***************, ***********" 400 226 "-" "-"
        * This allow to send only the 1st IP Address in the list which is the client
        * IP.
        */
        if (strpos($ipaddress, ',') !== false) {
            $ipaddressArr = explode(',', $ipaddress);
            return trim($ipaddressArr[0]);
        }
        return $ipaddress;
    }

    /**
     * Generate an ICS file from the event data
     *
     * This follow the RFC-5545 specification
     * @link: https://icalendar.org/RFC-Specifications/iCalendar-RFC-5545/
     *
     * @param string $folder
     * @param string $summary
     * @param string $uid                <EMAIL>
     * @param string $version            Version number of the event
     * @param string $method
     * @param string $status
     * @param string $dateStartUtc       UTC date
     * @param string $dateEndUtc
     * @param string $dateCreatedUtc
     * @param string $dateUpdatedUtc
     * @param string $location
     * @param array  $attendees       ['name' => '', 'email' => '']
     * @param string $description
     * @param string $url
     *
     * @return string|null Returns the path of the created temp file
     */
    public function createIcsFile(
        $folder,
        $summary,
        $uid,
        $version,
        $method,
        $status,
        $dateStartUtc,
        $dateEndUtc,
        $dateCreatedUtc,
        $dateUpdatedUtc,
        $location,
        $attendees,
        $description,
        $url
    ) {
        // Create a temp file
        $path = sys_get_temp_dir() . '/' . $folder;
        mkdir($path);
        $file = $path . '/invite.ics';
        $handle = fopen($file, 'w');
        if (!$handle) {
            return null;
        }

        $icsHeaderContent = [
            'BEGIN:VCALENDAR',
            'VERSION:2.0',
            'PRODID:-//Salesfloor//salesfloor.net v1.0//EN',
            'CALSCALE:GREGORIAN',
        ];

        // These are the event properties
        $icsEventContent = [
            'BEGIN:VEVENT',
            'SUMMARY:' . $summary,
            'UID:' . $uid,
            'SEQUENCE:' . $version,
            'METHOD:' . $method,
            'STATUS:' . $status,
            'DTSTART:' . $dateStartUtc,
            'DTEND:' . $dateEndUtc,
            'CREATED:' . $dateCreatedUtc,
            'LAST-MODIFIED:' . $dateUpdatedUtc,
            'LOCATION:' . $location,
            'ORGANIZER;CN=' . $attendees[0]['name'] . ':mailto:' . $attendees[0]['email'],
            'ATTENDEE;CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;PARTSTAT=ACCEPTED;CN=' . $attendees[0]['name'] . ';X-NUM-GUESTS=0:mailto:' . $attendees[0]['email'],
            'ATTENDEE;CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;PARTSTAT=ACCEPTED;CN=' . $attendees[1]['name'] . ';X-NUM-GUESTS=0:mailto:' . $attendees[1]['email'],
            'DESCRIPTION:' . $description,
            'URL;VALUE=URI:' . $url,
            'END:VEVENT',
        ];

        // This will setup an automatic reminder for the event
        $icsAlarmContent = [
            'BEGIN:VALARM',
            'TRIGGER:-P1D', // 24h hours before event
            'ACTION:DISPLAY',
            'DESCRIPTION:' . $summary,
            'END:VALARM',
        ];

        $icsFooterContent = [
            'END:VCALENDAR',
        ];

        $calendarContent = implode(PHP_EOL, array_merge($icsHeaderContent, $icsEventContent, $icsAlarmContent, $icsFooterContent));

        fwrite($handle, $calendarContent);
        fclose($handle);

        return $file;
    }

    /**
     * Use to add trailing slash to path where it's mandatory
     *
     * @param $path
     *
     * @return string
     */
    public function addTrailingSlash($path)
    {
        return rtrim($path, '/') . '/';
    }

    /**
     * Use to remove the first slash from path
     *
     * @param $path
     *
     * @return string
     */
    public function removeFirstSlash($path)
    {
        return ltrim($path, '/');
    }

    /**
     * Format price according to currency and locale
     *
     * By default currency is the retailer default currenry
     * and locale is the retailer default locale
     *
     * WARNING: if locale doesn't match the currency, an extra information regarding the country
     * will be displayed.
     *
     * eg: en_US and CAD => CA$148.00
     * en_CA and CAD => $148.00
     *
     * @param  string $price    price
     * @param  string $currency currency
     * @param  string $locale   locale
     * @param  string $decimals decimals
     * @param  null|bool $hideEmptyDecimals hideEmptyDecimals, ex:  148.00 => 148
     * @return string           Formated Price
     */
    public function formatPrice($price, $currency = null, $locale = null, $decimals = null, $hideEmptyDecimals = null)
    {
        if (empty($price)) {
            return '';
        }

        if (empty($currency)) {
            $currency = $this->app['configs']['retailer.default_currency'];
        }

        if (empty($locale)) {
            $locale = $this->app['configs']['retailer.i18n.default_locale'];
        }

        if ($price < 1) {
            // in case that $0.12 change to 0 price
            $decimals = 2;
        } elseif (!isset($decimals)) {
            $decimals = $this->app['configs']['retailer.pricePrecision'];
        }

        if (!isset($hideEmptyDecimals)) {
            $hideEmptyDecimals = $this->app['configs']['retailer.pricePrecision.hide_empty_decimals'];
        }

        // if decimals is 1 and original price 25.0x or 24.9x, since we have round the number, so don't hide decimal
        $roundDiff = abs(round($price) - $price);
        if ($decimals == 1 && 0 < $roundDiff && $roundDiff < 0.1) {
            $hideEmptyDecimals = false;
        }

        $formatter = \NumberFormatter::create($locale, \NumberFormatter::CURRENCY);
        $formatter->setAttribute(\NumberFormatter::MAX_FRACTION_DIGITS, $decimals);
        $price = $formatter->formatCurrency($price, $currency);

        $decimalSeparator = $formatter->getSymbol(\NumberFormatter::DECIMAL_SEPARATOR_SYMBOL);

        if ($price === false) {
            throw new \Exception(
                sprintf(
                    "Failed to format price properly with price [%s], currency [%s] and locale [%s]",
                    $price,
                    $currency,
                    $locale
                ),
                500
            );
        }

        return $this->overrideLocalizeCurrency($price, $hideEmptyDecimals, $decimalSeparator);
    }

    /**
     * check if a number is a valid sale price
     * @param $number
     * @return bool
     */
    public static function isValidSalePrice($number)
    {
        if (empty($number) || !is_numeric($number)) {
            return false;
        }

        // $number == '0.0', '0.00' or negative value
        if (floatval($number) <= 0) {
            return false;
        }

        return true;
    }

    /**
     * Check if string is a valid json
     *
     * @param $string
     *
     * @return bool
     */
    public function isJson($string)
    {
        $value = json_decode($string);
        return !($value === null && json_last_error() !== JSON_ERROR_NONE);
    }

    /**
     * Try our best to return a nice value. (convert html entities are removed those we can't convert).
     * Will convert accents to non-accent chars
     * Will remove emojis
     * The origin of this function is in the old product feed importer stack.
     * A similar function is cleanUpRowEncoding however it is less aggressive and is focused more or preserving as much as possible just converting the encoding
     *
     * @param string $value
     * @param bool $convertBrToSpace
     *
     * @return string
     */
    public function cleanUpValue($value, $convertBrToSpace = true)
    {
        // Don't do any cleanup, if string is empty
        if (empty($value)) {
            return $value;
        }

        $decoded = html_entity_decode($value, ENT_QUOTES);

        // Some retailer doesn't set ";" for some, try to decode them again before removing them
        // This may not the most clean regex, but should do the job
        $decoded = preg_replace("/(&#\d{1,3})(\d?)(?!;|\d)/", "$1;$2", $decoded);
        $decoded = html_entity_decode($decoded, ENT_QUOTES);

        // Convert to UTF8 and remove any chars that would break loadDataInfile
        $decoded = SanitizeService::convertToUtf8($decoded);

        // Try to decode windows character
        $decoded = preg_replace_callback("/&#([0-9]+);/u", function ($m) {
            return iconv('cp1250', 'utf-8', chr($m[1]));
        }, $decoded);

        // Sadly, some character (not utf8) (CP1252) can't be saved because of load in data file only support utf8
        // For now, just remove them
        $decoded = preg_replace("/&#\d*;?/", " ", $decoded);

        if ($convertBrToSpace) {
            $decoded = preg_replace("/<br\W*\/?>/i", " ", $decoded);
        }

        // In case we have double spaces, just set 1 instead
        $decoded = trim(preg_replace('/\s+/', ' ', $decoded));

        // TODO: only temporary solution of replace/remove some of utf8mb4 character in mysql 5.7.x (which would throw an error)
        // \x{1F16A} is one of french trademark symbols will be replaced, and other 4 bytes characters would be removed temporarily before all db change to utf8mb4
        $decoded = preg_replace('/\x{1F16A}/u', 'MC', $decoded);
        // https://tools.ietf.org/html/rfc3629
        // https://stackoverflow.com/questions/8491431/how-to-replace-remove-4-byte-characters-from-a-utf-8-string-in-php
        $decoded = preg_replace('/[\x{10000}-\x{10FFFF}]/u', '', $decoded);

        // Force to remove all non-utf8 character otherwise "load data infile" will return "invalid utf8 character"
        // And all next queries return "MySQL has gone away"
        $decoded = iconv("UTF-8", "UTF-8//IGNORE", $decoded);

        $decoded = strip_tags($decoded);

        // This is when you have input like this: "This is my description <h1>Something\<\h1>"
        // Without the rtrim, you will get "This is my description Something\" and escaping the last quote is causing issues.
        return rtrim($decoded, '\\');
    }

    /**
     * Clean up row encoding
     * Less aggressive version of cleanUpValue that focuses more on preserving as many chars as possible and converting to utf8
     * Will convert ISO accents to UTF-8
     * Will preserve emojis
     * Converts unknown chars to ? instead of multiple garbage utf8 chars
     * Convert encoding to UTF-8 and remove any chars which may break MySQLConnection\CsvBufferCest::testLoadDataInfile
     *
     * @param array|string $row
     * @return array|string
     */
    public static function cleanUpRowEncoding($row)
    {
        if (is_array($row)) {
            return array_map(function ($value) {
                return SanitizeService::convertToUtf8($value);
            }, $row);
        } else {
            return SanitizeService::convertToUtf8($row);
        }
    }

    /**
     * I didn't want to create a new config (url) for the new backoffice. Since we don't really know what it will be.
     * Keep it simple for now.
     * @param null $env
     * @param $route
     *
     * @return string
     */
    public function getBackofficeUrl($route, $env = null)
    {
        $base = $this->app['configs']['salesfloor_storefront.host'];
        return $base . '/preview/#/' . $route;
    }

    /**
     * Quick & Dirty CSRF token-generation functions move from:
     * ~/instance-webserver/src/salesfloor/sfadmin/includes/csrf.php
     * @param $userAgent
     * @param null $time
     * @return string
     */
    public function qdcsrf($userAgent, $time = null)
    {
        if (!isset($time)) {
            $time = time();
        }
        $timeWindow = 3600 * floor($time / 3600); // truncate minutes and seconds from the timestamp

        return md5($userAgent . $timeWindow . 'Security through 0bscur1ty');
    }

    /**
     * Quick & Dirty CSRF token-generation functions move from:
     * ~/instance-webserver/src/salesfloor/sfadmin/includes/csrf.php:check_qdcsrf
     * @param $token
     * @param $userAgent
     * @param null $time
     * @return bool
     */
    public function checkQdcsrf($token, $userAgent, $time = null)
    {
        if (!isset($time)) {
            $time = time();
        }

        if ($token == self::qdcsrf($userAgent, $time)) {
            return true;
        }
        $time -= 3600;
        if ($token == self::qdcsrf($userAgent, $time)) {
            return true;
        }

        return false;
    }

    /**
     * We want to modify the behavior of the standard `formatCurrency` method. Specifically, we don't want
     * to display alphanumeric currency codes at this time. This will be unnecessary once we correct HR to use
     * `en_CA` instead of `en_US`.
     *
     * @param string $price price
     * @param null $hideEmptyDecimals
     * @param string $decimalSeparator
     * @return string         price without alphanumeric currency codes
     */
    private function overrideLocalizeCurrency($price, $hideEmptyDecimals = null, $decimalSeparator = '.')
    {
        $price = preg_replace('/[a-zA-Z]/', '', $price);

        if ($hideEmptyDecimals) {
            // 'CAD 3.00' => 'CAD 3', but no hide apply to 'CAD 3.10'
            $price = preg_replace(sprintf('/\%s0{1,2}(.*)/', $decimalSeparator), '$1', $price);
        }
        return $price;
    }

    /**
     * Common function for process price kind of value, ex: price for product, variant, order etc.
     * If price is set return value, else return 0
     * @param $price
     * @return int|float
     */
    public function setNullPrice($price)
    {
        if (isset($price)) {
            return $price;
        }

        return 0;
    }

    /**
     * Paths in S3 have a number of common expectations and replacements.
     *
     * This will apply replacements and slash normalization.
     *
     * @param string $path S3 path to operate on
     * @param string $env current environment
     * @return string
     */
    public function normalizeS3Path(string $path, string $env): string
    {
        // Transform "prd" to "prod".
        // Once upon a time, we gave prod instead of prd in the path. By the time we noticed, it was
        // already used. In the interests of standardizing the public retailer-facing import interface,
        // we decided to stick to "prod". Here, we transform prd, as we still use that internally for configs.
        $env = $env === 'prd' ? 'prod' : $env;

        // We remove any left slash, because s3 doesn't like it
        return $this->removeFirstSlash(
            // Trailing slash is needed to use with delimiter
            $this->addTrailingSlash(str_replace(
                '{env}',
                $env,
                $path
            ))
        );
    }

    /**
     * Recursively return a list of class namespaces in specific directory from cache.
     *
     * @param string $dir               The directory you want to look
     * @param string baseNamespace      The base namespace used for this $dir
     * @param array $excludedClassNames A list of class names to be excluded (e.g: base)
     *
     * @return array $results
     */
    public function getClassNamespacesInDirectory(string $dir, string $baseNamespace, array $excludedClassNames): array
    {
        // Some caching adapter doesn't allow some characters, just normalize it
        $path = $this->slugify($dir);

        // Since this is not part of any services and "dir" could be in theory anything,
        // we won't warm the cache up. We will rely on the fallback adapter (file) in this case.
        return $this->app['service.cache']->wrap("classname-$path", function () use ($dir, $baseNamespace, $excludedClassNames) {
            return $this->getClassNamespacesInDirectionContent($dir, $baseNamespace, $excludedClassNames);
        });
    }

    /**
     * Recursively return a list of class namespaces in specific directory.
     * RecursiveDirectoryIterator was a bit more slower than scandir (at least locally).
     *
     * @param string $dir               The directory you want to look
     * @param string baseNamespace      The base namespace used for this $dir
     * @param array $excludedClassNames A list of class names to be excluded (e.g: base)
     * @param string $subNamespace      This is used since it's a recursive function
     *
     * @return array $results
     */
    private function getClassNamespacesInDirectionContent(string $dir, string $baseNamespace, array $excludedClassNames, string $subNamespace = ""): array
    {
        $results = [];

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;

            if (is_file($path)) {
                $className = pathinfo($path, PATHINFO_FILENAME);
                if (!in_array($className, $excludedClassNames)) {
                    $results[] = $baseNamespace . $subNamespace . $className;
                }
            } else {
                $subNamespace = empty($subNamespace) ? "" : rtrim($subNamespace, "/\\") . "\\";
                $subNamespace .= $file . "\\";

                $results = array_merge($results, $this->getClassNamespacesInDirectionContent($path, $baseNamespace, $excludedClassNames, $subNamespace));
            }
        }

        return $results;
    }

    /**
     * We can't use filter_var because sometimes we want to accept missing (http/https)
     * I'm using the same regex as mobile from https://www.regextester.com/93652
     *
     * @param string $url
     *
     * @return bool
     */
    public function validateUrl(string $url): bool
    {
        $matchRegex = '/^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i';
        return (bool)preg_match($matchRegex, $url);
    }

    /**
     * Remove and replace special character of the file to prevent possible error happen, for example: url encode file name etc.
     *
     * @param string $file
     * @return string
     */
    public static function sanitizeFileName(string $file)
    {
        // Remove anything which isn't a word, number, underscore,  path separator(linux only) and '.' and replace with '_'
        $file = preg_replace("/[^\w\.\/]/", '_', $file);

        return $file;
    }

    /**
     * In Android, if the picture are not https, they are not displayed. Here we are forcing it.
     *
     * Please don't use static, in general. Here i'm using it since it's painful to add dependencies in
     * old importer stack.
     *
     * In some places, we are using cloudinary to wrap it, but not everywhere. (e.g: chat)
     *
     * @param string|null $url
     *
     * @return string|null
     */
    public static function forceHttpsUrl(?string $url): ?string
    {
        // Sometimes, url is optional (swatches), so force it to null in this case.
        if (empty($url)) {
            return null;
        }

        $parsed = parse_url($url);

        if ($parsed && $parsed['scheme'] === 'http') {
            $url = preg_replace("/^http:/i", "https:", $url);
        }

        // keep as is, if it's not http or invalid url
        return $url;
    }


    /**
     * Calculate days difference from today by given date
     *
     * @param string $date
     * @return int $days
     * @throws \Exception
     */
    public function getDaysDifferenceFromToday($date): int
    {
        $today = new \DateTime('NOW');
        $otherDate = new \DateTime($date);
        $days  = $today->diff($otherDate)->format('%a');

        return (int)$days;
    }

    /**
     * Get date which is given days from given date
     *
     * @param string $date
     * @param string $days For example, future day will be '+1', pass day will be '-1'
     * @return string $dayTo
     * @throws \Exception
     */
    public function getDateFromDate($date, $days): string
    {
        $dayFrom = new \DateTime($date);

        $dayFrom->modify("$days day");
        $dayTo = $dayFrom->format('Y-m-d');

        return $dayTo;
    }

    /**
     * Overwrite querystring value. Used in shoppage
     *
     * @param string $url
     * @param array  $params
     *
     * @return string
     */
    public function replaceQueryStringParameter(string $url, array $params)
    {
        $parsedUrl = parse_url($url);

        parse_str($parsedUrl['query'], $queryString);

        // Now we always overwrite (replace) because we have precedence over what the retailer give us.
        // For example, if the link has sf_rep=fake, we will always overwrite it with proper value.
        foreach ($params as $key => $value) {
            $queryString[$key] = $value;
        }

        $parsedUrl['query'] = http_build_query($queryString);

        return self::unparseUrl($parsedUrl);
    }

    /**
     * Get and obfuscated version of the data by type
     *
     * @param string $type email|phone
     * @param string $data
     *
     * @return string
     */
    public function getObfuscatedData(string $type, string $data): string
    {
        switch ($type) {
            case 'email':
                return '*******@*******.***';
                break;
            case 'phone':
                return '+**********';
                break;
        }

        return $data;
    }

    /**
     * Get an unique array by any key's value.
     *
     * @param $array
     * @param $key
     * @return array
     */
    public static function uniqueValuesByKey($array, $key)
    {
        $result      = [];
        $valuesExist = [];

        foreach ($array as $item) {
            $value = is_array($item) ? $item[$key] : $item->{$key};
            if (!in_array($value, $valuesExist)) {
                $valuesExist[] = $value;
                $result[]      = $item;
            }
        }
        return $result;
    }

    /**
     * A simple replacement if `\class_uses_recursive()` could not load from /vendor/composer/autoload_files.php -> /illuminate/support/helpers.php
     *
     * @param $class
     * @param bool $autoload
     * @return array
     * @link https://www.php.net/manual/en/function.class-uses.php
     */
    public static function classUsesRecursive($class, $autoload = true)
    {
        $traits = [];
        do {
            $traits = array_merge(class_uses($class, $autoload), $traits);
        } while ($class = get_parent_class($class));
        foreach ($traits as $trait => $same) {
            $traits = array_merge(class_uses($trait, $autoload), $traits);
        }
        return array_unique($traits);
    }

    /**
     * Return file Encoding, an equivalent of command 'file -b --mime-encoding file'
     * @note this function depend on 'exec', which might be disable in server
     * @param string $filepath
     * @return mixed|string|null
     */
    public static function detectFileEncoding(string $filepath)
    {
        if (!function_exists('exec')) {
            return null;
        }
        $output = [];
        if (!file_exists($filepath)) {
            return null;
        }
        exec('file -i ' . $filepath, $output);
        if (isset($output[0])) {
            $ex = explode('charset=', $output[0]);
            return isset($ex[1]) ? $ex[1] : null;
        }
        return null;
    }

    /**
     * check if file is iso-8859-1/iso-8859-2 encoding
     * @param string $filepath
     * @return bool
     */
    public static function isIso8859Encoding(string $filepath)
    {
        if (strpos(self::detectFileEncoding($filepath), '8859') !== false) {
            return true;
        }
        return false;
    }

    /**
     * Check if a local or list of locales has CJK(Chinese,Japanese,Korean) local
     * @param $locales
     * @return bool
     */
    public static function isCjkLocale($locales)
    {
        $list = ['zh', 'ja', 'ko'];
        if (!array($locales)) {
            $locales = [$locales];
        }

        foreach ($locales as $locale) {
            if (in_array(substr(strtolower($locale), 0, 2), $list)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if string is CJK(Chinese,Japanese,Korean) Character
     * @note ref: https://stackoverflow.com/questions/2598898/detect-cjk-characters-in-php
     *            https://stackoverflow.com/questions/15033196/using-javascript-to-check-whether-a-string-contains-japanese-characters-includi
     * @param string $string
     * @return false|int
     */
    public static function isCjkString($string)
    {
        return self::isChinese($string) || self::isJapanese($string) || self::isKorean($string);
    }

    /**
     * Check if string is Chinese Character
     * @param string $string
     * @return false|int
     */
    public static function isChinese($string)
    {
        return preg_match("/\p{Han}+/u", $string);
    }

    /**
     * Check if string is Japanese Character
     * @param string $string
     * @return false|int
     */
    public static function isJapanese($string)
    {
        return preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}\x{4E00}-\x{9FBF}\x{3400}-\x{4DBF}]/u', $string);
    }

    /**
     * Check if string is Korean Character
     * @param string $string
     * @return false|int
     */
    public static function isKorean($string)
    {
        return preg_match('/[\x{3130}-\x{318F}\x{AC00}-\x{D7AF}]/u', $string);
    }

    /**
     * Dump the contents to file to be able to debug data processing locally
     */
    public static function debugToFile($nameTag, $someValue)
    {
        $useContents = $someValue;
        $useNameTag = !empty($nameTag) ? strval($nameTag) : 'dump';

        if (is_array($someValue)) {
            $useContents = json_encode($someValue, JSON_PRETTY_PRINT);
        }

        file_put_contents(PATH_ROOT . '/DEBUG.' . (++self::$debugFileSequenceCounter) . '.' . $useNameTag . '.txt', $useContents);
    }

    /**
     * Truncate a string
     *
     * If new line is found, this will truncate to the last occurrence of a new line
     * @param string $messages
     * @param int $limit
     * @param string $appendMessage
     * @return string
     */
    public function truncateString(string $messages, int $limit, string $appendMessage = ''): string
    {
        if (mb_strlen($messages) > $limit) {
            $formattedMessages = mb_substr($messages, 0, $limit);
            $formatToNewline = $this->truncateStringToLastNewline($formattedMessages);
            $messages = ($formatToNewline ?: $formattedMessages) . $appendMessage;
        }
        return $messages;
    }

    /**
     * Truncate a string to the last occurrence of a new line
     * @param string $formattedChatMessages
     * @return string
     */
    public function truncateStringToLastNewline(string $formattedChatMessages): string
    {
        return mb_substr($formattedChatMessages, 0, mb_strrpos($formattedChatMessages, PHP_EOL));
    }


    /**
     * Replace entire content of a string when bad character encoding is detected.
     * @param string $message
     * @param string $replaceWith
     * @return string
     */
    public function replaceWhenContainsMojibake(string $message, string $replaceWith = ''): string
    {
        // Caution: There may be the possibility for the occurrence of False positives for non latin charsets.
        // Detecting utf-8 or mojibake replacement characters can be a hit or miss.
        // replacing each invalid character occurrence with either space or a new message
        // makes the text less readable, thus replace entire line
        $containsMojibake = preg_grep('/[\xA4\xB2-\xBF\xC2-\xC7\xE2-\xE7\xF8\xFB-\xFF\x{FFFD}]+/u', [$message]);
        if ($containsMojibake) {
            return $replaceWith;
        }

        return $message;
    }

    /**
    *  Days between two dates
    *
    * @param string|null $fromDate
    * @param string|null $toDate
    * @return int number of days
    */
    public function getDays(string $fromDate = null, string $toDate = null): int
    {
        if (!$toDate) {
            $toDate = date('Y-m-d');
        }

        if (!$fromDate) {
            $fromDate = date('Y-m-d');
        }

        // The params could have 00:00:00 / 23:59:59.
        // Since we only want ot know the number of days between them, time doesn't matter.
        // also, if range is bigger than 24hrs, it will give 2 days difference.
        $start = date('Y-m-d', strtotime($fromDate));
        $end = date('Y-m-d', strtotime($toDate));

        $dateFrom = new \DateTimeImmutable($start);
        $dateTo = new \DateTimeImmutable($end);

        // In case, you have the wrong order. Just return 1, the default.
        if ($dateTo <= $dateFrom) {
            return 1;
        }

        // Adding one day because start/end date can be equal.
        $newDateTo = $dateTo->modify('+1 day');

        return $newDateTo->diff($dateFrom)->format('%a');
    }

    /**
     * It's used for share an update only.
     * The tag ids from request looks like:
     *
     *  [
     *      '1,2',
     *      '6,7,1',
     *      '11,12,2'
     *  ]
     *
     *  it converts to an array like:
     *  [
     *      1,
     *      2,
     *      6,
     *      7,
     *      11,
     *      12
     *  ]
     *
     * @param array $tagIds
     * @return array
     */
    public function convertTagIdsToRealArray(array $tagIds): array
    {
        return array_values(array_filter(
            array_unique(explode(',', implode(',', $tagIds))),
            fn ($id) => is_numeric($id)
        ));
    }

    /**
     * Copied from https://www.php.net/manual/en/function.class-uses.php
     * We can't rely only on class_uses() because it doesn't check for the parent.
     *
     * @param $class
     * @param $autoload
     *
     * @return array
     */
    public static function classUsesDeep($class, $autoload)
    {
        $traits = [];

        do {
            $traits = array_merge(class_uses($class, $autoload), $traits);
        } while ($class = get_parent_class($class));

        foreach ($traits as $trait => $same) {
            $traits = array_merge(class_uses($trait, $autoload), $traits);
        }

        return array_unique($traits);
    }

    public static function getIdentifier(string $name)
    {
        // replace accented letters etc by their non-accented equivalent
        // See http://stackoverflow.com/a/158265/1524002
        try {
            $id = iconv("utf-8", "ascii//TRANSLIT", $name);
        } catch (\Exception $e) {
            // This will happen if e.g. the input is "邁爾士戴維斯"
            $id = '';
        }

        $id = trim(strtolower($id));
        $id = preg_replace('/\s+/', '-', $id);
        $id = preg_replace('/[^a-z0-9-]/', '', $id);
        $id = preg_replace('/-+/', '-', $id);
        $id = preg_replace('/^-|-$/', '', $id);

        return $id;
    }

    /**
     *  Check if text passed has HTML
     *
     *  @param string $text
     *  @return boolean Whether text contains HTML or not
     */
    public static function containsHtml(string $text)
    {
        // detect html comment or tag
        return preg_match('/<(!--.*--|([a-zA-Z].*))>/', $text) > 0;
    }

    /**
     *  Format text passed (HTML or Plain Text)
     *  if contains HTML, returns as is
     *  if plain text, returns after applying nl2br( replace \n with <br> )
     *
     *  @param string $text
     *  @return string returns formatted text
     */
    public static function formatTextForEmail(string $text)
    {
        if (static::containsHtml($text)) {
            $messageContent = $text;
        } else {
            $messageContent = nl2br(str_replace("\\", "", $text));
        }
        return $messageContent;
    }

    /**
     * keyBy('attribute_id') converts
     *  [
     *      attribute_id => fav-color,
     *      attribute_val => blue
     *  ]
     *
     * to
     *
     *  [
     *      fav-color => [
     *          attribute_id => fav-color,
     *          attribute_val=>blue
     *      ]
     *  ]
     * @param array $attributes
     * @return mixed
     */
    public static function keyBy(array $attributes, string $key): array
    {
        return array_reduce($attributes, function ($carry, $item) use ($key) {
            $carry[$item[$key]] = $item;
            return $carry;
        }, []);
    }
}
