<?php

declare(strict_types=1);

namespace Salesfloor\Services;

use Carbon\Carbon;
use Predis\Client;
use IntlDateFormatter;
use Salesfloor\API\Managers\Reps;
use Salesfloor\Services\Multilang;
use malkusch\lock\mutex\PredisMutex;
use Doctrine\DBAL\Query\QueryBuilder;
use Salesfloor\Models\GroupTask\GroupTask;
use Salesfloor\Services\NotificationSystem;
use Salesfloor\API\Managers\Messages\Legacy;
use Salesfloor\Models\Messaging\Text\Attachment;
use Salesfloor\Services\SalesfloorAPIRepository;
use Salesfloor\Models\GroupTask\GroupTaskActivity;
use Salesfloor\Services\Mail\Client as MailClient;
use Salesfloor\API\Managers\GroupTask\GroupTaskActivities;
use Salesfloor\API\Managers\GroupTask\GroupTaskActivityAssets;
use Salesfloor\API\Managers\GroupTask\GroupTaskActivityProducts;
use Salesfloor\API\Managers\GroupTask\GroupTasks as GroupTasksManager;
use Salesfloor\Models\Rep;

class GroupTasks
{
    public const NOTIFICATION_MESSAGE_REMINDER_RETAILER_CUSTOMER = 'pn_group_task_reminder_for_retailer_customer';
    public const NOTIFICATION_MESSAGE_REMINDER = 'pn_group_task_reminder';

    private $configs;

    /** @var GroupTasksManager */
    private $groupTasks;

    /** @var GroupTaskActivities */
    private $activityManager;

    /** @var GroupTaskActivityProducts */
    private $activityProductsManager;

    /** @var GroupTaskActivityAssets  */
    private $activityAssetsManager;

    /** @var Client */
    private $redis;

    private $logger;

    /** @var Multilang */
    private $multiLang;

    /** @var Reps */
    private $repsManager;

     /** @var NotificationSystem */
    private $notificationService;

    /** @var Legacy */
    private $messagesManager;

    private $twig;

    /** @var  MailClient */
    private $emailService;

    private $cloudStorageFactory;

    private $translator;

    /** @var SalesfloorAPIRepository */
    private $salesfloorApi;

    public function __construct(
        $configs,
        GroupTasksManager $groupTasks,
        GroupTaskActivities $activityManager,
        GroupTaskActivityProducts $activityProductsManager,
        GroupTaskActivityAssets $activityAssetsManager,
        Client $redis,
        $logger,
        Multilang $multiLang,
        Reps $repsManager,
        NotificationSystem $notificationService,
        Legacy $messagesManager,
        $twig,
        MailClient $emailService,
        $cloudStorageFactory,
        $translator,
        SalesfloorAPIRepository $salesfloorApi
    ) {
        $this->configs = $configs;
        $this->groupTasks = $groupTasks;
        $this->activityManager = $activityManager;
        $this->activityProductsManager = $activityProductsManager;
        $this->activityAssetsManager = $activityAssetsManager;
        $this->redis = $redis;
        $this->logger = $logger;
        $this->multiLang = $multiLang;
        $this->repsManager = $repsManager;
        $this->notificationService = $notificationService;
        $this->messagesManager = $messagesManager;
        $this->twig = $twig;
        $this->emailService = $emailService;
        $this->cloudStorageFactory = $cloudStorageFactory;
        $this->translator = $translator;
        $this->salesfloorApi = $salesfloorApi;
    }

    public function trackActivity(
        string $action,
        int $groupTaskId,
        int $userId,
        string $title,
        string $details,
        array $products = [],
        array $assets = [],
        string $requestTime = null
    ): void {
        /** @var GroupTask $groupTask */
        $groupTask = $this->groupTasks->getOneOrNull(['id' => $groupTaskId], null, false);
        if ($groupTask === null) {
            return;
        }

        if ($action !== GroupTaskActivity::ACTION_REPLY_EMAIL && (bool)$groupTask->has_reply_from_customer) {
            $groupTask->has_reply_from_customer = false;
            $this->groupTasks->save($groupTask);
        }

        $data = [
            'group_task_id'  => $groupTaskId,
            'action'         => $action,
            'user_id'        => $userId,
            'title'          => $title,
            'details'        => $details,
            'request_time'   => $requestTime,
        ];

        /** @var GroupTaskActivity|null $activity */
        $activity = null;
        if ($requestTime !== null) {
            // The TEXT message is separated into 3 parts (body, products and asset)
            // The $requestTime is same for these 3 parts.
            // So we can create one row in activity log and use mutex to
            // make sure only one process is running when creating it.
            $mutexKey = sprintf(
                'mutex_group_task_activity_log_%d_%d_%s',
                $groupTaskId,
                $userId,
                $requestTime
            );
            $mutex = new PredisMutex([$this->redis], $mutexKey);
            $activity = $mutex->synchronized(function () use ($data) {
                $activity = $this->activityManager->getOneOrNull(
                    array_intersect_key($data, [
                        'group_task_id' => true,
                        'user_id' => true,
                        'request_time' => true,
                    ]),
                    null,
                    false
                );
                if ($activity === null) {
                    $activity = $this->activityManager->create($data);
                    $this->activityManager->save($activity);
                }
                return $activity;
            });
        } else {
            $activity = $this->activityManager->create($data);
            $this->activityManager->save($activity);
        }

        // Get details.
        if ($requestTime !== null && $details !== '[Attachment]' && $details !== $activity->details) {
            $activity->details = $details;
            $this->activityManager->save($activity);
        }

        foreach ($products as $product) {
            $data = $this->activityProductsManager->create([
                'group_task_activity_id' => $activity->id,
                // Share, email and sms use different key to store image url.
                // image_url => share/email
                // img => sms.
                'thumbnail_url'  => $product['image_url'] ?? $product['img'],
                'name' => $product['name'],
            ]);
            $this->activityProductsManager->save($data);
        }

        foreach ($assets as $asset) {
            $data = $this->activityAssetsManager->create([
                'group_task_activity_id' => $activity->id,
                'thumbnail_url'  => $asset['image'],
                'name' => $asset['title'] ?? $asset['label'],
            ]);
            $this->activityAssetsManager->save($data);
        }
    }

    public function processReplyEmailFromCustomer(
        int $groupTaskId,
        int $userId,
        string $title,
        string $details
    ): void {
        /** @var GroupTask $groupTask */
        $groupTask = $this->groupTasks->getOneOrNull(['id' => $groupTaskId]);
        if ($groupTask === null) {
            return;
        }

        $groupTask->status = GroupTask::STATUS_UNRESOLVED;
        $groupTask->has_reply_from_customer = true;
        $this->groupTasks->save($groupTask);

        $this->trackActivity(
            GroupTaskActivity::ACTION_REPLY_EMAIL,
            $groupTaskId,
            $userId,
            $title,
            $details
        );
    }

    public function trackActivityForTextMessage(array $message)
    {
        $groupTaskId = $message['groupTaskId'] ?? null;
        if ($groupTaskId === null) {
            return;
        }

        $requestTime = $message['to']['request_time'] ?? null;
        $body = $message['body'] ?? null;
        $attachments = $message['attachments'] ?? [];
        $products = [];
        $assets = [];
        if (empty($body) && !empty($attachments)) {
            $body = '[Attachment]';
            foreach ($attachments as $attachment) {
                switch ($attachment['type']) {
                    case Attachment::TYPE_PRODUCT:
                        $products[] = [
                            'img' => $attachment['url'],
                            'name' => $attachment['name'],
                        ];
                        break;
                    case Attachment::TYPE_ASSET:
                        $assets[] = [
                            'image' => $attachment['url'],
                            'title' => $attachment['title'],
                        ];
                        break;
                    default:
                        break;
                }
            }
        }
        $this->trackActivity(
            GroupTaskActivity::ACTION_SEND_A_TEXT_MESSAGE,
            (int)$groupTaskId,
            (int)$message['userId'],
            '',
            $body,
            $products,
            $assets,
            $requestTime
        );
    }

    public function reminder()
    {
        // Since we use generator, we don't know yet how much data we have
        $total = 0;

        // At the moment, if the user have X different tasks that have the same reminder_date, they will
        // receive different/multiple email/notification
        foreach ($this->getReminderGroupTasks() as $groupTask) {
            $this->logger->debug(
                sprintf(
                    "Processing task [%d] reminder_date [%s] from Rep [%d]",
                    $groupTask['id'],
                    $groupTask['reminder_date'],
                    $groupTask['preferred_user_id']
                )
            );

            // If both method failed, we will try again (since we don't use reminderDate)
            // If it's only one (method), it's enough for us. We will consider it done and won't do it again.
            // Success is also represented by a communication method not being allowed by configs. This is acceptable
            $success = false;

            // Create structured $preferredUser / $retailerCustomer arrays
            // that are expected by the Send Email / Notification functions
            list($preferredUser, $retailerCustomer) = $this->splitArrayIntoUserAndRetailerCustomer($groupTask);

            try {
                $this->sendEmailReminder($groupTask, $preferredUser, $retailerCustomer);
                $success = true;
            } catch (\Exception $e) {
                $this->logger->error(sprintf(
                    "Group task [%d] reminder via email failed [%s]",
                    $groupTask['id'],
                    $e->getMessage()
                ));
            }

            try {
                $this->sendNotificationGroupTaskReminder($groupTask, $preferredUser, $retailerCustomer);
                $success = true;
            } catch (\Exception $e) {
                $this->logger->error(sprintf(
                    "Group task [%d] reminder via notification failed [%s]",
                    $groupTask['id'],
                    $e->getMessage()
                ));
            }

            if ($success) {
                // If both methods of communication (email / notif) are not allowed by configs, we will still reach this
                // point. This is accepted as the result of bad configuration and not system flaw.
                $this->updateLastReminderDate($groupTask);
                $total++;
            } else {
                $this->logger->error(
                    sprintf(
                        "Group Task [%d] reminder failed for both method. We will try again later",
                        $groupTask['id']
                    )
                );
            }
        }

        $this->logger->info(sprintf("Processed %d due/expired group tasks", $total));

        return $total;
    }

    public function autoResolve(int $groupTaskId, int $userId): void
    {
        if (!$this->configs['retailer.tasks.auto-resolve.is_enabled']) {
            return;
        }

        /** @var GroupTask|null $groupTask */
        $groupTask = $this->groupTasks->getById($groupTaskId);
        if ($groupTask === null) {
            return;
        }

        $groupTask->status = GroupTask::STATUS_RESOLVED;
        $groupTask->has_reply_from_customer = 0;
        $this->groupTasks->save($groupTask);

        $this->trackActivity(
            GroupTaskActivity::ACTION_RESOLVE,
            $groupTaskId,
            $userId,
            '',
            '',
        );
    }

    /**
     * Process all the tasks that have reach the "auto dismiss date"
     *
     * @throws \Exception
     */
    public function autoDismiss()
    {
        /** @var Rep @admin */
        $admin = $this->repsManager->getOneOrNull(['user_login' => $this->configs['sfadmin.user_login']]);
        if ($admin === null) {
            $this->logger->error(
                sprintf(
                    "Can not auto-dismiss group tasks, no admin user [%s] found.",
                    $this->configs['sfadmin.user_login']
                )
            );
        }
        $repository = $this->groupTasks->getRepository();
        $count = 0;
        foreach ($this->getAutoDismissGroupTasks() as $groupTask) {
            $repository->beginTransaction();
            try {
                $query =
                    "UPDATE sf_group_tasks t
                    SET  t.status = :dismissed, t.updated_at = :currentTime
                    WHERE t.id = :groupTaskId";
                $repository->executeUpdate(
                    $query,
                    [
                        'groupTaskId'    => $groupTask['id'],
                        'currentTime'    => Carbon::now('UTC')->toDateTimeString(),
                        'dismissed'      => GroupTask::STATUS_DISMISSED,
                    ]
                );

                $this->trackActivity(
                    GroupTaskActivity::ACTION_AUTO_DISMISS,
                    (int)$groupTask['id'],
                    (int)$admin->ID,
                    '',
                    'Auto-dismissed by System.'
                );
                $repository->commit();
                $count++;
            } catch (\Exception $e) {
                $repository->rollback();
                $this->logger->error(
                    sprintf("Can not auto-dismiss group tasks, error: %s.", $e->getMessage())
                );
            }
        }
        return $count;
    }

    /**
     * Get all tasks that need to be reminded
     *
     * Since we don't want to get all in memory, use generator
     *
     * @return \Generator
     */
    private function getReminderGroupTasks()
    {
        // Get all tasks that need to be reminded. GetAll() doesn't support gt, use qB
        /** @var QueryBuilder $qb */
        $qb = $this->groupTasks->getRepository()->getQueryBuilder();

        // Force second to be 00 so we never double the task reminder
        // $now = gmdate('Y-m-d H:i:00');
        $now = Carbon::now('UTC')->startOfMinute();

        // Use configs in case in the future we want to change the frequency of the cron
        $interval = $this->configs['sf.group_tasks.reminder.cron'];
        $nextExecutionDate = $now->copy()->addMinutes($interval);

        // $date = new DateTime($now, new \DateTimeZone('UTC'));
        // $date->add(new DateInterval($interval));
        // $nextExecutionDate = $date->format('Y-m-d H:i:00');
        $this->logger->debug(
            sprintf(
                "Will process the task from [%s] to [%s]",
                $now->toDateTimeString(),
                $nextExecutionDate->toDateTimeString()
            )
        );

        // All the fields coming out of this query will be in one array, we have to make sure that there
        // is no name contention. Such as the id / ID fields, task.type vs customer.type, etc...
        // Do not to use alias.* in the select statement. Check the expected fields in validateTaskUserCustomerFields
        $qb->select([
                'gt.id',
                'gt.store_id',
                'gt.reminder_date',
                'gt.details',
                'gt.customer_id',
                'gt.last_reminder_date',
                'gt.preferred_user_id',

                'u.user_login',
                'u.user_email',

                'rc.customer_id as customerId',
                'rc.first_name as retailerCustomerFirstName',
                'rc.last_name as retailerCustomerLastName',
                'rc.email as retailerCustomerEmail',
                'rc.phone as retailerCustomerPhone',

                // What should be the value if there's not first name. Use user_alias for now
                'COALESCE(um.meta_value, u.user_alias) as userFirstName',
                's.timezone as timezone',
                's.name as store',
        ])->from('sf_group_tasks', 'gt')
        ->innerJoin('gt', 'wp_users', 'u', "gt.preferred_user_id = u.ID")
        ->innerJoin('gt', 'sf_store', 's', 's.store_id = gt.store_id')
        ->leftJoin('gt', 'sf_retailer_customers', 'rc', "rc.customer_id = gt.customer_id")
        ->leftJoin('u', 'wp_usermeta', 'um', "u.ID = um.user_id AND um.meta_key = 'first_name'")
        ->where($qb->expr()->andX(
            $qb->expr()->eq('gt.status', $qb->expr()->literal(GroupTask::STATUS_UNRESOLVED)),
            $qb->expr()->lt('gt.reminder_date', $qb->expr()->literal($nextExecutionDate->toDateTimeString())),
            $qb->expr()->orX(
                // New task (Not reminded yet)
                $qb->expr()->isNull('gt.last_reminder_date'),
                // Old task (Reminded)
                // gte() is important now to cover the edge case where last_reminder_date happen in the first second
                $qb->expr()->gte('gt.reminder_date', sprintf('gt.last_reminder_date + INTERVAL %d MINUTE', $interval))
            )
        ));

        $stmt = $qb->execute();

        while ($row = $stmt->fetch()) {
            yield $row;
        }
    }

    /**
     * Get all tasks that need to be auto-dismissed
     *
     * Since we don't want to get all in memory, use generator
     *
     * @return \Generator
     */
    private function getAutoDismissGroupTasks()
    {
        // Get all tasks that need to be reminded. GetAll() doesn't support gt, use qB
        /** @var QueryBuilder $qb */
        $qb = $this->groupTasks->getRepository()->getQueryBuilder();

        $now = Carbon::now('UTC');

        $qb->select([
                'gt.id',
                'gt.status',
                's.timezone',
        ])->from('sf_group_tasks', 'gt')
        ->innerJoin('gt', 'sf_store', 's', 's.store_id = gt.store_id')
        ->where($qb->expr()->andX(
            $qb->expr()->notIn('gt.status', ':resolutionStatuses'),
            $qb->expr()->isNotNull('gt.auto_dismiss_date'),
            $qb->expr()->lt("CONVERT_TZ(gt.auto_dismiss_date, s.timezone, 'UTC')", ':currentTime'),
        ))->setParameters(
            [
                'resolutionStatuses' => GroupTask::$resolutionStatuses,
                'currentTime' => $now->toDateTimeString(),
            ],
            [
                'resolutionStatuses' => \Doctrine\DBAL\Connection::PARAM_STR_ARRAY
            ]
        );

        $stmt = $qb->execute();

        while ($row = $stmt->fetch()) {
            yield $row;
        }
    }

    /**
     * Function attempts to split the $fields array into a valid $preferredUser and $customer array
     * All important fields for Email / Notification generation are included
     * @param  array  $fields Generic array with User and Retailer Customer data
     * @return array          With first entry representing $preferredUser and second $customer
     */
    private function splitArrayIntoUserAndRetailerCustomer(array $fields)
    {
        // Create structured $preferredUser / $retailerCustomer arrays that are expected by
        // sendEmailTaskReminder & sendNotificationGroupTaskReminder
        $preferredUser = [];
        if (!empty($fields['preferred_user_id'])) {
            $preferredUser = [
                'id' => $fields['preferred_user_id'],
                'user_login' => $fields['user_login'],
                'user_email' => $fields['user_email'],
                'first_name' => $fields['userFirstName'],
                'timezone' => $fields['timezone'],
                'store' => $fields['store'],
            ];
        }

        $retailerCustomer = [];
        if (!empty($fields['customerId'])) {
            $retailerCustomer = [
                'customer_id' => $fields['customerId'],
                'first_name' => $fields['retailerCustomerFirstName'] ?? null,
                'last_name' => $fields['retailerCustomerLastName'] ?? null,
                'email' => $fields['retailerCustomerEmail'] ?? null,
                'phone' => $fields['retailerCustomerPhone'] ?? null,
            ];
        }
        return [$preferredUser, $retailerCustomer];
    }

/**
     * Send email to the rep with task information
     *
     * @param array $groupTask     Task info
     * @param array $preferredUser     User info
     * @param array $customer Customer info
     * @return bool True if Email sent, False if Email sending disabled by config
     * @throws \Exception
     */
    private function sendEmailReminder(array $groupTask, array $preferredUser, array $retailerCustomer)
    {
        // Check for required fields
        $this->validateTaskUserCustomerFields($groupTask, $preferredUser, $retailerCustomer);

        $permissionKey = 'sf.group_tasks.reminder.emails_enabled';
        if (!$this->checkConfig($permissionKey)) {
            return false;
        }

        // If the task is associated to a customer, we have a different layout. Used raw twig template in nunjucks
        $retailerCustomerName = '';

        $subject = "[Salesfloor] Group Task Reminder";
        if (!empty($retailerCustomer)) {
            $retailerCustomerName = $retailerCustomer['first_name'] . ' ' . $retailerCustomer['last_name'];
            if (!empty(trim($retailerCustomerName))) {
                $subject .= ' for ' . $retailerCustomerName;
            }
        }

        // Detect the locale of the rep
        $locale = $this->multiLang->getLocaleFromUserOrStore($preferredUser['id'], null);

        $preferredUserId = $preferredUser['id'] ?? null;
        // No email send if no preferred user id;
        if ($preferredUserId === null) {
            return;
        }
        if (!$this->repsManager->isEligibleForEmailNotification($preferredUserId)) {
            $reminderDate = $this->multiLang->getLocalizedDate(
                new \DateTime($groupTask['reminder_date']),
                $locale,
                IntlDateFormatter::LONG,
                IntlDateFormatter::LONG
            );

            // Prepare the notification message
            $notificationService = $this->notificationService;
            $notificationService->setTitle('title.group_task.reminder');
            $notificationService->setBodyListInfo('group_task.due-date', $reminderDate);
            $notificationService->setCustomerName($retailerCustomerName);
            $notificationService->setCustomerEmail($retailerCustomer['email']);
            $notificationService->setCustomerPhone($retailerCustomer['phone']);
            $notificationService->setBodyListInfo(
                'group_task.detail',
                str_replace("\n", "<br>", $groupTask['details'])
            );

            // Send the notification message
            $this->messagesManager->createNotificationSystemMessage(
                $preferredUserId,
                $notificationService->getTitle($locale),
                $notificationService->getBodyList($locale)
            );

            return;
        }

        $template = $this->twig->render(
            $this->multiLang->addLocaleToEmailTemplate('emails/common/reminder_group_task.html', $locale),
            [
                'retailerCustomer' => $retailerCustomer,
                'customerNameIsEmpty' => empty(trim($retailerCustomerName))
            ]
        );

        // Convert date to the proper timezone (the one from the store or Montreal if empty)
        // WE SHOULD display proper information in email (EST) for example so they have all the information
        $preferredUser['timezone'] = $preferredUser['timezone'] ?: 'America/Montreal';

        $template = str_replace([
            '{USER_FIRST_NAME}',
            '{VIEW_TASK_LINK}',
            '{RETAILER_PRETTY_NAME}',
            '{RETAILER_CUSTOMER_NAME}',
            '{EXPIRATION_DATE}',
            '{TASK_DETAIL}',
            '{BACK_OFFICE_PAGE}',
            '{RETAILER_ID_STR}',
        ], [
            $preferredUser['first_name'],
            $this->getDeepLink($groupTask),
            $this->configs['retailer.pretty_name'],
            $retailerCustomerName,
            // Try to display locale based time
            Util::getLocaleTime(
                '%A %e %B %Y, %I:%M %p',
                Util::getTimezoneTime($groupTask['reminder_date'], $preferredUser['timezone'])
            ),
            $groupTask['details'],
            $this->configs['salesfloor_storefront.host'] . '/backoffice',
            $this->configs['retailer.idstr'],
        ], $template);

        $message = [
            'email'                => $preferredUser['user_email'],
            'subject'              => $subject,
            'sender_email'         => $this->configs['retailer.emails.no_reply_address'],
            'sender_name'          => $this->configs['retailer.emails.no_reply_name'],
            'html_message'         => $template,
            'track_clicks_in_html' => false,
            MailClient::PARAMS_KEY_CATEGORIES => [
                MailClient::CATEGORY_TRANSACTIONAL
            ]
        ];
        $this->emailService->sendRelay($message);
        return true;
    }

    /**
     * Function validates whether the $task + $preferredUser + $retailerCustomer arrays that are used to
     * Send Email / Notifications for reminders contain all the necessary fields. If a field
     * is missing an Exception will be thrown.
     * @param  array     $task     Array of task information
     * @param  array     $preferredUser     Array of user information
     * @param  array     $retailerCustomer Array of customer information
     * @throws Exception
     */
    private function validateTaskUserCustomerFields(array $groupTask, array $preferredUser, array $retailerCustomer)
    {
        $required = [
            'groupTask' => [
                'id',
                'details',
                'reminder_date',
                'customer_id',
            ],
            'preferredUser' => [
                'id',
                'user_email',
                'user_login',
                'first_name',
                'timezone',
                'store'
            ],
            'retailerCustomer' => [
                'customer_id',
                'first_name',
                'last_name',
                'email',
                'phone'
            ],
        ];
        foreach ($required as $variable => $fields) {
            // In the case of retailer customer array, either we have it or we don't
            if ($variable !== 'groupTask' && empty($$variable)) {
                continue;
            }
            foreach ($fields as $field) {
                if (!in_array($field, array_keys($$variable))) {
                    throw new \Exception(
                        "Missing required field '$field' from the '$variable' variable for sending Task Reminder"
                    );
                }
            }
        }
    }

    /**
     * Returns the value of the requested config $key, or throws an exception if undefined
     * @param $key
     * @return mixed
     * @throws \Exception
     */
    private function checkConfig($key)
    {
        if (!$this->configs->offsetExists($key)) {
            throw new \Exception('Requested config key does not exist: ' . $key);
        }
        return $this->configs[$key];
    }

    /**
     * Get deeplink url or desktop url
     *
     * @param $groupTask
     *
     * @return mixed|string
     */
    private function getDeepLink($groupTask)
    {
        $deepLinks = new DeepLinks(
            $this->configs,
            new MobileBuilds($this->configs, $this->cloudStorageFactory->getClient('mobile_s3_bucket'))
        );

        return $deepLinks->generate('GROUP_TASK_REMINDER', '', $groupTask['id']);
    }

    /**
     * Send a notification to the rep
     *
     * @param array $groupTask
     * @param array $preferredUser
     * @param array $retailerCustomer
     * @return bool True if Notification sent, False if notification sending disabled by config
     */
    private function sendNotificationGroupTaskReminder(array $groupTask, array $preferredUser, array $retailerCustomer)
    {
        $preferredUserId = $preferredUser['id'] ?? null;
        if ($preferredUserId === null) {
            return;
        }

        // Check for required fields
        $this->validateTaskUserCustomerFields($groupTask, $preferredUser, $retailerCustomer);

        $permissionKey = 'sf.group_tasks.reminder.notifications_enabled';
        if (!$this->checkConfig($permissionKey)) {
            return false;
        }

        $notification = [
            'inapp' => [
                'alertBox'     => 'false',
                'event_action' => 'group_task_reminder',
                'group_task_id' => $groupTask['id'],
                'force_inapp'  => 'true',
                'sound'        => 'true',
            ],
        ];

        // Detect the locale of the rep
        $locale = $this->multiLang->getLocaleFromUserOrStore($preferredUserId, null);

        // Notification message is a bit different if it's associated to a customer
        $getNotificationMessage = function () use ($retailerCustomer, $locale) {
            $retailerCustomerName = trim($retailerCustomer['first_name'] . ' ' . $retailerCustomer['last_name']);
            if (!empty($retailerCustomerName)) {
                return $this->translator->trans(
                    self::NOTIFICATION_MESSAGE_REMINDER_RETAILER_CUSTOMER,
                    ['%name%' => $retailerCustomerName],
                    null,
                    $locale
                );
            } else {
                return $this->translator->trans(self::NOTIFICATION_MESSAGE_REMINDER, [], null, $locale);
            }
        };

        // TODO : Stop calling backoffice and create a service for that
        $pushUrl = 'pushNotification/firebase/publish';
        $notification['reps'] = [$preferredUser['user_login'] => true];
        $notification['message'] = $getNotificationMessage();

        $this->salesfloorApi->insertJSON($pushUrl, $notification);
        return true;
    }

    /**
     * Keep track of the last time the cron ran on this task, so if we want, we could run the cron multiple time with
     * any impact (multiple email) being sent
     *
     * @param array $groupTask
     *
     * @throws \Exception
     */
    private function updateLastReminderDate(array $groupTask)
    {
        /** @var GroupTask|null $groupTask */
        $groupTask = $this->groupTasks->getOneOrNull([
            'id' => $groupTask['id']
        ]);

        if (empty($groupTask)) {
            $errorMessage = sprintf("The group task [%d] you want to process doesn't exist anymore", $groupTask['id']);
            throw new \Exception($errorMessage);
        }

        $groupTask->last_reminder_date = Carbon::now('UTC')->toDateTimeString();
        $this->groupTasks->save($groupTask);
    }
}
