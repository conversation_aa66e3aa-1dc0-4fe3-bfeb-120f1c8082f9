<?php

/**
 * MySQL Repository
 *
 * Copyright 2014 - Salesfloor
 */

namespace Salesfloor\Services;

use Salesfloor\Models\Base;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Doctrine\DBAL\Exception\ConnectionLost;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Query\QueryBuilder;

class MySQLRepository
{
    /**
     * A MySQL DB Connection
     *
     * @var Connection
     */
    protected $connection;

    private $connectionParams;

    const DELETED_TABLE_PREFIX = 'deleted_';
    const MAIN_SELECT_TABLE_ALIAS = 'm';

    // all const of configs can create&move to mysql config class if need
    /**
     * Group concat default limit is 1024.
     * If we reach the max size of packet ( 4194304 ) we will need to make multiple queries for variants
     */
    const CONFIG_KEY_GROUP_CONCAT_MAX_LEN = 'group_concat_max_len';

    /**
     * Constructor
     *
     * @param string $host A host
     * @param string $username A username
     * @param string $password A password
     * @param string $db A database
     * @param string $port A port
     */
    public function __construct($host, $username, $password, $db, $port, $slavesConfig = null)
    {
        // Default master mysql connection
        $this->connectionParams = array(
            'dbname' => $db,
            'user' => $username,
            'password' => $password,
            'host' => $host,
            'port' => $port,
            'driver' => 'pdo_mysql',
            'charset' => 'utf8mb4',
            // This is to enforce backward compatibility with previous PDO version.
            'driverOptions' => [
                \PDO::ATTR_STRINGIFY_FETCHES => true
            ],
        );

        // Slave mysql connection if slaves are defined
        if (isset($slavesConfig)) {
            $this->connectionParams = [
                'wrapperClass' => 'Doctrine\DBAL\Connections\MasterSlaveConnection',
                'driver' => 'pdo_mysql',
                'master' => $this->connectionParams,
                'slaves' => $slavesConfig,
            ];
        }
    }

    /**
     * Some query need change mysql config during connection
     *
     * ex: group_concat_max_len = 1024 by default,
     * some scenarios(ContactExporter query about sf_customer_notes) need to increase if some fields over length
     *
     * @param array $configs
     */
    public function executeSessionConfigs(array $configs)
    {
        $sql = "";
        foreach ($configs as $itemKey => $itemValue) {
            $sql .= sprintf("SET SESSION %s = %s; ", $itemKey, $itemValue);
        };

        $this->executeQuery($sql);
    }

    public function enableOnlyFullGroupBy(bool $previousState)
    {
        // If you didn't remove the flag previously, don't set it back.
        // It means, somewhere higher in the chain did it and will take care of put it back.
        if ($previousState) {
            $this->executeQuery(
                sprintf(
                    "SET SESSION sql_mode = %s",
                    "concat_ws(',', (SELECT @@sql_mode), 'ONLY_FULL_GROUP_BY')"
                )
            );
        }
    }

    public function disableOnlyFullGroupBy(): bool
    {
        // We have some edgecase with nested called to this function.
        // At the end, if it was already disabled, we don't want to enable it back.
        $result = $this->executeQuery('select @@sql_mode')->fetchFirstColumn()[0] ?? '';
        if (str_contains($result, 'ONLY_FULL_GROUP_BY')) {
            $this->executeQuery(
                sprintf(
                    "SET SESSION sql_mode = %s",
                    "(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))"
                )
            );
            return true;
        } else {
            return false;
        }
    }

    public static function escIdent($str)
    {
        $escd = '`' . str_replace(array('\0', '`'), array('', '``'), $str) . '`';
        if (strlen($escd) > 64) {
            return false;
        }

        return $escd;
    }

    /**
     * Destructor
     **/
    public function __destruct()
    {
        $this->disconnect();
    }

    private static function processComplexQuery($queryBuilder, $value, $model = null, $locale = null, $defaultLocale = null)
    {
        $orStatement = $queryBuilder->expr()->orX();

        $statements = explode('||', $value);

        foreach ($statements as $statement) {
            $statementParts = explode('=', $statement);

            $field = $statementParts[0];
            $value = $statementParts[1];

            if (
                !empty($locale) && $locale != $defaultLocale && $model->isI18n() && $locale != Lang::LOCALE_ALL &&
                $model->isI18nColumn($field)
            ) {
                $field = static::addColumnPrefix($field, $model->getI18nTableName());
            } else {
                $field = static::addColumnPrefix($field);
            }

            if ($value === "null") {
                $orStatement->add($queryBuilder->expr()->isNull($field));
            } elseif (preg_match('/like\(([^\)]+)\)/', $value, $matches)) {
                $value = $matches[1];
                $orStatement->add($queryBuilder->expr()->like($field, $queryBuilder->expr()->literal('%' . $value . '%')));
            } elseif (isset($value[0]) && $value[0] === "^") {
                $value = substr($value, 1, strlen($value));
                $orStatement->add($queryBuilder->expr()->neq($field, $queryBuilder->expr()->literal($value)));
            } else {
                $orStatement->add($queryBuilder->expr()->eq($field, $queryBuilder->expr()->literal($value)));
            }
        }

        return $orStatement;
    }

    /**
     * Set filters to a query builder
     *
     * @param QueryBuilder $queryBuilder
     * @param $className
     * @param $filters
     * @param string $op
     * @param null $whereStatement
     * @param null $model
     * @param null $defaultLocale
     * @param null $locale
     * @throws \Exception
     */
    public static function setQueryFilters(&$queryBuilder, $className, $filters, $op = "select", &$whereStatement = null, $model = null, $defaultLocale = null, $locale = null)
    {
        $joins = [];
        if (isset($filters['kludge']) && $filters['kludge'] == "relationship-aware") {
            $joins = $filters['rel'];
            $filters = $filters['scalar'];
        }

        $addWhere = false;
        if (!$whereStatement) {
            $whereStatement = $queryBuilder->expr()->andX();
            $addWhere = true;
        }

        $prefix = ($op == "select" ? "m." : "");

        foreach ($joins as $join) {
            list($rel, $v) = $join;
            if ($rel['table'] == $className) {
                $whereStatement->add($queryBuilder->expr()->eq($prefix . $rel['col'], $queryBuilder->expr()->literal($v)));
            } else {
                if ($op != "select") {
                    throw new \Exception("can't $op with a JOIN (yet)", 500);
                }
                $queryBuilder->join('m', $rel['table'], $rel['table'], "{$rel['table']}.{$rel['thisCol']} = m.{$rel['idCol']} AND {$rel['table']}.{$rel['col']} = " . $queryBuilder->expr()->literal($v));
            }
        }

        foreach ($filters as $field => $value) {
            $col = static::escIdent($field);
            if ($col !== false && ($value != "" || $value === 0)) {
                // Try to handle filtering with i18n table
                if (!empty($model)) {
                    if (!empty($locale) && $locale != $defaultLocale && $model->isI18n() && $locale != Lang::LOCALE_ALL && $model->isI18nColumn($field)) {
                        $col = static::addColumnPrefix($col, $model->getI18nTableName());
                    } else {
                        $col = static::addColumnPrefix($col, $prefix);
                    }
                } else {
                    // Backward compatibility when using own fetchAll() and calling prepareSelect() yourself
                     $col = static::addColumnPrefix($col, $prefix);
                }
                if (is_array($value)) {
                    if (array_intersect(['lte', 'lt', 'gte', 'gt'], array_keys($value))) {
                        foreach ($value as $operator => $expr) {
                            if ($operator == 'lte') {
                                $whereStatement->add($queryBuilder->expr()->lte($col, $queryBuilder->expr()->literal($expr)));
                            } elseif ($operator == 'lt') {
                                $whereStatement->add($queryBuilder->expr()->lt($col, $queryBuilder->expr()->literal($expr)));
                            } elseif ($operator == 'gte') {
                                $whereStatement->add($queryBuilder->expr()->gte($col, $queryBuilder->expr()->literal($expr)));
                            } elseif ($operator == 'gt') {
                                $whereStatement->add($queryBuilder->expr()->gt($col, $queryBuilder->expr()->literal($expr)));
                            }
                        }
                        continue;
                    }

                    $IN = "IN";
                    if (isset($value["kludge"]) && $value["kludge"] == "negate") {
                        $IN = "NOT IN";
                        unset($value["kludge"]);
                        if (!count($value)) {
                            continue; // there's nothing to exclude; filter out nothing.
                        }
                    }
                    if (!count($value)) {
                        $whereStatement->add("1=0"); // there's nothing in the empty set; filter out everything.
                        break;
                    }
                    $inParams = [];
                    $notInParams = [];
                    foreach ($value as $v) {
                        $param = static::getParamId();
                        if (strpos($v, '^') === 0) {
                            $queryBuilder->setParameter($param, substr($v, 1));
                            $notInParams[] = ":$param";
                        } else {
                            $queryBuilder->setParameter($param, $v);
                            $inParams[] = ":$param";
                        }
                    }
                    if (!empty($inParams)) {
                        $inExpr = "$col $IN(" . implode(",", $inParams) . ")";
                        $whereStatement->add($inExpr);
                    }
                    if (!empty($notInParams)) {
                        $notInExpr = "$col NOT IN(" . implode(",", $notInParams) . ")";
                        $whereStatement->add($notInExpr);
                    }
                } elseif ($field === "complex_query") {
                    $whereStatement->add(self::processComplexQuery($queryBuilder, $value, $model, $locale, $defaultLocale));
                } elseif ($field === "mandatory_fields") {
                    $mandatoryFields = !empty($filters['mandatory_fields']) ? explode(',', $filters['mandatory_fields']) : false;

                    if (!empty($mandatoryFields)) {
                        foreach ($mandatoryFields as $mandatoryField) {
                            $whereStatement->add($queryBuilder->expr()->neq(static::addColumnPrefix($mandatoryField, $prefix), $queryBuilder->expr()->literal('')));
                            $whereStatement->add($queryBuilder->expr()->isNotNull(static::addColumnPrefix($mandatoryField, $prefix)));
                        }
                    }
                } elseif ($value === "null") {
                    $whereStatement->add($queryBuilder->expr()->isNull($col));
                } elseif ($value === "^null") {
                    $whereStatement->add($queryBuilder->expr()->isNotNull($col));
                } elseif ($value === "empty") {
                    $whereStatement->add($queryBuilder->expr()->eq($col, $queryBuilder->expr()->literal('')));
                } elseif (preg_match('/like\(([^\)]+)\)/', $value, $matches)) {
                    $value = $matches[1];
                    $whereStatement->add($queryBuilder->expr()->like($col, $queryBuilder->expr()->literal('%' . $value . '%')));
                } elseif (is_string($value) && $value[0] === "^") {
                    $value = substr($value, 1, strlen($value));
                    $whereStatement->add($queryBuilder->expr()->neq($col, $queryBuilder->expr()->literal($value)));
                } else {
                    $whereStatement->add($queryBuilder->expr()->eq($col, $queryBuilder->expr()->literal($value)));
                }
            }
        }

        if ($addWhere && $whereStatement->count() > 0) {
            $queryBuilder->where($whereStatement);
        }
    }

    public static function setQueryOrder(&$queryBuilder, $sort)
    {
        if (!isset($sort)) {
            return;
        }
        if (!is_array($sort)) {
            $sort = [$sort];
        }
        foreach ($sort as $s) {
            $order = "ASC";
            if (substr($s, 0, 1) == '-') {
                $order = "DESC";
                $s = substr($s, 1);
            }
            $queryBuilder->addOrderBy($s, $order);
        }
    }

    public static function setGroupBy(&$queryBuilder, $groupsBy)
    {
        if (!isset($groupsBy)) {
            return;
        }

        if (!is_array($groupsBy)) {
            $groupsBy = [$groupsBy];
        }

        foreach ($groupsBy as $groupBy) {
            $queryBuilder->addGroupBy($groupBy);
        }
    }

    /**
     * Since we can't use default function from based because we don't use queryBuilder. Create our own one.
     * This will not include the values of the filters, they must be passed to the query during execution.
     *
     * @param string $alias
     * @param array $filters
     *
     * @return string
     */
    public function buildWhereClauseAsString($alias, array $filters)
    {
        $whereClause = '';
        if (!empty($filters)) {
            $whereClause = 'WHERE ';
            $whereList = [];

            foreach ($filters as $name => $value) {
                $whereList[] = $alias . '.' .  $name . '=?';
            }

            $whereClause .= implode(' AND ', $whereList);
        }

        return $whereClause;
    }

    /**
     * Create a customer paging clause
     *
     * @param string $alias
     * @param array $sort
     *
     * @return string
     */
    public function buildOrderByClauseAsString($alias, array $sort)
    {
        if (empty($sort)) {
            return '';
        }

        $sortClause = 'ORDER BY ';
        $sortList = [];
        foreach ($sort as $s) {
            $order = 'ASC';
            if (substr($s, 0, 1) == '-') {
                $order = 'DESC';
                $s = substr($s, 1);
            }
            $sortList[] = $alias . '.' . $s . ' ' . $order;
        }

        return $sortClause . implode(', ', $sortList);
    }

    /**
     * Create a customer paging clause
     *
     * @param int $page
     * @param int $perPage
     *
     * @return string
     */
    public function buildLimitClauseAsString(int $page, int $perPage)
    {
        $offset = $page * $page;

        return 'LIMIT ' . $offset . ',' . $perPage;
    }

    /**
     * @inheritdoc
     */
    public function count($className, $filters, $groupBy = null)
    {
        $queryBuilder = $this->prepareCount($className, $filters, $groupBy);
        $results = $this->executeCustomQuery($queryBuilder);

        return $results[0]['count'];
    }

    public function prepareCount($className, $filters, $groupBy = null)
    {
        $queryBuilder = $this->getQueryBuilder();

        $queryBuilder->select("COUNT(*) as count");
        $queryBuilder->from($className, 'm');

        static::setQueryFilters($queryBuilder, $className, $filters, "select", $whereStatement);
        static::setGroupBy($queryBuilder, $groupBy);

        return $queryBuilder;
    }

    /**
     * @inheritdoc
     */
    public function select($fields, $className, $filters, $page, $perPage, $sort = null, $groupBy = null, $isDistinct = false, $forceIndex = null)
    {
        $queryBuilder = $this->prepareSelect($fields, $className, $filters, $page, $perPage, $sort, $groupBy, $isDistinct, $forceIndex);

        return $this->executeCustomQuery($queryBuilder);
    }

    public function prepareSelect($columns, $table, $filters, $page, $perPage, $sort = null, $groupBy = null, $isDistinct = false, $forceIndex = null, $model = null, $defaultLocale = null, $locale = null)
    {
        $queryBuilder = $this->getQueryBuilder();

        $select = '';
        if ($isDistinct) {
            $select = 'DISTINCT ';
        }

        $select .= implode(',', array_map(
            function ($column) {
                    return static::addColumnPrefix($column);
            },
            $columns
        ));

        $queryBuilder->select($select);
        $queryBuilder->from($table, static::MAIN_SELECT_TABLE_ALIAS . $this->addForceIndex($forceIndex));

        static::setQueryFilters($queryBuilder, $table, $filters, "select", $whereStatement, $model, $defaultLocale, $locale);
        static::setGroupBy($queryBuilder, $groupBy);
        static::setQueryOrder($queryBuilder, $sort);

        if (isset($perPage) && $perPage > -1) {
            $queryBuilder->setFirstResult($page * $perPage);
            $queryBuilder->setMaxResults($perPage);
        }

        return $queryBuilder;
    }

    /**
     * Add the default prefix to the table only if there is no already a prefix in requested column
     * @param string $column column with prefix
     */
    private static function addColumnPrefix($column, $prefix = 'm')
    {
        if (static::columnHasPrefix($column)) {
            return $column;
        }

        // Remove last . in the prefix if needed
        $pos = strrpos($prefix, '.');

        if ($pos !== false) {
            $prefix = substr_replace($prefix, '', $pos, strlen($prefix));
        }

        return empty($prefix) ? $column : $prefix . '.' . $column;
    }

    /**
     * Detect if there is already a prefix in the column
     * We detect this form of column prefix.`columnname`
     * We can't only detect . (dot) bacause a column could contains a . (dot) in MySql
     */
    private static function columnHasPrefix($column)
    {
        return strpos($column, '.`') !== false;
    }


    /**
     * If needed, this method will automatically add SQL joins and COALESCE to manage getting translation
     *
     * @param  QueryBuilder $qb Querybuilder
     * @param  Base $model Model
     * @param  string $defaultLocale DefaultLocale
     * @param  array $selectableFields Fields for the query
     * @param  string $locale Locale
     * @param  boolean $isCount Are we in a context of a count SQL query
     * @param  boolean $isFallBack Fallback mode to get default locale if the requested locale doesn't exist,
     *         this value is from config and would be override if the model has a specific value
     * @param  string $forceI18nTable By default the translated table (i18n) is defined in the model. But sometims
     *                                         We need to force the i18n table name. Eg: when a model is shared amoung
     *                                         several tables (ProductPanel: sf_product +  sf_deal). We need to set the
     *                                         correct table depending on the context
     * @return QueryBuilder                    Querybuilder
     * @throws \Exception
     */
    public function prepareFetchAllI18nQuery($qb, $model, $defaultLocale, $selectableFields, $locale, $isCount = false, $isFallBack = false, $forceI18nTable = null)
    {
        // We verify that there is a locale param, if we need to translate and the model is translatable
        if (!empty($locale) && $locale != $defaultLocale && $model->isI18n() && $locale != Lang::LOCALE_ALL) {
            $i18nTableName = ((!empty($forceI18nTable)) ? $forceI18nTable : $model->getI18nTableName());
            $i18nKeys       = $model->getI18nKeys();
            $i18nKeyMaps    = $model->getI18nKeyMaps();

            // For count, we don't need to change the select
            if (!$isCount) {
                $fields = [];

                foreach ($selectableFields as $field) {
                    if ($model->isI18nColumn($field)) {
                        // Try to get the text for the requested locale, otherwise get the text from the default language
                        // need escape reserve mysql keyword, ex: sf_product_variant_attributes_i18n:group will crash if not escape
                        $fields[] = "COALESCE($i18nTableName.$field, m.$field) as `$field`";
                    } else {
                        // if this field has table alias already
                        if (strpos($field, '.') > 0) {
                            $fields[] = $field;
                        } else {
                            $fields[] = 'm.' . $field;
                        }
                    }
                }

                $qb->select($fields);
            }

            // We usually rely on a config to do the fallback to the default language.
            // However, it was decided that corporate task can be used even with ML on but without any ML data.
            // So we need to have an extra fallback only for this (for now) feature (╯°□°）╯ ┻━┻
            $isFallBack = $model->getI18nFallback() ?? $isFallBack;
            $joinType = $this->getI18nJoinType($isFallBack);

            $keyConditions = implode(' AND ', array_map(function ($key) use ($i18nTableName, $i18nKeyMaps) {
                // Sometimes, the key on main table and i18n table are not the same (By mistake)
                // We created a new mapping to solve this issue.
                if (!empty($i18nKeyMaps[$key])) {
                    return "m.{$i18nKeyMaps[$key]} = $i18nTableName.$key";
                }
                return "m.$key = $i18nTableName.$key";
            }, $i18nKeys));
            $qb->{$joinType}(
                'm',
                $i18nTableName,
                $i18nTableName,
                "$keyConditions AND $i18nTableName.locale = '$locale'"
            );
        }

        return $qb;
    }

    /**
     * For multilinguem with we can either be strict, meaning that if we request a table for a specific lanquage
     * we won't return any other language.
     * So we'll join using innerJoin otherwise, we'll fall back on the default
     * language.
     * @return string Type of join we want to use for queries
     */
    public function getI18nJoinType($isFallBack)
    {
        return ($isFallBack ? 'leftJoin' : 'innerJoin');
    }

    /**
     * Force an index
     * This is a hack because queryBuilder doesn't support forcing an index
     * We inject it after the table alias
     *
     * @param string $forceIndex $forceIndex
     */
    protected function addForceIndex($forceIndex)
    {
        return $forceIndex === null ? '' : ' FORCE INDEX(' . $forceIndex . ')';
    }

    /**
     * @inheritdoc
     */
    public function update($className, array $data, $primary_key = 'id')
    {
        $this->doUpdate($className, $data, $primary_key);
    }

    private function doUpdate($className, array $data, $primary_key = 'id')
    {
        if (!is_array($primary_key)) {
            $primary_key = array($primary_key);
        }

        $queryBuilder = $this->getQueryBuilder();

        $queryBuilder->update($className);

        $where = $queryBuilder->expr()->andx();
        foreach ($primary_key as $col) {
            $where->add($queryBuilder->expr()->eq("`$col`", $queryBuilder->expr()->literal($data[$col])));
            unset($data[$col]);
        }
        $queryBuilder->where($where);

        foreach ($data as $key => $value) {
            if ($value === null) {
                $queryBuilder->set("`$key`", 'NULL');
            } elseif (is_bool($value)) {
                $queryBuilder->set("`$key`", $value ? 1 : 0);
            } else {
                $queryBuilder->set("`$key`", $queryBuilder->expr()->literal($value));
            }
        }

        try {
            $affectedRows = $queryBuilder->execute($queryBuilder->getSql());
        } catch (\Exception $e) {
            error_log($e->getMessage());
            error_log($queryBuilder->getSql());
        }

        return true;
    }

    /**
     * @inheritdoc
     */
    public function insert($className, array $data)
    {
        $queryBuilder = $this->getQueryBuilder();

        $queryBuilder->insert($className);

        foreach ($data as $key => $value) {
            if (isset($value)) {
                if (is_bool($value)) {
                    $queryBuilder->setValue('`' . $key . '`', $value ? 1 : 0);
                } else {
                    $queryBuilder->setValue('`' . $key . '`', $queryBuilder->expr()->literal($value));
                }
            }
        }

        // To handle the case, when exception is thrown
        $affectedRows = 0;
        try {
            $affectedRows = $queryBuilder->execute($queryBuilder->getSql());
        } catch (\Exception $e) {
            error_log(
                sprintf(
                    "Could not create resource for classname [%s] and query [%s] with message [%s]",
                    $className,
                    $queryBuilder->getSql(),
                    $e->getMessage(),
                )
            );
        }

        if ($affectedRows == 1) {
            return $this->getConnection()->lastInsertId();
        } else {
            $message = sprintf(
                "Could not create resource for classname [%s] and data [%s]",
                $className,
                json_encode($data, JSON_UNESCAPED_UNICODE),
            );
            throw new \Exception($message, 400);
        }
    }

    public function insertMany($table, array $columns, array $rows, $ignoreError = false)
    {
        $values = $params = [];
        $n = count($columns);

        if (!count($rows)) {
            return;
        }
        foreach ($rows as $i => $row) {
            $l = count($row);
            if ($l != $n) {
                throw new \Exception("Bad number of values for row #$i; expected $n, have $l.", 500);
            }
            $params[] = "(" . implode(",", array_fill(0, $l, "?")) . ")";
            $values = array_merge($values, $row);
        }
        $ignore = $ignoreError === true ? 'IGNORE' : '';
        $q = "INSERT $ignore INTO $table (" . implode(",", $columns) . ") VALUES " . implode(",", $params);
        $n = $this->getConnection()->executeUpdate($q, $values);
        if ($n != count($rows) && $ignoreError === false) {
            throw new \Exception("Failed insert to $table", 500);
        }
    }

    public function updateMany($table, array $patch, array $filters, $expectedCount = "required")
    {
        // WESHOULD: ensure that $col is a valid identifier
        // (I think they're whitelisted for now, so we're not in any danger)
        if (!count($patch)) {
            // WESHOULD: take $expectedCount into account
            return;
        }
        $qb = $this->getQueryBuilder();
        $qb->update($table);
        $params = [];
        foreach ($patch as $col => $val) {
            $qb->set($col, ":$col");
            $qb->setParameter(
                $col,
                $val,
                is_bool($val) ? ParameterType::BOOLEAN : ParameterType::STRING
            );
        }
        static::setQueryFilters($qb, $table, $filters, "update");

        $n = $qb->execute();
    }

    public function deleteMany($table, array $filters)
    {
        if (!count($filters)) {
            return;
        }

        $qb = $this->getQueryBuilder();
        $qb->delete($table);
        static::setQueryFilters($qb, $table, $filters, "delete");

        return $qb->execute();
    }

     /**
     * This method allowes to archive rows of a table before deleting them
     * In case of errors we rollback.
     *
     * Prerequisite: we need an archive table named deleted_[nameOfTheTable] or sf_deleted_[nameOfTheTable]
     * 2nd option in case there is already a sf_ prefix.
     *
     * @param  string $table    Table name
     * @param  array  $filters  Array of filters
     * @return boolean          Result of the query
     */
    public function deleteManyWithArchive($table, array $filters)
    {
        if (!count($filters)) {
            return;
        }

        $this->getConnection()->beginTransaction();

        $return = null;
        try {
            $this->archiveRows($this->getDeletedTableName($table), $table, $filters);
            $return = $this->deleteMany($table, $filters);
            $this->getConnection()->commit();
        } catch (\Exception $e) {
            $this->getConnection()->rollback();
            error_log('Fail during deletion with archive: (table: ' . $table . ') - ' . $e->getMessage());
            throw new \Exception('An error occurred during suppression', 500);
        }

        return $return;
    }

    /**
     * Define the archive table name for a table
     * @param  string $tableName
     * @return string            Archive table name
     */
    private function getDeletedTableName($tableName)
    {
        $sfTableNamePattern = '/^sf_((\w|\-)+)/';
        if (preg_match($sfTableNamePattern, $tableName, $matches)) {
            if (isset($matches[1])) {
                return 'sf_' . self::DELETED_TABLE_PREFIX . $matches[1];
            }
        }

        return self::DELETED_TABLE_PREFIX . $tableName;
    }

    /**
     * This method allowes to archive rows of a table before deleting
     * @param  string $toTable   destination table
     * @param  string $fromTable
     * @param  array $filters    Filters to apply the copy
     */
    public function archiveRows($toTable, $fromTable, $filters)
    {
        $joinPreds = [];
        foreach ($filters as $key => $val) {
            $joinPreds[] = "$key = '$val' ";
        }

        $insertQuery = "INSERT IGNORE INTO `$toTable` SELECT *, NOW() as deleted_at FROM `$fromTable` ";

        if (!empty($joinPreds)) {
            $insertQuery .= " WHERE " . implode(" AND ", $joinPreds);
        }

        if (!$this->getConnection()->query($insertQuery)) {
            throw new \Exception("Fail to execute archive query", 500);
        }
    }

    /**
     * Get an instance of a query builder
     *
     * @return QueryBuilder A query builder instance
     */
    public function getQueryBuilder()
    {
        return $this->getConnection()->createQueryBuilder();
    }

    /**
     * Execute a custom query
     *
     * @param QueryBuilder A query builder instance
     *
     * @return array An array of key/value pairs
     */
    public function executeCustomQuery(QueryBuilder $queryBuilder)
    {
        $results = $queryBuilder->execute();

        if ($results instanceof \Doctrine\DBAL\Result) {
            $results = $results->fetchAllAssociative();
        }

        return $results;
    }

    /**
     * Attempts to insert a new row, does an update on insert failure due to duplicate key.
     * Nota bene: for this to work, your targeted table must have a UNIQUE INDEX constraint
     * which on duplication will trigger the update.
     *
     * @param string $className The name of the model class
     * @param array $data Field names and values
     * @param array $updatableFields Fields that should be updated on insert failure
     * @return string
     */
    public function insertOnDuplicateUpdate($className, array $data, array $updatableFields)
    {
        $insertQueryBuilder = $this->getQueryBuilder();
        $insertQueryBuilder->insert($className);

        $updateQueryBuilder = $this->getQueryBuilder();
        $updateQueryBuilder->update($className);

        foreach ($data as $key => $value) {
            if (isset($value)) {
                $insertQueryBuilder->setValue('`' . $key . '`', $insertQueryBuilder->expr()->literal($value));

                if (in_array($key, $updatableFields)) {
                    $updateQueryBuilder->set("`$key`", $updateQueryBuilder->expr()->literal($value));
                }
            }
        }

        $insertQuery = $insertQueryBuilder->getSQL();
        $updateQuery = $updateQueryBuilder->getSQL();

        $insertQuery = str_replace('INSERT INTO', 'INSERT IGNORE INTO', $insertQuery);
        $updateQuery = str_replace('UPDATE ' . $className . ' SET', 'ON DUPLICATE KEY UPDATE', $updateQuery);
        $insertOnDuplicateUpdateQuery = "$insertQuery $updateQuery";

        try {
            $this->getConnection()->query($insertOnDuplicateUpdateQuery);
            return $this->getConnection()->lastInsertId();
        } catch (\Exception $e) {
            error_log("Failed during insert on duplicate update: $insertOnDuplicateUpdateQuery - " . $e->getMessage());
            throw new \Exception('An error occurred during insert on duplicate update', 500);
        }

        return null;
    }

    public function updateGroupConcatMaxLength($maxLength)
    {
        $this->executeSessionConfigs([
            self::CONFIG_KEY_GROUP_CONCAT_MAX_LEN => $maxLength
        ]);
    }

    public function executeSelect($queryBuilder, $filters = [])
    {
        return $this->executeCustomQuery($queryBuilder);
    }

    public function executeRawQuery($query)
    {
        if (!empty($query)) {
            $results = $this->getConnection()->query($query);
            return $results;
        } else {
            return false;
        }
    }

    private static $paramCounter = 0;
    private static function getParamId()
    {
        static::$paramCounter += 1;

        return "p" . static::$paramCounter;
    }

    public function executeUpdate($q, $params = [], array $types = [])
    {
        return $this->getConnection()->executeUpdate($q, $params, $types);
    }

    // Add transaction function
    public function beginTransaction()
    {
        $this->getConnection()->beginTransaction();
    }

    public function commit()
    {
        $this->getConnection()->commit();
    }

    public function rollbackAll()
    {
        // 0 == No transaction left
        while ($this->getConnection()->getTransactionNestingLevel() > 0) {
            $this->getConnection()->rollback();
        }
    }

    public function commitAll()
    {
        // 0 == No transaction left
        while ($this->getConnection()->getTransactionNestingLevel() > 0) {
            $this->getConnection()->commit();
        }
    }

    public function rollback()
    {
        $this->getConnection()->rollBack();
    }

    public function lastInsertId()
    {
        return $this->getConnection()->lastInsertId();
    }

    public function executeQuery($query, $params = array(), $type = array())
    {
        return $this->getConnection()->executeQuery($query, $params, $type);
    }

    public function disconnect()
    {
        if (isset($this->connection)) {
            $this->connection->close();
            $this->connection = null;
        }
    }

    /**
     * This is only use in customer importer.
     * This is probably to patch some edge case when the file was too big and connection
     * timeout after 8 hrs of inactivity because something was happening in another connection (MysqlConnection)
     * @deprecated This should not be used in general.
     */
    public function refresh()
    {
        // The best would be to not use this and rely on the "ConnectionLost" exception.
        $this->ping();
    }

    /**
     * Since ping is now removed from DBAL v3, and I don't want to change too much code,
     * Will just "fake" it with a "select 1".
     * @return void
     * @throws \Doctrine\DBAL\Exception
     */
    private function ping()
    {
        $connection = $this->getConnection();
        // This will create "SELECT 1";
        $query = $connection->getDatabasePlatform()->getDummySelectSQL();

        try {
            $connection->executeQuery($query);
        } catch (ConnectionLost $e) {
            // Error 2006 (Mysql has gone away)
            error_log("MySQL Repository - ping failed - will force disconnect");

            $this->disconnect();
            // You don't need to reconnect because getConnection() will do it for you
        }
    }

    private function getConnection()
    {
        if (!isset($this->connection)) {
            $this->connection = DriverManager::getConnection($this->connectionParams);
            // Allow nesting transactions with savepoints.
            // https://dev.mysql.com/doc/refman/8.0/en/savepoint.html
            // Doctrine takes care of nesting and setting savepoints as appropriate within transactions.
            $this->connection->setNestTransactionsWithSavepoints(true);
        }

        return $this->connection;
    }

    /**
     * Allow to add literal to array values
     *
     * [value1, value2] => ['value1', 'value2']
     *
     * @param QueryBuilder $qb  Querybuilder object
     * @param array $values     Values
     */
    public function addLiteralToArrayValues($qb, $values)
    {
        return array_map(
            function ($value) use ($qb) {
                return $qb->expr()->literal($value);
            },
            $values
        );
    }
}
