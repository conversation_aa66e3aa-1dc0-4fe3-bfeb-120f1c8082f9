<?php

namespace Salesfloor\Services\Queue;

use Psr\Log\LoggerInterface;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Services\DataProcessing\Attribution;
use Salesfloor\Services\ExtendedInsertQueryBuilder;
use Salesfloor\Services\MessageQueue\MessageQueueClient;
use Salesfloor\Services\Exceptions\GenericQueueException;
use Salesfloor\Models\SidebarEventLog as SidebarEventLogModel;

/**
 * NOTE: the class now used for both sidebar and contextual widget events.
 * However, we keep the class name is still sidebarEventQueue,
 * TODO : some logic should move to models
 */
class SidebarEventQueue extends Base
{
    protected $queueId;

    protected $deadLetterQueueId;
    private $sidebarReceiptHandles;
    private $sidebarEvents;
    private $lastPushTime;
    private $pushInterval;
    private $bulkSize;

    /** @var int $half */
    private $half;

    /** @var bool $ackExtend */
    private $ackExtend;

    protected $hideMessage;

    public const SF_EVENT_NEW_SIDEBAR_DESKTOP_CLICK = 'SIDEBAR_DESKTOP_CLICK';
    public const SF_EVENT_NEW_SIDEBAR_DESKTOP_VIEW = 'SIDEBAR_DESKTOP_VIEW';
    public const SF_EVENT_NEW_SIDEBAR_DESKTOP_MINIMIZE = 'SIDEBAR_DESKTOP_MINIMIZE';
    public const SF_EVENT_NEW_SIDEBAR_DESKTOP_MAXIMIZE = 'SIDEBAR_DESKTOP_MAXIMIZE';
    public const SF_EVENT_NEW_SIDEBAR_MOBILE_CLICK = 'SIDEBAR_MOBILE_CLICK';
    public const SF_EVENT_NEW_SIDEBAR_MOBILE_VIEW = 'SIDEBAR_MOBILE_VIEW';
    public const SF_EVENT_NEW_SIDEBAR_MOBILE_MAXIMIZE = 'SIDEBAR_MOBILE_MAXIMIZE';
    public const SF_EVENT_NEW_SIDEBAR_MOBILE_MINIMIZE = 'SIDEBAR_MOBILE_MINIMIZE';
    public const SF_EVENT_NEW_SIDEBAR_MOBILE_TAGLINE_MINIMIZE = 'SIDEBAR_MOBILE_TAGLINE_MINIMIZE';

    public const SF_EVENT_CONNECT2_DESKTOP_MENU = 'SIDEBAR_DESKTOP_CONNECT2_MENU';
    public const SF_EVENT_CONNECT2_MOBILE_MENU = 'SIDEBAR_MOBILE_CONNECT2_MENU';
    public const SF_EVENT_CONNECT2_DESKTOP_REQUESTS = 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS';
    public const SF_EVENT_CONNECT2_MOBILE_REQUESTS = 'SIDEBAR_MOBILE_CONNECT2_REQUESTS';
    public const SF_EVENT_CONNECT2_LIVECHAT = 'live_chat';
    public const SF_EVENT_CONNECT2_APPOINTMENT = 'appointment';
    public const SF_EVENT_CONNECT2_MESSAGE = 'message';
    public const SF_EVENT_CONNECT2_SUPPORT = 'support';




    const SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW = 'CONTEXTUAL_WIDGET_DESKTOP_VIEW';

    const SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK = 'CONTEXTUAL_WIDGET_DESKTOP_CLICK';

    const SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW = 'CONTEXTUAL_WIDGET_MOBILE_VIEW';

    const SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK = 'CONTEXTUAL_WIDGET_MOBILE_CLICK';

    const SF_EVENT_CONTEXTUAL_WIDGET_SUB_ACTION = [
        // default, automatically or unknown => '0' or null
        'landingPage'     => 1,
        'liveChat'        => 2,
        'appointment'     => 3,
        'personalShopper' => 4,
        'contactMe'       => 5,
    ];

    public const SF_EVENT_CONNECT2_MENU_SUB_ACTION = [
        self::SF_EVENT_CONNECT2_LIVECHAT => 1,
        self::SF_EVENT_CONNECT2_APPOINTMENT => 2,
        self::SF_EVENT_CONNECT2_MESSAGE => 3, // aka contact me
        self::SF_EVENT_CONNECT2_SUPPORT => 4
    ];

    public const SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION = [
        self::SF_EVENT_CONNECT2_LIVECHAT => 1,
        self::SF_EVENT_CONNECT2_APPOINTMENT => 2,
        self::SF_EVENT_CONNECT2_MESSAGE => 3, // aka contact me
        // Support sub action doesn't exist for requests
    ];

    // List of New Sidebar Event Actions.
    public const SIDEBAR_WIDGET_ACTION = [
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_CLICK,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_VIEW,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_MINIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_MAXIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_CLICK,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_VIEW,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_MAXIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_MINIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_TAGLINE_MINIMIZE,
    ];

    public const SIDEBAR_WIDGET_CONNECT2_ACTION = [
        self::SF_EVENT_CONNECT2_DESKTOP_MENU,
        self::SF_EVENT_CONNECT2_MOBILE_MENU,
        self::SF_EVENT_CONNECT2_DESKTOP_REQUESTS,
        self::SF_EVENT_CONNECT2_MOBILE_REQUESTS,
    ];

    const CONTEXTUAL_WIDGET_ACTION = [
        self::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
        self::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
        self::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
        self::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
    ];

    /**
     * Construct the Queue Instance and initialize needed variables
     * @param Configs $configs Configs instance
     * @param Attribution $attributionService
     * @param LoggerInterface $logger
     */
    public function __construct(
        Configs $configs,
        Attribution $attributionService,
        LoggerInterface $logger,
        MessageQueueClient $messageQueueClient
    ) {
        parent::__construct($configs, $attributionService, $logger, $messageQueueClient);

        $this->queueId = $configs['queue.sidebar-events.full_name'];
        $this->deadLetterQueueId = $configs['queue.sidebar-events.dead_letter_name'];
        $this->pushInterval = $configs['sidebar.pushing.interval'];
        $this->bulkSize = $configs['sidebar.pushing.bulk.size'];
        $this->hideMessage = $configs['sidebar.message.hide'];
        $this->ackExtend  = $configs['services.queue.ack-extend'];
        $this->half = 0;

        // Initialize lastPushTime on startup. For various
        // reachedPushingInterval and reachedHalfPushingInterval to
        // properly calculate when to do the 1st push. Else it wont
        // behave properly for the 1st push interval.
        $this->lastPushTime = strtotime("now");
    }

    /**
     * Function validates the received event, throwing exception if any required
     * fields are missing or contain invalid data (action received not in list)
     * Once validated, a newly structure event is created with generated timestamp,
     * and retailer / environment information. Once ready it is returned in json
     * @param  array  $params Original Event Array
     * @return string         JSON representation of the validated Event
     */
    public function serializeSend(array $params): string
    {
        foreach (['action', 'fingerprint'] as $requiredField) {
            if (!isset($params[$requiredField])) {
                throw new \Exception("Received Invalid Event, must contain `$requiredField` field");
            }
        }
        $params['action'] = strtoupper($params['action']);
        // Build final structure for event
        $event = [];
        if (
            !in_array($params['action'], self::SIDEBAR_WIDGET_ACTION) &&
            !in_array($params['action'], self::SIDEBAR_WIDGET_CONNECT2_ACTION) &&
            !in_array($params['action'], self::CONTEXTUAL_WIDGET_ACTION)
        ) {
            throw new \Exception("PUSH Event: Action {$params['action']} is not valid. Skipping");
        }

        if (in_array($params['action'], self::SIDEBAR_WIDGET_ACTION)) {
            $numericFields = ['action_id', 'fingerprint'];
        } elseif (
            in_array($params['action'], self::CONTEXTUAL_WIDGET_ACTION) ||
            in_array($params['action'], self::SIDEBAR_WIDGET_CONNECT2_ACTION)
        ) {
            $numericFields = ['fingerprint'];
        }

        // Check that fingerprint(and also check action_id if it is sidebar action) are numeric
        foreach ($numericFields as $numericField) {
            if (isset($params[$numericField]) && !is_numeric($params[$numericField])) {
                throw new \Exception("PUSH Event: $numericField field must be numeric");
            }
        }
        foreach (['action', 'action_id', 'fingerprint'] as $expectedField) {
            $event[$expectedField] = (isset($params[$expectedField]) ? $params[$expectedField] : null);
        }
        // Add timestamp manually to represent time received by server
        $event['timestamp'] = gmdate('U');
        $event['env'] = $this->env;
        $event['retailer'] = $this->retailer;

        $strPayload = json_encode($event);
        if ($strPayload === null) {
            throw new \Exception("Unable to queue up event, failure to json encode " . var_export($event, true));
        }
        return $strPayload;
    }

    /**
     * Function un-encodes the event string (json) received from the queue polling
     * It validates that the string is in the expected structure and contains the
     * minimum required fields for saving a valid event
     * @param  string $json String JSON representation of the event in the queue
     * @return array        Valid event array decoded from json input
     */
    public function unserializeSend($json): array
    {
        $payload = json_decode($json, true);
        if (!isset($payload)) {
            throw new \Exception("Bad payload; it's not JSON: $json");
        }

        // note: no need to check message_id, since it is not in the message body, it's a property of the queue meta data
        foreach (['action', 'fingerprint', 'timestamp'] as $k) {
            if (!isset($payload[$k])) {
                throw new \Exception("Bad payload; it has no '$k' property: $json");
            }
        }
        if (
            !in_array($payload['action'], self::SIDEBAR_WIDGET_ACTION) &&
            !in_array($payload['action'], self::SIDEBAR_WIDGET_CONNECT2_ACTION) &&
            !in_array($payload['action'], self::CONTEXTUAL_WIDGET_ACTION)
        ) {
            throw new \Exception("POP Event: Action {$payload['action']} is not valid. Skipping");
        }
        return $payload;
    }

    /**
     * @param  MysqlRepository $mysqlRepository Repository instance used for writing events
     * @param  integer $waitTimeSeconds Override for the length of polling in seconds
     * @param  callable|null $callback
     * @return bool|array Successfully saved event array or false in case of failure
     *         true in case of event found but deleted
     * @throws \Exception
     */
    public function processQueue(MysqlRepository $mysqlRepository, $waitTimeSeconds = null, $callback = null)
    {
        // Here we change the message visibility to ensure we can insert into the db since we are doing batches.
        // so that it won't be inserted twice into the db
        $messages = $this->pop($waitTimeSeconds, $this->hideMessage);
        $message = $messages[0];

        if (isset($message)) {
            // Save the last message handle just in case the execution is suddenly terminated
            // Can be used by the __destruct method to push this message back into the queue
            $this->lastReceiptHandle = $message['ReceiptHandle'];
            try {
                $data = $this->unserializeSend($message['Body']);

                $messageId = $this->queue->getMessageId($message);
                if (empty($messageId)) {
                    // in case after un-serialize, wrong data received at this place
                    if (empty($message['action']) || empty($message['timestamp']) || empty($message['fingerprint'])) {
                        throw new \Exception('Invalid message received from sidebar-events queue. Missing required fields. Deleting message.');
                    }
                    $messageId = $this->generateUniqid($data);
                }
                $data['message_id'] = $messageId;
            } catch (\Exception $unserializeException) {
                // TODO: QUESTION: Deleting a un parsable element means it wont go in dead-letter queue
                // is that expected?. This is what was already implemented. As part of GCP Miration
                // the goal is to have same functionality not improve.
                // $this->logger->info(
                //     'Could not delete unserialized message: ' .
                //     print_r($unserializeException->getMessage())
                // );
                $this->queue->deleteMessage($this->lastQueueUrl, $this->lastReceiptHandle);
                $this->lastReceiptHandle = null;
                // Returning true so that the Read Queue loop continues
                return true;
            }

            // Could throw an EventQueueDatabaseException which is not caught here
            // Should be caught by the calling script
            $ok = $this->handleQueueData($mysqlRepository, $data);

            if ($ok) {
                return $data;
            }

            // Also throw specific exception that should be caught by the calling function (ReadEventQueue.php)
            // It will know how to handle it (exit the script, restart by supervisor)
            throw new GenericQueueException('QueueException: Possible Database Error', 500);
        }

        // Disable the below logic by the reasons:
        // a. simplify: consuming message by queue only; b. message_id(generated by pub/sub) as unique identifier is always needed

        // push the inflight events in the queue when queue is empty
        // if (!empty($this->lastQueueUrl) && !empty($this->sidebarEvents)) {
        //     $status = $this->persistBulkSidebarEvents($mysqlRepository);
        //     $this->sidebarEvents = [];
        //     if ($status) { // Success we can delete
        //         if ($this->queue->canDeleteInBatches()) {
        //             // For now Leaving AWS implementation how it is. on GCP we will perform batch delete instead.
        //             $this->queue->deleteMessages($this->lastQueueUrl, $this->sidebarReceiptHandles);
        //             $this->sidebarReceiptHandles = [];
        //         } else {
        //             $this->postProcessMessagesFromQueue(function ($handle) {
        //                 $this->queue->deleteMessage($this->lastQueueUrl, $handle);
        //             }, 'delete message');
        //         }
        //     } else { // Some error with the DB, will retry later on
        //         $this->postProcessMessagesFromQueue(function ($handle) {
        //             $this->queue->changeMessageVisibility($this->lastQueueUrl, $handle, $this->hideMessage);
        //         }, 'change message visibility');
        //     }
        // }

        return false;
    }

    /**
     * Function to bulk insert sidebar event
     * @param  MysqlRepository $mysqlRepository Repository instance used for writing events
     * @param  array $event Array representation of event to log
     * @param  callable|null $callback
     * @return bool TRUE when successfully saved, FALSE on error
     */
    public function handleQueueData(MysqlRepository $mysqlRepository, $event, $callback = null): bool
    {
        $this->sidebarEvents[] = $event;
        $this->sidebarReceiptHandles[] = $this->lastReceiptHandle;
        $this->lastReceiptHandle = null;

        $currentTimestamp = strtotime("now");

        // Extend messages if we have reached at least half of the PushingInterval. This will
        // prevent from inserting messages twice in the Database if it took long time before grabbing expected BulkSize.
        // This would ensure we do not insert these message and
        // the postProcessMessageFromQueue might not be able to delete
        // the message if it was put back in the queue.
        if ($this->ackExtend && $this->reachedHalfPushingInterval($currentTimestamp)) {
            $this->extendMessageAck();
        }

        if ($this->couldPersistEventsAndFlush($currentTimestamp)) {
            $this->lastPushTime = $currentTimestamp;
            $this->half = 0; // Reset HALF
            $status = $this->persistBulkSidebarEvents($mysqlRepository);
            $this->sidebarEvents = [];
            if ($status) { // Success
                if ($this->queue->canDeleteInBatches()) {
                    // For now Leaving AWS implementation how it is. on GCP we will perform batch delete instead.
                    $this->queue->deleteMessages($this->lastQueueUrl, $this->sidebarReceiptHandles);
                    $this->sidebarReceiptHandles = [];
                } else {
                    $this->postProcessMessagesFromQueue(function ($handle) {
                        $this->queue->deleteMessage($this->lastQueueUrl, $handle);
                    }, 'delete message');
                }
            } else { // Some error, will retry upon next iteration
                if ($this->queue->canDeleteInBatches()) {
                    // In GCP and in AWS, we should not require to hideMessage.
                    // The Queue system would retry automatically
                    // as we already locked the message for X seconds when we did the this->pop(...) function.
                    // Queue system will retry it when it expires.
                    //
                    // For now Leaving AWS implementation how it is.
                } else {
                    $this->postProcessMessagesFromQueue(function ($handle) {
                        $this->queue->changeMessageVisibility($this->lastQueueUrl, $handle, $this->hideMessage);
                    }, 'change message visibility');
                }
            }

            return $status;
        }

        return true;
    }

    /**
     * function to check if the count of sidebarEvents array reached bulk size
     * @return bool
     */
    private function reachedBulkSize(): bool
    {
        return (count($this->sidebarEvents) >= $this->bulkSize);
    }

    /**
     * function to check if the current time reached the time to bulk insert events to db
     * @param int $currentTimestamp
     * @return bool
     */
    private function reachedPushingInterval($currentTimestamp): bool
    {
        return ($this->lastPushTime !== null && (($currentTimestamp - $this->lastPushTime) >= $this->pushInterval));
    }

    /**
     * function to check if the current time reached half ot the time before bulk insert events
     * Could be used by GCP To extend deadline of messages modifyAckDeadline
     * This can only be called once
     *
     * @param int $currentTimestamp
     *
     * @return bool
     */
    private function reachedHalfPushingInterval($currentTimestamp): bool
    {
        if ($this->half == 1) {
            return false;
        }

        if (isset($this->lastPushTime) && (($currentTimestamp - $this->lastPushTime) >= ($this->pushInterval / 2))) {
            $this->half = 1;
            return true;
        }

        return false;
    }

    /**
     * Bulk extend message Acknowledgement, prevent Queue to return the element to Queue
     */
    public function extendMessageAck()
    {
        if (!empty($this->lastQueueUrl)) {
            // Extend all messages
            // TODO: Performance improvement
            // Should we find out which one that we should Ack?
            // (I guess its all of them, i'm over thinking this)

            // For now we only re-ack the first 50% of the elements (lets not over think)
            // GCP maximum Message Lock is set to 10 Minutes (and cannot go beyond that).
            // But we can re-extend it if needed (up to 10 a maximum of Minutes again.)
            //
            // This allow to extend the message, in the event that we have reached the PushInterval
            // otherwise the messages would be put back in queue and processed by the next consumer / iteration
            // which would be bad.
            //
            //To prevent other consumers from processing the message again,
            // Amazon SQS sets a visibility timeout, a period of time during which Amazon SQS prevents other
            // consumers from receiving and processing the message.
            // The default visibility timeout for a message is 30 seconds. The minimum is 0 seconds.
            // The maximum is 12 hours

            // The loop above use $configs['sidebar.message.hide'] = 900; which is beyond the 10 Minutes.
            // for GCP we re-extend the message up to 10 minutes maximum.
            $this->queue->changeMessageVisibilityBatch(
                $this->lastQueueUrl,
                $this->sidebarReceiptHandles,
                $this->hideMessage
            );
        }
    }

    /**
     * Bulk persist sidebar events to DB with transaction
     * @param MysqlRepository $mysqlRepository
     * @return bool
     */
    public function persistBulkSidebarEvents(MysqlRepository $mysqlRepository): bool
    {
        $mysqlRepository->beginTransaction();

        try {
            if (!empty($this->sidebarEvents)) {
                $this->insertBulkSidebarEvents($mysqlRepository);
            }
            $mysqlRepository->commit();
            $this->logger->info(
                'Inserted Bulk ' .
                count($this->sidebarEvents) .
                ' Size of [' .
                $this->bulkSize .
                '] Of Entries'
            );
            return true;
        } catch (\Exception $e) {
            $mysqlRepository->rollback();
            $this->logger->error('Error Inserting Bulk Events With Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * @param callable $callback
     * @param string $action
     * @return void
     */
    private function postProcessMessagesFromQueue(callable $callback, string $action): void
    {
        if (!empty($this->lastQueueUrl)) {
            foreach ($this->sidebarReceiptHandles as $index => $handle) {
                $this->retryProcessingMessage($callback, $handle, $action);
                unset($this->sidebarReceiptHandles[$index]);
            }
            $this->lastReceiptHandle = null;
        }
    }

    /**
     * retry message processing(delete/change visibility), maximum attempt 2 times
     * only try once more if it's failed since we don't want to slow up the whole process
     * if processing the message failed 2 times, we'll skip this message instead of stopping the script
     * @param callable $callback
     * @param $handle
     * @param string $action
     * @return mixed
     */
    private function retryProcessingMessage(callable $callback, $handle, string $action)
    {
        $nbAttempt = 0;
        $maxAttempt = 2;

        for (; $nbAttempt < $maxAttempt; $nbAttempt++) {
            try {
                return $callback($handle);
            } catch (\Exception $e) {
                $nbAttempt++;
                $this->logger->info(
                    sprintf(
                        "Try $action attempt [%s] on [%s]",
                        $nbAttempt,
                        $maxAttempt
                    )
                );
                $this->logger->error($e);
            }
        }
    }

    /**
     * Bulk insert events to DB
     * @param MysqlRepository $mysqlRepository
     * @return void
     */
    private function insertBulkSidebarEvents(MysqlRepository $mysqlRepository): void
    {
        $qb = $mysqlRepository->getQueryBuilder();

        $insertBuilder = new ExtendedInsertQueryBuilder($qb);

        $insertFields = [
            "timestamp" => true,
            "fingerprint" => true,
            "action" => true,
            "action_id" => true,
            "message_id" => true,
        ];

        // when inserting data, we must use the default sf_sidebar_event_log_yyyymmdd table
        $insertBuilder
            ->setTable(SidebarEventLogModel::getEventTable(null))
            ->addInsertFields(array_keys($insertFields))
            ->setInsertIgnore(true)
        ;

        foreach ($this->sidebarEvents as $stat) {
            $values = $insertBuilder->extract(array_keys($insertFields), $stat);

            if (in_array($values['action'], self::CONTEXTUAL_WIDGET_ACTION)) {
                // future consideration to tracking more sub-type action
                $values['action_id'] = $this->getContextualWidgetActionId($values['action_id']);
            }

            if (in_array($values['action'], self::SIDEBAR_WIDGET_CONNECT2_ACTION)) {
                $values['action_id'] = $this->getSidebarWidgetConnect2ActionId($values['action_id']);
            }

            $insertBuilder->addValuesSet($values);
        }

        $insert = ($insertBuilder->prepare());

        $mysqlRepository->executeQuery($insert['query'], $insert['parameters']);
    }

    /**
     * Front-end send string, we will to convert it to int
     * no usage for now, keep for later more granular tracking of the contextual widget
     * @param $subAction
     * @return int|null
     */
    private function getContextualWidgetActionId($subAction)
    {
        if (isset(self::SF_EVENT_CONTEXTUAL_WIDGET_SUB_ACTION[$subAction])) {
            return self::SF_EVENT_CONTEXTUAL_WIDGET_SUB_ACTION[$subAction];
        }
        return null;
    }

    /**
     * Rely on sub action for menu click. Convert string to int to match DB format.
     *
     * @param string $subAction Name of the sub action
     *
     * @return int|null
     */
    private function getSidebarWidgetConnect2ActionId(string $subAction)
    {
        return self::SF_EVENT_CONNECT2_MENU_SUB_ACTION[$subAction] ?? null;
    }

    /**
     * A fallback unique id for the message, which is a combination of timestamp, fingerprint and action
     * With assumption: in one second, same fingerprint and action only count once
     * @param $message
     * @return string ex: SB_1555502589_28035733861242_18   (length limit: 32)
     */
    private function generateUniqid($message): string
    {
        $action = strtoupper($message['action']);

        if (in_array($action, self::SIDEBAR_WIDGET_ACTION)) {
            $actionIndex = '1' . array_search($action, self::SIDEBAR_WIDGET_ACTION);
        } elseif (in_array($action, self::CONTEXTUAL_WIDGET_ACTION)) {
            $actionIndex = '2' . array_search($action, self::CONTEXTUAL_WIDGET_ACTION);
        } else {
            $actionIndex = '00';
        }
        return 'SB_' . $message['timestamp'] . '_' . $message['fingerprint'] . '_' . $actionIndex;
    }

    /**
     * if it is the time to: persist events, flush events in memory then delete message in queue
     * check the condition when the bulk size is reached or the pushing interval time is reached
     * @param $currentTimestamp
     * @return bool
     */
    private function couldPersistEventsAndFlush($currentTimestamp): bool
    {
        return $this->reachedPushingInterval($currentTimestamp) || $this->reachedBulkSize();
    }
}
