<?php

namespace Salesfloor\Services\AI\Agent;

use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\Util\Json;

/**
 * LanguageDetectionAgent
 *
 * Specialized agent for detecting the language/locale of user input
 * and resolving user-specified language preferences/overrides.
 */
class LanguageDetectionAgent
{
    private const CONFIDENCE_TARGET = 0.8;
    private const CONFIDENCE_HIGH = 0.9;
    private const CONFIDENCE_MODERATE = 0.5;
    private const CONFIDENCE_LOW = 0.3;
    private const CONFIDENCE_NO = 0.1;

    /**
     * System prompt for language detection
     */
    private const AGENT_PROMPT = <<<EOT
You are an expert language detection specialist. Your task is to:

1. Analyze the provided text to determine its primary language
2. Identify any explicit user language preferences or overrides
3. Resolve conflicts between detected language and user preferences
4. Return the final language that should be used for responses

You must be able to detect these languages and their common variations:
- English (en, en-US, en-GB, en-CA, en-AU)
- French (fr, fr-FR, fr-CA)
- Spanish (es, es-ES, es-MX, es-AR)
- German (de, de-DE, de-AT, de-CH)
- Italian (it, it-IT)
- Portuguese (pt, pt-BR, pt-PT)
- Dutch (nl, nl-NL, nl-BE)
- Chinese (zh, zh-CN, zh-TW)
- Japanese (ja, ja-JP)
- Korean (ko, ko-KR)
- Russian (ru, ru-RU)
- Arabic (ar, ar-SA, ar-EG)
- Hindi (hi, hi-IN)
- Polish (pl, pl-PL)
- Swedish (sv, sv-SE)
- Norwegian (no, nb-NO, nn-NO)
- Danish (da, da-DK)
- Finnish (fi, fi-FI)

Priority Rules:
1. If user explicitly requests a specific language (e.g., "respond in English", "reply in French"), use that language
2. If user specifies a locale preference (e.g., "use Canadian French"), use that specific locale
3. If no override is specified, use the detected language of the input text
4. If language cannot be detected reliably, default to English (en)

Language Override Patterns to Look For:
- "respond in [language]"
- "reply in [language]"
- "answer in [language]"
- "use [language]"
- "write in [language]"
- "generate in [language]"
- "create in [language]"
- "[language] response"
- "[language] version"
- "translate to [language]"
- "switch to [language]"
EOT;

    /**
     * Language code mappings
     */
    private const LANGUAGE_MAPPINGS = [
        // English variants
        'english' => 'en',
        'anglais' => 'en',
        'inglés' => 'en',
        'inglese' => 'en',
        'inglês' => 'en',
        'engels' => 'en',
        'englisch' => 'en',

        // French variants
        'french' => 'fr',
        'français' => 'fr',
        'francés' => 'fr',
        'francese' => 'fr',
        'francês' => 'fr',
        'frans' => 'fr',
        'französisch' => 'fr',

        // Spanish variants
        'spanish' => 'es',
        'espagnol' => 'es',
        'español' => 'es',
        'spagnolo' => 'es',
        'espanhol' => 'es',
        'spaans' => 'es',
        'spanisch' => 'es',

        // German variants
        'german' => 'de',
        'allemand' => 'de',
        'alemán' => 'de',
        'tedesco' => 'de',
        'alemão' => 'de',
        'duits' => 'de',
        'deutsch' => 'de',

        // Italian variants
        'italian' => 'it',
        'italien' => 'it',
        'italiano' => 'it',
        'italaans' => 'it',
        'italienisch' => 'it',

        // Portuguese variants
        'portuguese' => 'pt',
        'portugais' => 'pt',
        'portugués' => 'pt',
        'português' => 'pt',
        'portugees' => 'pt',
        'portugiesisch' => 'pt',

        // Other major languages
        'chinese' => 'zh',
        'japanese' => 'ja',
        'korean' => 'ko',
        'russian' => 'ru',
        'arabic' => 'ar',
        'hindi' => 'hi',
        'dutch' => 'nl',
        'polish' => 'pl',
        'swedish' => 'sv',
        'norwegian' => 'no',
        'danish' => 'da',
        'finnish' => 'fi',
    ];

    /**
     * Locale patterns for common regions
     */
    private const LOCALE_PATTERNS = [
        // English locales
        'american' => 'en-US',
        'british' => 'en-GB',
        'canadian' => 'en-CA',
        'australian' => 'en-AU',

        // French locales
        'canadian french' => 'fr-CA',
        'québécois' => 'fr-CA',
        'france french' => 'fr-FR',

        // Spanish locales
        'mexican' => 'es-MX',
        'argentinian' => 'es-AR',
        'spain spanish' => 'es-ES',

        // German locales
        'austrian' => 'de-AT',
        'swiss german' => 'de-CH',

        // Portuguese locales
        'brazilian' => 'pt-BR',
        'portugal' => 'pt-PT',

        // Chinese locales
        'simplified' => 'zh-CN',
        'traditional' => 'zh-TW',
        'mainland' => 'zh-CN',
        'taiwan' => 'zh-TW',
    ];

    /**
     * Constructor
     */
    public function __construct(
        private AIService $aiService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Detect language and resolve user preferences
     *
     * @param string $content The input content to analyze
     * @param array $context Additional context that might contain language hints
     * @param array $options AI Service options
     * @return array Language detection results
     * @throws AIAdapterException
     */
    public function detectLanguage(
        string $content,
        array $context = [],
        array $options = []
    ): array {
        // First, try rule-based detection for common patterns
        $ruleBasedResult = $this->detectLanguageByRules($content, $context);

        // If rule-based detection is confident, use it
        if ($ruleBasedResult['confidence'] >= self::CONFIDENCE_TARGET) {
            return $ruleBasedResult;
        }

        // Otherwise, use AI-based detection for more complex cases
        try {
            $aiResult = $this->detectLanguageByAI($content, $context, $options);

            // priority goes to AI for complex cases
            return $this->mergeDetectionResults($ruleBasedResult, $aiResult);
        } catch (AIAdapterException $e) {
            $this->logger->warning(
                'AI language detection failed, falling back to rule-based detection: ' . $e->getMessage(),
                [
                    'content_preview' => substr($content, 0, 100),
                    'error' => $e->getMessage(),
                ]
            );

            // Fall back to rule-based detection
            return $ruleBasedResult;
        }
    }

    /**
     * Detect language using rule-based patterns
     *
     * @param string $content The input content
     * @param array $context Additional context
     * @return array Detection results
     */
    protected function detectLanguageByRules(string $content, array $context = []): array
    {
        $fullText = $content . ' ' . implode(' ', array_values($context));
        $normalizedText = strtolower($fullText);

        $override = $this->detectLanguageOverride($normalizedText);
        if ($override) {
            return [
                'detected_language' => $override['detected'] ?? 'unknown',
                'final_language' => $override['final'],
                'confidence' => $override['detected'] ? self::CONFIDENCE_HIGH + 0.05 : self::CONFIDENCE_NO,
                'has_override' => true,
                'override_reason' => $override['reason'] ?? 'User explicitly requested specific language',
                'detection_method' => 'rule-based'
            ];
        }

        $detectedLang = $this->detectContentLanguage($content);

        return [
            'detected_language' => $detectedLang['language'],
            'final_language' => $detectedLang['language'],
            'confidence' => $detectedLang['confidence'],
            'has_override' => false,
            'override_reason' => null,
            'detection_method' => 'rule-based'
        ];
    }

    /**
     * Detect language using AI analysis
     *
     * @param string $content The input content
     * @param array $context Additional context
     * @param array $options AI options
     * @return array Detection results
     * @throws AIAdapterException
     */
    private function detectLanguageByAI(string $content, array $context, array $options): array
    {
        $messages = $this->buildDetectionMessages($content, $context);

        $response = $this->aiService->generateChatCompletion($messages, $options);
        return $this->parseLanguageDetectionResponse($response->getContent());
    }

    /**
     * Build messages for AI language detection
     *
     * @param string $content The content to analyze
     * @param array $context Additional context
     * @return array Messages for AI
     */
    private function buildDetectionMessages(string $content, array $context): array
    {
        $contextString = !empty($context) ? "\n\nAdditional context: " . json_encode($context, JSON_PRETTY_PRINT) : '';
        $responseFormat = $this->getDetectionResponseFormat();

        $userPrompt = <<<EOT
Analyze the following content and determine:
1. The primary language of the content
2. Any explicit language preferences or overrides mentioned by the user
3. The final language that should be used for responses

Content to analyze: "{$content}"{$contextString}

{$responseFormat}
EOT;

        return [
            [
                'role' => 'system',
                'content' => self::AGENT_PROMPT,
            ],
            [
                'role' => 'user',
                'content' => $userPrompt,
            ],
        ];
    }

    /**
     * Get response format for language detection
     */
    private function getDetectionResponseFormat(): string
    {
        return <<<EOT
Format your response as valid JSON with the following structure:
{
  "detected_language": "language code of the input content (e.g., 'fr', 'es', 'zh')",
  "final_language": "language code to use for responses (e.g., 'en', 'fr')",
  "confidence": 0.95,
  "has_override": true,
  "override_reason": "explanation of why the final language differs from detected",
  "locale_hint": "specific locale if detected (e.g., 'en-US', 'fr-CA')",
  "detection_method": "ai-based"
}
EOT;
    }

    /**
     * Parse AI response for language detection
     *
     * @param string $responseContent Raw AI response
     * @return array Parsed detection results
     */
    private function parseLanguageDetectionResponse(string $responseContent): array
    {
        try {
            $decoded = Json::decode($responseContent);

            return [
                'detected_language' => $decoded['detected_language'] ?? 'unknown',
                'final_language' => $decoded['final_language'] ?? 'en',
                'confidence' => (float) ($decoded['confidence'] ?? self::CONFIDENCE_MODERATE + 0.2),
                'has_override' => (bool) ($decoded['has_override'] ?? false),
                'override_reason' => $decoded['override_reason'] ?? null,
                'locale_hint' => $decoded['locale_hint'] ?? null,
                'detection_method' => $decoded['detection_method'] ?? 'ai-based'
            ];
        } catch (\Throwable $e) {
            $this->logger->error(
                'Error parsing language detection response: ' . $e->getMessage(),
                [
                    'response_content' => $responseContent,
                    'error' => $e->getMessage(),
                ]
            );

            return [
                'detected_language' => 'unknown',
                'final_language' => 'en',
                'confidence' => self::CONFIDENCE_NO,
                'has_override' => false,
                'override_reason' => 'Parse error occurred',
                'locale_hint' => null,
                'detection_method' => 'fallback'
            ];
        }
    }

    /**
     * Detect explicit language override requests
     *
     * @param string $normalizedText Lowercased text to analyze
     * @return array|null Override information or null if none found
     */
    protected function detectLanguageOverride(string $normalizedText): ?array
    {
        // Patterns for language override requests
        $overridePatterns = [
            '/(?:respond|reply|answer|write|generate|create|use|switch to|translate to)\s+(?:in\s+)?([a-z\s]+)(?:\s+(?:language|version|response))?/i',
            '/([a-z\s]+)\s+(?:response|version|language|reply|answer)/i',
            '/(?:please\s+)?(?:write|respond|reply|answer|generate|create)\s+(?:this\s+)?(?:in\s+)?([a-z\s]+)/i',
        ];

        foreach ($overridePatterns as $pattern) {
            if (preg_match_all($pattern, $normalizedText, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $languageName = trim($match[1]);

                    // Check if it's a valid language name
                    $languageCode = $this->resolveLanguageCode($languageName);
                    if ($languageCode !== null) {
                        // Try to detect the original content language
                        $detectedContent = $this->detectContentLanguage($normalizedText);

                        return [
                            'detected' => $detectedContent['language'],
                            'final' => $languageCode,
                            'reason' => "User explicitly requested {$languageName} language",
                            'override_text' => $match[0]
                        ];
                    }
                }
            }
        }

        return null;
    }

    /**
     * Detect content language using character patterns and common words
     *
     * @param string $content Content to analyze
     * @return array Language detection result
     */
    protected function detectContentLanguage(string $content): array
    {
        // Check for non-ASCII characters (strong indicator of non-English)
        if (preg_match('/[^\x00-\x7F]/', $content)) {
            // Unicode character patterns
            if (preg_match('/[\x{4E00}-\x{9FFF}]/u', $content)) {
                return ['language' => 'zh', 'confidence' => self::CONFIDENCE_HIGH];
            }
            if (preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}]/u', $content)) {
                return ['language' => 'ja', 'confidence' => self::CONFIDENCE_HIGH];
            }
            if (preg_match('/[\x{AC00}-\x{D7AF}]/u', $content)) {
                return ['language' => 'ko', 'confidence' => self::CONFIDENCE_HIGH];
            }
            if (preg_match('/[\x{0400}-\x{04FF}]/u', $content)) {
                return ['language' => 'ru', 'confidence' => self::CONFIDENCE_HIGH];
            }
            if (preg_match('/[\x{0600}-\x{06FF}]/u', $content)) {
                return ['language' => 'ar', 'confidence' => self::CONFIDENCE_HIGH];
            }
            if (preg_match('/[\x{0900}-\x{097F}]/u', $content)) {
                return ['language' => 'hi', 'confidence' => self::CONFIDENCE_HIGH];
            }
        }

        // Common word patterns for European languages
        $wordPatterns = [
            'fr' => '/\b(?:le|la|les|de|du|des|pour|avec|dans|sur|est|sont|cette|ce|un|une|qui|que|mais|ou|donc|alors|très|bien|tout|tous|avoir|être|faire|aller|venir|voir|savoir|pouvoir|vouloir|devoir)\b/i',
            'es' => '/\b(?:el|la|los|las|de|del|para|con|en|por|un|una|que|y|o|pero|si|no|es|son|está|están|tiene|tienen|hacer|ser|estar|tener|ir|venir|ver|saber|poder|querer|deber)\b/i',
            'de' => '/\b(?:der|die|das|den|dem|des|ein|eine|einen|einem|einer|eines|und|oder|aber|mit|von|zu|für|auf|in|an|bei|nach|vor|über|unter|durch|gegen|ohne|um|ist|sind|hat|haben|wird|werden|sein|haben|werden|können|müssen|sollen|wollen|dürfen|mögen)\b/i',
            'it' => '/\b(?:il|la|lo|gli|le|di|del|della|dei|delle|per|con|in|su|da|a|un|una|che|e|o|ma|se|non|è|sono|ha|hanno|fare|essere|avere|andare|venire|vedere|sapere|potere|volere|dovere)\b/i',
            'pt' => '/\b(?:o|a|os|as|de|do|da|dos|das|para|com|em|por|um|uma|que|e|ou|mas|se|não|é|são|está|estão|tem|têm|fazer|ser|estar|ter|ir|vir|ver|saber|poder|querer|dever)\b/i',
            'nl' => '/\b(?:de|het|een|van|in|op|voor|met|aan|bij|naar|over|onder|door|tegen|zonder|om|is|zijn|heeft|hebben|wordt|worden|zijn|hebben|worden|kunnen|moeten|zullen|willen|mogen)\b/i',
        ];

        $scores = [];
        foreach ($wordPatterns as $lang => $pattern) {
            preg_match_all($pattern, $content, $matches);
            $scores[$lang] = count($matches[0]);
        }

        if (!empty($scores)) {
            arsort($scores);
            $topLang = key($scores);
            $topScore = $scores[$topLang];

            // Calculate confidence based on word count and content length
            $wordCount = str_word_count($content);
            $confidence = $wordCount > 0 ? min(self::CONFIDENCE_HIGH, $topScore / $wordCount * 3) : self::CONFIDENCE_NO;

            if ($confidence >= self::CONFIDENCE_LOW) {
                return ['language' => $topLang, 'confidence' => $confidence];
            }
        }

        // Default to English if no clear pattern found. perhaps should it just fail here? TBD
        return ['language' => 'en', 'confidence' => self::CONFIDENCE_MODERATE];
    }

    /**
     * Resolve language name to language code
     *
     * @param string $languageName Language name to resolve
     * @return string|null Language code or null if not found
     */
    protected function resolveLanguageCode(string $languageName): ?string
    {
        $normalized = strtolower(trim($languageName));

        // Direct mapping
        if (isset(self::LANGUAGE_MAPPINGS[$normalized])) {
            return self::LANGUAGE_MAPPINGS[$normalized];
        }

        // Check for locale patterns
        foreach (self::LOCALE_PATTERNS as $pattern => $locale) {
            if (strpos($normalized, $pattern) !== false) {
                return $locale;
            }
        }

        // Check if it's already a valid language code
        if (preg_match('/^[a-z]{2}(?:-[A-Z]{2})?$/', $normalized)) {
            return $normalized;
        }

        return null;
    }

    /**
     * Merge rule-based and AI detection results
     *
     * @param array $ruleResult Rule-based detection result
     * @param array $aiResult AI-based detection result
     * @return array Merged result
     */
    private function mergeDetectionResults(array $ruleResult, array $aiResult): array
    {
        // If rule-based found an override, prioritize it
        if ($ruleResult['has_override']) {
            return $ruleResult;
        }

        // If AI has higher confidence, use AI result
        if ($aiResult['confidence'] > $ruleResult['confidence']) {
            return $aiResult;
        }

        // Otherwise, use rule-based result but with AI's locale hint if available
        $merged = $ruleResult;
        if (!empty($aiResult['locale_hint'])) {
            $merged['locale_hint'] = $aiResult['locale_hint'];
        }

        return $merged;
    }

    /**
     * Get a simple language code for the given content
     *
     * @param string $content Content to analyze
     * @param array $context Additional context
     * @return string Language code (e.g., 'en', 'fr', 'es')
     */
    public function getLanguageCode(string $content, array $context = []): string
    {
        try {
            $result = $this->detectLanguage($content, $context);
            return $result['final_language'] ?? 'en';
        } catch (\Exception $e) {
            $this->logger->error(
                'Error getting language code: ' . $e->getMessage(),
                [
                    'content_preview' => substr($content, 0, 100),
                    'error' => $e->getMessage(),
                ]
            );
            return 'en';
        }
    }

    /**
     * Check if the content contains a language override request
     *
     * @param string $content Content to check
     * @return bool True if override detected
     */
    public function hasLanguageOverride(string $content): bool
    {
        $normalized = strtolower($content);
        return $this->detectLanguageOverride($normalized) !== null;
    }
}
