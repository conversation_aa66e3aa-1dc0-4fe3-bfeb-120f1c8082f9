<?php

namespace Salesfloor\Services\AI\Agent;

use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\Util\Json;
use Salesfloor\Services\AI\Util\Prompt;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;

/**
 * RequestAnalyzerAgent
 *
 * Focused agent responsible for analyzing user requests to determine
 * intent and extract parameters for content generation.
 */
class RequestAnalyzerAgent
{
    /**
     * System prompt for request analysis
     */
    private const AGENT_PROMPT = <<<EOT
You are an expert at analyzing marketing communication requests and determining their intent.
Your task is to:

1. Analyze the user's request for marketing content
2. Determine the most appropriate campaign type
3. Extract relevant details from the request
4. Structure this information for processing

When analyzing requests, focus on:
- The primary goal/intent of the communication
- The marketing channel (email, SMS, etc.)
- The specific campaign category
- Key details that should be included in the content

IMPORTANT: If the user explicitly states what type of content they want (email, SMS, subject lines, etc.),
you MUST respect their choice. Never override a clearly stated user preference for content type.

You must classify requests into one of these campaign types:
- product_launch: New product announcements or introductions
- promotion: Sales, discounts, limited-time offers
- event: Invitations to events, webinars, or in-store activities
- newsletter: Regular updates or multi-topic communications
- customer_update: Important news, policy changes, or service updates
- reengagement: Attempts to reconnect with inactive customers
- welcome: Onboarding communications for new customers/subscribers
EOT;

    /**
     * Constructor
     */
    public function __construct(
        private AIService $aiService,
        private LoggerInterface $logger,
        private CampaignAgent $campaignAgent
    ) {
    }

    /**
     * Analyze a user request to determine campaign type and extract details
     *
     * @param string $userRequest The raw user request
     * @param array $context Additional context or preferences
     * @return array Analysis results including campaign type and details
     * @throws AIAdapterException
     */
    public function analyze(string $userRequest, array $context = []): array
    {
        $messages = $this->buildAnalysisMessages($userRequest, $context);

        try {
            $response = $this->aiService->generateChatCompletion($messages, []);
            return $this->parseAnalysisResponse($response->getContent());
        } catch (AIAdapterException $e) {
            $this->logger->error(
                'Failed to analyze marketing request: ' . $e->getMessage(),
                [
                    'user_request' => $userRequest,
                    'error' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    /**
     * Build messages for content analysis
     *
     * @param string $userRequest The raw user request
     * @param array $context Additional context or preferences
     * @return array instructions
     */
    private function buildAnalysisMessages(
        string $content,
        array $context = []
    ): array {
        $userPrompt = $this->buildUserPrompt($content, $context);

        return [
            [
                'role' => 'system',
                'content' => self::AGENT_PROMPT,
            ],
            [
                'role' => 'user',
                'content' => $userPrompt,
            ],
        ];
    }

    /**
     * Build user prompt for analysis requests
     * @param string $userRequest The raw user request
     * @param array $context Additional context or preferences
     * @return string instruction
     */
    private function buildUserPrompt(string $content, array $context): string
    {
        $contextString = Prompt::formatContextString($context);
        $contentTypeInfo = Prompt::extractContentTypeInfo($context);
        $campaignTypes = Prompt::getCampaignTypeDefinitions();
        $responseFormat = $this->getGenerationResponseFormat();

        return <<<EOT

    Analyze the following request for marketing content and determine:
    1. The most appropriate campaign type
    2. The content type (email, sms, subject_lines)
    3. All relevant details to extract for content generation

    {$campaignTypes}

    {$responseFormat}

    User request: {$content}{$contextString}{$contentTypeInfo}
EOT;
    }

    /**
     * Build a standard JSON response format instruction
     */
    public function getGenerationResponseFormat(): string
    {
        return <<<EOT
Format your response as valid JSON with the following structure:
{
  "campaign_type": "one of the predefined campaign types",
  "content_type": "email or other content type",
  "context": {
    "key1": "value1",
    "key2": "value2"
  },
  "reasoning": "brief explanation of your analysis"
}
EOT;
    }

    /**
     * Generate content based on analysis results using the CampaignAgent
     *
     * @param array $analysis Analysis results from analyzeRequest
     * @param array $options Additional options for the AI service
     * @return AIResponse Generated content
     * @throws AIAdapterException
     */
    public function generateContentFromAnalysis(
        array $analysis,
        array $options = []
    ): AIResponse {
        $campaignType = new CampaignType($analysis['campaign_type']);
        $context = $analysis['context'];
        $contentType = new ContentType($analysis['content_type'] ?? 'email');

        return match ($contentType->getValue()) {
            ContentType::SMS,
            ContentType::TEXT
                => $this->campaignAgent->generateMarketingSMS(
                    $campaignType,
                    $context,
                    $options
                ),
            ContentType::SUBJECT_LINES
                => $this->campaignAgent->generateSubjectLines(
                    $context['email_content'] ?? '',
                    $options
                ),
            default => $this->campaignAgent->generateMarketingEmail(
                $campaignType,
                $context,
                $options
            ),
        };
    }

    /**
     * Parse the AI response into a structured analysis result
     *
     * @param string $responseContent The raw response content
     * @return array Structured analysis results
     */
    private function parseAnalysisResponse(string $responseContent): array
    {
        try {
            $decoded = Json::decode($responseContent);

            // Normalize using value objects
            $campaignType = new CampaignType($decoded['campaign_type']);
            $contentType = new ContentType($decoded['content_type']);

            return [
                'campaign_type' => $campaignType->getValue(),
                'content_type' => $contentType->getValue(),
                'context' => $decoded['context'] ?? [],
                'reasoning' => $decoded['reasoning'] ?? '',
            ];
        } catch (\Throwable $e) {
            $this->logger->error(
                'Error parsing analysis response: ' . $e->getMessage(),
                [
                    'response_content' => $responseContent,
                    'error' => $e->getMessage(),
                ]
            );

            return [
                'campaign_type' => CampaignType::UNKNOWN,
                'content_type' => ContentType::EMAIL,
                'context' => [
                    'parsed_from_request' => false,
                    'error' => $e->getMessage(),
                ],
                'reasoning' => 'Error occurred during response parsing',
            ];
        }
    }

    /**
     * Get the Campaign Agent
     *
     * @return CampaignAgent
     */
    public function getCampaignAgent(): CampaignAgent
    {
        return $this->campaignAgent;
    }
}
