<?php

namespace Salesfloor\Services\AI\Agent;

use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;
use Salesfloor\Services\AI\Util\Prompt;

/**
 * CampaignAgent
 *
 * Focused agent responsible for generating marketing communications content
 * including email campaigns, SMS messages, and subject lines.
 */
class CampaignAgent
{
    /**
     * System prompt for marketing campaigns
     */
    private const AGENT_PROMPT = <<<EOT
You are an expert marketing communications specialist. Your task is to generate compelling,
persuasive, and professional content for marketing communications including:
- Email campaigns
- Product announcements
- Event invitations
- Sales promotions
- Customer updates

EOT;

    /**
     * System prompt for SMS generation
     */
    private const SMS_PROMPT_TEMPLATE = <<<EOT
You are an expert at crafting marketing SMS messages that drive engagement and conversions.
Your task is to create concise, compelling SMS marketing content that is under %d characters
including spaces. Never suggest anything or create content outside of the boundaries of what the user is attempting to communicate or express.
Include a clear call-to-action and, if provided, a tracking link.
EOT;

    /**
     * System prompt for subject line generation
     */
    private const SUBJECT_LINE_PROMPT = <<<EOT
You are an expert at crafting compelling email subject lines that increase open rates.
Generate a subject line option that is attention-grabbing,
concise (under 50 characters), and accurately reflect the email content.
Avoid clickbait tactics or misleading statements. do not explain the subject attributes
EOT;

    /**
     * Constructor
     */
    public function __construct(
        private AIService $aiService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Generate marketing email content
     *
     * @param CampaignType $campaignType Type of campaign
     * @param array $context Details about the campaign
     * @param array $options AI Service options
     * @return AIResponse Response containing the generated content
     * @throws AIAdapterException
     */
    public function generateMarketingEmail(
        CampaignType $campaignType,
        array $context,
        array $options = []
    ): AIResponse {
        $contentType = ContentType::email();

        $messages = $this->buildPrompt($campaignType, $contentType, $context);

        try {
            return $this->aiService->generateChatCompletion(
                $messages,
                $options
            );
        } catch (AIAdapterException $e) {
            $this->logger->error(
                'Failed to generate marketing email: ' . $e->getMessage(),
                [
                    'campaign_type' => $campaignType->getValue(),
                    'error' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    /**
     * Generate text message content for marketing
     *
     * @param CampaignType $campaignType Type of campaign
     * @param array $context Details about the campaign
     * @param array $options AI Service options
     * @return AIResponse Response containing the generated content
     * @throws AIAdapterException
     */
    public function generateMarketingSMS(
        CampaignType $campaignType,
        array $context,
        array $options = []
    ): AIResponse {
        $contentType = ContentType::sms();
        $messages = $this->buildPrompt($campaignType, $contentType, $context);

        try {
            return $this->aiService->generateChatCompletion(
                $messages,
                $options
            );
        } catch (AIAdapterException $e) {
            $this->logger->error(
                'Failed to generate marketing SMS: ' . $e->getMessage(),
                [
                    'campaign_type' => $campaignType->getValue(),
                    'error' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    /**
     * Build messages for content generation
     */
    public function buildPrompt(
        CampaignType $campaignType,
        ContentType $contentType,
        array $details
    ): array {
        $systemPrompt = $this->buildSystemPrompt($campaignType, $contentType);

        $userPrompt = $this->buildUserPrompt(
            $campaignType,
            $contentType,
            $details
        );

        $languageDetection = $details['language_detection'] ?? [];
        $marketingGuidelines = Prompt::getMarketingGuidelinesWithLanguage($languageDetection);

        return [
            [
                'role' => 'system',
                'content' => $systemPrompt,
            ],
            [
                'role' => 'system',
                'content' => $marketingGuidelines,
            ],
            [
                'role' => 'user',
                'content' => $userPrompt,
            ],
        ];
    }

    /**
     * Build user prompt for generation requests
     */
    private function buildUserPrompt(
        CampaignType $campaignType,
        ContentType $contentType,
        array $context
    ): string {
        $detailsString = Prompt::formatContextString($context);

        if ($contentType->isSms()) {
            return "Generate a marketing SMS for a {$campaignType} campaign with the following details:\n\n{$detailsString}";
        }

        return "Generate a marketing {$contentType} for a {$campaignType} campaign with the following details:\n\n{$detailsString}";
    }

    /**
     * Generate subject lines for marketing emails
     *
     * @param string $emailContent The email content to generate subject lines for
     * @param array $options Additional options
     * @return AIResponse Response containing the generated subject lines
     * @throws AIAdapterException
     */
    public function generateSubjectLines(
        string $emailContent,
        array $options = []
    ): AIResponse {
        $messages = [
            [
                'role' => 'system',
                'content' => self::SUBJECT_LINE_PROMPT,
            ],
            [
                'role' => 'user',
                'content' =>
                    "Create a compelling subject lines for the following email content: \n\n" .
                    $emailContent,
            ],
        ];

        try {
            return $this->aiService->generateChatCompletion(
                $messages,
                $options
            );
        } catch (AIAdapterException $e) {
            $this->logger->error(
                'Failed to generate subject lines: ' . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    /**
     * Build system prompt for content generation
     *
     * @param CampaignType $campaignType Type of campaign
     * @param ContentType $contentType Type of content
     * @return string Complete system prompt
     */
    private function buildSystemPrompt(
        CampaignType $campaignType,
        ContentType $contentType
    ): string {
        if ($contentType->isSms()) {
            $basePrompt = sprintf(
                self::SMS_PROMPT_TEMPLATE,
                Prompt::SMS_CHARACTER_LIMIT
            );
        } else {
            $basePrompt = self::AGENT_PROMPT;
        }

        $campaignInstructions = $this->getCampaignInstructions($campaignType);

        return implode(
            "\n\n",
            array_filter([$basePrompt, $campaignInstructions])
        );
    }

    /**
     * Get campaign-specific instructions
     */
    private function getCampaignInstructions(CampaignType $campaignType): string
    {
        return match ($campaignType->getValue()) {
            CampaignType::PRODUCT_LAUNCH
                => $this->getProductLaunchInstructions(),
            CampaignType::PROMOTION,
            CampaignType::SALE
                => $this->getPromotionInstructions(),
            CampaignType::EVENT => $this->getEventInstructions(),
            CampaignType::NEWSLETTER => $this->getNewsletterInstructions(),
            CampaignType::CUSTOMER_UPDATE
                => $this->getCustomerUpdateInstructions(),
            CampaignType::REENGAGEMENT => $this->getReengagementInstructions(),
            CampaignType::WELCOME => $this->getWelcomeInstructions(),
            default => $this->getDefaultInstructions(),
        };
    }

    private function getProductLaunchInstructions(): string
    {
        return <<<EOT
For product launch campaigns:
- Highlight what's new and innovative
- Focus on key benefits and features
- Create excitement and urgency
- Include product availability information
EOT;
    }

    private function getPromotionInstructions(): string
    {
        return <<<EOT
For promotional campaigns:
- Clearly state the offer and any conditions
- Create a sense of urgency with deadlines
- Highlight the value/savings
- Make the redemption process clear
EOT;
    }

    private function getEventInstructions(): string
    {
        return <<<EOT
For event campaigns:
- Clearly state the event details (what, when, where)
- Highlight the value of attending
- Include registration/RSVP information
- Create excitement about featured content/speakers
EOT;
    }

    private function getNewsletterInstructions(): string
    {
        return <<<EOT
For newsletter campaigns:
- Include a compelling table of contents
- Highlight the most valuable content first
- Use scannable headers and short paragraphs
- End with a clear next step for the reader
EOT;
    }

    private function getCustomerUpdateInstructions(): string
    {
        return <<<EOT
For customer update campaigns:
- Be clear and direct about the update
- Explain how it benefits the customer
- Include any action items required
- Provide support contact information if needed
EOT;
    }

    private function getReengagementInstructions(): string
    {
        return <<<EOT
For reengagement campaigns:
- Acknowledge the customer's absence
- Highlight what they've been missing
- Offer a compelling reason to return
- Make the reengagement process simple
EOT;
    }

    private function getWelcomeInstructions(): string
    {
        return <<<EOT
For welcome campaigns:
- Express appreciation for joining
- Set expectations for future communications
- Provide a clear first step or action
- Highlight key benefits of the relationship
EOT;
    }

    private function getDefaultInstructions(): string
    {
        return <<<EOT
For this campaign type:
- Focus on the customer benefits
- Be clear and concise
- Include a compelling call to action
- Maintain brand voice and tone
EOT;
    }
}
