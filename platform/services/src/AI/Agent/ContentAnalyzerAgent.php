<?php

namespace Salesfloor\Services\AI\Agent;

use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\Util\Prompt;
use Salesfloor\Services\AI\Util\Json;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;

/**
 * ContentAnalyzerAgent
 *
 * Focused agent responsible solely for analyzing existing marketing content
 * to determine type, campaign category, and quality metrics.
 */
class ContentAnalyzerAgent
{
    /**
     * System prompt for content analysis
     */
    private const AGENT_PROMPT = <<<EOT
You are an expert marketing content analyst. Your task is to analyze provided marketing content and determine:

1. Content type (email, sms, subject_lines)
2. Campaign type (promotion, product_launch, event, newsletter, welcome, reengagement and customer_update)
3. Current tone and style
4. Target audience indicators
5. Key messaging elements

You must classify requests into one of these campaign types:
- product_launch: New product announcements or introductions
- promotion: Sales, discounts, limited-time offers
- event: Invitations to events, webinars, or in-store activities
- newsletter: Regular updates or multi-topic communications
- customer_update: Important news, policy changes, or service updates
- reengagement: Attempts to reconnect with inactive customers
- welcome: Onboarding communications for new customers/subscribers

IMPORTANT: If the content is tagged with a content type,
you MUST honor it in your analysis. This is a critical requirement.

Base your analysis solely on the content provided. Be accurate and specific.
EOT;

    /**
     * Constructor
     */
    public function __construct(
        private AIService $aiService,
        private LoggerInterface $logger,
        private ContentRefinerAgent $contentRefinerAgent
    ) {
    }

    /**
     * Analyze existing content to determine type, campaign, and quality
     *
     * @param string $content The content to analyze
     * @param array $context Additional context for analysis
     * @return array Analysis results
     * @throws AIAdapterException
     */
    public function analyze(string $content, array $context = []): array
    {
        $messages = $this->buildAnalysisMessages($content, $context);

        try {
            $response = $this->aiService->generateChatCompletion($messages, []);
            return $this->parseAnalysisResponse($response->getContent());
        } catch (AIAdapterException $e) {
            $this->logger->error(
                'Failed to analyze content: ' . $e->getMessage(),
                [
                    'content_length' => strlen($content),
                    'error' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    /**
     * Build messages for content analysis
     */
    private function buildAnalysisMessages(
        string $content,
        array $context = []
    ): array {
        $userPrompt = $this->buildUserPrompt($content, $context);

        return [
            [
                'role' => 'system',
                'content' => self::AGENT_PROMPT,
            ],
            [
                'role' => 'user',
                'content' => $userPrompt,
            ],
        ];
    }

    /**
     * Build user prompt for analysis requests
     */
    private function buildUserPrompt(string $content, array $context): string
    {
        $contextString = Prompt::formatContextString($context);
        $contentTypeInfo = Prompt::extractContentTypeInfo($context);
        $campaignTypes = Prompt::getCampaignTypeDefinitions();
        $responseFormat = $this->getAnalysisResponseFormat();

        return <<<EOT
    Analyze the provided marketing content carefully and determine:
    1. The most appropriate campaign type
    2. The content type (email, sms)
    3. All relevant details to extract for content refinement

    {$campaignTypes}

    {$responseFormat}
    User Request: {$content}{$contextString}{$contentTypeInfo}
EOT;
    }

    /**
     * Refine content based on analysis results using the ContentRefinerAgent
     *
     * @param string $content to improve
     * @param array $analysis Analysis results from analyzeRequest
     * @param array $options Additional options for the AI service
     * @return AIResponse Generated content
     * @throws AIAdapterException
     */
    public function refineContentFromAnalysis(
        string $content,
        array $analysis,
        array $options = []
    ): AIResponse {
        $campaignType = new CampaignType($analysis['campaign_type']);
        $contentType = new ContentType($analysis['content_type'] ?? 'email');
        unset($analysis['campaign_type'], $analysis['content_type']);
        $context = $analysis;

        return match ($contentType->getValue()) {
            ContentType::SMS,
            ContentType::TEXT
                => $this->contentRefinerAgent->refineSMSContent(
                    $content,
                    $campaignType,
                    $context,
                    $options
                ),
            default => $this->contentRefinerAgent->refineEmailContent(
                $content,
                $campaignType,
                $context,
                $options
            ),
        };
    }

    /**
     * Get analysis response format for content analysis
     */
    private function getAnalysisResponseFormat(): string
    {
        return <<<EOT
Format your response as valid JSON with the following structure:
{
  "campaign_type": "one of the predefined campaign types",
  "content_type": "email or other content type",
  "tone": "professional|casual|urgent|friendly|formal|promotional",
  "target_audience": "general|vip|new_customers|existing_customers|specific_segment",
  "context": {
    "key1": "value1",
    "key2": "value2"
  },
  "key_elements": {
    "personalization": true|false,
    "call_to_action": true|false,
    "offer_details": "true|false",
    "urgency_indicators": true|false,
    "brand_elements": true|false
  },
  "content_quality": {
    "grammar": true|false,
    "clarity": true|false,
    "tone_consistency": true|false,
    "length_appropriate": true|false
  },
  "reasoning": "Provide a highly valuable improvement recommendation"

}
EOT;
    }

    /**
     * Parse the AI analysis response
     *
     * @param string $response The raw AI response
     * @return array Parsed analysis data
     */
    private function parseAnalysisResponse(string $response): array
    {
        try {
            $decoded = Json::decode($response);

            $contentType = new ContentType($decoded['content_type'] ?? 'email');
            $campaignType = new CampaignType(
                $decoded['campaign_type'] ?? 'newsletter'
            );

            return [
                'content_type' => $contentType->getValue(),
                'campaign_type' => $campaignType->getValue(),
                'context' => $decoded['context'] ?? [],
                'tone' => $decoded['tone'] ?? 'professional',
                'target_audience' => $decoded['target_audience'] ?? 'general',
                'key_elements' => $decoded['key_elements'] ?? [],
                'content_quality' => $decoded['content_quality'] ?? [],
                'reasoning' => $decoded['reasoning'] ?? '',
            ];
        } catch (\Exception $e) {
            $this->logger->warning(
                'Exception while parsing analysis response',
                [
                    'response' => $response,
                    'error' => $e->getMessage(),
                ]
            );
            return [
                'content_type' => ContentType::EMAIL,
                'campaign_type' => CampaignType::NEWSLETTER,
                'context' => [
                    'parsed_from_request' => false,
                    'error' => $e->getMessage(),
                ],
                'tone' => 'N/A',
                'target_audience' => 'N/A',
                'key_elements' => [],
                'content_quality' => [],
                'reasoning' => 'Error occurred during response parsing',
            ];
        }
    }
}
