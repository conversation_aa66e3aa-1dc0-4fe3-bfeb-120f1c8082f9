<?php

namespace Salesfloor\Services\AI\Model;

class AIResponse
{
    /**
     * The raw response from the AI provider
     */
    private array $rawResponse;

    /**
     * The generated text content
     */
    private string $content;

    /**
     * The model used to generate the response
     */
    private string $model;

    /**
     * The provider that generated the response
     */
    private string $provider;

    /**
     * Additional metadata from the response
     */
    private array $metadata = [];

    /**
     * Usage statistics from the model (tokens, cost, etc.)
     */
    private array $usage = [];

    /**
     * Create a new AI response
     *
     * @param string $content The generated text content
     * @param string $model The model used to generate the response
     * @param string $provider The provider that generated the response
     * @param array $rawResponse The raw response from the provider
     * @param array $metadata Additional metadata
     * @param array $usage Usage statistics
     */
    public function __construct(
        string $content,
        string $model,
        string $provider,
        array $rawResponse,
        array $metadata = [],
        array $usage = []
    ) {
        $this->content = $content;
        $this->model = $model;
        $this->provider = $provider;
        $this->rawResponse = $rawResponse;
        $this->metadata = $metadata;
        $this->usage = $usage;
    }

    /**
     * Get the generated content
     *
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * Get the model name
     *
     * @return string
     */
    public function getModel(): string
    {
        return $this->model;
    }

    /**
     * Get the provider name
     *
     * @return string
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Get the raw response
     *
     * @return array
     */
    public function getRawResponse(): array
    {
        return $this->rawResponse;
    }

    /**
     * Get all metadata
     *
     * @return array
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Get a specific metadata value
     *
     * @param string $key The metadata key
     * @param mixed $default The default value to return if the key doesn't exist
     * @return mixed
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Get usage statistics
     *
     * @return array
     */
    public function getUsage(): array
    {
        return $this->usage;
    }

    /**
     * Get a specific usage statistic
     *
     * @param string $key The usage key
     * @param mixed $default The default value to return if the key doesn't exist
     * @return mixed
     */
    public function getUsageValue(string $key, $default = null)
    {
        return $this->usage[$key] ?? $default;
    }

    /**
     * Get total token count used (prompt + completion)
     *
     * @return int|null
     */
    public function getTotalTokens(): ?int
    {
        return $this->usage['total_tokens'] ?? null;
    }

    /**
     * Get prompt token count
     *
     * @return int|null
     */
    public function getPromptTokens(): ?int
    {
        return $this->usage['prompt_tokens'] ?? null;
    }

    /**
     * Get completion token count
     *
     * @return int|null
     */
    public function getCompletionTokens(): ?int
    {
        return $this->usage['completion_tokens'] ?? null;
    }
}
