<?php

namespace Salesfloor\Services\AI\Exception;

use Salesfloor\Exceptions\GlobalException;

class AIAdapterException extends GlobalException
{
    /**
     * The provider that threw the exception
     */
    private string $provider;

    /**
     * The raw API response
     */
    private ?array $response;

    /**
     * Create a new AI adapter exception
     *
     * @param string $message The error message
     * @param string $provider The provider name
     * @param array|null $response The raw API response
     * @param int $code The error code
     * @param \Throwable|null $previous The previous exception
     */
    public function __construct(
        string $message,
        string $provider,
        ?array $response = null,
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->provider = $provider;
        $this->response = $response;
    }

    /**
     * Get the provider name
     *
     * @return string
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Get the raw API response
     *
     * @return array|null
     */
    public function getResponse(): ?array
    {
        return $this->response;
    }
}
