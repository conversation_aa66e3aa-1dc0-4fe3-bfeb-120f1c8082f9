<?php

namespace Salesfloor\Services\AI;

use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\AI\Config\AIConfig;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\ProviderAdapter\ProviderAdapterInterface;
use Salesfloor\Services\AI\ProviderAdapter\VertexAIAdapter;
use Salesfloor\Services\Auth\TokenService;

class Service
{
    /**
     * Default AI provider
     */
    private const DEFAULT_FALLBACK_PROVIDER = 'vertexai';

    /**
     * Default models for each provider
     */
    private const DEFAULT_MODELS = [
        'vertexai' => 'gemini-2.0-flash-lite',
        // add more models e.g 'openai' => 'gpt-3.5-turbo',
    ];

    /**
     * The application global configuration storage
     */
    private Configs $configs;

    /**
     * The logger
     */
    private LoggerInterface $logger;

    /**
     * The HTTP client
     */
    private Client $httpClient;

    /**
     * Token service for authentication
     */
    private TokenService $tokenService;

    /**
     * Available provider adapters
     *
     * @var ProviderAdapterInterface[]
     */
    private array $adapters = [];

    /**
     * Current active provider adapter
     */
    private ?ProviderAdapterInterface $activeAdapter = null;

    /**
     * AI Service constructor
     *
     * @param Configs $configs The application configs store
     * @param LoggerInterface $logger The logger
     * @param TokenService $tokenService The token service
     * @param Client|null $httpClient The HTTP client
     */
    public function __construct(
        Configs $configs,
        LoggerInterface $logger,
        TokenService $tokenService,
        ?Client $httpClient = null
    ) {
        $this->configs = $configs;
        $this->logger = $logger;
        $this->tokenService = $tokenService;
        $this->httpClient = $httpClient ?? new Client();

        $this->initializeAdapters();
    }

    /**
     * Initialize available provider adapters
     */
    private function initializeAdapters(): void
    {
        $this->adapters = [
            'vertexai' => new VertexAIAdapter(
                $this->logger,
                $this->tokenService,
                $this->httpClient
            ),
            // Add more adapters here as needed
            // 'openai' => new OpenAIAdapter($this->logger, $this->httpClient),
        ];
    }

    /**
     * Get the adapter for the specified provider
     *
     * @param string|null $provider The provider name, or null to use the default
     * @return ProviderAdapterInterface The provider adapter
     * @throws AIAdapterException If the adapter is not found
     */
    public function getAdapter(
        ?string $provider = null
    ): ProviderAdapterInterface {
        $providerName = $provider ?? $this->getDefaultProvider();

        if (!isset($this->adapters[$providerName])) {
            throw new AIAdapterException(
                "Provider adapter not found: {$providerName}",
                $providerName
            );
        }

        return $this->adapters[$providerName];
    }

    /**
     * Set the active provider adapter by name
     *
     * @param string $provider The provider name
     * @param AIConfig|null $config Optional configuration
     * @return self
     * @throws AIAdapterException If the adapter is not found or initialization fails
     */
    public function useProvider(
        string $provider,
        ?AIConfig $config = null
    ): self {
        $adapter = $this->getAdapter($provider);

        if ($config !== null) {
            $adapter->initialize($config);
        } else {
            $adapter->initialize($this->createConfigFromSettings($provider));
        }

        $this->activeAdapter = $adapter;

        return $this;
    }

    /**
     * Create a configuration from application settings
     *
     * @param string $provider The provider name
     * @return AIConfig The configuration
     */
    private function createConfigFromSettings(string $provider): AIConfig
    {
        $configs = $this->configs;
        $model =
            $configs["ai.{$provider}.model"] ??
            (self::DEFAULT_MODELS[$provider] ??
                throw new \InvalidArgumentException(
                    "No default model found for provider: $provider"
                ));

        return match ($provider) {
            'vertexai' => AIConfig::forVertexAI(
                $model,
                $configs['ai.vertexai.project_id'],
                $configs['ai.vertexai.location'],
                $this->getServiceAccountFromConfig(
                    $configs['ai.vertexai.service_account']
                ),
                [
                    'temperature' =>
                        (float) $configs['ai.vertexai.temperature'],
                    'max_tokens' => (int) $configs['ai.vertexai.max_tokens'],
                ]
            ),
            // add any other model here: e.g
            // 'openai' => AIConfig::forOpenAI(
            //     $model,
            //     $configs['ai.openai.apikey'],
            //     [
            //         'temperature' => (float)($configs['ai.openai.temperature'] ?? 0.7),
            //         'max_tokens' => (int)($configs['ai.openai.max_tokens'] ?? 1024),
            //     ]
            // ),
            default => throw new \InvalidArgumentException(
                "Unsupported provider: $provider"
            ),
        };
    }

    /**
     * Get the active provider adapter
     *
     * @return ProviderAdapterInterface The active provider adapter
     * @throws AIAdapterException If no active adapter is set
     */
    public function getActiveAdapter(): ProviderAdapterInterface
    {
        if ($this->activeAdapter === null) {
            $this->useProvider($this->getDefaultProvider());
        }
        return $this->activeAdapter;
    }

    /**
     * Get the default AI provider based on configuration or fallback
     *
     * @return string The default provider name
     */
    public function getDefaultProvider(): string
    {
        return $this->configs['ai.default_provider'] ??
            self::DEFAULT_FALLBACK_PROVIDER;
    }

    /**
     * Generate chat completion
     *
     * @param array<int, array{role: string, content: string}> $messages Array of message objects
     * @param array<string, mixed> $options Optional parameters
     * @return AIResponse The generated response
     * @throws AIAdapterException If generation fails
     */
    public function generateChatCompletion(
        array $messages,
        array $options = []
    ): AIResponse {
        return $this->getActiveAdapter()->generateChatCompletion(
            $messages,
            $options
        );
    }

    /**
     * Generate embeddings
     *
     * @param string|array<string> $text The text to generate embeddings for
     * @param array $options Optional parameters
     * @return array<int|string, array<float>> The generated embeddings
     * @throws AIAdapterException If generation fails
     */
    public function generateEmbeddings(
        string|array $text,
        array $options = []
    ): array {
        return $this->getActiveAdapter()->generateEmbeddings($text, $options);
    }

    /**
     * Check if a provider is available
     *
     * @param string $provider The provider name
     * @return bool True if the provider is available, false otherwise
     */
    public function isProviderAvailable(string $provider): bool
    {
        try {
            $adapter = $this->getAdapter($provider);
            return $adapter->isAvailable();
        } catch (\Throwable $_) {
            return false;
        }
    }

    /**
     * Get all available providers
     *
     * @return array<string> Array of available provider names
     */
    public function getAvailableProviders(): array
    {
        return array_keys(
            array_filter(
                $this->adapters,
                static fn(
                    ProviderAdapterInterface $adapter
                ): bool => $adapter->isAvailable()
            )
        ) ?:
            [];
    }

    /**
     * Adds a custom provider adapter
     *
     * @param string $name The provider name
     * @param ProviderAdapterInterface $adapter The provider adapter
     * @return self
     */
    public function addAdapter(
        string $name,
        ProviderAdapterInterface $adapter
    ): self {
        $this->adapters[$name] = $adapter;
        return $this;
    }

    /**
     * Get service account from base64 encoded config
     *
     * @param string $encodedServiceAccount Base64 encoded service account JSON
     * @return array<string, mixed>|null Decoded service account as array or null
     * @throws \JsonException When JSON cannot be decoded properly
     */
    private function getServiceAccountFromConfig(
        string $encodedServiceAccount
    ): ?array {
        if (empty($encodedServiceAccount)) {
            return null;
        }

        $json = base64_decode($encodedServiceAccount, true);
        if ($json === false || empty($json)) {
            return null;
        }

        try {
            $serviceAccount = json_decode(
                $json,
                associative: true,
                depth: 512,
                flags: JSON_THROW_ON_ERROR | JSON_INVALID_UTF8_IGNORE
            );
            return is_array($serviceAccount) ? $serviceAccount : null;
        } catch (\JsonException $e) {
            $this->logger->error(
                'Failed to decode service account JSON: ' . $e->getMessage()
            );
            return null;
        }
    }
}
