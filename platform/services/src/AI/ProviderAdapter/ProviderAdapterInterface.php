<?php

namespace Salesfloor\Services\AI\ProviderAdapter;

use Salesfloor\Services\AI\Config\AIConfig;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;

interface ProviderAdapterInterface
{
    /**
     * Initialize the provider adapter with the given configuration
     *
     * @param AIConfig $config The configuration for this adapter
     * @return void
     */
    public function initialize(AIConfig $config): void;

    /**
     * Check if this adapter is available (i.e., properly configured)
     *
     * @return bool True if the adapter is available, false otherwise
     */
    public function isAvailable(): bool;

    /**
     * Get the name of this provider
     *
     * @return string The provider name
     */
    public function getName(): string;

    /**
     * Generate chat completion based on the messages
     *
     * @param array $messages Array of message objects with 'role' and 'content' keys
     * @param array $options Optional parameters for the completion
     * @return AIResponse The generated response
     * @throws AIAdapterException If there's an error generating the chat completion
     */
    public function generateChatCompletion(
        array $messages,
        array $options = []
    ): AIResponse;

    /**
     * Generate embeddings for the given text
     *
     * @param string|array $text The text to generate embeddings for
     * @param array $options Optional parameters for the embeddings
     * @return array The generated embeddings
     * @throws AIAdapterException If there's an error generating the embeddings
     */
    public function generateEmbeddings($text, array $options = []): array;
}
