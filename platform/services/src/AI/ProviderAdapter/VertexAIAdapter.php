<?php

namespace Salesfloor\Services\AI\ProviderAdapter;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Config\AIConfig;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\Auth\TokenService;

class VertexAIAdapter implements ProviderAdapterInterface
{
    /**
     * determine how creative/random AI should be in response generation
     */
    private const AI_DEFAULT_TEMPERATURE = 0.7;

    /**
     * Determine maximum token response output
     */
    private const AI_DEFAULT_MAX_OUTPUT_TOKENS = 8192;

    /**
     * Determines vocabulary token selection of AI
     */
    private const AI_DEFAULT_TOP_K = 40;

    /**
     * Determine diversity of results of AI
     */
    private const AI_DEFAULT_TOP_P = 0.95;

    /**
     * The API base URL pattern
     */
    private const API_BASE_URL = 'https://{location}-aiplatform.googleapis.com/v1/projects/{project}/locations/{location}';

    /**
    * Represents the role used for specific system instructions - soft fine-tuning
    */
    private const AI_SYSTEM_ROLE = 'system';

    /**
    * Represents the user in the context
    */
    private const AI_USER_ROLE = 'user';

    /**
    * Represents the model in the context
    */
    private const AI_ASSISTANT_ROLE = 'assistant';


    /**
     * The AI configuration
     */
    private AIConfig $config;

    /**
     * The HTTP client
     */
    private Client $httpClient;

    /**
     * The logger
     */
    private LoggerInterface $logger;

    /**
     * Token service for Google authentication
     */
    private TokenService $tokenService;

    /**
     * Whether this adapter is initialized
     */
    private bool $initialized = false;

    /**
     * VertexAIAdapter constructor
     *
     * @param LoggerInterface $logger
     * @param TokenService $tokenService
     * @param Client|null $httpClient
     */
    public function __construct(
        LoggerInterface $logger,
        TokenService $tokenService,
        ?Client $httpClient = null
    ) {
        $this->logger = $logger;
        $this->tokenService = $tokenService;
        $this->httpClient = $httpClient ?? new Client();
    }

    /**
     * Check if the model is a Gemini model
     *
     * @param string $model The model name
     * @return bool True if it's a Gemini model
     */
    private function isGeminiModel(string $model): bool
    {
        return str_starts_with($model, 'gemini-');
    }

    /**
     * @inheritDoc
     */
    public function initialize(AIConfig $config): void
    {
        if ($config->getProvider() !== 'vertexai') {
            throw new AIAdapterException(
                'Invalid provider for Vertex AI adapter: ' .
                    $config->getProvider(),
                'vertexai'
            );
        }

        if (empty($config->getProjectId())) {
            throw new AIAdapterException(
                'Project ID is required for Vertex AI adapter',
                'vertexai'
            );
        }

        if (empty($config->getLocation())) {
            throw new AIAdapterException(
                'Location is required for Vertex AI adapter',
                'vertexai'
            );
        }

        $this->config = $config;
        $this->initialized = true;
    }

    /**
     * @inheritDoc
     */
    public function isAvailable(): bool
    {
        return $this->initialized &&
            !empty($this->config->getProjectId()) &&
            !empty($this->config->getLocation());
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'vertexai';
    }

    /**
     * @inheritDoc
     */
    public function generateChatCompletion(
        array $messages,
        array $options = []
    ): AIResponse {
        $this->ensureInitialized();

        $model = $options['model'] ?? $this->config->getModel();

        if ($this->isGeminiModel($model)) {
            return $this->generateGeminiCompletion($messages, $options);
        }

        throw new AIAdapterException(
            'Vertex AI model not supported',
            'vertexai'
        );
    }

    /**
     * Generate completion using Vertex AI Gemini models
     *
     * @param array $messages The messages
     * @param array $options Additional options
     * @return AIResponse The AI response
     * @throws AIAdapterException If there's an error
     */
    private function generateGeminiCompletion(
        array $messages,
        array $options = []
    ): AIResponse {
        $model = $options['model'] ?? $this->config->getModel();

        // Separate messages by role in a single pass
        $conversationMessages = [];
        $systemMessages = [];

        foreach ($messages as $message) {
            $role = strtolower($message['role'] ?? '');
            if ($role === self::AI_SYSTEM_ROLE) {
                $systemMessages[] = $message;
            } else {
                $conversationMessages[] = $message;
            }
        }

        // Convert messages to Gemini format
        $contents = [];
        foreach ($conversationMessages as $message) {
            $role = strtolower($message['role'] ?? self::AI_USER_ROLE);
            $content = $message['content'] ?? '';

            // Map 'assistant' role to 'model' for Gemini
            $role = match ($role) {
                self::AI_ASSISTANT_ROLE => 'model',
                self::AI_USER_ROLE => $role,
            };

            $contents[] = [
                'role' => $role,
                'parts' => [['text' => $content]],
            ];
        }

        $systemInstruction = [];
        if (!empty($systemMessages)) {
            // Combine all system message content
            $systemContents = [];
            foreach ($systemMessages as $message) {
                $systemContents[] = ['text' => $message['content'] ?? ''];
            }

            $systemInstruction = [
            'role' => self::AI_SYSTEM_ROLE,
            'parts' => $systemContents
            ];
        }



        $requestData = [
            'contents' => $contents,
            'generationConfig' => [
                'temperature' =>
                    $options['temperature'] ??
                    $this->config->getOption(
                        'temperature',
                        self::AI_DEFAULT_TEMPERATURE
                    ),
                'maxOutputTokens' =>
                    $options['max_tokens'] ??
                    $this->config->getOption(
                        'max_tokens',
                        self::AI_DEFAULT_MAX_OUTPUT_TOKENS
                    ),
                'topK' =>
                    $options['top_k'] ??
                    $this->config->getOption('top_k', self::AI_DEFAULT_TOP_K),
                'topP' =>
                    $options['top_p'] ??
                    $this->config->getOption('top_p', self::AI_DEFAULT_TOP_P),
            ],
            'safetySettings' => [

            ]
        ];

        // Only add systemInstruction if we have system messages
        if (!empty($systemInstruction)) {
            $requestData['systemInstruction'] = $systemInstruction;
        }



        try {
            // Not supporting stream at this time. if we want to support streaming responses, we could use the streamGenerateContent endpoint
            $endpoint = $this->buildEndpoint(
                "/publishers/google/models/{$model}:generateContent"
            );

            [$statusCode, $body] = $this->makeRequest($endpoint, $requestData);

            if ($statusCode !== 200) {
                $this->logger->error(
                    'Vertex AI Gemini API error: ' . json_encode($body)
                );
                throw new AIAdapterException(
                    'Vertex AI Gemini API error: ' .
                        ($body['error']['message'] ?? 'Unknown error'),
                    'vertexai',
                    $body,
                    $statusCode
                );
            }

            // Extract text from Gemini response format
            $content = '';
            if (!empty($body['candidates'][0]['content']['parts'])) {
                $parts = array_filter(
                    $body['candidates'][0]['content']['parts'],
                    static fn(array $part): bool => isset($part['text'])
                );

                $content = implode(
                    '',
                    array_map(
                        static fn(array $part): string => $part['text'],
                        $parts
                    )
                );
            }

            // Calculate token usage (approximation since Vertex doesn't return this)
            $promptText = implode(
                ' ',
                array_map(
                    static fn(array $msg): string => $msg['content'] ?? '',
                    $messages
                )
            );

            $promptTokens = $this->estimateTokenCount($promptText);
            $completionTokens = $this->estimateTokenCount($content);
            $usage = [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $promptTokens + $completionTokens,
            ];

            return new AIResponse(
                $content,
                $model,
                'vertexai',
                $body,
                [],
                $usage
            );
        } catch (GuzzleException $e) {
            $this->logger->error(
                'Vertex AI Gemini API request error: ' . $e->getMessage()
            );
            throw new AIAdapterException(
                'Vertex AI Gemini API request error: ' . $e->getMessage(),
                'vertexai',
                null,
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * @inheritDoc
     */
    public function generateEmbeddings($text, array $options = []): array
    {
        $this->ensureInitialized();

        $input = is_array($text) ? $text : [$text];
        $model = $options['model'] ?? 'text-embedding-005';

        return $this->isGeminiModel($model)
            ? $this->generateGeminiEmbeddings($input, $options)
            : $this->generateStandardEmbeddings($input, $model, $options);
    }

    /**
     * Generate embeddings using standard Vertex AI models
     *
     * @param array $input Array of texts to embed
     * @param string $model Model name
     * @param array $options Additional options
     * @return array The embeddings
     * @throws AIAdapterException If there's an error
     */
    private function generateStandardEmbeddings(
        array $input,
        string $model,
        array $options = []
    ): array {
        $requestData = [
            'instances' => array_map(
                static fn(string $text): array => ['content' => $text],
                $input
            ),
        ];

        try {
            $endpoint = $this->buildEndpoint(
                "/publishers/google/models/{$model}:predict"
            );

            [$statusCode, $body] = $this->makeRequest($endpoint, $requestData);

            if ($statusCode !== 200) {
                $this->logger->error(
                    'Vertex AI API error: ' . json_encode($body)
                );
                throw new AIAdapterException(
                    'Vertex AI API error: ' .
                        ($body['error']['message'] ?? 'Unknown error'),
                    'vertexai',
                    $body,
                    $statusCode
                );
            }

            $embeddings = array_map(
                static fn(array $prediction): array => $prediction[
                    'embeddings'
                ]['values'],
                $body['predictions']
            );

            return count($input) === 1 ? $embeddings[0] : $embeddings;
        } catch (GuzzleException $e) {
            $this->logger->error(
                'Vertex AI API request error: ' . $e->getMessage()
            );
            throw new AIAdapterException(
                'Vertex AI API request error: ' . $e->getMessage(),
                'vertexai',
                null,
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Generate embeddings using Vertex AI Gemini models
     *
     * @param array $texts The texts to generate embeddings for
     * @param array $options Additional options
     * @return array The embeddings
     * @throws AIAdapterException If there's an error
     */
    private function generateGeminiEmbeddings(
        array $texts,
        array $options = []
    ): array {
        $model = $options['model'] ?? $this->config->getModel();

        // Format request data for Gemini embedding models
        $requestData = [
            'instances' => array_map(
                static fn(string $text): array => [
                    'content' => [
                        'parts' => [['text' => $text]],
                    ],
                ],
                $texts
            ),
        ];

        try {
            $endpoint = $this->buildEndpoint(
                "/publishers/google/models/{$model}:embedContent"
            );

            [$statusCode, $body] = $this->makeRequest($endpoint, $requestData);

            if ($statusCode !== 200) {
                $this->logger->error(
                    'Vertex AI Gemini API error: ' . json_encode($body)
                );
                throw new AIAdapterException(
                    'Vertex AI Gemini API error: ' .
                        ($body['error']['message'] ?? 'Unknown error'),
                    'vertexai',
                    $body,
                    $statusCode
                );
            }

            $embeddings = array_map(
                static fn(array $embedding): array => $embedding['values'],
                $body['embeddings']
            );

            return count($texts) === 1 ? $embeddings[0] : $embeddings;
        } catch (GuzzleException $e) {
            $this->logger->error(
                'Vertex AI Gemini embedding API request error: ' .
                    $e->getMessage()
            );
            throw new AIAdapterException(
                'Vertex AI Gemini embedding API request error: ' .
                    $e->getMessage(),
                'vertexai',
                null,
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Get an access token for Google services
     *
     * @return string The access token
     * @throws AIAdapterException If unable to get an access token
     */
    private function getAccessToken(): string
    {
        try {
            return $this->tokenService->getGoogleAccessToken(
                $this->config->getServiceAccount()
            );
        } catch (\Exception $e) {
            $this->logger->error(
                'Failed to get Google access token: ' . $e->getMessage()
            );
            throw new AIAdapterException(
                'Failed to get Google access token: ' . $e->getMessage(),
                'vertexai',
                null,
                0,
                $e
            );
        }
    }

    /**
     * Build the endpoint URL with project and location
     *
     * @param string $path The API path
     * @return string The full endpoint URL
     */
    private function buildEndpoint(string $path): string
    {
        $baseUrl = str_replace(
            ['{project}', '{location}'],
            [$this->config->getProjectId(), $this->config->getLocation()],
            self::API_BASE_URL
        );

        return $baseUrl . $path;
    }

    /**
     * Estimate token count for a given text
     * This is a rough approximation (1 token ≈ 4 characters for English text)
     *
     * @param string $text The text to estimate tokens for
     * @return int Estimated token count
     */
    private function estimateTokenCount(string $text): int
    {
        return (int) ceil(mb_strlen($text, 'UTF-8') / 4);
    }

    /**
     * Ensure the adapter is initialized
     *
     * @throws AIAdapterException
     */
    private function ensureInitialized(): void
    {
        if (!$this->initialized) {
            throw new AIAdapterException(
                'Vertex AI adapter not initialized',
                'vertexai'
            );
        }
    }

    /**
     * Send a http request
     *
     * @param string $endpoint url to the provider api service
     * @param array $requestData payload
     */
    private function makeRequest(string $endpoint, array $requestData)
    {
        $accessToken = $this->getAccessToken();
        $response = $this->httpClient->request('POST', $endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $requestData,
            'http_errors' => false,
        ]);

        $statusCode = $response->getStatusCode();
        $body = json_decode($response->getBody()->getContents(), true);

        return [$statusCode, $body];
    }
}
