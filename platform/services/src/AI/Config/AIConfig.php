<?php

namespace Salesfloor\Services\AI\Config;

class AIConfig
{
    /**
     * The provider name (e.g., 'openai', 'vertexai', 'claude')
     */
    private string $provider;

    /**
     * The model name to use (e.g., 'gpt-4', 'gemini-2.0-flash', 'claude-3.5')
     */
    private string $model;

    /**
     * API key for authentication (if required)
     */
    private ?string $apiKey = null;

    /**
     * API endpoint URL
     */
    private ?string $apiEndpoint = null;

    /**
     * Service account credentials for Google services
     */
    private ?array $serviceAccount = null;

    /**
     * Project ID for Google services
     */
    private ?string $projectId = null;

    /**
     * Location for Google services (e.g., 'us-central1')
     */
    private ?string $location = null;

    /**
     * Additional configuration options
     */
    private array $options = [];

    /**
     * Create a new AI configuration
     *
     * @param string $provider The provider name
     * @param string $model    The model name
     */
    public function __construct(string $provider, string $model)
    {
        $this->provider = $provider;
        $this->model = $model;
    }

    /**
     * Create a configuration for Vertex AI
     *
     * @param  string     $model          The model name (e.g., 'text-bison')
     * @param  string     $projectId      The Google Cloud project ID
     * @param  string     $location       The location (e.g., 'us-central1')
     * @param  array|null $serviceAccount The service account credentials (JSON as array)
     * @param  array      $options        Additional options
     * @return AIConfig
     */
    public static function forVertexAI(
        string $model,
        string $projectId,
        string $location,
        ?array $serviceAccount = null,
        array $options = []
    ): AIConfig {
        $config = new self('vertexai', $model);
        $config->setProjectId($projectId);
        $config->setLocation($location);
        $config->setServiceAccount($serviceAccount);
        $config->setOptions($options);
        return $config;
    }

    /**
     * Get the provider name
     *
     * @return string
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Get the model name
     *
     * @return string
     */
    public function getModel(): string
    {
        return $this->model;
    }

    /**
     * Set the API key
     *
     * @param  string $apiKey
     * @return $this
     */
    public function setApiKey(string $apiKey): self
    {
        $this->apiKey = $apiKey;
        return $this;
    }

    /**
     * Get the API key
     *
     * @return string|null
     */
    public function getApiKey(): ?string
    {
        return $this->apiKey;
    }

    /**
     * Set the API endpoint
     *
     * @param  string $apiEndpoint
     * @return $this
     */
    public function setApiEndpoint(string $apiEndpoint): self
    {
        $this->apiEndpoint = $apiEndpoint;
        return $this;
    }

    /**
     * Get the API endpoint
     *
     * @return string|null
     */
    public function getApiEndpoint(): ?string
    {
        return $this->apiEndpoint;
    }

    /**
     * Set the service account credentials
     *
     * @param  array|null $serviceAccount
     * @return $this
     */
    public function setServiceAccount(?array $serviceAccount): self
    {
        $this->serviceAccount = $serviceAccount;
        return $this;
    }

    /**
     * Get the service account credentials
     *
     * @return array|null
     */
    public function getServiceAccount(): ?array
    {
        return $this->serviceAccount;
    }

    /**
     * Set the project ID
     *
     * @param  string $projectId
     * @return $this
     */
    public function setProjectId(string $projectId): self
    {
        $this->projectId = $projectId;
        return $this;
    }

    /**
     * Get the project ID
     *
     * @return string|null
     */
    public function getProjectId(): ?string
    {
        return $this->projectId;
    }

    /**
     * Set the location
     *
     * @param  string $location
     * @return $this
     */
    public function setLocation(string $location): self
    {
        $this->location = $location;
        return $this;
    }

    /**
     * Get the location
     *
     * @return string|null
     */
    public function getLocation(): ?string
    {
        return $this->location;
    }

    /**
     * Set additional options
     *
     * @param  array $options
     * @return $this
     */
    public function setOptions(array $options): self
    {
        $this->options = $options;
        return $this;
    }

    /**
     * Get all additional options
     *
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Get a specific option
     *
     * @param  string $key     The option key
     * @param  mixed  $default The default value to return if the option is not set
     * @return mixed
     */
    public function getOption(string $key, $default = null)
    {
        return $this->options[$key] ?? $default;
    }

    /**
     * Set a specific option
     *
     * @param  string $key   The option key
     * @param  mixed  $value The option value
     * @return $this
     */
    public function setOption(string $key, $value): self
    {
        $this->options[$key] = $value;
        return $this;
    }
}
