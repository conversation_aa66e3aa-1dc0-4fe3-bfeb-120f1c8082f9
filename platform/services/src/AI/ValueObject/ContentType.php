<?php

namespace Salesfloor\Services\AI\ValueObject;

final class ContentType
{
    public const EMAIL = 'email';
    public const SMS = 'sms';
    public const TEXT = 'text';
    public const SUBJECT_LINES = 'subject_lines';

    private const VALID_TYPES = [
        self::EMAIL,
        self::SMS,
        self::TEXT,
        self::SUBJECT_LINES,
    ];

    private const TYPE_ALIASES = [
        'txt' => self::SMS,
        'message' => self::SMS,
        'subject' => self::SUBJECT_LINES,
        'subject_line' => self::SUBJECT_LINES,
    ];

    private string $value;

    public function __construct(string $value)
    {
        $this->value = $this->normalize($value);
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function isEmail(): bool
    {
        return $this->value === self::EMAIL;
    }

    public function isSms(): bool
    {
        return $this->value === self::SMS || $this->value === self::TEXT;
    }

    public function isSubjectLines(): bool
    {
        return $this->value === self::SUBJECT_LINES;
    }

    public function __toString(): string
    {
        return $this->value;
    }

    private function normalize(string $value): string
    {
        $normalized = strtolower(trim($value));

        if (in_array($normalized, self::VALID_TYPES)) {
            return $normalized;
        }

        return self::TYPE_ALIASES[$normalized] ?? self::EMAIL;
    }

    public static function email(): self
    {
        return new self(self::EMAIL);
    }

    public static function sms(): self
    {
        return new self(self::SMS);
    }

    public static function subjectLines(): self
    {
        return new self(self::SUBJECT_LINES);
    }
}
