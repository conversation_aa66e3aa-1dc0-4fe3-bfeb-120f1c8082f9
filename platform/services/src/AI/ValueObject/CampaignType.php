<?php

namespace Salesfloor\Services\AI\ValueObject;

final class CampaignType
{
    public const PRODUCT_LAUNCH = 'product_launch';
    public const PROMOTION = 'promotion';
    public const EVENT = 'event';
    public const NEWSLETTER = 'newsletter';
    public const WELCOME = 'welcome';
    public const REENGAGEMENT = 'reengagement';
    public const CUSTOMER_UPDATE = 'customer_update';
    public const SALE = 'sale';
    public const UNKNOWN = 'unknown';

    private const VALID_TYPES = [
        self::PRODUCT_LAUNCH,
        self::PROMOTION,
        self::EVENT,
        self::NEWSLETTER,
        self::WELCOME,
        self::REENGAGEMENT,
        self::CUSTOMER_UPDATE,
        self::SALE,
    ];

    private const TYPE_ALIASES = [
        'promo' => self::PROMOTION,
        'discount' => self::PROMOTION,
        'offer' => self::PROMOTION,
        'launch' => self::PRODUCT_LAUNCH,
        'new_product' => self::PRODUCT_LAUNCH,
        'update' => self::CUSTOMER_UPDATE,
        'announcement' => self::CUSTOMER_UPDATE,
    ];

    private string $value;

    public function __construct(string $value)
    {
        $this->value = $this->normalize($value);
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function isPromotion(): bool
    {
        return $this->value === self::PROMOTION || $this->value === self::SALE;
    }

    public function isEvent(): bool
    {
        return $this->value === self::EVENT;
    }

    public function isProductLaunch(): bool
    {
        return $this->value === self::PRODUCT_LAUNCH;
    }

    public function __toString(): string
    {
        return $this->value;
    }

    private function normalize(string $value): string
    {
        $normalized = strtolower(trim($value));

        if (in_array($normalized, self::VALID_TYPES)) {
            return $normalized;
        }

        return self::TYPE_ALIASES[$normalized] ?? self::NEWSLETTER;
    }

    public static function promotion(): self
    {
        return new self(self::PROMOTION);
    }

    public static function productLaunch(): self
    {
        return new self(self::PRODUCT_LAUNCH);
    }

    public static function event(): self
    {
        return new self(self::EVENT);
    }

    public static function newsletter(): self
    {
        return new self(self::NEWSLETTER);
    }

    public static function welcome(): self
    {
        return new self(self::WELCOME);
    }

    public static function reengagement(): self
    {
        return new self(self::REENGAGEMENT);
    }

    public static function customerUpdate(): self
    {
        return new self(self::CUSTOMER_UPDATE);
    }
}
