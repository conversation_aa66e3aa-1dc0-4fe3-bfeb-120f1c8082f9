<?php

namespace Salesfloor\Services\AI\Util;

/**
 * Prompt utilities
 *
 * Contains only shared prompt components and utilities that can be reused
 * across multiple agents. Agent-specific prompts should remain in their respective agents.
 */
class Prompt
{
    public const SMS_CHARACTER_LIMIT = 160;

    /**
     * Get standard campaign type definitions
     */
    public static function getCampaignTypeDefinitions(): string
    {
        return <<<EOT
You must classify requests into one of these campaign types:
- product_launch: New product announcements or introductions
- promotion: Sales, discounts, limited-time offers
- event: Invitations to events, webinars, or in-store activities
- newsletter: Regular updates or multi-topic communications
- customer_update: Important news, policy changes, or service updates
- reengagement: Attempts to reconnect with inactive customers
- welcome: Onboarding communications for new customers/subscribers
EOT;
    }

    /**
     * Get common marketing guidelines
     */
    public static function getMarketingGuidelines(): string
    {
        return <<<EOT
        When generating marketing copy, adhere to the following guidelines:

        1. Tone & Style
           - Professional yet conversational.
           - Engaging and concise: short paragraphs or single-sentence blocks where possible.
           - Always highlight customer benefits.
           - Align with the brand voice and context.

        2. What to Avoid
           - Overly “salesy” or pushy language.
           - Clickbait tactics or sensational phrasing.
           - Lengthy paragraphs: break ideas into digestible pieces.
           - Generic or vague messaging: favor specificity.
           - Do not add subject lines unless the user explicitly asks for them.
           - acknowledging the request. just give the expected response.
           - responding to request that do not fit the context of a marketing campaign or not meeting the predefined campaign types

        3. Language & Politeness
           - If unable to fulfill a prompt request, inform the user politely and offer alternatives if possible.
           - **CRITICAL REQUIREMENT**: ALWAYS respond or communicate in the EXACT same language used by the user provided input or user provided prompt. This is mandatory and non-negotiable:
             - If the user prompt content is in French, you MUST reply in French.
             - If the user prompt content is in English but the user requests French, you MUST reply in French.
             - If the user prompt content is in Spanish, you MUST reply in Spanish.
             - If the user prompt content is in any other language, you MUST reply in that language.
             - Do NOT translate or change the language unless explicitly requested by the user.

        4. Placeholders & Customization
           - Use `(customer-name)` for referencing a customer’s name.
           - Use `[..]` for any explicit values the user must supply or replace.
             - E.g., “Get [discount amount] off on every purchase of [product] this summer!”
           - When suggesting events, discounts, durations, or metrics, use placeholders unless the user has provided concrete values.

        5. Mandatory Elements in Every Response (when relevant)
           - A clear value proposition: explain “what’s in it for the customer?”
           - A specific call to action (CTA): guide the user what to do next.
           - Personalized touches where appropriate (e.g., addressing by name if known).

        6. URL/link Usage
           - Use the provided URLs links:
             - For subscription prompts, use  provided `Subscription Url`. Example: “Subscribe: https://someurl.com/subscribe” or “Get the latest updates: https://someurl.com/subscribe”.
             - For appointments or bookings, use the `Appointment Url`. Example: “Book an appointment: https://someurl.com/appointment” or “Schedule a consultation: https://someurl.com/appointment”.
             - For directing to the store/website, use the `Storefront Url`. Example: “Visit our website: https://someurl.com” or “Explore our store: https://someurl.com”.
           - Embed links within action-based phrases so they read fluidly in context.

        7. Content Splitting & Formatting
           - Keep each logical idea or instruction in its own paragraph (no internal blank lines within a part).
           - If generating longer guidance or multi-step copy, break into clear numbered or bullet lists.
           - Ensure that any “systemInstruction” parts are each a single paragraph of text (no embedded blank lines).

        8. Error Handling & Edge Cases
           - If a required value is missing (e.g., user didn’t supply a date or discount), mention politely:
             “Please provide [required detail] so I can tailor the message.”
           - If the request contradicts brand policy or cannot be satisfied, respond:
             “I’m sorry, but I can’t help with that. However, here’s an alternative….”

EOT;
    }

    /**
     * Format context array as string
     */
    public static function formatContextString(array $context): string
    {
        if (empty($context)) {
            return '';
        }

        $contextString = "\n\nAdditional context:\n";
        foreach ($context as $key => $value) {
            if ($key === 'content_type') {
                continue; // Handle separately
            }

            $formattedKey = ucwords(str_replace('_', ' ', $key));
            $formattedValue = is_array($value) ? implode(', ', $value) : $value;
            $contextString .= "- {$formattedKey}: {$formattedValue}\n";
        }

        return $contextString;
    }

    /**
     * Extract content type information from context
     */
    public static function extractContentTypeInfo(array $context): string
    {
        if (!isset($context['content_type'])) {
            return '';
        }

        return "\n\nIMPORTANT: The user has explicitly requested content of type: " .
            $context['content_type'] .
            '. You MUST respect this choice in your analysis.';
    }

    /**
     * Build language enforcement directive based on detected language
     */
    public static function buildLanguageDirective(string $detectedLanguage, bool $hasOverride = false, ?string $overrideReason = null): string
    {
        if ($hasOverride && !empty($overrideReason)) {
            return "\n\nCRITICAL LANGUAGE OVERRIDE: {$overrideReason}. You MUST respond in the specified language regardless of the input language.";
        }

        if ($detectedLanguage !== 'en') {
            $languageNames = [
                'fr' => 'French',
                'es' => 'Spanish',
                'de' => 'German',
                'it' => 'Italian',
                'pt' => 'Portuguese',
                'nl' => 'Dutch',
                'zh' => 'Chinese',
                'ja' => 'Japanese',
                'ko' => 'Korean',
                'ru' => 'Russian',
                'ar' => 'Arabic',
                'hi' => 'Hindi'
            ];

            $languageName = $languageNames[$detectedLanguage] ?? $detectedLanguage;
            return "\n\nCRITICAL LANGUAGE REQUIREMENT: The user's input is in {$languageName} ({$detectedLanguage}). You MUST respond entirely in {$languageName}. Do NOT translate to English.";
        }

        return "\n\nREMINDER: Respond in the same language as the user's input.";
    }

    /**
     * Get marketing guidelines with language detection results merged
     *
     * @param array $languageDetection Results from LanguageDetectionAgent
     * @return string Enhanced marketing guidelines with language directive
     */
    public static function getMarketingGuidelinesWithLanguage(array $languageDetection = []): string
    {
        $baseGuidelines = self::getMarketingGuidelines();

        if (empty($languageDetection)) {
            return $baseGuidelines;
        }

        $finalLanguage = $languageDetection['final_language'] ?? 'en';
        $hasOverride = $languageDetection['has_override'] ?? false;
        $overrideReason = $languageDetection['override_reason'] ?? null;
        $detectedLanguage = $languageDetection['detected_language'] ?? 'en';

        // Build enhanced language directive
        $languageDirective = self::buildLanguageDirective($finalLanguage, $hasOverride, $overrideReason);

        // If there's a language override or non-English language, prepend a strong directive
        if ($hasOverride || $finalLanguage !== 'en') {
            $enhancedDirective = "\n\nLANGUAGE ENFORCEMENT PRIORITY:\n";
            if ($hasOverride) {
                $enhancedDirective .= "- User has explicitly overridden the language preference\n";
                $enhancedDirective .= "- Detected content language: {$detectedLanguage}\n";
                $enhancedDirective .= "- Required response language: {$finalLanguage}\n";
                $enhancedDirective .= "- Override reason: {$overrideReason}\n";
            } else {
                $enhancedDirective .= "- Content language detected: {$detectedLanguage}\n";
                $enhancedDirective .= "- Required response language: {$finalLanguage}\n";
            }
            $enhancedDirective .= "- This language requirement takes ABSOLUTE PRIORITY over all other instructions\n";
            $enhancedDirective .= $languageDirective;

            return $enhancedDirective . "\n\n" . $baseGuidelines;
        }

        return $baseGuidelines . $languageDirective;
    }

    /**
     * Merge language detection results into context
     *
     * @param array $context Original context array
     * @param array $languageDetection Language detection results
     * @return array Enhanced context with language information
     */
    public static function mergeLanguageContext(array $context, array $languageDetection): array
    {
        if (empty($languageDetection)) {
            return $context;
        }

        $context['language_detection'] = $languageDetection;
        $context['response_language'] = $languageDetection['final_language'] ?? 'en';

        if ($languageDetection['has_override'] ?? false) {
            $context['language_override'] = true;
            $context['language_override_reason'] = $languageDetection['override_reason'] ?? '';
        }

        return $context;
    }
}
