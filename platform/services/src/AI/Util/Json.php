<?php

namespace Salesfloor\Services\AI\Util;

use Salesfloor\Services\AI\Exception\InvalidJsonException;

class Json
{
    /**
     * Parse a json string format returned by llm.
     *
     * @param string $input string to be parsed.
     * @@return array
     */
    public static function decode(string $input)
    {
        preg_match('/```json\s*(.*?)\s*```|(\{.*\})/s', $input, $matches);

        $jsonString = '';
        if (!empty($matches[1])) {
            $jsonString = $matches[1];
        } elseif (!empty($matches[2])) {
            $jsonString = $matches[2];
        } else {
            $jsonString = $input;
        }

        // Clean up any markdown or text surrounding the JSON
        $jsonString = preg_replace('/```.*?```/s', '', $jsonString);
        $jsonString = $jsonString ?? '';

        $data = json_decode(trim($jsonString), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidJsonException();
        }
        return $data;
    }
}
