<?php

namespace Salesfloor\Services;

use Salesfloor\Services\MessageQueue\MessageQueueClient;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\Exceptions\EventQueueDatabaseException;
use Salesfloor\Services\MessageQueue\MessageQueueInterface;
use Salesfloor\Models\SidebarEventLog as SidebarEventLogModel;

class EventQueue
{
    const DB_TABLE = 'sf_event_log';

    const MIN_WAIT = 0;
    const MAX_WAIT = 20;
    const FINGERPRINT_COOKIE_POSTFIX = 'sf_wdt_fingerprint';

    const SERVER_CLICK_PREFIX = 'SERVER_CLICK_';

    const SF_EVENT_SOCIALSHOP_VIEW = 'SOCIALSHOP_VIEW';
    const SF_EVENT_SOCIALSHOP_STOREFRONT_CLICK = 'SOCIALSHOP_STOREFRONT_CLICK';
    const SF_EVENT_SOCIALSHOP_PRODUCT_CLICK = 'SOCIALSHOP_PRODUCT_CLICK';

    const SF_EVENT_NEW_SIDEBAR_DESKTOP_CLICK = 'SIDEBAR_DESKTOP_CLICK';
    const SF_EVENT_NEW_SIDEBAR_DESKTOP_VIEW = 'SIDEBAR_DESKTOP_VIEW';
    const SF_EVENT_NEW_SIDEBAR_DESKTOP_MINIMIZE = 'SIDEBAR_DESKTOP_MINIMIZE';
    const SF_EVENT_NEW_SIDEBAR_DESKTOP_MAXIMIZE = 'SIDEBAR_DESKTOP_MAXIMIZE';

    const SF_EVENT_NEW_SIDEBAR_MOBILE_CLICK = 'SIDEBAR_MOBILE_CLICK';
    const SF_EVENT_NEW_SIDEBAR_MOBILE_VIEW = 'SIDEBAR_MOBILE_VIEW';
    const SF_EVENT_NEW_SIDEBAR_MOBILE_MAXIMIZE = 'SIDEBAR_MOBILE_MAXIMIZE';
    const SF_EVENT_NEW_SIDEBAR_MOBILE_MINIMIZE = 'SIDEBAR_MOBILE_MINIMIZE';
    const SF_EVENT_NEW_SIDEBAR_MOBILE_TAGLINE_MINIMIZE = 'SIDEBAR_MOBILE_TAGLINE_MINIMIZE';


    protected $queueId;
    protected $queue;

    // Dead Letter Queue Settings
    protected $deadLetterQueueId;
    // 14 is the max days by AWS limits
    protected $deadLetterRetentionDays = 14;
    protected $numberReceivesBeforeDeadLetter = 5;

    protected $waitTimeSeconds = 20;
    protected $lastQueueUrl;
    protected $lastReceiptHandle;
    // Return to queue and hide message for # Seconds if failed to save it
    protected $hideMessage = 5;
    protected $env;
    protected $retailer;
    protected $attributionService;

    // List of Actions. ALWAYS APPEND TO BOTTOM
    public static $actions = [
        // FOOTER ACTIONS
        'FOOTER_VIEW',
        'FOOTER_CHAT_REQUEST',
        'FOOTER_CHAT_ANSWER',
        'FOOTER_CHAT_END',
        'FOOTER_CHAT_MISSED',
        'FOOTER_APPOINTMENT',
        'FOOTER_PERSONAL_SHOPPER',
        'FOOTER_EMAIL_ME',
        'FOOTER_EMAIL_ME_MISSED_CHAT',

        // Actions recorded from the server side
        'SERVER_ASSOCIATE_CUSTOMER',
        'SERVER_RECORD_TRANSACTION',
        'SERVER_CLICK_TOP_PICKS',       // SF_EVENT_CLICK_TOP_PICKS Clicking of a Top Pick product
        'SERVER_CLICK_LATEST_ARRIVALS', // SF_EVENT_CLICK_LATEST_ARRIVALS Clicking of a Latest Arrivals / Deals product
        'SERVER_CLICK_RECOMMENDED',     // SF_EVENT_CLICK_RECOMMENDED Clicking of a Recommended Product
        'SERVER_CLICK_CHAT',  // SF_EVENT_CLICK_CHAT Click on a product in the chat (*Only customer, not the rep)
        'SERVER_CLICK_SHARE',  // SF_EVENT_CLICK_SHARE Click on a product in the email from a share
        'SERVER_CLICK_MESSAGE',  // SF_EVENT_CLICK_MESSAGE Click on a product in a private message (*Only customer, not the rep)
        'SERVER_CLICK_FACEBOOK',  // SF_EVENT_CLICK_FACEBOOK Click on a product on facebook from a share
        'SERVER_CLICK_TWITTER',  // SF_EVENT_CLICK_TWITTER Click on a product on twitter from a share
        'SERVER_CLICK_LINKEDIN',  // SF_EVENT_CLICK_LINKEDIN NOT USED at the moment
        'SERVER_CLICK_LOOKBOOK',  // SF_EVENT_CLICK_LOOKBOOK Click on a product in a lookbook page
        'SERVER_CLICK_CUSTOMER_REQUEST',  // SF_EVENT_CLICK_CUSTOMER_REQUEST At the moment, we use the message code for customer service message

        // SIDEBAR / WIDGET ACTIONS
        'SIDEBAR_MAXIMIZE', // Maximize Widget
        'SIDEBAR_MINIMIZE', // Minimize Widget
        'SIDEBAR_HIDE', // Chose to not see widget anymore in session
        'SIDEBAR_FIND_STORE', // Click Location, and then CHOOSE location
        'SIDEBAR_FIND_REP', // Uses Find Stylist to open specific rep storefront
        'SIDEBAR_CHAT_REQUEST',
        'SIDEBAR_CHAT_ANSWER',
        'SIDEBAR_CHAT_END',
        'SIDEBAR_CHAT_MISSED',
        'SIDEBAR_APPOINTMENT',
        'SIDEBAR_PERSONAL_SHOPPER',
        'SIDEBAR_EMAIL_ME',
        'SIDEBAR_EMAIL_ME_MISSED_CHAT',

        // STOREFRONT ACTIONS
        'STOREFRONT_VIEW', // Combination of SF_EVENT[_NEW_USER_HIT + _RETURNING_USER_HIT + _USER_VISIT]
        'STOREFRONT_REDIRECT_TO_PRODUCT', // 'STOREFRONT_REDIRECT_TO_RETAILER' Not sure if these are differentiable
        'STOREFRONT_REPORT_CONCERN',
        'STOREFRONT_ABOUT_ME',
        'STOREFRONT_SUBSCRIBE',
        'STOREFRONT_SEARCH',
        'STOREFRONT_CHAT_REQUEST',
        'STOREFRONT_CHAT_ANSWER',
        'STOREFRONT_CHAT_END',
        'STOREFRONT_CHAT_MISSED',
        'STOREFRONT_APPOINTMENT',
        'STOREFRONT_PERSONAL_SHOPPER',
        'STOREFRONT_EMAIL_ME',
        'STOREFRONT_EMAIL_ME_MISSED_CHAT',

        // Additional View Events
        // When known that URL was coming from outbound, can add specificity (Email / Facebook / Twitter)
        'LOOKBOOK_VIEW',
        'LOOKBOOK_VIEW_EMAIL',
        'LOOKBOOK_VIEW_FACEBOOK',
        'LOOKBOOK_VIEW_TWITTER',
        'ASSET_VIEW',
        'ASSET_VIEW_EMAIL',
        'ASSET_VIEW_FACEBOOK',
        'ASSET_VIEW_TWITTER',
        'STOREFRONT_VIEW_EMAIL',
        'STOREFRONT_VIEW_FACEBOOK',
        'STOREFRONT_VIEW_TWITTER',
        'STOREFRONT_VIEW_LOOKBOOK',

        'SERVER_CLICK_TEXT_ASSET',      // When user clicks on an asset URL shared by text
        'SERVER_CLICK_TEXT_PRODUCT',    // When user clicks on a product URL shared by text
        'SERVER_CLICK_TEXT_STOREFRONT', // When user clicks on a storefront URL shared by text

        'SERVER_CLICK_SHARE_MENU', // When user clicks on a shared link through 3rd party app

        self::SF_EVENT_SOCIALSHOP_VIEW, // When user visit the socialshop page
        self::SF_EVENT_SOCIALSHOP_STOREFRONT_CLICK, // When user click storefront link on the socialshop page
        self::SF_EVENT_SOCIALSHOP_PRODUCT_CLICK,// When user click product link on the socialshop page

        // NEW SIDEBAR EVENT
        // ** WARNING ** Those are not events from sf_event_log but sf_sidebar_event_log(_yyyymmdd)
        // Not sure if we should move it out of the list.
        // TODO: Check if we should remove those events, because sidebar events are pushed in to sidebar event queue according \Salesfloor\API\Controllers\EventQueue::create,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_CLICK,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_VIEW,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_MINIMIZE,

        self::SF_EVENT_NEW_SIDEBAR_MOBILE_CLICK,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_VIEW,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_MAXIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_MINIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_DESKTOP_MAXIMIZE,
        self::SF_EVENT_NEW_SIDEBAR_MOBILE_TAGLINE_MINIMIZE,

        // Let's try not add SF_EVENT_CONTEXTUAL_WIDGET_* events here to see if anything break
        // if everything is fine we could even remove the above events SF_EVENT_NEW_SIDEBAR_*

        // ALWAYS APPEND TO THE BOTTOM
    ];


    /**
     * Construct the EventQueue Instance and initialize needed variables
     * @param Configs $configs Configs instance
     */
    public function __construct(MessageQueueInterface $queueClient, Configs $configs, $attributionService)
    {
        $this->env = $configs['env'];
        $this->queue = $queueClient;
        $this->queueId = $configs['queue.events.full_name'];
        $this->retailer = $configs['retailer.short_name'];
        $this->deadLetterQueueId = $configs['queue.events.dead_letter_name'];
        $this->attributionService = $attributionService;
    }

    /**
     * Destructor that can try to ensure that halfway read/handled
     *  messages are sent back into the events queue for reprocessing
     */
    public function __destruct()
    {
        if (!empty($this->lastQueueUrl) && !empty($this->lastReceiptHandle)) {
            $this->queue->changeMessageVisibility($this->lastQueueUrl, $this->lastReceiptHandle, $this->hideMessage);
        }
    }

    public function getQueueId()
    {
        return $this->queueId;
    }

    /**
     * Function will validate a requested polling length time (in seconds).
     * If it is valid (0-20) it will be returned, otherwise the class default is returned.
     * @param  integer $requestedTime Number of seconds you would like message polling to stay open
     * @return integer                Validated number of seconds that will be used in polling
     */
    public function getWaitTimeSeconds($requestedTime = null)
    {
        if (is_numeric($requestedTime) && $requestedTime >= self::MIN_WAIT && $requestedTime <= self::MAX_WAIT) {
            return $requestedTime;
        }
        return $this->waitTimeSeconds;
    }

    /**
     * Function pushes a given event into the AWS events queue
     * @param  array  $eventParams Array of details about the event
     *                             [
     *                                 'action' => 'SOME_ACTION_STRING', // REQUIRED
     *                                 'action_id' => 1234, // When linked directly to another table, i.e. transaction ID
     *                                 'fingerprint' => 12355848 // REQUIRED user fingerprint (integer result of hash -> hexdec)
     *                             ]
     * @return array               AWS Response from sendMessage()
     */
    public function push(array $eventParams)
    {
        $payload = $this->serializeSend($eventParams);
        $queueUrl = $this->getEventQueueUrl();
        return $this->queue->sendMessage($queueUrl, $payload);
    }

    /**
     * Function validates the received event, throwing exception if any required
     * fields are missing or contain invalid data (action received not in list)
     * Once validated, a newly structure event is created with generated timestamp,
     * and retailer / environment information. Once ready it is returned in json
     * @param  array  $params Original Event Array
     * @return string         JSON representation of the validated Event
     */
    public function serializeSend(array $params)
    {
        foreach (['action', 'fingerprint'] as $requiredField) {
            if (!isset($params[$requiredField])) {
                throw new \Exception("Received Invalid Event, must contain `$requiredField` field");
            }
        }
        $params['action'] = strtoupper($params['action']);
        // Build final structure for event
        $event = [];
        if (!in_array($params['action'], self::$actions)) {
            throw new \Exception("PUSH Event: Action {$params['action']} is not valid. Skipping");
        }
        // Check that action_id and fingerprint are numeric
        foreach (['action_id', 'fingerprint'] as $numericField) {
            if (isset($params[$numericField]) && !is_numeric($params[$numericField])) {
                throw new \Exception("PUSH Event: $numericField field must be numeric");
            }
        }
        foreach (['action', 'action_id', 'fingerprint'] as $expectedField) {
            $event[$expectedField] = (isset($params[$expectedField]) ? $params[$expectedField] : null);
        }
        // Add timestamp manually to represent time received by server
        $event['timestamp'] = gmdate('U');
        $event['env'] = $this->env;
        $event['retailer'] = $this->retailer;

        $strPayload = json_encode($event);
        if ($strPayload === null) {
            throw new \Exception("Unable to queue up event, failure to json encode " . var_export($event, true));
        }
        return $strPayload;
    }

    /**
     * Function pops a single message off the AWS queue. Queue polling
     * stays open either the default number of seconds (20) or the requested
     * number $waitTimeSeconds
     * @param  MysqlRepository $mysqlRepository Repository instance used for writing events
     * @param  integer         $waitTimeSeconds Override for the length of polling in seconds
     * @return bool|array                       Successfully saved event array or false in case of failure
     *                                                       true in case of event found but deleted
     */
    public function pop(MysqlRepository $mysqlRepository, $waitTimeSeconds = null)
    {
        $this->lastQueueUrl = $this->getEventQueueUrl();

        // In case of saving error, we change visibility timeout (hideMessage) to allow reprocessing of message later
        // If message has been read a max number of times (numberReceivesBeforeDeadLetter) and returns
        //     to the queue it will be pushed automatically by AWS to the "Dead Letter" queue
        $response = $this->queue->getMessages($this->lastQueueUrl, $this->getWaitTimeSeconds($waitTimeSeconds), $this->hideMessage);

        $messages = $response->getPath('Messages');
        // Expecting 1 message only
        // @TODO: This is old code that is supposed to work - we should double check that AWS doesn't return more than one message
        if (isset($messages[0])) {
            // Save the last message handle just in case the execution is suddenly terminated
            // Can be used by the __destruct method to push this message back into the queue
            $this->lastReceiptHandle = $messages[0]['ReceiptHandle'];
            try {
                $data = $this->unserializeSend($messages[0]['Body']);
            } catch (\Exception $unserializeException) {
                $this->queue->deleteMessage($this->lastQueueUrl, $this->lastReceiptHandle);
                // Message has been deleted, clear last receipt handle so we do not try to push it back to queue
                $this->lastReceiptHandle = null;
                // Returning true so that the Read Queue loop continues
                return true;
            }

            // Could throw an EventQueueDatabaseException which is not caught here
            // Should be caught by the calling script (usually ReadEventQueue.php cron)
            $ok = $this->saveEvent($mysqlRepository, $data);

            if ($ok) {
                // After processing emails correctly we delete the message (acknowledge)
                $this->queue->deleteMessage($this->lastQueueUrl, $this->lastReceiptHandle);
                $this->lastReceiptHandle = null;
                $this->postProcessEvent($data);
                return $data;
            }
            $this->lastReceiptHandle = null;

            // Also throw specific exception that should be caught by the calling function (ReadEventQueue.php)
            // It will know how to handle it (exit the script, restart by supervisor)
            throw new EventQueueDatabaseException('EventQueue: Possible Database Error', 500);
        }
        return false;
    }

    /**
     * Function un-encodes the event string (json) received from the queue polling
     * It validates that the string is in the expected structure and contains the
     * minimum required fields for saving a valid event
     * @param  string $json String JSON representation of the event in the queue
     * @return array        Valid event array decoded from json input
     */
    public function unserializeSend($json)
    {
        $payload = json_decode($json, true);
        if (!isset($payload)) {
            throw new \Exception("Bad payload; it's not JSON: $json");
        }
        foreach (['action', 'fingerprint', 'timestamp'] as $k) {
            if (!isset($payload[$k])) {
                throw new \Exception("Bad payload; it has no '$k' property: $json");
            }
        }
        if (!in_array($payload['action'], self::$actions)) {
            throw new \Exception("POP Event: Action {$payload['action']} is not valid. Skipping");
        }
        return $payload;
    }

    /**
     * Function fetches the needed queue URL and if not found it creates
     * the queue with the appropriate attributes
     * @return string  Event queue URL to be used to push / pop messages to / from
     */
    protected function getEventQueueUrl()
    {
        // Get the related "Events" queue URL. Create it if necessary
        $eventsQueueUrl = $this->queue->getQueueUrl($this->queueId);
        if (!$eventsQueueUrl) {
            $eventsQueueUrl = $this->queue->createQueue($this->queueId, $this->buildEventQueueAttributes());
        }
        return $eventsQueueUrl;
    }

    /**
     * Function returns the attributes array that is necessary to build the event queue
     * @return array Containing the attributes for the event queue
     */
    protected function buildEventQueueAttributes()
    {
        // Policy for the "Dead Letter" queue. Must specify: where to send "dead" messages
        // And after how many reads (receives) a message / letter can be deamed "dead"
        $redrivePolicy = [
            'deadLetterTargetArn' => $this->queue->getQueueArn($this->getDeadLetterQueueUrl()),
            'maxReceiveCount' => $this->numberReceivesBeforeDeadLetter
        ];
        return [
            'ReceiveMessageWaitTimeSeconds' => $this->waitTimeSeconds,
            'RedrivePolicy' => json_encode($redrivePolicy)
        ];
    }

    /**
     * Function fetches the needed dead letter queue URL and if not found
     * it creates the dead letter queue with the appropriate attributes.
     * @return string Dead Letter queue URL that failed messages fallback to
     */
    protected function getDeadLetterQueueUrl()
    {
        $deadLetterQueueUrl = $this->queue->getQueueUrl($this->deadLetterQueueId);
        if (!$deadLetterQueueUrl) {
            $deadLetterQueueUrl =  $this->queue->createQueue($this->deadLetterQueueId, $this->buildDeadLetterQueueAttributes());
        }
        return $deadLetterQueueUrl;
    }

    /**
     * Function returns the attributes array that is necessary to build the dead letter event queue
     * @return array Containing the attributes for the dead letterevent queue
     */
    protected function buildDeadLetterQueueAttributes()
    {
        return [
            'MessageRetentionPeriod' => ($this->deadLetterRetentionDays * 24 * 60 * 60), // Number of days a message is held in the queue
            'ReceiveMessageWaitTimeSeconds' => 20 // Max # Seconds that a getMessage request can "poll" / wait to get a result
        ];
    }

    /**
     * SF-17264 Split Dead Letter Queue
     * Function will reset the attributes for the Event Queue + Dead Letter Queue
     * Should be called after deployments to ensure that any changed settings such
     * as queue names / dead letter names / expiry times / etc... are synced
     * @return bool Based on Success / Failure
     */
    public function updateQueueSettings()
    {
        $updatedDeadLetter = false;
        try {
            // Update the settings for the Dead Letter Queue
            $this->queue->setQueueAttributes($this->getDeadLetterQueueUrl(), $this->buildDeadLetterQueueAttributes());
            $updatedDeadLetter = true;

            // Update the settings for the Event Queue
            $this->queue->setQueueAttributes($this->getEventQueueUrl(), $this->buildEventQueueAttributes());
        } catch (\Exception $updateSettingsException) {
            error_log('Error Updating ' . ($updatedDeadLetter ? 'Event' : 'Dead Letter') . ' Queue settings: ' . $updateSettingsException->getMessage());
            return false;
        }
        return true;
    }

    /**
     * Function saves an event (usually received from queue polling / pop) into
     * the database table "sf_event_log" using a DBAL insert
     * @param  MysqlRepository $mysqlRepository Repository instance used for writing events
     * @param  array           $event           Array representation of event to log
     * @return bool                             TRUE when successfully saved, FALSE on error
     */
    public function saveEvent(MysqlRepository $mysqlRepository, array $event)
    {
        $qb = $mysqlRepository->getQueryBuilder();

        $qb->insert(self::DB_TABLE)
            ->values([
                'action' => ':action',
                'action_id' => ':action_id',
                'fingerprint' => ':fingerprint',
                'timestamp' => ':timestamp'
            ])
            ->setParameters([
                'action' => $event['action'],
                'action_id' => $event['action_id'],
                'fingerprint' => $event['fingerprint'],
                'timestamp' => $event['timestamp']
            ]);

        try {
            $return = $qb->execute();
        } catch (\Exception $insertException) {
            error_log('Error Inserting Event: ' . json_encode($event) . ' - With Error: ' . $insertException->getMessage());
            return false;
        }
        return true;
    }

    /**
     * SF-16685 Attribution Correction: New events needed
     * Function appends event_source=$eventSource to all URLs that match $urlInLink
     * It takes into account whether or not the URL already has some query strings
     * or not and appends with ? or & first.
     * @param  string $urlInLink   String URL of the storefront to search and append to
     * @param  string $htmlContent Full string HTML content (possibly of an email or post to facebook ...)
     * @param  string $eventSource The source to mark the event_source as i.e. email facebook twitter
     * @return string              Full HTML content with event_source appended to $urlInLink URLs
     */
    public static function appendEventSourceToLinksInHtml($urlInLink, $htmlContent, $eventSource)
    {
        $eventSource = self::getValidatedEventSource($eventSource);
        if (empty($htmlContent) || empty($urlInLink) || empty($eventSource)) {
            return $htmlContent;
        }

        // SF-17056 Fix event source which wasn't being inserted into URLs
        // Remove the possible http(s):// from the URL
        // Ex: https://saks.dev.salesfloor.net/hello --> saks.dev.salesfloor.net/hello
        $urlInLink = preg_replace('/^(https?:)?\/\//', '', $urlInLink);

        // Protect special characters in URL [. /]
        // Ex: saks.dev.salesfloor.net/hello --> saks\.dev\.salesfloor\.net\/hello
        $urlInLink = preg_replace("/([\/.])/", '\\\$1', $urlInLink);

        // Append &event_source=email to all matching URLs WITH existing query strings
        $withQueryStringRegex = "/(['\"])(https?:)?(\/\/)?({$urlInLink}(\/\w+)*\?(\S*))(['\"])/";
        $htmlContent = preg_replace($withQueryStringRegex, "$1$2$3$4&event_source={$eventSource}$1", $htmlContent);

        // Append ?event_source=email to all matching URLs WITHOUT existing query strings
        $withoutQueryStringRegex = "/(['\"])(https?:)?(\/\/)?({$urlInLink}(\/\w+)*)(['\"])/";
        $htmlContent = preg_replace($withoutQueryStringRegex, "$1$2$3$4?event_source={$eventSource}$1", $htmlContent);

        return $htmlContent;
    }

    /**
     * Function will validate the received $eventSource
     * If valid, a lower case version of the $eventSource will be returned
     * If invalid, a False will be returned
     * @param  string      $eventSource Event source such as email facebook twitter
     * @return string|bool              Validated eventSource or false
     */
    public static function getValidatedEventSource($eventSource)
    {
        $validSources = ['email', 'facebook', 'twitter', 'lookbook'];
        $eventSource = strtolower($eventSource);
        if (in_array($eventSource, $validSources)) {
            return $eventSource;
        }
        return false;
    }

    /**
     * SF-16685 Attribution Correction: New events needed
     * Function appends event_source=$eventSource to given $url
     * It takes into account whether or not the URL already has some query strings
     * or not and appends with ? or & first.
     * @param  string $url         URL to have eventSource queryString appended to
     * @param  string $eventSource Event source such as email facebook twitter
     * @return string              URL with eventSource query string (unless invalid eventSource)
     */
    public static function appendEventSourceToUrl($url, $eventSource)
    {
        $eventSource = self::getValidatedEventSource($eventSource);
        if (empty($eventSource)) {
            return $url;
        }
        return $url . (strpos($url, '?') !== false ? '&' : '?') . 'event_source=' . $eventSource;
    }

    /**
     * Function will call any other services / functions that MAY act upon the results
     * of a just saved event.
     * Actions could include Processing Attribution when recording a transaction, updating
     * additional meta data tables when performing complex actions, etc...
     * @param  array  $savedEvent Data that was returned from unserializeSend and successfully saved
     *                                Minimum array structure ['action' => 'SOME_ACTION', 'fingerprint' => 1234, 'timestamp' => 5678]
     */
    public function postProcessEvent(array $savedEvent)
    {
        try {
            // SF-16685 Calculate Attribution if just saved event has action SERVER_RECORD_TRANSACTION
            $this->attributionService->calculateAttributionForSavedEvent($savedEvent);
        } catch (\Exception $postProcessingException) {
            error_log('ERROR: Post Processing Saved EventQueue Event - ' . $postProcessingException->getMessage());
        }
    }

    /**
     * Check if a given action is a SERVER_CLICK action.
     *
     * @param string $action
     * @param bool   $strict When false, it converts kebab-case to all caps snake_case and prefixes
     *                       the given $action with "SERVER_CLICK" when evaluating it.
     *
     * @return bool|string Returns the canonical SERVER_CLICK action key
     */
    public static function isServerClickAction($action, $strict = true)
    {
        if (strpos($action, self::SERVER_CLICK_PREFIX) === 0 && in_array($action, self::$actions)) {
            // perfect match
            return $action;
        }

        if ($strict) {
            // the evaluation ends here when it's a strict match case
            return false;
        }

        // basic kebab-case to all caps snake_case
        $action = trim($action);
        $action = strtoupper($action);
        $action = str_replace('-', '_', $action);

        if (strpos($action, self::SERVER_CLICK_PREFIX) === 0 && in_array($action, self::$actions)) {
            return $action;
        }

        // action doesn't have the correct prefix
        // concatenate it with the prefix and try a last shot
        $prefixedAction = self::SERVER_CLICK_PREFIX . $action;

        return in_array($prefixedAction, self::$actions)
            ? $prefixedAction
            : false;
    }
}
