<?php

namespace Salesfloor\Services\PushNotifications;

use Google\Auth\ApplicationDefaultCredentials;
use Google\Auth\Cache\MemoryCacheItemPool;
use Google\Auth\CredentialsLoader;
use Google\Auth\FetchAuthTokenCache;
use Guz<PERSON>Http\Pool;
use Guz<PERSON><PERSON>ttp\Psr7\Utils;
use Psr\Log\LoggerInterface;
use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\HandlerStack;
use G<PERSON><PERSON><PERSON><PERSON>ry\GuzzleRetryMiddleware;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Salesfloor\Services\Exceptions\PushNotificationException;
use Salesfloor\API\Managers\Devices as DeviceManager;
use Throwable;

class FCMBackend implements Backend
{
    /** @var int FCM Push notifications cannot send to more than 1000 Devices */
    const FCM_MATCH_BATCH_CHUNK = 1000;

    /** @var string small constant used to generate an endpoint id
     * with all informations that could be used for future migrations
     */
    const FCM_PREFIX = 'fcm';

    /** @var string SANDBOX Apple IOS Devices can either be an SANBOX devices.
     *
     * This value is found in the endpoint_id string stored (if it was AWS)
     * or from the PushNotificationConfig such as :
     * 'iOsApplicationArn'     => 'arn:aws:sns:us-west-1:935077261142:app/APNS_SANDBOX/com.salesfloor.salesfloor',
     *
     */
    const SANDBOX = 'SANDBOX';


    /** @var string FCM API v1:BatchImport Endpoint
     *  used to import Apple Devices into FCM
     */
    const FCM_V1_BATCHIMPORT = 'v1:batchImport';

    /** @var string FCM API inf Endpoint used */
    const FCM_INFO = 'info';

    const OAUTH2_SCOPES = [
        'https://www.googleapis.com/auth/firebase.messaging',
    ];

    const WEBPUSH_PRIORITY_HIGH = 'high';

    const ANDROID_PRIORITY_HIGH = 'high';
    const APPLE_PRIORITY_HIGH = '10';
    // ensures no latency and message is not queued for later delivery due to
    // device being initially unavailable. This means messages are not retried hence it is considered as realtime chat
    // where it is not possible for the user to act on the notification much later. e.g video chat.
    // @see https://firebase.google.com/docs/cloud-messaging/concept-options#ttl
    const APPLE_TTL = '0';
    // @see APPLE_TTL
    // Note, FCM will deregister device token in the event app is uninstalled and a message is pushed through.
    // @see https://firebase.google.com/docs/cloud-messaging/concept-options#ttl
    const ANDROID_TTL = '0s';

    const WEBPUSH_TTL_REALTIME = '0';
    const WEBPUSH_TTL_DEFAULT = '60';

    const ANDROID_PRIORITY_NORMAL = 'normal';

    const APPLE_INTERRUPTION_LEVEL_PASSIVE = 'passive';
    const APPLE_INTERRUPTION_LEVEL_ACTIVE = 'active';
    const APPLE_INTERRUPTION_LEVEL_TIME_SENSITIVE = 'time-sensitive';
    const APPLE_INTERRUPTION_LEVEL_CRITICAL = 'critical';

    // This is used within the payload sent to FCM
    const PUSH_NOTIFICATION_ANDROID = 'android';
    const PUSH_NOTIFICATION_APPLE = 'apns';

    const PUSH_NOTIFICATION_WEBPUSH = 'webpush';

    const FCM_DEVICE_NOT_FOUND_ERROR = 404;
    const FCM_DEVICE_INVALID_ARGUMENT_ERROR = 400;

    const GUZZLE_POOL_CONCURRENCY = 5;

    /** @var Client $client */
    private $client;

    /** @var string $googleIidEndpoint The google API Url */
    private $googleIidEndpoint;

    /** @var string $key Google FCM Sever Key to access the 3rdParty API */
    private $key;

    /** @var string $fcmVersion String something like 'v1' this could allow
     * future migrations if needed from FCM v1 to FCM v2 if we choose to change
     * the way we structure the sf_devices table / endpoint_id
     */
    private $fcmVersion;

    /** @var string $projectId the Google ProjectId that relates to the Google FCM $key */
    private $projectId;

    /** @var SnsConfig $snsConfig The PushNotification Config sharing configurations for AWS and GCP */
    private $snsConfig;

    /**
     * @var LoggerInterface $logger
     */
    private $logger;

    /**
     * @var DeviceManager
     */
    private $deviceManager;

    /*
     * This is a "tmp" service account under the Firebase project (radiant-fire-9638)
     * so batchImport endpoint can continue to work.
     *
     * @var array|null
     */
    private ?array $firebaseServiceAccount = null;

    private MemoryCacheItemPool $memoryCache;

    /**
     * FCMBackend constructor.
     *
     * @param $projectId
     * @param string $key
     * @param bool $useKeySecretFile it is actually a true/false string
     * @param string $fcmVersion version to store in endpoint_id for furture upgrades
     * @param string $googleIidEndpoint
     * @param SnsConfig $snsConfig
     * @param LoggerInterface|null $logger
     * @throws \Exception
     */
    public function __construct(
        string $projectId,
        string $key,
        bool $useKeySecretFile,
        string $fcmVersion,
        string $googleIidEndpoint,
        SnsConfig $snsConfig,
        LoggerInterface $logger = null,
        DeviceManager $deviceManager,
        string $firebaseServiceAccount = null,
    ) {
        // To ensure production Server Key are not leaked in the Git repository
        // We use an Kubernetes Secret file.
        if ($useKeySecretFile === true) {
            if (!file_exists($key)) {
                throw new PushNotificationException("Invalid Docker Configuration: Requested file '$key' but file did not exists.");
            }
            $this->key = file_get_contents($key);
        } else {
            $this->key = $key;
        }

        $this->projectId = $projectId;
        $this->fcmVersion = $fcmVersion;

        if (empty($this->key)) {
            throw new PushNotificationException("Error: Empty Google FCM Key.");
        }
        $this->googleIidEndpoint = rtrim($googleIidEndpoint, '/') . '/';

        $this->snsConfig = $snsConfig;

        $this->logger = $logger;

        $this->deviceManager = $deviceManager;

        if (!empty($firebaseServiceAccount)) {
            $json = base64_decode($firebaseServiceAccount);
            if (!empty($json)) {
                // In we don't have a proper structure (array); just ignore it.
                $array = json_decode($json, true);
                if (is_array($array)) {
                    $this->firebaseServiceAccount = $array;
                }
            }
        }

        $this->memoryCache = new MemoryCacheItemPool();
    }

    /**
     * Get an instance of Guzzle with retry handler
     */
    private function getClient()
    {
        if (!isset($this->client)) {
            $stack = HandlerStack::create();

            /**
             * Listen for retry events
             *
             * @param int                    $attemptNumber  How many attempts have been tried for this particular request
             * @param float                  $delay          How long the client will wait before retrying the request
             * @param RequestInterface       $request        Request
             * @param array                  $options        Guzzle request options
             * @param ResponseInterface|null $response       Response (or NULL if response not sent; e.g. connect timeout)
             */
            $listener = function (
                int $attemptNumber,
                float $delay,
                RequestInterface &$request,
                array &$options,
                ?ResponseInterface $response,
                ?Throwable $exception
            ) {
                $this->logger->error(
                    sprintf(
                        "Retrying request to %s.  Server responded with %s.  Will wait %s seconds.  This is attempt #%s. Response [%s]. The error was %s",
                        $request->getUri()->getPath(),
                        $response?->getStatusCode(),
                        number_format($delay, 2),
                        $attemptNumber,
                        $response?->getBody()?->getContents(),
                        $exception?->getMessage()
                    )
                );

                // This is to 'support' Microsoft Edge (webpush only) that doesn't seem to accept "0" for "realtime" behavior.
                // Let's change the TTL to higher number, since this fixes the issue.

                if (!empty($response) && $response->getStatusCode() === 400) {
                    $body = $request->getBody();
                    $body->rewind();
                    $content = json_decode($body->getContents(), true);

                    if (
                        !empty($content['message'][self::PUSH_NOTIFICATION_WEBPUSH])
                    ) {
                        $currentTTL = (int) $content['message'][self::PUSH_NOTIFICATION_WEBPUSH]['headers']['TTL'];
                        // Those new TTL are in theory, but by setting it to 1 on the first retry is enough. Rest are "not important".
                        // 0 (failed on Edge only) , 1 (Works), 4, 9, 16, 25 seconds ; based on the current algo.
                        $content['message'][self::PUSH_NOTIFICATION_WEBPUSH]['headers']['TTL'] = strval(($currentTTL + $attemptNumber) * $attemptNumber);

                        // withBody() create new request
                        $request = $request->withBody(Utils::streamFor(json_encode($content)));
                    }
                }
            };

            // See FCM Error codes here
            // https://firebase.google.com/docs/reference/fcm/rest/v1/ErrorCode
            //
            // 400 -> Invalid parameters / invalid registration, invalid package name, message too big etc...
            // 404 -> App instance was unregistered from FCM.
            // 429 -> QUOTA_EXCEEDED
            // 503 -> Server overloaded
            // 500 -> Internal error
            // 401 -> THIRD_PARTY_AUTH_ERROR - Check the validity of your development and production credentials
            //
            $stack->push(GuzzleRetryMiddleware::factory([
                    // Standard Guzzle options
                    'connect_timeout' => 10.0,
                    'retry_enabled' => true,
                    'max_retry_attempts' => 5,
                    'retry_only_if_retry_after_header' => false,
                    'retry_on_status' => [429, 503, 500, 400],
                    'on_retry_callback'  => $listener
            ]));
            $this->client = new Client(['handler' => $stack]);
        }
        return $this->client;
    }


    /**
     * Register the DeviceToken. in the FCM Backend.
     * For Android, this simply returns the custom format endpoint
     * For iOS (APNS) it will call the FCM Import API
     *    and return the new custom formatted Endpoint
     *
     * Custom Format Endpoint: fcm:{deviceclass}:token
     *
     * @param string $appId The application id received (sf-app-id header)
     * @param string $deviceClass the device class id
     * @param string $deviceToken the received device token from the App
     * @return string the custom formatted endpoint_id
     * @throws \Exception
     */
    public function registerDevice($appId, $deviceClass, $deviceToken)
    {
        $fcmTempDeviceToken = $deviceToken;

        $appArn = $this->snsConfig->getApplicationArn($appId, $deviceClass);
        $sandboxDevice = (strpos($appArn, Service::APNS_SANDBOX) !== false);

        // Perform APNS import deviceToken from Apple APN server to FCM
        if ($this->snsConfig->isApnsDevice($deviceClass)) {
            // Need to create FCM Token
            $fcmTempDeviceToken = $this->importAPNToken($deviceToken, $appId, $sandboxDevice);

            if (empty($fcmTempDeviceToken)) {
                throw new PushNotificationException("Could not import APN Device for '$deviceToken'. Token import failed.");
            }
        }

        // Return an custom endpoint_id that have a pattern of
        //
        // fcm:{deviceclass}:token
        //  - The token for Android is identical to the token field.
        //  - The token for iOS is the FCM imported token.
        //
        $fcmDeviceToken = $this->generateFcmEndpointId($fcmTempDeviceToken, $deviceClass, $appId, $sandboxDevice);

        return $fcmDeviceToken;
    }

    /**
     * Generate a custom endpoint so we can send push notification
     * with proper data content as part of the publish function.

     * @param string $deviceToken the FCM Device Token
     * @param string $deviceClass Android, Appple, etc..
     *
     * @return string and custom endpoint_id including the device token
     * @throws \Exception
     */
    public function generateFcmEndpointId($deviceToken, $deviceClass, $mobileApp, $sandbox)
    {
        switch ($deviceClass) {
            case Service::ANDROID:
                $deviceClassStr = Service::ANDROID;
                break;
            case Service::APPLE:
                $deviceClassStr = Service::APPLE;
                break;
            case Service::WEB:
                $deviceClassStr = Service::WEB;
                break;
            default:
                throw new PushNotificationException("Invalid device class '$deviceClass'");
        }
        $sandboxStrVal = $sandbox === true ? "1" : "0";

        //Warning: $deviceClassStr must be one of the
        // Service::ANDROID or Service::APPLE
        // As it is being used in SnsConfig reverseDeviceClassFromEndpoint
        if (empty($deviceToken)) {
            throw new PushNotificationException("Error: trying to create using an empty device token");
        }
        return implode(':', [Service::FCM, $deviceClassStr, $this->fcmVersion, $sandboxStrVal, "$mobileApp/$deviceToken"]);
    }


    /**
     * Send api call to Google FCM
     *
     * Returns FCM Token to send back to Mobile
     *
     * @param string $deviceToken the received device token from the App
     * @param string $appId The application id received (sf-app-id header)
     * @return string
     */
    public function sendToFcmApi(string $url, string $postData = ''): array
    {
        try {
            // Set Http Headers / Content-Length
            // Ensure no newline characters else google with complain
            // with content-lenght missing
            // Set Http Headers / Content-Length
            // Ensure no newline characters else google with complain
            // with content-length missing
            $headers = [
              'Authorization' => 'Bearer ' . $this->getAccessToken(),
              'access_token_auth' => "true",
              'project_id' => $this->projectId,
              'Content-Type' => 'application/json',
              'Content-length' => strlen($postData),
            ];
            $response = $this->getClient()->post($url, [
                'body' => $postData,
                'headers' => $headers
            ]);
            // Result nust be the raw http body response content.
            return [
                'result' => $response->getBody(),
                'code' => $response->getStatusCode(),
            ];
        } catch (RequestException $e) {
            $this->logger->error("PushNotification: Failed to send request " . $e->getMessage());
            return [];
        }
    }
    /**
     * Import a single APNS Token into FCM
     *
     * Returns FCM Token to send back to Mobile
     *
     * @param string $deviceToken the received device token from the App
     * @param string $appId The application id received (sf-app-id header)
     * @return string
     */
    public function importAPNToken(string $deviceToken, string $appId, bool $sandbox = false): string
    {
        //
        // Import Device Token
        //

        /*
         *
         * APN Migration Example:
            curl -X POST -H "Authorization: key=YOUR_FCM_SERVER_KEY" -H "Content-Type: application/json" -d '{
                "application": "com.nraboy.nativescriptexample",
                "sandbox":false,
                "apns_tokens":[
                    "368dde283db539abc4a6419b1795b6131194703b816e4f624ffa12",
                    "76b39c2b2ceaadee8400b8868c2f45325ab9831c1998ed70859d86"
                ]
            }' "https://iid.googleapis.com/iid/v1:batchImport"
        */

        $data = [];
        $data['application'] = $appId;
        $data['sandbox'] = boolval($sandbox);
        $data['apns_tokens'][] = $deviceToken;
        $postData = json_encode($data);

        $this->logger->debug('Importing APN deviceToken "' . $deviceToken . '" using data : ' . print_r($postData, true));
        $resArray = $this->sendToFcmApi($this->googleIidEndpoint . self::FCM_V1_BATCHIMPORT, json_encode($data));

        // Example of output
        // SF.DEBUG: Importing APN deviceToken "baf7522130a72a57fd5357c4da6d221a8d2435ed4ec23dae3f8cb648a5dcf9e6"
        // using data : {"application":"com.salesfloor.salesfloor","sandbox":true,"apns_tokens":["baf7522130a72a57fd5357c4da6d221a8d2435ed4ec23dae3f8cb648a5dcf9e6"]} [] []
        if ($resArray['code'] == 401) {
            $this->logger->error('Could not perform APN Import due to invalid/misconfigured Server FCM Server Key. Error: ' . print_r($resArray['result'], true));
            return '';
        }
        if ($resArray['code'] >= 404) {
            $this->logger->error('Could not contact 3rd party server.');
            return '';
        }

        $jsonResults = json_decode($resArray['result'], true);
        $importResponse = $jsonResults['results'][0];
        if ($importResponse['status'] === 'OK') {
            return $importResponse['registration_token'];
        } else {
            // Log import error, we should check logs to ensure we do not have too much errors?
            // One potential error is that the device is deactivated ?
            $this->logger->error('Error performing device APN Import for Token "' . $deviceToken . '" with error:' . print_r($importResponse, true));
        }
        return '';
    }

    /**
     * Perform migration from a different push notification backend.
     * Migration would be only for existing devices. New device registration
     * will have proper deviceToken as part of their endpoint_id
     *
     * Step 1: Extract APNS vs GCM from endpointId
     *   Result extracted would be something like com.salesfloor.enterprise
     * Step 2: Import the device into FCM
     * Step 3: Generate an EndpointId that woud allow upgrades / re-import
     *         if needed.
     *
     * @param string $deviceToken The device token received by the app.
     * @param string $endpointId The endpoint id
     * @return string
     */
    public function migrate($deviceToken, $endpointId): string
    {
        // Example of sNS Enpoint data
        // arn:aws:sns:us-west-1:935077261142:endpoint/GCM/Salesfloor-GCM/b2c91848-b6f3-3768-90bd-8b4ccc22eb04
        // arn:aws:sns:us-west-1:935077261142:endpoint/APNS/com.salesfloor.enterprise/cf1ef54a-abb5-3161-a3f6-faa02aff7163
        // arn:aws:sns:us-west-1:935077261142:endpoint/APNS/com.salesfloor.enterprise/cf1ef54a-abb5-3161-a3f6-faa02aff7163
        // arn:aws:sns:us-west-1:935077261142:endpoint/GCM/Salesfloor-GCM/52ce72ab-b9b1-3a15-80d5-69ca70907887

        // Check if we already have migrated it or created it properly.
        if (preg_match('/^(fcm:.*)/', $endpointId, $matches)) {
            return $endpointId;
        }

        // Set new endpointId to current endpoint (ie for Android Devices)
        $newDeviceToken = $deviceToken;

        $deviceClass = $this->snsConfig->reverseDeviceClassFromEndpoint($endpointId);
        $appId = $this->snsConfig->reverseAppIdFromEndpoint($endpointId);
        $sandboxDevice = $this->snsConfig->reverseAppSanboxFromEndpoint($endpointId);
        // Check if this is an APNS (iOS) Device, if so we need to import the device in FCM
        // This will allow us to target that FCM Token to send push notifications.
        if ($this->snsConfig->isApnsDevice($deviceClass)) {
            // For APNS We have to import into FCM to get a proper token
            // We have to import using the right mobile app bundleId ie:
            //
            // exemple: com.salesfloor.salesfloor, or com.salesfloor.enterprise, ...
            $newDeviceToken = $this->importAPNToken(
                $deviceToken,
                $appId,
                $sandboxDevice
            );
        }
        if (empty($newDeviceToken)) {
            $this->logger->error('Error: Could not import endpoint "' . $deviceToken . '"');
            // Log the error but lets keep processing using the old endpointId.
            // Though this endpoint will not be receiving the notification.
            return $endpointId;
        }
        return $this->generateFcmEndpointId($newDeviceToken, $deviceClass, $appId, $sandboxDevice);
    }

    /**
     * Check if a DeviceToken is still registered
     *
     * Returns false if the endpoint is disabled or does not exist.
     *
     * @param string $endpointId Device Token from Mobile used to Publish via FCM
     * @return bool
     * @throws \Exception
     */
    public function checkDeviceRegistration($endpointId)
    {
        if ($this->needMigration($endpointId)) {
            // "Invalid endpointId. This endpoint require migration. EndpointId: '$endpointId'");
            // Do not throw Exception here, registerNewDevice would fail
            // lets assume this is going to be a new device
            return false;
        }

        // We removed iid google api calls as its add latency, plus the data is not up-to-date.
        // That extra check was made.
        return true;
    }

    /**
     * This is not needed as we use FCM Directly. A new devicetoken consist of
     * a new FCM DeviceToken
     *
     * @param $endpoint_id the endpoint id
     * @param $endpoint the endpointObject
     * @param $deviceToken the deviceToken
     *
     * @return string the same endpointId, or a newer one.
     */
    public function refreshEndpointToken($endpointId, $endpoint, $deviceToken)
    {
        $deviceClass = $this->snsConfig->reverseDeviceClassFromEndpoint($endpointId);
        $appId = $this->snsConfig->reverseAppIdFromEndpoint($endpointId);
        $sandboxDevice = $this->snsConfig->reverseAppSanboxFromEndpoint($endpointId);

        // Check if this is an APNS (iOS) Device, if so we need to import the device in FCM
        // This will allow us to target that FCM Token to send push notifications.
        if ($this->snsConfig->isApnsDevice($deviceClass)) {
            // For APNS We have to import into FCM to get a proper token
            // We have to import using the right mobile app bundleId ie:
            //
            // exemple: com.salesfloor.salesfloor, or com.salesfloor.enterprise, ...
            $newDeviceToken = $this->importAPNToken(
                $deviceToken,
                $appId,
                $sandboxDevice
            );
            if (empty($newDeviceToken)) {
                // Returning the endpointId is critical but we faialed to import it
                // returning old endpointId for now...
                return $endpointId;
            }
            return $this->generateFcmEndpointId($newDeviceToken, $deviceClass, $appId, $sandboxDevice);
        }
        return $endpointId;
    }

    /**
     * Send the Push notification to the various devices (endpoints)
     *
     * @param array $payload The generated multi device response from Service getPayload
     * @param array $endpoints The array of objects generated by Service prepareEndpoints
     * @throws \Exception
     */
    public function publish(array $payload, array $endpoints)
    {
        // Create the Message Object
        $fcmAccumulator = [];
        $this->createPublishObject("generic", $payload, $endpoints, $fcmAccumulator);
        [$valid, $expired] = $this->publishBatches($fcmAccumulator);

        $this->updateValidatedAndCleanExpired($valid, $expired);
    }

    /**
     * Create a Push notification to the various devices (endpoints)
     *
     * @param array $payload The generated multi device response from Service getPayload
     * @param array $endpoints The array of objects generated by Service prepareEndpoints
     * @throws \Exception
     */
    public function createPublishObject($locale, array $payload, array $endpoints, array &$accumulator)
    {
        // There should be one or more DeviceClass ie:
        // Android or iOS (APNS)
        // The message structure for APNS and Android are different.
        foreach ($this->getEndpointsByDeviceClass($endpoints) as $deviceClass => $deviceClassEndpoints) {
            foreach ($deviceClassEndpoints as $endpoint) {
                $badge = $endpoint['badge'];

                // Create a hash map for grouping messages
                $key = implode('_', [$locale, $badge, $endpoint['store']]);
                if (!isset($accumulator[$key])) {
                    $accumulator[$key] = [
                        'messageMultiDevices' => $this->snsConfig->serializePayload($this->snsConfig->appendBadgeNumber($payload, $badge))
                    ];
                }

                $messageMultiDevices = $accumulator[$key]['messageMultiDevices'];

                // See doc, regarding payload structure
                // https://developer.apple.com/documentation/usernotifications/generating-a-remote-notification
                // https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

                // Group identical messages. Messages are identical if they target the same store, and have the same locale and badge value.
                if ($deviceClass === Service::APPLE) {
                    if (!isset($accumulator[$key]['apns'])) {
                        $accumulator[$key]['apns'] = [
                            'data' => $messageMultiDevices['APNS']['aps'],
                            'payload' => [
                                'headers' => [
                                    'apns-priority' => self::APPLE_PRIORITY_HIGH,
                                ],
                                'payload' => [
                                    'aps' => [
                                        'alert' => [
                                            'title' => $messageMultiDevices['APNS']['aps']['title'] ?? 'Salesfloor',
                                            'body' => $messageMultiDevices['APNS']['aps']['message'] ?? '',
                                        ],
                                        'badge' => ((int)$messageMultiDevices['APNS']['aps']['badge']) ?? null,
                                        'sound' => $messageMultiDevices['APNS']['aps']['sound'],
                                        'interruption-level' => self::APPLE_INTERRUPTION_LEVEL_TIME_SENSITIVE,
                                    ],
                                ],
                            ],
                            'endpoints' => []
                        ];
                    }
                    if (isset($messageMultiDevices['APNS']['options'])) {
                        $option = $messageMultiDevices['APNS']['options'];
                        if (isset($option['realtime']) && $option['realtime']) {
                            $accumulator[$key]['apns']['payload']['headers']['apns-expiration'] = self::APPLE_TTL;
                        }
                    }
                    // Add the endpoint to the APNS(iOS) list of endpoints
                    $accumulator[$key]['apns']['endpoints'][] = $endpoint;
                } elseif ($deviceClass === Service::ANDROID) {
                    if (!isset($accumulator[$key]['android'])) {
                        $accumulator[$key]['android'] = [
                            'data' => $messageMultiDevices['GCM']['data'],
                            'payload' => [
                                'priority' => self::ANDROID_PRIORITY_HIGH, // Other possible value is "NORMAL"
                                'notification' => [
                                    'notification_count' => ((int)$messageMultiDevices['GCM']['data']['badge']) ?? null,
                                    'title' => $messageMultiDevices['GCM']['data']['title'] ?? 'Salesfloor',
                                    'body' => $messageMultiDevices['GCM']['data']['message'] ?? '',
                                    // Otherwise I get a white box, probably because the launcher has color/ background issue
                                    'icon' => 'ic_notify',
                                    'sound' => $messageMultiDevices['GCM']['data']['soundname'],
                                    // This is mandatory otherwise, background push notification isn't triggered
                                    // https://github.com/havesource/cordova-plugin-push/issues/266
                                    'click_action' =>  'com.adobe.phonegap.push.background.MESSAGING_EVENT',
                                    'channel_id' => $messageMultiDevices['GCM']['data']['android_channel_id'] ?? null,
                                ],
                            ],
                            'endpoints' => []
                        ];
                    }
                    if (isset($messageMultiDevices['GCM']['options'])) {
                        $option = $messageMultiDevices['GCM']['options'];
                        if (isset($option['realtime']) && $option['realtime']) {
                            $accumulator[$key]['android']['payload']['ttl'] = self::ANDROID_TTL;
                        }
                    }
                    // Add the endpoint to the android list of endpoints
                    $accumulator[$key]['android']['endpoints'][] = $endpoint;
                } elseif ($deviceClass === Service::WEB) {
                    if (!isset($accumulator[$key][Service::WEB])) {
                        $accumulator[$key][Service::WEB] = [
                            'data' => $messageMultiDevices[Service::WEBPUSH]['data'],
                            'payload' => [
                                'headers' => [
                                    'TTL' => self::WEBPUSH_TTL_DEFAULT,
                                    'Urgency' => self::WEBPUSH_PRIORITY_HIGH, // Could be normal/low/very-low
                                ],
                                // Sound is passed via data
                                'data' => $messageMultiDevices[Service::WEBPUSH]['data'],
                            ]
                        ];
                    }

                    // This is used for chat notification
                    if (!empty($messageMultiDevices[Service::WEBPUSH]['options']['realtime'])) {
                        $accumulator[$key][Service::WEB]['payload']['headers']['TTL'] = self::WEBPUSH_TTL_REALTIME;
                    }

                    // Add the endpoint to the android list of endpoints
                    $accumulator[$key][Service::WEB]['endpoints'][] = $endpoint;
                } else {
                    $this->logger->critical(
                        sprintf(
                            "deviceclass [%s] is not supported. This should not happen",
                            $deviceClass
                        )
                    );
                }
            }
        }
    }


    /**
     * Support message sent by batches
     *
     * @return bool
     */
    public function supportBatches(): bool
    {
        return true;
    }

    /**
     * Send the Push notification by batches via FCM.
     *
     * @param array &$messagesByBadges An array by locale / badge and endpoints by ios/android
     * @return array [validatedTokens, expiredTokens]
     */
    public function publishBatches(array &$messagesByBadges): array
    {
        /*
         * $endpoint['device_token'] = $backendDeviceIdentifier;
         * $messagesByBadges = [
         *    'fr_X' => [
         *          'messageMultiDevices' => $this->snsConfig->serializePayload(): array
         *          'apns' => [
         *               'message' => $message,
         *               # see Salesfloor\Services\PushNotifications\Services->prepareEndpoints
         *               'endpoints'  => array(  endpoints )
         *          ],
         *          'android' => [
         *               'message' => $message,
         *               # see Salesfloor\Services\PushNotifications\Services->prepareEndpoints
         *               'endpoints'  => array(  endpoints )
         *          ],
         *     ],
         *    'en_X' => [
         *       ...
         *    ]
         */
        $requests = function (&$arrayBadges) {
            foreach ($arrayBadges as $messagesByBadgesAndDeviceTypes) {
                // TODO: Why we would split apns/android ?!? Afaik, FCM support sending to both at the same time.
                $apns = $messagesByBadgesAndDeviceTypes['apns'] ?? null;
                if ($apns !== null && !empty($apns['endpoints'])) {
                    foreach ($this->sendPushNotification(self::PUSH_NOTIFICATION_APPLE, $apns) as $key => $req) {
                        yield $key => $req;
                    }
                }
                $android = $messagesByBadgesAndDeviceTypes['android'] ?? null;
                if ($android !== null && !empty($android['endpoints'])) {
                    foreach ($this->sendPushNotification(self::PUSH_NOTIFICATION_ANDROID, $android) as $key => $req) {
                        yield $key => $req;
                    }
                }
                $android = $messagesByBadgesAndDeviceTypes['web'] ?? null;
                if ($android !== null && !empty($android['endpoints'])) {
                    foreach ($this->sendPushNotification(self::PUSH_NOTIFICATION_WEBPUSH, $android) as $key => $req) {
                        yield $key => $req;
                    }
                }
            }
        };

        $validatedDeviceTokens = $expiredDeviceTokens = [];
        $pool = new Pool($this->getClient(), $requests($messagesByBadges), [
            'concurrency' => self::GUZZLE_POOL_CONCURRENCY,
            'fulfilled' => function (Response $response, $index) use (&$validatedDeviceTokens) {

                $body = $response->getBody()->getContents();
                $validatedDeviceTokens[] = $index;

                $this->logger->debug(
                    sprintf(
                        "Success sending push notification. Index: [%s] Result: [%s]",
                        $index,
                        $body,
                    )
                );
            },
            'rejected' => function (mixed $reason, $index) use (&$expiredDeviceTokens) {
                // $reason could be anything, good practice to anticipate different type.
                $message = substr(json_encode($reason), 0, 2000);
                $code = $reason?->getCode() ?? 0;

                $isInvalidToken = function () use ($code) {
                    return in_array(
                        $code,
                        [
                            self::FCM_DEVICE_NOT_FOUND_ERROR,
                            self::FCM_DEVICE_INVALID_ARGUMENT_ERROR
                        ],
                        true
                    );
                };

                if ($reason instanceof RequestException) {
                    $message = $reason->getResponse()->getBody()->getContents();

                    if ($isInvalidToken()) {
                        $expiredDeviceTokens[] = $index;
                    }
                } elseif ($reason instanceof Throwable) {
                    $message = $reason->getMessage();
                }

                $this->logger->error(
                    sprintf(
                        "Error sending push notifications. Index: [%s] Response Code: [%s] Error: [%s]",
                        $index,
                        $code,
                        $message,
                    )
                );

                // In the DB currently, we have mostly invalid/old token. If we crash on all of them, it makes
                // everything super slow (queue-wise)
                if (!$isInvalidToken()) {
                    // Don't silence exception, This is called after the X retry from the middleware.
                    // This will make the message go back to the queue.
                    if ($reason instanceof Throwable) {
                        throw $reason;
                    } else {
                        throw new \Exception($message);
                    }
                }
            },
        ]);

        // Initiate the transfers and create a promise
        $promise = $pool->promise();

        // Force the pool of requests to complete.
        $promise->wait();

        return [$validatedDeviceTokens, $expiredDeviceTokens];
    }

    /**
     * Send push notifications to Google Firebasse Cloud Messaging
     *
     * @param string $source
     * @param array $payload
     *
     * @return array
     *
     * @throws PushNotificationException
     */
    private function sendPushNotification(string $source, array $payload): array
    {
        $req = [];

        // FCM v1 cannot send to more than 1 endpoint at the time.
        foreach ($payload['endpoints'] as $endpoint) {
            if (empty($endpoint['device_token'])) {
                continue;
            }

            $fields = [
                'message' => [
                    'data' => $payload['data'],
                    $source => $payload['payload'],
                    'token' => $endpoint['device_token'],
                ],
            ];

            $postData = json_encode($fields);
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->getAccessToken(),
            ];

            $key = $endpoint['token'];
            $req[$key] = new Request(
                'POST',
                "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send",
                $headers,
                $postData
            );
        }
        return $req;
    }

    /**
     * Check if we have to perform Migration
     *
     * @param string $endpointId The endpoint id
     * @return bool
     */
    public function needMigration($endpointId)
    {
        // Future usage, use multiple FCM Versions and Token
        // ie we want to upgrade to a newer TOKEN we might need to refresh
        // example, going from Certificate APNs would require a new ersion
        // we added in the entpointId the versionId of the token
        // per example fcm:apple:v1:com.salesfloor.enterprise/token
        if (preg_match('/fcm:(.*)/', $endpointId, $matches)) {
            return false;
        }

        return true;
    }

    /**
     * Extract the Devices frmo the endpoint and return an object
     * of each devicesClasses with an array of their endpoints.
     * This allow to send messages to all Android devices
     * and all iOS devices aftewards since the pushNotification message
     * format differs
     *
     * @param array $endpoints The array of objects generated by Service prepareEndpoints
     * @return array
     */
    private function getEndpointsByDeviceClass(array $endpoints)
    {
        $ret = [];

        foreach ($endpoints as $endpoint) {
            try {
                $deviceClass = $this->getEndpointDeviceClass($endpoint['endpoint_id']);
                if (!isset($ret[$deviceClass])) {
                    $ret[$deviceClass] = [];
                }

                $ret[$deviceClass][] = $endpoint;
            } catch (\Exception $e) {
                // This device is ignored it has not been migrated yet?
                $this->logger->error('Error: Invalid Endpoint ' . $e->getMessage());
            }
        }
        return $ret;
    }

    /**
     * Extract endpoint device class from endpointId
     * @param $endpointId
     * @return mixed
     * @throws \Exception
     */
    private function getEndpointDeviceClass($endpointId)
    {
        // Other backend endpoint would throw exception.
        if (!preg_match('/fcm:([^:]+):/', $endpointId, $matches)) {
            throw new \Exception("Malformed endpoint id '$endpointId'");
        }
        return $matches[1];
    }

    /**
     * Return the device endpoint from the endpoint array
     *
     * @param array $endpoint The array which was generated by Service prepareEndpoints
     * @throws \Exception
     */
    public function getDeviceEndpoint($endpoint)
    {
        if (isset($endpoint['device_token'])) {
            return $endpoint['device_token'];
        }

        if (!isset($endpoint['endpoint_id'])) {
            throw new \Exception("Invalid endpointObject : " . print_r($endpoint, true));
        }

        $endpointId = $endpoint['endpoint_id'];

        // example of content for $endpointId
        // fcm:apple:0:com.salesfloor.enterprise/ejc_cZLOvkY:APA91bEWt

        if (preg_match('/fcm:.*:' . $this->fcmVersion . ':[0-9]+:.*\/(.*)$/', $endpointId, $matches)) {
            return $matches[1];
        } else {
            $this->logger->error('Endpoint ID is missing FCM Migration token  :"' . $endpointId . '"');
        }
        return;
    }

    /**
     * Checks if an endpoint is enabled.
     *
     * Returns false if the endpoint is disabled or does not exist.
     *
     * @param string $endpointArn The AWS ARN to check for validity
     * @return bool
     */
    public function isValidEndpoint($endpoint)
    {
        return $this->checkDeviceRegistration($endpoint);
    }

    /**
     * New v1 is using short-lived tokens instead of
     * FCM keys. The ENV variable GOOGLE_APPLICATION_CREDENTIALS must
     * be present inside the running environment
     * Please note AccessToken and DeviceToken are not the same thing
     *
     * @return string
     */
    public function getAccessToken(): string
    {
        // Let's use a custom ServiceAccount and not ADC because of cross project it doesn't work for
        // batchImport API endpoint (https://iid.googleapis.com/iid/v1:batchImport)

        $memoryCache = $this->memoryCache;

        if (!empty($this->firebaseServiceAccount)) {
            $creds = CredentialsLoader::makeCredentials(
                self::OAUTH2_SCOPES,
                $this->firebaseServiceAccount,
            );

            $credentials = new FetchAuthTokenCache($creds, [], $memoryCache);
        } else {
            $credentials = ApplicationDefaultCredentials::getCredentials(
                self::OAUTH2_SCOPES,
                null,
                [],
                $memoryCache,
            );
        }

        $accessToken = $credentials->fetchAuthToken()['access_token'] ?? null;

        if (is_null($accessToken)) {
            $this->logger->error("PushNotification: Failed to get a new Access Token");

            throw new PushNotificationException("Error: FCM Access token is empty!");
        }

        return $accessToken;
    }

    /**
     * Set the Guzzle Client
     * Mostly used from the Codeception
     *
     * @param \GuzzleHttp\Client $client
     * @return void
     */
    public function setClient(Client $client): void
    {
        $this->client = $client;
    }

    /**
     * Clean the 404 expired Tokens
     * and update the validated ones
     * @param array $validTokens
     * @param array $expiredTokens
     *
     * @return void
     */
    public function updateValidatedAndCleanExpired(array $validTokens, array $expiredTokens)
    {
        $this->deviceManager->updateLastValidated($validTokens);
        $this->deviceManager->deleteDeviceTokensByTokenId($expiredTokens);
    }
}
