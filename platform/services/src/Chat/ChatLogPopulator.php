<?php

namespace Salesfloor\Services\Chat;

use Carbon\Carbon;
use Exception;
use Psr\Log\LoggerInterface;
use Salesfloor\API\Managers\Reps;
use Salesfloor\API\Managers\Stores;
use Salesfloor\Models\Rep;
use Salesfloor\Models\Store;
use Salesfloor\Services\ExtendedInsertQueryBuilder;
use Salesfloor\Services\Firebase;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Services\Sanitize\Sanitize;

/**
 * Populate the chat log tables.
 *
 * This is mostly code refactored out of the SyncChatLogs cron job into a service.
 */
class ChatLogPopulator
{
    // There's no model for those tables, only used here.
    private const DB_CUSTOMER_NAME_MAX_LENGTH = 128;
    private const DB_CUSTOMER_EMAIL_MAX_LENGTH = 128;
    private const DB_SOURCE_MAX_LENGTH = 64;

    /**
     * @var Firebase
     */
    private $firebase;

    /**
     * @var Stores
     */
    private $storeManager;

    /**
     * @var Reps
     */
    private $repManager;

    private $repository;

    private $logger;

    private $sanitizeService;

    private $firebaseDefaultPath;

    private $insertBuilder;

    public const SF_FIREBASE_CHATS_TABLE = 'sf_firebase_chats';
    public const SF_FIREBASE_CHAT_LOG_TABLE = 'sf_firebase_chat_log';

    private const CONNECT_2_BOT_USER_NAME = "AutomatWebchatBotAgent";

    public function __construct(
        Firebase $firebase,
        Stores $stores,
        Reps $reps,
        MySQLRepository $repository,
        LoggerInterface $logger,
        Sanitize $sanitizeService,
        ExtendedInsertQueryBuilder $insertBuilder,
    ) {
        $this->firebase     = $firebase;
        $this->storeManager = $stores;
        $this->repManager   = $reps;
        $this->repository = $repository;
        $this->logger = $logger;
        $this->sanitizeService = $sanitizeService;
        $this->insertBuilder = $insertBuilder;

        $this->firebaseDefaultPath = rtrim($this->firebase->getUrl(), '/') . $firebase->getDefaultPath();
    }

    public function getFirebaseDefaultPath()
    {
        return $this->firebaseDefaultPath;
    }

    /**
     * Figure out the store associated to this chat. There are several possibilities
     * to interpret:
     *
     * - A dash separated string, which equals the sf_identifier in the stores table
     * - A rep login - when coming from storefront, we use a totally different path.
     * - A number, which represents the unique ID of a store in our db.
     *
     * @param $roomId
     * @return string|null Store name, null on failure
     */
    public function retrieveStoreName($roomId, $userLogin)
    {
        // Try looking up by sf_identifier. This should be the standard.
        /** @var Store $storeObject */
        $storeObject = $roomId ? $this->storeManager->getOneOrNull(['sf_identifier' => $roomId]) : null;
        if ($storeObject !== null) {
            return $storeObject->name;
        }

        // Try store_id. Some older chats used this. In most cases, sf_identifier
        // matches this for backwards compatibility.
        $storeObject = $roomId ? $this->storeManager->getOneOrNull(['store_id' => $roomId]) : null;
        if ($storeObject !== null) {
            return $storeObject->name;
        }

        // It must be a rep login (storefront chat).
        // Try to retrieve the associated store.
        /** @var Rep $rep */
        $rep = $userLogin ? $this->repManager->getOneOrNull(['user_login' => $userLogin]) : null;
        if ($rep !== null) {
            $storeObject = $this->storeManager->getOneOrNull(['store_id' => $rep->store]);
            if ($storeObject !== null) {
                return $storeObject->name;
            }
        }

        return null;
    }

    // INFO
    // This was all migrated from the api/app/crons/SyncChatLogs.php into this service.
    // No refactor except moving it to this service.

    public function getShallowData(): array
    {
        $this->logger->info('Downloading Firebase\'s shallow chat data');


        $shallowFirebaseChatLog = $this->fetchFirebaseData('/message', [
            'shallow' => 'true'
        ]);

        $messageIds = json_decode($shallowFirebaseChatLog, true);

        if (empty($messageIds)) {
            $this->logger->error('Failed to retrieve shallow chat log from Firebase or empty');
            return [];
        }

        $this->logger->info(
            sprintf(
                "Shallow message data downloaded. Total [%s]",
                count($messageIds)
            )
        );

        return $messageIds;
    }

    public function loadShallowData(
        array $messageIds = [],
    ) {
        // So i can use it in heredoc
        $table = self::SF_FIREBASE_CHATS_TABLE;

        $this->logger->info(
            sprintf(
                "Loading firebase's shallow chat log in the db [%s]",
                !empty($messageIds) ? count($messageIds) : 0
            )
        );

        if (!empty($messageIds)) {
            // So we can escape character during sql query
            $qb = $this->repository->getQueryBuilder();

            $this->repository->beginTransaction();
            try {
                // Because it's an ids => boolean structure (true only afaik), array_chunk won't return what i want
                $messageIds = array_keys($messageIds);
                foreach (array_chunk($messageIds, 100) as $ids) {
                    $queryIds = [];
                    foreach ($ids as $id) {
                        $queryIds[] = "(" . $qb->expr()->literal($id) . ")";
                    }
                    $queryIdsValue = implode(',', $queryIds);

                    $sql = "INSERT IGNORE INTO $table (chat_id) VALUES $queryIdsValue;";
                    $this->repository->executeRawQuery($sql);
                }
                $this->repository->commit();
            } catch (Exception $e) {
                $this->repository->rollback();
                throw $e;
            }
        }
    }

    public function syncNewCompletedChats()
    {
        $this->logger->info('Syncing newly completed chats...');

        try {
            $qb = $this->repository->getQueryBuilder();
            $qb->select('chat_id, created_at, now() as current')
                ->from(self::SF_FIREBASE_CHATS_TABLE, 'chats')
                ->where('chats.updated_at IS NULL')
                ->andWhere('chats.customer_is_connected = 0')
                ->andWhere('chats.rep_is_connected = 0');

            $newChats = $qb->executeQuery()->fetchAllAssociative();

            // This is where we start fetching a chat's full details.
            foreach ($newChats as $newChat) {
                $path = "/message/{$newChat['chat_id']}";
                $chatLogEntry = $this->fetchFirebaseData($path);
                $chatLogEntry = json_decode($chatLogEntry);

                if (empty($chatLogEntry)) {
                    $this->logger->error(
                        sprintf(
                            "This chat_id [%s] doesn't exist or fetch failed",
                            $newChat['chat_id'],
                        )
                    );

                    continue;
                }

                $parsedChatLogEntry = $this->parseFirebaseChatLog(
                    $chatLogEntry,
                    $newChat
                );
                try {
                    $this->doUpdate($parsedChatLogEntry, $this->shouldUpdateChat($newChat, 4));
                } catch (\Exception $appendMessagesException) {
                    $error = sprintf(
                        'SyncChatLogs::%s Error append messages For new chat ID %s : %s',
                        __METHOD__,
                        $newChat['chat_id'],
                        $appendMessagesException->getMessage()
                    );
                    $this->logger->error($error);
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical($e);
        }
    }

    public function syncExistingIncompleteChats()
    {
        $this->logger->info('Syncing existing / incomplete chats...');

        try {
            $qb = $this->repository->getQueryBuilder();
            $qb->select('chat_id', 'created_at', 'updated_at as current')
                ->from(self::SF_FIREBASE_CHATS_TABLE, 'chats')
                ->where('chats.updated_at IS NOT NULL')
                ->andWhere('chats.customer_is_connected = 1 OR chats.rep_is_connected = 1');

            $existingChats = $qb->executeQuery()->fetchAllAssociative();
            foreach ($existingChats as $existingChat) {
                $path = "/message/{$existingChat['chat_id']}.json";
                $chatLogEntry = $this->fetchFirebaseData($path);
                $chatLogEntry = json_decode($chatLogEntry);

                if (empty($chatLogEntry)) {
                    $this->logger->error(
                        sprintf(
                            "This chat_id [%s] doesn't exist or fetch failed",
                            $existingChat['chat_id'],
                        )
                    );

                    continue;
                }

                $parsedChatLogEntry = $this->parseFirebaseChatLog(
                    $chatLogEntry,
                    $existingChat
                );

                try {
                    $this->doUpdate($parsedChatLogEntry);
                } catch (\Exception $appendMessagesException) {
                    $error = sprintf(
                        'SyncChatLogs::%s Error append messages for existing Chat ID %s : %s',
                        __METHOD__,
                        $existingChat['chat_id'],
                        $appendMessagesException->getMessage()
                    );
                    $this->logger->error($error);
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical($e);
        }
    }

    private function parseFirebaseChatLog(\stdClass $firebaseChatLogEntry, array $firebaseChatMetadata): array
    {
        $firebaseChatId = $firebaseChatMetadata['chat_id'];

        $this->logger->debug(
            sprintf(
                "Parsing chat [%s] with path [%s]",
                $firebaseChatId,
                $this->getFirebaseDefaultPath(),
            )
        );

        $returnValues = [
            'chat' => [
                'chat_id' => $firebaseChatId,
                'request_timestamp' => null,
                'acceptance_timestamp' => null,
                'store_name' => null,
                'customer_email' => null,
                'customer_name' => null,
                'customer_is_connected' => 0,
                'user_display_name' => null,
                'user_login' => null,
                'rep_is_connected' => 0,
                'source' => null,
                'specialty' => null,
                'flagged' => 0,
                'customer_subscribed' => 0
            ],
            'messages' => []
        ];

        // Get the customer name (from Firebase)
        if (isset($firebaseChatLogEntry->customer->name)) {
            $returnValues['chat']['customer_name'] = mb_substr($firebaseChatLogEntry->customer->name, 0, self::DB_CUSTOMER_NAME_MAX_LENGTH);
        }

        // Get the customer Email (from Firebase)
        if (isset($firebaseChatLogEntry->customer->email)) {
            $returnValues['chat']['customer_email'] = mb_substr($firebaseChatLogEntry->customer->email, 0, self::DB_CUSTOMER_EMAIL_MAX_LENGTH);
        }

        // Get the connection status for the customer
        if (isset($firebaseChatLogEntry->customer->isConnected)) {
            $returnValues['chat']['customer_is_connected'] = $this->getChatConnectedState($firebaseChatLogEntry->customer->isConnected, $firebaseChatMetadata);
        }

        // Get the rep name (from Firebase)
        if (isset($firebaseChatLogEntry->rep->name)) {
            $returnValues['chat']['user_display_name'] = $firebaseChatLogEntry->rep->name;
        }

        // Get the rep name (from Firebase)
        if (isset($firebaseChatLogEntry->rep->user_login)) {
            $returnValues['chat']['user_login'] = $firebaseChatLogEntry->rep->user_login;
        }

        // Get the connection status for the Rep
        if (isset($firebaseChatLogEntry->rep->isConnected)) {
            $returnValues['chat']['rep_is_connected'] = $this->getChatConnectedState($firebaseChatLogEntry->rep->isConnected, $firebaseChatMetadata);
        }

        // Get the Source of the Chat
        if (isset($firebaseChatLogEntry->meta->source)) {
            $returnValues['chat']['source'] = mb_substr($firebaseChatLogEntry->meta->source, 0, self::DB_SOURCE_MAX_LENGTH);
        }

        // Get the Specialty of the Chat
        if (isset($firebaseChatLogEntry->meta->specialty)) {
            $returnValues['chat']['specialty'] = $firebaseChatLogEntry->meta->specialty;
        }

        // Identify if the chat was flagged
        if (isset($firebaseChatLogEntry->rep->isConnected) && 'flaggedChat' === $firebaseChatLogEntry->rep->isConnected) {
            $returnValues['chat']['flagged'] = 1;
        }

        // Identify if the customer took the email opt-in subscription
        if (isset($firebaseChatLogEntry->customer->subscribed) && '1' === $firebaseChatLogEntry->customer->subscribed) {
            $returnValues['chat']['customer_subscribed'] = 1;
        }

        // Build the conversation...
        if (isset($firebaseChatLogEntry->messages)) {
            $this->logger->debug("Parsing message(s) for chat $firebaseChatId");

            $messageCount = 0;

            foreach ($firebaseChatLogEntry->messages as $firebaseChatLogEntryMessageId => $firebaseChatLogEntryMessage) {
                $messageCount++;

                if (1 === $messageCount) {
                    $returnValues['chat']['request_timestamp'] = $firebaseChatLogEntryMessage->timestamp;
                }

                $messageDetails['message_id'] = $firebaseChatLogEntryMessageId;

                if (is_string($firebaseChatLogEntryMessage->message)) {
                    $messageDetails['message'] = $firebaseChatLogEntryMessage->message;
                } else {
                    $messageDetails['message'] = json_encode($firebaseChatLogEntryMessage->message);
                }

                // Bot Message (Aka Connect 2 bot generated)
                // The Bot for the sake of protocol reasons acts like a "rep" but with a special "repLogin" value.
                // That said, we want our DB rows to track that it was specifically a Bot message for the sake
                // Of chat log exporting
                if (
                    $firebaseChatLogEntryMessage->type === 'rep'
                    && $firebaseChatLogEntryMessage->repLogin === self::CONNECT_2_BOT_USER_NAME
                ) {
                    $messageDetails['type'] = 'bot';
                } else {
                    $messageDetails['type'] = $firebaseChatLogEntryMessage->type;
                }
                $messageDetails['timestamp'] = $firebaseChatLogEntryMessage->timestamp;

                // Since this feature is optional ; will put null when not in used.
                if (($firebaseChatLogEntryMessage->skippedModerationCheck ?? null)) {
                    if (($firebaseChatLogEntryMessage->isModeratorFlagged ?? null)) {
                        // This should never happen, when we skipped, we should always have 0.
                        $this->logger->error("This should never happen. Moderation was skipped (timeout), but got flagged.");
                    }
                    $messageDetails['isModeratorFlagged'] = -1;
                } else {
                    $messageDetails['isModeratorFlagged'] = $firebaseChatLogEntryMessage->isModeratorFlagged ?? null;
                }

                $messageDetails['chat_id'] = $firebaseChatId;

                $returnValues['messages'][] = $messageDetails;
            }
        }

        /**
         * As per a discussion with Mike, rooms seem to depict the time when
         * a chat was accepted by a rep. Their structure is as follows:
         *
         * db->environment->retailer->room->rep->chat_id->timestamp
         *
         * A call has to be made to get the rooms, then a recursive call has
         * to be made for each room to attempt to find the timestamp of the
         * room/rep/chat_id combination
         */

        $userLogin = $returnValues['chat']['user_login'] ?? null;
        $storeOrUser = $firebaseChatLogEntry->meta->store ?? null;

        // Get the store name (from Firebase)
        $returnValues['chat']['store_name'] = $firebaseChatLogEntry->rep->store->name ?? ($storeOrUser ? $this->retrieveStoreName(
            $storeOrUser,
            $userLogin
        ) : null);

        // Get the time the associate accepted the request (from Firebase)
        if (isset($firebaseChatLogEntry->meta->request_accepted_on)) {
            $returnValues['chat']['acceptance_timestamp'] = $firebaseChatLogEntry->meta->request_accepted_on;
        }

        // Legacy fallback - The use of the room path for acceptance timestamp should be considered deprecated
        // and may be removed in the future when BC can no longer be supported or required.
        if (empty($returnValues['chat']['acceptance_timestamp']) || 'NULL' === $returnValues['chat']['acceptance_timestamp']) {
            $this->logger->debug(
                sprintf(
                    "acceptance_timestamp is missing, will use legacy logic to get the info"
                )
            );
            $chatInitializationTimestamp = $this->getChatInitializationTimestamp([
                'storeOrUser' => $storeOrUser,
                'userLogin' => $userLogin,
                'firebaseChatId' => $firebaseChatId
            ]);

            if ($chatInitializationTimestamp) {
                $returnValues['chat']['acceptance_timestamp'] = $chatInitializationTimestamp;
            }
        }

        return $returnValues;
    }

    private function shouldUpdateChat(array $chat, int $hours): bool
    {
        $diffInHours = (strtotime($chat['current']) - strtotime($chat['created_at'])) / 3600;
        return $diffInHours > $hours;
    }

    private function doUpdate(array $parsedChatLogEntry, bool $forceUpdate = false): void
    {
        $appendMessagesResult = $this->appendMessages($parsedChatLogEntry['messages']);

        $this->logger->debug(sprintf(
            "For chat [%s] number of messages [%s]",
            $parsedChatLogEntry['chat']['chat_id'] ?? '',
            $appendMessagesResult,
        ));

        // Will check chat again if no message appended until 4 hours later,
        if ($appendMessagesResult !== 0 || $forceUpdate) {
            $this->updateChat($parsedChatLogEntry['chat']);
        }
    }

    public function updateChat(array $updatedFieldsAndValues)
    {
        if (!isset($updatedFieldsAndValues['chat_id'])) {
            return;
        }

        $sanitized = [];
        $sanitized['customer_name'] = $this->sanitizeService->removeXss($updatedFieldsAndValues['customer_name']);
        $sanitized['user_display_name'] = $this->sanitizeService->removeXss($updatedFieldsAndValues['user_display_name']);

        $qb = $this->repository->getQueryBuilder();

        $qb->update(self::SF_FIREBASE_CHATS_TABLE)
            // Doctrine (without bound parameters) doesn't handle null value. In some old chat, this is missing
            ->set('request_timestamp', $updatedFieldsAndValues['request_timestamp'] ?? 'NULL')
            ->set('acceptance_timestamp', $updatedFieldsAndValues['acceptance_timestamp'] ?? 'NULL')
            ->set('store_name', $qb->expr()->literal($updatedFieldsAndValues['store_name']))
            ->set('customer_email', $qb->expr()->literal($updatedFieldsAndValues['customer_email'] ?? 'NULL'))
            ->set('customer_name', $qb->expr()->literal($sanitized['customer_name']))
            ->set('customer_is_connected', $updatedFieldsAndValues['customer_is_connected'])
            ->set('user_display_name', $qb->expr()->literal($sanitized['user_display_name']))
            ->set('user_login', $qb->expr()->literal($updatedFieldsAndValues['user_login']))
            ->set('rep_is_connected', $updatedFieldsAndValues['rep_is_connected'])
            ->set('source', $qb->expr()->literal($updatedFieldsAndValues['source']))
            ->set('specialty', $qb->expr()->literal($updatedFieldsAndValues['specialty']))
            ->set('flagged', $updatedFieldsAndValues['flagged'])
            ->set('customer_subscribed', $updatedFieldsAndValues['customer_subscribed'])
            ->set('updated_at', $qb->expr()->literal(gmdate('Y-m-d H:i:s')))
            ->where(
                $qb->expr()->andX(
                    $qb->expr()->eq('chat_id', $qb->expr()->literal($updatedFieldsAndValues['chat_id']))
                )
            );

        $this->repository->executeCustomQuery($qb);
    }

    private function appendMessages($messages): int
    {
        if (count($messages) == 0) {
            // No messages to insert. This is an expected case. No error should be thrown
            return 0;
        }

        // Since we are reusing the same instance, we need to init the state each time.
        $this->insertBuilder->resetState();

        $this->insertBuilder
            ->setTable(self::SF_FIREBASE_CHAT_LOG_TABLE)
            ->addInsertFields([
                'message_id',
                'message',
                'type',
                'timestamp',
                'is_moderator_flagged',
                'chat_id',
            ])
        ;

        foreach ($messages as $message) {
            if (empty($message['type'])) {
                $this->logger->error(
                    sprintf(
                        "This message will not be imported because required field are missing [%s]",
                        json_encode($message)
                    )
                );
                continue;
            }

            $this->insertBuilder->addValuesSet([
                'message_id' => $message['message_id'],
                'message' => $this->sanitizeService->removeXss($message['message']),
                'type' => $message['type'],
                'timestamp' => $message['timestamp'],
                'is_moderator_flagged' => (int)$message['isModeratorFlagged'],
                'chat_id' => $message['chat_id'],
            ]);
        }

        $this->insertBuilder->addOnDuplicateUpdateFields([
            'updated_at' => gmdate('Y-m-d H:i:s'),
        ]);

        $insert = ($this->insertBuilder->prepare());

        $this->repository->executeQuery($insert['query'], $insert['parameters']);

        return count($this->insertBuilder->getValuesLists());
    }

    /**
     * @param array $params - [
     *      'storeOrUser' => string|null,
     *      'userLogin' => string,
     *      'firebaseChatId' => string
     * ]
     * @return int|bool
     * @deprecated this feature should not be relied upon. it will be removed in the future
     */
    private function getChatInitializationTimestamp(array $params): int|bool
    {
        $storeOrUser = $params['storeOrUser'];
        $userLogin = $params['userLogin'];
        $firebaseChatId = $params['firebaseChatId'];

        if (empty($storeOrUser) || empty($userLogin)) {
            return false;
        }

        $roomTimestampPath = "/room/$storeOrUser/$userLogin/$firebaseChatId/timestamp";
        $content = $this->fetchFirebaseData($roomTimestampPath);

        // Do not return null if $content is "null"
        return json_decode($content) ?: false;
    }

    /**
     * Get the chat connected state
     *
     * @param string $connectedState
     * @param array $chatMetadata this has the following structure:
     * [ 'chat_id' => '-Nxs...', 'created_at' => '2019-01-01 00:00:00', 'current' => '2019-01-01 00:00:00' ]
     * @return int
     */
    private function getChatConnectedState(string $connectedState, array $chatMetadata): int
    {
        $diffInDays = (strtotime($chatMetadata['current']) - strtotime($chatMetadata['created_at'])) / 86400;
        $is_outdated = $diffInDays > 7; // No chat should be open longer than a week
        $chatIsConnectedStates = ['true', 'idle', 'pending'];
        if ($is_outdated) {
            $this->logger->info(sprintf('Chat %s is outdated, setting connected state to 0', $chatMetadata['chat_id']));
            return 0;
        }
        return (int)in_array($connectedState, $chatIsConnectedStates);
    }

    /**
     * This is to prevent (to some extent) timeout from firebase.
     * If needed, we could use guzzle with a better retry mechanism.
     *
     * @param string $url
     * @param int $nbAttempt
     *
     * @return string|bool
     */
    private function retryOnFailure(string $url, int $nbAttempt = 1): string|bool
    {
        $this->logger->debug(
            sprintf(
                "Fetch from firebase [%s]",
                strtok($url, '?') // This is to make sure auth key isn't saved in log
            )
        );

        $maxAttempts = 3;
        $baseSleep = 5;

        if ($nbAttempt > $maxAttempts) {
            $this->logger->error(
                sprintf(
                    'Reach the max retry on url [%s] NbAttempt [%s]',
                    $url,
                    $nbAttempt,
                )
            );

            return false;
        }

        $result = @file_get_contents($url);

        if ($result === false) {
            $error = error_get_last();
            $this->logger->error(
                sprintf(
                    'Failed to retrieve [%s] from firebase: [%s]',
                    $url,
                    $error['message'] ?? ''
                )
            );

            // 5, 25, 125 seconds of sleep
            sleep(pow($baseSleep, $nbAttempt));
            return $this->retryOnFailure($url, ++$nbAttempt);
        }

        return $result;
    }

    /**
     * Retrieves a pointer to the last successfully logged firebase chat.
     * Note: Only records within the last 24hrs will be scanned.
     * @return string|bool
     * @throws \Doctrine\DBAL\Exception
     */
    public function fetchCursor(): string | bool
    {
        $qb = $this->repository->getQueryBuilder();
        $afterDate = Carbon::now('UTC')->subDays(1)->setTime(0, 0)->toDateTimeString();
        $qb->select('chat_id')
            ->from(self::SF_FIREBASE_CHATS_TABLE, 'chats')
            ->andWhere('chats.created_at > :startDate')
            ->orderBy('chats.created_at', 'DESC')
            ->setMaxResults(1)
            ->setParameter('startDate', $afterDate);

        return $qb->fetchOne();
    }

    /**
     * Copy full chat log information from firebase to data store.
     * using a starting point reference.
     * All messages after the start point will be imported.
     *
     * Note this api does not use shallow. Thus, there is no limit to the
     * depth of each record returned.
     *
     * @params string $cursor the starting point reference. This is the firebase key e.g "-O38ZT7j7X6pKn3_gwMU"
     * @throws Exception
     */
    public function loadData(string $cursor): void
    {
        $params = [
            'orderBy' => '"$key"',
            'startAt' => '"' . $cursor . '"',
        ];

        $logs = json_decode($this->fetchFirebaseData('/message', $params));

        if (empty($logs)) {
            $this->logger->error('Failed to retrieve chat log from Firebase or empty');
            return;
        }

        // The result from firebase is inclusive and unordered
        // thus we need to remove the cursor object since we don't need it.
        unset($logs->{$cursor});
        $logs = new \ArrayObject($logs);

        $this->logger->info(
            sprintf(
                "Loading message data into db. Total [%s]",
                count($logs)
            )
        );

        $this->doInsert($logs);
    }

    /**
     * Add a new chat record and message logs into database
     * @param \ArrayObject $data
     * @return void
     */
    protected function doInsert(\ArrayObject $data): void
    {
        foreach ($data as $key => $log) {
            $newChat = [
                'chat_id' => $key,
                'created_at' => gmdate('Y-m-d H:i:s', $log->meta->request_accepted_on ?? null),
                'current' => gmdate('Y-m-d H:i:s'),
            ];

            $parsedChatLogEntry = $this->parseFirebaseChatLog(
                $log,
                $newChat
            );

            $this->appendMessages($parsedChatLogEntry['messages']);
            $this->insert($parsedChatLogEntry['chat']);
        }
    }

    /**
     * This will insert a new record into the sf_firebase_chats table.
     * Duplicate records will be ignored.
     * @param array $entry
     * @return void
     */
    private function insert(array $entry): void
    {
        $updated_at = !$entry['rep_is_connected'] ? gmdate('Y-m-d H:i:s') : null;

        $this->insertBuilder->resetState();
        $this->insertBuilder->setTable(self::SF_FIREBASE_CHATS_TABLE)
            ->addInsertFields([
            'chat_id',
            'request_timestamp',
            'acceptance_timestamp',
            'store_name',
            'customer_email',
            'customer_name',
            'customer_is_connected',
            'user_display_name',
            'user_login',
            'rep_is_connected',
            'source',
            'specialty',
            'flagged',
            'customer_subscribed',
            'updated_at',
            ])
            ->addValuesSet([
            'chat_id' => $entry['chat_id'],
            'request_timestamp' => ($entry['request_timestamp'] ?? null),
            'acceptance_timestamp' => ($entry['acceptance_timestamp'] ?? null),
            'store_name' => $entry['store_name'],
            'customer_email' => ($entry['customer_email'] ?? null),
            'customer_name' => $this->sanitizeService->removeXss($entry['customer_name']),
            'customer_is_connected' => $entry['customer_is_connected'],
            'user_display_name' => $this->sanitizeService->removeXss($entry['user_display_name']),
            'user_login' => $entry['user_login'],
            'rep_is_connected' => $entry['rep_is_connected'],
            'source' => $entry['source'],
            'specialty' => $entry['specialty'],
            'flagged' => $entry['flagged'],
            'customer_subscribed' => $entry['customer_subscribed'],
            'updated_at' => $updated_at
            ])
            ->addOnDuplicateUpdateFields(['chat_id' => $entry['chat_id']]);
        $insert = ($this->insertBuilder->prepare());

        $this->repository->executeQuery($insert['query'], $insert['parameters']);
    }

    /**
     * Sanitize firebase url and fetch firebase record in json format.
     * @param string $path
     * @param array $params
     * @return bool|string
     */
    protected function fetchFirebaseData(string $path, array $params = []): string|bool
    {
        $path = preg_replace(
            ['/^\/|\/$|\.json$|\.text$/', '/\s+/'],
            ['', ''],
            trim($path)
        );
        $url = $this->getFirebaseDefaultPath() . "/$path.json";
        $url = $url . '?' . http_build_query(array_merge(['auth' => $this->firebase->getToken()], $params));

        return $this->retryOnFailure($url);
    }
}
