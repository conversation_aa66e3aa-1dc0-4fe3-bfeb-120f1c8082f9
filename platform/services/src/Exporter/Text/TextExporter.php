<?php

namespace Salesfloor\Services\Exporter\Text;

use Doctrine\DBAL\Query\QueryBuilder;
use Salesfloor\API\Managers\Reps;
use Salesfloor\API\Managers\Stores;
use Salesfloor\API\Managers\UserFieldHistory;
use Salesfloor\Models\Rep;
use Salesfloor\Models\Store;
use Salesfloor\Services\Exporter\Base;
use Salesfloor\Services\Exporter\ExportType\ZipExportType;

class TextExporter extends Base
{
    const EXPORT_DIRECTORY = 'texts';
    const EXPORT_NAME = 'text';

    public $splitBy = ZipExportType::YEARLY;
    public $groupBy  = null;

    protected $exportType = self::EXPORT_TYPE_ZIP;

    private $encryptionEnabled;

    /** @var Reps $repManager */
    private $repManager;
    /** @var UserFieldHistory $userFieldHistoryManager */
    private $userFieldHistoryManager;
    /** @var Stores $storeManager */
    private $storeManager;

    public function loadDependencies($app)
    {
        $this->repManager = $app['reps.manager'];
        $this->userFieldHistoryManager = $app['user_field_history.manager'];
        $this->storeManager = $app['stores.manager'];

        $this->encryptionEnabled = $this->configs['exporter.text.encryption.enabled'];
    }

    public function setEncryptionEnabled($encryptionEnabled)
    {
        $this->encryptionEnabled = $encryptionEnabled;
    }

    protected $headers = [
        "Thread ID"             => "threadId",
        "User ID"               => "userId",
        "User name"             => "repName",
        "User email"            => "repEmail",
        "User phone"            => "repPhone",
        "Customer ID"           => "customerId",
        "Customer name"         => "customerName",
        "Customer email"        => "customerEmail",
        "Customer phone"        => "customerPhone",
        "Thread subscribed"     => "threadSubscribed",
        "Thread active"         => "threadActive",
        "Thread read"           => "threadRead",
        "Thread valid"          => "threadValid",
        "Message provider ID"   => "providerId",
        "Message direction"     => "direction",
        "Message body"          => "body",
        "Message active"        => "messageActive",
        "Message creation date" => "messageCreatedAt",
        "Retailer Store ID"     => 'retailerStoreId'
    ];

    protected function setBody()
    {
        $total = 0;
        foreach ($this->getText() as $text) {
            $text = $this->transformStoreByUserHistory($text);
            $this->addToFile($text);
            $total++;
        }

        $this->logger->info(
            sprintf("You have [%s] texts between [%s] and [%s]", $total, $this->startDay, $this->endDay)
        );
    }

    public function getDateFromRow($row)
    {
        return $row['messageCreatedAt'];
    }

    private function transformStoreByUserHistory($text)
    {
        /** @var Rep $rep */
        $rep = $this->repManager->getById($text['userId']);
        $storeId = $this->userFieldHistoryManager->getStoreIdOnDay($rep, $text['messageCreatedAt']);

        if ($storeId != $rep->store) {
            /** @var Store $store */
            $store = $this->storeManager->getById($storeId);

            if ($store !== null) {
                $text['retailerStoreId'] = $store->retailer_store_id;
            }
        }

        return $text;
    }

    private function getText()
    {
        /** @var QueryBuilder $qB */
        $qB = $this->repositories->getQueryBuilder();

        $qB
            ->select(
                'tt.id as threadId',
                'tt.user_id as userId',
                'u.display_name as repName',
                'u.user_email as repEmail',
                'tt.user_phone_number as repPhone',
                'tt.customer_id as customerId',
                'c.name as customerName',
                'c.email as customerEmail',
                'tt.customer_phone_number as customerPhone',
                'tt.is_subscribed as threadSubscribed',
                'tt.is_active as threadActive',
                'tt.is_read as threadRead',
                'tt.is_valid as threadValid',
                'tm.provider_message_id as providerId',
                'tm.direction as direction',
                'tm.body as body',
                'tm.is_active as messageActive',
                'tm.created_at as messageCreatedAt',
                's.retailer_store_id as retailerStoreId'
            )
            ->from('sf_text_thread', 'tt')
            ->innerJoin('tt', 'sf_text_message', 'tm', 'tt.id = tm.text_thread_id')
            ->innerJoin('tt', 'wp_users', 'u', 'tt.user_id = u.ID')
            ->innerJoin('u', 'sf_store', 's', 'u.store = s.store_id ')
            ->leftJoin('tt', 'sf_customer', 'c', 'tt.customer_id = c.ID')
            ->where(
                $qB->expr()->notlike(
                    'u.user_login',
                    $qB->expr()->literal('salesfloor\_%')
                )
            );

        // TODO: Confirm the order desired, but will use for now the same field used to split csvs
        // It makes sure the snapshot file can match the result from SQL query each time when running it.
        // Please check the test:
        //    ./robo test:functional Exporter/Text/TextExporterCest.php
        //
        // I put the test here as a reminder or explanation,
        // why we need it and can answer any questions from other DEVs quickly.
        $qB->addOrderBy('tm.created_at', 'ASC')
            ->addOrderBy('tt.id', 'ASC');

        $this->setDayFilters($qB, 'tm', 'created_at');

        $stmt = $qB->execute();

        while ($row = $stmt->fetch()) {
            yield $row;
        }
    }

    protected function isEncryptionEnabled()
    {
        return $this->encryptionEnabled;
    }
}
