<?php

use SF\ApiTester;
use Carbon\Carbon;
use SF\api\BaseApi;
use Codeception\Stub;
use Salesfloor\Services\Aws\S3;

class ProductPanelCest extends BaseApi
{
    // User in dump.sql
    protected const USER_REGGIE_ID = 1;
    protected const USER_STORE_ID  = 2;

    protected const LOCALE_FR_CA  = 'fr_CA';

    protected const EVENT_TYPE_TOP_PICKS = 70;
    protected const EVENT_TYPE_NEW_ARRIVALS = 71;

    protected const USER_TYPE = 'rep';

    protected const PRODUCT_ID1 = '54640cd49acae0386f777604ce15ab92';
    protected const PRODUCT_WRONG_ID = 'testtesttest';

    private $panels = [
        'top-picks' => [
            'numberProductsConfig' => 'retailer.num_top_picks',
            'manager' => 'products.top-picks.manager',
            // See in dump.sql table sf_product_category_map (products with storefront_slot from 1 to 8 number)
            'defaultProducts' => [
                '0cb4b8b709e00325461bc0c4688be637',
                '174d4437287e61204b5bc859a6c6f824',
                '2697848788fd5ef0715e2c3e4ae8ac0d',
                '2f97af6978a16c081cc0a094b73016bc',
                '363f075ba54f1c415b30d8f96a2ea5b0',
                '508927971f1252415e34bdb256efa6a1',
                '54640cd49acae0386f777604ce15ab92',
                '5e88a0d9359bd586d6dab6ee362ba330',
            ],
            'type' => self::EVENT_TYPE_TOP_PICKS,
        ],
        'new-arrivals' => [
            'numberProductsConfig' => 'retailer.num_deals',
            'manager' => 'products.new-arrivals.manager',
             // See in dump.sql table sf_products colum arrival dates
            'defaultProducts' => [
                '54640cd49acae0386f777604ce15ab92',
                'b2c1b175fdbbac5c5b33d2628042967c',
                '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'c5fdf5d2b068cdab85a9800b7987fb31',
            ],
            'type' => self::EVENT_TYPE_NEW_ARRIVALS,
        ],
        'latest-arrivals' => [
            'numberProductsConfig' => 'retailer.num_deals',
            'manager' => 'products.new-arrivals.manager',
            'type' => self::EVENT_TYPE_NEW_ARRIVALS,
        ],
        'specials' => [
            'numberProductsConfig' => 'retailer.num_deals',
            'manager' => 'products.specials.manager',
            // See in dump.sql table sf_products colum deal_ratio
            'defaultProducts' => [
                '508927971f1252415e34bdb256efa6a1',
                '508927971f1252415e34bdb256efa6a2',
                '174d4437287e61204b5bc859a6c6f824',
                'd961d23a31d503f8e75c03d4da0933c7',
            ]
        ],
        'deals' => [
            'numberProductsConfig' => 'retailer.num_deals',
            'manager' => 'products.specials.manager',
            'type' => self::EVENT_TYPE_NEW_ARRIVALS,
        ]
    ];

    private $nominatedProductsList = [
        '54640cd49acae0386f777604ce15ab92' => [
            'comment' => 'I recommend this product',
            'priority' => '1',
            'type' => 'new-arrivals',
        ],
        'b2c1b175fdbbac5c5b33d2628042967c' => [
            'comment' => 'I like this product',
            'priority' => '2',
            'type' => 'new-arrivals',
        ],
        '8b22196d3cb34f69ef0c5f5e06cb1c08' => [
            'comment' => 'I love this product',
            'priority' => '3',
            'type' => 'new-arrivals',
        ]
    ];

    private $nominatedProductsList2 = [
        'e1d6bfff6c8f2ae59b9eac0f05d80214' => [
            'comment' => '',
            'priority' => '1',
            'type' => 'new-arrivals',
        ],
        '6c4882e22e27e51c53b55e616cb5c120' => [
            'comment' => 'Commentaire',
            'priority' => '1',
            'type' => 'top-picks',
        ],
        '8b22196d3cb34f69ef0c5f5e06cb1c08' => [
            'comment' => '',
            'priority' => '3',
            'type' => 'top-picks',
        ],
        '2f97af6978a16c081cc0a094b73016bc' => [
            'comment' => '',
            'priority' => '5',
            'type' => 'top-picks',
        ],
        'd14508bf3f8d41fe999b7cacf6dd3a23' => [
            'comment' => '',
            'priority' => '4',
            'type' => 'top-picks',
        ],
    ];

    private $nominatedProductsListI18n = [
        '900e8fb9e3fa4858948171165f62a5fd' => [
            'comment' => '',
            'priority' => '1',
            'type' => 'new-arrivals',
        ],
        'eef7f7c98b1253581a5b1f08304fe286' => [
            'comment' => ['fr_CA' => 'c\'est un commentaire 2'],
            'priority' => '2',
            'type' => 'new-arrivals',
        ],
        '5e88a0d9359bd586d6dab6ee362ba330' => [
            'comment' => ['en_US' => 'english comment 2'],
            'priority' => '1',
            'type' => 'top-picks',
        ],
        '174d4437287e61204b5bc859a6c6f824' => [
            'comment' => [
                'en_US' => 'english comment',
                'fr_CA' => 'c\'est un commentaire'
            ],
            'priority' => ['2', '3'],
            'type' => 'top-picks',
        ],
        '508927971f1252415e34bdb256efa6a1' => [
            'comment' => '',
            'priority' => ['2', '3'],
            'type' => 'top-picks',
        ],
    ];

    public function _before($I)
    {
        parent::_before($I);
        $this->fixturesSave = require __DIR__ . '/../../fixtures/products/productpanel.php';
    }

    /** @group database_transaction */
    public function testCoreFetchTopPicks(ApiTester $I)
    {
        $this->testFetchPanel($I, 'top-picks', true);
    }

    /** @group database_transaction */
    public function testCoreFetchLastArrival(ApiTester $I)
    {
        $this->testFetchPanel($I, 'new-arrivals', true);
        // Double route to check
        $this->testFetchPanel($I, 'latest-arrivals', true);
    }

    /** @group database_transaction */
    public function testCoreFetchSpecial(ApiTester $I)
    {
        $this->testFetchPanel($I, 'specials', true);
        // Double route to check
        $this->testFetchPanel($I, 'deals', true);
    }

    /** @group database_transaction */
    public function testCoreFetchTopPicksI18n(ApiTester $I)
    {
        $this->testFetchPanel($I, 'top-picks', true, self::LOCALE_FR_CA);
    }

    /** @group database_transaction */
    public function testCoreFetchLastArrivalI18n(ApiTester $I)
    {
        $this->testFetchPanel($I, 'new-arrivals', true, self::LOCALE_FR_CA);
        // Double route to check
        $this->testFetchPanel($I, 'latest-arrivals', true, self::LOCALE_FR_CA);
    }

    /** @group database_transaction */
    public function testCoreFetchSpecialI18n(ApiTester $I)
    {
        $this->testFetchPanel($I, 'specials', true, self::LOCALE_FR_CA);
        // Double route to check
        $this->testFetchPanel($I, 'deals', true, self::LOCALE_FR_CA);
    }

    /** @group database_transaction */
    public function testCoreSaveTopPicks(ApiTester $I)
    {
        $this->testSavePanel($I, 'top-picks');
    }

    /** @group database_transaction */
    public function testCoreSaveLastArrival(ApiTester $I)
    {
        $this->testSavePanel($I, 'new-arrivals');
    }

    /** @group database_transaction */
    public function testCoreSaveSpecial(ApiTester $I)
    {
        $this->testSavePanel($I, 'specials');
    }

    private function testCoreSaveStoreMode(ApiTester $I)
    {
        $this->testSavePanelStoreMode($I);
    }

    /** @group database_transaction */
    public function testCoreSaveTopPicksI18n(ApiTester $I)
    {
        $this->testSavePanelI18n($I, 'top-picks');
    }

    /** @group database_transaction */
    public function testCoreSaveLastArrivalI18n(ApiTester $I)
    {
        $this->testSavePanelI18n($I, 'new-arrivals');
    }

    /** @group database_transaction */
    public function testCoreSaveSpecialI18n(ApiTester $I)
    {
        $this->testSavePanelI18n($I, 'specials');
    }

    private function testCoreSaveStoreModeI18n(ApiTester $I)
    {
        $this->testSavePanelStoreModeI18n($I);
    }

    /** @group database_transaction */
    public function testCoreDeleteTopPicks(ApiTester $I)
    {
        $this->testDeletePanel($I, 'top-picks');
    }

    /** @group database_transaction */
    public function testCoreDeleteLastArrival(ApiTester $I)
    {
        $this->testDeletePanel($I, 'new-arrivals');
    }

    /** @group database_transaction */
    public function testCoreDeleteSpecial(ApiTester $I)
    {
        $this->testDeletePanel($I, 'specials');
    }

    /** @group database_transaction */
    public function testCoreDuplicates(ApiTester $I)
    {
        $I->wantTo("Test duplicates");

        $params = $this->fixturesSave['duplicates'];
        $paramsVerif = $this->fixturesSave['duplicates-verifications'];

        // We test deduplicate script against new-arrival (code is the same for all panel)
        $response = $I->doDirectPost($this->app, "reps/" . self::USER_REGGIE_ID . "/products/new-arrivals", $params);
        $results = json_decode($response->getContent());
        $sku = [];

        foreach ($results as $result) {
            $sku[] = $result->product_sku;

            if ($paramsVerif['duplicated-product'] == $result->product_sku) {
                $position = $result->position;
            }
        }

        // We should have unique products
        $I->assertEquals(count($sku), count(array_unique($sku)));
        // The duplicated product should be only once in the result and at a specific position
        // according to fixtures
        $I->assertEquals($position, $paramsVerif['correct-position']);
    }

    /** @group database_transaction */
    public function testCoreAutoselectedProductsTopPicks(ApiTester $I)
    {
        $this->testDefaultProducts($I, 'top-picks');
    }

    /** @group database_transaction */
    public function testCoreAutoselectedProductsNewArrivals(ApiTester $I)
    {
        $this->testDefaultProducts($I, 'new-arrivals');
    }

    /** @group database_transaction */
    public function testCoreAutoselectedProductsSpecials(ApiTester $I)
    {
        $this->testDefaultProducts($I, 'specials');
    }

    private function testDefaultProducts(ApiTester $I, $type)
    {
        // Get default products
        $products = $this->app['products.' . $type . '.manager']->getDefaultProducts(
            static::USER_REGGIE_ID,
            static::USER_TYPE,
            $this->app['configs'][$this->panels[$type]['numberProductsConfig']],
            []
        );

        // Check if they match expected results
        $i = 0;
        foreach ($products as $product) {
            $I->assertEquals(
                $this->panels[$type]['defaultProducts'][$i],
                $product['product_sku']
            );
            ++$i;
        }
    }

    /** @group database_transaction */
    public function testCoreAutoselectedProductsFallBackTopPicks(ApiTester $I)
    {
        $this->testAutoselectedProductsFallBack($I, 'top-picks');
    }

    /** @group database_transaction */
    public function testCoreAutoselectedProductsFallBackLatestArrival(ApiTester $I)
    {
        $this->testAutoselectedProductsFallBack($I, 'new-arrivals');
    }

    /** @group database_transaction */
    public function testCoreAutoselectedProductsFallBackSpecials(ApiTester $I)
    {
        $this->testAutoselectedProductsFallBack($I, 'specials');
    }


    /** @group database_transaction */
    public function testCoreFetchProductWithDefaultProduct(ApiTester $I)
    {
        $I->wantTo("Test fetch a product");

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_ID1);
        $result = json_decode($response->getContent());
        $this->checkProductFormat($I, $result);
        $this->checkProductInfo($I, $result);
    }

    /** @group database_transaction */
    public function testCoreFetchProductWithDefaultProductI18n(ApiTester $I)
    {
        $I->wantTo("Test fetch a product i18n");

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_ID1 . $I->addLocaleFilter(self::LOCALE_FR_CA));
        $result = json_decode($response->getContent());
        $this->checkProductFormat($I, $result);
        $this->checkProductInfo($I, $result, self::LOCALE_FR_CA);
    }

    /** @group database_transaction */
    public function testCoreFetchProductWithDefaultProductWithWrongProduct(ApiTester $I)
    {
        $I->wantTo("Test that even if the product id is wrong the api returns autoselected product that corresponds to the user");

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_WRONG_ID);
        $result1 = json_decode($response->getContent());
        $this->checkProductFormat($I, $result1);

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_WRONG_ID);
        $result2 = json_decode($response->getContent());
        $this->checkProductFormat($I, $result2);

        if ($result1->product_id != $result2->product_id) {
            $I->assertTrue($result1->product_id != $result2->product_id);
        } else {
            $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_WRONG_ID);
            $result2 = json_decode($response->getContent());
            $this->checkProductFormat($I, $result2);
            $I->assertTrue($result1->product_id != $result2->product_id);
        }
    }

    /** @group database_transaction */
    public function testCoreFetchProductWithDefaultProductWithWrongProductI18n(ApiTester $I, $scenario)
    {
        $scenario->skip('flaky');

        $I->wantTo("Test that even if the product id is wrong the api returns autoselected product that corresponds to the user and the locale");

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_WRONG_ID . $I->addLocaleFilter(self::LOCALE_FR_CA));
        $result1 = json_decode($response->getContent());
        $this->checkProductFormat($I, $result1);
        $this->checkProductInfo($I, $result1, self::LOCALE_FR_CA);

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_WRONG_ID . $I->addLocaleFilter(self::LOCALE_FR_CA));
        $result2 = json_decode($response->getContent());
        $this->checkProductFormat($I, $result2);
        $this->checkProductInfo($I, $result2, self::LOCALE_FR_CA);

        if ($result1->product_id != $result2->product_id) {
            $I->assertTrue($result1->product_id != $result2->product_id);
        } else {
            $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/top-picks/" . self::PRODUCT_WRONG_ID . $I->addLocaleFilter(self::LOCALE_FR_CA));
            $result2 = json_decode($response->getContent());
            $this->checkProductFormat($I, $result2);
            $this->checkProductInfo($I, $result2, self::LOCALE_FR_CA);
            $I->assertTrue($result1->product_id != $result2->product_id);
        }
    }

    private function checkProductFormat(ApiTester $I, $result)
    {
        // Format
        $I->assertRegExpValue($I, 'string', $result->product_id);
        $I->assertRegExpValue($I, 'string', $result->name);
        $I->assertRegExpValue($I, 'string', $result->description);
        $I->assertRegExpValue($I, 'price', $result->price);
        $I->assertRegExpValue($I, 'price', $result->price_deal);
        $I->assertRegExpValue($I, 'price', $result->price_old);
        $I->assertRegExpValue($I, 'price', $result->regularPrice);
        $I->assertRegExpValue($I, 'price', $result->salePrice);
        $I->assertRegExpValue($I, 'price', $result->oldPrice);
        $I->assertRegExpValue($I, 'url', $result->productUrl);
        $I->assertRegExpValue($I, 'string', $result->vanityData);
        $I->assertRegExpValue($I, 'string', $result->vanity_data);
        $I->assertRegExpValue($I, 'string', $result->SaleEndDate);
        $I->assertRegExpValue($I, 'url', $result->img);
        $I->assertRegExpValue($I, 'url', $result->thumbnailImage);
        $I->assertRegExpValue($I, 'url', $result->img250);
        $I->assertRegExpValue($I, 'integer', $result->ehf);

        foreach ($result->additionalMedia as $media) {
            $I->assertRegExpValue($I, 'string', $media->mimeType);
            $I->assertRegExpValue($I, 'url', $media->thumbnailUrl);
            $I->assertRegExpValue($I, 'url', $media->url);
            $I->assertRegExpValue($I, 'url', $media->img250);
        }
    }

    private function checkProductInfo($I, $result, $locale = null)
    {
        $filters = ['product_id' => $result->product_id];

        $this->app['service.multilang']->setLocale($locale);

        $product = $this->app['products.manager']->getOne($filters, null, false);

        $I->assertEquals($product->name, $result->name);
        $I->assertEquals($product->description, $result->description);
        $I->assertEquals($product->price, $result->price);
        $I->assertEquals($product->price_deal, $result->price_deal);
        $I->assertEquals($product->price_old, $result->price_old);
        $I->assertEquals($product->vanity_data, $result->vanity_data);
        $I->assertEquals($product->deal_ratio, $result->deal_ratio);
        $I->assertEquals($product->name2, $result->name2);
        $I->assertEquals($product->img, $result->img);
        $I->assertEquals($product->img250, $result->img250);
        $I->assertEquals($product->productUrl, $result->productUrl);
        $I->assertEquals(0, $result->ehf);
        $I->assertEquals($product->additionalMedia[0]['mimeType'], $result->additionalMedia[0]->mimeType);
        $I->assertEquals($product->additionalMedia[0]['thumbnailUrl'], $result->additionalMedia[0]->thumbnailUrl);
        $I->assertEquals($product->additionalMedia[0]['url'], $result->additionalMedia[0]->url);
        $I->assertEquals($product->additionalMedia[0]['img250'], $result->additionalMedia[0]->img250);

        $I->assertEquals($result->price, $result->regularPrice);
        $I->assertEquals($result->price_deal, $result->salePrice);
        $I->assertEquals($result->price_old, $result->oldPrice);

        $this->checkI18nProduct($I, $locale, $product->name, $product->description);
    }

    private function testAutoselectedProductsFallBack(ApiTester $I, $type)
    {
        $nbProducts = $this->app['configs'][$this->panels[$type]['numberProductsConfig']];

        $products = $this->app['products.' . $type . '.manager']->getDefaultProducts(
            static::USER_STORE_ID,  // Use a user without specialties (dump.sql)
            static::USER_TYPE,
            $nbProducts,
            []
        );

        // Check if we have the correct number of results
        $I->assertEquals($nbProducts, count($products));
        // Check if all products are in the product catalog. We want default product even if rep has no specialty
        foreach ($products as $product) {
            $I->seeInDatabase('sf_products', ['product_id' => $product['product_sku']]);
        }
    }

    /** @group refresh_database */
    public function testCoreAutoselectedProductsNominatedProducts(ApiTester $I)
    {
        $I->wantTo("Happy Path for nominated products");

        // Enable nominated products and disable multi language
        $this->app['configs']->offsetSet('product.panels.new-arrivals.nominated-products', true);
        $this->app['configs']->offsetSet('product.panels.top-picks.nominated-products', true);
        $this->app['configs']->offsetSet('retailer.i18n.is_enabled', false);

        $this->app['s3.salesfloor'] = Stub::make(S3::class, [
            'downloadLatestFile' => function ($validationRules, $archivePrefix, $multiFile = false) {
                $tmpFile = tempnam('/tmp', 'codeception');
                copy(__DIR__ . rtrim($validationRules['regexp'], '/'), $tmpFile);
                return $tmpFile;
            },
            'getLastModifiedDate' => Carbon::now()->toDateString(),
        ]);
        // Import a first list of nominated products
        $this->app['products.nominated.importer']->import();
        $firstImport = $this->checkNominatedProductsImported($I, $this->nominatedProductsList);

        $this->changeNominatedProductsDate();

        // Import a second list of nominated products
        $this->app['products.nominated.importer']->import();
        $secondImport =  $this->checkNominatedProductsImported($I, $this->nominatedProductsList);

        // Verifications via the product manager
        $products = $this->app['products.new-arrivals.manager']->getDefaultProducts(
            static::USER_REGGIE_ID,
            static::USER_TYPE,
            $this->app['configs']['retailer.num_deals'],
            []
        );

        foreach ($this->nominatedProductsList as $productId => $nominatedProduct) {
            $nominatedProductOK = false;
            $comment = '';
            foreach ($products as $product) {
                if ($product['product_sku'] == $productId) {
                    $nominatedProductOK = true;
                    break;
                }
            }

            $I->assertTrue($nominatedProductOK);
        }
    }

    /** @group refresh_database */
    public function testCoreAutoselectedProductsNominatedProductsI18n(ApiTester $I)
    {
        $I->wantTo("Happy Path for nominated products i18n");

        // Enable nominated products
        $this->app['configs']->offsetSet('product.panels.new-arrivals.nominated-products', true);
        $this->app['configs']->offsetSet('retailer.i18n.is_enabled', true);

        $this->app['s3.salesfloor'] = Stub::make(S3::class, [
            'getLastModifiedDate' => Carbon::now()->toDateString(),
        ]);

        $this->app['products.nominated.importer']->import(__DIR__ . '/../../fixtures/nominated-products-i18n.csv', true);

        // We need to make sure we import nominated product correctly, with priority rules and that comments
        // are stored in sf_nominated_products or sf_nominated_products_i18n depending on the locale
        $this->checkNominatedProductsImported($I, $this->nominatedProductsListI18n);
    }

    /** @group refresh_database */
    public function testCoreAutoselectedProductsNominatedProductsFilesUnchanged(ApiTester $I)
    {
        $I->wantTo("Making sure we don't import if file is unchanged");

        // Enable nominated products
        $this->app['configs']->offsetSet('product.panels.new-arrivals.nominated-products', true);
        $this->app['configs']->offsetSet('retailer.i18n.is_enabled', false);

        $this->app['s3.salesfloor'] = Stub::make(S3::class, [
            'downloadLatestFile' => function ($validationRules, $archivePrefix, $multiFile = false) {
                $tmpFile = tempnam('/tmp', 'codeception');
                copy(__DIR__ . rtrim($validationRules['regexp'], '/'), $tmpFile);
                return $tmpFile;
            },
            'getLastModifiedDate' => Carbon::now()->toDateString(),
        ]);

        $this->app['products.nominated.importer']->import();
        $firstImport = $this->checkNominatedProductsImported($I, $this->nominatedProductsList);

        $nominatedProductModificationDate = $I->grabFromDatabase('sf_nominated_products', 'modification_date', array('id' => 1));
        $dateDiff = $this->dateDiff($nominatedProductModificationDate, date('Y-m-d'));

        $this->app['configs']->offsetSet('product.panels.refresh.autoselected.frequency', ($dateDiff->days + 1));

        $this->app['products.nominated.importer']->import();
        $secondImport =  $this->checkNominatedProductsImported($I, $this->nominatedProductsList);

        $I->assertTrue($firstImport == $secondImport);
    }

    protected function dateDiff($date1, $date2)
    {
        $date1 = new \DateTime($date1);
        $date2 = new \DateTime($date2);
        return $date1->diff($date2);
    }

    /** @group refresh_database */
    public function testCoreAutoselectedProductsNominatedProductsAutoSelectedUpdated(ApiTester $I)
    {
        $I->wantTo("Making sure we update autoselected every 2 weeks");

        // Enable nominated products
        $this->app['configs']->offsetSet('product.panels.new-arrivals.nominated-products', true);
        $this->app['configs']->offsetSet('retailer.i18n.is_enabled', false);

        $this->app['s3.salesfloor'] = Stub::make(S3::class, [
            'downloadLatestFile' => function ($validationRules, $archivePrefix, $multiFile = false) {
                $tmpFile = tempnam('/tmp', 'codeception');
                copy(__DIR__ . rtrim($validationRules['regexp'], '/'), $tmpFile);
                return $tmpFile;
            },
            'getLastModifiedDate' => Carbon::now()->toDateString(),
        ]);

        $this->app['products.nominated.importer']->import();
        $firstImport = $this->checkNominatedProductsImported($I, $this->nominatedProductsList);

        $this->app['configs']->offsetSet('product.panels.refresh.autoselected.frequency', -1);

        $this->app['products.nominated.importer']->import(__DIR__ . '/../../fixtures/nominated-products.csv', true);
        $secondImport = $this->checkNominatedProductsImported($I, $this->nominatedProductsList2);

        if ($firstImport != $secondImport) {
            $I->assertTrue($firstImport != $secondImport);
        } else {
            $this->app['products.nominated.importer']->import();
            $secondImport =  $this->checkNominatedProductsImported($I, $this->nominatedProductsList);
            $I->assertTrue($firstImport != $secondImport);
        }
    }

    private function changeNominatedProductsDate()
    {
        $products = $this->app['products.nominatedproducts.manager']->getAll([], 0, -1, false);
        foreach ($products as $product) {
            $product->modification_date = '1970-01-01';
            $this->app['products.nominatedproducts.manager']->save($product);
        }
    }

    protected function checkNominatedProductsImported(ApiTester $I, $expectedResults)
    {
        $results = [];

        foreach ($expectedResults as $productId => $product) {
            $filters = [
                'product_id' => $productId,
                'type'       => $product['type']
            ];

            $id       = $I->grabFromDatabase('sf_nominated_products', 'id', $filters);
            $comment  = $I->grabFromDatabase('sf_nominated_products', 'comment', $filters);
            $priority = $I->grabFromDatabase('sf_nominated_products', 'priority', $filters);
            $results[] = $id;

            $I->seeInDatabase('sf_nominated_products', $filters);
            $this->checkPriority($I, $product['priority'], $priority);
            $this->checkComment($I, $product['comment'], $comment, $id);
        }

        // This following product id (from nominated-products.csv in S3 bucket sf-tests) should not be imported
        // as it's not a real product in the product catalog
        $filters = [
            'product_id' => 'what is this product?'
        ];

        $I->dontSeeInDatabase('sf_nominated_products', $filters);
        return $results;
    }

    private function checkPriority(ApiTester $I, $expectedPriority, $priority)
    {
        if (is_array($expectedPriority)) {
            $I->assertTrue(in_array($priority, $expectedPriority));
        } else {
            $I->assertEquals($expectedPriority, $priority);
        }
    }

    private function checkComment(ApiTester $I, $expectedComment, $comment, $id)
    {
        if (is_array($expectedComment)) {
            foreach ($expectedComment as $locale => $text) {
                if ($locale == $this->app['configs']['retailer.i18n.default_locale']) {
                    $I->assertEquals($text, $comment);
                } else {
                    $I->seeInDatabase('sf_nominated_products_i18n', [
                        'id'          => $id,
                        'locale'      => $locale,
                        'comment'     => $text,
                    ]);
                }
            }
        } else {
            $I->assertEquals($expectedComment, $comment);
        }
    }

    private function testSavePanelI18n(ApiTester $I, $type)
    {
        $I->wantTo("Test route to save $type i18n");

        $params = $this->fixturesSave[$type];

        // Overwriting custom comments from fixtures with multilanguage version
        $params[0]['description'] = [
            'en_US' => 'comment en_US',
            'fr_CA' => 'comment fr_CA'
        ];

        // Overwriting custom comments from fixtures with multilanguage version
        $params[2]['description'] = [
            'en_US' => 'bla bla bla en_US',
            'fr_CA' => 'bla bla bla fr_CA'
        ];

        $response = $I->doDirectPost($this->app, "reps/" . self::USER_REGGIE_ID . "/products/" . $type, $params);
        $I->assertEquals(200, $response->getStatusCode());

        foreach (['en_US', 'fr_CA'] as $locale) {
            $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/" . $type . '?sf_locale=' . $locale);
            $results = json_decode($response->getContent());
            foreach ($results as $result) {
                if (isset($result->page) && isset($result->per_page)) {
                    $this->checkPagination($I, $result);
                } else {
                    $this->checkFetchFormatResponse($I, $result);
                    $this->checkSavedProduct($I, $result, $params, $locale);
                }
            }
        }
    }

    private function testSavePanel(ApiTester $I, $type)
    {
        $I->wantTo("Test route to save $type");

        $params = $this->fixturesSave[$type];

        $response = $I->doDirectPost($this->app, "reps/" . self::USER_REGGIE_ID . "/products/" . $type, $params);
        $I->assertEquals(200, $response->getStatusCode());
        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/" . $type);
        $results = json_decode($response->getContent());

        foreach ($results as $result) {
            if (isset($result->page) && isset($result->per_page)) {
                $this->checkPagination($I, $result);
            } else {
                $this->checkFetchFormatResponse($I, $result);
                $this->checkSavedProduct($I, $result, $params);
            }
        }
    }

    /** @group database_transaction */
    public function testSavePanelStoreMode(ApiTester $I)
    {
        $I->wantTo("Test route to save in store mode");

        // Enable store mode
        $this->app['configs']->offsetSet('retailer.storepage_mode', true);

        $params = $this->fixturesSave['store'];

        // We test store mode only for new arrival (code is the same for all panels)
        // We can't use the route as we can't force store mode. That's why we test using the manager
        $responses = $this->app['products.new-arrivals.manager']->saveMany($this->createModelsFromFixtures($params, 'new-arrivals'));
        // We want the format returned by mapper to get same response structure than using the route
        $results = $this->getResponseViaMapper($responses);

        foreach ($results as $result) {
            $this->checkFetchFormatResponse($I, $result);
            $this->checkSavedProduct($I, $result, $params);
        }
    }

    /** @group database_transaction */
    public function testSavePanelStoreModeI18n(ApiTester $I)
    {
        $I->wantTo("Test route to save in store mode i18n");

        // Enable store mode
        $this->app['configs']->offsetSet('retailer.storepage_mode', true);

        $params = $this->fixturesSave['store'];

        $params[0]['description'] = [
            'en_US' => 'This is a test en_US',
            'fr_CA' => 'This is a test fr_CA'
        ];

        $params[2]['description'] = [
            'en_US' => 'comment 2 en_US',
            'fr_CA' => 'comment 2 fr_CA'
        ];

        // We test store mode only for new arrival (code is the same for all panels)
        // We can't use the route as we can't force store mode. That's why we test using the manager
        $responses = $this->app['products.new-arrivals.manager']->saveMany($this->createModelsFromFixtures($params, 'new-arrivals'));
        // We want the format returned by mapper to get same response structure than using the route
        $results = $this->getResponseViaMapper($responses);

        foreach (['en_US', 'fr_CA'] as $locale) {
            $this->app['products.new-arrivals.manager']->setForcedLocale($locale);
            $results = $this->app['products.new-arrivals.manager']->getAll([
                'user_id' => self::USER_REGGIE_ID
            ], 0, 8);

            $results = $this->getResponseViaMapper($results);
            foreach ($results as $result) {
                if (isset($result->page) && isset($result->per_page)) {
                    $this->checkPagination($I, $result);
                } else {
                    $this->checkFetchFormatResponse($I, $result);
                    $this->checkSavedProduct($I, $result, $params, $locale);
                }
            }
        }
    }


    /** @group database_transaction */
    public function testRecommendationTopPicks(ApiTester $I)
    {
        $I->wantTo("Test recommendation on Top picks panel");

        $type = 'top-picks';
        $origin = 'webserver';

        $params = $this->fixturesSave['recommendation'][$type][0];

        $response = $I->doDirectPostWithHeaders(
            $this->app,
            "reps/" . self::USER_REGGIE_ID . "/products/" . $type,
            $params,
            [
                'SF-Origin' => $origin,
            ]
        );
        $results = json_decode($response->getContent());

        /**
         * We should have 1 new recommendation with 2 new products
         * @var \Salesfloor\Models\Event $event
         */
        $events = $this->app['events.manager']->getAll([
            'user_id' => self::USER_REGGIE_ID,
            'type' => $this->panels[$type]['type'],
            'source' => $origin,
        ], 0, -1, false);

        // We should have only one new event
        $I->assertCount(1, $events);

        $event = reset($events);

        $I->assertEquals($this->panels[$type]['type'], $event->type);
        $I->assertEquals($origin, $event->source);
        $I->assertEquals(1, $event->attributes);

        // we should have 2 new product recommendations
        $recommendations = $this->app['product_recommendations.manager']->getAll([
            'event_id' => $event->id,
        ], 0, -1, false);

        $I->assertCount(2, $recommendations);

        $first = $recommendations[0];
        $second = $recommendations[1];

        $I->assertEquals('54640cd49acae0386f777604ce15ab92', $first->product_id);
        $I->assertEquals('8b22196d3cb34f69ef0c5f5e06cb1c08', $second->product_id);

        // Update scenario
        $params = $this->fixturesSave['recommendation'][$type][1];

        $response = $I->doDirectPostWithHeaders(
            $this->app,
            "reps/" . self::USER_REGGIE_ID . "/products/" . $type,
            $params,
            [
                'SF-Origin' => $origin,
            ]
        );
        $results = json_decode($response->getContent());

        $events = $this->app['events.manager']->getAll([
            'user_id' => self::USER_REGGIE_ID,
            'type' => $this->panels[$type]['type'],
            'source' => $origin,
        ], 0, -1, false);

        // We should have only one new events
        $I->assertCount(2, $events);

        $event = $events[1];

        $recommendations = $this->app['product_recommendations.manager']->getAll([
            'event_id' => $event->id,
        ], 0, -1, false);

        $first = $recommendations[0];

        $I->assertCount(1, $recommendations);
        $I->assertEquals('174d4437287e61204b5bc859a6c6f824', $first->product_id);
    }

    /** @group database_transaction */
    public function testRecommendationNewArrivals(ApiTester $I)
    {
        $I->wantTo("Test recommendation on New arrivals panel");

        $type = 'new-arrivals';
        $origin = 'mobile';

        $params = $this->fixturesSave['recommendation'][$type][0];

        $response = $I->doDirectPostWithHeaders(
            $this->app,
            "reps/" . self::USER_REGGIE_ID . "/products/" . $type,
            $params,
            [
                'SF-Origin' => $origin,
            ]
        );
        $results = json_decode($response->getContent());

        /**
         * We should have 1 new recommendation with 2 new products
         * @var \Salesfloor\Models\Event $event
         */
        $events = $this->app['events.manager']->getAll([
            'user_id' => self::USER_REGGIE_ID,
            'type' => $this->panels[$type]['type'],
            'source' => $origin,
        ], 0, -1, false);

        // We should have only one new event
        $I->assertCount(1, $events);

        $event = reset($events);

        $I->assertEquals($this->panels[$type]['type'], $event->type);
        $I->assertEquals($origin, $event->source);
        $I->assertEquals(1, $event->attributes);

        $recommendations = $this->app['product_recommendations.manager']->getAll([
            'event_id' => $event->id,
        ], 0, -1, false);

        $I->assertCount(2, $recommendations);

        $first = $recommendations[0];
        $second = $recommendations[1];

        $I->assertEquals('82e3dc717c84286d7c99fd1f9d6dd398', $first->product_id);
        $I->assertEquals('e1d6bfff6c8f2ae59b9eac0f05d80214', $second->product_id);

        // Update with already existing products
        $params = $this->fixturesSave['recommendation'][$type][0];

        $response = $I->doDirectPostWithHeaders(
            $this->app,
            "reps/" . self::USER_REGGIE_ID . "/products/" . $type,
            $params,
            [
                'SF-Origin' => $origin,
            ]
        );
        $results = json_decode($response->getContent());

        $events = $this->app['events.manager']->getAll([
            'user_id' => self::USER_REGGIE_ID,
            'type' => $this->panels[$type]['type'],
            'source' => $origin,
        ], 0, -1, false);

        $I->assertCount(1, $events);

        // Update with a new ORIGIN and new product
        $params = $this->fixturesSave['recommendation'][$type][1];

        $newOrigin = 'webserver';
        $response = $I->doDirectPostWithHeaders(
            $this->app,
            "reps/" . self::USER_REGGIE_ID . "/products/" . $type,
            $params,
            [
                'SF-Origin' => $newOrigin,
            ]
        );
        $results = json_decode($response->getContent());

        $events = $this->app['events.manager']->getAll([
            'user_id' => self::USER_REGGIE_ID,
            'type' => $this->panels[$type]['type'],
        ], 0, -1, false);

        $I->assertCount(2, $events);
    }

    /** @group database_transaction */
    public function testRecommendationSpecials(ApiTester $I)
    {
        $I->wantTo("Test recommendation with specials panel");

        $type = 'specials';
        $origin = 'mobile';

        $params = $this->fixturesSave[$type];

        $response = $I->doDirectPostWithHeaders(
            $this->app,
            "reps/" . self::USER_REGGIE_ID . "/products/" . $type,
            $params,
            [
                'SF-Origin' => $origin,
            ]
        );
        $results = json_decode($response->getContent());

        $events = $this->app['events.manager']->getAll([
            'user_id' => self::USER_REGGIE_ID,
        ], 0, -1, false);

        $I->assertCount(2, $events);
    }

    /** @group database_transaction */
    public function testRecommendationsI18n(ApiTester $I)
    {
        $I->wantTo("Test recommendation with i18n");

        $I->haveInDatabase(
            'sf_trending_recommendations',
            [
                'product_id' => '54640cd49acae0386f777604ce15ab92',
                'n_recommendations' => 4,
            ]
        );

        $I->haveInDatabase(
            'sf_trending_recommendations',
            [
                'product_id' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'n_recommendations' => 5,
            ]
        );

        $I->haveInDatabase(
            'sf_trending_recommendations',
            [
                'product_id' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'n_recommendations' => 7,
            ]
        );

        $response = $I->doDirectGet($this->app, "recommendations" . $I->addLocaleFilter(self::LOCALE_FR_CA));
        $results = json_decode($response->getContent());

        foreach ($results as $result) {
            if (isset($result->product_data)) {
                $this->checkI18nProduct($I, self::LOCALE_FR_CA, $result->product_data->name, $result->product_data->description);
            }
        }

        $response = $I->doDirectGet($this->app, "recommendations/" . self::USER_REGGIE_ID . $I->addLocaleFilter(self::LOCALE_FR_CA));
        $results = json_decode($response->getContent());

        foreach ($results as $result) {
            if (isset($result->product_data)) {
                $this->checkI18nProduct($I, self::LOCALE_FR_CA, $result->product_data->name, $result->product_data->description);
            }
        }
    }

    private function getResponseViaMapper($responses)
    {
        $results = [];
        foreach ($responses as $response) {
            $results[] = $this->app['mapper']->map('ProductPanel', $response, 'api-response');
        }

        return json_decode(json_encode($results), false);
    }

    private function createModelsFromFixtures($params, $type)
    {
        $models = [];
        foreach ($params as $param) {
            $models[] = $this->createPanel($param, $type);
        }

        return $models;
    }

    private function createPanel($param, $type)
    {
        $classPath = "Salesfloor\\Models\\Products";
        $className = $classPath . '\\' . ($type == 'new-arrivals' ? 'Deal' : 'Product');
        $model = new $className();
        $model->user_id = $param['user_id'];
        $model->from_user_id = $param['from_user_id'];
        if (is_string($param['description'])) {
            $model->description = $param['description'];
        } else {
            $model->description = (object)$param['description'];
        }
        $model->product_sku = $param['sku'];
        $model->position = $param['position'];
        $model->date = date('Y-m-d H:i:s');
        $model->autoselected = $param['autoselected'];

        return $model;
    }

    private function testFetchPanel(ApiTester $I, $type, $firstTime = false, $locale = null)
    {
        $I->wantTo("Test route to fetch $type for first time");

        $response = $I->doDirectGet($this->app, "reps/" . self::USER_REGGIE_ID . "/products/" . $type . $I->addLocaleFilter($locale));
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        foreach ($results as $result) {
            if (isset($result->page) && isset($result->per_page)) {
                $this->checkPagination($I, $result);
            } else {
                $this->checkFetchFormatResponse($I, $result);
                if ($firstTime) {
                    $this->checkFetchDataFirstTimeResponse($I, $result, $type, $locale);
                } else {
                    $this->checkFetchDataResponse($I, $result, $type, $locale);
                }
            }
        }

        return $results;
    }

    private function checkSavedProduct(ApiTester $I, $result, $params, $locale = null)
    {
        $param = $params[$result->position];

        if ($param['autoselected'] == 1) {
            $this->checkAutoSelectedSavedProductIsOk($I, $result, $param, $locale);
        } else {
            $this->checkSelectedSavedProductIsOk($I, $result, $param, $locale);
        }
    }

    private function checkAutoSelectedSavedProductIsOk(ApiTester $I, $result, $param, $locale = null)
    {
        $I->assertRegExpValue($I, 'string', $result->product_sku);
        $I->assertEquals(0, $result->from_user_id);
        $I->assertEquals($param['position'], $result->position);

        $defaultComment = $this->app['products.defaultcomments']->get($result->product_sku);

        if (is_string($result->description) && is_string($defaultComment)) {
            $I->assertRegExpValue($I, 'string', $result->description);
            $I->assertEquals($defaultComment, $result->description);
        } elseif (!empty($locale)) {
            $I->assertEquals($defaultComment->{$locale}, $result->description);
        }
    }

    private function checkSelectedSavedProductIsOk(ApiTester $I, $result, $param, $locale = null)
    {
        $I->assertEquals($param['sku'], $result->product_sku);
        $I->assertEquals($param['from_user_id'], $result->from_user_id);
        $I->assertEquals($param['position'], $result->position);

        $defaultComment = $this->app['products.defaultcomments']->get($result->product_sku);

        // Default Comment
        if (empty($param['description'])) {
            if (is_string($result->description) && is_string($defaultComment)) {
                $I->assertEquals($defaultComment, $result->description);
            } elseif (!empty($locale)) {
                $I->assertEquals($defaultComment->$locale, $result->description);
            }
        } else {
            if (is_string($param['description']) && is_string($result->description)) {
                $I->assertEquals($param['description'], $result->description);
            } elseif (!empty($locale)) {
                $I->assertEquals($param['description'][$locale], $result->description);
            }
        }
    }

    private function testDeletePanel(ApiTester $I, $type)
    {
        $this->testSavePanel($I, $type);
        $params = $this->fixturesSave[$type];

        $positiontoDelete = 0;
        $paramToTest = [];

        foreach ($params as $param) {
            if ($param['autoselected'] == 0) {
                $positiontoDelete = $param['position'];
                $paramToTest = $param;
                break;
            }
        }

        $response = $I->doDirectDelete($this->app, "reps/" . self::USER_REGGIE_ID . "/products/" . $type . '/' . $positiontoDelete);
        $results = $this->testFetchPanel($I, $type);

        foreach ($results as $result) {
            if ($result->position == $positiontoDelete) {
                $this->checkAutoSelectedSavedProductIsOk($I, $result, $paramToTest);
                break;
            }
        }
    }

    private function checkFetchFormatResponse(ApiTester $I, $result)
    {
        // Format
        $I->assertRegExpValue($I, 'integer', $result->from_user_id);
        $I->assertRegExpValue($I, 'string', $result->note);
        $I->assertRegExpValue($I, 'string', $result->description);
        $I->assertRegExpValue($I, 'string', $result->product_sku);
        $I->assertRegExpValue($I, 'integer', $result->position);
        $I->assertRegExpValue($I, 'mysqlDate', $result->mtime);
        $I->assertRegExpValue($I, 'boolean', $result->autoselected);
        $I->assertRegExpValue($I, 'string', $result->product_data->product_id);
        $I->assertRegExpValue($I, 'string', $result->product_data->name);
        $I->assertRegExpValue($I, 'string', $result->product_data->description);
        $I->assertRegExpValue($I, 'price', $result->product_data->price);
        $I->assertRegExpValue($I, 'price', $result->product_data->price_deal);
        $I->assertRegExpValue($I, 'string', $result->product_data->vanity_data);
        $I->assertRegExpValue($I, 'integer', $result->product_data->deal_ratio);
        $I->assertRegExpValue($I, 'string', $result->product_data->name2);
        $I->assertRegExpValue($I, 'string', $result->product_data->sku);
        $I->assertRegExpValue($I, 'price', $result->product_data->regularPrice);
        $I->assertRegExpValue($I, 'price', $result->product_data->salePrice);
        $I->assertRegExpValue($I, 'url', $result->product_data->productUrl);
        $I->assertRegExpValue($I, 'string', $result->product_data->SaleEndDate);
        $I->assertRegExpValue($I, 'string', $result->product_data->vanityData);
        $I->assertRegExpValue($I, 'url', $result->product_data->img);
        $I->assertRegExpValue($I, 'url', $result->product_data->img250);
        $I->assertRegExpValue($I, 'string', $result->product_data->additionalMedia[0]->mimeType);
        $I->assertRegExpValue($I, 'url', $result->product_data->additionalMedia[0]->thumbnailUrl);
        $I->assertRegExpValue($I, 'url', $result->product_data->additionalMedia[0]->url);
        $I->assertRegExpValue($I, 'url', $result->product_data->additionalMedia[0]->img250);
        $I->assertRegExpValue($I, 'string', $result->from);
        $I->assertRegExpValue($I, 'string', $result->name);
        $I->assertRegExpValue($I, 'string', $result->user_login);

        // Additional structure validation (some info are duplicated on purpose for mobile and platform)
        $I->assertEquals($result->note, $result->description);
        $I->assertEquals($result->product_data->price, $result->product_data->regularPrice);
        $I->assertEquals($result->product_data->price_deal, $result->product_data->salePrice);
        $I->assertEquals($result->product_data->product_id, $result->product_data->sku);
    }

    private function checkFetchDataFirstTimeResponse(ApiTester $I, $result, $type, $locale = null)
    {
        $I->assertEquals($result->autoselected, 1);
        $this->checkFetchDataResponse($I, $result, $type, $locale);
    }

    private function checkFetchDataResponse(ApiTester $I, $result, $type, $locale = null)
    {
        $numberOfProducts = $this->app['configs'][$this->panels[$type]['numberProductsConfig']];
        // Check if position id between 0 and max position
        $I->assertTrue($result->position >= 0 && $result->position < $numberOfProducts);

        $this->checkProductPanelInfo($I, $result, $type, $locale);
        $this->checkProductInfoInPanel($I, $result, $type, $locale);
    }

    private function checkProductPanelInfo($I, $result, $type, $locale = null)
    {
        $filters = [
            'position' => (string)$result->position,
            'user_id' => self::USER_REGGIE_ID,
        ];

        $this->app['service.multilang']->setLocale($locale);

        $productPanel = $this->app[$this->panels[$type]['manager']]->getOne(
            $filters,
            null,
            false
        );

        $I->assertEquals($productPanel->product_sku, $result->product_sku);
        // Not sure what we should return from_user_id or user_id?
        $I->assertEquals($productPanel->from_user_id, $result->from_user_id);
        $I->assertEquals($productPanel->description, $result->description);
        $I->assertEquals($productPanel->date, $result->mtime);
        $I->assertEquals($productPanel->autoselected, $result->autoselected);
    }

    private function checkProductInfoInPanel($I, $result, $type, $locale = null)
    {
        $filters = ['product_id' => $result->product_sku];

        $this->app['service.multilang']->setLocale($locale);

        $product = $this->app['products.manager']->getOne($filters, null, false);

        $I->assertEquals($product->name, $result->product_data->name);
        $I->assertEquals($product->description, $result->product_data->description);
        $I->assertEquals($product->price, $result->product_data->price);
        $I->assertEquals($product->price_deal, $result->product_data->price_deal);
        $I->assertEquals($product->price_old, $result->product_data->price_old);
        $I->assertEquals($product->vanity_data, $result->product_data->vanity_data);
        $I->assertEquals($product->deal_ratio, $result->product_data->deal_ratio);
        $I->assertEquals($product->name2, $result->product_data->name2);
        $I->assertEquals($product->img, $result->product_data->img);
        $I->assertEquals($product->img250, $result->product_data->img250);
        $I->assertEquals($product->productUrl, $result->product_data->productUrl);
        $I->assertEquals($product->additionalMedia[0]['mimeType'], $result->product_data->additionalMedia[0]->mimeType);
        $I->assertEquals($product->additionalMedia[0]['thumbnailUrl'], $result->product_data->additionalMedia[0]->thumbnailUrl);
        $I->assertEquals($product->additionalMedia[0]['url'], $result->product_data->additionalMedia[0]->url);
        $I->assertEquals($product->additionalMedia[0]['img250'], $result->product_data->additionalMedia[0]->img250);


        $this->checkI18nProduct($I, $locale, $result->product_data->name, $result->product_data->description);
    }

    private function checkPagination(ApiTester $I, $result)
    {
        $I->assertRegExp('/' . $I->getRegExp('integer') . '/', (string) $result->page);
        $I->assertRegExp('/' . $I->getRegExp('integer') . '/', (string) $result->per_page);
        $I->assertRegExp('/' . $I->getRegExp('integer') . '/', (string) $result->count);
        $I->assertRegExp('/' . $I->getRegExp('integer') . '/', (string) $result->pages);
    }

    private function checkI18nProduct($I, $locale, $name, $description)
    {
        if (!empty($locale)) {
            $I->assertTrue((bool)preg_match('/\(' . $locale . '\)/', $name));
            $I->assertTrue((bool)preg_match('/\(' . $locale . '\)/', $description));
        }
    }
}
