<?php

namespace SF\api\lookbook;

use SF\ApiTester;
use SF\api\BaseApi;

class LookbookCest extends BaseApi
{
    public const LOCALE_FR_CA  = 'fr_CA';

    /**
     * PP-165 - Add Lookbook metrics in Store and Associate Activity report
     * Tests Lookbook creation trackking
     * @param ApiTester $I
     * @return mixed|null
     *
     * @group database_transaction
     */
    public function testLookbookCreationTracking(ApiTester $I)
    {
        $I->wantTo("Test the tracking of a lookbook creation");
        $resultLookbook = $this->testPublishLookBook($I);

        // two type of events will be generated: type 90 and type 120
        $I->canSeeNumRecords(2, 'sf_events');

        $I->seeInDatabase(
            'sf_events',
            array(
                // 'ID' => $resultLookbook->data->id,
                'type' => 90,
                'user_id' => 1
            )
        );

        $I->seeInDatabase('sf_events', ['type' => 120, 'user_id' => 1]);

        return $resultLookbook;
    }

    /**
     * @group database_transaction
     */
    public function testPublishLookBookProductRecommendation(ApiTester $I)
    {
        $I->wantTo("Test the product recommendation of a lookbook creation");
        $resultLookbook = $this->testPublishLookBook($I);

        $I->canSeeNumRecords(1, 'sf_product_recommendations');

        return $resultLookbook;
    }

    /**
     * PP-165 - Add Lookbook metrics in Store and Associate Activity report
     * Tests Lookbook update trackking
     * @param ApiTester $I
     * @return mixed|null
     *
     * @group database_transaction
     */
    public function testLookbookUpdateTracking(ApiTester $I)
    {
        $I->wantTo("Test the tracking of a lookbook update");
        $resultLookbook = $this->testLookbookCreationTracking($I);

        $data = [
            'lookbook_id' => $resultLookbook->data->id,
            'products'       => [
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => [], // you don't need this for creation of a look
                    'product_sku' => '54640cd49acae0386f777604ce15ab92',
                ],
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 1,
                    'position' => 1,
                    'product_data' => [], // you don't need this for creation of a look
                    'product_sku' => '54640cd49acae0386f777604ce15ab91',
                ],
            ],
            'rep_id'      => 1,
            'title'       => "title #2",
            'description' => "description #2",
        ];

        $response = $I->doDirectPut($this->app, "looks/{$resultLookbook->data->id}", $data);
        $resultLook = json_decode($response->getContent());

        $data = [
            'customer_id' => 1,
            'id' => $resultLookbook->data->id,
            'fragment' => $resultLookbook->data->fragment,
            'looks' => [
                json_decode(json_encode($resultLook->data), true),
            ]
        ];

        $response = $I->doDirectPut($this->app, "lookbooks/{$resultLookbook->data->id}?publish=1", $data);
        $I->canSeeNumRecords(4, 'sf_events');
        $I->seeInDatabase('sf_events', array(
            'type' => 90,
            'user_id' => 1,
            'attributes' => "{\"lookbook_id\":\"{$resultLookbook->data->id}\"}"
        ));
        $I->seeInDatabase('sf_events', array(
            'type' => 91,
            'user_id' => 1,
            'attributes' => "{\"lookbook_id\":\"{$resultLookbook->data->id}\"}"
        ));
    }


     /**
     * @param ApiTester $I
     * @return mixed|null
     *
     * @group database_transaction
     */
    public function testLookbookUpdateDuplicateProducts(ApiTester $I)
    {
        $data = [
            'customer_id' => 1,
            'looks'       => [],
            'rep_id'      => 1,
            'title'       => "",
        ];

        $response = $I->doDirectPost($this->app, 'lookbooks', $data);
        $resultLookbook = json_decode($response->getContent());

        $data = [
            'lookbook_id' => $resultLookbook->data->id,
            'products'       => [
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => [], // you don't need this for creation of a look
                    'product_sku' => '54640cd49acae0386f777604ce15ab92',
                ],
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => [],
                    'product_sku' => '54640cd49acae0386f777604ce15ab92', // duplicate
                ],
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => [],
                    'product_sku' => '54640cd49acae03868787878787',
                ],

                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => [],
                    'product_sku' => '54640cd49acae03868787878787', // duplicate
                ],
            ],
            'rep_id'      => 1,
            'title'       => "title #1",
            'description' => "description #1",
        ];

        $response = $I->doDirectPost($this->app, 'looks', $data);
        $resultLook = json_decode($response->getContent());

        $data = [
            'customer_id' => 1,
            'id' => $resultLookbook->data->id,
            'fragment' => $resultLookbook->data->fragment,
            'looks' => [
                json_decode(json_encode($resultLook->data), true),
            ]
        ];

        $response = $I->doDirectPut($this->app, "lookbooks/{$resultLookbook->data->id}?publish=1", $data);

        // The duplicate was removed and only two of them must be recorded
        $I->canSeeNumRecords(2, 'sf_product_recommendations');
    }


    /************************************************************************
     *
     *  REP MODE (Not the team mode)
     *
     ************************************************************************/

    /** @group database_transaction */
    public function testCreateLookBookRepFirstStep(ApiTester $I)
    {
        $I->wantTo("test to create lookbook in rep mode - from rep contact - with no look / no title");
        $data = [
            'customer_id' => 1,
            'looks'       => [],
            'rep_id'      => 1,
            'title'       => "",
        ];

        $response = $I->doDirectPost($this->app, 'lookbooks', $data);
        $result = json_decode($response->getContent());


        $this->testValidate($I, $result, $data);
    }

    /** @group database_transaction */
    public function testPublishLookBook(ApiTester $I)
    {
        $I->wantTo("test to publish lookbook");
        $data = [
            'customer_id' => 1,
            'looks'       => [],
            'rep_id'      => 1,
            'title'       => "",
        ];

        $response = $I->doDirectPost($this->app, 'lookbooks', $data);
        $resultLookbook = json_decode($response->getContent());

        $data = [
            'lookbook_id' => $resultLookbook->data->id,
            'products'       => [
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => [], // you don't need this for creation of a look
                    'product_sku' => '54640cd49acae0386f777604ce15ab92',
                ],
            ],
            'rep_id'      => 1,
            'title'       => "title #1",
            'description' => "description #1",
        ];

        $response = $I->doDirectPost($this->app, 'looks', $data);
        $resultLook = json_decode($response->getContent());

        $data = [
            'customer_id' => 1,
            'id' => $resultLookbook->data->id,
            'fragment' => $resultLookbook->data->fragment,
            'looks' => [
                json_decode(json_encode($resultLook->data), true),
            ]
        ];

        $response = $I->doDirectPut($this->app, "lookbooks/{$resultLookbook->data->id}?publish=1", $data);
        $resultLookbookPublish = json_decode($response->getContent());

        $this->testValidate($I, $resultLookbookPublish->data, array_merge($data, [
            // The fragment is trim because in the database in varchar(15), not sure if it's on purpose
            'fragment' => substr($resultLookbook->data->fragment, 0, 15),
            'customer' => [
                'user_id' => 1,
                'email' => '<EMAIL>',
                'first_name' => 'Joseph',
                'last_name' => 'Mallette',
                'name' => 'Joseph Mallette',
                'type' => 'corporate',
            ],
            'looks' => [
                'id' => $resultLookbook->data->id,
                'title' => 'title #1',
                'published' => true,
                'products' => [
                    'id' => $resultLookbookPublish->data->looks[0]->products[0]->id,
                    'product_sku' => '54640cd49acae0386f777604ce15ab92'
                ]
            ]
        ]));

        return $resultLookbook;
    }



    /** @group database_transaction */
    public function testUpdateLookBookRep(ApiTester $I)
    {
    }

    /** @group database_transaction */
    public function testDeleteLookBookRep(ApiTester $I)
    {
    }

    /** @group database_transaction */
    public function testCreateLookRep(ApiTester $I)
    {
        $I->wantTo("test to create look in rep mode - already attached to lookbook");

        // First create a lookbook
        $data = [
            'customer_id' => 1,
            'looks'       => [],
            'rep_id'      => 1,
            'title'       => "lookbook title",
        ];

        $response = $I->doDirectPost($this->app, 'lookbooks', $data);
        $result = json_decode($response->getContent());

        $productData = [
            'product_id'       => '54640cd49acae0386f777604ce15ab92',
            'name'             => 'ABANDON - MIEL VACHETTA',
            'description'      => 'It’s all about sexy simplicity this season: The summer staple is rejuvenated by way of luxurious leather laces designed to coil around the calf and is anchored by a woven wedge. A subtle, coordinating stitching detail stands out against a luxe leather trim. We love it with raw-hem jeans and an open-back shirt. Heel height : 3 ½&quot; Leather insole Rubber sole Made in Spain',
            'price'            => 698,
            'price_deal'       => 698,
            'price_old'        => 0,
            'vanity_data'      => '',
            'deal_ratio'       => 100,
            'name2'            => '',
            'sku'              => '54640cd49acae0386f777604ce15ab92',
            'shortDescription' => 'It’s all about sexy simplicity this season: The summer staple is rejuvenated by way of luxurious leather laces designed to coil around the calf and is anchored by a woven wedge. A subtle, coordinating stitching detail stands out against a luxe leather trim. We love it with raw-hem jeans and an open-back shirt. Heel height : 3 ½&quot; Leather insole Rubber sole Made in Spain',
            'regularPrice'     => 698,
            'salePrice'        => 698,
            'oldPrice'         => 0,
            'productUrl'       => 'http://www.stuartweitzman.ca/eng/products/abandon/miel-vachetta/',
            'SaleEndDate'      => '',
            'vanityData'       => '',
            'img'              => 'http://www.stuartweitzman.ca/assets/item/regular/abandon_mievac_12.jpg',
            'thumbnailImage'   => 'https://res.cloudinary.com/salesfloor-net/image/fetch/s--WBrvnXcP--/f_auto/http://www.stuartweitzman.ca/assets/item/regular/abandon_mievac_12.jpg',
            'img250'           => 'https://res.cloudinary.com/salesfloor-net/image/fetch/s--S3wmdVNl--/f_auto,q_90,w_250/http://www.stuartweitzman.ca/assets/item/regular/abandon_mievac_12.jpg',
        ];

        $data = [
            'lookbook_id' => $result->data->id,
            'products'       => [
                [
                    'description' => '',
                    'from' => '',
                    'from_user_id' => 1,
                    'id' => 0,
                    'position' => 0,
                    'product_data' => $productData,
                    'product_sku' => '54640cd49acae0386f777604ce15ab92',
                ],
            ],
            'rep_id'      => 1,
            'title'       => "title #1",
            'description' => "description #1",
        ];

        $response = $I->doDirectPost($this->app, 'looks', $data);
        $result = json_decode($response->getContent());

        $this->testValidate($I, $result->data, array_merge($data, [
            'products' => [[
                               'id'           => $result->data->products[0]->id,
                               'position'     => 0,
                               'note'         => '',
                               'description'  => '',
                               'product_sku'  => '54640cd49acae0386f777604ce15ab92',
                               'product_data' => $productData,
                               'from'         => '',
                               'from_user_id' => '',
                           ],
            ],
        ]));
    }

    /**
     * This test doesn't work without multilang enabled, and we only have one test retailer.
     * We have to solve the problem of test api only representing one of our many possible retailer configs.
     */
    /** @group database_transaction */
    public function testCoreFetchLookBookI18n(ApiTester $I)
    {
        $this->testCreateLookRep($I);
        $response = $I->doDirectGet($this->app, 'lookbooks/1' . $I->addLocaleFilter(self::LOCALE_FR_CA));
        $results = json_decode($response->getContent());
        foreach ($results->data->looks as $look) {
            foreach ($look->products as $product) {
                $this->checkI18nProduct($I, self::LOCALE_FR_CA, $product->product_data->name, $product->product_data->description);
            }
        }
    }

    public function testPIIViewLookbook(ApiTester $I)
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'lookbooks_test_pii');

        $response = $I->doDirectGet($this->app, "reps/1/lookbooks");
        $result = json_decode($response->getContent());

        $I->assertEquals($result->data[0]->customer->email, 'jo****************@********or.net');
        $I->assertEquals($result->data[0]->customer->phone, '+1514****45');

        // Get a specific resource by id
        $response = $I->doDirectGet($this->app, "reps/1/lookbooks/3");
        $result = json_decode($response->getContent());

        $I->assertEquals($result->data->customer->email, 'jo****************@********or.net');
        $I->assertEquals($result->data->customer->phone, '+1514****45');
    }

    private function testValidate($I, $result, $input)
    {
        foreach ($result as $k => $r) {
            if (isset($input[$k])) {
                if (is_array($input[$k])) {
                    $this->testValidate($I, $r, $input[$k]);
                } else {
                    $I->assertEquals($r, $input[$k]);
                }
            }
        }
    }

    private function checkI18nProduct($I, $locale, $name, $description)
    {
        if (!empty($locale)) {
            $I->assertTrue((bool)preg_match('/\(' . $locale  . '\)/', $name));
            $I->assertTrue((bool)preg_match('/\(' . $locale  . '\)/', $description));
        }
    }
}
