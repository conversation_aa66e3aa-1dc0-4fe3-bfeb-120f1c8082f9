<?php

namespace SF\api\transactions;

use Codeception\Util\HttpCode;
use SF\api\BaseApi;
use SF\ApiTester;

class TransactionsCest extends BaseApi
{
    /** @group database_transaction */
    public function testAddingTransaction(ApiTester $I)
    {
        $I->wantTo("test that transaction can be added successfully.");

        $params = [
            'trx_id' => 'TRX1616094504507',
            'trx_detail_total' => '13',
            'product_id' => 'SKU16160945045071',
            'quantity' => '4',
            'units' => 'each',
            'trx_thread_id' => 'TRX1616094504507',
        ];

        $response = $I->doDirectPost($this->app, 'transactions/TRX1616094504507/items', $params);
        $result = json_decode($response->getContent());
        $I->assertEquals($params['trx_id'], $result->trx_id);
        $I->seeInDatabase('sf_rep_transaction_detail', ['id' => $result->id]);


        // Send same request again should get 400 error.
        $response = $I->doDirectPost($this->app, 'transactions/TRX1616094504507/items', $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(HttpCode::BAD_REQUEST, $response->getStatusCode());
        $I->assertEquals('Attempt to save duplicated entity', $result->error);
    }
}
