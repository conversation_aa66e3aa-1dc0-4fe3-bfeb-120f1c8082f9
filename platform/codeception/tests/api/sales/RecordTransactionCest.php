<?php

use SF\ApiTester;
use SF\api\BaseApi;

class RecordTransactionCest extends BaseApi
{
    private function initData(ApiTester $I)
    {
        $I->haveInDatabase(
            'sf_product_retailer_ids',
            [
                'retailer_id' => 'product_A',
                'product_id' => 'product_A'
            ]
        );
        $I->haveInDatabase(
            'sf_product_retailer_ids',
            [
                'retailer_id' => 'product_A_alt_id',
                'product_id' => 'product_A'
            ]
        );
        $I->haveInDatabase(
            'sf_product_retailer_ids',
            [
                'retailer_id' => 'product_B',
                'product_id' => 'product_B'
            ]
        );
    }

    /** @group database_transaction */
    public function testCoreRecordTransactionItem(ApiTester $I)
    {
        $this->initData($I);

        $trxId = 'CodecepTestTrx01';
        $trxDetails = [
            'trx_id' => $trxId,
            'trx_detail_total' => 123,
            'product_id' => 'product_A',
            'quantity' => 1,
            'units' => 'each',
        ];
        $response = $I->doDirectPost($this->app, "transactions/$trxId/items", $trxDetails);
        $I->seeInDatabase('sf_rep_transaction_detail', $trxDetails);
    }

    /** @group database_transaction */
    public function testCoreRecordTransactionItemWithAltProductId(ApiTester $I)
    {
        $this->initData($I);

        $trxId = 'CodecepTestTrx02';
        $trxDetails = [
            'trx_id' => $trxId,
            'trx_detail_total' => 123,
            'product_id' => 'product_A_alt_id',
            'quantity' => 1,
            'units' => 'each',
        ];
        $response = $I->doDirectPost($this->app, "transactions/$trxId/items", $trxDetails);
        $I->seeInDatabase('sf_rep_transaction_detail', array_merge($trxDetails, ['product_id' => 'product_A']));
    }

    /** @group database_transaction */
    public function testCoreRecordTransactionItemWithFakeProductId(ApiTester $I)
    {
        $this->initData($I);

        $trxId = 'CodecepTestTrx03';
        $trxDetails = [
            'trx_id' => $trxId,
            'trx_detail_total' => 123,
            'product_id' => 'product_C',
            'quantity' => 1,
            'units' => 'each',
        ];
        $response = $I->doDirectPost($this->app, "transactions/$trxId/items", $trxDetails);
        $I->seeInDatabase('sf_rep_transaction_detail', $trxDetails);
    }
}
