<?php

use SF\ApiTester;
use SF\api\BaseApi;
use Salesfloor\Services\Event;
use Symfony\Component\HttpFoundation\Response;

class EmailTrackingCest extends BaseApi
{
    /**
     * Event #25 still use tracker.php (old WP)
     *
     * I move the code to the api, but the email doesn't use it yet.
     *
     * We use sendgrid data anyway.
     *
     * @param ApiTester $I
     *
     * @group database_transaction
     */
    public function testRouteOpenEmailWithEncryptedUserId(ApiTester $I)
    {
        // Since it's a public route
        $encryptedUserId = base64_encode($this->app['security.crypto']->encrypt('1'));

        // We can't use doGetJson since we want the raw data and not json_encode the result
        /** @var Response $response */
        $response = $I->doDirectGet(
            $this->app,
            "tracking/email/open/$encryptedUserId"
        );
        $I->assertEquals(200, $response->getStatusCode());

        $I->assertEquals(
            base64_decode('R0lGODlhAQABAJAAAP8AAAAAACH5BAUQAAAALAAAAAABAAEAAAICBAEAOw=='),
            $response->getContent()
        );

        // Look if we have an event in the database
        $results = $this->app['events.manager']->getAll(
            [
                'type' => Event::SF_EVENT_MAIL_OPEN,
            ],
            0,
            -1,
            false
        );

        $I->assertCount(1, $results);
    }

    /** @group database_transaction */
    public function testRouteOpenEmailWithBadlyEncryptedUserId(ApiTester $I)
    {
        $encryptedUserId = base64_encode($this->app['security.crypto']->encrypt('1231'));

        // We can't use doGetJson since we want the raw data and not json_encode the result
        $response = $I->doDirectGet(
            $this->app,
            "tracking/email/open/$encryptedUserId"
        );
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertEquals(
            base64_decode('R0lGODlhAQABAJAAAP8AAAAAACH5BAUQAAAALAAAAAABAAEAAAICBAEAOw=='),
            $response->getContent()
        );

        // Look if we have an event in the database
        $results = $this->app['events.manager']->getAll(
            [
                'type' => Event::SF_EVENT_MAIL_OPEN,
            ],
            0,
            -1,
            false
        );

        $I->assertCount(0, $results);
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailWithUnknownUniqId(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = 'X';

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailWithBadRepId(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('111'));
        $uniqId         = 'X';

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailWithBadEmail(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("josephbaemail@"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = 'X';

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailSuccessfulWithNoCustomerId(ApiTester $I)
    {

        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = uniqid();

        $this->addEventEmailSent(
            $I,
            $uniqId
        );

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(204, $response->getStatusCode());

        // Now we need to look that the event (feedback) is there and satisfied++
        $events = $this->app['events.manager']->getAll(
            [
                'uniq_id' => $uniqId,
            ],
            0,
            -1,
            false
        );

        $I->assertCount(2, $events);

        $this->checkArray(
            $I,
            [
                [
                    'type'        => 24,
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'uniq_id'     => $uniqId,
                    'attributes'  => 1,
                    'event_id'    => $events[0]->event_id,
                    'satisfied'   => 1,
                ],
                [
                    'type'        => 1,
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'uniq_id'     => $uniqId,
                    'attributes'  => json_encode('<EMAIL>'),
                    'event_id'    => $events[1]->event_id,
                    'satisfied'   => 0,
                ],
            ],
            $events
        );
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailSuccessfulWithCustomerId(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = uniqid();

        $this->addEventEmailSent(
            $I,
            $uniqId,
            1,
            2 // <NAME_EMAIL>
        );

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(204, $response->getStatusCode());

        // Now we need to look that the event (feedback) is there and satisfied++
        $events = $this->app['events.manager']->getAll(
            [
                'uniq_id' => $uniqId,
            ],
            0,
            -1,
            false
        );

        $I->assertCount(2, $events);

        $this->checkArray(
            $I,
            [
                [
                    'type'        => 24,
                    'user_id'     => 1,
                    'customer_id' => 2,
                    'uniq_id'     => $uniqId,
                    'attributes'  => 1,
                    'event_id'    => $events[0]->event_id,
                    'satisfied'   => 1,

                ],
                [
                    'type'        => 1,
                    'user_id'     => 1,
                    'customer_id' => 2,
                    'uniq_id'     => $uniqId,
                    'attributes'  => json_encode('<EMAIL>'),
                    'event_id'    => $events[1]->event_id,
                    'satisfied'   => 0,
                ],
            ],
            $events
        );
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailSuccessfulDuplicateWithCustomerId(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = uniqid();

        $this->addEventEmailSent(
            $I,
            $uniqId,
            1,
            2 // <NAME_EMAIL>
        );

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(204, $response->getStatusCode());

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $results = json_decode($response->getContent());

        $I->assertEquals(400, $response->getStatusCode());

        $I->assertEquals($results->error, $this->app['translator']->trans('api_error_tracking_feedback_failed'));
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailSuccessfulDuplicateWithNoCustomerId(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = uniqid();

        $this->addEventEmailSent(
            $I,
            $uniqId
        );

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(204, $response->getStatusCode());

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $results = json_decode($response->getContent());

        $I->assertEquals(400, $response->getStatusCode());

        $I->assertEquals($results->error, $this->app['translator']->trans('api_error_tracking_feedback_failed'));
    }

    /** @group database_transaction */
    public function testRouteFeedbackEmailSuccessfulMultipleEmail(ApiTester $I)
    {
        // Since it's a public route
        $I->removeAuthorization($I);

        $encryptedEmail = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));
        $uniqId         = uniqid();

        $this->addEventEmailSent(
            $I,
            $uniqId
        );

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail
        );
        $I->assertEquals(204, $response->getStatusCode());

        $encryptedEmail2 = base64_encode($this->app['security.crypto']->encrypt("<EMAIL>"));
        $encryptedRepId = base64_encode($this->app['security.crypto']->encrypt('1'));

        $response = $I->doDirectGet(
            $this->app,
            'tracking/email/feedback/' . $encryptedRepId . '/' . $uniqId . '/' . $encryptedEmail2
        );
        $I->assertEquals(204, $response->getStatusCode());

        $events = $this->app['events.manager']->getAll(
            [
                'uniq_id' => $uniqId,
            ],
            0,
            -1,
            false
        );

        $I->assertCount(3, $events);

        $this->checkArray(
            $I,
            [
                [
                    'type'        => 24,
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'uniq_id'     => $uniqId,
                    'attributes'  => 1,
                    'event_id'    => $events[0]->event_id,
                    'satisfied'   => 2,
                ],
                [
                    'type'        => 1,
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'uniq_id'     => $uniqId,
                    'attributes'  => json_encode('<EMAIL>'),
                    'event_id'    => $events[1]->event_id,
                    'satisfied'   => 0,
                ],
                [
                    'type'        => 1,
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'uniq_id'     => $uniqId,
                    'attributes'  => json_encode('<EMAIL>'),
                    'event_id'    => $events[2]->event_id,
                    'satisfied'   => 0,
                ],
            ],
            $events
        );
    }

    private function addEventEmailSent(
        $I,
        $uniqId,
        $repId = 1,
        $customerId = 0
    ) {
        // Send an email (Not possible at the moment) - add events with feedback
        $I->haveInDatabase(
            'sf_events',
            [
                'type'        => 24,
                'date'        => gmdate('Y-m-d H:i:s'),
                'source'      => 'sf_share_mail',
                'uniq_id'     => $uniqId,
                'user_id'     => $repId, // Reggie
                'customer_id' => $customerId, // No customer
                'attributes'  => 1, // 1 email sent
                'store_id'    => 1003,
            ]
        );
    }
}
