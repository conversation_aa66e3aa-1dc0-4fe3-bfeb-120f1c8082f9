<?php

use SF\ApiTester;
use SF\api\BaseApi;

/**
 * These tests should be recreated for the new menu parser
 */
class StorefrontCest extends BaseApi
{
    /** @group database_transaction */
    public function testCoreGenerateMenu(ApiTester $I)
    {
        $I->wantTo("Generate a menu from retailer website");
        $response = $I->doDirectPost($this->app, 'tests/storefront/menu/0');
        $result = json_decode($response->getContent());
        $I->assertEquals(1, $result);
    }

    /**
     * @param ApiTester $I
     * @skip skip this since it fail since 128 (we are not using the old parser anymore)
     * @group database_transaction
     */
    public function testCoreGetMenu(ApiTester $I)
    {
        $I->wantTo("Get a menu from retailer website");
        $this->fetchMenu($I);
        for ($i = 0; $i < 7; $i++) {
            $I->seeResponseContains('item ' . ($i + 1));
            $I->seeResponseContains('shop?rep={{user.user_login}}&sf_url=www.site.com%2Fitem' . ($i + 1));
        }
    }

    /**
     * @param ApiTester $I
     * @skip skip this since it crash since 121
     * @group database_transaction
     */
    public function testCoreChangeMenu(ApiTester $I)
    {
        $I->wantTo("Change the menu from retailer website");
        // Change the menu using another source
        $response = $I->doPostJson($I, 'tests/storefront/menu/menu-source2.html');
        $I->testJsonResponse($I);
        $I->seeResponseContains('1');
        // Make sure the menu is updated
        $this->fetchMenu($I);
        for ($i = 0; $i < 2; $i++) {
            $I->seeResponseContains('changed ' . ($i + 1));
            $I->seeResponseContains('shop?rep={{user.user_login}}&sf_url=www.site.com%2Fchanged' . ($i + 1));
        }
    }

    /**
     * @param ApiTester $I
     * @skip skip this since it fail since 128 (we are not using the old parser anymore)
     * @group database_transaction
     */
    public function testCoreWrongMenu(ApiTester $I)
    {
        $I->wantTo("Wrong menu from retailer website");
        // Reinit menu
        $response = $I->doPostJson($I, 'tests/storefront/menu/0');
        // Try to update menu
        $response = $I->doPostJson($I, 'tests/storefront/menu/menu-source-wrong.html');
        // Check for invalid menu response.
        $I->canSeeResponseCodeIs(500);
        $I->seeResponseContains("Retailer menu is not valid. Missing element .nav\"");
        // Make sure we didn't change the menu
        $this->testCoreGetMenu($I);
    }

    private function fetchMenu($I)
    {
        // $response = $I->doDirectPost($this->app, 'storefront/menu');
        // $result = json_decode($response->getContent());
        // return $result;
        $response = $I->doGetJson($I, 'storefront/menu');
        $I->debug($response);
        $I->testJsonResponse($I);
        $I->seeResponseMatchesJsonType([
         'menu' => 'string'
        ]);
    }
}
