<?php

use SF\ApiTester;
use SF\api\BaseApi;

class PhoneNumberNormalizationCest extends BaseApi
{
    /** @group database_transaction */
    public function testPhoneIsValid(ApiTester $I)
    {
        $I->wantTo("test that phone is valid");

        $result = $this->app['normalization.phonenumbers']->isValid('+15147564433', 'CA');

        $I->assertTrue($result);
    }

    /** @group database_transaction */
    public function testPhoneIsNotAPhoneNumber(ApiTester $I)
    {
        $I->wantTo("test that phone is not a phone number");

        $result = $this->app['normalization.phonenumbers']->isValid('bla bla', 'CA');

        $I->assertFalse($result);
    }

    /** @group database_transaction */
    public function testPhoneIsNotValid(ApiTester $I)
    {
        $I->wantTo("test that phone is not valid. It's a phone number from another country");

        $result = $this->app['normalization.phonenumbers']->isValid('066265434', 'CA');

        $I->assertFalse($result);
    }

    /** @group database_transaction */
    public function testNormalizePhone(ApiTester $I)
    {
        $I->wantTo("test that phone is correctly normalize");

        $phoneVariations = [
            '5147564433',
            '514 756 44 33',
            '(*************',
            '************',
            '514.756.44.33',
            '15147564433',
            '+15147564433',
        ];

        $expectedResult = '+15147564433';

        foreach ($phoneVariations as $phoneVariation) {
            $result = $this->app['normalization.phonenumbers']->normalize($phoneVariation, 'CA');
            $I->assertEquals($expectedResult, $result);
        }
    }

    /** @group database_transaction */
    public function testNormalizePhoneFailsStrictMode(ApiTester $I)
    {
        $I->wantTo("test that phone return an exception if phone number is wrong in strict mode");

        $country = 'CA';
        $expectedResult = '+15147564433';

        $phoneVariations = [
            'bla bla',
            '066265434',
            '8759875987598759857'
        ];

        $exceptionMessage = '';

        foreach ($phoneVariations as $phoneVariation) {
            try {
                $result = $this->app['normalization.phonenumbers']->normalize($phoneVariation, $country);
            } catch (Salesfloor\Services\Exceptions\PhoneNumberNotNormalizableException $e) {
                $exceptionMessage = $e->getMessage();
            }

            $I->assertEquals('Phone number [' . $phoneVariation . '] is not valid for country [' . $country . ']', $exceptionMessage);
        }
    }

    /** @group database_transaction */
    public function testNormalizePhoneFailsNonStrictMode(ApiTester $I)
    {
        $I->wantTo("test that phone return null if phone number is wrong in non strict mode");

        $country = 'CA';

        $result = $this->app['normalization.phonenumbers']->normalize('bla bla', $country, false);
        $I->assertEquals(null, $result);

        $result = $this->app['normalization.phonenumbers']->normalize('066265434', $country, false);
        $I->assertEquals('+1066265434', $result);

        $result = $this->app['normalization.phonenumbers']->normalize('8759875987598759857', $country, false);
        $I->assertEquals(null, $result);
    }
}
