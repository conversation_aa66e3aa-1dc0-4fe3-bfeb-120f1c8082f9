<?php

use SF\ApiTester;
use SF\api\BaseApi;

/**
 * These tests are very basic.
 * We just test here the service MessageQueue which uses another service that we shouldn't supposed to test SQS
 * However we could improve it by testing all methods and make test more accurate.
 *
 * It tests SQS service,  but it's not a good test, so skip it.
 *
 */
class MessageQueueCest extends BaseApi
{
    protected $queueName;

    public function _before($I)
    {
        parent::_before($I);
        $this->queueName = (isset($this->queueName) ? $this->queueName : 'tests_' . time() . uniqid());
    }

    /**
     * @skip  Do not need to test SQS service
     * @group database_transaction
     * */
    public function testCoreCreateQueue(ApiTester $I)
    {
        $I->wantTo("Create a queue");
        $response = $I->doDirectPost($this->app, 'tests/queue/' . $this->queueName . '/create');
        $result = json_decode($response->getContent());
        $I->assertEquals(1, $result);
    }

    /**
     * @skip  Do not need to test SQS service
     * @group database_transaction
     * */
    public function testCoreSendMessage(ApiTester $I)
    {
        $I->wantTo("Send a message");

        $response = $I->doDirectPost($this->app, 'tests/queue/' . $this->queueName . '/message/test1');
        $result = json_decode($response->getContent());
        $I->assertEquals(1, $result);

        $response = $I->doDirectGet($this->app, 'tests/queue/' . $this->queueName . '/message');
        $result = json_decode($response->getContent());
        $I->assertEquals('test1', $result[0]->Body);
    }

    /**
     * @skip  Do not need to test SQS service
     * @group database_transaction
     * */
    public function testCoreDeleteMessage(ApiTester $I)
    {
        $I->wantTo("Delete a message");

        $response = $I->doDirectPost($this->app, 'tests/queue/' . $this->queueName . '/message/testdelete');
        $result = json_decode($response->getContent());
        $I->assertEquals(1, $result);

        $response = $I->doDirectGet($this->app, 'tests/queue/' . $this->queueName . '/countmessages');
        $count = $response->getContent();

        for ($i = 0; $i < $count; $i++) {
            $response = $I->doDirectGet($this->app, 'tests/queue/' . $this->queueName . '/message');
            $results = json_decode($response->getContent());
            $receiptHandle = $results[0]->ReceiptHandle;

            $response = $I->doDirectDelete(
                $this->app,
                'tests/queue/' . $this->queueName . '/message',
                [
                    'receiptHandle' => $receiptHandle
                ]
            );
            $result = $response->getContent();
        }

        $response = $I->doDirectGet($this->app, 'tests/queue/' . $this->queueName . '/message');
        $I->assertEmpty($response->getContent());

        $response = $I->doDirectDelete($this->app, 'tests/queue/' . $this->queueName);
        $result = $response->getContent();
        $I->assertEquals(1, $result);
    }
}
