<?php

use SF\ApiTester;
use SF\api\BaseApi;
use Codeception\Scenario;
use Salesfloor\Models\Customer;
use Salesfloor\API\Managers\Reps;
use Salesfloor\API\Managers\CustomerMeta;
use Salesfloor\API\Managers\Transactions;
use Salesfloor\API\Managers\Client\Customers\Legacy;
use Salesfloor\Services\MessageQueue\MessageQueueClient;
use Salesfloor\Services\DataProcessing\TransactionCustomer;
use Salesfloor\API\Managers\Client\Customers\Legacy as Customers;

/**
 * PP-8 Post-sale contact creation
 * CEST class to test the TransactionCustomer DataProcessing Service
 * It will test that when messages are pushed into the queue, and popped
 * out the appropriate action of evaluating for existing customer and
 * associating to a transaction is done
 */
class TransactionCustomerCest extends BaseApi
{
    public const USER_ONE = 10001;
    public const USER_TWO = 10002;

    public const STORE_ONE = 1003;
    public const STORE_TWO = 2003;

    public const CUSTOMER_ONE = 10001;
    public const CUSTOMER_TWO = 10002;
    public const CUSTOMER_THREE = 10003;
    public const CUSTOMER_FOUR = 10004;
    public const CUSTOMER_FIVE = 10005;

    public const TYPE_EMAIL = 'email';
    public const TYPE_PHONE = 'phone';

    public const TRX_ONE = 10001;
    public const TRX_TWO = 10002;
    public const TRX_THREE = 10003;
    public const TRX_FOUR = 10004;

    public const TYPE_SALE = 'sale';

    const TXN_JOB_TABLE = 'sf_transaction_jobs';

    /** @var  ApiTester */
    protected $I;

    /** @var  \Silex\Application */
    protected $app;

    /** @var  \Salesfloor\Services\MySQLRepository */
    protected $repo;

    /** @var  TransactionCustomer */
    protected $service;

    /** @var  \Salesfloor\Services\MessageQueue */
    protected $messageQueueService;

    /** @var  Legacy */
    protected $customerManager;


    public function _before($I)
    {
        parent::_before($I);

        $this->repo = $this->app['repositories.mysql'];
        $this->service = $this->app['service.data_processing.transaction_customer'];
        $this->customerManager = $this->app['customers.manager'];
        $this->service->injectDeps(
            $this->repo,
            $this->customerManager,
            $this->app['service.customer_activity_feed']
        );
        $this->messageQueueService = $this->app['service.messagequeue'];

        $this->insertBaseUserData();
        $this->insertBaseCustomerData($I);
    }

    /** @group database_transaction */
    public function testTransactionWithNewCustomerWithoutLocaleMLEnabled(ApiTester $I)
    {
        $I->wantTo('Test transaction with new customer without locale ml enabled');
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail('newEmailMLEnabled');
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'New Email';
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                $name,
                null,
                self::STORE_ONE
            ),
        ]);

        $this->insertTransactionDetails([
            $this->buildTransactionDetails(
                1,
                $trxId,
                'detail_id',
                100,
                100,
                'product_id',
                1,
                'each',
                'sku',
                $trxId,
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email
        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $I->dontSeeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $I->dontSeeInDatabase(CustomerMeta::TABLE_NAME, ['type' => self::TYPE_EMAIL, 'value' => $email]);


        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([
            TransactionCustomer::MSG_KEY_TRANSACTION_ID => $trxId,
            TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false,
        ]);

        $rep = $this->app['reps.manager']->getOneOrNull(['ID' => $userId]);
        $I->assertNotNull($rep);
        $expectedLocale = $this->app['stores.manager']->getDefaultLocale($rep->store);

        $I->assertTrue($result);
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));
        $I->assertEquals($expectedLocale, $customers[0]['locale']);
        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $newCustomerId]);

        $esResults = $this->customerManager->getByIdFromElasticSearch($newCustomerId);
        $I->assertEquals($newCustomerId, $esResults['_id']);
        $results = $esResults['_source'];
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    /** @group database_transaction */
    public function testTransactionWithNewCustomerWithoutLocaleMLDisabled(ApiTester $I, Scenario $scenario)
    {
        $scenario->skip("This is relying on queue and are not enabled for 'tests-dev'");

        $this->I = $I;
        parent::_before($I);

        $this->repo = $this->app['repositories.mysql'];
        $this->service = $this->app['service.data_processing.transaction_customer'];
        $this->service->injectDeps(
            $this->repo,
            $this->app['customers.manager'],
            $this->app['service.customer_activity_feed']
        );
        $this->messageQueueService = $this->app['service.messagequeue'];

        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail('newEmailDisabled');
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'New Email';
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                $name,
                null,
                self::STORE_ONE
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email
        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $I->dontSeeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $I->dontSeeInDatabase(CustomerMeta::TABLE_NAME, ['type' => self::TYPE_EMAIL, 'value' => $email]);


        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);

        $expectedLocale = $this->app['configs']['retailer.i18n.default_locale'];

        $I->assertTrue($result);
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));
        $I->assertEquals($expectedLocale, $customers[0]['locale']);

        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $newCustomerId]);
    }


    /** @group database_transaction */
    public function testTransactionWithNewCustomer(ApiTester $I)
    {
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail('newEmail');
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'New Email';
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                $name,
                null,
                self::STORE_ONE
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email
        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $I->dontSeeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $I->dontSeeInDatabase(CustomerMeta::TABLE_NAME, ['type' => self::TYPE_EMAIL, 'value' => $email]);

        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([
            TransactionCustomer::MSG_KEY_TRANSACTION_ID => $trxId,
            TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false
        ]);

        $I->assertTrue($result);
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));

        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $newCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionWithExistingCustomerPrimaryEmail(ApiTester $I)
    {
        // Insert valid transaction with Primary email that did exist before
        $email = $this->buildEmail(self::CUSTOMER_ONE);
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                'Existing Primary Email',
                null,
                self::STORE_ONE
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email
        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $oldCustomerId = $customers[0]['ID'];
        $I->assertEquals(1, count($customers));

        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);

        $I->assertTrue($result);
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $oldCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $oldCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionWithExistingCustomerPrimaryEmailAndCustomerPhoneMeta(ApiTester $I)
    {
        // Insert valid transaction with Primary email that did exist before
        $email = $this->buildEmail(self::CUSTOMER_FIVE);
        $userId = self::USER_ONE;
        $trxId = self::TRX_FOUR;
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                'Existing Alternate Email',
                null,
                self::STORE_ONE
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email
        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $oldCustomerId = $customers[0]['ID'];
        $I->assertEquals(1, count($customers));

        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);

        $I->assertTrue($result);
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $oldCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $oldCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionWithExistingCustomerAlternateEmail(ApiTester $I)
    {
        // Insert valid transaction with alternate email that did exist before
        $email = $this->buildEmail(self::CUSTOMER_ONE, false);
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                'Existing Primary Email',
                null,
                self::STORE_ONE
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email
        $customerMetaFilter = ['customer_id' => self::CUSTOMER_ONE, 'type' => self::TYPE_EMAIL, 'value' => $email];
        $I->seeInDatabase(CustomerMeta::TABLE_NAME, $customerMetaFilter);
        $customerMetas = $this->getCustomerMetas($customerMetaFilter);
        $oldCustomerId = $customerMetas[0]['customer_id'];
        $I->assertEquals(1, count($customerMetas));

        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);

        $I->assertTrue($result);
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $oldCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $oldCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionWithExistingCustomerEmailForWrongUser(ApiTester $I)
    {
        // Insert valid transaction with email that existed before, but belonging to the wrong user
        // Should act in the same manner as did not exist before
        $email = $this->buildEmail(self::CUSTOMER_FOUR);
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'Email Exists Wrong User';
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                $name,
                null,
                self::STORE_ONE
            ),
        ]);

        // Validate that rows exist for transaction, and that no customer / customer meta has the $email
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => null]);
        // Validate that no customers exist with that email (for the given user)
        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $I->dontSeeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $I->dontSeeInDatabase(CustomerMeta::TABLE_NAME, ['type' => self::TYPE_EMAIL, 'value' => $email]);

        // Validate that customer with that email exists for wrong user
        $I->seeInDatabase(Customers::TABLE_NAME, ['email' => $email]);

        // Call service that will create customer and associate it to the transaction via customer_id
        $result = $this->service->process([
            TransactionCustomer::MSG_KEY_TRANSACTION_ID => $trxId,
            TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false
        ]);

        $I->assertTrue($result);
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));

        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->seeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => $newCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionWithInvalidId(ApiTester $I)
    {
        // Test with non existent transaction id
        $fakeTrxId = 222222;
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, ['ID' => $fakeTrxId]);
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => $fakeTrxId, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);
        // We return true, so we don't put it back to the queue when invalid transaction id
        $I->assertTrue($result);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $fakeTrxId]);

        // Test with non integer transaction id
        $fakeTrxId = 'invalidInteger';
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, ['ID' => $fakeTrxId]);
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => $fakeTrxId, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);
        // We return true, so we don't put it back to the queue when invalid transaction id
        $I->assertTrue($result);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $fakeTrxId]);
    }

    /** @group database_transaction */
    public function testTransactionWithInvalidEmail(ApiTester $I)
    {
        // Test with non existent transaction id
        $badEmail = 'Not Email';
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $badEmail,
                'Email Is invalid',
                null,
                self::STORE_ONE
            ),
        ]);
        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);
        $I->assertTrue($result);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId]);
    }

    /** @group database_transaction */
    public function testTransactionWithAlreadyPopulatedCustomer(ApiTester $I)
    {
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail(self::CUSTOMER_TWO);
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                'Already Has Customer',
                self::CUSTOMER_ONE,
                self::STORE_ONE
            ),
        ]);
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => self::CUSTOMER_ONE]);

        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => false]);
        // Customer is already linked, but we return true, so we don't put the message back in the queue
        $I->assertTrue($result);

        // Assert that we did not override the transaction's customer_id
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => self::CUSTOMER_ONE]);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => self::CUSTOMER_ONE]);
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, ['customer_id' => self::CUSTOMER_TWO]);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['customer_id' => self::CUSTOMER_TWO]);
    }

    /** @group database_transaction */
    public function testTransactionWithAlreadyPopulatedCustomerOverridden(ApiTester $I)
    {
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail(self::CUSTOMER_TWO);
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $this->insertTransactions([
            $this->buildTransaction(
                1,
                $userId,
                $trxId,
                $email,
                'Already Has Customer',
                self::CUSTOMER_ONE,
                self::STORE_ONE
            ),
        ]);
        $I->seeInDatabase(Transactions::TABLE_NAME, ['customer_id' => self::CUSTOMER_ONE]);

        $result = $this->service->process([TransactionCustomer::MSG_KEY_TRANSACTION_ID => 1, TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => true]);

        // Customer is already linked, but we return true, so we don't put the message back in the queue
        $I->assertTrue($result);

        // Assert that we DID override the transaction's customer_id from One -> Two
        // TODO: The test here is weired, will revisit it.
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => self::CUSTOMER_ONE]);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['transaction_id' => $trxId, 'customer_id' => self::CUSTOMER_ONE]);
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, ['customer_id' => self::CUSTOMER_TWO]);
        $I->dontSeeInDatabase(self::TXN_JOB_TABLE, ['customer_id' => self::CUSTOMER_TWO]);
    }

    /** @group database_transaction */
    public function testTransactionCustomerFullCycleWithUpdatingCopyTable(ApiTester $I, Scenario $scenario)
    {
        $scenario->skip("This is relying on queue and are not enabled for 'tests-dev'");

        $this->cleanUpTransactionCustomerQueue();
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail('newEmail');
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'New Email';
        $trxData = $this->buildTransaction(
            1,
            $userId,
            $trxId,
            $email,
            $name,
            null,
            self::STORE_ONE
        );
        $this->insertTransactions([$trxData]);
        $this->insertTransactionCopies([$trxData]);

        $I->seeInDatabase(Transactions::TABLE_NAME, $trxData);
        $I->seeInDatabase(Transactions::COPY_TABLE_NAME, $trxData);

        // Push message into queue
        $overrideCustomer = false;
        $this->service->push($trxId, $overrideCustomer);

        // Pop message from queue & process the transaction
        $result = $this->service->pop(
            $this->repo,
            $this->app['customers.manager'],
            $this->app['service.customer_activity_feed']
        );

        // Check that old version of the transaction is no longer in the table (customer_id no longer null)
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, $trxData);
        $I->dontSeeInDatabase(Transactions::COPY_TABLE_NAME, $trxData);

        // Check that $result is proper array that is returned/popped from the TransactionCustomer queue
        $I->assertNotEmpty($result);
        $this->validateMessageStructure($result, $trxId, $overrideCustomer);

        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));

        // Check that new version of the transaction is now in the table (generated customer_id)
        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->seeInDatabase(Transactions::COPY_TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionCustomerFullCycleWithNotUpdatingCopyTable(ApiTester $I, Scenario $scenario)
    {
        $scenario->skip("This is relying on queue and are not enabled for 'tests-dev'");

        $this->cleanUpTransactionCustomerQueue();
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail('newEmail');
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'New Email';
        $trxData = $this->buildTransaction(
            1,
            $userId,
            $trxId,
            $email,
            $name,
            null,
            self::STORE_ONE
        );
        $this->insertTransactions([$trxData]);
        $this->insertTransactionCopies([$trxData]);

        $I->seeInDatabase(Transactions::TABLE_NAME, $trxData);
        $I->seeInDatabase(Transactions::COPY_TABLE_NAME, $trxData);

        // Push message into queue
        $overrideCustomer = false;
        $this->service->push($trxId, $overrideCustomer, false);

        // Pop message from queue & process the transaction
        $result = $this->service->pop(
            $this->repo,
            $this->app['customers.manager'],
            $this->app['service.customer_activity_feed']
        );

        // Check that old version of the transaction is no longer in the table (customer_id no longer null)
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, $trxData);
        $I->SeeInDatabase(Transactions::COPY_TABLE_NAME, $trxData);

        // Check that $result is proper array that is returned/popped from the TransactionCustomer queue
        $I->assertNotEmpty($result);
        $this->validateMessageStructure($result, $trxId, $overrideCustomer);

        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));

        // Check that new version of the transaction is now in the table (generated customer_id)
        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->dontseeInDatabase(Transactions::COPY_TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionCustomerFullCycleWithUpdatingCopyTableFromOldPayload(ApiTester $I, Scenario $scenario)
    {
        $scenario->skip("This is relying on queue and are not enabled for 'tests-dev'");

        $this->cleanUpTransactionCustomerQueue();
        // Insert valid transaction with email that did not exist before
        $email = $this->buildEmail('newEmail');
        $userId = self::USER_ONE;
        $trxId = self::TRX_ONE;
        $name = 'New Email';
        $trxData = $this->buildTransaction(
            1,
            $userId,
            $trxId,
            $email,
            $name,
            null,
            self::STORE_ONE
        );
        $this->insertTransactions([$trxData]);
        $this->insertTransactionCopies([$trxData]);

        $I->seeInDatabase(Transactions::TABLE_NAME, $trxData);
        $I->seeInDatabase(Transactions::COPY_TABLE_NAME, $trxData);

        // Push message into queue
        $overrideCustomer = false;

        $message = [
            TransactionCustomer::MSG_KEY_TRANSACTION_ID    => $trxId,
            TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER => $overrideCustomer,
            TransactionCustomer::MSG_KEY_ENV               => 'dev',
            TransactionCustomer::MSG_KEY_RETAILER          => 'tests',
            TransactionCustomer::MSG_KEY_TIMESTAMP         => gmdate('U'),
        ];
        $oldPayload = json_encode($message);

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getQueueUrl');
        $method->setAccessible(true);
        $queueUrl = $method->invoke($this->service);

        $queue = new MessageQueueClient($this->app);

        $queue->sendMessage($queueUrl, $oldPayload);

        // Pop message from queue & process the transaction
        $result = $this->service->pop(
            $this->repo,
            $this->app['customers.manager'],
            $this->app['service.customer_activity_feed']
        );

        // Check that old version of the transaction is no longer in the table (customer_id no longer null)
        $I->dontSeeInDatabase(Transactions::TABLE_NAME, $trxData);
        $I->dontSeeInDatabase(Transactions::COPY_TABLE_NAME, $trxData);

        // Check that $result is proper array that is returned/popped from the TransactionCustomer queue
        $I->assertNotEmpty($result);
        $this->validateMessageStructure($result, $trxId, $overrideCustomer);

        $customerFilter = ['user_id' => $userId, 'email' => $email];
        $this->addOriginAndNameToCustomerFilter($customerFilter, $name);
        $I->seeInDatabase(Customers::TABLE_NAME, $customerFilter);
        $customers = $this->getCustomers($customerFilter);
        $I->assertEquals(1, count($customers));

        // Check that new version of the transaction is now in the table (generated customer_id)
        $newCustomerId = $customers[0]['ID'];
        $I->seeInDatabase(Transactions::TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
        $I->seeInDatabase(Transactions::COPY_TABLE_NAME, ['trx_id' => $trxId, 'customer_id' => $newCustomerId]);
    }

    /** @group database_transaction */
    public function testTransactionCustomerPushInvalid(ApiTester $I)
    {
        // Attempt to push bad info message into queue
        $overrideCustomer = false;
        $result = $this->service->push('not numeric', $overrideCustomer);
        $I->assertFalse($result);
    }

    private function validateMessageStructure($message, $transactionId = null, $overrideCustomer = null)
    {
        foreach ($this->service->messageKeys as $key) {
            $this->I->assertArrayHasKey($key, $message);
        }
        if (!is_null($transactionId)) {
            $this->I->assertEquals($transactionId, $message[TransactionCustomer::MSG_KEY_TRANSACTION_ID]);
        }
        if (!is_null($overrideCustomer)) {
            $this->I->assertEquals($overrideCustomer, $message[TransactionCustomer::MSG_KEY_OVERRIDE_CUSTOMER]);
        }
        $this->I->assertEquals($this->app['configs']['env'], $message[TransactionCustomer::MSG_KEY_ENV]);
        $this->I->assertEquals(
            $this->app['configs']['retailer.short_name'],
            $message[TransactionCustomer::MSG_KEY_RETAILER]
        );
        $this->I->assertGreaterThan(0, $message[TransactionCustomer::MSG_KEY_TIMESTAMP]);
    }

    /********************************
     **** HELPER FUNCTIONS BELOW ****
     ********************************/

    private function cleanUpTransactionCustomerQueue()
    {
        try {
            $queueUrl = $this->messageQueueService->getQueueUrl($this->service->getQueueId());
            if (empty($queueUrl)) {
                return;
            }
            $this->messageQueueService->purgeQueue($queueUrl);
        } catch (\Exception $e) {
            $this->app['logger']->error('Error purging QueueId ' . $this->service->getQueueId()
                . ' - With Url ' . $queueUrl);
        }
    }

    private function getCustomers($filters = [])
    {
        return $this->getResults(Customers::TABLE_NAME, $filters);
    }

    private function getCustomerMetas($filters = [])
    {
        return $this->getResults(CustomerMeta::TABLE_NAME, $filters);
    }

    private function getResults($table, $filters = [])
    {
        $qb = $this->repo->getQueryBuilder();
        $qb->select('c.*')
            ->from($table, 'c');

        foreach ($filters as $field => $value) {
            $qb->andWhere("c.$field = :$field")
                ->setParameter($field, $value);
        }
        return $this->repo->executeCustomQuery($qb);
    }


    private function buildTransaction(
        $id,
        $userId,
        $trxId,
        $email,
        $name = null,
        $customerId = null,
        $storeId = null
    ) {
        return [
            'ID' => $id,
            'user_id' => $userId,
            'trx_id' => $trxId,
            'customer_email' => $email,
            'customer_name' => $name,
            'customer_id' => $customerId,
            'trx_type' => self::TYPE_SALE,
            'trx_date' => '2024-01-02 03:04:05',
            'trx_apply_total' => 100,
            'trx_total' => 100,
            'store_id' => $storeId,
        ];
    }

    private function buildTransactionDetails(
        $id,
        $trxId,
        $trxDetailId,
        $applyTotal,
        $total,
        $productId,
        $qty,
        $unit,
        $sku,
        $trxThreadId
    ): array {
        return [
            'id' => $id,
            'trx_id' => $trxId ,
            'trx_detail_id' => $trxDetailId,
            'trx_detail_apply_total' => $applyTotal,
            'trx_detail_total' => $total,
            'product_id' => $productId,
            'quantity' => $qty,
            'units' => $unit,
            'sku' => $sku,
            'trx_thread_id' => $trxThreadId,
        ];
    }

    private function insertBaseUserData()
    {
        $this->insertUsers([
            [
                'ID' => self::USER_ONE,
                'user_login' => self::USER_ONE,
                'store' => self::STORE_ONE,
                'type' => 'rep',
                'group' => 1,
                'selling_mode' => 1,
            ],
            [
                'ID' => self::USER_TWO,
                'user_login' => self::USER_TWO,
                'store' => self::STORE_TWO,
                'type' => 'rep',
                'group' => 1,
                'selling_mode' => 1,
            ]
        ]);
    }

    private function buildEmail($id, $isPrimary = true)
    {
        return 'Email+' . $id . ($isPrimary ? '' : '+Alternate') . '@blurgh.com';
    }

    private function insertBaseCustomerData()
    {
        $this->insertCustomers([
            // Customers that belong to User One - Store One
            [
                'ID' => self::CUSTOMER_ONE,
                'name' => 'Cust ' . self::CUSTOMER_ONE,
                'email' => $this->buildEmail(self::CUSTOMER_ONE, true),
                'user_id' => self::USER_ONE,
            ],
            [
                'ID' => self::CUSTOMER_TWO,
                'name' => 'Cust ' . self::CUSTOMER_TWO,
                'email' => $this->buildEmail(self::CUSTOMER_TWO, true),
                'user_id' => self::USER_ONE,
            ],
            [
                'ID' => self::CUSTOMER_THREE,
                'name' => 'Cust ' . self::CUSTOMER_THREE,
                'email' => $this->buildEmail(self::CUSTOMER_THREE, true),
                'user_id' => self::USER_ONE,
            ],
            [
                'ID' => self::CUSTOMER_FIVE,
                'name' => 'Cust ' . self::CUSTOMER_FIVE,
                'email' => $this->buildEmail(self::CUSTOMER_FIVE, true),
                'user_id' => self::USER_ONE,
            ],

            // Customers that belong to User Two - Store Two
            [
                'ID' => self::CUSTOMER_FOUR,
                'name' => 'Cust ' . self::CUSTOMER_FOUR,
                'email' => $this->buildEmail(self::CUSTOMER_FOUR, true),
                'user_id' => self::USER_TWO,
            ],
        ]);

        $this->insertCustomerMetas([
            [
                'customer_id' => self::CUSTOMER_ONE,
                'type' => self::TYPE_EMAIL,
                'value' => $this->buildEmail(self::CUSTOMER_ONE, false),
                'label' => 'Alternate',
                'position' => 0,
            ],
            [
                'customer_id' => self::CUSTOMER_TWO,
                'type' => self::TYPE_EMAIL,
                'value' => $this->buildEmail(self::CUSTOMER_TWO, false),
                'label' => 'Alternate',
                'position' => 0,
            ],
            [
                'customer_id' => self::CUSTOMER_THREE,
                'type' => self::TYPE_EMAIL,
                'value' => $this->buildEmail(self::CUSTOMER_THREE, false),
                'label' => 'Alternate',
                'position' => 0,
            ],
            [
                'customer_id' => self::CUSTOMER_FOUR,
                'type' => self::TYPE_EMAIL,
                'value' => $this->buildEmail(self::CUSTOMER_FOUR, false),
                'label' => 'Alternate',
                'position' => 0,
            ],
            [
                'customer_id' => self::CUSTOMER_FIVE,
                'type' => self::TYPE_PHONE,
                'value' => $this->buildEmail(self::CUSTOMER_FIVE, false),
                'label' => 'Alternate',
                'position' => 0,
            ],
        ]);
    }

    private function insertRows($table, $rows)
    {
        foreach ($rows as $row) {
            $this->I->haveInDatabase($table, $row);
        }
    }

    private function insertUsers($rows)
    {
        $this->insertRows(Reps::TABLE_NAME, $rows);
    }

    private function insertCustomers($rows)
    {
        $this->insertRows(Customers::TABLE_NAME, $rows);
    }

    private function insertCustomerMetas($rows)
    {
        $this->insertRows(CustomerMeta::TABLE_NAME, $rows);
    }

    private function insertTransactions($rows)
    {
        $this->insertRows(Transactions::TABLE_NAME, $rows);
    }

    private function insertTransactionDetails(array $rows)
    {
        $this->insertRows('sf_rep_transaction_detail', $rows);
    }

    private function insertTransactionCopies($rows)
    {
        $this->insertRows(Transactions::COPY_TABLE_NAME, $rows);
    }

    private function addOriginAndNameToCustomerFilter(array &$customerFilter, $name = null)
    {
        list($firstName, $lastName) = $this->service->splitCustomerName($name);
        $customerFilter['origin'] = Customer::ORIGIN_TRANSACTION;
        $customerFilter['first_name'] = $firstName;
        $customerFilter['last_name'] = $lastName;
        $customerFilter['name'] = $name;
    }
}
