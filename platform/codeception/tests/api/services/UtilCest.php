<?php

use SF\ApiTester;
use SF\api\BaseApi;
use Salesfloor\Services\Util;

/**
 * TODO: Move this to the unit tests section when we make the switch to the other branch
 * @deprecated  move into codeception/tests/unit/Services/UtilTest.php
 * Class UtilCest
 */
class UtilCest extends BaseApi
{
    // ENCODE
    /** @group database_transaction */
    public function testUrlEncodeDeepString(ApiTester $I)
    {
        $I->wantTo("test url encode - string - space character");
        $input  = "Joseph Mallette";
        $output = "Joseph+Mallette";
        $result = $this->app['service.util']->urlEncodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlEncodeDeepStringCharacter(ApiTester $I)
    {
        $I->wantTo("test url encode - string - plus character");
        $input  = "Joseph+Mallette";
        $output = "Joseph%2BMallette";
        $result = $this->app['service.util']->urlEncodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlEncodeDeepArray(ApiTester $I)
    {
        $I->wantTo("test url encode - array - space character");
        $input = [
            'name' => '<PERSON>ette',
        ];
        $output = [
            'name' => "Joseph+Mallette",
        ];
        $result = $this->app['service.util']->urlEncodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlEncodeArrayCharacter(ApiTester $I)
    {
        $I->wantTo("test url encode - array - plus character");
        $input = [
            'name' => 'Joseph+Mallette',
        ];
        $output = [
            'name' => "Joseph%2BMallette",
        ];
        $result = $this->app['service.util']->urlEncodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlEncodeObject(ApiTester $I)
    {
        $I->wantTo("test url encode - object - space character");
        $input       = new StdClass();
        $input->name = "Joseph Mallette";
        $output       = new StdClass();
        $output->name = "Joseph+Mallette";
        $result = $this->app['service.util']->urlEncodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlEncodeObjectCharacter(ApiTester $I)
    {
        $I->wantTo("test url encode - object - plus character");
        $input       = new StdClass();
        $input->name = "Joseph+Mallette";
        $output       = new StdClass();
        $output->name = "Joseph%2BMallette";
        $result = $this->app['service.util']->urlEncodeDeep($input);
        $I->assertEquals($output, $result);
    }
    // DECODE
    /** @group database_transaction */
    public function testUrlDecodeDeepString(ApiTester $I)
    {
        $I->wantTo("test url decode - string - space character");
        $input = "Joseph+Mallette";
        $output  = "Joseph Mallette";
        $result = $this->app['service.util']->urlDecodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlDecodeDeepStringCharacter(ApiTester $I)
    {
        $I->wantTo("test url decode - string - plus character");
        $input = "Joseph%2BMallette";
        $output  = "Joseph+Mallette";
        $result = $this->app['service.util']->urlDecodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlDecodeDeepArray(ApiTester $I)
    {
        $I->wantTo("test url decode - array - space character");
        $input = [
            'name' => "Joseph+Mallette",
        ];
        $output = [
            'name' => 'Joseph Mallette',
        ];
        $result = $this->app['service.util']->urlDecodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlDecodeArrayCharacter(ApiTester $I)
    {
        $I->wantTo("test url decode - array - plus character");
        $input = [
            'name' => "Joseph%2BMallette",
        ];
        $output = [
            'name' => 'Joseph+Mallette',
        ];
        $result = $this->app['service.util']->urlDecodeDeep($input);
        $I->assertEquals($output, $result);
    }
    /** @group database_transaction */
    public function testUrlDecodeObject(ApiTester $I)
    {
        $I->wantTo("test url decode - object - space character");
        $input       = new StdClass();
        $input->name = "Joseph+Mallette";
        $output       = new StdClass();
        $output->name = "Joseph Mallette";
        $result = $this->app['service.util']->urlDecodeDeep($input);
        $I->assertEquals($output, $result);
    }

    /** @group database_transaction */
    public function testUrlDecodeObjectCharacter(ApiTester $I)
    {
        $I->wantTo("test url decode - object - plus character");
        $input       = new StdClass();
        $input->name = "Joseph%2BMallette";
        $output       = new StdClass();
        $output->name = "Joseph+Mallette";
        $result = $this->app['service.util']->urlDecodeDeep($input);
        $I->assertEquals($output, $result);
    }

    /** @group database_transaction */
    public function testConvertTagIdsToRealArray(ApiTester $I)
    {
        $I->wantTo("convert tag ids to real array");
        $requestTagIds = [
            '1,2',
            '6,7,1',
            '2,11,12'
        ];
        $result = $this->app['service.util']->convertTagIdsToRealArray($requestTagIds);

        $expected = [
            1,
            2,
            6,
            7,
            11,
            12
        ];

        $I->assertEquals($expected, $result);
    }

    /**
     * @param ApiTester $I
     * @return void
     * @throws Exception
     *
     * @skip
     * @group database_transaction
     */
    public function testRegisterSingletonProcess(ApiTester $I)
    {
        $label1 = 'testRegisterSingletonProcess' . Util::uuid();
        $label2 = 'testRegisterSingletonProcess' . Util::uuid();
        $pidFile1 = Util::registerSingletonProcess($label1);
        $I->assertFileExists($pidFile1);
        $I->trackTmpFile($pidFile1);
        $pidFile2 = Util::registerSingletonProcess($label2);
        $I->assertFileExists($pidFile2);
        $I->trackTmpFile($pidFile2);
        $I->assertFalse(Util::registerSingletonProcess($label1));
        $I->assertFalse(Util::registerSingletonProcess($label2));
        unlink($pidFile1);
        $pidFile1b = Util::registerSingletonProcess($label1);
        $I->assertEquals($pidFile1, $pidFile1b);
        $I->assertFalse(Util::registerSingletonProcess($label1));
        $unusedPid = pcntl_fork();
        if ($unusedPid) {
            file_put_contents($pidFile1, "$unusedPid\n");
            pcntl_waitpid($unusedPid, $_);
        } else {
            exit(0);
        }
        $pidFile1c = Util::registerSingletonProcess($label1);
        $I->assertEquals($pidFile1, $pidFile1c);
        $I->assertFalse(Util::registerSingletonProcess($label1));
    }
}
