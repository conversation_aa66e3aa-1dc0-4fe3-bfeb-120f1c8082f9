<?php

use SF\ApiTester;
use SF\api\BaseApi;

class ChatCest extends BaseApi
{
    const QUEUE = 'queue';
    const PRESENCE = 'presence';
    const FIREBASE_REQUEST_PATH = 'request';
    const FIREBASE_REQUEST_STORES_PATH = 'stores';
    const FIREBASE_REQUEST_REPS_PATH = 'reps';

    private $firebase;
    private $store1;
    private $user1;
    private $users;
    private $store2;
    private $userChatMiss;

    public function _before($I)
    {
        parent::_before($I);
        $this->firebase     = new \Firebase\FirebaseLib(
            $this->app['configs']['firebase.url'],
            $this->app['configs']['firebase.token']
        );
        $this->store1       = (isset($this->store1) ? $this->store1 : $this->createFakeStore());
        $this->store2       = (isset($this->store2) ? $this->store2 : $this->createFakeStore());
        $this->user1        = (isset($this->user1) ? $this->user1 : $this->createFakeUser());
        $this->userChatMiss = (isset($this->userChatMiss) ? $this->userChatMiss : $this->createFakeUser());
    }

    public function _after($I)
    {
        if (!empty($this->users)) {
            foreach ($this->users as $user) {
                $response = $I->doDirectPost($this->app, "chat/disconnect/$this->store2/$user");

                $this->checkUserDisconnected($this->store2, $user);
            }
        }

        if (!empty($this->firebase)) {
            $this->firebase->delete(
                $this->app['configs']['firebase.defaultpath'] . '/' . self::QUEUE . '/' . $this->store1
            );
            $this->firebase->delete(
                $this->app['configs']['firebase.defaultpath'] . '/' . self::QUEUE . '/' . $this->store2
            );
        }
        parent::_after($I);
    }

    /** @group database_transaction */
    public function testGetNearbyStoresToCenterStoreOneStoreOnly(ApiTester $I)
    {
        $I->wantTo('get a list of stores that are eligible to receive chats in broadcast mode - one only');

        require __DIR__ . '/../../fixtures/database/stores.php';
        $I->haveInDatabase('sf_store', $storeMontreal);
        $I->haveInDatabase('sf_store', $storeNYC);
        $I->haveInDatabase('sf_store', $storeVancover);
        $I->haveInDatabase('sf_store', $storeSherbrooke);
        $I->haveInDatabase('sf_store_locale', $storeLocaleMontreal);
        $I->haveInDatabase('sf_store_locale', $storeLocaleNyc);
        $I->haveInDatabase('sf_store_locale', $storeLocaleVancouver);
        $I->haveInDatabase('sf_store_locale', $storeLocaleSherbrooke);

        $response = $I->doDirectGet($this->app, 'chat/stores/test-fake-mall');
        $results = json_decode($response->getContent());
        $I->assertCount(1, $results);
        $I->assertEquals('test-fake-mall', $results[0]->identifier);
    }

    /** @group database_transaction */
    public function testGetNearbyStoresToCenterStoreMultipleStores(ApiTester $I)
    {
        $I->wantTo('get a list of stores that are eligible to receive chats in broadcast mode - many available');

        require __DIR__ . '/../../fixtures/database/stores.php';
        $I->haveInDatabase('sf_store', $storeMontreal);
        $I->haveInDatabase('sf_store', $storeNYC);
        $I->haveInDatabase('sf_store', $storeVancover);
        $I->haveInDatabase('sf_store', $storeSherbrooke);
        $I->haveInDatabase('sf_store_locale', $storeLocaleMontreal);
        $I->haveInDatabase('sf_store_locale', $storeLocaleNyc);
        $I->haveInDatabase('sf_store_locale', $storeLocaleVancouver);
        $I->haveInDatabase('sf_store_locale', $storeLocaleSherbrooke);

        $this->app['configs']['chat.broadcast.closest_stores'] = 3;
        // The chat service needs to be mocked up for the config override to take effect
        unset($this->app['service.chat']);
        $this->app['service.chat'] = new \Salesfloor\Services\Chat($this->app);
        $response = $I->doDirectGet($this->app, 'chat/stores/test-fake-mall');
        $content = json_decode($response->getContent());
        $I->debug($response);
        $I->assertCount(3, $content);
        $I->assertEquals('test-fake-mall', $content[0]->identifier);
        $I->assertEquals('test-sherbrooke', $content[1]->identifier);
        $I->assertEquals('test-vancouver', $content[2]->identifier);
    }

    /** @group database_transaction */
    public function testFlagChat(ApiTester $I)
    {
        $I->wantTo("Flag a chat as inappropriate");

        $params = [
            "user_id" => 1,
            "chat_thread_identifier" => "FIREBASEID",
            "chat_contents" => "this is a chat where someone was inappropriate"
        ];

        $response = $I->doDirectPost($this->app, 'chat/flag', $params);
        $results = json_decode($response->getContent());
        $this->checkArray(
            $I,
            [
                "success" => true,
            ],
            $results
        );

        $I->seeInDatabase('sf_chat_flagged', $params);
        $I->seeInDatabase(
            'sf_events',
            ['type' => \Salesfloor\Services\Event::SF_EVENT_CHAT_FLAGGED_INAPPROPRIATE, 'uniq_id' => '1']
        );

        $params = [
            "chat_thread_identifier" => "FIREBASEID",
            'customer_fingerprint' => 2381983,
            'customer_user_agent' => 'Browser/UserAgent Something Something'
        ];

        $response = $I->doDirectPut($this->app, 'chat/flag', $params);
        $I->seeInDatabase('sf_chat_flagged', $params);
    }

    /** @group database_transaction */
    public function testTrackChatTransfer(ApiTester $I)
    {
        $I->wantTo("Track a chat transfer");

        $params = [
            "user_id" => 1,
            "chat_thread_identifier" => "FIREBASEID",
            "chat_contents" => "this is the chat transcript",
            "handoff_message" => "this is the handoff message",
            "customer_name" => "Some customer",
            "customer_email" => "<EMAIL>"
        ];

        $response = $I->doDirectPost($this->app, 'chat/track-transfer', $params);
        $results = json_decode($response->getContent());
        $this->checkArray(
            $I,
            [
                "success" => true,
            ],
            $results
        );

        $I->seeInDatabase('sf_chat_transferred', $params);
        $I->seeInDatabase(
            'sf_events',
            ['type' => \Salesfloor\Services\Event::SF_EVENT_CHAT_TRANSFERRED, 'uniq_id' => '1']
        );
    }

    /** @group database_transaction */
    public function testCoreConnect(ApiTester $I)
    {
        $I->wantTo("Connect a user to chat");
        $response = $I->doDirectPost($this->app, "chat/connect/$this->store1/$this->user1");
        $results = json_decode($response->getContent());
        $this->checkArray(
            $I,
            [
                "success" => true,
            ],
            $results
        );

        $this->checkUserInFirebase($this->store1, $this->user1);

        $I->wantTo("Check user is in queue and in presence");
        $response = $I->doDirectGet($this->app, "chat/queue/$this->store1");
        $results = json_decode($response->getContent());
        // check response is ok and user in queue
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertEquals($results[0]->userName, $this->user1);
        $I->assertTrue($this->timeOk($results[0]->connection));
        $I->assertTrue($this->timeOk($results[0]->heartbeat));
        $I->assertEquals($this->user1, $results[0]->userName);
        $I->assertEquals($this->getPresence($this->store1, $this->user1), "true");
    }

    /** @group database_transaction */
    public function testCoreDisconnect(ApiTester $I)
    {
        $I->wantTo("Disconnect user from chat");
        $response = $I->doDirectPost($this->app, "chat/disconnect/$this->store1/$this->user1");

        // check response is ok and user in queue
        $results = json_decode($response->getContent());
        $this->checkArray(
            $I,
            [
                "success" => true,
            ],
            $results
        );

        $I->wantTo("Check user is not in queue and not in presence");
        $response = $I->doDirectGet($this->app, "chat/queue/$this->store1");
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        // check queue is empty
        $I->assertEmpty($results);
        $I->assertEquals($this->getPresence($this->store1, $this->user1), "null");
        $this->checkUserDisconnected($this->store1, $this->user1);
    }

    /** @group database_transaction */
    public function testCoreChatMiss(ApiTester $I)
    {
        $I->wantTo("Chat miss for a user");
        // Connect a new user
        $response = $I->doDirectPost($this->app, "chat/connect/$this->store1/$this->userChatMiss");

        $response = $I->doDirectPost($this->app, "chat/miss/$this->store1/$this->userChatMiss");
        $results = json_decode($response->getContent());
        $this->checkArray(
            $I,
            [
                "success" => true,
            ],
            $results
        );
        $this->checkUserChatMiss($this->store1, $this->userChatMiss);
    }

    private function setFakeMallTimezoneForEjection()
    {
        // Set fake-mall to a timezone that isn't in the valid range.
        $openingHour = $this->app['configs']['retailer.store.open_hour'];
        $closingHour = $this->app['configs']['retailer.store.close_hour'];
        $storeIsOpen = function ($hour) use ($openingHour, $closingHour) {
            return $hour < $closingHour && $hour >= $openingHour;
        };

        $getHour = function ($zone) {
            $tz = new DateTimeZone($zone);
            $dt = new DateTime('now', $tz);
            return $dt->format('H');
        };

        /** @var \Salesfloor\Services\MySQLRepository $repo */
        $repo = $this->app['repositories.mysql'];
        $updateTz = function ($zone) use ($repo) {
            $qb = $repo->getQueryBuilder();
            $qb->update('sf_store', 's')
               ->set("s.timezone", "'$zone'")
               ->where('sf_identifier = "fake-mall"');
            $qb->execute();
        };

        if ($storeIsOpen($getHour('America/New_York'))) {
            if (!$storeIsOpen($getHour('Asia/Tokyo'))) {
                $updateTz('Asia/Tokyo');
            } else {
                $updateTz('Europe/Amsterdam');
            }
        }
    }

    /** @group database_transaction */
    public function testConnectAfterHours(ApiTester $I, $scenario)
    {
        $I->wantTo("Validate a user can't connect to chat after hours");
        $scenario->skip('flaky - time zones are global');

        $this->app['configs']['retailer.chat.option.eject_after_hours'] = true;

        $this->setFakeMallTimezoneForEjection();

        /** @var \Symfony\Component\HttpFoundation\Response $response */
        $response = $I->doDirectRequest(
            $this->app,
            "chat/connect/fake-mall/reggie",
            'POST'
        );
        $I->assertEquals(500, $response->getStatusCode());

        $response = $I->doDirectGet($this->app, "chat/queue/fake-mall");
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        // check queue is empty
        $I->assertTrue(empty($results));
        $I->assertEquals($this->getPresence('fake-mall', 'reggie'), "null");
        $this->checkUserDisconnected('fake-mall', 'reggie');
    }

    /** @group database_transaction */
    public function testEjectAfterHours(ApiTester $I, $scenario)
    {
        $I->wantTo("Validate removing a user after hours");
        $scenario->skip('flaky - time zones are global');

        $this->app['configs']['retailer.chat.option.eject_after_hours'] = true;

        $response = $I->doDirectRequest(
            $this->app,
            "chat/connect/fake-mall/reggie",
            'POST'
        );
        $I->assertEquals(200, $response->getStatusCode());

        $response = $I->doDirectGet($this->app, "chat/queue/fake-mall");
        $results = json_decode($response->getContent());
        // check response is ok and user in queue
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertEquals('reggie', 'reggie');

        $this->setFakeMallTimezoneForEjection();

        $this->app['service.chat']->ejectUsersFromChatPoolInClosedStores();

        $response = $I->doDirectGet($this->app, "chat/queue/fake-mall");
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        // check queue is empty
        $I->assertTrue(empty($results));
        $I->assertEquals($this->getPresence('fake-mall', 'reggie'), "null");
        $this->checkUserDisconnected('fake-mall', 'reggie');

        $this->app['configs']['retailer.chat.option.eject_after_hours'] = false;
    }

    /** @group database_transaction */
    public function testCoreReconnectAfterChatMiss(ApiTester $I)
    {
        $I->wantTo("Reconnect a rep after a miss");
        $response = $I->doDirectPost($this->app, "chat/connect/$this->store1/$this->userChatMiss");
        $results = json_decode($response->getContent());
        $this->checkArray(
            $I,
            [
                "success" => true,
            ],
            $results
        );
        $this->checkUserInFirebase($this->store1, $this->userChatMiss);
    }

    /** @group database_transaction */
    public function testCoreConnectSeveralUsers(ApiTester $I)
    {
        $I->wantTo("Connect several users");

        // Connect x users to chat and check there are in firebase
        $numberOfUsers = 10;
        $this->users = [];

        for ($i = 0; $i < $numberOfUsers; $i++) {
            $user = $this->createFakeUser();
            $this->users[] = $user;
            $response = $I->doDirectPost($this->app, "chat/connect/$this->store2/$user");

            $this->checkUserInFirebase($this->store2, $user);
        }
    }

    /** @group database_transaction */
    public function testCoreQueueOrder(ApiTester $I)
    {
        $I->wantTo("Test queue order");

        $this->users = [];
        $this->testCoreConnectSeveralUsers($I);

        $response = $I->doDirectGet($this->app, "chat/queue/$this->store2");
        $results = json_decode($response->getContent());
        // check response is ok and user in queue
        $I->assertEquals(200, $response->getStatusCode());

        $i = 0;
        foreach ($results as $user) {
            $I->assertEquals($user->userName, $this->users[$i]);
            $I->assertTrue($this->timeOk($user->connection));
            $I->assertTrue($this->timeOk($user->heartbeat));
            $I->assertEquals($this->users[$i], $results[$i]->userName);
            $I->assertEquals($this->getPresence($this->store2, $this->users[$i]), "true");
            $i++;
        }
    }

    /** @group database_transaction */
    public function testChatPics(ApiTester $I)
    {
        $I->wantTo('Test reps returned for carousel listing');
        $this->users = [];

        $response = $I->doDirectGet($this->app, "chat/queue/pics/fake-mall?primarySize=84&secondarySize=64");
        $results = json_decode($response->getContent());
        // check response is ok and user in queue
        $I->assertEquals(200, $response->getStatusCode());

        foreach ($results as $rep) {
            $I->assertTrue(!empty($rep->image)); // Mandatory
            $I->assertTrue(property_exists($rep, 'firstName')); // Now null is a possible value
            $I->assertTrue(property_exists($rep, 'lastName')); // Now null is a possible value
        }
    }

    /** @group database_transaction */
    public function testCoreHeartbeat(ApiTester $I)
    {
        $I->wantTo("Receive heartbeat notifications");

        $this->users = [];
        $this->testCoreConnectSeveralUsers($I);

        // Modify date in firefase to test heatbeat notification for 4 first users
        for ($i = 0; $i <= 3; $i++) {
            $timeStamp = $this->firebase->get($this->app['configs']['firebase.defaultpath'] . '/' . self::QUEUE . '/' . $this->store2 . '/' . $this->users[$i] . '/heartbeat');
            $this->firebase->set(
                $this->app['configs']['firebase.defaultpath'] .
                '/' .
                self::QUEUE .
                '/' .
                $this->store2 .
                '/' .
                $this->users[$i] .
                '/heartbeat',
                $timeStamp - ($this->app['configs']['chat.num_sec_before_heartbeat'] * 1000) - 2000
            );
        }

        // Run cron
        $response = $I->doDirectPost($this->app, "chat/heartbeat/" . $this->store2);

        $response = $I->doDirectGet($this->app, "chat/queue/$this->store2");
        $results = json_decode($response->getContent());

        // 3 First users (and that have a heartbeat timestamp older than x seconds) should be notified.
        for ($i = 0; $i <= 2; $i++) {
            $this->checkHeartbeatNotification($this->store2, $this->users[$i]);
        }

        // Others should not receive notification
        for ($i = 3; $i <= 9; $i++) {
            $this->checkUserNotNotified($this->store2, $this->users[$i]);
        }

        $I->wantTo("User disconnected when he doesn't answer to heartbeat");

        // Modify date in firefase to test heartbeat disconneted
        $timeStamp = intval($this->firebase->get($this->app['configs']['firebase.defaultpath'] . '/' . self::QUEUE . '/' . $this->store2 . '/' . $this->users[4] . '/heartbeatConfirmation')) ?: time();
        $this->firebase->set($this->app['configs']['firebase.defaultpath'] . '/' . self::QUEUE . '/' . $this->store2 . '/' . $this->users[4] . '/heartbeatConfirmation', $timeStamp - ($this->app['configs']['chat.num_sec_disconnect_rep_not_confirm'] * 1000) - 2000);

        // Run cron
        $response = $I->doDirectPost($this->app, "chat/heartbeat/" . $this->store2);

        // Check that use is disconnected
        $this->checkUserDisconnected($this->store2, $this->users[4]);

        $I->wantTo("User can answer to heartbeat");
        $response = $I->doDirectPost($this->app, "chat/heartbeat/answer/$this->store2/" . $this->users[0]);

        $I->assertEquals(200, $response->getStatusCode());
        $this->checkHeartbeatConfirmation($this->store2, $this->users[0]);
    }

    /** @group database_transaction */
    public function testCleanQueue(ApiTester $I)
    {
        $I->wantTo("Clean the queue");

        $this->users = [];
        $this->testCoreConnectSeveralUsers($I);

        // Delete user from presence
        $this->firebase->delete($this->app['configs']['firebase.defaultpath'] . '/' . self::PRESENCE . '/' . $this->store2 . '/' . $this->users[1]);
        // Add new user in presence
        $this->firebase->set($this->app['configs']['firebase.defaultpath'] . '/' . self::PRESENCE . '/' . $this->store2 . '/1234567', true);

        // Run cron to run cleaning
        $response = $I->doDirectPost($this->app, "chat/heartbeat/" . $this->store2);


        // Deleted user from presence should not be in queue anymore
        $userFromQueue = json_decode($this->getQueue($this->store2, $this->users[1]));
        $I->assertTrue(!isset($userFromQueue));
        // Added user in presence should be in queue
        $userFromQueue = json_decode($this->getQueue($this->store2, '1234567'));
        $this->checkUserInFirebase($this->store2, '1234567');
    }

    /** @group database_transaction */
    public function testCleanUser(ApiTester $I)
    {
        $I->wantTo("Clean users");

        $this->users = [];
        $this->testCoreConnectSeveralUsers($I);

        // Delete user from presence
        $this->firebase->delete($this->app['configs']['firebase.defaultpath'] . '/' . self::PRESENCE . '/' . $this->store2 . '/' . $this->users[1]);
        // Add new user in presence
        $this->firebase->set($this->app['configs']['firebase.defaultpath'] . '/' . self::PRESENCE . '/' . $this->store2 . '/1234567', true);

        // Run cron to run cleaning
        $response = $I->doDirectPost($this->app, "chat/heartbeat/" . $this->store2);


        // Deleted user from presence should not be in queue anymore
        $userFromQueue = json_decode($this->getQueue($this->store2, $this->users[1]));
        $I->assertTrue(!isset($userFromQueue));
        // Added user in presence should be in queue
        $userFromQueue = json_decode($this->getQueue($this->store2, '1234567'));
        $this->checkUserInFirebase($this->store2, '1234567');
    }

    /** @group database_transaction */
    public function testCleanRequests(ApiTester $I)
    {
        $I->wantTo("Clean requests");

        // Test with a new store request. It should still be in firebase
        $response = $I->doDirectGet($this->app, "tests/chat/timestamp");
        $now = json_decode($response->getContent());
        $id = uniqid();
        $this->setStoreRequestInFirebase('store1', $id, $now);
        $response = $I->doDirectPost($this->app, "tests/chat/cleanrequest/300");

        $I->assertEquals($now, $this->getStoreRequestFromFirebase('store1', $id));

        // Test with a old store request. It shouldn't still be in firebase
        $response = $I->doDirectGet($this->app, "tests/chat/timestamp");
        $now = json_decode($response->getContent());
        $id = uniqid();
        $this->setStoreRequestInFirebase('store2', $id, $now - 8000);
        $response = $I->doDirectPost($this->app, "tests/chat/cleanrequest/5");

        sleep(2);
        $I->assertEquals('null', $this->getStoreRequestFromFirebase('store2', $id));

        // Test with mix of new store requests and old store requests. It should keep only new requests
        $response = $I->doDirectGet($this->app, "tests/chat/timestamp");
        $now = json_decode($response->getContent());
        $id1 = uniqid();
        $this->setStoreRequestInFirebase('store1', $id1, $now);
        $id2 = uniqid();
        $this->setStoreRequestInFirebase('store1', $id2, $now - 10000);
        $id3 = uniqid();
        $this->setStoreRequestInFirebase('store1', $id3, $now - 12000);
        $id4 = uniqid();
        $this->setStoreRequestInFirebase('store2', $id4, $now);
        $id5 = uniqid();
        $this->setStoreRequestInFirebase('store2', $id5, $now + 2000);
        $id6 = uniqid();
        $this->setStoreRequestInFirebase('store2', $id6, $now - 10000);
        $response = $I->doDirectPost($this->app, "tests/chat/cleanrequest/5");

        sleep(2);
        $I->assertEquals($now, $this->getStoreRequestFromFirebase('store1', $id1));
        $I->assertEquals('null', $this->getStoreRequestFromFirebase('store1', $id2));
        $I->assertEquals('null', $this->getStoreRequestFromFirebase('store1', $id3));
        $I->assertEquals($now, $this->getStoreRequestFromFirebase('store2', $id4));
        $I->assertEquals($now + 2000, $this->getStoreRequestFromFirebase('store2', $id5));
        $I->assertEquals('null', $this->getStoreRequestFromFirebase('store2', $id6));

        // Test with a new rep request. It should still be in firebase
        $response = $I->doDirectGet($this->app, "tests/chat/timestamp");
        $now = json_decode($response->getContent());
        $id = uniqid();
        $this->setRepRequestInFirebase('user1', $id, $now);
        $response = $I->doDirectPost($this->app, "tests/chat/cleanrequest/300");

        sleep(2);
        $I->assertEquals($now, $this->getRepRequestFromFirebase('user1', $id));

        // Test with a old rep request. It shouldn't still be in firebase
        $response = $I->doDirectGet($this->app, "tests/chat/timestamp");
        $now = json_decode($response->getContent());
        $id = uniqid();
        $this->setRepRequestInFirebase('user1', $id, $now - 8000);
        $response = $I->doDirectPost($this->app, "tests/chat/cleanrequest/5");

        sleep(2);
        $I->assertEquals('null', $this->getRepRequestFromFirebase('user1', $id));

        // Test with a new rep request and 2 old rep requests. It should keep only new request
        $response = $I->doDirectGet($this->app, "tests/chat/timestamp");
        $now = json_decode($response->getContent());
        $id1 = uniqid();
        $this->setRepRequestInFirebase('user1', $id1, $now);
        $id2 = uniqid();
        $this->setRepRequestInFirebase('user1', $id2, $now - 10000);
        $id3 = uniqid();
        $this->setRepRequestInFirebase('user1', $id3, $now - 10000);
        $response = $I->doDirectPost($this->app, "tests/chat/cleanrequest/5");

        sleep(2);
        $I->assertEquals($now, $this->getRepRequestFromFirebase('user1', $id1));
        $I->assertEquals('null', $this->getRepRequestFromFirebase('user1', $id2));
        $I->assertEquals('null', $this->getRepRequestFromFirebase('user1', $id3));
    }

    /**
     * Chat Helpers
     */

    private function timeOk($dateTime)
    {
        // Check if time is ok (date should be less than 60 secs from now)
        $dateDiff =  strtotime(date('Y-m-d H:i:s')) - round(($dateTime / 1000));
        return $dateDiff < 60;
    }

    private function createFakeStore()
    {
        return 'test' . uniqid("store");
    }

    private function createFakeUser()
    {
        return 'test' . uniqid("user");
    }

    private function getQueue($store, $user)
    {
        return $this->firebase->get($this->app['configs']['firebase.defaultpath'] . '/' . self::QUEUE . '/' . $store . '/' . $user);
    }

    private function getPresence($store, $user)
    {
        return $this->firebase->get($this->app['configs']['firebase.defaultpath'] . '/' . self::PRESENCE . '/' . $store . '/' . $user);
    }

    private function checkUserDisconnected($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $userFromPresence = json_decode($this->getPresence($store, $user));
        $this->I->assertTrue(!isset($userFromQueue));
        $this->I->assertTrue(!isset($userFromPresence) || $userFromPresence == 'heartbeatMissed');
    }

    private function checkUserChatMiss($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $userFromPresence = json_decode($this->getPresence($store, $user));
        $this->I->assertTrue(!isset($userFromQueue));
        $this->I->assertEquals($userFromPresence, 'missed');
    }

    private function checkUserInFirebase($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $this->I->assertNotEmpty($userFromQueue);
        if (!empty($userFromQueue)) {
            $this->I->assertEquals($userFromQueue->userName, $user);
            $this->I->assertNotEmpty($userFromQueue->connection);
            $this->I->assertNotEmpty($userFromQueue->heartbeat);
        }

        $userFromPresence = json_decode($this->getPresence($store, $user));
        $this->I->assertNotEmpty($userFromPresence);
        $this->I->assertTrue($userFromPresence);
    }

    private function checkHeartbeatNotification($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $this->I->assertNotEmpty($userFromQueue);
        if (!empty($userFromQueue)) {
            $this->I->assertNotEmpty($userFromQueue->heartbeatConfirmation);
            $this->I->assertNotEmpty($userFromQueue->heartbeat);
            $this->I->assertTrue($this->timeOk($userFromQueue->heartbeatConfirmation));
            $this->I->assertTrue($this->timeOk($userFromQueue->heartbeat));
        }
    }

    private function checkHeartbeatConfirmation($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $this->I->assertNotEmpty($userFromQueue);
        if (!empty($userFromQueue)) {
            $this->I->assertTrue(!isset($userFromQueue->heartbeatConfirmation));
            $this->I->assertNotEmpty($userFromQueue->heartbeat);
            $this->I->assertTrue($this->timeOk($userFromQueue->heartbeat));
        }
    }

    private function checkNextOnQueueNotification($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $this->I->assertNotEmpty($userFromQueue);
        if (!empty($userFromQueue)) {
            $this->I->assertNotEmpty($userFromQueue->topOfQueue);
            $this->I->assertNotEmpty($userFromQueue->heartbeat);
        }
    }

    private function checkUserNotNotified($store, $user)
    {
        $userFromQueue = json_decode($this->getQueue($store, $user));
        $this->I->assertNotEmpty($userFromQueue);
        if (!empty($userFromQueue)) {
            $this->I->assertTrue(!isset($userFromQueue->heartbeatConfirmation));
        }
    }

    private function setStoreRequestInFirebase($store, $id, $time)
    {
        $this->firebase->set($this->app['configs']['firebase.defaultpath'] . '/' . self::FIREBASE_REQUEST_PATH . '/' . self::FIREBASE_REQUEST_STORES_PATH . '/' . $store . '/' . $id, $time);
    }

    private function setRepRequestInFirebase($user, $id, $time)
    {
        $this->firebase->set($this->app['configs']['firebase.defaultpath'] . '/' . self::FIREBASE_REQUEST_PATH . '/' . self::FIREBASE_REQUEST_REPS_PATH . '/' . $user . '/' . $id, $time);
    }

    private function getStoreRequestFromFirebase($store, $id)
    {
        return str_replace('"', '', $this->firebase->get($this->app['configs']['firebase.defaultpath'] . '/' . self::FIREBASE_REQUEST_PATH . '/' . self::FIREBASE_REQUEST_STORES_PATH . '/' . $store . '/' . $id));
    }

    private function getRepRequestFromFirebase($user, $id)
    {
        return str_replace('"', '', $this->firebase->get($this->app['configs']['firebase.defaultpath'] . '/' . self::FIREBASE_REQUEST_PATH . '/' . self::FIREBASE_REQUEST_REPS_PATH . '/' . $user . '/' . $id));
    }
}
