<?php

use Salesfloor\Models\RepOnboarding as RepOnboardingModel;
use SF\ApiTester;
use SF\api\BaseApi;
use Codeception\Stub\Expected;
use Salesfloor\Models\Task as TaskModel;
use Salesfloor\API\Managers\Client\Customers\Legacy as Customers;

/**
 * TODO: Dates in this test are flaky, for some reason. Figure out why so date checks can be restored.
 */
class TasksCest extends BaseApi
{
    /** @var  \Salesfloor\Services\MySQLRepository */
    public $repo;

    /** @var \Salesfloor\Services\Tasks\Tasks tasksService */
    public $tasksService;

    /** @var  integer $taskReminderCron */
    public $taskReminderCron;

    /** @var  \Salesfloor\API\Managers\Task\Tasks $tasksManager */
    public $tasksManager;

    // ID of the Fake Mall in the dump.sql
    const FAKE_MALL_STORE = 1003;
    const OTHER_MALL_STORE = 2003;

    const FM_RETAIL_STORE = 'lololol';
    const OM_RETAIL_STORE = 'hahahah';

    public $storeRetailerStore = [
        self::FAKE_MALL_STORE  => self::FM_RETAIL_STORE,
        self::OTHER_MALL_STORE => self::OM_RETAIL_STORE,
    ];

    // Valid Retailer Transaction Types
    const TYPE_SALE = 'Sale';
    const TYPE_RETURN = 'Return';
    const TYPE_CANCELLATION = 'Cancellation';

    // Table Names
    const TABLE_TASKS                 = \Salesfloor\API\Managers\Task\Tasks::TABLE_NAME;
    const TABLE_USERS                 = \Salesfloor\API\Managers\Reps::TABLE_NAME;
    const TABLE_USER_META             = \Salesfloor\API\Managers\UserMeta::TABLE_NAME;
    const TABLE_EVENTS                = 'sf_events';
    const TABLE_CUSTOMERS             = Customers::TABLE_NAME;
    const TABLE_CUSTOMER_MAPPING      = 'sf_customers_to_retailer_customers';
    const TABLE_RETAILER_CUSTOMERS    = 'sf_retailer_customers';
    const TABLE_RETAILER_TRANSACTIONS = 'sf_retailer_transaction';
    const TABLE_REP_ONBOARDING        = \Salesfloor\API\Managers\RepOnboarding::TABLE_NAME;
    const TABLE_OAUTH_SESSIONS        = \Salesfloor\API\Managers\OauthSessions::TABLE_NAME;

    const META_ABOUT_ME = \Salesfloor\API\Managers\UserMeta::KEY_REP_INTRODUCTION;

    /** @var \Salesfloor\Services\Event Event Service */
    public $eventService;
    /** @var string The TIME unit that the New Purchase Automated Task search will be conducted in (MYSQL Interval) i.e. DAY */
    public $newPurchaseUnit;
    /** @var integer The VALUE that the New Purchase Automated Task search will be conducted in i.e. 2 (DAY) */
    public $newPurchaseValue;

    public $soonToLapseDays;
    public $newCustomerPurchaseDays;

    // IDs to be used when generating test users / customers / transactions in a loop
    // Use the ID, then when done with, increment the value so that the next iteration has a fresh value to use
    public $currentUserId;
    public $currentCustomerId;
    public $currentTransactionId;

    public $autoServices;

    const BASE_USER_ADMIN = 100000;
    const BASE_USER_ONE = 100001;
    const BASE_USER_TWO = 100002;
    const BASE_USER_STORE_MANAGER = 100003;
    const BASE_USER_MANAGER = 100004;
    const BASE_USER_CORP_ADMIN = 100005;
    const BASE_USER_SALESFLOOR = 100006;
    const BASE_ROUTE = 'tasks';
    const BASE_ROUTE_SINGULAR = 'task';

    /** @var  \Salesfloor\API\Managers\Reps */
    public $repsManager;

    public function _before($I)
    {
        parent::_before($I);

        $task = \Codeception\Util\Stub::construct(
            $this->app['service.tasks'],
            [$this->app],
            [
                'sendEmailTaskReminder' => Expected::atLeastOnce(function () {
                    return true;
                }),
                'sendNotificationTaskReminder' => \Codeception\Stub\Expected::atLeastOnce(function () {
                    return true;
                }),
            ]
            // https://github.com/Codeception/Codeception/issues/2747
            // Stub is working, but atLeastOnce not and i'm not sure how to fix them
        );

        $this->tasksService = $task;
        $this->tasksManager = $this->app['tasks.manager'];

        $this->taskReminderCron = (int)$this->app['configs']['sf.task.reminder.cron'];

        $this->eventService     = $this->app['service.event'];
        $this->currentUserId    = $this->currentCustomerId = $this->currentTransactionId = 10001;

        $this->repsManager = $this->app['reps.manager'];

        $this->repo = $this->app['repositories.mysql'];
        $this->translationService = $this->app['translator'];
    }


    /** @group database_transaction */
    public function testGetTaskReminderByTypeManual(ApiTester $I)
    {
        $I->wantTo("Task services - reminder - by type manual");

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'reminder_date' => gmdate('Y-m-d H:i:s')
        ]);

        $nb = $this->tasksService->reminder();

        // PP-164 - Dates are flaky, unknown why.
//        $this->checkLastReminderDate($I, 1, true);

        $I->assertEquals(1, $nb);
    }


    /** @group database_transaction */
    public function testGetTaskReminderByTypeAutomatic(ApiTester $I)
    {
        $I->wantTo("Task services - reminder - by type automatic");

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_AUTOMATED,
            'reminder_date' => gmdate('Y-m-d H:i:s')
        ]);

        $nb = $this->tasksService->reminder();

        $I->assertEquals(1, $nb);
    }


    /** @group database_transaction */
    public function testGetTaskReminderByStatusUnresolved(ApiTester $I)
    {
        $I->wantTo("Task services - reminder - by status unresolved");

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'status' => TaskModel::STATUS_UNRESOLVED,
            'reminder_date' => gmdate('Y-m-d H:i:s')
        ]);

        $nb = $this->tasksService->reminder();

        // PP-164 - Dates are flaky, unknown why.
//        $this->checkLastReminderDate($I, 1, true);

        $I->assertEquals(1, $nb);
    }


    /** @group database_transaction */
    public function testGetTaskReminderByStatusResolved(ApiTester $I)
    {
        $I->wantTo("Task services - reminder - by status resolved");

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'status' => TaskModel::STATUS_RESOLVED,
            'reminder_date' => gmdate('Y-m-d H:i:s')
        ]);

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'status' => TaskModel::STATUS_DISMISSED,
            'reminder_date' => gmdate('Y-m-d H:i:s')
        ]);

        $nb = $this->tasksService->reminder();

        $I->assertEquals(0, $nb);
    }


    /** @group database_transaction */
    public function testGetTaskReminderByDateWithLastWithin1(ApiTester $I)
    {
        $I->wantTo("Task services - reminder - by date with last reminder date - within (1)");

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'status' => TaskModel::STATUS_UNRESOLVED,
            'reminder_date' => gmdate('Y-m-d H:i:s'),
            'last_reminder_date' => gmdate("Y-m-d H:i:s"),
        ]);

        $nb = $this->tasksService->reminder();

        // PP-164 - Dates are flaky, unknown why.
//        $this->checkLastReminderDate($I, 1, false);

        $I->assertEquals(0, $nb);
    }


    /** @group database_transaction */
    public function testGetTaskReminderByDateWithLastNotWithin2(ApiTester $I, $scenario)
    {
        $scenario->skip('flaky');

        $I->wantTo("Task services - reminder - by date with last reminder date - within (2)");

        // not within last_reminder_date
        $datetime = new DateTime();
        $datetime->modify(sprintf('-%d minutes', $this->taskReminderCron - 1));

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'status' => TaskModel::STATUS_UNRESOLVED,
            'reminder_date' => gmdate('Y-m-d H:i:s'),
            'last_reminder_date' => $datetime->format('Y-m-d H:i:s'),
        ]);

        $nb = $this->tasksService->reminder();

        // PP-164 - Dates are flaky, unknown why.
        // $this->checkLastReminderDate($I, 1, false);

        $I->assertEquals(0, $nb);
    }


    /** @group database_transaction */
    public function testGetTaskReminderByDateWithLastNotWithin(ApiTester $I)
    {
        $I->wantTo("Task services - reminder - by date with last reminder date - not within");

        // not within last_reminder_date
        $datetime = new DateTime();
        $datetime->modify(sprintf('-%d minutes', $this->taskReminderCron));

        $this->addTask($I, [
            'user_id' => 1,
            'task_category_id' => 1,
            'details' => 'default details',
            'type' => TaskModel::TYPE_MANUAL,
            'status' => TaskModel::STATUS_UNRESOLVED,
            'reminder_date' => gmdate('Y-m-d H:i:s'),
            'last_reminder_date' => $datetime->format('Y-m-d H:i:s'),
        ]);

        $nb = $this->tasksService->reminder();

        // PP-164 - Dates are flaky, unknown why.
//        $this->checkLastReminderDate($I, 1, true);

        $I->assertEquals(1, $nb);
    }

    /**
     * Look if the last_reminder_date is valid
     *
     * @param ApiTester $I
     * @param           $id             Task id
     * @param bool      $greater        Comparison direction
     */
    private function checkLastReminderDate(ApiTester $I, $id, $greater = false)
    {
        /** @var TaskModel $task */
        $task = $this->tasksManager->getOneOrNull([
            'id' => $id
        ], [], false); // NO CACHE

        $I->assertNotEmpty($task);

        // Some issue using assertGreaterThanOrEqual with string
        if ($greater) {
            $I->assertTrue($task->last_reminder_date >= $task->reminder_date);
        } else {
            $I->assertTrue($task->last_reminder_date <= $task->reminder_date);
        }
    }

    private function addTask(ApiTester $I, $option)
    {
        $I->haveInDatabase('sf_task', $option);
    }

    /**
     * Function will generate, insert and verify the existence of a new USER based on the inputs received
     * @param  ApiTester $I                ApiTester instance
     * @param  boolean   $isRepType        Whether new User should have type 'rep' or 'store'
     * @param  boolean   $isSelling        Whether new User should be selling_mode 1 or 0
     * @param  boolean   $isSalesfloorUser Whether new User should have user_login starting with salesfloor_ or not
     * @param  boolean   $isFakeMall       Whether user should belong to Fake Mall or to Other Mall store
     * @param  integer   $group            The group permission to assign to user
     *                                      (1 Rep, 2 Store Manager, 3 Manager, 4 Corp Admin, 5 SF Admin)
     * @param  integer   $joinedDaysBack   Number of days back that rep was registered
     * @return array                       The values that were inserted to represent the User
     */
    private function insertUser(
        ApiTester $I,
        $isRepType = true,
        $isSelling = true,
        $isSalesfloorUser = false,
        $isFakeMall = true,
        $group = 1,
        $joinedDaysBack = 100,
        $locale = null
    ) {
        if (null == $locale) {
            $locale = $this->app['configs']['retailer.i18n.default_locale'];
        }

        $username = ($isSalesfloorUser ? 'salesfloor_' : '') . 'user' . $this->currentUserId;
        $user = [
            'ID' => $this->currentUserId,
            'user_login' => $username,
            'user_pass' => 'pass',
            'user_nicename' => $username,
            'user_email' => $username . '@salesfloor.net',
            'user_status' => 1,
            'user_alias' => $username,
            'display_name' => $username,
            'store' => ($isFakeMall ? self::FAKE_MALL_STORE : self::OTHER_MALL_STORE),
            'type' => ($isRepType ? 'rep' : 'store'),
            'group' => $group,
            'selling_mode' => ($isSelling ? 1 : 0),
            'user_registered' =>  date('Y-m-d H:i:s', strtotime("-$joinedDaysBack days", strtotime('now'))),
            'locale' =>  $locale,
        ];
        $I->haveInDatabase(self::TABLE_USERS, $user);
        $I->seeInDatabase(self::TABLE_USERS, $user);
        $this->currentUserId++;
        return $user;
    }

    /**
     * Function returns (if exists) the valid service container for the requested
     * Automated Task Scanner by the given $taskType. Exception if not found
     * @param $taskType
     * @return \Salesfloor\Services\Tasks\Automated\NagUpdateStorefrontProducts
     * @throws Exception
     */
    private function getService($taskType)
    {
        $serviceName = 'service.tasks.automated.' . $taskType;
        if (isset($this->app[$serviceName])) {
            return $this->app[$serviceName];
        }
        throw new \Exception("Service '$serviceName' is undefined");
    }

    /**
     * Function will test that after creating a new task and passing it to the recordUpdateEvent
     * function a valid SF_EVENT_TASK_{MANUAL|AUTOMATED}_CREATED is created.
     * @param  ApiTester $I ApiTester Instance
     *
     * @group database_transaction
     */
    public function testKpiEventsWithValidNewTasks(ApiTester $I)
    {
        $I->wantTo("Test that KPI Events are recorded for valid new tasks");

        $source = __METHOD__ . gmdate('U');
        $expectedInDatabase = true;

        // Create automated task, send to record NEW function, check that task + event are recorded properly
        $automatedTask = $this->getTaskArray($I, 1, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $automatedTaskModel = $this->addTaskAndReturnObject($I, $automatedTask);

        $returned = $this->tasksService->recordUpdateEvent($automatedTaskModel, null, $source);
        $this->checkTaskAgainstKpiEvent($I, $automatedTaskModel, $source, $returned, $expectedInDatabase);

        // Create manual task, send to record NEW function, check that task + event are recorded properly
        $manualTask = $this->getTaskArray($I, 2, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_MANUAL);
        $manualTaskModel = $this->addTaskAndReturnObject($I, $manualTask);

        $returned = $this->tasksService->recordUpdateEvent($manualTaskModel, null, $source);
        $this->checkTaskAgainstKpiEvent($I, $manualTaskModel, $source, $returned, $expectedInDatabase);
    }

    /**
     * Function will test that after creating a new task, if it is improperly set up and you
     * pass it to the recordUpdateEvent, no KPI Event will be recorded.
     * @param  ApiTester $I ApiTester Instance
     *
     * @group database_transaction
     */
    public function testKpiEventsWithInvalidNewTasks(ApiTester $I)
    {
        $I->wantTo("Test that KPI Events are recorded for in-valid new tasks");

        $source = __METHOD__ . gmdate('U');
        $expectedInDatabase = false;

        // Create automated task, set to RESOLVED (bad), send to record NEW function, check that event is not recorded
        $automatedTask = $this->getTaskArray($I, 1, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $automatedTaskModel = $this->addTaskAndReturnObject($I, $automatedTask);
        $automatedTaskModel = $this->completeTask($I, $automatedTaskModel, true, 'Notes');

        $returned = $this->tasksService->recordUpdateEvent($automatedTaskModel, null, $source);
        $this->checkTaskAgainstKpiEvent($I, $automatedTaskModel, $source, $returned, $expectedInDatabase);

        // Create automated task, set to DISMISSED (bad), send to record NEW function, check that event is not recorded
        $automatedTask = $this->getTaskArray($I, 2, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $automatedTaskModel = $this->addTaskAndReturnObject($I, $automatedTask);
        $automatedTaskModel = $this->completeTask($I, $automatedTaskModel, false, 'Notes');

        $returned = $this->tasksService->recordUpdateEvent($automatedTaskModel, null, $source);
        $this->checkTaskAgainstKpiEvent($I, $automatedTaskModel, $source, $returned, $expectedInDatabase);
    }

    /**
     * Function will test that after updating a task and passing it to the recordUpdateEvent
     * function a valid SF_EVENT_TASK_{MANUAL|AUTOMATED}_{RESOLVED|DISMISSED} is created.
     * @param  ApiTester $I ApiTester Instance
     *
     * @group database_transaction
     */
    public function testKpiEventsWithValidUpdatedTasks(ApiTester $I)
    {
        $I->wantTo("Test that KPI Events are recorded for valid Updated tasks");

        $source = __METHOD__ . gmdate('U');
        $expectedInDatabase = true;

        // Create automated task, set to DISMISSED (good), send to record UPDATED function against original, check that UPDATE event is recorded
        $automatedTask = $this->getTaskArray($I, 1, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $automatedTaskModel = $this->addTaskAndReturnObject($I, $automatedTask);
        $originalAutomatedTaskModel = clone $automatedTaskModel;
        // Dismiss the task
        $automatedTaskModel = $this->completeTask($I, $automatedTaskModel, false, 'Notes');

        $returned = $this->tasksService->recordUpdateEvent($automatedTaskModel, $originalAutomatedTaskModel, $source);
        $this->checkTaskAgainstKpiEvent($I, $automatedTaskModel, $source, $returned, $expectedInDatabase);

        // Create manual task, set to RESOLVED (good), send to record UPDATED function against original, check that UPDATE event is recorded
        $manualTask = $this->getTaskArray($I, 2, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_MANUAL);
        $manualTaskModel = $this->addTaskAndReturnObject($I, $manualTask);
        $originalManualTaskModel = clone $manualTaskModel;
        // Resolve the task
        $manualTaskModel = $this->completeTask($I, $manualTaskModel, true, 'Notes');

        $returned = $this->tasksService->recordUpdateEvent($manualTaskModel, $originalManualTaskModel, $source);
        $this->checkTaskAgainstKpiEvent($I, $manualTaskModel, $source, $returned, $expectedInDatabase);
    }

    /**
     * Function will test that after updating a task, if it is improperly set up and you
     * pass it to the recordUpdateEvent, no KPI Event will be recorded.
     * @param  ApiTester $I ApiTester Instance
     *
     * @group database_transaction
     */
    public function testKpiEventsWithInalidUpdatedTasks(ApiTester $I)
    {
        $I->wantTo("Test that KPI Events are not recorded for invalid Updated tasks");

        $source = __METHOD__ . gmdate('U');
        $expectedInDatabase = false;

        // Scenario 1: Two different task ids received -> No event recorded
        $taskOne = $this->getTaskArray($I, 1, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $taskOneModel = $this->addTaskAndReturnObject($I, $taskOne);
        $taskTwo = $this->getTaskArray($I, 2, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $taskTwoModel = $this->addTaskAndReturnObject($I, $taskTwo);
        $returned = $this->tasksService->recordUpdateEvent($taskOneModel, $taskTwoModel, $source);
        $this->checkTaskAgainstKpiEvent($I, $taskOneModel, $source, $returned, $expectedInDatabase);
        $this->checkTaskAgainstKpiEvent($I, $taskTwoModel, $source, $returned, $expectedInDatabase);

        // Scenario 2: Task still has same status as before (Other fields are updated) -> No event recorded
        // Scenario 3: Task is still unresolved (overlaps with scenario 2) -> No event recorded
        $taskUnresolved = $this->getTaskArray($I, 3, TaskModel::STATUS_UNRESOLVED, TaskModel::TYPE_AUTOMATED);
        $taskUnresolvedModel = $this->addTaskAndReturnObject($I, $taskUnresolved);
        $originalModel = clone $taskUnresolvedModel;
        $taskUnresolvedModel->details = 'Changed the details';
        $this->tasksManager->save($taskUnresolvedModel);
        $returned = $this->tasksService->recordUpdateEvent($taskUnresolvedModel, $originalModel, $source);
        $this->checkTaskAgainstKpiEvent($I, $taskUnresolvedModel, $source, $returned, $expectedInDatabase);
        $this->checkTaskAgainstKpiEvent($I, $originalModel, $source, $returned, $expectedInDatabase);
    }

    /**
     * Function will check whether or not an appropriate KPI Event was recorded in relation to the given $task
     * Based on the $expectedInDatabase parameter. If true, we should find the event, if false, we shouldn't
     * parameter
     * @param  ApiTester $I                  ApiTester instance
     * @param  TaskModel $task               The task to model the KPI event off of
     * @param  string    $source             The source that was passed to recordUpdateEvent function. Should be in the KPI Event `source` column
     * @param  boolean   $valueReturned      The value that was returned from the recordUpdateEvent. Represents whether or not something was recorded
     * @param  boolean   $expectedInDatabase Whether to check seeInDatabase or dontSeeInDatabase for the KPI Event
     */
    private function checkTaskAgainstKpiEvent(ApiTester $I, TaskModel $task, $source, $valueReturned, $expectedInDatabase = true)
    {
        $taskEventTypeName = $this->tasksService->getTaskEventType($task);
        $taskEventType = constant('Salesfloor\Services\Event::' . $taskEventTypeName);

        $testerFunction = ($expectedInDatabase ? 'seeInDatabase' : 'dontSeeInDatabase');
        $I->$testerFunction(self::TABLE_EVENTS, [
            'type' => $taskEventType,
            'source' =>  $source,
            'customer_id' => (empty($task->customer_id) ? 0 : $task->customer_id),
            'user_id' => $task->user_id,
            'attributes' => intval($task->getId()),
        ]);

        $assertionFunction = ($expectedInDatabase ? 'assertTrue' : 'assertFalse');
        $I->$assertionFunction($valueReturned);
    }

    /**
     * Function builds an array that can be used to create a TaskModel
     * @param  ApiTester    $I                ApiTester instance
     * @param  integer      $userId           User for Task
     * @param  string       $status           Status for Task
     * @param  string       $type             type for Task
     * @param  string|null  $details          details for Task
     * @param  string|null  $reminderDate     reminderDate for Task
     * @param  integer|null $customerId       customerId for Task
     * @param  integer|null $taskCategoryId   taskCategoryId for Task
     * @param  string|null  $resolutionNote   resolutionNote for Task
     * @param  string|null  $resolutionDate   resolutionDate for Task
     * @param  string|null  $lastReminderDate lastReminderDate for Task
     * @return array                          Array with populated fields that can be used to create Task
     */
    private function getTaskArray(ApiTester $I, $userId, $status, $type, $details = null, $reminderDate = null, $customerId = null, $taskCategoryId = null, $resolutionNote = null, $resolutionDate = null, $lastReminderDate = null)
    {
        $completed = (in_array($status, [TaskModel::STATUS_RESOLVED, TaskModel::STATUS_DISMISSED]) ? true : false);
        return [
            'user_id' => $userId,
            'customer_id' => $customerId,
            'task_category_id' => $taskCategoryId,
            'status' => $status,
            'type' => $type,
            'details' => (empty($details) ? 'Default Details' : $details),
            'resolution_note' => (empty($resolutionNote) && $completed ? 'Default Resolution Note' : $resolutionNote),
            'resolution_date' => (empty($resolutionDate) && $completed ? $I->now() : $resolutionDate),
            'reminder_date' => (empty($reminderDate) ? $I->now() : $reminderDate),
            'last_reminder_date' => $lastReminderDate,
        ];
    }

    /**
     * Function creates a TaskModel object based on the $options array received which should contain
     * the fields and values to be populated into the TaskModel
     * @param ApiTester   $I             ApiTester instance
     * @param array       $options       Fields / values to populate TaskModel
     * @return  TaskModel                Created TaskModel
     */
    private function addTaskAndReturnObject(ApiTester $I, $options)
    {
        $model = $this->tasksManager->create($options);
        if (empty($model)) {
            throw new \Exception('Error saving Task');
        }
        $this->tasksManager->save($model);
        $I->seeInDatabase(self::TABLE_TASKS, $model->toArray(true));
        return $model;
    }

    /**
     * Function will complete the given $task by RESOLVING / DISMISSING it based
     * on the $resolve boolean. It will also update the resolution note in the case
     * of RESOLVING
     * @param  ApiTester   $I              ApiTester instance
     * @param  TaskModel   $task           TaskModel to update
     * @param  boolean     $resolve        Set to 'resolved' if TRUE / 'dismissed' if FALSE
     * @param  string|null $resolutionNote The note to apply to TaskModel if Resolving. Wil be default filled if null
     * @return TaskModel                   The 'completed' Task
     */
    private function completeTask(ApiTester $I, TaskModel $task, $resolve = true, $resolutionNote = null)
    {
        $task->status = ($resolve ? TaskModel::STATUS_RESOLVED : TaskModel::STATUS_DISMISSED);
        if ($resolve) {
            $task->resolution_note = (empty($resolutionNote) ? 'Default Resolution Note' : $resolutionNote);
        }
        $this->tasksManager->save($task);
        $I->seeInDatabase(self::TABLE_TASKS, $task->toArray(true));
        return $task;
    }

    private function insertRouteTestingBaseData(ApiTester $I)
    {
        return [
            'users' => $this->insertRouteTestingBaseUsers($I),
            'customers' => $this->insertRouteTestingBaseCustomers($I),
            'tasks' => $this->insertRouteTestingBaseTasks($I),
        ];
    }

    private function insertRouteTestingBaseTasks(ApiTester $I)
    {
        $tasks = [
            [
                'id' => 1001,
                'user_id' => self::BASE_USER_ONE,
                'customer_id' => self::BASE_USER_ONE,
                'task_category_id' => 1,
                'details' => 'Details',
                'type' => TaskModel::TYPE_MANUAL,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'status' => TaskModel::STATUS_UNRESOLVED,
            ],
            [
                'id' => 1002,
                'user_id' => self::BASE_USER_ONE,
                'task_category_id' => 2,
                'details' => 'Details',
                'type' => TaskModel::TYPE_MANUAL,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'status' => TaskModel::STATUS_UNRESOLVED,
            ],
            [
                'id' => 1003,
                'user_id' => self::BASE_USER_ONE,
                'customer_id' => self::BASE_USER_ONE,
                'task_category_id' => 3,
                'details' => 'Details',
                'type' => TaskModel::TYPE_MANUAL,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'status' => TaskModel::STATUS_UNRESOLVED,
            ],
            [
                'id' => 1004,
                'user_id' => self::BASE_USER_TWO,
                'task_category_id' => 1,
                'details' => 'Details',
                'type' => TaskModel::TYPE_MANUAL,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'status' => TaskModel::STATUS_UNRESOLVED,
            ],
            [
                'id' => 1005,
                'user_id' => self::BASE_USER_TWO,
                'customer_id' => self::BASE_USER_TWO,
                'task_category_id' => null,
                'details' => 'Details',
                'type' => TaskModel::TYPE_MANUAL,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'status' => TaskModel::STATUS_UNRESOLVED,
            ],
            [
                'id' => 1006,
                'user_id' => self::BASE_USER_TWO,
                'task_category_id' => null,
                'details' => 'Details',
                'status' => TaskModel::STATUS_RESOLVED,
                'resolution_note' => 'Resolution Note!',
                'type' => TaskModel::TYPE_MANUAL,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
            ],
            [
                'id' => 1007,
                'user_id' => self::BASE_USER_TWO,
                'task_category_id' => null,
                'details' => 'Details',
                'type' => TaskModel::TYPE_AUTOMATED,
                'automated_type' => TaskModel::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED,
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'status' => TaskModel::STATUS_UNRESOLVED,
            ],
        ];
        $this->insertBulkRows($I, self::TABLE_TASKS, $tasks);
        return $tasks;
    }

    private function insertRouteTestingBaseCustomers(ApiTester $I)
    {
        $customers = [
            self::BASE_USER_ONE => [
                'ID' => self::BASE_USER_ONE,
                'user_id' => self::BASE_USER_ONE,
                'email' => self::BASE_USER_ONE . '@test.com',
                'name' => 'C-' . self::BASE_USER_ONE,
                'phone' => self::BASE_USER_ONE,
                'localization' => 'en',
                'first_name' => 'C-' . self::BASE_USER_ONE,
                'last_name' => 'C-' . self::BASE_USER_ONE,
            ],
            self::BASE_USER_TWO => [
                'ID' => self::BASE_USER_TWO,
                'user_id' => self::BASE_USER_TWO,
                'email' => self::BASE_USER_TWO . '@test.com',
                'name' => 'C-' . self::BASE_USER_TWO,
                'phone' => self::BASE_USER_TWO,
                'localization' => 'en',
                'first_name' => 'C-' . self::BASE_USER_TWO,
                'last_name' => 'C-' . self::BASE_USER_TWO,
            ],
        ];
        $this->insertBulkRows($I, self::TABLE_CUSTOMERS, $customers);
        return $customers;
    }

    private function insertRouteTestingBaseUsers(ApiTester $I)
    {
        $users = [
            self::BASE_USER_ADMIN => [
                'ID' => self::BASE_USER_ADMIN,
                'user_login' => 'Admin',
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::FAKE_MALL_STORE,
                'type' => 'rep',
                'group' => 5,
                'selling_mode' => 1
            ],
            self::BASE_USER_ONE => [
                'ID' => self::BASE_USER_ONE,
                'user_login' => 'User-' . self::BASE_USER_ONE,
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::FAKE_MALL_STORE,
                'type' => 'rep',
                'group' => 1,
                'selling_mode' => 1
            ],
            self::BASE_USER_TWO => [
                'ID' => self::BASE_USER_TWO,
                'user_login' => 'User-' . self::BASE_USER_TWO,
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::OTHER_MALL_STORE,
                'type' => 'rep',
                'group' => 1,
                'selling_mode' => 1
            ],
            self::BASE_USER_STORE_MANAGER => [
                'ID' => self::BASE_USER_STORE_MANAGER,
                'user_login' => 'User-' . self::BASE_USER_STORE_MANAGER,
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::FAKE_MALL_STORE,
                'type' => 'rep',
                'group' => 2,
                'selling_mode' => 1
            ],
            self::BASE_USER_MANAGER => [
                'ID' => self::BASE_USER_MANAGER,
                'user_login' => 'User-' . self::BASE_USER_MANAGER,
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::FAKE_MALL_STORE,
                'type' => 'rep',
                'group' => 3,
                'selling_mode' => 1
            ],
            self::BASE_USER_CORP_ADMIN => [
                'ID' => self::BASE_USER_CORP_ADMIN,
                'user_login' => 'User-' . self::BASE_USER_CORP_ADMIN,
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::FAKE_MALL_STORE,
                'type' => 'rep',
                'group' => 4,
                'selling_mode' => 1
            ],
            self::BASE_USER_SALESFLOOR => [
                'ID' => self::BASE_USER_SALESFLOOR,
                'user_login' => 'salesfloor_' . self::BASE_USER_SALESFLOOR,
                'user_pass' => 'pass',
                'user_status' => 1,
                'store' => self::FAKE_MALL_STORE,
                'type' => 'rep',
                'group' => 4,
                'selling_mode' => 1
            ],
        ];
        $this->insertBulkRows($I, self::TABLE_USERS, $users);
        return $users;
    }

    private function insertBulkRows(ApiTester $I, $table, array $rows)
    {
        foreach ($rows as $row) {
            $I->haveInDatabase($table, $row);
        }
    }

    private function buildUrl($urlEnd = null, $perPage = null, $pluralTaskRoute = true)
    {
        return ($pluralTaskRoute ? self::BASE_ROUTE : self::BASE_ROUTE_SINGULAR) . (!empty($urlEnd) ? '/' . $urlEnd : '') . (!empty($perPage) && $perPage > 0 ? '?per_page=' . $perPage : '');
    }

    private function removeAndReturnPagination(array &$results)
    {
        if (!isset($results['pagination'])) {
            return [];
        }

        $pagination = $results['pagination'];
        unset($results['pagination']);
        return $pagination;
    }

    /** @group database_transaction */
    public function testTaskRouteGetAllByManager(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);

        // Check that GetAll tasks cannot be called by MANAGER or below
        $userManager = $inserted['users'][self::BASE_USER_MANAGER];
        $response = $I->doDirectGet($this->app, $this->buildUrl(null, 100), [], $userManager['ID'], $userManager['user_login']);
        $I->assertEquals(403, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testTaskRouteGetAllByCorpAdmin(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);

        // Check that GetAll tasks can be called by CORP ADMIN and above
        $userCorpAdmin = $inserted['users'][self::BASE_USER_CORP_ADMIN];
        $response = $I->doDirectGet(
            $this->app,
            $this->buildUrl(null, 100),
            [],
            $userCorpAdmin['ID'],
            $userCorpAdmin['user_login']
        );
        $I->assertEquals(200, $response->getStatusCode());
        $results = json_decode($response->getContent(), true);
        $this->validateTaskRouteResults($I, $inserted, $results, []);
    }

    /** @group database_transaction */
    public function testTaskRouteGetCountByManager(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('count');
        // Check that GetAllCount tasks cannot be called by MANAGER or below
        $userManager = $inserted['users'][self::BASE_USER_MANAGER];
        $response = $I->doDirectGet($this->app, $route, [], $userManager['ID'], $userManager['user_login']);
        $I->assertEquals(403, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testTaskRouteGetCountByCorpAdmin(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('count');
        // Check that GetAllCount tasks can be called by CORP ADMIN and above
        $userCorpAdmin = $inserted['users'][self::BASE_USER_CORP_ADMIN];
        $response = $I->doDirectGet($this->app, $route, [], $userCorpAdmin['ID'], $userCorpAdmin['user_login']);
        $I->assertEquals(200, $response->getStatusCode());
        $results = json_decode($response->getContent(), true);
        $this->validateTaskRouteResults($I, $inserted, $results, []);
    }

    private function getExpectedEntriesFromArray(array $inserted, $type, array $restrictions)
    {
        if (empty($inserted[$type])) {
            return [];
        }
        $found = [];
        foreach ($inserted[$type] as $row) {
            $match = true;
            foreach ($restrictions as $key => $expectedValue) {
                if (!isset($row[$key]) || $row[$key] != $expectedValue) {
                    $match = false;
                    break;
                }
            }
            if ($match) {
                $found[] = $row;
            }
        }
        return $found;
    }

    /** @group database_transaction */
    public function testTaskRouteGetAllForUserByWrongUser(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('user/' . self::BASE_USER_ONE, 100);
        // Check that GetAllForUser tasks cannot be called by wrong user
        $userTwo = $inserted['users'][self::BASE_USER_TWO];
        $response = $I->doDirectGet($this->app, $route, [], $userTwo['ID'], $userTwo['user_login']);
        $I->assertEquals(403, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testTaskRouteGetAllForUserByCorrectUser(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('user/' . self::BASE_USER_ONE, 100);

        // Check that GetAll tasks can be called by correct user
        $userOne = $inserted['users'][self::BASE_USER_ONE];
        $response = $I->doDirectGet($this->app, $route, [], $userOne['ID'], $userOne['user_login']);
        $I->assertEquals(200, $response->getStatusCode());
        $results = json_decode($response->getContent(), true);
        $this->validateTaskRouteResults($I, $inserted, $results, ['user_id' => self::BASE_USER_ONE]);
    }

    private function validateTaskRouteResults(ApiTester $I, array $inserted, $results, $searchBy = [])
    {
        if (is_array($results)) {
            // Check general results returned
            $pagination = $this->removeAndReturnPagination($results);
        }
        $expectedEntries = $this->getExpectedEntriesFromArray($inserted, 'tasks', $searchBy);

        if (!empty($pagination)) {
            $I->assertEquals(count($expectedEntries), $pagination['count']);
        }

        // If results is array, validate each entry against the expected
        if (is_array($results)) {
            $I->assertEquals(count($expectedEntries), count($results));
            $i = 0;
            foreach ($expectedEntries as $entry) {
                $this->checkArray($I, $entry, $results[$i]);
                $i++;
            }
        } elseif (is_numeric($results)) {
            // Else if results is number, it represents a final count, validate against the expected
            $I->assertEquals(count($expectedEntries), $results);
        }
    }

    /** @group database_transaction */
    public function testTaskRouteGetCountForUserByWrongUser(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('user/' . self::BASE_USER_ONE . '/count');
        // Check that GetCountForUser tasks cannot be called by wrong user
        $userTwo = $inserted['users'][self::BASE_USER_TWO];
        $response = $I->doDirectGet($this->app, $route, [], $userTwo['ID'], $userTwo['user_login']);
        $I->assertEquals(403, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testTaskRouteGetCountForUserByCorrectUser(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('user/' . self::BASE_USER_ONE . '/count');
        // Check that GetCountForUser tasks can be called by correct user
        $userOne = $inserted['users'][self::BASE_USER_ONE];
        $response = $I->doDirectGet($this->app, $route, [], $userOne['ID'], $userOne['user_login']);
        $I->assertEquals(200, $response->getStatusCode());
        $results = json_decode($response->getContent(), true);
        $this->validateTaskRouteResults($I, $inserted, $results, ['user_id' => self::BASE_USER_ONE]);
    }

    /** @group database_transaction */
    public function testTaskRouteGetFiltersForUserByWrongUser(ApiTester $I)
    {
        $I->wantTo('Test task route get filters for user by wrong user.');
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('user/' . self::BASE_USER_ONE . '/filters');
        // Check that GetCountForUser tasks cannot be called by wrong user
        $userTwo = $inserted['users'][self::BASE_USER_TWO];
        $response = $I->doDirectGet($this->app, $route, [], $userTwo['ID'], $userTwo['user_login']);
        $I->assertEquals(403, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testTaskRouteGetFiltersForUserByCorrectUser(ApiTester $I)
    {
        $I->wantTo('Test task route get filters for user by correct user.');
        $inserted = $this->insertRouteTestingBaseData($I);
        $route = $this->buildUrl('user/' . self::BASE_USER_ONE . '/filters');
        // Check that GetCountForUser tasks can be called by correct user
        $userOne = $inserted['users'][self::BASE_USER_ONE];
        $response = $I->doDirectGet($this->app, $route, [], $userOne['ID'], $userOne['user_login']);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        // Check that each of the status filters is returned
        $checkedFilters = 0;
        foreach (TaskModel::$statuses as $status) {
            $searchBy = ['user_id' => self::BASE_USER_ONE, 'status' => $status];
            $expectedTasks = $this->getExpectedEntriesFromArray($inserted, 'tasks', $searchBy);

            $expectedResult = [
                $status => [
                    'key' => $status,
                    'count' => count($expectedTasks),
                    'type' => 'status'
                ]
            ];
            $this->checkArray($I, $expectedResult, $results);
            $checkedFilters++;
        }

        $this->checkArray(
            $I,
            [
                'unresolved' => [
                    'key' => 'unresolved',
                    'name' => 'All Active',
                    'query' => 'filter[status]=unresolved&sort=reminder_date',
                    'count' => 3,
                    'type' => 'status'
                ],
                'upcoming' => [
                    'key' => 'upcoming',
                    'name' => 'Upcoming',
                    'query' => 'filter[status]=unresolved&filter[is_upcoming]=1&sort=reminder_date',
                    'count' => 0,
                    'type' => 'custom'
                ],
                'due' => [
                    'key' => 'due',
                    'name' => 'Due Today',
                    'query' => 'filter[status]=unresolved&filter[is_due]=1&sort=reminder_date',
                    'count' => 3,
                    'type' => 'custom'
                ],
                'overdue' => [
                    'key' => 'overdue',
                    'name' => 'Overdue',
                    'query' => 'filter[status]=unresolved&filter[is_overdue]=1&sort=reminder_date',
                    'count' => 0,
                    'type' => 'custom'
                ],
                'dismissed' => [
                    'key' => 'dismissed',
                    'name' => 'Dismissed',
                    'query' => 'filter[status]=dismissed&sort=-resolution_date',
                    'count' => 0,
                    'type' => 'status'
                ],
                'resolved' => [
                    'key' => 'resolved',
                    'name' => 'Resolved',
                    'query' => 'filter[status]=resolved&sort=-resolution_date',
                    'count' => 0,
                    'type' => 'status'
                ],
            ],
            $results
        );
    }

    /** @group database_transaction */
    public function testTaskRouteGetOneByWrongUser(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);

        // Identify a task belonging to user 1
        $expectedTasks = $this->getExpectedEntriesFromArray($inserted, 'tasks', ['user_id' => self::BASE_USER_ONE]);
        $task = $expectedTasks[0];

        $route = $this->buildUrl($task['id'], null, false);
        // Check that GetOne tasks cannot returns no data for wrong user
        $userTwo = $inserted['users'][self::BASE_USER_TWO];
        $response = $I->doDirectGet($this->app, $route, [], $userTwo['ID'], $userTwo['user_login']);
        // Not being specific on code since this is not controller in the controller / manager of tasks, but by the Base Code
        $I->assertNotEquals(200, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testTaskRouteGetOneByCorrectUser(ApiTester $I)
    {
        $inserted = $this->insertRouteTestingBaseData($I);

        // Identify a task belonging to user 1
        $expectedTasks = $this->getExpectedEntriesFromArray($inserted, 'tasks', ['user_id' => self::BASE_USER_ONE]);
        $task = $expectedTasks[0];

        $route = $this->buildUrl($task['id'], null, false);
        // Check that GetAll tasks can be called by correct user
        $userOne = $inserted['users'][self::BASE_USER_ONE];
        $response = $I->doDirectGet($this->app, $route, [], $userOne['ID'], $userOne['user_login']);
        $I->assertEquals(200, $response->getStatusCode());
        $results = json_decode($response->getContent());

        $this->validateTaskRouteResults(
            $I,
            $inserted,
            [$results],
            [
                'user_id' => self::BASE_USER_ONE,
                'id' => $task['id']
            ]
        );
    }

    /** @group database_transaction */
    public function testTaskRouteCreate(ApiTester $I)
    {
        $users = $this->insertRouteTestingBaseUsers($I);
        $newTask = [
            'user_id' => self::BASE_USER_ONE,
            'task_category_id' => 1,
            'details' => 'Details',
            'type' => TaskModel::TYPE_MANUAL,
            'reminder_date' => gmdate('Y-m-d H:i:00'),
        ];
        $route = $this->buildUrl(null, null, false);
        // Check that route is not reachable unless authorized
        $headers = [
            'Authorization' => null,
        ];
        $response = $I->doDirectPostWithHeaders($this->app, $route, $newTask, $headers);
        $I->assertNotEquals(200, $response->getStatusCode());

        // Check that create tasks can be called once logged in
        $userOne = $users[self::BASE_USER_ONE];
        $response = $I->doDirectPost($this->app, $route, $newTask, $userOne['ID'], $userOne['user_login']);
        $results = json_decode($response->getContent(), true);
        $I->assertEquals(200, $response->getStatusCode());

        // Check response that task has been added
        $this->validateTaskRouteResults($I, ['tasks' => [$newTask]], [$results], $newTask);
        $I->assertNotEmpty($results['id']);
        $I->assertEquals(TaskModel::STATUS_UNRESOLVED, $results['status']);

        // Check that valid KPI event has been created
        $task = $this->tasksManager->getOne(['id' => $results['id']]);
        $expectedEvent = [
            'type' => $this->eventService->eventsList[$this->tasksService->getTaskEventType($task)],
            'user_id' => $task->user_id,
            'customer_id' => (empty($task->customer_id) ? 0 : $task->customer_id),
            'attributes' => $task->id,
        ];
        $I->seeInDatabase(self::TABLE_EVENTS, $expectedEvent);
    }

    /** @group database_transaction */
    public function testTaskRouteUpdate(ApiTester $I)
    {
        // Insert original task
        $users = $this->insertRouteTestingBaseUsers($I);
        $newTask = [
            'id' => 1,
            'user_id' => self::BASE_USER_ONE,
            'task_category_id' => 1,
            'details' => 'Details',
            'type' => TaskModel::TYPE_MANUAL,
            'reminder_date' => gmdate('Y-m-d H:i:00'),
        ];
        $this->insertBulkRows($I, self::TABLE_TASKS, [$newTask]);

        // Update the details field
        $update = [
            'details' => 'SOME UPDATED DETAILS'
        ];

        // Check that update tasks can be called once logged in
        $route = $this->buildUrl(1, null, false);
        $userOne = $users[self::BASE_USER_ONE];
        $response = $I->doDirectPut($this->app, $route, $update, $userOne['ID'], $userOne['user_login']);
        $results = json_decode($response->getContent(), true);
        $I->assertEquals(200, $response->getStatusCode());

        // Check response that task has been added
        $mergedTask = array_merge($newTask, $update);
        $this->validateTaskRouteResults($I, ['tasks' => [$mergedTask]], [$results], $mergedTask);
        $I->assertEquals($update['details'], $results['details']);
        $I->assertNotEmpty($results['updated_at']);
    }

    /** @group database_transaction */
    public function testTaskRouteCategories(ApiTester $I)
    {
        $users = $this->insertRouteTestingBaseUsers($I);
        $taskCategoriesManager = $this->app['task_categories.manager'];
        $allCategories = $taskCategoriesManager->getAll([], -1, -1);

        $route = 'task-categories';
        // Check that route is not reachable unless authorized
        $headers = [
            'Authorization' => null,
        ];
        $response = $I->doDirectGetWithHeaders($this->app, $route, [], $headers);
        $I->assertNotEquals(200, $response->getStatusCode());

        // Check that Task Categories can be called once logged in
        $userOne = $users[self::BASE_USER_ONE];
        $response = $I->doDirectGet($this->app, $route, [], $userOne['ID'], $userOne['user_login']);
        $results = json_decode($response->getContent(), true);
        $pagination = $this->removeAndReturnPagination($results);
        $I->assertEquals(200, $response->getStatusCode());

        // Check that each existing task category is received from the call
        $I->assertEquals(count($allCategories), count($results));
        $I->assertEquals(count($allCategories), $pagination['count']);
        $i = 0;
        foreach ($allCategories as $category) {
            $category = $category->toArray(true);
            $this->checkArray($I, $category, $results[$i]);
            $i++;
        }
    }

    /**
     * Function returns an array of objects & variables that are needed for all
     * Automated Nag Onboarding scenarios (About Me / Profile Pic / Social Network / Contacts)
     * based on the given $taskType i.e. 'nag_onboarding_connect_social_media'
     * @param $taskType
     * @return array
     */
    private function setupAutomatedNagOnboarding($taskType)
    {
        $service = $this->getService($taskType);
        $daysExpected = $service->getConfig($service::CONFIG_KEY_DAYS_SEARCH_BACK);

        $type = TaskModel::TYPE_AUTOMATED;
        $automatedType = $taskType;
        $status = TaskModel::STATUS_UNRESOLVED;

        $expectedTasks = $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS);

        return [$service, $daysExpected, $expectedTasks, $type, $automatedType, $status];
    }

    /**********************************************************
     * Tests for Automated - Nag Onboarding - Update About Me *
     **********************************************************/

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeWithValidUserThatHasNoAboutMe(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType, $status) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $tasksCreated = $service->process();
        $I->assertEquals(1, $tasksCreated);
        $this->validateInTask(true, $user['ID'], null, $type, $automatedType, null, $status);
        $expectedTasks++;
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeDoesNotCreateTwoTasksForUser(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(
            self::TABLE_TASKS,
            [
                'user_id' => $user['ID'],
                'type' => TaskModel::TYPE_AUTOMATED,
                'automated_type' => $automatedType,
                'status' => TaskModel::STATUS_UNRESOLVED,
                'details' => 'Task Details',
            ]
        );
        $expectedTasks++;
        $tasksCreated = $service->process();

        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeWithValidUserThatHasAboutMe(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);
        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(self::TABLE_USER_META, [
            'user_id' => $user['ID'],
            'meta_key' => self::META_ABOUT_ME,
            'meta_value' => 'About Me Text'
        ]);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeWithValidUserThatHasEmptyAboutMe(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType, $status) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);
        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(self::TABLE_USER_META, [
            'user_id' => $user['ID'],
            'meta_key' => self::META_ABOUT_ME,
            'meta_value' => '            '
        ]);
        $tasksCreated = $service->process();
        $I->assertEquals(1, $tasksCreated);
        $this->validateInTask(true, $user['ID'], null, $type, $automatedType, null, $status);
        $expectedTasks++;
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeWithValidUserThatHasEmptyAboutMeWithI18n(ApiTester $I)
    {
        $locale = 'fr_CA';

        list($service, $daysExpected, $expectedTasks, $type, $automatedType, $status) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);
        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected, $locale);
        $I->haveInDatabase(self::TABLE_USER_META, [
            'user_id' => $user['ID'],
            'meta_key' => self::META_ABOUT_ME,
            'meta_value' => '            '
        ]);
        $tasksCreated = $service->process();
        $task = $this->app['tasks.manager']->getOne(['user_id' => $user['ID']]);
        $I->assertEquals($task->details, $service->getTaskDetails(true, $locale));
    }

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeWithInvalidUserSettingsThatHasNoAboutMe(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);
        $this->insertUser($I, true, false, true, true, 1, $daysExpected);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUpdateAboutMeWithInvalidUserRegistrationDateThatHasNoAboutMe(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME);
        $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));
        $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));

        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /*************************************************************
     * Tests for Automated - Nag Onboarding - Upload Profile Pic *
     *************************************************************/

    /** @group database_transaction */
    public function testAutomatedUploadProfilePicWithInvalidUserThatHasNotOnboarded(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC);

        // User Id that has yet to onboard / be created (Onboarding row exists, but no wp_users row)
        $userId = 1000009;
        $this->insertOnboarding($I, $userId, 0, false);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUploadProfilePicWithValidUserThatHasNotUploaded(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType, $status) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC);

        $userIdsThatShouldHaveTasks = [];
        for ($i = 0; $i < 64; $i++) {
            $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
            $this->insertOnboarding($I, $user['ID'], $i);
            if (($i & RepOnboardingModel::STEP_UPLOAD_PIC) === 0) {
                $userIdsThatShouldHaveTasks[] = $user['ID'];
            }
        }
        $tasksCreated = $service->process();
        $I->assertEquals(count($userIdsThatShouldHaveTasks), $tasksCreated);
        $expectedTasks += count($userIdsThatShouldHaveTasks);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));

        foreach ($userIdsThatShouldHaveTasks as $userId) {
            $this->validateInTask(true, $userId, null, $type, $automatedType, null, $status);
        }
    }

    /** @group database_transaction */
    public function testAutomatedUploadProfilePicDoesNotCreateTwoTasksForUser(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(
            self::TABLE_TASKS,
            [
                'user_id' => $user['ID'],
                'type' => TaskModel::TYPE_AUTOMATED,
                'automated_type' => $automatedType,
                'status' => TaskModel::STATUS_UNRESOLVED,
                'details' => 'Task Details',
            ]
        );
        $expectedTasks++;
        $tasksCreated = $service->process();

        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUploadProfilePicWithValidUserThatHasUploaded(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $this->insertOnboarding($I, $user['ID'], RepOnboardingModel::STEP_UPLOAD_PIC);

        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUploadProfilePicWithInvalidUserSettingsThatHasNotUploaded(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC);
        $user = $this->insertUser($I, true, false, true, true, 1, $daysExpected);
        $this->insertOnboarding($I, $user['ID']);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedUploadProfilePicWithInvalidUserRegistrationDateThatHasNotUploaded(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC);
        $beforeDateUser = $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));
        $this->insertOnboarding($I, $beforeDateUser['ID']);
        $afterDateUser = $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));
        $this->insertOnboarding($I, $afterDateUser['ID']);

        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /**
     * Function will insert a row into sf_rep_onboarding based on the $userId
     * as well as the $stepsCompleted (integer representation of binary tracker)
     * and whether or not they have started $hasOnboarded
     * (a related wp_users row should exist if true, and the wp_user_id should be filled with that $userId)
     * @param $userId
     * @param int $stepsCompleted
     * @param bool $hasOnboarded
     */
    private function insertOnboarding($I, $userId, $stepsCompleted = 0, $hasOnboarded = true)
    {
        $prefix = 'RRID-';
        $I->haveInDatabase(
            self::TABLE_REP_ONBOARDING,
            [
                'token' => $prefix . $userId,
                'wp_user_id' => ($hasOnboarded ? $userId : null),
                'retailer_rep_id' => $prefix . $userId,
                'steps_completed' => $stepsCompleted,
                'rep_first_name' => 'John',
                'rep_last_name' => 'Doe',
            ]
        );
    }

    /*******************************************************
     * Tests for Automated - Nag Onboarding - Add Contacts *
     *******************************************************/

    /** @group database_transaction */
    public function testAutomatedAddContactsWithValidUserThatHasNoContacts(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType, $status) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $tasksCreated = $service->process();
        $I->assertEquals(1, $tasksCreated);
        $this->validateInTask(true, $user['ID'], null, $type, $automatedType, null, $status);
        $expectedTasks++;
        $I->assertEquals($expectedTasks, $I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedAddContactsDoesNotCreateTwoTasksForUser(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(
            self::TABLE_TASKS,
            [
                'user_id' => $user['ID'],
                'type' => TaskModel::TYPE_AUTOMATED,
                'automated_type' => $automatedType,
                'status' => TaskModel::STATUS_UNRESOLVED,
                'details' => 'Task details',
            ]
        );
        $expectedTasks++;
        $tasksCreated = $service->process();

        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedAddContactsWithValidUserThatHasContacts(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS);
        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(self::TABLE_CUSTOMERS, [
            'user_id' => $user['ID'],
            'name' => 'First Last',
            'first_name' => 'First',
            'last_name' => 'Last',
        ]);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedAddContactsWithInvalidUserSettingsThatHasNoContacts(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS);
        $this->insertUser($I, true, false, true, true, 1, $daysExpected);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedAddContactsWithInvalidUserRegistrationDateThatHasNoContacts(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS);
        $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));
        $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));

        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /***************************************************************
     * Tests for Automated - Nag Onboarding - Connect Social Media *
     ***************************************************************/

    /** @group database_transaction */
    public function testAutomatedConnectSocialMediaWithValidUserThatHasNoNetworks(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType, $status) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $tasksCreated = $service->process();
        $I->assertEquals(1, $tasksCreated);
        $this->validateInTask(true, $user['ID'], null, $type, $automatedType, null, $status);
        $expectedTasks++;
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedConnectSocialMediaDoesNotCreateTwoTasksForUser(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks, $type, $automatedType) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA);

        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(
            self::TABLE_TASKS,
            [
                'user_id' => $user['ID'],
                'type' => TaskModel::TYPE_AUTOMATED,
                'automated_type' => $automatedType,
                'status' => TaskModel::STATUS_UNRESOLVED,
                'details' => 'Task details',
            ]
        );
        $expectedTasks++;
        $tasksCreated = $service->process();

        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedConnectSocialMediaWithValidUserThatHasNetworks(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA);
        $user = $this->insertUser($I, true, true, false, true, 1, $daysExpected);
        $I->haveInDatabase(self::TABLE_OAUTH_SESSIONS, [
            'user_id' => $user['ID'],
            'provider' => 'Facebook',
            'name' => $user['user_nicename'],
            'creds' => '{"some":"credentials"}',
        ]);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedConnectSocialMediaWithInvalidUserSettingsThatHasNoNetworks(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA);
        $this->insertUser($I, true, false, true, true, 1, $daysExpected);
        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /** @group database_transaction */
    public function testAutomatedConnectSocialMediaWithInvalidUserRegistrationDateThatHasNoNetworks(ApiTester $I)
    {
        list($service, $daysExpected, $expectedTasks) =
            $this->setupAutomatedNagOnboarding(TaskModel::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA);
        $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));
        $this->insertUser($I, true, true, false, true, 1, ($daysExpected + 1));

        $tasksCreated = $service->process();
        $I->assertEquals(0, $tasksCreated);
        $I->assertEquals($expectedTasks, $this->I->getNumberRowsInTable($this->repo, self::TABLE_TASKS));
    }

    /**
     * Validates by checking if sf_task table has or doesn't have (based on $seeInDatabase)
     * a given row based on the specs requested in the below variables
     * If variable is null, we will not check for it
     * If variable contains string 'NULL', we will check for it being null
     *  (Applies to $customerId, $automatedType, $details, which can validly contain nulls in certain situations)
     * @param bool $seeInDatabase
     * @param null $userId
     * @param null $customerId
     * @param null $type
     * @param null $automatedType
     * @param null $details
     * @param null $status
     */
    private function validateInTask(
        $seeInDatabase = true,
        $userId = null,
        $customerId = null,
        $type = null,
        $automatedType = null,
        $details = null,
        $status = null
    ) {
        $structure = [];
        if (!empty($userId)) {
            $structure['user_id'] = $userId;
        }
        if (!empty($customerId)) {
            $structure['customer_id'] = ($customerId === 'NULL' ? null : $customerId);
        }
        if (!empty($type)) {
            $structure['type'] = $type;
        }
        if (!empty($automatedType)) {
            $structure['automated_type'] = ($automatedType === 'NULL' ? null : $automatedType);
        }
        if (!empty($details)) {
            $structure['details'] = ($details === 'NULL' ? null : $details);
        }
        if (!empty($status)) {
            $structure['status'] = $status;
        }
        $action = ($seeInDatabase ? 'seeInDatabase' : 'dontSeeInDatabase');
        $this->I->$action(self::TABLE_TASKS, $structure);
    }
}
