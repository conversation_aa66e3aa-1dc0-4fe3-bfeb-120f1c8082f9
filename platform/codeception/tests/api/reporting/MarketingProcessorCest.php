<?php

use SF\api\BaseApi;
use Codeception\Example;

/**
 * ReportingProcessor tests for Marketing.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * WESHOULD Move into unit/functional test suite
 */
class MarketingProcessorCest extends BaseApi
{
    /** @var ReportingProcessor $reportingProcessor */
    private $reportingProcessor;

    /** @var integer $daysBack */
    private $daysBack;

    /** @var array $onlyThisMetrics */
    private $onlyThisMetrics;

    /** @var array $fixtures */
    private $fixtures = [];

    const FIXTURES_PATH = __DIR__ . '/../../fixtures/reporting/';

    public function _before($I)
    {
        parent::_before($I);
        $this->reportingProcessor = $this->app['reporting.processor'];
        $this->daysBack = 0;
        $this->onlyThisMetrics = [];
        $this->loadTestFixtures();
    }

    /**
     * Provides the data for 'testAgainstMetricsGroups'.
     * @return array
     */
    protected function metricsGroupsProvider()
    {
        return require self::FIXTURES_PATH . 'metrics-groups.php';
    }

    /**
     * Runs ReportingEngine instance using different cases,
     * and validate collected metrics for 'get-marketing' report in 'query-only' mode.
     *
     * ticket SF-22035
     * ticket SF-26297
     *
     * @dataProvider metricsGroupsProvider
     * @param Example $dataset
     *
     * @skip
     * @group database_transaction
     */
    public function testAgainstMetricsGroups(Example $dataset)
    {
        $details = $this->getDetails($dataset, 'query-only');
        $reportingEngine = $this->getReportingEngineInstance($details);
        $results = $reportingEngine->run($this->app['repositories.mysql'], $this->app);
        $collectedMetrics = $this->getCollectedMetrics($results, $dataset);
        $this->validateCollectedMetrics($collectedMetrics, $dataset['expectedMetrics']);
    }

    /**
     * ticket SF-26297
     *
     * Runs ReportingEngine instance using different cases,
     * and validate collected metrics for 'get-marketing' report in 'backfill-only' mode.
     *
     * @group database_transaction
     */
    public function testAgainstBackfillOnly()
    {
        $I = $this->I;
        $dataset = require self::FIXTURES_PATH . 'backfill-only.php';
        insertFixture($I, 'sf_user_daily_stats', $dataset['dailyStats']);

        $details = $this->getDetails($dataset['instanceDetails'], 'backfill-only');
        $reportingEngine = $this->getReportingEngineInstance($details);
        $results = $reportingEngine->run($this->app['repositories.mysql'], $this->app);

        $I->assertArrayHasKey('user-101', $results['marketing']);
        $data = $results['marketing']['user-101'];
        $I->assertEquals($dataset['dailyStats']['scheduled_appointments'], $data['scheduled_appointments']);

        // test Diff operation
        $I->assertEquals(1, $data['chat_missed']);
        // test Calc operation - pass through operation
        $I->assertEquals(2, $data['chat_number_minutes_available']);
        $I->assertEquals(0.67, $data['chat_answer_rate']);
        $I->assertEquals(67, $data['chat_answer_rate_new']);
        // test div operation
        $I->assertEquals(15, $data['chat_avg_answer_time']);
        // test Sum operation with custom callback
        $I->assertEquals(0.42, $data['chat_avg_availability']);
        // test Sum operation
        $I->assertEquals(13, $data['text_messages_sent']);
        // test Formatter operation after aggregate ops - ex. formatTimeAsString()
        // given 'avg_init_resp_sum_times' and 'avg_init_resp_num_responses' === 0
        $I->assertEquals('-', $data['avg_init_resp']);
    }

    /**
     * Loads fixtures for the tests.
     */
    private function loadTestFixtures()
    {
        // load fixtures.
        require_once self::FIXTURES_PATH . '../database/reportingFixtureFunctions.php';
        $fixtures = require self::FIXTURES_PATH . '../database/reportingUsersStoresStats.php';
        $appointments = require self::FIXTURES_PATH . '../database/reportingAppointments.php';
        $this->fixtures = $fixtures['fixtures'];

        // modify transaction data to collect metrics.
        $date = new DateTime('now', new DateTimeZone('America/New_York'));
        foreach ($this->fixtures['transactions'] as $_key => $_transaction) {
            $this->fixtures['transactions'][$_key]['store_id'] = 10001;
            $this->fixtures['transactions'][$_key]['trx_date'] = $date->format('Y-m-d H:i:s');
            $this->fixtures['transactions'][$_key]['received_date'] = $date->format('Y-m-d H:i:s');
        }

        // modify store data to collect metrics.
        foreach ($this->fixtures['store'] as $_key => $_transaction) {
            $this->fixtures['store'][$_key]['store_user_id'] = 101;
        }

        insertAllTransactionalEntities($this->I, $this->fixtures);
        insertAllAppointments($this->I, $appointments);
    }

    /**
     * Returns the details for ReportingEngine instantiation.
     *
     * @param string $fetchType for ReportingEngine.
     * @return array
     */
    private function getDetails($dataset, $fetchType)
    {
        $type = $dataset['type'];
        $userId = $dataset['userId'];
        $onlyThisMetrics = $dataset['onlyThisMetrics'];

        // get specific user by id from dataset.
        $users = $this->reportingProcessor->getValidUsersForDate($this->daysBack);
        foreach ($users as $_user) {
            if ($_user['user_id'] == $userId) {
                $user = $_user;
                break;
            }
        }

        $details = [
            'users' => [$user['user_id']], // only one user.
            'date' => $user['dateToCalculate'],
            'startDate' => $user['dateToCalculate'] . ' 00:00:00',
            'endDate' => $user['dateToCalculate'] . ' 23:59:59',
            'timezone' => $user['timezone'],
            'fetchType' => $fetchType,
            'grouping' => true,
        ];

        if ($type == 'store') {
            $details['grouping'] = false;
            $details['stores'] = [$user['store_id']]; // only one store.
        }

        if (!empty($onlyThisMetrics)) {
            $this->onlyThisMetrics[] = $onlyThisMetrics;
        }

        return $details;
    }

    /**
     * Returns instance of ReportingEngine for 'get-marketing' report.
     *
     * @param array $details to pass to an instance as params.
     * @return ReportingEngine
     */
    private function getReportingEngineInstance(array $details)
    {
        return new ReportingEngine(
            ['get-marketing'],
            $details['users'],
            $details['timezone'],
            $details['startDate'],
            $details['endDate'],
            null,
            true,
            $details['grouping'],
            $details['stores'] ?? [],
            $details['fetchType'],
            null,
            $this->onlyThisMetrics
        );
    }

    /**
     * Extracts collected metrics from result depending on type.
     *
     * @param array $results of a run.
     * @param Example $dataset
     * @return array
     */
    private function getCollectedMetrics(array $results, Example $dataset)
    {
        return ($dataset['type'] == 'store') ? $results['marketing'] : $results['marketing']['user-' . $dataset['userId']];
    }

    /**
     * Validates collected metrics according to expected subset.
     *
     * @param array $data that represents a list of metrics returned by ReportingEngine.
     * @param array $expectedFields that represents a list of expected metrics.
     */
    private function validateCollectedMetrics(array $data, array $expectedFields)
    {
        $this->I->debug($data);
        $this->I->debug(count($data));
        $this->I->debug($expectedFields);

        // When this asset fails we need to check if there are new columns/generated value by compering the keys
        // of $data vs $expectedFields. The debug above should show which one is failing.
        // The fixtures are located here: codeception/tests/fixtures/reporting/metrics-groups.php
        $this->I->assertCount(count($expectedFields), $data);

        foreach ($expectedFields as $_name => $_value) {
            $this->I->assertArrayHasKey($_name, $data);
            $this->I->assertEquals($_value, $data[$_name]);
        }
    }
}
