<?php

use SF\ApiTester;
use SF\api\BaseApi;
use Salesfloor\API\Managers\Client\RetailerCustomers\Legacy as RetailerCustomersManager;
use Salesfloor\Services\ElasticSearch\ElasticSearch;

class RetailerCustomersCest extends BaseApi
{
    private $fixtures;
    private $fixturesRetailer;
    private $customer1;
    private $customer2;
    private $customer3;
    private $customerMetaEmail1;
    private $customerMetaEmail2;
    private $customerMetaPhone1;
    private $customerMetaPhone2;
    private $customerMetaPhone3;

    public function _before($I)
    {
        parent::_before($I);
        $this->fixtures = require __DIR__ . '/../../fixtures/customers/customers.php';
        $this->fixturesRetailer = require __DIR__ . '/../../fixtures/customers/retailer_customers.php';
        $this->customer1 = $this->fixturesRetailer['customer1'];
        $this->customer2 = $this->fixturesRetailer['customer2'];
        $this->customer3 = $this->fixturesRetailer['customer3'];
        $this->customerMetaEmail1 = $this->fixturesRetailer['customerMetaEmail1'];
        $this->customerMetaEmail2 = $this->fixturesRetailer['customerMetaEmail2'];
        $this->customerMetaPhone1 = $this->fixturesRetailer['customerMetaPhone1'];
        $this->customerMetaPhone2 = $this->fixturesRetailer['customerMetaPhone2'];
        $this->customerMetaPhone3 = $this->fixturesRetailer['customerMetaPhone3'];
    }

    /** @group database_transaction */
    public function testCoreTestFetchCustomers(ApiTester $I)
    {
        $I->wantTo("Test fetching customers");

        $id1 = $I->haveInDatabase('sf_retailer_customers', $this->customer1);
        $id2 = $I->haveInDatabase('sf_retailer_customers', $this->customer2);

        $response = $I->doDirectGet($this->app, "retailer-customers");
        $results = json_decode($response->getContent());

        $this->fakeExternalServices(
            [
                ElasticSearch::class => [$id2, $id1],
            ]
        );

        /** @var RetailerCustomersManager $retailerCustomersManager */
        $retailerCustomersManager = $this->app['retailer_customers.manager'];

        // we need to get ES updated with the latest information
        $retailerCustomersManager->reindex();

        $response = $I->doDirectGet($this->app, "retailer-customers?filter[state]=&filter[city]=");
        $results = json_decode($response->getContent());
        foreach ($results->data as $result) {
            $this->checkFormat($I, $result);
        }
        usort($results->data, function ($a, $b) {
            return $a->id - $b->id;
        });

        $this->checkCustomer($I, $results->data[0], $this->customer1);
        $this->checkCustomer($I, $results->data[1], $this->customer2);
    }

    /** @group database_transaction */
    public function testCoreTestFetchACustomerWithSimpleMeta(ApiTester $I)
    {
        $I->wantTo("Test fetching one customer with simple additional meta info");

        $id = $I->haveInDatabase('sf_retailer_customers', $this->customer1);
        $I->haveInDatabase('sf_retailer_customer_meta', $this->customerMetaEmail1);

        $response = $I->doDirectGet($this->app, "retailer-customers/" . $id);
        $result = json_decode($response->getContent());
        $this->checkFormat($I, $result);

        $this->checkCustomer($I, $result, $this->customer1);
        $this->checkCustomeAdditionalEmail($I, $result->additionalEmails[0], $this->customerMetaEmail1);
    }

    /** @group database_transaction */
    public function testCoreTestFetchACustomerWithMoreMeta(ApiTester $I)
    {
        $I->wantTo("Test fetching one customer with more additional meta info");

        $id = $I->haveInDatabase('sf_retailer_customers', $this->customer1);
        $I->haveInDatabase('sf_retailer_customer_meta', $this->customerMetaEmail1);
        $I->haveInDatabase('sf_retailer_customer_meta', $this->customerMetaEmail2);
        $I->haveInDatabase('sf_retailer_customer_meta', $this->customerMetaPhone1);
        $I->haveInDatabase('sf_retailer_customer_meta', $this->customerMetaPhone2);
        $I->haveInDatabase('sf_retailer_customer_meta', $this->customerMetaPhone3);

        $response = $I->doDirectGet($this->app, "retailer-customers/" . $id);
        $result = json_decode($response->getContent());
        $this->checkFormat($I, $result);

        $this->checkCustomer($I, $result, $this->customer1);
        $this->checkCustomeAdditionalEmail($I, $result->additionalEmails[0], $this->customerMetaEmail1);
        $this->checkCustomeAdditionalEmail($I, $result->additionalEmails[1], $this->customerMetaEmail2);
        $this->checkCustomeAdditionalPhone($I, $result->additionalPhones[0], $this->customerMetaPhone1);
        $this->checkCustomeAdditionalPhone($I, $result->additionalPhones[1], $this->customerMetaPhone2);
        $this->checkCustomeAdditionalPhone($I, $result->additionalPhones[2], $this->customerMetaPhone3);
    }

    /** @group database_transaction */
    public function testCoreRelatedCustomer(ApiTester $I)
    {
        $I->wantTo("Test get related customer");

        $customer_id = $I->haveInDatabase('sf_customer', $this->fixtures['create-simple']);
        $retailer_customer_id = $I->haveInDatabase('sf_retailer_customers', $this->fixturesRetailer['customer1']);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customer_id,
            'retailer_customer_id' => $retailer_customer_id,
            'comment' => 'retailer_customer_id: 1234',
        ]);

        $response = $I->doDirectGet($this->app, "retailer-customers/" . $retailer_customer_id . '?include=related-customers&filter[user_id]=' . $this->fixtures['create-simple']['user_id']);
        $results = json_decode($response->getContent());
        $I->assertEquals(1, $results->isRelatedCustomer);
        $I->assertEquals($customer_id, $results->relatedCustomers[0]->ID);
    }

    /** @group database_transaction */
    public function testCoreRelatedRetailerCustomerTransactions(ApiTester $I)
    {
        $I->wantTo("Test get related retailer customer transactions flag");

        $customer_id = $I->haveInDatabase('sf_customer', $this->fixtures['create-simple']);
        $retailer_customer_id = $I->haveInDatabase('sf_retailer_customers', $this->fixturesRetailer['customer1']);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customer_id,
            'retailer_customer_id' => $retailer_customer_id,
            'comment' => 'retailer_customer_id: 1234',
        ]);

        $I->haveInDatabase('sf_retailer_transaction', [
            'customer_id' => $this->fixturesRetailer['customer1']['customer_id'],
            'trx_thread_id' => '1234',
            'trx_id' => '1234',
            'trx_total' => '100',
            'trx_date' => '2020-10-10 10:00:01',
            'location' => 'US',
            'pos_id' => 'pos',
        ]);

        $response = $I->doDirectGet($this->app, "retailer-customers/" . $retailer_customer_id . '?include=related-customers&filter[user_id]=' . $this->fixtures['create-simple']['user_id']);
        $results = json_decode($response->getContent());

        $I->assertEquals(1, $results->isRelatedRetailerCustomerTransactions);
    }

    /** @group database_transaction */
    public function testCustomerWithNoMatching(ApiTester $I)
    {
        $I->wantTo('test get customer information with no matching');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        // The different between contact and customer, is in customer, we don't need a match to have the stats
        $response = $I->doDirectGet($this->app, 'retailer-customers/' . $retailerCustomerId);
        $result = json_decode($response->getContent());

        // Assert data
        $this->checkCustomer($I, $result, $contact);

        // We don't have a match to customer => contact
        $this->checkMatchingCustomer($I, $result, [
            'isRelatedCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
        ]);
    }

    /** @group database_transaction */
    public function testCustomerWithMatchingButConfigBlockRule1(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 1');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'retailer_customer_id: 1234213413251',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkCustomer($I, $results, $contact);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer'                     => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);
    }

    /** @group database_transaction */
    public function testCustomerWithMatchingButConfigBlockRule2(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 2');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'default_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkCustomer($I, $results, $contact);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer'                     => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);
    }

    /** @group database_transaction */
    public function testCustomerWithMatchingButConfigBlockRule3(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 3');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'alternate_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkCustomer($I, $results, $contact);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);
    }

    /** @group database_transaction */
    public function testCustomerWithMatchingButConfigBlockRule4(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 4');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'alternate_email_to_default_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkCustomer($I, $results, $contact);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);
    }

    /** @group database_transaction */
    public function testCustomerWithMatchingButConfigBlockRule5(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 5');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'default_email_to_alternate_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkCustomer($I, $results, $contact);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL
        ];

        $results = $this->app['retailer_customers.manager']->getOne([
            'id' => $retailerCustomerId,
        ], null, false);

        $this->checkMatchingCustomer($I, $results, [
            "id"  => $retailerCustomerId,
            'isRelatedCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ]);
    }

    private function checkFormat(ApiTester $I, $result)
    {
        // Format
        $I->assertRegExpValue($I, 'string', $result->customer_id);
        $I->assertRegExpValue($I, '(female|male|null|not known)', $result->gender);
        $I->assertRegExpValue($I, 'string', $result->first_name);
        $I->assertRegExpValue($I, 'string', $result->last_name);
        $I->assertRegExpValue($I, 'string', $result->email);
        $I->assertRegExpValue($I, 'string', $result->email_label);
        $I->assertRegExpValue($I, 'string', $result->phone);
        $I->assertRegExpValue($I, 'string', $result->phone_label);
        $I->assertRegExpValue($I, 'string', $result->address_line1);
        $I->assertRegExpValue($I, 'string', $result->address_line2);
        $I->assertRegExpValue($I, 'string', $result->zipcode);
        $I->assertRegExpValue($I, 'string', $result->city);
        $I->assertRegExpValue($I, 'string', $result->state);
        $I->assertRegExpValue($I, 'string', $result->country);
        $I->assertRegExpValue($I, 'string', $result->longitude);
        $I->assertRegExpValue($I, 'string', $result->latitude);
        $I->assertRegExpValue($I, 'boolean', $result->is_subscribed);
    }

    private function checkCustomer($I, $result, $expectedResults)
    {
        $I->assertEquals($expectedResults['customer_id'], $result->customer_id);
        $I->assertEquals($expectedResults['gender'], $result->gender);
        $I->assertEquals($expectedResults['first_name'], $result->first_name);
        $I->assertEquals($expectedResults['last_name'], $result->last_name);
        $I->assertEquals($expectedResults['email'], $result->email);
        $I->assertEquals($expectedResults['email_label'], $result->email_label);
        $I->assertEquals($expectedResults['phone'], $result->phone);
        $I->assertEquals($expectedResults['phone_label'], $result->phone_label);
        $I->assertEquals($expectedResults['address_line1'], $result->address_line1);
        $I->assertEquals($expectedResults['address_line2'], $result->address_line2);
        $I->assertEquals($expectedResults['zipcode'], $result->zipcode);
        $I->assertEquals($expectedResults['postalcode'], $result->postalcode);
        $I->assertEquals($expectedResults['city'], $result->city);
        $I->assertEquals($expectedResults['state'], $result->state);
        $I->assertEquals($expectedResults['country'], $result->country);
        $I->assertEquals($expectedResults['longitude'], $result->longitude);
        $I->assertEquals($expectedResults['latitude'], $result->latitude);
        $I->assertEquals($expectedResults['is_subscribed'], $result->is_subscribed);
    }

    private function checkCustomeAdditionalEmail($I, $result, $expectedResults)
    {
        $I->assertEquals($expectedResults['label'], $result->label);
        $I->assertEquals($expectedResults['position'], $result->position);
        $I->assertEquals($expectedResults['value'], $result->email);
    }

    private function checkCustomeAdditionalPhone($I, $result, $expectedResults)
    {
        $I->assertEquals($expectedResults['label'], $result->label);
        $I->assertEquals($expectedResults['position'], $result->position);
        $I->assertEquals($expectedResults['value'], $result->phone);
    }

    private function checkStats($I, $result, $expected)
    {
        $I->assertEquals($expected['stats']['avg_order_value'], $result->stats->avg_order_value);
        $I->assertEquals($expected['stats']['year_to_date_total_spend'], $result->stats->year_to_date_total_spend);
        $I->assertEquals($expected['stats']['lifetime_total_spend'], $result->stats->lifetime_total_spend);
        $I->assertEquals($expected['stats']['last_order_date'], $result->stats->last_order_date);
        $I->assertEquals($expected['brand']['name'], $result->stats->brand[0]->name);
        $I->assertEquals($expected['brand']['percentage_of_sale'], $result->stats->brand[0]->percentage_of_sale);
        $I->assertEquals($expected['category']['name'], $result->stats->category[0]->name);
        $I->assertEquals($expected['category']['percentage_of_sale'], $result->stats->category[0]->percentage_of_sale);
    }

    private function checkMatchingCustomer($I, $result, $expected)
    {
        foreach ($expected as $key => $value) {
            $I->varDump($key);
            if (isset($result->$key)) {
                $I->assertEquals($value, $result->$key);
            }
        }
    }
}
