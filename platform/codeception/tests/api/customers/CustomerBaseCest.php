<?php

namespace SF\api\customers;

use SF\ApiTester;
use SF\api\BaseApi;

class CustomerBaseCest extends BaseApi
{
    protected $fixtures;
    protected $fixturesRetailer;

    public function _before($I)
    {
        parent::_before($I);
        $this->fixtures = require __DIR__ . '/../../fixtures/customers/customers.php';
        $this->fixturesRetailer  = require __DIR__ . '/../../fixtures/customers/retailer_customers.php';
    }

    protected function checkCustomer(ApiTester $I, $expectedResults, $results)
    {
        foreach ($expectedResults as $key => $value) {
            if (is_array($value)) {
                $this->checkArray($I, $value, $results->{$key});
            } else {
                $I->assertEquals($value, $results->{$key});
            }
        }
    }
}
