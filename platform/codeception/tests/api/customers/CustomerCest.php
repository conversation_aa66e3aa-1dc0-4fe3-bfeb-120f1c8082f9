<?php

namespace SF\api\customers;

use SF\ApiTester;
use Carbon\Carbon;

class CustomerCest extends CustomerBaseCest
{
    /** @group database_transaction */
    public function testCoreCreateCustomer(ApiTester $I)
    {
        $I->wantTo("Test create simple customer");
        $params = $this->fixtures['create-simple'];


        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent(), true);
        $this->checkArray($I, $params, $result);
        $I->seeInDatabase('sf_customer', ['ID' => $result['ID']] + $params);
    }

    /** @group database_transaction */
    public function testCoreCreateCustomerByPhone(ApiTester $I)
    {
        $I->wantTo("Test create simple customer with only a phone number");

        $params = $this->fixtures['create-simple-by-phone'];


        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent(), true);
        $this->checkArray($I, $params, $result);
        $I->seeInDatabase('sf_customer', ['ID' => $result['ID']] + $params);

        $I->assertTrue(!isset($result['email']));
    }

    /** @group database_transaction */
    public function testCoreCreateCustomerComplex(ApiTester $I)
    {
        $I->wantTo("Test create complex customer");

        $params = $this->fixtures['create-complex'];

        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent(), true);
        $this->checkArray($I, $params, $result);
        $I->seeInDatabase('sf_customer', [
            'ID' => $result['ID'],
            'user_id' => $result['user_id'],
            'first_name' => $result['first_name'],
            'last_name' => $result['last_name'],
            'email' => $result['email'],
        ]);
    }

    /** @group database_transaction */
    public function testCreateCustomerNoEmailOrPhone(ApiTester $I)
    {
        $I->wantToTest('customers must have an email or phone');

        $params = $this->fixtures['create-no-email-or-phone'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent(), true);
        $I->assertEquals('Either email or phone must be provided when creating a contact.', $result['error']);
    }

    /** @group database_transaction */
    public function testCoreUpdateCustomer(ApiTester $I)
    {
        $I->wantTo("Test update simple customer");

        $params = $this->fixtures['create-simple'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-simple'];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());
        $this->checkArray($I, $params, $result);
    }

    /** @group database_transaction */
    public function testCoreUpdateCustomerComplex(ApiTester $I)
    {
        $I->wantTo("Test update complex customer");

        // Create a customer

        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());
        $params = $this->fixtures['update-complex'];

        // Get additional Email ids
        $i = 0;
        foreach ($result->additionalEmails as $additionalEmails) {
            $params['additionalEmails'][$i++]['id'] =  $additionalEmails->id;
        }

        // Get additional Phone ids
        $i = 0;
        foreach ($result->additionalPhones as $additionalPhone) {
            $params['additionalPhones'][$i++]['id'] =  $additionalPhone->id;
        }

        // Add new email
        $params['additionalEmails'][2] = [
            'email' => '<EMAIL>',
            'label' => 'Work',
            'position' => '2'
        ];

        // Add new phone
        $params['additionalPhones'][2] = [
            'phone' => '+15143546633',
            'label' => 'Test',
            'position' => '2'
        ];

        // Update customer info
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());
        // $result = $I->doPutJson($I, "customers/" . $result->ID, $params);
        // $this->checkArray($I, $params, $result);
        $this->checkArray($I, $params, $result);

        // Get additional Email ids
        $i = 0;
        foreach ($result->additionalEmails as $additionalEmails) {
            $params['additionalEmails'][$i++]['id'] =  $additionalEmails->id;
        }

        // Get additional Phone ids
        $i = 0;
        foreach ($result->additionalPhones as $additionalPhone) {
            $params['additionalPhones'][$i++]['id'] =  $additionalPhone->id;
        }

        // Delete an email and a phone
        $params['additionalEmails'][$i - 1]['email'] = '';
        $params['additionalEmails'][$i - 1]['label'] = '';

        $params['additionalPhones'][$i]['phone'] = '';
        $params['additionalPhones'][$i]['label'] = '';

        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        unset($params['additionalEmails'][$i - 1]);
        unset($params['additionalPhones'][$i]);
        $this->checkArray($I, $params, $result);
    }

    /** @group database_transaction */
    public function testCoreDeleteCustomer(ApiTester $I)
    {
        $I->wantTo("Test delete customer");

        // Create a customer

        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // $result = $I->doPostJson($I, "customers", $params);

        // Delete customer info
        $response = $I->doDirectDelete($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());
        $I->assertEquals('ok', $result->status);

        $I->dontSeeInDatabase('sf_customer', array('ID' => $result->ID));
        foreach ($result->additionalEmails as $additionalEmails) {
            $I->dontSeeInDatabase('sf_customer_meta', array('id' => $additionalEmails->id));
        }
        foreach ($result->additionalPhones as $additionalPhones) {
            $I->dontSeeInDatabase('sf_customer_meta', array('id' => $additionalPhones->id));
        }

        $customer = $params;
        $additionalEmails = $customer['additionalEmails'];
        $additionalPhones = $customer['additionalPhones'];
        unset($customer['additionalEmails']);
        unset($customer['additionalPhones']);

        // Check if customer is archived
        $I->seeInDatabase('sf_deleted_customer', $customer);

        foreach ($additionalEmails as $email) {
            $email['type'] = 'email';
            $email['value'] = $email['email'];
            unset($email['email']);
            $I->seeInDatabase('sf_deleted_customer_meta', $email);
        }

        foreach ($additionalPhones as $phone) {
            $phone['type'] = 'phone';
            $phone['value'] = $phone['phone'];
            unset($phone['phone']);
            $I->seeInDatabase('sf_deleted_customer_meta', $phone);
        }
    }

    /** @group database_transaction */
    public function testCoreFetchCustomer(ApiTester $I)
    {
        $I->wantTo("Test fetch customer");

        $id = $I->haveInDatabase('sf_customer', $this->fixtures['search-customer1']);
        $params = $this->fixtures['search-customer-meta1'];
        $params['customer_id'] = $id;
        $I->haveInDatabase('sf_customer_meta', $params);

        $params = $this->fixtures['search-customer-meta2'];
        $params['customer_id'] = $id;
        $I->haveInDatabase('sf_customer_meta', $params);

        $response = $I->doDirectGet($this->app, "customers/" . $id);
        $result = json_decode($response->getContent());

        $I->assertEquals(
            $this->fixtures['search-customer1']['first_name'],
            $result->first_name
        );
        $I->assertEquals(
            $this->fixtures['search-customer1']['last_name'],
            $result->last_name
        );
        $I->assertEquals(
            $this->fixtures['search-customer1']['email'],
            $result->email
        );
        $I->assertEquals(
            $this->fixtures['search-customer1']['phone'],
            $result->phone
        );
        $I->assertEquals(
            $this->fixtures['search-customer-meta1']['value'],
            reset($result->additionalEmails)->email
        );
        $I->assertEquals(
            $this->fixtures['search-customer-meta1']['position'],
            reset($result->additionalEmails)->position
        );
        $I->assertEquals(
            $this->fixtures['search-customer-meta1']['label'],
            reset($result->additionalEmails)->label
        );
        $I->assertEquals(
            $this->fixtures['search-customer-meta2']['value'],
            reset($result->additionalPhones)->phone
        );
        $I->assertEquals(
            $this->fixtures['search-customer-meta2']['position'],
            reset($result->additionalPhones)->position
        );
        $I->assertEquals(
            $this->fixtures['search-customer-meta2']['label'],
            reset($result->additionalPhones)->label
        );
    }

    /** @group database_transaction */
    public function testCoreGetLabel(ApiTester $I)
    {
        $I->wantTo("Test get label customer");

        $response = $I->doDirectGet($this->app, "customers-labels");
        $result = json_decode($response->getContent(), true);
        $I->assertEquals($this->app['configs']['customers.labels'], $result);
    }

    /** @group database_transaction */
    public function testCoreCustomerStats(ApiTester $I)
    {
        $I->wantTo('Test customer with stats');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'retailer_customer_id: 1234',
        ]);

        // $result = $I->doGetJson($I, 'customers/' . $customerId);
        $response = $I->doDirectGet($this->app, "customers/" . $customerId);
        $result = json_decode($response->getContent());

        // Assert data
        $this->checkArray(
            $I,
            [
                'first_name' => 'test',
                'last_name' => 'test',
                'name' => 'test test',
                'email' => '<EMAIL>',
                'phone' => '514514514',
                'type' => 'personal',
                'subcribtion_flag' => 0,
                'additionalEmails' => [],
                'additionalPhones' => []
            ],
            $result
        );
    }

    /** @group database_transaction */
    public function testCoreRelatedRetailerCustomer(ApiTester $I)
    {
        $I->wantTo("Test get related retailer customer");

        $customer_id = $I->haveInDatabase('sf_customer', $this->fixtures['create-simple']);
        $retailer_customer_id = $I->haveInDatabase('sf_retailer_customers', $this->fixturesRetailer['customer1']);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customer_id,
            'retailer_customer_id' => $retailer_customer_id,
            'comment' => 'retailer_customer_id: 1234',
        ]);

        $response = $I->doDirectGet($this->app, "customers/" . $customer_id . '?include=related-retailer-customers');
        $result = json_decode($response->getContent());

        $I->assertEquals(1, $result->isRelatedRetailerCustomer);
        $I->assertEquals($retailer_customer_id, $result->relatedRetailerCustomers[0]->id);
    }

    /** @group database_transaction */
    public function testCoreRelatedCustomerAddresses(ApiTester $I)
    {
        $I->wantTo("Test get related customer addresses");

        $customer_id = $I->haveInDatabase('sf_customer', $this->fixtures['create-simple']);
        foreach ($this->fixtures['customer_address'] as $address) {
            $address['customer_id'] = $customer_id;
            $I->haveInDatabase('sf_customer_addresses', $address);
        }

        $response = $I->doDirectGet($this->app, "customers/" . $customer_id . '?include=addresses');
        $result = json_decode($response->getContent());

        $this->checkArray($I, $this->fixtures['create-simple'], $result);
        $I->assertCount(3, $result->addresses);

        $this->checkArray(
            $I,
            [
                [
                  'customer_id' => $customer_id,
                  'address_line_1' => 'Calle Tierra #28',
                  'postal_code' => '077500',
                  'state' => 'Quintana Roo',
                  'city' => 'Cancun',
                  'country' => 'Mexico',
                  'label' => 'Home',
                  'is_default' => true,
                  'created_at' => '2022-03-18 18:12:26',
                  'updated_at' => '2022-03-18 18:12:26',
                ],
                [
                  'customer_id' => $customer_id,
                  'address_line_1' => 'Calle Tierra #29',
                  'address_line_2' => 'Suite #3',
                  'postal_code' => '077500',
                  'state' => 'Quintana Roo',
                  'city' => 'Cancun',
                  'country' => 'Mexico',
                  'label' => 'Office',
                  'is_default' => false,
                  'created_at' => '2022-03-18 18:12:29',
                ],
                [
                  'customer_id' => $customer_id,
                  'address_line_1' => 'Calle Tierra #30',
                  'address_line_2' => 'Appartment #45',
                  'postal_code' => '077500',
                  'state' => 'Quintana Roo',
                  'city' => 'Cancun',
                  'country' => 'Mexico',
                  'label' => 'Delivery',
                  'is_default' => false,
                  'created_at' => '2022-03-18 18:12:30',
                ],
            ],
            $result->addresses
        );
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDataSelfAlternateToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data within input - alternate to alternate email");


        $params  = $this->fixtures['create-complex-duplicate-alternate-to-alternate-emails'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // The duplicate email will be removed and no error will be throwed
        $this->checkArray($I, array_merge($params, [
            'additionalEmails' => [(object)[
                'email'    => '<EMAIL>',
                'label'    => 'Home', // Put uppercase until it's done in the beforeSave to force to lowercase
                'position' => '0',
            ]],
        ]), $result);

        // Also check the api respond after is good
        $response = $I->doDirectGet($this->app, "customers/" . $result->ID);
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            "user_id"                   => 1,
            "first_name"                => 'John',
            'name'                      => 'John Smith',
            'last_name'                 => 'Smith',
            'email'                     => '<EMAIL>',
            'label_email'               => 'Work',
            'note'                      => 'This is a note',
            'phone'                     => '+15147677923',
            'label_phone'               => 'Home',
            'type'                      => 'personal',
            'additionalEmails'          => [
                (object)[
                    'email'    => '<EMAIL>',
                    'label'    => 'Home',
                    'position' => 0,
                ],
            ],
            'additionalPhones'          => [],
            'subcribtion_flag'          => 1,
            'groups'                    => [
                (object)[
                    'user_id'       => $result->ID,
                    'name'          => 'Subscribers',
                    'ID'            => '10000000',
                    'default'       => false,
                    'isPrivate'     => false,
                    'isSubscribers' => true,
                ],
            ],
            'isRelatedRetailerCustomer' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDataSelfDefaultToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data within input - default to alternate email");

        $params  = $this->fixtures['create-complex-duplicate-default-to-alternate-emails'];

        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // The duplicate email will be removed and no error will be throwed
        $this->checkArray($I, array_merge($params, [
            // 'ID'               => 3,
            'additionalEmails' => [],
        ]), $result);

        // Also check the api respond after is good
        // $result = $I->doGetJson($I, "customers/" . $result->ID);
        $response = $I->doDirectGet($this->app, "customers/" . $result->ID . '?include=related-retailer-customers');
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            "user_id"                   => 1,
            "first_name"                => 'John',
            'name'                      => 'John Smith',
            'last_name'                 => 'Smith',
            'email'                     => '<EMAIL>',
            'label_email'               => 'Work',
            'note'                      => 'This is a note',
            'phone'                     => '+15147677956',
            'label_phone'               => 'Home',
            'type'                      => 'personal',
            'additionalEmails'          => [],
            'additionalPhones'          => [],
            'subcribtion_flag'          => 1,
            'groups'                    => [
                (object)[
                    'user_id'       => $result->ID,
                    'name'          => 'Subscribers',
                    'ID'            => '10000000',
                    'default'       => false,
                    'isPrivate'     => false,
                    'isSubscribers' => true,
                ],
            ],
            'isRelatedRetailerCustomer' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateSelfCheckReorder(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data within input - reorder the position of alternate emails");

        $params  = $this->fixtures['create-complex-duplicate-check-reorder'];

        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, array_merge($params, [
            'additionalEmails' => [
                (object)[
                    'email'    => '<EMAIL>',
                    'label'    => 'Home', // Put uppercase until it's done in the beforeSave to force to lowercase
                    'position' => '0',
                ],
                (object)[
                    'email'    => '<EMAIL>',
                    'label'    => 'Home', // Put uppercase until it's done in the beforeSave to force to lowercase
                    'position' => '1',
                ],
            ],
        ]), $result);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDatabaseDefaultToDefaultEmail(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database - default to default email");

        Carbon::setTestNow('2022-03-18 18:12:26');
        $params  = $this->fixtures['create-simple'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $message = json_encode([
            "email" => "<EMAIL>",
            "onBehalfOf" => null,
            "timestamp" => Carbon::now('UTC')->timestamp,
            "env" => "dev",
            "retailer" => "tests"
        ]);
        $this->verifyMessageQueueSent([
            $message,
            $result->ID
        ]);

        $params  = $this->fixtures['create-simple'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals('Contact creation failed. The data provided (email) matches another contact', $result->error);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDatabaseDefaultToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database - alternate to alternate email");


        $params  = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);

        // This contact have his default email equal as the alternate email of the previous one
        $params  = $this->fixtures['create-duplicate-default-to-alternate'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals('Contact creation failed. The data provided (email) matches another contact', $result->error);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDatabaseAlternateToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database - alternate to alternate email");

        // This contact have his default email equal as the alternate email of the previous one

        $params  = $this->fixtures['create-duplicate-alternate-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params  = $this->fixtures['create-duplicate-alternate-to-alternate-2'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals('Contact creation failed. The data provided (email) matches another contact', $result->error);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDatabaseDefaultToAlternateFromAnotherRep(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database - default to alternate - from another rep/user");


        $params  = $this->fixtures['create-duplicate-different-rep-default-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params  = $this->fixtures['create-duplicate-different-rep-default-to-alternate-2'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, array_merge($params, [
            'additionalEmails' => [
                (object)[
                    'email'    => '<EMAIL>',
                    'label'    => 'Home', // Put uppercase until it's done in the beforeSave to force to lowercase
                    'position' => '0',
                ],
            ]
        ]), $result);
    }

    /** @group database_transaction */
    public function testCustomerCreateDuplicateDatabaseAlternateToAlternateFromAnotherRep(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database - alternate to alternate - from another rep/user");


        $params  = $this->fixtures['create-duplicate-different-rep-alternate-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params  = $this->fixtures['create-duplicate-different-rep-alternate-to-alternate-2'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, array_merge($params, [
            'additionalEmails' => [
                (object)[
                    'email'    => '<EMAIL>',
                    'label'    => 'Home', // Put uppercase until it's done in the beforeSave to force to lowercase
                    'position' => '0',
                ],
            ]
        ]), $result);
    }

    /** @group database_transaction */
    public function testCustomerCreateMultipleNoDuplicate(ApiTester $I)
    {
        $I->wantTo("Test create multiple customer with no duplicate");

        // Since the multiple insert doesn't exist anymore, it's multiple time the single one
        // We don't need to test anything
    }

    /** @group database_transaction */
    public function testCustomerCreateLowercaseEmail(ApiTester $I)
    {
        $I->wantTo("Test create customer - lowercase the emails");


        $params  = $this->fixtures['create-customer-lowercase-email'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // Both email are lowercase now
        $this->checkArray($I, array_merge($params, [
            'email'            => '<EMAIL>',
            'additionalEmails' => [
                (object)[
                    'email'    => '<EMAIL>',
                    'label'    => 'Home', // Put uppercase until it's done in the beforeSave to force to lowercase
                    'position' => '0',
                ],
            ],
        ]), $result);
    }

    /** @group database_transaction */
    public function testCustomerCreateMultipleDuplicateLowercaseEmailDefaultToAlternate(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database case insensitive - default to alternate email");


        $params  = $this->fixtures['create-customer-duplicate-case-insensitve-email-default-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params  = $this->fixtures['create-customer-duplicate-case-insensitve-email-default-to-alternate-2'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals('Contact creation failed. The data provided (email) matches another contact', $result->error);
    }

    /** @group database_transaction */
    public function testCustomerCreateMultipleDuplicateLowercaseEmailAlternateToAlternate(ApiTester $I)
    {
        $I->wantTo("Test create customer with duplicate data in database case insensitive - alternate to alternate email");


        $params  = $this->fixtures['create-customer-duplicate-case-insensitve-email-alternate-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params  = $this->fixtures['create-customer-duplicate-case-insensitve-email-alternate-to-alternate-2'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
        $I->assertEquals('Contact creation failed. The data provided (email) matches another contact', $result->error);
    }

    /** @group database_transaction */
    public function testCustomerUpdateLowercaseEmail(ApiTester $I)
    {
        $I->wantTo("Test update customer - lowercase email");


        $params = $this->fixtures['create-simple'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-lowercase-emails'];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());


        $this->checkArray($I, array_merge($params, [
            'email' => '<EMAIL>',
            'first_name' => 'John-updated',
            'additionalEmails' => [
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Home',
                    'position' => '0'
                ],
            ],
        ]), $result);
    }

    /** @group database_transaction */
    public function testCustomerUpdateDefaultToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test update customer - default to alternate email");


        $params = $this->fixtures['update-customer-default-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-default-to-alternate-2'];
        $params["additionalEmails"][0]["id"] = $result->additionalEmails[0]->id;
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, $params, $result);

        // Also check the api respond after is good
        $response = $I->doDirectGet($this->app, "customers/" . $result->ID);
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            "ID"                        => $result->ID,
            "user_id"                   => 1,
            "first_name"                => 'John-updated',
            'name'                      => 'John-updated Smith',
            'last_name'                 => 'Smith',
            'email'                     => '<EMAIL>',
            'label_email'               => 'Work',
            'note'                      => 'This is a note',
            'phone'                     => '+15147677757',
            'label_phone'               => 'Home',
            'type'                      => 'personal',
            'additionalEmails'          => [
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Home',
                    'position' => '0'
                ],
            ],
            'additionalPhones'          => [],
            'subcribtion_flag'          => 1,
            'groups'                    => [
                (object)[
                    'user_id'       => $result->ID,
                    'name'          => 'Subscribers',
                    'ID'            => '10000000',
                    'default'       => false,
                    'isPrivate'     => false,
                    'isSubscribers' => true,
                ],
            ],
            'isRelatedRetailerCustomer' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testCustomerUpdateAlternateToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test update customer - alternate to alternate email");


        $params = $this->fixtures['update-customer-alternate-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-alternate-to-alternate-2'];
        $params["additionalEmails"][0]["id"] = $result->additionalEmails[0]->id;
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, $params, $result);

        // Also check the api respond after is good
        $response = $I->doDirectGet($this->app, "customers/" . $result->ID);
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            "ID"                        => $result->ID,
            "user_id"                   => 1,
            "first_name"                => 'John-updated',
            'name'                      => 'John-updated Smith',
            'last_name'                 => 'Smith',
            'email'                     => '<EMAIL>',
            'label_email'               => 'Work',
            'note'                      => 'This is a note',
            'phone'                     => '+15147677779',
            'label_phone'               => 'Home',
            'type'                      => 'personal',
            'additionalEmails'          => [
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Home',
                    'position' => '0'
                ],
            ],
            'additionalPhones'          => [],
            'subcribtion_flag'          => 1,
            'groups'                    => [
                (object)[
                    'user_id'       => $result->ID,
                    'name'          => 'Subscribers',
                    'ID'            => '10000000',
                    'default'       => false,
                    'isPrivate'     => false,
                    'isSubscribers' => true,
                ],
            ],
            'isRelatedRetailerCustomer' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testCustomerInvalidDefaultEmail(ApiTester $I)
    {
        $I->wantTo("Test we can't create/edit customer with invalid default email address");

        // Create
        $params  = $this->fixtures['create-customer-invalid-default-email'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(500, $response->getStatusCode());
        $I->assertEquals('This email is not valid: [' . $params['email'] . ']', $result->error);

        // Update

        $params  = $this->fixtures['create-simple'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // Change email by invalid email address
        $params['email'] = 'abcd';
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(500, $response->getStatusCode());
        $I->assertEquals('This email is not valid: [' . $params['email'] . ']', $result->error);
    }

    /** @group database_transaction */
    public function testCustomerInvalidAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test we can't create/edit customer with invalid alternate email address");

        // Create
        $params  = $this->fixtures['create-customer-invalid-alternate-email'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(500, $response->getStatusCode());
        $I->assertEquals('Wrong input. Format for alternate emails is not valid.', $result->error);

        // Update

        $params  = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // Change email by invalid email address
        $params['additionalEmails'][0] = 'abcd';
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(500, $response->getStatusCode());
        $I->assertEquals('Wrong input. Format for alternate emails is not valid.', $result->error);
    }

    /** @group database_transaction */
    public function testUpdateCustomerDuplicateDefaultToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test update customer with duplicate data in database - default to alternate email");


        $params = $this->fixtures['create-simple'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-duplicate-default-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-duplicate-default-to-alternate-2'];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testUpdateCustomerDuplicateAlternateToAlternateEmail(ApiTester $I)
    {
        $I->wantTo("Test update customer with duplicate data in database - alternate to alternate email");


        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-duplicate-alternate-to-alternate-1'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-duplicate-alternate-to-alternate-2'];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $I->assertEquals(409, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCreateAlternateEmailThatAlreadyExistsInDBForTheCustomer(ApiTester $I)
    {
        $I->wantTo("Test create alternate email that already exists for this customer");

        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-create-duplicate-alternate-email'];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $this->expectedResultForAlternateMetaAgainstDB($I, $result);
    }

    /** @group database_transaction */
    public function testCreateAlternatePhoneThatAlreadyExistsInDBForTheCustomer(ApiTester $I)
    {
        $I->wantTo("Test create alternate phone that already exists for this customer");


        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $params = $this->fixtures['update-customer-create-duplicate-alternate-phone'];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $this->expectedResultForAlternateMetaAgainstDB($I, $result);
    }

    private function expectedResultForAlternateMetaAgainstDB(ApiTester $I, $result)
    {
        $this->checkArray($I, [
            "ID"  => $result->ID,
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '+15147677857',
            'label_email' => 'Work',
            'label_phone' => 'Home',
            'additionalEmails' => [
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Home',
                    'position' => '0'
                ],
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Work',
                    'position' => '1'
                ],
            ],
            'additionalPhones' => [
                (object)[
                    'phone' => '+15147466656',
                    'label' => 'Home',
                    'position' => '0'
                ],
                (object)[
                    'phone' => '+15147466657',
                    'label' => 'Work',
                    'position' => '1'
                ],
            ],
            'note' => 'This is a note',
            'subcribtion_flag' => '1'

        ], $result);
    }

    /** @group database_transaction */
    public function testUpdateAlternateEmailThatAlreadyExistsInDBForTheCustomer(ApiTester $I)
    {
        $I->wantTo("Test update alternate email with email that already exists for this customer");


        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $result->additionalEmails[0]->email = '<EMAIL>';

        $params = $this->app['service.util']->objectToArray($result);
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $this->expectedResultForAlternateMetaAgainstDB($I, $result);
    }

    /** @group database_transaction */
    public function testUpdateAlternatePhoneThatAlreadyExistsInDBForTheCustomer(ApiTester $I)
    {
        $I->wantTo("Test update alternate phone with phone that already exists for this customer");


        $params = $this->fixtures['create-complex'];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $result->additionalPhones[0]->phone = '+15147677858';
        $response = $I->doDirectPut(
            $this->app,
            "customers/" . $result->ID,
            $this->app['service.util']->objectToArray($result)
        );
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            "ID"  => $result->ID,
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '+15147677857',
            'label_email' => 'Work',
            'label_phone' => 'Home',
            'additionalEmails' => [
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Home',
                    'position' => '0'
                ],
                (object)[
                    'email' => '<EMAIL>',
                    'label' => 'Work',
                    'position' => '1'
                ],
            ],
            'additionalPhones' => [
                (object)[
                    'phone' => '+15147677858',
                    'label' => 'Home',
                    'position' => '0'
                ],
                (object)[
                    'phone' => '+15147466657',
                    'label' => 'Work',
                    'position' => '1'
                ],
            ],
            'note' => 'This is a note',
            'subcribtion_flag' => '1'

        ], $result);
    }

    /** @group database_transaction */
    public function testCoreRelatedRetailerCustomerTransactions(ApiTester $I)
    {
        $I->wantTo("Test get related retailer customer transactions flag");

        $customer_id = $I->haveInDatabase('sf_customer', $this->fixtures['create-simple']);
        $retailer_customer_id = $I->haveInDatabase('sf_retailer_customers', $this->fixturesRetailer['customer1']);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customer_id,
            'retailer_customer_id' => $retailer_customer_id,
            'comment' => 'retailer_customer_id: 1234',
        ]);

        $I->haveInDatabase('sf_retailer_transaction', [
            'customer_id'   => $this->fixturesRetailer['customer1']['customer_id'],
            'trx_thread_id' => '1234',
            'trx_id'        => '1234',
            'trx_total'     => '100',
            'trx_date'      => '2023-10-10 18:00:00',
            'location'      => 'YYY',
            'pos_id'        => 'pos',
        ]);

        $response = $I->doDirectGet($this->app, "customers/" . $customer_id . '?include=related-retailer-customers');
        $result = json_decode($response->getContent());
        $I->assertEquals(1, $result->isRelatedRetailerCustomerTransactions);
    }

    /** @group database_transaction */
    public function testRetailerCustomerWithNoMatching(ApiTester $I)
    {
        $I->wantTo('test get retailer information with no matching');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $response = $I->doDirectGet($this->app, "customers/" . $customerId);
        $result = json_decode($response->getContent());

        // Assert data
        $this->checkArray(
            $I,
            [
                'first_name' => 'test',
                'last_name' => 'test',
                'name' => 'test test',
                'email' => '<EMAIL>',
                'phone' => '514514514',
                'type' => 'personal',
                'subcribtion_flag' => 0,
                'additionalEmails' => [],
                'additionalPhones' => [],
                'isRelatedRetailerCustomer' => 0,
            ],
            $result
        );
    }

    /** @group database_transaction */
    public function testRetailerCustomerWithMatchingButConfigBlockRule1(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 1');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'retailer_customer_id: 1234213413251',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager
        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'name' => 'test test',
            'first_name' => 'test',
            'last_name' => 'test',
            'phone' => '514514514',
            'label_email' => '',
            'label_phone' => '',
            'additionalEmails' => [],
            'additionalPhones' => [],
            'note' => '',
            'subcribtion_flag' => '0',
            'retailer_customer_id' => '1234213413251',
            'isRelatedRetailerCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'isRelatedRetailerCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testRetailerCustomerWithMatchingButConfigBlockRule2(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 2');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'default_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager
        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'name' => 'test test',
            'first_name' => 'test',
            'last_name' => 'test',
            'phone' => '514514514',
            'label_email' => '',
            'label_phone' => '',
            'additionalEmails' => [],
            'additionalPhones' => [],
            'note' => '',
            'subcribtion_flag' => '0',
            'retailer_customer_id' => '1234213413251',
            'isRelatedRetailerCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'isRelatedRetailerCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testRetailerCustomerWithMatchingButConfigBlockRule3(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 3');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'alternate_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager
        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'name' => 'test test',
            'first_name' => 'test',
            'last_name' => 'test',
            'phone' => '514514514',
            'label_email' => '',
            'label_phone' => '',
            'additionalEmails' => [],
            'additionalPhones' => [],
            'note' => '',
            'subcribtion_flag' => '0',
            'retailer_customer_id' => '1234213413251',
            'isRelatedRetailerCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'isRelatedRetailerCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testRetailerCustomerWithMatchingButConfigBlockRule4(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 4');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'alternate_email_to_default_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager
        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'name' => 'test test',
            'first_name' => 'test',
            'last_name' => 'test',
            'phone' => '514514514',
            'label_email' => '',
            'label_phone' => '',
            'additionalEmails' => [],
            'additionalPhones' => [],
            'note' => '',
            'subcribtion_flag' => '0',
            'retailer_customer_id' => '1234213413251',
            'isRelatedRetailerCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'isRelatedRetailerCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testRetailerCustomerWithMatchingButConfigBlockRule5(ApiTester $I)
    {
        $I->wantTo('test get retailer information with matching but config block - rule 5');

        $contact = [
            'customer_id' => '1234213413251',
            'gender' => 'female',
            'first_name' => 'Joseph',
            'last_name' => 'Mallette',
            'email' => '<EMAIL>',
            'email_label' => 'Personal',
            'phone' => '************',
            'phone_label' => 'Work',
            'address_line1' => '73975 Borer Mallll',
            'address_line2' => 'Juana Square',
            'zipcode' => '91266-7456',
            'postalcode' => '09302-0963',
            'city' => 'East Maryjaneview',
            'state' => 'California',
            'country' => 'Congo',
            'longitude' => '106.47575000',
            'latitude' => '42.32311300',
            'is_subscribed' => '0'
        ];

        $retailerCustomerId = $I->haveInDatabase('sf_retailer_customers', $contact);

        $customerId = $I->haveInDatabase('sf_customer', [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '514514514',
            'retailer_customer_id' => '1234213413251' // It need to match the one from the other table, otherwise we won't have stats
        ]);

        $I->haveInDatabase('sf_customers_to_retailer_customers', [
            'customer_id'          => $customerId,
            'retailer_customer_id' => $retailerCustomerId,
            'comment' => 'default_email_to_alternate_email: <EMAIL>',
        ]);

        // Since the config can't be modified over API CALL, use directly the manager
        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'name' => 'test test',
            'first_name' => 'test',
            'last_name' => 'test',
            'phone' => '514514514',
            'label_email' => '',
            'label_phone' => '',
            'additionalEmails' => [],
            'additionalPhones' => [],
            'note' => '',
            'subcribtion_flag' => '0',
            'retailer_customer_id' => '1234213413251',
            'isRelatedRetailerCustomer' => 1,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);

        // Now disable the config and do it again
        $this->app['configs']['retailer.clienteling.matching'] = [
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
            \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
            //\Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        ];

        $result = $this->app['customers.manager']->getOne([
            'id' => $customerId,
        ], null, false);

        $this->checkArray($I, [
            "ID"  => $customerId,
            'isRelatedRetailerCustomer' => 0,
            'isRelatedRetailerCustomerTransactions' => 0,
            'isRelatedRetailerCustomerStats' => 0,
        ], $result);
    }

    /** @group database_transaction */
    public function testGetDuplicateContactsByPhones(ApiTester $I)
    {
        $customer1Data = [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '11111111',
            'geo' => 'geo',
            'comment' => 'comment',
            'subcribtion_flag' => 0,
            'sms_marketing_subscription_flag' => 0,
        ];

        // Create customer 1 with default phone number
        $customer1Id = $I->haveInDatabase('sf_customer', $customer1Data);

        $customer2Data = [
            'user_id' => REGGIE_ID,
            'email' => '<EMAIL>',
            'first_name' => 'test',
            'last_name' => 'test',
            'name' => 'test test',
            'phone' => '22222222',
            'geo' => 'geo',
            'comment' => 'comment',
            'subcribtion_flag' => 0,
            'sms_marketing_subscription_flag' => 0,
        ];

        // Create customer 2 with default phone number and alternate phone number
        $customer2Id = $I->haveInDatabase('sf_customer', $customer2Data);

        $customer2MetaData = [
            'customer_id' => $customer2Id,
            'type' => 'phone',
            'value' => '33333333',
            'label' => 'label',
            'position' => 1,
            'creation_date' => '2020-10-10 10:10:00',
        ];

        $customer2Id = $I->haveInDatabase('sf_customer_meta', $customer2MetaData);

        // Call the customers-duplicates-by-phones route and verify that
        // we find customer via default phone number (customer 1) and alternate phone number (customer 2)
        $response = $I->doDirectGet($this->app, "customers-duplicates-by-phones?filter[phones]=11111111,33333333");
        $results = json_decode($response->getContent());

        $expectedContactEmails = ['<EMAIL>', '<EMAIL>'];

        foreach ($results as $result) {
            // Email address is the key that we use for the test.
            // We make sure we find the two contacts in the results
            $I->assertTrue(in_array($result->email, $expectedContactEmails));
            // Once the contact is found we remove it from the list of expected results
            unset($expectedContactEmails[$result->email]);
        }

        // Call the customers-duplicates-by-phones with wrong phone number
        $response = $I->doDirectGet($this->app, "customers-duplicates-by-phones?filter[phones]=wrong77777777");
        $result = json_decode($response->getContent());

        // Making sure we have no result (empty array)
        $I->assertTrue(empty($result));
    }

    /** @group database_transaction */
    public function testCheckCountryAddCustomer(ApiTester $I)
    {
        $I->wantTo('test add customer - valid/invalid country (phone)');

        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'CA',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL
        ];


        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // Once it's normalized
        $params['phone'] = '+14389276060';
        unset($params['country']);

        $this->checkArray($I, $params, $result);

        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'RU', // At the moment, russia is an invalid country
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->assertTrue(!isset($result->phone));
        unset($params['phone'], $params['country']);

        $this->checkArray($I, $params, $result);
    }

    /** @group database_transaction */
    public function testCheckCountryUpdateCustomer(ApiTester $I)
    {
        $I->wantTo('test update customer - valid/invalid country (phone)');


        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'CA',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        // Once it's normalized
        $params['phone'] = '+14389276060';
        unset($params['country']);

        $this->checkArray($I, $params, $result);

        $params = [
            'phone' => '4389276161',
            'country' => 'CA',
        ];

        // Once it's normalized
        $params['phone'] = '+14389276161';
        unset($params['country']);
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, $params, $result);

        $params = [
            'phone' => '4389276262',
            'country' => 'RU',
        ];
        $response = $I->doDirectPut($this->app, "customers/" . $result->ID, $params);
        $result = json_decode($response->getContent());
        $I->assertEquals("Phone number [4389276262] is not valid for country [RU]", $result->error);
    }

    /** @group database_transaction */
    public function testCreateCustomerSubscriptionWithActivity(ApiTester $I)
    {
        $I->wantTo('test update customer - subscription (email/text)');


        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'CA',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 1,
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->debug($result);

        $customerId = $result->ID;

        // Customer should be creating with both subscription flag set to 0
        $I->seeInDatabase('sf_customer', [
            'ID' => $customerId,
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 1,
        ]);

//        // 2 activities "subscribed" should be created for the customer
//        blocking these test as there is no update to activity feed on create any longer
//        $I->seeInDatabase('sf_customer_activity_feed', [
//            'customer_id' => $customerId,
//            'type' => \Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SUBSCRIBE_EMAIL_MARKETING,
//        ]);
//        $I->seeInDatabase('sf_customer_activity_feed', [
//            'customer_id' => $customerId,
//            'type' => \Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SUBSCRIBE_TEXT_MARKETING,
//        ]);
    }

    /** @group database_transaction */
    public function testCreateCustomerWithContactPreferenceAndUnsubscribedStatus(ApiTester $I)
    {
        $I->wantTo('test create unsubscribed customer with email as contact preference');

        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'CA',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            'subcribtion_flag' => 2,
            'sms_marketing_subscription_flag' => 2,
            'contact_preference' => 'email',
        ];


        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->debug($result);

        $customerId = $result->ID;

        // Customer should be creating with both subscription flag set to 0
        $I->seeInDatabase(
            'sf_customer',
            [
                'ID'                              => $customerId,
                'subcribtion_flag'                => 2,
                'sms_marketing_subscription_flag' => 2,
                'contact_preference'              => 'email',
            ]
        );
    }

    /** @group database_transaction */
    public function testUpdateCustomerFromSubscribedToUnsubscribed(ApiTester $I)
    {
        $I->wantTo('test update customer from subscribed to unsubscribed');


        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'CA',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 1,
            'contact_preference' => 'email',
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->debug($result);

        $customerId = $result->ID;

        // Customer should be creating with both subscription flag set to 0
        $I->seeInDatabase(
            'sf_customer',
            [
                'ID'                              => $customerId,
                'subcribtion_flag'                => 1,
                'sms_marketing_subscription_flag' => 1,
                'contact_preference'              => 'email',
            ]
        );

        $params = [
            'subcribtion_flag'                => 2,
            'sms_marketing_subscription_flag' => 2,
            'contact_preference'              => 'text',
        ];
        $response = $I->doDirectPut($this->app, "customers/" . $customerId, $params);
        $result = json_decode($response->getContent());

        $this->checkArray($I, $params, $result);
    }

    /** @group database_transaction */
    public function testCreateCustomerWithEmptyPhoneAndPhoneIsOnBlockList(ApiTester $I)
    {
        $I->wantTo('test update customer with empty phone, and no phone should be removed from the block list');

        $I->haveInDatabase('sf_sms_block_list', [
            'user_id' => 1,
            'store_id' => 1003,
            'phone_number' => '+15143345676'
        ]);


        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 0,
            'contact_preference' => 'email',
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $customerId = $result->ID;

        $I->seeInDatabase(
            'sf_customer',
            [
                'ID'                              => $customerId,
                'subcribtion_flag'                => 1,
                'sms_marketing_subscription_flag' => 0,
                'contact_preference'              => 'email',
            ]
        );

        // No entry from sf_sms_block_list Table should be removed when the customer's phone is empty.
        $I->seeInDatabase(
            'sf_sms_block_list',
            [
                'user_id' => 1,
                'store_id' => 1003,
                'phone_number' => '+15143345676'
            ]
        );
    }

    /** @group database_transaction */
    public function testUpdateCustomerSubscriptionWithActivity(ApiTester $I)
    {
        $I->wantTo('Test update customer subscription with activity');


        $params = [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'phone' => '4389276060',
            'country' => 'CA',
            'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            'subcribtion_flag' => 0,
            'sms_marketing_subscription_flag' => 0,
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());

        $I->debug($result);

        $customerId = $result->ID;

        // Customer should be creating with both subscription flag set to 0
        $I->seeInDatabase('sf_customer', [
            'ID' => $customerId,
            'subcribtion_flag' => 0,
            'sms_marketing_subscription_flag' => 0,
        ]);

        // Now, we have 3 states, opt-out means when the status is 1-1.
        // blocking this test out hence the app doesn't update activity feed on create any longer
    //    $I->seeInDatabase('sf_customer_activity_feed', [
    //        'customer_id' => $customerId,
    //    ]);

        // Change the status to subscribed (should create activity logs)
        $params = [
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 1,
        ];
        $response = $I->doDirectPut($this->app, "customers/" . $customerId, $params);
        $result = json_decode($response->getContent());

        $I->debug($result);

        // Customer should be updated with both subscription flag set to 1
        $I->seeInDatabase('sf_customer', [
            'ID' => $customerId,
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 1,
        ]);

        // $data = $I->grabRowsFromDatabase('sf_customer_activity_feed');
        // 2 activities "subscribed" should be created for the customer
        // $I->seeInDatabase('sf_customer_activity_feed', [
        //     'customer_id' => $customerId,
        //     'type' => \Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SUBSCRIBE_EMAIL_MARKETING,
        // ]);
        // $I->seeInDatabase('sf_customer_activity_feed', [
        //     'customer_id' => $customerId,
        //     'type' => \Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SUBSCRIBE_TEXT_MARKETING,
        // ]);

        // Change back the status to unsubscribed (should create activity logs)
        $params = [
            'subcribtion_flag' => 0,
            'sms_marketing_subscription_flag' => 0,
        ];

        $response = $I->doDirectPut($this->app, "customers/" . $customerId, $params);
        $result = json_decode($response->getContent());
        $I->debug($result);

        // Customer should be updated with both subscription flag set to 0
        $I->seeInDatabase('sf_customer', [
            'ID' => $customerId,
            'subcribtion_flag' => 0,
            'sms_marketing_subscription_flag' => 0,
        ]);

        // 2 activities "subscribed" should be created for the customer
        // $I->seeInDatabase('sf_customer_activity_feed', [
        //     'customer_id' => $customerId,
        //     'type' => \Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_OPT_OUT_EMAIL_MARKETING,
        // ]);
        // $I->seeInDatabase('sf_customer_activity_feed', [
        //     'customer_id' => $customerId,
        //     'type' => \Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_OPT_OUT_TEXT_MARKETING,
        // ]);
    }
}
