<?php

use SF\ApiTester;
use SF\api\BaseApi;

/**
 * Class CustomerActivityFeedCest
 *
 * For Customer Activity Feed it tests:
 *
 *  - The API
 *  - The Service
 *
 * All tests are conducted explicitly for customer_id 1
 */

class CustomerActivityFeedCest extends BaseApi
{
    private $customerActivityFeedTableName;
    private $mysqlRepository;

    /**
     * Prepares the testing environment
     * @param ApiTester $I
     */
    public function _before($I)
    {
        parent::_before($I);
        $this->mysqlRepository = $this->app['repositories.mysql'];
        $this->fixtures = require __DIR__ . '/../../fixtures/customers/activities/feed.php';
        $this->customerActivityFeedTableName = 'sf_customer_activity_feed';
    }

    /**
     * Calls an API end point to retrieve the available filters for Customer Activities
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testGetFilters(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feed filters");
        $response = $I->doDirectGet($this->app, 'customer-activity-feed-filters');
        $result = json_decode($response->getContent(), true);

        $I->debug($result);

        $I->assertEquals(200, $response->getStatusCode());
        $I->assertCount($this->getNumberOfActivitiesEnabled(), $result);
        $customerActivityFeedModel = new \Salesfloor\Models\CustomerActivityFeed();

        foreach ($result as $typeId => $typeDetails) {
            $customerActivityFeedModel->type = $typeId;
            $enabled = $this->isActivityEnabled($typeId);
            $I->assertTrue($enabled);
            $supported = $customerActivityFeedModel->isActivitySupported();
            $I->assertTrue($supported);
        }
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeed(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test User ID 1
        list($results) = $this->retrieveAndAssertGetAllAPICall($I, 1);
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(200, $results);

        // Test User ID 3
        list($results) = $this->retrieveAndAssertGetAllAPICall($I, 2);
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(12, $results);

        // Test User ID 3
        list($results) = $this->retrieveAndAssertGetAllAPICall($I, 3);
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(7, $results);
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1 filtered by Email Request type
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeedByEmail(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds filtered by Email");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test user id 1
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(59, $results);

        // Test user id 2
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            2,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);


        // Test user id 3
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            3,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1 filtered by Personal Shopper Request type
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeedByPersonalShopper(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds filtered by Personal Shopper");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test user id 1
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(63, $results);

        // Test user id 2
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            2,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);

        // Test user id 3
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            3,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1 filtered by Appointment Request type
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeedByAppointment(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds filtered by Appointment");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test user id 1
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(61, $results);

        // Test user id 2
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            2,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);

        // Test user id 3
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            3,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1 filtered by Text Message type
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeedByText(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds filtered by Text Messaging");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test user id 1
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(52, $results);

        // Test user id 2
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            2,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);

        // Test user id 3
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            3,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1 filtered by Direct Email type
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeedByDirectEmail(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds filtered by Direct Email");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test user id 1
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(50, $results);

        // Test user id 2
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            2,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);

        // Test user id 3
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            3,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
    }

    /**
     * Calls an API end point to retrieve all activities for customer_id 1 filtered by Transaction type
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testRetrievingCustomerActivityFeedByTransaction(ApiTester $I)
    {
        $I->wantTo("Test retrieving Customer Activity Feeds filtered by Transaction");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));

        // Test user id 1
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(25, $results);

        // Test user id 2
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            2,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);


        // Test user id 3
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            3,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION
        );
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(2, $results);
    }

    /**
     * Calls the Customer Activity Feed Service and invoques the cleanUp Method. I.e tests the
     * clean-up CRON.
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testCleanUpCron(ApiTester $I)
    {
        $I->wantTo("Test Customer Activity Feeds Clean Up CRON");
        $I->insertRowsInTable($I, $this->customerActivityFeedTableName, array_values($this->fixtures));
        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 329);

        $maxNumberOfEntries = $this->app['configs']['retailer.customer_activity_feed.max_num_entries'];
        $cleaner = new \Salesfloor\Services\CustomerActivityFeed($this->app);
        $cleaner->cleanUp($maxNumberOfEntries);

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 319);
    }

    /**
     * Sequentially tracks user activities
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testUserActivityTracking(ApiTester $I)
    {
        $I->wantTo("Test Customer Activity Tracking");

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 0);
        $tracker = new \Salesfloor\Services\CustomerActivityFeed($this->app);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST,
            1,
            'Email Request',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 1);
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST,
            2,
            'Personal Shopper Request',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 2);
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST,
            3,
            'Appointment Request',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 3);
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD,
            4,
            'Text Message',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 4);
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD,
            5,
            'Direct Email',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 5);
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION,
            6,
            '29.95',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 6);
        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        list($results) = $this->retrieveAndAssertGetAllAPICall($I, 1);
        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(6, $results);
    }

    /**
     * Calls the chat customer activity tracker
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testValidChatTracking(ApiTester $I)
    {
        $I->wantTo("Test Chat Customer Activity");

        $data = [
            'type' => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CHAT,
            'customerIdOrEmail' => '<EMAIL>',
            'userIdOrName' => 1,
            'preview' => null,
            'chatId' => '-KzOueTL_v0jlcIupPAT',
            'direction' => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            'status' => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ
        ];

        $inDbValidation = [
            'type'          => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CHAT,
            'customer_id'   => 1,
            'thread_id'     => $data['chatId'],
            'user_id'       => 1
        ];

        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());

        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, true, $inDbValidation);

        // Try to log an email request...
        $data['type'] = Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD;
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, false, $inDbValidation);

        // Reset the type back to chat
        $data['type'] = Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CHAT;

        // Try to log and invalid customer by email
        $data['customerIdOrEmail'] = '<EMAIL>';
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());

        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, false, $inDbValidation);

        // Try to log a valid customer ID
        $data['customerIdOrEmail'] = 1;
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, true, $inDbValidation);

        // Try to log an invalid user id
        $data['userIdOrName'] = 10;
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, false, $inDbValidation);

        // Try to log to a store
        $data['userIdOrName'] = 1003;
        $inDbValidation['user_id'] = 0;
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, true, $inDbValidation);

        /**
         * Reset customer to email, then reset to the wrong user id
         * - The customer exists but is not be associated to this user
         * */
        $data['customerIdOrEmail'] = '<EMAIL>';
        $data['userIdOrName'] = 5;
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, false, $inDbValidation);

        // Reset the user to a valid username that is not associated with the customer
        $data['userIdOrName'] = 'user2';
        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, false, $inDbValidation);
    }

    /**
     * Creates a new tracking entry, then tries to change its status from unread to read.
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testStatusChangeViaApi(ApiTester $I)
    {
        $I->wantTo("Test changing the status of a Customer Activity Feed entry via the Customer Activity Feed API");

        $data = [
            'type' => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CHAT,
            'customerIdOrEmail' => '<EMAIL>',
            'userIdOrName' => 1,
            'preview' => null,
            'chatId' => '-KzOueTL_v0jlcIupTEC',
            'direction' => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            'status' => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        ];

        $inDbValidation = [
            'type'          => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CHAT,
            'customer_id'   => 1,
            'thread_id'     => $data['chatId'],
            'user_id'       => 1,
            'status'        => Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        ];

        // $result = $I->doPostJson($I, 'customer-activity-feed/track-activity', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());

        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, true, $inDbValidation);

        // Change the status to 'read'
        unset($data['preview']);
        unset($data['direction']);
        $data['status'] = Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ;
        $inDbValidation['status'] = Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ;

        // $result = $I->doPostJson($I, 'customer-activity-feed/change-entry-status', $data);
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());

        $I->assertEquals(200, $response->getStatusCode());
        $this->validateChatTracking($I, $result, true, $inDbValidation);

        // Try to change the status to something invalid... API will execute succesfully but status won't change
        $data['status'] = 'something_invalid';
        $response = $I->doDirectPost($this->app, 'customer-activity-feed/track-activity', $data);
        $result = json_decode($response->getContent());

        $I->assertEquals(200, $response->getStatusCode());
        // It still returns true even the value is invalid and field get updated to empty string.
        // TODO: add more test to check this scenario.
        $inDbValidation['status'] = '';
        $this->validateChatTracking($I, $result, true, $inDbValidation);
        // $this->validateChatTracking($I, $result, false, $inDbValidation);
    }

    /**
     * Creates a new tracking entry, and attempts to change it to a valid and invalid
     * status. No new entries should be created while changing the status. The invalid
     * status shall be rejected. Upon successful status change no other data shall be
     * changed.
     * @param ApiTester $I
     * @group database_transaction
     */
    public function testStatusChangeViaService(ApiTester $I)
    {
        $I->wantTo("Test changing the status of a Customer Activity Feed entry via the Customer Activity Feed Service");
        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 0);

        $tracker = new \Salesfloor\Services\CustomerActivityFeed($this->app);

        $tracker->trackActivity(
            1,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST,
            1,
            'Need to find a set of Alpinestars boots',
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD
        );

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 1);

        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
        $I->assertEquals(
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_UNREAD,
            $results[0]['status']
        );

        // Change from unread to read
        $response = $tracker->changeStatus(
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ
        );
        $I->assertTrue($response);

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 1);

        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);

        $I->assertEquals(
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ,
            $results[0]['status']
        );

        // Ensure the entry did not get change with the status change
        $I->assertEquals(1, $results[0]['customer_id']);
        $I->assertEquals(
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST,
            $results[0]['type']
        );
        $I->assertEquals(1, $results[0]['thread_id']);
        $I->assertEquals('Need to find a set of Alpinestars boots', $results[0]['preview']);
        $I->assertEquals(
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECTION_INBOUND,
            $results[0]['direction']
        );
        $I->assertEquals(
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_STATUS_READ,
            $results[0]['status']
        );

        // Attempt to change to an invalid status
        $response = $tracker->changeStatus(
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST,
            1,
            '!nv4lid $t4tu5'
        );
        $I->assertFalse($response);

        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, 1);

        list($results) = $this->retrieveAndAssertGetAllAPICall(
            $I,
            1,
            Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST
        );

        $this->validateActivityFeedResponse($I, $results);
        $I->assertCount(1, $results);
    }

    /**
     * Wraps in a reusable toutine the code needed to validate a Chat tracking call
     * @param ApiTester $I
     * @param object $result
     * @param bool $expectedSuccess true|false
     * @param array $inDbValidation
     * @param int $expectedRowCount
     */
    private function validateChatTracking(
        ApiTester $I,
        &$result,
        $expectedSuccess,
        &$inDbValidation,
        $expectedRowCount = 1
    ) {
        $I->assertEquals($expectedSuccess, (bool)$result->success);
        $I->seeInDatabase($this->customerActivityFeedTableName, $inDbValidation);
        $I->assertNumberRowsInTable($this->mysqlRepository, $this->customerActivityFeedTableName, $expectedRowCount);
    }

    /**
     * Validates the fields contained in an array coming from a getAll API call to the Customer Activity Controller
     * @param ApiTester $I
     * @param array $results
     */
    private function validateActivityFeedResponse(ApiTester $I, $results)
    {
        $customerActivityFeedObject = new Salesfloor\Models\CustomerActivityFeed();

        foreach ($results as $key => $values) {
            foreach (array_keys($customerActivityFeedObject->fields) as $fieldName) {
                $I->assertContains($fieldName, array_keys($values));
            }
            $I->assertEquals(true, $this->isActivityEnabled($values['type']));

            $customerActivityFeedObject->type = $values['type'];
            $I->assertEquals(true, $customerActivityFeedObject->isActivitySupported());
        }
    }

    /**
     * Makes and assets an API call to the Customer Activity Controller. It returns an array with the API results.
     * @param ApiTester $I
     * @param null $filter
     * @return array
     */
    private function retrieveAndAssertGetAllAPICall(ApiTester $I, $customerId, $filter = null)
    {
        $maxResults = 200;

        if (null !== $filter) {
            $filter = "&filter[type]=$filter";
        }

        $response = $I->doDirectGet(
            $this->app,
            "customer-activity-feed?filter[customer_id]=$customerId$filter&page=0&per_page=$maxResults&sort=-updated_at"
        );
        $result = json_decode($response->getContent(), true);
        $I->assertEquals(200, $response->getStatusCode());

        $results = $result[Salesfloor\API\Controllers\CustomerActivityFeeds\Legacy::RESULTS_LABEL];
        $filters = $result[Salesfloor\API\Controllers\CustomerActivityFeeds\Legacy::GROUPS_LABEL];
        $pagination = $result[Salesfloor\API\Controllers\CustomerActivityFeeds\Legacy::PAGINATION_LABEL];

        $I->assertNotEmpty($results);
        $I->assertNotEmpty($filters);
        $I->assertNotEmpty($pagination);
        $I->assertTrue(is_array($pagination));

        if (count($results) < $maxResults) {
            $I->assertEquals(count($results), $pagination['count']);
            $I->assertEquals(count($results), $pagination['total']);
        } else {
            $I->assertEquals(310, $pagination['count']);
            $I->assertEquals(310, $pagination['total']);
        }

        return [$results, $pagination, $filters];
    }

    /**
     * Defines whether or not an actvity is enabled
     * @param $activity
     * @return bool
     */
    private function isActivityEnabled($activity)
    {
        switch ($activity) {
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_EMAIL_REQUEST:
                return (true === $this->app['configs']['service.contact_us']);
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_PERSONAL_SHOPPER_REQUEST:
                return (true === $this->app['configs']['retailer.has_personal_shopper']);
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_APPOINTMENT_REQUEST:
                return (true === $this->app['configs']['service.book_appointment']);
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TEXT_MESSAGE_THREAD:
                return (true === $this->app['configs']['messaging.text.enabled']);
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_DIRECT_EMAIL_THREAD:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION_RETURN:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_TRANSACTION:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CHAT:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SHARE_EMAIL:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_CALL_ATTEMPTED:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SUBSCRIBE_EMAIL_MARKETING:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_SUBSCRIBE_TEXT_MARKETING:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_OPT_OUT_EMAIL_MARKETING:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_OPT_OUT_TEXT_MARKETING:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_OPT_OUT_ALL_EMAILS:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_OPT_OUT_ALL_TEXT:
            case Salesfloor\Models\CustomerActivityFeed::SF_CUSTOMER_ACTIVITY_LOOKBOOK_PUBLISHED:
                return true;
            default:
                return false;
        }
    }

    /**
     * Retrieves the count of enabled activities
     * @return int
     */
    private function getNumberOfActivitiesEnabled()
    {
        // Always enabled:
        // - Direct Mail
        // - Transactions (Sale and Return)
        // - Live Chat
        // - Share email
        // - Call attempted
        // - Subscribe to email marketing
        // - Subscribe to text marketing
        // - Opt-Out from email marketing
        // - Opt-Out from text marketing
        // - Opt-Out-all from email marketing
        // - Opt-Out-all from text marketing
        // - lookbook-published
        $count = 13;

        if (true === $this->app['configs']['service.contact_us']) {
            $count++;
        }

        if (true === $this->app['configs']['service.personal_shopper']) {
            $count++;
        }

        if (true === $this->app['configs']['service.book_appointment']) {
            $count++;
        }

        if (true === $this->app['configs']['messaging.text.enabled']) {
            $count++;
        }

        return $count;
    }
}
