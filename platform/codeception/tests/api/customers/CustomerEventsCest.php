<?php

use SF\api\BaseApi;
use SF\ApiTester;

class CustomerEventsCest extends BaseApi
{
    protected $fixtures;

    public function _before($I)
    {
        parent::_before($I);
        $this->fixtures = require __DIR__ . '/../../fixtures/customers/events/customers.php';
    }

    /** @group database_transaction */
    public function testCustomerEventsInsertValid(ApiTester $I)
    {
        $I->wantTo('Test customer events - insert - valid');

        $input = [
            [
                'label' => 'birthday',
                'day'   => '1',
                'month' => '2',
            ],
            [
                'label' => 'anniversary',
                'day'   => '10',
                'month' => '12',
            ],
            [
                'label' => 'other',
                'day'   => '11',
                'month' => '3',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            [
                'id'    => $result[0]->id,
                'label' => 'birthday',
                'day'   => 1,
                'month' => 2,
                'year'  => null,
            ],
            [
                'id'    => $result[1]->id,
                'label' => 'anniversary',
                'day'   => 10,
                'month' => 12,
                'year'  => null,
            ],
            [
                'id'    => $result[2]->id,
                'label' => 'other',
                'day'   => 11,
                'month' => 3,
                'year'  => null,
            ],
        ], $result);

        $input = [
            [
                'label' => 'birthday',
                'day'   => '1',
                'month' => '2',
                'year'  => 2011,
            ],
            [
                'label' => 'anniversary',
                'day'   => '10',
                'month' => '12',
                'year'  => 2010,
            ],
            [
                'label' => 'other',
                'day'   => '11',
                'month' => '3',
                'year'  => 2009,
            ],
        ];

        $response = $I->doDirectPost($this->app, 'customers/2/events', $input);
        $result = json_decode($response->getContent());

        $this->checkArray($I, [
            [
                'id'    => $result[0]->id,
                'label' => 'birthday',
                'day'   => 1,
                'month' => 2,
                'year'  => 2011,
            ],
            [
                'id'    => $result[1]->id,
                'label' => 'anniversary',
                'day'   => 10,
                'month' => 12,
                'year'  => 2010,
            ],
            [
                'id'    => $result[2]->id,
                'label' => 'other',
                'day'   => 11,
                'month' => 3,
                'year'  => 2009,
            ],
        ], $result);
    }

    /** @group database_transaction */
    public function testCustomerEventsInsertEmptyDay(ApiTester $I)
    {
        $I->wantTo('Test customer events - insert - empty day');

        $input = [
            [
                'label' => 'birthday',
                'month' => '2',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $this->checkArray(
            $I,
            [[
                'id'    => $result[0]->id,
                'label' => 'birthday',
                'day'   => null,
                'month' => 2,
                'year'  => null,
            ]],
            $result
        );
    }

    /** @group database_transaction */
    public function testCustomerEventsInsertEmptyMonthCausesDelete(ApiTester $I)
    {
        $I->wantTo('Test customer events - insert - empty month causes delete');

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $customerId = $result->ID;

        // Create the event
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", [
            [
                'label' => 'birthday',
                'day'   => '2',
                'month' => '12'
            ],
        ]);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        // Eliminate the event
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", [
            [
                'label' => 'birthday',
                'day'   => '2',
            ],
        ]);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $I->assertEquals([], $result);
        $I->dontSeeInDatabase('sf_customer_events', ['label' => 'birthday']);
    }

    /** @group database_transaction */
    public function testCustomerEventsInsertInvalidDate(ApiTester $I)
    {
        $I->wantTo('Test customer events - insert - invalid date');

        $input = [
            [
                'label' => 'birthday',
                'day'   => '31',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());

        $I->assertEquals($result->error, $this->app['translator']->trans('api_error_customer_events_date_invalid'));
    }

    /** @group database_transaction */
    public function testCustomerEventsInsertInvalidLabel(ApiTester $I)
    {
        $I->wantTo('Test customer events - insert - invalid label');

        $input = [
            [
                'label' => 'potato',
                'day'   => '10',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());

        $I->assertEquals($result->error, $this->app['translator']->trans('api_error_customer_events_invalid_input'));
    }

    /** @group database_transaction */
    public function testCustomerEventsUpdateInvalidDate(ApiTester $I)
    {
        $I->wantTo('Test customer events - update - invalid date');

        $input = [
            [
                'label' => 'birthday',
                'day'   => '10',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $input = [
            [
                'id'    => $result[0]->id,
                'label' => 'birthday',
                'day'   => '100',
                'month' => '02',
            ],
        ];

        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
        $I->assertEquals(
            $this->app['translator']->trans('api_error_customer_events_date_invalid'),
            $result->error
        );
    }

    /** @group database_transaction */
    public function testCustomerEventsUpdateInvalidCustomerId(ApiTester $I)
    {
        $I->wantTo('Test customer events - update - invalid customer id');

        $input = [
            [
                'label' => 'potato',
                'day'   => '10',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID + 100;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());

        $I->assertEquals($result->error, $this->app['translator']->trans('api_error_missing_model'));
    }

    /** @group database_transaction */
    public function testCustomerEventsUpdateInvalidId(ApiTester $I)
    {
        $I->wantTo('Test customer events - update - invalid customer event id');

        $input = [
            [
                'label' => 'other',
                'day'   => '10',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $input = [
            [
                'id'    => 400,
                'label' => 'birthday',
                'day'   => '100',
                'month' => '02',
            ],
        ];

        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals($result->error, $this->app['translator']->trans('api_error_missing_model'));
    }

    ////////////////////////////////////////////////////////////////////
    ///
    /// Route section
    ///

    /** @group database_transaction */
    public function testCustomerEventsRoute1(ApiTester $I)
    {
        $I->wantTo('Test customer events - route - customer / include');

        $input = [
            [
                'label' => 'other',
                'day'   => '10',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        // TEST ROUTE
        $response = $I->doDirectGet($this->app, "customers/$customerId?include=events");
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $this->checkArray($I, [
            'ID'         => $customerId,
            'user_id'    => 1,
            'first_name' => "John",
            'last_name'  => "Smith",
            'events'     => [[
                'label' => 'other',
                'day'   => 10,
                'month' => 2,
            ]],
        ], $result);
    }

    /** @group database_transaction */
    public function testCustomerEventsRoute2(ApiTester $I)
    {
        $I->wantTo('Test customer events - route - customer events');
        $input = [
            [
                'label' => 'other',
                'day'   => '10',
                'month' => '02',
            ],
        ];

        // Now we have 3 users (2 from dump + one here)

        $response = $I->doDirectPost($this->app, 'customers', $this->fixtures['john']);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $customerId = $result->ID;
        $response = $I->doDirectPost($this->app, "customers/$customerId/events", $input);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        // TEST ROUTE
        $response = $I->doDirectGet($this->app, "customers/$customerId/events");
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertCount(1, $result);

        $this->checkArray($I, [
            'label' => 'other',
            'day'   => 10,
            'month' => 2,
        ], $result[0]);
    }
}
