<?php

use SF\ApiTester;
use SF\api\BaseApi;
use Salesfloor\Services\CloudStorage\CloudStorageAbstract;

class ExporterCustomerCest extends BaseApi
{
    /** @var \Salesfloor\Services\Exporter\CustomerExporter $exporter */
    private $exporter;

    /** @var  CloudStorageAbstract $cloudstorageClient */
    private $cloudstorageClient;

    /** @var  \Salesfloor\Services\FileManager $filemanager */
    private $filemanager;

    public function _before($I)
    {
        parent::_before($I);

        $this->exporter = $this->app['exporter.customer'];
        $this->cloudstorageClient = $this->app['cloudstorage.client'];
        $this->filemanager = $this->app['filemanager'];

        // Delete s3 file, since we don't want to mess with the other test
        $this->cloudstorageClient->deleteObject([
            'Bucket' => $this->app['configs']['s3.bucket'],
            'Key' => $this->exporter->getExportDestinationPath(),
        ]);
    }

    /** @group database_transaction */
    public function testExportCustomerProcess(ApiTester $I)
    {
        $I->wantTo("test export customer - complete process");

        $this->exporter->process();

        // Since it's been deleted
        $I->assertFalse(file_exists($this->exporter->export->getCurrentFile()));

        $this->checkInS3($I);
    }

    /** @group database_transaction */
    public function testExportCustomerExport(ApiTester $I)
    {
        $I->wantTo("test export customer - first step (export)");

        $this->exporter->export();

        $I->assertTrue(file_exists($this->exporter->export->getCurrentFile()));

        $this->checkInS3($I, false);

        $this->checkCSV($I, [
            [
                0 => 1,
                1 => 123456,
                2 => "Joseph",
                3 => "Mallette",
                4 => "<EMAIL>",
                5 => "",
                6 => "",
                7 => "",
                8 => "",
                9 => "",
                11 => "2017-03-22 19:08:26",
                12 => "",
            ],
            [
                0 => 1,
                1 => 1234567,
                2 => "Joseph",
                3 => "Mallette",
                4 => "<EMAIL>",
                5 => "work",
                6 => "",
                7 => "+15148467733",
                8 => "home",
                9 => "",
                11 => "2017-03-22 19:08:26",
                12 => "",
            ],
        ]);
    }

    /** @group database_transaction */
    public function testExportCustomerAfterExport(ApiTester $I)
    {
        $I->wantTo("test export customer - second step ( after export )");

        // This is simulating a full process()
        $this->exporter->process();

        $I->assertFalse(file_exists($this->exporter->export->getCurrentFile()));

        $this->checkInS3($I);
    }

    /** @group database_transaction */
    public function testExportCustomerDeletedIncluded(ApiTester $I)
    {
        $I->wantTo("test export customer - include deleted");

        // Delete a customer, it will go to sf_deleted_customer table
        $response = $I->doDirectDelete($this->app, "customers/1");
        $results = json_decode($response->getContent());

        $this->exporter->export();

        $I->assertTrue(file_exists($this->exporter->export->getCurrentFile()));

        $this->checkInS3($I, false);

        $result = $I->getRecordFromTable(
            $this->app['repositories.mysql'],
            'sf_deleted_customer',
            1
        );
        $I->assertEquals('<EMAIL>', $result['email']);

        $deletedAt = $result['deleted_at'];
        // This should be plenty of leeway to ensure the date is valid.
        // The time zone in DB may be different from that of machine which is running tests.
        // So just comment it out.

        // $I->assertTrue(time() - strtotime($deletedAt) < 60*10);

        $this->checkCSV($I, [
            [
                0 => 1,
                1 => 1234567,
                2 => "Joseph",
                3 => "Mallette",
                4 => "<EMAIL>",
                5 => "work",
                6 => "",
                7 => "+15148467733",
                8 => "home",
                9 => "",
                11 => "2017-03-22 19:08:26",
                12 => "",
            ],
            [
                0 => 1,
                1 => 123456,
                2 => "Joseph",
                3 => "Mallette",
                4 => "<EMAIL>",
                5 => "",
                6 => "",
                7 => "",
                8 => "",
                9 => "",
                // 10 => we can't know the created date
                11 => "2017-03-22 19:08:26", // We don't modified modification_date on deleted object
                12 => $deletedAt,
            ],
        ]);
    }

    private function checkInS3(ApiTester $I, $testExist = true)
    {
        $key = $this->exporter->getExportDestinationPath();

        $exist = $this->cloudstorageClient->doesObjectExist($this->cloudstorageClient->getBucket(), $key);

        if ($testExist) {
            $I->assertTrue($exist);
        } else {
            $I->assertFalse($exist);
        }
    }

    private function checkCSV(ApiTester $I, $expected)
    {
        // This can be test only if you don't do the full process() since we delete the tmp CSV file
        // Here we will load the tmp CSV file and read from it and see if the data are good

        $filename = $this->exporter->export->getCurrentFile();

        $data = $this->filemanager->openAndRead($filename);

        $I->assertEquals(count($expected), count($data));

        $loop = 0;
        foreach ($expected as $customer) {
            foreach ($customer as $key => $val) {
                $I->assertEquals($val, $data[$loop][$key]);
            }
            $loop++;
        }
    }
}
