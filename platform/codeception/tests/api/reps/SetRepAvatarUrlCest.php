<?php

use SF\ApiTester;
use SF\api\BaseApi;

class SetRepAvatarUrlCest extends BaseApi
{
    /** @group database_transaction */
    public function testCreateUserWithAvatar(ApiTester $I)
    {
        require __DIR__ . '/../../fixtures/database/users.php';

        $I->haveInDatabase('wp_users', $userReggie);

        $I->wantTo("create a user w/ an avatar");

        list($params, $res) = $this->createUser($I);
        $this->checkArray(
            $I,
            [
                "user_login" => $params["user_login"],
                "user_email" => $params["user_email"],
                "type" => "rep",
                "group" => "1",
            ],
            $res
        );
    }

    /** @group database_transaction */
    public function testUpdateUserWithAvatar(ApiTester $I)
    {
        $I->wantTo("update my rep's avatar");

        list($params, $res) = $this->createUser($I);

        $reqParams = [
            'avatar_url' => TEST_AVATAR_B,
        ];

        $response = $I->doDirectPut($this->app, "reps/{$res->ID}", $reqParams, $res->ID, 'reggie');
        $I->assertEquals(200, $response->getStatusCode());
    }

    private function createUser(ApiTester $I)
    {
        $login = uniqid("caprice");

        $params = [
            'user_login' => $login,
            'user_pass' => '123123Aa',
            'user_email' => "ppp+$<EMAIL>",
            'avatar_url' => TEST_AVATAR_A,
        ];

        $response = $I->doDirectPost($this->app, "reps", $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        return [$params, $results];
    }
}
