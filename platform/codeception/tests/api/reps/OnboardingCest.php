<?php

use SF\ApiTester;
use SF\api\BaseApi;

class OnboardingCest extends BaseApi
{
    public const USER_ID1 = 2;

    protected $fixtures;

    public function _before($I)
    {
        parent::_before($I);
        $this->fixtures = require __DIR__ . '/../../fixtures/reps/rep-onboarding.php';
    }

    /** @group database_transaction */
    public function testCoreVerifyValidToken(ApiTester $I)
    {
        $I->wantTo("Verify a valid token");

        $id     = $I->haveInDatabase('sf_rep_onboarding', $this->fixtures['onboarding-valid']);
        $params = [
            'token' => $this->fixtures['onboarding-valid']['token']
        ];

        $response = $I->doDirectPost($this->app, 'onboarding-verify-token', $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertTrue(!empty($results->data->access_token));

        return $results->data->access_token;
    }

    /** @group database_transaction */
    public function testCoreVerifyInvalidToken(ApiTester $I)
    {
        $I->wantTo("Verify an invalid token");

        $params = [
            'token' => '6666666'
        ];

        $response = $I->doDirectPost($this->app, 'onboarding-verify-token', $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(401, $response->getStatusCode());
        $I->assertTrue(!empty($results->error));
        $I->assertEquals('Invalid Token', $results->error);
    }

    /** @group database_transaction */
    public function testCoreVerifyNonSellingToken(ApiTester $I)
    {
        $I->wantTo("Verify a non selling token");

        $id = $I->haveInDatabase('sf_rep_onboarding', $this->fixtures['onboarding-non-selling']);

        $params = [
            'token' => $this->fixtures['onboarding-non-selling']['token']
        ];

        $response = $I->doDirectPost($this->app, 'onboarding-verify-token', $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $I->assertTrue(!empty($results->data->access_token));
    }

    /** @group database_transaction */
    public function testCoreVerifyCompletedToken(ApiTester $I)
    {
        $I->wantTo("Verify a completed token");

        $id = $I->haveInDatabase('sf_rep_onboarding', $this->fixtures['onboarding-completed']);

        $params = [
            'token' => $this->fixtures['onboarding-completed']['token']
        ];

        $response = $I->doDirectPost($this->app, 'onboarding-verify-token', $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(401, $response->getStatusCode());
        $I->assertTrue(!empty($results->error));
        $I->assertEquals('This token has already been used. <NAME_EMAIL> for assistance.', $results->error);
    }

    /** @group database_transaction */
    public function testCoreRoutesAccessToken(ApiTester $I)
    {
        $I->wantTo("Verify that I can access routes with a valid access token");

        $accessToken = $this->testCoreVerifyValidToken($I);
        $headers = [
            'Authorization' => $accessToken,
        ];
        // GET
        $response = $I->doDirectGetWithHeaders($this->app, 'onboarding-rep-onboarding', [], $headers);
        $I->assertEquals(200, $response->getStatusCode());

        $response = $I->doDirectGetWithHeaders($this->app, 'onboarding-reps/' . self::USER_ID1, [], $headers);
        $I->assertEquals(200, $response->getStatusCode());

        $response = $I->doDirectGetWithHeaders($this->app, 'onboarding-reps', [], $headers);
        $I->assertEquals(200, $response->getStatusCode());

        // POST

        // Promote
        $params = [
            'token'          => $this->fixtures['onboarding-valid']['token'],
            'user_login'     => 'random_user_login',
            'user_pass'      => 'random_user_pass',
            'rep_first_name' => 'random_first_name',
            'rep_last_name'  => 'random_last_name',
            'rep_email'      => '<EMAIL>',
            'store'          => $this->fixtures['onboarding-valid']['store'],
        ];

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-promote', $params, $headers);
        $results = json_decode($response->getContent());

        $userId = $results->wp_user_id;

        // Import contacts
        $params = [
            [
                'id'      => '7',
                'name'    => 'john',
                'email'   => '<EMAIL>',
                'user_id' => $userId,
                'origin'  => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_DEVICE_IMPORT
            ]
        ];

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-customers', $params, $headers);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-confirmation/' . $userId, [], $headers);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        // PUT
        // Update Pictures (same route than save specialties)
        $params = [
            'id'         => $userId,
            'avatar_url' => 'https://s3.amazonaws.com/sf-userimage/1504037941721-163-rep-image',
        ];

        $response = $I->doDirectPutWithHeaders($this->app, 'onboarding-reps/' . self::USER_ID1, $params, $headers);
        $I->assertEquals(200, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testOnboardingCustomersWithDifferentPhoneNo(ApiTester $I)
    {
        $I->wantTo("Verify Customer Onboarding With Different Phone Number");

        $accessToken = $this->testCoreVerifyValidToken($I);
        $headers = [
            'Authorization' => $accessToken,
        ];

        // Promote
        $params = [
            'token'          => $this->fixtures['onboarding-valid']['token'],
            'user_login'     => uniqid('username'), // user_login must start by a letter
            'user_pass'      => uniqid(),
            'rep_first_name' => uniqid(),
            'rep_last_name'  => uniqid(),
            'rep_email'      => uniqid() . '@salesfloor.net',
            'store'          => $this->fixtures['onboarding-valid']['store'],
        ];

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-promote', $params, $headers);
        $results = json_decode($response->getContent());
        $userId = $results->wp_user_id;

        $params = [
            [
                'user_id' => $userId,
                'name'    => 'John',
                'email'   => '<EMAIL>',
                'phone'   => '+13653451234',    // Valid Phone Number For User Country (CA)
                'origin'  => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_DEVICE_IMPORT
            ],
            [
                'user_id' => $userId,
                'name'    => 'Jane',
                'email'   => '<EMAIL>',
                'phone'   => '+919662366071',    // InValid Phone Number For User Country, But Valid From Available Countries (IN)
                'origin'  => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_DEVICE_IMPORT
            ],
            [
                'user_id' => $userId,
                'name'    => 'Sergio',
                'email'   => '<EMAIL>',
                'phone'   => '+1011525552924788',   // InValid and Long Phone Number For Country
                'origin'  => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_DEVICE_IMPORT
            ],
            [
                'user_id' => $userId,
                'name'    => 'Shawn',
                'email'   => '<EMAIL>',
                'phone'   => '6479718903',    // Valid Phone Number W/O Country Code
                'origin'  => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_DEVICE_IMPORT
            ],
            [
                'user_id' => $userId,
                'name'    => 'Sean',
                'email'   => '<EMAIL>',
                'phone'   => '+91912222222233',    // InValid Phone Number But with India as Country Code (+91)
                'origin'  => \Salesfloor\Models\Customer::ORIGIN_MOBILE_REP_DEVICE_IMPORT
            ],
        ];

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-customers', $params, $headers);
        $I->assertEquals(200, $response->getStatusCode());

        $I->seeInDatabase(
            'sf_customer',
            [
                'user_id' => $params[0]['user_id'],
                'name'    => $params[0]['name'],
                'email'   => $params[0]['email'],
                'phone'   => $params[0]['phone'],   // Phone Number is Valid, It will be Saved as is
                'origin'  => $params[0]['origin']
            ]
        );
        $I->seeInDatabase(
            'sf_customer',
            [
                'user_id' => $params[1]['user_id'],
                'name'    => $params[1]['name'],
                'email'   => $params[1]['email'],
                'phone'   => $params[1]['phone'],   // Phone Number is Valid, It will be Saved as is
                'origin'  => $params[1]['origin']
            ]
        );
        $I->seeInDatabase(
            'sf_customer',
            [
                'user_id' => $params[2]['user_id'],
                'name'    => $params[2]['name'],
                'email'   => $params[2]['email'],
                'phone'   => null,                  // Phone Number is InValid and Long, It will be Saved as NULL
                'origin'  => $params[2]['origin']
            ]
        );
        $I->seeInDatabase(
            'sf_customer',
            [
                'user_id' => $params[3]['user_id'],
                'name'    => $params[3]['name'],
                'email'   => $params[3]['email'],
                'phone'   => "+1" . $params[3]['phone'],  // Phone Number is Valid W/O Country Code, It will be Saved with Country Code after Normalize
                'origin'  => $params[3]['origin']
            ]
        );
        $I->seeInDatabase(
            'sf_customer',
            [
                'user_id' => $params[4]['user_id'],
                'name'    => $params[4]['name'],
                'email'   => $params[4]['email'],
                'phone'   => null,                  // Phone Number is InValid with Valid Country Code, It will be Saved as NULL
                'origin'  => $params[4]['origin']
            ]
        );
    }

    /** @group database_transaction */
    public function testCoreRoutesWithoutAccessToken(ApiTester $I)
    {
        $I->wantTo("Verify that we can't access routes without access token");

        // OBDT have 2 auth because we want to put mobile confige route there too. This route
        // can also be accessed by JWT.

        $headers = [
            'Authorization' => null,
        ];

        // GET
        $response = $I->doDirectGetWithHeaders($this->app, 'onboarding-rep-onboarding', [], $headers);
        $this->checkDenied($I, $response);

        $response = $I->doDirectGetWithHeaders($this->app, 'onboarding-reps/' . self::USER_ID1, [], $headers);
        $this->checkDenied($I, $response);

        $response = $I->doDirectGetWithHeaders($this->app, 'onboarding-reps', [], $headers);
        $this->checkDenied($I, $response);

        // POST
        $params   = [
            [
                'id'      => '7',
                'name'    => 'john',
                'email'   => '<EMAIL>',
                'user_id' => 1
            ]
        ];
        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-customers', $params, $headers);
        $this->checkDenied($I, $response);

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-promote', [], $headers);
        $this->checkDenied($I, $response);

        $response = $I->doDirectPostWithHeaders($this->app, 'onboarding-confirmation/' . self::USER_ID1, [], $headers);
        $this->checkDenied($I, $response);

        // PUT
        $response = $I->doDirectPutWithHeaders($this->app, 'onboarding-reps/' . self::USER_ID1, [], $headers);
        $this->checkDenied($I, $response);
    }

    /** @group database_transaction */
    public function testOnboardingRepWithTextEnabled($I, $response)
    {
        $I->wantTo("setup a rep with text messaging enabled");
        $token = 'test-token';
        $name  = "Toto$token";
        $email = "ppp+$<EMAIL>";

        $params = [
            "token"                  => $token,
            "retailer_rep_id"        => $token,
            "rep_first_name"         => $name,
            "rep_last_name"          => "Rep",
            "rep_email"              => $email,
            "store"                  => '' . STORE_ID,
            "text_messaging_enabled" => '1'
        ];

        $I->updateInDatabase('wp_users', ['group' => 5], "ID = 1");

        $response = $I->doDirectPost($this->app, "rep-onboarding", $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $expectedResults = array_merge($params, ['group' => '1', 'selling_mode' => '1', 'text_messaging_enabled' => '1']);
        $this->checkArray($I, $expectedResults, $results);
    }

    private function checkDenied($I, $response)
    {
        $I->assertEquals(401, $response->getStatusCode());
        $results = json_decode($response->getContent());
        $I->assertTrue(!empty($results->error));
        $I->assertEquals('A Token was not found in the TokenStorage.', $results->error);
    }
}
