<?php

namespace SF\api\reps;

use Salesfloor\Models\RepOnboarding as RepOnboardingModel;
use SF\ApiTester;
use SF\api\BaseApi;
use Salesfloor\API\Controllers\RepOnboarding;

class StepThroughOnboardingCest extends BaseApi
{
    /** @group database_transaction */
    public function testEverything(ApiTester $I)
    {
        $stepsCompleted = "0";

        $I->wantTo("setup my rep token");

        // This call is made by reggie and he's not an "admin" so he doesn't have access to "create-user" permissions
        // Will fake the DB for now so it works
        $I->updateInDatabase('wp_users', ['group' => 5], "ID = 1");


        $token = 'Token';
        $name = "Toto$token";
        $email = "ppp+$<EMAIL>";
        $params = [
            "token" => $token,
            "retailer_rep_id" => $token,
            "rep_first_name" => $name,
            "rep_last_name" => "Rep",
            "rep_email" => $email,
            "store" => '' . STORE_ID,
        ];

        $response = $I->doDirectPost($this->app, "rep-onboarding", $params);
        $result = json_decode($response->getContent());
        $expectedResult = array_merge($params, [
            'group' => '1',
            'selling_mode' => '1'
        ]);
        $this->checkArray($I, $expectedResult, $result);
        $I->assertEquals(200, $response->getStatusCode());

        // SF-21121 Setup some contacts that should get automatically assigned to the
        // rep as they onboard
        $params = [
            [
                'user_id'    => 0,
                'email'      => '<EMAIL>',
                'first_name' => 'John',
                'last_name'  => 'Smith',
                'origin'     => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            ],
            [
                'user_id'    => 0,
                'email'      => '<EMAIL>',
                'first_name' => 'John',
                'last_name'  => 'Doe',
                'origin'     => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            ],
            [
                'user_id'    => 0,
                'email'      => '<EMAIL>',
                'first_name' => 'Jane',
                'last_name'  => 'Doe',
                'origin'     => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
            ],
        ];

        $customers = [];
        foreach ($params as $param) {
            $response = $I->doDirectPost($this->app, "customers", $param);
            $result = json_decode($response->getContent());
            $customers[] = $result;
        }

        /** @var \Doctrine\DBAL\Query\QueryBuilder $qb */
        $qb = $this->app['repositories.mysql']->getQueryBuilder();
        $qb = $qb->update('sf_customer', 'c')
                ->set('user_id', 'null')
                ->set("unassigned_employee_id", "\"$token\"");
        $qb->execute();

        $I->wantTo("promote my rep token to a user");
        $login = "uuu$token";
        $params = [
            "username" => $login,
            "password" => "potatoponybatterystaple",
        ];

        $response = $I->doDirectPost($this->app, "rep-onboarding/$token/promote", $params);
        $result = json_decode($response->getContent());
        $expectedResult += [
            'steps_completed' => $stepsCompleted,
        ];
        $this->checkArray($I, $expectedResult, $result);
        $I->assertEquals(200, $response->getStatusCode());
        $repId = $result->wp_user_id;

        $response = $I->doDirectGet($this->app, 'specialties');
        $result = json_decode($response->getContent(), true);
        $validSpecialties = [];
        foreach ($result as $k => $v) {
            if ($k != 'pagination') {
                $validSpecialties[] = $v;
            }
        }
        $I->wantTo("record that my rep's specialties have been set");
        shuffle($validSpecialties);
        $specialties = array_slice($validSpecialties, 0, 3);
        $specialtyIds = [];
        foreach ($specialties as $s) {
            $specialtyIds[] = $s['category_id'];
        }

        $params = [
            "specialties" => $specialtyIds,
        ];
        $response = $I->doDirectPut($this->app, "/reps/" . $repId, $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $stepsCompleted |= RepOnboardingModel::STEP_CATEGORIES;
        $response = $I->doDirectGet($this->app, "rep-onboarding/$token");
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->checkArray($I, ['steps_completed' => $stepsCompleted], $result);

        $I->wantTo("record that my rep's profile pic has been set");
        $params = [
            'avatar_url' => TEST_AVATAR_B,
        ];
        $response = $I->doDirectPut($this->app, "/reps/" . $repId, $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $stepsCompleted |= RepOnboardingModel::STEP_UPLOAD_PIC;
        $response = $I->doDirectGet($this->app, "rep-onboarding/$token");
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->checkArray($I, ['steps_completed' => $stepsCompleted], $result);

        $I->wantTo("record that my rep has imported contacts");
        $id = 'random_id';
        $this->app['configs']['retailer.onboarding.step.add_contacts'] = true ;
        $email = "$<EMAIL>";
        $params = [
            "user_id" => $repId,
            "email" => $email,
        ];
        $response = $I->doDirectPost($this->app, "customers", $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $stepsCompleted |= RepOnboardingModel::STEP_EMAIL;
        $response = $I->doDirectGet($this->app, "rep-onboarding/$token");
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->checkArray($I, ['steps_completed' => $stepsCompleted], $result);

        $I->wantTo("record that I've set my rep's about-me text");
        $aboutMe = 'random about_me';
        $params = [
            "introduction" => $aboutMe,
        ];
        $response = $I->doDirectPut($this->app, "/reps/" . $repId, $params);
        $result = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());

        $response = $I->doDirectGet($this->app, "customers/" . $customers[0]->ID);
        $result = json_decode($response->getContent());
        $I->assertEquals($repId, $result->user_id);

        // It's undefined which of the dupe customer ids gets assigned to the rep, so
        // we're not testing for that. The proper fix is to catch duplicates at import
        // time, not at assign-time. We're just interested in testing that the presence
        // of duplicate data doesn't fail the whole assignment, i.e. that $customerId
        // still gets assigned to $repId even when there are dupe customers.
    }
}
