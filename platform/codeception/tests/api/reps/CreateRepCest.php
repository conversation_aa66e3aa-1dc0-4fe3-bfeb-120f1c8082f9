<?php

use SF\ApiTester;
use SF\api\BaseApi;

class CreateRepCest extends BaseApi
{
    /** @group database_transaction */
    public function testCoreCreateMinimumUser(ApiTester $I)
    {
        $I->wantTo("create a minimum viable user");
        $login = uniqid("caprice");
        $params = [
            'user_login' => $login,
            'user_pass' => '123123Aa',
            'user_email' => "ppp+$<EMAIL>",
        ];
        $response = $I->doDirectPost($this->app, "reps?fields=specialties", $params);
        $results = json_decode($response->getContent());
        $I->assertEquals(200, $response->getStatusCode());
        $this->checkArray(
            $I,
            [
                "user_login" => $login,
                "user_email" => $params["user_email"],
                "type" => "rep",
                "group" => "1",
                "specialities" => "",
            ],
            $results
        );

        $I->assertGreaterThan(
            0,
            strcmp($results->user_registered, gmdate('Y-m-d H:i:s', strtotime('-1 day'))),
            'user registered date is after yesterday'
        );
        $I->assertLessThan(
            0,
            strcmp($results->user_registered, gmdate('Y-m-d H:i:s', strtotime('+1 day'))),
            'user registered date is before tomorrow'
        );
    }

    /** @group database_transaction */
    public function testCoreCreateEmptyRequest(ApiTester $I)
    {
        $I->wantToTest("an empty request body is rejected");
        $response = $I->doDirectPost($this->app, "reps", []);
        $results = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCoreMandatoryUserLogin(ApiTester $I)
    {
        $I->wantToTest("user_login is a required field");
        $login = uniqid("UNACCEPTABLE");
        $response = $I->doDirectPost($this->app, "reps", ["user_pass" => "123123Aa", "user_email" => "ppp+$<EMAIL>"]);
        $results = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCoreMandatoryUserPass(ApiTester $I)
    {
        $I->wantToTest("user_pass is a required field");
        $login = uniqid("UNACCEPTABLE");
        $response = $I->doDirectPost($this->app, "reps", ["user_login" => $login, "user_email" => "ppp+$<EMAIL>"]);
        $results = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCoreMandatoryUserEmail(ApiTester $I)
    {
        $I->wantToTest("user_email is a required field");
        $login = uniqid("UNACCEPTABLE");
        $response = $I->doDirectPost($this->app, "reps", ["user_login" => $login, "user_pass" => "123123Aa"]);
        $results = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCoreInvalidUserLogin(ApiTester $I)
    {
        $I->wantToTest("invalid user_login gets rejected");
        $login = uniqid("UNACCEPTABLE");
        $response = $I->doDirectPost($this->app, "reps", ["user_login" => "shopLol_", "user_pass" => "123123Aa", "user_email" => "ppp+$<EMAIL>"]);
        $results = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCoreInvalidUserAlias(ApiTester $I)
    {
        $I->wantToTest("invalid user_alias gets rejected");
        $login = uniqid("UNACCEPTABLE");
        $response = $I->doDirectPost($this->app, "reps", ["user_login" => "lol_shop", "user_alias" => "shop_lol", "user_pass" => "123123Aa", "user_email" => "ppp+$<EMAIL>"]);
        $results = json_decode($response->getContent());
        $I->assertEquals(400, $response->getStatusCode());
    }

    /** @group database_transaction */
    public function testCoreCreateFullDetailedUser(ApiTester $I)
    {
        $I->wantTo("create a fully-detailed user");
        $login = uniqid("ramone");
        $params = [
            'user_login' => $login,
            'user_pass' => '123123Aa',
            'user_nicename' => 'wtf_is_this',
            'user_email' => "ppp+$<EMAIL>",
            'user_status' => '1',
            'display_name' => 'should get overriden',
            'user_alias' => "a$login",
            'store' => "" . STORE_ID,
            'type' => 'rep',
            'employee_id' => str_replace(" ", '', microtime()),
            'group' => '2',
            'selling_mode' => '0',
            'first_name' => ucfirst($login),
            'last_name' => "Rep",
            'title' => "ASSociate",
        ];
        $response = $I->doDirectPost($this->app, "reps", $params);
        $results = json_decode($response->getContent(), true);
        $I->assertEquals(200, $response->getStatusCode());
    }
}
