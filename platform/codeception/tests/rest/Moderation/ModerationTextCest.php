<?php

declare(strict_types=1);

namespace SF\rest\Moderation;

use SF\RestTester;
use Monolog\Logger;
use SF\rest\BaseRest;
use Codeception\Util\Stub;
use GuzzleHttp\Psr7\Response;
use Codeception\Util\Fixtures;
use Salesfloor\Services\Helpers\StringSplitter;
use Symfony\Component\HttpFoundation\HeaderBag;
use Salesfloor\Services\Moderation\Moderator\HiveModerator;

/**
 * @group database_transaction
 */
class ModerationTextCest extends BaseRest
{
    public function testModerationTextLongSentence(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextLongSentence1'),
            Fixtures::get('testModerationTextLongSentence2'),
            Fixtures::get('testModerationTextLongSentence3'),
        ]);

        // I need to store the input also, because of the logic to call api multiple times is directly
        // related to the length of the input.
        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => Fixtures::get('testModerationTextLongSentenceInput'),
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Do you know where I can buy chocolate ? Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ? You can die !!',
                    'is_moderator_flagged' => 1,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $I->assertNumberRowsInTable(1, 'sf_moderations');

        $row = $I->grabRowsFromDatabase('sf_moderations', null, [
            'id' => $response->data->id
        ]);

        $this->validateArray([[
            'id' => $response->data->id,
            'input_type' => 'text',
            'is_moderator_flagged' => 1,
            'moderator_response' => '[{"id":"7ddc9240-33d6-11ef-97df-156d81b027ce","code":200,"project_id":64620,"user_id":4762,"created_on":"2024-06-26T16:09:40.000Z","status":[{"status":{"code":"0","message":"SUCCESS"},"response":{"pii_entities":[],"urls":[],"custom_classes":[],"moderated_classes":["sexual","hate","violence","bullying","spam","promotions","gibberish","child_exploitation","phone_number","drugs","self_harm","child_safety","weapons","redirection"],"language":"EN","input":{"hash":"455acc1e2c62cd7480ce3d9d6f6f1f85","inference_client_version":"0.0.0","model_type":"TEXT_CLASSIFICATION","text":"Do you know where I can buy chocolate ? Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you kn","model":"pytorch_textmod_xlmr_multilevel_05_10_24_triton","model_version":1,"id":"7ddc9240-33d6-11ef-97df-156d81b027ce","created_on":"2024-06-26T16:09:39.940Z","user_id":4762,"project_id":64620,"charge":0.0005},"output":[{"classes":[{"class":"bullying","score":0},{"class":"drugs","score":0},{"class":"child_exploitation","score":0},{"class":"gibberish","score":0},{"class":"self_harm","score":0},{"class":"hate","score":0},{"class":"phone_number","score":0},{"class":"promotions","score":0},{"class":"redirection","score":0},{"class":"child_safety","score":0},{"class":"sexual","score":0},{"class":"spam","score":0},{"class":"violence","score":0},{"class":"weapons","score":0}],"start_char_index":0,"time":0,"end_char_index":1024}],"text_filters":[]}}],"from_cache":false},{"id":"7e16b510-33d6-11ef-bec6-0314a282aee8","code":200,"project_id":64620,"user_id":4762,"created_on":"2024-06-26T16:09:40.372Z","status":[{"status":{"code":"0","message":"SUCCESS"},"response":{"language":"EN","moderated_classes":["sexual","hate","violence","bullying","spam","promotions","gibberish","child_exploitation","phone_number","drugs","self_harm","child_safety","weapons","redirection"],"input":{"inference_client_version":"0.0.0","model":"pytorch_textmod_xlmr_multilevel_05_10_24_triton","model_version":1,"text":"ow where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I","model_type":"TEXT_CLASSIFICATION","hash":"455acc1e2c62cd7480ce3d9d6f6f1f85","id":"7e16b510-33d6-11ef-bec6-0314a282aee8","created_on":"2024-06-26T16:09:40.321Z","user_id":4762,"project_id":64620,"charge":0.0005},"output":[{"end_char_index":1024,"classes":[{"class":"bullying","score":0},{"class":"drugs","score":0},{"class":"child_exploitation","score":0},{"class":"gibberish","score":0},{"class":"self_harm","score":0},{"class":"hate","score":0},{"class":"phone_number","score":0},{"class":"promotions","score":0},{"class":"redirection","score":0},{"class":"child_safety","score":0},{"class":"sexual","score":0},{"class":"spam","score":0},{"class":"violence","score":0},{"class":"weapons","score":0}],"time":0,"start_char_index":0}],"custom_classes":[],"pii_entities":[],"urls":[],"text_filters":[]}}],"from_cache":false},{"id":"7e5592d0-33d6-11ef-a987-0d7ba3825fca","code":200,"project_id":64620,"user_id":4762,"created_on":"2024-06-26T16:09:40.794Z","status":[{"status":{"code":"0","message":"SUCCESS"},"response":{"output":[{"start_char_index":0,"end_char_index":932,"classes":[{"score":0,"class":"bullying"},{"score":0,"class":"drugs"},{"score":0,"class":"child_exploitation"},{"score":0,"class":"gibberish"},{"score":0,"class":"self_harm"},{"score":0,"class":"hate"},{"score":0,"class":"phone_number"},{"score":0,"class":"promotions"},{"score":0,"class":"redirection"},{"score":0,"class":"child_safety"},{"score":0,"class":"sexual"},{"score":0,"class":"spam"},{"score":2,"class":"violence"},{"score":0,"class":"weapons"}],"time":0}],"text_filters":[],"pii_entities":[],"custom_classes":[],"moderated_classes":["sexual","hate","violence","bullying","spam","promotions","gibberish","child_exploitation","phone_number","drugs","self_harm","child_safety","weapons","redirection"],"language":"EN","urls":[],"input":{"model_version":1,"model":"pytorch_textmod_xlmr_multilevel_05_10_24_triton","hash":"455acc1e2c62cd7480ce3d9d6f6f1f85","inference_client_version":"0.0.0","model_type":"TEXT_CLASSIFICATION","text":" can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ?Do you know where I can buy chocolate ? You can die !!","id":"7e5592d0-33d6-11ef-a987-0d7ba3825fca","created_on":"2024-06-26T16:09:40.733Z","user_id":4762,"project_id":64620,"charge":0.0005}}}],"from_cache":false}]',
            'moderator' => 'hive',
            'source' => null,
            'source_id' => null,
            'user_id' => 1,
        ]], $row);
    }

    public function testModerationTextGlobalThreshold(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextGlobalThreshold0'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextGlobalThreshold1'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => "Not important",
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextGlobalThreshold2'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => "Not important",
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => true,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextGlobalThreshold3'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => "Not important",
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'is_moderator_flagged' => 1,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $I->assertNumberRowsInTable(4, 'sf_moderations');
    }

    public function testModerationTextCustomThreshold(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [
            HiveModerator::HIVE_CLASS_VIOLENCE => 3,
        ], [
            Fixtures::get('testModerationTextGlobalThreshold2'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $this->mockupModerator(2, [
            HiveModerator::HIVE_CLASS_VIOLENCE => 3,
        ], [
            Fixtures::get('testModerationTextGlobalThreshold3'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 1,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $I->assertNumberRowsInTable(2, 'sf_moderations');
    }

    public function testModerationTextNotAcceptedClasses(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextNotAcceptedClasses'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $I->assertNumberRowsInTable(1, 'sf_moderations');

        $row = $I->grabRowsFromDatabase('sf_moderations', null, [
            'id' => $response->data->id
        ]);

        $this->validateArray([[
            'id' => $response->data->id,
            'input_type' => 'text',
            'input' => 'Not important',
            'is_moderator_flagged' => 0,
            'moderator_response' => '[{"id":"7ddc9240-33d6-11ef-97df-156d81b027ce","code":200,"project_id":64620,"user_id":4762,"created_on":"2024-06-26T16:09:40.000Z","status":[{"status":{"code":"0","message":"SUCCESS"},"response":{"pii_entities":[],"urls":[],"custom_classes":[],"moderated_classes":["sexual","hate","violence","bullying","spam","promotions","gibberish","child_exploitation","phone_number","drugs","self_harm","child_safety","weapons","redirection"],"language":"EN","input":{"hash":"455acc1e2c62cd7480ce3d9d6f6f1f85","inference_client_version":"0.0.0","model_type":"TEXT_CLASSIFICATION","text":"Not important","model":"pytorch_textmod_xlmr_multilevel_05_10_24_triton","model_version":1,"id":"7ddc9240-33d6-11ef-97df-156d81b027ce","created_on":"2024-06-26T16:09:39.940Z","user_id":4762,"project_id":64620,"charge":0.0005},"output":[{"classes":[{"class":"bullying","score":0},{"class":"drugs","score":0},{"class":"child_exploitation","score":0},{"class":"gibberish","score":0},{"class":"self_harm","score":3},{"class":"hate","score":0},{"class":"phone_number","score":0},{"class":"promotions","score":0},{"class":"redirection","score":0},{"class":"child_safety","score":0},{"class":"sexual","score":0},{"class":"spam","score":0},{"class":"violence","score":0},{"class":"weapons","score":0}],"start_char_index":0,"time":0,"end_char_index":1024}],"text_filters":[]}}],"from_cache":false}]',
            'moderator' => 'hive',
            'source' => null,
            'source_id' => null,
            'user_id' => 1,
        ]], $row);
    }

    public function testModerationTextFailure(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextFailure'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0, // 0 by default on failure
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);

        $I->assertNumberRowsInTable(1, 'sf_moderations');

        $row = $I->grabRowsFromDatabase('sf_moderations', null, [
            'id' => $response->data->id
        ]);

        $this->validateArray([[
            'id' => $response->data->id,
            'input_type' => 'text',
            'input' => 'Not important',
            'is_moderator_flagged' => 0,
            'moderator_response' => '[{"id":"7ddc9240-33d6-11ef-97df-156d81b027ce","code":200,"project_id":64620,"user_id":4762,"created_on":"2024-06-26T16:09:40.000Z","status":[{"status":{"code":"0","message":"FAILED"},"response":{"pii_entities":[],"urls":[],"custom_classes":[],"moderated_classes":["sexual","hate","violence","bullying","spam","promotions","gibberish","child_exploitation","phone_number","drugs","self_harm","child_safety","weapons","redirection"],"language":"EN","input":{"hash":"455acc1e2c62cd7480ce3d9d6f6f1f85","inference_client_version":"0.0.0","model_type":"TEXT_CLASSIFICATION","text":"Not important","model":"pytorch_textmod_xlmr_multilevel_05_10_24_triton","model_version":1,"id":"7ddc9240-33d6-11ef-97df-156d81b027ce","created_on":"2024-06-26T16:09:39.940Z","user_id":4762,"project_id":64620,"charge":0.0005},"output":[{"classes":[{"class":"bullying","score":0},{"class":"drugs","score":0},{"class":"child_exploitation","score":0},{"class":"gibberish","score":0},{"class":"self_harm","score":3},{"class":"hate","score":0},{"class":"phone_number","score":0},{"class":"promotions","score":0},{"class":"redirection","score":0},{"class":"child_safety","score":0},{"class":"sexual","score":0},{"class":"spam","score":0},{"class":"violence","score":0},{"class":"weapons","score":0}],"start_char_index":0,"time":0,"end_char_index":1024}],"text_filters":[]}}],"from_cache":false}]',
            'moderator' => 'hive',
            'source' => null,
            'source_id' => null,
            'user_id' => 1,
        ]], $row);
    }

    public function testModerationTextBinaryClass(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextBinaryClass'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 1,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);
    }

    public function testModerationTextBinaryClassInvalidValue(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextBinaryClassInvalidValue'),
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->debug($response);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0,
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);
    }

    public function testModerationTextFailed(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextBinaryClass')
        ], 500);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input_type' => 'text',
                    'input' => 'Not important',
                    'is_moderator_flagged' => 0, // Because of the 500, we ignore it
                    'user_id' => 1,
                    'source' => null,
                    'source_id' => null,
                ]
            ]
        ], $response);
    }

    public function testModerationTextException(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = true;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextBinaryClass')
        ], 200, new \Exception("random"));

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'errors' => [
                [
                    'status' => 422,
                    'code' => 1110,
                    'title' => 'Moderation failed',
                    'detail' => "We weren't able to call the moderation service or the application wasn't able to process it",
                ]
            ]
        ], $response);
    }

    public function testModerationTextDisabled(RestTester $I): void
    {
        $this->app['configs']['retailer.moderation.text.is_enabled'] = false;

        $this->mockupModerator(2, [], [
            Fixtures::get('testModerationTextBinaryClass')
        ]);

        $response = $I->doDirectPost($I, 'moderation/text', [
            'data' => [
                'type' => 'moderation',
                'attributes' => [
                    'input' => 'Not important',
                ]
            ]
        ]);

        $this->validateArray([
            'errors' => [
                [
                    'status' => 422, // Because the feature is off
                    'code' => 1009,
                ]
            ]
        ], $response);
    }

    private function mockupModerator($globalThreshold, $customThreshold, array $content = [], $httpCode = 200, $exception = null): void
    {
        $this->overwriteAppService('service.moderation.text', Stub::construct(
            '\Salesfloor\Services\Moderation\TextModeration',
            [
                Stub::construct(
                    '\Salesfloor\Services\Moderation\Moderator\HiveModerator',
                    [
                        Stub::makeEmpty(Logger::class),
                        Stub::construct(
                            '\Salesfloor\Services\Moderation\Moderator\ModeratorRequest',
                            [
                                $this->app['guzzle.http.client.factory'],
                                HiveModerator::HIVE_API_URL,
                                new HeaderBag(),
                            ],
                            [
                                'request' => function ($payload) use (&$content, $httpCode, $exception) {
                                    if (!empty($exception)) {
                                        throw new $exception();
                                    }

                                    // I return new response each time, because it can only get content once (stream)
                                    return new Response($httpCode, [], array_shift($content));
                                }
                            ]
                        ),
                        $globalThreshold,
                        $customThreshold,
                        new StringSplitter(),
                    ],
                    [
                    ]
                ),
                $this->app['moderations.manager']
            ],
            [
                 // No overwrites
            ]
        ));
    }
}
