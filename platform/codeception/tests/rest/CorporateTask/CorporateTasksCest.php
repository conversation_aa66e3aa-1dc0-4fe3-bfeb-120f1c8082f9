<?php

namespace SF\rest\CorporateTask;

use SF\RestTester;
use SF\rest\BaseRest;
use Codeception\Util\Fixtures;

class CorporateTasksCest extends BaseRest
{
    /** @group database_transaction */
    public function testChildTaskUpdate(RestTester $I)
    {
        $I->wantTo("Test sf_task to be updated if sf_corporate_tasks been updated (corporate_task is editable)");
        $I->fakeCorporateTaskQueueService();

        $updateData = $this->getUpdateData();

        $this->insertFixtureGroup($I, 'corporate_task_fixture_group_editable');

        $testUpdateId = 1;

        $userId = 6;
        $userName = 'user5';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->directPATCH($this->app, '/v2/corporate-tasks/' . $testUpdateId, [
            'data' => $updateData,
        ]);

        $I->seeInDatabase(
            'sf_corporate_tasks',
            ['id' => $updateData['id'], 'detail' => $updateData['attributes']['detail']]
        );

        // cron & queue about corporate_task/sf_task are in function test cest
    }

    /** @group database_transaction */
    public function testChildTaskUpdateFailed(RestTester $I)
    {
        $I->wantTo("Test sf_task to be updated if sf_corporate_tasks been updated (corporate_task is expired)");

        $updateData = $this->getUpdateData();

        $this->insertFixtureGroup($I, 'corporate_task_fixture_group_expired');

        $testUpdateId = 1;

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->directPATCH($this->app, '/v2/corporate-tasks/' . $testUpdateId, [
            'data' => $updateData,
        ]);

        $result = json_decode($I->grabResponseContent());

        $I->assertEquals(400, $result->errors[0]->status);
        $I->cantSeeInDatabase(
            'sf_corporate_tasks',
            ['id' => $updateData['id'], 'detail' => $updateData['attributes']['detail']]
        );
    }

    /** @group database_transaction */
    public function testGetCorporateTaskWithInclude(RestTester $I)
    {
        $I->wantTo("Test getList corporate task - with include");

        $this->insertFixtureGroup($I, 'corporate_task_get_list_include');

        $this->setTokenInRequestHeader($I, 6); // Group 4

        $response = $I->directGET($this->app, '/v2/corporate-tasks?include=corporate-task-product,corporate-task-asset,corporate-task-specialty,share');

        $this->validateArray(Fixtures::get('corporate_task_get_list_include_response'), $response);
    }

    private function getUpdateData()
    {
        $updateData = [
            'type'       => 'corporate-task',
            'id'         => '1',
            'attributes' => [
                'title'             => 'test title after update',
                'subject'           => 'test subject after update',
                'detail'            => 'test detail after update',
                'body'              => 'test body after update',
                'created_by'        => 999,
                'updated_by'        => 999,
                'reminder_time'     => '2019-04-20 09:01:00',
                'auto_dismiss_time' => '2019-05-20 09:01:00',
                'start_date' => '2019-04-15 09:01:00',
            ]
        ];

        return $updateData;
    }
}
