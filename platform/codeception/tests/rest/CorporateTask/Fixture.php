<?php

namespace SF\rest\CorporateTask;

use Codeception\Util\Fixtures;

class Fixture
{
    public function addCorporateTask()
    {
        Fixtures::add('corporate_task_fixture_group_editable', $this->getFakeCorporateTaskFixtureGroupEditable());
        Fixtures::add('corporate_task_fixture_group_expired', $this->getFakeCorporateTaskFixtureGroupExpired());
        Fixtures::add('corporate_task_get_list_include', $this->getCorporateTaskGetListInclude());
        Fixtures::add('corporate_task_get_list_include_response', json_decode($this->getCorporateTaskGetListIncludeResponse(), true));
    }

    private function getCorporateTaskGetListInclude()
    {
        return [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    'title'             => 'test title after create',
                    'detail'            => 'test detail after create',
                    'subject'           => 'test subject after create',
                    'body'              => 'test body after create',
                    'created_by'        => 474,
                    'updated_by'        => null,
                    'reminder_time'     => '2019-04-20 09:00:00',
                    'auto_dismiss_time' => '2025-05-20 09:00:00',
                    'created_at'        => '2019-03-22 17:25:59',
                    'updated_at'        =>  null,
                ]
            ],
            'sf_corporate_task_assets' => [
                [
                    'id' => 1,
                    'corporate_task_id' => 1,
                    'asset_id' => 1, // Fake
                    'created_at' => '2019-03-22 17:25:59',
                ]
            ],
            'sf_corporate_task_specialties' => [
                [
                    'id' => 1,
                    'corporate_task_id' => 1,
                    'specialty_id' => 'Lj_sKYHDe1hR9eTQjewTMC0Tg5I', // Fake
                    'created_at' => '2019-03-22 17:25:59',
                ]
            ],
            'sf_share_general' => [
                [
                    'user_id'         => 1,
                    'share_type'      => 'email',
                    'recipient_count' => 1,
                    'subject'         => 'not important',
                    'body'            => 'not_important',
                    'created_by'      => 1, // myself
                    'created_at'      => gmdate('Y-m-d H:i:s'),
                    'updated_at'      => null,
                    'event_uniq_id'   => 'uniq1',
                    'source'          => 'corporate_task',
                    'source_id'       => 1,
                ]
            ]
        ];
    }

    private function getCorporateTaskGetListIncludeResponse()
    {
        return <<<DATA
{
    "meta": {
        "pages": 1,
        "total": 1
    },
    "links": {
        "self": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks?sort=&include=corporate-task-product%2Ccorporate-task-asset%2Ccorporate-task-specialty%2Cshare&page%5Bnumber%5D=0&page%5Bsize%5D=10",
        "first": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks?sort=&include=corporate-task-product%2Ccorporate-task-asset%2Ccorporate-task-specialty%2Cshare&page%5Bnumber%5D=0&page%5Bsize%5D=10",
        "prev": null,
        "next": null,
        "last": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks?sort=&include=corporate-task-product%2Ccorporate-task-asset%2Ccorporate-task-specialty%2Cshare&page%5Bnumber%5D=0&page%5Bsize%5D=10"
    },
    "data": [
        {
            "type": "corporate-task",
            "id": "1",
            "attributes": {
                "title": "test title after create",
                "detail": "test detail after create",
                "subject": "test subject after create",
                "body": "test body after create",
                "created_by": "474",
                "updated_by": null,
                "reminder_time": "2019-04-20 09:00:00",
                "auto_dismiss_time": "2025-05-20 09:00:00",
                "updated_at": null,
                "is_deleted": "0",
                "virtual_fields": {
                    "dismiss_count": null,
                    "resolved_count": null,
                    "tasks_created_count": "0",
                    "user_created_by": null,
                    "user_modified_by": null
                }
            },
            "relationships": {
                "corporate-task-asset": {
                    "data": [
                        {
                            "type": "corporate-task-asset",
                            "id": "1"
                        }
                    ],
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1/relationships/corporate-task-asset",
                        "related": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1/corporate-task-asset"
                    }
                },
                "corporate-task-specialty": {
                    "data": [
                        {
                            "type": "corporate-task-specialty",
                            "id": "1"
                        }
                    ],
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1/relationships/corporate-task-specialty",
                        "related": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1/corporate-task-specialty"
                    }
                },
                "share": {
                    "data": [
                        {
                            "type": "share",
                            "id": "1"
                        }
                    ],
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1/relationships/share",
                        "related": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1/share"
                    }
                }
            },
            "links": {
                "self": "https://tests.api.dev.salesfloor.net/v2/corporate-tasks/1"
            }
        }
    ],
    "included": [
        {
            "type": "corporate-task-asset",
            "id": "1",
            "attributes": {
                "corporate_task_id": "1",
                "asset_id": "1"
            }
        },
        {
            "type": "corporate-task-specialty",
            "id": "1",
            "attributes": {
                "corporate_task_id": "1",
                "specialty_id": "Lj_sKYHDe1hR9eTQjewTMC0Tg5I"
            }
        },
        {
            "type": "share",
            "id": "1",
            "attributes": {
                "user_id": "1",
                "share_type": "email",
                "recipient_count": "1",
                "subject": "not important",
                "body": "not_important",
                "updated_at": null,
                "created_by": "1",
                "event_uniq_id": "uniq1",
                "source": "corporate_task",
                "source_id": "1"
            }
        }
    ]
}
DATA;
    }

    private function getFakeCorporateTaskFixtureGroupEditable()
    {
        return [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    'title'             => 'test title after create',
                    'detail'            => 'test detail after create',
                    'subject'           => 'test subject after create',
                    'body'              => 'test body after create',
                    'created_by'        => 474,
                    'updated_by'        => null,
                    'reminder_time'     => '2019-04-20 09:00:00',
                    'auto_dismiss_time' => '2025-05-20 09:00:00',
                    'created_at'        => '2019-03-22 17:25:59',
                    'updated_at'        => null,
                ],
            ],
        ];
    }

    private function getFakeCorporateTaskFixtureGroupExpired()
    {
        return [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    'title'             => 'test title after create',
                    'detail'            => 'test detail after create',
                    'subject'           => 'test subject after create',
                    'body'              => 'test body after create',
                    'created_by'        => 474,
                    'updated_by'        => null,
                    'reminder_time'     => '2019-04-20 09:00:00',
                    'auto_dismiss_time' => '2019-05-20 09:00:00',
                    'created_at'        => '2019-03-22 17:25:59',
                    'updated_at'        => null
                ],
            ],
        ];
    }
}
