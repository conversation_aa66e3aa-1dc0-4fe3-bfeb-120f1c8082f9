<?php

namespace SF\rest\Assets;

use Carbon\Carbon;
use Salesfloor\Models\Asset;
use SF\rest\BaseRest;
use SF\RestTester;

class AssetsCest extends BaseRest
{
    public const CORPORATE_USER_ID = 335;
    public const CORPORATE_USER_NAME = 'testuser335';
    public const USER_ID = 336;
    public const USER_NAME = 'testuser336';
    public const PASSWORD = '123123Aa';
    public const DESTINATION_URL = 'https://www.example.com?v1=1&v2=2&v3=3';

    /** @group database_transaction */
    public function testCreateAssetValid(RestTester $I)
    {
        /** @var \Salesfloor\API\Managers\Assets $assetManager */
        $assetManager = $this->app['assets.manager'];
        $this->addCorporateUserAndAssets($I);

        $attributes = $this->getAssetCreationData();

        $response = $I->doDirectPost($I, 'v2/assets', [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $newAsset = $response->data;

        foreach ($attributes as $key => $value) {
            if ($key !== 'virtual_fields') {
                $I->assertEquals($value, $newAsset->attributes->$key, 'Bad value for key ' . $key);
            }
        }

        $dbPostName = $I->grabFromDatabase('wp_posts', 'post_name', ['ID' => $newAsset->id]);
        $I->assertStringContainsString($attributes['virtual_fields']['label'], $dbPostName);

        // there is some issue on jsonapi put/post response, which data not include virtual_fields, let's check db directly
        $keys = $assetManager->getMetaFields();
        foreach ($attributes['virtual_fields'] as $vKey => $vValue) {
            if ($vKey == 'destination_url') {
                $I->canSeeInDatabase('wp_postmeta', ['post_id' => $newAsset->id, 'meta_key' => $keys[$vKey], 'meta_value' => '{REPPAGE}']);
            } else {
                $I->canSeeInDatabase('wp_postmeta', ['post_id' => $newAsset->id, 'meta_key' => $keys[$vKey], 'meta_value' => $vValue]);
            }
        }

        $I->assertStringContainsString('<table', $newAsset->attributes->post_content);
        $I->assertStringContainsString('sf_asset_footer', $newAsset->attributes->post_content);
        $I->assertStringContainsString(Asset::TARGET_URL_OPTION_STOREFRONT, $newAsset->attributes->post_content);

        $I->assertSame('draft', $newAsset->attributes->post_status);
        $I->assertSame('sf-library', $newAsset->attributes->post_type);
        $I->assertSame('text/html', $newAsset->attributes->post_mime_type);
    }

    /** @group database_transaction */
    public function testCreateAssetValidShopPage(RestTester $I)
    {
        /** @var \Salesfloor\API\Managers\Assets $assetManager */
        $assetManager = $this->app['assets.manager'];
        $this->addCorporateUserAndAssets($I);

        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['asset_url_option'] = Asset::TARGET_URL_OPTION_SHOPPAGE;

        $response = $I->doDirectPost($I, 'v2/assets', [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $newAsset = $response->data;
        $expectedTargetUrl = sprintf('%s%s', Asset::TARGET_URL_OPTION_SHOPPAGE, urlencode(self::DESTINATION_URL));
        $I->assertStringContainsString($expectedTargetUrl, $newAsset->attributes->post_content);

        $keys = $assetManager->getMetaFields();
        $I->canSeeInDatabase(
            'wp_postmeta',
            [
                'post_id' => $newAsset->id,
                'meta_key' => $keys['destination_url'],
                'meta_value' => Asset::TARGET_URL_OPTION_SHOPPAGE . self::DESTINATION_URL,
            ]
        );
    }

    /** @group database_transaction */
    public function testCreateAssetValidExternalPage(RestTester $I)
    {
        /** @var \Salesfloor\API\Managers\Assets $assetManager */
        $assetManager = $this->app['assets.manager'];
        $this->addCorporateUserAndAssets($I);

        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['asset_url_option'] = Asset::TARGET_URL_OPTION_EXTERNAL_PAGE;

        $response = $I->doDirectPost($I, 'v2/assets', [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $newAsset = $response->data;
        $I->assertStringContainsString(self::DESTINATION_URL, $newAsset->attributes->post_content);

        $keys = $assetManager->getMetaFields();
        $I->canSeeInDatabase(
            'wp_postmeta',
            [
                'post_id' => $newAsset->id,
                'meta_key' => $keys['destination_url'],
                'meta_value' => self::DESTINATION_URL,
            ]
        );
    }

    /** @group database_transaction */
    public function testCreateAssetInvalid(RestTester $I)
    {
        /** @var \Salesfloor\API\Managers\Assets $assetManager */
        $assetManager = $this->app['assets.manager'];
        $this->addCorporateUserAndAssets($I);

        $attributes = [
            'post_name'      => 'postName123123',
            'post_author'    => self::CORPORATE_USER_ID,
            'post_title'     => 'Post',
            'target_channel' => 'all',
            'virtual_fields' => [
                'blurb'                        => 'sf_post_description in meta',
                'label'                        => 'testtest',
                'asset_details'                => 'any details',
                'email_subject'                => 'sf_email_subject',
                'asset_suggested_copy'         => 'sf_asset_suggested_copy',
                'asset_url_option'             => Asset::TARGET_URL_OPTION_STOREFRONT,
                'destination_url'              => 'sf_destination_url_value',
                'imageUrl'                     => 'any_image_link',
                'asset_footer'                 => 'sf_asset_footer',

                "start_date" => "2020-01-12 01:00:00",
                "end_date"   => "2031-01-12 01:00:00",
            ]
        ];

        $response = $I->doDirectPost(
            $I,
            'v2/assets',
            [
                'data' => [
                    'type'       => 'asset',
                    'attributes' => $attributes
                ]
            ]
        );

        $I->assertNotEmpty($response->errors, 'this asset expected to be invalid');
    }

    /** @group database_transaction */
    public function testUpdateAssetValid(RestTester $I)
    {
        /** @var \Salesfloor\API\Managers\Assets $assetManager */
        $assetManager = $this->app['assets.manager'];
        $this->addCorporateUserAndAssets($I);

        $attributes = [
            'post_name' => 'test_asset_update',
            'post_author' => self::CORPORATE_USER_ID,
            'post_title' => 'Post title',
            'target_channel' => 'all',
            'virtual_fields' => [
                'blurb'                        => 'sf_post_description in meta',
                'label'                        => 'test_label_update',
                'asset_details'                => 'any details',
                'email_subject'                => 'sf_asset_suggested_subject_line_update',
                'asset_suggested_copy'         => 'sf_asset_suggested_copy',
                'asset_url_option'             => Asset::TARGET_URL_OPTION_STOREFRONT,
                'destination_url'              => 'sf_destination_url_value',
                'imageUrl'                     => 'any_image_link',
                'asset_footer'                 => 'sf_asset_footer',

                "start_date" => "2020-01-12 01:00:00",
                "end_date"   => "2031-01-12 01:00:00",
            ]
        ];

        $id = 10;
        $response = $I->doDirectPatch($I, "v2/assets/$id", [
            'data' => [
                'type' => 'asset',
                "id"  => $id,
                'attributes' => $attributes
            ]
        ]);

        $keys = $assetManager->getMetaFields();
        foreach ($attributes['virtual_fields'] as $vKey => $vValue) {
            if ($vKey == 'destination_url') {
                $I->canSeeInDatabase('wp_postmeta', ['post_id' => $id, 'meta_key' => $keys[$vKey], 'meta_value' => '{REPPAGE}']);
            } else {
                $I->canSeeInDatabase('wp_postmeta', ['post_id' => $id, 'meta_key' => $keys[$vKey], 'meta_value' => $vValue]);
            }
        }

        $I->canSeeInDatabase(
            'wp_posts',
            [
                'ID'         => $id,
                'post_title' => $attributes['post_title'],
                'post_name'  => $attributes['post_name'],
            ]
        );

        $response = $I->doDirectGet($I, 'v2/assets/' . $id);
        $I->assertEquals('testuser335', $response->data->attributes->virtual_fields->author);
        $I->assertStringContainsString('sf_asset_footer', $response->data->attributes->post_content);
    }

    /** @group database_transaction */
    public function testUpdateAssetInValid(RestTester $I)
    {
        /** @var \Salesfloor\API\Managers\Assets $assetManager */
        $assetManager = $this->app['assets.manager'];
        $this->addCorporateUserAndAssets($I);

        $attributes = [
            'post_name' => 'test_asset_update',
            'post_author' => self::CORPORATE_USER_ID,
            'post_title' => 'Post title',
            'target_channel' => 'all',
            'virtual_fields' => [
                'label'                        => 'a_label_over_length_Nulla facilisi. Interdum et malesuada fames ac ante ipsum primis in faucibus. Pellentesque'
                    . 'a posuere nisi. Maecenas rhoncus pharetra leo, a tincidunt nisi facilisis id. Suspendisse mattis tortor commodo, '
                    . 'interdum tortor at, convallis mauris. Phasellus vel urna tristique, lobortis purus eget, gravida dui. Nulla lobortisectet '
                    . 'interdum tortor at, convallis mauris. Phasellus vel urna tristique, lobortis purus eget, gravida dui. Nulla lobortisur. Vivamus et dignissim nisi.'
                    . 'interdum tortor at, convallis mauris. Phasellus vel urna tristique, lobortis purus eget, gravida dui. Nulla lobortisi felis lorem, hendre'
                    . 'efficitur sem et placerat. Morbi congue neque id laoreet tristique. Donec at eros bibendum augue luctus mattis vel in libero. Morb'
                    . 'rit quis vestibulum non, eleifend et magna. Duis blandit mauris non orci pellentesque rhoncus. Suspendisse urna eros, tempor in elit sed, '
                    . 'consequat suscipit tellus. Vestibulum suscipit viverra mauris, a tristique justo cursus sit amet. Donec dignissim elit bibendum porta cons',
                'email_subject' => 'sf_asset_suggested_subject_line_update',
                'asset_suggested_copy'         => 'sf_asset_suggested_copy',
                'asset_url_option'             => Asset::TARGET_URL_OPTION_STOREFRONT,
                'destination_url'              => 'sf_destination_url_value',
                'imageUrl'                     => 'any_image_link',
                'asset_footer'                 => 'sf_asset_footer',

                "start_date" => "2020-01-12 01:00:00",
                "end_date"   => "2031-01-12 01:00:00",
            ]
        ];

        $id = 10;
        $response = $I->doDirectPatch($I, "v2/assets/$id", [
            'data' => [
                'type' => 'asset',
                "id"  => $id,
                'attributes' => $attributes
            ]
        ]);

        $I->assertNotEmpty($response->errors, 'this asset expected to be invalid');
    }

    /** @group database_transaction */
    public function testUpdateAssetStatus(RestTester $I)
    {
        $I->wantTo('test Update asset status to draft, then update back to publish');
        $this->addCorporateUserAndAssets($I);

        $id = 10;
        $requestData = [
            'data' => [
                'type'       => 'asset',
                "id"         => $id,
                'attributes' => [
                    'post_status' => \Salesfloor\Models\Asset::UNPUBLISHED_POST_STATUSES_DRAFT,
                    // for updating  start_date/end_date which has 'T' in the middle cause validation failed
                    'virtual_fields' => [
                        "start_date" => "2020-01-12 01:00:00",
                        "end_date"   => "2031-01-12 01:00:00",
                    ]
                ]
            ]
        ];
        $postContent = '<table><tr><td><a href="{SHOPPAGE}https%3A%2F%2Fwww.stjohnknits.com%2Fshops%2Ffall-2021"><img src="https://salesfloor-assets.s3.amazonaws.com/shoppers/SJK20210813T213933705Z/images/1.jpg" /></a></td></tr></table>';

        $response = $I->doDirectPatch($I, "v2/assets/$id", $requestData);
        $I->canSeeInDatabase(
            'wp_posts',
            [
                'ID'           => $id,
                'post_status'  => \Salesfloor\Models\Asset::UNPUBLISHED_POST_STATUSES_DRAFT,
                'post_content' => $postContent,
            ]
        );

        $requestData = [
            'data' => [
                'type'       => 'asset',
                "id"         => $id,
                'attributes' => [
                    'post_status' => \Salesfloor\Models\Asset::PUBLISHED_POST_STATUSES,
                ]
            ]
        ];

        $response = $I->doDirectPatch($I, "v2/assets/$id", $requestData);
        $I->canSeeInDatabase(
            'wp_posts',
            [
                'ID'           => $id,
                'post_status'  => \Salesfloor\Models\Asset::PUBLISHED_POST_STATUSES,
                'post_content' => $postContent,
            ]
        );
    }

    /** @group database_transaction */
    public function testGetAssetsIfCreatedByCorpAdmin(RestTester $I)
    {
        $this->addCorporateUserAndAssets($I);

        $corpAssetId = 10;
        $I->wantTo("test GetAssets which is create by corporate admin (asset id:$corpAssetId)");

        $getIds = function ($asset) {
            return $asset->id;
        };

        $response = $I->doDirectGet(
            $I,
            'v2/assets?page[number]=0&page[size]=100'
        );

        $assertIds = array_map($getIds, $response->data);

        $I->assertContains((string)$corpAssetId, $assertIds);
    }

    /** @group database_transaction */
    public function testGetAssetsFilteredByScheduledStatus(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by Scheduled asset library status');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[asset_library_status][]=scheduled'
        );

        $I->assertCount(1, $response->data);

        $asset = $response->data[0];
        $startDate = Carbon::createFromFormat('d/m/Y', $asset->attributes->virtual_fields->startDate);

        $I->assertTrue(
            Carbon::now()->lt($startDate),
            'bad start date: ' . $asset->attributes->virtual_fields->startDate
        );
    }

    /** @group database_transaction */
    public function testGetAssetsFilteredByExpiredStatus(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by Expired asset library status');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[asset_library_status][]=expired'
        );

        $I->assertCount(1, $response->data);

        $asset = $response->data[0];
        $endDate = Carbon::createFromFormat('d/m/Y', $asset->attributes->virtual_fields->endDate);

        $I->assertTrue(
            Carbon::now()->gt($endDate),
            'bad end date: ' . $asset->attributes->virtual_fields->endDate
        );
    }

    /** @group database_transaction */
    public function testGetAssetsFilteredByCurrentlyActiveStatus(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by Currently Active asset library status');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[asset_library_status][]=currently-active'
        );

        $I->assertCount(7, $response->data);
        array_walk($response->data, $this->isCurrentlyPublishedFunc($I));
    }

    /** @group database_transaction */
    public function testGetAssetsFilteredByNoneStatusFilter(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered with none asset_library_status(same as &filter[asset_library_status][]=currently-active)');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100'
        );

        $I->assertCount(7, $response->data);
        array_walk($response->data, $this->isCurrentlyPublishedFunc($I));
    }


    /** @group database_transaction */
    public function testGetAssetsFilteredByAllStatusFilter(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by all status, include: currently-active+expired+scheduled');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[asset_library_status][]=all'
        );

        $I->assertCount(9, $response->data);
        array_walk($response->data, $this->isCurrentlyPublishedFunc($I));
    }

    /** @group database_transaction */
    public function testGetAssetsFilteredByCurrentlyActiveStatusWithStringInParam(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by Currently Active asset library status as string');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[asset_library_status]=currently-active'
        );

        $I->assertCount(7, $response->data);
        array_walk($response->data, $this->isCurrentlyPublishedFunc($I));
    }


    private function isCurrentlyPublishedFunc($I)
    {
        return function ($asset) use ($I) {
            $I->assertEquals('publish', $asset->attributes->post_status);

            $startDate = Carbon::create($asset->attributes->virtual_fields->start_date);
            $endDate = Carbon::create($asset->attributes->virtual_fields->end_date);

            $I->assertTrue(
                $asset->attributes->virtual_fields->start_date === null || Carbon::now()->gte($startDate),
                'bad start date: ' . $asset->attributes->virtual_fields->start_date
            );
            $I->assertTrue(
                $asset->attributes->virtual_fields->end_date === null || Carbon::now()->lte($endDate),
                'bad end date: ' . $asset->attributes->virtual_fields->end_date
            );
        };
    }


    /** @group database_transaction */
    public function testGetAssetsFilteredByDraftStatus(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by draft status');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[post_status][]=draft'
        );

        $I->assertCount(2, $response->data);

        $isDraft = function ($asset) use ($I) {
            $I->assertEquals('draft', $asset->attributes->post_status);
        };

        array_walk($response->data, $isDraft);
    }

    /** @group database_transaction */
    public function testGetAssetsFilteredByNotTrash(RestTester $I)
    {
        $I->wantTo('test GetAssets filtered by not trash status(draft&publish)');

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&filter[post_status]=^trash'
        );

        $I->assertCount(9, $response->data);

        $notTrash = function ($asset) use ($I) {
            $I->assertContains($asset->attributes->post_status, ['draft', 'publish']);
        };

        array_walk($response->data, $notTrash);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByPublishedFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by published first");

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100&sort=published-first');

        $getPublishedState = function ($asset) {
            return $asset->attributes->post_status;
        };

        $assetStatuses = array_map($getPublishedState, $response->data);
        $sortedStatuses = array_merge([], $assetStatuses);
        arsort($sortedStatuses, SORT_STRING);

        $I->assertEquals($sortedStatuses, $assetStatuses);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByDraftFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by draft first");

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100&sort=draft-first');

        $getPublishedState = function ($asset) {
            return $asset->attributes->post_status;
        };

        $assetStatuses = array_map($getPublishedState, $response->data);
        $sortedStatuses = array_merge([], $assetStatuses);
        asort($sortedStatuses, SORT_STRING);

        $I->assertEquals($sortedStatuses, $assetStatuses);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByStartDateNewestFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by start date newest first");

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&sort=start-date-newest-first'
        );

        $getStartDate = function ($asset) {
            return $asset->attributes->virtual_fields->startDate;
        };

        $assetDates = array_map($getStartDate, $response->data);
        $sortedDates = array_merge([], $assetDates);
        arsort($sortedDates, SORT_STRING);

        $I->assertEquals($sortedDates, $assetDates);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByStartDateOldestFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by start date oldest first");

        $this->addCorporateUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&sort=start-date-oldest-first'
        );

        $getStartDate = function ($asset) {
            return $asset->attributes->virtual_fields->startDate;
        };

        $assetDates = array_map($getStartDate, $response->data);
        $sortedDates = array_merge([], $assetDates);
        asort($sortedDates, SORT_STRING);

        $I->assertEquals($sortedDates, $assetDates);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByEndDateNewestFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by start date newest first");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&sort=end-date-newest-first'
        );

        $getEndDate = function ($asset) {
            return $asset->attributes->virtual_fields->endDate;
        };

        $assetDates = array_map($getEndDate, $response->data);
        $sortedDates = array_merge([], $assetDates);
        arsort($sortedDates, SORT_STRING);

        $I->assertEquals($sortedDates, $assetDates);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByEndDateOldestFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by end date oldest first");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&sort=end-date-newest-first'
        );

        $getEndDate = function ($asset) {
            return $asset->attributes->virtual_fields->endDate;
        };

        $assetDates = array_map($getEndDate, $response->data);
        $sortedDates = array_merge([], $assetDates);
        asort($sortedDates, SORT_STRING);

        $I->assertEquals($sortedDates, $assetDates);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByCreationDateNewestFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by creation date newest first");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&sort=creation-date-newest-first'
        );

        $getCreationDate = function ($asset) {
            return $asset->attributes->post_date;
        };

        $assetDates = array_map($getCreationDate, $response->data);

        $sortedDates = array_merge([], $assetDates);
        arsort($sortedDates, SORT_STRING);

        $I->assertEquals($sortedDates, $assetDates);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByCreationDateOldestFirst(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by creation date oldest first");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet(
            $I,
            'v2/assets?include=rep&page[number]=0&page[size]=100&sort=creation-date-oldest-first'
        );

        $getCreationDate = function ($asset) {
            return $asset->attributes->post_date;
        };

        $assetDates = array_map($getCreationDate, $response->data);

        $sortedDates = array_merge([], $assetDates);
        asort($sortedDates, SORT_STRING);

        $I->assertEquals($sortedDates, $assetDates);
    }


    /** @group database_transaction */
    public function testGetOne(RestTester $I)
    {
        $I->wantTo('test getOne');

        $this->addCorporateUserAndAssets($I);
        $assetId = 1;

        $this->setTokenInRequestHeader($I, 1);
        $response = $I->doDirectGet($I, 'v2/assets/' . $assetId);

        $I->assertEquals($assetId, $response->data->id);

        $responseAttributes = $response->data->attributes;

        $assertAttributeEquals = function ($expectedValue, $expectedKey) use ($I, $responseAttributes) {
            $I->assertEquals(
                $expectedValue,
                $responseAttributes->$expectedKey,
                'Response data attribute ' . $expectedKey . ' is not equal to expected value ' . $expectedValue . '.'
            );
        };

        $expectedAttributes = [
            'post_author' => 1,
            'post_content' => '<p>test content1</p>',
            'post_title' => 'test asset1',
            'post_status' => 'publish',
            'post_name' => 'testasset1',
            'post_type' => 'sf-library',
        ];

        array_walk($expectedAttributes, $assertAttributeEquals);
    }

    /** @group database_transaction */
    public function testGetOneOfBenUploader(RestTester $I)
    {
        $I->wantTo('test getOne of legacy data, which is from .ben uploader');

        $this->addCorporateUserAndAssets($I);
        $this->addAssetsOfBenUploader($I);
        $this->setTokenInRequestHeader($I, 1);

        $assetId = 21;
        $response = $I->doDirectGet($I, 'v2/assets/' . $assetId);
        $responseAttributes = $response->data->attributes;
        $I->assertEquals('{SHOPPAGE}', $responseAttributes->virtual_fields->asset_url_option, '{SHOPPAGE} should fallback to `asset_url_option`');

        $assetId = 22;
        $response = $I->doDirectGet($I, 'v2/assets/' . $assetId);
        $responseAttributes = $response->data->attributes;
        $I->assertEquals('{REPPAGE}', $responseAttributes->virtual_fields->asset_url_option, '{REPPAGE} should fallback to `asset_url_option`');

        $assetId = 23;
        $response = $I->doDirectGet($I, 'v2/assets/' . $assetId);
        $responseAttributes = $response->data->attributes;
        $I->assertEquals('{REPPAGE}?appointment=1', $responseAttributes->virtual_fields->asset_url_option, '`{REPPAGE}?appointment=1` should fallback to `asset_url_option`');

        $assetId = 24;
        $response = $I->doDirectGet($I, 'v2/assets/' . $assetId);
        $responseAttributes = $response->data->attributes;
        $I->assertEquals('{EXTERNAL_PAGE}', $responseAttributes->virtual_fields->asset_url_option, '`{EXTERNAL_PAGE}` should fallback to `asset_url_option`');
    }

    /** @group database_transaction */
    public function testGetAssetsOnly(RestTester $I)
    {
        $I->wantTo("Test GetAssets only");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100');

        $this->debug($response);

        $I->assertEquals(7, $response->meta->total);
        $I->assertEquals(2, count($response->included));

        // Custom behaviours
        $I->assertEquals('Corporate', $response->data[0]->attributes->virtual_fields->author);
        $I->assertEquals('test asset2', $response->data[2]->attributes->virtual_fields->subject);
        // with the new default sorting based on start date(Ascending)
        $I->assertEquals('24/04/2010', $response->data[0]->attributes->virtual_fields->startDate);
        $I->assertEquals('-', $response->data[0]->attributes->virtual_fields->endDate);
        $I->assertEquals('test asset8', $response->data[0]->attributes->virtual_fields->subject);
    }

    /** @group database_transaction */
    public function testGetAssetsSorted(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100&sort=z-a');

        $this->debug($response);

        // Since the z-a sorting is alphabetical and not numerical asset 10 will be after asset 1 in the results
        $I->assertEquals(9, $response->data[0]->id);
        $I->assertEquals(8, $response->data[1]->id);
        $I->assertEquals(7, $response->data[2]->id);
    }

    /** @group database_transaction */
    public function testGetAssetsSortedByStartDateDefault(RestTester $I)
    {
        $I->wantTo("Test GetAssets sorted by start date");

        $this->addUserAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?page[number]=0&page[size]=100');

        $this->debug($response);

        // Default sort is modified to be based on asset Start Date with ascending order.
        // Assets without a defined Start Date will come AFTER those with a Start Date.
        // Assets without a defined Start Date will be sorted by DESCENDING creation date.
        $I->assertEquals(8, $response->data[0]->id); //start date: 2010-04-24T09:00
        $I->assertEquals(6, $response->data[1]->id); //start date: 2010-04-25T09:00
        $I->assertEquals(2, $response->data[2]->id); //start date: 2010-04-26T09:00
        $I->assertEquals(1, $response->data[3]->id); //start date: 2010-04-27T09:00
        $I->assertEquals(10, $response->data[4]->id); //start date: 2010-04-27T09:00
        $I->assertEquals(9, $response->data[5]->id); // start date: NULL, post_modified: 2019-07-10 15:52:42
        $I->assertEquals(7, $response->data[6]->id); // start date: NULL, post_modified: 2019-07-09 15:52:42
    }

    /** @group database_transaction */
    public function testFilterAssetTargetChannel(RestTester $I)
    {
        $I->wantTo("Test GetAssets filtered by target channel");

        $this->addUserAndAssets($I);

        $url = 'v2/assets?page[number]=0&page[size]=100&filter[target_channel][]=social_post&filter[target_channel][]=all';
        $response = $I->doDirectGet($I, $url);

        $this->debug($response);

        $I->assertEquals(7, count($response->data));
    }

    /** @group database_transaction */
    public function testGetAssetsSearch(RestTester $I)
    {
        $I->wantTo("Test GetAssets");

        $this->addUserAndAssets($I);

        $url = 'v2/assets?include=rep&page[number]=0&page[size]=100&filter[query]=asset8';
        $response = $I->doDirectGet($I, $url);

        $this->debug($response);

        $I->assertEquals(8, $response->data[0]->id);
    }

    /** @group database_transaction */
    public function testGetAssetsForAdminWithFutureStartDate(RestTester $I)
    {
        $I->wantTo("Test GetAssets for corp admin or SF admin with future start date");

        $this->insertFixtureGroup($I, 'corporate_user_assets');

        $this->assertHavingAssetsWithRightDates($I, 7, 'user6');
        $this->assertHavingAssetsWithRightDates($I, 6, 'user5');
    }

    protected function assertHavingAssetsWithRightDates($I, $userId, $userName)
    {
        $this->addUser($I, $userId, $userName);

        $response = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100');

        $this->debug($response);

        $I->assertEquals(7, count($response->data));

        $asset = $response->data[4];
        $I->assertEquals('27/04/2010', $asset->attributes->virtual_fields->startDate);
        $I->assertEquals('27/04/2059', $asset->attributes->virtual_fields->endDate);

        $I->assertEquals('2010-04-27 09:00:00', $asset->attributes->virtual_fields->startDatetime);
        $I->assertEquals('2059-04-27 09:00:00', $asset->attributes->virtual_fields->endDatetime);

        $asset = $response->data[5];
        $I->assertEquals('-', $asset->attributes->virtual_fields->startDate);
        $I->assertEquals('27/04/2059', $asset->attributes->virtual_fields->endDate);

        $I->assertEquals(null, $asset->attributes->virtual_fields->startDatetime);
        $I->assertEquals('2059-04-27 09:00:00', $asset->attributes->virtual_fields->endDatetime);
    }

    /** @group database_transaction */
    public function testGetAssetsStoreMissingTimezone(RestTester $I)
    {
        $I->wantTo("Test GetAssets when the store doesn't have a timezone");

        $this->app['configs']['retailer.store.default_timezone'] = 'America/Los_Angeles';

        $this->insertFixtureGroup($I, 'user_assets');
        $this->insertFixtureGroup($I, 'user_assets_timezone');
        $I->updateInDatabase('wp_users', ['group' => 5], ['ID' => 1]);

        $userId = 337;
        $userName = 'testuser337';
        $this->addUser($I, $userId, $userName);

        $response = $I->doDirectGet($I, 'v2/assets?page[number]=0&page[size]=100');

        $this->debug($response);

        $I->assertEquals(7, $response->meta->total);
    }


    /** @group database_transaction */
    public function testGetAssetsStoreDatetimes(RestTester $I)
    {
        $I->wantTo("Test GetAssets datetimes");

        $this->app['configs']['retailer.store.default_timezone'] = 'America/Los_Angeles';

        $userId = 337;
        $userName = 'testuser337';

        $this->insertFixtureGroup($I, 'user_assets');
        $this->insertFixtureGroup($I, 'user_assets_timezone');
        $I->updateInDatabase('wp_users', ['group' => 5], ['ID' => 1]);

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, 'v2/assets?page[number]=0&page[size]=100');
        $response = json_decode(json_encode($response), true);

        $keyedAssets = [];
        foreach ($response['data'] as $asset) {
            $keyedAssets[$asset['id']] = $asset;
        }

        $I->assertEquals($keyedAssets['7']['attributes']['virtual_fields']['startDatetime'], null);
        $I->assertEquals($keyedAssets['7']['attributes']['virtual_fields']['endDatetime'], null);

        $I->assertEquals($keyedAssets['8']['attributes']['virtual_fields']['startDatetime'], '2010-04-24 09:00:00');
        $I->assertEquals($keyedAssets['8']['attributes']['virtual_fields']['endDatetime'], null);

        $I->assertEquals($keyedAssets['9']['attributes']['virtual_fields']['startDatetime'], null);
        $I->assertEquals($keyedAssets['9']['attributes']['virtual_fields']['endDatetime'], '2059-04-27 09:00:00');

        $I->assertEquals($keyedAssets['1']['attributes']['virtual_fields']['startDatetime'], '2010-04-27 09:00:00');
        $I->assertEquals($keyedAssets['1']['attributes']['virtual_fields']['endDatetime'], '2059-04-27 09:00:00');
    }

    /** @group database_transaction */
    public function testFlagForStorefrontAssets(RestTester $I)
    {
        $this->addAssetsOfBenUploader($I);

        // Disable the storefont:
        $this->app['configs']['retailer.modular_connect.storefront.is_enabled'] = false;
        $response = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100');

        // There are 2 {REPPAGE}s (storefront pages) in this data set:
        $repPage = 0;
        foreach ($response->data as $v) {
            if ($v->attributes->virtual_fields->hasUnsupportedDestinationUrl) {
                ++$repPage;
            }
        }

        $I->assertEquals(2, $repPage);

        // Enable the storefront:
        $this->app['configs']['retailer.modular_connect.storefront.is_enabled'] = true;
        $response2 = $I->doDirectGet($I, 'v2/assets?include=rep&page[number]=0&page[size]=100');

        $repPage = 0;
        foreach ($response2->data as $v) {
            if ($v->attributes->virtual_fields->hasUnsupportedDestinationUrl) {
                ++$repPage;
            }
        }

        $I->assertEquals(0, $repPage);
    }

    /**
     * @group refresh_database
     */
    public function testGetAssetsFromWordpressMobileApi(RestTester $I)
    {
        $I->wantTo("Test GetAssets from wordpress api for mobile");

        $url = '/api/assets/all';

        $this->verifyAssetsFromWordpressApi($I, $url);
    }

    /**
     * @group refresh_database
     */
    public function testGetAssetsFromWordpressPlatformApi(RestTester $I)
    {
        $I->wantTo("Test GetAssets from wordpress api for backoffice");

        $url = '/sfadmin/assets.php?page=1&elemPerPage=30&draft=false';

        $this->verifyAssetsFromWordpressApi($I, $url);
    }

    /** @group database_transaction */
    public function testAssetStartDateGreaterThanEndDateIsInvalidOnCreate(RestTester $I)
    {
        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['start_date'] = '2031-01-13 01:00:00';
        $attributes['virtual_fields']['end_date'] = '2031-01-12 01:00:00';

        $I->doDirectPost($I, "v2/assets", [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testAssetStartDateGreaterThanEndDateIsInvalidOnUpdate(RestTester $I)
    {
        $this->addCorporateUserAndAssets($I);
        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['start_date'] = '2031-01-13 01:00:00';
        $attributes['virtual_fields']['end_date'] = '2031-01-12 01:00:00';

        $id = 10;
        $I->doDirectPatch($I, "v2/assets/$id", [
            'data' => [
                'type' => 'asset',
                "id"  => $id,
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testAssetStartDateSameAsEndDateIsInvalidOnCreate(RestTester $I)
    {
        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['start_date'] = '2031-01-13 01:00:00';
        $attributes['virtual_fields']['end_date'] = '2031-01-13 01:00:00';

        $I->doDirectPost($I, "v2/assets", [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testAssetStartDateHasOffset30mFromEndDateIsValidOnCreate(RestTester $I)
    {
        $this->addCorporateUserAndAssets($I);
        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['start_date'] = '2031-01-13 01:00:00';
        $attributes['virtual_fields']['end_date'] = '2031-01-13 01:30:00';

        $I->doDirectPost($I, "v2/assets", [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(201);
    }

    /** @group database_transaction */
    public function testAssetIsValidWhenNoEndDateOnCreate(RestTester $I)
    {
        $this->addCorporateUserAndAssets($I);
        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['start_date'] = '2031-01-13 01:00:00';
        unset($attributes['virtual_fields']['end_date']);

        $I->doDirectPost($I, "v2/assets", [
            'data' => [
                'type' => 'asset',
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(201);
    }

    /** @group database_transaction */
    public function testAssetIsValidWhenNoEndDateOnUpdate(RestTester $I)
    {
        $this->addCorporateUserAndAssets($I);
        $attributes = $this->getAssetCreationData();
        $attributes['virtual_fields']['start_date'] = '2031-01-13 01:00:00';
        unset($attributes['virtual_fields']['end_date']);

        $id = 10;
        $I->doDirectPatch($I, "v2/assets/$id", [
            'data' => [
                'type' => 'asset',
                "id"  => $id,
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(201);
    }

    /** @group database_transaction */
    public function testAssetIsEditableWhenStartDateLaterThanAttachedCorpTaskDate(RestTester $I)
    {
        $this->addCorporateUserTaskAndAssets($I);
        $attributes = $this->getAssetCreationData();
        // Task has a start date of: 2019-04-18 09:00:00
        $attributes['virtual_fields']['start_date'] = '2022-06-27T09:00';

        $id = 1;
        $I->doDirectPatch($I, "v2/assets/$id", [
            'data' => [
                'type' => 'asset',
                "id"  => $id,
                'attributes' => $attributes
            ]
        ]);

        $I->assertStatusCode(201);
    }

    /** @group database_transaction */
    public function testGetAssetsWithValidCorpTask(RestTester $I)
    {
        $I->wantTo("Test that only non-deleted corporate task are returned as part of the assets relations and includes");

        $this->addCorporateUserTaskAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=corporate-task&page[number]=0&page[size]=5');

        $this->debug($response);

        $I->assertEquals(2, count($response->included));
        $I->assertFalse($response->included[0]->attributes->virtual_fields->is_resolved);
        $I->assertTrue($response->included[1]->attributes->virtual_fields->is_resolved);
    }

    /** @group database_transaction */
    public function testGetAssetsWithValidAndResolvedCorpTask(RestTester $I)
    {
        $I->wantTo("Test that a corporate task has resolved task assignments");

        $this->addCorporateUserTaskAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=corporate-task&page[number]=0&page[size]=5');

        $this->debug($response);

        // A corporate task is resolved when it is completed by all reps assigned to it.
        // That is, Task created Count must be the same as sum of dismissed and resolved count
        $I->assertEquals(1, $response->included[1]->attributes->virtual_fields->tasks_created_count);
        $I->assertEquals(1, $response->included[1]->attributes->virtual_fields->dismiss_count);
        $I->assertEquals(0, $response->included[1]->attributes->virtual_fields->resolved_count);
        $I->assertTrue($response->included[1]->attributes->virtual_fields->is_resolved);
    }

    /** @group database_transaction */
    public function testGetAssetsWithValidAndUnResolvedCorpTask(RestTester $I)
    {
        $I->wantTo("Test that a corporate task has unresolved task assignments");

        $this->addCorporateUserTaskAndAssets($I);

        $response = $I->doDirectGet($I, 'v2/assets?include=corporate-task&page[number]=0&page[size]=5');

        $this->debug($response);

        // A corporate task is unresolved when it is yet to be completed by all reps assigned to it.
        // That is, Task created Count must not be the same as sum of dismissed and resolved count
        $I->assertEquals(3, $response->included[0]->attributes->virtual_fields->tasks_created_count);
        $I->assertEquals(1, $response->included[0]->attributes->virtual_fields->dismiss_count);
        $I->assertEquals(1, $response->included[0]->attributes->virtual_fields->resolved_count);
        $I->assertFalse($response->included[0]->attributes->virtual_fields->is_resolved);
    }

    private function addCorporateUserAndAssets(RestTester $I)
    {
        $this->addCorporateUser($I);
        $this->addAssets($I);
    }

    private function addUserAndAssets(RestTester $I)
    {
        $this->addUser($I);
        $this->addAssets($I);
        $I->updateInDatabase('wp_users', ['group' => 5], ['ID' => 1]);
    }

    private function addCorporateUser(RestTester $I)
    {
        $this->addUser($I, self::CORPORATE_USER_ID, self::CORPORATE_USER_NAME);
    }

    private function addUser(RestTester $I, $userId = self::USER_ID, $userName = self::USER_NAME)
    {
        $this->setTokenInRequestHeader($I, $userId, $userName);
    }

    private function addAssets(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'corporate_user_assets');
    }

    private function addAssetsOfBenUploader(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'assets_of_ben_uploader');
    }

    private function verifyAssetsFromWordpressApi(RestTester $I, $url)
    {
        $I->applyAuthorization($I, '336', 'testuser336');
        $this->insertFixtureGroup($I, 'user_assets');

        $result = $I->doGetWP($I, $url);
        $assets = $result->assets;

        $I->assertEquals(7, count($assets));
    }

    private function getAssetCreationData()
    {
        $attributes = [
            'post_author'    => self::CORPORATE_USER_ID,
            'post_title'     => 'Post title',
            'target_channel' => 'all',
            'virtual_fields' => [
                'blurb'                => 'sf_post_description in meta',
                'label'                => 'testtest',
                'asset_details'        => 'any details',
                'email_subject'        => 'sf_email_subject',
                'asset_suggested_copy' => 'sf_asset_suggested_copy',
                'asset_url_option'     => '{REPPAGE}',
                'destination_url'      => self::DESTINATION_URL,
                'imageUrl'             => 'any_image_link',
                'asset_footer'         => 'sf_asset_footer',

                "start_date" => "2020-01-12 01:00:00",
                "end_date"   => "2031-01-12 01:00:00",
            ]
        ];

        return $attributes;
    }

    private function addCorporateUserTaskAndAssets(RestTester $I)
    {
        $this->addCorporateUser($I);
        $this->addTasksAndAssets($I);
    }

    private function addTasksAndAssets(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'corporate_user_assets_tasks');
    }
}
