<?php

declare(strict_types=1);

namespace SF\rest\Analytics;

use SF\RestTester;
use SF\rest\BaseRest;

class AnalyticsEventsCest extends BaseRest
{
    /** @group database_transaction */
    public function testHttpPOSTAnalyticsEventBasic(RestTester $I)
    {

        $this->insertFixtureGroup($I, 'user');

        $userId = 115;
        $userName = 'user_login';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        // Set headers for enrichment testing
        $I->haveHttpHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        $I->haveHttpHeader('Referer', 'https://test.salesfloor.net/messages');
        $I->haveHttpHeader('sf-app-version', '2.1.0');

        $response = $I->doDirectPost($I, '/v1/analytics-events', [
            "data" => [
                "type" => "analytics-events",
                "attributes" => [
                    "event" => "clicked_ai_assistant_button",
                    "event_data" => [
                        "feature" => "ai-outreach"
                    ]
                ]
            ]
        ]);

        $I->assertTrue(204 == $I->grabResponseStatusCode());

        $I->seeInDatabase('sf_analytics_events', [
            'event' => 'clicked_ai_assistant_button',
        ]);
    }

    /** @group database_transaction */
    public function testHttpPOSTAnalyticsEventWithComplexEventData(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'user');

        $userId = 115;
        $userName = 'user_login';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPost($I, '/v1/analytics-events', [
            "data" => [
                "type" => "analytics-events",
                "attributes" => [
                    "event" => "message_sent",
                    "event_data" => [
                        "message_type" => "ai_generated",
                        "character_count" => 150,
                        "customer_id" => 12345,
                        "metadata" => [
                            "prompt_used" => true,
                            "template_id" => "template_001"
                        ]
                    ]
                ]
            ]
        ]);

        $I->assertTrue(204 == $I->grabResponseStatusCode());

        // Verify complex event data was properly stored
        $record = $I->grabFromDatabase('sf_analytics_events', 'event_data', [
            'event' => 'message_sent',
            'user_id' => $userId
        ]);

        $eventData = json_decode($record, true);
        $I->assertEquals('ai_generated', $eventData['message_type']);
        $I->assertEquals(150, $eventData['character_count']);
        $I->assertEquals(12345, $eventData['customer_id']);
        $I->assertEquals(true, $eventData['metadata']['prompt_used']);
        $I->assertEquals('template_001', $eventData['metadata']['template_id']);
    }



    /** @group database_transaction */
    public function testHttpPOSTAnalyticsEventMissingEventName(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'user');

        $userId = 115;
        $userName = 'user_login';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPost($I, '/v1/analytics-events', [
            "data" => [
                "type" => "analytics-events",
                "attributes" => [
                    "event_data" => [
                        "some" => "data"
                    ]
                ]
            ]
        ]);

        // Should still return 204 No Content even with invalid data
        $I->assertTrue(204 == $I->grabResponseStatusCode());

        // Verify no data was saved due to missing event name
        $I->dontSeeInDatabase('sf_analytics_events', [
            'user_id' => $userId,
            'event' => null
        ]);
    }


    /** @group database_transaction */
    public function testHttpPOSTAnalyticsEventIpAddressCapture(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'user');

        $userId = 115;
        $userName = 'user_login';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPost($I, '/v1/analytics-events', [
            "data" => [
                "type" => "analytics-events",
                "attributes" => [
                    "event" => "ip_test_event"
                ]
            ]
        ]);

        $I->assertTrue(204 == $I->grabResponseStatusCode());

        // Verify IP address was captured
        $record = $I->grabFromDatabase('sf_analytics_events', 'ip_address', [
            'event' => 'ip_test_event',
            'user_id' => $userId
        ]);
        $I->assertNotEmpty($record);
    }
}
