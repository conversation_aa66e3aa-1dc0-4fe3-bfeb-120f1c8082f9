<?php

declare(strict_types=1);

namespace SF\rest\Analytics;

use Codeception\Util\Fixtures;

class Fixture
// phpcs:ignoreFile
{
    public function analyticsEvents()
    {
        Fixtures::add('user', [
            'wp_users' => [
                [
                    'ID'                  => 115,
                    'user_login'          => 'user_login',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'user_login',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'user_login',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ]
            ],
            'wp_usermeta' => [
                [
                    'user_id'    => 115,
                    'meta_key'   => 'first_name',
                    'meta_value' => 'Test'
                ],
                [
                    'user_id'    => 115,
                    'meta_key'   => 'last_name',
                    'meta_value' => 'User'
                ]
            ]
        ]);

        Fixtures::add('store', [
            'wp_posts' => [
                [
                    'ID'             => 1003,
                    'post_author'    => 1,
                    'post_date'      => '2001-01-01 00:00:00',
                    'post_date_gmt'  => '2001-01-01 00:00:00',
                    'post_content'   => '',
                    'post_title'     => 'Test Store',
                    'post_excerpt'   => '',
                    'post_status'    => 'publish',
                    'comment_status' => 'closed',
                    'ping_status'    => 'closed',
                    'post_password'  => '',
                    'post_name'      => 'test-store',
                    'to_ping'        => '',
                    'pinged'         => '',
                    'post_modified'  => '2001-01-01 00:00:00',
                    'post_modified_gmt' => '2001-01-01 00:00:00',
                    'post_content_filtered' => '',
                    'post_parent'    => 0,
                    'guid'           => 'http://localhost/?post_type=sf-store&#038;p=1003',
                    'menu_order'     => 0,
                    'post_type'      => 'sf-store',
                    'post_mime_type' => '',
                    'comment_count'  => 0
                ]
            ]
        ]);
    }
}
