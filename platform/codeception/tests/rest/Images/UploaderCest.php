<?php

namespace SF\rest\Images;

use SF\RestTester;
use Aws\S3\S3Client;
use SF\rest\BaseRest;
use Codeception\Util\Stub;
use Psr\Log\LoggerInterface;
use Salesfloor\Models\Moderation;
use Salesfloor\Services\Image\Image;
use Salesfloor\Services\CDN\CloudinaryImage;
use Symfony\Component\HttpFoundation\HeaderBag;
use Salesfloor\Services\Moderation\ImageModeration;
use Salesfloor\Services\Moderation\Moderator\HiveModerator;
use Salesfloor\Services\Moderation\Moderator\ModeratorFactory;
use Salesfloor\Services\Moderation\Moderator\ModeratorRequest;

class UploaderCest extends BaseRest
{
    /** @group database_transaction */
    public function testUploadOneImage(RestTester $I)
    {
        $I->wantTo('Test uploading one image.');

        $this->app['service.image'] = Stub::construct(
            Image::class,
            [
                $this->app['configs'],
                Stub::makeEmpty(LoggerInterface::class),
                Stub::make(S3Client::class, [
                    'upload' => function () use ($I) {
                        return new Class ($I) {
                            /** @var RestTester */
                            protected $I;

                            public function __construct(RestTester $I)
                            {
                                $this->I = $I;
                            }

                            public function get(string $objectUrl)
                            {
                                $this->I->assertEquals('ObjectURL', $objectUrl);
                                return 'https://www.salesfloor.net';
                            }
                        };
                    },
                    'getBucket' => function (): string {
                        return 'bucketName';
                    },
                ]),
                Stub::make(CloudinaryImage::class, [
                    'getProxyUrl' => function (string $objectUrl) use ($I) {
                        $I->assertEquals('https://www.salesfloor.net', $objectUrl);
                        return 'https://cdn.salesfloor.net';
                    },
                ]),
                $this->app['service.moderation.image'],
            ]
        );

        $results = $I->doDirectPost($I, 'images/upload', [
            'data' => [
                'type' => 'image_upload',
                'attributes' => [
                  'origin' => 'compose',
                  'source' => file_get_contents(__DIR__ . '/../../fixtures/images/imageSvgInBase64.txt'),
                ],
              ],
        ]);
        $this->debug($results);
        $attributes = $results->data->attributes;
        $I->assertEquals('https://www.salesfloor.net', $attributes->destination);
        $I->assertEquals('https://cdn.salesfloor.net', $attributes->cdn);
    }

    /**
     * Test internal upload and moderation
     * @param \SF\RestTester $I
     * @return void
     * @group database_transaction
     */
    public function testInternalUploadRoute(RestTester $I)
    {
        // Enable image moderation
        $this->app['configs']['retailer.moderation.image.is_enabled'] = true;

        $this->app['service.moderation.image'] = Stub::construct(
            ImageModeration::class,
            [
                (new ModeratorFactory())->build(
                    $this->app['logger'],
                    ModeratorFactory::MODERATOR_HIVE,
                    new ModeratorRequest(
                        $this->app['guzzle.http.client.factory'],
                        HiveModerator::HIVE_API_URL,
                        new HeaderBag([
                            'Content-Type' => 'application/json',
                            'Authorization' => 'token ' . $this->app['configs']['hive.image.api_key'],
                        ])
                    ),
                    $this->app['configs']['hive.image.score.threshold'],
                    $this->app['configs']['hive.image.custom.threshold'],
                    $this->app['service.helpers.stringSplitter'],
                ),
                $this->app['moderations.manager'],
            ],
            [
                'moderate' => function () {
                    $model = new Moderation();
                    $model->is_moderator_flagged = true;
                    return $model;
                }
            ]
        );

        $this->app['service.image'] = Stub::construct(
            Image::class,
            [
                $this->app['configs'],
                Stub::makeEmpty(LoggerInterface::class),
                Stub::make(S3Client::class, [
                    'upload' => function () use ($I) {
                        return new Class ($I) {
                            /** @var RestTester */
                            protected $I;

                            public function __construct(RestTester $I)
                            {
                                $this->I = $I;
                            }

                            public function get(string $objectUrl)
                            {
                                $this->I->assertEquals('ObjectURL', $objectUrl);
                                return 'https://www.salesfloor.net';
                            }
                        };
                    },
                    'getBucket' => function (): string {
                        return 'bucketName';
                    },
                ]),
                Stub::make(CloudinaryImage::class, [
                    'getProxyUrl' => function (string $objectUrl) use ($I) {
                        $I->assertEquals('https://www.salesfloor.net', $objectUrl);
                        return 'https://cdn.salesfloor.net';
                    },
                ]),
                $this->app['service.moderation.image']
            ]
        );

        $results = $I->doDirectPost($I, 'internal/images/upload', [
            'data' => [
                'type' => 'image_upload',
                'attributes' => [
                  'origin' => 'chat',
                  'user_id' => 1,
                  'source' => file_get_contents(__DIR__ . '/../../fixtures/images/imageSvgInBase64.txt'),
                ],
              ],
        ]);
        $this->debug($results);
        $attributes = $results->data->attributes;
        $I->assertEquals('https://www.salesfloor.net', $attributes->destination);
        $I->assertEquals('https://cdn.salesfloor.net', $attributes->cdn);
        $I->assertEquals(true, $attributes->is_moderator_flagged);
    }
}
