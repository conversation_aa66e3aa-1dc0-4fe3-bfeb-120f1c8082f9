<?php

declare(strict_types=1);

namespace SF\rest\JsonApi\SidePatching;

use SF\RestTester;
use SF\rest\BaseRest;
use Codeception\Util\Fixtures;

class CorporateTaskCest extends BaseRest
{
    /** @group database_transaction */
    public function testUpdateCorporateTaskWithProductAndSpecialtyAndStore(RestTester $I)
    {
        $userId = 7;
        $userName = 'user6';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $I->fakeCorporateTaskQueueService();
        $response = $I->doDirectPost(
            $I,
            '/v2/corporate-tasks',
            Fixtures::get('corporate-task-with-product-and-specialty-body-1')
        );

        $corporateTaskSpecialties = array_filter(
            $response->included,
            function ($included) {
                return $included->type === 'corporate-task-specialty';
            }
        );
        $I->assertCount(2, $corporateTaskSpecialties);
        foreach ($corporateTaskSpecialties as $speciality) {
            $I->seeInDatabase(
                'sf_corporate_task_specialties',
                [
                    'corporate_task_id' => $speciality->attributes->corporate_task_id,
                    'specialty_id' => $speciality->attributes->specialty_id,
                ]
            );
        }

        $corporateTaskId = (int)$speciality->attributes->corporate_task_id;
        $requestData = Fixtures::get('corporate-task-with-product-body-2');
        $requestData['data']['id'] = $corporateTaskId;
        $response = $I->doDirectPatch($I, "/v2/corporate-tasks/$corporateTaskId", $requestData);

        foreach ($corporateTaskSpecialties as $speciality) {
            $I->cantSeeInDatabase(
                'sf_corporate_task_specialties',
                [
                    'corporate_task_id' => $speciality->attributes->corporate_task_id,
                    'specialty_id' => $speciality->attributes->specialty_id,
                ]
            );
        }
    }

    /** @group database_transaction */
    public function testCreateAndUpdateCorporateTaskWithMultiLang(RestTester $I)
    {
        $userId = 7;

        $this->setTokenInRequestHeader($I, $userId);

        $I->fakeCorporateTaskQueueService();

        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];

        $response = $I->doDirectPost(
            $I,
            '/v2/corporate-tasks',
            Fixtures::get('corporate-task-with-product-and-specialty-body-post-multi-lang')
        );

        $corporateTaskId = $response->data->id;
        $I->seeInDatabase(
            'sf_corporate_tasks',
            [
                'id' => $corporateTaskId,
                'title' => 'title',
                'detail' => 'detail'
            ]
        );
        $I->seeInDatabase(
            'sf_corporate_tasks_i18n',
            [
                'locale' => 'fr_CA',
                'corporate_task_id' => $corporateTaskId,
                'title' => 'titre',
                'detail' => 'detailee'
            ]
        );

        $requestData = Fixtures::get('corporate-task-with-product-body-patch-multi-lang');
        $this->setIdentities($requestData, $response);
        $response = $I->doDirectPatch($I, "/v2/corporate-tasks/$corporateTaskId", $requestData);

        $I->seeInDatabase(
            'sf_corporate_tasks',
            [
                'id' => $corporateTaskId,
                'title' => 'title updated',
                'detail' => 'detail updated'
            ]
        );
        $I->seeInDatabase(
            'sf_corporate_tasks_i18n',
            [
                'locale' => 'fr_CA',
                'corporate_task_id' => $corporateTaskId,
                'title' => 'titre mis a jour',
                'detail' => 'detailee mis a jour'
            ]
        );
    }

    protected function setIdentities(array &$requestData, $response)
    {
        $requestData['data']['id'] = $response->data->id;
        foreach ($response->data->relationships->{'corporate-task-product'}->data as $i => $data) {
            $requestData['data']['relationships']['corporate-task-product']['data'][$i]['id'] = $data->id;
            $requestData['included'][$i]['id'] = $data->id;
        }
    }
}
