<?php

namespace SF\rest\Customer;

use Carbon\Carbon;
use SF\RestTester;
use SF\rest\BaseRest;
use Codeception\Util\Fixtures;
use Salesfloor\Models\Customer;
use Salesfloor\API\Managers\Client\Customers\Legacy;
use Salesfloor\Services\Salesfloor\Customer as CustomerService;

class CustomerApiCest extends BaseRest
{
    /** @group database_transaction */
    public function testCoreCustomerApiGet(RestTester $I)
    {
        $I->wantTo("api customer - get (HTTP)");

        $results = $I->doDirectGet($I, 'customers');

        $I->assertEquals(2, count($results));

        $this->validateArray(Fixtures::get('customer1'), $results[0]);
        $this->validateArray(Fixtures::get('customer2'), $results[1]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGet(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST)");

        $results = $I->doDirectGet($I, 'customers');

        $I->assertEquals(2, count($results));

        $this->validateArray(Fixtures::get('customer1'), $results[0]);
        $this->validateArray(Fixtures::get('customer2'), $results[1]);

        // Duplicate seems to be used to test a require_once
        $results = $I->doDirectGet($I, 'customers');

        $I->assertEquals(2, count($results));

        $this->validateArray(Fixtures::get('customer1'), $results[0]);
        $this->validateArray(Fixtures::get('customer2'), $results[1]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetFilterByMainEmail(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST), filter by email in sf_customer ");
        $this->insertFixtureGroup($I, 'CustomerMeta');

        $results = $I->doDirectGet($I, 'customers?filter[email]=<EMAIL>');

        $I->assertEquals(1, count($results));

        $this->validateArray(Fixtures::get('customer1'), $results[0]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetFilterByCustomerMetaEmailFailed(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST), filter by email in sf_customer_meta as alternative email  ");
        $this->insertFixtureGroup($I, 'CustomerMeta');

        $results = $I->doDirectGet($I, 'customers?filter[email]=<EMAIL>');

        $I->assertEquals(0, count($results));
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetFilterByCustomerMetaEmail(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST), filter by email in sf_customer_meta as alternative email  ");
        $this->insertFixtureGroup($I, 'CustomerMeta');

        $results = $I->doDirectGet($I, 'customers?filter[any_email]=<EMAIL>');

        $I->assertEquals(1, count($results));

        $this->validateArray(Fixtures::get('customer1'), $results[0]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetWithEmail(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST) - mandatory_fields:email");

        $this->insertFixtures($I, [
            'customer-no-email-or-phone' => 'sf_customer',
            'customer-no-email' => 'sf_customer',
            'customer-no-phone' => 'sf_customer',
        ]);

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=email');

        $I->assertEquals(3, count($results));

        // The query is sorting by CONCAT(COALESCE(m.first_name, ''), COALESCE(m.last_name, ''), COALESCE(m.email, ''), so we need to adjust
        // the orders.
        $this->validateArray(Fixtures::get('customer1'), $results[0]);
        $this->validateArray(Fixtures::get('customer2'), $results[1]);
        $this->validateArray(Fixtures::get('customer-no-phone'), $results[2]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetWithEmailES(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST) - mandatory_fields:email and ES");

        $this->insertFixtures($I, [
            'customer-no-email-or-phone' => 'sf_customer',
            'customer-no-email' => 'sf_customer',
            'customer-no-phone' => 'sf_customer',
        ]);


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=email&filter[search]=Jo');

        $I->assertEquals(3, count($results));

        // The results are sorted by the ES, re-sort it by ID.
        // It ensures that we can assert them properly later.
        usort($results, function ($left, $right) {
            return $left->ID - $right->ID;
        });

        $customer1 = Fixtures::get('customer1');
        $customer2 = Fixtures::get('customer2');
        $customerNoPhone = Fixtures::get('customer-no-phone');

        $this->validateArray($customer1, $results[0]);
        $this->validateArray($customer2, $results[1]);
        $this->validateArray($customerNoPhone, $results[2]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetWithPhone(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST) - mandatory_fields:phone");

        $this->insertFixtures($I, [
            'customer-no-email-or-phone' => 'sf_customer',
            'customer-no-email' => 'sf_customer',
            'customer-no-phone' => 'sf_customer',
        ]);

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=phone');

        $I->assertEquals(2, count($results));

        // The query is sorting by CONCAT(COALESCE(m.first_name, ''), COALESCE(m.last_name, ''), COALESCE(m.email, ''), COALESCE(m.phone, ''))
        // So we need to adjust the order.
        $this->validateArray(Fixtures::get('customer-no-email'), $results[0]);
        $this->validateArray(Fixtures::get('customer2'), $results[1]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetWithPhoneES(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST) - mandatory_fields:phone and ES");

        $this->insertFixtures($I, [
            'customer-no-email-or-phone' => 'sf_customer',
            'customer-no-email' => 'sf_customer',
            'customer-no-phone' => 'sf_customer',
        ]);


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=phone&filter[search]=Jo');

        $I->assertEquals(2, count($results));

        $this->validateArray(Fixtures::get('customer2'), $results[0]);
        $this->validateArray(Fixtures::get('customer-no-email'), $results[1]);
    }

    /** @group database_transaction */
    public function testCoreCustomerDirectGetWithEmailAndPhone(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST) - mandatory_fields:email,phone");

        $this->insertFixtures($I, [
            'customer-no-email-or-phone' => 'sf_customer',
            'customer-no-email' => 'sf_customer',
            'customer-no-phone' => 'sf_customer',
        ]);


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=email,phone');
        $I->assertEquals(1, count($results));

        $this->validateArray(Fixtures::get('customer2'), $results[0]);
    }

    /** @group database_transaction */
    public function testCoreCustomerTagsDirectGetCountSubscribersByTagsWithEmail(RestTester $I)
    {
        $I->wantTo("api customer-countbytags - get (REQUEST) - mandatory_fields:email");

        $this->insertFixtures($I, [
            'customer-subscriber' => 'sf_customer',
            'customer-subscriber-no-email' => 'sf_customer',
        ]);


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $customerService = new CustomerService(
            $this->app['configs'],
            $this->app['logger'],
            $this->app['repositories.mysql'],
            $manager,
            $this->app['customer_field_history.manager'],
            $this->app['reps.manager'],
            $this->app['elasticsearch.contact.query']
        );
        unset($this->app['service.customer']);
        $this->app['service.customer'] = $customerService;

        $results = $I->doDirectGet(
            $I,
            'customers-countbytags?filter[customers]=subscribers&filter[mandatory_fields]=email'
        );

        $I->assertEquals(1, $results);
    }

    /** @group database_transaction */
    public function testGetCustomersFilteredBySmsMarketingFlag(RestTester $I)
    {
        $I->wantTo("api customer - get (REQUEST), filter by SMS marketing flag in sf_customer ");
        $this->insertFixtureGroup($I, 'CustomerSmsMarketing');

        $results = $I->doDirectGet($I, 'customers?filter[sms_marketing_subscription_flag]=1');

        $I->assertEquals(1, count($results));

        $I->assertEquals(1, $results[0]->sms_marketing_subscription_flag);
    }

    /** @group database_transaction */
    public function testUpdateCustomerWithSignature(RestTester $I)
    {
        $I->wantTo("api update customer - put (REQUEST), update customer with signature");

        $this->insertFixtureGroup($I, 'customer_signature_update');

        $customerId = 1001;
        $signature  = 'test-what-ever-value-it-is';

        $customer = [
            'email'                           => "<EMAIL>",
            'first_name'                      => "shawn",
            'last_name'                       => "jiang",
            'phone'                           => "+15145610744",
            'sms_marketing_subscription_flag' => "1",
            'subcribtion_flag'                => "1",
            'signature'                       => $signature,
        ];


        $response = $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->canSeeInDatabase('sf_customer_signature', ['customer_id' => $customerId, 'signature' => $signature, 'is_latest' => 1]);
        $I->canSeeInDatabase('sf_customer_signature', ['customer_id' => $customerId, 'signature' => 'what-ever-the-value', 'is_latest' => 0]);
        $I->canSeeInDatabase('sf_customer_signature', ['customer_id' => $customerId, 'signature' => 'what-ever-the-value-old-old', 'is_latest' => 0]);
    }

    /** @group database_transaction */
    public function testWhenUpdateCustomerUnsubscribeRemove(RestTester $I, $scenario)
    {
        $I->wantTo("api update customer - remove from Unsubscribe list When UpdateCustomer with signature and subscribe flag");

        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;
        $this->insertFixtureGroup($I, 'customer_signature_update');
        $this->insertFixtureGroup($I, 'customer_block_list');

        $this->app['configs']['retailers.short'] = 'elguntors';
        $onBehalfOf                              = 'sf-elguntors-dev';

        $email = '<EMAIL>';
        $customerId = 1001;
        $customer   = [
            'email'                           => $email,
            'first_name'                      => "shawn",
            'last_name'                       => "jiang",
            'phone'                           => "+15145610744",
            'sms_marketing_subscription_flag' => "1",
            'subcribtion_flag'                => "1",
            'signature'                       => 'test-what-ever-value-it-is',
        ];



        $sendgridProvider = $this->app['service.sendgrid.provider'];
        $sendgridProvider->addEmailsToGlobalUnsubscribes([$email], $onBehalfOf);
        $globalUnsubscribes = $this->getGlobalUnsubscribes($sendgridProvider, $onBehalfOf);
        $I->assertContains($email, $globalUnsubscribes);

        $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->cantSeeInDatabase('sf_email_block_list', ['email' => '<EMAIL>',]);
        $I->cantSeeInDatabase('sf_sms_block_list', ['phone_number' => '+15145610744',]);

        // $sendgridProvider->removeEmailsFromGlobalUnsubscribes($customer['email'], $onBehalfOf);
        // $I->assertNotContains($customer['email'], $this->getGlobalUnsubscribes($sendgridProvider, $onBehalfOf));
    }

    /**
     * @param RestTester $I
     * @param $scenario
     * @return void
     * @throws \Exception
     *
     * @group database_transaction
     */
    public function testWhenUpdateCustomerUnsubscribeKeep(RestTester $I, $scenario)
    {
        $I->wantTo("api update customer -Keep unsubscribe list When UpdateCustomer with signature and subscribe flag");

        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;
        $this->insertFixtureGroup($I, 'customer_signature_update');
        $this->insertFixtureGroup($I, 'customer_block_list');

        $this->app['configs']['retailers.short'] = 'elguntors';
        $onBehalfOf                              = 'sf-elguntors-dev';
        $email = '<EMAIL>';

        $customerId = 1001;
        $customer   = [
            'email'                           => $email,
            'first_name'                      => "shawn",
            'last_name'                       => "jiang",
            'phone'                           => "+15145610744",
            'sms_marketing_subscription_flag' => "0",
            'subcribtion_flag'                => "0",
            'signature'                       => 'test-what-ever-value-it-is',
        ];



        $sendgridProvider = $this->app['service.sendgrid.provider'];
        $sendgridProvider->addEmailsToGlobalUnsubscribes([$email], $onBehalfOf);
        $globalUnsubscribes = $this->getGlobalUnsubscribes($sendgridProvider, $onBehalfOf);
        $I->assertContains($customer['email'], $globalUnsubscribes);

        $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->canSeeInDatabase('sf_email_block_list', ['email' => '<EMAIL>',]);
        $I->canSeeInDatabase('sf_sms_block_list', ['phone_number' => '+15145610744',]);
        $I->assertContains($customer['email'], $this->getGlobalUnsubscribes($sendgridProvider, $onBehalfOf));
    }

    /** @group database_transaction */
    public function testHistoryLogWhenUpdateCustomerWithSubscriptionChange(RestTester $I)
    {
        $I->wantTo("api update customer - put (REQUEST), update customer with Subscription flag Change");

        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;
        $this->insertFixtureGroup($I, 'customer_signature_update');

        $customerId = 1001;

        $customer = [
            'email'                           => "<EMAIL>",
            'first_name'                      => "shawn",
            'last_name'                       => "jiang",
            'phone'                           => "+15145610744",
            'sms_marketing_subscription_flag' => "1",
            'subcribtion_flag'                => "1",
            'signature'                       => 'test-what-ever-value-it-is',
        ];



        $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => '1', 'old_value' => '0', 'field_name' => 'subcribtion_flag']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['email'], 'old_value' => '<EMAIL>', 'field_name' => 'email']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => '1', 'old_value' => '0', 'field_name' => 'sms_marketing_subscription_flag']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['phone'], 'old_value' => '+15145555555', 'field_name' => 'phone']);
    }

    /** @group database_transaction */
    public function testHistoryLogWhenUpdateCustomerWithoutSubscriptionChange(RestTester $I)
    {
        $I->wantTo("api update customer - put (REQUEST), update customer, we should record history log even subscription flag not changed");

        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;
        $this->insertFixtureGroup($I, 'customer_signature_update');

        $customerId = 1001;

        $customer = [
            'email'                           => "<EMAIL>",
            'first_name'                      => "shawn",
            'last_name'                       => "jiang",
            'phone'                           => "+15145610744",
            'sms_marketing_subscription_flag' => "0",
            'subcribtion_flag'                => "0",
            'signature'                       => 'test-what-ever-value-it-is',
        ];

        $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => '0', 'old_value' => '0', 'field_name' => 'subcribtion_flag']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['email'], 'old_value' => '<EMAIL>', 'field_name' => 'email']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => '0', 'old_value' => '0', 'field_name' => 'sms_marketing_subscription_flag']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['phone'], 'old_value' => '+15145555555', 'field_name' => 'phone']);
    }

    /** @group database_transaction */
    public function testHistoryLogWhenUpdateCustomerMeta(RestTester $I)
    {
        $I->wantTo("api update customer - put (REQUEST), update customer, we should record history log even subscription flag not changed");

        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;
        $this->insertFixtureGroup($I, 'customer_signature_update');

        $customerId = 1001;

        $customer = [
            'email'                           => "<EMAIL>",
            'first_name'                      => "shawn",
            'last_name'                       => "jiang",
            'phone'                           => "+15145610744",
            'sms_marketing_subscription_flag' => "0",
            'subcribtion_flag'                => "0",
            'signature'                       => 'test-what-ever-value-it-is',
            'additionalEmails'                => [
                [
                    'email'    => '<EMAIL>',
                    'label'    => 'Home',
                    'position' => '0'
                ],
                [
                    'email'    => '<EMAIL>',
                    'label'    => 'Work',
                    'position' => '1'
                ],
            ],
            'additionalPhones'                => [
                [
                    'phone'    => '+15147677858',
                    'label'    => 'Home',
                    'position' => '0'
                ],
                [
                    'phone'    => '+15147677859',
                    'label'    => 'Work',
                    'position' => '1'
                ],
            ]
        ];



        $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => '0', 'old_value' => '0', 'field_name' => 'subcribtion_flag']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['email'], 'old_value' => '<EMAIL>', 'field_name' => 'email']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => '0', 'old_value' => '0', 'field_name' => 'sms_marketing_subscription_flag']);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['phone'], 'old_value' => '+15145555555', 'field_name' => 'phone']);

        // check if meta data record correctly
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['additionalEmails'][0]['email'], 'old_value' => null, 'field_name' => 'email', 'position' => 0]);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['additionalEmails'][1]['email'], 'old_value' => null, 'field_name' => 'email', 'position' => 1]);

        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['additionalPhones'][0]['phone'], 'old_value' => null, 'field_name' => 'phone', 'position' => 0]);
        $I->canSeeInDatabase('sf_customer_field_history', ['source' => 'mobile', 'customer_id' => $customerId, 'new_value' => $customer['additionalPhones'][1]['phone'], 'old_value' => null, 'field_name' => 'phone', 'position' => 1]);
    }

    /** @group database_transaction */
    public function testCreateUnsubCustomerWhenEmailAndPhoneOnBlockList(RestTester $I)
    {
        $I->wantTo("api create customer - post (REQUEST), create customer and set subscription status as unsubscribed");

        $this->insertFixtureGroup($I, 'CustomerBlockList');

        $customer = [
            'user_id'                         => 6,
            'email'                           => "<EMAIL>",
            'first_name'                      => "test",
            'last_name'                       => "unsub",
            'phone'                           => "+15142345749",
            'sms_marketing_subscription_flag' => "0",
            'subcribtion_flag'                => "0",
            'signature'                       => 'test-what-ever-value-it-is',
            'origin'                          => "mobile-rep-manual-creation",
            'country'                         => 'CA',
        ];



        $customer = $I->doDirectPost($I, "customers", $customer);

        $I->assertEquals('2', $customer->subcribtion_flag);
        // $I->assertEquals('2', $customer->sms_marketing_subscription_flag);
    }

    /** @group database_transaction */
    public function testUpdateUnsubCustomerWhenEmailAndPhoneOnBlockList(RestTester $I)
    {
        $I->wantTo("api update customer - post (REQUEST), create customer and set subscription status as unsubscribed");

        $this->insertFixtureGroup($I, 'CustomerBlockList');

        $customerId = 1005;

        $customer = [
            'user_id'                         => 6,
            'email'                           => "<EMAIL>",
            'first_name'                      => "test",
            'last_name'                       => "unsub",
            'phone'                           => "+15142345749",
            'sms_marketing_subscription_flag' => "1",
            'subcribtion_flag'                => "1",
            'signature'                       => 'test-what-ever-value-it-is',
            'country'                         => 'CA',
        ];



        $customer = $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->assertEquals('2', $customer->subcribtion_flag);
        $I->assertEquals('2', $customer->sms_marketing_subscription_flag);
    }

    /** @group database_transaction */
    public function testUpdateUnsubscribedCustomerFromLiveChat(RestTester $I)
    {
        $I->wantTo("api update unsubscribed customer - from live chat");

        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;
        $this->insertFixtureGroup($I, 'CustomerBlockList');


        $customerId = 1005;

        $customer = [
            'subcribtion_flag' => "1",
            'source' => "live-chat",
        ];



        $customer = $I->doDirectPut($I, "customers/$customerId", $customer);

        $I->assertEquals('1', $customer->subcribtion_flag);
    }

    /** @group database_transaction */
    public function testMandatoryFieldsEmail(RestTester $I)
    {
        $I->wantTo('Mandatory Email Field');

        $this->insertFixtureGroup($I, 'MandatoryFields');


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=email&filter[search]=Jordan');

        $I->assertEquals(2, count($results));
    }

    /** @group database_transaction */
    public function testMandatoryFieldsPhone(RestTester $I)
    {
        $I->wantTo('Mandatory Phone Field');

        $this->insertFixtureGroup($I, 'MandatoryFields');


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[mandatory_fields]=phone&filter[search]=Jordan');

        $I->assertEquals(1, count($results));
    }

    /** @group database_transaction */
    public function testUnsubscribedCustomers(RestTester $I)
    {
        $I->wantTo('Unsubscribed Customers');

        $this->insertFixtureGroup($I, 'UnsubscribedCustomers');


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[subcribtion_flag]=2');

        $I->assertEquals(1, count($results));
        $this->validateArray(Fixtures::get('customer9'), $results[0]);
    }

    /** @group database_transaction */
    public function testNotSubscribedCustomers(RestTester $I)
    {
        $I->wantTo('Not-subscribed Customers');

        $this->insertFixtureGroup($I, 'UnsubscribedCustomers');


        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        $results = $I->doDirectGet($I, 'customers?filter[subcribtion_flag]=0&filter[search]=Jordan');

        $I->assertEquals(1, count($results));
        $this->validateArray(Fixtures::get('customer10-no-email'), $results[0]);
    }


    /**
     * a function get list of Global Unsubscribe email from sendgrid provider
     * @param $sendgridProvider
     * @param $onBehalfOf
     * @return array
     */
    private function getGlobalUnsubscribes($sendgridProvider, $onBehalfOf)
    {
        $emails             = [];
        $globalUnsubscribes = $sendgridProvider->getAllGlobalUnsubscribes(
            null,
            null,
            $onBehalfOf
        );

        foreach ($globalUnsubscribes as $globalUnsubscribe) {
            foreach ($globalUnsubscribe as $list) {
                $emails[] = $list['email'];
            }
        }

        return $emails;
    }

    /** @group database_transaction */
    public function testCreateCustomerFromStorefrontWithModeStore(RestTester $I)
    {
        $I->wantTo('Add a new customer from a storefront with mode as Store');
        //enable team mode
        $this->app['configs']['retailer.storepage_mode'] = true;

        $I->haveInDatabase('wp_users', [
            'ID' => '101',
            'user_login' => 'test_login',
            'user_pass' => 'test_pass',
            'user_email' => '<EMAIL>',
            'user_nicename' => 'test nicer',
            'user_url' => 'localhost',
            'user_registered' => '2012-12-21 14:14:14',
            'user_status' => '1',
            'store' => '123',
            'type' => 'store',
        ]);

        $I->haveInDatabase('sf_store', [
            'store_id' => 123,
            'name' => 'A store',
            'latitude' => 45.4910,
            'longitude' => -73.5658,
            'country' => 'Canada',
            'store_user_id' => 101,
            'timezone' => 'America/Montreal',
            'locale' => 'en_US',
            'shame_type' => 'store',
            'sf_identifier' => 123,
        ]);

        $payload = [
            "name" => "Ooo Yyy",
            "email" => "<EMAIL>",
            "category" => "",
            "subcribtion_flag" => "0",
            "phone" => "",
            "localization" => "",
            "geo" => "",
            "type" => "corporate",
            "origin" => "storefront-chat",
            "locale" => "ja_JP",
            "store_id" => "123",
        ];



        $result = $I->doDirectPost($I, '/customers', $payload);
        $this->debug($result);

        $I->seeInDatabase(
            'sf_customer',
            [
                'user_id' => 101,
                'email' => '<EMAIL>',
                'name' => 'Ooo Yyy',
                'first_name' => 'Ooo',
                'last_name' => 'Yyy',
            ]
        );
    }

    /** @group database_transaction */
    public function testCreateCustomerWhenConnectConfigIsSetToFalse(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.can_add_contacts'] = false;
        $this->app['configs']['retailer.storepage_mode'] = true;

        $I->haveInDatabase('wp_users', [
            'ID' => '101',
            'user_login' => 'test_login',
            'user_pass' => 'test_pass',
            'user_email' => '<EMAIL>',
            'user_nicename' => 'test nicer',
            'user_url' => 'localhost',
            'user_registered' => '2012-12-21 14:14:14',
            'user_status' => '1',
            'store' => '123',
            'type' => 'store',
        ]);

        $I->haveInDatabase('sf_store', [
            'store_id' => 123,
            'name' => 'A store',
            'latitude' => 45.4910,
            'longitude' => -73.5658,
            'country' => 'Canada',
            'store_user_id' => 101,
            'timezone' => 'America/Montreal',
            'locale' => 'en_US',
            'shame_type' => 'store',
            'sf_identifier' => 123,
        ]);

        $payload = [
            "name" => "Ooo Yyy",
            "email" => "<EMAIL>",
            "category" => "",
            "subcribtion_flag" => "0",
            "phone" => "",
            "localization" => "",
            "geo" => "",
            "type" => "corporate",
            "origin" => "storefront-chat",
            "locale" => "ja_JP",
            "store_id" => "123",
        ];

        $result = $I->doDirectPost($I, '/customers', $payload);
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);
    }

    protected function getErrorMessage($error = 'error')
    {
        $errors = new \stdClass();
        $errors->error = $error;

        return $errors;
    }

    protected function checkStatus(RestTester $I, $code)
    {
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals($code, $statusCode);
    }


    /** @group database_transaction */
    public function testCreateContactWhenConnectAddContactsIsFalseNoSms(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.can_add_contacts'] = false;
        // Both 'first_name' and 'last_name' combine to form 'name',
        // otherwise 'name' will not be updated in the DB
        $payload = [
            'first_name' => 'Testing',
            'last_name' => 'Names',
        ];
        $result = $I->doDirectPost($I, '/customers', $payload);
        $this->checkStatus($I, 400);
    }

    /** @group database_transaction */
    public function testCreateContactWhenConnectAddContactsIsFalseWithSms(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.can_add_contacts'] = false;
        // Both 'first_name' and 'last_name' combine to form 'name',
        // otherwise 'name' will not be updated in the DB.
        // With a false config, 'pre_customer_id' (the SMS flag) must be set:
        $payload = [
            'pre_customer_id' => 1,
            'user_id' => 1,
            'first_name' => 'Testing',
            'last_name' => 'Names',
            'email' => '<EMAIL>',
            'origin' => Customer::ORIGIN_MOBILE_REP_MANUAL,
        ];



        $result = $I->doDirectPost($I, '/customers', $payload);
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);

        $I->assertEquals($result->name, 'Testing Names');
    }

    /** @group database_transaction */
    public function testCreateContactWhenConnectConsentIsFalseNotCustomerNoSms(RestTester $I)
    {
        $I->wantTo("as rep, set the consent, because this is not an SMS (customer) request");

        $this->app['configs']['retailer.modular_connect.can_add_contacts'] = true;
        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = false;

        $payload = [
            'user_id' => 1,
            'first_name' => 'Nivens',
            'last_name' => 'McTwisp',
            'email' => '<EMAIL>',
            'phone' => '+15145555555',
            'subcribtion_flag' => '9',
            'sms_marketing_subscription_flag' => '1',
        ];



        $result = $I->doDirectPost($I, '/customers', $payload);
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);

        $I->assertEquals($result->name, 'Nivens McTwisp');
        // I's only 9 in this test to make certain that it's what we gave as input:
        $I->assertEquals($result->subcribtion_flag, 9);
    }

    /** @group database_transaction */
    public function testCreateContactWhenConnectConsentIsFalseCustomerSms(RestTester $I)
    {
        $I->wantTo("as rep, I cannot set consent (because this is a customer request)");

        $this->app['configs']['retailer.modular_connect.can_add_contacts'] = true;
        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = false;

        $payload = [
            'pre_customer_id' => 1,
            'user_id' => 1,
            'first_name' => 'Nivens',
            'last_name' => 'McTwisp',
            'email' => '<EMAIL>',
            'phone' => '+15145555555',
            'subcribtion_flag' => '9',
            'sms_marketing_subscription_flag' => '9',
        ];

        $result = $I->doDirectPost($I, '/customers', $payload);
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(403, $statusCode);

        $I->assertEquals($result->subcribtion_flag, 0);
        $I->assertEquals($result->sms_marketing_subscription_flag, 0);
    }

    /** @group database_transaction */
    public function testUpdateContactWhenConnectConsentTrue(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = true;

        $I->updateInDatabase(
            'sf_customer',
            [
                'subcribtion_flag' => 1,
                'sms_marketing_subscription_flag' => 2,
            ],
            [
                'ID' => 1
            ]
        );

        // Regardless of the "can_change_communication_consent" flag, we must have these set:
        // "email", else "subscription_flag" reverts to 0, and
        // "phone", else "sms_marketing_subscription_flag" reverts to 0.
        // See api/app/src/be/Managers/Client/Customers/Legacy.php::beforeSave(), lines 1102--1113
        $updateData = [
            'user_id' => 1,
            'first_name' => 'Testing',
            'last_name' => 'Names',
            'email' => "<EMAIL>",
            'phone' => "************",
            'subcribtion_flag' => "0",
            'sms_marketing_subscription_flag' => "0",
        ];



        $results = $I->doDirectPut($I, "customers/1", $updateData);
        $db = $I->grabRowsFromDatabase('sf_customer', null, ['ID' => 1])[0];

        $I->assertEquals('<EMAIL>', $results->email);
        $I->assertEquals($db['email'], $results->email);

        $I->assertEquals('+15145555555', $results->phone);
        $I->assertEquals($db['phone'], $results->phone);

        $I->assertEquals('0', $results->subcribtion_flag);
        $I->assertEquals($db['subcribtion_flag'], $results->subcribtion_flag);


        $I->assertEquals('0', $results->sms_marketing_subscription_flag);
        $I->assertEquals($db['sms_marketing_subscription_flag'], $results->sms_marketing_subscription_flag);
    }

    protected function updateSfCustomerTableStatusInDb(RestTester $I)
    {
        $I->updateInDatabase(
            'sf_customer',
            [
                'first_name' => 'Joseph',
                'last_name' => 'Mallette',
                'email' => "<EMAIL>",
                'phone' => "",
                'subcribtion_flag'                => 1,
                'sms_marketing_subscription_flag' => 0,
            ],
            [
                'ID' => 1
            ]
        );
    }

    /**
     * This is a test of all the new conditions in controllers/src/Base/LegacyController.php::verifyNoChangeToConsent()
     *
     * @param RestTester $I
     * @return void
     *
     * @group database_transaction
     */
    public function testUpdateContactUnderAllConditionsWhenConsentIsFalse(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.can_change_communication_consent'] = false;

        $this->updateSfCustomerTableStatusInDb($I);
        $errors = $this->getErrorMessage('Consent status cannot be changed.');

        // These changes are allowed, even with 'can_change_communication_consent = false'.
        // Regardless of the "can_change_communication_consent" flag, we must have these set:
        //      "email", else "subscription_flag" reverts to 0, and
        //      "phone", else "sms_marketing_subscription_flag" reverts to 0 --
        // See api/app/src/be/Managers/Client/Customers/Legacy.php::beforeSave(/*...*/), lines 1102--1118
        // We must also explicitly include both the "subscription_flag" AND "sms_marketing_subscription_flag":
        $updateData = [
            'user_id'    => 1,
            'first_name' => 'Testing',
            'last_name'  => 'Namechange',
            'email'      => '<EMAIL>',
            'phone'      => '',
            'subcribtion_flag'                => 1,
            'sms_marketing_subscription_flag' => 0,
        ];



        $results = $I->doDirectPut($I, "customers/1", $updateData);
        $db = $I->grabRowsFromDatabase('sf_customer', null, ['ID' => 1])[0];
        $I->assertEquals($db['first_name'], $results->first_name);
        $I->assertEquals($db['last_name'], $results->last_name);
        $I->assertEquals($db['email'], $results->email);

        // Condition 1: Do not allow change of consent to existing phone or email:
        $updateData['sms_marketing_subscription_flag'] = 1;
        $results = $I->doDirectPut($I, "customers/1", $updateData);
        $this->checkStatus($I, 403);
        $I->assertEquals($results, $errors);

        // Condition 2: If a subscribed phone/email is being updated (phone/email being changed),
        // do not allow it to remain subscribed -- the status should be 1 to 1 only
        // if FE tries to subscribe, return 'Consent status cannot be changed' error.
        $updateData['email'] = '<EMAIL>';
        $updateData['subcribtion_flag'] = 1;
        $updateData['sms_marketing_subscription_flag'] = 0;
        $results = $I->doDirectPut($I, "customers/1", $updateData);
        $this->checkStatus($I, 403);
        $I->assertEquals($results, $errors);

        // Condition 3: If new email / phone is added, the only allowed consent would be 1 to 1 (flag == 0)
        // in any other case, return 'Consent status cannot be changed' error
        $updateData['phone'] = '+15555555555';
        $updateData['sms_marketing_subscription_flag'] = 1;
        $updateData['email'] = '<EMAIL>';
        $updateData['subcribtion_flag'] = 0;
        $results = $I->doDirectPut($I, "customers/1", $updateData);
        $this->checkStatus($I, 403);
        $I->assertEquals($results, $errors);
    }

    /**
     * During rep onboarding, we can import multiple contacts many times.
     * This test is to check if we can import multiple contacts during rep onboarding
     * even some of contacts imported already.
     *
     * @param RestTester $I
     * @return void
     *
     * @group database_transaction
     */
    public function testCreateMultipleContactsDuringRepOnboarding(RestTester $I)
    {
        $customers = [
            [
                "id" => 7,
                "name" => "Ben",
                "email" => "<EMAIL>",
                "user_id" => 1,
                "origin" => "mobile-rep-device-import"
            ],
        ];
        $customer = $I->doDirectPost($I, "customers", $customers);
        $I->assertEquals(0, $customer->created);
        $I->assertEquals(0, $customer->updated);
        $I->assertEquals(1, $customer->skipped);
        $I->assertEquals([1], $customer->reps);
    }
}
