<?php

namespace SF\rest\Customer;

use SF\RestTester;
use SF\rest\BaseRest;
use Salesfloor\API\Managers\Client\Customers\Legacy;
use Salesfloor\API\Managers\Client\RetailerCustomers\Legacy as RetailerCustomerManager;

class AdvancedSearchCest extends BaseRest
{
    /** @group database_transaction */
    public function testAdvancedSearchContacts(RestTester $I)
    {
        $I->wantTo("Test advanced search contacts");

        $this->insertFixtureGroup($I, 'AdvancedSearchContacts');

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        // Regular search by email with UPPERCASE chars.
        $results = $I->doDirectGet($I, 'v1/customers?filter[search]=20_EMAIL@Salesfloor.N');
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search first_name and find one
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][first_name]=rst20na');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search first_name and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][first_name]=ast20na');
        $I->assertCount(0, $results->data);

        // Search last_name and find one
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][last_name]=ast20na');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search last_name and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][last_name]=rst20na');
        $I->assertCount(0, $results->data);

        // Search email and find one with upper case.
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][email]=Customer20');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search email and find one with special/UPPERCASE chars
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][email]=20_EMAIL@salesflooR.N');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search email and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][email]=customer22');
        $I->assertCount(0, $results->data);

        // Search phone and find one
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][phone]=1820');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search phone and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][phone]=1822');
        $I->assertCount(0, $results->data);

        // Search city and find one
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][city]=montreal');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search city and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][city]=ottawa');
        $I->assertCount(0, $results->data);

        // Search state and find one
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][state]=qc');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search state and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][state]=bc');
        $I->assertCount(0, $results->data);

        // Search note and find one
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][notes]=note20');
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Search note and nothing found
        $results = $I->doDirectGet($I, 'v1/customers?filter[search][notes]=note22');
        $I->assertCount(0, $results->data);

        // Combine all conditions
        $query = [
            'filter[search][first_name]' => 'rst20na',
            'filter[search][last_name]' => 'ast20na',
            'filter[search][email]' => 'customer20',
            'filter[search][phone]' => '1820',
            'filter[search][city]' => 'montreal',
            'filter[search][state]' => 'qc',
            'filter[search][notes]' => 'note20',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);
    }

    /** @group database_transaction */
    public function testAdvancedSearchRetailerCustomers(RestTester $I)
    {
        $I->wantTo("Test advanced search retailer customers");

        $this->insertFixtureGroup($I, 'AdvancedSearchRetailerCustomers');

        /** @var RetailerCustomerManager @manager */
        $manager = $this->app['retailer_customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        // Search first_name and find one
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][first_name]=rst1na&filter[city]=Montreal');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search first_name and nothing found
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][first_name]=ast2na');
        $I->assertCount(0, $results->data);

        // Search last_name and find one
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][last_name]=ast1na');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search last_name and nothing found
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][last_name]=rst2na');
        $I->assertCount(0, $results->data);

        // Search email and find one
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][email]=Mer1em');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search email and find one with special chars
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][email]=mer1email@salesfloor.n');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search email and nothing found
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][email]=mer3em');
        $I->assertCount(0, $results->data);

        // Search phone and find one
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][phone]=425');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search phone and nothing found
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][phone]=9999');
        $I->assertCount(0, $results->data);

        // Search city and find one
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][city]=montreal');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search city and nothing found
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][city]=ottawa');
        $I->assertCount(0, $results->data);

        // Search state and find one
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][state]=qc');
        $I->assertCount(1, $results->data);
        $retailerCustomer = $results->data[0];
        $I->assertEquals('customer1id', $retailerCustomer->customer_id);

        // Search state and nothing found
        $results = $I->doDirectGet($I, 'retailer-customers?filter[search][state]=bc');
        $I->assertCount(0, $results->data);
    }

    /** @group database_transaction */
    public function testAdvancedSearchContactsByTransaction(RestTester $I)
    {
        $I->wantTo("Test advanced search contacts by transactions");

        $this->insertFixtureGroup($I, 'AdvancedSearchContacts');
        $this->insertFixtureGroup($I, 'AdvancedSearchRetailerCustomers');
        $this->insertFixtureGroup($I, 'AdvancedSearchTransactions');
        $this->insertFixtureGroup($I, 'AdvancedSearchRetailerTransactions');

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['elasticsearch.max_bulk_size'] = 3;

        // 10 years old
        $this->app['configs']['elasticsearch.index.transactions.threshold_month'] = 12 * 10;

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        // Filter by transactions amount
        $query = [
            'filter[transaction][total][from]' => 50,
            'filter[transaction][total][to]' => 150,
            // 'filter[city]' => 'Montreal',
            // 'filter[search][first_name]' => 'rst20na1',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Filter by rep transactions date
        $query = [
            'filter[transaction][date][start]' => '2021-06-01',
            'filter[transaction][date][end]' => '2021-06-30',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(21, $customer->ID);

        // Filter by retailer transactions date
        $query = [
            'filter[transaction][date][start]' => '2023-06-01',
            'filter[transaction][date][end]' => '2023-06-30',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(21, $customer->ID);

        // Filter by brand
        $query = [
            'filter[transaction][brand]' => 'BRAND-20-1-1,AnyBrand',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Filter by brand and amount (check duplicate products)
        // Only one customer matched for condition
        //     1. Transaction amount (100 < total < 200)
        //     2. Transaction has duplicate products [brand, category]
        $query = [
            'filter[transaction][brand]' => 'BRAND-20-1-1,AnyBrand',
            'filter[transaction][total][from]' => 100,
            'filter[transaction][total][to]' => 200,
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Filter by category
        $query = [
            'filter[transaction][category_id]' => 'category-id-21-1-2,AnyCategoryID',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(21, $customer->ID);

        // Filter by brand and category matched
        $query = [
            'filter[transaction][brand]' => 'BRAND-21-2-2',
            'filter[transaction][category_id]' => 'category-id-21-2-2',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(21, $customer->ID);

        // Filter by brand and category do not matched
        $query = [
            'filter[transaction][brand]' => 'BRAND-21-2-2',
            'filter[transaction][category_id]' => 'category-id-21-2-1',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(0, $results->data);

        // Filter by  transactions amount/date/brand/category_id and search keyword
        $query = [
            'filter[transaction][total][from]' => 200,
            'filter[transaction][total][to]' => 300,
            'filter[transaction][date][start]' => '2020-06-01',
            'filter[transaction][date][end]' => '2020-06-30',
            'filter[transaction][brand]' => 'BRAND-20-2-1,BRAND-20-2-2',
            'filter[transaction][category_id]' => 'category-id-20-2-1,category-id-20-2-2',
            'filter[city]' => 'Montreal',
            'filter[search][first_name]' => 'rst20na',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
        $customer = $results->data[0];
        $I->assertEquals(20, $customer->ID);

        // Filter by  transactions amount with wrong date
        $query = [
            'filter[transaction][total][from]' => 200,
            'filter[transaction][total][to]' => 300,
            'filter[transaction][date][start]' => '2020-02-01',
            'filter[transaction][date][end]' => '2020-02-28',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(0, $results->data);
    }

    /** @group database_transaction */
    public function testGetCountForShareAnUpdateWitTransactionFilters(RestTester $I)
    {
        $I->wantTo("Test filters for share an update with transactions");

        $this->insertFixtureGroup($I, 'AdvancedSearchContacts');
        $this->insertFixtureGroup($I, 'AdvancedSearchTransactions');

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['elasticsearch.max_bulk_size'] = 3;
        $this->app['configs']['elasticsearch.index.transactions.threshold_month'] = 12 * 10;

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        // Filter by  transactions amount/date/brand/category_id and search keyword
        $query = [
            'mandatory_fields' => 'email',
            'filter[customers]' => 'subscribers',
            'filter[mandatory_fields]' => 'email',
            'filter[favorite_contacts]' => 1,
            'filter[tags]' => '1,2',
            'filter[transaction][total][from]' => 200,
            'filter[transaction][total][to]' => 300,
            'filter[transaction][date][start]' => '2020-06-01',
            'filter[transaction][date][end]' => '2020-06-30',
            'filter[transaction][brand]' => 'BRAND-20-2-1,BRAND-20-2-2',
            'filter[transaction][category_id]' => 'category-id-20-2-1,category-id-20-2-2',
            'filter[city]' => 'Montreal,Quebec',
            'filter[state]' => 'QC,ON',
        ];
        $result = $I->doDirectGet($I, 'customers-countbytags?' . http_build_query($query));
        $I->assertEquals(1, $result);
    }

    /** @group database_transaction */
    public function testAdvancedSearchContactsGetSortedProperlyWithoutElasticsearch(RestTester $I)
    {
        $I->wantTo("Test the search result get sorted properly");

        $this->insertFixtureGroup($I, 'AdvancedSearchContacts');

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['elasticsearch.max_bulk_size'] = 3;

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        // Filter by transactions amount
        $query = [
            'filter[sort]' => '-last_contacted',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(4, $results->data);
    }

    /** @group database_transaction */
    public function testAdvancedSearchContactsGetSortedProperlyWithElasticsearch(RestTester $I)
    {
        $I->wantTo("Test the search result get sorted properly");

        $this->insertFixtureGroup($I, 'AdvancedSearchContacts');

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['elasticsearch.max_bulk_size'] = 3;

        /** @var Legacy @manager */
        $manager = $this->app['customers.manager'];
        // populate elastic search indexes with customer data
        $manager->reindex();

        // Search only
        $query = [
            'filter[search]' => 'rst',
            'filter[sort]' => '-first_name',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(2, $results->data);
        $I->assertEquals('First21name', $results->data[0]->first_name);
        $I->assertEquals('First20name', $results->data[1]->first_name);

        $query = [
            'filter[search]' => 'rst',
            'filter[sort]' => '-last_name',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(2, $results->data);
        $I->assertEquals('Last21name', $results->data[0]->last_name);
        $I->assertEquals('Last20name', $results->data[1]->last_name);

        $query = [
            'filter[search]' => 'rst',
            'filter[sort]' => '-created',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(2, $results->data);
        $I->assertEquals('Last21name', $results->data[0]->last_name);
        $I->assertEquals('Last20name', $results->data[1]->last_name);

        $query = [
            'filter[search]' => 'rst',
            'filter[sort]' => '-last_contacted',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(2, $results->data);
        $I->assertEquals('Last21name', $results->data[0]->last_name);
        $I->assertEquals('Last20name', $results->data[1]->last_name);

        // Filter only
        $query = [
            'filter[city]' => 'Montreal',
            'filter[sort]' => '-last_name',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);

        // Search & filter
        $query = [
            'filter[search]' => 'rst',
            'filter[city]' => 'Montreal',
            'filter[sort]' => '-first_name',
        ];
        $results = $I->doDirectGet($I, 'v1/customers?' . http_build_query($query));
        $I->assertCount(1, $results->data);
    }
}
