<?php

namespace SF\rest\Customer;

use SF\rest\BaseRest;
use SF\RestTester;

/**
 * @group database_transactionaa
 */
class CustomerMetaCest extends BaseRest
{
    public function testCanRemoveAdditionalEmailAndPhone(RestTester $I)
    {
        $I->wantTo("Test removing additional email and phone");
        $this->insertFixtureGroup($I, 'CustomerMeta');

        $I->seeInDatabase('sf_customer_meta', [
            'customer_id' => 1,
            'type' => 'email'
        ]);

        $I->seeInDatabase('sf_customer_meta', [
            'customer_id' => 1,
            'type' => 'phone'
        ]);

        $response = $I->doDirectPut($I, 'customers/1', [
            "user_id" => 1,
            "email" => "<EMAIL>",
            "name" => "Aaabb Abab",
            "phone" => "+14389256742",
            "localization" => "en",
            "geo" => "",
            "latitude" => null,
            "longitude" => null,
            "comment" => "",
            "subcribtion_flag" => "1",
            "sms_marketing_subscription_flag" => "1",
            "first_name" => "Aaabb",
            "last_name" => "Abab",
            "note" => "",
            "created" => "2021-07-15 21:56:23",
            "last_modified" => "2025-01-15 16:48:17",
            "label_email" => null,
            "label_phone" => null,
            "retailer_customer_id" => null,
            "origin" => "mobile-rep-manual-creation",
            "locale" => "en_US",
            "unassigned_employee_id" => null,
            "retailer_parent_customer_id" => null,
            "entity_last_modified" => "2025-01-15 16:48:17",
            "entity_last_export" => "0000-00-00 00:00:00",
            "contact_preference" => null,
            "customer_type" => "personal",
            "id" => "296",
            "type" => "customer",
            "is_sms_blacklisted" => 1,
            "is_favorite_contact" => false,
            "additionalEmails" => [
                [
                    "customer_id" => "1",
                    "value" => "<EMAIL>",
                    "label" => "",
                    "position" => -1,
                    "creation_date" => "2025-01-15 17:51:45",
                    "modification_date" => null,
                    "customer-meta_type" => "email",
                    "id" => "1",
                    "type" => "customer-meta",
                    "email" => ""
                ]
            ],
            "additionalPhones" => [
                [
                    "customer_id" => "1",
                    "value" => "+15144321111",
                    "label" => "",
                    "position" => -1,
                    "creation_date" => "2025-01-15 16:47:04",
                    "modification_date" => null,
                    "customer-meta_type" => "phone",
                    "id" => "2",
                    "type" => "customer-meta",
                    "phone" => ""
                ]
            ],
            "events" => [],
            "socials" => [],
            "addresses" => [],
            "attributePanels" => [],
            "customer_tags" => [],
            "subscriptionStatus" => "2",
            "smsSubscriptionStatus" => "2",
            "isSubscribed" => false,
            "isUnsubscribed" => true,
            "isSMSSubscribed" => false,
            "isSMSUnsubscribed" => true,
            "hasSubscriptionFlag" => true,
            "hasSMSSubscriptionFlag" => true,
            "ID" => 296,
            "country" => "CA",
            "subscription_flag" => "0"
        ]);

        $this->debug($response);

        $I->dontSeeInDatabase('sf_customer_meta', [
            'customer_id' => 1,
            'type' => 'email'
        ]);

        $I->dontSeeInDatabase('sf_customer_meta', [
            'customer_id' => 1,
            'type' => 'phone'
        ]);
    }
}
