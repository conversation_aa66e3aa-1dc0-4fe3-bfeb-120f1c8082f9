<?php

declare(strict_types=1);

namespace SF\rest\Customer;

use SF\RestTester;
use SF\rest\BaseRest;
use Codeception\Example;
use Codeception\Scenario;
use Codeception\Util\Fixtures;
use SF\Helper\Traits\Asserts;

class CustomerJsonApiCest extends BaseRest
{
    use Asserts;

    public function _before($I)
    {
        parent::_before($I);
        $I->setMocksForGetMyUpdates();
    }

    // =========== customer attribute get api ====================

    /** @group database_transaction */
    public function testGetCustomerWithAllTheProperties(RestTester $I)
    {
        $I->wantTo('Test get one customer with all the properties');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $customerProperties = Fixtures::get('CustomerProperties');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        $response = $I->doDirectGet($I, '/v2/customers/1?include=customer-social-media,customer-event,customer-address,customer-social-media.network,customer-to-customer-attribute,customer-to-customer-attribute.customer-attribute,customer-to-customer-attribute.customer-attribute.customer-attribute-panel');

        // remove created_at from response which is dynamically generated
        unset($response->data->attributes->created);
        foreach ($response->included as & $included) {
            unset($included->attributes->created_at);
        }
        // sort included by type and id to make snapshot stable.
        usort($response->included, function ($a, $b) {
            return $a->type <=> $b->type ?: $a->id <=> $b->id;
        });

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($response, $jsonFile);
    }

    /** @group database_transaction */
    public function testGetRetailerCustomerWithAllTheProperties(RestTester $I, Scenario $scenario)
    {
        // $scenario->skip();
        $I->wantTo('Test get one retailer customer with all the properties');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $customerProperties = Fixtures::get('CustomerProperties');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, '/v2/retailer-customers/2001?include=retailer-customer-to-customer-attribute,retailer-customer-to-customer-attribute.customer-attribute,retailer-customer-to-customer-attribute.customer-attribute.customer-attribute-panel');

        $data = $response->data;
        $included = $response->included;

        $I->assertEquals('12345', $data->attributes->customer_id);
        $I->assertEquals('<EMAIL>', $data->attributes->email);

        $I->assertEquals('customer-attribute-panel', $included[0]->type);
        $I->assertEquals('panel123', $included[0]->attributes->panel_id);
        $I->assertEquals('test label', $included[0]->attributes->label);

        $I->assertEquals('customer-attribute', $included[1]->type);
        $I->assertEquals($customerProperties['sf_customer_attributes'][0]['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($customerProperties['sf_customer_attributes'][0]['label'], $included[1]->attributes->label);

        $I->assertEquals('retailer-customer-to-customer-attribute', $included[2]->type);
        $I->assertEquals($customerProperties['sf_retailer_customers_to_customer_attributes'][0]['attribute_id'], $included[2]->attributes->attribute_id);
        $I->assertEquals($customerProperties['sf_retailer_customers_to_customer_attributes'][0]['attribute_value'], $included[2]->attributes->attribute_value);
        $I->assertEquals($customerProperties['sf_retailer_customers_to_customer_attributes'][0]['customer_id'], $included[2]->attributes->customer_id);
    }

    // =========== customer attribute side Patch ====================

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueValidSingle(RestTester $I)
    {
        $I->wantTo('Test update one customer attribute value to another valid attribute value');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-single');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;
        $expected = $requestData['included'];

        // the attribute value change from 180 => 181 in patch data
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected[0]['attributes']['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected[0]['attributes']['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected[0]['attributes']['customer_id'], $included[0]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueValidMultiplePatch(RestTester $I)
    {
        $I->wantTo('Test update one customer multiple attribute value to other valid multiple attribute values');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithMultipleAttribute');

        $requestData = Fixtures::get('customer-with-customer-attribute-valid-multiple');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;

        $expected = $requestData['included'];
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected[0]['attributes']['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected[0]['attributes']['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected[0]['attributes']['customer_id'], $included[0]->attributes->customer_id);

        $I->assertEquals('customer-to-customer-attribute', $included[1]->type);
        $I->assertEquals($expected[1]['attributes']['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($expected[1]['attributes']['attribute_value'], $included[1]->attributes->attribute_value);
        $I->assertEquals($expected[1]['attributes']['customer_id'], $included[1]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueValidMultiplePostAndPatch(RestTester $I)
    {
        $I->wantTo('Test update one customer multiple attribute value to other valid multiple attribute values ( post + patch sametime)');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $requestData = Fixtures::get('customer-with-customer-attribute-valid-multiple-post-and-patch');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;

        $expected = $requestData['included'];
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected[0]['attributes']['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected[0]['attributes']['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected[0]['attributes']['customer_id'], $included[0]->attributes->customer_id);

        $I->assertEquals('customer-to-customer-attribute', $included[1]->type);
        $I->assertEquals($expected[1]['attributes']['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($expected[1]['attributes']['attribute_value'], $included[1]->attributes->attribute_value);
        $I->assertEquals($expected[1]['attributes']['customer_id'], $included[1]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueInValidMultiple(RestTester $I)
    {
        $I->wantTo('Test update one customer multiple attribute value to other valid multiple attribute values');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $this->insertFixtureGroup($I, 'CustomerPropertiesWithMultipleAttribute');
        $requestData = Fixtures::get('customer-with-customer-attribute-invalid-multiple');

        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', $requestData);

        $included = $response->included;

        // sf_customers_to_customer_attributes fixture data
        $attributeFixture = Fixtures::get('CustomerProperties');
        $expected0 = $attributeFixture['sf_customers_to_customer_attributes'][0];
        $I->wantTo('this data keep as before from db fixture');
        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected0['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected0['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected0['customer_id'], $included[0]->attributes->customer_id);

        $I->wantTo('this data be updated by request data');
        $expected1 = $requestData['included'][1]['attributes'];
        $I->assertEquals('customer-to-customer-attribute', $included[1]->type);
        $I->assertEquals($expected1['attribute_id'], $included[1]->attributes->attribute_id);
        $I->assertEquals($expected1['attribute_value'], $included[1]->attributes->attribute_value);
        $I->assertEquals($expected1['customer_id'], $included[1]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeValueInvalid(RestTester $I)
    {
        $I->wantTo('Test update one customer attribute value to an invalid attribute value');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', Fixtures::get('customer-with-customer-attribute-invalid-single'));

        $included = $response->included;

        // we need get expected from fixture data: sf_customers_to_customer_attributes
        $attributeFixture = Fixtures::get('CustomerProperties');
        $expected0 = $attributeFixture['sf_customers_to_customer_attributes'][0];

        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected0['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected0['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected0['customer_id'], $included[0]->attributes->customer_id);
    }

    /** @group database_transaction */
    public function testUpdateCustomerAttributeIdInvalid(RestTester $I)
    {
        $I->wantTo('Test update one customer attribute to an invalid attribute id');

        $this->insertFixtureGroup($I, 'CustomerMatchingOnly');
        $this->insertFixtureGroup($I, 'CustomerProperties');
        $userId   = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPatch($I, '/v2/customers/1', Fixtures::get('customer-with-customer-attribute-invalid-id'));

        $included = $response->included;

        // we need get expected from fixture data: sf_customers_to_customer_attributes
        $attributeFixture = Fixtures::get('CustomerProperties');
        $expected0 = $attributeFixture['sf_customers_to_customer_attributes'][0];

        $I->assertEquals('customer-to-customer-attribute', $included[0]->type);
        $I->assertEquals($expected0['attribute_id'], $included[0]->attributes->attribute_id);
        $I->assertEquals($expected0['attribute_value'], $included[0]->attributes->attribute_value);
        $I->assertEquals($expected0['customer_id'], $included[0]->attributes->customer_id);
    }

    /** =========== Customer Get My Updates ==================== */
    /**
     * Provides data for Get My Updates form.
     * @return array
     */
    protected function getMyUpdatesFormDataProvider()
    {
        return Fixtures::get('getMyUpdatesFormData');
    }

    /**
     * @group database_transaction
     * @dataProvider getMyUpdatesFormDataProvider
     * @param RestTester $I
     * @param Example $dataProvider
     *
     */
    public function testGetMyUpdatesForm(RestTester $I, Example $dataProvider)
    {
        $I->wantTo('test end-point for Get My Updates form');

        if ($dataProvider['data']['attributes']['user_id'] === 1001) {
            // In this specific case we want to test that a system notification is created because the rep is not eligible
            $this->app['configs']['retailer.corporate-email.required'] = false;
            $this->insertFixtureGroup($I, 'users_my_update');
        }

        if (isset($dataProvider['data_purpose']) && $dataProvider['data_purpose'] === 'test_exist_customer_to_update') {
            $this->insertFixtureGroup($I, 'exist_customer_to_update');
        }

        $I->amGoingTo('send data to end-point');
        $I->doDirectPost($I, '/v2/customers/get-my-updates', ['data' => $dataProvider['data']]);

        $I->amGoingTo('validate response');

        // expect response status code.
        $I->assertEquals($dataProvider['expectedStatusCode'], $I->getLastResponse()->getStatusCode());

        // expect content is not empty.
        $content = $I->getLastResponse()->getContent();

        $I->assertNotEmpty($content);
        // validate returned JSON fields.
        if (!empty($dataProvider['expectedContent'])) {
            $content = json_decode($content, true);
            $I->assertArrayHasKey('data', $content);
            $customerAttrs = $content['data'][0]['attributes'];

            foreach ($dataProvider['expectedContent'] as $_key => $expectedValue) {
                $I->assertArrayHasKey($_key, $customerAttrs);
                $returnedValue = is_string($customerAttrs[$_key]) ? trim($customerAttrs[$_key]) : $customerAttrs[$_key];
                $I->assertEquals($expectedValue, $returnedValue);
            }

            if (!empty($dataProvider['expectedAction']) && $dataProvider['expectedAction'] == 'createNewCustomer') {
                $I->seeInDatabase('sf_events', [
                    'type'        => \Salesfloor\Services\Event::SF_EVENT_USER_ADD,
                    'source'      => 'storefront',
                    'user_id'     => $dataProvider['data']['attributes']['user_id'],
                    'customer_id' => $content['data'][0]['id'],
                    'attributes'  => '1',
                ]);
            }

            if ($dataProvider['data']['attributes']['user_id'] === 1001) {
                $I->seeInDatabase('sf_messages', [
                    'title' => 'New subscriber',
                ]);
            }
        }

        if (!empty($dataProvider['seeInDatabase'])) {
            foreach ($dataProvider['seeInDatabase'] as $asserts) {
                [$table, $criteria] = $asserts;
                $I->seeInDatabase($table, $criteria);
            }
        }
    }
}
