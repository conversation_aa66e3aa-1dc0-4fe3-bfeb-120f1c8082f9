<?php

namespace SF\rest\RetailerCustomer;

use SF\RestTester;
use SF\rest\BaseRest;
use Codeception\Scenario;
use Codeception\Util\Fixtures;

class RetailerCustomersSearchCest extends BaseRest
{
    protected $retailerCustomerManager;

    public function _before($I)
    {
        parent::_before($I);
        $this->retailerCustomerManager = $this->app['retailer_customers.manager'];
    }

    /** @group database_transaction */
    public function testSearchCustomerByAddresses(RestTester $I, Scenario $scenario)
    {
        // $scenario->skip("Do not need to test elasticsearch.");
        $I->wantTo("Test search retailer customers by addresses");

        $this->insertFixtureGroup($I, 'Elasticsearch');
        $this->retailerCustomerManager->reindex();
        $retailerCustomer = Fixtures::get('Elasticsearch')['sf_retailer_customers'][0];

        // Search by country
        $results = $I->doDirectGet($I, "retailer-customers?filter[search]=canada&filter[user_id]=1");
        $this->validateArray($retailerCustomer, $results->data[0]);

        // Search by city
        $results = $I->doDirectGet($I, "retailer-customers?filter[search]=montreal&filter[user_id]=1");
        $this->validateArray($retailerCustomer, $results->data[0]);

        // Search by state
        $results = $I->doDirectGet($I, "retailer-customers?filter[search]=QC&filter[user_id]=1");
        $this->validateArray($retailerCustomer, $results->data[0]);

        // Search by street
        $results = $I->doDirectGet($I, "retailer-customers?filter[search]=Notre&filter[user_id]=1");
        $this->validateArray($retailerCustomer, $results->data[0]);

        // Search by postal code
        $results = $I->doDirectGet($I, "retailer-customers?filter[search]=H3C8H4");
        $this->validateArray($retailerCustomer, $results->data[0]);
    }
}
