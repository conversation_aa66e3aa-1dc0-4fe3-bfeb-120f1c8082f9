<?php

namespace SF\rest\RetailerCustomer;

use Codeception\Util\Fixtures;
use Salesfloor\Models\Customer;
use Salesfloor\Models\RetailerCustomer;
use Salesfloor\Services\Event;
use SF\rest\BaseRest;
use SF\RestTester;

class RetailerCustomerCest extends BaseRest
{
    /** @group database_transaction */
    public function testRetailerCustomerBaseIncludes(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - address - events - meta - social");

        $date = $this->insertFixtureGroup($I, 'RetailerCustomerBaseIncludes');

        $includes = [
            'customer-tag',
            'retailer-customer-address',
            'retailer-customer-event',
            'retailer-customer-meta',
            'retailer-customer-social-media',
            'retailer-customer-social-media.network',
        ];

        $results = $I->doDirectGet($I, sprintf('v2/retailer-customers/1?include=%s', implode(',', $includes)));
        $this->validateArray(Fixtures::get('RetailerCustomerBaseIncludesResponse'), $results);
    }

    /** @group database_transaction */
    public function testRetailerCustomerStatsSectionsOnly(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - stats - sections-panels-datapoints include - only section");

        $this->insertFixtureGroup($I, 'RetailerCustomerStatsSectionOnly');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1?include=retailer-customer-stats-section.retailer-customer-stats-panel.retailer-customer-stats-datapoint,retailer-customer-stats-panel.retailer-customer-stats-datapoint');


        $this->validateArray(Fixtures::get('RetailerCustomerStatsSectionOnlyResponse'), $results);
    }

    /** @group database_transaction */
    public function testRetailerCustomerStatsSectionsAndPanels(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - stats - sections-panels-datapoints include - section and panel");

        $this->insertFixtureGroup($I, 'RetailerCustomerStatsSectionAndPanel');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1?include=retailer-customer-stats-section.retailer-customer-stats-panel.retailer-customer-stats-datapoint,retailer-customer-stats-panel.retailer-customer-stats-datapoint');


        $this->validateArray(Fixtures::get('RetailerCustomerStatsSectionAndPanelResponse'), $results);
    }

    /** @group database_transaction */
    public function testRetailerCustomerStatsPanelsOnly(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - stats - sections-panels-datapoints include - only panel");

        $this->insertFixtureGroup($I, 'RetailerCustomerStatsPanelOnly');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1?include=retailer-customer-stats-section.retailer-customer-stats-panel.retailer-customer-stats-datapoint,retailer-customer-stats-panel.retailer-customer-stats-datapoint');


        $this->validateArray(Fixtures::get('RetailerCustomerStatsPanelOnlyResponse'), $results);
    }

    /* Legacy list */

    /** @group database_transaction */
    public function testLegacyListRetailerCustomerLimitedVisibilityShownPrimary(RestTester $I)
    {
        $I->wantTo("test - retailer customer legacy list endpoint - limited visibility - shown primary");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityShownPrimary');

        $results = $I->doDirectGet($I, 'retailer-customers');


        $I->assertTrue(!empty($results->data[0]->link_to_rep) && $results->data[0]->link_to_rep === RetailerCustomer::LINK_TO_REP_PRIMARY);
        $I->assertTrue(!empty($results->data[0]->email) && $results->data[0]->email === '<EMAIL>');
        $I->assertTrue(!empty($results->data[0]->phone) && $results->data[0]->phone === 'phone-to-show');
    }

    /** @group database_transaction */
    public function testLegacyListRetailerCustomerLimitedVisibilityShownSecondary(RestTester $I)
    {
        $I->wantTo("test - retailer customer legacy list endpoint - limited visibility - shown secondary");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityShownSecondary');

        $results = $I->doDirectGet($I, 'retailer-customers');


        $I->assertTrue(!empty($results->data[0]->link_to_rep) && $results->data[0]->link_to_rep === RetailerCustomer::LINK_TO_REP_SECONDARY);
        $I->assertTrue(!empty($results->data[0]->email) && $results->data[0]->email === '<EMAIL>');
        $I->assertTrue(!empty($results->data[0]->phone) && $results->data[0]->phone === 'phone-to-show');
    }

    /** @group database_transaction */
    public function testLegacyListRetailerCustomerLimitedVisibilityObfuscated(RestTester $I)
    {
        $I->wantTo("test - retailer customer legacy list endpoint - limited visibility - obfuscated");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityObfuscated');

        $results = $I->doDirectGet($I, 'retailer-customers');


        $I->assertTrue(empty($results->data[0]->link_to_rep));
        $I->assertTrue(!empty($results->data[0]->email) && preg_match('/^([\*]+@[\*]+\.[\*]+)$/', $results->data[0]->email));
        $I->assertTrue(!empty($results->data[0]->phone) && preg_match('/^\+?[\*]+$/', $results->data[0]->phone));
    }

    /** @group database_transaction */
    public function testLegacyListRetailerCustomerLimitedVisibilityShownDefault(RestTester $I)
    {
        $I->wantTo("test - retailer customer legacy list endpoint - limited visibility - shown default");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityShownDefault');

        $results = $I->doDirectGet($I, 'retailer-customers');


        $I->assertTrue(!empty($results->data[0]->email) && $results->data[0]->email === '<EMAIL>');
        $I->assertTrue(!empty($results->data[0]->phone) && $results->data[0]->phone === 'phone-to-show-by-default');
    }
    /* V2 one */

    /** @group database_transaction */
    public function testRetailerCustomerLimitedVisibilityShownPrimary(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - limited visibility - shown primary");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityShownPrimary');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1');


        $I->assertTrue(!empty($results->data->attributes->virtual_fields->link_to_rep) && $results->data->attributes->virtual_fields->link_to_rep === RetailerCustomer::LINK_TO_REP_PRIMARY);
        $I->assertTrue(!empty($results->data->attributes->email) && $results->data->attributes->email === '<EMAIL>');
        $I->assertTrue(!empty($results->data->attributes->phone) && $results->data->attributes->phone === 'phone-to-show');
    }

    /** @group database_transaction */
    public function testRetailerCustomerLimitedVisibilityShownSecondary(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - limited visibility - shown secondary");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityShownSecondary');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1');


        $I->assertTrue(
            !empty($results->data->attributes->virtual_fields->link_to_rep) &&
            $results->data->attributes->virtual_fields->link_to_rep === RetailerCustomer::LINK_TO_REP_SECONDARY
        );
        $I->assertTrue(
            !empty($results->data->attributes->email) &&
            $results->data->attributes->email === '<EMAIL>'
        );
        $I->assertTrue(
            !empty($results->data->attributes->phone) &&
            $results->data->attributes->phone === 'phone-to-show'
        );
    }

    /** @group database_transaction */
    public function testRetailerCustomerLimitedVisibilityObfuscated(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - limited visibility - obfuscated");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityObfuscated');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1');


        $I->assertTrue(empty($results->data->attributes->virtual_fields->link_to_rep));
        $I->assertTrue(
            !empty($results->data->attributes->email) &&
            preg_match('/^([\*]+@[\*]+\.[\*]+)$/', $results->data->attributes->email)
        );
        $I->assertTrue(
            !empty($results->data->attributes->phone) &&
            preg_match('/^\+?[\*]+$/', $results->data->attributes->phone)
        );
    }

    /** @group database_transaction */
    public function testRetailerCustomerLimitedVisibilityShownDefault(RestTester $I)
    {
        $I->wantTo("test - retailer customer json api endpoint - limited visibility - shown default");

        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'CustomerLimitedVisibilityShownDefault');

        $results = $I->doDirectGet($I, 'v2/retailer-customers/1');


        $I->assertTrue(
            !empty($results->data->attributes->email)
            && $results->data->attributes->email === '<EMAIL>'
        );
        $I->assertTrue(
            !empty($results->data->attributes->phone)
            && $results->data->attributes->phone === 'phone-to-show-by-default'
        );
    }

    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsWithSubscriptionStatusAreNull(RestTester $I)
    {
        $I->wantTo('test retailer customer was added to my contacts with subscription status are null');

        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;
        $this->app['configs']['retailer.add_customer_to_my_contacts.default_subscription_status'] =
            Customer::SUBSCRIPTION_STATUS_UNSUBSCRIBED;

        $userId = 1;
        $userName = 'reggie';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $this->insertFixtureGroup($I, 'RetailerCustomers');
        $retailerCustomerWithSubscriptionStatusNull = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf3456789',
                ],
            ],
        ];

        $contact = $I->doDirectPost(
            $I,
            "v2/retailer-customers/add-to-my-contacts",
            $retailerCustomerWithSubscriptionStatusNull
        );

        $I->assertEquals(
            '<EMAIL>',
            $contact->data->attributes->email
        );

        $I->assertEquals(
            Customer::SUBSCRIPTION_STATUS_UNSUBSCRIBED,
            $contact->data->attributes->subcribtion_flag
        );

        $I->assertEquals(
            Customer::SUBSCRIPTION_STATUS_UNSUBSCRIBED,
            $contact->data->attributes->sms_marketing_subscription_flag
        );

        $I->seeInDatabase('sf_customer', [
            'ID' => $contact->data->id,
            'email' => '<EMAIL>',
            'origin' => Customer::ORIGIN_REP_ADD_TO_CONTACT,
            'subcribtion_flag' => Customer::SUBSCRIPTION_STATUS_UNSUBSCRIBED,
            'sms_marketing_subscription_flag' => Customer::SUBSCRIPTION_STATUS_UNSUBSCRIBED,
        ]);
    }

    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsWithSubscriptionStatusAreNotNull(RestTester $I)
    {
        $I->wantTo('test retailer customer was added to my contacts with subscription status are not null');

        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;

        $userId = 1;
        $userName = 'reggie';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $this->insertFixtureGroup($I, 'RetailerCustomers');
        $retailerCustomerWithSubscriptionStatusNotNull = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf2345678',
                ],
            ],
        ];

        $contact = $I->doDirectPost(
            $I,
            "v2/retailer-customers/add-to-my-contacts",
            $retailerCustomerWithSubscriptionStatusNotNull
        );

        $I->assertEquals(
            '<EMAIL>',
            $contact->data->attributes->email
        );

        $I->assertEquals(
            Customer::SUBSCRIPTION_STATUS_SUBSCRIBED,
            $contact->data->attributes->subcribtion_flag
        );

        $I->assertEquals(
            Customer::SUBSCRIPTION_STATUS_SUBSCRIBED,
            $contact->data->attributes->sms_marketing_subscription_flag
        );

        $I->seeInDatabase('sf_customer', [
            'ID' => $contact->data->id,
            'email' => '<EMAIL>',
            'origin' => Customer::ORIGIN_REP_ADD_TO_CONTACT,
            'subcribtion_flag' => Customer::SUBSCRIPTION_STATUS_SUBSCRIBED,
            'sms_marketing_subscription_flag' => Customer::SUBSCRIPTION_STATUS_SUBSCRIBED,
        ]);
    }

    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsAsSecondaryTrue(RestTester $I)
    {
        $I->wantTo('test retailer customer was added to my contacts as secondary true');

        $this->app['configs']['retailer.clienteling.mode'] = true;
        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = false;
        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;
        $this->app['configs']['retailer.add_customer_to_my_contacts.as_secondary'] = true;

        $userId = 1;
        $userName = 'reggie';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $this->insertFixtureGroup($I, 'RetailerCustomers');
        $params_limited_visibility_0 = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf1234511',
                ],
            ],
        ];
        $params_limited_visibility_1 = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf0012345',
                ],
            ],
        ];


        $contact = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_0);
        $I->assertEquals(
            null,
            $contact->data->attributes->retailer_customer_id
        );
        $I->assertEquals(
            $params_limited_visibility_0["data"]["attributes"]["customer_id"],
            $contact->data->attributes->retailer_parent_customer_id
        );

        $I->seeInDatabase('sf_customer', [
            'ID' => $contact->data->id,
            'email' => '<EMAIL>',
            'origin' => Customer::ORIGIN_REP_ADD_TO_CONTACT,
            'subcribtion_flag' => 1,
            'sms_marketing_subscription_flag' => 1,
        ]);

        $I->seeInDatabase('sf_events', [
            'type' => Event::SF_EVENT_USER_ADD,
            'source' => 'contact_management',
            'user_id' => $userId,
            'customer_id' => $contact->data->id,
            'attributes' => 1,
        ]);

        $I->seeInDatabase('sf_customers_to_customer_attributes', [
            'customer_id' => $contact->data->id,
            'attribute_id' => 123,
            'attribute_value' => 160,
        ]);

        // We check we can't add the same retailer customer twice to my contacts.
        $error = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_0);
        $I->assertEquals(
            "This customer is already linked to a representative.",
            $error->errors[0]->meta->exception_message
        );

        // We check we can't add a customer with limited_visibility flag set to 1.
        $contact = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_1);
        $I->assertEquals(
            $params_limited_visibility_1["data"]["attributes"]["customer_id"],
            $contact->data->attributes->retailer_parent_customer_id
        );

        $I->seeInDatabase('sf_customers_to_retailer_customers', [
            'comment' => 'manual:add_to_contacts'
        ]);
    }

    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsAsSecondaryFalse(RestTester $I)
    {
        $I->wantTo('test retailer customer was added to my contacts as secondary false');

        $this->app['configs']['retailer.clienteling.mode'] = true;
        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = false;
        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;
        $this->app['configs']['retailer.add_customer_to_my_contacts.as_secondary'] = false;

        $userId = 115;
        $userName = 'reggie';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $this->insertFixtureGroup($I, 'RetailerCustomers');
        $params_limited_visibility_0 = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf1234511',
                ],
            ],
        ];
        $params_limited_visibility_1 = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf0012345',
                ],
            ],
        ];


        $contact = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_0);
        $I->assertEquals(
            null,
            $contact->data->attributes->retailer_customer_id
        );
        $I->assertEquals(
            null,
            $contact->data->attributes->retailer_parent_customer_id
        );

        $I->seeInDatabase('sf_customer', [
            'ID' => $contact->data->id,
            'email' => '<EMAIL>',
        ]);

        $my_contacts = $I->doDirectGet(
            $I,
            '/v1/customers?per_page=2&page=&filter[user_id]=' . $userId . '&fields=count_tasks&sf_locale=en_US'
        );
        $I->assertCount(1, $my_contacts->data);

        // We check we can't add the same retailer customer twice to my contacts.
        $error = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_0);
        $I->assertEquals(
            "This customer is already linked to a representative.",
            $error->errors[0]->meta->exception_message
        );
        $my_contacts = $I->doDirectGet(
            $I,
            '/v1/customers?per_page=2&page=&filter[user_id]=' . $userId . '&fields=count_tasks&sf_locale=en_US'
        );
        $I->assertCount(1, $my_contacts->data);

        // We check we can't add a customer with limited_visibility flag set to 1.
        $contact = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_1);
        $I->assertEquals(
            null,
            $contact->data->attributes->retailer_customer_id
        );
        $my_contacts = $I->doDirectGet(
            $I,
            '/v1/customers?per_page=2&page=filter[user_id]=' . $userId . '&fields=count_tasks&sf_locale=en_US'
        );
        $I->assertCount(2, $my_contacts->data);
        $I->seeInDatabase('sf_customers_to_retailer_customers', [
            'comment' => 'manual:add_to_contacts'
        ]);
    }


    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsWithLimitedVisibilityTrue(RestTester $I)
    {
        $I->wantTo('test retailer customer was added to my contacts only if limited_visibility is 0');

        $this->app['configs']['retailer.clienteling.mode'] = true;
        $this->app['configs']['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;
        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;
        $this->app['configs']['retailer.add_customer_to_my_contacts.as_secondary'] = true;

        $userId = 1;
        $userName = 'reggie';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $this->insertFixtureGroup($I, 'RetailerCustomers');
        $params_limited_visibility_0 = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf1234511',
                ],
            ],
        ];
        $params_limited_visibility_1 = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf0012345',
                ],
            ],
        ];


        // We check we can't add a customer with limited_visibility flag set to 0.
        $contact = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_0);
        $I->assertEquals(
            null,
            $contact->data->attributes->retailer_customer_id
        );

        // We check we can't add a customer with limited_visibility flag set to 1.
        $error = $I->doDirectPost($I, "v2/retailer-customers/add-to-my-contacts", $params_limited_visibility_1);
        $I->assertEquals(
            "This customer has limited visibility flag set to true.",
            $error->errors[0]->meta->exception_message
        );
    }

    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsWithPhoneDifferentCountry(RestTester $I)
    {
        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'RetailerCustomers');

        $retailerCustomerWithSubscriptionStatusNotNull = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf123451151436',
                ],
            ],
        ];

        $contact = $I->doDirectPost(
            $I,
            "v2/retailer-customers/add-to-my-contacts",
            $retailerCustomerWithSubscriptionStatusNotNull
        );

        $this->validateArray([
            'data' => [
                'attributes' => [
                    'email' => '<EMAIL>',
                    'phone' => '+19722555400',
                ]
            ]
        ], $contact);

        $I->seeInDatabase('sf_customer', [
           'user_id' => 1,
           'email' => '<EMAIL>',
           'phone' => '+19722555400',
        ]);
    }

    /** @group database_transaction */
    public function testRetailerCustomerAddedToMyContactsWithPhoneSameCountry(RestTester $I)
    {
        $this->app['configs']['retailer.add_customer_to_my_contacts.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'RetailerCustomers');

        $retailerCustomerWithSubscriptionStatusNotNull = [
            "data" => [
                "type" => "retailer-customer",
                "attributes" => [
                    "customer_id" => 'sf1234511',
                ],
            ],
        ];

        $contact = $I->doDirectPost(
            $I,
            "v2/retailer-customers/add-to-my-contacts",
            $retailerCustomerWithSubscriptionStatusNotNull
        );

        $this->validateArray([
            'data' => [
                'attributes' => [
                    'email' => '<EMAIL>',
                    'phone' => '+15144321222',
                ]
            ]
        ], $contact);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1,
            'email' => '<EMAIL>',
            'phone' => '+15144321222',
        ]);
    }
}
