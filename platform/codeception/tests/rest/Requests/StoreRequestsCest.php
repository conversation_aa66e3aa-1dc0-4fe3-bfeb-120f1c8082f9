<?php

declare(strict_types=1);

namespace SF\rest\Requests;

use Salesfloor\API\Managers\Services\Appointments as AppointmentsManager;
use Salesfloor\Models\Message;
use Salesfloor\Models\Requests\Request;
use Salesfloor\Schemas\Customer as CustomerSchema;
use Salesfloor\Schemas\Rep as RepSchema;
use Salesfloor\Schemas\Store as StoreSchema;
use SF\rest\BaseRest;
use SF\RestTester;

class StoreRequestsCest extends BaseRest
{
    /** @group database_transaction */
    public function testGetUserStoreRequests(RestTester $I)
    {
        $I->wantTo("Test GetStoreRequests");

        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        $response = $I->doDirectGet($I, '/v2/requests?include=rep,rep.store,customer,appointment,personal-shopper,question&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(4, $response->meta->total);

        // Check that the virtual fields are set
        foreach ($response->data as $request) {
            $I->assertTrue(isset($request->attributes->virtual_fields->display_status));
            $I->assertTrue(isset($request->attributes->virtual_fields->display_sender_name));
            $I->assertTrue(isset($request->attributes->virtual_fields->message_count));
        }

        // Check that the included objects are present
        $isCustomerIncluded = false;
        $isRepIncluded = false;
        $isStoreIncluded = false;
        foreach ($response->included as $included) {
            switch ($included->type) {
                case CustomerSchema::TYPE:
                    $isCustomerIncluded = true;
                    break;
                case RepSchema::TYPE:
                    $isRepIncluded = true;
                    break;
                case StoreSchema::TYPE:
                    $isStoreIncluded = true;
                    break;
            }
        }

        $I->assertEquals(true, $isCustomerIncluded);
        $I->assertEquals(true, $isRepIncluded);
        $I->assertEquals(true, $isStoreIncluded);
    }

    /** @group database_transaction */
    public function testGetManagerStoreRequestsAll(RestTester $I)
    {
        $I->wantTo("Test GetStoreRequests (manager)");

        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        // Test corpo admin access
        $userId = 333;
        $userName = 'user_corpo_admin';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, '/v2/managers/requests?include=rep,rep.store,customer,appointment,personal-shopper,question&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(8, $response->meta->total);

        // Check that the virtual fields are set
        foreach ($response->data as $request) {
            $I->assertTrue(isset($request->attributes->virtual_fields->display_status));
            $I->assertTrue(isset($request->attributes->virtual_fields->display_sender_name));
            $I->assertTrue(isset($request->attributes->virtual_fields->message_count));
            // Customer data validation for values/virtual fields
            // We probably can move this to the getOne call later
            switch ($request->attributes->request_type) {
                case Message::CUSTOMER_REQUEST_BOOK_APPOINTMENT:
                    if ($request->attribute->request_id == 4) {
                        // Validate deleted customer case
                        $I->assertEquals(AppointmentsManager::APPOINTMENT_STATUS_CONFIRMED, $request->attributes->virtual_fields->display_status);
                        $I->assertEquals('Customer deleted', $request->attributes->virtual_fields->display_sender_name);
                        $I->assertEquals('<EMAIL>', $request->attributes->virtual_fields->rep_display_name);
                        $I->assertEquals(1, $request->attributes->virtual_fields->message_count);
                    }
                    break;
                case Message::CUSTOMER_REQUEST_CONTACT_ME:
                    if ($request->attribute->request_id == 6) {
                        // Validate pending status
                        $I->assertEquals(Request::DISPLAY_STATUS_PENDING, $request->attributes->virtual_fields->display_status);
                        $I->assertEquals('<EMAIL>', $request->attributes->virtual_fields->display_sender_name);
                        $I->assertEquals('<EMAIL>', $request->attributes->virtual_fields->rep_display_name);
                        $I->assertEquals(2, $request->attributes->virtual_fields->message_count);
                    }
                    break;
            }
        }

        // Check that the included objects are present
        $isCustomerIncluded = false;
        $isRepIncluded = false;
        $isStoreIncluded = false;
        foreach ($response->included as $included) {
            switch ($included->type) {
                case CustomerSchema::TYPE:
                    $isCustomerIncluded = true;
                    break;
                case RepSchema::TYPE:
                    $isRepIncluded = true;
                    break;
                case StoreSchema::TYPE:
                    $isStoreIncluded = true;
                    break;
            }
        }

        $I->assertEquals(true, $isCustomerIncluded);
        $I->assertEquals(true, $isRepIncluded);
        $I->assertEquals(true, $isStoreIncluded);
    }

    /** @group database_transaction */
    public function testGetManagerStoreRequestsWithoutInclude(RestTester $I)
    {
        $I->wantTo("Test GetStoreRequests without includes (virtual fields must still be generated properly)");

        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        // Test corpo admin access
        $userId = 333;
        $userName = 'user_corpo_admin';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, '/v2/managers/requests?page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(8, $response->meta->total);

        // Check that the virtual fields are set
        foreach ($response->data as $request) {
            $I->assertTrue(isset($request->attributes->virtual_fields->display_status));
            $I->assertTrue(isset($request->attributes->virtual_fields->display_sender_name));
            $I->assertTrue(isset($request->attributes->virtual_fields->message_count));
        }
    }

    /** @group database_transaction */
    public function testGetManagerStoreRequestsWithFilters(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        // Test corpo admin access
        $userId = 333;
        $userName = 'user_corpo_admin';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        // Filter rep IDs
        $I->wantTo('Test GetStoreRequests user_ids filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[user_ids]=336&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);

        // Filter store IDs
        $I->wantTo('Test GetStoreRequests store_ids filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[store_ids]=1031&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(4, $response->meta->total);

        // Filter status resolved
        $I->wantTo('Test GetStoreRequests status=resolved filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[status]=resolved&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);

        // Filter status unresolved
        $I->wantTo('Test GetStoreRequests status=^resolved filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[status]=^resolved&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(5, $response->meta->total);

        // Filter search
        $I->wantTo('Test GetStoreRequests search filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[q]=Demande&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);

        // Filter type appointment
        $I->wantTo('Test GetStoreRequests request_type=book_appointment filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[request_type]=book_appointment&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);

        // Filter type contact_me (question)
        $I->wantTo('Test GetStoreRequests request_type=contact_me filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[request_type]=contact_me&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);

        // Filter type personal_shopper
        $I->wantTo('Test GetStoreRequests request_type=personal_shopper filter');
        $response = $I->doDirectGet($I, '/v2/managers/requests?filter[request_type]=personal_shopper&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(2, $response->meta->total);
    }

    // THIS IS NOT WORKING
    // @TODO: Fix the route and system for the get one
    //    public function testGetRequestAppointment(RestTester $I)
//    {
//        $I->wantTo("Test GetRequestAppointment");
//
//        $this->insertFixtureGroup($I, 'appointments_fixture_group');
//        $this->insertFixtureGroup($I, 'users');
//
//        $response = $I->doDirectGet($I, '/v2/requests/1?include=rep,rep.store,customer,appointment');
//
//        $this->debug($response);
//
//        $I->assertEquals(RequestSchema::TYPE, $response->data->type);
//
//        $isAppointmentIncluded = false;
//        $isCustomerIncluded = false;
//        $isRepIncluded = false;
//        $isStoreIncluded = false;
//        foreach ($response->included as $included) {
//            switch ($included->type) {
//                case AppointmentSchema::TYPE:
//                    $isAppointmentIncluded = true;
//                    break;
//                case CustomerSchema::TYPE:
//                    $isCustomerIncluded = true;
//                    break;
//                case RepSchema::TYPE:
//                    $isRepIncluded = true;
//                    break;
//                case StoreSchema::TYPE:
//                    $isStoreIncluded = true;
//                    break;
//            }
//        }
//
//        $I->assertEquals(true, $isAppointmentIncluded);
//        $I->assertEquals(true, $isCustomerIncluded);
//        $I->assertEquals(true, $isRepIncluded);
//        $I->assertEquals(true, $isStoreIncluded);
//    }
//
//    public function testGetRequestPersonalShopper(RestTester $I)
//    {
//        $I->wantTo("Test GetRequestPersonalShopper");
//
//        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
//        $this->insertFixtureGroup($I, 'users');
//
//        $response = $I->doDirectGet($I, '/v2/requests/1?include=rep,rep.store,customer,personal-shopper');
//
//        $this->debug($response);
//
//        $I->assertEquals(RequestSchema::TYPE, $response->data->type);
//
//        $isPersonalShopperIncluded = false;
//        $isCustomerIncluded = false;
//        $isRepIncluded = false;
//        $isStoreIncluded = false;
//        foreach ($response->included as $included) {
//            switch ($included->type) {
//                case PersonalShopperSchema::TYPE:
//                    $isPersonalShopperIncluded = true;
//                    break;
//                case CustomerSchema::TYPE:
//                    $isCustomerIncluded = true;
//                    break;
//                case RepSchema::TYPE:
//                    $isRepIncluded = true;
//                    break;
//                case StoreSchema::TYPE:
//                    $isStoreIncluded = true;
//                    break;
//            }
//        }
//
//        $I->assertEquals(true, $isPersonalShopperIncluded);
//        $I->assertEquals(true, $isCustomerIncluded);
//        $I->assertEquals(true, $isRepIncluded);
//        $I->assertEquals(true, $isStoreIncluded);
//    }
//
//    public function testGetRequestQuestion(RestTester $I)
//    {
//        $I->wantTo("Test GetRequestQuestion");
//
//        $this->insertFixtureGroup($I, 'question_fixture_group');
//        $this->insertFixtureGroup($I, 'users');
//
//        $response = $I->doDirectGet($I, '/v2/requests/1?include=rep,rep.store,customer,question');
//
//        $this->debug($response);
//
//        $I->assertEquals(RequestSchema::TYPE, $response->data->type);
//
//        $isQuestionIncluded = false;
//        $isCustomerIncluded = false;
//        $isRepIncluded = false;
//        $isStoreIncluded = false;
//        foreach ($response->included as $included) {
//            switch ($included->type) {
//                case QuestionSchema::TYPE:
//                    $isQuestionIncluded = true;
//                    break;
//                case CustomerSchema::TYPE:
//                    $isCustomerIncluded = true;
//                    break;
//                case RepSchema::TYPE:
//                    $isRepIncluded = true;
//                    break;
//                case StoreSchema::TYPE:
//                    $isStoreIncluded = true;
//                    break;
//            }
//        }
//
//        $I->assertEquals(true, $isQuestionIncluded);
//        $I->assertEquals(true, $isCustomerIncluded);
//        $I->assertEquals(true, $isRepIncluded);
//        $I->assertEquals(true, $isStoreIncluded);
//    }
//
//    public function testGetRequestQuestionVirtualFields(RestTester $I)
//    {
//        $I->wantTo("Test GetRequestQuestionVirtualFields");
//
//        $this->insertFixtureGroup($I, 'question_fixture_group');
//        $this->insertFixtureGroup($I, 'users');
//
//        $response = $I->doDirectGet($I, '/v2/requests/6?include=rep,rep.store,customer,question');
//
//        $this->debug($response);
//
//        $I->assertTrue(!empty($response->attributes->virtual_fields));
//
//        $virtualFields = $response->attributes->virtual_fields;
//        $I->assertEquals(Request::DISPLAY_STATUS_PENDING, $virtualFields->display_status);
//    }


    protected function getResponseForVaryingConfigs(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        // Test corpo admin access
        $userId = 333;
        $userName = 'user_corpo_admin';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, '/v2/managers/requests?include=rep,rep.store,customer,appointment,personal-shopper,question&page[number]=0&page[size]=10');

        return $response;
    }

    protected function assertRelationNotPresent($I, $response, $relation)
    {
        foreach ($response->included as $v) {
            $I->assertNotEquals($relation, $v->type);
        }

        foreach ($response->data as $data) {
            foreach ($data->relationships as $k => $v) {
                $I->assertNotEquals($relation, $k);
            }
        }
    }

    /** @group database_transaction */
    public function testGetManagerStoreRequestsWithoutAppointments(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.appointment_request.is_enabled'] = false;
        $this->app['configs']['retailer.has_personal_shopper'] = true;

        $I->wantTo("Test GetStoreRequests without appointments for tier 1");

        $response = $this->getResponseForVaryingConfigs($I);
        $this->assertRelationNotPresent($I, $response, 'appointment');
    }

    /** @group database_transaction */
    public function testGetManagerStoreRequestsWithoutPersonalShopper(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.appointment_request.is_enabled'] = true;
        $this->app['configs']['retailer.has_personal_shopper'] = false;

        $I->wantTo("Test GetStoreRequests without personal shopper for tier 1");

        $response = $this->getResponseForVaryingConfigs($I);
        $this->assertRelationNotPresent($I, $response, 'personal-shopper');
    }

    /** @group database_transaction */
    public function testGetManagerStoreRequestsWithoutAppointmentsOrPersonalShopper(RestTester $I)
    {
        $this->app['configs']['retailer.modular_connect.appointment_request.is_enabled'] = false;
        $this->app['configs']['retailer.has_personal_shopper'] = false;

        $I->wantTo("Test GetStoreRequests without appointments or personal shopper for tier 1");

        $response = $this->getResponseForVaryingConfigs($I);
        $this->assertRelationNotPresent($I, $response, 'appointment');
        $this->assertRelationNotPresent($I, $response, 'personal-shopper');
    }
}
