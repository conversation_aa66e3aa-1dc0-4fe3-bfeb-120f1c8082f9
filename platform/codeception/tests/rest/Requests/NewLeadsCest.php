<?php

declare(strict_types=1);

namespace SF\rest\Requests;

use Salesfloor\Models\Requests\Request;
use Salesfloor\Schemas\Customer as CustomerSchema;
use Codeception\Util\HttpCode;
use SF\rest\BaseRest;
use SF\RestTester;

class NewLeadsCest extends BaseRest
{
    /** @group database_transaction */
    public function testGetUserNewLeadsAll(RestTester $I)
    {
        $I->wantTo("Test GetNewLeads");

        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        $response = $I->doDirectGet($I, '/v2/requests/new-leads?include=customer,appointment,personal-shopper,question&page[number]=0&page[size]=10');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);
        $I->assertEquals(3, count($response->data));

        // Check that the virtual fields are set
        foreach ($response->data as $request) {
            $I->assertTrue(isset($request->attributes->virtual_fields->display_status));
            $I->assertTrue(isset($request->attributes->virtual_fields->display_sender_name));
            $I->assertTrue(isset($request->attributes->virtual_fields->message_count));
        }

        // Check that the included objects are present
        $isCustomerIncluded = false;
        foreach ($response->included as $included) {
            switch ($included->type) {
                case CustomerSchema::TYPE:
                    $isCustomerIncluded = true;
                    break;
            }
        }

        $I->assertEquals(true, $isCustomerIncluded);
    }

    /** @group database_transaction */
    public function testGetUserNewLeadsFilterSortPaging(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');

        // Test paging
        $I->wantTo('Test GetNewLeads paging');
        $response = $I->doDirectGet($I, '/v2/requests/new-leads?page[number]=1&page[size]=1');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);
        $I->assertEquals(3, $response->meta->pages);

        // Should get 1 result / the second one
        $I->assertEquals(Request::CUSTOMER_REQUEST_CONTACT_ME, $response->data[0]->attributes->request_type);
        $I->assertEquals(10001, $response->data[0]->attributes->request_id);

        // Test sort
        $I->wantTo('Test GetNewLeads paging');
        $response = $I->doDirectGet($I, '/v2/requests/new-leads?sort=-date');

        $this->debug($response);

        $I->assertEquals(3, $response->meta->total);
        $I->assertEquals(1, $response->meta->pages);

        // Should get 1 result / the second one
        $I->assertEquals(Request::CUSTOMER_REQUEST_BOOK_APPOINTMENT, $response->data[0]->attributes->request_type);
        $I->assertEquals(10001, $response->data[0]->attributes->request_id);
        $I->assertEquals(Request::CUSTOMER_REQUEST_CONTACT_ME, $response->data[1]->attributes->request_type);
        $I->assertEquals(10001, $response->data[1]->attributes->request_id);
        $I->assertEquals(Request::CUSTOMER_REQUEST_PERSONAL_SHOPPER, $response->data[2]->attributes->request_type);
        $I->assertEquals(10001, $response->data[2]->attributes->request_id);

        // Test filter request_type
        $I->wantTo('Test GetNewLeads filter request_type');
        $response = $I->doDirectGet($I, '/v2/requests/new-leads?filter[request_type]=book_appointment');

        $this->debug($response);

        $I->assertEquals(1, $response->meta->total);
        $I->assertEquals(1, $response->meta->pages);

        // Should get 1 result / the second one
        $I->assertEquals(Request::CUSTOMER_REQUEST_BOOK_APPOINTMENT, $response->data[0]->attributes->request_type);
        $I->assertEquals(10001, $response->data[0]->attributes->request_id);
    }

    protected function insertFixturesToTestLeads($I)
    {
        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'personal_shopper_fixture_group');
        $this->insertFixtureGroup($I, 'question_fixture_group');
        $this->insertFixtureGroup($I, 'events_fixture_group');
    }

     /** @group database_transaction */
    public function testGetNewLeadsCountWordPressToSilex(RestTester $I)
    {
        $this->insertFixturesToTestLeads($I);

        $response = $I->doDirectGet($I, 'leads-count');
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);

        $I->assertEquals($response->count, 3);
    }

    /**
     * This method tests the transfer from WP to Silex from the Landing Page (Sidebar / Widget),
     * when the BE gives it the query string "?repId=71"
     * in services/src/PushNotifications/Service.php::getLeadsCounts()
     *
     * @param RestTester $I
     * @return void
     * @group database_transaction
     */

    public function testGetNewLeadsCountFromWidgetWithQuery(RestTester $I)
    {
        $this->insertFixturesToTestLeads($I);
        $response   = $I->doDirectGet($I, 'leads-count?repId=71');
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);
        $I->assertEquals($response->count, 3);
    }

    /**
     * This method returns the actual New Leads, and not just their count / number
     *
     * @param RestTester $I
     * @return void
     * @group database_transaction
     */
    public function testGetNewLeadsWordPressToSilex(RestTester $I)
    {
        $this->insertFixturesToTestLeads($I);
        $response = $I->doDirectGet($I, 'leads?count=5');
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);

        // See if the $response got all three service tables
        // sf_appointments, sf_questions, and sf_personal_shopper:
        $uniqIdInServiceTables = [
            'SFID5c0e983066d3f8.49958000',
            'SFID5c0e983066d3f8.49958001',
            'SFID5c0e983066d3f8.49958002',
        ];

        $bool = in_array($response[0]->request_id, $uniqIdInServiceTables);
        $I->assertEquals($bool, true);
        $bool = in_array($response[1]->request_id, $uniqIdInServiceTables);
        $I->assertEquals($bool, true);
        $bool = in_array($response[2]->request_id, $uniqIdInServiceTables);
        $I->assertEquals($bool, true);
    }

    /**
     * When a new lead is claimed by a rep,
     * the user_id in all relevant SQL tables is switched
     * from 0 to a rep's ID.
     *
     * @param RestTester $I
     * @return void
     * @group database_transaction
     */
    public function testClaimNewLeadsWordPressToSilex(RestTester $I)
    {
        $this->insertFixturesToTestLeads($I);

        // type 6 == sf_questions,
        // user_id == 0 before the request:
        $I->seeInDatabase('sf_events', [
            'ID'      => 150,
            'type'    => 6,
            'user_id' => 0,
            'source'  => '',
        ]);

        // "[requestId]=..." === SQL's "sf_events.ID":
        $response = $I->doDirectGet($I, 'leads/claim?data[dontClaim]=false&data[requestId]=150');
        $statusCode = $I->grabResponseStatusCode();
        $I->assertEquals(200, $statusCode);

        // type 27 == an added Contact,
        // user_id == 1 after the request:
        $I->seeInDatabase('sf_events', [
            'type'    => 27,
            'user_id' => 1,
            'source'  => 'contact_management',
        ]);

        // type 6 is sf_questions,
        // user_id == 1 after the request:
        $I->seeInDatabase('sf_events', [
            'type'    => 6,
            'user_id' => 1,
            'uniq_id' => 'SFID5c0e983066d3f8.49958000'
        ]);

        // Same uniq_id as in the sf_events table,
        // and the user_id has been switched from 0 to 1:
        $I->seeInDatabase('sf_questions', [
            'ID'      => 71,
            'user_id' => 1,
            'uniq_id' => 'SFID5c0e983066d3f8.49958000'
        ]);
    }

      /** @group database_transaction */
    public function testGetLeadIsObfuscatedWhenPIIisOn(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'users');
        $this->insertFixtureGroup($I, 'question_fixture_pii');

        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $response = $I->doDirectGet($I, '/leads/question/SFID5d095de3095ff9.123123');

        $I->assertEquals("jo*************@*****le.com", $response->messages->storeRequest->sender->email);
        $I->assertEquals("61*-***-**43", $response->messages->storeRequest->sender->phone);
    }
}
