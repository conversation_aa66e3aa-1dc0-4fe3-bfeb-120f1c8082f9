<?php

declare(strict_types=1);

namespace SF\rest\Requests;

use Codeception\Util\Fixtures;
use Salesfloor\Models\Message;

class Fixture
{
    private function mockupUsers()
    {
        return [
            'sf_store' => [
                [
                    'store_id'          => 1031,
                    'name'              => 'test store',
                    'timezone'          => 'America/Los_Angeles',
                    'sf_identifier'     => 'store-one',
                    'store_user_id'     => 335,
                    'retailer_store_id' => 'ny1',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ],
            ],
            'wp_users' => [
                [
                    'ID'                  => 71,
                    'user_login'          => 'regginito',
                    'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_activation_key' => '$P$BeVBLo6ClVmamoRr7hudBZEVOh9jPb/',
                    'user_alias'          => 'regino',
                    'user_nicename'       => 'reggito',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'rex_reggie',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
                [
                    'ID'                  => 333,
                    'user_login'          => 'user_corpo_admin',
                    'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename'       => 'user_corpo_admin',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'user_corpo_admin',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1031,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 5,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
                [
                    'ID'                  => 335,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1031,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
                [
                    'ID'                  => 336,
                    'user_login'          => 'testuser336',
                    'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename'       => 'testuser336',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser336',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1031,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
        ];
    }

    private function mockupAppointments()
    {
        return [
            'sf_appointments' => [
                [
                    'ID'                  => 1,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => '2018-12-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* with request status of unresolved, message status of unsolved  */
                    'ID'                  => 2,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => date('Y-m-d H:i:s'),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* with request status of resolved  */
                    'ID'                  => 3,
                    'customer_id'         => 12,
                    'user_id'             => 336,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date' => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* Past event - deleted customer */
                    'ID'                  => 4,
                    'customer_id'         => 217632763, // Customer does not exist / deleted
                    'user_id'             => 336,
                    'event_type'          => 'In-Store',
                    'event_duration'      => 60,
                    'date'                => '2019-03-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1031,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* Past appointment */
                    'ID'                  => 9001,
                    'customer_id'         => 1,
                    'user_id'             => 336,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => '2018-12-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1031,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* Future appointment unclaimed */
                    'ID'                  => 10001,
                    'customer_id'         => 1,
                    'user_id'             => 0,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate('Y-m-d H:i:s', strtotime('+1 day')),
                    'status'              => 'pending',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate('Y-m-d H:i:s', strtotime('-1 day')),
                    'enddate'             => gmdate('Y-m-d H:i:s', strtotime('+1 day 30 minutes')),
                    'phone'               => '',
                    'store_id'            => 1031,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
            ],
            'sf_messages' => [
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 2,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 336,
                    'owner_id'      => 336,
                    'customer_id'   => 2,
                    'thread_id'     => 17,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 3,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 336,
                    'owner_id'      => 336,
                    'customer_id'   => 217632763, // Customer does not exist / deleted
                    'thread_id'     => 18,
                    'from_type'     => Message::FROM_TYPE_CUSTOMER,
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => Message::CUSTOMER_REQUEST_BOOK_APPOINTMENT,
                    'request_id'    => 4,
                    'attachment'    => '',
                    'type'          => Message::TYPE_CUSTOMERREQUEST,
                    'date'          => '2019-02-04 19:11:11',
                    'status'        => Message::STATUS_RESOLVED,
                    'category'      => Message::CATEGORY_INBOX,
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
            ],
        ];
    }

    private function mockupPersonalShopper()
    {
        return [
            'geoip_location' => [
                [
                    'id'              => 1,
                    'country_code'    => 'CA',
                    'region'          => 'QC',
                    'city'            => 'Montreal',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-10 16:45:36',
                ],
                [
                    'id'              => 2,
                    'country_code'    => 'US',
                    'region'          => 'AL',
                    'city'            => 'SFID5c0e983066d3f8.49953381',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-31 16:45:36',
                ],
            ],
            'sf_personal_shopper' => [
                [ // status = pending
                    'ID'                  => 1,
                    'customer_id'         => 12,
                    'user_id'             => 115,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'shoes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => '',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 2,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'clothes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => '',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 3,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'shoes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => '',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 4,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'clothes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => '',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [ // Unclaimed
                    'ID'                  => 10001,
                    'customer_id'         => 12,
                    'user_id'             => 0,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'clothes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => '',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate('Y-m-d H:i:s', strtotime('-5 day')),
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 1031,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
            ],
            'sf_messages' => [
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'personal_shopper',
                    'request_id'    => 2,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Personal Shopper',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'personal_shopper',
                    'request_id'    => 3,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'resolved',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Personal Shopper',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
            ]
        ];
    }

    private function mockupQuestions()
    {
        return [
            'geoip_location' => [
                [
                    'id'              => 3,
                    'country_code'    => 'CA',
                    'region'          => 'QC',
                    'city'            => 'Montreal',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-10 16:45:36',
                ],
                [
                    'id'              => 4,
                    'country_code'    => 'US',
                    'region'          => 'AL',
                    'city'            => 'SFID5c0e983066d3f8.49953381',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-31 16:45:36',
                ],
            ],
            'sf_questions' => [
                [
                    'ID'              => 1,
                    'customer_id'     => 12,
                    'user_id'         => 115,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My easy first question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 2,
                    'customer_id'     => 12,
                    'user_id'         => 115,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My easy first question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 3,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My second tricky question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 4,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My third and most important question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 5,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My third and most important question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                // QUESTION-6: Should have a display_status = pending
                [
                    'ID'              => 6,
                    'customer_id'     => 0,
                    'user_id'         => 333,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'Test pending',
                    'topic'           => 'Sales',
                    'timezone'        => 'UTC',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '+15148127108',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'text',
                    'source'          => 'sidebar',
                    'chat_handoff'    => 0,
                ],
                [ // Unclaimed
                    'ID'              => 10001,
                    'customer_id'     => 12,
                    'user_id'         => 0,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'Test pending',
                    'topic'           => 'Sales',
                    'timezone'        => 'UTC',
                    'creation'        => gmdate('Y-m-d H:i:s', strtotime('-3 day')),
                    'phone'           => '+15148127108',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'text',
                    'source'          => 'sidebar',
                    'chat_handoff'    => 0,
                ],
            ],
            'sf_messages' => [
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'contact_me',
                    'request_id'    => 2,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Question',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 336,
                    'owner_id'      => 336,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'contact_me',
                    'request_id'    => 3,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'resolved',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Question',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                // QUESTION-6: Messages
                [
                    'user_id'       => 333,
                    'owner_id'      => 333,
                    'customer_id'   => 0,
                    'thread_id'     => 17,
                    'from_type'     => 'customer',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary',
                    'request_type'  => 'contact_me',
                    'request_id'    => 6,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'read',
                    'category'      => 'inbox',
                    'message'       => '22',
                    'title'         => 'Contact Me Request',
                    'products'      => '',
                    'comment'       => 'Trying to see if the store is open for returns and shopping',
                    'locale'        => 'en_US'
                ],
                [
                    'user_id'       => 333,
                    'owner_id'      => 333,
                    'customer_id'   => -1,
                    'thread_id'     => 18,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Rep',
                    'request_type'  => 'contact_me',
                    'request_id'    => 6,
                    'attachment'    => '',
                    'type'          => 'message',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'read',
                    'category'      => 'sent',
                    'message'       => 'You replied to this request via SMS',
                    'title'         => 'system-message',
                    'products'      => '',
                    'comment'       => null,
                    'locale'        => 'en_US'
                ],
            ],
            'sf_pre_customer' => [
                [
                    'name' => 'Mary',
                    'email' => '<EMAIL>',
                    'phone' => '+15148127108',
                    'user_id' => '0',
                    'customer_id' => null,
                    'origin' => 'widget-email',
                    'source' => 'sf_questions',
                    'source_id' => 6,
                    'locale' => 'en_US',
                ]
            ]
        ];
    }

    private function mockupEvents()
    {
        return [
            'sf_customer' => [
                [
                    'ID'                   => 71,
                    'user_id'              => 0,
                    'email'                => '<EMAIL>',
                    'name'                 => 'My Name',
                    'phone'                => '+15145550000',
                    'localization'         => 'en',
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'first_name'           => 'My',
                    'last_name'            => 'Name',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                    'origin'               => 'unknown',
                    'locale'               => 'en_US',
                ],
            ],
            'sf_questions' => [
                [
                    'ID'              => 71,
                    'customer_id'     => 71,
                    'user_id'         => 0,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49958000',
                    'store_id'        => 1003,
                    'channel'         => 'email',
                    'question'        => '',
                ],
            ],
            'sf_appointments' => [
                [
                    'ID'              => 71,
                    'customer_id'     => 71,
                    'user_id'         => 0,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49958001',
                    'store_id'        => 1003,
                    'channel'         => 'email',
                    'event_duration'  => 60,
                    'date' => gmdate('Y-m-d H:i:s'),
                ],
            ],
            'sf_personal_shopper' => [
                [
                    'ID'              => 71,
                    'customer_id'     => 71,
                    'user_id'         => 0,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49958002',
                    'store_id'        => 1003,
                    'channel'         => 'email',
                ],
            ],
            'sf_events' => [
                [
                    'id' => 150,
                    'type' => 6, // sf_questions
                    'uniq_id' => 'SFID5c0e983066d3f8.49958000',
                    'customer_id' => 71,
                    'attributes' => 71, // ID from sf_questions
                    'store_id' => 1003,
                    'user_id' => 0,
                ],
                [
                    'id' => 151,
                    'type' => 7, // sf_appointments
                    'uniq_id' => 'SFID5c0e983066d3f8.49958001',
                    'customer_id' => 71,
                    'attributes' => 71, // ID from sf_appointments
                    'store_id' => 1003,
                    'user_id' => 0,
                ],
                [
                    'id' => 152,
                    'type' => 8, // sf_personal_shopper
                    'uniq_id' => 'SFID5c0e983066d3f8.49958002',
                    'customer_id' => 71,
                    'attributes' => 71, // ID from sf_personal_shopper
                    'store_id' => 1003,
                    'user_id' => 0,
                ],

            ]
        ];
    }

    private function mockupQuestionsPII()
    {
        return [
            'geoip_location' => [
                [
                    'id'              => 3,
                    'country_code'    => 'CA',
                    'region'          => 'QC',
                    'city'            => 'Montreal',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-10 16:45:36',
                ],
                [
                    'id'              => 4,
                    'country_code'    => 'US',
                    'region'          => 'AL',
                    'city'            => 'SFID5c0e983066d3f8.49953381',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-31 16:45:36',
                ],
            ],
            'sf_questions' => [
                [
                    'ID'              => 1,
                    'customer_id'     => 12,
                    'user_id'         => 115,
                    'uniq_id'         => 'SFID5d095de3095ff9.123123',
                    'question'        => 'My easy first question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 2,
                    'customer_id'     => 12,
                    'user_id'         => 115,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My easy first question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 3,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My second tricky question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 4,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My third and most important question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 5,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My third and most important question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                // QUESTION-6: Should have a display_status = pending
                [
                    'ID'              => 6,
                    'customer_id'     => 0,
                    'user_id'         => 333,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'Test pending',
                    'topic'           => 'Sales',
                    'timezone'        => 'UTC',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '+15148127108',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'text',
                    'source'          => 'sidebar',
                    'chat_handoff'    => 0,
                ],
                [ // Unclaimed
                    'ID'              => 10001,
                    'customer_id'     => 12,
                    'user_id'         => 0,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'Test pending',
                    'topic'           => 'Sales',
                    'timezone'        => 'UTC',
                    'creation'        => gmdate('Y-m-d H:i:s', strtotime('-3 day')),
                    'phone'           => '+15148127108',
                    'status'          => '',
                    'loc_id'          => 3,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'text',
                    'source'          => 'sidebar',
                    'chat_handoff'    => 0,
                ],
            ],
            'sf_messages' => [
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'contact_me',
                    'request_id'    => 2,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Question',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 336,
                    'owner_id'      => 336,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'contact_me',
                    'request_id'    => 3,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'resolved',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Question',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                // QUESTION-6: Messages
                [
                    'user_id'       => 333,
                    'owner_id'      => 333,
                    'customer_id'   => 0,
                    'thread_id'     => 17,
                    'from_type'     => 'customer',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary',
                    'request_type'  => 'contact_me',
                    'request_id'    => 6,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'read',
                    'category'      => 'inbox',
                    'message'       => '22',
                    'title'         => 'Contact Me Request',
                    'products'      => '',
                    'comment'       => 'Trying to see if the store is open for returns and shopping',
                    'locale'        => 'en_US'
                ],
                [
                    'user_id'       => 333,
                    'owner_id'      => 333,
                    'customer_id'   => -1,
                    'thread_id'     => 18,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Rep',
                    'request_type'  => 'contact_me',
                    'request_id'    => 6,
                    'attachment'    => '',
                    'type'          => 'message',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'read',
                    'category'      => 'sent',
                    'message'       => 'You replied to this request via SMS',
                    'title'         => 'system-message',
                    'products'      => '',
                    'comment'       => null,
                    'locale'        => 'en_US'
                ],
            ],
            'sf_pre_customer' => [
                [
                    'name' => 'Mary',
                    'email' => '<EMAIL>',
                    'phone' => '+15148127108',
                    'user_id' => '0',
                    'customer_id' => null,
                    'origin' => 'widget-email',
                    'source' => 'sf_questions',
                    'source_id' => 6,
                    'locale' => 'en_US',
                ]
            ],
            'sf_events' => [
                [
                    'type'       => 6,
                    'date'       => '2018-08-30 14:38:08',
                    'source'     => 'storefront',
                    'uniq_id'    => 'SFID5d095de3095ff9.123123',
                    'user_id'    => 1,
                    'attributes' => '2',
                    'store_id'   => 1001,
                    'customer_id' => 12,
                ],
            ],
            'sf_customer' => [
                [
                    'ID'                     => 12,
                    'user_id'                => 3,
                    'email'                  => '<EMAIL>',
                    'name'                   => 'Jonathan Brown',
                    'phone'                  => '************',
                    'localization'           => 'en',
                    'geo'                    => '',
                    'latitude'               => null,
                    'longitude'              => null,
                    'comment'                => '',
                    'subcribtion_flag'       => 0,
                    'first_name'             => 'Jonathan',
                    'last_name'              => 'Brown',
                    'note'                   => null,
                    'created'                => '2018-07-17 17:49:13',
                    'last_modified'          => null,
                    'entity_last_modified'   => '2018-09-25 13:58:40',
                    'type'                   => 'personal',
                    'retailer_customer_id'   => null,
                    'label_email'            => null,
                    'label_phone'            => null,
                    'origin'                 => 'mobile-rep-device-import',
                    'locale'                 => null,
                    'unassigned_employee_id' => null,
                    'retailer_parent_customer_id' => '4321abc'
                ],
            ],
        ];
    }

    public function insert()
    {
        Fixtures::add('appointments_fixture_group', $this->mockupAppointments());
        Fixtures::add('personal_shopper_fixture_group', $this->mockupPersonalShopper());
        Fixtures::add('question_fixture_group', $this->mockupQuestions());
        Fixtures::add('events_fixture_group', $this->mockupEvents());
        Fixtures::add('users', $this->mockupUsers());
        Fixtures::add('question_fixture_pii', $this->mockupQuestionsPII());
    }
}
