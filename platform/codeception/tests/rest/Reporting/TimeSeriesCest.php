<?php

namespace SF\rest\Reporting;

use SF\rest\BaseRest;
use SF\RestTester;

class TimeSeriesCest extends BaseRest
{
    /**
     * @group database_transaction
     */
    public function testGetSaleTimeSeriesUserGroup1(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'usersAndStore');
        $this->insertFixtureGroup($I, 'transactions');

        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[user_ids]' => '10',
            'filter[store_ids]' => '10',
        ];

        $this->setTokenInRequestHeader($I, '10', 'tests_10');
        $results = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    /**
     * @group database_transaction
     */
    public function testGetSaleTimeSeriesUserGroup2(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'usersAndStore');
        $this->insertFixtureGroup($I, 'transactions');

        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[user_ids]' => '20,21',
            'filter[store_ids]' => '20',
        ];

        $this->setTokenInRequestHeader($I, '20', 'tests_10');
        $results = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    /**
     * @group database_transaction
     */
    public function testGetSaleTimeSeriesUserGroup3(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'usersAndStore');
        $this->insertFixtureGroup($I, 'transactions');

        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[user_ids]' => '10,20,30,40,50',
            'filter[store_ids]' => '10,20,30,40,50',
        ];

        $this->setTokenInRequestHeader($I, '30', 'tests_10');
        $results = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    /**
     * @group database_transaction
     */
    public function testGetSaleTimeSeriesAuthorizationError(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'usersAndStore');
        $this->insertFixtureGroup($I, 'transactions');

        //  user group 1: wrong store id
        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[store_ids]' => '20',
        ];
        $this->setTokenInRequestHeader($I, '10', 'tests_10');
        $result = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $I->assertEquals('403', $result->errors[0]->code);

        // user group 1: wrong user id
        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[user_ids]' => '20',
        ];

        $this->setTokenInRequestHeader($I, '10', 'tests_10');
        $result = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $I->assertEquals('403', $result->errors[0]->code);

        //  user group 2: wrong store id
        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[store_ids]' => '10',
        ];
        $this->setTokenInRequestHeader($I, '20', 'tests_20');
        $result = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $I->assertEquals('403', $result->errors[0]->code);

        // user group 2: wrong user id
        $queryString = [
            'filter[start_date]' => '2000-09-01',
            'filter[end_date]' => '2000-11-01',
            'filter[user_ids]' => '10',
        ];

        $this->setTokenInRequestHeader($I, '20', 'tests_20');
        $result = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));
        $I->assertEquals('403', $result->errors[0]->code);
    }

    /**
     * @group database_transaction
     */
    public function testGetSaleTimeSeriesWithBadRequest(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'usersAndStore');
        $this->insertFixtureGroup($I, 'transactions');

        $queryString = [
            'filter[start_date]' => 'a-b-c',
            'filter[end_date]' => '2000-11-01',
            'filter[user_ids]' => '10',
            'filter[store_ids]' => '10',
        ];

        $this->setTokenInRequestHeader($I, '10', 'tests_10');
        $results = $I->directGET($this->app, 'reporting/time-series/sales?' . http_build_query($queryString));

        $I->assertNotNull($results->errors);

        // It's a random number, so we hardcode it to 1 here.
        $results->errors[0]->id = 1;

        // Some error trace which are dynamic
        unset($results->errors[0]->meta);
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }
}
