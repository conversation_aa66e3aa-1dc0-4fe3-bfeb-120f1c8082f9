{"errors": [{"id": 1, "status": "422", "code": "422", "title": "Invalid start_date field", "detail": "[a-b-c] is not a valid value for field start_date", "meta": {"exception_message": "Could not parse 'a-b-c': Failed to parse time string (a-b-c) at position 1 (-): Unexpected character", "exception_trace": "#0 /srv/www/sf_platform/current/api/app/src/be/Controllers/Reports/TimeSeries/Sales.php(27): Salesfloor\\API\\Controllers\\Reports\\TimeSeries\\Sales->transformFilters(Object(Salesfloor\\Models\\Virtuals\\Reports\\SaleTimeSeries), Array)\n#1 /srv/www/sf_platform/current/vendor/symfony/http-kernel/HttpKernel.php(169): Salesfloor\\API\\Controllers\\Reports\\TimeSeries\\Sales->get(Object(Silex\\Application))\n#2 /srv/www/sf_platform/current/vendor/symfony/http-kernel/HttpKernel.php(81): Symfony\\Component\\HttpKernel\\HttpKernel->handleRaw(Object(Symfony\\Component\\HttpFoundation\\Request), 1)\n#3 /srv/www/sf_platform/current/vendor/silex/silex/src/Silex/Application.php(496): Symfony\\Component\\HttpKernel\\HttpKernel->handle(Object(Symfony\\Component\\HttpFoundation\\Request), 1, true)\n#4 /srv/www/sf_platform/current/codeception/tests/_support/Helper/Module/DirectRequest.php(232): Silex\\Application->handle(Object(Symfony\\Component\\HttpFoundation\\Request))\n#5 /srv/www/sf_platform/current/codeception/tests/_support/Helper/Module/DirectRequest.php(65): SF\\Helper\\Module\\DirectRequest->doDirectRequest(Object(Silex\\Application), 'reporting/time-...', 'GET', Array, 'application/jso...')\n#6 [internal function]: SF\\Helper\\Module\\DirectRequest->directGET(Object(Silex\\Application), 'reporting/time-...')\n#7 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Step.php(285): call_user_func_array(Array, Array)\n#8 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Scenario.php(76): Codeception\\Step->run(Object(Codeception\\Lib\\ModuleContainer))\n#9 /srv/www/sf_platform/current/codeception/tests/_support/_generated/RestTesterActions.php(4750): Codeception\\Scenario->runStep(Object(Codeception\\Step\\Action))\n#10 /srv/www/sf_platform/current/codeception/tests/rest/Reporting/TimeSeriesCest.php(152): SF\\RestTester->directGET(Object(Silex\\Application), 'reporting/time-...')\n#11 [internal function]: SF\\rest\\Reporting\\TimeSeriesCest->testGetSaleTimeSeriesWithBadRequest(Object(SF\\RestTester))\n#12 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Lib/Di.php(128): ReflectionMethod->invokeArgs(Object(SF\\rest\\Reporting\\TimeSeriesCest), Array)\n#13 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Test/Cest.php(138): Codeception\\Lib\\Di->injectDependencies(Object(SF\\rest\\Reporting\\TimeSeriesCest), 'testGetSaleTime...', Array)\n#14 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Test/Cest.php(150): Codeception\\Test\\Cest->invoke('testGetSaleTime...', Array)\n#15 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Test/Cest.php(82): Codeception\\Test\\Cest->executeTestMethod(Object(SF\\RestTester))\n#16 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Test/Test.php(98): Codeception\\Test\\Cest->test()\n#17 /srv/www/sf_platform/current/vendor/phpunit/phpunit/src/Framework/TestSuite.php(685): Codeception\\Test\\Test->run(Object(PHPUnit\\Framework\\TestResult))\n#18 /srv/www/sf_platform/current/vendor/codeception/phpunit-wrapper/src/Runner.php(117): PHPUnit\\Framework\\TestSuite->run(Object(PHPUnit\\Framework\\TestResult))\n#19 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/SuiteManager.php(161): Codeception\\PHPUnit\\Runner->doEnhancedRun(Object(Codeception\\Suite), Object(PHPUnit\\Framework\\TestResult), Array)\n#20 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Codecept.php(208): Codeception\\SuiteManager->run(Object(Codeception\\PHPUnit\\Runner), Object(PHPUnit\\Framework\\TestResult), Array)\n#21 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Codecept.php(162): Codeception\\Codecept->runSuite(Array, 'rest', 'Reporting/TimeS...')\n#22 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Command/Run.php(401): Codeception\\Codecept->run('rest', 'Reporting/TimeS...', Array)\n#23 /srv/www/sf_platform/current/vendor/symfony/console/Command/Command.php(255): Codeception\\Command\\Run->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#24 /srv/www/sf_platform/current/vendor/symfony/console/Application.php(1021): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#25 /srv/www/sf_platform/current/vendor/symfony/console/Application.php(275): Symfony\\Component\\Console\\Application->doRunCommand(Object(Codeception\\Command\\Run), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#26 /srv/www/sf_platform/current/vendor/symfony/console/Application.php(149): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#27 /srv/www/sf_platform/current/vendor/codeception/codeception/src/Codeception/Application.php(117): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#28 /srv/www/sf_platform/current/vendor/codeception/codeception/app.php(46): Codeception\\Application->run()\n#29 /srv/www/sf_platform/current/vendor/codeception/codeception/app.php(47): {closure}()\n#30 /srv/www/sf_platform/current/vendor/codeception/codeception/codecept(7): require('/srv/www/sf_pla...')\n#31 /srv/www/sf_platform/current/vendor/bin/codecept(119): include('/srv/www/sf_pla...')\n#32 {main}"}}]}