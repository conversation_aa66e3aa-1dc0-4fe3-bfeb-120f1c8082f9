<?php

namespace SF\rest\Reporting;

use Codeception\Util\Fixtures;
use SF\BaseFixture;

class Fixture extends BaseFixture
{
    public function mockupUsers()
    {
        Fixtures::add(
            'usersAndStore',
            [
                'sf_store' => [
                    [
                        'store_id'          => 2736,
                        'name'              => 'test store',
                        'timezone'          => 'Asia/Tokyo',
                        'sf_identifier'     => 'store-one',
                        'store_user_id'     => 335,
                        'retailer_store_id' => 'ny1',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                        'postal'            => 'H3A 1T5',
                        'phone'             => '************',
                        'latitude'          => 45.4910,
                        'longitude'         => -73.5658,
                        'locale'            => 'ja_JP',
                    ],
                    [
                        'store_id'          => 10,
                        'name'              => 'test store 10',
                        'timezone'          => 'America/Montreal',
                        'sf_identifier'     => 'store-one-10',
                        'store_user_id'     => 33510,
                        'retailer_store_id' => 'ny10',
                        'locale'            => 'en_CA',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                        'postal'            => 'H3A 1T5',
                        'phone'             => '************',
                        'latitude'          => 45.4910,
                        'longitude'         => -73.5658,
                    ],
                    [
                        'store_id'          => 20,
                        'name'              => 'test store 20',
                        'timezone'          => 'America/Montreal',
                        'sf_identifier'     => 'store-one-20',
                        'store_user_id'     => 33520,
                        'retailer_store_id' => 'ny20',
                        'locale'            => 'en_CA',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                        'postal'            => 'H3A 1T5',
                        'phone'             => '************',
                        'latitude'          => 45.4910,
                        'longitude'         => -73.5658,
                    ],
                    [
                        'store_id'          => 30,
                        'name'              => 'test store 30',
                        'timezone'          => 'America/Montreal',
                        'sf_identifier'     => 'store-one-30',
                        'store_user_id'     => 33530,
                        'retailer_store_id' => 'ny30',
                        'locale'            => 'en_CA',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                        'postal'            => 'H3A 1T5',
                        'phone'             => '************',
                        'latitude'          => 45.4910,
                        'longitude'         => -73.5658,
                    ],
                    [
                        'store_id'          => 40,
                        'name'              => 'test store 40',
                        'timezone'          => 'America/Montreal',
                        'sf_identifier'     => 'store-one-40',
                        'store_user_id'     => 33540,
                        'retailer_store_id' => 'ny40',
                        'locale'            => 'en_CA',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                        'postal'            => 'H3A 1T5',
                        'phone'             => '************',
                        'latitude'          => 45.4910,
                        'longitude'         => -73.5658,
                    ],
                    [
                        'store_id'          => 50,
                        'name'              => 'test store 50',
                        'timezone'          => 'America/Montreal',
                        'sf_identifier'     => 'store-one-50',
                        'store_user_id'     => 33550,
                        'retailer_store_id' => 'ny50',
                        'locale'            => 'en_CA',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                        'postal'            => 'H3A 1T5',
                        'phone'             => '************',
                        'latitude'          => 45.4910,
                        'longitude'         => -73.5658,
                    ],

                ],
                'sf_store_locale' => [
                    [
                        'locale'     => 'ja_JP',
                        'store_id'   => 2736,
                        'is_default' => 1
                    ],
                ],
                'wp_users' => [
                    [
                        'ID'                  => 1417,
                        'user_login'          => 'mrep22',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep22',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '2001-01-01 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep22',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 2736,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 1,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'ja_JP',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                    [
                        'ID'                  => 10,
                        'user_login'          => 'mrep10',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep10',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '0000-00-00 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep10',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 10,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 1,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'en_CA',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                    [
                        'ID'                  => 20,
                        'user_login'          => 'mrep20',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep20',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '2001-01-01 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep20',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 20,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 2,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'en_CA',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                    [
                        'ID'                  => 21,
                        'user_login'          => 'mrep21',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep20',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '2001-01-01 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep20',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 20,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 2,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'en_CA',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                    [
                        'ID'                  => 30,
                        'user_login'          => 'mrep30',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep30',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '2001-01-01 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep30',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 30,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 3,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'en_CA',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                    [
                        'ID'                  => 40,
                        'user_login'          => 'mrep40',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep40',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '2001-01-01 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep40',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 40,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 4,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'en_CA',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                    [
                        'ID'                  => 50,
                        'user_login'          => 'mrep50',
                        'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                        'user_nicename'       => 'mrep50',
                        'user_email'          => '<EMAIL>',
                        'user_url'            => '',
                        'user_registered'     => '2001-01-01 00:00:00',
                        'user_activation_key' => '',
                        'user_status'         => 1,
                        'user_alias'          => null,
                        'display_name'        => 'mrep50',
                        'description'         => null,
                        'photo'               => null,
                        'last_login'          => null,
                        'localization'        => null,
                        'feature'             => null,
                        'status'              => null,
                        'store'               => 50,
                        'type'                => 'rep',
                        'commission_rate'     => 0.00,
                        'employee_id'         => null,
                        'group'               => 5,
                        'selling_mode'        => 1,
                        'isPhoto'             => 0,
                        'locked_at'           => null,
                        'locale'              => 'en_CA',
                        'creation_source'     => 'invite',
                        'shop_feed'           => 0
                    ],
                ],
            ],
        );
    }

    public function mockupTransaction()
    {
        $yesterdayUtc = date('Y-m-d', time() - 60 * 60 * 24);
        Fixtures::add(
            'yesterdayUtcTransaction',
            [
                'sf_rep_transaction'        => [
                    [
                        'user_id'             => 1417,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'F210720137369',
                        'trx_date'            => "$yesterdayUtc 15:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36300.0000,
                        'trx_total'           => 36300.0000,
                        'status'              => 1,
                        'store_id'            => 2736,
                        'received_date'       => "$yesterdayUtc 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                ],
                'sf_rep_transaction_detail' => [
                    [
                        'trx_id'                 => 'F210720137369',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5500.0000,
                        'product_id'             => 'E38201',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                    [
                        'trx_id'                 => 'F210720137369',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 12650.0000,
                        'product_id'             => 'E38202',
                        'quantity'               => '2',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                    [
                        'trx_id'                 => 'F210720137369',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5500.0000,
                        'product_id'             => 'E38201',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                ],
            ],
        );

        Fixtures::add(
            'transactions',
            [
                'sf_rep_transaction'        => [
                    [
                        'user_id'             => 10,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx10',
                        'trx_date'            => "2000-10-01 15:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36310.0000,
                        'trx_total'           => 36310.0000,
                        'status'              => 1,
                        'store_id'            => 10,
                        'received_date'       => "2000-10-01 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    [
                        'user_id'             => 10,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx10',
                        'trx_date'            => "2000-10-01 16:30:00",
                        'trx_type'            => 'return',
                        'trx_apply_total'     => -100,
                        'trx_total'           => -100,
                        'status'              => 1,
                        'store_id'            => 10,
                        'received_date'       => "2000-10-01 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    [
                        'user_id'             => 20,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx20',
                        'trx_date'            => "2000-10-02 15:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36320.0000,
                        'trx_total'           => 36320.0000,
                        'status'              => 1,
                        'store_id'            => 20,
                        'received_date'       => "2000-10-02 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    [
                        'user_id'             => 30,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx30',
                        'trx_date'            => "2000-10-03 15:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36330.0000,
                        'trx_total'           => 36330.0000,
                        'status'              => 1,
                        'store_id'            => 30,
                        'received_date'       => "2000-10-03 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    [
                        'user_id'             => 40,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx40',
                        'trx_date'            => "2000-10-04 15:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36340.0000,
                        'trx_total'           => 36340.0000,
                        'status'              => 1,
                        'store_id'            => 40,
                        'received_date'       => "2000-10-04 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    [
                        'user_id'             => 50,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx50',
                        'trx_date'            => "2000-10-05 15:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36350.0000,
                        'trx_total'           => 36350.0000,
                        'status'              => 1,
                        'store_id'            => 50,
                        'received_date'       => "2000-10-01 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    // duplicated
                    [
                        'user_id'             => 10,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx10',
                        'trx_date'            => "2000-10-01 10:30:00",
                        'trx_type'            => 'sale',
                        'trx_apply_total'     => 36310.0000,
                        'trx_total'           => 36310.0000,
                        'status'              => 1,
                        'store_id'            => 10,
                        'received_date'       => "2000-10-01 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    //  the 'return' is not related to any sale trx.
                    [
                        'user_id'             => 8,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx10',
                        'trx_date'            => "2000-10-01 10:30:00",
                        'trx_type'            => 'return',
                        'trx_apply_total'     => -50,
                        'trx_total'           => -50,
                        'status'              => 1,
                        'store_id'            => 10,
                        'received_date'       => "2000-10-01 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                    //  Cancelled before sales transaction
                    [
                        'user_id'             => 8,
                        'retailer_rep_id'     => null,
                        'trx_id'              => 'trx10',
                        'trx_date'            => "2000-10-01 05:30:00",
                        'trx_type'            => 'cancel',
                        'trx_apply_total'     => -100,
                        'trx_total'           => -100,
                        'status'              => 1,
                        'store_id'            => 10,
                        'received_date'       => "2000-10-01 15:30:00",
                        'customer_name'       => null,
                        'customer_email'      => null,
                        'ip'                  => null,
                        'user_agent'          => null,
                        'acquisition'         => '',
                        'currency'            => 'USD',
                        'attribution'         => null,
                        'customer_id'         => null,
                        'deactivated_user_id' => null,
                        'fingerprint'         => null,
                        'origin'              => 'storefront',
                    ],
                ],
                'sf_rep_transaction_detail' => [
                    [
                        'trx_id'                 => 'trx10',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5510.0000,
                        'product_id'             => 'E3820110',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                    [
                        'trx_id'                 => 'trx20',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5520.0000,
                        'product_id'             => 'E3820120',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                    [
                        'trx_id'                 => 'trx30',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5530.0000,
                        'product_id'             => 'E3820130',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                    [
                        'trx_id'                 => 'trx40',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5540.0000,
                        'product_id'             => 'E3820140',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                    [
                        'trx_id'                 => 'trx50',
                        'trx_detail_id'          => '',
                        'trx_detail_apply_total' => 0.0000,
                        'trx_detail_total'       => 5550.0000,
                        'product_id'             => 'E3820150',
                        'quantity'               => '1',
                        'units'                  => 'each',
                        'sku'                    => null
                    ],
                ],
            ],
        );
    }

    public function inactiveFilteringCest()
    {
        // I'm still questioning if this was needed for those tests.
        // I could just have 1 kpi and it would be enough but since it's there, I will keep it.
        $kpis1 = $this->generateDailyStats(1);
        $kpis2 = $this->generateDailyStats(2);
        $kpis3 = $this->generateDailyStats(3);
        $kpis4 = $this->generateDailyStats(4);

        $store1111 = $this->mergeDailyStats($kpis1, $kpis2);
        $store1112 = $this->mergeDailyStats($kpis3, $kpis4);
        $store0 = $this->mergeDailyStats($store1111, $store1112);

        Fixtures::add('InactiveFilteringCest', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1111,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 9,
                    'user_login'          => 'testuser336',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser336',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 0,
                    'user_alias'          => null,
                    'display_name'        => 'testuser336',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1111,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 10,
                    'user_login'          => 'testuser337',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser337',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser337',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1112,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 11,
                    'user_login'          => 'testuser338',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser338',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 0,
                    'user_alias'          => null,
                    'display_name'        => 'testuser338',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1112,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 12,
                    'user_login'          => 'admin',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'admin',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'admin',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 0,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 5,
                    'selling_mode'        => 0,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ]
            ],
            'sf_store'        => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country' => 'CA',
                    'region' => 'QC',
                    'city' => 'Montreal',
                    'address' => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country' => 'CA',
                    'region' => 'QC',
                    'city' => 'Montreal',
                    'address' => '1455 Peel Streets',
                ],
            ],
            'sf_user_daily_stats' => [
                array_merge(
                    [
                        'user_id' => 8, // active user / active store
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis1,
                ),
                array_merge(
                    [
                        'user_id' => 9, // inactive user / active store
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis2,
                ),
                array_merge(
                    [
                        'user_id' => 10, // active user / inactive store
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis3,
                ),
                array_merge(
                    [
                        'user_id' => 11, // inactive user / inactive store
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis4,
                ),
            ],
            'sf_store_daily_stats' => [
                array_merge(
                    [
                        'store_id' => 0,
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'GLOBAL',
                    ],
                    $store0,
                ),
                array_merge(
                    [
                        'store_id' => 1111,
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'America/Montreal',
                    ],
                    $store1111,
                ),
                array_merge(
                    [
                        'store_id' => 1112,
                        'date' => gmdate('Y-m-d'),
                        'timezone' => 'America/Montreal',
                    ],
                    $store1112,
                ),
            ],
            'sf_rep_transaction' => [
                [
                    'user_id'             => 8,
                    'retailer_rep_id'     => '631648',
                    'trx_id'              => '103224195',
                    'trx_date'            => gmdate('Y-m-d H:i:s'),
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1100.0000,
                    'trx_total'           => 1100.0000,
                    'status'              => 1,
                    'store_id'            => 1111,
                    'received_date'       => gmdate('Y-m-d H:i:s'),
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 9,
                    'retailer_rep_id'     => '631649',
                    'trx_id'              => '103224196',
                    'trx_date'            => gmdate('Y-m-d H:i:s'),
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1200.0000,
                    'trx_total'           => 1200.0000,
                    'status'              => 1,
                    'store_id'            => 1111,
                    'received_date'       => gmdate('Y-m-d H:i:s'),
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 10,
                    'retailer_rep_id'     => '631650',
                    'trx_id'              => '103224197',
                    'trx_date'            => gmdate('Y-m-d H:i:s'),
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1300.0000,
                    'trx_total'           => 1300.0000,
                    'status'              => 1,
                    'store_id'            => 1112,
                    'received_date'       => gmdate('Y-m-d H:i:s'),
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 11,
                    'retailer_rep_id'     => '631651',
                    'trx_id'              => '103224198',
                    'trx_date'            => gmdate('Y-m-d H:i:s'),
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1400.0000,
                    'trx_total'           => 1400.0000,
                    'status'              => 1,
                    'store_id'            => 1112,
                    'received_date'       => gmdate('Y-m-d H:i:s'),
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
            ],
            'sf_rep_transaction_detail' => [
                [
                    'trx_id'                 => '103224195',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1100.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224196',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1200.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224197',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1300.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224198',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1400.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
            ]
        ]);
    }

    public function testDashboardFilteringStores()
    {
        $response = <<<DATA
{"type":"rep","ids":null,"filter":{"kpi-period":"MTD","exclude_inactive":{"user":false,"store":true},"include-variance":true},"ranks":{"sales":{"value":29,"rank":"-","variance":"-","old_rank":"-","label":"Total Sales"},"response-time":{"value":1,"rank":"-","variance":"-","old_rank":"-","label":"Request Reply Time"},"requests-received":{"value":119,"rank":"-","variance":"-","old_rank":"-","label":"Total Requests"},"unique-visitors":{"value":67,"rank":"-","variance":"-","old_rank":"-","label":"Unique Visitors"},"chat-answer-rate":{"value":300,"rank":"-","variance":"-","old_rank":"-","label":"Live Chat Answer Rate"},"global":{"value":"-","rank":"-","variance":"-","old_rank":"-","label":"Total Ranking"}},"rank-out-of":"-","kpis":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":3,"onboarding_end":5,"user_add":7,"chat_request":9,"chat_answer":11,"response_mail_sent":13,"mail_sent":15,"email_stats_open":17,"email_stats_click":19,"avg_init_resp_sum_times":21,"avg_init_resp_num_responses":23,"chat_answer_rate_requests":25,"chat_answer_rate_answers":27,"total_order_value":29,"n_sales_transactions":31,"feedback":33,"profile_update":35,"content_create":37,"content_update":39,"content_curate":41,"ask_question_req":43,"personal_shopper_req":45,"live_session_start":47,"live_session_end":49,"product_update":51,"deal_update":53,"unsubscribe":55,"new_sale":57,"subscribe":59,"retail_event":61,"raise_concern":63,"shopping_page":65,"unique_visitor":67,"page_hit":69,"sale_duplicate":71,"user_visit":73,"com_ref":75,"soc_ref":77,"retail_hit":79,"social_post":81,"soc_share":83,"moderate_lead":85,"customer_card":87,"help_useful":89,"livesession_register":91,"change_categories":93,"chatsession_register":95,"event_create":97,"event_update":99,"event_delete":101,"event_subscribe":103,"sidebar_view":105,"sidebar_click":107,"footer_view":109,"footer_click":111,"storefront_click":113,"transactional_mail_sent":115,"courtesy_mail_sent":117,"service_total":119,"traffic_total":121,"content_total":123,"number_seconds_available":125,"total_return_value":127,"recommendation_chat":129,"recommendation_compose_message":131,"recommendation_share_email":133,"recommendation_share_facebook":135,"recommendation_share_twitter":137,"recommendation_new_arrivals":139,"recommendation_top_picks":141,"click_top_picks":143,"click_latest_arrivals":145,"click_recommended":147,"avg_selected_top_picks":149,"avg_selected_new_arrivals":151,"salesfloor_visits":153,"text_messages_outbound_api":155,"text_messages_outbound_call":157,"text_messages_outbound_reply":159,"text_messages_inbound":161,"recommendation_text_message":163,"tasks_automated_created":165,"tasks_automated_resolved":167,"tasks_automated_dismissed":169,"tasks_manual_created":171,"tasks_manual_resolved":173,"tasks_manual_dismissed":175,"chat_abandoned":177,"chat_abandonment_time":179,"chat_answer_time":181,"chat_early_redirect":183,"chat_auto_redirect":185,"ask_question_req_email":187,"ask_question_req_text":189,"appointment_req_email":191,"appointment_req_text":193,"personal_shopper_req_email":195,"personal_shopper_req_text":197,"library_share_attempts":199,"lookbook_create":201,"lookbook_update":203,"received_chats_answered_by_other":205,"request_email_sent":207,"request_email_open":209,"request_email_click":211,"compose_email_sent":213,"compose_email_open":215,"compose_email_click":217,"share_email_sent":219,"share_email_open":221,"share_email_click":223,"total_share_sent":225,"chat_abandon_0_29":227,"chat_abandon_30_59":229,"chat_abandon_60_89":231,"chat_abandon_90_120":233,"ask_question_req_chat_handoff":235,"tasks_system_created":237,"tasks_system_resolved":239,"tasks_system_dismissed":241,"tasks_followup_created":243,"tasks_followup_resolved":245,"tasks_followup_dismissed":247,"tasks_corporate_created":249,"tasks_corporate_resolved":251,"tasks_corporate_dismissed":253,"tasks_manual_resolved_sum_time":255,"tasks_manual_dismissed_sum_time":257,"tasks_automated_resolved_sum_time":259,"tasks_automated_dismissed_sum_time":261,"tasks_system_resolved_sum_time":263,"tasks_system_dismissed_sum_time":265,"tasks_followup_resolved_sum_time":267,"tasks_followup_dismissed_sum_time":269,"tasks_corporate_resolved_sum_time":271,"tasks_corporate_dismissed_sum_time":273,"socialshop_post_created":275,"socialshop_total_visit":277,"socialshop_product_click":279,"socialshop_storefront_click":281,"socialshop_sales_count":283,"socialshop_sales_amount_total":285,"tasks_corporate_deleted":287,"ask_question_req_cs_email":289,"ask_question_req_cs_text":291,"appointment_req_cs_email":293,"appointment_req_cs_text":295,"personal_shopper_req_cs_email":297,"personal_shopper_req_cs_text":299,"scheduled_appointments":301,"cancelled_appointment":303,"video_chat_sessions":305,"video_chat_duration":307,"virtual_appointment_sessions":309,"virtual_appointment_duration":311,"avg_init_resp":"-","avg_init_resp_raw":0.9130434782608695,"avg_order_value":0.9354838709677419,"chat_answer_rate":3,"mail_click":1.2666666666666666,"mail_open":1.1333333333333333,"tasks_manual_resolved_avg_time":"\u003C 1m","tasks_manual_resolved_avg_time_raw":1.4739884393063585,"tasks_manual_dismissed_avg_time":"\u003C 1m","tasks_manual_dismissed_avg_time_raw":1.4685714285714286,"tasks_automated_resolved_avg_time":"\u003C 1m","tasks_automated_resolved_avg_time_raw":1.5508982035928143,"tasks_automated_dismissed_avg_time":"\u003C 1m","tasks_automated_dismissed_avg_time_raw":1.544378698224852,"tasks_system_resolved_avg_time":"\u003C 1m","tasks_system_resolved_avg_time_raw":1.100418410041841,"tasks_system_dismissed_avg_time":"\u003C 1m","tasks_system_dismissed_avg_time_raw":1.099585062240664,"tasks_followup_resolved_avg_time":"\u003C 1m","tasks_followup_resolved_avg_time_raw":1.089795918367347,"tasks_followup_dismissed_avg_time":"\u003C 1m","tasks_followup_dismissed_avg_time_raw":1.0890688259109311,"tasks_corporate_resolved_avg_time":"\u003C 1m","tasks_corporate_resolved_avg_time_raw":1.0796812749003983,"tasks_corporate_dismissed_avg_time":"\u003C 1m","tasks_corporate_dismissed_avg_time_raw":1.0790513833992095,"net_sales":156,"n_recommendations":"1108","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":471,"text_messages_received":161,"service_exclude_live_chat_total":1152,"service_cs_total":1764}}
DATA;

        Fixtures::add('testDashboardFilteringStoresResponse', json_decode($response, true));
    }

    public function testDashboardFilteringStoresUsers()
    {
        $response = <<<DATA
{"type":"rep","ids":null,"filter":{"kpi-period":"MTD","exclude_inactive":{"user":true,"store":true},"include-variance":true},"ranks":{"sales":{"value":14,"rank":"-","variance":"-","old_rank":"-","label":"Total Sales"},"response-time":{"value":1,"rank":"-","variance":"-","old_rank":"-","label":"Request Reply Time"},"requests-received":{"value":59,"rank":"-","variance":"-","old_rank":"-","label":"Total Requests"},"unique-visitors":{"value":33,"rank":"-","variance":"-","old_rank":"-","label":"Unique Visitors"},"chat-answer-rate":{"value":325,"rank":"-","variance":"-","old_rank":"-","label":"Live Chat Answer Rate"},"global":{"value":"-","rank":"-","variance":"-","old_rank":"-","label":"Total Ranking"}},"rank-out-of":"-","kpis":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"chat_answer_rate":3.25,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"\u003C 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"\u003C 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"\u003C 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"\u003C 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"\u003C 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"\u003C 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"\u003C 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"\u003C 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"\u003C 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"\u003C 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":234,"text_messages_received":80,"service_exclude_live_chat_total":573,"service_cs_total":879}}
DATA;

        Fixtures::add('testDashboardFilteringStoresUsersResponse', json_decode($response, true));
    }

    public function testDashboardFilteringUsers()
    {
        $response = <<<DATA
{"type":"rep","ids":null,"filter":{"kpi-period":"MTD","exclude_inactive":{"user":true,"store":false},"include-variance":true},"ranks":{"sales":{"value":30,"rank":"-","variance":"-","old_rank":"-","label":"Total Sales"},"response-time":{"value":1,"rank":"-","variance":"-","old_rank":"-","label":"Request Reply Time"},"requests-received":{"value":120,"rank":"-","variance":"-","old_rank":"-","label":"Total Requests"},"unique-visitors":{"value":68,"rank":"-","variance":"-","old_rank":"-","label":"Unique Visitors"},"chat-answer-rate":{"value":280,"rank":"-","variance":"-","old_rank":"-","label":"Live Chat Answer Rate"},"global":{"value":"-","rank":"-","variance":"-","old_rank":"-","label":"Total Ranking"}},"rank-out-of":"-","kpis":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":4,"onboarding_end":6,"user_add":8,"chat_request":10,"chat_answer":12,"response_mail_sent":14,"mail_sent":16,"email_stats_open":18,"email_stats_click":20,"avg_init_resp_sum_times":22,"avg_init_resp_num_responses":24,"chat_answer_rate_requests":26,"chat_answer_rate_answers":28,"total_order_value":30,"n_sales_transactions":32,"feedback":34,"profile_update":36,"content_create":38,"content_update":40,"content_curate":42,"ask_question_req":44,"personal_shopper_req":46,"live_session_start":48,"live_session_end":50,"product_update":52,"deal_update":54,"unsubscribe":56,"new_sale":58,"subscribe":60,"retail_event":62,"raise_concern":64,"shopping_page":66,"unique_visitor":68,"page_hit":70,"sale_duplicate":72,"user_visit":74,"com_ref":76,"soc_ref":78,"retail_hit":80,"social_post":82,"soc_share":84,"moderate_lead":86,"customer_card":88,"help_useful":90,"livesession_register":92,"change_categories":94,"chatsession_register":96,"event_create":98,"event_update":100,"event_delete":102,"event_subscribe":104,"sidebar_view":106,"sidebar_click":108,"footer_view":110,"footer_click":112,"storefront_click":114,"transactional_mail_sent":116,"courtesy_mail_sent":118,"service_total":120,"traffic_total":122,"content_total":124,"number_seconds_available":126,"total_return_value":128,"recommendation_chat":130,"recommendation_compose_message":132,"recommendation_share_email":134,"recommendation_share_facebook":136,"recommendation_share_twitter":138,"recommendation_new_arrivals":140,"recommendation_top_picks":142,"click_top_picks":144,"click_latest_arrivals":146,"click_recommended":148,"avg_selected_top_picks":75,"avg_selected_new_arrivals":76,"salesfloor_visits":154,"text_messages_outbound_api":156,"text_messages_outbound_call":158,"text_messages_outbound_reply":160,"text_messages_inbound":162,"recommendation_text_message":164,"tasks_automated_created":166,"tasks_automated_resolved":168,"tasks_automated_dismissed":170,"tasks_manual_created":172,"tasks_manual_resolved":174,"tasks_manual_dismissed":176,"chat_abandoned":178,"chat_abandonment_time":180,"chat_answer_time":182,"chat_early_redirect":184,"chat_auto_redirect":186,"ask_question_req_email":188,"ask_question_req_text":190,"appointment_req_email":192,"appointment_req_text":194,"personal_shopper_req_email":196,"personal_shopper_req_text":198,"library_share_attempts":200,"lookbook_create":202,"lookbook_update":204,"received_chats_answered_by_other":206,"request_email_sent":208,"request_email_open":210,"request_email_click":212,"compose_email_sent":214,"compose_email_open":216,"compose_email_click":218,"share_email_sent":220,"share_email_open":222,"share_email_click":224,"total_share_sent":226,"chat_abandon_0_29":228,"chat_abandon_30_59":230,"chat_abandon_60_89":232,"chat_abandon_90_120":234,"ask_question_req_chat_handoff":236,"tasks_system_created":238,"tasks_system_resolved":240,"tasks_system_dismissed":242,"tasks_followup_created":244,"tasks_followup_resolved":246,"tasks_followup_dismissed":248,"tasks_corporate_created":250,"tasks_corporate_resolved":252,"tasks_corporate_dismissed":254,"tasks_manual_resolved_sum_time":256,"tasks_manual_dismissed_sum_time":258,"tasks_automated_resolved_sum_time":260,"tasks_automated_dismissed_sum_time":262,"tasks_system_resolved_sum_time":264,"tasks_system_dismissed_sum_time":266,"tasks_followup_resolved_sum_time":268,"tasks_followup_dismissed_sum_time":270,"tasks_corporate_resolved_sum_time":272,"tasks_corporate_dismissed_sum_time":274,"socialshop_post_created":276,"socialshop_total_visit":278,"socialshop_product_click":280,"socialshop_storefront_click":282,"socialshop_sales_count":284,"socialshop_sales_amount_total":286,"tasks_corporate_deleted":288,"ask_question_req_cs_email":290,"ask_question_req_cs_text":292,"appointment_req_cs_email":294,"appointment_req_cs_text":296,"personal_shopper_req_cs_email":298,"personal_shopper_req_cs_text":300,"scheduled_appointments":302,"cancelled_appointment":304,"video_chat_sessions":306,"video_chat_duration":308,"virtual_appointment_sessions":310,"virtual_appointment_duration":312,"avg_init_resp":"-","avg_init_resp_raw":0.9166666666666666,"avg_order_value":0.9375,"chat_answer_rate":2.8,"mail_click":1.25,"mail_open":1.125,"tasks_manual_resolved_avg_time":"\u003C 1m","tasks_manual_resolved_avg_time_raw":1.471264367816092,"tasks_manual_dismissed_avg_time":"\u003C 1m","tasks_manual_dismissed_avg_time_raw":1.4659090909090908,"tasks_automated_resolved_avg_time":"\u003C 1m","tasks_automated_resolved_avg_time_raw":1.5476190476190477,"tasks_automated_dismissed_avg_time":"\u003C 1m","tasks_automated_dismissed_avg_time_raw":1.5411764705882354,"tasks_system_resolved_avg_time":"\u003C 1m","tasks_system_resolved_avg_time_raw":1.1,"tasks_system_dismissed_avg_time":"\u003C 1m","tasks_system_dismissed_avg_time_raw":1.0991735537190082,"tasks_followup_resolved_avg_time":"\u003C 1m","tasks_followup_resolved_avg_time_raw":1.089430894308943,"tasks_followup_dismissed_avg_time":"\u003C 1m","tasks_followup_dismissed_avg_time_raw":1.0887096774193548,"tasks_corporate_resolved_avg_time":"\u003C 1m","tasks_corporate_resolved_avg_time_raw":1.0793650793650793,"tasks_corporate_dismissed_avg_time":"\u003C 1m","tasks_corporate_dismissed_avg_time_raw":1.078740157480315,"net_sales":158,"n_recommendations":"1116","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":474,"text_messages_received":162,"service_exclude_live_chat_total":1158,"service_cs_total":1770}}
DATA;

        Fixtures::add('testDashboardFilteringUsersResponse', json_decode($response, true));
    }

    public function testSalesFilteringStores()
    {
        $response1 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":null,"tot_comm":"0.0000000000","total":"0.0000"}]}}}}
DATA;
        $response2 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"2300.0000"}]}}}}
DATA;
        $response3 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"9","id":"103224196","total":"1200.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"},{"user_id":"8","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testSalesFilteringStoresResponse1', json_decode($response1, true));
        Fixtures::add('testSalesFilteringStoresResponse2', json_decode($response2, true));
        Fixtures::add('testSalesFilteringStoresResponse3', json_decode($response3, true));
    }

    public function testSalesFilteringStoresUsers()
    {
        $response1 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":null,"tot_comm":"0.0000000000","total":"0.0000"}]}}}}
DATA;
        $response2 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"1100.0000"}]}}}}
DATA;
        $response3 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"8","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testSalesFilteringStoresUsersResponse1', json_decode($response1, true));
        Fixtures::add('testSalesFilteringStoresUsersResponse2', json_decode($response2, true));
        Fixtures::add('testSalesFilteringStoresUsersResponse3', json_decode($response3, true));
    }

    public function testSalesFilteringUsers()
    {
        $response1 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":null,"tot_comm":"0.0000000000","total":"0.0000"}]}}}}
DATA;
        $response2 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"2400.0000"}]}}}}
DATA;
        $response3 = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"10","id":"103224197","total":"1300.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1112","store_name":"Fake Mall Inactive","retailer_store_id":"fakemall"},{"user_id":"8","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testSalesFilteringUsersResponse1', json_decode($response1, true));
        Fixtures::add('testSalesFilteringUsersResponse2', json_decode($response2, true));
        Fixtures::add('testSalesFilteringUsersResponse3', json_decode($response3, true));
    }

    public function testKpisFilteringStores()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":3,"onboarding_end":5,"user_add":7,"chat_request":9,"chat_answer":11,"response_mail_sent":13,"mail_sent":15,"email_stats_open":17,"email_stats_click":19,"avg_init_resp_sum_times":21,"avg_init_resp_num_responses":23,"chat_answer_rate_requests":25,"chat_answer_rate_answers":27,"total_order_value":29,"n_sales_transactions":31,"feedback":33,"profile_update":35,"content_create":37,"content_update":39,"content_curate":41,"ask_question_req":43,"personal_shopper_req":45,"live_session_start":47,"live_session_end":49,"product_update":51,"deal_update":53,"unsubscribe":55,"new_sale":57,"subscribe":59,"retail_event":61,"raise_concern":63,"shopping_page":65,"unique_visitor":67,"page_hit":69,"sale_duplicate":71,"user_visit":73,"com_ref":75,"soc_ref":77,"retail_hit":79,"social_post":81,"soc_share":83,"moderate_lead":85,"customer_card":87,"help_useful":89,"livesession_register":91,"change_categories":93,"chatsession_register":95,"event_create":97,"event_update":99,"event_delete":101,"event_subscribe":103,"sidebar_view":105,"sidebar_click":107,"footer_view":109,"footer_click":111,"storefront_click":113,"transactional_mail_sent":115,"courtesy_mail_sent":117,"service_total":119,"traffic_total":121,"content_total":123,"number_seconds_available":125,"total_return_value":127,"recommendation_chat":129,"recommendation_compose_message":131,"recommendation_share_email":133,"recommendation_share_facebook":135,"recommendation_share_twitter":137,"recommendation_new_arrivals":139,"recommendation_top_picks":141,"click_top_picks":143,"click_latest_arrivals":145,"click_recommended":147,"avg_selected_top_picks":149,"avg_selected_new_arrivals":151,"salesfloor_visits":153,"text_messages_outbound_api":155,"text_messages_outbound_call":157,"text_messages_outbound_reply":159,"text_messages_inbound":161,"recommendation_text_message":163,"tasks_automated_created":165,"tasks_automated_resolved":167,"tasks_automated_dismissed":169,"tasks_manual_created":171,"tasks_manual_resolved":173,"tasks_manual_dismissed":175,"chat_abandoned":177,"chat_abandonment_time":179,"chat_answer_time":181,"chat_early_redirect":183,"chat_auto_redirect":185,"ask_question_req_email":187,"ask_question_req_text":189,"appointment_req_email":191,"appointment_req_text":193,"personal_shopper_req_email":195,"personal_shopper_req_text":197,"library_share_attempts":199,"lookbook_create":201,"lookbook_update":203,"received_chats_answered_by_other":205,"request_email_sent":207,"request_email_open":209,"request_email_click":211,"compose_email_sent":213,"compose_email_open":215,"compose_email_click":217,"share_email_sent":219,"share_email_open":221,"share_email_click":223,"total_share_sent":225,"chat_abandon_0_29":227,"chat_abandon_30_59":229,"chat_abandon_60_89":231,"chat_abandon_90_120":233,"ask_question_req_chat_handoff":235,"tasks_system_created":237,"tasks_system_resolved":239,"tasks_system_dismissed":241,"tasks_followup_created":243,"tasks_followup_resolved":245,"tasks_followup_dismissed":247,"tasks_corporate_created":249,"tasks_corporate_resolved":251,"tasks_corporate_dismissed":253,"tasks_manual_resolved_sum_time":255,"tasks_manual_dismissed_sum_time":257,"tasks_automated_resolved_sum_time":259,"tasks_automated_dismissed_sum_time":261,"tasks_system_resolved_sum_time":263,"tasks_system_dismissed_sum_time":265,"tasks_followup_resolved_sum_time":267,"tasks_followup_dismissed_sum_time":269,"tasks_corporate_resolved_sum_time":271,"tasks_corporate_dismissed_sum_time":273,"socialshop_post_created":275,"socialshop_total_visit":277,"socialshop_product_click":279,"socialshop_storefront_click":281,"socialshop_sales_count":283,"socialshop_sales_amount_total":285,"tasks_corporate_deleted":287,"ask_question_req_cs_email":289,"ask_question_req_cs_text":291,"appointment_req_cs_email":293,"appointment_req_cs_text":295,"personal_shopper_req_cs_email":297,"personal_shopper_req_cs_text":299,"scheduled_appointments":301,"cancelled_appointment":303,"video_chat_sessions":305,"video_chat_duration":307,"virtual_appointment_sessions":309,"virtual_appointment_duration":311,"avg_init_resp":"-","avg_init_resp_raw":0.9130434782608695,"avg_order_value":0.9354838709677419,"chat_answer_rate":3,"mail_click":1.2666666666666666,"mail_open":1.1333333333333333,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4739884393063585,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4685714285714286,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5508982035928143,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.544378698224852,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.100418410041841,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.099585062240664,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.089795918367347,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0890688259109311,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0796812749003983,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0790513833992095,"net_sales":156,"n_recommendations":"1108","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":471,"text_messages_received":161,"service_exclude_live_chat_total":1152,"service_cs_total":1764},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"2300.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilteringStoresResponse', json_decode($response, true));
    }

    public function testKpisFilteringStoresUsers()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"chat_answer_rate":3.25,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":234,"text_messages_received":80,"service_exclude_live_chat_total":573,"service_cs_total":879},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"1100.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilteringStoresUsersResponse', json_decode($response, true));
    }

    public function testKpisFilteringUsers()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":4,"onboarding_end":6,"user_add":8,"chat_request":10,"chat_answer":12,"response_mail_sent":14,"mail_sent":16,"email_stats_open":18,"email_stats_click":20,"avg_init_resp_sum_times":22,"avg_init_resp_num_responses":24,"chat_answer_rate_requests":26,"chat_answer_rate_answers":28,"total_order_value":30,"n_sales_transactions":32,"feedback":34,"profile_update":36,"content_create":38,"content_update":40,"content_curate":42,"ask_question_req":44,"personal_shopper_req":46,"live_session_start":48,"live_session_end":50,"product_update":52,"deal_update":54,"unsubscribe":56,"new_sale":58,"subscribe":60,"retail_event":62,"raise_concern":64,"shopping_page":66,"unique_visitor":68,"page_hit":70,"sale_duplicate":72,"user_visit":74,"com_ref":76,"soc_ref":78,"retail_hit":80,"social_post":82,"soc_share":84,"moderate_lead":86,"customer_card":88,"help_useful":90,"livesession_register":92,"change_categories":94,"chatsession_register":96,"event_create":98,"event_update":100,"event_delete":102,"event_subscribe":104,"sidebar_view":106,"sidebar_click":108,"footer_view":110,"footer_click":112,"storefront_click":114,"transactional_mail_sent":116,"courtesy_mail_sent":118,"service_total":120,"traffic_total":122,"content_total":124,"number_seconds_available":126,"total_return_value":128,"recommendation_chat":130,"recommendation_compose_message":132,"recommendation_share_email":134,"recommendation_share_facebook":136,"recommendation_share_twitter":138,"recommendation_new_arrivals":140,"recommendation_top_picks":142,"click_top_picks":144,"click_latest_arrivals":146,"click_recommended":148,"avg_selected_top_picks":75,"avg_selected_new_arrivals":76,"salesfloor_visits":154,"text_messages_outbound_api":156,"text_messages_outbound_call":158,"text_messages_outbound_reply":160,"text_messages_inbound":162,"recommendation_text_message":164,"tasks_automated_created":166,"tasks_automated_resolved":168,"tasks_automated_dismissed":170,"tasks_manual_created":172,"tasks_manual_resolved":174,"tasks_manual_dismissed":176,"chat_abandoned":178,"chat_abandonment_time":180,"chat_answer_time":182,"chat_early_redirect":184,"chat_auto_redirect":186,"ask_question_req_email":188,"ask_question_req_text":190,"appointment_req_email":192,"appointment_req_text":194,"personal_shopper_req_email":196,"personal_shopper_req_text":198,"library_share_attempts":200,"lookbook_create":202,"lookbook_update":204,"received_chats_answered_by_other":206,"request_email_sent":208,"request_email_open":210,"request_email_click":212,"compose_email_sent":214,"compose_email_open":216,"compose_email_click":218,"share_email_sent":220,"share_email_open":222,"share_email_click":224,"total_share_sent":226,"chat_abandon_0_29":228,"chat_abandon_30_59":230,"chat_abandon_60_89":232,"chat_abandon_90_120":234,"ask_question_req_chat_handoff":236,"tasks_system_created":238,"tasks_system_resolved":240,"tasks_system_dismissed":242,"tasks_followup_created":244,"tasks_followup_resolved":246,"tasks_followup_dismissed":248,"tasks_corporate_created":250,"tasks_corporate_resolved":252,"tasks_corporate_dismissed":254,"tasks_manual_resolved_sum_time":256,"tasks_manual_dismissed_sum_time":258,"tasks_automated_resolved_sum_time":260,"tasks_automated_dismissed_sum_time":262,"tasks_system_resolved_sum_time":264,"tasks_system_dismissed_sum_time":266,"tasks_followup_resolved_sum_time":268,"tasks_followup_dismissed_sum_time":270,"tasks_corporate_resolved_sum_time":272,"tasks_corporate_dismissed_sum_time":274,"socialshop_post_created":276,"socialshop_total_visit":278,"socialshop_product_click":280,"socialshop_storefront_click":282,"socialshop_sales_count":284,"socialshop_sales_amount_total":286,"tasks_corporate_deleted":288,"ask_question_req_cs_email":290,"ask_question_req_cs_text":292,"appointment_req_cs_email":294,"appointment_req_cs_text":296,"personal_shopper_req_cs_email":298,"personal_shopper_req_cs_text":300,"scheduled_appointments":302,"cancelled_appointment":304,"video_chat_sessions":306,"video_chat_duration":308,"virtual_appointment_sessions":310,"virtual_appointment_duration":312,"avg_init_resp":"-","avg_init_resp_raw":0.9166666666666666,"avg_order_value":0.9375,"chat_answer_rate":2.8,"mail_click":1.25,"mail_open":1.125,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.471264367816092,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4659090909090908,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5476190476190477,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5411764705882354,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.0991735537190082,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.089430894308943,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0887096774193548,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0793650793650793,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.078740157480315,"net_sales":158,"n_recommendations":"1116","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":474,"text_messages_received":162,"service_exclude_live_chat_total":1158,"service_cs_total":1770},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"2400.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilteringUsersResponse', json_decode($response, true));
    }

    public function testKpisFilterUsersAndOneStoreEnabled()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"text_messages_sent":234,"text_messages_received":80,"chat_answer_rate":3.25,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":18,"chat_avg_availability":0.01,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":573,"service_cs_total":879},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"1100.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilterUsersAndOneStoreEnabledResponse', json_decode($response, true));
    }

    public function testKpisFilterUsersAndOneStoreDisabled()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":3,"onboarding_end":4,"user_add":5,"chat_request":6,"chat_answer":7,"response_mail_sent":8,"mail_sent":9,"email_stats_open":10,"email_stats_click":11,"avg_init_resp_sum_times":12,"avg_init_resp_num_responses":13,"chat_answer_rate_requests":14,"chat_answer_rate_answers":15,"total_order_value":16,"n_sales_transactions":17,"feedback":18,"profile_update":19,"content_create":20,"content_update":21,"content_curate":22,"ask_question_req":23,"personal_shopper_req":24,"live_session_start":25,"live_session_end":26,"product_update":27,"deal_update":28,"unsubscribe":29,"new_sale":30,"subscribe":31,"retail_event":32,"raise_concern":33,"shopping_page":34,"unique_visitor":35,"page_hit":36,"sale_duplicate":37,"user_visit":38,"com_ref":39,"soc_ref":40,"retail_hit":41,"social_post":42,"soc_share":43,"moderate_lead":44,"customer_card":45,"help_useful":46,"livesession_register":47,"change_categories":48,"chatsession_register":49,"event_create":50,"event_update":51,"event_delete":52,"event_subscribe":53,"sidebar_view":54,"sidebar_click":55,"footer_view":56,"footer_click":57,"storefront_click":58,"transactional_mail_sent":59,"courtesy_mail_sent":60,"service_total":61,"traffic_total":62,"content_total":63,"number_seconds_available":64,"total_return_value":65,"recommendation_chat":66,"recommendation_compose_message":67,"recommendation_share_email":68,"recommendation_share_facebook":69,"recommendation_share_twitter":70,"recommendation_new_arrivals":71,"recommendation_top_picks":72,"click_top_picks":73,"click_latest_arrivals":74,"click_recommended":75,"avg_selected_top_picks":76,"avg_selected_new_arrivals":77,"salesfloor_visits":78,"text_messages_outbound_api":79,"text_messages_outbound_call":80,"text_messages_outbound_reply":81,"text_messages_inbound":82,"recommendation_text_message":83,"tasks_automated_created":84,"tasks_automated_resolved":85,"tasks_automated_dismissed":86,"tasks_manual_created":87,"tasks_manual_resolved":88,"tasks_manual_dismissed":89,"chat_abandoned":90,"chat_abandonment_time":91,"chat_answer_time":92,"chat_early_redirect":93,"chat_auto_redirect":94,"ask_question_req_email":95,"ask_question_req_text":96,"appointment_req_email":97,"appointment_req_text":98,"personal_shopper_req_email":99,"personal_shopper_req_text":100,"library_share_attempts":101,"lookbook_create":102,"lookbook_update":103,"received_chats_answered_by_other":104,"request_email_sent":105,"request_email_open":106,"request_email_click":107,"compose_email_sent":108,"compose_email_open":109,"compose_email_click":110,"share_email_sent":111,"share_email_open":112,"share_email_click":113,"total_share_sent":114,"chat_abandon_0_29":115,"chat_abandon_30_59":116,"chat_abandon_60_89":117,"chat_abandon_90_120":118,"ask_question_req_chat_handoff":119,"tasks_system_created":120,"tasks_system_resolved":121,"tasks_system_dismissed":122,"tasks_followup_created":123,"tasks_followup_resolved":124,"tasks_followup_dismissed":125,"tasks_corporate_created":126,"tasks_corporate_resolved":127,"tasks_corporate_dismissed":128,"tasks_manual_resolved_sum_time":129,"tasks_manual_dismissed_sum_time":130,"tasks_automated_resolved_sum_time":131,"tasks_automated_dismissed_sum_time":132,"tasks_system_resolved_sum_time":133,"tasks_system_dismissed_sum_time":134,"tasks_followup_resolved_sum_time":135,"tasks_followup_dismissed_sum_time":136,"tasks_corporate_resolved_sum_time":137,"tasks_corporate_dismissed_sum_time":138,"socialshop_post_created":139,"socialshop_total_visit":140,"socialshop_product_click":141,"socialshop_storefront_click":142,"socialshop_sales_count":143,"socialshop_sales_amount_total":144,"tasks_corporate_deleted":145,"ask_question_req_cs_email":146,"ask_question_req_cs_text":147,"appointment_req_cs_email":148,"appointment_req_cs_text":149,"personal_shopper_req_cs_email":150,"personal_shopper_req_cs_text":151,"scheduled_appointments":152,"cancelled_appointment":153,"video_chat_sessions":154,"video_chat_duration":155,"virtual_appointment_sessions":156,"virtual_appointment_duration":157,"avg_init_resp":"-","avg_init_resp_raw":0.9230769230769231,"avg_order_value":0.9411764705882353,"mail_click":1.2222222222222223,"mail_open":1.1111111111111112,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4659090909090908,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4606741573033708,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5411764705882354,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5348837209302326,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.0991735537190082,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.098360655737705,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0887096774193548,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.088,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.078740157480315,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.078125,"text_messages_sent":240,"text_messages_received":82,"chat_answer_rate":2.5,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":13.142857142857142,"chat_avg_availability":0.01,"net_sales":81,"n_recommendations":"566","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":585,"service_cs_total":891},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"10","tot_comm":"0.0000000000","total":"1300.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilterUsersAndOneStoreDisabledResponse', json_decode($response, true));
    }

    public function testKpisFilterStoresAndOneUserEnabledFromDisabledStored()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":null,"tot_comm":"0.0000000000","total":"0.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilterStoresAndOneUserEnabledFromDisabledStoredResponse', json_decode($response, true));
    }

    public function testKpisFilterStoresAndOneUserDisabledFromDisabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":null,"tot_comm":"0.0000000000","total":"0.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilterStoresAndOneUserDisabledFromDisabledStoreResponse', json_decode($response, true));
    }

    public function testKpisFilterStoresAndOneUserEnabledFromEnabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"text_messages_sent":234,"text_messages_received":80,"chat_answer_rate":3.25,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":18,"chat_avg_availability":0.01,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":573,"service_cs_total":879},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"1100.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilterStoresAndOneUserEnabledFromEnabledStoreResponse', json_decode($response, true));
    }

    public function testKpisFilterStoresAndOneUserDisabledFromEnabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":2,"onboarding_end":3,"user_add":4,"chat_request":5,"chat_answer":6,"response_mail_sent":7,"mail_sent":8,"email_stats_open":9,"email_stats_click":10,"avg_init_resp_sum_times":11,"avg_init_resp_num_responses":12,"chat_answer_rate_requests":13,"chat_answer_rate_answers":14,"total_order_value":15,"n_sales_transactions":16,"feedback":17,"profile_update":18,"content_create":19,"content_update":20,"content_curate":21,"ask_question_req":22,"personal_shopper_req":23,"live_session_start":24,"live_session_end":25,"product_update":26,"deal_update":27,"unsubscribe":28,"new_sale":29,"subscribe":30,"retail_event":31,"raise_concern":32,"shopping_page":33,"unique_visitor":34,"page_hit":35,"sale_duplicate":36,"user_visit":37,"com_ref":38,"soc_ref":39,"retail_hit":40,"social_post":41,"soc_share":42,"moderate_lead":43,"customer_card":44,"help_useful":45,"livesession_register":46,"change_categories":47,"chatsession_register":48,"event_create":49,"event_update":50,"event_delete":51,"event_subscribe":52,"sidebar_view":53,"sidebar_click":54,"footer_view":55,"footer_click":56,"storefront_click":57,"transactional_mail_sent":58,"courtesy_mail_sent":59,"service_total":60,"traffic_total":61,"content_total":62,"number_seconds_available":63,"total_return_value":64,"recommendation_chat":65,"recommendation_compose_message":66,"recommendation_share_email":67,"recommendation_share_facebook":68,"recommendation_share_twitter":69,"recommendation_new_arrivals":70,"recommendation_top_picks":71,"click_top_picks":72,"click_latest_arrivals":73,"click_recommended":74,"avg_selected_top_picks":75,"avg_selected_new_arrivals":76,"salesfloor_visits":77,"text_messages_outbound_api":78,"text_messages_outbound_call":79,"text_messages_outbound_reply":80,"text_messages_inbound":81,"recommendation_text_message":82,"tasks_automated_created":83,"tasks_automated_resolved":84,"tasks_automated_dismissed":85,"tasks_manual_created":86,"tasks_manual_resolved":87,"tasks_manual_dismissed":88,"chat_abandoned":89,"chat_abandonment_time":90,"chat_answer_time":91,"chat_early_redirect":92,"chat_auto_redirect":93,"ask_question_req_email":94,"ask_question_req_text":95,"appointment_req_email":96,"appointment_req_text":97,"personal_shopper_req_email":98,"personal_shopper_req_text":99,"library_share_attempts":100,"lookbook_create":101,"lookbook_update":102,"received_chats_answered_by_other":103,"request_email_sent":104,"request_email_open":105,"request_email_click":106,"compose_email_sent":107,"compose_email_open":108,"compose_email_click":109,"share_email_sent":110,"share_email_open":111,"share_email_click":112,"total_share_sent":113,"chat_abandon_0_29":114,"chat_abandon_30_59":115,"chat_abandon_60_89":116,"chat_abandon_90_120":117,"ask_question_req_chat_handoff":118,"tasks_system_created":119,"tasks_system_resolved":120,"tasks_system_dismissed":121,"tasks_followup_created":122,"tasks_followup_resolved":123,"tasks_followup_dismissed":124,"tasks_corporate_created":125,"tasks_corporate_resolved":126,"tasks_corporate_dismissed":127,"tasks_manual_resolved_sum_time":128,"tasks_manual_dismissed_sum_time":129,"tasks_automated_resolved_sum_time":130,"tasks_automated_dismissed_sum_time":131,"tasks_system_resolved_sum_time":132,"tasks_system_dismissed_sum_time":133,"tasks_followup_resolved_sum_time":134,"tasks_followup_dismissed_sum_time":135,"tasks_corporate_resolved_sum_time":136,"tasks_corporate_dismissed_sum_time":137,"socialshop_post_created":138,"socialshop_total_visit":139,"socialshop_product_click":140,"socialshop_storefront_click":141,"socialshop_sales_count":142,"socialshop_sales_amount_total":143,"tasks_corporate_deleted":144,"ask_question_req_cs_email":145,"ask_question_req_cs_text":146,"appointment_req_cs_email":147,"appointment_req_cs_text":148,"personal_shopper_req_cs_email":149,"personal_shopper_req_cs_text":150,"scheduled_appointments":151,"cancelled_appointment":152,"video_chat_sessions":153,"video_chat_duration":154,"virtual_appointment_sessions":155,"virtual_appointment_duration":156,"avg_init_resp":"-","avg_init_resp_raw":0.9166666666666666,"avg_order_value":0.9375,"mail_click":1.25,"mail_open":1.125,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.471264367816092,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4659090909090908,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5476190476190477,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5411764705882354,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.0991735537190082,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.089430894308943,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0887096774193548,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0793650793650793,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.078740157480315,"text_messages_sent":237,"text_messages_received":81,"chat_answer_rate":2.8,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":15.166666666666666,"chat_avg_availability":0.01,"net_sales":79,"n_recommendations":"558","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":579,"service_cs_total":885},"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"9","tot_comm":"0.0000000000","total":"1200.0000"}]}}}}
DATA;

        Fixtures::add('testKpisFilterStoresAndOneUserDisabledFromEnabledStoreResponse', json_decode($response, true));
    }
}
