<?php

namespace SF\rest\Reporting;

use Codeception\Util\Fixtures;
use SF\rest\BaseRest;
use SF\RestTester;

class InactiveFilteringCest extends BaseRest
{
    ///////////////////
    /// Dashboard (1 api call)
    ///

    /**
     * I cannot put the all file because the WP call are done via CURL.
     *
     * @group database_transaction
     */
    public function testDashboardFilteringStores(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        $response = $I->directGET($this->app, '/reporting/ranks-kpis?filter[kpi-period]=MTD&filter[exclude_inactive][store]=1');

        $this->validateArray(Fixtures::get('testDashboardFilteringStoresResponse'), $response);
    }

    /**
     * I cannot put the all file because the WP call are done via CURL.
     *
     * @group database_transaction
     */
    public function testDashboardFilteringStoresUsers(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        $response = $I->directGET($this->app, '/reporting/ranks-kpis?filter[kpi-period]=MTD&filter[exclude_inactive][store]=1&filter[exclude_inactive][user]=1');

        $this->validateArray(Fixtures::get('testDashboardFilteringStoresUsersResponse'), $response);
    }

    /**
     * I cannot put the all file because the WP call are done via CURL.
     *
     * @group database_transaction
     */
    public function testDashboardFilteringUsers(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        $response = $I->directGET($this->app, '/reporting/ranks-kpis?filter[kpi-period]=MTD&filter[exclude_inactive][user]=1');

        $this->validateArray(Fixtures::get('testDashboardFilteringUsersResponse'), $response);
    }

    ///////////////////
    /// Sales (4 apis calls)
    ///

    public function testSalesFilteringStores(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        // Sales total
        $response1 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=yesterday&type%5B0%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=169039427936');
        $this->validateArray(Fixtures::get('testSalesFilteringStoresResponse1'), $response1);

        $response2 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=WTD&type%5B0%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=169039427936');
        $this->validateArray(Fixtures::get('testSalesFilteringStoresResponse2'), $response2);

        // Sales (We can't validate received_date and utc_date since they are dynamic
        $response3 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-sales&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=1690394279371');
        $this->validateArray(Fixtures::get('testSalesFilteringStoresResponse3'), $response3);
    }

    public function testSalesFilteringStoresUsers(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        // Sales total
        $response1 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=yesterday&type%5B0%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&exclude_inactive%5Buser%5D=1&_=169039427936');
        $this->validateArray(Fixtures::get('testSalesFilteringStoresUsersResponse1'), $response1);

        $response2 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=WTD&type%5B0%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&exclude_inactive%5Buser%5D=1&_=169039427936');
        $this->validateArray(Fixtures::get('testSalesFilteringStoresUsersResponse2'), $response2);

        // Sales (We can't validate received_date and utc_date since they are dynamic
        $response3 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-sales&brand=&store=0&exclude_inactive%5Bstore%5D=1&exclude_inactive%5Buser%5D=1&_=1690394279371');
        $this->validateArray(Fixtures::get('testSalesFilteringStoresUsersResponse3'), $response3);
    }

    public function testSalesFilteringUsers(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        // Sales total
        $response1 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=yesterday&type%5B0%5D=get-sales-total&brand=&store=0&exclude_inactive%5Buser%5D=1&_=169039427936');
        $this->validateArray(Fixtures::get('testSalesFilteringUsersResponse1'), $response1);

        $response2 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=WTD&type%5B0%5D=get-sales-total&brand=&store=0&exclude_inactive%5Buser%5D=1&_=169039427936');
        $this->validateArray(Fixtures::get('testSalesFilteringUsersResponse2'), $response2);

        // Sales (We can't validate received_date and utc_date since they are dynamic
        $response3 = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-sales&brand=&store=0&exclude_inactive%5Buser%5D=1&_=1690394279371');
        $this->validateArray(Fixtures::get('testSalesFilteringUsersResponse3'), $response3);
    }

    ///////////////////
    /// Transactions (Nothing to test since same api call then Sales
    ///


    ///////////////////
    /// Kpis
    ///

    public function testKpisFilteringStores(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=1690394279399');
        $this->validateArray(Fixtures::get('testKpisFilteringStoresResponse'), $response);
    }

    public function testKpisFilteringStoresUsers(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&exclude_inactive%5Buser%5D=1&_=1690394279400');
        $this->validateArray(Fixtures::get('testKpisFilteringStoresUsersResponse'), $response);
    }

    public function testKpisFilteringUsers(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Buser%5D=1&_=1690394279401');
        $this->validateArray(Fixtures::get('testKpisFilteringUsersResponse'), $response);
    }

    public function testKpisFilterStoresAndOneUserEnabledFromDisabledStored(RestTester $I)
    {
        $I->wantTo("Test - kpis exclude inactive stores - select one user from a disabled store");
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        // Exclude inactive store
        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=10&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=1690394279401');
        $this->validateArray(Fixtures::get('testKpisFilterStoresAndOneUserEnabledFromDisabledStoredResponse'), $response);
    }

    public function testKpisFilterStoresAndOneUserDisabledFromDisabledStore(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=11&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=1690394279401');
        $this->validateArray(Fixtures::get('testKpisFilterStoresAndOneUserDisabledFromDisabledStoreResponse'), $response);
    }

    public function testKpisFilterStoresAndOneUserEnabledFromEnabledStore(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=8&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=1690394279401');
        $this->validateArray(Fixtures::get('testKpisFilterStoresAndOneUserEnabledFromEnabledStoreResponse'), $response);
    }

    public function testKpisFilterStoresAndOneUserDisabledFromEnabledStore(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=9&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=0&exclude_inactive%5Bstore%5D=1&_=1690394279401');
        $this->validateArray(Fixtures::get('testKpisFilterStoresAndOneUserDisabledFromEnabledStoreResponse'), $response);
    }

    /////////////
    /// Kpis filter inactive user with one store (enabled/disabled)

    public function testKpisFilterUsersAndOneStoreEnabled(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        // Only 1 store + exclude inactive user => 1 kpi only since the other is another store
        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=1111&exclude_inactive%5Buser%5D=1&_=1690394279401');
        $this->validateArray(Fixtures::get('testKpisFilterUsersAndOneStoreEnabledResponse'), $response);
    }

    public function testKpisFilterUsersAndOneStoreDisabled(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        // You need to have "store-management" validateFilters() to access everyone's kpis.
        $I->applyAuthorization($I, '12', 'admin');

        // Only 1 store + exclude inactive user => 1 kpi only since the other is another store
        $response = $I->doGetWP($I, '/wp-admin/admin-ajax.php?user=0&timezone=America%2FNew_York&action=sfreports&period=30days&type%5B0%5D=get-marketing&type%5B1%5D=get-sales-total&brand=&store=1112&exclude_inactive%5Buser%5D=1&_=1690394279401');

        $this->validateArray(Fixtures::get('testKpisFilterUsersAndOneStoreDisabledResponse'), $response);
    }
}
