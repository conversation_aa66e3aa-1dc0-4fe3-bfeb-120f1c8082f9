<?php

namespace SF\rest\Reps;

use Codeception\Util\Fixtures;
use SF\rest\BaseRest;
use SF\RestTester;

class OnboardingCest extends BaseRest
{
    /** @group database_transaction */
    public function testCreateTokenWithoutEmail(RestTester $I)
    {
        $I->wantToTest('creating an invite token with no corporate email');

        $this->app['configs']['retailer.corporate-email.required'] = false;

        $params = [
            "token" => 'my-rep-id',
            "retailer_rep_id" => 'my-rep-id',
            "rep_first_name" => 'Reppy',
            "rep_last_name" => "Reperson",
            "store" => 1003,
        ];

        $userId = 7;
        $userName = 'user6';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPost($I, '/rep-onboarding', $params);

        $I->assertEquals(1, $response->ID);
        $I->assertEquals('my-rep-id', $response->token);
        $I->assertNull($response->wp_user_id);
        $I->assertNull($response->rep_email);

        $I->seeInDatabase(
            'sf_rep_onboarding',
            [
                'id' => 1,
                'token' => 'my-rep-id',
                'rep_email' => null,
            ]
        );
    }

    /** @group database_transaction */
    public function testCreateTokenWithEmailRequire(RestTester $I)
    {
        $I->wantToTest('creating an invite token with email required');

        $params = [
            "token" => 'my-rep-id',
            "retailer_rep_id" => 'my-rep-id',
            "rep_first_name" => 'Reppy',
            "rep_last_name" => "Reperson",
            "store" => 1003,
            'rep_email' => '<EMAIL>'
        ];

        $userId = 7;
        $userName = 'user6';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPost($I, '/rep-onboarding', $params);

        $I->assertEquals('my-rep-id', $response->token);
        $I->assertNull($response->wp_user_id);
        $I->assertEquals('<EMAIL>', $response->rep_email);

        $I->seeInDatabase(
            'sf_rep_onboarding',
            [
                'id' => $response->ID,
                'rep_email' => '<EMAIL>',
                'token' => 'my-rep-id'
            ]
        );
    }

    /** @group database_transaction */
    public function testCreateTokenWithEmailRequireNoEmailProvided(RestTester $I)
    {
        $I->wantToTest('creating an invite token with email required but no email provided');
        $this->app['configs']['retailer.corporate-email.required'] = true;

        $params = [
            "token" => 'my-rep-id',
            "retailer_rep_id" => 'my-rep-id',
            "rep_first_name" => 'Reppy',
            "rep_last_name" => "Reperson",
            "store" => 'fake-mall'
        ];

        $userId = 7;
        $userName = 'user6';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $I->doDirectPost($I, '/rep-onboarding', $params);
        $I->assertEquals(400, $I->grabResponseStatusCode());
    }

    /** @group database_transaction */
    public function testGetAllInvitedUsers(RestTester $I)
    {
        $I->wantToTest('get all invited users');

        $this->insertFixtureGroup($I, 'OnBoardingUsers');

        $userId = 7;
        $userName = 'user6';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, '/rep-onboarding?filter[wp_user_id]=null&page=0&per_page=50&sort=fullName');

        $this->debug($response);

        $response = json_decode(json_encode($response), true);

        unset($response['pagination']);

        $I->assertEquals(2, count($response));

        $user1 = $response[0];

        $I->assertEquals('a', $user1['rep_first_name']);
        $I->assertEquals('Corporate Admin', $user1['group_label']);
        $I->assertEquals('Other Mall', $user1['store_name']);
    }

    /** @group database_transaction */
    public function testGetAllInvitedUsersWhenCurrentUserIsStoreManager(RestTester $I)
    {
        $I->wantToTest('Get all invited users when current user is store manager');

        $this->insertFixtureGroup($I, 'getOnboardingUsersWithDifferentGroups');

        $userId = 8;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, '/rep-onboarding?filter[wp_user_id]=null&page=0&per_page=50');

        $response = json_decode(json_encode($response), true);

        unset($response['pagination']);
        $I->assertCount(2, $response);

        $user1 = $response[0];
        $user2 = $response[1];

        $I->assertEquals(1, $user1['group']);
        $I->assertEquals('User', $user1['group_label']);
        $I->assertEquals(2, $user2['group']);
        $I->assertEquals('Store Manager', $user2['group_label']);
    }

    /** @group database_transaction */
    public function testGetAllInvitedUsersWhenCurrentUserIsManager(RestTester $I)
    {
        $I->wantToTest('Get all invited users when current user is manager');

        $this->insertFixtureGroup($I, 'getOnboardingUsersWithDifferentGroups');

        $userId = 9;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, '/rep-onboarding?filter[wp_user_id]=null&page=0&per_page=50');

        $response = json_decode(json_encode($response), true);

        unset($response['pagination']);

        $I->assertCount(3, $response);

        $user1 = $response[0];
        $user2 = $response[1];
        $user3 = $response[2];

        $I->assertEquals(1, $user1['group']);
        $I->assertEquals('User', $user1['group_label']);
        $I->assertEquals(2, $user2['group']);
        $I->assertEquals('Store Manager', $user2['group_label']);
        $I->assertEquals(3, $user3['group']);
        $I->assertEquals('Management', $user3['group_label']);
    }

    /** @group database_transaction */
    public function testGetAllInvitedUsersWhenCurrentUserIsCorpAdmin(RestTester $I)
    {
        $I->wantToTest('Get all invited users when current user is corp admin');

        $this->insertFixtureGroup($I, 'getOnboardingUsersWithDifferentGroups');

        $userId = 6;
        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, '/rep-onboarding?filter[wp_user_id]=null&page=0&per_page=50');

        $response = json_decode(json_encode($response), true);

        unset($response['pagination']);

        $I->assertCount(4, $response);

        $user1 = $response[0];
        $user2 = $response[1];
        $user3 = $response[2];
        $user4 = $response[3];

        $I->assertEquals(1, $user1['group']);
        $I->assertEquals('User', $user1['group_label']);
        $I->assertEquals(2, $user2['group']);
        $I->assertEquals('Store Manager', $user2['group_label']);
        $I->assertEquals(3, $user3['group']);
        $I->assertEquals('Management', $user3['group_label']);
        $I->assertEquals(4, $user4['group']);
        $I->assertEquals('Corporate Admin', $user4['group_label']);
    }

    /** @group database_transaction */
    public function testGetAllInvitedUsersWhenCurrentUserIsAdmin(RestTester $I)
    {
        $I->wantToTest('get all invited users');

        $this->insertFixtureGroup($I, 'getOnboardingUsersWithDifferentGroups');

        $userId = 7;
        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, '/rep-onboarding?filter[wp_user_id]=null&page=0&per_page=50');

        $response = json_decode(json_encode($response), true);

        unset($response['pagination']);

        $I->assertEquals(5, count($response));

        $user1 = $response[0];
        $user2 = $response[1];
        $user3 = $response[2];
        $user4 = $response[3];
        $user5 = $response[4];

        $I->assertEquals(1, $user1['group']);
        $I->assertEquals('User', $user1['group_label']);
        $I->assertEquals(2, $user2['group']);
        $I->assertEquals('Store Manager', $user2['group_label']);
        $I->assertEquals(3, $user3['group']);
        $I->assertEquals('Management', $user3['group_label']);
        $I->assertEquals(4, $user4['group']);
        $I->assertEquals('Corporate Admin', $user4['group_label']);
        $I->assertEquals(5, $user5['group']);
        $I->assertEquals('Salesfloor Admin', $user5['group_label']);
    }

    /** @group database_transaction */
    public function testUpdate(RestTester $I)
    {
        $I->wantToTest('update invited users');

        $this->insertFixtureGroup($I, 'OnBoardingUsers');
        $onboardingRepToUpdate = $this->getMockUpRepToUpdate();

        $onboardingRepToUpdate['rep_first_name'] = "first_name_update_test";
        $onboardingRepToUpdate['rep_last_name']  = "last_name_update_test";
        $onboardingRepToUpdate['rep_email']      = "<EMAIL>";

        $response = $I->doDirectPut($I, '/rep-onboarding/' . $onboardingRepToUpdate['ID'], $onboardingRepToUpdate);
        $response = json_decode(json_encode($response), true);

        $I->assertEquals($onboardingRepToUpdate['rep_first_name'], $response['rep_first_name']);
        $I->assertEquals($onboardingRepToUpdate['rep_last_name'], $response['rep_last_name']);
        $I->assertEquals($onboardingRepToUpdate['rep_email'], $response['rep_email']);
    }

    /** @group database_transaction */
    public function testUpdateError(RestTester $I)
    {
        $I->wantToTest('update invited users some error happen');

        $this->insertFixtureGroup($I, 'OnBoardingUsers');
        $onboardingRepToUpdate = $this->getMockUpRepToUpdate();

        $onboardingRepToUpdate['rep_first_name'] = "first_name_update_test_too_long_first_name_update_test_too_long";
        $onboardingRepToUpdate['rep_last_name']  = "last_name_update_test_too_long_last_name_update_test_too_long";
        $onboardingRepToUpdate['rep_email']      = "<EMAIL>";

        $response = $I->doDirectPut($I, '/rep-onboarding/' . $onboardingRepToUpdate['ID'], $onboardingRepToUpdate);
        $response = json_decode(json_encode($response), true);

        $I->assertStringContainsString('rep_first_name', $response['error']);
        $I->assertStringContainsString('rep_last_name', $response['error']);
    }

    /** @group database_transaction */
    public function testGetExistingUser(RestTester $I)
    {
        $I->wantToTest('get an existing user');

        $this->insertFixtureGroup($I, 'getExistingUser');

        $response = $I->doDirectGet($I, '/reps/9?hack=2&fields=user_login,user_nicename,user_email,user_status,display_name,user_alias,store_data,type,employee_id,group,selling_mode,name,title,first_name,last_name,storefront_url,locked_at,shop_feed_url,group_label,created_user_name,updated_user_name,retailer_shop_feed,retailer_text_messaging&page=0&per_page=50&include-phone-number=1');

        $response = json_decode(json_encode($response), true);

        $I->assertEquals('user4', $response['created_user_name']);
        $I->assertEquals('2019-06-11 11:00:00', $response['user_registered']);
        $I->assertEquals('user3', $response['updated_user_name']);
        $I->assertEquals('2019-06-11 15:00:00', $response['updated_at']);
        $I->assertEquals('User', $response['group_label']);
        $I->assertEquals('Fake Mall', $response['store_data']['name']);
    }

    /** @group database_transaction */
    public function testCreateTokenWithLargeTokenAsValidationFail(RestTester $I)
    {
        $I->wantToTest('creating an invite token with very large token (char > 45) fails validation and returns appropriate response');

        $params = [
            "token" => '8333444433222233444444444455677744333222222223344590999584885848594885993003934899398484888888888888848488383838384848484838388249343',
            "rep_first_name" => 'Reppy',
            "rep_last_name" => "Reperson",
            "store" => 'fake-mall',
            'rep_email' => '<EMAIL>'
        ];

        $userId = 7;
        $userName = 'user6';
        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectPost($I, '/rep-onboarding', $params);

        $I->assertStatusCode(400);
        $I->assertStringContainsString('"token":["This value is too long', $response->error);
    }

    /**
     * @group database_transaction
     */
    public function testOnboardingRepPromoteSSODuplicateAlias(RestTester $I)
    {
        $this->app['configs']['retailer.sso.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testOnboardingRepPromoteSSODuplicateAlias');

        $this->setAuthOnboarding($I, "12222");

        $params = [
            'creation_source' => 'feed',
            'device_id' => null,
            'id' => null,
            'locale' => 'en_US',
            'rep_email' => '<EMAIL>',
            'rep_first_name' => 'test',
            'rep_last_name' => 'test',
            'store' => '1003',
            'token' => '12222',
        ];

        /** @var RepOnboarding $onBoardedUser */
        $promote = $I->directPOST($this->app, "onboarding-promote", $params);

        $I->seeInDatabase('wp_users', [
            'ID' => $promote->wp_user_id,
            'user_login' => 'duplicate1', // Because 'duplicate' was used by another rep's alias.
        ]);
    }

    /**
     * @group database_transaction
     */
    public function testOnboardingRepPromoteSSODuplicateLogin(RestTester $I)
    {
        $this->app['configs']['retailer.sso.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testOnboardingRepPromoteSSODuplicateLogin');

        $this->setAuthOnboarding($I, "12222");

        $params = [
            'creation_source' => 'feed',
            'device_id' => null,
            'id' => null,
            'locale' => 'en_US',
            'rep_email' => '<EMAIL>',
            'rep_first_name' => 'test',
            'rep_last_name' => 'test',
            'store' => '1003',
            'token' => '12222',
        ];

        /** @var RepOnboarding $onBoardedUser */
        $promote = $I->directPOST($this->app, "onboarding-promote", $params);

        $I->seeInDatabase('wp_users', [
            'ID' => $promote->wp_user_id,
            'user_login' => 'test_user81', // Because 'test_rep8' was used by another rep's alias.
        ]);
    }

    private function setAuthOnboarding($I, $token)
    {
        $I->setHeaders([
            'authorization' => $this->app['auth.controller']->createOnboardingAccessToken($token)
        ]);
    }

    /** @group database_transaction */
    public function getMockUpRepToUpdate()
    {
        $user = Fixtures::get('OnBoardingUsers');
        return $user['sf_rep_onboarding'][1];
    }
}
