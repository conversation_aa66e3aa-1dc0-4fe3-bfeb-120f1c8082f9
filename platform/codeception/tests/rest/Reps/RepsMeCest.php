<?php

declare(strict_types=1);

namespace SF\rest\Reps;

use SF\RestTester;
use SF\rest\BaseRest;
use SF\Helper\Traits\Asserts;

/**
 * @group database_transaction
 */
class RepsMeCest extends BaseRest
{
    use Asserts;

    public function testGetRepsMeRepModeGroup1Attributes(RestTester $I)
    {
        $I->wantTo("Test reps/me in rep mode - attributes");

        // Json API endpoint (v2 is hardcoded in mobile, so let's keep it this way)
        $response = $I->doDirectGet($I, "v2/reps/me"); // Reggie

        $this->debug($response);

        $I->assertEquals('rep', $response->data->type);
        $I->assertEquals('1', $response->data->id);

        $this->validateArray([
            'user_login' => 'reggie',
            'user_nicename' => 'wtf_is_this',
            'user_email' => '<EMAIL>',
            'user_status' => 1,
            'display_name' => 'should get overriden',
            'user_alias' => 'lol56eff3d6ed8b91',
            'store' => 1003,
            'employee_id' => 2546011,
            'group' => 1,
            'selling_mode' => 1,
            'user_registered' => '2001-01-01 00:00:00',
            'is_photo' => 0,
            'sso_auth' => 0,
            'rep_type' => 'rep', // Because type is reserved, we prefix it with the schema name
        ], $response->data->attributes);

        $this->validateArrayRegExp([
            'components' => [
                'dashboard.kpis',
                'dashboard.requests',
                'dashboard.messages',
                'dashboard.appointments',
                'dashboard.tasks',
            ],
            // The avatar's version in cloudinary always change on each test pass.
            'avatar' => 'https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_5.jpg,g_face,h_250,w_250/v[0-9]*/dev/tests/reggie',
            'permissions' => [], // From GroupPermissionService // getAccessiblePermissions()
            'first_name' => 'Sally', // This can be found in relationship
            'last_name' => 'Sellers', // This can be found in relationship
            'findrep_name' => 'Sally', // This is custom 'prepare_user_name' display information
            'alias' => 'lol56eff3d6ed8b91', // This is because in team mode, we don't use the rep's alias
        ], $response->data->attributes->virtual_fields);
    }

    public function testGetRepsMeRepModeFindRepName(RestTester $I)
    {
        $I->wantTo("Test reps/me in rep mode - virtual field - findrep_name");

        // This is needed because otherwise it's the default
        $this->app['configs']['name_fmt.en_US.prepare_user_name']  = '{Fn} {Ln}';

        // Json API endpoint (v2 is hardcoded in mobile, so let's keep it this way)
        $response = $I->doDirectGet($I, "v2/reps/me"); // Reggie

        $this->validateArray([
            'components' => [
                'dashboard.kpis',
                'dashboard.requests',
                'dashboard.messages',
                'dashboard.appointments',
                'dashboard.tasks',
            ],
            // The avatar's version in cloudinary always change on each test pass.
            'permissions' => [], // From GroupPermissionService // getAccessiblePermissions()
            'first_name' => 'Sally', // This can be found in relationship
            'last_name' => 'Sellers', // This can be found in relationship
            'findrep_name' => 'Sally Sellers', // This is custom 'prepare_user_name' display information
            'alias' => 'lol56eff3d6ed8b91', // This is because in team mode, we don't use the rep's alias
        ], $response->data->attributes->virtual_fields);
    }

    public function testGetRepsMeRepModeGroup1Relationships(RestTester $I)
    {
        $I->wantTo("Test reps/me in rep mode - relationships");

        // Insert fake phone-number and onboarding for the test
        $this->insertFixtureGroup($I, 'testGetRepsMeRepModeGroup1Attributes');

        // Json API endpoint (v2 is hardcoded in mobile, so let's keep it this way)
        $response = $I->doDirectGet($I, "v2/reps/me?include=store,user-phone-number,user-meta,specialty,specialty.category,rep-onboarding"); // Reggie

        $avatar = $response->data->attributes->virtual_fields->avatar;
        // The avatar's version in cloudinary always change on each test pass.
        // So we need to replace the version in the response with a fixed one.
        // "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_5.jpg,g_face,h_250,w_250/v1737745357/dev/tests/reggie"
        $response->data->attributes->virtual_fields->avatar = preg_replace(
            '#/v[0-9]+/#',
            '/v1/',
            $avatar
        );

        // Sort all array values to have a consistent output
        usort($response->data->relationships->{"user-meta"}->data, function ($a, $b) {
            return $a->id <=> $b->id;
        });

        usort($response->included, function ($a, $b) {
            return $a->type <=> $b->type ?: $a->id <=> $b->id;
        });

        $this->debug($response);

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($response, $jsonFile);
    }

    /////////////////// TEAM MODE ///////////////////

    public function testGetRepsMeTeamModeGroup1Attributes(RestTester $I)
    {
        $I->wantTo("Test reps/me in team mode - attributes");

        $this->app['configs']['retailer.storepage_mode'] = true;

        $this->insertFixtureGroup($I, 'testGetRepsMeTeamModeGroup1');

        $this->setTokenInRequestHeader($I, 99, 'test1');

        // Json API endpoint (v2 is hardcoded in mobile, so let's keep it this way)
        $response = $I->doDirectGet($I, "v2/reps/me"); // User 100 (custom)

        $this->debug($response);

        $I->assertEquals('rep', $response->data->type);
        $I->assertEquals('99', $response->data->id);

        $this->validateArray([
            'user_login' => 'test1',
            'user_nicename' => 'test1',
            'user_email' => '<EMAIL>',
            'user_status' => 1,
            'display_name' => 'test1',
            'user_alias' => '',
            'store' => 2222,
            'employee_id' => null,
            'group' => 1,
            'selling_mode' => 1,
            'user_registered' => '2001-01-01 00:00:00',
            'is_photo' => 0,
            'sso_auth' => 0,
            'rep_type' => 'rep', // Because type is reserved, we prefix it with the schema name
        ], $response->data->attributes);

        $this->validateArrayRegExp([
            'components' => [
                'dashboard.kpis',
                'dashboard.requests',
                'dashboard.messages',
                'dashboard.appointments',
                'dashboard.tasks',
            ],
            // The avatar's version in cloudinary always change on each test pass.
            'avatar' => 'https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_2.jpg,g_face,h_250,w_250/v[0-9]*/dev/tests/test1',
            'permissions' => [], // From GroupPermissionService // getAccessiblePermissions()
            'first_name' => 'test-first-name', // This can be found in relationship. Use null and not '' when using RegExp
            'last_name' => 'test-last-name', // This can be found in relationship.  Use null and not '' when using RegExp
            'findrep_name' => '', // This is custom 'prepare_user_name' display information
            'reporting_pref' => null, // Use null and not '' when using RegExp since
            'alias' => 'team-test1', // This is because in team mode, we don't use the rep's alias
        ], $response->data->attributes->virtual_fields);
    }

    public function testGetRepsMeTeamModeGroup1Relationships(RestTester $I)
    {
        $I->wantTo("Test reps/me in team mode - relationships");

        $this->app['configs']['retailer.storepage_mode'] = true;

        $this->insertFixtureGroup($I, 'testGetRepsMeTeamModeGroup1');

        $this->setTokenInRequestHeader($I, 99, 'test1');

        // Json API endpoint (v2 is hardcoded in mobile, so let's keep it this way)
        $response = $I->doDirectGet($I, "v2/reps/me?include=store,user-phone-number,user-meta,specialty,specialty.category,rep-onboarding");

        $this->debug($response);

        ///  Included section

        // included store
        $this->validateArray([
            'attributes' => [
                'name' => 'test store',
                'country' => 'CA',
                'region' => 'QC',
                'city' => 'Montreal',
                'address' => '1455 Peel Streets',
                'postal' => 'H3A 1T5',
                'phone' => '************',
                'hours' => '',
                'store_user_id' => 100,
                'locale' => 'en_US',
                'timezone' => 'America/Los_Angeles',
                'sf_identifier' => 'store-one',
                'retailer_store_id' => 'ny1',
                'is_virtual' => 0,
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'store', 2222));

        $metaId = $this->getFirst($I->grabColumnFromDatabase('wp_usermeta', 'umeta_id', [
            'meta_key' => 'first_name',
            'user_id' => 99,
        ]));

        // included user-meta
        $this->validateArray([
            'attributes' => [
                'umeta_id' => $metaId,
                'user_id' => 99,
                'meta_key' => 'first_name',
                'meta_value' => 'test-first-name',
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'user-meta', $metaId));

        $metaId = $this->getFirst($I->grabColumnFromDatabase('wp_usermeta', 'umeta_id', [
            'meta_key' => 'last_name',
            'user_id' => 99,
        ]));

        $this->validateArray([
            'attributes' => [
                'umeta_id' => $metaId,
                'user_id' => 99,
                'meta_key' => 'last_name',
                'meta_value' => 'test-last-name',
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'user-meta', $metaId));

        $metaId = $this->getFirst($I->grabColumnFromDatabase('wp_usermeta', 'umeta_id', [
            'meta_key' => 'nickname',
            'user_id' => 99,
        ]));

        $this->validateArray([
            'attributes' => [
                'umeta_id' => $metaId,
                'user_id' => 99,
                'meta_key' => 'nickname',
                'meta_value' => 'test-nick-name',
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'user-meta', $metaId));

        $metaId = $this->getFirst($I->grabColumnFromDatabase('wp_usermeta', 'umeta_id', [
            'meta_key' => 'rep_introduction',
            'user_id' => 99,
        ]));

        $this->validateArray([
            'attributes' => [
                'umeta_id' => $metaId,
                'user_id' => 99,
                'meta_key' => 'rep_introduction', // No idea what this is (Legacy)
                'meta_value' => 'test-intro',
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'user-meta', $metaId));

        $metaId = $this->getFirst($I->grabColumnFromDatabase('wp_usermeta', 'umeta_id', [
            'meta_key' => 'title',
            'user_id' => 99,
        ]));

        $this->validateArray([
            'attributes' => [
                'umeta_id' => $metaId,
                'user_id' => 99,
                'meta_key' => 'title',
                'meta_value' => 'test-title',
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'user-meta', $metaId));

        $relationshipId = $this->getFirst($I->grabColumnFromDatabase('sf_user_phone_number', 'id', [
            'user_id' => 100,
        ]));

        // included phone number
        $this->validateArray([
            'attributes' => [
                'user_id' => 100,
                'phone_number' => '+15141231234',
                'created_by_user_id' => 1,
            ],
        ], $this->getIncludedFromJsonApi($response->included, 'user-phone-number', $relationshipId));
    }

    public function testGetRepsMeTeamModeGroupAdminWithoutStoreAttributes(RestTester $I)
    {
        $I->wantTo("Test reps/me in team mode - admin without store - attributes");

        $this->app['configs']['retailer.storepage_mode'] = true;

        $this->insertFixtureGroup($I, 'testGetRepsMeTeamModeGroup1');

        $this->setTokenInRequestHeader($I, 98, 'test2');

        // Json API endpoint (v2 is hardcoded in mobile, so let's keep it this way)
        $response = $I->doDirectGet($I, "v2/reps/me"); // User 100 (custom)

        $this->debug($response);

        // The avatar's version in cloudinary always change on each test pass.
        // So we need to replace the version in the response with a fixed one.
        // "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_2.jpg,g_face,h_250,w_250/v1737745357/dev/tests/test2"
        $response->data->attributes->virtual_fields->avatar = preg_replace(
            '#/v[0-9]+/#',
            '/v1/',
            $response->data->attributes->virtual_fields->avatar
        );
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($response, $jsonFile);
    }
}
