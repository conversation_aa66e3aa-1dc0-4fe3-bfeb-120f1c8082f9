<?php

namespace SF\rest\Reps;

use Codeception\Util\Fixtures;

class Fixture
{
    public function testGetRepsMeTeamModeGroup1Attributes()
    {
        Fixtures::add('testGetRepsMeTeamModeGroup1', [
            'wp_users' => [
                [
                    'ID' => 100,
                    'user_login' => 'team-test1',
                    'user_pass' => '',
                    'user_nicename' => 'team-test1',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'team-test1',
                    'description' => null,
                    'photo' => null,
                    'last_login' => null,
                    'localization' => null,
                    'feature' => null,
                    'status' => null,
                    'store' => 2222,
                    'type' => 'store',
                    'commission_rate' => 0.00,
                    'employee_id' => null,
                    'group' => 1,
                    'selling_mode' => 1,
                    'isPhoto' => 0,
                    'locked_at' => null,
                    'locale' => null,
                    'creation_source' => 'invite',
                    'shop_feed' => 0,
                    'sso_auth' => 0,
                ],
                [
                    'ID' => 99,
                    'user_login' => 'test1',
                    'user_pass' => '',
                    'user_nicename' => 'test1',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'test1',
                    'description' => null,
                    'photo' => null,
                    'last_login' => null,
                    'localization' => null,
                    'feature' => null,
                    'status' => null,
                    'store' => 2222,
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'employee_id' => null,
                    'group' => 1,
                    'selling_mode' => 1,
                    'isPhoto' => 0,
                    'locked_at' => null,
                    'locale' => null,
                    'creation_source' => 'invite',
                    'shop_feed' => 0,
                    'sso_auth' => 0,
                ],
                [
                    'ID' => 98,
                    'user_login' => 'test2',
                    'user_pass' => '',
                    'user_nicename' => 'test2',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'test2',
                    'description' => null,
                    'photo' => null,
                    'last_login' => null,
                    'localization' => null,
                    'feature' => null,
                    'status' => null,
                    'store' => 0,
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'employee_id' => null,
                    'group' => 4,
                    'selling_mode' => 1,
                    'isPhoto' => 0,
                    'locked_at' => null,
                    'locale' => null,
                    'creation_source' => 'invite',
                    'shop_feed' => 0,
                    'sso_auth' => 0,
                ]
            ],
            'sf_store' => [
                [
                    'store_id' => 2222,
                    'name' => 'test store',
                    'timezone' => 'America/Los_Angeles',
                    'sf_identifier' => 'store-one',
                    'store_user_id' => 100,
                    'retailer_store_id' => 'ny1',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ]
            ],
            'sf_user_phone_number' =>  [
                [
                    'user_id' => 100, // In team mode, the phone number is linked to the store user.
                    'phone_number' => '+15141231234',
                    'created_by_user_id' => 1,
                ]
            ],
            'wp_usermeta' => [
                [
                    'user_id' => 100,
                    'meta_key' => 'first_name',
                    'meta_value' => 'first',
                ],
                [
                    'user_id' => 100,
                    'meta_key' => 'last_name',
                    'meta_value' => 'last',
                ],
                [
                    'user_id' => 100,
                    'meta_key' => 'wp_user_level',
                    'meta_value' => 2,
                ],
                [
                    'user_id' => 100,
                    'meta_key' => 'wp_capabilities',
                    'meta_value' =>  'a:1:{s:6:"author";b:1;}', // Legacy, prob. not needed
                ],
                [
                    'user_id' => 99,
                    'meta_key' => 'first_name',
                    'meta_value' =>  'test-first-name',
                ],
                [
                    'user_id' => 99,
                    'meta_key' => 'last_name',
                    'meta_value' =>  'test-last-name',
                ],
                [
                    'user_id' => 99,
                    'meta_key' => 'nickname',
                    'meta_value' =>  'test-nick-name',
                ],
                [
                    'user_id' => 99,
                    'meta_key' => 'rep_introduction',
                    'meta_value' =>  'test-intro',
                ],
                [
                    'user_id' => 99,
                    'meta_key' => 'title',
                    'meta_value' =>  'test-title',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 2222,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'fr_CA',
                    'store_id'   => 2222,
                    'is_default' => 0
                ],
            ],
        ]);
    }

    public function testGetRepsMeRepModeGroup1Attributes()
    {
        Fixtures::add('testGetRepsMeRepModeGroup1Attributes', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 1,
                    'phone_number' => '************',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_rep_onboarding' => [
                [
                    'token' => '12221',
                    'retailer_rep_id' => '12221',
                    'rep_first_name' => 'test',
                    'rep_last_name' => 'test',
                    'rep_email' => '<EMAIL>',
                    'store' => 1003,
                    'group' => 1,
                    'selling_mode' => 1,
                    'onboarding_completed' => 1,
                    'wp_user_id' => 1,
                ]
            ]
        ]);
    }

    public function onBoarding()
    {
        Fixtures::add('OnBoardingUsers', [
            'sf_rep_onboarding' => [
                [
                    'ID'  => 1,
                    'token'                => '12221',
                    'retailer_rep_id'      => '12221',
                    'rep_first_name'       => 'b',
                    'rep_last_name'        => 'e',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 1,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
                [
                    'ID'                   => 2,
                    'token'                => '12222',
                    'retailer_rep_id'      => '12222',
                    'rep_first_name'       => 'a',
                    'rep_last_name'        => 'd',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 2003,
                    'group'                => 4,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
            ],
        ]);

        Fixtures::add('getOnboardingUsersWithDifferentGroups', [
            'wp_users' => [
                [
                    'ID'                  => 8,
                    'user_login'          => 'test_user8',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User 8',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
                [
                    'ID'                  => 9,
                    'user_login'          => 'test_user9',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User 9',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'group'               => 3,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ]
            ],
            'sf_rep_onboarding' => [
                [
                    'ID' => 1,
                    'token'                => '12221',
                    'retailer_rep_id'      => '12221',
                    'rep_first_name'       => 'b',
                    'rep_last_name'        => 'e',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 1,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
                [
                    'ID' => 2,
                    'token'                => '12222',
                    'retailer_rep_id'      => '12222',
                    'rep_first_name'       => 'a',
                    'rep_last_name'        => 'd',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 2,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
                [
                    'ID' => 3,
                    'token'                => '12223',
                    'retailer_rep_id'      => '12223',
                    'rep_first_name'       => 'a',
                    'rep_last_name'        => 'd',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 3,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
                [
                    'ID' => 4,
                    'token'                => '12224',
                    'retailer_rep_id'      => '12224',
                    'rep_first_name'       => 'a',
                    'rep_last_name'        => 'd',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 4,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
                [
                    'ID' => 5,
                    'token'                => '12225',
                    'retailer_rep_id'      => '12225',
                    'rep_first_name'       => 'a',
                    'rep_last_name'        => 'd',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 5,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
            ],
        ]);
    }

    public function existingUsers()
    {
        Fixtures::add('getExistingUser', [
            'wp_users'          => [
                [
                    'ID'                  => 9,
                    'user_login'          => 'test_user',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
            'sf_rep_onboarding' => [
                [
                    'token'           => '12221',
                    'retailer_rep_id' => '12221',
                    'rep_first_name'  => 'b',
                    'rep_last_name'   => 'e',
                    'rep_email'       => '<EMAIL>',
                    'wp_user_id'      => 9,
                    'store'           => 1003,
                    'group'           => 1,
                    'selling_mode'    => 1,
                    'creation_source' => 'invite',
                    'created_by'      => 5,
                    'created_at'      => '2019-06-11 10:00:00',
                    'updated_by'      => 4,
                    'updated_at'      => '2019-06-11 12:00:00',
                ],
            ],
        ]);
    }

    public function insertSortDataSuite()
    {
        Fixtures::add('repSortDataSuite', [
            'wp_users'          => [
                [
                    'ID'                  => 101,
                    'user_login'          => 'test_user',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 2003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
                [
                    'ID'                  => 102,
                    'user_login'          => 'a_test_user',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'a_test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'a test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 2003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
                [
                    'ID'                  => 103,
                    'user_login'          => 'an_asia_user',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'an_asia_user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'an asia User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 2003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
            'wp_usermeta' => [
                [
                    'user_id'    => 101,
                    'meta_key'   => 'first_name',
                    'meta_value' => 'test'
                ],
                [
                    'user_id'    => 102,
                    'meta_key'   => 'first_name',
                    'meta_value' => 'a_test'
                ],
                [
                    'user_id'    => 103,
                    'meta_key'   => 'first_name',
                    'meta_value' => '阿部'
                ],
            ],
        ]);
    }

    /**
     * Returns a list of requesters, along with the data,
     * to perform update requests and check responses.
     */
    public function requestersDataForUpdate()
    {
        $putData = [
            'ID' => 3,
            'user_login' => 'user2',
            'user_email' => '<EMAIL>',
            'store' => '1003',
            'type' => 'rep',
            'group' => '1'
        ];

        Fixtures::add('requestersDataForUpdate', [
            [ // owner
                'requesterId' => 3,
                'requesterLogin' => 'user2',
                'putData' => $putData,
                'expectedStatusCode' => 200,
                'expectedContent' => $putData
            ], [ // admin
                'requesterId' => 6,
                'requesterLogin' => 'user5',
                'putData' => $putData,
                'expectedStatusCode' => 200,
                'expectedContent' => $putData
            ], [ // intruder
                'requesterId' => 1,
                'requesterLogin' => 'reggie',
                'putData' => ['ID' => 6, 'user_login' => 'user5'] + $putData,
                'expectedStatusCode' => 403,
                'expectedContent' => [
                    'error' => 'You are not authorized to access this resource'
                ]
            ]
        ]);
    }

    /**
     * Returns a list of groups, to perform update requests and check responses.
     */
    public static function groupsDataForUpdate()
    {
        $putData = [
            'ID' => 3,
            'user_login' => 'user2',
            'user_email' => '<EMAIL>',
            'user_alias' => 'user2', // <-- event with changed alias cen not do update
            'store' => '1003',
            'type' => 'rep',
        ];

        Fixtures::add('groupsDataForUpdate', [
            [ // lower than requester.
                'requesterId' => 6,
                'requesterLogin' => 'user5',
                'putData' => $putData + ['group' => '3'],
                'expectedStatusCode' => 200,
                'expectedContent' => $putData + ['group' => '3']
            ], [ // equals to requester.
                'requesterId' => 6,
                'requesterLogin' => 'user5',
                'putData' => $putData + ['group' => '4'],
                'expectedStatusCode' => 200,
                'expectedContent' => $putData + ['group' => '4']
            ], [ // greater than requester.
                'requesterId' => 6,
                'requesterLogin' => 'user5',
                'putData' => $putData  + ['group' => '5'],
                'expectedStatusCode' => 403,
                'expectedContent' => [
                    'error' => 'You are not authorized to access this resource'
                ]
            ]
        ]);
    }

    public function repFilteringReporting()
    {
        Fixtures::add('FilteringReportingAddNewUser', [
            'wp_users' => [
                [
                    'ID'                  => 9,
                    'user_login'          => 'test_user',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
        ]);

        Fixtures::add('FilteringReportingUpdateUserValid', [
            "reporting_pref" => [
                'stores' => [
                    [
                        'id' => 8811,
                    ],
                ],
                "users" => [
                    [
                        'id' => 110,
                    ]
                ],
                "brands" => []
            ]
        ]);

        Fixtures::add('FilteringReportingUpdateUserInvalidContent', [
            "reporting_pref" => [
                'storeeees' => [
                    [
                        'id' => 8811,
                    ],
                ],
                "users" => [
                    [
                        'id' => 110,
                    ],
                ],
                "brands" => []
            ]
        ]);
    }

    public function repCustomerRelationship()
    {
        Fixtures::add('repCustomerRelationship', [
            'sf_customer' => [
                [
                    'ID'                   => 15,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'customer1',
                    'phone'                => '',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'retailer_customer_id' => '12345',
                    'first_name'           => 'customer1',
                    'last_name'            => 'test1',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
                [
                    'ID'                          => 16,
                    'user_id'                     => 2,
                    'email'                       => '<EMAIL>',
                    'name'                        => 'customer1',
                    'phone'                       => '',
                    'localization'                => null,
                    'geo'                         => '',
                    'comment'                     => '',
                    'subcribtion_flag'            => 1,
                    'retailer_parent_customer_id' => '12345',
                    'first_name'                  => 'customer1',
                    'last_name'                   => 'test1',
                    'entity_last_modified'        => '2020-10-10 10:10:10',
                    'entity_last_export'          => '2020-10-11 10:10:10',
                ],
                [
                    'ID'                   => 17,
                    'user_id'              => 3,
                    'email'                => '<EMAIL>',
                    'name'                 => 'customer1',
                    'phone'                => '',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'customer1',
                    'last_name'            => 'test1',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
                [
                    'ID'                   => 18,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'customer2',
                    'phone'                => '',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'retailer_customer_id' => '67890',
                    'first_name'           => 'customer2',
                    'last_name'            => 'test1',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
                [
                    'ID'                          => 19,
                    'user_id'                     => 2,
                    'email'                       => '<EMAIL>',
                    'name'                        => 'customer2',
                    'phone'                       => '',
                    'localization'                => null,
                    'geo'                         => '',
                    'comment'                     => '',
                    'subcribtion_flag'            => 1,
                    'retailer_parent_customer_id' => '67890',
                    'first_name'                  => 'customer2',
                    'last_name'                   => 'test1',
                    'entity_last_modified'        => '2020-10-10 10:10:10',
                    'entity_last_export'          => '2020-10-11 10:10:10',
                ],
                [
                    'ID'                   => 20,
                    'user_id'              => 3,
                    'email'                => '<EMAIL>',
                    'name'                 => 'customer2',
                    'phone'                => '',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'customer2',
                    'last_name'            => 'test1',
                    'entity_last_modified' => '2020-10-10 10:10:10',
                    'entity_last_export'   => '2020-10-11 10:10:10',
                ],
            ],
            'sf_retailer_customers' => [
                [
                    'customer_id'        => '12345',
                    'gender'             => 'male',
                    'first_name'         => 'customer1',
                    'last_name'          => 'test1',
                    'email'              => '<EMAIL>',
                    'email_label'        => 'Personal',
                    'phone'              => 'phone-to-show',
                    'phone_label'        => 'Work',
                    'address_line1'      => '73975 Borer Mallll',
                    'address_line2'      => 'Juana Square',
                    'zipcode'            => '91266-7456',
                    'postalcode'         => '09302-0963',
                    'city'               => 'East Maryjaneview',
                    'state'              => 'California',
                    'country'            => 'Congo',
                    'longitude'          => '106.47575000',
                    'latitude'           => '42.32311300',
                    'is_subscribed'      => '0',
                    'limited_visibility' => '1',
                ],
                [
                    'customer_id'        => '67890',
                    'gender'             => 'male',
                    'first_name'         => 'customer2',
                    'last_name'          => 'test2',
                    'email'              => '<EMAIL>',
                    'email_label'        => 'Personal',
                    'phone'              => 'phone-to-show',
                    'phone_label'        => 'Work',
                    'address_line1'      => '73975 Borer Mallll',
                    'address_line2'      => 'Juana Square',
                    'zipcode'            => '91266-7456',
                    'postalcode'         => '09302-0963',
                    'city'               => 'East Maryjaneview',
                    'state'              => 'California',
                    'country'            => 'Congo',
                    'longitude'          => '106.47575000',
                    'latitude'           => '42.32311300',
                    'is_subscribed'      => '0',
                    'limited_visibility' => '0',
                ],
            ],
        ]);
    }

    public function storeWithLocale()
    {
        Fixtures::add(
            'storeWithLocale',
            [
                'sf_store_i18n' => [
                    [
                        'locale'   => 'fr_CA',
                        'store_id' => 2003,
                        'name'     => 'Other Mall Fr',
                        'country'           => 'CA',
                        'region'            => 'QC',
                        'city'              => 'Montreal',
                        'address'           => '1455 Peel Streets',
                    ],
                ],
            ]
        );
    }

    public function addPhoneNumberToRep()
    {
        Fixtures::add(
            'EnablePhoneNumberForRep',
            [
                'sf_user_phone_number' => [
                    [
                        'user_id'            => 101,
                        'phone_number'       => '+15141231234',
                        'created_by_user_id' => 1,
                    ]
                ],
            ]
        );
    }

    public function addRepRelationTests()
    {
         Fixtures::add('repRelationOnDemand', [
            'wp_users'          => [
                [
                    'ID'                  => 101,
                    'user_login'          => 'test_user',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 2003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
            'wp_usermeta' => [
                [
                    'user_id'    => 101,
                    'meta_key'   => 'first_name',
                    'meta_value' => 'test'
                ],
            ],
            'sf_rep_favorites' => [
                [
                    'id' => 986,
                    'user_id' => 101,
                    'position' => 1,
                    'product_id' => '123aaff',
                    'note'   => 'cool products'
                ]
            ],
            'sf_products' => [
                [
                    'product_id'   => '123aaff',
                    'name'         => 'name 01',
                    'description'  => 'description 01',
                    'price'        => 10,
                    'available'    => 1,
                    'retailer_sku' => '',
                    'vanity_data'  => 'vanity',
                    'deal_ratio'   => 1,
                    'name2'        => 'name 01',
                ],
            ],
            'sf_images' => [
                [
                    'product_id' => '123aaff',
                    'url' => 'salesfloor.net/image1.png',
                    'language' => 'en_CA',
                    'size' => 0
                ]
            ],
            'sf_siteurl' => [
                [
                    'product_id' => '123aaff',
                    'url' => 'salesfloor.net/image1.png',
                    'language' => 'en_CA',
                ]
            ]

         ]);
    }

    public function testOnboardingRepPromoteSSODuplicateAlias()
    {
        Fixtures::add('testOnboardingRepPromoteSSODuplicateAlias', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login' => 'test_user8',
                    'user_pass' => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename' => 'test user',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => 'duplicate',
                    'display_name' => 'Test User 8',
                    'description' => null,
                    'photo' => null,
                    'last_login' => null,
                    'localization' => null,
                    'feature' => null,
                    'status' => 1,
                    'store' => 1003,
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'group' => 2,
                    'selling_mode' => 1,
                    'isPhoto' => 0,
                    'locked_at' => null,
                    'locale' => null,
                    'creation_source' => 'invite',
                    'shop_feed' => 0,
                    'updated_by' => 4,
                    'updated_at' => '2019-06-11 15:00:00',
                ]
            ],
            'sf_rep_onboarding' => [
                [
                    'token' => '12221',
                    'retailer_rep_id' => '12221',
                    'rep_first_name' => 'test',
                    'rep_last_name' => 'test',
                    'rep_email' => '<EMAIL>',
                    'store' => 1003,
                    'group' => 1,
                    'selling_mode' => 1,
                    'onboarding_completed' => 1,
                    'wp_user_id' => 8,
                ],
                [
                    'token' => '12222',
                    'retailer_rep_id' => '12222',
                    'rep_first_name' => 'test',
                    'rep_last_name' => 'test',
                    'rep_email' => '<EMAIL>',
                    'rep_login' => 'duplicate',
                    'store' => 1003,
                    'group' => 1,
                    'selling_mode' => 1,
                    'sso_auth' => 1,
                ],
            ],
        ]);
    }

    public function testOnboardingRepPromoteSSODuplicateLogin()
    {
        Fixtures::add('testOnboardingRepPromoteSSODuplicateLogin', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login' => 'test_user8',
                    'user_pass' => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename' => 'test user',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => 'alias',
                    'display_name' => 'Test User 8',
                    'description' => null,
                    'photo' => null,
                    'last_login' => null,
                    'localization' => null,
                    'feature' => null,
                    'status' => 1,
                    'store' => 1003,
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'group' => 2,
                    'selling_mode' => 1,
                    'isPhoto' => 0,
                    'locked_at' => null,
                    'locale' => null,
                    'creation_source' => 'invite',
                    'shop_feed' => 0,
                    'updated_by' => 4,
                    'updated_at' => '2019-06-11 15:00:00',
                ]
            ],
            'sf_rep_onboarding' => [
                [
                    'token' => '12221',
                    'retailer_rep_id' => '12221',
                    'rep_first_name' => 'test',
                    'rep_last_name' => 'test',
                    'rep_email' => '<EMAIL>',
                    'store' => 1003,
                    'group' => 1,
                    'selling_mode' => 1,
                    'onboarding_completed' => 1,
                    'wp_user_id' => 8,
                ],
                [
                    'token' => '12222',
                    'retailer_rep_id' => '12222',
                    'rep_first_name' => 'test',
                    'rep_last_name' => 'test',
                    'rep_email' => '<EMAIL>',
                    'rep_login' => 'test_user8',
                    'store' => 1003,
                    'group' => 1,
                    'selling_mode' => 1,
                    'sso_auth' => 1,
                ],
            ],
        ]);
    }
}
