{"data": {"type": "rep", "id": "98", "attributes": {"user_login": "test2", "user_nicename": "test2", "user_email": "<EMAIL>", "user_status": "1", "display_name": "test2", "user_alias": null, "store": "0", "employee_id": null, "group": "4", "selling_mode": "1", "user_registered": "2001-01-01 00:00:00", "isPhoto": "0", "locked_at": null, "locale": "en_US", "creation_source": "invite", "shop_feed": "0", "updated_at": null, "updated_by": null, "sso_auth": "0", "rep_type": "rep", "virtual_fields": {"components": ["dashboard.kpis", "dashboard.requests", "dashboard.messages", "dashboard.appointments", "dashboard.tasks"], "avatar": "https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_2.jpg,g_face,h_250,w_250/v1/dev/tests/test2", "permissions": ["user-management", "set-rep-status", "store-management", "create-user", "view-admin-tab", "show-corp-admin-cols", "set-bulk-text-messaging", "set-rep-text-messaging", "set-rep-social-shop", "see-all-tasks", "unlock-rep", "report.admin.all-associates", "set-sidebar-outstanding-threshold", "delete-invite", "corporate-task", "reassign-appointment", "asset-management", "contextual-widgets-kpi", "transactions-attribution"], "first_name": null, "last_name": null, "findrep_name": "", "reporting_pref": null, "alias": "test2", "store_user": {"ID": "98", "user_login": "test2", "user_nicename": "test2", "user_email": "<EMAIL>", "user_status": "1", "display_name": "test2", "user_alias": null, "store": "0", "type": "rep", "employee_id": null, "group": "4", "selling_mode": "1", "isPhoto": "0", "locked_at": null, "user_registered": "2001-01-01 00:00:00", "locale": "en_US", "creation_source": "invite", "shop_feed": "0", "updated_at": null, "updated_by": null, "sso_auth": "0"}}}, "links": {"self": "https://tests.api.dev.salesfloor.net/v2/reps/98"}}}