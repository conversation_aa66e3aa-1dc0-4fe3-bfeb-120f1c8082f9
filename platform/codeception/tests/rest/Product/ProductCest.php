<?php

namespace SF\rest\Product;

use SF\RestTester;
use SF\rest\BaseRest;
use SF\Helper\Traits\Asserts;

class ProductCest extends BaseRest
{
    use Asserts;

    /** @group database_transaction */
    public function testGetProduct(RestTester $I)
    {
        $I->wantTo("In message center: Test get product information by retailer id(could be product_id, sku/old variant id, gtn etc), in message center ");

        $productId = '8b22196d3cb34f69ef0c5f5e06cb1c08';
        $sku       = '8b22196d3cb34f69ef0c5f5e06cb1c08';

        //if not set exact, will random pick up any product
        $response = $I->doDirectGet($I, "/products/$sku?include=media&filter[exact]=1");

        $I->assertEquals($productId, $response->product_id);
        $I->assertEquals(475, $response->price);
    }

    /** @group database_transaction */
    public function testGetProductVariantBrands(RestTester $I)
    {
        $I->wantTo("get brands when product variants enabled");

        $this->insertFixtureGroup($I, 'BrandsCategories');

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $results = $I->doDirectGet($I, "v2/product-brands?filter[name]=20-2");

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }

    /** @group database_transaction */
    public function testGetProductBrands(RestTester $I)
    {
        $I->wantTo("get brands when product variants disabled");

        $this->insertFixtureGroup($I, 'BrandsCategories');

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $results = $I->doDirectGet($I, "v2/product-brands?filter[name]=20-2");

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }

    /** @group database_transaction */
    public function testGetProductCategories(RestTester $I)
    {
        $I->wantTo("get categories when product variants disabled");

        $this->insertFixtureGroup($I, 'BrandsCategories');

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $results = $I->doDirectGet($I, "v2/product-categories?filter[name]=gory20-2");

        // category_id could be a list, call sort to make sure the order get kept.
        $ids = explode(',', $results->data[0]->attributes->category_id);
        sort($ids);
        $results->data[0]->attributes->category_id = implode(',', $ids);
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }

    /** @group database_transaction */
    public function testGetProductCategoriesWithI18nEnable(RestTester $I)
    {
        $I->wantTo("get categories when product variants disabled and I18N enabled");

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];

        $this->insertFixtureGroup($I, 'BrandsCategories');

        $results = $I->doDirectGet($I, "v2/product-categories?sf_locale=fr_CA");

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }

    /** @group database_transaction */
    public function testGetProductVariantCategories(RestTester $I)
    {
        $I->wantTo("get categories when product variants enabled");

        $this->insertFixtureGroup($I, 'BrandsCategories');

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $results = $I->doDirectGet($I, "v2/product-categories?filter[name]=gory20-2");

        // category_id could be a list, call sort to make sure the order get kept.
        $ids = explode(',', $results->data[0]->attributes->category_id);
        sort($ids);
        $results->data[0]->attributes->category_id = implode(',', $ids);
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }

    /** @group database_transaction */
    public function testGetProductVariantCategoriesWithI18nEnable(RestTester $I)
    {
        $I->wantTo("get categories when product variants enabled and I18N enabled");

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];

        $this->insertFixtureGroup($I, 'BrandsCategories');

        $results = $I->doDirectGet($I, "v2/product-categories?sf_locale=fr_CA");

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }
}
