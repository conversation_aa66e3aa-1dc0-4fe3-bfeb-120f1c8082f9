<?php

namespace SF\rest\Products;

use Codeception\Util\Fixtures;

/**
 *
 */
class Fixture
{
    public function productVariants()
    {
        Fixtures::add(
            'productVariantsWithAttributes',
            [
                'sf_product_variants' => [
                    [
                        'product_id' => '54640cd49acae0386f777604ce15ab92',
                        'sku' => '11111',
                        'name' => 'variant name',
                        'description' => 'variant description',
                        'price' => '10.00',
                        'available' => 1,
                        'product_url' => 'product_url',
                        'image_url' => 'image_url',
                        'price_deal' => '5.00',
                        'price_old' => null,
                        'deal_start_date' => '2023-01-01 00:00:00',
                        'deal_end_date' => '2023-01-31 23:59:59',
                        'brand' => 'brand',
                        'is_default' => '1',
                        'arrival_date' => '2023-01-01 00:00:00',
                        'gtin' => '12345',
                    ],
                    [
                        'product_id' => '54640cd49acae0386f777604ce15ab92',
                        'sku' => '22222',
                        'name' => 'variant name',
                        'description' => 'variant description',
                        'price' => '20.00',
                        'available' => 1,
                        'product_url' => 'product_url',
                        'image_url' => 'image_url',
                        'price_deal' => '10.00',
                        'price_old' => null,
                        'deal_start_date' => '2023-01-01 00:00:00',
                        'deal_end_date' => '2023-01-31 23:59:59',
                        'brand' => 'brand',
                        'is_default' => '0',
                        'arrival_date' => '2023-01-01 00:00:00',
                        'gtin' => '54321',
                    ],
                ],
                'sf_product_variant_attributes' => [
                    [
                        'sku' => '11111',
                        'name' => 'color',
                        'value' => 'midnight blue',
                        'group' => 'blue',
                        'swatch_image_url' => 'swatch_url',
                        'position' => '1',
                    ],
                    [
                        'sku' => '11111',
                        'name' => 'size',
                        'value' => '51',
                        'group' => 'small',
                        'swatch_image_url' => '',
                        'position' => '2',
                    ],
                    [
                        'sku' => '11111',
                        'name' => 'form',
                        'value' => 'circle',
                        'group' => 'circle',
                        'swatch_image_url' => '',
                        'position' => '3',
                    ],
                    [
                        'sku' => '22222',
                        'name' => 'color',
                        'value' => 'dark room',
                        'group' => 'black',
                        'swatch_image_url' => '',
                        'position' => '1',
                    ],
                ],
                'sf_product_variants_i18n' => [
                    [
                        'locale' => 'fr_CA',
                        'sku' => '11111',
                        'name' => 'name fr',
                        'description' => 'description fr',
                        'product_url' => 'product_url fr',
                        'image_url' => 'image_url fr',
                    ],
                    [
                        'locale' => 'fr_CA',
                        'sku' => '22222',
                        'name' => 'name fr',
                        'description' => 'description fr',
                        'product_url' => 'product_url fr',
                        'image_url' => 'image_url fr',
                    ],
                ],
                'sf_product_variant_attributes_i18n' => [
                    [
                        'locale' => 'fr_CA',
                        'sku' => '11111',
                        'name' => 'couleur',
                        'value' => 'bleu de minuit',
                        'group' => 'bleu',
                        'swatch_image_url' => '',
                        'position' => '1',
                    ],
                    [
                        'locale' => 'fr_CA',
                        'sku' => '11111',
                        'name' => 'grandeur',
                        'value' => '51',
                        'group' => 'petit',
                        'swatch_image_url' => '',
                        'position' => '2',
                    ],
                    [
                        'locale' => 'fr_CA',
                        'sku' => '11111',
                        'name' => 'forme',
                        'value' => 'cercle',
                        'group' => 'cercle',
                        'swatch_image_url' => '',
                        'position' => '3',
                    ],
                    [
                        'locale' => 'fr_CA',
                        'sku' => '22222',
                        'name' => 'couleur',
                        'value' => 'pièce sombre',
                        'group' => 'noir',
                        'swatch_image_url' => '',
                        'position' => '1',
                    ],
                ],
            ]
        );
    }

    public function testGetProductSearchFilterWhenCategoryMultipleLevelOffI18n()
    {
        Fixtures::add('testGetProductSearchFilterWhenCategoryMultipleLevelOffI18n', [
            'sf_products' => [
                [
                    'product_id' => 'aabbcc',
                    'name' => 'test name',
                    'description' => 'test description',
                    'price' => 50.00,
                    'deal_start_date' => '2018-09-04 19:11:11',
                    'deal_end_date' => '2018-09-04 19:11:11',
                    'available' => 1,
                    'name2' => 'test',
                    'deal_ratio' => 0.2,
                ],
            ],
            'sf_images' => [
                [
                    'product_id' => 'aabbcc',
                    'url' => 'www.example1.com',
                    'language' => 'en_CA',
                ],
            ],
            'sf_siteurl' => [
                [
                    'product_id' => 'aabbcc',
                    'url' => 'site.example.com',
                    'language' => 'en_CA',
                ],
            ],
            'sf_categories' => [
                [
                    'name' => 'L3',
                    'category_id' => 'L3-id',
                    'language' => 'en_CA',
                ],
                [
                    'name' => 'L4',
                    'category_id' => 'L4-id',
                    'language' => 'en_CA',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'L4-id',
                    'parent_id' => 'L3-id',
                ],
                [
                    'category_id' => 'L3-id',
                    'parent_id' => 'dbf52747',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'category_id' => 'L4-id',
                    'product_id' => 'aabbcc'
                ],
            ]
        ]);
    }

    public function testGetProductSearchFilterWhenCategoryMultipleLevelOnI18n()
    {
        Fixtures::add('testGetProductSearchFilterWhenCategoryMultipleLevelOnI18n', [
            'sf_products' => [
                [
                    'product_id' => 'aabbcc',
                    'name' => 'test name',
                    'description' => 'test description',
                    'price' => 50.00,
                    'deal_start_date' => '2018-09-04 19:11:11',
                    'deal_end_date' => '2018-09-04 19:11:11',
                    'available' => 1,
                    'name2' => 'test',
                    'deal_ratio' => 0.2,
                ],
            ],
            'sf_products_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'product_id' => 'aabbcc',
                    'description' => 'description FR',
                    'name' => 'name FR',
                    'vanity_data' => '', // Can't be null
                ]
            ],
            'sf_images' => [
                [
                    'product_id' => 'aabbcc',
                    'url' => 'www.example1.com',
                    'language' => 'en_CA',
                ],
            ],
            'sf_images_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'product_id' => 'aabbcc',
                    'url' => 'www.example1.com/fr',
                ],
            ],
            'sf_siteurl' => [
                [
                    'product_id' => 'aabbcc',
                    'url' => 'site.example.com',
                    'language' => 'en_CA',
                ],
            ],
            'sf_siteurl_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'product_id' => 'aabbcc',
                    'url' => 'site.example.com/fr',
                ],
            ],
            'sf_categories' => [
                [
                    'name' => 'L3',
                    'category_id' => 'L3-id',
                    'language' => 'en_CA',
                ],
                [
                    'name' => 'L4',
                    'category_id' => 'L4-id',
                    'language' => 'en_CA',
                ],
            ],
            'sf_categories_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'category_id' => 'L3-id',
                    'name' => 'L4 name FR',
                ],
                [
                    'locale' => 'fr_CA',
                    'category_id' => 'L4-id',
                    'name' => 'L3 name FR',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'L4-id',
                    'parent_id' => 'L3-id',
                ],
                [
                    'category_id' => 'L3-id',
                    'parent_id' => 'dbf52747',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'category_id' => 'L4-id',
                    'product_id' => 'aabbcc'
                ],
            ]
        ]);
    }
}
