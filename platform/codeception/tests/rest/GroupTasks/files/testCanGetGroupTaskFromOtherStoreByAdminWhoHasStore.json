{"data": {"type": "group-task", "id": "1", "attributes": {"store_id": "1003", "title": "Test Title", "details": "Test Detail", "start_date": "2023-04-01 00:00:23", "reminder_date": "2023-03-01 00:01:02", "last_reminder_date": null, "auto_dismiss_date": "2024-01-01 00:01:02", "status": "unresolved", "customer_id": "1234", "has_reply_from_customer": "0", "preferred_user_id": "255", "suggested_subject_line": "Test Subject Line", "suggested_copy": "Test Suggested Copy", "virtual_fields": {"preferred_user": {"name": "Sleepy Simba", "first_name": "Sleepy", "last_name": "Simba"}, "retailer_customer": {"id": "1", "first_name": "Test", "last_name": "RC", "email": "<EMAIL>", "phone": "+15148127108", "is_subscribed": "1", "is_subscribed_sms_marketing": "2"}}}, "relationships": {"product": {"data": [{"type": "product", "id": "12001"}, {"type": "product", "id": "12002"}], "links": {"self": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/product", "related": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/product"}}, "product_variant": {"data": [{"type": "product", "id": "sku01"}, {"type": "product", "id": "sku02"}], "links": {"self": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/product_variant", "related": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/product_variant"}}, "asset": {"data": [{"type": "asset", "id": "1"}], "links": {"self": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/asset", "related": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/asset"}}, "group-task-activity": {"data": [{"type": "group-task-activity", "id": "1"}], "links": {"self": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/group-task-activity", "related": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/group-task-activity"}}}, "links": {"self": "https://tests.api.dev.salesfloor.net/v2/group-tasks/1"}}, "included": [{"type": "product", "id": "12001", "attributes": {"product_id": "12001", "name": "name 01", "description": "description 01", "price": 10, "price_deal": 0, "price_old": 0, "vanity_data": "vanity", "deal_ratio": 1, "name2": "name 01", "available": 1, "deal_start_date": null, "deal_end_date": null, "gender": null, "min_age": null, "max_age": null, "retailer_sku": "", "arrival_date": null, "country": null, "brand": null, "img": "www.example1.com", "productUrl": "site.example1.com", "sku": "12001", "shortDescription": "description 01", "regularPrice": 10, "salePrice": 0, "oldPrice": 0, "SaleEndDate": "", "vanityData": "vanity", "thumbnailImage": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--IbCPBGXK--/f_auto/www.example1.com", "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--cR2zuQwe--/f_auto,q_90,w_250/www.example1.com", "ehf": 0, "additionalMedia": [{"mimeType": "image", "thumbnailUrl": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--IbCPBGXK--/f_auto/www.example1.com", "url": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--IbCPBGXK--/f_auto/www.example1.com", "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--cR2zuQwe--/f_auto,q_90,w_250/www.example1.com"}]}}, {"type": "product", "id": "12002", "attributes": {"product_id": "12002", "name": "name 02", "description": "description 02", "price": 20, "price_deal": 0, "price_old": 0, "vanity_data": "vanity", "deal_ratio": 1, "name2": "name 01", "available": 1, "deal_start_date": null, "deal_end_date": null, "gender": null, "min_age": null, "max_age": null, "retailer_sku": "", "arrival_date": null, "country": null, "brand": null, "img": "www.example2.com", "productUrl": "site.example2.com", "sku": "12002", "shortDescription": "description 02", "regularPrice": 20, "salePrice": 0, "oldPrice": 0, "SaleEndDate": "", "vanityData": "vanity", "thumbnailImage": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--iXGbO5Mr--/f_auto/www.example2.com", "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--BcME9Lfp--/f_auto,q_90,w_250/www.example2.com", "ehf": 0, "additionalMedia": [{"mimeType": "image", "thumbnailUrl": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--iXGbO5Mr--/f_auto/www.example2.com", "url": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--iXGbO5Mr--/f_auto/www.example2.com", "img250": "https://res.cloudinary.com/salesfloor-net/image/fetch/s--BcME9Lfp--/f_auto,q_90,w_250/www.example2.com"}]}}, {"type": "product", "id": "sku01", "attributes": {"product_id": "12001", "sku": "sku01", "name": "variant name 01", "description": "variant description 01", "price": "10.00", "available": "1", "product_url": "product_url_01", "image_url": "image_url_01", "price_deal": "5.00", "price_old": null, "deal_start_date": null, "deal_end_date": null, "brand": "brand", "is_default": "1", "arrival_date": null}}, {"type": "product", "id": "sku02", "attributes": {"product_id": "12002", "sku": "sku02", "name": "variant name 02", "description": "variant description 02", "price": "20.00", "available": "1", "product_url": "product_url_02", "image_url": "image_url_02", "price_deal": "5.20", "price_old": null, "deal_start_date": null, "deal_end_date": null, "brand": "brand", "is_default": "1", "arrival_date": null}}, {"type": "asset", "id": "1", "attributes": {"post_name": "testasset", "post_author": "1", "post_title": "test asset", "post_content": "<p>test content</p>", "post_excerpt": null, "post_status": "publish", "post_type": "sf-library", "post_mime_type": "", "post_date_gmt": null, "post_modified_gmt": "2025-01-01 00:00:00", "post_date": null, "post_modified": "2025-01-01 00:00:00", "post_content_filtered": null, "pinged": null, "to_ping": null, "label": null, "blurb": null, "image": null, "storefront_image": null, "facebook_image": null, "linkedin_image": null, "twitter_image": null, "facebook_type": null, "email": null, "storefront": null, "facebook": null, "linkedin": null, "twitter": null, "destination_url": null, "email_subject": null, "start_date": null, "end_date": null, "target_channel": "all", "virtual_fields": {"label": "", "permanentUrl": "https://tests.dev.salesfloor.net/?sf-library=testasset", "assetType": "sf-library", "status": "publish", "feedIndex": "1", "subject": "test asset", "author": "Corporate", "previewUrl": "/?sf-library=testasset", "date": "2025-01-01 00:00:00", "startDate": "-", "endDate": "-", "startDatetime": null, "endDatetime": null}}}, {"type": "group-task-activity", "id": "1", "attributes": {"group_task_id": "1", "user_id": "1", "request_time": null, "action": "SEND-AN-EMAIL", "title": null, "details": "Test Activity Details", "created_at": "2023-10-17 17:07:05", "virtual_fields": {"is_reply_from_customer": false, "action_description": "sent an email", "user": {"first_name": null, "last_name": null}}}}]}