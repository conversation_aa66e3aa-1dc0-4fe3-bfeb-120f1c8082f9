<?php

namespace SF\rest\GroupTasks;

use Carbon\Carbon;
use Salesfloor\Models\GroupTask\GroupTask;
use Salesfloor\Models\GroupTask\GroupTaskActivity;
use SF\RestTester;
use SF\rest\BaseRest;

/** @group database_transaction */
class GroupTasksCest extends BaseRest
{
    public function testUserCanGetFilters(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $results = $I->doDirectGET($I, 'v2/group-tasks/filters');
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testAdminCanGetFilters(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $I->actingAs($this->app, '255', 'tests_19063');

        $results = $I->doDirectGET($I, 'v2/group-tasks/filters');
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testUserCanGetFiltersWithPreferredUserIds(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $results = $I->doDirectGET($I, 'v2/group-tasks/filters?filter[preferred_user_ids]=255,none');

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testAdminCanGetFiltersWithStoreIdsAndPreferredUserIds(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $I->actingAs($this->app, '256', 'tests_256');
        $queries = [
            'filter[store_ids]' => '2003',
            'filter[preferred_user_ids]' => '256,none',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks/filters?' . http_build_query($queries));

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetListOfGroupTasksUnresolved(RestTester $I)
    {
        Carbon::setTestNow('2023-03-01 00:01:02');
        $this->insertFixtureGroup($I, 'GroupTasks');

        $queries = [
            'filter[status]' => 'unresolved',
            'filter[preferred_user_ids]' => '255,none',
            'sort' => '-has_reply_from_customer,reminder_date',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks?' . http_build_query($queries));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetListOfGroupTasksDue(RestTester $I)
    {
        Carbon::setTestNow('2023-03-01 00:01:02');
        $this->insertFixtureGroup($I, 'GroupTasks');

        $queries = [
            'filter[status]' => 'unresolved',
            'filter[is_due]' => 1,
            'filter[preferred_user_ids]' => '255,none',
            'sort' => 'reminder_date',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks?' . http_build_query($queries));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetListOfGroupTasksDueForAdmin(RestTester $I)
    {
        Carbon::setTestNow('2023-03-01 00:01:02');
        $this->insertFixtureGroup($I, 'GroupTasks');

        $I->actingAs($this->app, '256', 'tests_19063');
        $queries = [
            'filter[status]' => 'unresolved',
            'filter[is_due]' => 1,
            'filter[preferred_user_ids]' => '255,none',
            'filter[store_ids]' => '2003',
            'sort' => 'reminder_date',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks?' . http_build_query($queries));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetListOfGroupTasksUpcoming(RestTester $I)
    {
        Carbon::setTestNow('2023-02-01 00:01:02');
        $this->insertFixtureGroup($I, 'GroupTasks');

        $queries = [
            'filter[status]' => 'unresolved',
            'filter[is_upcoming]' => 1,
            'sort' => 'reminder_date',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks?' . http_build_query($queries));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetListOfGroupTasksOverDue(RestTester $I)
    {
        Carbon::setTestNow('2023-04-01 00:01:02');
        $this->insertFixtureGroup($I, 'GroupTasks');

        $queries = [
            'filter[status]' => 'unresolved',
            'filter[is_overdue]' => 1,
            'sort' => 'reminder_date',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks?' . http_build_query($queries));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetListOfGroupTasksResolved(RestTester $I)
    {
        Carbon::setTestNow('2022-04-01 00:01:02');
        $this->insertFixtureGroup($I, 'GroupTasks');

        $queries = [
            'filter[status]' => GroupTask::STATUS_RESOLVED,
            'sort' => '-updated_at',
        ];

        $results = $I->doDirectGET($I, 'v2/group-tasks?' . http_build_query($queries));
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetAGroupTask(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');
        $includes = [
            'product',
            'product_variant',
            'asset',
            'group-task-activity',
        ];

        $requestUrl = sprintf('v2/group-tasks/1?include=%s', implode(',', $includes));
        $result = $I->doDirectGET($I, $requestUrl);

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($result, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $result);
    }

    public function testCannotGetAGroupTaskFromOtherStore(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');
        $includes = [
            'product',
            'product_variant',
            'asset',
            'group-task-activity',
        ];

        $requestUrl = sprintf('v2/group-tasks/2?include=%s', implode(',', $includes));
        $result = $I->doDirectGET($I, $requestUrl);
        $I->assertNull($result);
    }

    public function testCanGetGroupTaskFromOtherStoreByAdminWhoHasStore(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');
        $includes = [
            'product',
            'product_variant',
            'asset',
            'group-task-activity',
        ];

        $I->actingAs($this->app, '256', 'tests_19063');
        $requestUrl = sprintf('v2/group-tasks/1?include=%s', implode(',', $includes));
        $result = $I->doDirectGET($I, $requestUrl);

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($result, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $result);
    }

    public function testCanDismissGroupTask(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $data = [
            "data" => [
                "type" => "group-task",
                "id" => "1",
                "attributes" => [
                    "status" => GroupTask::STATUS_DISMISSED,
                ],
                "relationships" => [
                    'group-task-activity' => [
                        'data' => [
                            [
                                'type' => 'group-task-activity',
                                'lid' => '1',
                            ]
                        ]
                    ],
                ],
            ],
            "included" => [
                [
                    "type" => "group-task-activity",
                    "id" => null,
                    "lid" => "1",
                    "attributes" => [
                        "user_id" => "1",
                        "action" => GroupTaskActivity::ACTION_DISMISS,
                        "details" => "I hate it.",
                    ]
                ]
            ]
        ];
        $response = $I->doDirectPost($I, '/v2/group-tasks/1', $data);

        $expected = [
            'data' => [
              'type' => 'group-task',
              'id' => '1',
              'attributes' => [
                'store_id' => '1003',
                'title' => 'Test Title',
                'details' => 'Test Detail',
                'start_date' => '2023-04-01 00:00:23',
                'reminder_date' => '2023-03-01 00:01:02',
                'auto_dismiss_date' => '2024-01-01 00:01:02',
                'status' => 'dismissed',
                'customer_id' => '1234',
                'preferred_user_id' => '255',
                'suggested_subject_line' => 'Test Subject Line',
                'suggested_copy' => 'Test Suggested Copy',
              ],
              'relationships' => [
                'group-task-activity' => [
                  'data' => [
                    [
                      'type' => 'group-task-activity',
                    //   'id' => '1',
                    ],
                  ],
                  'links' => [
                    'self' => 'https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/group-task-activity',
                    'related' => 'https://tests.api.dev.salesfloor.net/v2/group-tasks/1/group-task-activity',
                  ],
                ],
              ],
              'links' => [
                'self' => 'https://tests.api.dev.salesfloor.net/v2/group-tasks/1',
              ],
            ],
            'included' => [
              [
                'type' => 'group-task-activity',
                // 'id' => '1',
                'attributes' => [
                  'group_task_id' => '1',
                  'user_id' => '1',
                  'action' => 'DISMISS',
                  'details' => 'I hate it.',
                ],
              ],
            ],
        ];

        $this->validateArray($expected, $response);

        $groupTasks = $I->grabRowsFromDatabase('sf_group_tasks', [], ['id' => '1']);
        $I->assertEquals(GroupTask::STATUS_DISMISSED, $groupTasks[0]['status']);
        $activities = $I->grabRowsFromDatabase('sf_group_task_activities', [], ['group_task_id' => '1']);

        $I->assertCount(2, $activities);
        $expected = [
            //   'id' => '1',
              'group_task_id' => '1',
              'user_id' => '1',
              'action' => GroupTaskActivity::ACTION_DISMISS,
              'details' => 'I hate it.',
            //   'created_at' => '2023-10-25 16:01:59',
        ];
        $this->validateArray($expected, $activities[1]);
    }

    public function testCanResolveGroupTask(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $data = [
            "data" => [
                "type" => "group-task",
                "id" => "1",
                "attributes" => [
                    "status" => GroupTask::STATUS_RESOLVED,
                ],
                "relationships" => [
                    'group-task-activity' => [
                        'data' => [
                            [
                                'type' => 'group-task-activity',
                                'lid' => '1',
                            ]
                        ]
                    ],
                ],
            ],
            "included" => [
                [
                    "type" => "group-task-activity",
                    "id" => null,
                    "lid" => '1',
                    "attributes" => [
                        "user_id" => "1",
                        "action" => GroupTaskActivity::ACTION_RESOLVE,
                        "details" => "I like it.",
                    ]
                ]
            ]
        ];
        $response = $I->doDirectPost($I, '/v2/group-tasks/1', $data);

        $expected = [
            'data' => [
              'type' => 'group-task',
              'id' => '1',
              'attributes' => [
                'store_id' => '1003',
                'title' => 'Test Title',
                'details' => 'Test Detail',
                'start_date' => '2023-04-01 00:00:23',
                'reminder_date' => '2023-03-01 00:01:02',
                'auto_dismiss_date' => '2024-01-01 00:01:02',
                'status' => GroupTask::STATUS_RESOLVED,
                'customer_id' => '1234',
                'preferred_user_id' => '255',
                'suggested_subject_line' => 'Test Subject Line',
                'suggested_copy' => 'Test Suggested Copy',
              ],
              'relationships' => [
                'group-task-activity' => [
                  'data' => [
                    [
                      'type' => 'group-task-activity',
                    //   'id' => '1',
                    ],
                  ],
                  'links' => [
                    'self' => 'https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/group-task-activity',
                    'related' => 'https://tests.api.dev.salesfloor.net/v2/group-tasks/1/group-task-activity',
                  ],
                ],
              ],
              'links' => [
                'self' => 'https://tests.api.dev.salesfloor.net/v2/group-tasks/1',
              ],
            ],
            'included' => [
              [
                'type' => 'group-task-activity',
                // 'id' => '1',
                'attributes' => [
                  'group_task_id' => '1',
                  'user_id' => '1',
                  'action' => GroupTaskActivity::ACTION_RESOLVE,
                  'details' => 'I like it.',
                ],
              ],
            ],
        ];

        $this->validateArray($expected, $response);

        $groupTasks = $I->grabRowsFromDatabase('sf_group_tasks', [], ['id' => '1']);
        $I->assertEquals(GroupTask::STATUS_RESOLVED, $groupTasks[0]['status']);
        $activities = $I->grabRowsFromDatabase('sf_group_task_activities', [], ['group_task_id' => '1']);

        $I->assertCount(2, $activities);

        $expected = [
            //   'id' => '1',
              'group_task_id' => '1',
              'user_id' => '1',
              'action' => GroupTaskActivity::ACTION_RESOLVE,
              'details' => 'I like it.',
            //   'created_at' => '2023-10-25 16:01:59',
        ];
        $this->validateArray($expected, $activities[1]);
    }

    public function testCanBatchResolveGroupTasks(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $data = [
            "data" => [
                "type" => "batch-update",
                "attributes" => [
                    "ids" => [1],
                    "columns" => [
                        "status" => GroupTask::STATUS_RESOLVED,
                    ]
                ],
                "relationships" => [
                    'group-task-activity' => [
                        'data' => [
                            [
                                'type' => 'group-task-activity',
                                'lid' => '1',
                            ]
                        ]
                    ],
                ],
            ],
            "included" => [
                [
                    "type" => "group-task-activity",
                    "id" => null,
                    'lid' => '1',
                    "attributes" => [
                        "user_id" => "1",
                        "action" => GroupTaskActivity::ACTION_RESOLVE,
                        "details" => "I like it.",
                    ]
                ]
            ]
        ];
        $response = $I->doDirectPost($I, '/v2/group-tasks/batch-update', $data);

        $expected = [
            "data" => [
                [
                    "type" => "group-task",
                    "id" => "1",
                    "attributes" => [
                        "store_id" => "1003",
                        "title" => "Test Title",
                        "details" => "Test Detail",
                        "start_date" => "2023-04-01 00:00:23",
                        "reminder_date" => "2023-03-01 00:01:02",
                        "auto_dismiss_date" => "2024-01-01 00:01:02",
                        "status" => "resolved",
                        "customer_id" => "1234",
                        "preferred_user_id" => "255",
                        "suggested_subject_line" => "Test Subject Line",
                        "suggested_copy" => "Test Suggested Copy",
                    ],
                    "relationships" => [
                        "group-task-activity" => [
                            "data" => [[
                                "type" => "group-task-activity",
                                // "id" => "2"
                            ]],
                            "links" => [
                                "self" =>
                                    "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/relationships/group-task-activity",
                                "related" =>
                                    "https://tests.api.dev.salesfloor.net/v2/group-tasks/1/group-task-activity",
                            ],
                        ],
                    ],
                    "links" => [
                        "self" =>
                            "https://tests.api.dev.salesfloor.net/v2/group-tasks/1",
                    ],
                ],
            ],
            "included" => [
                [
                    "type" => "group-task-activity",
                    // "id" => "2",
                    "attributes" => [
                        "group_task_id" => "1",
                        "user_id" => "1",
                        "action" => "RESOLVE",
                        "title" => null,
                        "details" => "I like it.",
                        // "created_at" => "2023-11-22 19:51:41",
                    ],
                ],
            ],
        ];

        $this->validateArray($expected, $response);

        $groupTasks = $I->grabRowsFromDatabase('sf_group_tasks', [], ['id' => '1']);
        $I->assertEquals(GroupTask::STATUS_RESOLVED, $groupTasks[0]['status']);
        $activities = $I->grabRowsFromDatabase('sf_group_task_activities', [], ['group_task_id' => '1']);

        $I->assertCount(2, $activities);

        $expected = [
            //   'id' => '1',
              'group_task_id' => '1',
              'user_id' => '1',
              'action' => GroupTaskActivity::ACTION_RESOLVE,
              'details' => 'I like it.',
            //   'created_at' => '2023-10-25 16:01:59',
        ];
        $this->validateArray($expected, $activities[1]);
    }

    public function testGetListOfGroupTaskActivities(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $queries = [
            'filter[group_task_id]' => '1',
            'include' => 'group-task-activity-product,group-task-activity-asset',
        ];
        $results = $I->doDirectGET($I, 'v2/group-task-activities?' . http_build_query($queries));

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($results, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $results);
    }

    public function testGetAGroupTaskWithMaskedFieldsPII(RestTester $I)
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'GroupTasks');
        $includes = [
            'product',
            'product_variant',
            'asset',
            'group-task-activity',
        ];

        $requestUrl = sprintf('v2/group-tasks/1?include=%s', implode(',', $includes));
        $result = $I->doDirectGET($I, $requestUrl);

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($result, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $result);
    }

    public function testGetAGroupTaskListWithMaskedFieldsPII(RestTester $I)
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'GroupTasks');

        $result = $I->doDirectGET($I, 'v2/group-tasks');

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($result, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $result);
    }
}
