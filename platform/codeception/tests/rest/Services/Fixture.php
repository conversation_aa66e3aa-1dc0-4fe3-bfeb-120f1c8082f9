<?php

namespace SF\rest\Services;

use Carbon\Carbon;
use Codeception\Util\Fixtures;
use Salesfloor\Models\Message;
use Salesfloor\Models\Services\Appointment;

class Fixture
{
    private function mockupAppointments()
    {
        return [
            'sf_appointments' => [
                [
                    'ID'                  => 1,
                    'customer_id'         => 12,
                    'user_id'             => 115,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => '2018-12-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* with request status of unresolved, message status of unsolved  */
                    'ID'                  => 2,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => date('Y-m-d H:i:s'),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* with request status of resolved  */
                    'ID'                  => 3,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date' => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* request of different rep could be get by store manager  */
                    'ID'                  => 4,
                    'customer_id'         => 12,
                    'user_id'             => 2,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date' => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* request of different rep & different store could be get by admin manager  */
                    'ID'                  => 5,
                    'customer_id'         => 12,
                    'user_id'             => 20031,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date' => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 2003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 9001,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => Appointment::EVENT_TYPE_CHAT,
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+2 day')),  //upcoming
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+3 day')),
                    'phone'               => '+5145610765',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => Appointment::CHANNEL_TYPE_TEXT,
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 10,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+2 day')),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+3 day')),
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 11,
                    'customer_id'         => null,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+2 day')),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+3 day')),
                    'phone'               => '+15148796589',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'text',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 12,
                    'customer_id'         => 14,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+2 day')),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+3 day')),
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 13,
                    'customer_id'         => 15,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+2 day')),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+3 day')),
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 14,
                    'customer_id'         => 17,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+2 day')),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+3 day')),
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'text',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ // To cancel --> success
                    'ID'                  => 15,
                    'customer_id'         => 19,
                    'user_id'             => 20033,
                    'event_type'          => Appointment::EVENT_TYPE_INSTORE,
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+7 day')),  //upcoming
                    'status'              => Appointment::STATUS_ACCEPTED,
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953390',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+7 day')),
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => Appointment::CHANNEL_TYPE_EMAIL,
                    'status_notification' => null,
                    'source'              => Appointment::SOURCE_MOBILE_REP,
                ],
                [ // Already cancelled --> error
                    'ID'                  => 16,
                    'customer_id'         => 19,
                    'user_id'             => 20033,
                    'event_type'          => Appointment::EVENT_TYPE_INSTORE,
                    'event_duration'      => 60,
                    'date'                => gmdate("Y-m-d H:i:s", strtotime('+7 day')),  //upcoming
                    'status'              => Appointment::STATUS_CANCELLED,
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953390',
                    'timezone'            => 'America/New_York',
                    'creation'            => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                    'enddate'             => gmdate("Y-m-d H:i:s", strtotime('+7 day')),
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => Appointment::CHANNEL_TYPE_EMAIL,
                    'status_notification' => null,
                    'source'              => Appointment::SOURCE_MOBILE_REP,
                ],
            ],
            'sf_messages' => [
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 2,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 3,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'resolved',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 2,
                    'owner_id'      => 2,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 4,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 20031,
                    'owner_id'      => 20031,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 5,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 115,
                    'owner_id'      => 1,
                    'customer_id'   => 2,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 1,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'waiting',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => 12,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'test user ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 10,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => gmdate("Y-m-d H:i:s"),
                    'status'        => 'unread',
                    'category'      => 'sent',
                    'message'       => '1',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 1,
                    'owner_id'      => 1,
                    'customer_id'   => null,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'test user ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 11,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => gmdate("Y-m-d H:i:s"),
                    'status'        => 'unread',
                    'category'      => 'sent',
                    'message'       => '1',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
                [
                    'user_id'       => 20033,
                    'owner_id'      => 20033,
                    'customer_id'   => 19,
                    'thread_id'     => 118,
                    'from_type'     => Message::FROM_TYPE_USER,
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'test user ',
                    'request_type'  => Message::CUSTOMER_REQUEST_BOOK_APPOINTMENT,
                    'request_id'    => 15,
                    'attachment'    => '',
                    'type'          => Message::TYPE_CUSTOMERREQUEST,
                    'date'          => gmdate("Y-m-d H:i:s"),
                    'status'        => Message::STATUS_UNREAD,
                    'category'      => Message::CATEGORY_SENT,
                    'message'       => Message::MESSAGE_TYPE_NEW_APPOINTMENT_TO_CUSTOMER,
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
            ],
        ];
    }

    private function mockupAppointmentsSimple()
    {
        return [
            'sf_appointments' => [
                [
                    'ID'                  => 1,
                    'customer_id'         => 12,
                    'user_id'             => 115,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => '2018-12-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* with request status of unresolved, message status of unsolved  */
                    'ID'                  => 2,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => date('Y-m-d H:i:s'),  //upcoming
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* with request status of resolved  */
                    'ID'                  => 3,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* request of different rep could be get by store manager  */
                    'ID'                  => 4,
                    'customer_id'         => 12,
                    'user_id'             => 2,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [ /* request of different rep & different store could be get by admin manager  */
                    'ID'                  => 5,
                    'customer_id'         => 12,
                    'user_id'             => 20031,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => gmdate('Y-m-d H:i:s'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 2003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 9001,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => '2018-12-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
            ],
            'sf_messages'     => [
                [
                    'user_id'      => 1,
                    'owner_id'     => 1,
                    'customer_id'  => 2,
                    'thread_id'    => 16,
                    'from_type'    => 'user',
                    'from_email'   => '<EMAIL>',
                    'from_name'    => 'Mary ',
                    'request_type' => 'book_appointment',
                    'request_id'   => 2,
                    'attachment'   => '',
                    'type'         => 'customer_request',
                    'date'         => '2018-09-04 19:11:11',
                    'status'       => 'waiting',
                    'category'     => 'sent',
                    'message'      => '2',
                    'title'        => 'Demande de rendez-vous',
                    'products'     => '',
                    'comment'      => '',
                    'locale'       => 'fr_CA'
                ],
                [
                    'user_id'      => 1,
                    'owner_id'     => 1,
                    'customer_id'  => 2,
                    'thread_id'    => 16,
                    'from_type'    => 'user',
                    'from_email'   => '<EMAIL>',
                    'from_name'    => 'Mary ',
                    'request_type' => 'book_appointment',
                    'request_id'   => 3,
                    'attachment'   => '',
                    'type'         => 'customer_request',
                    'date'         => '2018-09-04 19:11:11',
                    'status'       => 'resolved',
                    'category'     => 'sent',
                    'message'      => '2',
                    'title'        => 'Demande de rendez-vous',
                    'products'     => '',
                    'comment'      => '',
                    'locale'       => 'fr_CA'
                ],
                [
                    'user_id'      => 2,
                    'owner_id'     => 2,
                    'customer_id'  => 2,
                    'thread_id'    => 16,
                    'from_type'    => 'user',
                    'from_email'   => '<EMAIL>',
                    'from_name'    => 'Mary ',
                    'request_type' => 'book_appointment',
                    'request_id'   => 4,
                    'attachment'   => '',
                    'type'         => 'customer_request',
                    'date'         => '2018-09-04 19:11:11',
                    'status'       => 'waiting',
                    'category'     => 'sent',
                    'message'      => '2',
                    'title'        => 'Demande de rendez-vous',
                    'products'     => '',
                    'comment'      => '',
                    'locale'       => 'fr_CA'
                ],
                [
                    'user_id'      => 20031,
                    'owner_id'     => 20031,
                    'customer_id'  => 2,
                    'thread_id'    => 16,
                    'from_type'    => 'user',
                    'from_email'   => '<EMAIL>',
                    'from_name'    => 'Mary ',
                    'request_type' => 'book_appointment',
                    'request_id'   => 5,
                    'attachment'   => '',
                    'type'         => 'customer_request',
                    'date'         => '2018-09-04 19:11:11',
                    'status'       => 'waiting',
                    'category'     => 'sent',
                    'message'      => '2',
                    'title'        => 'Demande de rendez-vous',
                    'products'     => '',
                    'comment'      => '',
                    'locale'       => 'fr_CA'
                ],
                [
                    'user_id'      => 115,
                    'owner_id'     => 1,
                    'customer_id'  => 2,
                    'thread_id'    => 16,
                    'from_type'    => 'user',
                    'from_email'   => '<EMAIL>',
                    'from_name'    => 'Mary ',
                    'request_type' => 'book_appointment',
                    'request_id'   => 1,
                    'attachment'   => '',
                    'type'         => 'customer_request',
                    'date'         => '2018-09-04 19:11:11',
                    'status'       => 'waiting',
                    'category'     => 'sent',
                    'message'      => '2',
                    'title'        => 'Demande de rendez-vous',
                    'products'     => '',
                    'comment'      => '',
                    'locale'       => 'fr_CA'
                ],
            ],
            'wp_users'        => [
                [
                    'ID'                  => 115,
                    'user_login'          => 'test_user_whatever',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
                [
                    'ID'                  => 20031,
                    'user_login'          => 'store_2003_user_1',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 2003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
        ];
    }

    private function mockupSalesfloorUserAppointments()
    {
        return [
            'sf_appointments' => [
                [
                    'ID'                  => 99,
                    'customer_id'         => 12,
                    'user_id'             => 999,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => '2018-12-31 05:00:00',
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
            ],
            'sf_messages'     => [
                [
                    'user_id'      => 999,
                    'owner_id'     => 1,
                    'customer_id'  => 12,
                    'thread_id'    => 16,
                    'from_type'    => 'user',
                    'from_email'   => '<EMAIL>',
                    'from_name'    => 'Mary ',
                    'request_type' => 'book_appointment',
                    'request_id'   => 99,
                    'attachment'   => '',
                    'type'         => 'customer_request',
                    'date'         => '2018-09-04 19:11:11',
                    'status'       => 'waiting',
                    'category'     => 'sent',
                    'message'      => '2',
                    'title'        => 'Demande de rendez-vous',
                    'products'     => '',
                    'comment'      => '',
                    'locale'       => 'fr_CA'
                ],
            ],
            'wp_users'        => [
                [
                    'ID'                  => 999,
                    'user_login'          => 'Salesfloor_test',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 4,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
        ];
    }

    private function mockupManagerUsers()
    {
        return [
            'wp_users'          => [
                [
                    'ID'                  => 115,
                    'user_login'          => 'test_user_whatever',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
                [
                    'ID'                  => 20031,
                    'user_login'          => 'store_2003_user_1',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 2003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
                [
                    'ID'                  => 20033,
                    'user_login'          => 'Salesfloor_store_20033_user_5',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'test user5',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2019-06-11 11:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Test User5',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => 1,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                    'updated_by'          => 4,
                    'updated_at'          => '2019-06-11 15:00:00',
                ],
            ],
        ];
    }

    private function mockupCustomers()
    {
        return [
            'sf_customer' => [
                [
                    'ID'                   => 12,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user',
                    'phone'                => '+5145610765',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'sms_marketing_subscription_flag'     => 2,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'rep-appointment',
                    'created'              => gmdate("Y-m-d H:i:s"),
                ],
                [
                    'ID'                   => 13,
                    'user_id'              => 20033,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user',
                    'phone'                => '+5145610765',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'sms_marketing_subscription_flag'     => 0,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'rep-appointment',
                    'created'              => gmdate("Y-m-d H:i:s"),
                ],
                [
                    'ID'                   => 14,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610765',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'sms_marketing_subscription_flag'     => 0,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'rep-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
                [
                    'ID'                   => 15,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610765',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'sms_marketing_subscription_flag'     => 0,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'rep-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
                [
                    'ID'                   => 16,
                    'user_id'              => 115,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610789',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'sms_marketing_subscription_flag'     => 0,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'storefront-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
                [
                    'ID'                   => 17,
                    'user_id'              => 1,
                    'email'                => '',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610788',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 2,
                    'sms_marketing_subscription_flag'     => 2,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'rep-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
                [
                    'ID'                   => 18,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610799',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'sms_marketing_subscription_flag'     => 2,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'storefront-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
                [
                    'ID'                   => 19,
                    'user_id'              => 20033,
                    'email'                => '',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610799',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 2,
                    'sms_marketing_subscription_flag'     => 2,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'storefront-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
                [
                    'ID'                   => 20,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user1',
                    'phone'                => '+5145610799',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 2,
                    'sms_marketing_subscription_flag'     => 1,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'storefront-appointment',
                    'created'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
            ],
            'sf_pre_customer' => [
                [
                    'ID'                   => 10,
                    'user_id'              => 1,
                    'email'                => null,
                    'name'                 => 'test user',
                    'phone'                => '+15148796589',
                    'customer_id'          => null,
                    'origin'               => 'storefront-appointment',
                    'source'               => 'sf_appointments',
                    'source_id'            => 11,
                    'created_at'           => gmdate("Y-m-d H:i:s"),
                    'updated_at'           => gmdate("Y-m-d H:i:s"),
                    'locale'               => 'en_US',
                ],
                [
                    'ID'                   => 11,
                    'user_id'              => 1,
                    'email'                => null,
                    'name'                 => 'test user',
                    'phone'                => '+5145610788',
                    'customer_id'          => 17,
                    'origin'               => 'storefront-appointment',
                    'source'               => 'sf_appointments',
                    'source_id'            => 14,
                    'created_at'           => gmdate("Y-m-d H:i:s"),
                    'updated_at'           => gmdate("Y-m-d H:i:s"),
                    'locale'               => 'en_US',
                ],
            ],
            'sf_customer_meta' => [
                [
                    'customer_id'          => 16,
                    'type'                 => 'email',
                    'value'                => '<EMAIL>',
                    'label'                => 'home',
                    'position'             => 0,
                ],
                [
                    'customer_id'          => 16,
                    'type'                 => 'phone',
                    'value'                => '+5145610765',
                    'label'                => 'home',
                    'position'             => 1,
                ],
                [
                    'customer_id'          => 18,
                    'type'                 => 'phone',
                    'value'                => '+5145610799',
                    'label'                => 'home',
                    'position'             => 0,
                ],
                [
                    'customer_id'          => 20,
                    'type'                 => 'email',
                    'value'                => '<EMAIL>',
                    'label'                => 'home',
                    'position'             => 0,
                ],
                [
                    'customer_id'          => 20,
                    'type'                 => 'phone',
                    'value'                => '+5145610765',
                    'label'                => 'home',
                    'position'             => 1,
                ],
            ],
        ];
    }

    private function mockupSMSBlockedListCustomers()
    {
        return [
            'sf_sms_block_list' => [
                [
                    'ID'                   => 1,
                    'user_id'              => 1,
                    'phone_number'                => '+5145610799',
                    'store_id'               =>  13,
                    'created_at'              => gmdate("Y-m-d H:i:s", strtotime('+1 day')),
                ],
            ],
        ];
    }

    private function mockupRetailerCustomers()
    {
        return [
            'sf_retailer_customers' => [
                [
                    'customer_id' => '1234',
                    'gender' => 'female',
                    'first_name' => 'Test',
                    'last_name' => 'RC',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '+15140010001', // Invalid phone becomes empty
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'customer_id' => '12345',
                    'gender' => 'female',
                    'first_name' => 'Test',
                    'last_name' => 'RC Email',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '+15148127108',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'customer_id' => '123456',
                    'gender' => 'female',
                    'first_name' => 'Test',
                    'last_name' => 'RC Phone',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '+15148127108',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'customer_id' => '1234567',
                    'gender' => 'female',
                    'first_name' => 'Test',
                    'last_name' => 'RC Phone',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => '+10000010001',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
                [
                    'customer_id' => '12345678',
                    'gender' => 'female',
                    'first_name' => 'Test',
                    'last_name' => 'RC Phone',
                    'email' => '',
                    'email_label' => 'Personal',
                    'phone' => '+10000010001',
                    'phone_label' => 'Work',
                    'address_line1' => '73975 Borer Mallll',
                    'address_line2' => 'Juana Square',
                    'zipcode' => '91266-7456',
                    'postalcode' => '09302-0963',
                    'city' => 'East Maryjaneview',
                    'state' => 'California',
                    'country' => 'Congo',
                    'longitude' => '106.47575000',
                    'latitude' => '42.32311300',
                    'is_subscribed' => '0',
                ],
            ],
        ];
    }

    private function mockupPersonalShopper()
    {
        return [
            'geoip_location' => [
                [
                    'id'              => 1,
                    'country_code'    => 'CA',
                    'region'          => 'QC',
                    'city'            => 'Montreal',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-10 16:45:36',
                ],
                [
                    'id'              => 2,
                    'country_code'    => 'US',
                    'region'          => 'AL',
                    'city'            => 'SFID5c0e983066d3f8.49953381',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-31 16:45:36',
                ],
            ],
            'sf_personal_shopper' => [
                [ // status = pending
                    'ID'                  => 1,
                    'customer_id'         => 12,
                    'user_id'             => 115,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'shoes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => 'read',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 2,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'clothes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => 'pending',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 3,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'shoes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => 'read',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
                [
                    'ID'                  => 4,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'category'            => 'clothes',
                    'sub_category'        => '',
                    'min_budget'          => '1000',
                    'max_budget'          => '1199',
                    'notes'               => 'my wonderful notes',
                    'status'              => 'read',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'phone'               => '************',
                    'loc_id'              => 1,
                    'category_id'         => '',
                    'sub_category_id'     => '',
                    'flagged'             => 0,
                    'store_id'            => 2003,
                    'unattached_id'       => '',
                    'source_url'          => '',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'source'              => 'storefront',
                ],
            ],
        ];
    }

    private function mockupQuestions()
    {
        return [
            'geoip_location' => [
                [
                    'id'              => 1,
                    'country_code'    => 'CA',
                    'region'          => 'QC',
                    'city'            => 'Montreal',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-10 16:45:36',
                ],
                [
                    'id'              => 2,
                    'country_code'    => 'US',
                    'region'          => 'AL',
                    'city'            => 'SFID5c0e983066d3f8.49953381',
                    'postal_code'     => 'H4J 2S9',
                    'latitude'        => 45.4910,
                    'longitude'       => -73.5658,
                    'area_code'       => null,
                    'created'         => '2018-12-31 16:45:36',
                ],
            ],
            'sf_questions' => [
                [
                    'ID'              => 1,
                    'customer_id'     => 12,
                    'user_id'         => 115,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My easy first question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => 'accepted',
                    'loc_id'          => 1,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 2,
                    'customer_id'     => 12,
                    'user_id'         => 115,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My easy first question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => 'pending',
                    'loc_id'          => 1,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 3,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My second tricky question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => 'pending',
                    'loc_id'          => 1,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 4,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My third and most important question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => 'pending',
                    'loc_id'          => 1,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
                [
                    'ID'              => 5,
                    'customer_id'     => 12,
                    'user_id'         => 1,
                    'uniq_id'         => 'SFID5c0e983066d3f8.49953381',
                    'question'        => 'My third and most important question',
                    'topic'           => 'Sales',
                    'timezone'        => 'America/New_York',
                    'creation'        => '2018-12-10 16:45:36',
                    'phone'           => '',
                    'status'          => 'completed',
                    'loc_id'          => 1,
                    'category_id'     => '',
                    'sub_category_id' => '',
                    'flagged'         => 0,
                    'store_id'        => 2003,
                    'unattached_id'   => '',
                    'source_url'      => '',
                    'source_title'    => '',
                    'locale'          => 'en_US',
                    'channel'         => 'email',
                    'source'          => 'storefront',
                    'chat_handoff'    => 0,
                ],
            ],
        ];
    }

    private function mockupNewAppointmentGetReplied()
    {
        $appointmentDate = Carbon::now('UTC')->addDays(1)->toDateTimeString();

        return [
            'sf_appointments' => [
                [
                    'ID'                  => 1,
                    'customer_id'         => 12,
                    'user_id'             => 1,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    'date'                => $appointmentDate,
                    'status'              => '',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1003,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
            ],
            'sf_messages' => [
                [
                    // Any ID but 1 (owner_id), because we only use 'owner_id'
                    'user_id'       => 100,
                    'owner_id'      => 1,
                    'customer_id'   => 12,
                    'thread_id'     => 16,
                    'from_type'     => 'user',
                    'from_email'    => '<EMAIL>',
                    'from_name'     => 'Mary ',
                    'request_type'  => 'book_appointment',
                    'request_id'    => 1,
                    'attachment'    => '',
                    'type'          => 'customer_request',
                    'date'          => '2018-09-04 19:11:11',
                    'status'        => 'read',
                    'category'      => 'sent',
                    'message'       => '2',
                    'title'         => 'Demande de rendez-vous',
                    'products'      => '',
                    'comment'       => '',
                    'locale'        => 'fr_CA'
                ],
            ],
        ];
    }

    public function appointmentCreateManageFromContact()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '{
            "data": {
                "type": "appointment",
                "attributes": {
                    "customer_id": "12",
                    "event_type": "Virtual",
                    "channel": "email",
                    "date": "' . $date->format('Y-m-d') . ' 12:00:00",
                    "event_duration": "30",
                    "rep_comment": "Rep Comment",
                    "notes": "Notes"
                },
                "relationships": {}
            }
        }';

        Fixtures::add('appointment-create-manager-body', json_decode(trim($body), true));
    }
    public function appointmentCreateFromUnsubscribedEmailContact()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "20",
			"event_type": "In-Store",
			"channel": "email",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes"
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-body-email-unsubscribed', json_decode(trim($body), true));
    }
    public function appointmentCreateFromUnsubscribedSMSContact()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "18",
			"event_type": "In-Store",
			"channel": "text",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes"
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-body-sms-unsubscribed', json_decode(trim($body), true));
    }

    public function appointmentCreateManagerFromCustomer()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "1234",
			"event_type": "In-Store",
			"channel": "email",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes",
			"virtual_fields": {
			    "customer_origin": "rep-appointment"
			}
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-from-customer-body', json_decode(trim($body), true));
    }

    public function appointmentCreateManagerFromCustomerUpdateEmail()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "12345",
			"event_type": "In-Store",
			"channel": "email",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes",
			"virtual_fields": {
			    "customer_origin": "rep-appointment",
			    "customer_email": "<EMAIL>"
			}
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-from-customer-update-email-body', json_decode(trim($body), true));
    }

    public function appointmentCreateManagerFromCustomerUpdatePhone()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "123456",
			"event_type": "In-Store",
			"channel": "text",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes",
			"virtual_fields": {
			    "customer_origin": "rep-appointment",
			    "customer_phone": "+15148127109"
			}
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-from-customer-update-phone-body', json_decode(trim($body), true));
    }

    public function appointmentCreateManagerFromCustomerUpdatePhoneInvalidRetailerData()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "1234567",
			"event_type": "In-Store",
			"channel": "text",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes",
			"virtual_fields": {
			    "customer_origin": "rep-appointment",
			    "customer_phone": "+15148127109"
			}
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-from-customer-update-phone-invalid-retailer-data-body', json_decode(trim($body), true));
    }

    public function appointmentCreateManagerFromCustomerUpdateEmailInvalidRetailerData()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "12345678",
			"event_type": "In-Store",
			"channel": "email",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes",
			"virtual_fields": {
			    "customer_origin": "rep-appointment",
			    "customer_email": "<EMAIL>"
			}
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-create-manager-from-customer-update-email-invalid-retailer-data-body', json_decode(trim($body), true));
    }

    public function appointmentCreateManagerFromCustomerInvalidRetailerPhone()
    {
        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"customer_id": "1234567",
			"event_type": "In-Store",
			"channel": "text",
			"date": "' . $date->format('Y-m-d') . ' 12:00:00",
			"event_duration": "30",
			"rep_comment": "Rep Comment",
			"notes": "Notes",
			"virtual_fields": {
			    "customer_origin": "rep-appointment"
			}
		},
		"relationships": {}
	}
}';

        Fixtures::add(
            'appointment-create-manager-from-customer-invalid-retailer-phone-body',
            json_decode(trim($body), true)
        );
    }

    public function appointmentReassignManager()
    {
        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"user_id": 115,
			"reassigned_by": 20033
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-reassign-manager-body', json_decode(trim($body), true));
    }

    public function appointmentCancelManager()
    {
        $body = '
{
	"data": {
		"type": "appointment",
		"attributes": {
			"status": "cancelled",
			"rep_comment": "Rep cancellation notes"
		},
		"relationships": {}
	}
}';

        Fixtures::add('appointment-cancel-manager-body', json_decode(trim($body), true));
    }

    public function insert()
    {
        Fixtures::add('manager_users_fixture_group', $this->mockupManagerUsers());
        Fixtures::add('appointments_fixture_group', $this->mockupAppointments());
        Fixtures::add('appointments_fixture_group_simple', $this->mockupAppointmentsSimple());
        Fixtures::add('appointments_fixture_group_salesfloor_user', $this->mockupSalesfloorUserAppointments());
        Fixtures::add('personal_shopper_fixture_group', $this->mockupPersonalShopper());
        Fixtures::add('question_fixture_group', $this->mockupQuestions());
        Fixtures::add('customer_fixture_group', $this->mockupCustomers());
        Fixtures::add('contact_sms_blocked_fixture_group', $this->mockupSMSBlockedListCustomers());
        Fixtures::add('retailer_customer_fixture_group', $this->mockupRetailerCustomers());
        Fixtures::add('new_appointment_get_replied', $this->mockupNewAppointmentGetReplied());
    }
}
