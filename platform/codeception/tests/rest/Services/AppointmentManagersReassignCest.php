<?php

declare(strict_types=1);

namespace SF\rest\Services;

use Codeception\Util\Fixtures;
use SF\RestTester;

class AppointmentManagersReassignCest extends AppointmentManagersCestBase
{
    /** @group database_transaction */
    public function testReassignAppointmentCreatedByEmailAndCloneCustomerContactToReassignedUser(RestTester $I)
    {
        $I->wantTo("Test reassign appointment created from email channel and create a new customer contact for the reassigned user.");

        $this->app['configs']['retailer.services.appointment_reassignment.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-reassign-manager-body');
        $appointmentFixture['data']['id'] = 10;

        $this->setTokenInRequestHeader($I, 20033, 'store_20033_user_5');

        $I->seeInDatabase('sf_appointments', ['ID' => 10, 'customer_id' => 12, 'user_id' => 1, 'reassigned_by' => null]);
        $I->seeInDatabase('sf_customer', ['ID' => 12, 'user_id' => 1, 'origin' => 'rep-appointment']);


        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/10', $appointmentFixture);

        $this->debug($response);
        $appointment = $I->grabRowsFromDatabase('sf_appointments', null, [ 'ID' => 10]);

        $I->seeInDatabase(
            'sf_appointments',
            [
                'ID' => 10,
                'customer_id' => $appointment[0]['customer_id'],
                'user_id' => 115,
                'reassigned_by' => 20033,
            ]
        );
        $I->seeInDatabase(
            'sf_messages',
            [
                'customer_id' => $appointment[0]['customer_id'],
                'owner_id' => 115,
                'user_id' => 115,
                'request_id' => 10,
                'status' => 'reassigned'
            ]
        );
        $I->seeInDatabase(
            'sf_customer',
            [
                'ID' => $appointment[0]['customer_id'],
                'user_id' => 115
            ]
        );
    }

    /** @group database_transaction */
    public function testReassignAppointmentCreatedByEmailAndTransferTheCustomerContactToReassignedUser(RestTester $I)
    {
        $I->wantTo("Test reassign appointment created from email channel and transfer the customer contact to the reassigned user.");

        $this->app['configs']['retailer.services.appointment_reassignment.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-reassign-manager-body');
        $appointmentFixture['data']['id'] = 12;

        $this->setTokenInRequestHeader($I, 20033, 'store_20033_user_5');

        $I->seeInDatabase('sf_appointments', ['ID' => 12, 'customer_id' => 14, 'user_id' => 1, 'reassigned_by' => null]);
        $I->seeInDatabase('sf_customer', ['ID' => 14,'user_id' => 1, 'origin' => 'rep-appointment']);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/12', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', ['ID' => 12, 'customer_id' => 14, 'user_id' => 115, 'reassigned_by' => 20033]);
        $I->seeInDatabase('sf_messages', ['customer_id' => 14, 'owner_id' => 115, 'user_id' => 115, 'request_id' => 12, 'status' => 'reassigned']);
        $I->seeInDatabase('sf_customer', ['ID' => 14, 'user_id' => 115]);
    }

    /** @group database_transaction */
    public function testReassignAppointmentCreatedByEmailAndUpdateReassignedUserCustomerPrimaryAndSecondaryContact(RestTester $I)
    {
        $I->wantTo("Test reassign appointment created from email channel and update the reassigned user's primary or secondary email or phone of the customer contact.");

        $this->app['configs']['retailer.services.appointment_reassignment.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-reassign-manager-body');
        $appointmentFixture['data']['id'] = 13;

        $this->setTokenInRequestHeader($I, 20033, 'store_20033_user_5');

        $I->seeInDatabase('sf_appointments', ['ID' => 13, 'customer_id' => 15, 'user_id' => 1, 'reassigned_by' => null]);
        $I->seeInDatabase('sf_customer', ['ID' => 15,'user_id' => 1, 'origin' => 'rep-appointment']);
        $I->seeInDatabase('sf_customer', ['ID' => 16,'user_id' => 115, 'email' => '<EMAIL>', 'phone' => '+5145610789', 'origin' => 'storefront-appointment']);
        $I->seeInDatabase('sf_customer_meta', ['customer_id' => 16, 'value' => '<EMAIL>', 'type' => 'email']);
        $I->seeInDatabase('sf_customer_meta', ['customer_id' => 16, 'value' => '+5145610765', 'type' => 'phone']);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/13', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', ['ID' => 13, 'customer_id' => 16, 'user_id' => 115, 'reassigned_by' => 20033]);
        $I->seeInDatabase('sf_messages', ['customer_id' => 16, 'owner_id' => 115, 'user_id' => 115, 'request_id' => 13, 'status' => 'reassigned']);
        $I->seeInDatabase('sf_customer', ['ID' => 16, 'user_id' => 115, 'email' => '<EMAIL>', 'phone' => '+5145610765']);
        $I->seeInDatabase('sf_customer_meta', ['customer_id' => 16, 'value' => '<EMAIL>', 'type' => 'email']);
        $I->seeInDatabase('sf_customer_meta', ['customer_id' => 16, 'value' => '+5145610789', 'type' => 'phone']);
    }

    /** @group database_transaction */
    public function testReassignAppointmentCreatedByTextWhenCustomerIsPreCustomer(RestTester $I)
    {
        $I->wantTo("Test reassign appointment created from text channel when customer is pre customer");

        $this->app['configs']['retailer.services.appointment_reassignment.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-reassign-manager-body');
        $appointmentFixture['data']['id'] = 11;

        $this->setTokenInRequestHeader($I, 20033, 'store_20033_user_5');

        $I->seeInDatabase('sf_appointments', ['ID' => 11, 'customer_id' => null, 'user_id' => 1, 'reassigned_by' => null]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/11', $appointmentFixture);
        $this->debug($response);

        $I->seeInDatabase(
            'sf_appointments',
            [
                'ID' => 11,
                'customer_id' => null,
                'user_id' => 115,
                'reassigned_by' => 20033
            ]
        );
        $I->seeInDatabase(
            'sf_messages',
            [
                'customer_id' => null,
                'owner_id' => 115,
                'user_id' => 115,
                'request_id' => 11,
                'status' => 'reassigned'
            ]
        );
        $I->seeInDatabase(
            'sf_pre_customer',
            [
                'customer_id' => null,
                'user_id' => 115,
                'source_id' => 11
            ]
        );
    }
}
