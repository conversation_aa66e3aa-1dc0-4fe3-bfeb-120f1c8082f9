<?php

declare(strict_types=1);

namespace SF\rest\Services;

use Codeception\Stub;
use Codeception\Util\Fixtures;
use Salesfloor\Models\Message;
use Salesfloor\Models\Services\Appointment;
use Salesfloor\Services\Services\Appointments\AppointmentsCancel as AppointmentsCancelService;
use SF\RestTester;

class AppointmentManagersCancelCest extends AppointmentManagersCestBase
{
    /** @group database_transaction */
    public function testCancelAppointmentSuccess(RestTester $I)
    {
        $I->wantTo("Test cancel appointment from the rep");

        $this->app['configs']['retailer.services.appointment_management.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-cancel-manager-body');
        $appointmentFixture['data']['id'] = 15;

        $this->mockupAppointmentCancelService();

        $this->setTokenInRequestHeader($I, 20033, 'Salesfloor_store_20033_user_5');

        $I->seeInDatabase('sf_appointments', [
            'ID' => 15,
            'status' => Appointment::STATUS_ACCEPTED,
        ]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/15', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', [
            'ID' => 15,
            'status' => Appointment::STATUS_CANCELLED,
            'rep_comment' => 'Rep cancellation notes',
        ]);
        $I->seeInDatabase('sf_messages', [
            'customer_id' => 19,
            'owner_id' => 20033,
            'user_id' => 20033,
            'request_id' => 15,
            'from_type' => Message::FROM_TYPE_USER,
            'message' => Message::MESSAGE_TYPE_CANCEL_APPOINTMENT_FROM_REP,
            'comment' => 'Rep cancellation notes'
        ]);
    }

    /** @group database_transaction */
    public function testCancelAppointmentSuccessWithNotifyWithoutConsentOffAndUnsubscribedEmail(RestTester $I)
    {
        $I->wantTo("Test cancel appointment from the rep - with notify consent disabled on unsubscribe email");

        $this->app['configs']['retailer.services.appointment_management.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment_management.notify_without_consent'] = false;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-cancel-manager-body');
        $appointmentFixture['data']['id'] = 15;

        // This mail factory should not be called when customer is unsubscribed.
        // build is currently set to Expected::never by default
        $this->mockupMailFactoryService();

        $this->setTokenInRequestHeader($I, 20033, 'Salesfloor_store_20033_user_5');

        $I->seeInDatabase('sf_appointments', [
            'ID' => 15,
            'status' => Appointment::STATUS_ACCEPTED,
        ]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/15', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', [
            'ID' => 15,
            'status' => Appointment::STATUS_CANCELLED,
            'rep_comment' => 'Rep cancellation notes',
        ]);
        $I->seeInDatabase('sf_messages', [
            'customer_id' => 19,
            'owner_id' => 20033,
            'user_id' => 20033,
            'request_id' => 15,
            'from_type' => Message::FROM_TYPE_USER,
            'message' => Message::MESSAGE_TYPE_CANCEL_APPOINTMENT_FROM_REP,
            'comment' => 'Rep cancellation notes'
        ]);

        // ensure customer subscription settings didn't get altered as per SF-30171 ACs
        $I->seeInDatabase('sf_customer', [
            'ID' => 19,
            'subcribtion_flag' => '2',
            'sms_marketing_subscription_flag' => '2',
        ]);
    }

    /** @group database_transaction */
    public function testCancelAppointmentSuccessWithNotifyWithoutConsentOnAndUnsubscribedEmail(RestTester $I)
    {
        $I->wantTo("Test cancel appointment from the rep - with notify consent enabled on unsubscribe email");

        $this->app['configs']['retailer.services.appointment_management.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment_management.notify_without_consent'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-cancel-manager-body');
        $appointmentFixture['data']['id'] = 15;

        // This mail factory should be called even when customer is unsubscribed.
        // build is currently set to be called at least once, send email is set to be called at least once
        $this->mockupMailFactoryService(true);

        $this->setTokenInRequestHeader($I, 20033, 'Salesfloor_store_20033_user_5');

        $I->seeInDatabase('sf_appointments', [
            'ID' => 15,
            'status' => Appointment::STATUS_ACCEPTED,
        ]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/15', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', [
            'ID' => 15,
            'status' => Appointment::STATUS_CANCELLED,
            'rep_comment' => 'Rep cancellation notes',
        ]);
        $I->seeInDatabase('sf_messages', [
            'customer_id' => 19,
            'owner_id' => 20033,
            'user_id' => 20033,
            'request_id' => 15,
            'from_type' => Message::FROM_TYPE_USER,
            'message' => Message::MESSAGE_TYPE_CANCEL_APPOINTMENT_FROM_REP,
            'comment' => 'Rep cancellation notes'
        ]);
        // ensure customer subscription settings didn't get altered as per SF-30171 ACs
        $I->seeInDatabase('sf_customer', [
            'ID' => 19,
            'subcribtion_flag' => '2',
            'sms_marketing_subscription_flag' => '2',
        ]);
    }

    /** @group database_transaction */
    public function testCancelAppointmentSuccessWithNotifyWithoutConsentOffAndUnsubscribedSMS(RestTester $I)
    {
        $I->wantTo("Test cancel appointment from the rep - with notify consent disabled on unsubscribe email");

        $this->app['configs']['retailer.services.appointment_management.is_enabled'] = true;
        $this->app['configs']['retailer.services.channel.text.enabled'] = true;
        $this->app['configs']['retailer.services.appointment_management.notify_without_consent'] = false;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-cancel-manager-body');
        $appointmentFixture['data']['id'] = 9001;

        // This mail factory should not be called when customer is unsubscribed.
        // build is currently set to Expected::never by default
        $this->mockupSMSFactoryService();

        $this->setTokenInRequestHeader($I, 1, 'store_1_user_5');

        $I->seeInDatabase('sf_appointments', [
            'ID' => 9001,
            'status' => Appointment::STATUS_ACCEPTED,
        ]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/9001', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', [
            'ID' => 9001,
            'status' => Appointment::STATUS_CANCELLED,
            'rep_comment' => 'Rep cancellation notes',
        ]);

        // ensure customer subscription settings didn't get altered as per SF-30171 ACs
        $I->seeInDatabase('sf_customer', [
            'ID' => 12,
            'subcribtion_flag' => '0',
            'sms_marketing_subscription_flag' => '2',
        ]);
    }

    /** @group database_transaction */
    public function testCancelAppointmentSuccessWithNotifyWithoutConsentOnAndUnsubscribedSMS(RestTester $I)
    {
        $I->wantTo("Test cancel appointment from the rep - with notify consent enabled on unsubscribe email");

        $this->app['configs']['retailer.services.appointment_management.is_enabled'] = true;
        $this->app['configs']['retailer.services.channel.text.enabled'] = true;
        $this->app['configs']['retailer.services.appointment_management.notify_without_consent'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-cancel-manager-body');
        $appointmentFixture['data']['id'] = 9001;

        // This mail factory should be called even when customer is unsubscribed.
        //  send message is set to be called atleast once
        $this->mockupSMSFactoryService(true);

        $this->setTokenInRequestHeader($I, 1, 'store_1_user_5');

        $I->seeInDatabase('sf_appointments', [
            'ID' => 9001,
            'status' => Appointment::STATUS_ACCEPTED,
        ]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/9001', $appointmentFixture);

        $this->debug($response);

        $I->seeInDatabase('sf_appointments', [
            'ID' => 9001,
            'status' => Appointment::STATUS_CANCELLED,
            'rep_comment' => 'Rep cancellation notes',
        ]);

        // ensure customer subscription settings didn't get altered as per SF-30171 ACs
        $I->seeInDatabase('sf_customer', [
            'ID' => 12,
            'subcribtion_flag' => '0',
            'sms_marketing_subscription_flag' => '2',
        ]);
    }

    /** @group database_transaction */
    public function testCancelAppointmentAlreadyCancelledError(RestTester $I)
    {
        $I->wantTo("Test cancel appointment from the rep (error already cancelled)");

        $this->app['configs']['retailer.services.appointment_management.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'manager_users_fixture_group');
        $this->insertFixtureGroup($I, 'appointments_fixture_group');
        $this->insertFixtureGroup($I, 'customer_fixture_group');

        $appointmentFixture = Fixtures::get('appointment-cancel-manager-body');
        $appointmentFixture['data']['id'] = 16;

        $this->mockupAppointmentCancelService();


        $this->setTokenInRequestHeader($I, 20033, 'Salesfloor_store_20033_user_5');

        $I->seeInDatabase('sf_appointments', [
            'ID' => 16,
            'status' => Appointment::STATUS_CANCELLED,
        ]);

        $response = $I->doDirectPatch($I, 'v2/managers/services/appointments/16', $appointmentFixture);

        $this->debug($response);

        $I->assertEquals(true, !empty($response->errors));
        $I->assertEquals($response->errors[0]->code, 1300);
    }

    /**
     * Create an updated version of the appointment manager to prevent sending notifications
     *
     * @throws \Exception
     */
    protected function mockupAppointmentCancelService()
    {
        $appointmentsSerStub = Stub::construct(
            AppointmentsCancelService::class,
            [
                $this->app
            ],
            [
                'sendCancelNotificationsFromRep' => function (Appointment $appointment) {
                    return null;
                },
            ]
        );

        $this->app['appointments_cancel.services'] = $appointmentsSerStub;
    }
}
