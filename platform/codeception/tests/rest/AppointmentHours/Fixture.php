<?php

declare(strict_types=1);

namespace SF\rest\AppointmentHours;

use Codeception\Util\Fixtures;

class Fixture
// phpcs:ignoreFile
{
    public function appointmentHours()
    {
        Fixtures::add('user', [
            'wp_users' => [
                [
                'ID'                  => 115,
                'user_login'          => 'testuser335',
                'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                'user_nicename'       => 'testuser335',
                'user_email'          => '<EMAIL>',
                'user_url'            => '',
                'user_registered'     => '2001-01-01 00:00:00',
                'user_activation_key' => '',
                'user_status'         => 1,
                'user_alias'          => null,
                'display_name'        => 'testuser335',
                'description'         => null,
                'photo'               => null,
                'last_login'          => null,
                'localization'        => null,
                'feature'             => null,
                'status'              => null,
                'store'               => 1003,
                'type'                => 'store',
                'commission_rate'     => 0.00,
                'employee_id'         => null,
                'group'               => 2,// must be allowed by $configs['retailer.store_appointment_hours.group_permissions']
                'selling_mode'        => 1,
                'isPhoto'             => 0,
                'locked_at'           => null,
                'locale'              => null,
                'creation_source'     => 'invite',
                'shop_feed'           => 0
                ],
                ['ID'                 => 111,
                'user_login'          => 'testuser111',
                'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                'user_nicename'       => 'testuser11',
                'user_email'          => '<EMAIL>',
                'user_url'            => '',
                'user_registered'     => '2001-01-01 00:00:00',
                'user_activation_key' => '',
                'user_status'         => 1,
                'user_alias'          => null,
                'display_name'        => 'testuser11',
                'description'         => null,
                'photo'               => null,
                'last_login'          => null,
                'localization'        => null,
                'feature'             => null,
                'status'              => null,
                'store'               => 1003,
                'type'                => 'store',
                'commission_rate'     => 0.00,
                'employee_id'         => null,
                'group'               => 1,//not enough access level
                'selling_mode'        => 1,
                'isPhoto'             => 0,
                'locked_at'           => null,
                'locale'              => null,
                'creation_source'     => 'invite',
                'shop_feed'           => 0
                ]
            ]
        ]);

        Fixtures::add('store', [
            'sf_store' => [
                [
                    'store_id'          => '13',
                    'name'              => 'Vancouver',
                    'latitude'          => 49.2496,
                    'longitude'         => -123.1193,
                    'country'           => 'CA',
                    'region'            => 'BC',
                    'city'              => 'Vancouver',
                    'address'           => '4985 Sherbrooke St',
                    'postal'            => 'V5W 3M1',
                    'phone'             => '(*************',
                    'timezone'          => 'America/New_York',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'Vancouver',
                    'sf_identifier'     => 'test-vancouver'
                ]
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 13,
                    'is_default' => 1
                ]
            ],
        ]);

        Fixtures::add('generic-store-hours', json_decode(
            '{
            "data": [
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "sunday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "monday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "tuesday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "wednesday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "thursday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "friday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "attributes": {
                        "is_available": "1",
                        "day_of_week": "saturday",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "22:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/"
                    }
                }
            ]
            }',
            true)
        );

        Fixtures::add('generic-store-hours-status', json_decode('
            {
                "data": {
                    "type": "store-appointment-hours-status",
                    "attributes": {
                        "is_enabled": "0"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/store-appointment-hours-status/"
                    }
                }
            }
        ', true));

        Fixtures::add('custom-store-hours-status', json_decode('
        {
            "data": {
                "type": "store-appointment-hours-status",
                "id": "1",
                "attributes": {
                    "created_by": "115",
                    "updated_by": "115",
                    "store_id": "1003",
                    "is_enabled": "1",
                    "created_at": "2022-01-14 01:04:59",
                    "updated_at": "2022-01-14 01:04:59"
                },
                "links": {
                    "self": "https://tests.api.dev.salesfloor.net/v2/store-appointment-hours-status/1"
                }
            }
        }
        ', true));

        Fixtures::add('custom-store-hours', json_decode(
            '{
            "data": [
                {
                    "type": "store-appointment-hours",
                    "id": "1",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "sunday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/1"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "id": "2",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "monday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/2"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "id": "3",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "tuesday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/3"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "id": "4",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "wednesday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/4"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "id": "5",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "thursday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/5"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "id": "6",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "friday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/6"
                    }
                },
                {
                    "type": "store-appointment-hours",
                    "id": "7",
                    "attributes": {
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "15:00:00"
                            }
                        ],
                        "is_available": "1",
                        "day_of_week": "saturday",
                        "store_id": "1003"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/timeslots/7"
                    }
                }
            ]
            }', true)
        );

        Fixtures::add('custom-store-hours-overrides', json_decode('
        {
            "data": [
                {
                    "type": "store-appointment-hours-overrides",
                    "id": "2",
                    "attributes": {
                        "store_id": "1003",
                        "is_available": "1",
                        "date": "' . date('Y-m-d', strtotime("+7 days")) . '",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "18:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/store-appointment-hours-overrides/2"
                    }
                },
                {
                    "type": "store-appointment-hours-overrides",
                    "id": "3",
                    "attributes": {
                        "store_id": "1003",
                        "is_available": "0",
                        "date": "' . date('Y-m-d', strtotime("+14 days")) . '",
                        "timeslots": [
                            {
                                "start_time": "09:00:00",
                                "end_time": "18:00:00"
                            }
                        ]
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/store-appointment-hours-overrides/3"
                    }
                }
            ]
        }
        ', true));
        $tz  = new \DateTimeZone('America/Montreal');
        $from  =   new \DateTime('+1 day', $tz);
        $to  =   new \DateTime('+2 day', $tz);

        $timezone = new \DateTimeZone('America/Montreal'); // same as sf_store timezone
        $dateTime = new \DateTime('now', $timezone);
        // See https://www.php.net/manual/en/datetime.format.php:
        $tzOffset = $dateTime->format('P');

        Fixtures::add('public-api-generic-hours', [
            "tz_offset" => $tzOffset,
            "days" => [
                $from->format('Y-m-d') => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $to->format('Y-m-d') => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
            ]
        ]);

        $from  =   new \DateTime('next Monday', $tz);
        $monday = $from->format('Y-m-d');
        $tuesday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $wednesday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $thursday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $friday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $saturday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $sunday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');

        Fixtures::add('public-api-sunday-off', [
            "tz_offset" => $tzOffset,
            "days" => [
                $monday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => false],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => false],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => false],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => false],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => false],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $tuesday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => false],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => false],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => false],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => false],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => false],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $wednesday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => false],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => false],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => false],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => false],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => false],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $thursday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => false],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => false],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => false],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => false],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => false],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $friday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => false],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => false],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => false],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => false],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => false],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $saturday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => false],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => false],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => false],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => false],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => false],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $sunday => [],
            ]
        ]);

        $from  =   new \DateTime('next Monday', $tz);
        $monday = $from->format('Y-m-d');
        $tuesday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $wednesday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $thursday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $friday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $saturday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $sunday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');

        Fixtures::add('public-api-monday-only', [
            "tz_offset" => $tzOffset,
            "days" => [
                $monday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => false],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => false],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => false]
                ],
                $tuesday => [],
                $wednesday => [],
                $thursday => [],
                $friday => [],
                $saturday => [],
                $sunday => []
            ]
        ]);

        $from  =   new \DateTime('next Monday', $tz);
        $monday = $from->format('Y-m-d');
        $tuesday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $wednesday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $thursday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $friday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $saturday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');
        $sunday  = $from->add(\DateInterval::createFromDateString('1 day'))->format('Y-m-d');

        Fixtures::add('public-api-odd-weekdays-off', [
            "tz_offset" => $tzOffset,
            "days" => [
                $monday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $tuesday => [],
                $wednesday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $thursday => [],
                $friday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $saturday => [],
                $sunday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true],
                ]
            ]
        ]);

        Fixtures::add('public-api-tuesday-off-but-override-is-on', [
            "tz_offset" => $tzOffset,
            "days" => [
                $monday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $tuesday => [
                    ["start_time" => "9:00", "end_time" => "10:00", "available" => true],
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $wednesday => [],
                $thursday => [],
                $friday => [],
                $saturday => [],
                $sunday => []
            ]
        ]);

        Fixtures::add('public-api-overlapping-timeslots', [
            "tz_offset" => $tzOffset,
            "days" => [
                $monday => [
                    ["start_time" => "10:00", "end_time" => "11:00", "available" => true],
                    ["start_time" => "11:00", "end_time" => "12:00", "available" => true],
                    ["start_time" => "12:00", "end_time" => "13:00", "available" => true],
                    ["start_time" => "13:00", "end_time" => "14:00", "available" => true],
                    ["start_time" => "14:00", "end_time" => "15:00", "available" => true],
                    ["start_time" => "15:00", "end_time" => "16:00", "available" => true],
                    ["start_time" => "16:00", "end_time" => "17:00", "available" => true],
                    ["start_time" => "17:00", "end_time" => "18:00", "available" => true],
                    ["start_time" => "18:00", "end_time" => "19:00", "available" => true],
                    ["start_time" => "19:00", "end_time" => "20:00", "available" => true],
                    ["start_time" => "20:00", "end_time" => "21:00", "available" => true],
                    ["start_time" => "21:00", "end_time" => "22:00", "available" => true]
                ],
                $tuesday => [],
                $wednesday => [],
                $thursday => [],
                $friday => [],
                $saturday => [],
                $sunday => []
            ]
        ]);
        $nextMonday = date('Y-m-d', strtotime("next Monday"));

        Fixtures::add('public-api-overrides-8-am-booked', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:00:00",
                            "end_time" => "17:00:00"
                        ]
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        Fixtures::add('public-api-overrides-9-am-booked', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "08:00:00",
                            "end_time" => "08:00:00"
                        ],
                        [
                            "start_time" => "10:00:00",
                            "end_time" => "17:00:00"
                        ]
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        Fixtures::add('public-api-overrides-6-pm-booked', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "08:00:00",
                            "end_time" => "16:00:00"
                        ]
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        Fixtures::add('public-api-overrides-day-unavailable', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "08:00:00",
                            "end_time" => "11:00:00"
                        ]
                    ],
                    "is_available" => "0"
                ]
            ],
        ]);

        
        Fixtures::add('public-api-new-day-override', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:00:00",
                            "end_time" => "12:00:00"
                        ],
                        [
                            "start_time" => "14:00:00",
                            "end_time" => "17:00:00"
                        ]

                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        Fixtures::add('public-api-day-is-unavailable', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "13:00:00",
                            "end_time" => "14:00:00"
                        ]
                    ],
                    "is_available" => "0"
                ]
            ],
        ]);

        Fixtures::add('public-api-day-is-unavailable-half-slot', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "13:00:00",
                            "end_time" => "14:30:00"
                        ]
                    ],
                    "is_available" => "0"
                ]
            ],
        ]);
        Fixtures::add('public-api-day-booked-but-feature-off', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:00:00",
                            "end_time" => "12:00:00"
                        ],
                        [
                            "start_time" => "14:00:00",
                            "end_time" => "21:00:00"
                        ]
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        $tomorrow = date('Y-m-d', strtotime("tomorrow"));
        Fixtures::add('public-api-date-override-two-days-booked', [
            [
                "attributes" => [
                    "date" => $tomorrow,
                    "timeslots" => [
                        [
                            "start_time" => "09:00:00",
                            "end_time" => "12:30:00"
                        ],
                        [
                            "start_time" => "14:00:00",
                            "end_time" => "17:00:00"
                        ]
                    ],
                    "is_available" => "1"
                ]
            ],
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:00:00",
                            "end_time" => "12:30:00"
                        ],
                        [
                            "start_time" => "14:00:00",
                            "end_time" => "17:00:00"
                        ]

                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        $tz  = new \DateTimeZone('utc');
        $time = new \DateTime('now', $tz);
        $now = $time->format('Y-m-d H:i:s');
        Fixtures::add('bookings-get-response-empty-db', json_decode(
            '{
                "data": [
                    {
                        "type": "appointment-hours-booking",
                        "attributes": {
                            "updated_by": "115",
                            "store_id": "1003",
                            "unlimited_slots": "1",
                            "max_booking_slots": "1"
                        },
                        "links": {
                            "self": "https://tests.api.dev.salesfloor.net/v2/stores/"
                        }
                    }
                ]
            }',
            true)
        );

        Fixtures::add('bookings-get-response-full-db', json_decode(
            '{
                "data": [
                    {
                        "type": "appointment-hours-booking",
                        "id": "1",
                        "attributes": {
                            "created_at": "2022-01-14 01:04:59",
                            "updated_at": "2022-01-14 01:04:59",
                            "updated_by": "115",
                            "store_id": "1003",
                            "unlimited_slots": "1",
                            "max_booking_slots": "1"
                        },
                        "links": {
                            "self": "https://tests.api.dev.salesfloor.net/v2/stores/1"
                        }
                    }
                ]
            }',
            true)
        );

        Fixtures::add('bookings-post-response-unlimited-1', json_decode(
            '{
                "data": {
                    "type": "appointment-hours-booking",
                    "id": "1",
                    "attributes": {
                        "created_at": "2022-01-14 01:04:59",
                        "updated_at": "2022-01-14 01:04:59",
                        "updated_by": "115",
                        "store_id": "1003",
                        "unlimited_slots": "1",
                        "max_booking_slots": "1"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/stores/1"
                    }
                }
            }',
            true)
        );

        Fixtures::add('bookings-post-response-unlimited-0', json_decode(
            '{
                "data": {
                    "type": "appointment-hours-booking",
                    "id": "1",
                    "attributes": {
                        "created_at": "2022-01-14 01:04:59",
                        "updated_at": "2022-01-14 01:04:59",
                        "updated_by": "115",
                        "store_id": "1003",
                        "unlimited_slots": "0",
                        "max_booking_slots": "42"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/stores/1"
                    }
                }
            }',
            true)
        );

        Fixtures::add('bookings-patch-response-unlimited-0', json_decode(
            '{
                "data": {
                    "type": "appointment-hours-booking",
                    "id": "1",
                    "attributes": {
                        "updated_by": "115",
                        "store_id": "1003",
                        "unlimited_slots": "0",
                        "max_booking_slots": "42"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/stores/1"
                    }
                }
            }',
            true)
        );

        Fixtures::add('bookings-patch-response-unlimited-1', json_decode(
            '{
                "data": {
                    "type": "appointment-hours-booking",
                    "id": "1",
                    "attributes": {
                        "updated_by": "115",
                        "store_id": "1003",
                        "unlimited_slots": "1",
                        "max_booking_slots": "42"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/stores/1"
                    }
                }
            }',
            true)
        );

        $errors = new \stdClass;
        $errors->status = '400';
        $errors->code = '400';
        $errors->title = 'Enter a value of 1 or more';
        $errors->detail = 'Enter a value of 1 or more';
        Fixtures::add('bookings-response-exception', $errors);

        Fixtures::add('date-override-30m-finish-at-30m', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:30:00",
                            "end_time" => "17:00:00"
                        ],
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        Fixtures::add('date-override-duration-config-30min', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:30:00",
                            "end_time" => "17:30:00"
                        ],
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);

        Fixtures::add('date-override-with-empty-schedule', [
            [
                "attributes" => [
                    "date" => $nextMonday,
                    "timeslots" => [
                        [
                            "start_time" => "09:30:00",
                            "end_time" => "21:30:00"
                        ],
                    ],
                    "is_available" => "1"
                ]
            ],
        ]);
    }


}
