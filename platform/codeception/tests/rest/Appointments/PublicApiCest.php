<?php

declare(strict_types=1);

namespace SF\rest\Appointments;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Salesfloor\Services\Mail\EmailTemplateFactory;
use Salesfloor\Services\Mail\Templates\CustomerRequestEmailTemplate;
use Codeception\Stub;
use Salesfloor\Models\Customer;
use SF\RestTester;
use SF\rest\BaseRest;

/**
 * @group database_transaction
 */
class PublicApiCest extends BaseRest
{
    public function _before($I)
    {
        parent::_before($I);

        $this->insertFixtureGroup($I, 'user');
        $this->insertFixtureGroup($I, 'store');
    }


    /**
     * Changes the data in some way
     *
     * @param array $data The data we're operating on
     * @param string $operation What we're doing to the data
     * @param string $key
     * @param string|integer $value
     * @return array
     */
    protected function changeData($data, $operation, $key, $value = ''): array
    {
        switch ($operation) {
            case 'delete':
                unset($data[$key]);
                break;

            case 'replace':
                $data[$key] = $value;
                break;
        }

        return $data;
    }


    protected function getData($whichData)
    {
        Carbon::setTestNow('2020-10-01 10:00:00');
        $date = Carbon::now()->addDay()->toDateString();

        switch ($whichData) {
            case 'request':
                return [
                    "store" =>  1003,
                    "user_id" =>  115,
                    "sf_locale" => "en_US",
                    "service" => "Virtual",
                    "date" => $date,
                    "time" => "10:00",
                    "name" => "Jean Bon",
                    "phone" => "5145555555",
                    "country" => "CA",
                    "contact_via" => "email",
                    "email" => "<EMAIL>",
                    "timezone" => "America/New_York",
                    "extraInfo" => "These are the customer notes",
                    "category_id" => "1YtcsOgliKX9kE34hKt2goabYh0",
                    "customer_ip" => "**************",
                ];

            case 'email_request':
                return [
                    "store" => 1003,
                    "date" => $date,
                    "time" => "10:00",
                    "name" => "Jean Bon",
                    "contact_via" => "email",
                    "email" => "<EMAIL>",
                    "timezone" => "America/New_York",
                    "user_id" => 115,
                    "customer_ip" => "**************",
                ];

            case 'email_request_2':
                return [
                    "store" => 1003,
                    "date" => $date,
                    "time" => "10:00",
                    "name" => "Jean Bon",
                    "contact_via" => "email",
                    "email" => "<EMAIL>",
                    "timezone" => "America/New_York",
                    "user_id" => 115,
                    "customer_ip" => "**************",
                    "phone" => '',
                ];

            case 'sms_request':
                return [
                    "user_id" => 115,
                    "date" => $date,
                    "time" => "10:00",
                    "name" => "Jean Bon",
                    "contact_via" => "text_message",
                    "phone" => "+15145555555",
                    "timezone" => "America/New_York",
                    "customer_ip" => "**************",
                ];
            case 'sms_request_subscribed':
                return [
                    "user_id" => 115,
                    "date" => $date,
                    "time" => "10:00",
                    "name" => "Jean Bon",
                    "contact_via" => "text_message",
                    "subscription" => true,
                    "phone" => "+15145555555",
                    "timezone" => "America/New_York",
                    "customer_ip" => "**************",
                ];
            case 'sf_appointments':
                $datetime = Carbon::now()->addDay();
                return [
                    'user_id' => 115,
                    // Mr Bon:
                    // 'customer_id' => 3,
                    'store_id' => 1003,
                    // 'loc_id' => 1,
                    'locale' => 'en_US',
                    'event_type' => 'Virtual',
                    'notes' => 'These are the customer notes',
                    'channel' => 'email',
                    'source' => null,
                    'source_title' => '',
                    'source_url' => '',
                    'timezone' => 'America/New_York',
                    // DB stores in UTC:
                    // Comment it out since it does not take care of summer time.
                    // 'date' => $UTCDateTime->format('Y-m-d') . ' 15:00:00',
                    'status' => '',
                    'version' => 0,
                    'phone' => '+15145555555',
                ];

            case 'sf_appointments_sms':
                $datetime = Carbon::now()->addDay()->hour(10)->minute(0);
                return [
                    'user_id' => 115,
                    'store_id' => 1003,
                    'loc_id' => 1,
                    'locale' => 'en_US',
                    'event_type' => 'Virtual',
                    'channel' => 'text',
                    'source_url' => '',
                    'timezone' => 'America/New_York',
                    // DB stores in UTC:
                    // Comment it out since it does not take care of summer time.
                    // 'date' => $UTCDateTime->format('Y-m-d') . ' 15:00:00',
                    'version' => 0,
                    'phone' => '+15145555555',
                ];

            case 'geoip_location':
                return [
                    // 'country_code' => 'CA',
                    // 'region' => '',
                    // 'city' => 'Windsor',
                    // 'postal_code' => 'N8X',
                    // // Our version of Codeception does not recognize floats
                    // // I tried casting them spercifically as floats (rather than "double"s),
                    // // to strings, no matter what I do no luck. But these ARE in fact the same
                    // // as the ones in the DB:
                    // // 'latitude' => floatval(42.3028),
                    // // 'longitude' => floatval(-83.0345),
                    // 'area_code' => 0,

                    'country_code' => 'CA',
                    'region' => '',
                    'city' => 'Toronto',
                    'postal_code' => 'M6S',
                    // 'area_code' => 'ON',
                    // Our version of Codeception does not recognize floats
                    // I tried casting them spercifically as floats (rather than "double"s),
                    // to strings, no matter what I do no luck. But these ARE in fact the same
                    // as the ones in the DB:
                    // 'latitude' => floatval(42.3028),
                    // 'longitude' => floatval(-83.0345),
                    'area_code' => 0,
                ];

            case 'geoip_location_not_found':
                return [
                    'country_code' => 'CA',
                    'region' => '',
                    'city' => null,
                    'postal_code' => '',
                    'area_code' => null,
                ];

            case 'sf_customer':
                return [
                    'user_id' => 115,
                    'name'  => 'Jean Bon',
                    'email' => '<EMAIL>',
                    'phone' => '+15145555555',
                    'first_name' => 'Jean',
                    'last_name'  => 'Bon',
                    'localization' => 'en',
                    'note'   => 'These are the customer notes',
                    'subcribtion_flag' => 0,
                    'contact_preference' => 'email',
                ];

            case 'sf_events':
                return [
                    [
                        "type" => 27,
                        "source" => "contact_management",
                        "user_id" => 115,
                        // "customer_id" => 0,
                        // "attributes" => "1",
                        "satisfied" => 0,
                        "event_id" => 0,
                        "acknowledged" => 0,
                        "store_id" => 1000
                    ],
                    [
                        "type" => 7,
                        "source" => "",
                        "user_id" => 115,
                        // "customer_id" => 3,
                        // "attributes" => "1",
                        "satisfied" => 0,
                        "event_id" => 0,
                        "acknowledged" => 0,
                        "store_id" => 1000
                    ]
                ];

            case 'sf_messages':
                return [
                    "user_id" => 115,
                    "owner_id" => 115,
                    // "customer_id" => 3,
                    // "thread_id" => 1,
                    "from_type" => "customer",
                    "from_email" => "<EMAIL>",
                    "from_name" => "Jean Bon",
                    "request_type" => "book_appointment",
                    // "request_id" => 1,
                    "attachment" => "",
                    "type" => "customer_request",
                    "status" => "unread",
                    "category" => "inbox",
                    "last_category" => null,
                    "message" => "1",
                    "title" => "Appointment Request",
                    "products" => "",
                    "comment" => "These are the customer notes",
                    "locale" => "en_US"
                ];


            case 'sf_customer_activity_feed':
                return [
                    // "customer_id" => 3,
                    "user_id" => 115,
                    "type" => 3,
                    // "thread_id" => "1",
                    "preview" => "These are the customer notes",
                    "direction" => "inbound",
                    "status" => "unread",
                ];

            case 'sf_customer_field_history':
                return [
                    [
                        // "customer_id" => 3,
                        "field_name" => "email",
                        "old_value" => "<EMAIL>",
                        "new_value" => "<EMAIL>",
                        "field_modified_by" => null,
                        "source" => "sf_appointments",
                        // "source_id" => 1,
                        "position" => null
                    ],
                    [
                        // "customer_id" => 3,
                        "field_name" => "subcribtion_flag",
                        "old_value" => "0",
                        "new_value" => "0",
                        "field_modified_by" => null,
                        "source" => "sf_appointments",
                        // "source_id" => 1,
                        "position" => null
                    ],
                    [
                        // "customer_id" => 3,
                        "field_name" => "phone",
                        "old_value" => "+15145555555",
                        "new_value" => "+15145555555",
                        "field_modified_by" => null,
                        "source" => "sf_appointments",
                        // "source_id" => 1,
                        "position" => null
                    ],
                    [
                        // "customer_id" => 3,
                        "field_name" => "sms_marketing_subscription_flag",
                        "old_value" => "0",
                        "new_value" => "0",
                        "field_modified_by" => null,
                        "source" => "sf_appointments",
                        // "source_id" => 1,
                        "position" => null
                    ]
                ];

            case 'sf_unattached_customers':
                return [
                    "user_id" => 0,
                    "stand_down" => 0,
                    "stand_down_count" => 0,
                    "last_stand_down" => null
                ];

            default:
                return;
        }
    }

    /** @group database_transaction */
    public function testAPICanCreateNewAppointment(RestTester $I)
    {
        // Create appointment hours status
        $request = $this->getData('request');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->assertArrayNotHasKey('error', (array)$response);

        $customers = $I->grabRowsFromDatabase('sf_customer', [], $this->getData('sf_customer'));
        $I->assertCount(1, $customers);
        $customer = $customers[0];

        $geoIpLocatons = $I->grabRowsFromDatabase('geoip_location', [], $this->getData('geoip_location'));
        $I->assertCount(1, $geoIpLocatons);
        $geoIpLocation = $geoIpLocatons[0];

        $sf_appointments = $this->getData('sf_appointments');
        $sf_appointments = array_merge($sf_appointments, [
            'customer_id' => (int)$customer['ID'],
            'loc_id' => (int)$geoIpLocation['id'],
        ]);
        $I->seeInDatabase('sf_appointments', $sf_appointments);
    }

    /** @group database_transaction */
    public function testAPICanCreateTwoNewAppointmentsForSameUser(RestTester $I)
    {

        // Create appointment hours status
        $request = $this->getData('request');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertArrayNotHasKey('error', (array)$response);

        $response2 = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());

        $customers = $I->grabRowsFromDatabase('sf_customer', [], $this->getData('sf_customer'));
        $I->assertCount(1, $customers);
        $customer = $customers[0];

        $geoIpLocatons = $I->grabRowsFromDatabase('geoip_location', [], $this->getData('geoip_location'));
        $I->assertCount(1, $geoIpLocatons);
        $geoIpLocation = $geoIpLocatons[0];

        $sf_appointments = $this->getData('sf_appointments');
        $sf_appointments = array_merge($sf_appointments, [
            'customer_id' => (int)$customer['ID'],
            'loc_id' => (int)$geoIpLocation['id'],
        ]);
        $I->seeInDatabase('sf_appointments', $sf_appointments);
    }

    /** @group database_transaction */
    public function testAPIStoreIdNotFound(RestTester $I)
    {
        // Create appointment hours status
        $request = $this->getData('request');
        $response = $I->doDirectPost($I, 'v2/public/stores/1234/appointments', $request);

        $error = new \stdClass();
        $error->error = 'Store Id is invalid';
        $I->assertEquals($response, $error);
    }

    /** @group database_transaction */
    public function testAPICanCreateNewAppointmentWithSmsData(RestTester $I)
    {

        $I->haveInDatabase('sf_user_phone_number', [
            'id' => '1',
            'user_id' => 115,
            'phone_number' => '+151455555555', // fake phone number
            'created_by_user_id' => 1, // can't be null
        ]);

        $smsRequest = $this->getData('sms_request_subscribed');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $smsRequest);
        $I->assertArrayNotHasKey('error', (array)$response);

        $geoIpLocatons = $I->grabRowsFromDatabase('geoip_location', [], $this->getData('geoip_location'));
        $I->assertCount(1, $geoIpLocatons);
        $geoIpLocation = $geoIpLocatons[0];

        $sf_appointments = $this->getData('sf_appointments_sms');
        $sf_appointments = array_merge($sf_appointments, [
            'loc_id' => (int)$geoIpLocation['id'],
        ]);
        $I->seeInDatabase('sf_appointments', $sf_appointments);

        $customers = $I->grabRowsFromDatabase('sf_customer', [], [
            'user_id' => 115,
            'phone' => '+15145555555',
        ]);

        $I->assertCount(1, $customers);
        $customer = $customers[0];
        $I->assertEquals(Customer::SMS_SUBSCRIPTION_STATUS_SUBSCRIBED, $customer['sms_marketing_subscription_flag']);

        $textThread = [
            "id" => "3",
            "customer_id" => $customer['ID'],
            "user_id" => "115",
            "user_phone_number" => "+151455555555",
            "customer_phone_number" => "+15145555555",
            "is_read" => "1",
            "is_valid" => "1",
            "is_active" => "1"
        ];

        $textMessage = [
            "text_thread_id" => "3",
            "direction" => "inbound-service",
            "status" => "received",
            "is_active" => "1",
        ];

        $I->seeInDatabase('sf_text_thread', $textThread);
        $I->seeInDatabase('sf_text_message', $textMessage);
    }


    /** @group database_transaction */
    public function testAPIMissingOrInvalidDataFromAppointmentsValidator(RestTester $I)
    {
        $goodRequest = $this->getData('email_request');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $goodRequest);
        $I->assertEquals(200, $I->grabResponseStatusCode());

        $error = new \stdClass();
        $error->error = 'Missing or invalid field from payload';

        $noDate = $this->changeData($goodRequest, 'delete', 'date');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $noDate);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, $error);

        $noTime = $this->changeData($goodRequest, 'delete', 'time');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $noTime);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, $error);

        $noEmail = $this->changeData($goodRequest, 'delete', 'email');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $noEmail);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, $error);

        $noTimezone = $this->changeData($goodRequest, 'delete', 'timezone');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $noTimezone);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, $error);

        $wrongTimezone = $this->changeData($goodRequest, 'replace', 'timezone', 'America/NewYork');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $wrongTimezone);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $timeZoneError = new \stdClass();
        $timeZoneError->error = 'Invalid timezone';
        $I->assertEquals($response, $timeZoneError);
    }


    /** @group database_transaction */
    public function testTeamModeWithUserId(RestTester $I)
    {
        // Team Mode:
        $this->app['configs']['retailer.storepage_mode'] = true;

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => '1000',
            'is_enabled' => '1',
            'created_by' => '115',
        ]);

        $request = $this->getData('request');
        // user_id (== Rep ID) in the request is originally 115; we leave it as is,
        // and Team Mode should change it to the store_user_id when saving to the DB.
        // However 'store' (from Fixture.php) must be changed before sending the request
        // to make sure we get the right store_user_id:
        $request['store'] = 1000;


        $response = $I->doDirectPost($I, 'v2/public/stores/1000/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());

        $sf_appointments = $this->getData('sf_appointments');
        // In the DB, we save store_user_id for Team Mode, regardless of including user_id (== Rep ID):
        $sf_appointments['user_id'] = 280;
        $sf_appointments['store_id'] = 1000;
        $I->seeInDatabase('sf_appointments', $sf_appointments);

        $sf_customer = $this->getData('sf_customer');
        $sf_customer['user_id'] = 280;
        $I->seeInDatabase('sf_customer', $sf_customer);

        $geoip_location = $this->getData('geoip_location');
        $I->seeInDatabase('geoip_location', $geoip_location);

        $sf_events = $this->getData('sf_events');
        foreach ($sf_events as $k => $v) {
            $sf_events[$k]['user_id'] = 280;
            $I->seeInDatabase('sf_events', $sf_events[$k]);
        }

        // $appointments = $I->grabRowsFromDatabase('sf_messages');
        $sf_messages = $this->getData('sf_messages');
        $sf_messages['user_id'] =  $sf_messages['owner_id'] = 280;
        $I->seeInDatabase('sf_messages', $sf_messages);

        $sf_customer_activity_feed = $this->getData('sf_customer_activity_feed');
        $sf_customer_activity_feed['user_id'] = 280;
        $I->seeInDatabase('sf_customer_activity_feed', $sf_customer_activity_feed);

        $sfDb = $I->grabRowsFromDatabase('sf_customer_field_history');
        $I->assertEquals($sfDb[0]['uniq_action'], $sfDb[1]['uniq_action']);
        $I->assertEquals($sfDb[1]['uniq_action'], $sfDb[2]['uniq_action']);
        $I->assertEquals($sfDb[2]['uniq_action'], $sfDb[3]['uniq_action']);
        $sf_customer_field_history = $this->getData('sf_customer_field_history');
        foreach ($sf_customer_field_history as $k => $v) {
            $I->seeInDatabase('sf_customer_field_history', $sf_customer_field_history[$k]);
        }

        // With a user_id (== Rep ID), there is no unattached_id:
        $unattached_id = $I->grabFromDatabase('sf_appointments', 'unattached_id', ['ID' => 1]);
        $I->assertEmpty($unattached_id);

        $sfDb = $I->grabRowsFromDatabase('sf_unattached_customers');
        $I->assertEmpty($sfDb[0]['id']);
    }


    /** @group database_transaction */
    public function testTeamModeNoUserId(RestTester $I)
    {
        // Team Mode:
        $this->app['configs']['retailer.storepage_mode'] = true;

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => '1000',
            'is_enabled' => '1',
            'created_by' => '115',
        ]);

        $request = $this->getData('request');
        // user_id (== Rep ID) in the request is originally 115; we leave it as is,
        // and Team Mode should change it to the store_user_id when saving to the DB.
        // However 'store' (from Fixture.php) must be changed before sending the request
        // to make sure we get the right store_user_id:
        $request['store'] = 1000;
        // Get rid of the user_id (== Rep ID):
        unset($request['user_id']);
        $request['customer_tracking_id'] = "0bprteada";
        $request['fingerprint'] = "918107325694";


        $response = $I->doDirectPost($I, 'v2/public/stores/1000/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());

        $sf_appointments = $this->getData('sf_appointments');
        // In the DB, we save store_user_id for Team Mode, regardless of including user_id (== Rep ID):
        $sf_appointments['user_id'] = 280;
        $sf_appointments['store_id'] = 1000;

        $a = $I->grabRowsFromDatabase('sf_appointments');
        $I->seeInDatabase('sf_appointments', $sf_appointments);

        $sf_customer = $this->getData('sf_customer');
        $sf_customer['user_id'] = 280;
        $I->seeInDatabase('sf_customer', $sf_customer);

        $geoip_location = $this->getData('geoip_location');
        $I->seeInDatabase('geoip_location', $geoip_location);

        $sf_events = $this->getData('sf_events');
        foreach ($sf_events as $k => $v) {
            $sf_events[$k]['user_id'] = 280;
            $I->seeInDatabase('sf_events', $sf_events[$k]);
        }

        $sf_messages = $this->getData('sf_messages');
        $sf_messages['user_id'] =  $sf_messages['owner_id'] = 280;
        $I->seeInDatabase('sf_messages', $sf_messages);

        $sf_customer_activity_feed = $this->getData('sf_customer_activity_feed');
        $sf_customer_activity_feed['user_id'] = 280;
        $I->seeInDatabase('sf_customer_activity_feed', $sf_customer_activity_feed);

        $sfDb = $I->grabRowsFromDatabase('sf_customer_field_history');
        $I->assertEquals($sfDb[0]['uniq_action'], $sfDb[1]['uniq_action']);
        $I->assertEquals($sfDb[1]['uniq_action'], $sfDb[2]['uniq_action']);
        $I->assertEquals($sfDb[2]['uniq_action'], $sfDb[3]['uniq_action']);
        $sf_customer_field_history = $this->getData('sf_customer_field_history');
        foreach ($sf_customer_field_history as $k => $v) {
            $I->seeInDatabase('sf_customer_field_history', $sf_customer_field_history[$k]);
        }

        // With no user_id (== Rep ID), there is an unattached_id:
        $unattached_id = $I->grabFromDatabase('sf_appointments', 'unattached_id');
        $I->assertNotEmpty($unattached_id);

        $sf_unattached_customers = $this->getData('sf_unattached_customers');
        $I->seeInDatabase('sf_unattached_customers', $sf_unattached_customers);
        $sfDb = $I->grabRowsFromDatabase('sf_unattached_customers');
        // Can't get these exact values, so:
        $I->assertNotEmpty($sfDb[0]['id']);
    }

    /** @group database_transaction */
    public function testRepModeWithUserId(RestTester $I)
    {
        // Rep Mode:
        $this->app['configs']['retailer.storepage_mode'] = false;

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => '1000',
            'is_enabled' => '1',
            'created_by' => '115',
        ]);

        $request = $this->getData('request');
        // user_id (== Rep ID) in the request is originally 115; we leave it as is,
        // and Team Mode should change it to the store_user_id when saving to the DB.
        // However 'store' (from Fixture.php) must be changed before sending the request
        // to make sure we get the right store_user_id:
        $request['store'] = 1000;


        $response = $I->doDirectPost($I, 'v2/public/stores/1000/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());

        $sf_appointments = $this->getData('sf_appointments');
        // In the DB, we save user_id (== Rep ID):
        $sf_appointments['store_id'] = 1000;
        $I->seeInDatabase('sf_appointments', $sf_appointments);

        $sf_customer = $this->getData('sf_customer');
        $I->seeInDatabase('sf_customer', $sf_customer);

        $geoip_location = $this->getData('geoip_location');
        $I->seeInDatabase('geoip_location', $geoip_location);

        $sf_events = $this->getData('sf_events');
        foreach ($sf_events as $k => $v) {
            $I->seeInDatabase('sf_events', $sf_events[$k]);
        }

        $sf_messages = $this->getData('sf_messages');
        $I->seeInDatabase('sf_messages', $sf_messages);

        $sf_customer_activity_feed = $this->getData('sf_customer_activity_feed');
        $I->seeInDatabase('sf_customer_activity_feed', $sf_customer_activity_feed);


        $sfDb = $I->grabRowsFromDatabase('sf_customer_field_history');
        $I->assertEquals($sfDb[0]['uniq_action'], $sfDb[1]['uniq_action']);
        $I->assertEquals($sfDb[1]['uniq_action'], $sfDb[2]['uniq_action']);
        $I->assertEquals($sfDb[2]['uniq_action'], $sfDb[3]['uniq_action']);
        $sf_customer_field_history = $this->getData('sf_customer_field_history');
        foreach ($sf_customer_field_history as $k => $v) {
            $I->seeInDatabase('sf_customer_field_history', $sf_customer_field_history[$k]);
        }

        $unattached_id = $I->grabFromDatabase('sf_appointments', 'unattached_id', ['ID' => 1]);
        $I->assertEmpty($unattached_id);

        $sfDb = $I->grabRowsFromDatabase('sf_unattached_customers');
        $I->assertEmpty($sfDb[0]['id']);
    }


    /** @group database_transaction */
    public function testRepModeNoUserId(RestTester $I)
    {
        // Rep Mode:
        $this->app['configs']['retailer.storepage_mode'] = false;

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => '1000',
            'is_enabled' => '1',
            'created_by' => '115',
        ]);

        $request = $this->getData('request');
        // user_id (== Rep ID) in the request is originally 115; we leave it as is,
        // and Team Mode should change it to the store_user_id when saving to the DB.
        // However 'store' (from Fixture.php) must be changed before sending the request
        // to make sure we get the right store_user_id:
        $request['store'] = 1000;
        // Get rid of the user_id (== Rep ID):
        unset($request['user_id']);
        $request['customer_tracking_id'] = "0bprteada";
        $request['fingerprint'] = "918107325694";


        $response = $I->doDirectPost($I, 'v2/public/stores/1000/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());

        $sf_appointments = $this->getData('sf_appointments');
        // In the DB, we save user_id (== Rep ID):
        $sf_appointments['user_id'] = 0;
        $sf_appointments['store_id'] = 1000;
        $I->seeInDatabase('sf_appointments', $sf_appointments);

        $sf_customer = $this->getData('sf_customer');
        $sf_customer['user_id'] = 0;
        $I->seeInDatabase('sf_customer', $sf_customer);

        $geoip_location = $this->getData('geoip_location');
        $I->seeInDatabase('geoip_location', $geoip_location);

        $sf_events = $this->getData('sf_events');
        foreach ($sf_events as $k => $v) {
            $sf_events[$k]['user_id'] = 0;
            $sf_events[$k]['store_id'] = 0;
            $I->seeInDatabase('sf_events', $sf_events[$k]);
        }

        // We don't send messages without a Rep Id (replicating the widget/sidebar):
        $sf_messages = [];
        $sfDb = $I->grabRowsFromDatabase('sf_messages');
        $I->assertEquals($sfDb, $sf_messages);

        $sf_customer_activity_feed = $this->getData('sf_customer_activity_feed');
        $sf_customer_activity_feed['user_id'] = 0;
        $I->seeInDatabase('sf_customer_activity_feed', $sf_customer_activity_feed);

        $sfDb = $I->grabRowsFromDatabase('sf_customer_field_history');
        $I->assertEquals($sfDb[0]['uniq_action'], $sfDb[1]['uniq_action']);
        $I->assertEquals($sfDb[1]['uniq_action'], $sfDb[2]['uniq_action']);
        $I->assertEquals($sfDb[2]['uniq_action'], $sfDb[3]['uniq_action']);
        $sf_customer_field_history = $this->getData('sf_customer_field_history');
        foreach ($sf_customer_field_history as $k => $v) {
            $I->seeInDatabase('sf_customer_field_history', $sf_customer_field_history[$k]);
        }

        // Without a user_id (== Rep ID), there is an unattached_id:
        $unattached_id = $I->grabFromDatabase('sf_appointments', 'unattached_id');
        $I->assertNotEmpty($unattached_id);

        $sfDb = $I->grabRowsFromDatabase('sf_unattached_customers');
        $I->assertNotEmpty($sfDb[0]['id']);
    }


    public function testSettingAppointmentOutsideOfCustomStoreHours(RestTester $I)
    {
        $nextMonday = date('m/d/Y', strtotime("next Monday"));

        $this->insertWeeklySchedule($I);
        $request = $this->getData('email_request');
        $request['date'] = $nextMonday;
        $request['time'] = '11:00';

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $error = new \stdClass();
        $error->error = 'api_exception_appointment_time_not_available';

        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, $error);
    }

    /** @group database_transaction */
    public function testMoreAppointmentsThanAllowed(RestTester $I)
    {
        // Team Mode:
        $this->app['configs']['retailer.storepage_mode'] = true;

        $nextMonday = Carbon::now()->next(Carbon::MONDAY);

        $this->insertWeeklySchedule($I);
        $request = $this->getData('email_request');
        $request['date'] = $nextMonday->format('m/d/Y');
        $request['time'] = '10:00';


        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, []);

        $response2 = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response2, []);

        $response3 = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $error = new \stdClass();
        $error->error = 'api_exception_appointment_time_not_available';
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response3, $error);
    }

    /** @group database_transaction */
    public function testDateOverridesForCustomStoreHours(RestTester $I)
    {
        $nextMonday = Carbon::now()->next(Carbon::MONDAY);

        $this->insertWeeklySchedule($I);
        $request = $this->getData('email_request');
        $request['date'] = $nextMonday->format('m/d/Y');
        $request['time'] = '11:00';

        $I->haveInDatabase('sf_store_appointment_hours_overrides', [
            'id' => 1,
            'store_id' => 1003,
            'is_available' => 1,
            'updated_by' => 115,
            'date' => $nextMonday->toDateString(),
            'timeslots' => json_encode([['start_time' => '09:00:00', 'end_time' => '11:00:00']])
        ]);


        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, []);
    }

    private function insertWeeklySchedule($I, $startTime = "09:00:00", $endTime = "22:00:00")
    {
        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => 1003,
            'is_enabled' => 1,
            'created_by' => 115,
        ]);

        $I->haveInDatabase('sf_store_appointment_hours_settings', [
            'store_id' => 1003,
            'unlimited_slots' => 0,
            'max_booking_slots' => 2,
            'updated_by' => 115
        ]);

        $rows = [
            [
                'id' => 1,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'sunday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])
            ],
            [
                'id' => 2,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'monday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => '10:00:00']])
            ],
            [
                'id' => 3,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'tuesday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])
            ],
            [
                'id' => 4,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'wednesday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])
            ],
            [
                'id' => 5,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'thursday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])
            ],
            [
                'id' => 6,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'friday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])
            ],
            [
                'id' => 7,
                'store_id' => '1003',
                'is_available' => '1',
                'day_of_week' => 'saturday',
                'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])
            ],
        ];
        foreach ($rows as $row) {
            $I->haveInDatabase('sf_store_appointment_hours', $row);
        }
    }

    /** @group database_transaction */
    public function testForGeoIpFeature(RestTester $I)
    {
        $appointments  = $I->grabRowsFromDatabase('sf_appointments');
        $geoIpLocation = $I->grabRowsFromDatabase('geoip_location');

        $I->assertEquals([], $appointments);
        $I->assertEquals([], $geoIpLocation);


        // status 200, $geoIP full, database empty, gets a $geoIP row:
        $request = $this->getData('request');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertArrayNotHasKey('error', (array)$response);

        $appointments = [
            'store_id'    => $request['store'],
            'user_id'     => $request['user_id'],
            // 'loc_id'      => 1,
            'locale'      => $request['sf_locale'],
            'event_type'  => $request['service'],
            'phone'       => '+1' . $request['phone'],
            'channel'     => $request['contact_via'],
            'timezone'    => $request['timezone'],
            'notes'       => $request['extraInfo'],
        ];

        $I->seeInDatabase('sf_appointments', $appointments);
        $I->seeInDatabase('geoip_location', ['country_code' => $request['country']]);

        // No customer IP:
        $request = $this->getData('email_request');
        unset($request['customer_ip']);
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertArrayHasKey('error', (array)$response);
    }

    /** @group database_transaction */
    public function testAppointmentHourCannotBeInThePast(RestTester $I)
    {
        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => 1003,
            'is_enabled' => 1,
            'created_by' => 115,
        ]);

        $I->haveInDatabase('sf_store_appointment_hours_settings', [
            'store_id' => 1003,
            'unlimited_slots' => 1,
            'max_booking_slots' => 2,
            'updated_by' => 115
        ]);

        $startTime = "00:00:00";
        $endTime = "24:00:00";

        $rows = [
            ['id' => 1, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'sunday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 2, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'monday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => '10:00:00']])],
            ['id' => 3, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'tuesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 4, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'wednesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 5, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'thursday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 6, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'friday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 7, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'saturday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])]
        ];
        foreach ($rows as $row) {
            $I->haveInDatabase('sf_store_appointment_hours', $row);
        }

        $request = $this->getData('email_request');
        $today = Carbon::now('America/New_York');
        $pastHour = $today->copy()->subHours(1);
        $request['date'] = $today->format('Y-m-d');
        $request['time'] = $pastHour->format('H:30');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals((array)$response, ['error' => 'Date cannot be in the past']);

        $nextHour = $today->copy()->addHours(1);
        $request['time'] = $nextHour->format('H:00');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals($response, []);
    }

    /** @group database_transaction */
    public function testUserCanNotCreatePhoneServiceWithoutEmailAndPhone(RestTester $I)
    {
        $request = $this->getData('request');
        $request['service'] = 'Phone';

        $I->haveInDatabase('sf_user_phone_number', [
            'id' => '1',
            'user_id' => 115,
            'phone_number' => '+15142225555',
            'created_by_user_id' => 1,
        ]);

        // No phone (no email also causes an exception much earlier b/c $contactPreference == 'email'):
        unset($request['phone']);
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals('Must include both email and phone', $response->error);

        $request['contact_via'] = 'text_message';
        $request['phone'] = '5145555555';
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);
        $I->assertEquals(200, $I->grabResponseStatusCode());
        $I->assertEquals($response, []);
    }

    /** @group database_transaction */
    public function testEmailTemplatesWithLocale(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'events');

        // Same $context as exists in
        // services/src/Services/Appointments/Appointments.php::sendEmailToCustomer()
        // and Appointments::sendEmailToRep():
        $context = [
            'userId'    => 2,
            'eventId'   => 1,
            'emailType' => CustomerRequestEmailTemplate::TEMPLATE_REQUEST_SUCCESS, // 24
            'type'      => CustomerRequestEmailTemplate::REQUEST_TYPE_APPOINTMENT, // 'appointment'
            'locale'    => 'fr_CA',
        ];

        $emailTemplate = Stub::construct(EmailTemplateFactory::class, [$this->app]);
        $template = $emailTemplate->build('CustomerRequest', $context);
        $I->assertEquals($template->getContext()['locale'], 'fr_CA');
    }

    /** @group database_transaction */
    public function testGetAppointmentsPublicAPI(RestTester $I)
    {
        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => 1003,
            'is_enabled' => 1,
            'created_by' => 115,
        ]);

        $startTime = "09:00:00";
        $endTime = "12:00:00";

        $rows = [
            ['id' => 1, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'sunday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 2, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'monday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 3, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'tuesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 4, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'wednesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 5, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'thursday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 6, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'friday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 7, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'saturday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])]
        ];
        foreach ($rows as $row) {
            $I->haveInDatabase('sf_store_appointment_hours', $row);
        }

        // Tomorrow 9am to 12am
        $tz = new \DateTimeZone('America/New_York');
        $from = (new \DateTime(date('Y-m-d', strtotime("tomorrow")) . ' 9:00:00', $tz))->getTimestamp();
        $to = (new \DateTime(date('Y-m-d', strtotime("tomorrow")) . ' 12:00:00', $tz))->getTimestamp();
        $tomorrow = (new \DateTime('tomorrow', $tz))->format('Y-m-d');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to");

        $assertResponse = [

                "$tomorrow" => [
                    [
                        'start_time' => "9:00",
                        'end_time' => "9:30",
                        'available' => true
                    ],
                    [
                        'start_time' => "9:30",
                        'end_time' => "10:00",
                        'available' => true
                    ],
                    [
                        'start_time' => "10:00",
                        'end_time' => "10:30",
                        'available' => true
                    ],
                    [
                        'start_time' => "10:30",
                        'end_time' => "11:00",
                        'available' => true
                    ],
                    [
                        'start_time' => "11:00",
                        'end_time' => "11:30",
                        'available' => true
                    ],
                    [
                        'start_time' => "11:30",
                        'end_time' => "12:00",
                        'available' => true
                    ]
                ]
        ];
        $response = json_decode(json_encode($response), true);
        $I->assertEquals($assertResponse, $response['days']);
    }

    public function testAppointmentCreationOverwriteField(RestTester $I)
    {
        // Before making a request, make sure the user has already a contact with same email
        $this->insertFixtureGroup($I, 'testAppointmentCreationOverwriteField');

        $request = $this->getData('email_request_2');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        // Current customer should still have his old phone number.
        $I->seeInDatabase('sf_customer', [
            'email' => '<EMAIL>',
            'phone' => '+5145610765'
        ]);
    }

    /** @group database_transaction */
    public function testGetAppointmentsPublicAPIOneHourDuration(RestTester $I)
    {
        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment.duration'] = 60;

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => 1003,
            'is_enabled' => 1,
            'created_by' => 115,
        ]);

        $startTime = "09:00:00";
        $endTime = "12:00:00";

        $rows = [
            ['id' => 1, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'sunday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 2, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'monday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 3, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'tuesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 4, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'wednesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 5, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'thursday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 6, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'friday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 7, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'saturday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])]
        ];
        foreach ($rows as $row) {
            $I->haveInDatabase('sf_store_appointment_hours', $row);
        }

        // Tomorrow 9am to 12am
        $tz = new \DateTimeZone('America/New_York');
        $from = (new \DateTime(date('Y-m-d', strtotime("tomorrow")) . ' 9:00:00', $tz))->getTimestamp();
        $to = (new \DateTime(date('Y-m-d', strtotime("tomorrow")) . ' 12:00:00', $tz))->getTimestamp();
        $tomorrow = (new \DateTime('tomorrow', $tz))->format('Y-m-d');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to");

        $assertResponse = [

            "$tomorrow" => [
                [
                    'start_time' => "9:00",
                    'end_time' => "9:30",
                    'available' => true
                ],
                [
                    'start_time' => "9:30",
                    'end_time' => "10:00",
                    'available' => true
                ],
                [
                    'start_time' => "10:00",
                    'end_time' => "10:30",
                    'available' => true
                ],
                [
                    'start_time' => "10:30",
                    'end_time' => "11:00",
                    'available' => true
                ],
                [
                    'start_time' => "11:00",
                    'end_time' => "11:30",
                    'available' => true
                ]
            ]
        ];
        $response = json_decode(json_encode($response), true);
        $I->assertEquals($assertResponse, $response['days']);
    }

    /**
     * Test Appointment public endpoint + customer creation with Email/SMS channel
     */

    public function testAppointmentCustomerPhoneNew(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerPhoneNew');

        $request = $this->getData('sms_request');

        $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        // Even with sms, a new customer is now created
        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555',
            'name' => 'Jean Bon',
            'email' => null,
        ]);

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'phone' => '+15145555555',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'text',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateOnlyPhone(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerPhoneUpdateOnlyPhone');

        $request = $this->getData('sms_request');

        $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555',
            'name' => 'Jean Bon',
            'email' => null,
        ]);

        $I->assertNumberRowsInTable(1, 'sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555'
        ]);

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'phone' => '+15145555555',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'text',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateWithEmail(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerPhoneUpdateWithEmail');

        $request = $this->getData('sms_request');

        $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555',
            'name' => 'Jean Bon',
            'email' => '<EMAIL>',
        ]);

        $I->assertNumberRowsInTable(1, 'sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555'
        ]);

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'text',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateMultiple(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerPhoneUpdateMultiple');

        $request = $this->getData('sms_request');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        // Will take the one with the latest "modified_date"
        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555',
            'name' => 'Jean Bon',
            'email' => '<EMAIL>'
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555',
            'email' => '',
            'name' => null,
        ]);

        $I->assertNumberRowsInTable(2, 'sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555555'
        ]);

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'text',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateAlternate(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerPhoneUpdateAlternate');

        $request = $this->getData('sms_request');

        $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555556', // Because we don't overwrite phone if it exists.
            'name' => 'Jean Bon',
            'email' => '<EMAIL>',
        ]);

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'text',
            'customer_id' => $customerID
        ]);
    }

    ///////////////////////////////33
    /// Email

    public function testAppointmentCustomerEmailNew(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerEmailNew');

        $request = $this->getData('email_request');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => null,
            'name' => 'Jean Bon',
            'email' => '<EMAIL>',
        ]);

        $I->seeNumRecords(3, 'sf_customer');

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'email',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerEmailUpdateOnlyEmail(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerEmailUpdateOnlyEmail');

        $request = $this->getData('email_request');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => null,
            'name' => 'Jean Bon',
            'email' => '<EMAIL>',
        ]);

        $I->seeNumRecords(3, 'sf_customer');

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'email',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerEmailUpdateWithPhone(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerEmailUpdateWithPhone');

        $request = $this->getData('email_request');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15148825555',
            'name' => 'Jean Bon',
            'email' => '<EMAIL>',
        ]);

        $I->seeNumRecords(3, 'sf_customer');

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'email',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerEmailUpdateMultiple(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerEmailUpdateMultiple');

        $request = $this->getData('email_request');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15148825555',
            'name' => 'Jean Bon',
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1,
            'phone' => '+15148825555',
            'name' => null,
            'first_name' => 'John',
            'email' => '<EMAIL>',
        ]);

        $I->seeNumRecords(4, 'sf_customer');

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>',
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'email',
            'customer_id' => $customerID
        ]);
    }

    public function testAppointmentCustomerEmailUpdateAlternate(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerEmailUpdateAlternate');

        $request = $this->getData('email_request');

        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 115,
            'phone' => '+15145555556',
            'name' => 'Jean Bon',
            'email' => '<EMAIL>', // Email not updated since not empty.
        ]);

        $I->seeNumRecords(3, 'sf_customer');

        $customerID = $I->grabFromDatabase('sf_customer', 'ID', [
            'user_id' => 115,
            'email' => '<EMAIL>', // because it wasn't updated
        ]);

        $I->seeInDatabase('sf_appointments', [
            'user_id' => 115,
            'store_id' => 1003,
            'channel' => 'email',
            'customer_id' => $customerID
        ]);
    }

    public function testAPICanCreateNewAppointmentForDisabledStores(RestTester $I)
    {
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', [
            "store" =>  1003,
            "user_id" =>  115,
            "sf_locale" => "en_US",
            "service" => "Virtual",
            "date" => Carbon::now()->addDay()->toDateString(),
            "time" => "10:00",
            "name" => "Jean Bon",
            "country" => "ADMIN_CA",
            "contact_via" => "email",
            "email" => "<EMAIL>",
            "timezone" => "America/New_York",
            "extraInfo" => "These are the customer notes",
            "category_id" => "1YtcsOgliKX9kE34hKt2goabYh0",
            "customer_ip" => "**************",
        ]);

        $I->assertArrayNotHasKey('error', (array)$response);

        $I->seeInDatabase('sf_appointments', ['store_id' => '1003']);
    }

    /**
     * Test appointment booking when local time is too
     * late or too early
     * @param \SF\RestTester $I
     * @return void
     *
     */
    public function testGetTodayHoursWhenOutsideStoreHours(RestTester $I)
    {
        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $now = CarbonImmutable::now(new \DateTimeZone('America/New_York'));
        $storeHourStart = $now->subHours(3)->hour;
        $storeHourEnd = $now->subHours(2)->hour;

        // simulate the scenario when the customer
        // is trying to book appointment when local time is outside store hours
        $this->app['configs']['retailer.store_appointment_hours'] = [
            [
                'open'  => $storeHourStart,
                'close' => $storeHourEnd,
                'is_available' => '1', // 0 or 1
            ],
        ];

        $startTime = "09:00:00";
        $endTime = "12:00:00";

        $this->insertWeeklySchedule($I, $startTime, $endTime);

        // Today 9am to 12am
        $tz = new \DateTimeZone('America/New_York');
        $start = $now->addHours(3)->hour;
        $end = $now->addHours(4)->hour;
        $from = (new \DateTime(date('Y-m-d', strtotime("today")) . " $start:00:00", $tz))->getTimestamp();
        $to = (new \DateTime(date('Y-m-d', strtotime("today")) . " $end:00:00", $tz))->getTimestamp();
        $day = (new \DateTime('today', $tz))->format('Y-m-d');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to");

        $assertResponse = [
            "$day" => [],
        ];
        $response = json_decode(json_encode($response), true);
        $I->assertEquals($assertResponse, $response['days']);
    }

    /**
     * When the customer is from New Lead the user_id = 0
     * And we can't create new appointment for that scenario
     *
     */
    public function testAPICanCreateNewAppointmentForCustomerFromNewLeads(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'testAppointmentCustomerFromNewLead');

        $response = $I->doDirectPost($I, 'v2/public/stores/1000/appointments', [
            "store" =>  1003,
            "sf_locale" => "en_US",
            "service" => "Virtual",
            "date" => Carbon::now()->addDay()->toDateString(),
            "time" => "10:00",
            "name" => "Joseph Champion",
            "country" => "CA",
            "contact_via" => "email",
            "email" => "<EMAIL>",
            "timezone" => "America/New_York",
            "extraInfo" => "These are the customer notes",
            "category_id" => "1YtcsOgliKX9kE34hKt2goabYh0",
            "customer_ip" => "**************",
        ]);

        $I->assertArrayNotHasKey('error', (array)$response);
        $I->seeInDatabase('sf_appointments', ['customer_id' => '400']);

        $response = $I->doDirectPost($I, 'v2/public/stores/1000/appointments', [
            "store" =>  1003,
            "sf_locale" => "en_US",
            "service" => "Virtual",
            "date" => Carbon::now()->addDay()->toDateString(),
            "time" => "10:00",
            "name" => "Mary Anderson",
            "country" => "CA",
            "contact_via" => "text_message",
            'phone' => '+15145005555',
            "timezone" => "America/New_York",
            "extraInfo" => "These are the customer notes",
            "category_id" => "1YtcsOgliKX9kE34hKt2goabYh0",
            "customer_ip" => "**************",
        ]);
        $I->assertArrayNotHasKey('error', (array)$response);
        $I->seeInDatabase('sf_appointments', ['customer_id' => '500']);
    }

    public function testPublicTimeslotsAPIRemovesBookedAppointmentSlots(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'bookedTimeslots');

        $I->haveInDatabase('sf_store_appointment_hours_status', [
            'id' => 1,
            'store_id' => 1003,
            'is_enabled' => 1,
            'created_by' => 115,
        ]);

        $I->haveInDatabase('sf_store_appointment_hours_settings', [
            'store_id' => 1003,
            'unlimited_slots' => 0,
            'max_booking_slots' => 2,
            'updated_by' => 115
        ]);

        $startTime = "10:00:00";
        $endTime = "12:00:00";

        $rows = [
            ['id' => 1, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'sunday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 2, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'monday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 3, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'tuesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 4, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'wednesday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 5, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'thursday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 6, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'friday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])],
            ['id' => 7, 'store_id' => '1003', 'is_available' => '1', 'day_of_week' => 'saturday', 'timeslots' => json_encode([['start_time' => $startTime, 'end_time' => $endTime]])]
        ];
        foreach ($rows as $row) {
            $I->haveInDatabase('sf_store_appointment_hours', $row);
        }
        $tz = new \DateTimeZone('America/New_York');

        $from = (new \DateTime(date('Y-m-d', strtotime("tomorrow")) . ' 9:00:00', $tz))->getTimestamp();
        $to = (new \DateTime(date('Y-m-d', strtotime("tomorrow")) . ' 12:00:00', $tz))->getTimestamp();

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to");
        $response = json_decode(json_encode($response), true);
        $timeslots = ($response['days'])[date('Y-m-d', strtotime("tomorrow"))];
        $expected = [
            [
                'start_time' => "9:00",
                'end_time' => "9:30",
                'available' => false,
            ],
            [
                'start_time' => "9:30",
                'end_time' => "10:00",
                'available' => false,
            ],
            [
                'start_time' => "10:00",
                'end_time' => "10:30",
                'available' => false,
            ],
            [
                'start_time' => "10:30",
                'end_time' => "11:00",
                'available' => false,
            ],
            [
                'start_time' => "11:00",
                'end_time' => "11:30",
                'available' => true,
            ],
            [
                'start_time' => "11:30",
                'end_time' => "12:00",
                'available' => true,
            ],
        ];
        $I->assertEquals($expected, $timeslots);
    }
     /** @group database_transaction */
    public function testGetAppointmentsPublicAPIWithLeadTime(RestTester $I)
    {
        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment.duration'] = 60;
        $this->app['configs']['retailer.appointment_lead_time.widget'] = [
           'store' => 60, // minutes
           'virtual' => 120,
           'phone' => 180,
        ];

        $tz = new \DateTimeZone('America/Montreal');
        Carbon::setTestNow(Carbon::parse('2020-10-01 10:00:00', $tz));

        $startTime = Carbon::parse('2020-10-01 10:00:00', 'America/Montreal')->format('H:i:s');
        $endTime = Carbon::parse('2020-10-01 18:00:00', 'America/Montreal')->format('H:i:s');

        $this->insertWeeklySchedule($I, $startTime, $endTime);

        $from = Carbon::parse("2020-10-01 09:00:00", $tz)->format('U');
        $to = Carbon::parse("2020-10-01 17:00:00", $tz)->format('U');

        // lead time for Store appointments is 60 minutes
        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");
        $response = json_decode(json_encode($response), true);
        $I->assertEquals('11:00', $response['days']['2020-10-01'][0]['start_time']);

        // Second test for type phone
        // Lead time for Phone appointments is 180 minutes
        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=phone");
        $response = json_decode(json_encode($response), true);
        $I->assertEquals('13:00', $response['days']['2020-10-01'][0]['start_time']);

        // Test with minutes > 0
        Carbon::setTestNow(Carbon::parse('2020-10-01 13:44:00', $tz));
        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");
        $response = json_decode(json_encode($response), true);
        $I->assertEquals('15:00', $response['days']['2020-10-01'][0]['start_time']);

        // Getting the slots for next day
        $from = Carbon::parse("2020-10-03 10:00:00", $tz)->format('U');
        $to = Carbon::parse("2020-10-03 18:00:00", $tz)->format('U');
        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");

        $response = json_decode(json_encode($response), true);
        $I->assertEquals('10:00', $response['days']['2020-10-03'][0]['start_time']);

        Carbon::setTestNow();
    }

    /** @group database_transaction */
    public function testGetAppointmentsPublicAPIWithLeadTimeSpansNextDay(RestTester $I)
    {
        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment.duration'] = 60;
        $this->app['configs']['retailer.appointment_lead_time.widget'] = [
            'store' => 60 * 48, // minutes
            'virtual' => 120,
            'phone' => 180,
        ];

        $tz = new \DateTimeZone('America/New_York');
        Carbon::setTestNow(Carbon::parse('2020-10-01 10:00:00', $tz));
        $startTime = Carbon::parse('2020-10-01 10:00:00', 'America/New_York')->format('H:i:s');
        $endTime = Carbon::parse('2020-10-01 18:00:00', 'America/New_York')->format('H:i:s');

        $this->insertWeeklySchedule($I, $startTime, $endTime);

        $from = Carbon::now($tz)->format('U');
        $to = Carbon::parse("+4 hours", $tz)->format('U');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");

        $response = json_decode(json_encode($response), true);
        $expectedDay = Carbon::parse("+2 days", $tz)->format('Y-m-d');
        $expectedHour = Carbon::parse("+2 days", $tz)->format('H');
        $responseDays = $response['days'];
        $I->assertTrue(isset($responseDays[$expectedDay]));

        $real = (new \DateTime($responseDays[$expectedDay][0]['start_time'], $tz))->format('H');
        $real = Carbon::parse($responseDays[$expectedDay][0]['start_time'], $tz)->format('H');

        $I->assertEquals($expectedHour, $real);

        Carbon::setTestNow();
    }

    /** @group database_transaction */
    public function testGetAppointmentsPublicAPIWithLeadTimeZero(RestTester $I)
    {
        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment.duration'] = 60;
        $this->app['configs']['retailer.appointment_lead_time.widget'] = [
            'store' => 0, // minutes
            'virtual' => 120,
            'phone' => 180,
        ];

        $tz = new \DateTimeZone('America/New_York');
        Carbon::setTestNow(Carbon::parse('2020-10-01 11:59:00', $tz));
        $startTime = Carbon::parse('2020-10-01 10:00:00', 'America/New_York')->format('H:i:s');
        $endTime = Carbon::parse('2020-10-01 18:00:00', 'America/New_York')->format('H:i:s');

        $this->insertWeeklySchedule($I, $startTime, $endTime);
        $from = Carbon::now($tz)->format('U');
        $to = Carbon::parse("+4 hours", $tz)->format('U');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");
        $response = json_decode(json_encode($response), true);

        $day = Carbon::now($tz)->format('Y-m-d');

        $I->assertEquals('12:00', $response['days'][$day][0]['start_time']);

        Carbon::setTestNow();
    }

    public function testNewAppointmentUpdatesStatusToAutoAcceptForTeamMode(RestTester $I)
    {
        // For team mode
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['retailer.services.appointment.auto-accept.is_enabled'] = true;

        $request = $this->getData('request');
        $I->haveInDatabase('sf_messages', [
            'ID' => 3434,
            'customer_id' => 3,
            'user_id' => 115,
            'request_id' => 444333,
            'status' => 'unread',
            'date' => '2023-10-01 12:00:00',
        ]);
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->assertArrayNotHasKey('error', (array)$response);

        $sf_appointments = array_merge($this->getData('sf_appointments'), [
            'user_id' => 1, // this is store_user_id when in Team Mode
            'status' => 'accepted'
        ]);

        $I->seeInDatabase('sf_appointments', $sf_appointments);
        $I->seeInDatabase('sf_messages', ['ID' => 3434, 'status' => 'read']);

        // Make sure the config is working correctly
        $this->app['configs']['retailer.services.appointment.auto-accept.is_enabled'] = false;
        // Create appointment hours status
        $request = $this->getData('request');
        $response = $I->doDirectPost($I, 'v2/public/stores/1003/appointments', $request);

        $I->assertArrayNotHasKey('error', (array)$response);

        $sf_appointments = array_merge($this->getData('sf_appointments'), [
            'user_id' => 1, // this is store_user_id when in Team Mode
            'status' => '' // empty
        ]);
        $I->seeInDatabase('sf_appointments', $sf_appointments);
    }

    /** @group database_transaction */
    public function testGetAppointmentsPublicAPIWithLeadTimeFromTimeInPast(RestTester $I)
    {
        $tz = new \DateTimeZone('America/New_York');
        Carbon::setTestNow(Carbon::parse('2020-10-01 14:46:00', $tz));

        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment.duration'] = 60;
        $this->app['configs']['retailer.appointment_lead_time.widget'] = [
            'store' => 120, // minutes
            'virtual' => 120,
            'phone' => 120,
        ];

        $startTime = Carbon::parse('2020-10-01 10:00:00', 'America/New_York')->format('H:i:s');
        $endTime = Carbon::parse('2020-10-01 18:00:00', 'America/New_York')->format('H:i:s');

        $this->insertWeeklySchedule($I, $startTime, $endTime);
        $from = Carbon::parse("2020-10-01 09:00:00", $tz)->format('U');
        $to = Carbon::parse("2020-10-02 10:00:00", $tz)->format('U');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");
        $response = json_decode(json_encode($response), true);


        $I->assertEquals('17:00', $response['days']['2020-10-01'][0]['start_time']);

        Carbon::setTestNow();
    }

    public function testGetAppointmentsPublicAPIWithLeadTimeAddsFromPastToFuture(RestTester $I)
    {
        $tz = new \DateTimeZone('America/New_York');
        Carbon::setTestNow(Carbon::parse('2020-10-01 10:43:00', $tz));

        $this->app['configs']['retailer.store_appointment_hours.is_enabled'] = true;
        $this->app['configs']['retailer.services.appointment.duration'] = 60;
        $this->app['configs']['retailer.appointment_lead_time.widget'] = [
            'store' => 180, // 3h
        ];

        $this->insertWeeklySchedule($I, '08:00:00', '18:00:00');
        $from = Carbon::parse("2020-10-01 09:30:00", $tz)->format('U');
        $to = Carbon::parse("2020-10-02 10:00:00", $tz)->format('U');

        $response = $I->doDirectGet($I, "v2/public/stores/1003/appointment-hours?from=$from&to=$to&type=store");
        $response = json_decode(json_encode($response), true);


        $I->assertEquals('14:00', $response['days']['2020-10-01'][0]['start_time']);

        Carbon::setTestNow();
    }
}
