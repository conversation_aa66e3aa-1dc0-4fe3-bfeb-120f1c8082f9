<?php

declare(strict_types=1);

namespace SF\rest\Appointments;

use Carbon\Carbon;
use Codeception\Util\Fixtures;

class Fixture
// phpcs:ignoreFile
{
    public function appointments()
    {
        Fixtures::add('user', [
            'wp_users' => [
                [
                'ID'                  => 115,
                'user_login'          => 'testuser335',
                'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                'user_nicename'       => 'testuser335',
                'user_email'          => '<EMAIL>',
                'user_url'            => '',
                'user_registered'     => '2001-01-01 00:00:00',
                'user_activation_key' => '',
                'user_status'         => 1,
                'user_alias'          => null,
                'display_name'        => 'testuser335',
                'description'         => null,
                'photo'               => null,
                'last_login'          => null,
                'localization'        => null,
                'feature'             => null,
                'status'              => null,
                'store'               => 1003,
                'type'                => 'store',
                'commission_rate'     => 0.00,
                'employee_id'         => null,
                'group'               => 2,// must be allowed by $configs['retailer.store_appointment_hours.group_permissions']
                'selling_mode'        => 1,
                'isPhoto'             => 0,
                'locked_at'           => null,
                'locale'              => null,
                'creation_source'     => 'invite',
                'shop_feed'           => 0
                ],
                ['ID'                 => 111,
                'user_login'          => 'testuser111',
                'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                'user_nicename'       => 'testuser11',
                'user_email'          => '<EMAIL>',
                'user_url'            => '',
                'user_registered'     => '2001-01-01 00:00:00',
                'user_activation_key' => '',
                'user_status'         => 1,
                'user_alias'          => null,
                'display_name'        => 'testuser11',
                'description'         => null,
                'photo'               => null,
                'last_login'          => null,
                'localization'        => null,
                'feature'             => null,
                'status'              => null,
                'store'               => 1003,
                'type'                => 'store',
                'commission_rate'     => 0.00,
                'employee_id'         => null,
                'group'               => 1,//not enough access level
                'selling_mode'        => 1,
                'isPhoto'             => 0,
                'locale'              => null,
                'creation_source'     => 'invite',
                'shop_feed'           => 0
                ],
                [
                'ID'                  => 280,
                'user_login'          => 'testuser280',
                'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                'user_nicename'       => 'testuser280',
                'user_email'          => '<EMAIL>',
                'user_url'            => '',
                'user_registered'     => '2001-01-01 00:00:00',
                'user_activation_key' => '',
                'user_status'         => 1,
                'user_alias'          => null,
                'display_name'        => 'testuser280',
                'description'         => null,
                'photo'               => null,
                'last_login'          => null,
                'localization'        => null,
                'feature'             => null,
                'status'              => null,
                'store'               => 1000,
                'type'                => 'store',
                'commission_rate'     => 0.00,
                'employee_id'         => null,
                'group'               => 1,// same as DB for wp_users.type == store
                'selling_mode'        => 1,
                'isPhoto'             => 0,
                'locked_at'           => null,
                'locale'              => null,
                'creation_source'     => 'invite',
                'shop_feed'           => 0
                ]
            ]
        ]);

        Fixtures::add('store', [
            'sf_store' => [
                [
                    'store_id'          => '13',
                    'name'              => 'Vancouver',
                    'latitude'          => 49.2496,
                    'longitude'         => -123.1193,
                    'country'           => 'CA',
                    'region'            => 'BC',
                    'city'              => 'Vancouver',
                    'address'           => '4985 Sherbrooke St',
                    'postal'            => 'V5W 3M1',
                    'phone'             => '(*************',
                    'timezone'          => 'America/Vancouver',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'Vancouver',
                    'sf_identifier'     => 'test-vancouver'
                ],
                [
                    'store_id'          => '1000',
                    'store_user_id'     => 280,
                    'name'              => 'New York',
                    'latitude'          => 49.2496,
                    'longitude'         => -123.1193,
                    'country'           => 'CA',
                    'region'            => 'BC',
                    'city'              => 'New York',
                    'address'           => '4985 Sherbrooke St',
                    'postal'            => 'V5W 3M1',
                    'phone'             => '(*************',
                    'timezone'          => 'America/New_York',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'NewYork',
                    'sf_identifier'     => 'test-newyork'
                ]
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 13,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'ja_JP',
                    'store_id'   => 1000,
                    'is_default' => 1
                ]
            ],
        ]);

        Fixtures::add('events', [
            'sf_events' => [
                [
                    'ID'           => 1,
                    'type'         => 7,
                    'date'         => '2023-04-19 13:57:40',
                    'source'       => '',
                    'uniq_id'      => 'SFID643ff353afe681.48826197',
                    'user_id'      => 0,
                    'customer_id'  => 2,
                    'attributes'   => '1',
                    'satisfied'    => 0,
                    'event_id'     => 0,
                    'acknowledged' => 0,
                    'store_id'     => 0
                ],
            ],
            'sf_appointments' => [
                [
                    'ID'             => 1,
                    'customer_id'    => 2,
                    'user_id'        => 0,
                    'event_type'     => 'In-Store',
                    'event_duration' => 60,
                    'date'           => '2024-04-18 17:00:00',
                    'status'         => '',
                    'location'       => '',
                    'notes'          => 'This is a test!!!!!!!',
                    'uniq_id'        => 'SFID643ff353afe681.48826197',
                    'timezone'       => 'America/Toronto',
                    'creation'       => '2023-04-19 13:57:39',
                    'enddate'        => '2024-04-20 18:00:00',
                    'store_id'       => 13,
                    'category_id'    => 'Beauty',
                    'unattached_id'  => 'ik6ck0nlg',
                    'source_url'     => 'https://holt-qa04.salesfloor.net/en_US/example',
                    'source_title'   => 'Shop with  Example Example  - Holt Renfrew',
                    'locale'         => 'fr_CA',
                    'channel'        => 'email',
                    'version'        => 0,
                    'updated_at'     => '2023-04-19 13:57:39',
                ],
            ],
        ]);
    }

    public function testAppointmentCreationOverwriteField()
    {
        Fixtures::add('testAppointmentCreationOverwriteField', [
            'sf_customer' => [
                [
                    'user_id'              => 115,
                    'email'                => '<EMAIL>',
                    'name'                 => 'test user',
                    'phone'                => '+5145610765',
                    'localization'         => NULL,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'sms_marketing_subscription_flag'     => 2,
                    'retailer_customer_id' => null,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'user',
                    'origin'               => 'rep-appointment',
                    'created'              => gmdate("Y-m-d H:i:s"),
                ],
            ]
        ]);
    }

    public function testAppointmentCustomerPhoneNew()
    {
        Fixtures::add('testAppointmentCustomerPhoneNew', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ]
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateOnlyPhone()
    {
        Fixtures::add('testAppointmentCustomerPhoneUpdateOnlyPhone', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'user_id' => 115,
                    'email' => null,
                    'phone' => '+15145555555',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ]
            ],
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateWithEmail()
    {
        Fixtures::add('testAppointmentCustomerPhoneUpdateWithEmail', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => '+15145555555',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ]
            ],
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateMultiple()
    {
        Fixtures::add('testAppointmentCustomerPhoneUpdateMultiple', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => '+15145555555',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                    'last_modified' =>  Carbon::now('UTC')->addDays(1)->toDateTimeString(),
                ],
                [
                    'user_id' => 115,
                    'email' => '',
                    'phone' => '+15145555555',
                    'first_name' => 'Mike',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ],
            ],
        ]);
    }

    public function testAppointmentCustomerPhoneUpdateAlternate()
    {
        Fixtures::add('testAppointmentCustomerPhoneUpdateAlternate', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'ID' => 100,
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => '+15145555556',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                    'last_modified' =>  Carbon::now('UTC')->addDays(1)->toDateTimeString(),
                ],
            ],
            'sf_customer_meta' => [
                [
                    'customer_id' => 100,
                    'type' => 'phone',
                    'value' => "+15145555555",
                    'label' => 'home',
                    'position' => 0,
                ]
            ]
        ]);
    }

    public function testAppointmentCustomerEmailNew()
    {
        Fixtures::add('testAppointmentCustomerEmailNew', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ]
        ]);
    }

    public function testAppointmentCustomerEmailUpdateOnlyEmail()
    {
        Fixtures::add('testAppointmentCustomerEmailUpdateOnlyEmail', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => null,
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ]
            ],
        ]);
    }

    public function testAppointmentCustomerEmailUpdateWithPhone()
    {
        Fixtures::add('testAppointmentCustomerEmailUpdateWithPhone', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => '+15148825555',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ]
            ],
        ]);
    }

    public function testAppointmentCustomerEmailUpdateMultiple()
    {
        Fixtures::add('testAppointmentCustomerEmailUpdateMultiple', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => '+15148825555',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ],
                [
                    'user_id' => 1,
                    'email' => '<EMAIL>',
                    'phone' => '+15148825555',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                ]
            ],
        ]);
    }

    public function testAppointmentCustomerEmailUpdateAlternate()
    {
        Fixtures::add('testAppointmentCustomerEmailUpdateAlternate', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 115,
                    'phone_number' => '+15148825555',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_customer' => [
                [
                    'ID' => 100,
                    'user_id' => 115,
                    'email' => '<EMAIL>',
                    'phone' => '+15145555556',
                    'first_name' => 'John',
                    'last_name' => 'Nothingburger',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                    'last_modified' => Carbon::now('UTC')->addDays(1)->toDateTimeString(),
                ],
            ],
            'sf_customer_meta' => [
                [
                    'customer_id' => 100,
                    'type' => 'email',
                    'value' => "<EMAIL>",
                    'label' => 'home',
                    'position' => 0,
                ]
            ]
        ]);
    }

    public function testAppointmentCustomerFromNewLead()
    {
        Fixtures::add('testAppointmentCustomerFromNewLead', [
            'sf_customer' => [
                [
                    'ID' => 400,
                    'user_id' => 0,
                    'email' => '<EMAIL>',
                    'phone' => '+15145555556',
                    'first_name' => 'Joseph',
                    'last_name' => 'Champion',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                    'last_modified' =>  Carbon::now('UTC')->addDays(1)->toDateTimeString(),
                ],
                [
                    'ID' => 500,
                    'user_id' => 0,
                    'email' => '<EMAIL>',
                    'phone' => '+15145005555',
                    'first_name' => 'Mary',
                    'last_name' => 'Anderson',
                    'subcribtion_flag' => 1,
                    'type' => 'personal',
                    'locale' => 'en-US',
                    'last_modified' =>  Carbon::now('UTC')->addDays(1)->toDateTimeString(),
                ],

            ],
        ]);
    }

    public function testBookedTimeslots()
    {
        $booked = $this->convertNyTimeToUtc(Carbon::now()->addDays(1)->toDateString() . ' 10:00:00');
        Fixtures::add('bookedTimeslots', [
            'sf_appointments' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF3456',
                    'date'        => $booked,
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'status'      => '',
                    'timezone' => 'America/New_York',
                    'event_duration' => '60',
                ],
                [
                    'ID' => 2,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF3456',
                    'date'        => $booked,
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'status'      => '',
                    'timezone' => 'America/New_York',
                    'event_duration' => '60',
                ],
            ],
        ]);
    }

      /**
     * Helper function convert local to UTC
     *
     * @param string $date
     * @return string
     */
    private function convertNyTimeToUtc(string $date)
    {
        $nyTimezone = new \DateTimeZone('America/New_York');
        $time = new \DateTime($date, $nyTimezone);

        $utcTimezone = new \DateTimeZone('UTC');
        $time->setTimeZone($utcTimezone);

        return $time->format('Y-m-d H:i:s');
    }
}
