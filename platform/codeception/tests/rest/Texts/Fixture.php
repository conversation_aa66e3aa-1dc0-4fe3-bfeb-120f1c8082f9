<?php

declare(strict_types=1);

namespace SF\rest\Texts;

use Codeception\Util\Fixtures;

class Fixture
{
    public function textThreadTestGET()
    {
        Fixtures::add('textThreadGeneric1', [
            'sf_text_thread'  => [
                [   'id' => 3,
                    'user_id'               => 1,
                    'user_phone_number'     => '+15141231234', // I need to fake a twilio phone number
                    'customer_id'           => 2,
                    'customer_phone_number' => '+15148467733', // Fake phone from the dump
                    'is_read'               => 1,
                    'is_valid'              => 1,
                    'is_active'             => 1,
                    'is_subscribed'         => 1,
                    'created_at'            => '2017-06-15 11:18:10',
                    'updated_at'            => '2017-06-15 21:18:10',
                ],
                [
                    'id' => 4,
                    'user_id'               => 1,
                    'user_phone_number'     => '+15141231234', // I need to fake a twilio phone number
                    'customer_id'           => null,
                    'customer_phone_number' => '+15148466699', // Fake phone from the dump
                    'is_read'               => 1,
                    'is_valid'              => 1,
                    'is_active'             => 1,
                    'is_subscribed'         => 1,
                    'created_at'            => '2017-06-15 11:18:10',
                    'updated_at'            => '2017-06-15 21:18:10',
                ],
            ],
            'sf_text_message' => [
                [
                    'text_thread_id'      => 3, // Because it's the 3rd one (since 2 are already in the DB)
                    'provider_message_id' => 'thisisafakeid',
                    'direction'           => 'outbound-api',
                    'body'                => 'fake message body',
                    'status'              => 'queued', // not really needed
                    'is_active'           => 1,
                    // More stuff, but not needed for now, since we only need thread info
                ],
                [
                    'text_thread_id'      => 4,
                    'provider_message_id' => 'thisisafakeid2',
                    'direction'           => 'outbound-api',
                    'body'                => 'fake message body',
                    'status'              => 'queued', // not really needed
                    'is_active'           => 1,
                ],
            ]
        ]);

        Fixtures::add('userGroup2SameStoreAsReggie', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ]
            ]
        ]);

        Fixtures::add('userGroup2DifferentStoreAsReggie', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 2003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ]
            ]
        ]);

        Fixtures::add('userGroup3', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1031,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 3,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ]
            ]
        ]);

        Fixtures::add('preCustomers', [
            'sf_pre_customer' => [
                [
                    'name' => 'Mary',
                    'email' => '<EMAIL>',
                    'phone' => '+15148466699', // Linked to thread number 4
                    'user_id' => '0',
                    'customer_id' => null,
                    'origin' => 'widget-email',
                    'source' => 'sf_questions',
                    'source_id' => 6,
                    'locale' => 'en_US',
                ]
            ]
        ]);
    }

    public function textVisualThreadsGET()
    {
        Fixtures::add('testVisualThreads', [
            'sf_text_thread'  => [
                [   'id' => 3,
                    'user_id'               => 1,
                    'user_phone_number'     => '+15141231234', // I need to fake a twilio phone number
                    'customer_id'           => 2,
                    'customer_phone_number' => '+15148467733',
                    'is_read'               => 1,
                    'is_valid'              => 1,
                    'is_active'             => 1,
                    'is_subscribed'         => 1,
                    'created_at'            => '2017-06-15 11:18:10',
                    'updated_at'            => '2017-06-15 21:18:10',
                ],
            ],
            'sf_text_message' => [
                [
                    'text_thread_id'      => 3,
                    'provider_message_id' => 'thisisafakeid',
                    'direction'           => 'outbound-api',
                    'body'                => 'fake message body',
                    'status'              => 'queued', // not really needed
                    'is_active'           => 1,
                ],
            ]
        ]);
    }

    //////////////////////
    ///  TextSendCest
    ///
    ///

    public function testSendByCustomerPhoneNumber()
    {
        Fixtures::add('testSendByCustomerPhoneNumberEmptyThread', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 1,
                    'phone_number' => '+14387941585',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_text_thread' => [
                [
                        'id' => 3,
                        'user_id'               => 1,
                        'user_phone_number'     => '+14387941585',
                        'customer_phone_number' => '+15145555555',
                        'is_read'               => 1,
                        'is_valid'              => 1,
                        'is_active'             => 1,
                        'is_subscribed'         => 1,
                        'created_at'            => '2017-06-15 11:18:10',
                        'updated_at'            => '2017-06-15 21:18:10',
                ]
            ]
        ]);

        Fixtures::add('testSendByCustomerPhoneNumberWithOneCustomerMessage', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 1,
                    'phone_number' => '+14387941585',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_text_thread' => [
                [
                    'id' => 3,
                    'user_id'               => 1,
                    'user_phone_number'     => '+14387941585',
                    'customer_phone_number' => '+15145555555',
                    'is_read'               => 1,
                    'is_valid'              => 1,
                    'is_active'             => 1,
                    'is_subscribed'         => 1,
                    'created_at'            => '2017-06-15 11:18:10',
                    'updated_at'            => '2017-06-15 21:18:10',
                ]
            ],
            'sf_text_message' => [
                [
                    'text_thread_id' => 3,
                    'provider_message_id' => 'fake-unique-132511',
                    'direction' => 'inbound',
                    'body' => 'hi',
                    'status' => 'received',
                    'is_active' => 1,
                ]
            ],
        ]);

        Fixtures::add('testSendByCustomerPhoneNumberWithOneRepMessage', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 1,
                    'phone_number' => '+14387941585',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_text_thread' => [
                [
                    'id' => 3,
                    'user_id'               => 1,
                    'user_phone_number'     => '+14387941585',
                    'customer_phone_number' => '+15145555555',
                    'is_read'               => 1,
                    'is_valid'              => 1,
                    'is_active'             => 1,
                    'is_subscribed'         => 1,
                    'created_at'            => '2017-06-15 11:18:10',
                    'updated_at'            => '2017-06-15 21:18:10',
                ]
            ],
            'sf_text_message' => [
                [
                    'text_thread_id' => 3,
                    'provider_message_id' => 'fake-unique-132511',
                    'direction' => 'outbound-api',
                    'body' => 'hi',
                    'status' => 'queue',
                    'is_active' => 1,
                ]
            ],
        ]);

        Fixtures::add('testSendByCustomerPhoneNumberEmptyThreadAndCustomerId', [
            'sf_user_phone_number' => [
                [
                    'user_id' => 1,
                    'phone_number' => '+14387941585',
                    'created_by_user_id' => 1,
                ]
            ],
            'sf_text_thread' => [
                [
                    'id' => 3,
                    'user_id'               => 1,
                    'customer_id'           => 2,
                    'user_phone_number'     => '+14387941585',
                    'customer_phone_number' => '+15145555555',
                    'is_read'               => 1,
                    'is_valid'              => 1,
                    'is_active'             => 1,
                    'is_subscribed'         => 1,
                    'created_at'            => '2017-06-15 11:18:10',
                    'updated_at'            => '2017-06-15 21:18:10',
                ]
            ]
        ]);
    }
}
