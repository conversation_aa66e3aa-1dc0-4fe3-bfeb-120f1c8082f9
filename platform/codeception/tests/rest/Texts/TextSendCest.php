<?php

declare(strict_types=1);

namespace SF\rest\Texts;

use Codeception\Stub;
use Codeception\Stub\Expected;
use SF\rest\BaseRest;
use SF\RestTester;

/**
 * This is slow because ES is not mocked up properly here atm.
 *
 * @group database_transaction
 */
class TextSendCest extends BaseRest
{
    ///////////////////////////
    /// Phone number
    ///
    /// - customer_id + no thread_id
    /// - customer_id + thread_id
    /// - thread_id (DB: empty thread with customer_id)
    /// - thread_id (DB: empty thread)
    /// - thread_id (DB: on rep message in the thread)
    /// - thread_id (DB: on customer message in the thread)

    public function testSendByCustomerPhoneEmptyThreadWithCustomerIdAndNoThreadId(RestTester $I): void
    {
        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThreadAndCustomerId');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the rep's locale, because there's a customer_id linked to the thread.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'Ceci est un message de Sally',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'recipients' => [
                [
                    "customer_phone_number" => '+15145555555', // This is ignored only when pii is on and customer's phone will overwrite
                    "customer_id" => 2,
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneEmptyThreadWithCustomerId(RestTester $I): void
    {
        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThreadAndCustomerId');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the rep's locale, because there's a customer_id linked to the thread.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'Ceci est un message de Sally',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+15145555555',
                    "customer_id" => 2,
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberEmptyThreadWithCustomerId(RestTester $I): void
    {
        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThreadAndCustomerId');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the rep's locale, because there's a customer_id linked to the thread.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'Ceci est un message de Sally',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+15145555555'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberRepThread(RestTester $I): void
    {
        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberWithOneRepMessage');

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the locale of the store, because this thread has no customer_id.
                    $I->assertStringStartsWith('allo', $body);

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+15145555555'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Rep message + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberCustomerThread(RestTester $I): void
    {
        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberWithOneCustomerMessage');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the locale of the store, because this thread has no customer_id.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'This is a message from Sally at tests (Fake Mall)',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+15145555555'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(7, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberEmptyThread(RestTester $I): void
    {
        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThread');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the locale of the store, because this thread has no customer_id.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'This is a message from Sally at tests (Fake Mall)',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+15145555555'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }


    ///////////////////////////
    /// PII (obfuscated phone number)
    ///
    /// - customer_id + no thread_id
    /// - customer_id + thread_id
    /// - thread_id (DB: empty thread with customer_id)
    /// - thread_id (DB: empty thread)
    /// - thread_id (DB: on rep message in the thread)
    /// - thread_id (DB: on customer message in the thread)

    public function testSendByCustomerPhoneWithPIIEmptyThreadWithCustomerIdAndNoThreadId(RestTester $I): void
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThreadAndCustomerId');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15148467733', $to['customer_phone_number']);

                    // This is the rep's locale, because there's a customer_id linked to the thread.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'Ceci est un message de Sally',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55', // This is ignored and customer's phone will overwrite
                    "customer_id" => 2,
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id, // Since this is a new phone number, new thread is created
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneWithPIIEmptyThreadWithCustomerId(RestTester $I): void
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThreadAndCustomerId');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15148467733', $to['customer_phone_number']);

                    // This is the rep's locale, because there's a customer_id linked to the thread.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'Ceci est un message de Sally',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55',
                    "customer_id" => 2,
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id, // New thread because another phone number was used (because of customer_id)
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberWithPIIEmptyThreadWithCustomerId(RestTester $I): void
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThreadAndCustomerId');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the rep's locale, because there's a customer_id linked to the thread.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'Ceci est un message de Sally',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberWithPIIRepThread(RestTester $I): void
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberWithOneRepMessage');

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the locale of the store, because this thread has no customer_id.
                    $I->assertStringStartsWith('allo', $body);

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Rep message + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberWithPIICustomerThread(RestTester $I): void
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberWithOneCustomerMessage');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the locale of the store, because this thread has no customer_id.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'This is a message from Sally at tests (Fake Mall)',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(7, 'sf_text_message');
    }

    public function testSendByCustomerPhoneNumberWithPIIEmptyThread(RestTester $I): void
    {
        $this->app['configs']['retailer.pii.obfuscate.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testSendByCustomerPhoneNumberEmptyThread');

        $count = 0;

        $stub = Stub::construct(
            'Salesfloor\Services\Messaging\Text\Channel',
            [$this->app],
            [
                'postToProvider' => Expected::atLeastOnce(function ($userPhoneNumber, $to, $body, $attachments) use ($I, &$count) {
                    $I->assertEquals('+14387941585', $userPhoneNumber);
                    $I->assertEquals('+15145555555', $to['customer_phone_number']);

                    // This is the locale of the store, because this thread has no customer_id.
                    $I->assertStringStartsWith(match ($count) {
                        0 => 'This is a message from Sally at tests (Fake Mall)',
                        1 => 'allo',
                    }, $body);

                    $count++;

                    return [
                        'userPhoneNumber' => $userPhoneNumber,
                        'customerPhoneNumber' => $to['customer_phone_number'],
                        'direction' => 'outbound-api',
                        'status' => 'queued',
                        'body' => $body,
                        'providerMessageId' => "fake$count", // Because it's unique
                    ];
                }),
            ],
        );

        $this->overwriteAppService('service.messaging.text', $stub);

        $response = $I->doDirectPost($I, '/messaging/private/text/send', [
            'body' => 'allo',
            'locale' => 'en_US',
            'thread_id' => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ]);

        $this->debug($response);

        $I->seeInDatabase('sf_text_message', [
            'body' => 'allo',
            'direction' => 'outbound-api',
            'text_thread_id' => $response->text_thread_id,
        ]);

        // Opt-OUT + new POST
        $I->assertNumberRowsInTable(6, 'sf_text_message');
    }

    public function testSendToCustomerKeywordBlockBody(RestTester $I): void
    {
        $text = [
            'body'       => 'should blocked keyword CanNabis',
            'locale'     => 'en_US',
            'thread_id'  => 3,
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ];

        $response = $I->doDirectPost($I, '/messaging/private/text/send', $text);

        $I->seeInDatabase('sf_moderations', [
            'input'                => $text['body'],
            'is_moderator_flagged' => 1,
        ]);
    }

    public function testSendToCustomerKeywordBlockProduct(RestTester $I): void
    {
        $text = [
            'body'       => 'whatever',
            'locale'     => 'en_US',
            'thread_id'  => 3,
            'attachment' => [
                [
                    'type'        => "product",
                    'product_id'  => 1,
                    'name'        => 'should blocked keyword Cannabis',
                    'product_sku' => '9523890100',
                    'link_url'    => 'https://www.example.com',
                    'url'         => "https://res.cloudinary.com/salesfloor-net/image/fetch/s--XbMzcp0o--/f_auto/https://pimg.bucklecontent.com/images/products/81105SXPP2MA/BGR/f/91353852c4908ebe97b59cbbfc5d11f8v3%3Fquality%3D0.8%26width%3D392",
                ]
            ],
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ];

        $response = $I->doDirectPost($I, '/messaging/private/text/send', $text);

        $I->seeInDatabase('sf_moderations', [
            'input'                => $text['attachment'][0]['name'],
            'is_moderator_flagged' => 1,
        ]);
    }

    public function testSendToCustomerKeywordBlockUrl(RestTester $I): void
    {
        $text = [
            'body'       => 'whatever',
            'locale'     => 'en_US',
            'thread_id'  => 3,
            'attachment' => [
                [
                    'type'        => "product",
                    'product_id'  => 1,
                    'name'        => 'whatever',
                    'product_sku' => '9523890100',
                    'link_url'    => 'https://www.example.com',
                    'url'         => "https://res.cloudinary.com/salesfloor-net/cannabis/fetch/s--XbMzcp0o--/f_auto/https://pimg.bucklecontent.com/images/products/81105SXPP2MA/BGR/f/91353852c4908ebe97b59cbbfc5d11f8v3%3Fquality%3D0.8%26width%3D392",
                ]
            ],
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ];

        $response = $I->doDirectPost($I, '/messaging/private/text/send', $text);

        $I->seeInDatabase('sf_moderations', [
            'input'                => $text['attachment'][0]['url'],
            'is_moderator_flagged' => 1,
        ]);
    }

    public function testSendToCustomerKeywordBlockLinkUrl(RestTester $I): void
    {
        $text = [
            'body'       => 'whatever',
            'locale'     => 'en_US',
            'thread_id'  => 3,
            'attachment' => [
                [
                    'type'        => "product",
                    'product_id'  => 1,
                    'name'        => 'whatever',
                    'product_sku' => '9523890100',
                    'link_url'    => 'https://www.example.com/cannabis/',
                    'url'         => "https://res.cloudinary.com/salesfloor-net/whatever/fetch/s--XbMzcp0o--/f_auto/https://pimg.bucklecontent.com/images/products/81105SXPP2MA/BGR/f/91353852c4908ebe97b59cbbfc5d11f8v3%3Fquality%3D0.8%26width%3D392",
                ]
            ],
            'recipients' => [
                [
                    "customer_phone_number" => '+1514*****55'
                ]
            ]
        ];

        $response = $I->doDirectPost($I, '/messaging/private/text/send', $text);

        $I->seeInDatabase('sf_moderations', [
            'input'                => $text['attachment'][0]['link_url'],
            'is_moderator_flagged' => 1,
        ]);
    }
}
