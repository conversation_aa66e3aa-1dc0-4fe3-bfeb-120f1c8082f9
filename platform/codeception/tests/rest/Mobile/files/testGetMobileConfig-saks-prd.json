{"saks-prd": {"RetailerId": "tests-dev", "Stack": "dev", "BaseUrl": "https://tests.dev.salesfloor.net", "SfApiUrl": "https://tests.api.dev.salesfloor.net/", "SfWpApiUrl": "https://tests.dev.salesfloor.net/api/", "SfMposProxyUrl": "https://mpos-proxy.salesfloor.net", "NameTemplate": {"MobileDashboard": "{Fi.} {Ln}", "MobileContact": "{Fn} {Ln}", "ChatRepName": "{Fn} {Li.}", "GroupTaskRepName": "{Fn} {Li.}", "ChatCustomerName": "{Fn} {Ln}"}, "TaskAutoDismissEnabled": false, "TaskAutoDismissSettings": [{"happen_after_event": "reminder", "days_after_event": 7, "automated_types": ["retailer_customer_soon_to_lapse", "retailer_customer_soon_to_lapse_filtered", "retailer_customer_soon_to_lapse_secondary_filtered", "retailer_customer_stats_registry_event", "retailer_customer_stats_registry_followup", "transactions_distribution_by_stores", "cancelled_transaction_follow_up", "new_retailer_transaction", "new_retailer_transaction_filtered", "new_retailer_transaction_filtered_multiple", "new_rep_transaction", "rep_transaction_imported", "retailer_transaction_imported", "retailer_transaction_employee_assigned", "retailer_transaction_employee_assigned_multiple"]}], "OnboardingLogoPath": "/img/retailers/tests/tests-logo-onboarding.png", "Specificities": "{    \"pages\": {        \"enter-token\": {            \"token\": \"true\"        },        \"create-user\": {            \"username\": \"true\",            \"password\": \"true\",            \"confirm\": \"true\"        },        \"enter-email\": {            \"email\": \"email\",            \"phone\": \"true\",            \"alias\": \"((firstname || '') + (lastname || '')[0]).replace(\\/\\\\s\\/g, '').toLowerCase()\",            \"storefront\": \"BaseUrl + '\\/' + alias\"        },        \"personal-info\": {            \"firstname\": \"firstname || ''\",            \"lastname\": \"lastname || ''\"        },        \"pick-store\": {            \"store\": \"store\",            \"introduction\": \"\\\"Hello! I am your dedicated Sales Associate. I can provide personalized services, answer your questions and meet with you in store or online to help you find what you desire.\\\"\"        },        \"out-of-store\": \"true\",        \"take-picture\": \"true\",        \"choose-specialties\": \"true\",        \"import-contact\": \"true\",        \"connect-social\": {            \"twitter\": \"true\"        },        \"congrats\": \"false\"    },    \"overrides\": [        {            \"rules\": \"selling_mode == 0\",            \"override\": {                \"enter-email\": \"false\",                \"out-of-store\": \"false\",                \"take-picture\": \"true\",                \"choose-specialties\": \"false\",                \"import-contact\": \"false\",                \"connect-social\": \"false\",                \"details\": \"false\"            }        },        {            \"rules\": \"group > 3 && selling_mode == 1\",            \"override\": {                \"enter-email\": {                    \"alias\": \"false\"                },                \"pick-store\": \"false\",                \"take-picture\": \"false\",                \"choose-specialties\": \"false\",                \"import-contact\": \"false\",                \"connect-social\": \"false\"            }        },        {            \"rules\": \"group > 3 && selling_mode == 0\",            \"override\": {                \"enter-email\": {                    \"alias\": \"false\"                },                \"pick-store\": \"false\",                \"take-picture\": \"false\",                \"choose-specialties\": \"false\",                \"import-contact\": \"false\",                \"connect-social\": \"false\",                \"details\": \"false\"            }        }    ]}", "i18nIsEnabled": true, "defaultLocale": "en_US", "globalLocales": ["en_US", "fr_CA"], "RetailerPrefixNumericUsernames": false, "RetailerCanChangeRetailerId": false, "CookieShowPrefix": false, "RetailerShortName": "tests", "StoreHours": [{"open": "9", "close": "22", "is_available": "1"}], "StoreAppointmentHours": [{"open": "9", "close": "22", "is_available": "1"}], "StoreTextHours": [{"open": "9", "close": "22"}], "RetailerMFAEnabled": false, "RetailerPasswordUpdateEnabled": false, "RetailerLogoPath": "https://cdn.salesfloor.net/salesfloor-assets/tests/logo.svg", "MobileAppUpgradeNotificationEnabled": true, "MobileAppUpgradeNotificationTimeout": 86400, "MobileAppUpgradeNotificationMessage": {"en_US": "<p>IMPORTANT: This Android version of Salesfloor is no longer supported and should be replaced with the latest Salesfloor app.</p><p>Please download it here: <a href=\"https://salesfloor.net/android\" onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a></p><p>Thanks.</p>", "fr_CA": "<p>IMPORTANT: Cette version Android de Salesfloor n'est plus supportée and doit être remplacée par la plus récente application Salesfloor.</p><p>Téléchargez-la ici: <a href='https://salesfloor.net/android' onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a> et n'oubliez pas d'effacer l'ancienne version de votre appareil ! Vos informations ne seront pas perdues.</p><p>Merci.</p>", "ja_JP": "<p>重要：このAndroidバージョンのSalesfloorはサポート停止となりました。最新のSalesfloorアプリに更新してください。</p><p>ここからダウンロードしてください： <a href='https://salesfloor.net/android' onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a></p><p>よろしくお願いいたします。</p>"}, "isOauth2Enabled": false, "Oauth2AuthorizationUrl": null, "Oauth2AccessTokenUrl": null, "PermissionsMap": {"create-user": "2"}, "ShareConnectedServicesEnabled": true, "ShareEmailEnabled": true, "ShareInstagramEnabled": false, "ShareFacebookEnabled": false, "SharePinterestEnabled": false, "AppDownloadUrls": {"ios": "https://build.phonegap.com/apps/1221243/download/ios", "android": "https://build.phonegap.com/apps/1221243/download/android"}, "AppVersionUrls": null, "RetailerLongName": "tests", "RetailerPrettyName": "tests", "RetailerHomepageUrl": "http://www.salesfloor.net", "RetailerCurrentBrand": null, "RetailerBrandName": "tests", "RetailerAlternateBrand": null, "RetailerStorepageMode": "rep", "RetailerGoogleAnalyticsUID": "UA-92296294-4", "RetailerGoogleAnalyticsWebBOUID": "UA-92296294-11", "RetailerCorpEmailRequired": false, "PasswordPolicyType": "legacy", "PasswordPolicyMinimumStrength": 3, "RetailerDefaultCountry": "US", "RetailerCountriesApplicationPreferred": ["CA", "US"], "RetailerCountriesApplicationAvailable": ["AU", "AT", "BE", "BR", "CA", "CL", "HR", "CZ", "DK", "EE", "FI", "FR", "GT", "DE", "HU", "HK", "IE", "IL", "IT", "LV", "LT", "MY", "MX", "NL", "NO", "PH", "PL", "PT", "PR", "SG", "ZA", "KR", "ES", "SE", "CH", "TW", "GB", "US", "JP", "IN"], "ProductApiUrl": "https://tests.api.dev.salesfloor.net/products/", "RetailerPricePrecision": 2, "RetailerPricePrecisionHideEmptyDecimals": false, "RetailerShowProductsBrandName": true, "AlgoliaIndexKey": "tests_dev", "AlgoliaAppId": "WNMJ4Q8002", "AlgoliaApiKey": "********************************", "FirebaseUrl": "https://amber-heat-9378.firebaseio.com/", "FirebaseToken": "uX6phAd8lwarYsQDDq5z6loj1UK4bPtgQBkopOS4", "CANSUrl": "https://tests.api.dev.salesfloor.net/microservice/cans", "CANSApiVersion": "v1.1", "CloudinaryApiKey": "733331198774847", "CloudinaryCloudName": "salesfloor-net", "S3AccessKeyId": "********************", "S3SecretAccessKey": "NC+TV6AjQOimdDNhyhcgwDv9eYC2teDuvpUb6ERu", "S3Region": "us-east-1", "ChatQuickResponses": [{"short": {"en_US": "Give me a moment", "en_IE": "Give me a moment", "en_IN": "Give me a moment", "fr_CA": "Un instant", "ja_JP": "少しお時間をください"}, "full": {"en_US": "Please give me a moment and I will look into this for you.", "en_IE": "Please give me a moment and I will look into this for you.", "en_IN": "Please give me a moment and I will look into this for you.", "fr_CA": "S'il vous plaît, laissez-moi un instant et je vais m'en occuper pour vous.", "ja_JP": "少しお時間をください、調査させていただきます。"}}, {"short": {"en_US": "Thank you and goodbye", "en_IE": "Thank you and goodbye", "en_IN": "Thank you and goodbye", "fr_CA": "Merci et au revoir", "ja_JP": "ありがとうございました。さようなら"}, "full": {"en_US": "Thank you for your request. It will be my pleasure to serve you again. Have a great day.", "en_IE": "Thank you for your request. It will be my pleasure to serve you again. Have a great day.", "en_IN": "Thank you for your request. It will be my pleasure to serve you again. Have a great day.", "fr_CA": "<PERSON><PERSON><PERSON> de votre demande. Il me fera plaisir de vous servir à nouveau. Passez une bonne journée.", "ja_JP": "リクエストありがとうございます。再び、お役に立てることを嬉しく思います。良い一日をお過ごしください。"}}, {"short": {"en_US": "Out of stock", "en_IE": "Out of stock", "en_IN": "Out of stock", "fr_CA": "Rupture de stock", "ja_JP": "在庫切れ"}, "full": {"en_US": "Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href=\"retailer_link\" target=\"_blank\">here</a>", "en_IE": "Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href=\"retailer_link\" target=\"_blank\">here</a>", "en_IN": "Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href=\"retailer_link\" target=\"_blank\">here</a>", "fr_CA": "Malheureusement, nous sommes en rupture de stock en magasin, mais vous pouvez l'acheter en ligne en cliquant <a href=\"retailer_link\" target=\"_blank\">ici</a>", "ja_JP": "残念ながら、現在この製品の在庫はありませんが、<a href=\"retailer_link\" target=\"_blank\">こちら</a>をクリックして、オンラインでご購入になれます"}}, {"short": {"en_US": "My Storefront Link", "en_IE": "My Storefront Link", "en_IN": "My Storefront Link", "fr_CA": "Lien de ma vitrine web"}, "full": "storefront_link"}, {"short": {"en_US": "Subscribe Link", "en_IE": "Subscribe Link", "en_IN": "Subscribe Link", "fr_CA": "Lien d'abonnement"}, "full": "subscribe_link"}, {"short": {"en_US": "Appointment Link", "en_IE": "Appointment Link", "en_IN": "Appointment Link", "fr_CA": "Lien de rendez-vous"}, "full": "appointment_link"}], "SMSQuickChatResponses": [{"short": {"en_US": "Insert Storefront Link", "en_IE": "Insert Storefront Link", "en_IN": "Insert Storefront Link", "fr_CA": "Insérer le lien du magasin", "ja_JP": "ストアリンクを挿入"}, "full": {"en_US": "Here's the link to my Storefront: {store_link}", "en_IE": "Here's the link to my Storefront: {store_link}", "en_IN": "Here's the link to my Storefront: {store_link}", "fr_CA": "Voici le lien vers ma vitrine: {store_link}", "ja_JP": "これが私のストアフロントへのリンクです：{store_link}"}}, {"short": {"en_US": "Insert Subscribe Link", "en_IE": "Insert Subscribe Link", "en_IN": "Insert Subscribe Link", "fr_CA": "Insérer le lien d'abonnement", "ja_JP": "購読リンクを挿入"}, "full": {"en_US": "Here's the link to subscribe: {subscribe_link}", "en_IE": "Here's the link to subscribe: {subscribe_link}", "en_IN": "Here's the link to subscribe: {subscribe_link}", "fr_CA": "Voici le lien pour vous inscrire: {subscribe_link}", "ja_JP": "購読するためのリンクは次のとおりです。: {subscribe_link}"}}, {"short": {"en_US": "Appointment Link", "en_IE": "Appointment Link", "en_IN": "Appointment Link", "fr_CA": "Lien de rendez-vous"}, "full": {"en_US": "Here's the link to book an appointment: {appointment_link}", "en_IE": "Here's the link to book an appointment: {appointment_link}", "en_IN": "Here's the link to book an appointment: {appointment_link}", "fr_CA": "Voici le lien pour prendre rendez-vous: {appointment_link}", "ja_JP": "予約するためのリンクはこちら: {appointment_link}"}}], "ChatExtraQuickResponses": [{"short": "Contact Customer Service", "full": "Our Customer Service team can help you with this request. They are available via phone at xx.xxx.xxxx.xxxx, thanks."}], "ChatTransferToCs": false, "ChatTransferToCsTextRequired": false, "Clienteling": true, "RetailerHasLimitedVisibility": false, "ClientelingBlackout": false, "ClientelingBlackoutPeriod": 604800, "isUserStoresEnabled": false, "TextMessaging": true, "RetailerHasTextMessageChannel": false, "RetailerCanSendSmsToMultipleRecipients": false, "RetailerCanSendEmailToMultipleRecipients": true, "RetailerMaximumSmsRecipients": 40, "RetailerHasCustomerTags": true, "CustomerTagsAreReadOnly": false, "RetailerHasCustomerActivityFeed": true, "ServiceChatLabel": "Live Chat", "ServiceAppointmentLabel": "Appointment Request", "ServicePersonalShopperLabel": "Personal Shopper", "ServiceEmailMeLabel": "Ask & Answer", "ServiceEmailMeReportsLabel": "Ask & Answer", "ServiceChatRequestLabel": "Live Chat Request", "ServiceAppointmentRequestLabel": "Appointment Request", "ServicePersonalShopperRequestLabel": "Personal Shopper Request", "ServiceEmailMeRequestLabel": "Ask & Answer Request", "ServiceEmailMeReportsRequestLabel": "Ask & Answer Request", "ServiceEmailMeRequestChatHandoffLabel": "Email Requests (Missed Live Chat)", "SubscriptionEmailCheckboxLabel": "Yes! Sign me up to receive :retailer emails and :retailer_specialist communication.", "SubscriptionSMSCheckboxLabel": "Yes, I want to receive SMS updates from an Associate. Messages and data rates may apply. You can reply STOP at any time to unsubscribe. You can read our Privacy Policy here", "OutOfOfficeMessage": "Thank you for contacting me! I am currently unavailable and can't respond to your message.\\n I will be away from {startDate} to {endDate}. I'll get back to you once I return.", "RetailerServicesAppointmentManagementIsEnabled": false, "RetailerServicesAppointmentManagementAllCustomerIsEnabled": false, "RetailerHasServicesAppointmentSaveToDevice": false, "RetailerServicesAppointmentReassignmentIsEnabled": false, "RetailerServicesAppointmentTypes": [{"type": "store", "is_enabled": true, "is_default": true, "position": 0, "use_alternate_label": false}, {"type": "phone", "is_enabled": true, "is_default": false, "position": 1, "use_alternate_label": false}, {"type": "chat", "is_enabled": true, "is_default": false, "position": 2, "use_alternate_label": false}], "RetailerCanEditEvents": false, "algoliaSearchableAttributes": ["product_id", "category_id", "category_id_en_US", "category_id_fr_CA", "category", "category_en_US", "category_fr_CA", "brand", "retailer_sku", "name", "name_en_US", "name_fr_CA", "description", "description_en_US", "description_fr_CA"], "RetailerHasChat": true, "RetailerHasDeals": false, "RetailerHasNewArrivals": true, "RetailerHasPersonalShopper": true, "RetailerHasSpecialties": false, "StorefrontMaxProductCounts": {"deals": 4, "myLooks": 4, "topPicks": 8, "newArrivals": 4, "recommendations": 8}, "StorefrontMinProductCounts": {"recommendations": 2}, "StorefrontTrendingRecommendationsMode": true, "RetailerHasChatAppointment": true, "RetailerHasAppointments": true, "RetailerHasStorefront": true, "RetailerHasAppointmentRequests": true, "ContactsBarcodeScanner": false, "ProductBarcodeScanner": true, "RetailerHasLookbooks": true, "RetailerCanBrowseLibrary": false, "RetailerCanShareFromBrowseLibrary": false, "RetailerHasTasks": true, "RetailerHasMultipleEmailTemplates": false, "RetailerEmailTemplatesLayouts": "", "RetailerCanImportContacts": false, "RetailerContactsConsentRequired": false, "RetailerCanChangeCommunicationConsent": true, "ProductsHasModal": false, "RetailerHasInventoryLookup": false, "RetailerHasPhoneCallEnabled": true, "RetailerCanAddCustomerToContacts": false, "RetailerHasAssetManagement": true, "RetailerHasFeedValidation": true, "RetailerHasAssociateRelationships": true, "RetailerHasProductsFeed": true, "RetailerCanAddContacts": true, "RetailerCanShareAnUpdate": true, "RetailerHasCorporateTasks": true, "RetailerHasAutomatedTasks": true, "RetailerBeginWeek": 0, "RetailerChatMode": "all", "RetailerChatDelay": {"queue": 120000, "broadcast": 60000, "dynamic": 60000}, "RetailerChatRoutingMode": "legacy", "RetailerHasExtendedAttributes": true, "MenuLogoPath": "/img/retailers/tests/tests_p_menu.png", "CameraMaxHeight": 1500, "CameraMaxWidth": 1500, "CameraQuality": 100, "CameraMaxPhotos": 5, "BrandNeedsPadding": "", "EmailMeLabel": "Email Me Request", "AvailableLabelOptions": {"email": ["Home", "Work", "Other"], "phone": ["Home", "Work", "Mobile", "Other"]}, "LoaderDelay": 1500, "LoaderMessageInterval": 4000, "FirebaseMaxReconnectionTime": 500, "FirebaseShowDisconnectMessage": false, "isShopFeedEnabled": false, "isMobileCheckoutEnabled": true, "ProductsExpandedVariantsEnabled": false, "UsePriorityVariants": false, "RetailerStartDate": "2016-03-01 00:00:00", "CanRepViewAllAppointments": false, "AssetTargetChannelLabels": [{"identifier": "all", "label": "All"}, {"identifier": "share_email", "label": "Share Email"}, {"identifier": "social_post", "label": "Social Post"}, {"identifier": "1_to_many_text", "label": "1-to-Many Text"}, {"identifier": "email_text_chat", "label": "Email/Text/Chat"}, {"identifier": "native_apps", "label": "Native Apps"}, {"identifier": "event_image", "label": "Event Image"}], "isVideoChatEnabled": false, "isVirtualVideoChatEnabled": false, "isMfaEnabled": false, "isOutfitsEnabled": true, "outfitsSectionLabel": "My Closet", "outfitsTransactionsPerPage": 20, "retailerChatFindNearbyStores": false, "StoreAppointmentHoursIsEnabled": true, "StoreAppointmentHoursGroupPermissions": [2], "StoreAppointmentHoursFirstDayOfWeek": 0, "GroupedProductsIsEnabled": false, "GroupedProductsURL": "url_to_be_replaced", "GroupedProductsMaxProducts": 10, "GroupedProductsMinProducts": 2, "GroupedProductsSendStyledLinkLabel": "Send Styled Link", "GroupedProductsKpiStyledLinkLabel": "Styled Link", "GroupTasksIsEnabled": false, "GroupTasksLabel": "Group Tasks", "AdvancedSearchTransactionMaxAmount": 20000, "ApiCacheExpiration": 180000, "TaskQueryCutOffDaysBack": 30, "PiiObfuscationIsEnabled": false, "ContextualWidgetEventsRecordingEnabled": true}}