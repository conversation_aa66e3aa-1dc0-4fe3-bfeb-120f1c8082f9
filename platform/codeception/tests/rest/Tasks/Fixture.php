<?php

namespace SF\rest\Tasks;

use Carbon\Carbon;
use Codeception\Util\Fixtures;

class Fixture
{
    public const NAME_FIXTURE_CUSTOMER_ACTIVE_TASKS = 'customer-active-task';

    public function addTasks()
    {

        Fixtures::add(
            'task-unresolved-1',
            [
                'id' => 1,
                'user_id' => 1,
                'status' => 'unresolved',
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-unresolved-2',
            [
                'id' => 2,
                'user_id' => 1,
                'status' => 'unresolved',
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-unresolved-3',
            [
                'id' => 3,
                'user_id' => 1,
                'status' => 'unresolved',
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-due-today-unresolved',
            [
                'id' => 1,
                'user_id' => 1,
                'status' => 'unresolved',
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            // It is not returned to the user since it's scheduled for tomorrow
            'task-overdue-unresolved-start-tomorrow',
            [
                'id' => 2,
                'user_id' => 1,
                'status' => 'unresolved',
                'reminder_date' => gmdate('Y-m-d H:i:s', strtotime('-2 day')),
                'start_date' => gmdate('Y-m-d H:i:s', strtotime('+1 day')),
                'details' => 'junk', // can't be null
            ],
        );

        Fixtures::add(
            // It is returned to the user since it's scheduled yesterday.
            'task-overdue-unresolved-start-yesterday',
            [
                'id' => 7,
                'user_id' => 1,
                'status' => 'unresolved',
                'reminder_date' => gmdate('Y-m-d H:i:s', strtotime('-2 day')),
                'start_date' => gmdate('Y-m-d H:i:s', strtotime('-1 day')),
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-upcoming-unresolved',
            [
                'id' => 3,
                'user_id' => 1,
                'status' => 'unresolved',
                'reminder_date' => gmdate('Y-m-d H:i:s', strtotime('+1 day')),
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-due-today-resolved',
            [
                'id' => 4,
                'user_id' => 1,
                'status' => 'resolved',
                'reminder_date' => gmdate('Y-m-d H:i:s'),
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-overdue-resolved',
            [
                'id' => 5,
                'user_id' => 1,
                'status' => 'resolved',
                'reminder_date' => gmdate('Y-m-d H:i:s', strtotime('-1 day')),
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add(
            'task-upcoming-resolved',
            [
                'id' => 6,
                'user_id' => 1,
                'status' => 'resolved',
                'reminder_date' => gmdate('Y-m-d H:i:s', strtotime('+1 day')),
                'details' => 'junk', // can't be null
            ]
        );

        Fixtures::add('manager_view_tasks_fixture', [
            'sf_task' => $this->getManagerViewTasks(),
        ]);

        Fixtures::add('rep_view_tasks_fixture', [
            'sf_task' =>      array_merge(
                $this->getManagerViewTasks(),
                [[
                    'id'               => 14,
                    'user_id'          => 1,
                    'customer_id'      => 2,
                    'task_category_id' => 1,
                    'status'           => 'unresolved',
                    'reminder_date'    => date('Y-m-d H:i:s'),
                    'details'          => '',
                ]]
            ),
        ]);

        Fixtures::add('retailer_customer_task_fixture_group', $this->getRetailerCustomerTaskFixtureGroup());

        Fixtures::add('corporate_tasks_fixture', [
            'sf_corporate_tasks' => $this->getCorporateTaskBase()
        ]);

        Fixtures::add('corporate_tasks_fixture_active', [
            'sf_corporate_tasks' => $this->getCorporateTaskActive()
        ]);
        Fixtures::add('corporate_tasks_fixture_upcoming', [
            'sf_corporate_tasks' => $this->getCorporateTaskUpcoming()
        ]);

        Fixtures::add('corporate_tasks_fixture_expired', [
            'sf_corporate_tasks' => $this->getCorporateTaskExpired()
        ]);

        Fixtures::add('corporate_tasks_with_specialty_fixture', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 0,
                ],
            ],
            'sf_corporate_task_specialties' => [
                [
                    'corporate_task_id' => 1,
                    'specialty_id'      => 'JtDN4T9R8pX3ruQMwhjNA6aMaTw',
                    'created_at'        => '2019-05-28 17:03:37',
                ],
                [
                    'corporate_task_id' => 1,
                    'specialty_id'      => '9q8WZbyTorsq7L8CqZIChtlAM6I',
                    'created_at'        => '2019-05-28 17:03:37',
                ],
            ],
        ]);

        Fixtures::add('corporate_tasks_with_multi_lang', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title 1",
                    "subject"           => "test subject 1",
                    "detail"            => "test detail 1",
                    "body"              => "test body 1",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 0,
                ],
                [
                    'id'                => 2,
                    "title"             => "test title 2",
                    "subject"           => "test subject 2",
                    "detail"            => "test detail 2",
                    "body"              => "test body 2",
                    "created_by"        => 2,
                    "updated_by"        => 2,
                    "reminder_time"     => "2019-04-21 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 1,
                ],
            ],
            'sf_corporate_tasks_i18n' => [
                [
                    'locale'                => 'fr_CA',
                    'corporate_task_id'     => 1,
                    "title"             => "titre1",
                    "subject"           => "sujet1",
                    "detail"            => "detailee1",
                    "body"              => "corp1",
                ],
                [
                    'locale'                => 'fr_CA',
                    'corporate_task_id'     => 2,
                    "title"             => "titre2",
                    "subject"           => "sujet2",
                    "detail"            => "detailee2",
                    "body"              => "corp2",
                ],
            ],
            'sf_corporate_task_specialties' => [
                [
                    'corporate_task_id' => 1,
                    'specialty_id'      => 'JtDN4T9R8pX3ruQMwhjNA6aMaTw',
                    'created_at'        => '2019-05-28 17:03:37',
                ],
                [
                    'corporate_task_id' => 1,
                    'specialty_id'      => '9q8WZbyTorsq7L8CqZIChtlAM6I',
                    'created_at'        => '2019-05-28 17:03:37',
                ],
                [
                    'corporate_task_id' => 2,
                    'specialty_id'      => 'JtDN4T9R8pX3ruQMwhjNA6aMaTw',
                    'created_at'        => '2019-05-28 17:03:37',
                ],
                [
                    'corporate_task_id' => 2,
                    'specialty_id'      => '9q8WZbyTorsq7L8CqZIChtlAM6I',
                    'created_at'        => '2019-05-28 17:03:37',
                ],
            ],
        ]);

        Fixtures::add('corporate_tasks_with_multi_lang_i18n_fallback', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 999,
                    "title"             => "test title 999",
                    "subject"           => "test subject 999",
                    "detail"            => "test detail 999",
                    "body"              => "test body 999",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 0,
                ],
            ],
        ]);

        Fixtures::add(self::NAME_FIXTURE_CUSTOMER_ACTIVE_TASKS, ['sf_task' => $this->getListTasks()]);
    }

    private function getCorporateTaskActive()
    {
        $timezone = 'America/Montreal';
        $data = $this->getCorporateTaskBase();

        $data[0]['auto_dismiss_time'] = Carbon::now()->addDays(5)->toDateTimeString();

        $data[1]['start_date'] = $this->getDateByTimezone('+ 1days', $timezone);
        $data[1]['auto_dismiss_time'] = gmdate('Y-m-d H:i:s', strtotime('+ 5days'));
        $data[1]['reminder_time'] = gmdate('Y-m-d H:i:s', strtotime('+ 2days'));
        $data[1]['is_deleted'] = 0;
        return $data;
    }

    private function getCorporateTaskUpcoming()
    {
        $timezone = 'America/Montreal';
        $data = $this->getCorporateTaskBase();
        $data[0]['start_date'] = $this->getDateByTimezone('+ 1days', $timezone);
        $data[0]['auto_dismiss_time'] = gmdate('Y-m-d H:i:s', strtotime('+ 5days'));
        $data[0]['reminder_time'] = gmdate('Y-m-d H:i:s', strtotime('+ 2days'));

        $data[2]['start_date'] = $this->getDateByTimezone('- 5mins', $timezone);
        $data[2]['auto_dismiss_time'] = gmdate('Y-m-d H:i:s', strtotime('+ 5days'));
        $data[2]['reminder_time'] = gmdate('Y-m-d H:i:s', strtotime('+ 2days'));
        return $data;
    }

    private function getCorporateTaskExpired()
    {
        $data = $this->getCorporateTaskBase();

        $data[0]['auto_dismiss_time'] = "2019-05-20 09:00:00";
        return $data;
    }

    private function getCorporateTaskBase()
    {
        return [
            [
                'id'                => 1,
                "title"             => "test title",
                "subject"           => "test subject",
                "detail"            => "test detail",
                "body"              => "test body",
                "created_by"        => 1,
                "updated_by"        => 1,
                "reminder_time"     => "2019-04-20 09:00:00",
                "auto_dismiss_time" => "2025-05-20 09:00:00",
                "start_date"        => "2019-04-18 09:00:00",
                "created_at"        => "2019-04-15 09:00:00",
                "updated_at"        => null,
                "is_deleted"        => 0,
            ],
            [
                'id'                => 2,
                "title"             => "test title 2",
                "subject"           => "test subject 2",
                "detail"            => "test detail 2",
                "body"              => "test body 2",
                "created_by"        => 2,
                "updated_by"        => 2,
                "reminder_time"     => "2019-04-21 09:00:00",
                "auto_dismiss_time" => "2019-05-20 09:00:00",
                "created_at"        => "2019-04-15 09:00:00",
                "updated_at"        => null,
                "is_deleted"        => 1,
            ],
            [
                'id'                => 3,
                "title"             => "test title",
                "subject"           => "test subject",
                "detail"            => "test detail",
                "body"              => "test body",
                "created_by"        => 1,
                "updated_by"        => 1,
                "reminder_time"     => "2019-04-20 09:00:00",
                "auto_dismiss_time" => null,
                "created_at"        => "2019-04-15 09:00:00",
                "updated_at"        => null,
                "is_deleted"        => 0,
            ],
        ];
    }

    private function getManagerViewTasks()
    {
        return [
            [
                'id'               => 1,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s'),
                'details'          => '',
            ],
            [
                'id'            => 2,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'unresolved',
                'reminder_date' => date('Y-m-d H:i:s'),
                'details'          => '',
            ],
            [
                'id'            => 3,
                'user_id'       => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'unresolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => '',
            ],
            [
                'id'            => 4,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'unresolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => '',
            ],
            [
                'id'            => 5,
                'user_id'       => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'unresolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => '',
            ],
            [
                'id'            => 6,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'unresolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'start_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => '',
            ],
            [
                'id'            => 7,
                'user_id'       => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'resolved',
                'reminder_date' => date('Y-m-d H:i:s'),
                'details'          => '',
            ],
            [
                'id'            => 8,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'resolved',
                'reminder_date' => date('Y-m-d H:i:s'),
                'details'          => '',
            ],
            [
                'id'            => 9,
                'user_id'       => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'resolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => '',
            ],
            [
                'id'            => 10,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'resolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => '',
            ],
            [
                'id'            => 11,
                'user_id'       => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'resolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => '',
            ],
            [
                'id'            => 12,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'resolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => '',
            ],
            [
                'id'            => 13,
                'user_id'       => 2,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'        => 'unresolved',
                'reminder_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'start_date' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => '',
            ],
        ];
    }

    /**
     * Return Data Set for test API for Mob app (Contacts list, Single Contact info and My Tasks filter for customer)
     *
     * @return array[]
     */
    private function getListTasks()
    {
        return [
            [
                'id'               => 1,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s'),
                'details'          => 'junk',
            ],
            [
                'id'               => 2,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s'),
                'details'          => 'junk',
            ],
            [
                'id'               => 3,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 4,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 5,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 6,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 7,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 8,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'unresolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 9,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s'),
                'details'          => 'junk',
            ],
            [
                'id'               => 10,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s'),
                'details'          => 'junk',
            ],
            [
                'id'               => 11,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s'),
                'details'          => 'junk',
            ],
            [
                'id'               => 12,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 13,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 14,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 15,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 16,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 17,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'resolved',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('+1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 18,
                'user_id'          => 1,
                'customer_id'      => 1,
                'task_category_id' => 1,
                'status'           => 'dismissed',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 19,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'dismissed',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
            [
                'id'               => 20,
                'user_id'          => 1,
                'customer_id'      => 2,
                'task_category_id' => 1,
                'status'           => 'dismissed',
                'reminder_date'    => date('Y-m-d H:i:s', strtotime('-1 day')),
                'details'          => 'junk',
            ],
        ];
    }

    private function getRetailerCustomerTaskFixtureGroup()
    {
        $after30Day = date('Y-m-d', time() + 3600 * 24 * 30);
        $before14day =  date('Y-m-d', time() - 3600 * 24 * 14);

        return [
            'sf_retailer_customer_stats_datapoints' => [
                [
                    'id' => 1,
                    'datapoint_id' => 'p1d2',
                    'panel_id'     => '1',
                    'customer_id'  => '1000016665430',
                    'label'        => 'Event Date',
                    'type'         => 'date',
                    'value'        => $after30Day,
                    'color'        => '',
                    'position'     => 2,
                    'created_at'   => '2019-03-29 18:01:24',
                ],
                [
                    'id' => 2,
                    'datapoint_id' => 'p1d6',
                    'panel_id'     => '1',
                    'customer_id'  => '1000009868767',
                    'label'        => 'Curr Assigned Store',
                    'type'         => 'number',
                    'value'        => '3651',
                    'color'        => '',
                    'position'     => 6,
                    'created_at'   => '2019-03-29 18:01:24',
                ],
                [
                    'id' => 3,
                    'datapoint_id' => 'p1d3',
                    'panel_id'     => '1',
                    'customer_id'  => '1000009868767',
                    'label'        => 'Registry Create Dt',
                    'type'         => 'date',
                    'value'        => $before14day,
                    'color'        => '',
                    'position'     => 3,
                    'created_at'   => '2019-03-29 18:01:24',
                ],
            ],

            'sf_retailer_customers' => [
                [
                    'id' => 1,
                    'customer_id'   => '1000009868767',
                    'gender'        => null,
                    'first_name'    => 'testFirst',
                    'last_name'     => 'testLast',
                    'email'         => '<EMAIL>',
                    'email_label'   => null,
                    'phone'         => null,
                    'phone_label'   => null,
                    'address_line1' => null,
                    'address_line2' => null,
                    'zipcode'       => null,
                    'postalcode'    => null,
                    'city'          => null,
                    'state'         => null,
                    'country'       => null,
                    'longitude'     => null,
                    'latitude'      => null,
                    'is_subscribed' => 0,
                ],
                [
                    'id' => 2,
                    'customer_id'   => '1000016665430',
                    'gender'        => null,
                    'first_name'    => 'testFirst',
                    'last_name'     => 'testLast',
                    'email'         => '<EMAIL>',
                    'email_label'   => null,
                    'phone'         => null,
                    'phone_label'   => null,
                    'address_line1' => null,
                    'address_line2' => null,
                    'zipcode'       => null,
                    'postalcode'    => null,
                    'city'          => null,
                    'state'         => null,
                    'country'       => null,
                    'longitude'     => null,
                    'latitude'      => null,
                    'is_subscribed' => 0,
                ],
            ],

            'sf_customer' => [
                [
                    'ID'                   => 101,
                    'user_id'              => 335,
                    'email'                => '<EMAIL>',
                    'name'                 => 'testFirst Jiang',
                    'phone'                => '5145610765',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'retailer_customer_id' => 1000016665430,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'Jiang',
                ],
                [
                    'ID'                   => 123,
                    'user_id'              => 335,
                    'email'                => '<EMAIL>',
                    'name'                 => 'testFirst Jiang',
                    'phone'                => '5145610765',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'retailer_customer_id' => 1000009868767,
                    'first_name'           => 'TestFirst',
                    'last_name'            => 'Jiang',
                ],
            ],

            'wp_users' => [
                [
                    'ID'                  => 335,
                    'user_login'          => 'fake-mall',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1031,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],

            'sf_store' => [
                [
                    'store_id'          => 1031,
                    'name'              => 'Fake Mall',
                    'timezone'          => 'America/New_York',
                    'sf_identifier'     => 'store-one',
                    'store_user_id'     => 335,
                    'retailer_store_id' => 'ny1',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                ],
            ],
        ];
    }

    public function customerSidePostingBodyStructure1()
    {

        $body1 = <<<START
{
	"data": {
		"type": "corporate-task",
		"attributes": {
			"title": "title",
			"subject": "subject",
			"detail": "detail",
			"body": "body",
			"created_by": 474,
			"updated_by": 333,
			"reminder_time": "2019-04-20 09:00:00",
			"auto_dismiss_time": "2019-05-20 09:00:00",
			"start_date": "2019-04-19 09:00:00"
		},
		"relationships": {
			"corporate-task-specialty": {
				"data": [{
					"type": "corporate-task-specialty",
					"lid": "1236"
				}, {
					"type": "corporate-task-specialty",
					"lid": "1237"
				}]
			}
		}
	},
	"included": [
		{
			"type": "corporate-task-specialty",
			"lid": "1236",
			"attributes": {
				"specialty_id": "JtDN4T9R8pX3ruQMwhjNA6aMaTw"
			}
		},
		{
			"type": "corporate-task-specialty",
			"lid": "1237",
			"attributes": {
				"specialty_id": "9q8WZbyTorsq7L8CqZIChtlAM6I"
			}
		}
	]
}
START;

        Fixtures::add('corporate-task-with-specialty-body', json_decode($body1, true));
    }

    public function postingAsset()
    {

        $body =  <<<START
{
	"data": {
		"type": "corporate-task",
		"attributes": {
			"title": "title",
			"subject": "subject",
			"detail": "detail",
			"body": "body",
			"created_by": 474,
			"updated_by": 333,
			"reminder_time": "2023-04-20 09:00:00",
			"auto_dismiss_time": "2023-05-20 09:00:00",
			"start_date": "2023-04-19 09:00:00"
		},
		"relationships": {
			"corporate-task-asset": {
				"data": [{
					"type": "corporate-task-asset",
					"lid": "1236"
				}]
			}
		}
	},
	"included": [
		{
			"type": "corporate-task-asset",
			"lid": "1236",
			"attributes": {
                "asset_id": "1"
            }
		}
	]
}
START;

        Fixtures::add('corporate-task-with-asset-body', json_decode($body, true));
    }

    public function corporateTaskWithEmptyTitleBodyStructure()
    {

        $body1 = <<<START
{
	"data": {
		"type": "corporate-task",
		"attributes": {
			"title": "",
			"subject": "subject",
			"detail": "detail",
			"body": "body",
			"created_by": 474,
			"updated_by": 333,
			"reminder_time": "2019-04-20 09:00:00",
			"auto_dismiss_time": "2019-05-20 09:00:00"
		},
		"relationships": {
			"corporate-task-specialty": {
				"data": [{
					"type": "corporate-task-specialty",
					"lid": "1236"
				}, {
					"type": "corporate-task-specialty",
					"lid": "1237"
				}]
			}
		}
	},
	"included": [
		{
			"type": "corporate-task-specialty",
			"lid": "1236",
			"attributes": {
				"specialty_id": "JtDN4T9R8pX3ruQMwhjNA6aMaTw"
			}
		},
		{
			"type": "corporate-task-specialty",
			"lid": "1237",
			"attributes": {
				"specialty_id": "9q8WZbyTorsq7L8CqZIChtlAM6I"
			}
		}
	]
}
START;

        Fixtures::add('corporate-task-with-empty-title', json_decode($body1, true));
    }

    public function corporateTaskWithTitleExceedsMaxCharactersBodyStructure()
    {

        $body1 = <<<START
{
	"data": {
		"type": "corporate-task",
		"attributes": {
			"title": "title ljkljlfsafasfasfasfasfas fsfsafas title ljkljlfsafasfasfasfasfas fsfsafas title ljkljlfsafasfasfasfasfas fsfsafas title title ljkljlfsafasfasfasfasfas fsfsafas title",
			"subject": "subject",
			"detail": "detail",
			"body": "body",
			"created_by": 474,
			"updated_by": 333,
			"reminder_time": "2019-04-20 09:00:00",
			"auto_dismiss_time": "2019-05-20 09:00:00"
		},
		"relationships": {
			"corporate-task-specialty": {
				"data": [{
					"type": "corporate-task-specialty",
					"lid": "1236"
				}, {
					"type": "corporate-task-specialty",
					"lid": "1237"
				}]
			}
		}
	},
	"included": [
		{
			"type": "corporate-task-specialty",
			"lid": "1236",
			"attributes": {
				"specialty_id": "JtDN4T9R8pX3ruQMwhjNA6aMaTw"
			}
		},
		{
			"type": "corporate-task-specialty",
			"lid": "1237",
			"attributes": {
				"specialty_id": "9q8WZbyTorsq7L8CqZIChtlAM6I"
			}
		}
	]
}
START;

        Fixtures::add('corporate-task-with-title-exceeds-max-100-characters', json_decode($body1, true));
    }


    public function insertAsset()
    {
        Fixtures::add('corporate_user_assets', $this->mockupCorporateUserAssets());
    }

    public function insertTaskToObfuscate()
    {
        Fixtures::add('task_to_obfuscate', [
            'sf_task' => [
                [
                    'id'                    => 1,
                    'user_id'               => 1,
                    'customer_id'           => 787,
                    'task_category_id'      => 5,
                    'status'                => 'dismissed',
                    'type'                  => 'automated',
                    'details'               => 'Any',
                    'resolution_note'       => 'System Auto-Dismiss',
                    'resolution_date'       => '2024-03-22 03:17:21',
                    'reminder_date'         => '2024-03-22 03:17:21',
                    'last_reminder_date'    => '2024-03-22 03:17:21',
                    'created_at'            => '2024-01-01 03:17:21',
                    'updated_at'            => '2024-01-01 03:17:21',
                    'automated_type'        => 'corporate',
                    'parent_id'             => 1,
                    'start_date'            => '2024-01-01 03:17:21'
                ],
            ],
            'sf_customer' => [
                [
                    'ID'                   => 787,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'Pauline McCain',
                    'phone'                => '8193148374',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 0,
                    'retailer_customer_id' => 1000016665430,
                    'first_name'           => 'Pauline',
                    'last_name'            => 'McCain',
                ],
            ]
        ]);
    }

    private function mockupCorporateUserAssets(): array
    {
        return [
            'sf_store' => [
                [
                    'store_id'          => 1031,
                    'name'              => 'test store',
                    'timezone'          => 'America/Los_Angeles',
                    'sf_identifier'     => 'store-one',
                    'store_user_id'     => 335,
                    'retailer_store_id' => 'ny1',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                ],
            ],
            'wp_users' => [
                [
                    'ID'                  => 335,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1031,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 4,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
            'wp_posts' => [
                [ // published with start date < now < end + author = 1 (should appear in results)
                    'ID'            => 1,
                    'post_author'   => 1,
                    'post_content'  => '<p>test content1</p>',
                    'post_title'    => 'test asset1',
                    'post_status'   => 'publish',
                    'post_name'     => 'testasset1',
                    'post_type'     => 'sf-library',
                    'post_modified' => '2019-07-09 15:52:42'
                ],

            ],
            'wp_postmeta' => [
                [
                    'post_id'    => 1,
                    'meta_key'   => 'sf_label',
                    'meta_value' => 'testasset1'
                ],
                [
                    'post_id'    => 1,
                    'meta_key'   => 'sf_destination_url',
                    'meta_value' => 'http://example1.ca'
                ],
                [
                    'post_id'    => 1,
                    'meta_key'   => 'sf_start_date',
                    'meta_value' => '2023-04-27T09:00'
                ],
                [
                    'post_id'    => 1,
                    'meta_key'   => 'sf_end_date',
                    'meta_value' => '2023-05-30T09:00'
                ],

            ],
        ];
    }

    public function testManagerTaskFutureStartDate()
    {
        Fixtures::add('testManagerTaskFutureStartDate', [
            'sf_task' => [
                [
                    'id'               => 1,
                    'user_id'          => 1,
                    'customer_id'      => 1,
                    'task_category_id' => 1,
                    'status'           => 'unresolved',
                    'details'          => 'future start date',
                    'reminder_date'    => date('Y-m-d H:i:s'),
                    'start_date'       => Carbon::now('UTC')->addDays(5)->toDateTimeString(),
                ]
            ],
            'wp_users' => [
                [
                    'ID' => 1001,
                    'user_login' => 'reggie1001',
                    'user_pass' => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename' => 'reggie2',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'reggie2',
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'employee_id' => '12345',
                    'group' => 2,
                    'selling_mode' => 1,
                    'isPhoto' => 1,
                    'store' => 1003,
                ]
            ]
        ]);
    }

    public function testManagerTaskPastStartDate()
    {
        Fixtures::add('testManagerTaskPastStartDate', [
            'sf_task' => [
                [
                    'id'               => 1,
                    'user_id'          => 1001,
                    'customer_id'      => 1,
                    'task_category_id' => 1,
                    'status'           => 'unresolved',
                    'details'          => 'past start date',
                    'reminder_date'    => date('Y-m-d H:i:s'),
                    'start_date'       => Carbon::now('UTC')->subDays(5)->toDateTimeString(),
                    'automated_type'   => 'nag_share_update',
                ]
            ],
            'wp_users' => [
                [
                    'ID' => 1001,
                    'user_login' => 'reggie1001',
                    'user_pass' => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename' => 'reggie2',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'reggie2',
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'employee_id' => '12345',
                    'group' => 2,
                    'selling_mode' => 1,
                    'isPhoto' => 1,
                    'store' => 1003,
                ]
            ]
        ]);

        $response = <<<DATA
{
    "meta": {
        "pages": 1,
        "total": 1
    },
    "links": {
        "self": "https://tests.api.dev.salesfloor.net/v2/tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=customer%2Ctask-category&page%5Bnumber%5D=0&page%5Bsize%5D=100",
        "first": "https://tests.api.dev.salesfloor.net/v2/tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=customer%2Ctask-category&page%5Bnumber%5D=0&page%5Bsize%5D=100",
        "prev": null,
        "next": null,
        "last": "https://tests.api.dev.salesfloor.net/v2/tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=customer%2Ctask-category&page%5Bnumber%5D=0&page%5Bsize%5D=100"
    },
    "data": [
        {
            "type": "task",
            "id": "1",
            "attributes": {
                "user_id": "1001",
                "customer_id": "1",
                "task_category_id": "1",
                "status": "unresolved",
                "automated_type": "nag_share_update",
                "details": "past start date",
                "resolution_note": null,
                "resolution_date": null,
                "last_reminder_date": null,
                "parent_id": null,
                "task_type": "manual",
                "virtual_fields": {
                    "is_due": null,
                    "is_overdue": null,
                    "is_upcoming": null,
                    "rep_transaction_trx_id": null,
                    "retailer_transaction_thread_id": null,
                    "repName": " ",
                    "storeName": "Fake Mall"
                }
            },
            "relationships": {
                "customer": {
                    "data": {
                        "type": "customer",
                        "id": "1"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/tasks/1/relationships/customer",
                        "related": "https://tests.api.dev.salesfloor.net/v2/tasks/1/customer"
                    }
                },
                "task-category": {
                    "data": {
                        "type": "task-category",
                        "id": "1"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/tasks/1/relationships/task-category",
                        "related": "https://tests.api.dev.salesfloor.net/v2/tasks/1/task-category"
                    }
                }
            },
            "links": {
                "self": "https://tests.api.dev.salesfloor.net/v2/tasks/1"
            }
        }
    ],
    "included": [
        {
            "type": "customer",
            "id": "1",
            "attributes": {
                "user_id": "1",
                "email": "<EMAIL>",
                "name": "Joseph Mallette",
                "phone": "",
                "localization": "en",
                "geo": "",
                "latitude": null,
                "longitude": null,
                "comment": "",
                "subcribtion_flag": "0",
                "sms_marketing_subscription_flag": "0",
                "first_name": "Joseph",
                "last_name": "Mallette",
                "note": null,
                "last_modified": "2017-03-22 19:08:26",
                "label_email": null,
                "label_phone": null,
                "retailer_customer_id": "123456",
                "origin": "unknown",
                "locale": "en_US",
                "unassigned_employee_id": null,
                "retailer_parent_customer_id": null,
                "entity_last_modified": null,
                "entity_last_export": null,
                "contact_preference": null,
                "customer_type": "corporate"
            }
        },
        {
            "type": "task-category",
            "id": "1",
            "attributes": {
                "name": "Generic",
                "updated_at": null
            }
        }
    ]
}
DATA;

        Fixtures::add('testManagerTaskPastStartDateResponse', json_decode($response, true));
    }

    public function testManagerTaskNullStartDate()
    {
        Fixtures::add('testManagerTaskNullStartDate', [
            'sf_task' => [
                [
                    'id'               => 1,
                    'user_id'          => 1001,
                    'customer_id'      => 1,
                    'task_category_id' => 1,
                    'details'          => 'null start date',
                    'status'           => 'unresolved',
                    'reminder_date'    => date('Y-m-d H:i:s'),
                    'start_date'       => null,
                    'automated_type'   => 'nag_share_update',
                ]
            ],
            'wp_users' => [
                [
                    'ID' => 1001,
                    'user_login' => 'reggie1001',
                    'user_pass' => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_nicename' => 'reggie2',
                    'user_email' => '<EMAIL>',
                    'user_url' => '',
                    'user_registered' => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status' => 1,
                    'user_alias' => null,
                    'display_name' => 'reggie2',
                    'type' => 'rep',
                    'commission_rate' => 0.00,
                    'employee_id' => '12345',
                    'group' => 2,
                    'selling_mode' => 1,
                    'isPhoto' => 1,
                    'store' => 1003,
                ]
            ]
        ]);

        $response = <<<DATA
{
    "meta": {
        "pages": 1,
        "total": 1
    },
    "links": {
        "self": "https://tests.api.dev.salesfloor.net/v2/tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=customer%2Ctask-category&page%5Bnumber%5D=0&page%5Bsize%5D=100",
        "first": "https://tests.api.dev.salesfloor.net/v2/tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=customer%2Ctask-category&page%5Bnumber%5D=0&page%5Bsize%5D=100",
        "prev": null,
        "next": null,
        "last": "https://tests.api.dev.salesfloor.net/v2/tasks?filter%5Bstatus%5D=unresolved&sort=reminder_date&include=customer%2Ctask-category&page%5Bnumber%5D=0&page%5Bsize%5D=100"
    },
    "data": [
        {
            "type": "task",
            "id": "1",
            "attributes": {
                "user_id": "1001",
                "customer_id": "1",
                "task_category_id": "1",
                "status": "unresolved",
                "automated_type": "nag_share_update",
                "details": "null start date",
                "resolution_note": null,
                "resolution_date": null,
                "last_reminder_date": null,
                "parent_id": null,
                "task_type": "manual",
                "virtual_fields": {
                    "is_due": null,
                    "is_overdue": null,
                    "is_upcoming": null,
                    "rep_transaction_trx_id": null,
                    "retailer_transaction_thread_id": null,
                    "repName": " ",
                    "storeName": "Fake Mall"
                }
            },
            "relationships": {
                "customer": {
                    "data": {
                        "type": "customer",
                        "id": "1"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/tasks/1/relationships/customer",
                        "related": "https://tests.api.dev.salesfloor.net/v2/tasks/1/customer"
                    }
                },
                "task-category": {
                    "data": {
                        "type": "task-category",
                        "id": "1"
                    },
                    "links": {
                        "self": "https://tests.api.dev.salesfloor.net/v2/tasks/1/relationships/task-category",
                        "related": "https://tests.api.dev.salesfloor.net/v2/tasks/1/task-category"
                    }
                }
            },
            "links": {
                "self": "https://tests.api.dev.salesfloor.net/v2/tasks/1"
            }
        }
    ],
    "included": [
        {
            "type": "customer",
            "id": "1",
            "attributes": {
                "user_id": "1",
                "email": "<EMAIL>",
                "name": "Joseph Mallette",
                "phone": "",
                "localization": "en",
                "geo": "",
                "latitude": null,
                "longitude": null,
                "comment": "",
                "subcribtion_flag": "0",
                "sms_marketing_subscription_flag": "0",
                "first_name": "Joseph",
                "last_name": "Mallette",
                "note": null,
                "last_modified": "2017-03-22 19:08:26",
                "label_email": null,
                "label_phone": null,
                "retailer_customer_id": "123456",
                "origin": "unknown",
                "locale": "en_US",
                "unassigned_employee_id": null,
                "retailer_parent_customer_id": null,
                "entity_last_modified": null,
                "entity_last_export": null,
                "contact_preference": null,
                "customer_type": "corporate"
            }
        },
        {
            "type": "task-category",
            "id": "1",
            "attributes": {
                "name": "Generic",
                "updated_at": null
            }
        }
    ]
}
DATA;

        Fixtures::add('testManagerTaskNullStartDateResponse', json_decode($response, true));
    }

    /**
     * @param string $timezone
     * @return string
     * @throws \Exception
     */
    private function getDateByTimezone(string $datetimeString, string $timezone): string
    {
        $time = gmdate('Y-m-d H:i:s', strtotime($datetimeString));
        $datetime = new \DateTime($time, new \DateTimeZone('UTC'));
        $datetime->setTimezone(new \DateTimeZone($timezone));
        return $datetime->format('Y-m-d H:i:s');
    }
}
