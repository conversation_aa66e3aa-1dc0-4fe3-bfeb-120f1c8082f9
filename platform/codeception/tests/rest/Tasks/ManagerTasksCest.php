<?php

namespace SF\rest\Tasks;

use SF\Helper\Traits\Asserts;
use SF\rest\BaseRest;
use SF\RestTester;

class ManagerTasksCest extends BaseRest
{
    use Asserts;

    /** @group database_transaction */
    public function testFilterUpcomingForManagerView(RestTester $I)
    {
        $I->wantTo("Test get upcoming tasks for manager view");

        $this->insertFixtureGroup($I, 'manager_view_tasks_fixture');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[is_upcoming]=1&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $tasks = $response->data;

        $I->assertEquals(2, count($tasks));

        $included = $response->included;
        $customer = $included[0];
        $taskCategory = $included[1];

        $tomorrow = date('Y-m-d', strtotime(' +1 day'));

        $task1 = $tasks[0];

        $I->assertEquals('1', $task1->attributes->user_id);
        $I->assertEquals('unresolved', $task1->attributes->status);
        $I->assertEquals($tomorrow, date('Y-m-d', strtotime($task1->attributes->reminder_date)));
        $I->assertEquals('1', $customer->id);
        $I->assertEquals('1', $taskCategory->id);

        $task2 = $tasks[1];

        $I->assertEquals('2', $task2->attributes->user_id);
        $I->assertEquals('unresolved', $task2->attributes->status);
        $I->assertEquals($tomorrow, date('Y-m-d', strtotime($task2->attributes->reminder_date)));
        $I->assertEquals('1', $customer->id);
        $I->assertEquals('1', $taskCategory->id);
    }

    /** @group database_transaction */
    public function testFilterDueTodayForManagerView(RestTester $I)
    {
        $I->wantTo("Test get today's due tasks for manager view");

        $this->insertFixtureGroup($I, 'manager_view_tasks_fixture');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[is_due]=1&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $tasks = $response->data;

        $I->assertEquals(2, count($tasks));

        $included = $response->included;
        $customer = $included[0];
        $taskCategory = $included[1];

        $today = date('Y-m-d');

        $task1 = $tasks[0];

        $I->assertEquals('1', $task1->attributes->user_id);
        $I->assertEquals('unresolved', $task1->attributes->status);
        $I->assertEquals($today, date('Y-m-d', strtotime($task1->attributes->reminder_date)));
        $I->assertEquals('1', $customer->id);
        $I->assertEquals('1', $taskCategory->id);

        $task2 = $tasks[1];

        $I->assertEquals('2', $task2->attributes->user_id);
        $I->assertEquals('unresolved', $task2->attributes->status);
        $I->assertEquals($today, date('Y-m-d', strtotime($task2->attributes->reminder_date)));
        $I->assertEquals('1', $customer->id);
        $I->assertEquals('1', $taskCategory->id);
    }

    /** @group database_transaction */
    public function testFilterOverDueForManagerView(RestTester $I)
    {
        $I->wantTo("Test get over due tasks for manager view");

        $this->insertFixtureGroup($I, 'manager_view_tasks_fixture');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[is_overdue]=1&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $tasks = $response->data;

        $I->assertEquals(2, count($tasks));

        $included = $response->included;
        $customer = $included[0];
        $taskCategory = $included[1];

        $yesterday = date('Y-m-d', strtotime(' -1 day'));

        $task1 = $tasks[0];

        $I->assertEquals('1', $task1->attributes->user_id);
        $I->assertEquals('unresolved', $task1->attributes->status);
        $I->assertEquals($yesterday, date('Y-m-d', strtotime($task1->attributes->reminder_date)));
        $I->assertEquals('1', $customer->id);
        $I->assertEquals('1', $taskCategory->id);

        $task2 = $tasks[1];

        $I->assertEquals('2', $task2->attributes->user_id);
        $I->assertEquals('unresolved', $task2->attributes->status);
        $I->assertEquals($yesterday, date('Y-m-d', strtotime($task2->attributes->reminder_date)));
        $I->assertEquals('1', $customer->id);
        $I->assertEquals('1', $taskCategory->id);
    }

    /** @group database_transaction */
    public function testFilterByStoresForManagerView(RestTester $I)
    {
        $I->wantTo("Test get tasks filtered by stores for manager view");

        $this->insertFixtureGroup($I, 'manager_view_tasks_fixture');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[store_ids]=1003&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $tasks = $response->data;

        $I->assertEquals(6, count($tasks));
    }

    /** @group database_transaction */
    public function testFilterByUsersForManagerView(RestTester $I)
    {
        $I->wantTo("Test get tasks filtered by stores for manager view");

        $this->insertFixtureGroup($I, 'manager_view_tasks_fixture');

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[user_ids]=1&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $tasks = $response->data;

        $I->assertEquals(3, count($tasks));
    }

    /** @group database_transaction */
    public function testGetAllFiltersForManagerView(RestTester $I)
    {
        $I->wantTo("Test get all filters for manager view");

        $this->insertFixtureGroup($I, 'manager_view_tasks_fixture');

        $userId = 6;
        $userName = 'user5';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        //we must specify store ids to get counts
        $response = $I->doDirectGet($I, "v2/managers/tasks/filters?filter[store_ids]=1003,2003");

        $filters = $response->data;

        $unresolvedCount = $filters[0]->attributes->count;
        $I->assertEquals(6, $unresolvedCount);

        $resolvedCount = $filters[1]->attributes->count;
        $I->assertEquals(6, $resolvedCount);

        $dismissedCount = $filters[2]->attributes->count;
        $I->assertEquals(0, $dismissedCount);

        $dueCount = $filters[3]->attributes->count;
        $I->assertEquals(2, $dueCount);

        $overDueCount = $filters[4]->attributes->count;
        $I->assertEquals(2, $overDueCount);

        $upcomingCount = $filters[5]->attributes->count;
        $I->assertEquals(2, $upcomingCount);

        // no store ids will return 0 to avoid performance issue if too many stores and tasks
        $response = $I->doDirectGet($I, "v2/managers/tasks/filters");

        $filters = $response->data;

        $unresolvedCount = $filters[0]->attributes->count;
        $I->assertEquals(0, $unresolvedCount);

        $resolvedCount = $filters[1]->attributes->count;
        $I->assertEquals(0, $resolvedCount);

        $dismissedCount = $filters[2]->attributes->count;
        $I->assertEquals(0, $dismissedCount);

        $dueCount = $filters[3]->attributes->count;
        $I->assertEquals(0, $dueCount);

        $overDueCount = $filters[4]->attributes->count;
        $I->assertEquals(0, $overDueCount);

        $upcomingCount = $filters[5]->attributes->count;
        $I->assertEquals(0, $upcomingCount);
    }

    /** @group database_transaction */
    public function testManagerTaskPastStartDate(RestTester $I)
    {
        $I->wantTo("Test - store task (manager) - start date if the past");

        $this->insertFixtureGroup($I, 'testManagerTaskPastStartDate');

        $userId = 1001;
        $userName = 'fake';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $response->data[0]->attributes->reminder_date = '2025-01-01 00:00:00';
        $response->included[0]->attributes->created = '2025-01-01 00:00:00';
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($response, $jsonFile);
    }

    /** @group database_transaction */
    public function testManagerTaskFutureStartDate(RestTester $I)
    {
        $I->wantTo("Test - store task (manager) - start date if the future");

        $this->insertFixtureGroup($I, 'testManagerTaskFutureStartDate');
        $userId = 1001;
        $userName = 'fake';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $I->assertEquals(0, count($response->data));
    }

    /** @group database_transaction */
    public function testManagerTaskNullStartDate(RestTester $I)
    {
        $I->wantTo("Test - store task (manager) - start date is null");

        $this->insertFixtureGroup($I, 'testManagerTaskNullStartDate');

        $userId = 1001;
        $userName = 'fake';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $response = $I->doDirectGet($I, "v2/managers/tasks?sort=reminder_date&filter[status]=unresolved&page[number]=0&page[size]=100&include=customer,task-category");

        $response->data[0]->attributes->reminder_date = '2025-01-01 00:00:00';
        $response->included[0]->attributes->created = '2025-01-01 00:00:00';
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($response, $jsonFile);
    }
}
