<?php

namespace SF\rest\Tasks;

use SF\RestTester;
use SF\rest\BaseRest;
use Codeception\Util\Fixtures;

class CorporateTasksCest extends BaseRest
{
    /** @group database_transaction */
    public function testCreateCorporateTask(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task");
        $I->fakeCorporateTaskQueueService();

        $userId = 6;
        $userName = 'user5';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => "2019-05-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-21 09:00:00",
                    "start_date" => "2019-05-19 09:00:00"
                ],
            ],
        ];

        $I->doDirectPost($I, "v2/corporate-tasks", $params);

        $I->seeInDatabase('sf_corporate_tasks', $params["data"]["attributes"]);
    }

    /** @group database_transaction */
    public function testCreateCorporateTaskWithEmptyTitle(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task with empty title");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectPost($I, "v2/corporate-tasks", Fixtures::get('corporate-task-with-empty-title'));

        $errors = $response->errors;

        $I->assertEquals('400', $errors[0]->status);
        $I->assertEquals('Invalid request', $errors[0]->title);
    }

    /** @group database_transaction */
    public function testCreateCorporateTaskWithTitleExceedsMax100Characters(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task with title exceeding max 100 characters");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectPost($I, "v2/corporate-tasks", Fixtures::get('corporate-task-with-title-exceeds-max-100-characters'));

        $errors = $response->errors;

        $I->assertEquals('400', $errors[0]->status);
        $I->assertEquals('Invalid request', $errors[0]->title);
    }

    /** @group database_transaction */
    public function testCreateCorporateTaskWithSpecialty(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task with specialty");
        $I->fakeCorporateTaskQueueService();
        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $I->doDirectPost($I, "v2/corporate-tasks", Fixtures::get('corporate-task-with-specialty-body'));

        $I->seeInDatabase('sf_corporate_task_specialties', [
            'specialty_id' => 'JtDN4T9R8pX3ruQMwhjNA6aMaTw'
        ]);
        $I->seeInDatabase('sf_corporate_task_specialties', [
            'specialty_id' => '9q8WZbyTorsq7L8CqZIChtlAM6I'
        ]);
    }

/** @group database_transaction */
    public function testCorporateTaskWithLateAssetStartDateIsValid(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task with Asset that has late start date");

        $this->insertFixtureGroup($I, 'corporate_user_assets');

        $userId = 335;

        $this->setTokenInRequestHeader($I, $userId, 'testuser335');

        $payload = Fixtures::get('corporate-task-with-asset-body');

        $I->doDirectPost($I, "v2/corporate-tasks", $payload);

        $I->seeInDatabase('sf_corporate_task_assets', [
            'asset_id' => 1
        ]);
    }
    public function testShouldNotCreateCorporateTaskWithLateAssetEndDate(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task with Asset that has late start date");

        $this->insertFixtureGroup($I, 'corporate_user_assets');

        $userId = 335;

        $this->setTokenInRequestHeader($I, $userId, 'testuser335');

        $payload = Fixtures::get('corporate-task-with-asset-body');
        $payload['data']['attributes']['start_date'] = '2023-05-30 09:00:00';

        $I->doDirectPost($I, "v2/corporate-tasks", $payload);

        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testGetCorporateTaskWithSpecialty(RestTester $I)
    {
        $I->wantTo("Test Get Corporate Task With Specialty");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_with_specialty_fixture');

        $response = $I->doDirectGet($I, "v2/corporate-tasks/1?include=corporate-task-specialty,corporate-task-specialty.category");

        $included = $response->included;

        $I->assertEquals(4, count($included));


        $I->assertEquals('category', $included[0]->type);
        $I->assertEquals('JtDN4T9R8pX3ruQMwhjNA6aMaTw', $included[0]->attributes->category_id);
        $I->assertEquals('Men', $included[0]->attributes->name);

        $I->assertEquals('category', $included[2]->type);
        $I->assertEquals('9q8WZbyTorsq7L8CqZIChtlAM6I', $included[2]->attributes->category_id);
        $I->assertEquals("Women's Apparel", $included[2]->attributes->name);
    }

    /** @group database_transaction */
    public function testGetListOfCorporateTasks(RestTester $I)
    {
        $I->wantTo("Test Get A List Of Corporate Task");

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, "v2/corporate-tasks?include=corporate-task-product,corporate-task-asset,corporate-task-specialty");

        $tasks = $response->data;

        $I->assertEquals(3, count($tasks));

        $task = $tasks[0];

        $I->assertEquals(1, $task->id);

        $I->assertEquals('test title', $task->attributes->title);
        $I->assertEquals(0, $task->attributes->is_deleted);
    }

    /** @group database_transaction */
    public function testGetListOfCorporateTasksWithStartDateForAKnownTimezoneReturnedAsIs(RestTester $I)
    {
        $I->wantTo("Get A List Of Corporate Task with start date set for same timezone returned as provided");

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');
        $dump = Fixtures::get('corporate_tasks_fixture');
        $data = $dump['sf_corporate_tasks'];

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, "v2/corporate-tasks");

        $tasksWithStartDateStaysTheSame = $response->data[0]->attributes->start_date;
        $I->assertEquals($data[0]['start_date'], $tasksWithStartDateStaysTheSame);
    }

    /** @group database_transaction */
    public function testGetListOfExistingCorporateTasksWithNoStartDateFilledOnReturn(RestTester $I)
    {
        $I->wantTo("Get A List Of Corporate Task having empty start date filled by converting created_at using retailer default timezone");

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');
        $dump = Fixtures::get('corporate_tasks_fixture');
        $data = $dump['sf_corporate_tasks'];

        $date     = new \DateTime($data[0]['created_at'], new \DateTimeZone('UTC'));
        $timezone = new \DateTimeZone($this->app['configs']['retailer.store.default_timezone']);
        $date->setTimezone($timezone);

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, "v2/corporate-tasks");

        $tasksWithNoStartDateIsFilled = $response->data[2]->attributes->start_date;

        $I->assertEquals($date->format('Y-m-d H:i:s'), $tasksWithNoStartDateIsFilled);
    }

    /** @group database_transaction */
    public function testGetAnExistingCorporateTasksWithNoStartDateConvertedAndFilledOnReturn(RestTester $I)
    {
        $I->wantTo("Get A Corporate Task having empty start date filled by converting created_at using store timezone");

        $this->insertFixtureGroup($I, 'corporate_user_assets');
        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');
        $userDump = fixtures::get('corporate_user_assets');
        $dump = Fixtures::get('corporate_tasks_fixture');
        $data = $dump['sf_corporate_tasks'];

        $date     = new \DateTime($data[2]['created_at'], new \DateTimeZone('UTC'));
        $timezone = new \DateTimeZone($userDump['sf_store'][0]['timezone']);
        $date->setTimezone($timezone);

        $userId = $userDump['wp_users'][0]['ID'];

        $this->setTokenInRequestHeader($I, $userId);

        $response = $I->doDirectGet($I, "v2/corporate-tasks/3");

        $tasksWithNoStartDateIsFilled = $response->data->attributes->start_date;

        $I->assertEquals($date->format('Y-m-d H:i:s'), $tasksWithNoStartDateIsFilled);
    }

    /** @group database_transaction */
    public function testDeleteCorporateTask(RestTester $I)
    {
        $I->wantTo("Test Delete Corporate Task");
        $I->fakeCorporateTaskQueueService();

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $I->seeInDatabase(
            'sf_corporate_tasks',
            [
                'id'         => 1,
                'is_deleted' => 0
            ]
        );

        $response = $I->doDirectDelete($I, "v2/corporate-tasks/1");

        $task = $response->data;

        $I->assertEquals(1, $task->attributes->is_deleted);
    }

    /** @group database_transaction */
    public function testGetListByFilterAll(RestTester $I)
    {
        $I->wantTo("Test GetList of JsonApi filter by view panel status (all)");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');

        $response = $I->doDirectGet($I, "v2/corporate-tasks?sort=-reminder_time&filter[view_panel_status_filter]=all");
        $I->assertEquals(3, $response->meta->total);
    }

    /** @group database_transaction */
    public function testGetListByFilterDeleted(RestTester $I)
    {
        $I->wantTo("Test GetList of JsonApi filter by view panel status (deleted)");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture');

        $response = $I->doDirectGet($I, "v2/corporate-tasks?sort=-reminder_time&filter[view_panel_status_filter]=deleted");
        $I->assertEquals(1, $response->meta->total);
    }

    /** @group database_transaction */
    public function testGetListByFilterExpired(RestTester $I)
    {
        $I->wantTo("Test GetList of JsonApi filter by view panel status (expired)");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture_expired');

        $response = $I->doDirectGet($I, "v2/corporate-tasks?sort=-reminder_time&filter[view_panel_status_filter]=expired");
        $I->assertEquals(1, $response->meta->total);
    }

    /** @group database_transaction */
    public function testGetListByFilterActive(RestTester $I)
    {
        $I->wantTo("Test GetList of JsonApi filter by view panel status (active)");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture_active');

        $response = $I->doDirectGet($I, "v2/corporate-tasks?sort=-reminder_time&filter[view_panel_status_filter]=active");
        $I->assertEquals(2, $response->meta->total);
    }

    /** @group database_transaction */
    public function testGetListByFilterUpcoming(RestTester $I)
    {
        $I->wantTo("Test GetList of JsonApi filter by view panel status (upcoming)");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_fixture_upcoming');

        $response = $I->doDirectGet($I, "v2/corporate-tasks?sort=-reminder_time&filter[view_panel_status_filter]=upcoming");
        $I->assertEquals(1, $response->meta->total);
    }

    /** @group database_transaction */
    public function testCreateCorporateTaskWithoutAuthorization(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task Without Authorization: Only Admin Or Corp Admin Can Create Corp Task");

        $userId = 1;

        $this->setTokenInRequestHeader($I, $userId);

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00"
                ],
            ],
        ];

        $response = $I->doDirectPost($I, "v2/corporate-tasks", $params);

        $errors = $response->errors;

        $I->assertEquals('403', $errors[0]->code);
        $I->assertEquals('You don\'t have the authorization for this action', $errors[0]->title);
    }

    /** @group database_transaction */
    public function testGetListOfCorporateTasksWithMultilang(RestTester $I)
    {
        $I->wantTo("Test Get List of Corporate Task With Multilang");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_with_multi_lang');

        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];

        $response = $I->doDirectGet($I, "v2/corporate-tasks?include=corporate-task-specialty,corporate-task-specialty.category");

        $data1 = $response->data[0]->attributes;
        $title1 = $data1->title;

        $I->assertEquals('test title 1', $title1->en_US);
        $I->assertEquals('titre1', $title1->fr_CA);

        $data2 = $response->data[1]->attributes;
        $title2 = $data2->title;

        $I->assertEquals('test title 2', $title2->en_US);
        $I->assertEquals('titre2', $title2->fr_CA);
    }

    /** @group database_transaction */
    public function testGetOneCorporateTasksWithMultilang(RestTester $I)
    {
        $I->wantTo("Test Get One Corporate Task With Multilang");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_with_multi_lang');

        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];

        $response = $I->doDirectGet($I, "v2/corporate-tasks/1?include=corporate-task-specialty,corporate-task-specialty.category");

        $data = $response->data->attributes;
        $title = $data->title;

        $I->assertEquals('test title 1', $title->en_US);
        $I->assertEquals('titre1', $title->fr_CA);

        // NOTE : this api return all language data in an array even specify language specify in params `&sf_locale=all`
        // BO admin section need output multi-lang value as an array into i18n field
        $response = $I->doDirectGet($I, "v2/corporate-tasks/1?include=corporate-task-specialty,corporate-task-specialty.category&sf_locale=all");
        $I->assertEquals('test title 1', $response->data->attributes->title->en_US);
        $I->assertEquals('titre1', $response->data->attributes->title->fr_CA);

        // NOTE : this api return all language data to keep backward compatibility
        $response2 = $I->doDirectGet($I, "v2/corporate-tasks/1?include=corporate-task-specialty,corporate-task-specialty.category");
        $I->assertEquals('test title 1', $response2->data->attributes->title->en_US);
        $I->assertEquals('titre1', $response2->data->attributes->title->fr_CA);

        // NOTE : this api should return data in plain text for specify language in params `&sf_locale=en_US` (Mobile rep section)
        // TODO : TBD return format, ignore the params now, fixed could ref: https://github.com/Salesfloor/platform/pull/7674/files
        $response3 = $I->doDirectGet($I, "v2/corporate-tasks/1?include=corporate-task-specialty,corporate-task-specialty.category&sf_locale=en_US");
        $I->assertEquals('test title 1', $response3->data->attributes->title->en_US);
        $I->assertEquals('titre1', $response3->data->attributes->title->fr_CA);

        // NOTE : this api return data in plain text for specify language in params `&sf_locale=fr_CA` (Mobile rep section)
        // TODO : TBD return format, ignore the params now, fixed could ref: https://github.com/Salesfloor/platform/pull/7674/files
        $response4 = $I->doDirectGet($I, "v2/corporate-tasks/1?include=corporate-task-specialty,corporate-task-specialty.category&sf_locale=fr_CA");
        $I->assertEquals('test title 1', $response4->data->attributes->title->en_US);
        $I->assertEquals('titre1', $response4->data->attributes->title->fr_CA);
    }

    /** @group database_transaction */
    public function testGetOneCorporateTasksWithMultilangWithFallback(RestTester $I)
    {
        $I->wantTo("Test Get One Corporate Task With Multilang");

        $userId = 6;

        $this->setTokenInRequestHeader($I, $userId);

        $this->insertFixtureGroup($I, 'corporate_tasks_with_multi_lang');
        $this->insertFixtureGroup($I, 'corporate_tasks_with_multi_lang_i18n_fallback');

        $this->app['configs']['retailer.i18n.is_enabled'] = true;
        $this->app['configs']['sf.i18n.locales'] = ['en_US', 'fr_CA'];

        // NOTE : this api return data in plain text for specify language in params `&sf_locale=fr_CA` (Mobile rep section)
        // TODO : TBD return format
        $response = $I->doDirectGet($I, "v2/corporate-tasks/999?include=corporate-task-specialty,corporate-task-specialty.category&sf_locale=all");
        $I->assertEquals('test title 999', $response->data->attributes->title);

        // NOTE : this api return data in plain text for specify language in params `&sf_locale=fr_CA` (Mobile rep section)
        // TODO : TBD return format
        $response = $I->doDirectGet(
            $I,
            "v2/corporate-tasks/999?include=corporate-task-specialty,corporate-task-specialty.category&sf_locale=fr_CA"
        );
        $I->assertEquals('test title 999', $response->data->attributes->title);
    }

    /** @group database_transaction */
    public function testCreateCorporateTaskWithInvalidAutoDismiss(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task");

        $userId = 6;
        $userName = 'user5';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00"
                ],
            ],
        ];

        $I->doDirectPost($I, "v2/corporate-tasks", $params);
        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testCreateCorporateTaskWithInvalidReminderTime(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task");

        $userId = 6;
        $userName = 'user5';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-22 09:00:00",
                    "start_date" => "2019-05-20 09:00:00"
                ],
            ],
        ];

        $I->doDirectPost($I, "v2/corporate-tasks", $params);
        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testShouldNotCreateCorporateTaskWithReminderTimeLaterThanAutoDismissTime(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task");

        $userId = 6;
        $userName = 'user5';
        $pwd = '123123Aa';

        $this->setTokenInRequestHeader($I, $userId, $userName);

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => "2019-06-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-22 09:00:00",
                    "start_date" => "2019-05-20 09:00:00"
                ],
            ],
        ];

        $I->doDirectPost($I, "v2/corporate-tasks", $params);
        $I->assertStatusCode(400);
    }

    /** @group database_transaction */
    public function testCanUpdateCorporateTaskWithStartDateAndReminderTimeOnSameDayDifferentTimeAndNotLive(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task");

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        $timezone = 'America/Montreal';
        $dateTime = new \DateTime('+1 hour', new \DateTimeZone($timezone));
        $startDate = $dateTime->format('Y-m-d H:i:s');
        $reminderDate = $dateTime->add(new \DateInterval('PT2H'))->format('Y-m-d H:i:s');

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => $reminderDate,
                    "start_date" => $startDate
                ],
            ],
        ];

        $response = $I->doDirectPost($I, "v2/corporate-tasks", $params);
        $taskId = $response->data->id;
        $params['data']['id'] = $taskId;
        $dateTime = new \DateTime('+1 hour 30min', new \DateTimeZone($timezone));
        $params['data']['attributes']['start_date'] = $dateTime->format('Y-m-d H:i:s');
        $I->doDirectPatch($I, "v2/corporate-tasks/$taskId", $params);
        $I->assertStatusCode(201);
    }

    /** @group database_transaction */
    public function testUpdateCorporateTaskAfterStartDateLiveFails(RestTester $I)
    {
        $I->wantTo("Test Create Corporate Task");

        $userId = 6;
        $userName = 'user5';

        $this->setTokenInRequestHeader($I, $userId, $userName);
        $timezone = 'America/Montreal';
        $dateTime = new \DateTime('-2 hour', new \DateTimeZone($timezone));
        $startDate = $dateTime->format('Y-m-d H:i:s');
        $reminderDate = $dateTime->add(new \DateInterval('PT2H'))->format('Y-m-d H:i:s');

        $params = [
            "data" => [
                "type" => "corporate-task",
                "attributes" => [
                    "title" => "test title",
                    "subject" => null,
                    "detail" => "test detail",
                    "body" => null,
                    "created_by" => 1,
                    "updated_by" => 1,
                    "reminder_time" => $reminderDate,
                    "start_date" => $startDate
                ],
            ],
        ];

        $response = $I->doDirectPost($I, "v2/corporate-tasks", $params);
        $taskId = $response->data->id;
        $params['data']['id'] = $taskId;
        $dateTime = new \DateTime('+1 hour 30min', new \DateTimeZone($timezone));
        $params['data']['attributes']['start_date'] = $dateTime->format('Y-m-d H:i:s');
        $I->doDirectPatch($I, "v2/corporate-tasks/$taskId", $params);
        $I->assertStatusCode(400);
    }


    /** @group database_transaction */
    public function testCorporateTaskIsNotCachedWhenQueriedFromAttachedAssetAfterCreated(RestTester $I)
    {
        $this->addAssets($I);
        $userId = 335;

        $this->setTokenInRequestHeader($I, $userId, 'testuser335');
        // prefill the cache
        $included = $this->getAssetsWithCorporateTasks($I);
        $I->assertNull($included);

        $payload = Fixtures::get('corporate-task-with-asset-body');
        $response = $I->doDirectPost($I, "v2/corporate-tasks", $payload);

        $taskId = $response->data->id;
        $reminder_time = $payload['data']['attributes']['reminder_time'];
        $this->assertAssetHasCorporateTask($I, $taskId, $reminder_time);
    }

    /** @group database_transaction */
    public function testCorporateTaskIsNotCachedWhenQueriedFromAttachedAssetAfterUpdated(RestTester $I)
    {
        $this->addAssets($I);
        $userId = 335;

        $this->setTokenInRequestHeader($I, $userId, 'testuser335');

        $payload = Fixtures::get('corporate-task-with-asset-body');

        $payload['data']['attributes']['auto_dismiss_time'] = date('Y-m-d H:i:s', strtotime('+5 days'));
        $response = $I->doDirectPost($I, "v2/corporate-tasks", $payload);
        $taskId = $response->data->id;

        // prefill the cache
        $this->assertAssetHasCorporateTask($I, $taskId, $payload['data']['attributes']['reminder_time']);

        // update data
        $payload['data']['id'] = $taskId;
        $reminder_time = $payload['data']['attributes']['reminder_time'] = '2023-04-25 09:00:00';
        $I->doDirectPatch($I, "v2/corporate-tasks/$taskId", $payload);
        $this->assertAssetHasCorporateTask($I, $taskId, $reminder_time);
    }


    private function addAssets(RestTester $I)
    {
        $this->insertFixtureGroup($I, 'corporate_user_assets');
    }

    /**
     * @param RestTester $I
     * @param int $taskId
     * @param $reminder_time
     * @return void
     */
    private function assertAssetHasCorporateTask(RestTester $I, int $taskId, $reminder_time): void
    {
        $included = $this->getAssetsWithCorporateTasks($I);
        $I->assertEquals(1, count($included));
        $I->assertEquals($taskId, $included[0]->id);
        $I->assertEquals($reminder_time, $included[0]->attributes->reminder_time);
    }

    /**
     * @param RestTester $I
     * @return array|null
     */
    private function getAssetsWithCorporateTasks(RestTester $I): ?array
    {
        $response = $I->doDirectGet($I, 'v2/assets/1?include=corporate-task');
        return $response->included ?? null;
    }
}
