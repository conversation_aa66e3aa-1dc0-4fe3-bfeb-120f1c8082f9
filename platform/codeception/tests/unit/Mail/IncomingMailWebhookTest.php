<?php

declare(strict_types=1);

namespace SF\unit\Mail;

use Salesfloor\Services\IncomingMail\Parser;
use Symfony\Component\HttpFoundation\HeaderBag;

class IncomingMailWebhookTest extends \Codeception\Test\Unit
{
    public function testNoProviderIdentification()
    {
        $result = (new Parser())->parseWebhookRequest(
            [],
            new HeaderBag()
        );

        $this->assertNull($result);
    }

    public function testMandrillIdentification()
    {
        $mandrillBody = <<<'BODY'
[{"event":"inbound","msg":{"dkim":{"signed":true,"valid":true},"email":"<EMAIL>","from_email":"<EMAIL>","headers":{"Content-Type":"multipart\/alternative; boundary=\"_av-7r7zDhHxVEAo2yMWasfuFw\"","Date":"Fri, 10 May 2013 19:28:20 +0000","Dkim-Signature":["v=1; a=rsa-sha1; c=relaxed\/relaxed; s=mandrill; d=mail115.us4.mandrillapp.com; h=From:Sender:Subject:List-Unsubscribe:To:Message-Id:Date:MIME-Version:Content-Type; i=<EMAIL>; bh=d60x72jf42gLILD7IiqBL0OBb40=; b=iJd7eBugdIjzqW84UZ2xynlg1SojANJR6xfz0JDD44h78EpbqJiYVcMIfRG7mkrn741Bd5YaMR6p 9j41OA9A5am+8BE1Ng2kLRGwou5hRInn+xXBAQm2NUt5FkoXSpvm4gC4gQSOxPbQcuzlLha9JqxJ 8ZZ89\/20txUrRq9cYxk=","v=1; a=rsa-sha256; c=relaxed\/relaxed; d=c.mandrillapp.com; i=@c.mandrillapp.com; q=dns\/txt; s=mandrill; t=1368214100; h=From : Sender : Subject : List-Unsubscribe : To : Message-Id : Date : MIME-Version : Content-Type : From : Subject : Date : X-Mandrill-User : List-Unsubscribe; bh=y5Vz+RDcKZmWzRc9s0xUJR6k4APvBNktBqy1EhSWM8o=; b=PLAUIuw8zk8kG5tPkmcnSanElxt6I5lp5t32nSvzVQE7R8u0AmIEjeIDZEt31+Q9PWt+nY sHHRsXUQ9SZpndT9Bk++\/SmyA2ntU\/2AKuqDpPkMZiTqxmGF80Wz4JJgx2aCEB1LeLVmFFwB 5Nr\/LBSlsBlRcjT9QiWw0\/iRvCn74="],"Domainkey-Signature":"a=rsa-sha1; c=nofws; q=dns; s=mandrill; d=mail115.us4.mandrillapp.com; b=X6qudHd4oOJvVQZcoAEUCJgB875SwzEO5UKf6NvpfqyCVjdaO79WdDulLlfNVELeuoK2m6alt2yw 5Qhp4TW5NegyFf6Ogr\/Hy0Lt411r\/0lRf0nyaVkqMM\/9g13B6D9CS092v70wshX8+qdyxK8fADw8 kEelbCK2cEl0AGIeAeo=;","From":"<<EMAIL>>","List-Unsubscribe":"<mailto:<EMAIL>?subject=unsub>","Message-Id":"<<EMAIL>>","Mime-Version":"1.0","Received":["from mail115.us4.mandrillapp.com (mail115.us4.mandrillapp.com [***************]) by mail.example.com (Postfix) with ESMTP id AAAAAAAAAAA for <<EMAIL>>; Fri, 10 May 2013 19:28:21 +0000 (UTC)","from localhost (127.0.0.1) by mail115.us4.mandrillapp.com id hhl55a14i282 for <<EMAIL>>; Fri, 10 May 2013 19:28:20 +0000 (envelope-from <<EMAIL>>)"],"Sender":"<<EMAIL>>","Subject":"This is an example webhook message","To":"<<EMAIL>>","X-Report-Abuse":"Please forward a copy of this message, including all headers, to <EMAIL>"},"html":"<p>This is an example inbound message.<\/p><img src=\"http:\/\/mandrillapp.com\/track\/open.php?u=999&id=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa&tags=_all,<EMAIL>\" height=\"1\" width=\"1\">\n","raw_msg":"Received: from mail115.us4.mandrillapp.com (mail115.us4.mandrillapp.com [***************])\n\tby mail.example.com (Postfix) with ESMTP id AAAAAAAAAAA\n\tfor <<EMAIL>>; Fri, 10 May 2013 19:28:20 +0000 (UTC)\nDKIM-Signature: v=1; a=rsa-sha1; c=relaxed\/relaxed; s=mandrill; d=mail115.us4.mandrillapp.com;\n h=From:Sender:Subject:List-Unsubscribe:To:Message-Id:Date:MIME-Version:Content-Type; i=<EMAIL>;\n bh=d60x72jf42gLILD7IiqBL0OBb40=;\n b=iJd7eBugdIjzqW84UZ2xynlg1SojANJR6xfz0JDD44h78EpbqJiYVcMIfRG7mkrn741Bd5YaMR6p\n 9j41OA9A5am+8BE1Ng2kLRGwou5hRInn+xXBAQm2NUt5FkoXSpvm4gC4gQSOxPbQcuzlLha9JqxJ\n 8ZZ89\/20txUrRq9cYxk=\nDomainKey-Signature: a=rsa-sha1; c=nofws; q=dns; s=mandrill; d=mail115.us4.mandrillapp.com;\n b=X6qudHd4oOJvVQZcoAEUCJgB875SwzEO5UKf6NvpfqyCVjdaO79WdDulLlfNVELeuoK2m6alt2yw\n 5Qhp4TW5NegyFf6Ogr\/Hy0Lt411r\/0lRf0nyaVkqMM\/9g13B6D9CS092v70wshX8+qdyxK8fADw8\n kEelbCK2cEl0AGIeAeo=;\nReceived: from localhost (127.0.0.1) by mail115.us4.mandrillapp.com id hhl55a14i282 for <<EMAIL>>; Fri, 10 May 2013 19:28:20 +0000 (envelope-from <<EMAIL>>)\nDKIM-Signature: v=1; a=rsa-sha256; c=relaxed\/relaxed; d=c.mandrillapp.com; \n i=@c.mandrillapp.com; q=dns\/txt; s=mandrill; t=1368214100; h=From : \n Sender : Subject : List-Unsubscribe : To : Message-Id : Date : \n MIME-Version : Content-Type : From : Subject : Date : X-Mandrill-User : \n List-Unsubscribe; bh=y5Vz+RDcKZmWzRc9s0xUJR6k4APvBNktBqy1EhSWM8o=; \n b=PLAUIuw8zk8kG5tPkmcnSanElxt6I5lp5t32nSvzVQE7R8u0AmIEjeIDZEt31+Q9PWt+nY\n sHHRsXUQ9SZpndT9Bk++\/SmyA2ntU\/2AKuqDpPkMZiTqxmGF80Wz4JJgx2aCEB1LeLVmFFwB\n 5Nr\/LBSlsBlRcjT9QiWw0\/iRvCn74=\nFrom: <<EMAIL>>\nSender: <<EMAIL>>\nSubject: This is an example webhook message\nList-Unsubscribe: <mailto:<EMAIL>?subject=unsub>\nTo: <<EMAIL>>\nX-Report-Abuse: Please forward a copy of this message, including all headers, to <EMAIL>\nX-Mandrill-User: md_999\nMessage-Id: <<EMAIL>>\nDate: Fri, 10 May 2013 19:28:20 +0000\nMIME-Version: 1.0\nContent-Type: multipart\/alternative; boundary=\"_av-7r7zDhHxVEAo2yMWasfuFw\"\n\n--_av-7r7zDhHxVEAo2yMWasfuFw\nContent-Type: text\/plain; charset=utf-8\nContent-Transfer-Encoding: 7bit\n\nThis is an example inbound message.\n--_av-7r7zDhHxVEAo2yMWasfuFw\nContent-Type: text\/html; charset=utf-8\nContent-Transfer-Encoding: 7bit\n\n<p>This is an example inbound message.<\/p><img src=\"http:\/\/mandrillapp.com\/track\/open.php?u=999&id=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa&tags=_all,<EMAIL>\" height=\"1\" width=\"1\">\n--_av-7r7zDhHxVEAo2yMWasfuFw--","sender":null,"spam_report":{"matched_rules":[{"description":"RBL: Sender listed at http:\/\/www.dnswl.org\/, no","name":"RCVD_IN_DNSWL_NONE","score":0},{"description":null,"name":null,"score":0},{"description":"in iadb.isipp.com]","name":"listed","score":0},{"description":"RBL: Participates in the IADB system","name":"RCVD_IN_IADB_LISTED","score":-0.4},{"description":"RBL: ISIPP IADB lists as vouched-for sender","name":"RCVD_IN_IADB_VOUCHED","score":-2.2},{"description":"RBL: IADB: Sender publishes SPF record","name":"RCVD_IN_IADB_SPF","score":0},{"description":"RBL: IADB: Sender publishes Sender ID record","name":"RCVD_IN_IADB_SENDERID","score":0},{"description":"RBL: IADB: Sender publishes Domain Keys record","name":"RCVD_IN_IADB_DK","score":-0.2},{"description":"RBL: IADB: Sender has reverse DNS record","name":"RCVD_IN_IADB_RDNS","score":-0.2},{"description":"SPF: HELO matches SPF record","name":"SPF_HELO_PASS","score":0},{"description":"BODY: HTML included in message","name":"HTML_MESSAGE","score":0},{"description":"BODY: HTML: images with 0-400 bytes of words","name":"HTML_IMAGE_ONLY_04","score":0.3},{"description":"Message has a DKIM or DK signature, not necessarily valid","name":"DKIM_SIGNED","score":0.1},{"description":"Message has at least one valid DKIM or DK signature","name":"DKIM_VALID","score":-0.1}],"score":-2.6},"spf":{"detail":"sender SPF authorized","result":"pass"},"subject":"This is an example webhook message","tags":[],"template":null,"text":"This is an example inbound message.\n","text_flowed":false,"to":[["<EMAIL>",null]]},"ts":1368214102},{"event":"inbound","msg":{"dkim":{"signed":true,"valid":true},"email":"<EMAIL>","from_email":"<EMAIL>","headers":{"Content-Type":"multipart\/alternative; boundary=\"_av-7r7zDhHxVEAo2yMWasfuFw\"","Date":"Fri, 10 May 2013 19:28:20 +0000","Dkim-Signature":["v=1; a=rsa-sha1; c=relaxed\/relaxed; s=mandrill; d=mail115.us4.mandrillapp.com; h=From:Sender:Subject:List-Unsubscribe:To:Message-Id:Date:MIME-Version:Content-Type; i=<EMAIL>; bh=d60x72jf42gLILD7IiqBL0OBb40=; b=iJd7eBugdIjzqW84UZ2xynlg1SojANJR6xfz0JDD44h78EpbqJiYVcMIfRG7mkrn741Bd5YaMR6p 9j41OA9A5am+8BE1Ng2kLRGwou5hRInn+xXBAQm2NUt5FkoXSpvm4gC4gQSOxPbQcuzlLha9JqxJ 8ZZ89\/20txUrRq9cYxk=","v=1; a=rsa-sha256; c=relaxed\/relaxed; d=c.mandrillapp.com; i=@c.mandrillapp.com; q=dns\/txt; s=mandrill; t=1368214100; h=From : Sender : Subject : List-Unsubscribe : To : Message-Id : Date : MIME-Version : Content-Type : From : Subject : Date : X-Mandrill-User : List-Unsubscribe; bh=y5Vz+RDcKZmWzRc9s0xUJR6k4APvBNktBqy1EhSWM8o=; b=PLAUIuw8zk8kG5tPkmcnSanElxt6I5lp5t32nSvzVQE7R8u0AmIEjeIDZEt31+Q9PWt+nY sHHRsXUQ9SZpndT9Bk++\/SmyA2ntU\/2AKuqDpPkMZiTqxmGF80Wz4JJgx2aCEB1LeLVmFFwB 5Nr\/LBSlsBlRcjT9QiWw0\/iRvCn74="],"Domainkey-Signature":"a=rsa-sha1; c=nofws; q=dns; s=mandrill; d=mail115.us4.mandrillapp.com; b=X6qudHd4oOJvVQZcoAEUCJgB875SwzEO5UKf6NvpfqyCVjdaO79WdDulLlfNVELeuoK2m6alt2yw 5Qhp4TW5NegyFf6Ogr\/Hy0Lt411r\/0lRf0nyaVkqMM\/9g13B6D9CS092v70wshX8+qdyxK8fADw8 kEelbCK2cEl0AGIeAeo=;","From":"<<EMAIL>>","List-Unsubscribe":"<mailto:<EMAIL>?subject=unsub>","Message-Id":"<<EMAIL>>","Mime-Version":"1.0","Received":["from mail115.us4.mandrillapp.com (mail115.us4.mandrillapp.com [***************]) by mail.example.com (Postfix) with ESMTP id AAAAAAAAAAA for <<EMAIL>>; Fri, 10 May 2013 19:28:21 +0000 (UTC)","from localhost (127.0.0.1) by mail115.us4.mandrillapp.com id hhl55a14i282 for <<EMAIL>>; Fri, 10 May 2013 19:28:20 +0000 (envelope-from <<EMAIL>>)"],"Sender":"<<EMAIL>>","Subject":"This is an example webhook message","To":"<<EMAIL>>","X-Report-Abuse":"Please forward a copy of this message, including all headers, to <EMAIL>"},"html":"<p>This is an example inbound message.<\/p><img src=\"http:\/\/mandrillapp.com\/track\/open.php?u=999&id=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa&tags=_all,<EMAIL>\" height=\"1\" width=\"1\">\n","raw_msg":"Received: from mail115.us4.mandrillapp.com (mail115.us4.mandrillapp.com [***************])\n\tby mail.example.com (Postfix) with ESMTP id AAAAAAAAAAA\n\tfor <<EMAIL>>; Fri, 10 May 2013 19:28:20 +0000 (UTC)\nDKIM-Signature: v=1; a=rsa-sha1; c=relaxed\/relaxed; s=mandrill; d=mail115.us4.mandrillapp.com;\n h=From:Sender:Subject:List-Unsubscribe:To:Message-Id:Date:MIME-Version:Content-Type; i=<EMAIL>;\n bh=d60x72jf42gLILD7IiqBL0OBb40=;\n b=iJd7eBugdIjzqW84UZ2xynlg1SojANJR6xfz0JDD44h78EpbqJiYVcMIfRG7mkrn741Bd5YaMR6p\n 9j41OA9A5am+8BE1Ng2kLRGwou5hRInn+xXBAQm2NUt5FkoXSpvm4gC4gQSOxPbQcuzlLha9JqxJ\n 8ZZ89\/20txUrRq9cYxk=\nDomainKey-Signature: a=rsa-sha1; c=nofws; q=dns; s=mandrill; d=mail115.us4.mandrillapp.com;\n b=X6qudHd4oOJvVQZcoAEUCJgB875SwzEO5UKf6NvpfqyCVjdaO79WdDulLlfNVELeuoK2m6alt2yw\n 5Qhp4TW5NegyFf6Ogr\/Hy0Lt411r\/0lRf0nyaVkqMM\/9g13B6D9CS092v70wshX8+qdyxK8fADw8\n kEelbCK2cEl0AGIeAeo=;\nReceived: from localhost (127.0.0.1) by mail115.us4.mandrillapp.com id hhl55a14i282 for <<EMAIL>>; Fri, 10 May 2013 19:28:20 +0000 (envelope-from <<EMAIL>>)\nDKIM-Signature: v=1; a=rsa-sha256; c=relaxed\/relaxed; d=c.mandrillapp.com; \n i=@c.mandrillapp.com; q=dns\/txt; s=mandrill; t=1368214100; h=From : \n Sender : Subject : List-Unsubscribe : To : Message-Id : Date : \n MIME-Version : Content-Type : From : Subject : Date : X-Mandrill-User : \n List-Unsubscribe; bh=y5Vz+RDcKZmWzRc9s0xUJR6k4APvBNktBqy1EhSWM8o=; \n b=PLAUIuw8zk8kG5tPkmcnSanElxt6I5lp5t32nSvzVQE7R8u0AmIEjeIDZEt31+Q9PWt+nY\n sHHRsXUQ9SZpndT9Bk++\/SmyA2ntU\/2AKuqDpPkMZiTqxmGF80Wz4JJgx2aCEB1LeLVmFFwB\n 5Nr\/LBSlsBlRcjT9QiWw0\/iRvCn74=\nFrom: <<EMAIL>>\nSender: <<EMAIL>>\nSubject: This is an example webhook message\nList-Unsubscribe: <mailto:<EMAIL>?subject=unsub>\nTo: <<EMAIL>>\nX-Report-Abuse: Please forward a copy of this message, including all headers, to <EMAIL>\nX-Mandrill-User: md_999\nMessage-Id: <<EMAIL>>\nDate: Fri, 10 May 2013 19:28:20 +0000\nMIME-Version: 1.0\nContent-Type: multipart\/alternative; boundary=\"_av-7r7zDhHxVEAo2yMWasfuFw\"\n\n--_av-7r7zDhHxVEAo2yMWasfuFw\nContent-Type: text\/plain; charset=utf-8\nContent-Transfer-Encoding: 7bit\n\nThis is an example inbound message.\n--_av-7r7zDhHxVEAo2yMWasfuFw\nContent-Type: text\/html; charset=utf-8\nContent-Transfer-Encoding: 7bit\n\n<p>This is an example inbound message.<\/p><img src=\"http:\/\/mandrillapp.com\/track\/open.php?u=999&id=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa&tags=_all,<EMAIL>\" height=\"1\" width=\"1\">\n--_av-7r7zDhHxVEAo2yMWasfuFw--","sender":null,"spam_report":{"matched_rules":[{"description":"RBL: Sender listed at http:\/\/www.dnswl.org\/, no","name":"RCVD_IN_DNSWL_NONE","score":0},{"description":null,"name":null,"score":0},{"description":"in iadb.isipp.com]","name":"listed","score":0},{"description":"RBL: Participates in the IADB system","name":"RCVD_IN_IADB_LISTED","score":-0.4},{"description":"RBL: ISIPP IADB lists as vouched-for sender","name":"RCVD_IN_IADB_VOUCHED","score":-2.2},{"description":"RBL: IADB: Sender publishes SPF record","name":"RCVD_IN_IADB_SPF","score":0},{"description":"RBL: IADB: Sender publishes Sender ID record","name":"RCVD_IN_IADB_SENDERID","score":0},{"description":"RBL: IADB: Sender publishes Domain Keys record","name":"RCVD_IN_IADB_DK","score":-0.2},{"description":"RBL: IADB: Sender has reverse DNS record","name":"RCVD_IN_IADB_RDNS","score":-0.2},{"description":"SPF: HELO matches SPF record","name":"SPF_HELO_PASS","score":0},{"description":"BODY: HTML included in message","name":"HTML_MESSAGE","score":0},{"description":"BODY: HTML: images with 0-400 bytes of words","name":"HTML_IMAGE_ONLY_04","score":0.3},{"description":"Message has a DKIM or DK signature, not necessarily valid","name":"DKIM_SIGNED","score":0.1},{"description":"Message has at least one valid DKIM or DK signature","name":"DKIM_VALID","score":-0.1}],"score":-2.6},"spf":{"detail":"sender SPF authorized","result":"pass"},"subject":"This is an example webhook message","tags":[],"template":null,"text":"This is an example inbound message.\n","text_flowed":false,"to":[["<EMAIL>",null]]},"ts":1368214102}]
BODY;

        $result = (new Parser())->parseWebhookRequest(
            ['mandrill_events' => addslashes($mandrillBody)], // Have to add slashes to match wordpress's magic...
            new HeaderBag(['User-Agent' => 'Mandrill-Webhook'])
        )[0];

        $this->assertArrayHasKey('event', $result);
        $this->assertArrayHasKey('msg', $result);
        $this->assertEquals('inbound', $result['event']);
        $this->assertEquals('<EMAIL>', $result['msg']['email']);
    }

    public function testSendgridIdentification()
    {
        $headers = <<<HEADERS
Received: by mx0035p1mdw1.sendgrid.net with SMTP id rNjGa4wiQ2 Fri, 05 Apr 2019 18:35:35 +0000 (UTC)
Received: from mail-pg1-f176.google.com (mail-pg1-f176.google.com [209.85.215.176]) by mx0035p1mdw1.sendgrid.net (Postfix) with ESMTPS id 682ED74074A for <<EMAIL>>; Fri,  5 Apr 2019 18:35:35 +0000 (UTC)
Received: by mail-pg1-f176.google.com with SMTP id e6so3522554pgc.4 for <<EMAIL>>; Fri, 05 Apr 2019 11:35:35 -0700 (PDT)
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=salesfloor-net.20150623.gappssmtp.com; s=20150623; h=mime-version:references:in-reply-to:from:date:message-id:subject:to :cc; bh=4HBKUfW5DKn3bvHhj5Ye0SF2GglQvd958wvUascVArQ=; b=jGxVQDL6QRer6Z1/KJ/3ZyW8XPDWQhGN8rEXMN+2vDv9qc72J02nqcIvgyga1BJZ96 8t3l4mqjnvBbJDD46p1HHuftPriX50+0L2+FrXOGv9YpBkfUk0J3cZH3O/DDpXqrG2mR RALGL4aS4pOtu9TFtJQoA256S1bxppNin4hK49/qoNRnRUOmb/2NsvFCdPFL8fspFqno zIEBu27ia3qSmOtPnMYE4kxqPjIy31ThxEcHEdZDPp7liyBfVhBj+97ARVGcqN+KNA77 19a6OkgcEijC4yUvQ3NLHgXUerNkRUw5N5A5H/h0+MUO6OCMy8W5c+HxrkFvQ5QnlswJ TiFA==
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=1e100.net; s=20161025; h=x-gm-message-state:mime-version:references:in-reply-to:from:date :message-id:subject:to:cc; bh=4HBKUfW5DKn3bvHhj5Ye0SF2GglQvd958wvUascVArQ=; b=G6x2huxCYo6S3p5elEYUawqoqRaZBSV4c1NqF1QweEufuVKxIcJVroiGIf389T2Cqt koRPie8FL9CbZ4YPLd5vqGIXfWA0uKFC16b2RScjLhm3SuJBvsBQdVjyN8AL4kMksYMC Cv2ZISfgWS5FLxZkJKY9Mknai7++Ib20+bkUIhwsvN4ZyF7FmmLLnm2DW/bOqn9c7V6O fGKKsSRBQhiFlH6n8IDcDF/0EZ3ZW5gmQApxBCKqNqxyTG899ji0oJOtylD6qEhrLSzU WM0yL0eH8BlVBhdFEijC+dA9H+hrNGB/9KX01rVAMEzBOLXL127bw7Dok5+qM5UFbHxc HPKQ==
X-Gm-Message-State: APjAAAX0ZU+JUMBzvV7JjCvUw02dSrxF+D8lQr/f9OT0JugjSz2EsdaR etudpuHzuOKYDEK83wVAjO3EZ4T70ijCVte5t8Rt12CVp8/Qwg==
X-Google-Smtp-Source: APXvYqwDyHn94yM5t3tYzGkMfvUdU69gpUmJxcxUpJuIzQWdne9/bZzyxDU3RYa1jB96U2lBqqDjqEDY+lf0ew6kaUE=
X-Received: by 2002:a62:1d94:: with SMTP id d142mr13818908pfd.83.1554489334463; Fri, 05 Apr 2019 11:35:34 -0700 (PDT)
MIME-Version: 1.0
References: <<EMAIL>>
In-Reply-To: <<EMAIL>>
From: Samuel Schmidt <<EMAIL>>
Date: Fri, 5 Apr 2019 14:35:18 -0400
Message-ID: <<EMAIL>>
Subject: Re: hello
To: "Reggie Cedric, Saks" <<EMAIL>>
Cc: <EMAIL>
HEADERS;

        $result = (new Parser())->parseWebhookRequest(
            [
                'text' => 'hello',
                'html' => '<br/> hello',
                'headers' => $headers,
                'to' => '<EMAIL>',
                'subject' => 'This is a subject',
                'dkim' => '{@salesfloor-net.20150623.gappssmtp.com : pass}',
                'SPF' => 'softfail',
                'spamscore' => 50,
                'charsets' => '{"to":"UTF-8","html":"UTF-8","subject":"UTF-8","from":"UTF-8","text":"UTF-8"}'
            ],
            new HeaderBag(['User-Agent' => 'Sendlib'])
        );

        $this->assertCount(1, $result);
        $body = $result[0];

        $this->assertEquals('hello', $body['msg']['text']);
        $this->assertEquals('<br/> hello', $body['msg']['html']);
        $this->assertEquals('<EMAIL>', $body['msg']['from_email']);
        $this->assertEquals('Samuel Schmidt', $body['msg']['from_name']);
        $this->assertEquals('<EMAIL>', $body['msg']['to'][0][0]);
        $this->assertTrue($body['msg']['dkim']['signed']);
        $this->assertTrue($body['msg']['dkim']['valid']);
        $this->assertEquals('fail', $body['msg']['spf']['result']);
    }
}
