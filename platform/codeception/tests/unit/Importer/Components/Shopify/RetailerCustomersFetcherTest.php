<?php

declare(strict_types=1);

use Codeception\Stub;
use Salesfloor\Services\Shopify;
use SF\unit\Configs\BaseConfigs;
use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Importer\Components\LastUpdatedDetector;
use Salesfloor\Services\Importer\Components\Fetcher\Api\Shopify\RetailerCustomers;

class RetailerCustomersFetcherTest extends BaseConfigs
{
    public function testImportFilename()
    {
        $fetcher = new RetailerCustomers(
            Stub::makeEmpty(Shopify::class),
            Stub::makeEmpty(LastUpdatedDetector::class),
            Stub::makeEmpty(\Psr\Log\LoggerInterface::class),
            $this->getConfigs(Loader::SOURCE_API, 'tests-dev')
        );

        $this->assertRegExp('/shopify_api_retailercustomers_[\d]{14}/', $fetcher->getImportFilename());
    }

    public function testStreamCreationLoadEverything()
    {
        $cursorPaginated = function ($parameters) use (&$page) {
            // This doesn't exactly match Shopify's data,
            // but we don't care in this test. We just want to see that fetching works. Data validation is in the ingestor.
            $data = $this->getCustomersData();

            foreach ($data['customers'] as $customer) {
                yield [$customer];
            }
        };

        /** @var Shopify $shopify */
        $shopify = Stub::makeEmpty(Shopify::class, [
            'cursorPaginated' => $cursorPaginated
        ]);

        $fetcher = new RetailerCustomers(
            $shopify,
            $this->getLastUpdatedDetectorStub(),
            Stub::makeEmpty(\Psr\Log\LoggerInterface::class),
            $this->getConfigs(Loader::SOURCE_API, 'tests-dev'),
        );
        foreach ($fetcher->getStreamInfo() as $customerCollection) {
            // We're not going to test the actual contents, just that something is present
            // It's passed straight through to the ingestor, with no additional processing
            // so there's no point in testing that. Processing tests will happen on the ingestor.
            $this->assertNotEmpty($customerCollection);
        }
    }

    /**
     * @param null $timestamp
     * @return LastUpdatedDetector
     * @throws Exception
     */
    private function getLastUpdatedDetectorStub($timestamp = null)
    {
        return Stub::makeEmpty(
            LastUpdatedDetector::class,
            [
                'getLastUpdatedDateTime' => function ($tableName) use ($timestamp) {
                    return $timestamp;
                }
            ]
        );
    }

    private function getCustomersData(): mixed
    {
        //This doesn't exactly match shopify's data, but we don't
        // care in this test. We just want to see that fetching and
        // paging works. Data validation is in the ingestor.
        $data = json_decode(
            '{
   "customers":[
      {
         "id":207119551,
         "email":"<EMAIL>",
         "accepts_marketing":false,
         "created_at":"2019-02-05T12:00:00-05:00",
         "updated_at":"2019-02-05T12:00:00-05:00",
         "first_name":"Bob",
         "last_name":"Norman",
         "orders_count":1
      },
      {
         "id":207119661,
         "email":"<EMAIL>",
         "accepts_marketing":false,
         "created_at":"2019-02-15T12:00:00-05:00",
         "updated_at":"2019-02-15T15:00:00-05:00",
         "first_name":"John",
         "last_name":"Davis",
         "orders_count":1
      }
   ]
}',
            true
        );
        return $data;
    }
}
