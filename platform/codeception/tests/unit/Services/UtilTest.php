<?php

declare(strict_types=1);

namespace SF\unit\Services;

use Codeception\Test\Unit;
use Codeception\Util\Stub;
use Salesfloor\Services\Util;
use SF\unit\BaseUnit;
use Silex\Application;

/**
 *
 */
class UtilTest extends BaseUnit
{
    public function testReplaceParamsNew()
    {
        $app = $this->make(Application::class);

        $util = new Util($app);

        $location = "https://example.com";

        $newLocation = $util->replaceQueryStringParameter($location, [
            "new" => 1
        ]);

        $this->assertEquals("https://example.com?new=1", $newLocation);
    }

    public function testReplaceParamsExist()
    {
        $app = $this->make(Application::class);

        $util = new Util($app);

        $location = "https://example.com?new=4";

        $newLocation = $util->replaceQueryStringParameter($location, [
            "new" => 1
        ]);

        $this->assertEquals("https://example.com?new=1", $newLocation);
    }

    public function testReplaceParamsMultipleNew()
    {
        $app = $this->make(Application::class);

        $util = new Util($app);

        $location = "https://example.com";

        $newLocation = $util->replaceQueryStringParameter($location, [
            "new1" => 1,
            "new2" => 2,
        ]);

        $this->assertEquals("https://example.com?new1=1&new2=2", $newLocation);
    }

    public function testReplaceParamsDuplicate()
    {
        $app = $this->make(Application::class);

        $util = new Util($app);

        $location = "https://example.com";

        $newLocation = $util->replaceQueryStringParameter($location, [
            "new1" => 1,
            "new1" => 2,
        ]);

        $this->assertEquals("https://example.com?new1=2", $newLocation);
    }

    public function testReplaceParamsNewWithHash()
    {
        $app = $this->make(Application::class);

        $util = new Util($app);

        $location = "https://example.com#gotosomewhere";

        $newLocation = $util->replaceQueryStringParameter($location, [
            "new1" => 2,
        ]);

        $this->assertEquals("https://example.com?new1=2#gotosomewhere", $newLocation);
    }

    public function testIsValidSalePrice()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $this->assertTrue($util->isValidSalePrice(0.1));
        $this->assertTrue($util->isValidSalePrice(0.01));
        $this->assertTrue($util->isValidSalePrice(1));
        $this->assertTrue($util->isValidSalePrice(1.11));
        $this->assertTrue($util->isValidSalePrice('9.99'));

        $this->assertFalse($util->isValidSalePrice(0.0));
        $this->assertFalse($util->isValidSalePrice(0.00));
        $this->assertFalse($util->isValidSalePrice(-1.1));
        $this->assertFalse($util->isValidSalePrice('-1.1'));
        $this->assertFalse($util->isValidSalePrice('abc'));
        $this->assertFalse($util->isValidSalePrice(''));
        $this->assertFalse($util->isValidSalePrice(null));
    }

    public function testImporterCleanValue()
    {
        $this->tester->wantTo("test import clean value");

        $app  = $this->make(Application::class);
        $util = new Util($app);

        // An edge case happen on PRD env, not sure the reason, see another test for more details: functional\Services\MySQLConnection\CsvBufferCest::testLoadDataInfile
        $input1 = "This bundle consists of: <br /><br />1 x Clynelish Reserve GOT House Tyrell Single Malt Scotch Whisky 700mL<br />House Tyrell of Highgarden rules over the Reach, the lush and fertile region of Westeros. Like the Reach, Clynelish is positioned among green pastures and rolling hills, with scenic views of the North Sea. This vibrant, golden Scotch is light and floral, like House Tyrell, it’s not to be underestimated with its underlying complex combination of Highland and maritime qualities.<br /><br />1 x The Singleton Of Glendullan Select GOT Tully Single Malt Whisky 700mL<br />House Tully located at Riverrun, rules as the lord of the River lands. The power of water flows through both House Tully and The Singleton Glendullan Select as it is made on the banks of the River Fiddich in the wooded hills of Dufftown. Here they harnessed the water that flowed through the land utilizing a water wheel to power the entire distillery.<br /><br />1 x Talisker Select Reserve GOT Greyjoy Single Malt Whisky 700mL<br />House Greyjoy rules the Iron Islands and worships the Drowned God. Talisker was a natural pair for House Greyjoy as this single malt is distilled on the shores of the Isle of Skye, one of the most remote and rugged areas of Scotland. The layered flavours and signature maritime character of Talisker Select Reserve are the result of its wave-battered shores. This liquid is an intense smoky single malt Scotch with spicy, powerful and sweet elements combined with maritime flavours.<br /><br />1 x Royal Lochnagar 12YO GOT House Baratheon Single Malt Scotch Whisky 700mL<br />Royal lineage drives the iconic pairing between House Baratheon and Royal Lochnagar . Similar to Robert Baratheon ruling the Seven Kingdoms upon the Iron Throne, Royal Lochnagar was deemed a whisky worthy of a royal family as it was granted a Royal Warrant after Queen Victoria and Prince Albert visited the distillery in 1848. Balanced with delicate fruits and spices, this taste of royalty is best enjoyed neat.<br /><br />1 x Oban Bay Reserve GOT The Night&apos;s Watch Single Malt Scotch Whisky 700mL<br />The Oban distillery sits beneath the steep cliff that overlooks the bay in the frontier between the west Highlands and the Islands of Scotland, separating land and sea, just as Castle Black, home of The Night’s Watch, sits between Westeros and the lands beyond The Wall. The liquid’s richness is balanced with a woody, spicy dryness that The Night’s Watch could enjoy even on the coldest of nights.<br /><br />1 x Lagavulin 9YO GOT House Lannister Single Malt Scotch Whisky 700mL<br />Lagavulin is one of the most legendary single malt brands and has been crafted on the shores of Islay for more than 200 years mirroring the meticulous calculation and tenacity employed by the Lannister’s in their rise to conquer the Iron Throne. This single malt whisky is a roaring single malt that recalls the Lannister’s riches and is best served neat or with a single drop of water.<br /><br />1 x Dalwhinnie Winter&apos;s Frost GOT House Stark Highland Single Malt Scotch Whisky 700mL<br />House Stark’s resilience, strength and ability to thrive under the most intense situations are greatly shaped by Winterfell’s frigid temperatures. Dalwhinnie , known for being one of the highest distilleries in all of Scotland is cold and remote much like The North where House Stark calls home, making the two an iconic pairing. Extreme conditions are responsible for shaping the signature Dalwhinnie Winter’s Frost honeyed sweetness and spicy warmth. Naturally, it’s best served chilled or over ice.<br /><br />1 x Cardhu Gold Reserve GOT House Targaryen Single Malt Scotch Whisky 700mL<br />Fueled by the same fiery spirit of the fierce female leadership of Daenerys Targaryen, this single malt celebrates legendary women and their unwavering perseverance. The Cardhu Distillery was pioneered by Helen Cumming and her daughter in law Elizabeth during the 1800s, a time when the whisky industry was almost entirely male dominated.";
        $expect1 = "This bundle consists of: 1 x Clynelish Reserve GOT House Tyrell Single Malt Scotch Whisky 700mL House Tyrell of Highgarden rules over the Reach, the lush and fertile region of Westeros. Like the Reach, Clynelish is positioned among green pastures and rolling hills, with scenic views of the North Sea. This vibrant, golden Scotch is light and floral, like House Tyrell, it’s not to be underestimated with its underlying complex combination of Highland and maritime qualities. 1 x The Singleton Of Glendullan Select GOT Tully Single Malt Whisky 700mL House Tully located at Riverrun, rules as the lord of the River lands. The power of water flows through both House Tully and The Singleton Glendullan Select as it is made on the banks of the River Fiddich in the wooded hills of Dufftown. Here they harnessed the water that flowed through the land utilizing a water wheel to power the entire distillery. 1 x Talisker Select Reserve GOT Greyjoy Single Malt Whisky 700mL House Greyjoy rules the Iron Islands and worships the Drowned God. Talisker was a natural pair for House Greyjoy as this single malt is distilled on the shores of the Isle of Skye, one of the most remote and rugged areas of Scotland. The layered flavours and signature maritime character of Talisker Select Reserve are the result of its wave-battered shores. This liquid is an intense smoky single malt Scotch with spicy, powerful and sweet elements combined with maritime flavours. 1 x Royal Lochnagar 12YO GOT House Baratheon Single Malt Scotch Whisky 700mL Royal lineage drives the iconic pairing between House Baratheon and Royal Lochnagar . Similar to Robert Baratheon ruling the Seven Kingdoms upon the Iron Throne, Royal Lochnagar was deemed a whisky worthy of a royal family as it was granted a Royal Warrant after Queen Victoria and Prince Albert visited the distillery in 1848. Balanced with delicate fruits and spices, this taste of royalty is best enjoyed neat. 1 x Oban Bay Reserve GOT The Night&apos;s Watch Single Malt Scotch Whisky 700mL The Oban distillery sits beneath the steep cliff that overlooks the bay in the frontier between the west Highlands and the Islands of Scotland, separating land and sea, just as Castle Black, home of The Night’s Watch, sits between Westeros and the lands beyond The Wall. The liquid’s richness is balanced with a woody, spicy dryness that The Night’s Watch could enjoy even on the coldest of nights. 1 x Lagavulin 9YO GOT House Lannister Single Malt Scotch Whisky 700mL Lagavulin is one of the most legendary single malt brands and has been crafted on the shores of Islay for more than 200 years mirroring the meticulous calculation and tenacity employed by the Lannister’s in their rise to conquer the Iron Throne. This single malt whisky is a roaring single malt that recalls the Lannister’s riches and is best served neat or with a single drop of water. 1 x Dalwhinnie Winter&apos;s Frost GOT House Stark Highland Single Malt Scotch Whisky 700mL House Stark’s resilience, strength and ability to thrive under the most intense situations are greatly shaped by Winterfell’s frigid temperatures. Dalwhinnie , known for being one of the highest distilleries in all of Scotland is cold and remote much like The North where House Stark calls home, making the two an iconic pairing. Extreme conditions are responsible for shaping the signature Dalwhinnie Winter’s Frost honeyed sweetness and spicy warmth. Naturally, it’s best served chilled or over ice. 1 x Cardhu Gold Reserve GOT House Targaryen Single Malt Scotch Whisky 700mL Fueled by the same fiery spirit of the fierce female leadership of Daenerys Targaryen, this single malt celebrates legendary women and their unwavering perseverance. The Cardhu Distillery was pioneered by Helen Cumming and her daughter in law Elizabeth during the 1800s, a time when the whisky industry was almost entirely male dominated.";
        $this->assertEquals("$expect1", $util->cleanUpValue($input1));

        $input2 = "test test";
        $this->assertEquals("test test", $util->cleanUpValue($input2));
    }

    public function testIsValidPriceWithCurrency()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $this->assertEquals('$0.10', $util->formatPrice(0.1, 'CAD', 'en_CA', 2, true));
        $this->assertEquals('$0.10', $util->formatPrice(0.10, 'CAD', 'en_CA', 2, true));
        $this->assertEquals('$0.10', $util->formatPrice(0.10, 'CAD', 'en_CA', 2, false));

        $this->assertEquals('$0.11', $util->formatPrice(0.11, 'CAD', 'en_CA', 0, false));
        $this->assertEquals('$0.10', $util->formatPrice(0.10, 'CAD', 'en_CA', 0, true));

        $this->assertEquals('$25.1', $util->formatPrice(25.10, 'CAD', 'en_CA', 1, true));
        $this->assertEquals('$25.0', $util->formatPrice(25.01, 'CAD', 'en_CA', 1, true));
        $this->assertEquals('$25.0', $util->formatPrice(24.99, 'CAD', 'en_CA', 1, true));
        $this->assertEquals('$25', $util->formatPrice(25.00, 'CAD', 'en_CA', 1, true));

        $this->assertEquals('$25.10', $util->formatPrice(25.10, 'CAD', 'en_CA', 2, true));
        $this->assertEquals('$25', $util->formatPrice(25.00, 'CAD', 'en_CA', 2, true));

        $this->assertEquals('¥3,000', $util->formatPrice(3000.00, 'JPY', 'jp_JP', 0, true));
        $this->assertEquals('¥3,000', $util->formatPrice(3000.10, 'JPY', 'jp_JP', 0, true));
        $this->assertEquals('¥3,001', $util->formatPrice(3000.90, 'JPY', 'jp_JP', 0, true));
        $this->assertEquals('¥3,000.2', $util->formatPrice(3000.19, 'JPY', 'jp_JP', 1, true));

        $this->assertEquals('2 €', $util->formatPrice(1.99, 'Euro', 'de-DE', 0, true));
        $this->assertEquals('1,99 €', $util->formatPrice(1.99, 'Euro', 'de-DE', 2, true));

        $this->assertEquals('3.000,20 €', $util->formatPrice(3000.20, 'Euro', 'de-DE', 2, true));
        $this->assertEquals('3.000 €', $util->formatPrice(3000.00, 'Euro', 'de-DE', 2, true));
        $this->assertEquals('3.000,90 €', $util->formatPrice(3000.90, 'Euro', 'de-DE', 2, true));

        $this->assertEquals('3.000 €', $util->formatPrice(3000.20, 'Euro', 'de-DE', 0, false));
        $this->assertEquals('3.001 €', $util->formatPrice(3000.90, 'Euro', 'de-DE', 0, false));
        $this->assertEquals('3.000 €', $util->formatPrice(3000.00, 'Euro', 'de-DE', 0, false));
    }

    public function testConvertLocalToUtc()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $dt = $util->convertLocalToUtc('2022-10-18 00:00:00', 'America/New_York');

        $this->assertEquals('2022-10-18 04:00:00', $dt[0] . ' ' . $dt[1]);
    }

    public function testIsTimezoneIdentifiers()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $this->assertTrue($util->isTimezoneIdentifiers('UTC'));
        $this->assertTrue($util->isTimezoneIdentifiers('America/New_York'));
        $this->assertTrue($util->isTimezoneIdentifiers('America/Montreal'));

        $this->assertNotTrue($util->isTimezoneIdentifiers('xxx_yyy'));
    }

    public function testLimitImageSize()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $maxFileSize = 2 * 1024 * 1024;
        $filePath    = __DIR__ . '/Fixtures/colorful.jpg';

        list($width, $height) = getimagesize($filePath);
        $widthRatio = $width / $height;

        // Resize the image
        $resized       = $util->limitImageFileSize($filePath, $maxFileSize);
        $resizedWidth  = imagesx($resized);
        $resizedHeight = imagesy($resized);
        $newWidthRatio = $resizedWidth / $resizedHeight;

        // Check the aspect ratio of the image
        $ratioDifference = abs($widthRatio - $newWidthRatio);
        $this->assertLessThan(1 / 100, $ratioDifference, "The aspect ratio should be basically the same");


        // Create tmp file to get the file size
        $localPath = sys_get_temp_dir() . '/' . 'testUtilImage.jpg';
        if (file_exists($localPath)) {
            unlink($localPath);
        }
        imagejpeg($resized, $localPath);
        $fileSize = filesize($localPath);
        unlink($localPath);

        $this->assertLessThan($maxFileSize, $fileSize, "File size must be reduced to below the limit");
    }

    public function testReplaceWhenContainsMojibake()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);
        $stringTotest = <<<STT
�ï¿½ï¿½4ï¿½hï¿½Ò¢
temrina 😀
i'm a happy man with 😀 conciousness lol %%$##@@.????<.opw9128÷
あseeあŽ	€×
un à travers
dans la soirée.
i'm a happy man with 😀 conciousness ¿½ï¿½4ï¿½hï¿½Ò¢
¿½ï¿½4ï¿½hï¿½Ò¢i'm a happy man with 😀
STT;
        $output = <<<OUT
[=== BAD INPUT OMITTED ===]
temrina 😀
i'm a happy man with 😀 conciousness lol %%$##@@.????<.opw9128÷
あseeあŽ	€×
un à travers
dans la soirée.
[=== BAD INPUT OMITTED ===]
[=== BAD INPUT OMITTED ===]
OUT;
        $parsed = array_map(function ($v) use ($util) {
            return $util->replaceWhenContainsMojibake($v, '[=== BAD INPUT OMITTED ===]');
        }, explode("\n", $stringTotest));
        $this->assertEquals($output, implode("\n", $parsed));
    }

    public function testTruncateString()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);
        $stringToTest = "i'm a happy man with 😀 conciousness lol";
        $output = "i'm a happy man with 😀\n<=== MESSAGE TRUNCATED ===>";
        $this->assertEquals($output, $util->truncateString($stringToTest, 22, "\n" . '<=== MESSAGE TRUNCATED ===>'));
    }

    public function testTruncateToNewLineString()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);
        $stringToTest = "i'm a happy man\n with 😀 conciousness lol";
        $output = "i'm a happy man\n<=== MESSAGE TRUNCATED ===>";
        $this->assertEquals($output, $util->truncateString($stringToTest, 22, "\n" . '<=== MESSAGE TRUNCATED ===>'));
    }

    public function testGetDays()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);
        $days = 3;
        // 01-02-03 ; we don't calculate based on seconds per day (86400) anymore.
        $this->assertEquals($days, $util->getDays('2019-01-01', '2019-01-03'));
    }

    public function testGetDaysInvalidDateOrdering()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);
        $days = 1; // default to one day
        $this->assertEquals($days, $util->getDays('2019-01-03', '2019-01-01'));
    }

    public function testGetDaysInvalidDate()
    {
        $app  = $this->make(Application::class);
        $util = new Util($app);
        $days = 1; // default to one day
        $this->assertEquals($days, $util->getDays('201901A-03', 'A stitch in time saves nine'));
    }

    public function testGetDaysToDateIsNull()
    {
        $app = $this->make(Application::class);
        $util = new Util($app);
        $days = (strtotime(date('Y-m-d')) - strtotime('2019-01-03')) / 86400;

        // We need to add one because the "today" is included now.
        // for example 2024-01-01 - 2024-01-02 will return 2 ; not one.
        $this->assertEquals($days + 1, $util->getDays('2019-01-03', null));

        $this->assertEquals(1, $util->getDays('2024-01-22', '2024-01-22'));
        $this->assertEquals(2, $util->getDays('2024-01-22', '2024-01-23'));
        $this->assertEquals(3, $util->getDays('2024-01-22', '2024-01-24'));
        $this->assertEquals(4, $util->getDays('2024-01-22', '2024-01-25'));
        $this->assertEquals(5, $util->getDays('2024-01-22', '2024-01-26'));
        $this->assertEquals(6, $util->getDays('2024-01-22', '2024-01-27'));
        $this->assertEquals(7, $util->getDays('2024-01-22', '2024-01-28'));
    }

    public function testGetDaysStartDateIsNull()
    {
        $app = $this->make(Application::class);
        $util = new Util($app);
        $days = 1;
        $this->assertEquals($days, $util->getDays(null, null));
    }

    public function testGetDaysWithDatetimeSameDay()
    {
        $app = $this->make(Application::class);
        $util = new Util($app);
        $days = 1;
        $this->assertEquals($days, $util->getDays('2019-01-03 13:00:00', '2019-01-03 23:59:59'));

        // Time is ignored, because we want the nb of days
        $this->assertEquals($days, $util->getDays('2019-01-03 23:00:00', '2019-01-03 13:59:59'));
    }

    public function testGetWorkingDays()
    {
        $app = $this->make(Application::class);
        $util = new Util($app);

        # 20/21 are weekend

        $this->assertEquals(0, $util->getWorkingDays('2024-01-20', '2024-01-20'));
        $this->assertEquals(0, $util->getWorkingDays('2024-01-20', '2024-01-21'));
        $this->assertEquals(0, $util->getWorkingDays('2024-01-21', '2024-01-21'));
        $this->assertEquals(1, $util->getWorkingDays('2024-01-19', '2024-01-20'));
        $this->assertEquals(1, $util->getWorkingDays('2024-01-19', '2024-01-21'));
        $this->assertEquals(1, $util->getWorkingDays('2024-01-22', '2024-01-22'));
        $this->assertEquals(2, $util->getWorkingDays('2024-01-22', '2024-01-23'));
        $this->assertEquals(3, $util->getWorkingDays('2024-01-22', '2024-01-24'));
        $this->assertEquals(4, $util->getWorkingDays('2024-01-22', '2024-01-25'));
        $this->assertEquals(5, $util->getWorkingDays('2024-01-22', '2024-01-26'));
        $this->assertEquals(5, $util->getWorkingDays('2024-01-22', '2024-01-27'));
        $this->assertEquals(5, $util->getWorkingDays('2024-01-22', '2024-01-28'));
        $this->assertEquals(6, $util->getWorkingDays('2024-01-22', '2024-01-29'));
        $this->assertEquals(7, $util->getWorkingDays('2024-01-22', '2024-01-30'));
    }

    public function testGetWorkingDaysWithHolidays()
    {
        $app = $this->make(Application::class);
        $util = new Util($app);

        # 20/21 are weekend

        $this->assertEquals(0, $util->getWorkingDays('2024-01-20', '2024-01-20', ['2024-01-22']));
        $this->assertEquals(0, $util->getWorkingDays('2024-01-20', '2024-01-21', ['2024-01-22']));
        $this->assertEquals(0, $util->getWorkingDays('2024-01-21', '2024-01-21', ['2024-01-22']));
        $this->assertEquals(1, $util->getWorkingDays('2024-01-19', '2024-01-20', ['2024-01-22']));
        $this->assertEquals(1, $util->getWorkingDays('2024-01-19', '2024-01-21', ['2024-01-22']));
        $this->assertEquals(0, $util->getWorkingDays('2024-01-22', '2024-01-22', ['2024-01-22']));
        $this->assertEquals(1, $util->getWorkingDays('2024-01-22', '2024-01-23', ['2024-01-22']));
        $this->assertEquals(2, $util->getWorkingDays('2024-01-22', '2024-01-24', ['2024-01-22']));
        $this->assertEquals(3, $util->getWorkingDays('2024-01-22', '2024-01-25', ['2024-01-22']));
        $this->assertEquals(4, $util->getWorkingDays('2024-01-22', '2024-01-26', ['2024-01-22']));
        $this->assertEquals(4, $util->getWorkingDays('2024-01-22', '2024-01-27', ['2024-01-22']));
        $this->assertEquals(4, $util->getWorkingDays('2024-01-22', '2024-01-28', ['2024-01-22']));
        $this->assertEquals(5, $util->getWorkingDays('2024-01-22', '2024-01-29', ['2024-01-22']));
        $this->assertEquals(6, $util->getWorkingDays('2024-01-22', '2024-01-30', ['2024-01-22']));
    }

    public function testExcelColumnLetter()
    {
        $this->tester->wantTo("test excel column number could convert to letter easily");
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $this->assertEquals(Util::excelColumnLetter(0), 'A');
        $this->assertEquals(Util::excelColumnLetter(26), 'AA');
        $this->assertEquals(Util::excelColumnLetter(1000), 'ALM');
        $this->assertEquals(Util::excelColumnLetter(1001), 'ALN');
    }

    public function testContainsHtml()
    {
        $this->tester->wantTo("test if the text has HTML");

        $arrShouldContainHtml = [
            '<test>',
            '<book-title>',
            '<book-_title>',
            '<br/>',
            '<br>',
            '<a>',
            '<test name="<>">',
            '<test name=\'<>"\'>',
            '<test name=\'<>\'>',
            '<test name="<\"> ">',
            '<test name="<\' >',
            '<test --data-- ="<\"> ">',
            '<test --data-- = "<\"> " href="name">',
            '<test --data:html-- =\'\\\'\'>',
            '<test --data-- =\'\\\'\'>',
            '<!-- data here -->',
            '<!-- data <a> here -->',
            '<!---->',
            '<test data=\'\\\'\'>',
            '<test name="sdfsf\"sdfsdf">',
            '<test name="<\">',
            '<test name=\'\'>',
            '<h1>Test Header</h1>',
            '<p>This is a <b>test</b> string.</p>',
        ];

        $shouldNotContainHtml = [
            'dsf',
            '<3br/>',
            '<--  -->',
            '---> dgdgd sdgdgf <---',
            '<3>',
            '<3',
            '<>',
            '< >',
        ];

        foreach ($arrShouldContainHtml as $str) {
            $this->assertEquals(Util::containsHtml($str), true, "$str should contain html");
        }

        foreach ($shouldNotContainHtml as $str) {
            $this->assertEquals(Util::containsHtml($str), false, "$str should not contain html");
        }
    }

    public function testFormatTextForEmail()
    {
        $this->tester->wantTo("test text format function for email");
        $app  = $this->make(Application::class);
        $util = new Util($app);

        $htmlText = "<p>This is a <b>test</b> string.</p>";
        $this->assertEquals($htmlText, $util->formatTextForEmail($htmlText));

        $plainText = "This is a test string.";
        $this->assertEquals($plainText, $util->formatTextForEmail($plainText));

        $plainTextParagraph = "Line 1\nLine 2 Line 3";
        $this->assertEquals("Line 1<br />\nLine 2 Line 3", $util->formatTextForEmail($plainTextParagraph));

        $brokenTagText = "<This is a test string.";
        $this->assertEquals($brokenTagText, $util->formatTextForEmail($brokenTagText));

        $brokenTagText2 = "This is a test string.>";
        $this->assertEquals($brokenTagText2, $util->formatTextForEmail($brokenTagText2));
    }

    /**
     * Test clean up row encoding
     * Further tests for this can be found in ./robo test:functional Services/Sanitize/SanitizeCest:testSanitizingUtf8Encoding
     * @return void
     */
    public function testCleanUpRowEncoding()
    {
        $this->tester->wantTo("Test that cleanUpRowEncoding will convert both strings and arrays");

        // Define a set of strings to attempt to convert
        $rawIsoString = call_user_func_array('pack', array_merge(array('C*'), range(0x00, 0xFF)));
        $convertedIso = iconv('iso8859-1', 'utf-8', $rawIsoString);
        $emoji = '😀😃😄😁😆😅';
        $illegal = chr(0) . html_entity_decode("&#xFFFD;") . html_entity_decode("&#xFFFA;");
        $rawStrings = [
            join('|', [$emoji]),
            join('|', [$convertedIso, $emoji]),
            join('|', [$rawIsoString, $emoji]),
            join('|', [$illegal, $convertedIso, $emoji]),
            join('|', [$illegal, $rawIsoString, $convertedIso, $emoji]),
            join('|', [$convertedIso, $rawIsoString, $illegal]),
            join('|', [$illegal]),
            join('|', [$rawIsoString]),
        ];
        $encodedResults = json_decode('[
            "f09f9880f09f9883f09f9884f09f9881f09f9886f09f9885",
            "3f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7fc280c281c282c283c284c285c286c287c288c289c28ac28bc28cc28dc28ec28fc290c291c292c293c294c295c296c297c298c299c29ac29bc29cc29dc29ec29f20c2a1c2a2c2a3c2a4c2a5c2a6c2a7c2a8c2a9c2aac2abc2acc2adc2aec2afc2b0c2b1c2b2c2b3c2b4c2b5c2b6c2b7c2b8c2b9c2bac2bbc2bcc2bdc2bec2bfc380c381c382c383c384c385c386c387c388c389c38ac38bc38cc38dc38ec38fc390c391c392c393c394c395c396c397c398c399c39ac39bc39cc39dc39ec39fc3a0c3a1c3a2c3a3c3a4c3a5c3a6c3a7c3a8c3a9c3aac3abc3acc3adc3aec3afc3b0c3b1c3b2c3b3c3b4c3b5c3b6c3b7c3b8c3b9c3bac3bbc3bcc3bdc3bec3bf7cf09f9880f09f9883f09f9884f09f9881f09f9886f09f9885",
            "3f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7fc280c281c282c283c284c285c286c287c288c289c28ac28bc28cc28dc28ec28fc290c291c292c293c294c295c296c297c298c299c29ac29bc29cc29dc29ec29f20c2a1c2a2c2a3c2a4c2a5c2a6c2a7c2a8c2a9c2aac2abc2acc2adc2aec2afc2b0c2b1c2b2c2b3c2b4c2b5c2b6c2b7c2b8c2b9c2bac2bbc2bcc2bdc2bec2bfc380c381c382c383c384c385c386c387c388c389c38ac38bc38cc38dc38ec38fc390c391c392c393c394c395c396c397c398c399c39ac39bc39cc39dc39ec39fc3a0c3a1c3a2c3a3c3a4c3a5c3a6c3a7c3a8c3a9c3aac3abc3acc3adc3aec3afc3b0c3b1c3b2c3b3c3b4c3b5c3b6c3b7c3b8c3b9c3bac3bbc3bcc3bdc3bec3bf7cc3b0c29fc298c280c3b0c29fc298c283c3b0c29fc298c284c3b0c29fc298c281c3b0c29fc298c286c3b0c29fc298c285",
            "3f3f3f7c3f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7fc280c281c282c283c284c285c286c287c288c289c28ac28bc28cc28dc28ec28fc290c291c292c293c294c295c296c297c298c299c29ac29bc29cc29dc29ec29f20c2a1c2a2c2a3c2a4c2a5c2a6c2a7c2a8c2a9c2aac2abc2acc2adc2aec2afc2b0c2b1c2b2c2b3c2b4c2b5c2b6c2b7c2b8c2b9c2bac2bbc2bcc2bdc2bec2bfc380c381c382c383c384c385c386c387c388c389c38ac38bc38cc38dc38ec38fc390c391c392c393c394c395c396c397c398c399c39ac39bc39cc39dc39ec39fc3a0c3a1c3a2c3a3c3a4c3a5c3a6c3a7c3a8c3a9c3aac3abc3acc3adc3aec3afc3b0c3b1c3b2c3b3c3b4c3b5c3b6c3b7c3b8c3b9c3bac3bbc3bcc3bdc3bec3bf7cf09f9880f09f9883f09f9884f09f9881f09f9886f09f9885",
            "3fc3afc2bfc2bdc3afc2bfc2ba7c3f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7fc280c281c282c283c284c285c286c287c288c289c28ac28bc28cc28dc28ec28fc290c291c292c293c294c295c296c297c298c299c29ac29bc29cc29dc29ec29f20c2a1c2a2c2a3c2a4c2a5c2a6c2a7c2a8c2a9c2aac2abc2acc2adc2aec2afc2b0c2b1c2b2c2b3c2b4c2b5c2b6c2b7c2b8c2b9c2bac2bbc2bcc2bdc2bec2bfc380c381c382c383c384c385c386c387c388c389c38ac38bc38cc38dc38ec38fc390c391c392c393c394c395c396c397c398c399c39ac39bc39cc39dc39ec39fc3a0c3a1c3a2c3a3c3a4c3a5c3a6c3a7c3a8c3a9c3aac3abc3acc3adc3aec3afc3b0c3b1c3b2c3b3c3b4c3b5c3b6c3b7c3b8c3b9c3bac3bbc3bcc3bdc3bec3bf7c3f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7fc382c280c382c281c382c282c382c283c382c284c382c285c382c286c382c287c382c288c382c289c382c28ac382c28bc382c28cc382c28dc382c28ec382c28fc382c290c382c291c382c292c382c293c382c294c382c295c382c296c382c297c382c298c382c299c382c29ac382c29bc382c29cc382c29dc382c29ec382c29fc38220c382c2a1c382c2a2c382c2a3c382c2a4c382c2a5c382c2a6c382c2a7c382c2a8c382c2a9c382c2aac382c2abc382c2acc382c2adc382c2aec382c2afc382c2b0c382c2b1c382c2b2c382c2b3c382c2b4c382c2b5c382c2b6c382c2b7c382c2b8c382c2b9c382c2bac382c2bbc382c2bcc382c2bdc382c2bec382c2bfc383c280c383c281c383c282c383c283c383c284c383c285c383c286c383c287c383c288c383c289c383c28ac383c28bc383c28cc383c28dc383c28ec383c28fc383c290c383c291c383c292c383c293c383c294c383c295c383c296c383c297c383c298c383c299c383c29ac383c29bc383c29cc383c29dc383c29ec383c29fc38320c383c2a1c383c2a2c383c2a3c383c2a4c383c2a5c383c2a6c383c2a7c383c2a8c383c2a9c383c2aac383c2abc383c2acc383c2adc383c2aec383c2afc383c2b0c383c2b1c383c2b2c383c2b3c383c2b4c383c2b5c383c2b6c383c2b7c383c2b8c383c2b9c383c2bac383c2bbc383c2bcc383c2bdc383c2bec383c2bf7cc3b0c29fc298c280c3b0c29fc298c283c3b0c29fc298c284c3b0c29fc298c281c3b0c29fc298c286c3b0c29fc298c285",
            "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",
            "3f3f3f",
            "3f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7fc280c281c282c283c284c285c286c287c288c289c28ac28bc28cc28dc28ec28fc290c291c292c293c294c295c296c297c298c299c29ac29bc29cc29dc29ec29f20c2a1c2a2c2a3c2a4c2a5c2a6c2a7c2a8c2a9c2aac2abc2acc2adc2aec2afc2b0c2b1c2b2c2b3c2b4c2b5c2b6c2b7c2b8c2b9c2bac2bbc2bcc2bdc2bec2bfc380c381c382c383c384c385c386c387c388c389c38ac38bc38cc38dc38ec38fc390c391c392c393c394c395c396c397c398c399c39ac39bc39cc39dc39ec39fc3a0c3a1c3a2c3a3c3a4c3a5c3a6c3a7c3a8c3a9c3aac3abc3acc3adc3aec3afc3b0c3b1c3b2c3b3c3b4c3b5c3b6c3b7c3b8c3b9c3bac3bbc3bcc3bdc3bec3bf"
        ]', true);


        // test single values
        foreach ($rawStrings as $i => $rawStr) {
            $str = Util::cleanUpRowEncoding($rawStr);
            // Confirm the string is as expected
            $this->assertEquals($encodedResults[$i], bin2hex($str), "Expected string $i to be converted properly");
        }

        // test array
        $convertedRow = Util::cleanUpRowEncoding($rawStrings);
        foreach ($convertedRow as $i => $str) {
            // Confirm the string is as expected
            $this->assertEquals($encodedResults[$i], bin2hex($str), "Expected string $i to be converted properly");
        }
    }

    public function testParseUserAgent()
    {
        // Test empty user agent
        $result = Util::parseUserAgent('');
        $expected = [
            'device_type' => null,
            'os_name' => null,
            'os_version' => null,
            'browser_name' => null,
            'browser_version' => null
        ];
        $this->assertEquals($expected, $result);

        // Test Chrome on Windows
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('web', $result['device_type']);
        $this->assertEquals('Windows', $result['os_name']);
        $this->assertEquals('10.0', $result['os_version']);
        $this->assertEquals('Chrome', $result['browser_name']);
        $this->assertEquals('91.0.4472.124', $result['browser_version']);

        // Test Safari on macOS
        $userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('web', $result['device_type']);
        $this->assertEquals('macOS', $result['os_name']);
        $this->assertEquals('10.15.7', $result['os_version']);
        $this->assertEquals('Safari', $result['browser_name']);
        $this->assertEquals('605.1.15', $result['browser_version']);

        // Test Firefox on Linux
        $userAgent = 'Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('web', $result['device_type']);
        $this->assertEquals('Linux', $result['os_name']);
        $this->assertNull($result['os_version']);
        $this->assertEquals('Firefox', $result['browser_name']);
        $this->assertEquals('89.0', $result['browser_version']);

        // Test iPhone mobile
        $userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('mobile', $result['device_type']);
        $this->assertEquals('iOS', $result['os_name']);
        $this->assertEquals('14.6', $result['os_version']);
        $this->assertEquals('Safari', $result['browser_name']);
        $this->assertEquals('604.1', $result['browser_version']);

        // Test iPad tablet
        $userAgent = 'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('tablet', $result['device_type']);

        // Test Android mobile
        $userAgent = 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('mobile', $result['device_type']);
        $this->assertEquals('Android', $result['os_name']);
        $this->assertEquals('11', $result['os_version']);
        $this->assertEquals('Chrome', $result['browser_name']);
        $this->assertEquals('91.0.4472.120', $result['browser_version']);

        // Test Edge browser (modern Chromium-based Edge)
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('Edge', $result['browser_name']);
        $this->assertEquals('91.0.864.59', $result['browser_version']);

        // Test Opera browser
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.254';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('Opera', $result['browser_name']);
        $this->assertEquals('77.0.4054.254', $result['browser_version']);

        // Test BlackBerry mobile detection
        $userAgent = 'Mozilla/5.0 (BlackBerry; U; BlackBerry 9900; en) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.1.0.346 Mobile Safari/534.11+';
        $result = Util::parseUserAgent($userAgent);
        $this->assertEquals('mobile', $result['device_type']);
    }
}
