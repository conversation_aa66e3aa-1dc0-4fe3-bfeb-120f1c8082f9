<?php

declare(strict_types=1);

namespace SF\unit\Services\PrepareShareTest;

use Codeception\Test\Unit;
use Salesfloor\Services\PrepareShare;
use ReflectionClass;

define('WP_USE_THEMES', true);
define('ABSPATH', __DIR__ . '../../../../../instance-webserver/src/salesfloor/');
require_once ABSPATH . 'includes/publisher/Publisher.php';
require_once ABSPATH . 'includes/PostFilter.php';

class PrepareShareTest extends Unit
{
    public function testPushToSocialMediaAndMessageIsNotQueued(): void
    {
        $prepareShare = $this->getMockBuilder(prepareShare::class)
            ->disableOriginalConstructor()
            ->setMethodsExcept(['prepareMessage'])
            ->getMock();

        $prepareShare->expects($this->once())
            ->method('publish')
            ->willReturn([
                'success' => true,
                'data' => []
            ]);

        /** @var PrepareShare $prepareShare */
        $result = $prepareShare->prepareMessage('social', [
            'caption' => 'Test caption',
            'userId' => 1,
            'sanitizeAssetData' => [],
        ]);
        $this->assertTrue($result['success']);
    }

    public function testPushEmailToQueue(): void
    {
        $prepareShare = $this->getMockBuilder(prepareShare::class)
            ->disableOriginalConstructor()
            ->setMethodsExcept(['prepareMessage'])
            ->getMock();

        $prepareShare->expects($this->once())
            ->method('push');

        $this->setProperty([
            'obj' => $prepareShare,
            'prop' => 'isQueueEnabled',
            'value' => true
        ]);

        $result = $prepareShare->prepareMessage('email', []);
        $this->assertTrue($result['success']);
    }

    public function testPushEmailWithQueueNotEnabled(): void
    {
        $prepareShare = $this->getMockBuilder(prepareShare::class)
            ->disableOriginalConstructor()
            ->setMethodsExcept(['prepareMessage'])
            ->getMock();

        $this->setProperty([
            'obj' => $prepareShare,
            'prop' => 'isQueueEnabled',
            'value' => false
        ]);

        $result = $prepareShare->prepareMessage('email', []);
        $this->assertFalse($result['success']);
    }

    private function setProperty(array $params): void
    {
        $reflector = new ReflectionClass($params['obj']);
        $property = $reflector->getProperty($params['prop']);
        $property->setAccessible(true);
        $property->setValue($params['obj'], $params['value']);
    }
}
