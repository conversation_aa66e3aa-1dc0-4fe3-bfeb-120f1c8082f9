<?php

namespace SF\unit\Services;

use Codeception\Test\Unit;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;
use Salesfloor\Services\ExtendedInsertQueryBuilder;

class ExtendedInsertQueryBuilderTest extends Unit
{
    private function getBuilder($quoteResult = '')
    {
        $expr = new \Doctrine\DBAL\Query\Expression\ExpressionBuilder(
            $this->make(Connection::class, [
                'quote' => $quoteResult
            ])
        );

        $qb = $this->make(QueryBuilder::class, [
            'expr' => $expr
        ]);

        return new ExtendedInsertQueryBuilder(
            $qb
        );
    }

    public function testBasicInsertCreation()
    {
        $prep = $this->getBuilder()
            ->setTable('sf_test_table')
            ->addInsertFields(['email','name'])
            ->addValuesSet(['email' => '<EMAIL>','name' => 'sam'])
            ->prepare()
        ;

        $query = $prep['query'];
        $params = $prep['parameters'];

        $this->assertCount(2, $params);
        $keys = array_keys($params);
        $this->assertStringEndsWith('_email', $keys[0]);
        $this->assertEquals('<EMAIL>', $params[$keys[0]]);

        $this->assertStringEndsWith('_name', $keys[1]);
        $this->assertEquals('sam', $params[$keys[1]]);

        $this->assertStringStartsWith(
            'INSERT INTO `sf_test_table` (`email`,`name`) VALUES',
            $query
        );
    }

    public function testInsertCreationWithOnDuplicate()
    {
        $year = date('Y');

        $dupes = [
            'email' => true,
            'name' => true,
            'created' => $year
        ];

        $prep = $this->getBuilder("`$year`")
            ->setTable('sf_test_table')
            ->addInsertFields(['email','name','created'])
            ->addValuesSet(['email' => '<EMAIL>','name' => 'sam','created' => 'now'])
            ->addOnDuplicateUpdateFields($dupes)
            ->prepare()
        ;

        $query = $prep['query'];
        $params = $prep['parameters'];

        $this->assertCount(3, $params);
        $keys = array_keys($params);
        $this->assertStringEndsWith('_email', $keys[0]);
        $this->assertEquals('<EMAIL>', $params[$keys[0]]);

        $this->assertStringEndsWith('_name', $keys[1]);
        $this->assertEquals('sam', $params[$keys[1]]);

        $this->assertStringEndsWith('_created', $keys[2]);
        $this->assertEquals('now', $params[$keys[2]]);

        $this->assertStringStartsWith(
            'INSERT INTO `sf_test_table` (`email`,`name`,`created`) VALUES',
            $query
        );

        $currentYear = date('Y');

        $this->assertStringEndsWith(
            "ON DUPLICATE KEY UPDATE  `email` =  VALUES(`email`) , `name` =  VALUES(`name`) , `created` = `{$currentYear}`",
            $query
        );
    }
}
