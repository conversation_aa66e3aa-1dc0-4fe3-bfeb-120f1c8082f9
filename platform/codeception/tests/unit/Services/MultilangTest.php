<?php

namespace SF\unit\Services;

use Codeception\Test\Unit;
use DateTime;
use DateTimeZone;
use Salesfloor\Services\Cache\GenericCache;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\MySQLRepository;
use Symfony\Component\Translation\Translator;
use Codeception\Util\Stub;

/**
 *
 */
class MultilangTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /** @var Multilang */
    protected $multilang;

    protected function getMultilangService($additionalConfigs = [], $stubs = [])
    {
        $configs = [
            'retailer.i18n.is_enabled' => true,
            'sf.i18n.locales' => ['en_US', 'fr_CA'],
            'retailer.i18n.default_locale' => 'en_US'
        ];

        $configs = array_merge($configs, $additionalConfigs);

        $multilang = Stub::construct(
            Multilang::class,
            [
                $configs,
                $this->make(\Monolog\Logger::class),
                $this->make(Translator::class),
                $this->make(MySQLRepository::class),
                $this->make(GenericCache::class)
            ],
            $stubs
        );

        return $multilang;
    }

    public function testIsI18n()
    {
        $multilang = $this->getMultilangService();

        $this->assertTrue($multilang->isI18N());
    }

    public function testIsNotI18n()
    {
        $multilang = $this->getMultilangService([
            'retailer.i18n.is_enabled' => false
        ]);

        $this->assertFalse($multilang->isI18N());
    }

    public function testIsValidRetailerLocale()
    {
        $multilang = $this->getMultilangService();
        $this->assertTrue($multilang->isValidRetailerLocale('en_US'));
    }

    public function testIsNotValidRetailerLocale()
    {
        $multilang = $this->getMultilangService();
        $this->assertFalse($multilang->isValidRetailerLocale('fake_Locale'));
    }

    public function testGetRetailerDefaultLocale()
    {
        $multilang = $this->getMultilangService();
        $this->assertEquals('en_US', $multilang->getRetailerDefaultLocale());
    }

    public function testSanitizeLocaleAgainstValidLocale()
    {
        $multilang = $this->getMultilangService();
        $this->assertEquals(
            'fr_CA',
            $multilang->sanitizeLocale('fr_CA')
        );
    }

    public function testSanitizeLocaleAgainstInvalidLocale()
    {
        $multilang = $this->getMultilangService();
        $this->assertEquals(
            'en_US',
            $multilang->sanitizeLocale('fake_Locale')
        );
    }

    public function testSanitizeLocaleAgainstNotI18n()
    {
        $multilang = $this->getMultilangService([
            'retailer.i18n.is_enabled' => false
        ]);

        $this->assertEquals(
            'en_US',
            $multilang->sanitizeLocale('fake_Locale')
        );
    }

    public function testSanitizeLocaleByStore()
    {
        $multilang = $this->getMultilangService([
            'retailer.i18n.is_enabled' => true,
            'sf.i18n.locales' => ['en_US', 'fr_CA'],
            'retailer.i18n.default_locale' => 'en_US'
        ], [
            'getActiveLocales' => function ($storeId) {
                return [
                    'en_US' => ['locale' => 'en_US', 'is_default' => 0],
                    'fr_CA' => ['locale' => 'fr_CA', 'is_default' => 1],
                ];
            },
        ]);

        $locale = $multilang->sanitizeLocaleByStore('en_US', 1);
        $this->assertEquals('en_US', $locale);
    }

    public function testSanitizeLocaleByStoreInvalidRequestedLocale()
    {
        $multilang = $this->getMultilangService([
            'retailer.i18n.is_enabled' => true,
            'sf.i18n.locales' => ['en_US', 'fr_CA'],
            'retailer.i18n.default_locale' => 'en_US'
        ], [
            'getActiveLocales' => function ($storeId) {
                return [
                    'en_US' => ['locale' => 'en_US', 'is_default' => 0],
                    'fr_CA' => ['locale' => 'fr_CA', 'is_default' => 1],
                ];
            },
        ]);

        $locale = $multilang->sanitizeLocaleByStore('fake_Locale', 1);
        $this->assertEquals('fr_CA', $locale);
    }

    public function testGetLocalizedDate()
    {
        $multilang = $this->getMultilangService([
            'retailer.i18n.is_enabled' => true,
            'sf.i18n.locales' => ['en_US', 'fr_CA'],
            'retailer.i18n.default_locale' => 'en_US'
        ]);

        // UTC TEST
        // If the default timezone has been altered at some point, make sure it's UTC
        // Hooray for global variables in PHP...
        date_default_timezone_set('UTC');
        $date = new DateTime('May 2 2018');

        $this->assertEquals(
            'Wednesday, May 2, 2018 at 12:00:00 a.m. UTC',
            $multilang->getLocalizedDate($date, 'en_CA', \IntlDateFormatter::FULL, \IntlDateFormatter::LONG)
        );

        $this->assertEquals(
            'mercredi 2 mai 2018 à 00 h 00 min 00 s UTC',
            $multilang->getLocalizedDate($date, 'fr_CA', \IntlDateFormatter::FULL, \IntlDateFormatter::LONG)
        );

        // America/Montreal test

        $date = new DateTime('May 2 2018');
        $timezone = new DateTimeZone('America/Montreal');
        $date->setTimezone($timezone);

        $this->assertEquals(
            'Tuesday, May 1, 2018 at 8:00:00 p.m. EDT',
            $multilang->getLocalizedDate($date, 'en_CA', \IntlDateFormatter::FULL, \IntlDateFormatter::LONG)
        );

        $this->assertEquals(
            'mardi 1 mai 2018 à 20 h 00 min 00 s HAE',
            $multilang->getLocalizedDate($date, 'fr_CA', \IntlDateFormatter::FULL, \IntlDateFormatter::LONG)
        );
    }
}
