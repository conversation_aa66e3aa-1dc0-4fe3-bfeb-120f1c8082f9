<?php

declare(strict_types=1);

namespace SF\unit\Configs;

use Salesfloor\Services\Config\ConfigsLoader\InfraConfigLoader;
use Salesfloor\Services\Config\ConfigsLoader\Loader;

/**
 * @group configs
 */
class ConfigsMultiDomainsTest extends BaseConfigs
{
    /**
     * @dataProvider retailerIdDataProvider
     */
    public function testLoadConfigsMultiDomainsDevValidRetailer($retailerId, $host, $result)
    {
        $_SERVER['HTTP_HOST'] = $host;
        $configs = $this->getConfigs(Loader::SOURCE_API, $retailerId);

        $this->assertEquals($result, $configs[InfraConfigLoader::CONFIG_NAME_DOMAIN_KEY]);
    }

    /**
     * @dataProvider multiDomainDevDataProvider
     */
    public function testLoadConfigsMultiDomainsHeaderNoInfraPathBasedOnHost($retailer, $delimiter, $env, $host, $domain, $header)
    {
        $_SERVER['HTTP_HOST'] = $host;

        $configs = $this->getConfigs(Loader::SOURCE_API, "$retailer-$env");

        $this->assertEquals($header, $configs[InfraConfigLoader::CONFIG_NAME_DOMAIN_KEY]);

        // this is also coming from infra, but this is the default
        $this->validateArray([
            "retailer.api_domain" => "{$retailer}{$delimiter}api{$delimiter}{$env}.$domain",
            "retailer.rest_api_url" => "https://{$retailer}{$delimiter}api{$delimiter}{$env}.$domain",
            "retailer.webserver_domain" => "{$retailer}{$delimiter}{$env}.$domain",
            "retailer.widget.host" => "{$retailer}{$delimiter}widgets{$delimiter}{$env}.$domain",
            "retailer.widgets_domain" => "{$retailer}{$delimiter}widgets{$delimiter}{$env}.$domain",
            "salesfloor_rest_api.host" => "https://{$retailer}{$delimiter}api{$delimiter}{$env}.$domain",
            "salesfloor_storefront.host" => "https://{$retailer}{$delimiter}{$env}.$domain",
            "salesfloor_widgets.host" => "https://{$retailer}{$delimiter}widgets{$delimiter}{$env}.$domain",
            "wordpress.cookie_domain" => "$domain",
        ], $configs);
    }

    /**
     * @dataProvider multiDomainDevDataProvider
     */
    public function testLoadConfigsMultiDomainsHeaderNoInfraPathBasedOnHeader($retailer, $delimiter, $env, $host, $domain, $header)
    {
        $_SERVER[Loader::HEADER_DOMAIN_KEY] = $header;

        $configs = $this->getConfigs(Loader::SOURCE_API, "$retailer-$env");

        $this->assertEquals($header, $configs[InfraConfigLoader::CONFIG_NAME_DOMAIN_KEY]);

        // this is also coming from infra, but this is the default
        $this->validateArray([
            "retailer.api_domain" => "{$retailer}{$delimiter}api{$delimiter}{$env}.$domain",
            "retailer.rest_api_url" => "https://{$retailer}{$delimiter}api{$delimiter}{$env}.$domain",
            "retailer.webserver_domain" => "{$retailer}{$delimiter}{$env}.$domain",
            "retailer.widget.host" => "{$retailer}{$delimiter}widgets{$delimiter}{$env}.$domain",
            "retailer.widgets_domain" => "{$retailer}{$delimiter}widgets{$delimiter}{$env}.$domain",
            "salesfloor_rest_api.host" => "https://{$retailer}{$delimiter}api{$delimiter}{$env}.$domain",
            "salesfloor_storefront.host" => "https://{$retailer}{$delimiter}{$env}.$domain",
            "salesfloor_widgets.host" => "https://{$retailer}{$delimiter}widgets{$delimiter}{$env}.$domain",
            "wordpress.cookie_domain" => "$domain",
        ], $configs);
    }

    /**
     * @dataProvider multiDomainHbcDataProvider
     */
    public function testLoadConfigsMultiDomainsHeaderWithInfraPathHost($retailer, $delimiter, $env, $host, $domain, $header)
    {
        // This is to "fake" infra loader and not rely on the default
        $this->setEnv(InfraConfigLoader::ENV_INFRA_CONFIG_PATH, getcwd() . "/tests/unit/Configs/Fixtures/hbc-prd-infra-multidomain");

        $_SERVER['HTTP_HOST'] = $host;

        $configs = $this->getConfigs(Loader::SOURCE_API, "$retailer-$env");

        $this->assertEquals($header, $configs[InfraConfigLoader::CONFIG_NAME_DOMAIN_KEY]);

        $this->validateArray([
            "retailer.api_domain" => "hbc-api-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.rest_api_url" => "https://hbc-api-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.webserver_domain" => "hbc-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.widget.host" => "hbc-widgets-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.widgets_domain" => "hbc-widgets-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "salesfloor_rest_api.host" => "https://hbc-api-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "salesfloor_storefront.host" => "https://hbc-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "salesfloor_widgets.host" => "https://hbc-widgets-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "wordpress.cookie_domain" => "{$domain}", // coming from infra config file
        ], $configs);
    }

    /**
     * @dataProvider multiDomainHbcDataProvider
     */
    public function testLoadConfigsMultiDomainsHeaderWithInfraPathHeader($retailer, $delimiter, $env, $host, $domain, $header)
    {
        // This is to "fake" infra loader and not rely on the default
        $this->setEnv(InfraConfigLoader::ENV_INFRA_CONFIG_PATH, getcwd() . "/tests/unit/Configs/Fixtures/hbc-prd-infra-multidomain");

        $_SERVER[Loader::HEADER_DOMAIN_KEY] = $header;

        $configs = $this->getConfigs(Loader::SOURCE_API, "$retailer-$env");

        $this->assertEquals($header, $configs[InfraConfigLoader::CONFIG_NAME_DOMAIN_KEY]);

        $this->validateArray([
            "retailer.api_domain" => "hbc-api-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.rest_api_url" => "https://hbc-api-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.webserver_domain" => "hbc-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.widget.host" => "hbc-widgets-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "retailer.widgets_domain" => "hbc-widgets-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "salesfloor_rest_api.host" => "https://hbc-api-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "salesfloor_storefront.host" => "https://hbc-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "salesfloor_widgets.host" => "https://hbc-widgets-prd" . ($header ? "-$header" : '') . ".{$domain}",
            "wordpress.cookie_domain" => "{$domain}", // coming from infra config file
        ], $configs);
    }

    public function multiDomainDevDataProvider()
    {
        return [
            ['saks', '.', 'dev', 'salesfloor.net','salesfloor.net', 'en'],
            ['saks', '.', 'dev', 'salesfloor-new.net','salesfloor.net', 'en'],
            ['saks', '.', 'dev', 'salesfloor-ecom.net','salesfloor-ecom.net', 'fr'],
            ['saks', '-', 'box01', 'salesfloor.net', 'salesfloor.net',null],
            ['saks', '-', 'qa04', 'salesfloor.net','salesfloor.net', null],
            ['saks', '-', 'stg', 'salesfloor.net', 'salesfloor.net',null],
            ['saks', '-', 'prd', 'salesfloor.net','salesfloor.net', null],
        ];
    }

    public function retailerIdDataProvider()
    {
        return [
            ['saks-dev', 'salesfloor.net', 'en'],
            ['saks-stg', 'salesfloor.net', null],
            ['saks-box01', 'salesfloor.net', null],
            ['saks-qa04', 'salesfloor.net', null],
            ['saks-prd', 'salesfloor.net', null],
            ['saks-dev', 'salesfloor-new.net', 'en'],
            ['saks-stg', 'salesfloor-new.net', null],
            ['saks-box01', 'salesfloor-new.net', null],
            ['saks-qa04', 'salesfloor-new.net', null],
            ['saks-prd', 'salesfloor-new.net', null],
            ['saks-dev', 'salesfloor-ecom.net', 'fr'],
            ['saks-stg', 'salesfloor-ecom.net', null],
            ['saks-box01', 'salesfloor-ecom.net', null],
            ['saks-qa04', 'salesfloor-ecom.net', null],
            ['saks-prd', 'salesfloor-ecom.net', null],
            ['hbc-dev', 'salesfloor.net', 'en'],
            ['hbc-stg', 'salesfloor.net', null],
            ['hbc-box01', 'salesfloor.net', null],
            ['hbc-qa04', 'salesfloor.net', null],
            ['hbc-prd', 'salesfloor.net', null],
            ['hbc-dev', 'salesfloor-new.net', 'en'],
            ['hbc-stg', 'salesfloor-new.net', null],
            ['hbc-box01', 'salesfloor-new.net', null],
            ['hbc-qa04', 'salesfloor-new.net', null],
            ['hbc-prd', 'salesfloor-new.net', null],
            ['hbc-dev', 'salesfloor-ecom.net', 'fr'],
            ['hbc-stg', 'salesfloor-ecom.net', null],
            ['hbc-box01', 'salesfloor-ecom.net', null],
            ['hbc-qa04', 'salesfloor-ecom.net', null],
            ['hbc-prd', 'salesfloor-ecom.net', null],
        ];
    }
}
