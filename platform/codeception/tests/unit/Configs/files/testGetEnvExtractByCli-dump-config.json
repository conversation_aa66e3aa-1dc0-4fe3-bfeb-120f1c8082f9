{"retailer.short_name": "tests", "env": "dev", "source": "api", "retailers.current": "tests", "retailer.idstr": "tests", "stack": "dev", "retailer.brand": null, "retailer.brand2host": null, "retailer.addr": "127.0.0.1", "wordpress.cookie_domain": "salesfloor.net", "retailer.api_domain": "tests.api.dev.salesfloor.net", "retailer.rest_api_url": "https://tests.api.dev.salesfloor.net", "salesfloor_rest_api.host": "https://tests.api.dev.salesfloor.net", "retailer.widgets_domain": "tests.widgets.dev.salesfloor.net", "retailer.widgets_url": "https://tests.widgets.dev.salesfloor.net", "retailer.widget.host": "tests.widgets.dev.salesfloor.net", "salesfloor_widgets.host": "https://tests.widgets.dev.salesfloor.net", "retailer.webserver_domain": "tests.dev.salesfloor.net", "retailer.webserver_url": "https://tests.dev.salesfloor.net", "salesfloor_storefront.host": "https://tests.dev.salesfloor.net", "retailer.domain_key": null, "retailer.domains": {"en": {"retailer.api_domain": "tests.api.dev.salesfloor.net", "retailer.rest_api_url": "https://tests.api.dev.salesfloor.net", "salesfloor_rest_api.host": "https://tests.api.dev.salesfloor.net", "retailer.widgets_domain": "tests.widgets.dev.salesfloor.net", "retailer.widgets_url": "https://tests.widgets.dev.salesfloor.net", "retailer.widget.host": "tests.widgets.dev.salesfloor.net", "salesfloor_widgets.host": "https://tests.widgets.dev.salesfloor.net", "retailer.webserver_domain": "tests.dev.salesfloor.net", "retailer.webserver_url": "https://tests.dev.salesfloor.net", "salesfloor_storefront.host": "https://tests.dev.salesfloor.net", "wordpress.cookie_domain": "salesfloor.net"}, "fr": {"retailer.api_domain": "tests.api.dev.salesfloor-ecom.net", "retailer.rest_api_url": "https://tests.api.dev.salesfloor-ecom.net", "salesfloor_rest_api.host": "https://tests.api.dev.salesfloor-ecom.net", "retailer.widgets_domain": "tests.widgets.dev.salesfloor-ecom.net", "retailer.widgets_url": "https://tests.widgets.dev.salesfloor-ecom.net", "retailer.widget.host": "tests.widgets.dev.salesfloor-ecom.net", "salesfloor_widgets.host": "https://tests.widgets.dev.salesfloor-ecom.net", "retailer.webserver_domain": "tests.dev.salesfloor-ecom.net", "retailer.webserver_url": "https://tests.dev.salesfloor-ecom.net", "salesfloor_storefront.host": "https://tests.dev.salesfloor-ecom.net", "wordpress.cookie_domain": "salesfloor-ecom.net"}}, "retailer.host": "tests.api.dev.salesfloor.net", "cache.prefix": "dev:tests", "core.dependencies.use-container-app": true, "twig.views_dir": "", "algolia.filters.prices.options": {"0-50": "$0 - $50", "50-150": "$50 - $150", "150-300": "$150 - $300", "300-500": "$300 - $500", "500-1000": "$500 - $1000", "1000-2000": "$1000 - $2000", ">2000": "$2000+"}, "algolia.filters.discounts.options": {"1": "On sale"}, "algolia.filters.ttl": 43200, "algolia.tablediff": "sf_table_diff", "algolia.maxrecords": 100, "algolia.recordsbybatch": 2, "algolia.index.reset": false, "algolia.relevancy_strictness": 90, "algolia.newretailer": true, "aws.arn.account-id": "************", "s3.bucket_name_prefix": "sf-dev-", "s3.bucket": "sf-tests", "s3.key": "********************", "s3.secret": "z7To6SBbUPIFvUoU20umqH/Ow7e4Y+PXn7BnRqUs", "s3.bucket.provider": "aws", "s3.bucket.region": "us-east-1", "s3.image-bucket": "salesfloor-assets", "s3.image-bucket.provider": "aws", "s3.image-bucket.region": "us-east-1", "mobile.s3.accessKeyId": "********************", "mobile.s3.secretAccessKey": "NC+TV6AjQOimdDNhyhcgwDv9eYC2teDuvpUb6ERu", "mobile.s3.region": "us-east-1", "mobile.dashboard.task_cutoff_days_back": 30, "mobile.api_cache_duration": 180000, "s3.bucket.encryption.enabled": false, "s3.bucket.encryption.type": "AES256", "s3.bucket.signature": null, "s3.host": "amazonaws.com", "gcp.host": "storage.googleapis.com", "s3.exports.prefix": "Exports/", "s3.exports.monthly_prefix": "MonthlyCsvs/", "s3.exports.want_unzipped": false, "s3.exports.columns.get-sales": null, "s3.exports.columns.get-sales-details": null, "exporter.private.bucket": "sf-dev-tests-private", "salesfloor_storefront.addr": "127.0.0.1", "retailer.limit_rep_searches_to_country": true, "retailer.hq_address": "651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9", "retailer.feed_url": "http://www.salesfloor.net/feed/", "retailer.can_share_an_update": true, "products.default_img": "http://res.cloudinary.com/salesfloor-net/image/upload/v1507828679/ann/Image-Coming-Soon-Placeholder.png", "products.root_category_id": "1", "catalog.min_expected_products": 1, "importer.buffer_size": 52428800, "store_users_is_still_a_thing": false, "storefront.num_top_picks": 8, "storefront.have_secondary_products": true, "storefront.top_picks_strategies": ["DEALS"], "storefront.can_edit_events": false, "storefront.can_edit_posts": false, "use_new_arrivals_list": false, "allowed_origins": ["tests.widgets.dev.salesfloor.net", "https://tests.dev.salesfloor.net", "https://127.0.0.1:8555", "https://0.0.0.0:8555"], "origin": null, "throttle.sidebar": false, "debug": false, "kpi_email.n_top_stores": 3, "kpi_email.email_override": null, "kpi_email.demo_addresses": ["<EMAIL>"], "defaults.rep.selling_mode": {"1": "1", "2": "1", "3": "0", "4": "0", "5": "0"}, "retailer.application_start_date": "2016-03-01 00:00:00", "retailer.max_reps_per_store": 0, "retailer.findrep_name_fmt": "fn_li", "customerservice.can_forward": false, "customerservice.email": "<EMAIL>", "retailer.label_question": "Ask & Answer", "retailer.label_appointment": "Book Appointment", "retailer.label_finder": "Personal Shopper", "retailer.services.channel.text.enabled": false, "retailer.services.channel.text.generate_reserve_and_save_number_for_noreply": false, "retailer.services.channel.text.a2p10dlc.is_required": false, "retailer.services.channel.text.a2p10dlc.message_service.associate": null, "retailer.services.channel.text.a2p10dlc.message_service.no_reply": null, "retailer.avatar_transform": {"width": 250, "height": 250, "crop": "fill", "gravity": "face"}, "retailer.multibrand": false, "onboarding.allow_dupe_emails": false, "update_products.sanity_check": null, "monolog.path": "/tmp/", "chat.num_sec_before_heartbeat": 300, "chat.num_sec_disconnect_rep_not_confirm": 60, "chat.request.maxduration": 300, "firebase.token": "uX6phAd8lwarYsQDDq5z6loj1UK4bPtgQBkopOS4", "firebase.url": "https://amber-heat-9378.firebaseio.com/", "firebase.service_account": null, "common_firebase.service_account": null, "common_firebase.token": "FrKImESGm4Hol1G095CVX3Al5YuVPznNoxAeifsF", "common_firebase.url": "https://sf-common-firebase-default-rtdb.firebaseio.com/", "config_descriptions.namespace": "default", "chat.rep_pictures.number": 3, "chat.rep_pictures.default": [], "retailer.rep_avatar_picture.random_default": ["retailer_common_default_circle_1.jpg", "retailer_common_default_circle_2.jpg", "retailer_common_default_circle_3.jpg", "retailer_common_default_circle_4.jpg", "retailer_common_default_circle_5.jpg"], "chat.rep_pictures.blacklist": [], "chat.rep_pictures.ttl": 21600, "chat.queue_rep_pictures.ttl": 60, "multibrand.is_active": false, "multibrand.other_brand_firebase": null, "retailer.storepage_mode": false, "retailer.url": "http://www.salesfloor.net", "stats_db.host": null, "stats_db.user": null, "stats_db.name": null, "stats_db.port": null, "sf.import_unsubs.s3.provider": "aws", "geonames.token": "LxT2JE5MhMwEvv6Vn2JwuRXjmYY", "geonames.user": "salesfloor", "geoip.s3bucket": "sf-deployment-tarballs", "geoip.s3bucket.provider": "aws", "geoip.s3bucket.region": "us-west-1", "geoip.s3key": "maxmind-geolite2-city.mmdb.tar.gz", "geoip.local_filename": "GeoLite2-City.mmdb", "maxmind.key": "4o4jogMkvhEKjFXf", "product_img_size": 250, "slack.key": "*******************************************************", "slack.alerts.channel": "#alerts-success-tests", "service.elasticsearch": "gcp", "gcp.elasticsearch.endpoint": "https://sf-env-test-8.es.northamerica-northeast1.gcp.elastic-cloud.com:9243", "gcp.elasticsearch.username": "elastic", "gcp.elasticsearch.password": "u2LtT93yo4sGyLtZqJCbHk9p", "amazon.elasticsearch.endpoint": null, "amazon.elasticsearch.region": "us-east-1", "services.queue": "aws", "aws.queue.management_enabled": true, "services.queue.enabled": false, "services.record_chat_metrics_cans.enabled": false, "aws.account.number": ************, "gcp.queue.regions": ["us-central1", "us-east1", "us-east4"], "gcp.queue.management_enabled": false, "gcp.queue.max_messages": 1000, "gcp.project_id": "sf-dev-1b4e41f578", "gcp.s3.secret": "UR4iYLl5UTYME0SUGcd7vLUm+C+2uoZ7iNxwj7fj", "gcp.s3.key": "GOOG1EVEPYLAUI6NOZ35NAXC32QB7OLTLXWCS4SU4L43XPDGCHWEEU5GPOGKI", "gcp.s3.secret.access_key_file": "/opt/secrets/cloud-storage/access_key_id", "gcp.s3.secret.secret_access_key_file": "/opt/secrets/cloud-storage/secret_access_key", "google.api_key": "AIzaSyCruwzEM1-X5tiyisQym90z5yVKEZw7x0E", "aws.queue.key": "********************", "aws.queue.secret": "z7To6SBbUPIFvUoU20umqH/Ow7e4Y+PXn7BnRqUs", "aws.queue.region": "us-east-1", "aws.queue.version": "latest", "aws.queue.ack_deadline_seconds": 120, "update_products.importer.class": "\\Salesfloor\\Services\\CatalogImporter\\Test", "update_products.input_dir": null, "update_products.archive_input": false, "update_products.keep_input_file": true, "update_products.multi_file_dump": false, "update_products.skip_diff": true, "update_products.input_file": null, "update_products.max_errors": 50, "update_products.min_products": 1, "update_products.min_categories": 1, "cron.event_updater.class": null, "rankings.global_metrics": ["sales", "response-time", "requests-received", "chat-answer-rate"], "retailer.events_from_feed": false, "elb_logs.region": "us-east-1", "elb_logs.elb_names": [], "redshift.host": "sf-red-1.cdpjytthwjdw.us-east-1.redshift.amazonaws.com", "redshift.port": 5439, "redshift.db": "elb", "redshift.password": "wi0Paequii7uuChoh5bo1iedieJ1Uwei", "redshift.username": "sfred", "storefrontmenu.config.path": "/srv/www/sf_platform/current/api/app/bin/../../../configs/configs/common/storefrontmenu/tests.php", "translator.default_language": "en", "translator.available_languages": ["en"], "salesfloor_mpos_proxy.host": "https://mpos-proxy.salesfloor.net", "retailer.show_store_search_field": true, "retailer.rep_name_format": 3, "retailer.additional_tracking": [], "retailer.sale_cookie_expire": 1209600000, "retailer.acquisition_cookie_expire": 2592000000, "retailer.session_cookie_expire": 0, "retailer.remove_cookie_expire": 1, "retailer.cookie_domain": "salesfloor.net", "retailer.customer_cookie_expire": 0, "retailer.attached_services": "", "retailer.notification_on_sale": true, "retailer.welcome_chat_message": "Hi, my name is %%user%%, how can I help you today?", "retailer.tracker.method": "", "retailer.tracker.url": "", "retailer.disable_cookies": false, "retailer.modular_connect.stand_down_sales_tracking_cookies.is_enabled": false, "lookbook.customer_name_parts": ["first", "last"], "queue.byStore": "all", "retailer.chat.routing.mode": "legacy", "retailer.chat.routing.boundary.single-store": false, "retailer.chat.routing.notification.show_origin_location": false, "multibrand.storefront_host": null, "multibrand.widgets_host": null, "multibrand.other_brand": null, "services.version": 2, "storefront.available_product_grids": [{"type": "top-picks", "max_products": 8, "show_comments": 1}, {"type": "specials", "max_products": 8, "show_comments": 1}, {"type": "new-arrivals", "max_products": 8, "show_comments": 1}, {"type": "favorites", "max_products": 8, "show_comments": 1}], "storefront.store_events": {"active": true, "min_events": 0, "max_events": 3, "show_button": 1}, "storefront.store_events.convert_event_times": true, "storefront.product_brand_logos": {"active": false, "path": "", "file_prefix": "", "file_suffix": ""}, "storefront.static_content": {"active": false, "has_title": true}, "sf.email.mode-rep": 1, "sf.email.mode-salesfloor": 2, "sf.email.mode-retailer": 3, "retailer.name": "tests", "retailer.brand_name": "tests", "retailer.site": "www.salesfloor.net", "retailer.url.i18n": [], "retailer.url.locales": [], "retailer.product_url": "http://www.example.com/products", "retailer.worldwide_catalog": false, "retailer.has_carousel": true, "retailer.mobile_host": "m.example.com", "retailer.tablet_host": "t.example.com", "retailer.www_product_tmpl": "/source=(?P<id>\\d{4,8})/", "retailer.mobile_product_tmpl": "mobile-product-tmpl", "retailer.tablet_product_tmpl": "tablet-product-tmpl", "retailer.use_device_host": true, "retailer.num_top_picks": 8, "retailer.associate": "associate", "retailer.num_deals": 4, "retailer.num_looks": 4, "retailer.num_product_shared": 30, "retailer.trending_recommendations": true, "retailer.trending_recommendations.min": 2, "retailer.trending_recommendations.max": 8, "retailer.trending_recommendations.min_recommendations": 3, "retailer.trending_recommendations.title": "Trending Recommendations", "retailer.trending_recommendations.backoffice_settings_panel": true, "storefront.use_thumbnails": true, "retailer.backoffice.contact_list.limit": 2000, "retailer.default_feed_url": "http://example.com/feed", "retailer.prepared_blog_url": "", "retailer.default_product_array": "", "retailer.num_posts": 4, "retailer.default_secondary_products": "LATEST_ARRIVALS", "retailer.products.html_encoded": false, "retailer.show.products_brand_name.backoffice": true, "retailer.real_api_addr": "127.0.0.1", "retailer.commission_rate": 0, "sf.shoppage_pixel_offset": "-147px", "retailer.store_source": true, "retailer.backoffice_primary_login_through_app.enabled": true, "retailer.pricePrecision": 2, "retailer.pricePrecision.hide_empty_decimals": false, "retailer.has_returns": false, "retailer.reports.commission": false, "retailer.social_networks": ["twitter"], "retailer.facebook_app_id": "909457515744007", "linkedin.key": "78ou0qano3vbtz", "linkedin.secret": "iWefTgvCHYoezMcn", "locationiq.key": "a0a704f11e4361", "mandrill.api_key": "**********************", "mobile.cloudinary.apikey": "733331198774847", "mobile.cloudinary.cloudName": "salesfloor-net", "recaptcha.key": "6LdIUPASAAAAAO9BphszhxtbmX1-GEdwIVQYpl25", "twitter.key": "*************************", "twitter.secret": "ks027rf2PEQEbNEEsp4oOuRdFuFuU9JWj7T1ApsTpPo1VK4KHk", "facebook.app_id": "909457515744007", "facebook.app_secret": "********************************", "retailer.attached_storefront": false, "retailer.prevent_redirect": false, "retailer.storage_method": "cookie", "cookie_prefix": "dev_tests_", "retailer.cookie_show_prefix": false, "retailer.grid": "container", "service.live": true, "service.book_appointment": true, "service.personal_shopper": true, "service.contact_us": true, "service.book_appointment.duration": true, "retailer.label.chat": "Live Chat", "retailer.label.appointment": "Appointment Request", "retailer.label.personal-shopper": "Personal Shopper", "retailer.label.email-me": "Ask & Answer", "retailer.label.email-me.reports": "Ask & Answer", "retailer.label.about-me": "About Me", "retailer.label.inscription": "Get My Updates", "retailer.label.report-concern": "Report A Concern", "retailer.label.find-rep": "Find Advisor", "retailer.label_appointment.textarea.placeholder": "e.g. \"Can you recommend a product for this…?\" or \"I am looking for a product that has the following features…?\"", "retailer.label_finder.textarea.placeholder": "e.g. \"Can you recommend a product for this…?\" or \"I am looking for a product that has the following features…?\"", "retailer.label_question.textarea.placeholder": "e.g. \"Can you reserve this item for pick up at your store?\"", "retailer.label_kpi_deals": "New Arrivals", "retailer.label_kpi_top_picks": "Top Picks", "service.widget": true, "widget.restricted_hours": true, "widget.restricted_range": 5000, "widget.id": "etvasticky", "service.video": false, "service.chat": true, "service.phone": true, "service.instore": true, "service.inperson": false, "retailer.services.hide_optional_phone_input": false, "retailer.services.appointment.is_enabled": true, "retailer.services.appointment.auto-accept.is_enabled": false, "retailer.services.appointment.duration": 30, "retailer.services.appointment.types": [{"type": "store", "is_enabled": true, "is_default": true, "position": 0, "use_alternate_label": false}, {"type": "phone", "is_enabled": true, "is_default": false, "position": 1, "use_alternate_label": false}, {"type": "chat", "is_enabled": true, "is_default": false, "position": 2, "use_alternate_label": false}, {"type": "virtual", "is_enabled": false, "is_default": false, "position": 0, "use_alternate_label": false}], "retailer.services.carousel.show_names": true, "retailer.services.carousel.alternate_logo": null, "retailer.services.carousel.display_mode": null, "retailer.mainSearchUrl": "http://example.com/search", "retailer.email_mode": 3, "product.comment_name_fmt": "fn", "chat.custTimeOut": 33, "chat.repTimeOut": 30, "redis.host": "redis.salesfloor", "redis.port": "6379", "mysql.host": "mysql.salesfloor", "mysql.port": "3306", "mysql.username": "wordpress", "mysql.db": "wordpress_tests", "mysql.password": "AvFAN4LpKl1EYH", "wordpress.db.password": "AvFAN4LpKl1EYH", "mysql.charset": "utf8mb4", "mysql.collate": "", "logstash.topic": "fake", "wordpress.shell_dir": "/opt/salesfloor/shell", "wordpress.cache_dir": "/tmp/cache", "wordpress.word_filter_list": "filter/filter_words.txt", "wordpress.string_filter_list": "filter/filter_strings.txt", "wordpress.debug": 1, "wordrpess.debug_log": 0, "wordpress.js.prodApi": 1, "wordpress.disable_cron": 1, "wordpress.js.menuPosition": 44, "wordpress.js.anchorLabels.defaultState.defaultLabel": "More", "wordpress.js.anchorLabels.defaultState.openedLabel": "Less", "wordpress.js.searchCategories.exclude": [], "wordpress.js.searchCategories.excludeDeals": "Current Offers", "wordpress.js.defaultSpeedAnimation": 400, "wordpress.js.defaultOverlayColor": "#000000", "wordpress.js.iframes.appointment.height": 1040, "wordpress.js.iframes.finder.height": 855, "wordpress.js.iframes.question.height": 660, "wordpress.js.iframes.inscription.height": 640, "wordpress.js.iframes.concern.height": 630, "wordpress.templates_dir": null, "wordpress.properties_dir": null, "wordpress.rep_status_poll_interval": 30000, "wordpress.auth_key": "3lH!A3`[!m>(4AR<2[KFw|OayNGY*&2-xRO1%;R@R+)o]{-[*40$9>?etests-dev", "wordpress.auth_salt": "a<ptuYkPVXv_wwx_Ez>o<w/C?%:dCmo-3gJj|sw_|-A`E82FSK&~KLy4tests-dev", "wordpress.logged_in_key": "4.+3nuTM1^=4LT~A-HH5,/pM&i+ysFS-}@p!-g_JK#=?++M;MZ@}n]omtests-dev", "wordpress.logged_in_salt": "NuG(^*_*?aS%*3[se&+k$6Jg;|HO60G,wfy|y#%~x{RR-BUnYv.w1ckqtests-dev", "wordpress.nonce_key": "Xy4| Tt#8w@2)~ns5yz+xAJ%dN01u4Q-k[u!Sun>b1kI<,]n  ]X$,mPtests-dev", "wordpress.nonce_salt": "0F72i}wxwmb?W*d!)fK)<<|Yk(3^{+<*~x;]+7m%bcD_U]FUs0v|x!D-tests-dev", "wordpress.secure_auth_key": "0ZujT%*OY_LE(g#2vHJ0aJb&r]K6nhZd[ zT+HOtOH6=e+6Mt~H*F7G-tests-dev", "wordpress.secure_auth_salt": "5k^DA#+e]bE0TM5AU$g3qIe,+lzU}zex| K985IoMfa<6ED+d-_yQ@Cptests-dev", "publisher.maxRecipients": 100, "content.help_link_url": "https://salesfloor.zendesk.com/hc/en-us", "content.help_link_url_query": "https://salesfloor.zendesk.com/hc/en-us/search?utf8=%E2%9C%93&query={query}&commit=Search", "content.text_blacklist": null, "content.whitelist_links": false, "onboarding.choose_own_username": true, "onboarding.alias_match_username": false, "onboarding.alias_template": 4, "onboarding.example_username_full": true, "onboarding.choose_alias": false, "retailer.alias_tmpl": 2, "retailer.username_tmpl": 3, "retailer.rep.display_name_tmpl": 3, "onboarding.allow_about_me": true, "sns_endpoint": {"android": {"applicationArn": "arn:aws:sns:us-west-1:************:app/GCM/Salesfloor-GCM"}}, "retailer.time_fmt": 24, "msg_center.max_recipients": 20, "retailer.report_concern_mailboxes": ["<EMAIL>"], "retailer.label_appointment_special": "", "retailer.label_question_special": "", "retailer.chat_delay": {"queue": 120000, "broadcast": 60000, "dynamic": 60000}, "service.version": 2, "retailer.email.contact_us": "", "retailer.services.casl": false, "retailer.services.casl.v2": false, "retailer.services.casl.v3": false, "retailer.single_category": false, "cache.storefront.ttl": 43200, "storefront.menu.isdynamic": false, "autoresponder.send_normal_response_too": true, "sns.key": "********************", "sns.secret": "B9gpJGW3HzXWh3txAlVf45A5uOHFXwyjXUN2EGnV", "sns.region": "us-west-1", "sns.bugs.region": "us-east-1", "sns.bugs.topic": null, "mobile.app_download_urls": {"ios": "https://build.phonegap.com/apps/1221243/download/ios", "android": "https://build.phonegap.com/apps/1221243/download/android"}, "mobile.app_version_urls": null, "mobile.camera_max_height": 1500, "mobile.camera_max_width": 1500, "mobile.camera_quality": 100, "mobile.camera_max_photos": 5, "mobile.email_me_label": "Email Me Request", "mobile.loader_delay": 1500, "mobile.loader_message_interval": 4000, "mobile.fb_max_reconnection_time": 500, "mobile.fb_show_disconnect_message": false, "mobile.product_barcode_scanner": true, "mobile.retailer_has_chat": true, "mobile.retailer_has_deals": false, "mobile.retailer_has_lookbooks": true, "mobile.retailer_has_new_arrivals": true, "mobile.retailer_has_personal_shopper": true, "mobile.retailer_has_specialties": false, "mobile.retailer_has_chat_appointment": true, "mobile.retailer_has_mobile_checkout": true, "mobile.barcode_scanner.contacts.enabled": false, "mobile.phone_call.is_enabled": true, "mobile.retailer_can_browse_library": false, "mobile.retailer_can_share_from_browse_library": false, "mobile.retailer_has_tasks": true, "mobile.retailer_has_asset_management": true, "mobile.retailer_has_feed_validation": true, "feed_validation.detect_missing_columns.enabled": true, "feed_validation.data_sample_limit": 20, "mobile.retailer_has_products_feed": true, "mobile.compose_wysiwyg_enabled": true, "mobile.share_wysiwyg_enabled": true, "backoffice.compose_wysiwyg_enabled": true, "backoffice.share_wysiwyg_enabled": true, "mobile.share_connected_services_enabled": true, "mobile.share_email_enabled": true, "mobile.share_facebook_enabled": false, "mobile.share_instagram_enabled": false, "mobile.share_pinterest_enabled": false, "mobile.retailer_can_edit_events": false, "mobile.draft.compose_enabled": true, "mobile.draft.share_enabled": true, "mobile.draft.auto_save_interval": 5000, "mobile.sms.quick_responses": [{"short": {"en_US": "Insert Storefront Link", "en_IE": "Insert Storefront Link", "en_IN": "Insert Storefront Link", "fr_CA": "Insérer le lien du magasin", "ja_JP": "ストアリンクを挿入"}, "full": {"en_US": "Here's the link to my Storefront: {store_link}", "en_IE": "Here's the link to my Storefront: {store_link}", "en_IN": "Here's the link to my Storefront: {store_link}", "fr_CA": "Voici le lien vers ma vitrine: {store_link}", "ja_JP": "これが私のストアフロントへのリンクです：{store_link}"}}, {"short": {"en_US": "Insert Subscribe Link", "en_IE": "Insert Subscribe Link", "en_IN": "Insert Subscribe Link", "fr_CA": "Insérer le lien d'abonnement", "ja_JP": "購読リンクを挿入"}, "full": {"en_US": "Here's the link to subscribe: {subscribe_link}", "en_IE": "Here's the link to subscribe: {subscribe_link}", "en_IN": "Here's the link to subscribe: {subscribe_link}", "fr_CA": "Voici le lien pour vous inscrire: {subscribe_link}", "ja_JP": "購読するためのリンクは次のとおりです。: {subscribe_link}"}}, {"short": {"en_US": "Appointment Link", "en_IE": "Appointment Link", "en_IN": "Appointment Link", "fr_CA": "Lien de rendez-vous"}, "full": {"en_US": "Here's the link to book an appointment: {appointment_link}", "en_IE": "Here's the link to book an appointment: {appointment_link}", "en_IN": "Here's the link to book an appointment: {appointment_link}", "fr_CA": "Voici le lien pour prendre rendez-vous: {appointment_link}", "ja_JP": "予約するためのリンクはこちら: {appointment_link}"}}], "mobile.quick_responses": [{"short": {"en_US": "Give me a moment", "en_IE": "Give me a moment", "en_IN": "Give me a moment", "fr_CA": "Un instant", "ja_JP": "少しお時間をください"}, "full": {"en_US": "Please give me a moment and I will look into this for you.", "en_IE": "Please give me a moment and I will look into this for you.", "en_IN": "Please give me a moment and I will look into this for you.", "fr_CA": "S'il vous plaît, laissez-moi un instant et je vais m'en occuper pour vous.", "ja_JP": "少しお時間をください、調査させていただきます。"}}, {"short": {"en_US": "Thank you and goodbye", "en_IE": "Thank you and goodbye", "en_IN": "Thank you and goodbye", "fr_CA": "Merci et au revoir", "ja_JP": "ありがとうございました。さようなら"}, "full": {"en_US": "Thank you for your request. It will be my pleasure to serve you again. Have a great day.", "en_IE": "Thank you for your request. It will be my pleasure to serve you again. Have a great day.", "en_IN": "Thank you for your request. It will be my pleasure to serve you again. Have a great day.", "fr_CA": "<PERSON><PERSON><PERSON> de votre demande. Il me fera plaisir de vous servir à nouveau. Passez une bonne journée.", "ja_JP": "リクエストありがとうございます。再び、お役に立てることを嬉しく思います。良い一日をお過ごしください。"}}, {"short": {"en_US": "Out of stock", "en_IE": "Out of stock", "en_IN": "Out of stock", "fr_CA": "Rupture de stock", "ja_JP": "在庫切れ"}, "full": {"en_US": "Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href=\"retailer_link\" target=\"_blank\">here</a>", "en_IE": "Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href=\"retailer_link\" target=\"_blank\">here</a>", "en_IN": "Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href=\"retailer_link\" target=\"_blank\">here</a>", "fr_CA": "Malheureusement, nous sommes en rupture de stock en magasin, mais vous pouvez l'acheter en ligne en cliquant <a href=\"retailer_link\" target=\"_blank\">ici</a>", "ja_JP": "残念ながら、現在この製品の在庫はありませんが、<a href=\"retailer_link\" target=\"_blank\">こちら</a>をクリックして、オンラインでご購入になれます"}}, {"short": {"en_US": "My Storefront Link", "en_IE": "My Storefront Link", "en_IN": "My Storefront Link", "fr_CA": "Lien de ma vitrine web"}, "full": "storefront_link"}, {"short": {"en_US": "Subscribe Link", "en_IE": "Subscribe Link", "en_IN": "Subscribe Link", "fr_CA": "Lien d'abonnement"}, "full": "subscribe_link"}, {"short": {"en_US": "Appointment Link", "en_IE": "Appointment Link", "en_IN": "Appointment Link", "fr_CA": "Lien de rendez-vous"}, "full": "appointment_link"}], "mobile.extra_quick_responses": [{"short": "Contact Customer Service", "full": "Our Customer Service team can help you with this request. They are available via phone at xx.xxx.xxxx.xxxx, thanks."}], "mobile.products_modal_enabled": false, "mobile.retailer_can_change_retailer_id": false, "mobile.login.mdm.is_enabled": true, "mobile.login.store.is_enabled": true, "mobile.app_upgrade_notification.is_enabled": true, "mobile.app_upgrade_notification.timeout": 86400, "mobile.app_upgrade_notification.message": {"en_US": "<p>IMPORTANT: This Android version of Salesfloor is no longer supported and should be replaced with the latest Salesfloor app.</p><p>Please download it here: <a href=\"https://salesfloor.net/android\" onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a></p><p>Thanks.</p>", "fr_CA": "<p>IMPORTANT: Cette version Android de Salesfloor n'est plus supportée and doit être remplacée par la plus récente application Salesfloor.</p><p>Téléchargez-la ici: <a href='https://salesfloor.net/android' onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a> et n'oubliez pas d'effacer l'ancienne version de votre appareil ! Vos informations ne seront pas perdues.</p><p>Merci.</p>", "ja_JP": "<p>重要：このAndroidバージョンのSalesfloorはサポート停止となりました。最新のSalesfloorアプリに更新してください。</p><p>ここからダウンロードしてください： <a href='https://salesfloor.net/android' onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a></p><p>よろしくお願いいたします。</p>"}, "mobile.retailer.pricePrecision": 2, "mobile.retailer.show.products_brand_name": true, "mobile.retailer.pricePrecision.hide_empty_decimals": false, "retailer.alternate_brand": null, "retailer.alternate_name": null, "mysql.slaves": null, "mysql.use_wpdb_slave": false, "bloom.api_key": null, "customer_ftp.host": null, "customer_ftp.password": null, "customer_ftp.user": null, "firebase.defaultMultibrandPath": null, "multibrand.other_brand_host": null, "mysql.slave-1.db": null, "mysql.slave-1.host": null, "mysql.slave-1.port": null, "mysql.slave-1.username": null, "payroll.email": null, "retailer.email_domain": "www.salesfloor.net", "retailer.contacturl": null, "retailer.google.host": null, "retailer.google.id": null, "retailer.google.uid": null, "retailer.instagram.account": "macys", "instagram.media.ttl": 604800, "storefront.instagram": {"active": false}, "retailer.label_unique_chat": null, "retailer.live.url": null, "retailer.multibrand.mapping": null, "retailer.onboarding_logo": "http://salesfloor.net/assets/images/logos/salesfloor.png", "retailer.outlook.id": null, "retailer.services.logo": null, "retailer.services.policy": true, "retailer.yahoo.consumer_key": null, "retailer.yahoo.consumer_secret": null, "stats_db.db": null, "stats_db.username": null, "update_products.categories_entry": null, "update_products.categories_exclude": null, "update_products.categories_selectors": null, "retailer.filter_new_leads_by_specialty": false, "retailer.has_live_chat_appointment": true, "retailer.transfer_store_name_to_id": false, "retailer.saks_note": "", "retailer.inscription_policy": false, "retailer.bo_logo_is_landscape": false, "retailer.bo_logo_url": null, "retailer.show_publisher_in_bo": true, "retailer.publisher_email_option_none": true, "retailer.publisher_social_sharing": true, "retailer.publisher_storefront": true, "retailer.notifications_deals_current": true, "retailer.notifications_posts_current": false, "retailer.notifications_send_update": true, "retailer.notifications_create_event": true, "retailer.notifications_create_description": true, "retailer.typekit_freight_display_pro": false, "retailer.google_tag_manager": false, "retailer.sort_user_by_username": false, "retailer.has_personal_shopper": true, "retailer.has_kpi_deals": true, "retailer.has_latest_arrivals": true, "retailer.remove_cookie_domains": null, "retailer.load_typekit": [], "retailer.google.ga_backoffice_web": "UA-********-3", "retailer.google.ga_backoffice_mobile": "UA-********-4", "retailer.google.ga_services": "UA-********-6", "retailer.google.ga_storefront": "UA-********-7", "retailer.google.ga_socialshop": "UA-********-10", "retailer.google.ga_backoffice_web_v2": "UA-********-11", "retailer.contacts.import_enabled": false, "monolog.level": 400, "logger.slack.channel.errors": "#alerts-errors-tests", "logger.slack.channel.success": "#alerts-success-tests", "logger.slack.channel.account_monitor": "#alerts-account-monitor-tests", "throw_exception.empty_filters_by_get_one": true, "retailer.default_currency": "USD", "retailer.allowed_currencies": ["USD", "CAD"], "retailer.storefront.is-new-stack": false, "stores.max_distance": "800", "stores.max_distance.per_store.enable": false, "stores.available_hours": [true, true, true, true, true, true, true], "linkshare_on_storefront_view": false, "products.defaultcomment_display.is_enabled": true, "products.defaultcomments.storemode": ["api_products_defaultcomments_storemode_1", "api_products_defaultcomments_storemode_2"], "products.defaultcomments.repmode": ["api_products_defaultcomments_repmode_1", "api_products_defaultcomments_repmode_2", "api_products_defaultcomments_repmode_3"], "product.panels": {"top-picks": "TopPicks", "new-arrivals": "NewArrivals", "specials": "Specials"}, "product.panels.new-arrivals.nominated-products": false, "product.panels.top-picks.nominated-products": false, "product.panels.refresh.autoselected.frequency": 14, "products.variants.groupby": null, "products.is-price-displayed": true, "customers.labels": {"email": ["Home", "Work", "Other"], "phone": ["Home", "Work", "Mobile", "Other"]}, "mobile.specificities": "{\n  \"pages\": {\n    \"enter-token\": {\n      \"token\": \"true\"\n    },\n    \"create-user\": {\n      \"username\": \"true\",\n      \"password\": \"true\",\n      \"confirm\": \"true\"\n    },\n    \"enter-email\": {\n      \"email\": \"email\",\n      \"phone\": \"true\",\n      \"alias\": \"((firstname || '') + (lastname || '')[0]).replace(/\\\\s/g, '').toLowerCase()\",\n      \"storefront\": \"BaseUrl + '/' + alias\"\n    },\n    \"personal-info\": {\n      \"firstname\": \"firstname || ''\",\n      \"lastname\": \"lastname || ''\"\n    },\n    \"pick-store\": {\n      \"store\": \"store\",\n      \"introduction\": \"\\\"Hello! I am your dedicated Sales Associate. I can provide personalized services, answer your questions and meet with you in store or online to help you find what you desire.\\\"\"\n    },\n    \"out-of-store\": \"true\",\n    \"take-picture\": \"true\",\n    \"choose-specialties\": \"true\",\n    \"import-contact\": \"true\",\n    \"connect-social\": {\n      \"twitter\": \"true\"\n    },\n    \"congrats\": \"false\"\n  },\n\n  \"overrides\": [{\n    \"rules\": \"selling_mode == 0\",\n    \"override\": {\n      \"enter-email\": \"false\",\n      \"out-of-store\": \"false\",\n      \"take-picture\": \"true\",\n      \"choose-specialties\": \"false\",\n      \"import-contact\": \"false\",\n      \"connect-social\": \"false\",\n      \"details\": \"false\"\n    }\n  }, {\n    \"rules\": \"group > 3 && selling_mode == 1\",\n    \"override\": {\n      \"enter-email\": {\n        \"alias\": \"false\"\n      },\n      \"pick-store\": \"false\",\n      \"take-picture\": \"false\",\n      \"choose-specialties\": \"false\",\n      \"import-contact\": \"false\",\n      \"connect-social\": \"false\"\n    }\n  }, {\n    \"rules\": \"group > 3 && selling_mode == 0\",\n    \"override\": {\n      \"enter-email\": {\n        \"alias\": \"false\"\n      },\n      \"pick-store\": \"false\",\n      \"take-picture\": \"false\",\n      \"choose-specialties\": \"false\",\n      \"import-contact\": \"false\",\n      \"connect-social\": \"false\",\n      \"details\": \"false\"\n    }\n  }]\n}", "sf.sendgrid.api_key": "*********************************************************************", "sf.sendgrid.master_api_key": "*********************************************************************", "sf.sendgrid.api_url": "https://api.sendgrid.com/v3/", "sf.sendgrid.percentage": 100, "sf.casl.address": "651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9", "sf.sendgrid.queue_sends": false, "retailer.contacts.unsubscribe_automated.enabled": false, "retailer.contacts.unsubscribe_automated.days_threshold": 180, "retailer.consent_required.mobile": false, "retailer.consent_required.desktop": false, "retailer.term_required.desktop": false, "retailer.emails.no_reply_address": "<EMAIL>", "retailer.emails.no_reply_name": "Salesfloor", "retailer.emails_exclusion_list.filter.enabled": false, "retailer.emails_exclusion_list.domain": ["gmail"], "retailer.emails_exclusion_list.start_days_before": 14, "retailer.emails_exclusion_list.end_days_before": 0, "retailer.emails_filter.allow_email.any": true, "retailer.emails_filter.allow_email.block_notification": true, "retailer.emails_filter.allow_email.domain_whitelist": ["salesfloor.net"], "retailer.emails_filter.allow_email.address_suffix_whitelist": [], "retailer.email.encoded.link": false, "retailer.clienteling.mode": true, "retailer.clienteling.customer.sync": false, "exporter.email.stats.daily.enabled": false, "retailer.clienteling.customer.limited_visibility.is_enabled": false, "retailer.clienteling.enabled.customer_stats": true, "sf.import_ci_customers.encrypted": false, "retailer.clienteling.stats.importer.class": "Salesfloor\\Services\\Importer\\RetailerCustomerStatsInsights\\RetailerCustomerStatsInsightsImporter", "importer.customer_stats.fetcher": "importer.fetcher.s3.retailer_stats", "retailer.clienteling.enabled.transactions": true, "retailer.clienteling.transactions.attribution.is_enabled": true, "retailer.clienteling.transactions.attribution.addition_transaction_type": [], "retailer.clienteling.transactions.attribution.include_trx_date": true, "retailer.clienteling.transactions.attribution.milliseconds_search_back": null, "sf.import_ci_transactions.encrypted": false, "retailer.clienteling.transactions.importer.class": "Salesfloor\\Services\\Importer\\RetailerTransaction\\RetailerCustomerTransactionImporter", "importer.transaction.fetcher": "importer.fetcher.s3.retailer_transaction", "importer.transaction.loader": "importer.retailer_customers_transaction.loader.id", "retailer.clienteling.enabled.create_contact": true, "retailer.clienteling.stats.manager.class": "CustomerInsights\\Stats\\Panels", "sf.import_ci_stats.encrypted": false, "retailer.clienteling.customers.importer.class": "Salesfloor\\Services\\Importer\\RetailerCustomer\\RetailerCustomerImporter", "retailer.clienteling.plan.importer.class": "Salesfloor\\Services\\Importer\\CustomerInsights\\Importer", "retailer.clienteling.customers2contacts.class": "Salesfloor\\Services\\Importer\\RetailerCustomer\\CustomersToContacts", "importer.customer.fetcher": "importer.fetcher.s3.retailer_customer", "importer.customer.loader": "importer.retailer_customers.loader.id", "sf.import_ci_customers.s3.path": "", "sf.import_ci_customers.s3.filename_regexp": "/test-file/", "shopify.url": null, "shopify.api_version": "2024-07", "retailer.contacts.delete_unassociated_ci_imported.is_enabled": false, "retailer.retailer_customers.delete_not_changed_recently.is_enabled": false, "retailer.retailer_customers.delete_not_changed_recently.safety_guard": {"allow_delete_count": 10000, "allow_delete_ratio": 1.01}, "retailer.retailer_customers.delete_contact_with_orphan_retailer_customer.safety_guard": {"allow_delete_count": 5000, "allow_delete_ratio": 1.01}, "importer.customer_remapping.s3.path": "inbound/{env}/crm-remapping", "importer.customer_remapping.s3.filename_regexp": "{retailer}-ci-customer-remapping-\\d{8}-\\d{6}\\.csv", "importer.customer_remapping.encrypted": false, "importer.customer.ingestor.csv.delimiter": null, "sf.import_ci_stats.s3.path": "inbound/{env}/ci-stats", "sf.import_ci_stats.s3.filename_regexp": "/test-file/", "importer.customer_stats.ingestor.csv.delimiter": null, "sf.import_ci_transactions.s3.path": "inbound/{env}/ci-transactions", "sf.import_ci_transactions.s3.filename_regexp": "/test-file/", "importer.transaction.ingestor.csv.delimiter": null, "importer.rep_transaction.s3.path": "inbound/{env}/transactions", "importer.rep_transaction.s3.filename_regexp": "{retailer}-transactionupdates-\\d{8}-\\d{6}.csv", "importer.rep_transaction.ingestor": "importer.ingestor.csv.transactions", "importer.rep_transaction.fetcher": "importer.fetcher.s3.transaction", "sf.import_ci_customers.bulk_size": 500, "sf.import_ci_transactions.bulk_size": 1000, "sf.import_ci_customers.c2c_split_size": 300000, "sf.import_ci_customers.update_fields_excluded": [], "retailer.rules.attribution_days": 30, "importer.tasks.is_enabled": true, "importer.tasks.s3.path": "inbound/{env}/task/", "importer.tasks.s3.filename_regexp": "{retailer}-task-\\d{8}-\\d{6}.csv", "retailer.clienteling.matching": ["retailer_customer_id", "default_email", "alternate_email", "alternate_email_to_default_email", "default_email_to_alternate_email", "retailer_parent_customer_id", "manual"], "retailer.clienteling.matching.is_enabled": true, "retailer.clienteling.import.notification.emails": [], "retailer.nags_eligible_suggestions": ["onboarding_description", "onboarding_social", "onboarding_picture", "onboarding_emails", "email", "share", "product", "deal", "new_arrivals"], "retailer.qa_product_url_parser.pattern": null, "retailer.qa_product_url_parser.replace": null, "sns.topics.all": ["action-required", "operations", "pager-duty"], "sns.topics.operations": "arn:aws:sns:us-east-1:************:operations", "sns.topics.pager-duty": "arn:aws:sns:us-east-1:************:pager-duty", "sns.topics.action-required": "arn:aws:sns:us-east-1:************:action-required", "alarms.enabled": [], "alarms.events_ApproximateAgeOfOldestMessage.alarm_description": "Messages lingering in Events Queue. Activate more ReadEventQueue workers on Supervisor. Look into possible reasons for server slow downs or increased events", "alarms.events_ApproximateAgeOfOldestMessage.actions_enabled": true, "alarms.events_ApproximateAgeOfOldestMessage.ok_actions_configs": ["action-required"], "alarms.events_ApproximateAgeOfOldestMessage.alarm_actions_configs": ["action-required"], "alarms.events_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs": [], "alarms.events_ApproximateAgeOfOldestMessage.metric_name": "ApproximateAgeOfOldestMessage", "alarms.events_ApproximateAgeOfOldestMessage.namespace": "sqs", "alarms.events_ApproximateAgeOfOldestMessage.statistic": "Average", "alarms.events_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs": ["queue.events.full_name"], "alarms.events_ApproximateAgeOfOldestMessage.period_in_seconds": 300, "alarms.events_ApproximateAgeOfOldestMessage.unit": "Seconds", "alarms.events_ApproximateAgeOfOldestMessage.evaluation_periods": 1, "alarms.events_ApproximateAgeOfOldestMessage.threshold": 57600, "alarms.events_ApproximateAgeOfOldestMessage.comparison_operator": "GreaterThanThreshold", "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description": "Too many messages in Dead Letter Events Queue. Look into reason for growing number of dead letter events. Database Issues / Errors are the usual reason for being placed in dead letter", "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.actions_enabled": true, "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs": [], "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs": ["action-required"], "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs": [], "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.metric_name": "ApproximateNumberOfMessagesVisible", "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.namespace": "sqs", "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.statistic": "Average", "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs": ["queue.events.dead_letter_name"], "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds": 900, "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.unit": "Count", "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods": 1, "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.threshold": 100, "alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator": "GreaterThanThreshold", "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.alarm_description": "Messages are going to expire in 4 days from Dead Letter Queue. Evaluate if there is an important number of messages that will expire or not and act upon them", "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.actions_enabled": true, "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.ok_actions_configs": [], "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.alarm_actions_configs": ["action-required"], "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs": [], "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.metric_name": "ApproximateAgeOfOldestMessage", "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.namespace": "sqs", "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.statistic": "Average", "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs": ["queue.events.dead_letter_name"], "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.period_in_seconds": 900, "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.unit": "Seconds", "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.evaluation_periods": 4, "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.threshold": 864000, "alarms.events-dead-letter_ApproximateAgeOfOldestMessage.comparison_operator": "GreaterThanThreshold", "alarms.share-an-update_ApproximateAgeOfOldestMessage.alarm_description": "Messages lingering in share-an-update Queue. Look into why emails are not being popped off the queue / sent by ESP", "alarms.share-an-update_ApproximateAgeOfOldestMessage.actions_enabled": true, "alarms.share-an-update_ApproximateAgeOfOldestMessage.ok_actions_configs": ["action-required"], "alarms.share-an-update_ApproximateAgeOfOldestMessage.alarm_actions_configs": ["action-required"], "alarms.share-an-update_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs": [], "alarms.share-an-update_ApproximateAgeOfOldestMessage.metric_name": "ApproximateAgeOfOldestMessage", "alarms.share-an-update_ApproximateAgeOfOldestMessage.namespace": "sqs", "alarms.share-an-update_ApproximateAgeOfOldestMessage.statistic": "Average", "alarms.share-an-update_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs": ["queue.share-an-update"], "alarms.share-an-update_ApproximateAgeOfOldestMessage.period_in_seconds": 300, "alarms.share-an-update_ApproximateAgeOfOldestMessage.unit": "Seconds", "alarms.share-an-update_ApproximateAgeOfOldestMessage.evaluation_periods": 1, "alarms.share-an-update_ApproximateAgeOfOldestMessage.threshold": 1800, "alarms.share-an-update_ApproximateAgeOfOldestMessage.comparison_operator": "GreaterThanThreshold", "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.alarm_description": "Messages lingering in cakemail-relays Queue. Look into why emails are not being popped off the queue / sent by ESP", "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.actions_enabled": true, "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.ok_actions_configs": ["action-required"], "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.alarm_actions_configs": ["action-required"], "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs": [], "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.metric_name": "ApproximateAgeOfOldestMessage", "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.namespace": "sqs", "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.statistic": "Average", "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs": ["queue.cakemail-relays"], "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.period_in_seconds": 300, "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.unit": "Seconds", "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.evaluation_periods": 1, "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.threshold": 1800, "alarms.cakemail-relays_ApproximateAgeOfOldestMessage.comparison_operator": "GreaterThanThreshold", "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description": "Unprocessable mail", "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.enabled": true, "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs": [], "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs": ["action-required"], "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs": [], "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.metric_name": "ApproximateNumberOfMessagesVisible", "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.namespace": "sqs", "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.statistic": "Average", "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs": ["queue.cakemail-relays-dead-letter"], "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds": 3600, "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.unit": "Count", "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods": 1, "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.threshold": 1, "alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator": "GreaterThanOrEqualToThreshold", "alarms.record-transactions_ApproximateAgeOfOldestMessage.alarm_description": "Old messages are piling up in the Record Transactions queue. Look into why transactions are not being recorded", "alarms.record-transactions_ApproximateAgeOfOldestMessage.actions_enabled": true, "alarms.record-transactions_ApproximateAgeOfOldestMessage.ok_actions_configs": ["action-required"], "alarms.record-transactions_ApproximateAgeOfOldestMessage.alarm_actions_configs": ["action-required"], "alarms.record-transactions_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs": [], "alarms.record-transactions_ApproximateAgeOfOldestMessage.metric_name": "ApproximateAgeOfOldestMessage", "alarms.record-transactions_ApproximateAgeOfOldestMessage.namespace": "sqs", "alarms.record-transactions_ApproximateAgeOfOldestMessage.statistic": "Maximum", "alarms.record-transactions_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs": ["queue.record-transactions.full_name"], "alarms.record-transactions_ApproximateAgeOfOldestMessage.period_in_seconds": 300, "alarms.record-transactions_ApproximateAgeOfOldestMessage.unit": "Seconds", "alarms.record-transactions_ApproximateAgeOfOldestMessage.evaluation_periods": 1, "alarms.record-transactions_ApproximateAgeOfOldestMessage.threshold": 1800, "alarms.record-transactions_ApproximateAgeOfOldestMessage.comparison_operator": "GreaterThanThreshold", "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description": "Unprocessable transaction events", "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.enabled": true, "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs": [], "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs": ["action-required"], "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs": [], "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.metric_name": "ApproximateNumberOfMessagesVisible", "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.namespace": "sqs", "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.statistic": "Average", "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs": ["queue.record-transactions.dead_letter_name"], "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds": 3600, "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.unit": "Count", "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods": 1, "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.threshold": 1, "alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator": "GreaterThanOrEqualToThreshold", "sf.sendgrid.threshold.ips": ["**************"], "sf.sendgrid.threshold.leeway": 1, "sf.pagerduty.integrationkey.v1": "54f872e74aa148dabc064a4fda8c41a2", "messaging.email.enabled": true, "messaging.social.enabled": true, "messaging.text.enabled": true, "content.text.twilio_blocklist.is_enabled": true, "messaging.text.twilio.override_callback_host": null, "messaging.text.twilio.account_sid": "FAKEACCOUNTSID", "messaging.text.twilio.api_key_id": "FAKEAPIKEY", "messaging.text.twilio.api_key_secret": "FAKEAPISECRET", "messaging.text.default_country_code": "+1", "messaging.text.noreply_address": null, "messaging.text.multiple-recipients.enabled": false, "messaging.email.multiple-recipients.enabled": true, "messaging.text.multiple-recipients.max": 40, "messaging.text.multiple-recipients.send_directly_limit": 1, "messaging.text.application.messaging": "Messaging", "messaging.text.application.noreply": "NoReply", "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description": "Messages in messaging-text-enable-dead-letter Queue. Either numbers are not being reserved with twilio / saved in DB OR released with twilio / deleted in DB. Check Supervisor processes. Empty queue and mark as resolved.", "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.actions_enabled": true, "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs": ["action-required"], "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs": ["action-required"], "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs": [], "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.metric_name": "ApproximateNumberOfMessagesVisible", "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.namespace": "sqs", "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.statistic": "Average", "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs": ["queue.text_messaging_enable.dead_letter_name"], "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds": 300, "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.unit": "Count", "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods": 1, "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.threshold": 1, "alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator": "GreaterThanOrEqualToThreshold", "importer.text_message_unsubscribe.enable": false, "importer.text_message_unsubscribe.s3.path": "inbound/{env}/unsubscribes/text/", "importer.text_message_unsubscribe.s3.filename_regexp": "{retailer}-textmessage_unsubscribes-\\d{8}-\\d{6}.csv", "importer.text_message_unsubscribe.s3.filename.header": true, "deep_links_enabled": true, "retailer.storefront.dynamic_content.is_active": false, "retailer.storefront.dynamic_content.max_num_posts": null, "retailer.storefront.dynamic_content.url": [], "retailer.i18n.is_enabled": true, "retailer.i18n.default_locale": "en_US", "retailer.i18n.locale.fallback": "en_US", "retailer.country.code": "US", "retailer.country.code.fallbacks": ["US", "CA"], "retailer.i18n.widgets.sync.method": null, "retailer.i18n.default_language_fallback": {"en": "en_US", "fr": "fr_CA"}, "retailer.i18n.locale_display_name": {"en_US_en": "English", "en_US_fr": "<PERSON><PERSON><PERSON>", "fr_CA_en": "French", "fr_CA_fr": "Français"}, "retailer.i18n.products.is_default_locale_fallback": false, "sf.i18n.locales": ["en_US", "fr_CA"], "sf.l10n.phone_format": {"place_holder": "************", "format": "ddd-ddd-dddd"}, "importer.import-as-subscriber.email": 1, "importer.import-as-subscriber.sms": 0, "retailer.reporting.begin_week": 0, "sf.task.reminder.cron": 5, "sf.task.manual.enabled": true, "sf.task.manual.emails_enabled": false, "sf.task.manual.notifications_enabled": false, "sf.task.automated.enabled": true, "sf.task.automated.emails_enabled": false, "sf.task.automated.notifications_enabled": false, "sf.task.auto_dismiss.enabled": false, "sf.task.auto_dismiss.settings": [{"happen_after_event": "reminder", "days_after_event": 7, "automated_types": ["retailer_customer_soon_to_lapse", "retailer_customer_soon_to_lapse_filtered", "retailer_customer_soon_to_lapse_secondary_filtered", "retailer_customer_stats_registry_event", "retailer_customer_stats_registry_followup", "transactions_distribution_by_stores", "cancelled_transaction_follow_up", "new_retailer_transaction", "new_retailer_transaction_filtered", "new_retailer_transaction_filtered_multiple", "new_rep_transaction", "rep_transaction_imported", "retailer_transaction_imported", "retailer_transaction_employee_assigned", "retailer_transaction_employee_assigned_multiple"]}], "sf.task.automated.nag_update_storefront_products.enabled": true, "sf.task.automated.nag_update_storefront_products.days_search_back": 30, "sf.task.automated.nag_update_storefront_products.days_recur_after": 30, "sf.task.automated.nag_update_storefront_products.emails_enabled": false, "sf.task.automated.nag_update_storefront_products.notifications_enabled": true, "sf.task.automated.nag_update_storefront_products.category": null, "sf.task.automated.nag_share_update.enabled": true, "sf.task.automated.nag_share_update.days_search_back": 30, "sf.task.automated.nag_share_update.days_recur_after": 30, "sf.task.automated.nag_share_update.emails_enabled": false, "sf.task.automated.nag_share_update.notifications_enabled": true, "sf.task.automated.nag_share_update.category": null, "sf.task.automated.new_retailer_transaction_filtered.enabled": true, "sf.task.automated.new_retailer_transaction_filtered.days_search_back": [30], "sf.task.automated.new_retailer_transaction_filtered.dynamic_rule": [], "sf.task.automated.new_retailer_transaction_filtered.emails_enabled": false, "sf.task.automated.new_retailer_transaction_filtered.notifications_enabled": true, "sf.task.automated.new_retailer_transaction_filtered.category": "Services", "sf.task.automated.new_retailer_transaction_filtered.max_per_owner": 5, "sf.task.automated.new_retailer_transaction_filtered.any_store": false, "sf.task.automated.retailer_transaction_employee_assigned.enabled": false, "sf.task.automated.retailer_transaction_employee_assigned.days_search_back": [30], "sf.task.automated.retailer_transaction_employee_assigned.emails_enabled": false, "sf.task.automated.retailer_transaction_employee_assigned.notifications_enabled": true, "sf.task.automated.retailer_transaction_employee_assigned.category": "Services", "sf.task.automated.retailer_transaction_employee_assigned.matching": true, "sf.task.automated.retailer_transaction_employee_assigned.max_per_owner": 5, "sf.task.automated.retailer_transaction_employee_assigned.dynamic_rule": [], "sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled": true, "sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back": 5, "sf.task.automated.retailer_customer_soon_to_lapse_filtered.emails_enabled": false, "sf.task.automated.retailer_customer_soon_to_lapse_filtered.notifications_enabled": true, "sf.task.automated.retailer_customer_soon_to_lapse_filtered.category": "Services", "sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled": false, "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.enabled": false, "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.days_search_back": 365, "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.emails_enabled": false, "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.notifications_enabled": true, "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.category": "Services", "sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner": 5, "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.max_per_owner": 5, "sf.task.automated.nag_onboarding_update_about_me.enabled": true, "sf.task.automated.nag_onboarding_update_about_me.days_search_back": 30, "sf.task.automated.nag_onboarding_update_about_me.emails_enabled": false, "sf.task.automated.nag_onboarding_update_about_me.notifications_enabled": true, "sf.task.automated.nag_onboarding_update_about_me.category": null, "sf.task.automated.nag_onboarding_upload_profile_pic.enabled": true, "sf.task.automated.nag_onboarding_upload_profile_pic.days_search_back": 30, "sf.task.automated.nag_onboarding_upload_profile_pic.emails_enabled": false, "sf.task.automated.nag_onboarding_upload_profile_pic.notifications_enabled": true, "sf.task.automated.nag_onboarding_upload_profile_pic.category": null, "sf.task.automated.nag_onboarding_connect_social_media.enabled": true, "sf.task.automated.nag_onboarding_connect_social_media.days_search_back": 30, "sf.task.automated.nag_onboarding_connect_social_media.emails_enabled": false, "sf.task.automated.nag_onboarding_connect_social_media.notifications_enabled": true, "sf.task.automated.nag_onboarding_connect_social_media.category": null, "sf.task.automated.nag_onboarding_add_contacts.enabled": true, "sf.task.automated.nag_onboarding_add_contacts.days_search_back": 30, "sf.task.automated.nag_onboarding_add_contacts.emails_enabled": false, "sf.task.automated.nag_onboarding_add_contacts.notifications_enabled": true, "sf.task.automated.nag_onboarding_add_contacts.category": null, "sf.task.automated.new_rep_transaction.enabled": false, "sf.task.automated.new_rep_transaction.days_search_back": 30, "sf.task.automated.new_rep_transaction.emails_enabled": false, "sf.task.automated.new_rep_transaction.notifications_enabled": true, "sf.task.automated.new_rep_transaction.category": "Services", "sf.task.automated.retailer_customer_stats_registry_event.enabled": false, "sf.task.automated.retailer_customer_stats_registry_followup.enabled": false, "sf.task.automated.new_rep_transaction.max_per_owner": 5, "sf.task.automated.transaction.min": 0, "sf.task.automated.transactions_distribution_by_stores.is_enabled": false, "sf.task.automated.transactions_distribution_by_stores.days_search_back": 5, "sf.task.automated.transactions_distribution_by_stores.emails_enabled": false, "sf.task.automated.transactions_distribution_by_stores.notifications_enabled": true, "sf.task.automated.transactions_distribution_by_stores.category": "Services", "sf.task.automated.transactions_distribution_by_stores.max_per_owner": 5, "sf.task.automated.transactions_distribution_by_stores.distance_radius": 99999, "sf.task.automated.transactions_distribution_by_stores.min_trx_total": 0, "sf.task.automated.cancelled_transaction_follow_up.enabled": false, "sf.task.automated.cancelled_transaction_follow_up.category": null, "sf.task.automated.cancelled_transaction_follow_up.emails_enabled": false, "sf.task.automated.cancelled_transaction_follow_up.notifications_enabled": false, "sf.task.automated.cancelled_transaction_follow_up.max_daily_threshold_per_associate": 5, "sf.task.automated.cancelled_transaction_follow_up.import_day_threshold": 1, "sf.task.automated.cancelled_transaction_follow_up.task_creation_criteria": "customer_required", "sf.task.automated.retailer_customer_event.enabled": false, "sf.task.automated.retailer_customer_event.category": "Services", "sf.task.automated.retailer_customer_event.emails_enabled": false, "sf.task.automated.retailer_customer_event.notifications_enabled": true, "sf.task.automated.retailer_customer_event.days_search_back": 0, "sf.task.automated.retailer_customer_event.dynamic_rule": [], "retailer.online_store_id": "0", "inventory.lookup.retailer.online_store_name": "Web", "logrotate.s3.bucket": "sf-dev-platform-logs", "logrotate.s3.provider": "gcp", "logrotate.s3.region": "us-east-1", "logrotate.s3.key": "********************", "logrotate.s3.secret": "GuLb3Xt9sErgerRuJxh6EhXsdCPNuBoCKe0Zz/ie", "logrotate_client.s3.bucket": "", "logrotate_client.s3.provider": "gcp", "logrotate_client.s3.secret": "", "logrotate_client.s3.key": "", "s3.provider": "aws", "retailer.lockout.duration": 20, "retailer.lockout.try": 5, "retailer.lockout.try.duration": 30, "api.locale_dir": "/srv/www/sf_platform/current/api/app/locale", "sf.onboarding.security.secretkey": "239f8hiuv204vihwfRiwoeifjGRG$wqewoek#$5", "sf.onboarding.security.method": "AES-128-CTR", "sf.security.secretkey": "def0000076a412725145a3fa9df7d03b0bbe165230ce52f451823e4e27fe813887b968a477b854df82cdb8187372c59582a53b5d6582f989cda862f33128c3231e7d278d", "retailer.services.findarep.is_enabled": true, "tests.routing.messagev0": "message 0", "tests.routing.messagev1": "bar", "tests.routing.messagev2": "message 2", "tests.routing.messagev3": "message 3", "retailer.is-storename-identifier": false, "retailer.customer_tags.is_enabled": true, "retailer.customer_tags.is_read_only": false, "rep-export.s3.bucket": null, "rep-export.s3.provider": "gcp", "customer-import.sftp.server": null, "customer-import.sftp.username": null, "customer-import.sftp.password": null, "retailer.customer_activity_feed.is_enabled": true, "retailer.customer_activity_feed.max_num_entries": 300, "sf.phone-number-format": 0, "retailer.onboarding.step.add_contacts": false, "retailer.onboarding.step.add_categories": true, "mobile_ios_appid": "com.salesfloor.salesfloor", "mobile_ios_channel": "s3", "mobile_android_appid": "com.salesfloor.salesfloor", "mobile_android_channel": "s3", "mobile_s3_bucket": "mobile-app-version", "mobile_s3_bucket.provider": "aws", "mobile_s3_bucket.region": "us-west-1", "customer-import.sftp.dir": null, "security.policy.password": "legacy", "security.policy.password.zxcvbn.strength": 3, "retailer.combine_countries": false, "retailer.combine_countries_all_distance": true, "aws.cloudwatch.metrics.namespace": "Salesfloor", "importer.trxs.purchase_type": "imported", "importer.transactions.cancels_and_returns.classname": "Salesfloor\\Services\\Importer\\RetailerTransactionCancelsAndReturns\\Saks\\Importer", "importer.transactions.cancels_and_returns.s3.filename.prefix": "Salesfloor_Returns_Cancels", "importer.transactions.cancels_and_returns.s3.filename.extension": null, "importer.transactions.cancels_and_returns.s3.filename.header": true, "importer.transactions.cancels_and_returns.match-transaction-only": false, "importer.transactions.cancels_and_returns.s3.sub-folder": "inbound/dev/transactions_cancels_and_returns/", "importer.transactions.cancels_and_returns.s3.archive-folder": "", "retailer.i18n.products.sync": false, "chat.broadcast.closest_stores": 1, "retailer.countries.text.available": ["CA", "US"], "retailer.countries.text.preferred": ["CA", "US"], "retailer.countries.preferred.threshold": 5, "retailer.countries.application.preferred": ["CA", "US"], "retailer.countries.application.available": ["AU", "AT", "BE", "BR", "CA", "CL", "HR", "CZ", "DK", "EE", "FI", "FR", "GT", "DE", "HU", "HK", "IE", "IL", "IT", "LV", "LT", "MY", "MX", "NL", "NO", "PH", "PL", "PT", "PR", "SG", "ZA", "KR", "ES", "SE", "CH", "TW", "GB", "US", "JP", "IN"], "retailer.shoppage.tracking.additional_params": false, "retailer.shoppage.tracking.leftpad_store_id": false, "retailer.shoppage.tracking.custom_params": [], "retailer.shoppage.tracking.redirect_url_encode_chars": [], "rollback_on_inconsistent_migrations": true, "retailer.sidebar.v3.enabled": false, "retailer.sidebar.v3.mode": "carousel", "retailer.sidebar.v3.logopath": null, "retailer.sidebar.v3.horizontalPosition": "left", "retailer.sidebar.v3.tagline": true, "retailer.sidebar.v3.location": true, "retailer.sidebar.v3.minimize.desktop": false, "retailer.sidebar.v3.minimize.mobile": false, "retailer.sidebar.v3.minimize.icon_url": "https://res.cloudinary.com/salesfloor-net/image/upload/v1553103646/generic/assets/sidebar/chat-bubble.svg", "retailer.sidebar.v3.media.desktop.bottom": 20, "retailer.sidebar.v3.media.desktop.width": 230, "retailer.sidebar.v3.media.desktop.height": 140, "retailer.sidebar.v3.media.mobile.bottom": 0, "retailer.sidebar.v3.media.mobile.width": 275, "retailer.sidebar.v3.media.mobile.height": 64, "retailer.sidebar.v3.animation.enabled": false, "retailer.sidebar.v3.animation.type": "sidebarSpinner", "retailer.sidebar.v3.animation.delay": 15, "retailer.sidebar.v3.alternateTaglines": [], "retailer.sidebar.v3.variant": false, "retailer.sidebar.v3.landingPosition.enabled": true, "retailer.sidebar.v3.landingPosition.horizontalPosition": null, "retailer.sidebar.v3.landingPosition.atBottom": false, "retailer.contextual_widget_events_recording.is_enabled": true, "retailer.services.subscription.enabled": true, "retailer.services.termconditions.enabled": false, "messages.max_displayed": 250, "retailer.chat.option.transfer-to-cs": false, "retailer.chat.option.transfer-to-cs.text.required": false, "retailer.chat.option.transfer-to-cs.redirect.url": null, "retailer.chat.option.video-chat": false, "retailer.chat.option.video-chat.2ways": false, "retailer.virtual.option.video-chat": false, "retailer.virtual.option.video-chat.2ways": false, "retailer.storefront.menu.min.categories": 1, "retailer.storefront.menu.max.categories": 10, "boldchat.enabled": false, "boldchat.username": "bold_chat", "boldchat.password": "not-valid", "retailer.api.username": "tests", "retailer.api.password": "$P$FnSAr5J.b4NcCGH/q1489oRJW/YoKY/", "retailer.store.close_hour": 22, "retailer.store.open_hour": 8, "retailer.store.default_timezone": "America/Montreal", "retailer.store_appointment_hours": [{"open": "9", "close": "22", "is_available": "1"}], "retailer.store_text_hours": [{"open": "9", "close": "22"}], "retailer.chat.option.eject_after_hours": false, "retailer.corporate-email.required": false, "retailer.features": {"multiple_email_templates": "retailer.multiple_email_templates.enabled", "shopify_ci_pipeline": "retailer.clienteling.shopify_importer.enabled"}, "retailer.multiple_email_templates.enabled": false, "retailer.importer.customer.validation": [], "crypto.inbound.general.private_key": "/srv/www/sf_platform/current/services/keys/sf-gpg-private.key", "crypto.inbound.general.public_key": "/srv/www/sf_platform/current/services/keys/sf-gpg-public.key", "crypto.outbound.general.private_key": "/srv/www/sf_platform/current/services/keys/sf-gpg-private.key", "crypto.outbound.general.public_key": "/srv/www/sf_platform/current/services/keys/sf-gpg-public.key", "importer.products.has_variants": false, "importer.products.variant_attributes": [], "sf.report-performance-email.path": "report/email", "importer.products.feed-match-regex": null, "importer.products.file_pattern": null, "retailer.stand_down.forwarded_to_cs.is_enabled": false, "cleanup_operations.devices.days_between_validations": 1, "cleanup_operations.text_message.enabled": true, "exporter.encryption.type": "gpg", "exporter.private_bucket_duplicated.is_enabled": true, "exporter.public_bucket_duplicated.is_enabled": false, "exporter.daily_transaction.encryption.enabled": false, "exporter.daily_transaction_details.encryption.enabled": false, "exporter.text.encryption.enabled": false, "exporter.share_email.encryption.enabled": false, "exporter.weekly_summary.encryption.enabled": false, "exporter.request.encryption.enabled": false, "exporter.message.encryption.enabled": false, "exporter.lookbook.encryption.enabled": false, "exporter.live_chat.encryption.enabled": false, "exporter.email_stats.encryption.enabled": false, "exporter.customer.encryption.enabled": false, "exporter.associate_report.encryption.is_enabled": false, "exporter.user_management.encryption.is_enabled": false, "exporter.transactions.attribution.outbound.enabled": false, "exporter.transactions.attribution.daily.enabled": false, "exporter.transactions.attribution.daily.start_days_before": -4, "exporter.transactions.attribution.daily.end_days_before": -4, "exporter.transactions.attribution.outbound.encryption.enabled": false, "exporter.contact_for_sync.encryption.enabled": false, "exporter.contact.encryption.enabled": false, "exporter.rep_activity_summary.encryption.enabled": false, "exporter.store_activity_summary.encryption.enabled": false, "exporter.rep_live_chat_metrics.encryption.enabled": false, "exporter.store_live_chat_metrics.encryption.enabled": false, "exporter.legacy.sms.daily": false, "exporter.request.daily.enabled": false, "exporter.request.daily.start_days_before": -1, "exporter.request.daily.end_days_before": -1, "exporter.request.zip_split_by": "", "exporter.sms.daily.enabled": false, "exporter.sms.daily.start_days_before": -1, "exporter.sms.daily.end_days_before": -1, "exporter.sms.all.zip_split_by": "", "exporter.livechat.daily.enabled": false, "exporter.livechat.daily.start_days_before": -1, "exporter.livechat.daily.end_days_before": -1, "exporter.live_chat.all.zip_split_by": "", "exporter.live_chat_metrics.rep.daily.enabled": false, "exporter.live_chat_metrics.rep.daily.start_days_before": -1, "exporter.live_chat_metrics.rep.daily.end_days_before": -1, "exporter.live_chat_metrics.store.daily.enabled": false, "exporter.live_chat_metrics.store.daily.start_days_before": -1, "exporter.live_chat_metrics.store.daily.end_days_before": -1, "exporter.transactions.daily.enabled": false, "exporter.transactions.daily.start_days_before": -1, "exporter.transactions.daily.end_days_before": -1, "exporter.transactions.weekly.enabled": false, "exporter.transactions.weekly.start_days_before": -8, "exporter.transactions.weekly.end_days_before": -2, "exporter.transactions.weekly.cron_date_of_week": 1, "exporter.activitySummary.rep.daily.enabled": false, "exporter.activitySummary.rep.daily.start_days_before": -1, "exporter.activitySummary.rep.daily.end_days_before": -1, "exporter.activitySummary.store.daily.enabled": false, "exporter.activitySummary.store.daily.start_days_before": -1, "exporter.activitySummary.store.daily.end_days_before": -1, "exporter.sidebar_metric.daily.enabled": false, "exporter.sidebar_metric.daily.start_days_before": -1, "exporter.sidebar_metric.daily.end_days_before": -1, "exporter.contacts.all.zip_split_by": "", "exporter.contacts.all.query_pagination": false, "exporter.contacts.daily.enabled": false, "exporter.all_contacts_outbound.daily.enabled": false, "exporter.all_contacts_outbound.daily.path": "outbound", "exporter.messages.daily.enabled": false, "exporter.share.email.daily.enabled": false, "exporter.lookbook.daily.enabled": false, "exporter.task.daily.enabled": false, "exporter.task.daily.start_days_before": -2, "exporter.task.daily.end_days_before": -2, "exporter.retailer_customer.daily.enabled": false, "exporter.dynamic_exporter.daily.is_enabled": false, "exporter.dynamic_exporter.exporters": [], "importer.reps.enabled": false, "importer.reps.s3.path": "inbound/{env}/users/", "importer.reps.s3.filename_regexp": "{retailer}-users-\\d{8}-\\d{6}\\.csv", "sf.import_users.encrypted": false, "retailer.prefix_numeric_usernames": false, "importer.shutdown_function.enabled": false, "elasticsearch.index.mapping.nested_objects.limit": 50000, "elasticsearch.max_bulk_size": 20000, "elasticsearch.index.transactions.threshold_month": 18, "retailer.shop_feed.enabled": false, "messaging-json-api.enabled": false, "retailer.transaction.use_mail_relay_queue": true, "php7.disable_warn_method_declaration": false, "php7.disable_countable_warning": false, "importer.fetcher.s3": "importer.fetcher.s3", "importer.ingestor.csv": "importer.ingestor.csv", "products.expanded_variants.enabled": false, "products.expanded_variants.empty_attribute_position_holder": false, "importer.product.use_new_importer": false, "importer.product.s3.filename_regexp": "{retailer}-products-\\d{8}-\\d{6}.csv", "importer.product.s3.path": "inbound/{env}/products/", "importer.product.fetcher": "importer.fetcher.s3.product", "importer.product.ingestor": "importer.ingestor.csv.products", "logrotate.is_enabled": false, "importer.product.fetcher.api.is_paginated": true, "importer.product.fetcher.api.per_page": 10, "importer.product.fetcher.api.start_page": 1, "importer.product.fetcher.api.url": "", "importer.product.fetcher.api.credentials": null, "importer.product.fetcher.direct.type": null, "importer.product.validation.amend": [], "importer.product.validation": {"id": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "parent-id": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "gtin": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 255, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "title": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 250, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "description": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}], "brand": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "link": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "message": "This value is not a valid URL.", "dnsMessage": "The host could not be resolved.", "protocols": ["http", "https"], "checkDNS": false, "relativeProtocol": false, "normalizer": null}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 500, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "image-link": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "message": "This value is not a valid URL.", "dnsMessage": "The host could not be resolved.", "protocols": ["http", "https"], "checkDNS": false, "relativeProtocol": false, "normalizer": null}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 500, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "is-available": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "choices": ["0", "1", 0, 1], "callback": null, "multiple": false, "strict": true, "min": null, "max": null, "message": "The value you selected is not a valid choice.", "multipleMessage": "One or more of the given values is invalid.", "minMessage": "You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.", "maxMessage": "You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices."}], "price": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 11, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^[0-9]+(\\.\\d+)?$/", "htmlPattern": null, "match": true, "normalizer": null}], "sale-price": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 11, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^[0-9]+(\\.\\d+)?$|^$/", "htmlPattern": null, "match": true, "normalizer": null}], "sale-start-date": [{"payload": null, "message": "This value is not a valid date."}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "sale-end-date": [{"payload": null, "message": "This value is not a valid date."}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "category-level1-id": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 30, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "category-level1-title": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 50, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "category-level2-id": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 30, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "category-level2-title": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 50, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "arrival-date": [{"payload": null, "message": "This value is not valid.", "pattern": "/^(?!0000-00-00)\\d{4}-\\d{2}-\\d{2}( \\d\\d?:\\d\\d?:\\d\\d?)?$/", "htmlPattern": null, "match": true, "normalizer": null}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "language-code": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 10, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-1-name": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-1-value": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 150, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-1-group": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 150, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-1-swatch-url": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 500, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not a valid URL.", "dnsMessage": "The host could not be resolved.", "protocols": ["http", "https"], "checkDNS": false, "relativeProtocol": false, "normalizer": null}], "variant-attribute-2-name": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-2-value": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 150, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-2-group": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 150, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-2-swatch-url": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 500, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not a valid URL.", "dnsMessage": "The host could not be resolved.", "protocols": ["http", "https"], "checkDNS": false, "relativeProtocol": false, "normalizer": null}], "variant-attribute-3-name": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-3-value": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 150, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-3-group": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 150, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "variant-attribute-3-swatch-url": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 500, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not a valid URL.", "dnsMessage": "The host could not be resolved.", "protocols": ["http", "https"], "checkDNS": false, "relativeProtocol": false, "normalizer": null}], "is-default": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "choices": ["0", "1", 0, 1], "callback": null, "multiple": false, "strict": true, "min": null, "max": null, "message": "The value you selected is not a valid choice.", "multipleMessage": "One or more of the given values is invalid.", "minMessage": "You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.", "maxMessage": "You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices."}]}, "importer.product.ingestor.api.nested_data_path": ["data"], "importer.transaction.validation": {"parent-id": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "id": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "type": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 255, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "choices": ["sale", "cancel", "return", "delete", "reassign"], "callback": null, "multiple": false, "strict": true, "min": null, "max": null, "message": "The value you selected is not a valid choice.", "multipleMessage": "One or more of the given values is invalid.", "minMessage": "You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.", "maxMessage": "You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices."}], "date": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "message": "This value is not valid.", "pattern": "/^\\d{4}-\\d{2}-\\d{2}[ T]\\d{2}:\\d{2}:\\d{2}([+-]\\d{2}:\\d{2})?$/", "htmlPattern": null, "match": true, "normalizer": null}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 25, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "employee-id": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "store-id": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 45, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "customer-email": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 128, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "customer-name": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 128, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "currency": [{"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 3, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "transaction-total": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 12, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^[0-9]+(\\.\\d+)?$/", "htmlPattern": null, "match": true, "normalizer": null}], "product-id": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 32, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}], "unit-price": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 12, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^[0-9]+(\\.\\d+)?$/", "htmlPattern": null, "match": true, "normalizer": null}], "units": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "groups": ["<PERSON><PERSON><PERSON>"], "constraints": []}, {"payload": null, "maxMessage": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "minMessage": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "exactMessage": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "charsetMessage": "This value does not match the expected {{ charset }} charset.", "max": 12, "min": null, "charset": "UTF-8", "normalizer": null, "allowEmptyString": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^\\d+$/", "htmlPattern": null, "match": true, "normalizer": null}]}, "devops-1420.kludge": false, "autoresponder.ttl": 3600, "algolia.admin.app_id": "WNMJ4Q8002", "algolia.admin.api_key": "********************************", "algolia.read.app_id": "WNMJ4Q8002", "algolia.read.api_key": "********************************", "sf-api": {"apikey": {"key": "1234", "header": "SF-APIKEY"}, "jwt": {"secret": "~2jq9G9!%tVQG#:s8Wa{>sg<aT-%*2LFxdD]_-u#]:D*xSm$FE;=T\"WvJsvmJ7jvf4)]F:", "exp": 600, "ref": 4838400, "header": "Authorization", "prefix": "Bearer"}, "cookie": {"name": "dev_tests_SF-TOKEN"}, "enabled": true}, "sfadmin.user_login": "salesfloor_admin", "sfadmin.password": "bCaref00l!", "sidebar.tracking.enabled": true, "importer.c2c.s3.path": "inbound/{env}/c2c/", "importer.c2c.s3.filename_regexp": "c2c-import-parent-\\d+-\\d{4}-\\d{2}-\\d{2}-.+-\\d{4}.csv", "unknown_incoming_email.tracking_and_reject.is_enabled": true, "notifications.late_messages.enabled": true, "notifications.late_requests.enabled": true, "notifications.near_appointments.enabled": true, "sidebar.pushing.interval": 500, "gcp.queue.queue_ack_deadline_seconds": 30, "gcp.queue.ack_deadline_seconds": 600, "services.queue.ack-extend": true, "sidebar.pushing.bulk.size": 900, "sidebar.message.hide": 900, "importer.contacts.tag_delimiter": [","], "assets.vendor_hash": true, "alerts.failed_import.emails": ["<EMAIL>"], "alerts.salesfloor_admin_changed.emails": ["<EMAIL>"], "alerts.salesfloor_admin_changed.expected_email": "<EMAIL>", "inventory.lookup.is_enabled": false, "inventory.lookup.retailer.endpoint": "", "inventory.lookup.retailer_default_location.type": 1, "inventory.lookup.retailer_default_location.id": "", "retailer.hq_address.street": "651 Notre Dame Ouest, Suite 350", "retailer.hq_address.city": "Montreal", "retailer.hq_address.region": "QC", "retailer.hq_address.isoCountry": "CA", "retailer.hq_address.postalCode": "H3C 1H9", "retailer.hq_address.customerName": "Salesfloor", "retailer.sale_cookie.refreshed": true, "products.expanded_variants.priority_badge.enabled": false, "products.expanded_variants.priority_badge.position": 1, "reps.find_reps.sort.name_alphabetically": true, "importer.rep_transactions.enabled": false, "importer.rep_transactions.skip_stats": false, "importer.customer_attributes.s3.path": "inbound/{env}/customer-extend-attributes", "importer.customer_attributes.s3.filename_regexp": "customer-extend-attributes-\\d{8}-\\d{6}.csv", "user-stores.user_assigned_stores.is_enabled": false, "importer.user_stores.s3.path": "inbound/{env}/userstores", "importer.user_stores.s3.filename_regexp": "{retailer}-userstores-\\d{8}-\\d{6}.csv", "importer.attributes.validation": {"none": [], "alphanumeric": [{"payload": null, "message": "This value is not valid.", "pattern": "/^[\\w\\-\\s]+$/", "htmlPattern": null, "match": true, "normalizer": null}], "alphanumeric_no_space": [{"payload": null, "message": "This value is not valid.", "pattern": "/^[a-zA-Z0-9\\-_]+$/", "htmlPattern": null, "match": true, "normalizer": null}], "numeric_only": [{"payload": null, "message": "This value is not valid.", "pattern": "/^[0-9]+$/", "htmlPattern": null, "match": true, "normalizer": null}], "int": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "message": "This value should be of type {{ type }}.", "type": "int"}], "decimal": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^-?\\d*\\.?\\d*$/", "htmlPattern": null, "match": true, "normalizer": null}], "boolean": [{"payload": null, "message": "This value should not be blank.", "allowNull": false, "normalizer": null}, {"payload": null, "message": "This value is not valid.", "pattern": "/^[01]{1}$/", "htmlPattern": null, "match": true, "normalizer": null}], "alpha_only": [{"payload": null, "message": "This value is not valid.", "pattern": "/^[a-zA-Z]+$/", "htmlPattern": null, "match": true, "normalizer": null}], "date": [{"payload": null, "message": "This value is not a valid date."}], "time": [{"payload": null, "message": "This value is not a valid time."}], "datetime": [{"payload": null, "format": "Y-m-d H:i:s", "message": "This value is not a valid datetime."}], "url": [{"payload": null, "message": "This value is not a valid URL.", "dnsMessage": "The host could not be resolved.", "protocols": ["http", "https"], "checkDNS": false, "relativeProtocol": false, "normalizer": null}], "email": [{"payload": null, "message": "This value is not a valid email address.", "checkMX": false, "checkHost": false, "strict": null, "mode": null, "normalizer": null}], "extend": []}, "retailer.services.appointment_management.is_enabled": false, "retailer.services.appointment_management.notify_without_consent": true, "retailer.services.appointment_management.all_customer.is_enabled": false, "retailer.services.appointment.save_to_device.is_enabled": false, "retailer.services.appointment.all.is_enabled": false, "retailer.services.appointment_reassignment.is_enabled": false, "security.pii.crypto.enabled": true, "twilio.available.phone.numbers.distance": 5, "retailer.clienteling.customers.communication.blackout.is_enabled": false, "retailer.clienteling.customers.communication.blackout.all_matched_customers": false, "retailer.clienteling.customers.communication.blackout.period": 604800, "mfa.authentication.is_enabled": false, "retailer.storefront.display_state": true, "mfa.authentication.type": "email", "mfa.authentication.otp.valid_period": 600, "mfa.authentication.valid_window": 2592000, "mfa.authentication.otp.length": 8, "retailer.backoffice.pii.is_visible": true, "retailer.backoffice.reports.filter_maximum_stores": 50, "trustee.proxy.ip_range": [], "retailer.routing.configs.is_public": false, "retailer.specialties.is_enabled": true, "retailer.specialties.can_select": false, "retailer.specialties.is_required": false, "retailer.specialties.has_holidays": false, "retailer.specialties.exclude": [], "retailer.specialties.mapping": [], "retailer.specialties.appending": [], "retailer.password_update.is_enabled": false, "service.pushnotification": "aws", "fcm.key.secret_file": false, "fcm.key": "fcmkey", "fcm.project_id": "project_id", "fcm.version": "v1", "fcm.verification.url": "https://iid.googleapis.com/iid/", "services.metrics": "aws", "services.metrics.create_alarms": false, "logger.slack.channel.debug": "alert-debug-tests", "monolog.debug.tickets": [], "redirect_whitelist.is_enabled": true, "redirect_whitelist.slack_notice.is_enabled": true, "redirect_whitelist.slack_notice.channel": "security-notification-test", "redirect_whitelist.domains.global": ["salesfloor.net"], "redirect_whitelist.domains": ["example.com"], "retailer.multi_domains.is_enabled": false, "retailer.multi_domains.short_name": "", "retailer.multi_domains.locale": "", "retailer.outfits.is_enabled": true, "retailer.outfits.section_label": "My Closet", "retailer.outfits.stylitics.endpoint": "https://widget-api.stylitics.com/api/outfits", "retailer.outfits.external_service": "stylitics", "retailer.outfits.mapper": "Chicos", "retailer.outfits.account": "chicos", "retailer.outfits.mobile.transaction_per_page": 20, "encryption.use_case.default": ["email_unsubscribe"], "encryption.use_case.extra": ["sodium_aead_chacha20poly1305", "none", "url", "base64"], "encryption.use_case.email_unsubscribe.is_enabled": false, "encryption.use_case.email_unsubscribe.strategy": "Url", "encryption.use_case.email_unsubscribe.secret_key": null, "retailer.unsubscribe_link": null, "oauth.io.key": "U7Gw0dWc2hfk55sZjkyYcR2hhJU", "oauth.io.secret": "haaOXGLsamrwL70dZ6RyReEqjbs", "retailer.sso.is_enabled": false, "retailer.sso.provider.type": "Azure", "retailer.sso.provider.scope": "openid email profile", "retailer.sso.login.prompt": "select_account", "retailer.sso.identity": "email", "retailer.sso.provider.tenant": "TO BE REPLACED", "retailer.sso.provider.client_id": "TO BE REPLACED", "retailer.sso.provider.client_id.mobile": "", "retailer.sso.provider.server": "https://login.microsoftonline.com/TO BE REPLACED", "retailer.sso.provider.authorize_endpoint": "/oauth2/v2.0/authorize", "retailer.sso.provider.token_endpoint": "/oauth2/v2.0/token", "retailer.sso.provider.token_endpoint.version": "v2.0", "retailer.sso.provider.public_keys.endpoint": "/discovery/keys", "retailer.sso.provider.redirect_url.mobile": "http://localhost:9000/oauth/code", "retailer.sso.provider.redirect_url.web": "https://tests.dev.salesfloor.net/app", "retailer.sso.provider.url.userinfo_endpoint": "TO BE REPLACED", "retailer.sso.provider.client_secret": "TO BE REPLACED", "retailer.chat.find_nearby_stores": false, "retailer.chat.find_nearby_stores.max_distance": "35", "retailer.chat_availability_status.sidebar.enabled": true, "sf.import_unsubs.s3.bucket": "sf-tests", "sf.import_unsubs.s3.path": "test-unsubscribes", "sf.import_unsubs.s3.filename_regexp": "{retailer}-unsubscribes-\\d{8}-\\d{6}.csv", "sf.import_sub_unsubs.s3.path": "test-subscribe-unsubscribe", "sf.import_sub_unsubs.s3.filename_regexp": "{retailer}-subscribe-unsubscribe-_\\d{8}\\d{6}.csv", "currency.symbol": "$", "name_fmt.default": "{Fn}", "name_fmt.default.storefront_product_comment": "{Fn}", "name_fmt.default.global_email_username": "{Fn}", "name_fmt.default.rep_display_name": "{Fn}", "name_fmt.default.rep_store_display_name": "{Fn} {Ln}", "name_fmt.default.store_display_name": "{Fn} {Ln}", "name_fmt.default.event_display_name": "{Fn}", "name_fmt.default.team_mode_rep_display_name": "{Fn}", "name_fmt.default.team_mode_email_from_name": "{Fn}", "name_fmt.default.rep_mode_email_from_name": "{Fn}", "name_fmt.default.findrep_name": "{Fn}", "name_fmt.default.bo_services_rep_name": "{Fn}", "name_fmt.default.prepare_user_name": "{Fn}", "name_fmt.default.email_onboarding": "{Fn}", "name_fmt.default.recover_password_rep_name": "{Fn}", "name_fmt.default.email_appt_calendar": "{Fn}", "name_fmt.default.customer_name_reschedule_appt": "{Fn} {Ln}", "name_fmt.default.customer_name_cancel_confirm_appt": "{Fn} {Ln}", "name_fmt.default.rep_mode_email_lookbook": "{Fn}", "name_fmt.default.rep_mode_email_lookbook_subject": "{Fn}", "name_fmt.default.team_mode_email_lookbook": "{Fn} {Ln}", "name_fmt.default.team_mode_email_lookbook_subject": "{Fn} {Ln}", "name_fmt.default.lookbook_hello_customer_name": "{Fn} {Ln}", "name_fmt.default.email_autoresponder": "{Fn}", "name_fmt.default.email_leads_forward_to_customer_rep_name": "{Fn}", "name_fmt.default.email_leads_forward_to_customer_cs_rep_from_name": "{Fn}", "name_fmt.default.email_leads_email_to_customer_rep_name": "{Fn}", "name_fmt.default.mobile_dashboard": "{Fi.} {Ln}", "name_fmt.default.mobile_contact": "{Fn} {Ln}", "name_fmt.default.chat_rep_name": "{Fn} {Li.}", "name_fmt.default.placeholder_customer_name": "{Fn}", "name_fmt.default.group_task_rep_name": "{Fn} {Li.}", "name_fmt.default.chat_customer_name": "{Fn} {Ln}", "name_fmt.default.store_request_rep_name": "{Fn} {Ln}", "name_fmt.default.store_request_customer_name": "{Fn} {Ln}", "name_fmt.default.sidebar_carousel_rep_name": "{Fn}", "name_fmt.ja_JP.storefront_product_comment": "{Ln}", "name_fmt.ja_JP.global_email_username": "{Ln}", "name_fmt.ja_JP.rep_display_name": "{Ln}", "name_fmt.ja_JP.rep_store_display_name": "{Fn} {Ln}", "name_fmt.ja_JP.store_display_name": "{Fn} {Ln}", "name_fmt.ja_JP.event_display_name": "{Ln}", "name_fmt.ja_JP.team_mode_rep_display_name": "{Ln}", "name_fmt.ja_JP.team_mode_email_from_name": "{Ln}", "name_fmt.ja_JP.rep_mode_email_from_name": "{Ln}", "name_fmt.ja_JP.findrep_name": "{Fn} {Ln}", "name_fmt.ja_JP.bo_services_rep_name": "{Fn} {Ln}", "name_fmt.ja_JP.prepare_user_name": "{Ln}", "name_fmt.ja_JP.email_onboarding": "{Ln}", "name_fmt.ja_JP.recover_password_rep_name": "{Ln}", "name_fmt.ja_JP.email_appt_calendar": "{Ln}", "name_fmt.ja_JP.customer_name_reschedule_appt": "{Ln} {Fn}", "name_fmt.ja_JP.customer_name_cancel_confirm_appt": "{Ln} {Fn}", "name_fmt.ja_JP.rep_mode_email_lookbook": "{Ln}", "name_fmt.ja_JP.rep_mode_email_lookbook_subject": "{Ln}", "name_fmt.ja_JP.team_mode_email_lookbook": "{Ln} {Fn}", "name_fmt.ja_JP.team_mode_email_lookbook_subject": "{Ln} {Fn}", "name_fmt.ja_JP.lookbook_hello_customer_name": "{Ln} {Fn}", "name_fmt.ja_JP.email_autoresponder": "{Ln}", "name_fmt.ja_JP.email_leads_forward_to_customer_rep_name": "{Ln}", "name_fmt.ja_JP.email_leads_forward_to_customer_cs_rep_from_name": "{Ln}", "name_fmt.ja_JP.email_leads_email_to_customer_rep_name": "{Ln}", "name_fmt.ja_JP.mobile_dashboard": "{Ln} {Fn}", "name_fmt.ja_JP.mobile_contact": "{Ln} {Fn}", "name_fmt.ja_JP.chat_rep_name": "{Ln}", "name_fmt.ja_JP.placeholder_customer_name": "{Ln}", "name_fmt.ja_JP.group_task_rep_name": "{Ln}", "name_fmt.ja_JP.chat_customer_name": "{Ln} {Fn}", "name_fmt.ja_JP.store_request_rep_name": "{Ln} {Fn}", "name_fmt.ja_JP.store_request_customer_name": "{Ln} {Fn}", "sender_fmt.default": "Salesfloor", "sender_fmt.default.rep_mode_email_lookbook": "{rep}, {retailer}", "sender_fmt.default.team_mode_email_lookbook": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_compose_email": "{rep}, {retailer}", "sender_fmt.default.team_mode_compose_email": "{rep}, {retailer}", "sender_fmt.default.rep_mode_new_apt_to_cust": "{rep}, {retailer}", "sender_fmt.default.team_mode_new_apt_to_cust": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_apt_accept_by_rep": "{rep}, {retailer}", "sender_fmt.default.team_mode_apt_accept_by_rep": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_apt_accept_resc_by_rep": "{retailer}", "sender_fmt.default.team_mode_apt_accept_resc_by_rep": "{retailer}", "sender_fmt.default.rep_mode_apt_resc_success_customer": "{retailer}", "sender_fmt.default.team_mode_apt_resc_success_customer": "{retailer}", "sender_fmt.default.rep_mode_apt_remind_24h": "{rep}, {retailer}", "sender_fmt.default.team_mode_apt_remind_24h": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_apt_remind_1h": "{rep}, {retailer}", "sender_fmt.default.team_mode_apt_remind_1h": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_apt_remind_now": "{rep}, {retailer}", "sender_fmt.default.team_mode_apt_remind_now": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_apt_cancel": "{rep}, {retailer}", "sender_fmt.default.team_mode_apt_cancel": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_apt_no_response": "{rep}, {retailer}", "sender_fmt.default.team_mode_apt_no_response": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_request_success": "{store_name}, {retailer}", "sender_fmt.default.team_mode_request_success": "{store_name}, {retailer}", "sender_fmt.default.rep_mode_publisher": "{rep}, {retailer}", "sender_fmt.default.team_mode_publisher": "{rep}, {retailer}", "sender_fmt.default.rep_mode_ics_summary": "{retailer} - {store_name} - {rep}", "sender_fmt.default.team_mode_ics_summary": "{retailer} - {store_name}", "feed_scanner.folder_path_template": "inbound/{env}/{feedType}/scanner/", "s3.userimage": "sf-dev-tests-public", "s3.userimage.region": "us-east-1", "s3.userimage.provider": "gcp", "retailer.store_appointment_hours.group_permissions": [2], "retailer.store_appointment_hours.first_day_of_week": 0, "retailer.store_appointment_hours.is_enabled": true, "alerts.operations.emails": ["<EMAIL>"], "retailer_customer.associate_relationships": true, "remote_appointment.isc.chat_link": false, "retailer.add_customer_to_my_contacts.is_enabled": false, "retailer.add_customer_to_my_contacts.default_subscription_status": 0, "retailer.add_customer_to_my_contacts.as_secondary": false, "zoom.keys_dir": "/opt/salesfloor/keys", "zoom.api_dir": "/opt/salesfloor/lib/zoom", "zoom.download_url": "/plugin/zoomusInstaller.pkg", "cans.internal.url": "http://cans:3030", "cans.api.version": "v1.1", "retailer.prd.sf_domain": null, "env.proto": "https", "branch.base_url": "https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT", "tier": null, "retailer.modular_connect.storefront.is_enabled": true, "retailer.modular_connect.appointment_request.is_enabled": true, "retailer.modular_connect.can_add_contacts": true, "retailer.modular_connect.corporate_tasks.is_enabled": true, "retailer.modular_connect.can_change_communication_consent": true, "lure.cta_text": [{"en_US": "Connect with an associate", "fr_CA": "Jo<PERSON>re un associé"}], "typhoon.url": "https://typhoon-playground-leiyang.play.salesfloor.net", "connect2.bot.integrationId": "", "connect2.conversation.entrypoint": "01.01. <PERSON><PERSON>/Sidebar: Default", "connect2.conversation.timeout": 1800000, "connect2.saas.organizationId": "", "connect2.saas.brandId": "", "connect2.enabled": false, "retailer.modular_connect.extended_attributes.is_enabled": true, "retailer.modular_connect.can_export_contacts": true, "retailer.grouped_products.is_enabled": false, "retailer.grouped_products.url": "url_to_be_replaced", "retailer.grouped_products.max_products": 10, "retailer.grouped_products.min_products": 2, "retailer.label.toggle-send-styled-link": "Send Styled Link", "importer.addresses.fetcher": "importer.fetcher.web.addresses", "importer.addresses.ingestor": "importer.ingestor.csv", "retailer.label.group-tasks": "Group Tasks", "retailer.group_tasks.is_enabled": false, "importer.group_tasks.s3.path": "inbound/{env}/group-tasks/", "importer.group_tasks.s3.filename_regexp": "{retailer}-group-tasks-\\d{8}-\\d{6}.csv", "sf.group_tasks.reminder.cron": 5, "sf.group_tasks.reminder.emails_enabled": false, "sf.group_tasks.reminder.notifications_enabled": false, "retailer.pii.obfuscate.is_enabled": false, "retailer.search.filters.transaction.max": 20000, "transactions.indexer.buffer": 1800, "retailer.moderation.text.is_enabled": false, "retailer.moderation.text.timeout": 2000, "retailer.moderation.image.is_enabled": true, "hive.text.api_key": "awQzeTfTGxF8u0vLFNwSwM64qF2UKmEc", "hive.image.api_key": "kOgGS2rCmY8GWBJoWI76W7LwOEVWVhuF", "hive.text.multilevel.threshold": 2, "hive.image.score.threshold": 0.9, "hive.text.custom.threshold": [], "hive.image.custom.threshold": [], "retailer.chat.photo_attachment.is_enabled": false, "retailer.transaction.show_product_color_as_upc": false, "retailer.appointment_lead_time.widget": {"store": 240, "virtual": 240, "phone": 240}, "retailer.appointment_lead_time.mobile_app": {"store": 0, "virtual": 0, "phone": 0}, "retailer.products.category.max.level": 4, "firebase.apps.web.configuration": {"apiKey": "AIzaSyBQmu4YHCb0htRe4-vNWR2ZYT8maxGJz_8", "authDomain": "radiant-fire-9638.firebaseapp.com", "databaseURL": "https://radiant-fire-9638.firebaseio.com", "projectId": "radiant-fire-9638", "storageBucket": "radiant-fire-9638.appspot.com", "messagingSenderId": "1070259995388", "appId": "1:1070259995388:web:99af12bf27f5edaa408d79", "measurementId": "G-S4185TM8FM"}, "firebase.apps.web.vapid_key": "BMkqKD0brFtzi9jg6-Wnt7RLA7piKubJS42iGYYEgHuUHNGAuNXIIqrBIBsURYP9R8reQ3ie3O99Sk2YJ7BRFkI", "lure.special.mobile": false, "encryption.use_case.sodium_aead_chacha20poly1305.secret_key": "VFGaTPYgOeX_NEyfdKaEsIWhdjGAEGSk2adRYwR1_K4", "shop_by_brand.services.username": "swapl", "shop_by_brand.services.password": "zGzrKBXyBP", "retailers.name": "tests", "retailer.pretty_name": "tests", "retailers.url": "http://www.salesfloor.net", "firebase.retailername": "tests", "catalog.force.path": "/srv/www/sf_platform/current/api/app/tests/fixtures/products-catalog.csv", "retailers.short": "tests", "retailer.domain": "salesfloor.net", "test.url": "https://saks-api-stg.salesfloor.net", "test.store_id": 601, "test.store_name": "new-york", "test.reggie_id": 1, "mobile.menu_logo_path": "/img/retailers/tests/tests_p_menu.png", "mobile.onboarding_logo_path": "/img/retailers/tests/tests-logo-onboarding.png", "retailer.label.chat.exception": "Connect to chat", "sf.task.automated.new_retailer_transaction.enabled": true, "sf.task.automated.retailer_customer_soon_to_lapse.enabled": true, "sf.import_unsubs.s3.region": "us-east-1", "sf.import_unsubs.s3.signature": "v4", "elasticsearch.wait_for": true, "encryption.use_case.sodium_aead_chacha20poly1305.is_enabled": true, "encryption.use_case.sodium_aead_chacha20poly1305.strategy": "SodiumAeadChacha20Poly1305Base64Safe", "encryption.use_case.none.is_enabled": true, "encryption.use_case.none.strategy": "Base", "encryption.use_case.url.is_enabled": true, "encryption.use_case.url.strategy": "Url", "encryption.use_case.base64.is_enabled": true, "encryption.use_case.base64.strategy": "Base64", "shop_by_brand.services.enabled": true, "shop_by_brand.services.url": "https://www.shiseido.co.jp/sw/api/auth/V1/SWFR070480.seam?tenpo_cd=123456", "mobile.retailer-id": "tests-dev", "sf_posts_dir": "/srv/www/sf_platform/current/configs/posts/generic", "retailer_posts_dir": "/srv/www/sf_platform/current/configs/posts/tests", "firebase.defaultpath": "/tests/tests", "queue.prepare-share": "tests-prepare-share-dev", "queue.share-an-update": "tests-share-an-update-dev", "queue.cakemail-relays": "tests-cakemail-relays-dev", "queue.cakemail-relays-dead-letter": "tests-cakemail-relays-dead-letter-dev", "queue.match-customers.customers": "tests-match-customers-dev", "queue.match-customers.retailer-customers": "tests-match-retailer-customers-dev", "queue.events.base_name": "events", "queue.events.full_name": "tests-events-dev", "queue.events.dead_letter_name": "tests-events-dead-letter-dev", "queue.text_messaging_enable.base_name": "messaging-text-enable", "queue.text_messaging_enable.full_name": "tests-messaging-text-enable-dev", "queue.text_messaging_enable.dead_letter_name": "tests-messaging-text-enable-dead-letter-dev", "queue.transaction_customer.base_name": "transaction-customer", "queue.transaction_customer.full_name": "tests-transaction-customer-dev", "queue.transaction_customer.dead_letter_name": "tests-transaction-customer-dead-letter-dev", "queue.record-transactions.full_name": "tests-record-transactions-dev", "queue.record-transactions.dead_letter_name": "tests-record-transactions-dead-letter-dev", "queue.corporate-tasks.base_name": "corporate-tasks", "queue.corporate-tasks.full_name": "tests-corporate-tasks-dev", "queue.corporate-tasks.dead_letter_name": "tests-corporate-tasks-dead-letter-dev", "queue.sidebar-events.base_name": "sidebar-events", "queue.sidebar-events.full_name": "tests-sidebar-events-dev", "queue.sidebar-events.dead_letter_name": "tests-sidebar-events-dead-letter-dev", "queue.text_message_sending.base_name": "messaging-text-sending", "queue.text_message_sending.full_name": "tests-messaging-text-sending-dev", "queue.text_message_sending.dead_letter_name": "tests-messaging-text-sending-dead-letter-dev", "queue.push_notification.base_name": "push-notification", "queue.push_notification.full_name": "tests-push-notification-dev", "queue.push_notification.dead_letter_name": "tests-push-notification-dead-letter-dev", "queue.push_notification.number_of_messages": 1, "queue.push_notification.hide_message_on_pull": 120, "queue.sendgrid-resubscribe.base_name": "sendgrid-resubscribe", "queue.sendgrid-resubscribe.full_name": "tests-sendgrid-resubscribe-dev", "queue.sendgrid-resubscribe.dead_letter_name": "tests-sendgrid-resubscribe-dead-letter-dev", "cans.url": "https://tests.api.dev.salesfloor.net/microservice/cans", "retailers.current.id": "tests-dev", "mapping": {"rep-onboarding": {"api-response": {"ID": {"key": "ID", "type": "int"}, "token": {"key": "token", "type": "string"}, "retailer_rep_id": {"key": "retailer_rep_id", "type": "string"}, "rep_first_name": {"key": "rep_first_name", "type": "string"}, "rep_last_name": {"key": "rep_last_name", "type": "string"}, "rep_title": {"key": "rep_title", "type": "string"}, "rep_email": {"key": "rep_email", "type": "string"}, "page": {"key": "page", "type": "int"}, "wp_user_id": {"key": "wp_user_id", "type": "int"}, "steps_completed": {"key": "steps_completed", "type": "int"}, "team_page_rep": {"key": "team_page_rep", "type": "int"}, "rep_login": {"key": "rep_login", "type": "string"}, "store": {"key": "store", "type": "int"}, "group": {"key": "group", "type": "int"}, "selling_mode": {"key": "selling_mode", "type": "string"}, "onboarding_completed": {"key": "onboarding_completed", "type": "int"}, "text_messaging_enabled": {"key": "text_messaging_enabled", "type": "int"}, "creation_source": {"key": "creation_source", "type": "string"}, "password": {"key": "password", "type": "string"}, "shop_feed": {"key": "shop_feed", "type": "int"}, "created_by": {"key": "created_by", "type": "int"}, "updated_by": {"key": "updated_by", "type": "int"}, "created_at": {"key": "created_at", "type": "string"}, "updated_at": {"key": "updated_at", "type": "string"}, "sso_auth": {"key": "sso_auth", "type": "int"}, "group_label": {"key": "group_label", "type": "string"}, "store_name": {"key": "store_name", "type": "string"}, "created_user_name": {"key": "created_user_name", "type": "string"}, "updated_user_name": {"key": "updated_user_name", "type": "string"}, "retailer_shop_feed": {"key": "retailer_shop_feed", "type": "boolean"}, "retailer_text_messaging": {"key": "retailer_text_messaging", "type": "boolean"}}}, "Customer": {"api-request": {"user_id": {"key": "user_id", "type": "int"}, "first_name": {"key": "first_name", "type": "string"}, "last_name": {"key": "last_name", "type": "string"}, "name": {"key": "name", "type": "string"}, "email": {"key": "email", "type": "string"}, "label_email": {"key": "label_email", "type": "string"}, "note": {"key": "note", "type": "string"}, "phone": {"key": "phone", "type": "string"}, "label_phone": {"key": "label_phone", "type": "string"}, "country": {"key": "country", "type": "string"}, "user_login": {"key": "user_login", "type": "string"}, "origin": {"key": "origin", "type": "string"}, "locale": {"key": "locale", "type": "string"}, "source": {"key": "source", "type": "string"}, "additionalEmails": {"key": "additionalEmails", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "email": {"key": "value", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "creationDate": {"key": "creation_date", "type": "string"}, "modificationDate": {"key": "modification_date", "type": "string"}}}, "additionalPhones": {"key": "additionalPhones", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "phone": {"key": "value", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "country": {"key": "country", "type": "string"}, "creationDate": {"key": "creation_date", "type": "string"}, "modificationDate": {"key": "modification_date", "type": "string"}}}, "subcribtion_flag": {"key": "subcribtion_flag", "type": "int"}, "sms_marketing_subscription_flag": {"key": "sms_marketing_subscription_flag", "type": "int"}, "contact_preference": {"key": "contact_preference", "type": "string", "validation": {"required": true}}, "additionalNotes": {"key": "additionalNotes", "type": "object"}, "pre_customer_id": {"key": "pre_customer_id", "type": "int"}, "request_type": {"key": "request_type", "type": "string"}, "signature": {"key": "signature", "type": "string"}, "store_id": {"key": "store_id", "type": "int"}}, "api-response": {"ID": {"key": "ID", "type": "int"}, "user_id": {"key": "user_id", "type": "int"}, "first_name": {"key": "first_name", "type": "string"}, "name": {"key": "name", "type": "string"}, "last_name": {"key": "last_name", "type": "string"}, "email": {"key": "email", "type": "string"}, "label_email": {"key": "label_email", "type": "string"}, "note": {"key": "note", "type": "string"}, "phone": {"key": "phone", "type": "string"}, "label_phone": {"key": "label_phone", "type": "string"}, "type": {"key": "type", "type": "string"}, "origin": {"key": "origin", "type": "string"}, "locale": {"key": "locale", "type": "string"}, "is_favorite_contact": {"key": "is_favorite_contact", "type": "boolean"}, "additionalEmails": {"key": "additionalEmails", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "email", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "creation_date": {"key": "creationDate", "type": "string"}, "modification_date": {"key": "modificationDate", "type": "string"}}}, "additionalPhones": {"key": "additionalPhones", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "phone", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "creation_date": {"key": "creationDate", "type": "string"}, "modification_date": {"key": "modificationDate", "type": "string"}}}, "additionalNotes": {"key": "additionalNotes", "type": "object"}, "subcribtion_flag": {"key": "subcribtion_flag", "type": "int"}, "sms_marketing_subscription_flag": {"key": "sms_marketing_subscription_flag", "type": "int"}, "contact_preference": {"key": "contact_preference", "type": "string", "validation": {"required": true}}, "groups": {"key": "groups", "type": "object"}, "isRelatedRetailerCustomer": {"key": "isRelatedRetailerCustomer", "type": "int"}, "isRelatedRetailerCustomerTransactions": {"key": "isRelatedRetailerCustomerTransactions", "type": "int"}, "relatedRetailerCustomers": {"key": "relatedRetailerCustomers", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "customer_id": {"key": "customer_id", "type": "string"}, "gender": {"key": "gender", "type": "string"}, "first_name": {"key": "first_name", "type": "string"}, "last_name": {"key": "last_name", "type": "string"}, "email": {"key": "email", "type": "string"}, "email_label": {"key": "email_label", "type": "string"}, "phone": {"key": "phone", "type": "string"}, "phone_label": {"key": "phone_label", "type": "string"}, "address_line1": {"key": "address_line1", "type": "string"}, "address_line2": {"key": "address_line2", "type": "string"}, "zipcode": {"key": "zipcode", "type": "string"}, "postalcode": {"key": "postalcode", "type": "string"}, "city": {"key": "city", "type": "string"}, "state": {"key": "state", "type": "string"}, "country": {"key": "country", "type": "string"}, "longitude": {"key": "longitude", "type": "string"}, "latitude": {"key": "latitude", "type": "string"}, "additionalEmails": {"key": "additionalEmails", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "email", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "created_at": {"key": "createdAt", "type": "string"}, "updated_at": {"key": "updatedAt", "type": "string"}}}, "additionalPhones": {"key": "additionalPhones", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "phone", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "created_at": {"key": "createdAt", "type": "string"}, "updated_at": {"key": "updatedAt", "type": "string"}}}, "additionalNotes": {"key": "additionalNotes", "type": "object"}, "created_at": {"key": "created_at", "type": "string"}, "updated_at": {"key": "updated_at", "type": "string"}, "is_subscribed": {"key": "is_subscribed", "type": "int"}}}, "socialMedia": {"key": "socialMedia", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "social_media_network_id": {"key": "social_media_network_id", "type": "int"}, "username": {"key": "username", "type": "string"}}}, "events": {"key": "events", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "label": {"key": "label", "type": "string"}, "day": {"key": "day", "type": "int"}, "month": {"key": "month", "type": "int"}, "year": {"key": "year", "type": "int"}}}, "count_tasks": {"key": "count_tasks", "type": "int"}, "count_tasks_due": {"key": "count_tasks_due", "type": "int"}, "count_customer_notes": {"key": "count_customer_notes", "type": "int"}, "customer_tags": {"key": "customer_tags", "type": "virtual_array"}, "addresses": {"key": "addresses", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "customer_id": {"key": "customer_id", "type": "string"}, "address_line_1": {"key": "address_line_1", "type": "string"}, "address_line_2": {"key": "address_line_2", "type": "string"}, "postal_code": {"key": "postal_code", "type": "string"}, "state": {"key": "state", "type": "string"}, "city": {"key": "city", "type": "string"}, "country": {"key": "country", "type": "string"}, "label": {"key": "label", "type": "string"}, "is_default": {"key": "is_default", "type": "boolean"}, "created_at": {"key": "created_at", "type": "string"}, "updated_at": {"key": "updated_at", "type": "string"}}}, "retailer_customer_stats_insights": {"key": "retailer_customer_stats_insights", "type": "virtual_array"}}}, "RetailerCustomer": {"api-response": {"id": {"key": "id", "type": "int"}, "customer_id": {"key": "customer_id", "type": "string"}, "gender": {"key": "gender", "type": "string"}, "first_name": {"key": "first_name", "type": "string"}, "last_name": {"key": "last_name", "type": "string"}, "email": {"key": "email", "type": "string"}, "email_label": {"key": "email_label", "type": "string"}, "phone": {"key": "phone", "type": "string"}, "phone_label": {"key": "phone_label", "type": "string"}, "address_line1": {"key": "address_line1", "type": "string"}, "address_line2": {"key": "address_line2", "type": "string"}, "zipcode": {"key": "zipcode", "type": "string"}, "postalcode": {"key": "postalcode", "type": "string"}, "city": {"key": "city", "type": "string"}, "state": {"key": "state", "type": "string"}, "country": {"key": "country", "type": "string"}, "longitude": {"key": "longitude", "type": "string"}, "latitude": {"key": "latitude", "type": "string"}, "additionalEmails": {"key": "additionalEmails", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "email", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "created_at": {"key": "createdAt", "type": "string"}, "updated_at": {"key": "updatedAt", "type": "string"}}}, "additionalPhones": {"key": "additionalPhones", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "phone", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "created_at": {"key": "createdAt", "type": "string"}, "updated_at": {"key": "updatedAt", "type": "string"}}}, "additionalNotes": {"key": "additionalNotes", "type": "object"}, "created_at": {"key": "created_at", "type": "string"}, "updated_at": {"key": "updated_at", "type": "string"}, "is_subscribed": {"key": "is_subscribed", "type": "int"}, "isRelatedCustomer": {"key": "isRelatedCustomer", "type": "int"}, "isRelatedRetailerCustomerTransactions": {"key": "isRelatedRetailerCustomerTransactions", "type": "int"}, "relatedCustomers": {"key": "relatedCustomers", "type": "array", "children": {"ID": {"key": "ID", "type": "int"}, "user_id": {"key": "user_id", "type": "int"}, "first_name": {"key": "first_name", "type": "string"}, "name": {"key": "name", "type": "string"}, "last_name": {"key": "last_name", "type": "string"}, "email": {"key": "email", "type": "string"}, "label_email": {"key": "label_email", "type": "string"}, "note": {"key": "note", "type": "string"}, "phone": {"key": "phone", "type": "string"}, "label_phone": {"key": "label_phone", "type": "string"}, "type": {"key": "type", "type": "string"}, "additionalEmails": {"key": "additionalEmails", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "email", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "creation_date": {"key": "creationDate", "type": "string"}, "modification_date": {"key": "modificationDate", "type": "string"}}}, "additionalPhones": {"key": "additionalPhones", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "value": {"key": "phone", "type": "string"}, "label": {"key": "label", "type": "string"}, "position": {"key": "position", "type": "string"}, "creation_date": {"key": "creationDate", "type": "string"}, "modification_date": {"key": "modificationDate", "type": "string"}}}, "additionalNotes": {"key": "additionalNotes", "type": "object"}, "subcribtion_flag": {"key": "subcribtion_flag", "type": "int"}, "groups": {"key": "groups", "type": "object"}}}, "retailer_customer_stats_insights": {"key": "retailer_customer_stats_insights", "type": "virtual_array"}, "retailer_customer_tags": {"key": "retailer_customer_tags", "type": "virtual_array"}}}, "ProductPanel": {"db": {"id": {"key": "id", "type": "int"}, "user_id": {"key": "user_id", "type": "int"}, "from_user_id": {"key": "from_user_id", "type": "int"}, "description": {"key": "description", "type": "generic"}, "product_sku": {"key": "product_sku", "type": "string"}, "position": {"key": "position", "type": "int"}, "date": {"key": "date", "type": "string"}, "autoselected": {"key": "autoselected", "type": "int"}}, "api-request": {"user_id": {"key": "user_id", "type": "int"}, "from_user_id": {"key": "from_user_id", "type": "int"}, "description": {"key": "description", "type": "generic"}, "sku": {"key": "product_sku", "type": "generic"}, "variant_id": {"key": "variant_id", "type": "string"}, "position": {"key": "position", "type": "int"}, "mtime": {"key": "date", "type": "string"}, "autoselected": {"key": "autoselected", "type": "int"}}, "api-response": {"id": {"key": "id", "type": "int", "validation": {"required": true}}, "from_user_id": {"key": "from_user_id", "type": "int", "validation": {"required": true}}, "description": {"key": "note", "type": "generic", "validation": {"required": true}}, "descriptionI18n": {"key": "descriptionI18n", "type": "object"}, "comment": {"key": "description", "type": "generic", "validation": {"required": true}}, "product_sku": {"key": "product_sku", "type": "string", "validation": {"required": true}}, "variant_id": {"key": "variant_id", "type": "string", "validation": {"required": true}}, "sku": {"key": "sku", "type": "string", "validation": {"required": true}}, "position": {"key": "position", "type": "int", "validation": {"required": true}}, "date": {"key": "mtime", "type": "string", "validation": {"required": true}}, "autoselected": {"key": "autoselected", "type": "int", "validation": {"required": true}}, "productData": {"key": "product_data", "type": "object", "children": {"product_id": {"key": "product_id", "type": "string", "validation": {"required": true}}, "variant_id": {"key": "variant_id", "type": "string", "validation": {"required": true}}, "name": {"key": "name", "type": "string", "validation": {"required": true}}, "description": {"key": "description", "type": "generic", "validation": {"required": true}}, "price": {"key": "price", "type": "float", "validation": {"required": true}}, "price_deal": {"key": "price_deal", "type": "float", "validation": {"required": true}}, "price_old": {"key": "price_old", "type": "float", "validation": {"required": true}}, "vanity_data": {"key": "vanity_data", "type": "string", "validation": {"required": true}}, "deal_ratio": {"key": "deal_ratio", "type": "int", "validation": {"required": true}}, "name2": {"key": "name2", "type": "string", "validation": {"required": true}}, "sku": {"key": "sku", "type": "string", "validation": {"required": true}}, "regularPrice": {"key": "regularPrice", "type": "float", "validation": {"required": true}}, "salePrice": {"key": "salePrice", "type": "float", "validation": {"required": true}}, "oldPrice": {"key": "oldPrice", "type": "float", "validation": {"required": true}}, "productUrl": {"key": "productUrl", "type": "string", "validation": {"required": true}}, "SaleEndDate": {"key": "SaleEndDate", "type": "string", "validation": {"required": true}}, "vanityData": {"key": "vanityData", "type": "string", "validation": {"required": true}}, "img": {"key": "img", "type": "string", "validation": {"required": true}}, "img250": {"key": "img250", "type": "string", "validation": {"required": true}}, "available": {"key": "available", "type": "string", "validation": {"required": true}}, "additionalMedia": {"key": "additionalMedia", "type": "object"}, "swatches": {"key": "swatches", "type": "object"}}}, "from": {"key": "from", "type": "string", "validation": {"required": true}}, "name": {"key": "name", "type": "string", "validation": {"required": true}}, "user_login": {"key": "user_login", "type": "string", "validation": {"required": true}}}}, "ProductShared": {"db": {"id": {"key": "id", "type": "int"}, "user_id": {"key": "user_id", "type": "int"}, "from_user_id": {"key": "from_user_id", "type": "int"}, "product_sku": {"key": "product_sku", "type": "string"}, "product_id": {"key": "product_id", "type": "string"}}, "api-request": {"user_id": {"key": "user_id", "type": "int"}, "from_user_id": {"key": "from_user_id", "type": "int"}, "sku": {"key": "product_sku", "type": "string"}, "product_id": {"key": "product_id", "type": "string"}}, "api-response": {"id": {"key": "id", "type": "int", "validation": {"required": true}}, "user_id": {"key": "user_id", "type": "int", "validation": {"required": true}}, "from_user_id": {"key": "from_user_id", "type": "int", "validation": {"required": true}}, "product_sku": {"key": "product_sku", "type": "string", "validation": {"required": true}}, "variant_id": {"key": "variant_id", "type": "string", "validation": {"required": true}}, "product_id": {"key": "product_id", "type": "string", "validation": {"required": true}}, "productData": {"key": "product_data", "type": "object", "children": {"product_id": {"key": "product_id", "type": "string", "validation": {"required": true}}, "variant_id": {"key": "variant_id", "type": "string", "validation": {"required": true}}, "product_sku": {"key": "product_sku", "type": "string", "validation": {"required": true}}, "name": {"key": "name", "type": "string", "validation": {"required": true}}, "description": {"key": "description", "type": "generic", "validation": {"required": true}}, "price": {"key": "price", "type": "float", "validation": {"required": true}}, "price_deal": {"key": "price_deal", "type": "float", "validation": {"required": true}}, "price_old": {"key": "price_old", "type": "float", "validation": {"required": true}}, "vanity_data": {"key": "vanity_data", "type": "string", "validation": {"required": true}}, "deal_ratio": {"key": "deal_ratio", "type": "int", "validation": {"required": true}}, "name2": {"key": "name2", "type": "string", "validation": {"required": true}}, "sku": {"key": "sku", "type": "string", "validation": {"required": true}}, "regularPrice": {"key": "regularPrice", "type": "float", "validation": {"required": true}}, "salePrice": {"key": "salePrice", "type": "float", "validation": {"required": true}}, "oldPrice": {"key": "oldPrice", "type": "float", "validation": {"required": true}}, "productUrl": {"key": "productUrl", "type": "string", "validation": {"required": true}}, "SaleEndDate": {"key": "SaleEndDate", "type": "string", "validation": {"required": true}}, "vanityData": {"key": "vanityData", "type": "string", "validation": {"required": true}}, "img": {"key": "img", "type": "string", "validation": {"required": true}}, "img250": {"key": "img250", "type": "string", "validation": {"required": true}}, "additionalMedia": {"key": "additionalMedia", "type": "object"}, "variant_count": {"key": "variant_count", "type": "int", "validation": {"required": true}}, "priority_variant_count": {"key": "priority_variant_count", "type": "int", "validation": {"required": true}}}}, "user_login": {"key": "user_login", "type": "string", "validation": {"required": true}}}}, "RetailerTransactions": {"api-response": {"id": {"key": "id", "type": "int"}, "customer_id": {"key": "customer_id", "type": "string"}, "trx_thread_id": {"key": "trx_thread_id", "type": "string"}, "trx_type": {"key": "trx_type", "type": "string"}, "trx_date": {"key": "trx_date", "type": "string"}, "trx_id": {"key": "trx_id", "type": "string"}, "location": {"key": "location", "type": "string"}, "pos_id": {"key": "pos_id", "type": "string"}, "trx_total": {"key": "trx_total", "type": "string"}, "is_cancelled": {"key": "is_cancelled", "type": "boolean"}, "currency": {"key": "currency", "type": "string"}, "fulfillment_order": {"key": "fulfillment_order", "type": "string"}, "products": {"key": "products", "type": "array", "children": {"id": {"key": "id", "type": "int"}, "trx_type": {"key": "trx_type", "type": "string"}, "trx_date": {"key": "trx_date", "type": "string"}, "product_id": {"key": "product_id", "type": "string"}, "sku": {"key": "sku", "type": "string"}, "product_name": {"key": "product_name", "type": "string"}, "product_description": {"key": "product_description", "type": "string"}, "product_price": {"key": "product_price", "type": "float"}, "product_quantity": {"key": "product_quantity", "type": "int"}, "product_quantity_returned": {"key": "product_quantity_returned", "type": "int"}, "product_brand": {"key": "product_brand", "type": "string"}, "product_img": {"key": "product_img", "type": "string"}, "product_url": {"key": "product_url", "type": "string"}, "product_available": {"key": "product_available", "type": "int"}, "return_dates": {"key": "return_dates", "type": "virtual_array"}, "product_size": {"key": "product_size", "type": "string"}, "product_color": {"key": "product_color", "type": "string"}}}}}, "TextThreads": {"api-response": {"id": {"key": "id", "type": "int"}, "user_id": {"key": "user_id", "type": "int"}, "user_phone_number": {"key": "user_phone_number", "type": "string"}, "customer_id": {"key": "customer_id", "type": "int"}, "customer_phone_number": {"key": "customer_phone_number", "type": "string"}, "is_read": {"key": "is_read", "type": "boolean"}, "is_active": {"key": "is_active", "type": "boolean"}, "is_valid": {"key": "is_valid", "type": "boolean"}, "is_subscribed": {"key": "is_subscribed", "type": "boolean"}, "created_at": {"key": "created_at", "type": "string"}, "updated_at": {"key": "updated_at", "type": "string"}, "last_message_date": {"key": "last_message_date", "type": "string"}, "last_message_body": {"key": "last_message_body", "type": "string"}, "name": {"key": "name", "type": "string"}, "first_name": {"key": "first_name", "type": "string"}, "last_name": {"key": "last_name", "type": "string"}, "email": {"key": "email", "type": "string"}}}, "Rep": {"find-rep-api-response-public": {"response": {"key": "response", "type": "array", "children": {"ID": {"key": "ID", "type": "string"}, "name": {"key": "name", "type": "string"}, "findrep_name": {"key": "findrep_name", "type": "string"}, "storefront_url": {"key": "storefront_url", "type": "string"}, "avatar": {"key": "avatar", "type": "string"}, "introduction": {"key": "introduction", "type": "string"}, "store_data": {"key": "store_data", "type": "object"}, "specialities": {"key": "specialities", "type": "object"}, "user_login": {"key": "user_login", "type": "string"}, "has_sms": {"key": "has_sms", "type": "boolean"}}}}, "footer-api-response-public": {"response": {"key": "response", "type": "object", "children": {"ID": {"key": "ID", "type": "string"}, "name": {"key": "name", "type": "string"}, "storefront_url": {"key": "storefront_url", "type": "string"}, "avatar": {"key": "avatar", "type": "string"}, "introduction": {"key": "introduction", "type": "string"}, "selling_mode": {"key": "selling_mode", "type": "string"}, "type": {"key": "type", "type": "string"}, "user_status": {"key": "user_status", "type": "string"}, "store_data": {"key": "store_data", "type": "object"}, "specialities": {"key": "specialities", "type": "object"}, "employee_id": {"key": "employee_id", "type": "object"}, "user_login": {"key": "user_login", "type": "object"}}}}, "reps-api-response-public": {"response": {"key": "response", "type": "object", "children": {"data": {"key": "data", "type": "array", "children": {"ID": {"key": "ID", "type": "string"}, "name": {"key": "name", "type": "string"}, "storefront_url": {"key": "storefront_url", "type": "string"}, "avatar": {"key": "avatar", "type": "string"}, "introduction": {"key": "introduction", "type": "string"}, "selling_mode": {"key": "selling_mode", "type": "string"}, "type": {"key": "type", "type": "string"}, "user_status": {"key": "user_status", "type": "string"}, "store_data": {"key": "store_data", "type": "object"}, "specialities": {"key": "specialities", "type": "object"}}}}}}}, "Task": {"api-response": {"id": {"validation": {"required": true}, "key": "id", "type": "int"}, "user_id": {"validation": {"required": true}, "key": "user_id", "type": "int"}, "customer_id": {"validation": {"required": true}, "key": "customer_id", "type": "int"}, "task_category_id": {"validation": {"required": true}, "key": "task_category_id", "type": "int"}, "parent_id": {"validation": {"required": true}, "key": "parent_id", "type": "int"}, "status": {"validation": {"required": true}, "key": "status", "type": "string"}, "type": {"validation": {"required": true}, "key": "type", "type": "string"}, "automated_type": {"validation": {"required": true}, "key": "automated_type", "type": "string"}, "details": {"validation": {"required": true}, "key": "details", "type": "string"}, "resolution_note": {"validation": {"required": true}, "key": "resolution_note", "type": "string"}, "resolution_date": {"validation": {"required": true}, "key": "resolution_date", "type": "string"}, "reminder_date": {"validation": {"required": true}, "key": "reminder_date", "type": "string"}, "created_at": {"validation": {"required": true}, "key": "created_at", "type": "string"}, "updated_at": {"validation": {"required": true}, "key": "updated_at", "type": "string"}, "rep_transaction_trx_id": {"key": "rep_transaction_trx_id", "type": "string"}, "retailer_transaction_thread_id": {"key": "retailer_transaction_thread_id", "type": "string"}, "repName": {"key": "repName", "type": "string"}, "storeName": {"key": "storeName", "type": "string"}, "task_category_name": {"key": "task_category", "type": "virtual_array", "children": {"task_category_id": {"key": "id", "type": "int"}, "task_category_name": {"key": "name", "type": "string"}}}, "customer_name": {"key": "customer", "type": "virtual_array", "children": {"customer_id": {"key": "id", "type": "int"}, "customer_name": {"key": "name", "type": "string"}, "customer_first_name": {"key": "first_name", "type": "string"}, "customer_last_name": {"key": "last_name", "type": "string"}, "customer_email": {"key": "email", "type": "string"}, "customer_phone": {"key": "phone", "type": "string"}}}, "is_due": {"key": "is_due", "type": "boolean"}, "count_pending": {"key": "count_pending", "type": "int"}, "count_no_contact": {"key": "count_no_contact", "type": "int"}, "count_with_contact": {"key": "count_with_contact", "type": "int"}, "count_due": {"key": "count_due", "type": "int"}, "count_completed": {"key": "count_completed", "type": "int"}, "count_dismissed": {"key": "count_dismissed", "type": "int"}}}, "CustomerNote": {"api-response": {"id": {"validation": {"required": true}, "key": "id", "type": "int"}, "customer_id": {"validation": {"required": true}, "key": "customer_id", "type": "int"}, "note": {"validation": {"required": true}, "key": "note", "type": "string"}, "created_at": {"validation": {"required": true}, "key": "created_at", "type": "string"}, "updated_at": {"validation": {"required": true}, "key": "updated_at", "type": "string"}, "updated_by": {"validation": {"required": true}, "key": "updated_by", "type": "int"}, "calculated_date": {"validation": {"required": false}, "key": "calculated_date", "type": "string"}}}, "CustomerAddresses": {"db": {"id": {"key": "id", "type": "int"}, "customer_id": {"key": "customer_id", "type": "int"}, "address_line_1": {"key": "address_line_1", "type": "string"}, "address_line_2": {"key": "address_line_2", "type": "string"}, "postal_code": {"key": "postal_code", "type": "string"}, "state": {"key": "state", "type": "string"}, "city": {"key": "city", "type": "string"}, "country": {"key": "country", "type": "string"}, "label": {"key": "label", "type": "string"}, "is_default": {"key": "is_default", "type": "boolean"}}, "api-request": {"customer_id": {"key": "customer_id", "type": "int"}, "address_line_1": {"key": "address_line_1", "type": "string"}, "address_line_2": {"key": "address_line_2", "type": "string"}, "postal_code": {"key": "postal_code", "type": "string"}, "state": {"key": "state", "type": "string"}, "city": {"key": "city", "type": "string"}, "country": {"key": "country", "type": "string"}, "label": {"key": "label", "type": "string"}, "is_default": {"key": "is_default", "type": "boolean"}}, "api-response": {"id": {"validation": {"required": true}, "key": "id", "type": "int"}, "customer_id": {"validation": {"required": true}, "key": "customer_id", "type": "int"}, "address_line_1": {"validation": {"required": true}, "key": "address_line_1", "type": "string"}, "address_line_2": {"validation": {"required": true}, "key": "address_line_2", "type": "string"}, "postal_code": {"validation": {"required": true}, "key": "postal_code", "type": "string"}, "state": {"validation": {"required": true}, "key": "state", "type": "string"}, "city": {"validation": {"required": true}, "key": "city", "type": "string"}, "country": {"validation": {"required": true}, "key": "country", "type": "string"}, "label": {"validation": {"required": true}, "key": "label", "type": "string"}, "is_default": {"validation": {"required": true}, "key": "is_default", "type": "boolean"}, "created_at": {"validation": {"required": true}, "key": "created_at", "type": "string"}, "updated_at": {"validation": {"required": true}, "key": "updated_at", "type": "string"}}}, "BlockIncomingEmailAddress": {"api-response": {"id": {"validation": {"required": true}, "key": "id", "type": "int"}, "user_id": {"validation": {"required": true}, "key": "user_id", "type": "int"}, "email": {"validation": {"required": true}, "key": "email", "type": "string"}, "created_at": {"validation": {"required": true}, "key": "created_at", "type": "string"}, "updated_at": {"validation": {"required": true}, "key": "updated_at", "type": "string"}, "user_login": {"validation": {"required": false}, "key": "user_login", "type": "string"}}}, "ShopFeed": {"api-response": {"id": {"validation": {"required": true}, "key": "id", "type": "int"}, "user_id": {"validation": {"required": true}, "key": "user_id", "type": "int"}, "source": {"validation": {"required": true}, "key": "source", "type": "string"}, "comment": {"validation": {"required": true}, "key": "comment", "type": "string"}, "show_on_shop": {"validation": {"required": true}, "key": "show_on_shop", "type": "int"}, "created_at": {"validation": {"required": true}, "key": "created_at", "type": "string"}, "updated_at": {"validation": {"required": true}, "key": "updated_at", "type": "string"}, "products": {"validation": {"required": false}, "key": "products", "type": "array", "children": {"product_id": {"key": "id", "type": "string"}, "product_sku": {"key": "sku", "type": "string"}, "name": {"key": "name", "type": "string"}, "description": {"key": "description", "type": "string"}, "price": {"key": "price", "type": "string"}, "price_deal": {"key": "price_deal", "type": "string"}, "name2": {"key": "brand", "type": "string"}, "productUrl": {"key": "url", "type": "string"}, "img": {"key": "image", "type": "string"}, "shopFeedProductId": {"key": "shopFeedProductId", "type": "string"}}}, "assets": {"validation": {"required": false}, "key": "assets", "type": "generic"}, "images": {"validation": {"required": false}, "key": "images", "type": "generic"}}}}, "dev.environment.id": "6375c2cd7872b"}