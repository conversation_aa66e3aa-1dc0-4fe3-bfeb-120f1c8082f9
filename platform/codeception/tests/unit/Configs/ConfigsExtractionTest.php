<?php

declare(strict_types=1);

namespace SF\unit\Configs;

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Config\ConfigsLoader\RetailerInfoExtraction;
use SF\Helper\Traits\Asserts;

/**
 * @group configs
 */
class ConfigsExtractionTest extends BaseConfigs
{
    use Asserts;

    public function _before()
    {
        parent::_before();

        // This has precedence over everything and if you are running the test locally, it won't
        // work as expected, since I want to rely on the host for the config server test.
        $this->resetEnv(Loader::ENV_STACK);
        $this->resetEnv(Loader::ENV_RETAILER);
    }

    //////////// Env extraction ////////////

    public function testGetEnvExtractByEnvVariable()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $this->setEnv(Loader::ENV_STACK, 'dev');
        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $this->setEnv(Loader::ENV_STACK, 'prd');
        $env = $extraction->getEnv();
        $this->assertEquals('prd', $env);

        $this->setEnv(Loader::ENV_STACK, 'box01');
        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);

        // We can have any type of env now (ondemand, loadtest)
        $this->setEnv(Loader::ENV_STACK, 'box10');
        $env = $extraction->getEnv();
        $this->assertEquals('box10', $env);
    }

    public function testGetEnvExtractByHostConfigServerDev()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloom-dev';

        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloom-prd';

        $env = $extraction->getEnv();
        $this->assertEquals('prd', $env);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloom-box01';

        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales';

        $env = $extraction->getEnv();
        $this->assertEquals('prd', $env);
    }

    public function testGetEnvExtractByHostConfigServerPrd()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloom-prd';

        $env = $extraction->getEnv();
        $this->assertEquals('prd', $env);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloom-dev';

        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloom-box01';

        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);
    }

    public function testGetEnvExtractByHostConfigServerAlias()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales-dev';
        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales';
        $env = $extraction->getEnv();
        $this->assertEquals('prd', $env);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales-prd';
        $env = $extraction->getEnv();
        $this->assertEquals('prd', $env);
    }

    public function testGetEnvExtractByHostApi()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.api.dev.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'tests-api-stg.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('stg', $env);

        $_SERVER['HTTP_HOST'] = 'tests-api-box01.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);
    }

    public function testGetEnvExtractByHostWidget()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.widgets.dev.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'tests-widgets-stg.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('stg', $env);

        $_SERVER['HTTP_HOST'] = 'tests-widgets-box01.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);
    }

    public function testGetEnvExtractByHostStorefront()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.dev.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'tests-stg.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('stg', $env);

        $_SERVER['HTTP_HOST'] = 'tests-box01.salesfloor.net';
        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);
    }

    public function testGetEnvExtractByHostMultiDomain()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.dev.salesfloor-ecom.net';
        $env = $extraction->getEnv();
        $this->assertEquals('dev', $env);

        $_SERVER['HTTP_HOST'] = 'tests-stg.salesfloor-ecom.net';
        $env = $extraction->getEnv();
        $this->assertEquals('stg', $env);

        $_SERVER['HTTP_HOST'] = 'tests-box01.salesfloor-ecom.net';
        $env = $extraction->getEnv();
        $this->assertEquals('box01', $env);
    }


    public function testGetEnvExtractByHostInvalid()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.widgets.dev.salesfloor-funny.net';
        $env = $extraction->getEnv();
        // default is dev when domain is invalid
        $this->assertEquals('dev', $env);
    }

    public function testGetEnvExtractByCli()
    {
        // leverage config:show robo command to test
        // The path is "codeception", so I need to move one higher

        $output = `./../robo config:show tests-dev retailer.short_name`;
        $this->assertEquals('tests', trim($output));

        $output = `./../robo config:show tests-dev env`;
        $this->assertEquals('dev', trim($output));

        $output = `./../robo config:show tests-stg retailer.short_name`;
        $this->assertEquals('tests', trim($output));

        $output = `./../robo config:show tests-stg env`;
        $this->assertEquals('stg', trim($output));

        $output = `./../robo config:show tests-prd retailer.short_name`;
        $this->assertEquals('tests', trim($output));

        $output = `./../robo config:show tests-prd env`;
        $this->assertEquals('prd', trim($output));

        // I'm not sure why but I can't use run:bin properly here, I always get exit code 1
        // I need to overwrite env variable otherwise it will always be "dev"
        $output = `ENV=stg php ../api/app/bin/dump_config.php tests-stg`;
        $this->assertStringContainsString("source\":\"api\"", $output);
        // No secrets are loaded, so we will use the default from InfraLoader
        $this->assertStringContainsString("fcm.key\":\"fake\"", $output);
        $this->assertStringContainsString("redis.port\":\"6379\"", $output);
        $this->assertStringContainsString("retailer.widgets_domain\":\"tests-widgets-stg.salesfloor.net\"", $output);

        $output = `php ../api/app/bin/dump_config.php tests-dev`;

        $results = json_decode($output, true);
        // Set dynamic value to constants.
        $results['retailer.customer_cookie_expire'] = 0;
        $jsonFile = sprintf('%s/files/%s-dump-config.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($results, $jsonFile);
    }

    public function testGetEnvDefault()
    {
        $extraction = $this->getRetailerInfoExtraction();
        $env = $extraction->getEnv();

        $this->assertEquals('dev', $env);
    }

    //////////// Retailer extraction ////////////

    public function testGetRetailerExtractByEnvVariable()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $this->setEnv(Loader::ENV_RETAILER, 'tests');
        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        // No pre validation on the retailer name, however it will
        // throw exception during feature loader.
        $this->setEnv(Loader::ENV_RETAILER, 'tests-random');
        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests-random', $retailer);
    }

    public function testGetRetailerExtractByHeader()
    {
        // TODO : Wait until confirmation if needed
    }

    public function testGetRetailerExtractByHostConfigServer()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/tests-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/tests-stg';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/testse-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('testse', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('bloom', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales-prd';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('bloom', $retailer);

        /// prd config server

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/tests-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/tests-stg';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/testse-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('testse', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('bloom', $retailer);

        $_SERVER['HTTP_HOST'] = 'configs.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/bloomingdales-prd';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('bloom', $retailer);
    }

    public function testGetRetailerExtractConfigServerByEnv()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $this->setEnv(Loader::ENV_RETAILER, 'configs');

        $_SERVER['HTTP_HOST'] = 'configs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/tests-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        // Even if it's not the "configs" url, ENV_RETAILER has precedence
        $_SERVER['HTTP_HOST'] = 'notconfigs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/tests-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'notconfigs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/elguntors-dev';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('elguntors', $retailer);

        $_SERVER['HTTP_HOST'] = 'notconfigs.dev.salesfloor.net';
        $_SERVER['REQUEST_URI'] = '/configs/mobile/bootstrap/elguntors-prd';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('elguntors', $retailer);
    }

    public function testGetRetailerExtractByHostApi()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.api.dev.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'tests-api-stg.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'tests-api-box01.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'testse-api-box01.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('testse', $retailer);

        // We cannot extract retailer by host in production since we don't have
        // the hostmap anymore. We must rely on the env variable.
    }

    public function testGetRetailerExtractByHostWidgets()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.widgets.dev.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'tests-widgets-stg.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'tests-widgets-box01.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'testse-widgets-box01.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('testse', $retailer);
    }

    public function testGetRetailerExtractByHostStorefront()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'tests.dev.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'tests-stg.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'tests-box01.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['HTTP_HOST'] = 'testse-box01.salesfloor.net';

        $retailer = $extraction->getRetailer();
        $this->assertEquals('testse', $retailer);
    }

    public function testGetRetailerExtractByHostInvalid()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['HTTP_HOST'] = 'testse.salesfloorrrr.net';

        $retailer = $extraction->getRetailer();
        // Since the second param (because it's not a robo command) is not a valid regex
        // for a retailer, if return null
        $this->assertEquals(null, $retailer);

        $_SERVER['HTTP_HOST'] = 'testse.salesfloorrrr.net';

        $retailer = $extraction->getRetailer();
        $_SERVER['argv'] = null;
        $this->assertEquals(null, $retailer);
    }

    public function testGetRetailerExtractByCli()
    {
        $extraction = $this->getRetailerInfoExtraction();

        $_SERVER['argv'] = [
            'robo',
            'fake:command',
            'tests-stg',
        ];
        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['argv'] = [
            'robo',
            'fake:command',
            'tests-stg',
            'other_params',
        ];
        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['argv'] = [
            'robo',
            'fake:command',
        ];
        $retailer = $extraction->getRetailer();
        // This should not happen since we force retailer id when there's no retailer
        // commands/src/Base.php:49 : $forceRetailerId = $retailerId;
        $this->assertEquals(null, $retailer);

        $_SERVER['argv'] = [
            'fake/path/script.php',
            'tests-stg',
        ];
        $retailer = $extraction->getRetailer();
        $this->assertEquals('tests', $retailer);

        $_SERVER['argv'] = [
            'fake/path/script.php',
            'testsstginvalid',
        ];
        $retailer = $extraction->getRetailer();
        $this->assertEquals(null, $retailer);
    }

    private function getRetailerInfoExtraction()
    {
        return new RetailerInfoExtraction();
    }
}
