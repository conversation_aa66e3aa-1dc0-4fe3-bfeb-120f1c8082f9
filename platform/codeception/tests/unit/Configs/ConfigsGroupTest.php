<?php

declare(strict_types=1);

namespace SF\unit\Configs;

use Salesfloor\Services\Config\ConfigsLoader\InfraConfigLoader;
use Salesfloor\Services\Config\ConfigsLoader\Loader;

/**
 * @group configs
 */
class ConfigsGroupTest extends BaseConfigs
{
    // Only chicos is using this 'feature', lets use this retailer for this test.
    // this is not perfect and may not work in the future, but less painful than creating a new
    // parent retailer for this test.
    public function testLoadConfigsGroup()
    {
        $box01Configs = $this->getConfigs(Loader::SOURCE_API, 'chicos-box01');
        $qa04Configs = $this->getConfigs(Loader::SOURCE_API, 'chicos-qa04');
        $stgConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-stg');
        $devConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-dev');
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-box01.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-box01.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-box01.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-box01.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-box01.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-box01.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-box01.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-box01.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in box01, but it's the default
            'retailer.group' => 'chicos',
        ], $box01Configs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-qa04.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-qa04.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-qa04.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-qa04.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-qa04.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-qa04.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-qa04.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-qa04.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in qa04, but it's the default
            'retailer.group' => 'chicos',
        ], $qa04Configs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-stg.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-stg.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-stg.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-stg.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-stg.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-stg.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-stg.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-stg.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in stg, but it's the default
            'retailer.group' => 'chicos',
        ], $stgConfigs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos.api.dev.salesfloor.net',
            'retailer.widgets_domain' => 'chicos.widgets.dev.salesfloor.net',
            'retailer.webserver_domain' => 'chicos.dev.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos.dev.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos.api.dev.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos.api.dev.salesfloor.net',
            'retailer.widget.host' => 'chicos.widgets.dev.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos.widgets.dev.salesfloor.net',
            'mysql.db' => 'wordpress_chicos',
            'retailer.group' => 'chicos',
        ], $devConfigs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-prd.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-prd.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-prd.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-prd.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-prd.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-prd.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-prd.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-prd.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in prd, but it's the default
            'retailer.group' => 'chicos',
        ], $prdConfigs);


        $box01Configs = $this->getConfigs(Loader::SOURCE_API, 'whbm-box01');
        $qa04Configs = $this->getConfigs(Loader::SOURCE_API, 'whbm-qa04');
        $stgConfigs = $this->getConfigs(Loader::SOURCE_API, 'whbm-stg');
        $devConfigs = $this->getConfigs(Loader::SOURCE_API, 'whbm-dev');
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'whbm-prd');

        $this->validateArray([
            'retailer.api_domain' => 'whbm-api-box01.salesfloor.net',
            'retailer.widgets_domain' => 'whbm-widgets-box01.salesfloor.net',
            'retailer.webserver_domain' => 'whbm-box01.salesfloor.net',
            'salesfloor_storefront.host' => 'https://whbm-box01.salesfloor.net',
            'retailer.rest_api_url' => 'https://whbm-api-box01.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://whbm-api-box01.salesfloor.net',
            'retailer.widget.host' => 'whbm-widgets-box01.salesfloor.net',
            'salesfloor_widgets.host' => 'https://whbm-widgets-box01.salesfloor.net',
            'mysql.db' => 'wordpress_whbm', // this is not valid in box01, but it's the default
            'retailer.group' => 'chicos',
        ], $box01Configs);

        $this->validateArray([
            'retailer.api_domain' => 'whbm-api-qa04.salesfloor.net',
            'retailer.widgets_domain' => 'whbm-widgets-qa04.salesfloor.net',
            'retailer.webserver_domain' => 'whbm-qa04.salesfloor.net',
            'salesfloor_storefront.host' => 'https://whbm-qa04.salesfloor.net',
            'retailer.rest_api_url' => 'https://whbm-api-qa04.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://whbm-api-qa04.salesfloor.net',
            'retailer.widget.host' => 'whbm-widgets-qa04.salesfloor.net',
            'salesfloor_widgets.host' => 'https://whbm-widgets-qa04.salesfloor.net',
            'mysql.db' => 'wordpress_whbm', // this is not valid in qa04, but it's the default
            'retailer.group' => 'chicos',
        ], $qa04Configs);

        $this->validateArray([
            'retailer.api_domain' => 'whbm-api-stg.salesfloor.net',
            'retailer.widgets_domain' => 'whbm-widgets-stg.salesfloor.net',
            'retailer.webserver_domain' => 'whbm-stg.salesfloor.net',
            'salesfloor_storefront.host' => 'https://whbm-stg.salesfloor.net',
            'retailer.rest_api_url' => 'https://whbm-api-stg.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://whbm-api-stg.salesfloor.net',
            'retailer.widget.host' => 'whbm-widgets-stg.salesfloor.net',
            'salesfloor_widgets.host' => 'https://whbm-widgets-stg.salesfloor.net',
            'mysql.db' => 'wordpress_whbm', // this is not valid in stg, but it's the default
            'retailer.group' => 'chicos',
        ], $stgConfigs);

        $this->validateArray([
            'retailer.api_domain' => 'whbm.api.dev.salesfloor.net',
            'retailer.widgets_domain' => 'whbm.widgets.dev.salesfloor.net',
            'retailer.webserver_domain' => 'whbm.dev.salesfloor.net',
            'salesfloor_storefront.host' => 'https://whbm.dev.salesfloor.net',
            'retailer.rest_api_url' => 'https://whbm.api.dev.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://whbm.api.dev.salesfloor.net',
            'retailer.widget.host' => 'whbm.widgets.dev.salesfloor.net',
            'salesfloor_widgets.host' => 'https://whbm.widgets.dev.salesfloor.net',
            'mysql.db' => 'wordpress_whbm',
            'retailer.group' => 'chicos',
        ], $devConfigs);

        $this->validateArray([
            'retailer.api_domain' => 'whbm-api-prd.salesfloor.net',
            'retailer.widgets_domain' => 'whbm-widgets-prd.salesfloor.net',
            'retailer.webserver_domain' => 'whbm-prd.salesfloor.net',
            'salesfloor_storefront.host' => 'https://whbm-prd.salesfloor.net',
            'retailer.rest_api_url' => 'https://whbm-api-prd.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://whbm-api-prd.salesfloor.net',
            'retailer.widget.host' => 'whbm-widgets-prd.salesfloor.net',
            'salesfloor_widgets.host' => 'https://whbm-widgets-prd.salesfloor.net',
            'mysql.db' => 'wordpress_whbm', // this is not valid in prd, but it's the default
            'retailer.group' => 'chicos',
        ], $prdConfigs);
    }

    public function testLoadConfigsHBCGroup()
    {
        $box01Configs = $this->getConfigs(Loader::SOURCE_API, 'labaie-prd');

        // because labaie is not a real retailer and the fallback is used when not provided by header
        $this->validateArray([
            'retailer.api_domain' => 'hbc-api-prd.salesfloor-ecom.net',
            'retailer.widgets_domain' => 'hbc-widgets-prd.salesfloor-ecom.net',
            'retailer.webserver_domain' => 'hbc-prd.salesfloor-ecom.net',
            'salesfloor_storefront.host' => 'https://hbc-prd.salesfloor-ecom.net',
            'retailer.rest_api_url' => 'https://hbc-api-prd.salesfloor-ecom.net',
            'salesfloor_rest_api.host' => 'https://hbc-api-prd.salesfloor-ecom.net',
            'retailer.widget.host' => 'hbc-widgets-prd.salesfloor-ecom.net',
            'salesfloor_widgets.host' => 'https://hbc-widgets-prd.salesfloor-ecom.net',
            'mysql.db' => 'wordpress_hbc',
            'retailer.domain_key' => 'fr', // because on locally (or at build time), this is what the fallback will be
        ], $box01Configs);

        // this config is only set for valid group but doesn't seem to be used
        $this->assertFalse($box01Configs->offsetExists('retailer.group'));
    }
}
