<?php

declare(strict_types=1);

namespace SF\unit\Configs;

use Salesfloor\Configs\Configs;
use Salesfloor\Services\Config\ConfigsLoader\Exceptions\ConfigsException;
use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Config\ConfigsLoader\InfraConfigLoader;
use SF\Helper\Traits\Asserts;

class ConfigsForceRetailerIdTest extends BaseConfigs
{
    use Asserts;

    public function testLoadConfigsInvalidSource()
    {
        $this->expectException(ConfigsException::class);
        $this->getConfigs('invalid', 'chicos-dev');
    }

    public function testLoadOtherEnvironmentConfigWithoutContamination()
    {
        $devConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-dev');
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        // Verify the two sets of configs don't contaminate each other.
        $this->assertEquals('dev', $devConfigs['env']);
        $this->assertEquals('prd', $prdConfigs['env']);
    }

    public function testLoadConfigsValidateDevSecrets()
    {
        // Dev has special behaviour (e.g: has secrets locally)
        $configs = $this->getConfigs(Loader::SOURCE_API, 'chicos-dev');

        $this->assertEquals('93dca609b40a04c08db4c8628c02cefe04cbde1decc7eb915fad9a3a3de2423bb1f69ec8bd99442989e87201dd23814f2ea060adf41e99316519f26e5d979891fb1a2b933fa6', $configs['sf.security.secretkey']);
    }

    public function testLoadConfigsValidationDefault()
    {
        $box01Configs = $this->getConfigs(Loader::SOURCE_API, 'chicos-box01');
        $qa04Configs = $this->getConfigs(Loader::SOURCE_API, 'chicos-qa04');
        $stgConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-stg');
        $devConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-dev');
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-box01.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-box01.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-box01.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-box01.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-box01.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-box01.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-box01.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-box01.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in box01, but it's the default
        ], $box01Configs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-qa04.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-qa04.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-qa04.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-qa04.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-qa04.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-qa04.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-qa04.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-qa04.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in qa04, but it's the default
        ], $qa04Configs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-stg.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-stg.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-stg.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-stg.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-stg.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-stg.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-stg.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-stg.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in stg, but it's the default
        ], $stgConfigs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos.api.dev.salesfloor.net',
            'retailer.widgets_domain' => 'chicos.widgets.dev.salesfloor.net',
            'retailer.webserver_domain' => 'chicos.dev.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos.dev.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos.api.dev.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos.api.dev.salesfloor.net',
            'retailer.widget.host' => 'chicos.widgets.dev.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos.widgets.dev.salesfloor.net',
            'mysql.db' => 'wordpress_chicos',
        ], $devConfigs);

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-prd.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-prd.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-prd.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-prd.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-prd.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-prd.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-prd.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-prd.salesfloor.net',
            'mysql.db' => 'wordpress_chicos', // this is not valid in prd, but it's the default
        ], $prdConfigs);
    }

    public function testLoadConfigsValidationPrdNoInfraPath()
    {
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-prd.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-prd.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-prd.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-prd.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-prd.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-prd.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-prd.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-prd.salesfloor.net',
            'mysql.db' => 'wordpress_chicos',
        ], $prdConfigs);

        $this->assertEquals('prd', $prdConfigs['env']);
        // $this->assertEquals('2702352998', $prdConfigs['retailer.heap.app_id']);
    }

    public function testLoadConfigsValidationPrdWithInfraPath()
    {
        // This is to "fake" infra loader and not rely on the default
        $this->setEnv(
            InfraConfigLoader::ENV_INFRA_CONFIG_PATH,
            getcwd() . "/tests/unit/Configs/Fixtures/chicos-prd-infra"
        );

        /** @var Configs $prdConfigs */
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        $actual = [];
        foreach ($prdConfigs->keys() as $key) {
            if (in_array($key, ['retailer.customer_cookie_expire'], true)) {
                // skip dynamic values
                continue;
            }
            $actual[$key] = $prdConfigs[$key];
        }
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($actual, $jsonFile);
    }

    public function testLoadConfigsValidationOverrides()
    {
        // This is to "fake" override loader and not rely on the default
        $this->setEnv(InfraConfigLoader::ENV_INFRA_CONFIG_PATH, getcwd() . "/tests/unit/Configs/Fixtures/chicos-prd-override");

        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        $this->validateArray([
            'retailer.api_domain' => 'chicos-api-prd-override.salesfloor.net',
            'retailer.widgets_domain' => 'chicos-widgets-prd-override.salesfloor.net',
            'retailer.webserver_domain' => 'chicos-prd-override.salesfloor.net',
            'salesfloor_storefront.host' => 'https://chicos-prd-override.salesfloor.net',
            'retailer.rest_api_url' => 'https://chicos-api-prd-override.salesfloor.net',
            'salesfloor_rest_api.host' => 'https://chicos-api-prd-override.salesfloor.net',
            'retailer.widget.host' => 'chicos-widgets-prd-override.salesfloor.net',
            'salesfloor_widgets.host' => 'https://chicos-widgets-prd-override.salesfloor.net',
            'mysql.db' => 'wordpress',
            'test' => 'potato-override',
        ], $prdConfigs);

        $this->assertEquals('prd', $prdConfigs['env']);
        // $this->assertEquals('2702352998', $prdConfigs['retailer.heap.app_id']);

        // Because there's no InfraConfigPath, will use the default
        $this->assertEquals(
            'https://chicos-api-prd-override.salesfloor.net/microservice/cans',
            $prdConfigs['cans.url']
        );
    }

    public function testLoadConfigsValidationFeatures()
    {
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'chicos-prd');

        $this->validateArray([
            'default_template' => [
                'id' => 'default_template',
                'subject' => null,
                'display_name' => 'Standard Template',
                'filename' => 'curation_mixed_variation-2',
                'products' => [
                    'min' => 0,
                    'max' => 9,
                ],
                'assets' => [
                    'min' => 0,
                    'max' => 1,
                    'or'  => array('photos')
                ],
                'photos' => [
                    'min' => 0,
                    'max' => 1,
                    'or'  => array('assets')
                ],
                'thumbnail' => 'curation_mixed_variation-2.png',
                'default' => true,
            ],
            'look_template' => [
                'id' => 'look_template',
                'subject' => null,
                'display_name' => 'Shop the Look Template',
                'filename' => 'curation_mixed_variation-3',
                'products' => [
                    'min' => 3,
                    'max' => 3,
                ],
                'photos' => [
                    'min' => 1,
                    'max' => 1,
                    'or'  => array('assets'),
                ],
                'assets' => [
                    'min' => 1,
                    'max' => 1,
                    'or'  => array('photos'),
                ],
                'thumbnail' => 'curation_mixed_variation-3.png',
                'default' => false,
            ]
        ], $prdConfigs['retailer.multiple_email_templates.layout']);

        // saks doesn't have this feature on
        $prdConfigs2 = $this->getConfigs(Loader::SOURCE_API, 'saks-prd');
        $this->assertNull($prdConfigs2['retailer.multiple_email_templates.layout'] ?? null);
    }

    public function testLoadConfigsInvalidRetailer()
    {
        $this->expectException(ConfigsException::class);
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'sakse-prd');
    }

    public function testLoadConfigsInvalidEnv()
    {
        // Since we force the retailer id, there's no validation on the env
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'saks-prdeee');

        $this->assertEquals('prdeee', $prdConfigs['env']);
    }

    public function testLoadConfigsMobileAlias()
    {
        // You cannot use mobile alias when "forced" since you bypass the logic to extract it
        $this->expectException(\TypeError::class);
        $prdConfigs = $this->getConfigs(Loader::SOURCE_API, 'bloomingdale');
    }
}
