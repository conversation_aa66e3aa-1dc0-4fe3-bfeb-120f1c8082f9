<?php

namespace Salesfloor\Tests\Unit\AI;

use Codeception\Test\Unit;
use Psr\Log\LoggerInterface;
use Salesfloor\API\Managers\AI\Message as MessageManager;
use Salesfloor\Models\Virtuals\AI\Message as AIGeneratedMessage;
use Salesfloor\Services\AI\Agent\ContentAnalyzerAgent;
use Salesfloor\Services\AI\Agent\RequestAnalyzerAgent;
use Salesfloor\Services\AI\Agent\CampaignAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\MySQLRepository;
use Silex\Application;

class MessageManagerTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * @var RequestAnalyzerAgent|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockRequestAnalyzer;

    /**
     * @var ContentAnalyzerAgent|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockContentAnalyzer;

    /**
     * @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockLogger;

    /**
     * @var MySQLRepository|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockRepository;

    /**
     * @var Application|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockApp;

    /**
     * @var MessageManager
     */
    private $messageManager;

    protected function _before()
    {
        // Mock dependencies
        $this->mockRequestAnalyzer = $this->createMock(RequestAnalyzerAgent::class);
        $this->mockContentAnalyzer = $this->createMock(ContentAnalyzerAgent::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        $this->mockRepository = $this->createMock(MySQLRepository::class);
        $this->mockApp = $this->createMock(Application::class);

        // Create the message manager with required dependencies
        $this->messageManager = new MessageManager(
            $this->mockRepository,
            null, // cache
            '', // cache prefix
            ['ai.agent.refiner.override.temperature' => 0.7], // configs
            null, // mapper service
            null, // multilang service
            $this->mockLogger // logger
        );

        // Set up the app dependency injection
        $this->mockApp
            ->method('offsetGet')
            ->willReturnMap([
                ['service.ai.agent.request_analyzer', $this->mockRequestAnalyzer],
                ['service.ai.agent.content_analyzer', $this->mockContentAnalyzer]
            ]);

        // Inject dependencies
        $this->messageManager->injectDeps($this->mockApp);
    }

    /**
     * Test successful message generation for email content
     */
    public function testGenerateMessageEmailSuccess()
    {
        $input = "Create an email about our summer sale with 30% off all items";
        $userId = 123;
        $context = ['organization' => 'Test Store', 'content_type' => 'email'];

        $analysis = [
            'content_type' => 'email',
            'campaign_type' => 'promotion',
            'context' => [
                'offer' => '30% off all items',
                'season' => 'summer',
                'event' => 'sale'
            ],
            'reasoning' => 'This is a promotional email request'
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("Subject: Summer Sale - 30% Off All Items!\n\nDear Valued Customer,\n\nWe're excited to announce our summer sale with 30% off all items!\n\nDon't miss out on this amazing opportunity to refresh your wardrobe.\n\nShop now!\n\nBest regards,\nTest Store Team");

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->with($input, $context)
            ->willReturn($analysis);

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('generateContentFromAnalysis')
            ->with($analysis)
            ->willReturn($mockContentResponse);

        $mockCampaignAgent = $this->createMock(CampaignAgent::class);
        $mockSubjectResponse = $this->createMock(AIResponse::class);
        $mockSubjectResponse
            ->method('getContent')
            ->willReturn("Summer Sale - 30% Off All Items!");

        $mockCampaignAgent
            ->expects($this->once())
            ->method('generateSubjectLines')
            ->willReturn($mockSubjectResponse);

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('getCampaignAgent')
            ->willReturn($mockCampaignAgent);

        $this->mockLogger
            ->expects($this->atLeastOnce())
            ->method('info');

        $result = $this->messageManager->generateMessage($input, $userId, $context);

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals('email', $result->content_type);
        $this->assertEquals('promotion', $result->campaign_type);
        $this->assertEquals($userId, $result->user_id);
        $this->assertEquals($input, $result->prompt);
        $this->assertEquals('Summer Sale - 30% Off All Items!', $result->recommended_subject);
        $this->assertNotEmpty($result->content);
    }

    /**
     * Test successful message generation for SMS content
     */
    public function testGenerateMessageSMSSuccess()
    {
        $input = "Create an SMS about flash sale";
        $userId = 456;
        $context = ['organization' => 'Fashion Store'];

        $analysis = [
            'content_type' => 'sms',
            'campaign_type' => 'promotion',
            'context' => [
                'event' => 'flash sale'
            ]
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("FLASH SALE! Limited time only. Shop now!");

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->with($input, $context)
            ->willReturn($analysis);

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('generateContentFromAnalysis')
            ->with($analysis)
            ->willReturn($mockContentResponse);

        // Should not try to generate subject lines for SMS
        $this->mockRequestAnalyzer
            ->expects($this->never())
            ->method('getCampaignAgent');

        $result = $this->messageManager->generateMessage($input, $userId, $context);

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals('sms', $result->content_type);
        $this->assertEquals('promotion', $result->campaign_type);
        $this->assertEquals($userId, $result->user_id);
        $this->assertEquals($input, $result->prompt);
        $this->assertEquals("FLASH SALE! Limited time only. Shop now!", $result->content);
    }

    /**
     * Test successful message refinement for email content
     */
    public function testRefineMessageEmailSuccess()
    {
        $input = "Hi [Name],\n\nReady to refresh your wardrobe?\n\nWe're excited to offer you an exclusive promotion on our women's outfits.\n\nFor a limited time, enjoy [Discount Percentage]% off on all women's outfits.";
        $userId = 789;
        $context = ['organization' => 'Allen Edmonds', 'content_type' => 'email'];

        $analysis = [
            'content_type' => 'email',
            'campaign_type' => 'promotion',
            'context' => [
                'offer' => '[Discount Percentage]% off women\'s outfits',
                'personalization' => true
            ],
            'tone' => 'friendly',
            'target_audience' => 'existing_customers',
            'key_elements' => [
                'personalization' => true,
                'call_to_action' => true
            ],
            'content_quality' => [
                'grammar' => true,
                'clarity' => true
            ],
            'reasoning' => 'Content shows good structure but could be more engaging'
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("Hi [Name],\n\nReady to refresh your wardrobe?\n\nWe're excited to offer you an exclusive promotion on our women's outfits.\n\nFor a limited time, enjoy [Discount Percentage]% off all women's outfits.\n\nShop now and transform your style!\n\nBest regards,\nAllen Edmonds Team");

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->with($input, $context)
            ->willReturn($analysis);

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('refineContentFromAnalysis')
            ->with(
                $input,
                $analysis,
                ['temperature' => 0.7]
            )
            ->willReturn($mockContentResponse);

        $mockCampaignAgent = $this->createMock(CampaignAgent::class);
        $mockSubjectResponse = $this->createMock(AIResponse::class);
        $mockSubjectResponse
            ->method('getContent')
            ->willReturn("Exclusive Offer: Refresh Your Wardrobe");

        $mockCampaignAgent
            ->expects($this->once())
            ->method('generateSubjectLines')
            ->willReturn($mockSubjectResponse);

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('getCampaignAgent')
            ->willReturn($mockCampaignAgent);

        $result = $this->messageManager->refineMessage($input, $userId, $context);

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals('email', $result->content_type);
        $this->assertEquals('promotion', $result->campaign_type);
        $this->assertEquals($userId, $result->user_id);
        $this->assertEquals($input, $result->prompt);
        $this->assertEquals('Exclusive Offer: Refresh Your Wardrobe', $result->recommended_subject);
        $this->assertNotEmpty($result->content);
    }

    /**
     * Test successful message refinement for SMS content
     */
    public function testRefineMessageSMSSuccess()
    {
        $input = "Flash sale! 30% off everything. Shop now: link.com";
        $userId = 101;
        $context = ['organization' => 'Test Store'];

        $analysis = [
            'content_type' => 'sms',
            'campaign_type' => 'promotion',
            'context' => [
                'offer' => '30% off everything'
            ]
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("FLASH SALE! 30% off everything. Shop now: link.com");

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->with($input, $context)
            ->willReturn($analysis);

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('refineContentFromAnalysis')
            ->with(
                $input,
                $analysis,
                ['temperature' => 0.7]
            )
            ->willReturn($mockContentResponse);

        // Should not try to generate subject lines for SMS
        $this->mockRequestAnalyzer
            ->expects($this->never())
            ->method('getCampaignAgent');

        $result = $this->messageManager->refineMessage($input, $userId, $context);

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals('sms', $result->content_type);
        $this->assertEquals('promotion', $result->campaign_type);
        $this->assertEquals($userId, $result->user_id);
        $this->assertEquals($input, $result->prompt);
    }

    /**
     * Test message generation with content type override
     */
    public function testGenerateMessageWithContentTypeOverride()
    {
        $input = "Create promotional content";
        $userId = 202;
        $context = ['organization' => 'Test Store', 'content_type' => 'sms'];

        $analysis = [
            'content_type' => 'email', // This should be overridden
            'campaign_type' => 'promotion'
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("PROMO! Check out our deals.");

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->willReturn($analysis);

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('generateContentFromAnalysis')
            ->with($this->callback(function ($analysisParam) {
                return $analysisParam['content_type'] === 'sms'; // Should be overridden
            }))
            ->willReturn($mockContentResponse);

        $result = $this->messageManager->generateMessage($input, $userId, $context);

        $this->assertEquals('sms', $result->content_type);
    }

    /**
     * Test subject line generation failure with fallback
     */
    public function testSubjectLineGenerationFailure()
    {
        $input = "Email content that needs subject lines";
        $userId = 303;
        $context = ['organization' => 'Test Store'];

        $analysis = [
            'content_type' => 'email',
            'campaign_type' => 'newsletter'
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("Refined email content");

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->willReturn($analysis);

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('refineContentFromAnalysis')
            ->willReturn($mockContentResponse);

        $mockCampaignAgent = $this->createMock(CampaignAgent::class);
        $mockCampaignAgent
            ->expects($this->once())
            ->method('generateSubjectLines')
            ->willThrowException(new \Exception('Subject generation failed'));

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('getCampaignAgent')
            ->willReturn($mockCampaignAgent);

        $this->mockLogger
            ->expects($this->once())
            ->method('warning')
            ->with(
                $this->stringContains('Failed to generate subject lines')
            );

        $result = $this->messageManager->refineMessage($input, $userId, $context);

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals('Announcement!', $result->recommended_subject); // Default subject
    }

    /**
     * Test message generation error handling
     */
    public function testGenerateMessageError()
    {
        $input = "Content to generate";
        $userId = 404;
        $context = ['organization' => 'Test Store'];

        $this->mockRequestAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->willThrowException(new AIAdapterException('AI service error', 'test'));

        $this->expectException(AIAdapterException::class);

        $this->messageManager->generateMessage($input, $userId, $context);
    }

    /**
     * Test message refinement error handling
     */
    public function testRefineMessageError()
    {
        $input = "Content to refine";
        $userId = 505;
        $context = ['organization' => 'Test Store'];

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->willThrowException(new AIAdapterException('AI service error', 'test'));

        $this->expectException(AIAdapterException::class);

        $this->messageManager->refineMessage($input, $userId, $context);
    }

    /**
     * Test message refinement with explicit content type
     */
    public function testRefineMessageWithExplicitContentType()
    {
        $input = "Some marketing message";
        $userId = 606;
        $context = ['organization' => 'Test Store'];

        $analysis = [
            'content_type' => 'email', // Provide content_type to avoid null error
            'campaign_type' => 'newsletter'
        ];

        $mockContentResponse = $this->createMock(AIResponse::class);
        $mockContentResponse
            ->method('getContent')
            ->willReturn("Refined marketing message");

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('analyze')
            ->willReturn($analysis);

        $this->mockContentAnalyzer
            ->expects($this->once())
            ->method('refineContentFromAnalysis')
            ->willReturn($mockContentResponse);

        $result = $this->messageManager->refineMessage($input, $userId, $context);

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals('email', $result->content_type);
        $this->assertEquals('newsletter', $result->campaign_type);
    }

    /**
     * Test parseSubjectLines method
     */
    public function testParseSubjectLines()
    {
        $reflection = new \ReflectionClass($this->messageManager);
        $method = $reflection->getMethod('parseSubjectLines');
        $method->setAccessible(true);

        // Test numbered subject lines
        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn("1. First Subject Line\n2. Second Subject Line\n3. Third Subject Line");

        $result = $method->invoke($this->messageManager, $mockResponse);

        $this->assertCount(2, $result);
        $this->assertEquals('First Subject Line', $result[0]);
        $this->assertEquals('Third Subject Line', $result[1]);

        // Test single subject line
        $mockResponse2 = $this->createMock(AIResponse::class);
        $mockResponse2
            ->method('getContent')
            ->willReturn("Single Subject Line");

        $result2 = $method->invoke($this->messageManager, $mockResponse2);

        $this->assertCount(1, $result2);
        $this->assertEquals('Single Subject Line', $result2[0]);

        // Test empty response
        $mockResponse3 = $this->createMock(AIResponse::class);
        $mockResponse3
            ->method('getContent')
            ->willReturn("");

        $result3 = $method->invoke($this->messageManager, $mockResponse3);

        $this->assertEmpty($result3);
    }

    /**
     * Test createMessageModel method
     */
    public function testCreateMessageModel()
    {
        $reflection = new \ReflectionClass($this->messageManager);
        $method = $reflection->getMethod('createMessageModel');
        $method->setAccessible(true);

        $content = "Test message content";
        $contentType = new \Salesfloor\Services\AI\ValueObject\ContentType('email');
        $campaignType = 'promotion';
        $userId = 123;
        $prompt = "Original prompt";

        $result = $method->invoke(
            $this->messageManager,
            $content,
            $contentType,
            $campaignType,
            $userId,
            $prompt
        );

        $this->assertInstanceOf(AIGeneratedMessage::class, $result);
        $this->assertEquals($content, $result->content);
        $this->assertEquals('email', $result->content_type);
        $this->assertEquals($campaignType, $result->campaign_type);
        $this->assertEquals($userId, $result->user_id);
        $this->assertEquals($prompt, $result->prompt);
    }
}
