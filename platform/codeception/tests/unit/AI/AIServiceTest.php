<?php

declare(strict_types=1);

namespace SF\unit\AI;

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Psr\Log\LoggerInterface;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\AI\Config\AIConfig;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\ProviderAdapter\ProviderAdapterInterface;
use Salesfloor\Services\AI\ProviderAdapter\VertexAIAdapter;
use Salesfloor\Services\AI\Service;
use Salesfloor\Services\Auth\TokenService;
use SF\unit\BaseUnit;

class AIServiceTest extends BaseUnit
{
    private const DUMMY_SERVICE_ACCOUNT = [
        'type' => 'service_account',
        'project_id' => 'test-project-id',
        'private_key_id' => 'test-key-id',
        'private_key' => 'test-private-key',
        'client_email' => '<EMAIL>',
    ];

    /**
     * @var Configs|\PHPUnit\Framework\MockObject\MockObject
     */
    private $configsMock;

    /**
     * @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $loggerMock;

    /**
     * @var TokenService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $tokenServiceMock;

    /**
     * @var Client
     */
    private $httpClient;

    /**
     * @var MockHandler
     */
    private $mockHandler;

    protected function _before()
    {
        $this->configsMock = $this->createMock(Configs::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->tokenServiceMock = $this->createMock(TokenService::class);

        // Setup mock HTTP client
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $this->httpClient = new Client(['handler' => $handlerStack]);
    }

    /**
     * Test default provider getter
     */
    public function testGetDefaultProvider()
    {
        $this->configsMock
            ->method('offsetGet')
            ->with('ai.default_provider')
            ->willReturn('vertexai');

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $this->assertEquals('vertexai', $service->getDefaultProvider());
    }

    /**
     * Test default provider getter when no default provider is set
     */
    public function testGetDefaultProviderFallback()
    {
        $this->configsMock
            ->method('offsetGet')
            ->with('ai.default_provider')
            ->willReturn(null);

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $this->assertEquals('vertexai', $service->getDefaultProvider());
    }

    public function testGetAdapterThrowsExceptionForInvalidProvider()
    {
        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $this->expectException(AIAdapterException::class);
        $this->expectExceptionMessage(
            'Provider adapter not found: invalid-provider'
        );

        $service->getAdapter('invalid-provider');
    }

    /**
     * Test get adapter for valid provider
     */
    public function testGetAdapter()
    {
        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $adapter = $service->getAdapter('vertexai');
        $this->assertInstanceOf(VertexAIAdapter::class, $adapter);
    }

    /**
     * Test get adapter for valid provider
     */
    public function testUseProvider()
    {
        $this->configsMock->method('offsetExists')->willReturn(true);
        $this->configsMock
            ->method('offsetGet')
            ->willReturnMap([
                ['ai.vertexai.model', 'gemini-2.0-flash-lite'],
                ['ai.vertexai.project_id', 'test-project'],
                ['ai.vertexai.location', 'us-central1'],
                [
                    'ai.vertexai.service_account',
                    base64_encode(json_encode(self::DUMMY_SERVICE_ACCOUNT)),
                ],
                ['ai.vertexai.temperature', 0.7],
                ['ai.vertexai.max_tokens', 1024],
            ]);

        $this->tokenServiceMock
            ->method('getGoogleAccessToken')
            ->willReturn('fake-token');

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $config = AIConfig::forVertexAI(
            'gemini-2.0-flash-lite',
            'test-project',
            'us-central1',
            self::DUMMY_SERVICE_ACCOUNT,
            ['temperature' => 0.7, 'max_tokens' => 1024]
        );

        $result = $service->useProvider('vertexai', $config);
        $this->assertSame($service, $result);

        $activeAdapter = $service->getActiveAdapter();
        $this->assertInstanceOf(VertexAIAdapter::class, $activeAdapter);
    }

    /**
     * Test getting available providers
     */
    public function testGetAvailableProviders()
    {
        $mockAdapter = $this->createMock(ProviderAdapterInterface::class);
        $mockAdapter->method('isAvailable')->willReturn(true);
        $mockAdapter->method('getName')->willReturn('mock-provider');

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $service->addAdapter('mock-provider', $mockAdapter);

        $providers = $service->getAvailableProviders();
        $this->assertContains('mock-provider', $providers);
    }

    /**
     * Test adding a custom adapter
     */
    public function testAddAdapter()
    {
        $mockAdapter = $this->createMock(ProviderAdapterInterface::class);
        $mockAdapter->method('getName')->willReturn('custom-provider');

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $result = $service->addAdapter('custom-provider', $mockAdapter);

        $this->assertSame($service, $result);

        $retrievedAdapter = $service->getAdapter('custom-provider');
        $this->assertSame($mockAdapter, $retrievedAdapter);
    }

    /**
     * Test generating a chat completion
     */
    public function testGenerateChatCompletion()
    {
        $mockAdapter = $this->createMock(ProviderAdapterInterface::class);
        $mockAdapter->method('isAvailable')->willReturn(true);
        $mockAdapter->method('getName')->willReturn('mock-provider');

        $expectedResponse = new AIResponse(
            'This is a test response',
            'test-model',
            'mock-provider',
            ['raw' => 'response'],
            [],
            [
                'prompt_tokens' => 10,
                'completion_tokens' => 20,
                'total_tokens' => 30,
            ]
        );

        $messages = [
            ['role' => 'assistant', 'content' => 'You are a helpful assistant'],
            ['role' => 'user', 'content' => 'Hello'],
        ];

        // Create a config to pass directly to useProvider
        $config = new AIConfig('mock-provider', 'mock-model');

        $mockAdapter
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with($messages, [])
            ->willReturn($expectedResponse);

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $service
            ->addAdapter('mock-provider', $mockAdapter)
            ->useProvider('mock-provider', $config);

        $response = $service->generateChatCompletion($messages);

        $this->assertSame($expectedResponse, $response);
        $this->assertEquals('This is a test response', $response->getContent());
        $this->assertEquals(30, $response->getTotalTokens());
    }

    /**
     * Test the generateEmbeddings method.
     */
    public function testGenerateEmbeddings()
    {
        $mockAdapter = $this->createMock(ProviderAdapterInterface::class);
        $mockAdapter->method('isAvailable')->willReturn(true);

        $expectedEmbeddings = [[0.1, 0.2, 0.3, 0.4], [0.5, 0.6, 0.7, 0.8]];

        $texts = ['Text 1', 'Text 2'];

        $mockAdapter
            ->expects($this->once())
            ->method('generateEmbeddings')
            ->with($texts, [])
            ->willReturn($expectedEmbeddings);

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $config = new AIConfig('mock-provider', 'mock-model');

        $service
            ->addAdapter('mock-provider', $mockAdapter)
            ->useProvider('mock-provider', $config);

        $embeddings = $service->generateEmbeddings($texts);

        $this->assertSame($expectedEmbeddings, $embeddings);
    }

    /**
     * Test the method
     */
    public function testIsProviderAvailable()
    {
        $availableMock = $this->createMock(ProviderAdapterInterface::class);
        $availableMock->method('isAvailable')->willReturn(true);

        $unavailableMock = $this->createMock(ProviderAdapterInterface::class);
        $unavailableMock->method('isAvailable')->willReturn(false);

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        $service->addAdapter('available-provider', $availableMock);
        $service->addAdapter('unavailable-provider', $unavailableMock);

        $this->assertTrue($service->isProviderAvailable('available-provider'));
        $this->assertFalse(
            $service->isProviderAvailable('unavailable-provider')
        );
        $this->assertFalse(
            $service->isProviderAvailable('non-existent-provider')
        );
    }

    /**
     * Test getting service account from config.
     */
    public function testGetServiceAccountFromConfig()
    {
        $encodedServiceAccount = base64_encode(
            json_encode(self::DUMMY_SERVICE_ACCOUNT)
        );

        $this->configsMock
            ->method('offsetGet')
            ->willReturnMap([
                ['ai.default_provider', 'vertexai'],
                ['ai.vertexai.model', 'gemini-2.0-flash-lite'],
                ['ai.vertexai.project_id', 'test-project'],
                ['ai.vertexai.location', 'us-central1'],
                ['ai.vertexai.service_account', $encodedServiceAccount],
                ['ai.vertexai.temperature', 0.7],
                ['ai.vertexai.max_tokens', 1024],
            ]);

        $service = new Service(
            $this->configsMock,
            $this->loggerMock,
            $this->tokenServiceMock,
            $this->httpClient
        );

        // Use reflection to access private method
        $method = $this->getMethod(
            Service::class,
            'getServiceAccountFromConfig'
        );

        $result = $method->invokeArgs($service, [$encodedServiceAccount]);
        $this->assertEquals(self::DUMMY_SERVICE_ACCOUNT, $result);

        $result = $method->invokeArgs($service, ['']);
        $this->assertNull($result);

        $result = $method->invokeArgs($service, ['invalid-base64']);
        $this->assertNull($result);

        $result = $method->invokeArgs($service, [
            base64_encode('not-valid-json'),
        ]);
        $this->assertNull($result);
    }
}
