<?php

namespace Salesfloor\Tests\Unit\AI\Agent;

use Codeception\Test\Unit;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Agent\CampaignAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;

class CampaignAgentTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * @var AIService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockAIService;

    /**
     * @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockLogger;

    /**
     * @var CampaignAgent
     */
    private $campaignAgent;

    protected function _before()
    {
        // Mock dependencies
        $this->mockAIService = $this->createMock(AIService::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);

        // Create the agent with mocked dependencies
        $this->campaignAgent = new CampaignAgent(
            $this->mockAIService,
            $this->mockLogger
        );
    }

    /**
     * Test generating marketing email content
     */
    public function testGenerateMarketingEmail()
    {
        $campaignType = CampaignType::promotion();
        $campaignDetails = [
            'offer' => '30% off summer items',
            'deadline' => 'End of month',
            'target_audience' => 'Existing customers',
        ];
        $options = ['temperature' => 0.7];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn('This is a promotional email content.');

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) {
                    return count($messages) === 3 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'assistant' &&
                        $messages[2]['role'] === 'user' &&
                        strpos($messages[2]['content'], 'promotion campaign') !== false;
                }),
                $options
            )
            ->willReturn($mockResponse);

        $result = $this->campaignAgent->generateMarketingEmail(
            $campaignType,
            $campaignDetails,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals(
            'This is a promotional email content.',
            $result->getContent()
        );
    }

    /**
     * Test generating SMS marketing content
     */
    public function testGenerateMarketingSMS()
    {
        $campaignType = CampaignType::promotion();
        $campaignDetails = [
            'offer' => '30% off summer items',
            'deadline' => 'End of month',
            'tracking_link' => 'example.com/sale',
        ];
        $options = ['temperature' => 0.7];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn(
                'SUMMER SALE: 30% off until end of month. Shop now: example.com/sale'
            );

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) {
                    return count($messages) === 3 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'assistant' &&
                        $messages[2]['role'] === 'user' &&
                        strpos($messages[2]['content'], 'SMS') !== false &&
                        strpos($messages[0]['content'], 'characters') !== false;
                }),
                $options
            )
            ->willReturn($mockResponse);

        $result = $this->campaignAgent->generateMarketingSMS(
            $campaignType,
            $campaignDetails,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals(
            'SUMMER SALE: 30% off until end of month. Shop now: example.com/sale',
            $result->getContent()
        );
    }

    /**
     * Test generating subject lines
     */
    public function testGenerateSubjectLines()
    {
        $emailContent =
            'This is an email about our summer sale with 30% off all items.';
        $options = ['temperature' => 0.7];

        $subjectLinesContent =
            "Hot Summer Sale: 30% Off Everything";

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse->method('getContent')->willReturn($subjectLinesContent);

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) use ($emailContent) {
                    return count($messages) === 2 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'user' &&
                        strpos($messages[1]['content'], $emailContent) !==
                            false;
                }),
                $options
            )
            ->willReturn($mockResponse);

        $result = $this->campaignAgent->generateSubjectLines(
            $emailContent,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals($subjectLinesContent, $result->getContent());
    }

    /**
     * Test buildPrompt method for email content
     */
    public function testBuildPromptEmail()
    {
        $campaignType = CampaignType::productLaunch();
        $contentType = ContentType::email();
        $details = [
            'product_name' => 'New Widget',
            'features' => ['Feature 1', 'Feature 2'],
            'launch_date' => '2024-01-15'
        ];

        $messages = $this->campaignAgent->buildPrompt(
            $campaignType,
            $contentType,
            $details
        );

        $this->assertIsArray($messages);
        $this->assertCount(3, $messages);

        // Check system prompt structure
        $this->assertEquals('assistant', $messages[0]['role']);
        $this->assertStringContainsString('marketing communications specialist', $messages[0]['content']);
        $this->assertStringContainsString('product launch', $messages[0]['content']);

        // Check marketing guidelines
        $this->assertEquals('assistant', $messages[1]['role']);

        // Check user prompt
        $this->assertEquals('user', $messages[2]['role']);
        $this->assertStringContainsString('product_launch campaign', $messages[2]['content']);
        $this->assertStringContainsString('New Widget', $messages[2]['content']);
    }

    /**
     * Test buildPrompt method for SMS content
     */
    public function testBuildPromptSMS()
    {
        $campaignType = CampaignType::promotion();
        $contentType = ContentType::sms();
        $details = [
            'offer' => '20% off',
            'deadline' => 'Today only'
        ];

        $messages = $this->campaignAgent->buildPrompt(
            $campaignType,
            $contentType,
            $details
        );

        $this->assertIsArray($messages);
        $this->assertCount(3, $messages);

        // Check SMS-specific system prompt
        $this->assertEquals('assistant', $messages[0]['role']);
        $this->assertStringContainsString('SMS', $messages[0]['content']);
        $this->assertStringContainsString('160 characters', $messages[0]['content']);

        // Check user prompt for SMS
        $this->assertEquals('user', $messages[2]['role']);
        $this->assertStringContainsString('marketing SMS', $messages[2]['content']);
        $this->assertStringContainsString('20% off', $messages[2]['content']);
    }

    /**
     * Test campaign-specific instructions for different campaign types
     */
    public function testCampaignInstructions()
    {
        $reflection = new \ReflectionClass($this->campaignAgent);
        $method = $reflection->getMethod('getCampaignInstructions');
        $method->setAccessible(true);

        // Test product launch instructions
        $productLaunchType = CampaignType::productLaunch();
        $instructions = $method->invoke($this->campaignAgent, $productLaunchType);
        $this->assertStringContainsString('new and innovative', $instructions);
        $this->assertStringContainsString('benefits and features', $instructions);

        // Test promotion instructions
        $promotionType = CampaignType::promotion();
        $instructions = $method->invoke($this->campaignAgent, $promotionType);
        $this->assertStringContainsString('offer and any conditions', $instructions);
        $this->assertStringContainsString('sense of urgency', $instructions);

        // Test event instructions
        $eventType = CampaignType::event();
        $instructions = $method->invoke($this->campaignAgent, $eventType);
        $this->assertStringContainsString('event details', $instructions);
        $this->assertStringContainsString('what, when, where', $instructions);

        // Test welcome instructions
        $welcomeType = CampaignType::welcome();
        $instructions = $method->invoke($this->campaignAgent, $welcomeType);
        $this->assertStringContainsString('appreciation for joining', $instructions);
        $this->assertStringContainsString('first step', $instructions);
    }

    /**
     * Test error handling when generating marketing email
     */
    public function testGenerateMarketingEmailError()
    {
        $campaignType = CampaignType::promotion();
        $campaignDetails = ['offer' => '30% off'];

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('API error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Failed to generate marketing email'));

        $this->expectException(AIAdapterException::class);
        $this->campaignAgent->generateMarketingEmail(
            $campaignType,
            $campaignDetails
        );
    }

    /**
     * Test error handling when generating SMS
     */
    public function testGenerateMarketingSMSError()
    {
        $campaignType = CampaignType::promotion();
        $campaignDetails = ['offer' => '30% off'];

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('API error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Failed to generate marketing SMS'));

        $this->expectException(AIAdapterException::class);
        $this->campaignAgent->generateMarketingSMS(
            $campaignType,
            $campaignDetails
        );
    }

    /**
     * Test error handling when generating subject lines
     */
    public function testGenerateSubjectLinesError()
    {
        $emailContent = 'This is a test email content.';

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('API error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with($this->stringContains('Failed to generate subject lines'));

        $this->expectException(AIAdapterException::class);
        $this->campaignAgent->generateSubjectLines($emailContent);
    }

    /**
     * Test building system prompt for different content types
     */
    public function testBuildSystemPrompt()
    {
        $reflection = new \ReflectionClass($this->campaignAgent);
        $method = $reflection->getMethod('buildSystemPrompt');
        $method->setAccessible(true);

        // Test email system prompt
        $emailPrompt = $method->invoke(
            $this->campaignAgent,
            CampaignType::newsletter(),
            ContentType::email()
        );
        $this->assertStringContainsString('marketing communications specialist', $emailPrompt);
        $this->assertStringContainsString('Include a compelling table of contents', $emailPrompt);

        // Test SMS system prompt
        $smsPrompt = $method->invoke(
            $this->campaignAgent,
            CampaignType::promotion(),
            ContentType::sms()
        );
        $this->assertStringContainsString('SMS marketing content', $smsPrompt);
        $this->assertStringContainsString('160 characters', $smsPrompt);
        $this->assertStringContainsString('Clearly state the offer', $smsPrompt);
    }
}
