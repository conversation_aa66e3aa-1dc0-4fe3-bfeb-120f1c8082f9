<?php

namespace Salesfloor\Tests\Unit\AI\Agent;

use Codeception\Test\Unit;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Agent\CampaignAgent;
use Salesfloor\Services\AI\Agent\RequestAnalyzerAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;

class RequestAnalyzerAgentTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * @var AIService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockAIService;

    /**
     * @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockLogger;

    /**
     * @var CampaignAgent|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockCampaignAgent;

    /**
     * @var RequestAnalyzerAgent
     */
    private $analyzerAgent;

    protected function _before()
    {
        // Mock dependencies
        $this->mockAIService = $this->createMock(AIService::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        $this->mockCampaignAgent = $this->createMock(CampaignAgent::class);

        // Create the agent with mocked dependencies
        $this->analyzerAgent = new RequestAnalyzerAgent(
            $this->mockAIService,
            $this->mockLogger,
            $this->mockCampaignAgent
        );
    }

    /**
     * Test analyzing a user request
     */
    public function testAnalyze()
    {
        $userRequest = 'Create an email about our summer sale with 30% off';
        $additionalContext = ['organization' => 'test-retailer'];

        $analysis = [
            'campaign_type' => 'promotion',
            'content_type' => 'email',
            'context' => [
                'offer' => '30% off',
                'season' => 'summer',
                'event' => 'sale',
            ],
            'reasoning' => 'This is a promotional email request'
        ];

        $mockAnalysisResponse = $this->createMock(AIResponse::class);
        $mockAnalysisResponse
            ->method('getContent')
            ->willReturn(json_encode($analysis));

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) use ($userRequest) {
                    return count($messages) === 2 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'user' &&
                        strpos($messages[1]['content'], $userRequest) !== false;
                }),
                $this->equalTo([])
            )
            ->willReturn($mockAnalysisResponse);

        $result = $this->analyzerAgent->analyze($userRequest, $additionalContext);

        $this->assertIsArray($result);
        $this->assertEquals('promotion', $result['campaign_type']);
        $this->assertEquals('email', $result['content_type']);
        $this->assertArrayHasKey('context', $result);
        $this->assertEquals('30% off', $result['context']['offer']);
    }

    /**
     * Test generating content from analysis for email
     */
    public function testGenerateContentFromAnalysisEmail()
    {
        $analysis = [
            'campaign_type' => 'newsletter',
            'content_type' => 'email',
            'context' => ['topic' => 'Monthly update'],
        ];
        $options = ['temperature' => 0.5];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn('Newsletter email content');

        $this->mockCampaignAgent
            ->expects($this->once())
            ->method('generateMarketingEmail')
            ->with(
                $this->callback(function ($campaignType) {
                    return $campaignType instanceof CampaignType &&
                           $campaignType->getValue() === 'newsletter';
                }),
                $this->equalTo(['topic' => 'Monthly update']),
                $this->equalTo($options)
            )
            ->willReturn($mockResponse);

        $result = $this->analyzerAgent->generateContentFromAnalysis($analysis, $options);

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals('Newsletter email content', $result->getContent());
    }

    /**
     * Test generating content from analysis for SMS
     */
    public function testGenerateContentFromAnalysisSMS()
    {
        $analysis = [
            'campaign_type' => 'promotion',
            'content_type' => 'sms',
            'context' => ['offer' => 'Flash sale'],
        ];
        $options = [];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn('Flash sale SMS content');

        $this->mockCampaignAgent
            ->expects($this->once())
            ->method('generateMarketingSMS')
            ->with(
                $this->callback(function ($campaignType) {
                    return $campaignType instanceof CampaignType &&
                           $campaignType->getValue() === 'promotion';
                }),
                $this->equalTo(['offer' => 'Flash sale']),
                $this->equalTo([])
            )
            ->willReturn($mockResponse);

        $result = $this->analyzerAgent->generateContentFromAnalysis($analysis, $options);

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals('Flash sale SMS content', $result->getContent());
    }

    /**
     * Test generating content from analysis for text (should use SMS)
     */
    public function testGenerateContentFromAnalysisText()
    {
        $analysis = [
            'campaign_type' => 'promotion',
            'content_type' => 'text',
            'context' => ['offer' => 'Text message sale'],
        ];

        $mockResponse = $this->createMock(AIResponse::class);

        $this->mockCampaignAgent
            ->expects($this->once())
            ->method('generateMarketingSMS')
            ->willReturn($mockResponse);

        $result = $this->analyzerAgent->generateContentFromAnalysis($analysis);

        $this->assertInstanceOf(AIResponse::class, $result);
    }

    /**
     * Test generating subject lines from analysis
     */
    public function testGenerateContentFromAnalysisSubjectLines()
    {
        $analysis = [
            'campaign_type' => 'newsletter',
            'content_type' => 'subject_lines',
            'context' => ['email_content' => 'This is the email body content'],
        ];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn('Amazing Newsletter Subject');

        $this->mockCampaignAgent
            ->expects($this->once())
            ->method('generateSubjectLines')
            ->with(
                $this->equalTo('This is the email body content'),
                $this->equalTo([])
            )
            ->willReturn($mockResponse);

        $result = $this->analyzerAgent->generateContentFromAnalysis($analysis);

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals('Amazing Newsletter Subject', $result->getContent());
    }

    /**
     * Test generating subject lines with empty email content
     */
    public function testGenerateContentFromAnalysisSubjectLinesEmpty()
    {
        $analysis = [
            'campaign_type' => 'newsletter',
            'content_type' => 'subject_lines',
            'context' => [], // No email_content
        ];

        $mockResponse = $this->createMock(AIResponse::class);

        $this->mockCampaignAgent
            ->expects($this->once())
            ->method('generateSubjectLines')
            ->with(
                $this->equalTo(''),
                $this->equalTo([])
            )
            ->willReturn($mockResponse);

        $result = $this->analyzerAgent->generateContentFromAnalysis($analysis);

        $this->assertInstanceOf(AIResponse::class, $result);
    }

    /**
     * Test error handling in analyze
     */
    public function testAnalyzeError()
    {
        $userRequest = 'Create a promotional email';

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('API error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Failed to analyze marketing request')
            );

        $this->expectException(AIAdapterException::class);
        $this->analyzerAgent->analyze($userRequest);
    }

    /**
     * Test getCampaignAgent method
     */
    public function testGetCampaignAgent()
    {
        $campaignAgent = $this->analyzerAgent->getCampaignAgent();
        $this->assertSame($this->mockCampaignAgent, $campaignAgent);
    }

    /**
     * Test parsing analysis response with valid JSON
     */
    public function testParseAnalysisResponseValid()
    {
        $reflection = new \ReflectionClass($this->analyzerAgent);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $response = json_encode([
            'campaign_type' => 'promotion',
            'content_type' => 'email',
            'context' => ['offer' => '20% off'],
            'reasoning' => 'This is a promotional request'
        ]);

        $result = $method->invoke($this->analyzerAgent, $response);

        $this->assertEquals('promotion', $result['campaign_type']);
        $this->assertEquals('email', $result['content_type']);
        $this->assertEquals(['offer' => '20% off'], $result['context']);
        $this->assertEquals('This is a promotional request', $result['reasoning']);
    }

    /**
     * Test parsing analysis response with invalid JSON
     */
    public function testParseAnalysisResponseInvalid()
    {
        $reflection = new \ReflectionClass($this->analyzerAgent);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Error parsing analysis response')
            );

        $result = $method->invoke($this->analyzerAgent, 'invalid json');

        // Should return fallback values
        $this->assertEquals('newsletter', $result['campaign_type']);
        $this->assertEquals('email', $result['content_type']);
        $this->assertArrayHasKey('context', $result);
        $this->assertArrayHasKey('reasoning', $result);
    }

    /**
     * Test parsing analysis response with invalid campaign type
     */
    public function testParseAnalysisResponseInvalidCampaignType()
    {
        $reflection = new \ReflectionClass($this->analyzerAgent);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $response = json_encode([
            'campaign_type' => 'invalid_campaign',
            'content_type' => 'email'
        ]);

        $result = $method->invoke($this->analyzerAgent, $response);

        $this->assertEquals('newsletter', $result['campaign_type']); // Should default
        $this->assertEquals('email', $result['content_type']);
    }

    /**
     * Test parsing analysis response with invalid content type
     */
    public function testParseAnalysisResponseInvalidContentType()
    {
        $reflection = new \ReflectionClass($this->analyzerAgent);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $response = json_encode([
            'campaign_type' => 'promotion',
            'content_type' => 'invalid_content'
        ]);

        $result = $method->invoke($this->analyzerAgent, $response);

        $this->assertEquals('promotion', $result['campaign_type']);
        $this->assertEquals('email', $result['content_type']); // Should default
    }

    /**
     * Test building analysis messages
     */
    public function testBuildAnalysisMessages()
    {
        $reflection = new \ReflectionClass($this->analyzerAgent);
        $method = $reflection->getMethod('buildAnalysisMessages');
        $method->setAccessible(true);

        $userRequest = 'Create an SMS promotion';
        $context = ['organization' => 'Test Store'];

        $messages = $method->invoke($this->analyzerAgent, $userRequest, $context);

        $this->assertIsArray($messages);
        $this->assertCount(2, $messages);

        // Check system message
        $this->assertEquals('assistant', $messages[0]['role']);
        $this->assertStringContainsString('analyzing marketing communication requests', $messages[0]['content']);

        // Check user message
        $this->assertEquals('user', $messages[1]['role']);
        $this->assertStringContainsString($userRequest, $messages[1]['content']);
        $this->assertStringContainsString('Test Store', $messages[1]['content']);
    }

    /**
     * Test building user prompt with context
     */
    public function testBuildUserPromptWithContext()
    {
        $reflection = new \ReflectionClass($this->analyzerAgent);
        $method = $reflection->getMethod('buildUserPrompt');
        $method->setAccessible(true);

        $userRequest = 'Create promotional content';
        $context = [
            'organization' => 'Test Store',
            'content_type' => 'sms'
        ];

        $prompt = $method->invoke($this->analyzerAgent, $userRequest, $context);

        $this->assertStringContainsString($userRequest, $prompt);
        $this->assertStringContainsString('Test Store', $prompt);
        $this->assertStringContainsString('IMPORTANT: The user has explicitly requested content of type: sms', $prompt);
    }

    /**
     * Test generation response format
     */
    public function testGetGenerationResponseFormat()
    {
        $format = $this->analyzerAgent->getGenerationResponseFormat();

        $this->assertStringContainsString('JSON', $format);
        $this->assertStringContainsString('campaign_type', $format);
        $this->assertStringContainsString('content_type', $format);
        $this->assertStringContainsString('context', $format);
        $this->assertStringContainsString('reasoning', $format);
    }
}
