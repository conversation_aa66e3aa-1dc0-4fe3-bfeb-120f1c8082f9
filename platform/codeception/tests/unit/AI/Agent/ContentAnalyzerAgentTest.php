<?php

namespace Salesfloor\Tests\Unit\AI\Agent;

use Codeception\Test\Unit;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Agent\ContentAnalyzerAgent;
use Salesfloor\Services\AI\Agent\ContentRefinerAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;

class ContentAnalyzerAgentTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * @var AIService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockAIService;

    /**
     * @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockLogger;

    /**
     * @var ContentRefinerAgent|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockContentRefiner;

    /**
     * @var ContentAnalyzerAgent
     */
    private $contentAnalyzer;

    protected function _before()
    {
        // Mock dependencies
        $this->mockAIService = $this->createMock(AIService::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        $this->mockContentRefiner = $this->createMock(ContentRefinerAgent::class);

        // Create the agent with mocked dependencies
        $this->contentAnalyzer = new ContentAnalyzerAgent(
            $this->mockAIService,
            $this->mockLogger,
            $this->mockContentRefiner
        );
    }

    /**
     * Test successful content analysis
     */
    public function testAnalyzeSuccess()
    {
        $content = "Hi [Name],\n\nReady to refresh your wardrobe?\n\nWe're excited to offer you an exclusive promotion on our women's outfits.\n\nFor a limited time, enjoy 25% off on all women's outfits.";
        $context = ['organization' => 'Test Store'];

        $analysisResponse = [
            'content_type' => 'email',
            'campaign_type' => 'promotion',
            'tone' => 'friendly',
            'target_audience' => 'existing_customers',
            'context' => [
                'offer' => '25% off women\'s outfits',
                'personalization' => true
            ],
            'key_elements' => [
                'personalization' => true,
                'call_to_action' => true,
                'offer_details' => true,
                'urgency_indicators' => true,
                'brand_elements' => false
            ],
            'content_quality' => [
                'grammar' => true,
                'clarity' => true,
                'tone_consistency' => true,
                'length_appropriate' => true
            ],
            'reasoning' => 'Content shows good personalization and clear offer details'
        ];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn(json_encode($analysisResponse));

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) use ($content) {
                    return count($messages) === 2 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'user' &&
                        strpos($messages[1]['content'], $content) !== false;
                }),
                $this->equalTo([])
            )
            ->willReturn($mockResponse);

        $result = $this->contentAnalyzer->analyze($content, $context);

        $this->assertIsArray($result);
        $this->assertEquals('email', $result['content_type']);
        $this->assertEquals('promotion', $result['campaign_type']);
        $this->assertEquals('friendly', $result['tone']);
        $this->assertArrayHasKey('key_elements', $result);
        $this->assertArrayHasKey('content_quality', $result);
        $this->assertArrayHasKey('context', $result);
    }

    /**
     * Test content analysis with invalid JSON response
     */
    public function testAnalyzeInvalidJson()
    {
        $content = "Some marketing content";

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn('Invalid JSON response');

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willReturn($mockResponse);

        $this->mockLogger
            ->expects($this->once())
            ->method('warning')
            ->with(
                $this->stringContains('Exception while parsing analysis response')
            );

        $result = $this->contentAnalyzer->analyze($content);

        // Should return fallback analysis
        $this->assertIsArray($result);
        $this->assertEquals('email', $result['content_type']);
        $this->assertEquals('newsletter', $result['campaign_type']);
        $this->assertEquals('N/A', $result['tone']);
        $this->assertEquals('N/A', $result['target_audience']);
    }

    /**
     * Test content analysis error handling
     */
    public function testAnalyzeError()
    {
        $content = "Content to analyze";

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('API error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Failed to analyze content'),
                $this->arrayHasKey('content_length')
            );

        $this->expectException(AIAdapterException::class);
        $this->contentAnalyzer->analyze($content);
    }

    /**
     * Test successful content refinement from analysis
     */
    public function testRefineContentFromAnalysisEmail()
    {
        $content = "Hi [Name], We have a sale going on. Check it out!";
        $analysis = [
            'content_type' => 'email',
            'campaign_type' => 'promotion',
            'context' => [
                'organization' => 'Test Store'
            ],
            'tone' => 'professional'
        ];
        $options = ['temperature' => 0.5];

        $refinedContent = "Hi [Name],\n\nWe're excited to announce our special sale! Don't miss out on great savings.\n\nCheck it out now!";

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn($refinedContent);

        $this->mockContentRefiner
            ->expects($this->once())
            ->method('refineEmailContent')
            ->with(
                $this->equalTo($content),
                $this->callback(function ($campaignType) {
                    return $campaignType instanceof CampaignType &&
                           $campaignType->getValue() === 'promotion';
                }),
                $this->equalTo([
                    'context' => [
                        'organization' => 'Test Store'
                    ],
                    'tone' => 'professional'
                ]),
                $this->equalTo($options)
            )
            ->willReturn($mockResponse);

        $result = $this->contentAnalyzer->refineContentFromAnalysis(
            $content,
            $analysis,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals($refinedContent, $result->getContent());
    }

    /**
     * Test SMS content refinement from analysis
     */
    public function testRefineContentFromAnalysisSMS()
    {
        $content = "Sale! 30% off everything!";
        $analysis = [
            'content_type' => 'sms',
            'campaign_type' => 'promotion',
            'context' => [
                'offer' => '30% off everything'
            ]
        ];
        $options = [];

        $refinedContent = "SALE: 30% off everything! Shop now.";

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn($refinedContent);

        $this->mockContentRefiner
            ->expects($this->once())
            ->method('refineSMSContent')
            ->with(
                $this->equalTo($content),
                $this->callback(function ($campaignType) {
                    return $campaignType instanceof CampaignType &&
                           $campaignType->getValue() === 'promotion';
                }),
                $this->equalTo([
                    'context' => [
                        'offer' => '30% off everything'
                    ]
                ]),
                $this->equalTo($options)
            )
            ->willReturn($mockResponse);

        $result = $this->contentAnalyzer->refineContentFromAnalysis(
            $content,
            $analysis,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals($refinedContent, $result->getContent());
    }

    /**
     * Test refinement with text content type (should use SMS)
     */
    public function testRefineContentFromAnalysisText()
    {
        $content = "Text message content";
        $analysis = [
            'content_type' => 'text',
            'campaign_type' => 'newsletter'
        ];

        $mockResponse = $this->createMock(AIResponse::class);

        $this->mockContentRefiner
            ->expects($this->once())
            ->method('refineSMSContent')
            ->willReturn($mockResponse);

        $result = $this->contentAnalyzer->refineContentFromAnalysis(
            $content,
            $analysis
        );

        $this->assertInstanceOf(AIResponse::class, $result);
    }

    /**
     * Test parse analysis response with valid JSON
     */
    public function testParseAnalysisResponseValid()
    {
        $reflection = new \ReflectionClass($this->contentAnalyzer);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $response = json_encode([
            'content_type' => 'email',
            'campaign_type' => 'promotion',
            'tone' => 'friendly',
            'target_audience' => 'vip',
            'context' => ['offer' => '20% off'],
            'key_elements' => ['personalization' => true],
            'content_quality' => ['grammar' => true],
            'reasoning' => 'Good content structure'
        ]);

        $result = $method->invoke($this->contentAnalyzer, $response);

        $this->assertEquals('email', $result['content_type']);
        $this->assertEquals('promotion', $result['campaign_type']);
        $this->assertEquals('friendly', $result['tone']);
        $this->assertEquals('vip', $result['target_audience']);
        $this->assertEquals(['offer' => '20% off'], $result['context']);
    }

    /**
     * Test parse analysis response with invalid content type
     */
    public function testParseAnalysisResponseInvalidContentType()
    {
        $reflection = new \ReflectionClass($this->contentAnalyzer);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $response = json_encode([
            'content_type' => 'invalid_type',
            'campaign_type' => 'promotion'
        ]);

        $result = $method->invoke($this->contentAnalyzer, $response);

        $this->assertEquals('email', $result['content_type']); // Should default to email
        $this->assertEquals('promotion', $result['campaign_type']);
    }

    /**
     * Test parse analysis response with invalid campaign type
     */
    public function testParseAnalysisResponseInvalidCampaignType()
    {
        $reflection = new \ReflectionClass($this->contentAnalyzer);
        $method = $reflection->getMethod('parseAnalysisResponse');
        $method->setAccessible(true);

        $response = json_encode([
            'content_type' => 'email',
            'campaign_type' => 'invalid_campaign'
        ]);

        $result = $method->invoke($this->contentAnalyzer, $response);

        $this->assertEquals('email', $result['content_type']);
        $this->assertEquals('newsletter', $result['campaign_type']); // Should default to newsletter
    }
}
