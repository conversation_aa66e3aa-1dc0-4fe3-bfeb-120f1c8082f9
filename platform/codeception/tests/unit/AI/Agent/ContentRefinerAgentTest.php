<?php

namespace Salesfloor\Tests\Unit\AI\Agent;

use Codeception\Test\Unit;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\AI\Agent\ContentRefinerAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service as AIService;
use Salesfloor\Services\AI\ValueObject\CampaignType;
use Salesfloor\Services\AI\ValueObject\ContentType;

class ContentRefinerAgentTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * @var AIService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockAIService;

    /**
     * @var LoggerInterface|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockLogger;

    /**
     * @var ContentRefinerAgent
     */
    private $contentRefiner;

    protected function _before()
    {
        // Mock dependencies
        $this->mockAIService = $this->createMock(AIService::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);

        // Create the agent with mocked dependencies
        $this->contentRefiner = new ContentRefinerAgent(
            $this->mockAIService,
            $this->mockLogger
        );
    }

    /**
     * Test refining email content
     */
    public function testRefineEmailContent()
    {
        $content = "Hi there, We have a big sale going on. Check it out!";
        $campaignType = CampaignType::promotion();
        $context = [
            'organization' => 'Test Store',
            'tone' => 'professional',
            'target_audience' => 'existing_customers'
        ];
        $options = ['temperature' => 0.5];

        $refinedContent = "Hi there,\n\nWe're excited to announce our special sale! Don't miss out on great savings.\n\nCheck it out now!\n\nBest regards,\nTest Store Team";

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn($refinedContent);

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) use ($content) {
                    return count($messages) === 3 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'assistant' &&
                        $messages[2]['role'] === 'user' &&
                        strpos($messages[2]['content'], $content) !== false &&
                        strpos($messages[2]['content'], 'Refine this Email message') !== false;
                }),
                $options
            )
            ->willReturn($mockResponse);

        $result = $this->contentRefiner->refineEmailContent(
            $content,
            $campaignType,
            $context,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals($refinedContent, $result->getContent());
    }

    /**
     * Test refining SMS content
     */
    public function testRefineSMSContent()
    {
        $content = "Big sale happening now! Come check us out for great deals on everything!";
        $campaignType = CampaignType::promotion();
        $context = [
            'offer' => 'Great deals on everything',
            'urgency' => 'now'
        ];
        $options = ['temperature' => 0.7];

        $refinedContent = "BIG SALE NOW! Great deals on everything. Shop today!";

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn($refinedContent);

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->with(
                $this->callback(function ($messages) use ($content) {
                    return count($messages) === 3 &&
                        $messages[0]['role'] === 'assistant' &&
                        $messages[1]['role'] === 'assistant' &&
                        $messages[2]['role'] === 'user' &&
                        strpos($messages[2]['content'], $content) !== false &&
                        strpos($messages[2]['content'], 'Refine this text message') !== false &&
                        strpos($messages[0]['content'], '160 characters') !== false;
                }),
                $options
            )
            ->willReturn($mockResponse);

        $result = $this->contentRefiner->refineSMSContent(
            $content,
            $campaignType,
            $context,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
        $this->assertEquals($refinedContent, $result->getContent());
    }

    /**
     * Test refining email content with minimal context
     */
    public function testRefineEmailContentMinimalContext()
    {
        $content = "Basic email content";
        $campaignType = CampaignType::newsletter();
        $context = [];
        $options = [];

        $mockResponse = $this->createMock(AIResponse::class);
        $mockResponse
            ->method('getContent')
            ->willReturn('Refined basic email content');

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willReturn($mockResponse);

        $result = $this->contentRefiner->refineEmailContent(
            $content,
            $campaignType,
            $context,
            $options
        );

        $this->assertInstanceOf(AIResponse::class, $result);
    }

    /**
     * Test error handling when refining email content
     */
    public function testRefineEmailContentError()
    {
        $content = "Content to refine";
        $campaignType = CampaignType::promotion();
        $context = ['organization' => 'Test Store'];

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('Refinement error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Failed to refine content'),
                $this->arrayHasKey('content_type')
            );

        $this->expectException(AIAdapterException::class);
        $this->contentRefiner->refineEmailContent($content, $campaignType, $context);
    }

    /**
     * Test error handling when refining SMS content
     */
    public function testRefineSMSContentError()
    {
        $content = "SMS content to refine";
        $campaignType = CampaignType::promotion();
        $context = ['offer' => '20% off'];

        $this->mockAIService
            ->expects($this->once())
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('SMS refinement error', 'test'));

        $this->mockLogger
            ->expects($this->once())
            ->method('error')
            ->with(
                $this->stringContains('Failed to refine content'),
                $this->arrayHasKey('content_type')
            );

        $this->expectException(AIAdapterException::class);
        $this->contentRefiner->refineSMSContent($content, $campaignType, $context);
    }

    /**
     * Test building prompt for email content
     */
    public function testBuildPromptEmail()
    {
        $reflection = new \ReflectionClass($this->contentRefiner);
        $method = $reflection->getMethod('buildPrompt');
        $method->setAccessible(true);

        $content = "Test email content";
        $campaignType = CampaignType::productLaunch();
        $contentType = ContentType::email();
        $context = [
            'product_name' => 'New Widget',
            'tone' => 'professional'
        ];

        $messages = $method->invoke(
            $this->contentRefiner,
            $content,
            $campaignType,
            $contentType,
            $context
        );

        $this->assertIsArray($messages);
        $this->assertCount(3, $messages);

        // Check system prompt structure
        $this->assertEquals('assistant', $messages[0]['role']);
        $this->assertStringContainsString('marketing communications editor', $messages[0]['content']);
        $this->assertStringNotContainsString('SMS', $messages[0]['content']);

        // Check marketing guidelines
        $this->assertEquals('assistant', $messages[1]['role']);

        // Check user prompt
        $this->assertEquals('user', $messages[2]['role']);
        $this->assertStringContainsString($content, $messages[2]['content']);
        $this->assertStringContainsString('Refine this Email message', $messages[2]['content']);
        $this->assertStringContainsString('New Widget', $messages[2]['content']);
    }

    /**
     * Test building prompt for SMS content
     */
    public function testBuildPromptSMS()
    {
        $reflection = new \ReflectionClass($this->contentRefiner);
        $method = $reflection->getMethod('buildPrompt');
        $method->setAccessible(true);

        $content = "Test SMS content";
        $campaignType = CampaignType::promotion();
        $contentType = ContentType::sms();
        $context = [
            'offer' => '25% off',
            'deadline' => 'Today only'
        ];

        $messages = $method->invoke(
            $this->contentRefiner,
            $content,
            $campaignType,
            $contentType,
            $context
        );

        $this->assertIsArray($messages);
        $this->assertCount(3, $messages);

        // Check SMS-specific system prompt
        $this->assertEquals('assistant', $messages[0]['role']);
        $this->assertStringContainsString('SMS marketing content', $messages[0]['content']);
        $this->assertStringContainsString('160 characters', $messages[0]['content']);

        // Check user prompt for SMS
        $this->assertEquals('user', $messages[2]['role']);
        $this->assertStringContainsString($content, $messages[2]['content']);
        $this->assertStringContainsString('Refine this text message', $messages[2]['content']);
        $this->assertStringContainsString('25% off', $messages[2]['content']);
    }

    /**
     * Test building system prompt for different content types
     */
    public function testBuildSystemPrompt()
    {
        $reflection = new \ReflectionClass($this->contentRefiner);
        $method = $reflection->getMethod('buildSystemPrompt');
        $method->setAccessible(true);

        // Test email system prompt
        $emailPrompt = $method->invoke(
            $this->contentRefiner,
            CampaignType::newsletter(),
            ContentType::email()
        );
        $this->assertStringContainsString('marketing communications editor', $emailPrompt);
        $this->assertStringNotContainsString('SMS', $emailPrompt);

        // Test SMS system prompt
        $smsPrompt = $method->invoke(
            $this->contentRefiner,
            CampaignType::promotion(),
            ContentType::sms()
        );
        $this->assertStringContainsString('SMS marketing content', $smsPrompt);
        $this->assertStringContainsString('160 characters', $smsPrompt);
    }

    /**
     * Test building refinement instructions with various context elements
     */
    public function testBuildRefinementInstructions()
    {
        $reflection = new \ReflectionClass($this->contentRefiner);
        $method = $reflection->getMethod('buildRefinementInstructions');
        $method->setAccessible(true);

        $context = [
            'content_type' => 'email',
            'campaign_type' => 'promotion',
            'target_audience' => 'vip_customers',
            'tone' => 'professional',
            'content_quality' => [
                'grammar' => false,
                'clarity' => false,
                'tone_consistency' => true,
                'length_appropriate' => true
            ],
            'key_elements' => [
                'personalization' => true,
                'call_to_action' => true,
                'offer_details' => false,
                'urgency_indicators' => false
            ],
            'reasoning' => 'Improve grammar and clarity while maintaining professional tone',
            'custom_field' => 'custom_value'
        ];

        $instructions = $method->invoke($this->contentRefiner, $context);

        $this->assertStringContainsString('Content type: email', $instructions);
        $this->assertStringContainsString('Campaign type: promotion', $instructions);
        $this->assertStringContainsString('Target audience: vip_customers', $instructions);
        $this->assertStringContainsString('Desired tone: professional', $instructions);
        $this->assertStringContainsString('Focus on improving: grammar, clarity', $instructions);
        $this->assertStringContainsString('Key elements that must be emphasized or retained: personalization, call to action', $instructions);
        $this->assertStringContainsString('Adopt this recommendation: Improve grammar and clarity', $instructions);
        $this->assertStringContainsString('Custom Field: custom_value', $instructions);
    }

    /**
     * Test building refinement instructions with empty context
     */
    public function testBuildRefinementInstructionsEmpty()
    {
        $reflection = new \ReflectionClass($this->contentRefiner);
        $method = $reflection->getMethod('buildRefinementInstructions');
        $method->setAccessible(true);

        $instructions = $method->invoke($this->contentRefiner, []);

        $this->assertIsString($instructions);
        $this->assertEmpty(trim($instructions));
    }

    /**
     * Test building user prompt for different campaign types
     */
    public function testBuildUserPromptDifferentCampaigns()
    {
        $reflection = new \ReflectionClass($this->contentRefiner);
        $method = $reflection->getMethod('buildUserPrompt');
        $method->setAccessible(true);

        $content = "Test content";
        $context = ['organization' => 'Test Store'];

        // Test email prompt
        $emailPrompt = $method->invoke(
            $this->contentRefiner,
            $content,
            CampaignType::welcome(),
            ContentType::email(),
            $context
        );
        $this->assertStringContainsString('Refine this Email message', $emailPrompt);
        $this->assertStringContainsString('welcome campaign', $emailPrompt);
        $this->assertStringContainsString($content, $emailPrompt);

        // Test SMS prompt
        $smsPrompt = $method->invoke(
            $this->contentRefiner,
            $content,
            CampaignType::reengagement(),
            ContentType::sms(),
            $context
        );
        $this->assertStringContainsString('Refine this text message', $smsPrompt);
        $this->assertStringContainsString('reengagement campaign', $smsPrompt);
        $this->assertStringContainsString($content, $smsPrompt);
    }
}
