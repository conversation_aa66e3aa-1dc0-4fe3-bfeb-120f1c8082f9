<?php

namespace Salesfloor\Tests\Unit\AI\ValueObject;

use Codeception\Test\Unit;
use Salesfloor\Services\AI\ValueObject\ContentType;

class ContentTypeTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * Test valid content type constants
     */
    public function testValidContentTypes()
    {
        $contentType = new ContentType(ContentType::EMAIL);
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue());

        $contentType = new ContentType(ContentType::SMS);
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        $contentType = new ContentType(ContentType::TEXT);
        $this->assertEquals(ContentType::TEXT, $contentType->getValue());

        $contentType = new ContentType(ContentType::SUBJECT_LINES);
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());
    }

    /**
     * Test content type aliases
     */
    public function testContentTypeAliases()
    {
        // Test SMS aliases
        $contentType = new ContentType('txt');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        $contentType = new ContentType('message');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        // Test subject line aliases
        $contentType = new ContentType('subject');
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());

        $contentType = new ContentType('subject_line');
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());
    }

    /**
     * Test invalid content types default to email
     */
    public function testInvalidContentTypeDefaults()
    {
        $contentType = new ContentType('invalid_type');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue());

        $contentType = new ContentType('');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue());

        $contentType = new ContentType('random_string');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue());
    }

    /**
     * Test case insensitive normalization
     */
    public function testCaseInsensitiveNormalization()
    {
        $contentType = new ContentType('EMAIL');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue());

        $contentType = new ContentType('SMS');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        $contentType = new ContentType('TXT');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        $contentType = new ContentType('SUBJECT');
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());
    }

    /**
     * Test whitespace trimming
     */
    public function testWhitespaceTrimming()
    {
        $contentType = new ContentType(' email ');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue());

        $contentType = new ContentType("\tsms\n");
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        $contentType = new ContentType('  txt  ');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());
    }

    /**
     * Test static factory methods
     */
    public function testStaticFactoryMethods()
    {
        $this->assertEquals(ContentType::EMAIL, ContentType::email()->getValue());
        $this->assertEquals(ContentType::SMS, ContentType::sms()->getValue());
        $this->assertEquals(ContentType::SUBJECT_LINES, ContentType::subjectLines()->getValue());
    }

    /**
     * Test helper methods
     */
    public function testHelperMethods()
    {
        $emailType = ContentType::email();
        $this->assertTrue($emailType->isEmail());
        $this->assertFalse($emailType->isSms());
        $this->assertFalse($emailType->isSubjectLines());

        $smsType = ContentType::sms();
        $this->assertTrue($smsType->isSms());
        $this->assertFalse($smsType->isEmail());
        $this->assertFalse($smsType->isSubjectLines());

        $textType = new ContentType(ContentType::TEXT);
        $this->assertTrue($textType->isSms()); // Text is considered SMS
        $this->assertFalse($textType->isEmail());

        $subjectType = ContentType::subjectLines();
        $this->assertTrue($subjectType->isSubjectLines());
        $this->assertFalse($subjectType->isEmail());
        $this->assertFalse($subjectType->isSms());
    }

    /**
     * Test string conversion
     */
    public function testStringConversion()
    {
        $contentType = ContentType::email();
        $this->assertEquals(ContentType::EMAIL, (string) $contentType);

        $contentType = ContentType::sms();
        $this->assertEquals(ContentType::SMS, (string) $contentType);

        $contentType = new ContentType('invalid');
        $this->assertEquals(ContentType::EMAIL, (string) $contentType);
    }

    /**
     * Test object equality
     */
    public function testObjectEquality()
    {
        $contentType1 = ContentType::sms();
        $contentType2 = new ContentType(ContentType::SMS);
        $contentType3 = new ContentType('txt'); // Alias

        $this->assertEquals($contentType1->getValue(), $contentType2->getValue());
        $this->assertEquals($contentType1->getValue(), $contentType3->getValue());
        $this->assertEquals($contentType2->getValue(), $contentType3->getValue());

        $contentType4 = ContentType::email();
        $this->assertNotEquals($contentType1->getValue(), $contentType4->getValue());
    }

    /**
     * Test edge cases with special characters
     */
    public function testSpecialCharacters()
    {
        // Should handle underscores in input
        $contentType = new ContentType('subject_lines');
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());

        $contentType = new ContentType('subject_line');
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());

        // Should handle hyphens and convert to valid type or default
        $contentType = new ContentType('e-mail');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue()); // Invalid, defaults

        // Should handle numbers and special chars
        $contentType = new ContentType('sms123');
        $this->assertEquals(ContentType::EMAIL, $contentType->getValue()); // Invalid, defaults
    }

    /**
     * Test all valid types work correctly
     */
    public function testAllValidTypes()
    {
        $validTypes = [
            ContentType::EMAIL,
            ContentType::SMS,
            ContentType::TEXT,
            ContentType::SUBJECT_LINES,
        ];

        foreach ($validTypes as $type) {
            $contentType = new ContentType($type);
            $this->assertEquals($type, $contentType->getValue());
        }
    }

    /**
     * Test SMS and TEXT both return true for isSms()
     */
    public function testSmsAndTextEquivalence()
    {
        $smsType = new ContentType(ContentType::SMS);
        $textType = new ContentType(ContentType::TEXT);

        $this->assertTrue($smsType->isSms());
        $this->assertTrue($textType->isSms());
        $this->assertFalse($smsType->isEmail());
        $this->assertFalse($textType->isEmail());
    }

    /**
     * Test alias resolution with mixed cases
     */
    public function testMixedCaseAliases()
    {
        $contentType = new ContentType('Message');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());

        $contentType = new ContentType('Subject');
        $this->assertEquals(ContentType::SUBJECT_LINES, $contentType->getValue());

        $contentType = new ContentType('TxT');
        $this->assertEquals(ContentType::SMS, $contentType->getValue());
    }
}
