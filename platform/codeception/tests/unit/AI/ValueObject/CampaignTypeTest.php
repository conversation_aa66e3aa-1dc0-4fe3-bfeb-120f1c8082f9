<?php

namespace Salesfloor\Tests\Unit\AI\ValueObject;

use Codeception\Test\Unit;
use Salesfloor\Services\AI\ValueObject\CampaignType;

class CampaignTypeTest extends Unit
{
    /**
     * @var \UnitTester
     */
    protected $tester;

    /**
     * Test valid campaign type constants
     */
    public function testValidCampaignTypes()
    {
        $campaignType = new CampaignType(CampaignType::PRODUCT_LAUNCH);
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::PROMOTION);
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::EVENT);
        $this->assertEquals(CampaignType::EVENT, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::NEWSLETTER);
        $this->assertEquals(CampaignType::NEWSLETTER, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::WELCOME);
        $this->assertEquals(CampaignType::WELCOME, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::REENGAGEMENT);
        $this->assertEquals(CampaignType::REENGAGEMENT, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::CUSTOMER_UPDATE);
        $this->assertEquals(CampaignType::CUSTOMER_UPDATE, $campaignType->getValue());

        $campaignType = new CampaignType(CampaignType::SALE);
        $this->assertEquals(CampaignType::SALE, $campaignType->getValue());
    }

    /**
     * Test campaign type aliases
     */
    public function testCampaignTypeAliases()
    {
        // Test promotion aliases
        $campaignType = new CampaignType('promo');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        $campaignType = new CampaignType('discount');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        $campaignType = new CampaignType('offer');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        // Test product launch aliases
        $campaignType = new CampaignType('launch');
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, $campaignType->getValue());

        $campaignType = new CampaignType('new_product');
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, $campaignType->getValue());

        // Test customer update aliases
        $campaignType = new CampaignType('update');
        $this->assertEquals(CampaignType::CUSTOMER_UPDATE, $campaignType->getValue());

        $campaignType = new CampaignType('announcement');
        $this->assertEquals(CampaignType::CUSTOMER_UPDATE, $campaignType->getValue());
    }

    /**
     * Test invalid campaign types default to newsletter
     */
    public function testInvalidCampaignTypeDefaults()
    {
        $campaignType = new CampaignType('invalid_type');
        $this->assertEquals(CampaignType::NEWSLETTER, $campaignType->getValue());

        $campaignType = new CampaignType('');
        $this->assertEquals(CampaignType::NEWSLETTER, $campaignType->getValue());

        $campaignType = new CampaignType('random_string');
        $this->assertEquals(CampaignType::NEWSLETTER, $campaignType->getValue());
    }

    /**
     * Test case insensitive normalization
     */
    public function testCaseInsensitiveNormalization()
    {
        $campaignType = new CampaignType('PROMOTION');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        $campaignType = new CampaignType('Product_Launch');
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, $campaignType->getValue());

        $campaignType = new CampaignType('PROMO');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        $campaignType = new CampaignType('Launch');
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, $campaignType->getValue());
    }

    /**
     * Test whitespace trimming
     */
    public function testWhitespaceTrimming()
    {
        $campaignType = new CampaignType(' promotion ');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());

        $campaignType = new CampaignType("\tevent\n");
        $this->assertEquals(CampaignType::EVENT, $campaignType->getValue());

        $campaignType = new CampaignType('  promo  ');
        $this->assertEquals(CampaignType::PROMOTION, $campaignType->getValue());
    }

    /**
     * Test static factory methods
     */
    public function testStaticFactoryMethods()
    {
        $this->assertEquals(CampaignType::PROMOTION, CampaignType::promotion()->getValue());
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, CampaignType::productLaunch()->getValue());
        $this->assertEquals(CampaignType::EVENT, CampaignType::event()->getValue());
        $this->assertEquals(CampaignType::NEWSLETTER, CampaignType::newsletter()->getValue());
        $this->assertEquals(CampaignType::WELCOME, CampaignType::welcome()->getValue());
        $this->assertEquals(CampaignType::REENGAGEMENT, CampaignType::reengagement()->getValue());
        $this->assertEquals(CampaignType::CUSTOMER_UPDATE, CampaignType::customerUpdate()->getValue());
    }

    /**
     * Test helper methods
     */
    public function testHelperMethods()
    {
        $promotionType = CampaignType::promotion();
        $this->assertTrue($promotionType->isPromotion());
        $this->assertFalse($promotionType->isEvent());
        $this->assertFalse($promotionType->isProductLaunch());

        $saleType = new CampaignType(CampaignType::SALE);
        $this->assertTrue($saleType->isPromotion()); // Sale is considered promotion
        $this->assertFalse($saleType->isEvent());

        $eventType = CampaignType::event();
        $this->assertTrue($eventType->isEvent());
        $this->assertFalse($eventType->isPromotion());
        $this->assertFalse($eventType->isProductLaunch());

        $productLaunchType = CampaignType::productLaunch();
        $this->assertTrue($productLaunchType->isProductLaunch());
        $this->assertFalse($productLaunchType->isPromotion());
        $this->assertFalse($productLaunchType->isEvent());
    }

    /**
     * Test string conversion
     */
    public function testStringConversion()
    {
        $campaignType = CampaignType::promotion();
        $this->assertEquals(CampaignType::PROMOTION, (string) $campaignType);

        $campaignType = CampaignType::productLaunch();
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, (string) $campaignType);

        $campaignType = new CampaignType('invalid');
        $this->assertEquals(CampaignType::NEWSLETTER, (string) $campaignType);
    }

    /**
     * Test object equality
     */
    public function testObjectEquality()
    {
        $campaignType1 = CampaignType::promotion();
        $campaignType2 = new CampaignType(CampaignType::PROMOTION);
        $campaignType3 = new CampaignType('promo'); // Alias

        $this->assertEquals($campaignType1->getValue(), $campaignType2->getValue());
        $this->assertEquals($campaignType1->getValue(), $campaignType3->getValue());
        $this->assertEquals($campaignType2->getValue(), $campaignType3->getValue());

        $campaignType4 = CampaignType::event();
        $this->assertNotEquals($campaignType1->getValue(), $campaignType4->getValue());
    }

    /**
     * Test edge cases with special characters
     */
    public function testSpecialCharacters()
    {
        // Should handle underscores in input
        $campaignType = new CampaignType('product_launch');
        $this->assertEquals(CampaignType::PRODUCT_LAUNCH, $campaignType->getValue());

        $campaignType = new CampaignType('customer_update');
        $this->assertEquals(CampaignType::CUSTOMER_UPDATE, $campaignType->getValue());

        // Should handle hyphens and convert to valid type or default
        $campaignType = new CampaignType('product-launch');
        $this->assertEquals(CampaignType::NEWSLETTER, $campaignType->getValue()); // Invalid, defaults

        // Should handle numbers and special chars
        $campaignType = new CampaignType('promotion123');
        $this->assertEquals(CampaignType::NEWSLETTER, $campaignType->getValue()); // Invalid, defaults
    }

    /**
     * Test all valid types work correctly
     */
    public function testAllValidTypes()
    {
        $validTypes = [
            CampaignType::PRODUCT_LAUNCH,
            CampaignType::PROMOTION,
            CampaignType::EVENT,
            CampaignType::NEWSLETTER,
            CampaignType::WELCOME,
            CampaignType::REENGAGEMENT,
            CampaignType::CUSTOMER_UPDATE,
            CampaignType::SALE,
        ];

        foreach ($validTypes as $type) {
            $campaignType = new CampaignType($type);
            $this->assertEquals($type, $campaignType->getValue());
        }
    }
}
