<?php

namespace SF;

use Pimple\Container;
use Codeception\Util\Stub;
use Salesfloor\Services\CorporateTaskQueue;
use Salesfloor\API\Managers\Client\Customers\V1;
use Salesfloor\Providers\CorporateTaskQueueServiceProvider;

/**
 * Inherited Methods
 * @method void wantToTest($text)
 * @method void wantTo($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method \Codeception\Lib\Friend haveFriend($name, $actorClass = NULL)
 *
 * @SuppressWarnings(PHPMD)
*/
class RestTester extends \Codeception\Actor
{
    use _generated\RestTesterActions;

    public function setMocksForGetMyUpdates()
    {
        $app = $this->getApp();
        Stub::construct(V1::class, [$app['repositories.mysql']], [
            'sendEmailNotificationToRep' => function () {
                return null;
            }
        ]);
    }

    /**
     * The service 'service.corporate.task.queue' resolved before testing,
     * so we need to fake it by registering a new service provider.
     * We'll improve it later.
     *
     */
    public function fakeCorporateTaskQueueService()
    {
        $this->getApp()->register(new class ($this) extends CorporateTaskQueueServiceProvider {
            /** @var RestTester */
            protected $self;

            public function __construct($self)
            {
                $this->self = $self;
            }

            public function register(Container $app)
            {
                unset($app['service.corporate.task.queue']);
                $app['service.corporate.task.queue'] = function ($app) {
                    return  Stub::make(CorporateTaskQueue::class, [
                        'push' => function (array $params) {
                            $this->self->assertIsNumeric($params['corporate_task_id']);
                            $this->self->assertContains(
                                $params['action'],
                                [
                                    CorporateTaskQueue::TASK_ACTION_CREATE,
                                    CorporateTaskQueue::TASK_ACTION_INSERT,
                                    CorporateTaskQueue::TASK_ACTION_UPDATE_AND_INSERT,
                                    CorporateTaskQueue::TASK_ACTION_DELETE,
                                ]
                            );
                            return;
                        }
                    ]);
                };
            }
        });
    }
}
