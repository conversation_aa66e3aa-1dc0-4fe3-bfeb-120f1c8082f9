<?php

namespace SF\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I

use Codeception\Lib\ModuleContainer;
use SF\Helper\Module\Db;
use Codeception\Module;
use Codeception\TestInterface;
use Codeception\Util\Fixtures;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\MySQLRepository;
use Silex\Application;

/**
 * Class Base
 *
 * Only core helper (from suite) should extend this class, to be sure we only have one loaded getApp()
 *
 * @package SF\Helper
 */
abstract class Base extends Module
{
    abstract protected function loadApplication();

    /**
     * @var Application|null $app
     */
    protected $app = null;

    /**
     * Store in memory all stubs you want to verify (in _after)
     * This is a ugly patch, but once/never are not working atm in cest. To be improved
     * Patch from: https://stackoverflow.com/questions/26140295/codeception-util-stub-methods-exactly-and-once-dont-work
     * @var array $stubsToVerify
     */
    private $stubsToVerify = [];

    public function _before(TestInterface $test)
    {
        $this->debug("Base Helper - Before");

        if ($this->app === null) {
            $this->loadApplication();
        }

        parent::_before($test); // TODO: Change the autogenerated stub
    }

    public function _after(TestInterface $test)
    {
        $this->debug("Base Helper - After");
        // It's important to reset it;
        $this->app = null;

        // Yup that's weird, but it's to reset the global used in WP, otherwise it will use the $app of your first
        // 'loadApplication' and will cause issue with database_transaction.
        unset($GLOBALS['app']);
    }

    public function _beforeSuite($settings = [])
    {
        $this->debug("Base Helper - Before Suite ");

        // The dump SQL happen after this
        parent::_beforeSuite($settings); // TODO: Change the autogenerated stub
    }

    public function _afterSuite()
    {
        $this->debug("Base Helper - After Suite");

        Fixtures::cleanup();

        // In same cases, (bad test name for example), beforeSuite is not even called, but afterSuite() yes.
        if (!empty($this->app)) {
            try {
                // This is to make sure we don't have orphan index in ES cluster.
                $this->app['elasticsearch']->deleteIndex('sf_customer');
                $this->app['elasticsearch']->deleteIndex('sf_retailer_customers');
            } catch (\Exception $e) {
                // TODO : why sometimes it failed with message `Cannot delete indices that are being snapshotted: (truncated...)`?
            }
        }

        parent::_afterSuite(); // TODO: Change the autogenerated stub
    }

    public function __construct(ModuleContainer $moduleContainer, $config = null)
    {
        $this->debug("Base Helper Construct");

        parent::__construct($moduleContainer, $config);
    }

    /**
     * Each test will load the app as a member so you can do $this->app directly in your test without any problems
     *
     * @return mixed
     */
    public function getApp()
    {
        if ($this->app === null) {
            $this->loadApplication();
        }
        return $this->app;
    }

    /**
     * In the future (With directRequest), we may want to play with configs. Put this in your _before or _after to
     * be sure you have fresh configs.
     *
     * This doesn't seems to be necessary since we are loading app on each test
     *
     * @throws \Exception
     */
    public function reloadConfigs()
    {
        $this->debug("Base Reload Configs");

        // This is same behaviour as before where SF_CONF is always tests-dev (codeception/tests.sh:64)
        $this->app['configs'] = $this->app['services.configs']->load('api', "tests-dev");
    }

    /**
     * When you want to test another retailer configs, be careful about what we are doing when loaded different
     * retailer config / env.
     *
     * @param $sourceRepository
     * @param $retailer
     * @param $stack
     * @param $brand
     * @param $retailerId
     *
     * @throws \Exception
     */
    public function reloadConfigsSpecific($sourceRepository, $retailerId)
    {
        $this->debug("Base Reload Configs");

        // This is same behaviour as before where SF_CONF is always tests-dev (codeception/tests.sh:64)
        $this->app['configs'] = $this->app['services.configs']->load($sourceRepository, $retailerId);
    }

    public function changeModuleConfigs($module, $configs)
    {
        $this->getModule($module)->_reconfigure($configs);
    }

    public function setApplication($app)
    {
        $moduleName = Db::class;
        if (!$this->hasModule($moduleName)) {
            return;
        }
        $module = $this->getModule($moduleName);
        $module->_setConfig([
            'application' => $app,
        ]);
    }

    public function setMySqlRepository(MySQLRepository $mySQLRepository)
    {
        $moduleName = Db::class;
        if (!$this->hasModule($moduleName)) {
            return;
        }

        /** @var Db $module  */
        $module = $this->getModule($moduleName);
        $module->_setConfig([
            'repositories.mysql' => $mySQLRepository,
        ]);

        $module->drivers[$module->currentDatabase]->setMySqlRepository($mySQLRepository);
    }

    public function debug($var)
    {
        \Codeception\Util\Debug::debug($var);
    }

    public function reloadApplication()
    {
        $this->loadApplication();
    }

    /**
     * Assert that number of rows in $table matches the $expectedNumber
     * @param integer $expectedNumber Expect number of rows
     * @param string $table Table to assert number of rows of
     * @param array $filter
     */
    public function assertNumberRowsInTable($expectedNumber, $table, $filter = [])
    {
        /** @var MySQLRepository $repo */
        $repo = $this->app['repositories.mysql'];
        $qb   = $repo->getQueryBuilder();
        $qb->select('COUNT(*) as count')
           ->from($table, 'anAlias')
        ;

        foreach ($filter as $column => $value) {
            $qb->andWhere(
                $qb->expr()->eq(
                    $column,
                    $qb->expr()->literal($value)
                )
            );
        }

        $results   = $repo->executeCustomQuery($qb);
        $rowsFound = (isset($results[0]['count']) ? intval($results[0]['count']) : 0);

        $this->assertEquals($expectedNumber, $rowsFound);
    }


    /**
     * Improve "grabColumnFromDatabase()" since i want multiple columns.
     *
     * @param string $table         The table name
     * @param array  $columns       If empty, we select all fields
     * @param array  $filters       Array of filters (same structure as DB module)
     *
     * @return array
     * @throws \Codeception\Exception\ModuleException
     */
    public function grabRowsFromDatabase($table, ?array $columns = [], ?array $filters = []): array
    {
        $fields = '*';
        if (!empty($columns)) {
            $fields = implode(',', $columns);
        }

        /** @var Module\Db $dbModule */
        $dbModule = $this->getModule(Db::class);
        $driver = $dbModule->_getDriver();

        $query      = $driver->select($fields, $table, $filters);
        $parameters = array_values($filters);

        $this->debugSection('Query', $query);
        $this->debugSection('Parameters', $parameters);

        $sth = $driver->executeQuery($query, $parameters);

        return $sth->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * Please use 'delete from $table' instead of 'truncate $table'
     * because it supports database transaction which is important for our tests.
     *
     * @param string $table
     * @return void
     */
    public function emptyTableFromDatabase($table)
    {
        if (empty($table)) {
            return;
        }

        /** @var Module\Db $dbModule */
        $dbModule = $this->getModule(Db::class);
        $driver = $dbModule->_getDriver();

        $driver->executeQuery("DELETE FROM $table", []);
    }

    public function deleteTableFromDatabase($table)
    {
        if (empty($table)) {
            return;
        }

        /** @var Module\Db $dbModule */
        $dbModule = $this->getModule(Db::class);
        $driver = $dbModule->_getDriver();

        $driver->executeQuery("DELETE FROM $table", []);
    }

    /**
     * Reset
     * @return void
     */
    public function resetVerifyStubs()
    {
        $this->stubsToVerify = [];
    }

    /**
     * This is called during _after()
     * @return $this
     */
    public function verifyStubs()
    {
        foreach ($this->stubsToVerify as $stub) {
            $stub->__phpunit_verify();
        }


        return $this;
    }

    /**
     * Add a stub to be check in _after()
     * @param $stub
     */
    public function addStubToVerify($stub)
    {
        $this->stubsToVerify[] = $stub;
    }
}
