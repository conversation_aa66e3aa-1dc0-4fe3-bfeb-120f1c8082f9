<?php

namespace  SF\Helper\Module;

use Codeception\Module;
use Codeception\Test\Cest;
use Codeception\TestInterface;

/**
 * It fakes the 3rd party services for tests.
 * @package SF\Helper\Module
 */
class Snapshot extends Module
{
    protected $requiredFields = [
        'capture',
        'ignore',
        // we use this to skip all tests if it's TRUE.
        'skip'
    ];

    /** @var Cest */
    protected $test;

    /**
     * It represents the current test function and looks like:
     *    ApiTester/EmailBlockListCest/testValidateEmailIsOnGlobalUnsubscribes
     *
     * For the test with data provider, the name keeps same.
     *
     * @var string
     * */
    protected $snapshotName = '';

    /**
     * It represents the current test and looks like:
     *    ApiTester/EmailBlockListCest/testValidateEmailIsOnGlobalUnsubscribes[_index]
     *
     * For the test with data provider, the value keeps changing,
     * So each data set of data provider will have its own value of snapshot file.
     *
     * For example, the api suite has test testFile.php which has function testFunction.
     * this test is working with data provider which has 3 data sets, so each file call
     * in platform/codeception/tests/Base.php:
     *
     *    $initSnapshotFile = $this->I->getSnapshotFile();
     *
     * will return the following values:
     *    ApiTester/testFile/testFunction
     *    ApiTester/testFile/testFunction_1
     *    ApiTester/testFile/testFunction_2
     *
     * Finally, there're 3 snapshots files created.
     *
     * @var string
     * */
    protected $snapshotFile;

    public function _before(TestInterface $test)
    {
        if (!$test instanceof Cest) {
            throw new \RuntimeException('Snapshot module only supports Cest');
        }

        $this->test = $test;
    }

    public function isCaptureSnapshotEnabled(): bool
    {
        return $this->config['capture'];
    }

    public function isIgnoreSnapshotEnabled(): bool
    {
        return $this->config['ignore'];
    }

    // Skip all tests if it's TRUE.
    public function skip(): bool
    {
        return $this->config['skip'];
    }

    /**
     * It returns file name as 'suite/class/method[_index]'
     *
     * @return string
     */
    public function getSnapshotFile(): string
    {
        $folder = (new \ReflectionClass($this->test->getTestClass()))->getShortName();

        $actor = $this->test->getMetadata()->getCurrent('actor');
        $suite = explode('\\', $actor)[1];

        $snapshotName = sprintf('%s/%s/%s', $suite, $folder, $this->test->getTestMethod());
        if ($this->snapshotName !== $snapshotName) {
            // Start new test.
            $this->snapshotName = $snapshotName;
            $this->snapshotFile = $snapshotName;
        } else {
            // Fake services multiple times, there're two reason:
            // 1. Call fakeExternalServices multiple times.
            // 2. Test has multiple data providers.
            $indexes = explode('_', $this->snapshotFile);
            $index = $indexes[1] ?? 0;
            $this->snapshotFile = sprintf('%s_%d', $snapshotName, ++$index);
        }
        return $this->snapshotFile;
    }
}
