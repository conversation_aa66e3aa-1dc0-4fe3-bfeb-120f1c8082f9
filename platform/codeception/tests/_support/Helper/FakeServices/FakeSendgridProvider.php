<?php

namespace SF\Helper\FakeServices;

use Codeception\Util\Stub;
use <PERSON><PERSON>\Container;
use <PERSON><PERSON>\ServiceProviderInterface;
use Salesfloor\Services\Mail\SendgridProvider;
use SF\Helper\Interceptors\SendgridProviderInterceptor;

class FakeSendgridProvider extends Base
{
    protected $className = SendgridProvider::class;

    public function fake()
    {
        $mockedMethods = [];
        $indexCurlApi = 0;
        /** @var Collection $snapshot  */
        foreach ($this->snapshots as $method => $snapshot) {
            switch ($method) {
                case 'curlApi':
                    $mockedMethods['curlApi'] = function (
                        $url,
                        $method,
                        $postFields = [],
                        $useMasterKey = false,
                        $onBehalfOf = null
                    ) use (
                        $snapshot,
                        &$indexCurlApi
                    ) {
                        $input = $snapshot[$indexCurlApi]['input'];
                        $output = $snapshot[$indexCurlApi]['output'];
                        $indexCurlApi++;

                        $this->assertSnapshot($input['url'], $url, 'Failed to assert url');
                        $this->assertSnapshot($input['method'], $method, 'Failed to assert method');

                        $expected = $this->processInputOfSendgrid($input['postFields']);
                        $actual = $this->processInputOfSendgrid($postFields);
                        $this->assertSnapshot($expected, $actual, 'curlApi: Failed to assert postFields');

                        $this->assertSnapshot($input['useMasterKey'], $useMasterKey, 'Failed to assert useMasterKey');
                        $this->assertSnapshot($input['onBehalfOf'], $onBehalfOf, 'Failed to assert onBehalfOf');
                        return $output;
                    };
                    break;
            }
        }

        // Please view the code platform/services/src/Mail/Client.php@140
        //  return $this->app[$this->mailProvider];
        //
        // The service is resolved dynamically,
        // so we register a fake service just in case it is resolved before testing.
        $this->app->register(new class ($mockedMethods) implements ServiceProviderInterface {
            protected $mockedMethods;

            public function __construct(array $mockedMethods)
            {
                $this->mockedMethods = $mockedMethods;
            }

            public function register(Container $app)
            {
                unset($app['service.sendgrid.provider']);
                $app['service.sendgrid.provider'] = Stub::construct(
                    SendgridProvider::class,
                    [$app],
                    $this->mockedMethods
                );
            }

            public function boot($app)
            {
                // Nothing to do here.
            }
        });
    }

    protected function setInterceptor(): void
    {
        if ($this->interceptor === null) {
            $this->interceptor = new SendgridProviderInterceptor($this->app);
        }

        $this->app->register(new class ($this->interceptor) implements ServiceProviderInterface {
            protected $mockup;

            public function __construct($mockup)
            {
                $this->mockup = $mockup;
            }

            public function register(Container $app)
            {
                unset($app['service.sendgrid.provider']);
                $app['service.sendgrid.provider'] = $this->mockup;
            }

            public function boot($app)
            {
                // Nothing to do here.
            }
        });
    }

    /**
     * It process the request of send email by sendgrid,
     * Any dynamic values will be replaced by specific values.
     *
     * For example:
     *     Your unique personal token is: 12376
     *     from email:  <EMAIL>.
     *     .......
     *
     * Please add any logic into it if you find new dynamic values which are not processed.
     *
     * @param array $params
     * @return array
     */
    protected function processInputOfSendgrid(array $params): array
    {
        // Rep onboarding will generate token, the email :
        // Your unique personal token is: 14825192,
        // Find this token and replace it by 1234567,
        $content = collect($params['content'])->filter(function ($param) {
            return $param['type'] == 'text/plain';
        })->first();
        if ($content !== null) {
            $contentValue = $content['value'];
            if (preg_match('/Your unique personal token is: (\d+),/', $contentValue, $matches)) {
                foreach ($params['content'] as &$value) {
                    $value['value'] = str_replace($matches[1], '1234567', $value['value']);
                }
            }
        }

        // Appointment , reply to email related to customer id (customer_id)
        // (ar|pr|qr)-{random_int}-<EMAIL>
        // e.g.  <EMAIL>
        if (isset($params['from']['email'])) {
            // platform/services/src/EmailThreads.php::$reqCodes for '(ar|pr|qr)'
            $params['from']['email'] = preg_replace('/^(ar|pr|qr)-\d+-/', '', $params['from']['email']);
            $params['from']['email'] = preg_replace('/^customer\+\d+/', 'customer+1', $params['from']['email']);
        }

        if (isset($params['reply_to']['email'])) {
            // platform/services/src/EmailThreads.php::$reqCodes for '(ar|pr|qr)'
            $params['reply_to']['email'] = preg_replace('/^(ar|pr|qr)-\d+-/', '', $params['reply_to']['email']);
            $params['reply_to']['email'] = preg_replace('/^customer\+\d+/', 'customer+1', $params['reply_to']['email']);
        }

        // Lookbooks contains $params["custom_args"]["sf_events_uniq_id"] which contains random id:
        if (isset($params["custom_args"]["sf_events_uniq_id"])) {
            $params["custom_args"]["sf_events_uniq_id"] = 'events_uniq_id';
        }

        // file_put_contents('postFields.json', json_encode($params), FILE_APPEND);
        foreach ($params['content'] as &$value) {
            // Lookbooks contains link:
            // https://tests.dev.salesfloor.net/en_US/lookbooks/lol56eff3d6ed8b91/{random_string}?sf_rep=reggie&event_source=lookbook

            $value['value'] = preg_replace(
                '^/lookbooks/\w+/\w+\?sf_rep=^',
                '/lookbooks/name/id?sf_rep=',
                $value['value']
            );

            // Appointment Accept/Cancel link which contains
            // ...%3DSFID64809a5c09e8c3.31917255%26...
            // &$deeplink_path=/store-requests/id/book_appointment_1
            // ... meetingid%3D1 ....
            // ... meetingid=1 ....
            // ... mailto:<EMAIL>...
            $patterns = [
                '^SFID\w+\.\w+^' => 'SFID123456',
                '^book_appointment_\d+^' => 'book_appointment_1',
                '^meetingid%3D\d+^' => 'meetingid=1',
                '^meetingid=\d+^' => 'meetingid=1',
                'mailto:(ar|pr|qr)-\d+-' => 'mailto:',
            ];
            $value['value'] = preg_replace(array_keys($patterns), array_values($patterns), $value['value']);

            // Reset password link, for text/html
            // https://tests.dev.salesfloor.net/login?action=rp&amp;key=I392PWSqaufrztagkyLw
            // &amp;login=testemail&event_source=email
            $value['value'] = preg_replace('^&amp;key=\w+&amp;^', '&amp;key=key&amp;', $value['value']);
            $value['value'] = preg_replace('^&key=\w+&^', '&key=key&', $value['value']);

            if ($value['type'] == 'text/html') {
                // $this->assertSnapshotNotEmpty($value['value']);
                // Link for image ... /v1685981576/ .....
                // https://res.cloudinary.com/
                //     salesfloor-net/
                //     image/
                //     upload/
                //     a_exif,c_fill,d_retailer_common_default_circle_2.jpg,g_face,h_250,w_250/
                //     v1685981576/
                //     dev/tests/
                //     testemail
                $value['value'] = preg_replace('^/v\d{9,}/^', '1234567890', $value['value']);
            }
        }

        foreach ($params['attachments'] as &$value) {
            $value['content'] = 'content';
        }
        return $params;
    }
}
