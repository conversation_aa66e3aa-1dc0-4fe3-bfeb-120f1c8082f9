<?php

namespace SF\Helper\FakeServices;

use Codeception\Util\Stub;
use Guzzle\Service\Resource\Model;
use Salesfloor\Services\MessageQueue\MessageQueueClient;
use SF\Helper\Interceptors\MessageQueueClientInterceptor;

class FakeMessageQueueClient extends Base
{
    protected $className = MessageQueueClient::class;

    /**
     * All messages sent by function 'sendMessage' will be stored here.
     * We can use $this->verifyMessageQueueSent() in test to verify that.
     *
     * @var array
     */
    private $messagesSent;

    public function fake()
    {
        $this->messagesSent = [];

        $mockedMethods = [
            'getMessages' => function (
                $queueUrl,
                $waitTimeSeconds = null,
                $hideMessage = null,
                $numberOfMessages = null
            ) {
                return [
                    [
                        "MessageId" => "3759d54a-f88c-43f4-bcad-95ac5858110a",
                        "ReceiptHandle" => "ThisIsATestReceiptHandle",
                        "MD5OfBody" => "5a105e8b9d40e1329780d62ea2265d8a",
                        "Body" => 'Message Body',
                    ]
                ];
            },
            'deleteMessage' => function ($queueUrl, $receiptHandle) {
                return true;
            },
            'deleteQueue' => function ($queueUrl) {
                return true;
            },
            'getNumberOfMessages' => function ($queueUrl) {
                return 1;
            },
        ];

        $indexGetQueueUrl = 0;
        $indexSendMessage = 0;
        $indexCreateQueue = 0;
        $indexSetQueueAttributes = 0;
        $indexGetQueueArn = 0;
        /** @var Collection $snapshot  */
        foreach ($this->snapshots as $method => $snapshot) {
            switch ($method) {
                case 'sendMessage':
                    $mockedMethods['sendMessage'] = function ($queueUrl, $message) use ($snapshot, &$indexSendMessage) {
                        $input = $snapshot[$indexSendMessage]['input'];
                        $output = $snapshot[$indexSendMessage]['output'];
                        $indexSendMessage++;

                        $this->assertSnapshot($input['queueUrl'], $queueUrl, 'Failed to assert queueUrl');
                        $this->messagesSent[] = $message;
                        return $output;
                    };
                    break;
                case 'getQueueUrl':
                    $mockedMethods['getQueueUrl'] = function ($queueName) use ($snapshot, &$indexGetQueueUrl) {
                        $input = $snapshot[$indexGetQueueUrl]['input'];
                        $output = $snapshot[$indexGetQueueUrl]['output'];
                        $indexGetQueueUrl++;

                        $this->assertSnapshot(
                            $this->processQueueName($input['queueName']),
                            $this->processQueueName($queueName),
                            'Failed to assert queueName'
                        );
                        return $output;
                    };
                    break;
                case 'createQueue':
                    $mockedMethods['createQueue'] = function (
                        $queueName,
                        array $attributes = []
                    ) use (
                        $snapshot,
                        &$indexCreateQueue
                    ) {
                        $input = $snapshot[$indexCreateQueue]['input'];
                        $output = $snapshot[$indexCreateQueue]['output'];
                        $indexCreateQueue++;
                        $this->assertSnapshot(
                            $this->processQueueName($input['queueName']),
                            $this->processQueueName($queueName),
                            'Failed to assert queueName'
                        );
                        $this->assertSnapshot($input['attributes'], $attributes, 'Failed to assert attributes');
                        return $output;
                    };
                    break;
                case 'setQueueAttributes':
                    $mockedMethods['setQueueAttributes'] = function (
                        $queueUrl,
                        $attributes
                    ) use (
                        $snapshot,
                        &$indexSetQueueAttributes
                    ) {
                        $input = $snapshot[$indexSetQueueAttributes]['input'];
                        $indexSetQueueAttributes++;

                        $this->assertSnapshot($input['queueUrl'], $queueUrl, 'Failed to assert queueUrl');
                        $this->assertSnapshot($input['attributes'], $attributes, 'Failed to assert attributes');
                    };
                    break;
                case 'getQueueArn':
                    $mockedMethods['getQueueArn'] = function ($queueUrl) use ($snapshot, &$indexGetQueueArn) {
                        $input = $snapshot[$indexGetQueueArn]['input'];
                        $output = $snapshot[$indexGetQueueArn]['output'];
                        $indexGetQueueArn++;

                        $this->assertSnapshot($input['queueUrl'], $queueUrl, 'Failed to assert queueUrl');
                        return $output;
                    };
                    break;
            }
        }

        $messageQueueClient = Stub::makeEmpty(MessageQueueClient::class, $mockedMethods);
        $this->setMockup($messageQueueClient);
    }

    protected function setInterceptor(): void
    {
        if ($this->interceptor === null) {
            $this->interceptor = new MessageQueueClientInterceptor($this->app);
        }

        $this->setMockup($this->interceptor);
    }

    protected function setMockup($mockup): void
    {
        $serviceNames = [
            'match-customer-retailercustomers',
            'service.sendgrid.generic-bulk.queue'
        ];
        foreach ($serviceNames as $serviceName) {
            $service = $this->app[$serviceName];
            $reflectionClass = new \ReflectionClass($service);
            $property = $reflectionClass->getProperty('queue');
            $property->setAccessible(true);
            $property->setValue($service, $mockup);
        }
        unset($this->app['service.messagequeue']);
        $this->app['service.messagequeue'] = $mockup;
    }

    /**
     * Some tests appends a random string to the queue name.
     *  platform/codeception/tests/api/services/EventQueueCest.php appends uuid() and it looks like:
     *      tests-events-dev-xxxxxxxxxxxxxxxxxxxx
     *
     * So, we need to remove it.
     *
     * @param string $queueName
     * @return string
     */
    protected function processQueueName(string $queueName): string
    {
        $eventQueueNameStartWith = "tests-events-dev-";
        if (strpos($queueName, $eventQueueNameStartWith) !== 0) {
            return $queueName;
        }
        return $eventQueueNameStartWith;
    }

    public function getMessagesSent()
    {
        return $this->messagesSent;
    }
}
