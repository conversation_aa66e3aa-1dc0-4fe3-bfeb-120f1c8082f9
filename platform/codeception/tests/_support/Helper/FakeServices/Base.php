<?php

namespace SF\Helper\FakeServices;

use Codeception\Actor;
use Silex\Application;
use SF\Helper\Interceptors\ElasticSearchInterceptor;

abstract class Base
{
    /** @var Actor */
    protected $I;

    /** @var Application */
    protected $app;

    /** @var bool */
    protected $isCaptureSnapshotEnabled;

    /** @var bool*/
    protected $isIgnoreSnapshotEnabled;

    /** @var string */
    protected $className;

    /**
     * The snapshot file name relative to the service name.
     * It's passed to the constructor of the service and looks like:
     *  ApiTester/EmailBlockListCest/testValidateEmailIsOnGlobalUnsubscribes
     *
     * Please view platform/codeception/tests/Base.php:fakeExternalServices()
     *
     *     $snapshotFile = $this->I->getSnapshotFile();
     *
     * @var string
     * */
    protected $initSnapshotFile;

    /**
     * Full snapshot file name.
     * The actual/full snapshot file name. it's used by the fake services to store/fetch snapshots:
     * It looks like codeception_data_folder/$initSnapshotFile:
     *   codeception/tests/_data/SendgridProvider/ApiTester/EmailBlockListCest/testValidateEmailIsOnGlobalUnsubscribes
     *
     * @var string
     * */
    protected $snapshotFile;

    /**
     * The content of snapshot file
     * Each line is a snapshot of a specific request/response to/from external service
     *
     * @var array
     */
    protected $snapshots;


    /**
     * For now, it's only used when faking ES (searchByFilters/searchByFields)
     * Please view platform/codeception/tests/_support/Helper/FakeServices/FakeElasticSearch.php@159
     *
     * The real ids from ES and it looks like:
     *   [
     *      ElasticSearch::class = [
     *          100,200,300,
     *      ]
     *   ]
     *
     * @var array
     * */
    protected $ids;

    protected $interceptor = null;

    public function __construct(
        Actor $I,
        Application $app,
        string $initSnapshotFile,
        array $ids = []
    ) {
        $this->I = $I;
        $this->app = $app;
        $this->initSnapshotFile = $initSnapshotFile;
        $this->ids = $ids;

        $this->isCaptureSnapshotEnabled = $this->I->isCaptureSnapshotEnabled();
        $this->isIgnoreSnapshotEnabled = $this->I->isIgnoreSnapshotEnabled();

        // Default is no snapshot.
        $this->snapshots = [];

        if (!isset($this->className)) {
            $message = sprintf('Please set the class name property ($className) on your service %s', static::class);
            throw new \Symfony\Component\PropertyAccess\Exception\RuntimeException($message);
        }
    }

    protected function getSnapshotServiceName(): string
    {
        $class = new \ReflectionClass($this->className);
        $name = $class->getShortName();
        return $name;
    }

    /**
     * Initialization:
     *  1. snapshots from snapshot file if it exists.
     *  2. or set interceptor to mock real services if capture snapshot is enabled(--env=capture).
     */
    public function initialize(): void
    {
        $serviceName = $this->getSnapshotServiceName();
        // It's something likes:
        // SendgridProvider/ApiTester/EmailBlockListCest/testValidateEmailIsOnGlobalUnsubscribes
        $fullFileName = sprintf('%s%s/%s', codecept_data_dir(), $serviceName, $this->initSnapshotFile);
        if ($this->isCaptureSnapshotEnabled) {
            // Capture snapshot is enabled, the test will talk to real services.
            $this->snapshotFile = $fullFileName;
            $this->setInterceptor();
            return;
        }

        if ($this->isIgnoreSnapshotEnabled) {
            return;
        }

        if (!file_exists($fullFileName)) {
            // No snapshot, do not need to mock.
            codecept_debug(
                sprintf('No snapshot for this test %s/%s, do not need to mock.', $serviceName, $this->initSnapshotFile)
            );
            return;
        }

        $lines = file($fullFileName);
        if (count($lines) === 0) {
            $this->I->assertNotEmpty($lines, "Snapshot file $fullFileName is empty, please run --env=capture.");
        }
        $snapshots = [];
        foreach ($lines as $line) {
            $call = json_decode($line, true);
            foreach ($call as $method => $data) {
                if (!isset($snapshots[$method])) {
                    $snapshots[$method] = [];
                }
                $snapshots[$method][] = $data;
            }
        }
        $this->snapshots = $snapshots;
    }

    public function hasSnapshots(): bool
    {
        return count($this->snapshots) > 0;
    }

    /**
     * It generally used by interceptors to store snapshot to file or remove snapshot file if no snapshot
     * at the end of each test when capture snapshot is enabled.
     *
     * @return void
     */
    public function cleanUp(): void
    {
        if (!$this->isCaptureSnapshotEnabled) {
            return;
        }

        $snapshot = $this->interceptor->snapshot;
        if ($snapshot === '') {
            if (file_exists($this->snapshotFile)) {
                unlink($this->snapshotFile);
            }
        } else {
            if (!file_exists($this->snapshotFile)) {
                $dirName = dirname($this->snapshotFile);
                if (!file_exists($dirName)) {
                    mkdir($dirName, 0777, true);
                }
            }

            file_put_contents($this->snapshotFile, $snapshot);
            $this->interceptor->snapshot = '';
        }
    }

    /**
     * Fake the external services by using snapshots.
     *
     * @return void
     */
    abstract public function fake();

    /**
     * When capture snapshot is enabled (--env=capture), the external services will be intercepted.
     * This function will set the interceptor for each external service.
     *
     * @return void
     */
    abstract protected function setInterceptor(): void;

    protected function assertSnapshot($expected, $actual, $message = ''): void
    {
        $message .= ' Fix the issues and run --env=capture to capture snapshots.';
        $this->I->assertEquals($expected, $actual, $message);

        // Comment out the following lines to capture snapshots in __output folder for debugging only.

        // try {
        //     $this->I->assertEquals($expected, $actual, $message);
        // } catch (\Throwable $th) {
        //     $this->captureData($expected, $actual);
        //     throw $th;
        // }
    }

    /**
     * Sometimes, the snapshot files are big and pretty hard to find the issues(differences).
     * This function will help you get both snapshots (actual, expected) in _output folder,
     * then you can use any IDE/DIFF tools to figure out any issues.
     *
     * Please view function 'assertSnapshot'
     *
     * @param string $expected
     * @param string $actual
     * @return void
     */
    private function captureData($expected, $actual): void
    {
        $fileExpected = codecept_output_dir() . '/snapshot-expected.txt';
        $fileActual = codecept_output_dir() . '/snapshot-actual.txt';
        unlink($fileExpected);
        unlink($fileActual);
        file_put_contents($fileExpected, json_encode($expected) . PHP_EOL, FILE_APPEND);
        file_put_contents($fileActual, json_encode($actual) . PHP_EOL, FILE_APPEND);
        exit(1);
    }
}
