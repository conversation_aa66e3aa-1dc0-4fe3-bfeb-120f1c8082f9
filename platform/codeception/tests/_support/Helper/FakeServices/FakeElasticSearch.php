<?php

namespace SF\Helper\FakeServices;

use Codeception\Util\Stub;
use Salesfloor\Services\ElasticSearch\ElasticSearch;
use SF\Helper\Interceptors\ElasticSearchInterceptor;

class FakeElasticSearch extends Base
{
    protected $className = ElasticSearch::class;

    public function fake()
    {
        $mockedMethods = [];

        $indexSearchByFields = 0;
        $indexSearchByFilters = 0;
        $indexBulk = 0;
        $indexSwitchAlias = 0;
        $indexUpdateSettings = 0;
        $indexSearch = 0;
        $indexReIndex = 0;
        $indexCreateIndex = 0;
        $indexDeleteIndex = 0;
        $indexIndexExist = 0;
        $indexIndex = 0;
        $indexGet = 0;
        $indexDelete = 0;
        /** @var Collection $snapshot  */
        foreach ($this->snapshots as $method => $snapshot) {
            switch ($method) {
                case 'bulk':
                    $mockedMethods['bulk'] = function (
                        $index,
                        $action,
                        array $data
                    ) use (
                        $snapshot,
                        &$indexBulk
                    ) {
                        $params = $snapshot[$indexBulk]['input'];
                        $indexBulk++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            ' bulk: Failed to assert index.'
                        );

                        $this->assertSnapshot($params['action'], $action, 'Failed to assert type.');

                        $expectedData = $params['data'];
                        // Set same id.
                        for ($i = 0; $i < count($expectedData); $i++) {
                            if (isset($data[$i]['id'])) {
                                $expectedData[$i]['id'] = $data[$i]['id'];
                            }

                            if (isset($data[$i]['tags'])) {
                                // should check these tags in test function.
                                $expectedData[$i]['tags'] = $data[$i]['tags'];
                            }

                            // 'onboarding-promote' create new rep.
                            if (isset($data[$i]['user_id'])) {
                                $expectedData[$i]['user_id'] = $data[$i]['user_id'];
                            }
                        }

                        $this->assertSnapshot($expectedData, $data, ' bulk: Failed to assert bulk.');
                    };
                    break;
                case 'switchAlias':
                    $mockedMethods['switchAlias'] = function ($index, $alias) use ($snapshot, &$indexSwitchAlias) {
                        $params = $snapshot[$indexSwitchAlias];
                        $indexSwitchAlias++;
                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            ' switchAlias: Failed to assert index.'
                        );
                        $this->assertSnapshot($params['alias'], $alias, 'Failed to assert alias.');
                        return;
                    };
                    break;
                case 'updateSettings':
                    $mockedMethods['updateSettings'] = function (
                        string $index,
                        array $settings
                    ) use (
                        $snapshot,
                        &$indexUpdateSettings
                    ) {
                        $params = $snapshot[$indexUpdateSettings];
                        $indexUpdateSettings++;
                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'updateSettings: Failed to assert index.'
                        );
                        $this->assertSnapshot(
                            $params['settings'],
                            $settings,
                            'updateSettings: Failed to assert settings.'
                        );
                        return;
                    };
                    break;
                case 'searchByFields':
                    $mockedMethods['searchByFields'] = function (
                        $index,
                        $input,
                        $fields,
                        array $filters,
                        $page,
                        $perPage,
                        $wildCard = ElasticSearch::WILDCARD_BOTH
                    ) use (
                        $snapshot,
                        &$indexSearchByFields
                    ) {
                        $params = $snapshot[$indexSearchByFields]['input'];
                        $output = $snapshot[$indexSearchByFields]['output'];
                        $indexSearchByFields++;
                        $this->assertSnapshot($params['index'], $index, ' searchByFields: Failed to assert index.');
                        $this->assertSnapshot($params['input'], $input, 'Failed to assert input.');
                        $this->assertSnapshot($params['fields'], $fields, 'Failed to assert fields.');
                        $this->assertSnapshot($params['filters'], $filters, 'Failed to assert filters.');
                        $this->assertSnapshot($params['page'], $page, 'Failed to assert page.');
                        $this->assertSnapshot($params['perPage'], $perPage, 'Failed to assert perPage.');
                        $this->assertSnapshot($params['wildCard'], $wildCard, 'Failed to assert wildCard.');

                        if ($this->ids !== []) {
                            foreach ($output['hits']['hits'] as $i => &$hit) {
                                $hit['_id'] = $this->ids[$i];
                            }
                        }
                        return $output;
                    };
                    break;
                case 'searchByFilters':
                    $mockedMethods['searchByFilters'] = function (
                        $index,
                        $filters,
                        $page = null,
                        $perPage = null,
                        $fields = null,
                        $countOnly = false
                    ) use (
                        $snapshot,
                        &$indexSearchByFilters
                    ) {
                        $params = $snapshot[$indexSearchByFilters]['input'];
                        $output = $snapshot[$indexSearchByFilters]['output'];
                        $indexSearchByFilters++;

                        $this->assertSnapshot($params['index'], $index, ' searchByFilters: Failed to assert index.');
                        if (isset($params['filters']["must"][0]["tags"])) {
                            // Search by tags.
                            $params['filters']["must"][0]["tags"] = $filters["must"][0]["tags"];
                        }
                        $this->assertSnapshot($params['filters'], $filters, 'Failed to assert filters.');
                        $this->assertSnapshot($params['page'], $page, 'Failed to assert page.');
                        $this->assertSnapshot($params['perPage'], $perPage, 'Failed to assert perPage.');
                        $this->assertSnapshot($params['fields'], $fields, 'Failed to assert fields.');
                        $this->assertSnapshot($params['countOnly'], $countOnly, 'Failed to assert countOnly.');

                        if ($this->ids !== []) {
                            foreach ($output['hits']['hits'] as $i => &$hit) {
                                $hit['_id'] = $this->ids[$i];
                            }
                        }
                        return $output;
                    };
                    break;
                case 'search':
                    $mockedMethods['search'] = function (
                        $index,
                        $input,
                        $page,
                        $perPage,
                        $wildCard = true
                    ) use (
                        $snapshot,
                        &$indexSearch
                    ) {
                        $params = $snapshot[$indexSearch]['input'];
                        $output = $snapshot[$indexSearch]['output'];
                        $indexSearch++;

                        $this->assertSnapshot($params['index'], $index, 'search: Failed to assert index.');
                        $this->assertSnapshot($params['input'], $input, 'search: Failed to assert input.');
                        $this->assertSnapshot($params['page'], $page, 'Failed to assert page.');
                        $this->assertSnapshot($params['perPage'], $perPage, 'Failed to assert perPage.');
                        $this->assertSnapshot($params['wildCard'], $wildCard, 'Failed to assert wildCard.');
                        if ($this->ids !== []) {
                            foreach ($output['hits']['hits'] as $i => &$hit) {
                                $hit['_id'] = $this->ids[$i];
                            }
                        }
                        return $output;
                    };
                    break;
                case 'reIndex':
                    $mockedMethods['reIndex'] = function ($source, $destination) use ($snapshot, &$indexReIndex) {
                        $params = $snapshot[$indexReIndex]['input'];
                        $output = $snapshot[$indexReIndex]['output'];
                        $indexReIndex++;
                        $this->assertSnapshot($params['source'], $source, 'reIndex: Failed to assert source.');
                        $this->assertSnapshot(
                            $params['destination'],
                            $destination,
                            'reIndex: Failed to assert destination.'
                        );
                        return $output;
                    };
                    break;
                case 'createIndex':
                    $mockedMethods['createIndex'] = function (
                        $index,
                        $body = null
                    ) use (
                        $snapshot,
                        &$indexCreateIndex
                    ) {
                        $params = $snapshot[$indexCreateIndex]['input'];
                        $output = $snapshot[$indexCreateIndex]['output'];
                        $indexCreateIndex++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'createIndex: Failed to assert index.'
                        );

                        $this->assertSnapshot(
                            $params['body'],
                            $body,
                            'createIndex: Failed to assert body.'
                        );

                        return $output;
                    };
                    break;
                case 'deleteIndex':
                    $mockedMethods['deleteIndex'] = function ($index) use ($snapshot, &$indexDeleteIndex) {
                        $params = $snapshot[$indexDeleteIndex]['input'];
                        $output = $snapshot[$indexDeleteIndex]['output'];
                        $indexDeleteIndex++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'deleteIndex: Failed to assert index.'
                        );
                        return $output;
                    };
                    break;
                case 'indexExist':
                    $mockedMethods['indexExist'] = function ($index) use ($snapshot, &$indexIndexExist) {
                        $params = $snapshot[$indexIndexExist]['input'];
                        $output = $snapshot[$indexIndexExist]['output'];
                        $indexIndexExist++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'indexExist: Failed to assert index.'
                        );
                        return $output;
                    };
                    break;
                case 'index':
                    $mockedMethods['index'] = function ($index, $data) use ($snapshot, &$indexIndex) {
                        $params = $snapshot[$indexIndex]['input'];
                        $output = $snapshot[$indexIndex]['output'];
                        $indexIndex++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'index: Failed to assert index.'
                        );
                        $this->assertSnapshot($params['data'], $data, 'index: Failed to assert data.');
                        return $output;
                    };
                    break;
                case 'get':
                    $mockedMethods['get'] = function ($index, $id) use ($snapshot, &$indexGet) {
                        $params = $snapshot[$indexGet]['input'];
                        $output = $snapshot[$indexGet]['output'];
                        $indexGet++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'get: Failed to assert index.'
                        );
                        $this->assertSnapshot($params['id'], $id, 'get: Failed to assert id.');
                        return $output;
                    };
                    break;
                case 'delete':
                    $mockedMethods['delete'] = function ($index, $id) use ($snapshot, &$indexDelete) {
                        $params = $snapshot[$indexDelete]['input'];
                        $output = $snapshot[$indexDelete]['output'];
                        $indexDelete++;

                        $this->assertSnapshot(
                            $this->updateTimestampToMoreGeneric($params['index']),
                            $this->updateTimestampToMoreGeneric($index),
                            'delete: Failed to assert index.'
                        );
                        $this->assertSnapshot($params['id'], $id, 'delete: Failed to assert id.');
                        return $output;
                    };
                    break;
            }
        }

        $elasticsearch = Stub::construct(
            ElasticSearchInterceptor::class,
            [$this->app],
            $mockedMethods
        );
        $this->setMockup($elasticsearch);
    }

    protected function setInterceptor(): void
    {
        if ($this->interceptor === null) {
            $this->interceptor = new ElasticSearchInterceptor($this->app);
        }

        $this->setMockup($this->interceptor);
    }

    protected function setMockup($mockup): void
    {
        foreach (['customers.manager', 'retailer_customers.manager', 'customers_v1.manager'] as $managerName) {
            $manager = $this->app[$managerName];
            $reflectionClass = new \ReflectionClass($manager);
            $property = $reflectionClass->getProperty('elasticsearch');
            $property->setAccessible(true);
            $property->setValue($manager, $mockup);
        }
        unset($this->app['elasticsearch']);
        $this->app['elasticsearch'] = $mockup;
    }

    /**
     *
     * The name of index contains timestamp which is dynamic(sf_customer_2023-02-18-00-00-34).
     * This function replace the timestamp to more generic(xxxx-xx-xx-xx-xx-xx) => sf_customer_xxxx-xx-xx-xx-xx-xx
     * It makes sure assertEquals() works.
     *
     * @param string $content
     * @return void
     */
    protected function updateTimestampToMoreGeneric(string $content)
    {
        return preg_replace(
            '/\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}/',
            'xxxx-xx-xx-xx-xx-xx',
            $content
        );
    }
}
