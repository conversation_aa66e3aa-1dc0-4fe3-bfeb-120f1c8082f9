<?php

namespace SF\Helper\Extensions\Traits;

use Carbon\Carbon;
use Silex\Application;
use SF\Helper\Interceptors\ElasticSearchInterceptor;

/**
 * There're some indices are not cleanup properly at the end of each test.
 * It makes sure that any old indices are removed before each test.
 */
trait ElasticsearchAutoCleanUp
{
    /**
     * The function elasticsearchCleanup is called for the event 'onAfterTest',
     * So set this flag to avoid calling it multiple time.
     *
     * @var bool
     * */
    private $elasticsearch_cleanup_done = false;

    private function elasticsearchCleanup(Application $app)
    {
        if ($this->elasticsearch_cleanup_done) {
            return;
        }

        $elasticsearch = $app['elasticsearch'];
        if (!$elasticsearch instanceof ElasticSearchInterceptor) {
            return;
        }

        $indices = $elasticsearch->getIndices();
        $yesterday = Carbon::now('UTC')->subDay()->format('Y-m-d-H-i-s');
        foreach ($indices as $index => $aliases) {
            if (preg_match('/\.(sf_customer|sf_retailer_customers)-(\d{4}(-\d\d){5})/', $index, $matched)) {
                if ($matched[2] <= $yesterday) {
                    foreach ($aliases['aliases'] as $alias => $empty) {
                        $elasticsearch->deleteAnyAlias($index, $alias);
                    }
                    $elasticsearch->deleteAnyIndex($index);
                }
            }
        }
        $this->elasticsearch_cleanup_done = true;
    }
}
