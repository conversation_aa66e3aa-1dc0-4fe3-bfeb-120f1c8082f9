<?php

namespace SF\Helper\Interceptors;

use Salesfloor\Services\Mail\SendgridProvider;
use Silex\Application;

class SendgridProviderInterceptor extends SendgridProvider
{
    use CaptureSnapshot;

    public function __construct(Application $app)
    {
        parent::__construct($app);
        $this->snapshot = '';
    }

    public function curlApi(
        $url,
        $method = self::GET_METHOD,
        $postFields = [],
        $useMasterKey = false,
        $onBehalfOf = null
    ) {
        $result = parent::curlApi($url, $method, $postFields, $useMasterKey, $onBehalfOf);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'url' => $url,
                    'method' => $method,
                    'postFields' => $postFields,
                    'useMasterKey' => $useMasterKey,
                    'onBehalfOf' => $onBehalfOf,
                ],
                'output' => $result,
            ]
        ]);
        return $result;
    }
}
