<?php

namespace SF\Helper\Interceptors;

use Silex\Application;
use GuzzleHttp\Exception\RequestException;
use Salesfloor\Services\ElasticSearch\ElasticSearch;
use Salesfloor\Services\ElasticSearch\Exceptions\ElasticSearchException;

class ElasticSearchInterceptor extends ElasticSearch
{
    use CaptureSnapshot;

    public function __construct(Application $app)
    {
        parent::__construct($app['configs'], $app['logger']);
        $this->snapshot = '';
    }

    public function reIndex($source, $destination)
    {
        $result = parent::reIndex($source, $destination);

        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'source'    => $source,
                    'destination' => $destination,
                ],
                'output' => $result,
            ]
        ]);

        return $result;
    }

    public function createIndex($index, $body = null)
    {
        $result = parent::createIndex($index, $body);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'body'  => $body,
                ],
                'output' => $result,
            ]
        ]);

        return $result;
    }

    public function deleteIndex($index)
    {
        $result = parent::deleteIndex($index);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                ],
                'output' => $result,
            ]
        ]);
        return $result;
    }

    public function indexExist($index)
    {
        $result = parent::indexExist($index);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                ],
                'output' => $result,
            ]
        ]);
        return $result;
    }

    public function index($index, $data)
    {
        $result = parent::index($index, $data);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'data'  => $data,
                ],
                'output' => $result
            ]
        ]);

        return $result;
    }

    public function get($index, $id)
    {
        $result = parent::get($index, $id);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'id'    => $id,
                ],
                'output' => $result
            ]
        ]);
        return $result;
    }

    public function delete($index, $id)
    {
        $result = parent::delete($index, $id);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'id'    => $id,
                ],
                'output' => $result
            ]
        ]);
        return $result;
    }

    public function bulk($index, $action, array $data): void
    {
        parent::bulk($index, $action, $data);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'action' => $action,
                    'data'  => $data,
                ],
            ]
        ]);
    }

    public function search($index, $input, $page, $perPage, $wildCard = true)
    {
        $result = parent::search($index, $input, $page, $perPage, $wildCard);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'input' => $input,
                    'page'  => $page,
                    'perPage' => $perPage,
                    'wildCard' => $wildCard,
                ],
                'output' => $result,
            ]
        ]);
        return $result;
    }

    public function searchByFilters(
        $index,
        $filters,
        $page = null,
        $perPage = null,
        $fields = null,
        $countOnly = false
    ) {
        $result = parent::searchByFilters(
            $index,
            $filters,
            $page,
            $perPage,
            $fields,
            $countOnly
        );
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'filters' => $filters,
                    'page' => $page,
                    'perPage' => $perPage,
                    'fields' => $fields,
                    'countOnly' => $countOnly,
                ],
                'output' => $result,
            ]
        ]);
        return $result;
    }

    public function searchByFields(
        $index,
        $input,
        $fields,
        array $filters,
        $page,
        $perPage,
        $wildCard = self::WILDCARD_BOTH
    ) {
        $result = parent::searchByFields(
            $index,
            $input,
            $fields,
            $filters,
            $page,
            $perPage,
            $wildCard
        );
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'index' => $index,
                    'input' => $input,
                    'fields' => $fields,
                    'filters' => $filters,
                    'page' => $page,
                    'perPage' => $perPage,
                    'wildCard' => $wildCard,
                ],
                'output' => $result,
            ],
        ]);
        return $result;
    }

    public function switchAlias($index, $alias)
    {
        parent::switchAlias($index, $alias);
        $this->capture([
            __FUNCTION__ => [
                'index' => $index,
                'alias' => $alias,
            ],
        ]);
    }

    public function updateSettings(string $index, array $settings)
    {
        parent::updateSettings($index, $settings);
        $this->capture([
            __FUNCTION__ => [
                'index' => $index,
                'settings' => $settings,
            ],
        ]);
    }
}
