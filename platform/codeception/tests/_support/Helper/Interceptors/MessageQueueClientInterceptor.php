<?php

namespace SF\Helper\Interceptors;

use Silex\Application;
use Salesfloor\Services\MessageQueue\MessageQueueClient;

class MessageQueueClientInterceptor extends MessageQueueClient
{
    use CaptureSnapshot;

    public function __construct(Application $app)
    {
        parent::__construct($app['configs']);
        $this->snapshot = '';
    }

    public function createQueue($queueName, array $attributes = []): string
    {
        $result = parent::createQueue($queueName, $attributes);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'queueName' => $queueName,
                    'attributes' => $attributes,
                ],
                'output' => $result,
            ],
        ]);
        return $result;
    }

    public function getQueueUrl($queueName): string
    {
        $result = parent::getQueueUrl($queueName);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'queueName' => $queueName,
                ],
                'output' => $result,
            ],
        ]);

        return $result;
    }

    public function setQueueAttributes($queueUrl, $attributes)
    {
        parent::setQueueAttributes($queueUrl, $attributes);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'queueUrl' => $queueUrl,
                    'attributes' => $attributes,
                ],
            ],
        ]);
    }

    public function sendMessage($queueUrl, $message)
    {
        $result = parent::sendMessage($queueUrl, $message);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'queueUrl' => $queueUrl,
                    'message' => $message,
                ],
                'output' => $result,
            ],
        ]);
        return $result;
    }

    public function getQueueArn($queueUrl)
    {
        $result = parent::getQueueArn($queueUrl);
        $this->capture([
            __FUNCTION__ => [
                'input' => [
                    'queueUrl' => $queueUrl,
                ],
                'output' => $result,
            ],
        ]);
        return $result;
    }
}
