<?php

namespace SF;

use Salesfloor\Services\Exporter\Base;
use Codeception\Util\Stub;
use Silex\Application;

/**
 * Inherited Methods
 * @method void wantToTest($text)
 * @method void wantTo($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method \Codeception\Lib\Friend haveFriend($name, $actorClass = NULL)
 *
 * @SuppressWarnings(PHPMD)
 */
class FunctionalTester extends \Codeception\Actor
{
    use _generated\FunctionalTesterActions;

    /**
     * Get an exporter, mocked to exclude S3 upload.
     *
     * @param Application $app
     * @param $className
     * @return \Salesfloor\Services\Exporter\Base
     * @throws \Exception
     */
    public function getMockExporter(Application $app, $className)
    {
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = Stub::construct(
            $className,
            [$app],
            [
                'afterExport' => function () {
                    return null;
                },
                'cleanup' => function () {
                    return null;
                },
            ]
        );
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        return $exporter;
    }

    public function assertFileContentsAreIdentical($file1, $file2)
    {
        $file1Lines = file($file1);
        $file2Lines = file($file2);

        $this->assertCount(count($file1Lines), $file2Lines);
        for ($i = 0; $i < count($file1Lines); $i++) {
            $this->assertEquals($file1Lines[$i], $file2Lines[$i]);
        }
    }

    /**
     * Compare the content of two files with customer validation exceptions
     *
     * @param string $file1
     * @param string $file2
     * @param array $rules
     *
     * Available rules: [
     *     'skip' => [...], Where we set an array of the line indexes to skip
     * ]
     */
    public function assertFileContentsAreIdenticalWithException(
        $file1,
        $file2,
        $rules = []
    ) {
        $file1Lines = file($file1);
        $file2Lines = file($file2);

        $this->assertCount(count($file1Lines), $file2Lines);
        for ($i = 0; $i < count($file1Lines); $i++) {
            if (!empty($rules['skip']) && in_array($i, $rules['skip'])) {
                continue;
            }
            $this->assertEquals($file1Lines[$i], $file2Lines[$i]);
        }
    }

    public function assertFileLineCount($expectedLines, $filename)
    {
        $this->assertCount($expectedLines, file($filename));
    }

    /**
     * Creates a mock object for the specified class.
     *
     * This implementation uses Codeception's Stub utility to create mocks
     *
     * @param string $originalClassName Name of the class to mock.
     * @return \PHPUnit\Framework\MockObject\MockObject
     */
    public function createMock(
        string $originalClassName
    ): \PHPUnit\Framework\MockObject\MockObject {
        return \Codeception\Util\Stub::makeEmpty($originalClassName);
    }

    /**
     * Grabs a service from the application container
     *
     * @param string $serviceName
     * @return mixed
     * @throws \RuntimeException if service not found
     */
    public function grabService(string $serviceName)
    {
        $app = $this->getApp();

        if (!isset($app[$serviceName])) {
            throw new \RuntimeException(
                "Service '$serviceName' not found in the application container"
            );
        }

        return $app[$serviceName];
    }
}
