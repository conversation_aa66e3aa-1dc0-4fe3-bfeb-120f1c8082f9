<?php

declare(strict_types=1);

namespace SF\functional\Products;

use Salesfloor\API\Managers\Products;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class ProductsCest extends BaseFunctional
{
    /**
     * The test is unstable, sometimes the rand() returns same value, so it's skipped.
     * @skip
     * @group database_transaction
     * */
    public function testGetRandProduct(FunctionalTester $I)
    {
        $I->wantTo('test that we get a random product');

        $this->insertFixtureGroup($I, 'random_products');

        /** @var Products $productManager */
        $productManager = $this->app['products.manager'];

        $product1 = $productManager->getRandProduct();
        sleep(1);
        $product2 = $productManager->getRandProduct();

        $this->debug($product1);
        $this->debug($product2);

        $I->assertNotEquals($product1->getId(), $product2->getId());
    }
}
