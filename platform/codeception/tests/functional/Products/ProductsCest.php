<?php

declare(strict_types=1);

namespace SF\functional\Products;

use SF\FunctionalTester;
use Salesfloor\Models\Product;
use SF\functional\BaseFunctional;
use Salesfloor\API\Managers\Products;

class ProductsCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testGetRandProduct(FunctionalTester $I)
    {
        $I->wantTo('test that we get a random product');

        $this->insertFixtureGroup($I, 'random_products');

        /** @var Products $productManager */
        $productManager = $this->app['products.manager'];

        $product = $productManager->getRandProduct();

        $this->debug($product);

        $I->assertTrue($product instanceof Product);
    }
}
