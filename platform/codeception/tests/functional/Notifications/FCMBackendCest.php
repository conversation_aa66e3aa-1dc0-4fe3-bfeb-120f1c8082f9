<?php

namespace SF\functional\Notifications;

use Guz<PERSON><PERSON>ttp\Client;
use SF\FunctionalTester;
use Guz<PERSON><PERSON>ttp\HandlerStack;
use Guzzle<PERSON>ttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use SF\functional\BaseFunctional;
use GuzzleHttp\Handler\MockHandler;
use Salesfloor\Services\PushNotifications\SnsConfig;
use Salesfloor\Services\PushNotifications\FCMBackend;
use GuzzleHttp\Exception\RequestException;

class FCMBackendCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testCreatePublishObject(FunctionalTester $I)
    {
        $configs = $this->app['configs'];
        $pushNotificationConfig = new SnsConfig($configs['env'], $configs['retailer.short_name']);

        /** @var FCMBackend $backend */
        $backend = new FCMBackend(
            $configs['fcm.project_id'],
            $configs['fcm.key'],
            $configs['fcm.key.secret_file'],
            $configs['fcm.version'],
            $configs['fcm.verification.url'],
            $pushNotificationConfig,
            $this->app['logger'],
            $this->app['devices.manager'],
        );

        $locale = 'ja_JP';
        $payload = $this->getPayload();
        $accumulator = [];

        $endPoints = [
            [
                'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                'token' => 'new_android_token',
                'device_token' => 'new_android_token',
                'badge' => 111,
                'store' => 100,
            ],
            [
                'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token',
                'token' => '47e1b6c1dfc270787780355c9c515ac9d33f64cf1a4ef7ecde7f739c1a6d343b',
                'device_token' => 'new_apple_token',
                'badge' => 111,
                'store' => 100,
            ],
        ];

        $backend->createPublishObject($locale, $payload, $endPoints, $accumulator);
        $I->assertEquals($this->expectedAccumulator(), $accumulator);
    }

    /** @group database_transaction */
    public function testCreatePublishObjectForChat(FunctionalTester $I)
    {
        $configs = $this->app['configs'];
        $pushNotificationConfig = new SnsConfig($configs['env'], $configs['retailer.short_name']);

        /** @var FCMBackend $backend */
        $backend = new FCMBackend(
            $configs['fcm.project_id'],
            $configs['fcm.key'],
            $configs['fcm.key.secret_file'],
            $configs['fcm.version'],
            $configs['fcm.verification.url'],
            $pushNotificationConfig,
            $this->app['logger'],
            $this->app['devices.manager'],
        );

        $locale = 'ja_JP';
        $payload = $this->getPayload();
        $payload['GCM']['data']['event_action'] = 'new_chat';
        $payload['GCM']['options']['realtime'] = true;
        $payload['APNS']['aps']['event_action'] = 'new_chat';
        $payload['APNS']['options'] = $payload['GCM']['options'];
        $accumulator = [];

        $endPoints = [
            [
                'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                'token' => 'new_android_token',
                'device_token' => 'new_android_token',
                'badge' => 111,
                'store' => 100,
            ],
            [
                'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token',
                'token' => '47e1b6c1dfc270787780355c9c515ac9d33f64cf1a4ef7ecde7f739c1a6d343b',
                'device_token' => 'new_apple_token',
                'badge' => 111,
                'store' => 100,
            ],
        ];

        $backend->createPublishObject($locale, $payload, $endPoints, $accumulator);
        $I->assertEquals($this->expectedAccumulator(true), $accumulator);
    }

    /**
     * Test 404 for FCM push notifications
     * @param \SF\FunctionalTester $I
     * @return void
     *
     * @group database_transaction
     */
    public function testFCM404ErrorHandling(FunctionalTester $I)
    {
        $configs = $this->app['configs'];
        $pushNotificationConfig = new SnsConfig($configs['env'], $configs['retailer.short_name']);

        /** @var FCMBackend $backend */
        $backend = new FCMBackend(
            $configs['fcm.project_id'],
            $configs['fcm.key'],
            $configs['fcm.key.secret_file'],
            $configs['fcm.version'],
            $configs['fcm.verification.url'],
            $pushNotificationConfig,
            $this->app['logger'],
            $this->app['devices.manager'],
        );

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ABC',
        ];

        $messagesByBadges = [
            'fr_X' => [
                'messageMultiDevices' => '',
                'android' => [
                    'message' => 'test message',
                    'endpoints' => [
                        [
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test01',
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test01',
                            'data' => [],
                            'android' => []
                        ]
                    ]
                ],
            ],
        ];

        $mock = new MockHandler([
            new Response(404, $headers, json_encode(['message' => 'Requested entity was not found.']))
        ]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);
        $backend->setClient($client);
        $results = $backend->publishBatches($messagesByBadges);
        // The response will be 404 which is compared to 2nd parameter for $results
        $I->assertEquals([[], ['eLGUFEa7R-4IQhRg57Kpe-test01']], $results);
    }

    /**
     * Test last_updated field for FCM push notifications
     * @param \SF\FunctionalTester $I
     * @return void
     *
     * @group database_transaction
     */
    public function testFCMLastUpdatedOnSuccess(FunctionalTester $I)
    {
        $configs = $this->app['configs'];
        $pushNotificationConfig = new SnsConfig($configs['env'], $configs['retailer.short_name']);

        /** @var FCMBackend $backend */
        $backend = new FCMBackend(
            $configs['fcm.project_id'],
            $configs['fcm.key'],
            $configs['fcm.key.secret_file'],
            $configs['fcm.version'],
            $configs['fcm.verification.url'],
            $pushNotificationConfig,
            $this->app['logger'],
            $this->app['devices.manager'],
        );

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ABC',
        ];

        $messagesByBadges = [
            'generic_30' => [ // 30 messages notification
                'messageMultiDevices' => '',
                'android' => [
                    'message' => 'test message',
                    'endpoints' => [
                        [
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test01',
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test01',
                            'data' => [],
                            'android' => []
                        ],
                        [
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test02',
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test02',
                            'data' => [],
                            'android' => []
                        ],
                        [
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test03',
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test03',
                            'data' => [],
                            'android' => []
                        ],


                    ]
                ],
            ],
            'generic_55' => [
                'messageMultiDevices' => '',
                'apns' => [
                    'message' => 'test message',
                    'endpoints' => [
                        [
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test04',
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test04',
                            'data' => [],
                            'android' => []
                        ],
                        [
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test05',
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test05',
                            'data' => [],
                            'android' => []
                        ],
                        [
                            'token' => 'eLGUFEa7R-4IQhRg57Kpe-test06',
                            'device_token' => 'eLGUFEa7R-4IQhRg57Kpe-test06',
                            'data' => [],
                            'android' => []
                        ],


                    ]
                ],
            ],

        ];

        $mock = new MockHandler([
            new Response(204, $headers, json_encode(['message' => 'Success test01'])),
            new Response(404, $headers, json_encode(['message' => 'Failed test02'])), // Fails
            new Response(204, $headers, json_encode(['message' => 'Success test03'])),
            new Response(204, $headers, json_encode(['message' => 'Success test04'])),
            new Response(404, $headers, json_encode(['message' => 'Failed test05'])), // Fails
            new Response(204, $headers, json_encode(['message' => 'Success test06'])),

        ]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);
        $backend->setClient($client);
        $results = $backend->publishBatches($messagesByBadges);
        // The response will be 204 which is compared to 1st parameter for $results
        $I->assertEquals([
            'eLGUFEa7R-4IQhRg57Kpe-test01',
            'eLGUFEa7R-4IQhRg57Kpe-test03',
            'eLGUFEa7R-4IQhRg57Kpe-test04',
            'eLGUFEa7R-4IQhRg57Kpe-test06',
        ], $results[0]);
    }


    /** @group database_transaction */
    public function testCreatePublishObjectGroupsMessagesByStore(FunctionalTester $I)
    {
        $configs = $this->app['configs'];
        $pushNotificationConfig = new SnsConfig($configs['env'], $configs['retailer.short_name']);

        $backend = new FCMBackend(
            $configs['fcm.project_id'],
            $configs['fcm.key'],
            $configs['fcm.key.secret_file'],
            $configs['fcm.version'],
            $configs['fcm.verification.url'],
            $pushNotificationConfig,
            $this->app['logger'],
            $this->app['devices.manager'],
        );

        $locale = 'en_US';

        $endPoints = [
            [
                'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token_2',
                'token' => '45e1b6c1dfc270787780355c9c515ac9d33f64cf1a4ef7ecde7f749c1a6d343b',
                'device_token' => 'new_apple_token_2',
                'badge' => 111,
                'store' => 200,
            ],
            [
                'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token_3',
                'token' => '54f3b6c1dfc270787780355c9c515ac9d33f64cf1a4ef7ecde7f749c1a6d343b',
                'device_token' => 'new_apple_token_3',
                'badge' => 111,
                'store' => 200,
            ],
            [
                'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token',
                'token' => '47e1b6c1dfc270787780355c9c515ac9d33f64cf1a4ef7ecde7f739c1a6d343b',
                'device_token' => 'new_apple_token',
                'badge' => 111,
                'store' => 100
            ],
            [
                'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                'token' => 'new_android_token',
                'device_token' => 'new_android_token',
                'badge' => 111,
                'store' => 100,
            ],
        ];
        $data = [
            'alert' => 'this is payback 200',
            'title' => 'Salesfloor',
            'vibrate' => 'true',
            'message' => 'this is payback 200',
            'event_action' => 'new_chat',
            'request_id' => 'contact_me_19',
            'alertBox' => 'false',
            'chat_handoff' => '0',
            'sound' => 'default',
            'badge' => '111'
        ];
        $data2 = $data;
        $data2['alert'] = 'this is payback 100';
        $data2['message'] = $data2['alert'];
        $gcmSound = ['soundname' => 'default'];
        $gcmData = array_merge($data, $gcmSound);
        unset($gcmData['sound']);
        $gcmData2 = array_merge($data2, $gcmSound);
        unset($gcmData2['sound']);
        $outcome = [
            'en_US_111_200' => [
                'messageMultiDevices' => [
                    'default' => "",
                    'GCM' => [
                        'options' => ['realtime' => true],
                        'data' => $gcmData,
                    ],
                    'APNS' => [
                        'options' => ['realtime' => true],
                        'aps' => $data,
                    ],
                    'APNS_SANDBOX' => [
                        'options' => ['realtime' => true],
                        'aps' => $data,
                    ],

                ],
                'apns' => [

                    'data' => $data,
                    'payload' => [
                        'headers' => [
                            'apns-priority' => '10',
                            'apns-expiration' => '0',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $data['title'],
                                    'body' => $data['message'],
                                ],
                                'badge' => $data['badge'],
                                'sound' => $data['sound'],
                                'interruption-level' => FCMBackend::APPLE_INTERRUPTION_LEVEL_TIME_SENSITIVE,
                            ],
                        ],
                    ],
                    'endpoints' => array_slice($endPoints, 0, 2)
                ],

            ],
            'en_US_111_100' => [
                'messageMultiDevices' => [
                    'default' => "",
                    'GCM' => [
                        'options' => ['realtime' => true],
                        'data' => $gcmData2,
                    ],
                    'APNS' => [
                        'options' => ['realtime' => true],
                        'aps' => $data2,
                    ],
                    'APNS_SANDBOX' => [
                        'options' => ['realtime' => true],
                        'aps' => $data2,
                    ],

                ],
                'apns' => [
                    'data' => $data2,
                    'payload' => [
                        'headers' => [
                            'apns-priority' => '10',
                            'apns-expiration' => '0',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $data2['title'],
                                    'body' => $data2['message'],
                                ],
                                'badge' => $data2['badge'],
                                'sound' => $data2['sound'],
                                'interruption-level' => FCMBackend::APPLE_INTERRUPTION_LEVEL_TIME_SENSITIVE,
                            ],
                        ],
                    ],
                    'endpoints' => array_slice($endPoints, 2, 1)
                ],
                'android' => [
                    'data' => $gcmData2,
                    'payload' => [
                        'ttl' => '0s',
                        'priority' => FCMBackend::ANDROID_PRIORITY_HIGH,
                        'notification' => [
                            'title' => $gcmData2['title'],
                            'body' => $gcmData2['message'],
                            'notification_count' => (int)$gcmData2['badge'],
                            'sound' => $gcmData2['soundname'],
                            'icon' => 'ic_notify',
                            'click_action' => 'com.adobe.phonegap.push.background.MESSAGING_EVENT',
                            'channel_id' => null,
                        ],
                    ],
                    'endpoints' => array_slice($endPoints, 3)
                ],

            ],
        ];
        $accumulator = [];

        foreach ($endPoints as $endPoint) {
            $store = $endPoint['store'];
            $payload = [
                'APNS' => [
                    'options' => ['realtime' => true],
                    'aps' => [
                        'alert' => "this is payback $store",
                        'title' => 'Salesfloor',
                        'vibrate' => 'true',
                        'message' => "this is payback $store",
                        'event_action' => 'new_chat',
                        'request_id' => 'contact_me_19',
                        'alertBox' => 'false',
                        'chat_handoff' => '0',
                        'sound' => 'default',
                    ],
                ],
                'GCM' => [
                    'options' => ['realtime' => true],
                    'data' => [
                        'alert' => "this is payback $store",
                        'title' => 'Salesfloor',
                        'vibrate' => 'true',
                        'message' => "this is payback $store",
                        'event_action' => 'new_chat',
                        'request_id' => 'contact_me_19',
                        'alertBox' => 'false',
                        'chat_handoff' => '0',
                        'soundname' => 'default',
                    ],
                ],
            ];

            $backend->createPublishObject($locale, $payload, [$endPoint], $accumulator);
        }

        $I->assertEquals($outcome, $accumulator);
    }

    protected function getPayload(): array
    {
        return [
            'GCM' => [
                'data' => [
                    'alert' => '新しいお問い合わせのリクエストがあります',
                    'title' => 'Salesfloor',
                    'vibrate' => 'true',
                    'message' => '新しいお問い合わせのリクエストがあります',
                    'event_action' => 'new_request',
                    'request_id' => 'contact_me_19',
                    'alertBox' => 'false',
                    'chat_handoff' => 0,
                    'soundname' => 'default',
                ],
                'priority' => 'high',
            ],
            'APNS' => [
                'aps' => [
                    'alert' => '新しいお問い合わせのリクエストがあります',
                    'title' => 'Salesfloor',
                    'vibrate' => 'true',
                    'message' => '新しいお問い合わせのリクエストがあります',
                    'event_action' => 'new_request',
                    'request_id' => 'contact_me_19',
                    'alertBox' => 'false',
                    'chat_handoff' => 0,
                    'sound' => 'default',
                ],
            ],
            'APNS_SANDBOX' => [
                'aps' => [
                    'alert' => '新しいお問い合わせのリクエストがあります',
                    'title' => 'Salesfloor',
                    'vibrate' => 'true',
                    'message' => '新しいお問い合わせのリクエストがあります',
                    'event_action' => 'new_request',
                    'request_id' => 'contact_me_19',
                    'alertBox' => 'false',
                    'chat_handoff' => 0,
                    'sound' => 'default',
                ],
            ],
        ];
    }

    protected function expectedAccumulator($newChat = false): array
    {
        $data = [
            'alert' => '新しいお問い合わせのリクエストがあります',
            'title' => 'Salesfloor',
            'vibrate' => 'true',
            'message' => '新しいお問い合わせのリクエストがあります',
            'event_action' => $newChat ? 'new_chat' : 'new_request',
            'request_id' => 'contact_me_19',
            'alertBox' => 'false',
            'chat_handoff' => '0',
            'soundname' => 'default',
            'badge' => '111',
        ];
        $apnData = $data;
        $apnData['sound'] = 'default';
        unset($apnData['soundname']);
        $return = [
            'ja_JP_111_100' => [
                'messageMultiDevices' => [
                    'default' => '',
                    'GCM' => [
                        'data' => $data,
                        'priority' => 'high',
                    ],
                    'APNS' => [
                        'aps' => $apnData
                    ],
                    'APNS_SANDBOX' => [
                        'aps' => $apnData
                    ],
                ],
                'android' => [
                    'data' => $data,
                    'payload' => [
                        'priority' => FCMBackend::ANDROID_PRIORITY_HIGH,
                        'notification' => [
                            'title' => $data['title'],
                            'body' => $data['message'],
                            'notification_count' => (int)$data['badge'],
                            'sound' => $data['soundname'],
                            'icon' => 'ic_notify',
                            'click_action' => 'com.adobe.phonegap.push.background.MESSAGING_EVENT',
                            'channel_id' => null,
                        ],
                    ],
                    'endpoints' => [
                        0 => [
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                            'token' => 'new_android_token',
                            'device_token' => 'new_android_token',
                            'badge' => 111,
                            'store' => 100,
                        ],
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'headers' => [
                            'apns-priority' => '10',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $apnData['title'],
                                    'body' => $apnData['message'],
                                ],
                                'badge' => 111,
                                'sound' => 'default',
                                'interruption-level' => FCMBackend::APPLE_INTERRUPTION_LEVEL_TIME_SENSITIVE
                            ],
                        ],
                    ],
                    'data' => $apnData,
                    'endpoints' => [
                        0 => [
                            'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token',
                            'token' => '47e1b6c1dfc270787780355c9c515ac9d33f64cf1a4ef7ecde7f739c1a6d343b',
                            'device_token' => 'new_apple_token',
                            'badge' => 111,
                            'store' => 100,
                        ],
                    ],
                ],
            ],
        ];

        if ($newChat) {
            $return['ja_JP_111_100']['messageMultiDevices']['GCM']['options']['realtime'] = true;
            $return['ja_JP_111_100']['messageMultiDevices']['APNS']['options']['realtime'] = true;
            $return['ja_JP_111_100']['messageMultiDevices']['APNS_SANDBOX']['options']['realtime'] = true;
            $return['ja_JP_111_100']['android']['payload']['ttl'] = '0s';
            $return['ja_JP_111_100']['apns']['payload']['headers']['apns-expiration'] = '0';
        }

        return $return;
    }
}
