<?php

namespace SF\functional\Notifications;

use Codeception\Util\Stub;
use Salesfloor\Services\Notifications\NotificationLateMessages;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\PushNotifications\Service as PushNotificationService;

class LateMessagesCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testLateMessagesNotificationReps(FunctionalTester $I)
    {
        $I->wantTo('Test that we are retrieving the right events for late messages');

        $this->insertFixtureGroup($I, 'late_messages_reps');

        /** @var NotificationLateMessages $notificationService */
        $notificationService = $this->app['notifications.late_messages'];

        $this->app['configs']['notifications.late_messages.enabled'] = true;

        $events = $notificationService->getEvents();

        $I->assertEquals(1, count($events));
    }

    /** @group database_transaction */
    public function testLateMessagesNotificationStore(FunctionalTester $I)
    {
        $I->wantTo('Test that we are retrieving the right events for late messages');

        $this->insertFixtureGroup($I, 'late_messages_store');

        /** @var NotificationLateMessages $notificationService */
        $notificationService = $this->app['notifications.late_messages'];

        $this->app['configs']['notifications.late_messages.enabled'] = true;

        $events = $notificationService->getEvents();

        $I->assertEquals(1, count($events));
    }

    /**
     * This test is for push and email testing, should be skipped during automation
     *
     * /!\ For this test to work you need to replace the token and endpoint in the fixture, you should then receive
     * the notification directly on your device
     *
     * @param FunctionalTester $I
     *
     * @group database_transaction
     * @skip
     */
    public function testLateMessagesNotificationRepsSkipped(FunctionalTester $I)
    {
        $I->wantTo('Test that we are sending the notifications for late message');

        $this->insertFixtureGroup($I, 'late_messages_reps');
        $this->insertFixtureGroup($I, 'device');

        /** @var NotificationLateMessages $notificationService */
        $notificationService = $this->app['notifications.late_messages'];

        $this->app['configs']['notifications.late_messages.enabled'] = true;

        $events = $notificationService->getEvents();

        $this->debug($events);

        $I->assertEquals(1, count($events));

        $I->wantTo('Test that we are sending the notifications');

        $notificationService->process();
    }

    /**
     * This test is for push and email testing, should be skipped during automation
     *
     * /!\ For this test to work you need to replace the token and endpoint in the fixture, you should then receive
     * the notification directly on your device
     *
     * @param FunctionalTester $I
     *
     * @group database_transaction
     * @skip
     */
    public function testLateMessagesNotificationStoreSkipped(FunctionalTester $I)
    {
        $I->wantTo('Test that we are sending the notifications for late message');

        $this->insertFixtureGroup($I, 'late_messages_store');
        $this->insertFixtureGroup($I, 'device');

        /** @var NotificationLateMessages $notificationService */
        $notificationService = $this->app['notifications.late_messages'];

        $this->app['configs']['notifications.late_messages.enabled'] = true;

        $events = $notificationService->getEvents();

        $this->debug($events);

        $I->assertEquals(1, count($events));

        $I->wantTo('Test that we are sending the notifications');

        $notificationService->process();
    }

    /**
     * Test autoload parameter is set as false in wp_options table for late message
     *
     * @param FunctionalTester $I
     * @throws \Exception
     *
     * @group database_transaction
     * @skip
     */
    public function testUpdateAutoloadAsNoInWpOptionsTable(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'late_messages_reps');
        $this->insertFixtureGroup($I, 'device');

        /** @var NotificationLateMessages $notificationService */
        $notificationService = $this->mockPushNotificationService();

        $this->app['configs']['notifications.late_messages.enabled'] = true;

        $events = $notificationService->getEvents();

        $this->debug($events);

        $I->assertEquals(1, count($events));

        $notificationService->process();

        $I->seeInDatabase('wp_options', [
            'option_name' => 'sf|late_message_notification|34',
            'autoload' => 'no'
        ]);
    }

    /**
     * Mock the push notification service in lat message notification service
     *
     * @throws \Exception
     */
    private function mockPushNotificationService()
    {
        $pushService = Stub::makeEmpty(
            PushNotificationService::class,
            [
                'publishToReps' => function () {
                    return true;
                }
            ]
        );

        $this->app['service.push'] = $pushService;

        $notificationService = $this->app['notifications.late_messages'];

        return $notificationService;
    }
}
