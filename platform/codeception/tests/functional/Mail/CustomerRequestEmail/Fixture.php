<?php

namespace SF\functional\Mail\CustomerRequestEmail;

use Codeception\Util\Fixtures;

class Fixture
{
    public function addNearAppointments()
    {
        Fixtures::add('near_appointments', [
            'sf_store'        => [
                [
                    'store_id'          => 1004,
                    'name'              => 'Store',
                    'timezone'          => 'America/New_York',
                    'sf_identifier'     => 'store',
                    'retailer_store_id' => '515151',
                    'store_user_id'     => null,
                    'address'           => '651 Notre-Dame Street West',
                    'city'              => 'Montreal',
                    'region'            => 'QC',
                    'postal'            => 'H7D 9K3',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_CA',
                    'store_id'   => '1004',
                    'is_default' => 1,
                ],
            ],
            'sf_appointments' => [
                [
                    'ID'                  => 40,
                    'customer_id'         => 77,
                    'user_id'             => 33,
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                    // appointment is used to test getVariables only(not nearAppointment notification), so we could had code date
                    'date'                => date('2020-09-10 00:00:00'),
                    'status'              => 'accepted',
                    'location'            => '',
                    'notes'               => 'sfsfsfsdf',
                    'uniq_id'             => 'SFID5c0e983066d3f8.49953381',
                    'timezone'            => 'America/New_York',
                    'creation'            => '2018-12-10 16:45:36',
                    'enddate'             => '2018-12-31 06:00:00',
                    'phone'               => '',
                    'store_id'            => 1004,
                    'source_url'          => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
                    'source_title'        => 'Shop with Sally Sellers - Chico\'s',
                    'locale'              => 'en_US',
                    'channel'             => 'email',
                    'status_notification' => null,
                    'source'              => 'storefront',
                ],
            ],
            'sf_customer'     => [
                [
                    'ID'                   => 77,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'Test Customer',
                    'phone'                => '+15148127107',
                    'localization'         => 'en',
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'Test Customer',
                    'last_name'            => 'Lastname',
                    'created'              => '2019-02-06 15:55:43',
                    'last_modified'        => '2019-02-06 15:55:43',
                    'type'                 => 'corporate',
                    'retailer_customer_id' => '3009301582',
                    'label_email'          => 'home',
                    'label_phone'          => 'home',
                    'origin'               => null,
                    'locale'               => 'en_US',
                    'entity_last_modified' => '2019-02-06 15:55:49',
                ],
                [
                    'ID'                   => 78,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'name'                 => 'Test Customer',
                    'phone'                => '+15148127107',
                    'localization'         => 'en',
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'first_name'           => 'Test Customer',
                    'last_name'            => 'Lastname',
                    'created'              => '2019-02-06 15:55:43',
                    'last_modified'        => '2019-02-06 15:55:43',
                    'type'                 => 'corporate',
                    'retailer_customer_id' => '3009301583',
                    'label_email'          => 'home',
                    'label_phone'          => 'home',
                    'origin'               => null,
                    'locale'               => 'en_US',
                    'entity_last_modified' => '2019-02-06 15:55:49',
                ],
            ],
            'wp_users'        => [
                [ // Store
                    'ID'                  => 33,
                    'user_login'          => 'store_test',
                    'user_pass'           => '<EMAIL>',
                    'user_nicename'       => 'store_test',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2016-08-25 18:58:15',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'store_test',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1004,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'employee_id'         => '151515',
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null
                ],
                [ // Rep
                    'ID'                  => 34,
                    'user_login'          => 'rep_test2',
                    'user_pass'           => '<EMAIL>',
                    'user_nicename'       => 'rep_test2',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2016-08-25 18:58:15',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'rep_test2',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1004,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => '151515',
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null
                ],
            ],
            'wp_usermeta'     => [
                [
                    'user_id'    => 33,
                    'meta_key'   => 'mail_notifications_salesfloor',
                    'meta_value' => '1',
                ],
                [
                    'user_id'    => 33,
                    'meta_key'   => 'first_name',
                    'meta_value' => 'Store',
                ],
                [
                    'user_id'    => 33,
                    'meta_key'   => 'last_name',
                    'meta_value' => 'REP',
                ],
                [
                    'user_id'    => 34,
                    'meta_key'   => 'mail_notifications_salesfloor',
                    'meta_value' => '1',
                ],
                [
                    'user_id'    => 34,
                    'meta_key'   => 'first_name',
                    'meta_value' => 'Rep Rep',
                ],
                [
                    'user_id'    => 34,
                    'meta_key'   => 'last_name',
                    'meta_value' => 'REP',
                ],
            ],
        ]);
    }
}
