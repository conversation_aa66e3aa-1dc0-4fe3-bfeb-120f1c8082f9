<?php

namespace SF\functional\Mail\CustomerRequestEmail;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Mail\Templates\Variables\VariablesInterface;

/**
 * Functional/Unit test for CustomerRequestEmailTemplate

 *
 * @return array
 */
class AppointmentEmailCest extends BaseFunctional
{
    /**
     * The initial expected value extract from runtime, it might be incorrect.
     * We use it by purpose of regression test if any change/refactoring in the future
     * @param FunctionalTester $I
     * @skip
     * @group database_transaction
     */
    public function testTemplateGlobalVariablesGetVariables(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'near_appointments');

        $context = $this->getMockUpContext();

        $template = new \Salesfloor\Services\Mail\Templates\CustomerRequestEmailTemplate();
        $template->load($this->app);
        $template->setContext($context);

        /** @var VariablesInterface $varService */
        $varService = $this->app['sf.mail.variables.global'];

        $actual = $varService->getVariables($context);

        $expected           = $this->getExpectedValueGlobalVariables();
        $expected['AVATAR'] = substr($expected['AVATAR'], strrpos($expected['AVATAR'], '/'));
        $actual['AVATAR']   = substr($actual['AVATAR'], strrpos($actual['AVATAR'], '/'));
        $I->assertEquals($expected, $actual);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @throws \ReflectionException
     * @skip
     * @group database_transaction
     */
    public function testTemplateGetVariables(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'near_appointments');

        $context = $this->getMockUpContext();

        $template = new \Salesfloor\Services\Mail\Templates\CustomerRequestEmailTemplate();
        $template->load($this->app);
        $template->setContext($context);

        $reflection = new \ReflectionClass($template);

        $getVariablesMethod = $reflection->getMethod('getVariables');
        $getVariablesMethod->setAccessible(true);
        $actual = $getVariablesMethod->invoke($template, $context);

        $expected = $this->getExpectedValueTemplateGetVariables();

        $I->assertEquals($expected, $actual);
    }

    private function getMockUpContext()
    {
        $context = [
            'userId'           => 34,
            'emailType'        => \Salesfloor\Services\Mail\Templates\CustomerRequestEmailTemplate::TEMPLATE_REMIND_1H_TO_CUST,
            'eventId'          => 40,
            'type'             => 'appointment',
            //only for test
            'retailerTemplate' => 'saks',
            'virtualId' => 'virtual_appointment_id',
        ];

        return $context;
    }

    private function getExpectedValueGlobalVariables()
    {
        $isAppLoginPrimary = $this->app['configs']['retailer.backoffice_primary_login_through_app.enabled'];
        if ($isAppLoginPrimary) {
            $urlMapping = [
                'BACKOFFICEPAGE'    => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fapp&%24deeplink_path=%2Fapp%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
                'EMAIL_LINK'        => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fapp&%24deeplink_path=%2Fapp%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
                'NOTIFICATIONS_URL' => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fapp&%24deeplink_path=%2Fapp%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
            ];
        } else {
            $urlMapping = [
                'BACKOFFICEPAGE'    => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&%24deeplink_path=%2F%3FretailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
                'EMAIL_LINK'        => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice%2Fstore-request-center%23%2Fmessages%2Fcompose%3Ftarget%3D{CUSTOMER_EMAIL_ENC}&%24deeplink_path=%2Fstore-messages%2Fcompose%3Femail%3D{CUSTOMER_EMAIL_ENC}%26retailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
                'NOTIFICATIONS_URL' => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice%2Fstore-request-center%23%2FstoreRequests%2Funresolved&%24deeplink_path=%2Fstore-requests%3FretailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
            ];
        }

        $expected = [
            'ACTION'                  => '',
            'APPOINTMENT_LABEL'       => 'Appointment Request',
            'APPOINTMENT_LINK'        => 'https://tests.dev.salesfloor.net/en_US/rep_test2?appointment=1',
            'ASK_LINK'                => 'https://tests.dev.salesfloor.net/en_US/rep_test2?ask=1',
            'AVATAR'                  => 'https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_5.jpg,g_face,h_250,w_250/v1562862994/dev/tests/rep_test2',
            'BACKOFFICEPAGE'          => $urlMapping['BACKOFFICEPAGE'],
            'CHAT_LABEL'              => 'Live Chat',
            'CONTACT_US_EMAIL'        => '',
            'CURRENT_YEAR'            => date('Y'),
            'CUSTOMER_NAME'           => 'a customer',
            'CUSTOMER_PHONE'          => '',
            'DESCRIPTION'             => 'MISSING DESCRIPTION',
            'EMAIL'                   => '<EMAIL>',
            'EMAIL_LINK'              => [
                'string'       => $urlMapping['EMAIL_LINK'],
                'dependencies' => [
                    'CUSTOMER_EMAIL_ENC',
                    'CUSTOMER_EMAIL_ENCRYPTED'
                ],
            ],
            'FINDER_LABEL'            => 'Personal Shopper',
            'HAPPY_CUSTOMER_LINK'     => [
                'format'       => 'https://tests.dev.salesfloor.net/en_US/rep_test2?feedback=1&magicid=%s&email=%s',
                'placeholders' => [
                     'EVENT_MAGIC_ID',
                     'CUSTOMER_EMAIL_ENC',
                     'CUSTOMER_EMAIL_ENCRYPTED'
                ],
            ],
            'HOMEURL'                 => 'https://tests.dev.salesfloor.net/en_US',
            'HOST'                    => 'www.salesfloor.net',
            'LIVE_SERVICE_LINK'       => 'https://tests.dev.salesfloor.net/en_US/rep_test2?live_service=1',
            'MAILCHIMP_ADDRESS_FIELD' => '[CLIENTS.ADDRESS]',
            'NOTIFICATIONS_URL'       => $urlMapping['NOTIFICATIONS_URL'],
            'PHONE'                   => '',
            'POSTCARD'                => [
                'format'       => 'https://tests.dev.salesfloor.net/r/sf-postcard.php?repname=rep_test2&sku=%s',
                'placeholders' => [
                    'PRODUCT_SKU',
                ],
            ],
            'POWEREDBY'               => 'https://tests.dev.salesfloor.net/img/salesfloor_poweredby.png',
            'PREVIEWTEXT'             => '',
            'QUESTION_LABEL'          => 'Ask & Answer',
            'REPORT_CONCERN_LINK'     => 'https://tests.dev.salesfloor.net/en_US/rep_test2?report=1',
            'REPPAGE'                 => 'https://tests.dev.salesfloor.net/en_US/rep_test2',
            'REPTITLE'                => '@@retailerName@@',
            'RETAILER'                => '@@retailerName@@',
            'RETAILERLOGO'            => 'https://tests.dev.salesfloor.net/img/retailers/tests/tests.png',
            'RETAILERLOGOSMALL'       => 'https://tests.dev.salesfloor.net/img/retailers/tests/tests_p.png',
            'RETAILER_BRAND_NAME'     => '@@retailerName@@',
            'RETAILER_IDSTR'          => 'tests',
            'RETAILER_PRETTY_NAME'    => '@@retailerName@@',
            'RETAILERURL'             => 'https://tests.dev.salesfloor.net/shop?rep=rep_test2&sf_url=http%3A%2F%2Fwww.salesfloor.net',
            'SHOPPAGE'                => 'https://tests.dev.salesfloor.net/shop?rep=rep_test2&sf_url=',
            'SHOPPER_LINK'            => 'https://tests.dev.salesfloor.net/en_US/rep_test2?shopper=1',
            'STORE'                   => 'Montreal, QC',
            'STORE_ADDRESS'           => 'Store: Store, 651 Notre-Dame Street West, Montreal QC H7D 9K3',
            'STORE_NAME'              => 'Store',
            'UNSUBSCRIBE_LINK'        => '[GLOBAL_UNSUBSCRIBE]',
            'URL'                     => 'https://tests.dev.salesfloor.net/en_US/rep_test2',
            'USER'                    => 'Rep Rep REP',
            'USERLOGIN'               => 'rep_test2',
            'USER_FIRST_NAME'         => 'Rep Rep',
            'STORE_LOCATION_STREET'   => '',
            'STORE_LOCATION_CITY'     => 'Montreal',
            'STORE_LOCATION_REGION'   => 'QC',
            'UNSUBSCRIBE_EXTERNAL_LINK' => [
                'string' => null,
                'dependencies' => [
                    'CUSTOMER_EMAIL_ENC',
                    'CUSTOMER_EMAIL_ENCRYPTED',
                ]
            ],
            'JOIN_MEETING_LINK' => 'https://tests.dev.salesfloor.net/en_US/virtual-appointment/virtual_appointment_id'
        ];

        return $expected;
    }

    private function getExpectedValueTemplateGetVariables()
    {
        $expected = [
            'USER'                  => 'Store REP',
            'REPTITLE'              => '@@retailerName@@',  // We use retailer.name language variable if missing
            'CUSTOMER_NAME'         => 'Test Customer Lastname',
            'CUSTOMER_EMAIL'        => '<EMAIL>',
            'CUSTOMER_EMAIL_ENC'    => 'pierre-julien%2Btest%40salesfloor.net',
            'CUSTOMER_EMAIL_ENCRYPTED' => '<EMAIL>',
            'EVENT_TYPE'            => 'Chat',
            'IS_LAUNCH_CHAT'        => true,
            'IS_YOUR_PHONE'         => false,
            'IS_JOIN_MEETING'       => false,
            'IS_APPOINTMENT'        => true,
            'IS_FINDER'             => false,
            'IS_QUESTION'           => false,
            'PHONE_TITLE'           => '',
            'CUSTOMER_PHONE'        => '',
            'STORE_LABEL'           => 'Location',
            'STORE_NAME'            => 'Store',
            'STORE_ADDR'            => '651 Notre-Dame Street West, Montreal',
            'STORE_POST'            => 'H7D 9K3',
            'MEETING_NOTE'          => '',
            'MEETING_TIME'          => 'Wednesday, September 9, 2020 at 8:00 PM',
            'MEETING_DATE'          => 'Wednesday, September 9, 2020 at 8:00 PM',
            'USERLOGIN'             => 'store_test',
            'USER_PHONE'            => '',
            'USER_PHONE_TITLE'      => '',
            'MEETING_NOTE_FROM_REP' => '',
            'CATEGORY'              => '',
            'SUBCATEGORY'           => '',
            'BUDGET_TITLE'          => '',
            'BUDGET'                => '',
            'QUESTION'              => '',
            'ADDITIONALINFOTITLE'   => '',
            'EVENT_MAGIC_ID'        => 'SFID5c0e983066d3f8.49953381',
            'SOURCE_URL'            => 'https://chicos.dev.salesfloor.net/lol56eff3d6ed8b9?fromBackoffice=1',
            'SOURCE_TITLE'          => 'Shop with Sally Sellers - Chico\'s',
            'SERVICE_VERSION'       => 2,
            'VIEW_REQUEST_LINK'     => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice%2Fstore-request-center%23%2FstoreRequests%2Funresolved%2Fthread%2Fbook_appointment_40&%24deeplink_path=%2Fstore-requests%2Fid%2Fbook_appointment_40%3FretailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
            'REPLY_REQUEST_LINK'    => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice%2Fstore-request-center%23%2FstoreRequests%2Funresolved%2Fthread%2Fbook_appointment_40&%24deeplink_path=%2Fstore-requests%2Fid%2Fbook_appointment_40%3FretailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
            'ACCEPT_LINK'           => 'https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5c0e983066d3f8.49953381&meetingid=40&sf_locale=en_US&version=2&action=accept&customer=true&rep=store_test',
            'RESCHEDULE_LINK'       => 'https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5c0e983066d3f8.49953381&meetingid=40&sf_locale=en_US&version=2&action=change',
            'CANCEL_LINK'           => 'https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5c0e983066d3f8.49953381&meetingid=40&sf_locale=en_US&version=2&action=cancel&rep=store_test',
            'REP_ACCEPT_LINK'       => [
                'string'       => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fsfadmin%2Fappointment.php%3Fmagicid%3DSFID5c0e983066d3f8.49953381%26meetingid%3D40%26action%3Daccept%26customer%3Dfalse%26rep%3D{USERLOGIN}%26version%3D{SERVICE_VERSION}&%24deeplink_path=%2Fstore-requests%2Fid%2Fbook_appointment_40%3FretailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
                'dependencies' => [
                    'USERLOGIN',
                    'SERVICE_VERSION',
                ],
            ],
            'REP_RESCHEDULE_LINK'   => [
                'string'       => 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fsfadmin%2Fappointment.php%3Fmagicid%3DSFID5c0e983066d3f8.49953381%26meetingid%3D40%26action%3Dchange%26version%3D{SERVICE_VERSION}&%24deeplink_path=%2Fstore-requests%2Fnew-time%2Fid%2Fbook_appointment_40%3FretailerId%3Dtests.dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg',
                'dependencies' => [ 'SERVICE_VERSION',],
            ],
            'LIVE_SERVICE_LINK'     => 'https://tests.dev.salesfloor.net/en_US/store_test?live_service=1&version=2&sf_locale=en_US',
            'EMAIL'                 => '<EMAIL>',
            'SUBJECT'               => 'Appointment Request',
            'YOUR_PHONE'            => false,
            'LAUNCH_CHAT'           => true,
            'JOIN_MEETING'          => false,
            'EVENT_DURATION'        => '60',
            'REPLY_EMAIL'           => '<EMAIL>',
            'WITH_INFO'             => 'Store, ',
            'UNSUBSCRIBE_EXTERNAL_LINK' => [
                'string' => null,
                'dependencies' => [
                    'CUSTOMER_EMAIL_ENC',
                    'CUSTOMER_EMAIL_ENCRYPTED'
                ]
            ],
            'JOIN_MEETING_LINK' => 'https://tests.dev.salesfloor.net/en_US/virtual-appointment/'
        ];

        return $expected;
    }
}
