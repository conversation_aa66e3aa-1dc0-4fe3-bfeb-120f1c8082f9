<?php

namespace SF\functional\Cache;

use Codeception\Util\Fixtures;

class Fixture
{
    private function populateLookbookData()
    {
        return [
            'sf_lookbooks' => [
                [
                    "title" => "lookbook title",
                    "user_id" => 1, // because default JWT is reggie
                    "customer_id" => 1,
                    "fragment" => "12345",
                    "locale" => "en_US"
                ]
            ],
            'sf_looks' => [
                [
                    'title' => 'title',
                    'description' => 'description',
                    'published' => 1,
                    'user_id' => 1, // Not sure why this information twice
                ]
            ],
            'sf_look_products' => [
                [
                    'look_id' => 1,
                    'position' => 0,
                    'product_id' => '54640cd49acae0386f777604ce15ab92'
                ],
                [
                    'look_id' => 1,
                    'position' => 1,
                    'product_id' => 'b2c1b175fdbbac5c5b33d2628042967c'
                ],
                [
                    'look_id' => 1,
                    'position' => 2,
                    'product_id' => '8b22196d3cb34f69ef0c5f5e06cb1c08'
                ],
                [
                    'look_id' => 1,
                    'position' => 3,
                    'product_id' => 'c5fdf5d2b068cdab85a9800b7987fb31'
                ]
            ]
        ];
    }

    public function insert()
    {
        Fixtures::add('lookbookData', $this->populateLookbookData());
    }
}
