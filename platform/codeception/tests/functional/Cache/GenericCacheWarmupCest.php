<?php

declare(strict_types=1);

namespace SF\functional\Cache;

use Salesfloor\Services\Application\Routing;
use Salesfloor\Services\Cache\Adapters\ArrayAdapter;
use Salesfloor\Services\Cache\GenericCache;
use Salesfloor\Services\Multilang;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Symfony\Component\Translation\Translator;

class GenericCacheWarmupCest extends BaseFunctional
{
    /** @var GenericCache */
    private $cache;

    /** @var string  */
    private $testFunction;

    public function _before($I)
    {
        parent::_before($I);

        // I need to force to clear it before any tests, because codeception itself load app, so cache
        // gets already populated by them and I didn't want to add a patch on boot().
        $this->getCacheService()->clear();
    }

    public function _after($I)
    {
        // Don't keep any tests file/cache after each test
        $this->getCacheService()->clear();

        parent::_after($I); // TODO: Change the autogenerated stub
    }

    /**
     * @group database_transaction
     * */
    public function testWarmupCacheFromWidgets(FunctionalTester $I)
    {
        $I->wantTo("Test - cache warm up - initialed from stack - Widgets");
        $this->testFunction = __FUNCTION__;

        // Widgets doesn't warm up on boot()
        $I->sendGet($this->app['configs']['salesfloor_widgets.host'] . '/404');
        $this->validateService($I);

        // This will make sure that $this->app['service.cache'] is reset properly ( this will boot like an API)
        $this->reloadApplication($I);

        $this->validateCache($I);
        $this->validateService($I);
    }

    /**
     * @group database_transaction
     *
     * */
    public function testWarmupCacheFromAPI(FunctionalTester $I)
    {
        $I->wantTo("Test - cache warm up - initialed from stack - API");

        // This will make sure that $this->app['service.cache'] is reset properly ( this will boot like an API)
        $this->reloadApplication($I);

        $this->validateCache($I);
        $this->validateService($I);
    }

    /**
     * @group database_transaction
     *
     * */
    public function testWarmupCacheFromWP(FunctionalTester $I)
    {
        $I->wantTo("Test - cache warm up - initialed from stack - WP");

        // WP doesn't warm up on boot()
        $I->sendGet($this->app['configs']['salesfloor_storefront.host'] . '/robots.txt');
        $this->validateService($I);

        // This will make sure that $this->app['service.cache'] is reset properly ( this will boot like an API)
        $this->reloadApplication($I);

        $this->validateCache($I);
        $this->validateService($I);
    }

    private function validateService(FunctionalTester $I)
    {
        /** @var Multilang $multilang */
        $multilang = $this->app['service.multilang'];

        /** @var Translator $translator */
        $translator = $this->getProperty($multilang, 'translator')->getValue($multilang);

        $messagesWidgetsEN = $translator->trans('meta_title', [], null, 'en_CA');
        $messagesWidgetsFR = $translator->trans('meta_title', [], null, 'fr_CA');

        $messagesAPIEN = $translator->trans('no_title', [], null, 'en_CA');
        $messagesAPIFR = $translator->trans('no_title', [], null, 'fr_CA');

        $I->assertEquals('meta title tests', $messagesWidgetsEN);
        $I->assertEquals('meta titre tests', $messagesWidgetsFR);
        $I->assertEquals('No title', $messagesAPIEN);
        $I->assertEquals('Sans titre', $messagesAPIFR);

        $paths = $this->getMethod($multilang, 'getTranslationPaths')->invoke($multilang);
        $this->validateMLPaths($I, $paths);

        // Test routing
        $routing = $this->app['sf.routing'];
        $routes = $this->getMethod($routing, 'getRouteFiles')->invoke($routing, PATH_ROUTES);
        $this->validateRoutes($I, $routes);
    }

    private function validateMLPaths(FunctionalTester $I, array $paths)
    {
        // Make sure we store snapshot in different file.
        static $index = 1;

        $jsonFile = sprintf('%s/files/%s-paths-%d.json', __DIR__, $this->testFunction, $index++);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($paths, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);

        $this->validateArrayRegExp($expected, $paths);
    }

    private function validateRoutes(FunctionalTester $I, array $routes)
    {
        static $index = 1;
        $jsonFile = sprintf('%s/files/%s-routes-%d.json', __DIR__, $this->testFunction, $index++);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($routes, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);

        $this->validateArray($expected, $routes);
    }

    private function validateCache(FunctionalTester $I)
    {
        /** @var ArrayAdapter $adapter */
        $adapter = $this->getProperty($this->getCacheService(), 'cacheAdapter')->getValue($this->getCacheService());

        switch (get_class($adapter)) {
            case 'Salesfloor\\Services\\Cache\\Adapters\\ArrayAdapter':
                $I->assertEquals('/tmp/cache-array-file', $adapter::CACHE_FILENAME);
                // $I->assertTrue(filesize($adapter::CACHE_FILENAME) > 0);
                break;
            default:
                break;
        }

        // validate the content of the warm up cache
        // routing (list of files that contains the routes)
        // translation (list of files that contains the translations)
        $translations = $adapter->get(Multilang::CACHING_TRANSLATION_PATH);
        // TODO: have to check it before validation, update it if possible.
        if ($translations !== null) {
            $this->validateMLPaths($I, $translations);
        }
        $routes = $adapter->get(Routing::CACHING_ROUTES_NAME);
        if ($routes !== null) {
            $this->validateRoutes($I, $routes);
        }
    }

    private function getCacheService()
    {
        return $this->app['service.cache'];
    }
}
