<?php

namespace SF\functional\Share;

use SF\FunctionalTester;
use SF\functional\BaseFunctional;
use Salesfloor\Services\Share\ShareEmail;
use Salesfloor\Services\Mail\AlternateTemplate\ShareAlternateTemplate;

class AlternateTemplateCest extends BaseFunctional
{
    private ShareEmail $shareEmail;
    private ShareAlternateTemplate $shareAlternateTemplate;

    public function _before($I)
    {
        parent::_before($I);

        $this->shareAlternateTemplate = $this->app['sf.mail.alternate_template.share'];
        $this->shareEmail = $this->app['service.share_email'];

        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['retailer.multiple_email_templates.layout'] = [
            'default_template' => [
                'id' => 'default_template',
                'subject' => null,
                'display_name' => 'Standard Template',
                'filename' => 'curation_mixed_variation-2',
                'products' => [
                    'min' => 0,
                    'max' => 9,
                ],
                'assets' => [
                    'min' => 0,
                    'max' => 1,
                    'or'  => array('photos')
                ],
                'photos' => [
                    'min' => 0,
                    'max' => 1,
                    'or'  => array('assets')
                ],
                'thumbnail' => 'curation_mixed_variation-2.png',
                'default' => true,
            ],
            'look_template' => [
                'id' => 'look_template',
                'subject' => null,
                'display_name' => 'Shop the Look Template',
                'filename' => 'curation_mixed_variation-3',
                'products' => [
                    'min' => 3,
                    'max' => 3,
                ],
                'photos' => [
                    'min' => 1,
                    'max' => 1,
                    'or'  => array('assets'),
                ],
                'assets' => [
                    'min' => 1,
                    'max' => 1,
                    'or'  => array('photos'),
                ],
                'thumbnail' => 'curation_mixed_variation-3.png',
                'default' => false,
            ]
        ];
    }

    /** @group database_transaction */
    public function testInitializeRequestFromScanningSku(FunctionalTester $I)
    {
        $this->doTest($I, __DIR__, __FUNCTION__);
    }

    /** @group database_transaction */
    public function testInitializeRequestFromSearchingProductId(FunctionalTester $I)
    {
        $this->doTest($I, __DIR__, __FUNCTION__);
    }

    /** @group database_transaction */
    public function testInitializeRequestFromProductList(FunctionalTester $I)
    {
        $this->doTest($I, __DIR__, __FUNCTION__);
    }

    private function doTest(FunctionalTester $I, string $dir, string $func)
    {
        $this->insertFixtureGroup($I, 'variants');
        $requestFile = sprintf('%s/files/%s.json', $dir, $func);
        $requestData = json_decode(file_get_contents($requestFile), true);

        $data = $this->shareEmail->sanitizeAssetData($requestData);
        $data['userId'] = 1;
        $data['locale'] = 'us_EN';

        $this->shareAlternateTemplate->initialize($data);
        $products = $this->shareAlternateTemplate->getProducts();

        $jsonFile = sprintf('%s/files/%s-products.json', $dir, $func);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($products, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $I->assertEquals($expected, $products);
    }
}
