<?php

/**
 * Overview
 * --------
 *
 * We're testing some fairly complex database queries. Because it's easy to get
 * SQL queries wrong, we're going to take a bulldozer approach here and test a
 * lot of permutations. To make it possible to read through these hundreds of
 * test scenarios, we're going to use a simple DSL that lets us write these
 * scenarios in a single line rather than define a test* method for each of them.
 *
 * We'll create a list of scenarios, each of which will have this form:
 *
 *      input => expected output
 *
 * We'll setup a helper class that's responsible for parsing these scenarios,
 * setting up the initial state from the input, exercising the system under
 * tests, and asserting that it produces the expected results.
 *
 * Our test method will end up looking like this:
 *
 *      $scenarios = [
 *          'input' => $expectedOutput, ...
 *      ];
 *      foreach ($scenarios as $s) {
 *          $helper->testScenario($s);
 *      }
 *
 *
 * Test Scope and DSL
 * ------------------
 *
 * We're interested in users and their activity logs. We want to test, given
 * a user and their activity log,
 *
 *  1. Can we find all inconsistencies in the data (or lack thereof)?
 *  2. Can we fix these inconsistencies?
 *
 * So our scenarios look like this:
 *
 *      activity log + current user status => expected inconsistencies
 *
 * The activity log is a sequence of any number of these four events:
 *
 *  - Active Session start; we'll write this as "[".
 *  - Active Session end;   we'll write this as "]".
 *  - Chat Session start;   we'll write this as "c".
 *  - Chat Session end;     we'll write this as ".".
 *
 * We can write activity logs as strings of events, e.g.
 *
 *      [][c.][c
 *
 * The user status is either "0" or "1". We can tack that on to the end of an
 * activity log to give a full specification of a test's initial state, e.g.
 *
 *      [][c.][c 1
 *
 * There are seven kinds of problems that the inconsistency checker can detect:
 *
 *  - User's status is 0, but they have an active session; we'll write this as "0".
 *  - User's status is 0, but they have no active session; we'll write this as "1".
 *  - There are chats before the user's first session; we'll write this as "before".
 *  - There are chats after the user's last session; we'll write this as "after".
 *  - There are chats between some of the user's sessions; we'll write this as "between".
 *  - There are active sessions that don't end before the next one starts; we'll write this as "concurrent".
 *  - There's activity whose start date is later than its end date; we'll write this as "backwards".
 *
 * We'll give this list of expected inconsistencies as an array, e.g.
 *
 *      ['0', 'before', 'between']
 *
 * Putting this all together, we can write test scenarios like this:
 *
 *  $scenarios = [
 *      '[][c.][c 1' => [],
 *      'c[]c[[]c 1'  => ['1', 'before', 'between', 'after', 'concurrent'],
 *  ];
 *
 * For each scenario, we'll set the given activity log and user status in the database,
 * then ask the system under test to report on inconsistencies, and check that they're
 * what we expect. Then, we'll run the inconsistency-fixing queries provided by the
 * SUT, ask for another inconsistency report, and check that it's empty (because the
 * inconsistencies were indeed fixed by the given queries).
 *
 * Background tests
 * ----------------
 *
 * In addition to verifying given scenarios individually, we'll want to check that
 * they're independent from each other. We'll do this by combining scenarios automatically
 * and checking that the results are the same as when we test them one by one.
 *
 * We're abstracting away specific timestamps from the way we write our activity logs.
 * One item in the log can start at the exact same time as the one before it ends, or
 * it can start later. We'll test both cases intermittently.
 */

namespace SF\functional\UserActivityConsistencyChecker;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class UserConsistencyCheckerCest extends BaseFunctional
{
    private $checker;

    public function _before($I)
    {
        parent::_before($I);
        $this->checker = new MockChecker($this->app);
    }

    /** @group database_transaction */
    public function testUserConsistencyChecker(FunctionalTester $I)
    {
        foreach ($this->getScenarios() as $scenario) {
            $this->checker->verifyScenario($I, $scenario);
        }
    }

    /** @group database_transaction */
    public function testUserConsistencyFixer(FunctionalTester $I)
    {
        $this->checker->realistic = true;
        foreach ($this->getScenarios() as $scenario) {
            $this->checker->verifyScenario($I, $scenario);
        }
    }

    private function getScenarios()
    {
        $ret = [];

        $scenarios = [
            // Complex but correct
            '[][c.c.c.c.][c.c.c.c.c.c.][c][][c.c.c.c. 1' => [],
            '[][c.c.c.c.][cccccc][c.][][c.c.c.c] 0'      => [],

            // Zero chats
            '0'            => [],
            '1'            => ['1'],
            '[ 0'          => ['0'],
            '[[ 1'         => ['concurrent'],
            '[] 0'         => [],
            '[] 1'         => ['1'],
            '[][ 0'        => ['0'],
            '[][ 1'        => [],
            '[[[][] 0'     => ['concurrent'],
            '[][] 1'       => ['1'],
            '[]]]]][[] 1'  => ['backwards'],
            '[][][ 0'      => ['0'],
            '[][][ 1'      => [],
            '[][][] 0'     => [],
            '[][[][[] 1'   => ['1', 'concurrent'],
            '[][][][ 0'    => ['0'],
            '[][][][ 1'    => [],
            '[][][][] 0'   => [],
            '[][][][] 1'   => ['1'],
            '[[][][][][ 0' => ['0', 'concurrent'],
            '[][][][][ 1'  => [],
            '[][][][][] 0' => [],
            '[][][][][] 1' => ['1'],

            // One chat
            'c 0'         => ['before'],
            'c. 0'        => ['before'],
            'c 1'         => ['before', '1'],
            'c. 1'        => ['before', '1'],
            'c [ 0'       => ['before', '0'],
            'c.[ 0'       => ['before', '0'],
            '[[c 0'       => ['0', 'concurrent'],
            ']c.[ 0'      => ['backwards'],
            '[c. 0'       => ['0'],
            'c [ 1'       => ['before'],
            'c.[ 1'       => ['before'],
            '[c 1'        => [],
            '[[c. 1'      => ['concurrent'],
            'c [] 0'      => ['before'],
            'c.[] 0'      => ['before'],
            '[c ] 0'      => [],
            '[c.] 0'      => [],
            '[[]c  0'     => ['after', 'concurrent'],
            '[]c. 0'      => ['after'],
            'c [] 1'      => ['before', '1'],
            'c.[] 1'      => ['before', '1'],
            '[c ] 1'      => ['1'],
            '[c[.] 1'     => ['1', 'concurrent'],
            '[]c  1'      => ['after', '1'],
            '[]c. 1'      => ['after', '1'],
            'c [][ 0'     => ['before', '0'],
            'c.[][ 0'     => ['before', '0'],
            '[c ][[ 0'    => ['0', 'concurrent'],
            '[c.][ 0'     => ['0'],
            '[]c [ 0'     => ['between', '0'],
            '[]c.[ 0'     => ['between', '0'],
            '[][c  0'     => ['0'],
            '[[][c. 0'    => ['0', 'concurrent'],
            'c [][ 1'     => ['before'],
            'c.[][ 1'     => ['before'],
            '[c ][ 1'     => [],
            '[c ]]c.[ 1'  => ['backwards'],
            '[c.][ 1'     => [],
            '[[[]c [ 1'   => ['between', 'concurrent'],
            '[]c.[ 1'     => ['between'],
            '[][c  1'     => [],
            '[][c. 1'     => [],
            'c [][] 0'    => ['before'],
            'c.[[][[[] 0' => ['before', 'concurrent'],
            '[c ][] 0'    => [],
            '[c.][] 0'    => [],
            '[]c [] 0'    => ['between'],
            '[]c.[] 0'    => ['between'],
            '[][[c ] 0'   => ['concurrent'],
            '[][c.] 0'    => [],
            '[][]c  0'    => ['after'],
            '[][]c. 0'    => ['after'],
            'c [][] 1'    => ['1', 'before'],
            'c.[][[] 1'   => ['1', 'before', 'concurrent'],
            '[c ][] 1'    => ['1'],
            '[c.][] 1'    => ['1'],
            '[]c [] 1'    => ['1', 'between'],
            '[]c.[] 1'    => ['1', 'between'],
            '[][c [] 1'   => ['1', 'concurrent'],
            '[][c.] 1'    => ['1'],
            '[][]c  1'    => ['1', 'after'],
            '[][]c. 1'    => ['1', 'after'],
            'c [][][ 0'   => ['0', 'before'],
            'c.[[][[][ 0' => ['0', 'before', 'concurrent'],
            '[c ][][ 0'   => ['0'],
            '[c.][][ 0'   => ['0'],
            '[]c [][ 0'   => ['0', 'between'],
            '[]c.[][ 0'   => ['0', 'between'],
            '[]...c[][ 0' => ['backwards'],
            '[][c ][[ 0'  => ['0', 'concurrent'],
            '[][c.][ 0'   => ['0'],
            '[][]c [ 0'   => ['0', 'between'],
            '[][]c.[ 0'   => ['0', 'between'],
            '[][][c  0'   => ['0'],
            '[[][][c. 0'  => ['0', 'concurrent'],
            'c [][][ 1'   => ['before'],
            'c.[][][ 1'   => ['before'],
            '[c ][][ 1'   => [],
            '[c.][][ 1'   => [],
            '[[]c [][ 1'  => ['between', 'concurrent'],
            '[]c.[][ 1'   => ['between'],
            '[][c ][ 1'   => [],
            '[][c.][ 1'   => [],
            '[][]c [ 1'   => ['between'],
            '[][[]c.[[ 1' => ['between', 'concurrent'],
            '[][][c  1'   => [],
            ']]][][[c  1' => ['backwards'],
            '[][][c. 1'   => [],

            // Two chats
            'c c  0' => ['before'],
            'c.c  0' => ['before'],
            'c c. 0' => ['before'],
            'c.c. 0' => ['before'],
            'c c  1' => ['before', '1'],
            'c.c  1' => ['before', '1'],
            'c c. 1' => ['before', '1'],
            'c.c. 1' => ['before', '1'],

            'c c [ 0'  => ['before', '0'],
            'c.c [ 0'  => ['before', '0'],
            'c c.[ 0'  => ['before', '0'],
            'c.c.[ 0'  => ['before', '0'],
            'c [c[  0' => ['before', '0', 'concurrent'],
            'c.[c  0'  => ['before', '0'],
            'c [c. 0'  => ['before', '0'],
            'c.[c. 0'  => ['before', '0'],
            '[c c  0'  => ['0'],
            '[c[.c  0' => ['0', 'concurrent'],
            '[c c. 0'  => ['0'],
            '[c.c. 0'  => ['0'],
            'c c [ 1'  => ['before'],
            'c.c [ 1'  => ['before'],
            'c c.[[ 1' => ['before', 'concurrent'],
            'c.c.[ 1'  => ['before'],
            'c [c  1'  => ['before'],
            'c.[c  1'  => ['before'],
            '..c[c  1' => ['backwards'],
            'c [c. 1'  => ['before'],
            'c.[[c. 1' => ['before', 'concurrent'],
            '[c c  1'  => [],
            '[c.c  1'  => [],
            '[c c. 1'  => [],
            '[c.c. 1'  => [],

            'c c [[] 0' => ['before', 'concurrent'],
            'c.c [] 0'  => ['before'],
            'c c.[] 0'  => ['before'],
            'c.c.[] 0'  => ['before'],
            'c [c ] 0'  => ['before'],
            'c.[c [] 0' => ['before', 'concurrent'],
            'c [c.] 0'  => ['before'],
            'c.[c.] 0'  => ['before'],
            'c []c  0'  => ['before', 'after'],
            'c.[]c  0'  => ['before', 'after'],
            'c [[]c. 0' => ['before', 'after', 'concurrent'],
            'c.[]c. 0'  => ['before', 'after'],
            '[c c ] 0'  => [],
            '[c.c ] 0'  => [],
            '[....c] 0' => ['backwards'],
            '[c c.] 0'  => [],
            '[c.c.[] 0' => ['concurrent'],
            '[c ]c  0'  => ['after'],
            '[c.]c  0'  => ['after'],
            '[c ]c. 0'  => ['after'],
            '[c.]c. 0'  => ['after'],
            '[[]c c  0' => ['after', 'concurrent'],
            '[]c.c  0'  => ['after'],
            '[]c c. 0'  => ['after'],
            '[]c.c. 0'  => ['after'],
            'c c [] 1'  => ['1', 'before'],
            'c.c [[] 1' => ['1', 'before', 'concurrent'],
            'c c.[] 1'  => ['1', 'before'],
            'c.c.[] 1'  => ['1', 'before'],
            'c [c ] 1'  => ['1', 'before'],
            'c.[c ] 1'  => ['1', 'before'],
            'c [[c.] 1' => ['1', 'before', 'concurrent'],
            'c.[c.] 1'  => ['1', 'before'],
            'c []c  1'  => ['1', 'before', 'after'],
            'c.[]c  1'  => ['1', 'before', 'after'],
            'c []c. 1'  => ['1', 'before', 'after'],
            'c.[[]c. 1' => ['1', 'before', 'after', 'concurrent'],
            '[c c ] 1'  => ['1'],
            '[c.c ] 1'  => ['1'],
            '[c c.] 1'  => ['1'],
            '[c.c.] 1'  => ['1'],
            '[c[]c  1'  => ['1', 'after', 'concurrent'],
            '[c.]c  1'  => ['1', 'after'],
            '[c ]c. 1'  => ['1', 'after'],
            '[c.]c. 1'  => ['1', 'after'],
            '[]c c  1'  => ['1', 'after'],
            '[[]c.c  1' => ['1', 'after', 'concurrent'],
            '[]c c. 1'  => ['1', 'after'],
            '[]c.c. 1'  => ['1', 'after'],

            'c c [][ 0'  => ['0', 'before'],
            'c c.[][ 0'  => ['0', 'before'],
            'c.c [[][ 0' => ['0', 'before', 'concurrent'],
            'c.c.[][ 0'  => ['0', 'before'],
            'c [c ][ 0'  => ['0', 'before'],
            'c [c.][ 0'  => ['0', 'before'],
            'c.[c ][ 0'  => ['0', 'before'],
            'c.[c.][[ 0' => ['0', 'before', 'concurrent'],
            'c []c [ 0'  => ['0', 'before', 'between'],
            'c []c.[ 0'  => ['0', 'before', 'between'],
            'c.[]c [ 0'  => ['0', 'before', 'between'],
            'c.[]c.[ 0'  => ['0', 'before', 'between'],
            'c [[][c  0' => ['0', 'before', 'concurrent'],
            'c [][c. 0'  => ['0', 'before'],
            'c.[][c  0'  => ['0', 'before'],
            'c.[][c. 0'  => ['0', 'before'],
            '[c c ][ 0'  => ['0'],
            '[c[c.][ 0'  => ['0', 'concurrent'],
            '[c.c ][ 0'  => ['0'],
            '[c.c.][ 0'  => ['0'],
            '[c ]c [ 0'  => ['0', 'between'],
            '[c ]c.[ 0'  => ['0', 'between'],
            '[c.]c [[ 0' => ['0', 'between', 'concurrent'],
            '[c.]c.[ 0'  => ['0', 'between'],
            '[c ][c  0'  => ['0'],
            '[c ][c. 0'  => ['0'],
            '[c.][c  0'  => ['0'],
            '[c.][c[. 0' => ['0', 'concurrent'],
            '[]c c [ 0'  => ['0', 'between'],
            '[]c c.[ 0'  => ['0', 'between'],
            '[]c.c [ 0'  => ['0', 'between'],
            '[]c.c.[ 0'  => ['0', 'between'],
            '[[]c [c  0' => ['0', 'between', 'concurrent'],
            '[]c [c. 0'  => ['0', 'between'],
            '[]c.[c  0'  => ['0', 'between'],
            '[]c.[c. 0'  => ['0', 'between'],
            '[][c c  0'  => ['0'],
            '[][c[c. 0'  => ['0', 'concurrent'],
            '[][c.c  0'  => ['0'],
            '[][c.c. 0'  => ['0'],
            'c c [][ 1'  => ['before'],
            'c c.[][ 1'  => ['before'],
            'c.c [[][ 1' => ['before', 'concurrent'],
            'c.c.[][ 1'  => ['before'],
            'c [c ][ 1'  => ['before'],
            'c [c.][ 1'  => ['before'],
            'c.[c ][ 1'  => ['before'],
            'c.[c.[][ 1' => ['before', 'concurrent'],
            'c []c [ 1'  => ['before', 'between'],
            'c []c.[ 1'  => ['before', 'between'],
            'c.[]c [ 1'  => ['before', 'between'],
            'c.[]c.[ 1'  => ['before', 'between'],
            'c [][[c  1' => ['before', 'concurrent'],
            'c [][c. 1'  => ['before'],
            'c.[][c  1'  => ['before'],
            'c.[][c. 1'  => ['before'],
            '[c c ][ 1'  => [],
            '[[c c.][ 1' => ['concurrent'],
            '[.c.c ][ 1' => ['backwards'],
            '[c.c.][ 1'  => [],
            '[c ]c [ 1'  => ['between'],
            '[c....c[ 1' => ['backwards'],
            '[c ]c.[ 1'  => ['between'],
            '[c[.]c [ 1' => ['between', 'concurrent'],
            '[c.]c.[ 1'  => ['between'],
            '[c ][c  1'  => [],
            '[c ][c. 1'  => [],
            '[c.][c  1'  => [],
            '[c.[][c. 1' => ['concurrent'],
            '[]c c [ 1'  => ['between'],
            '[]c c.[ 1'  => ['between'],
            '[]c.c [ 1'  => ['between'],
            '[]c.c.[ 1'  => ['between'],
            '[]c [[c  1' => ['between', 'concurrent'],
            '[]c [c. 1'  => ['between'],
            '[]c.[c  1'  => ['between'],
            '[]c.[c. 1'  => ['between'],
            '[][c c  1'  => [],
            '[[][c c. 1' => ['concurrent'],
            '[][c.c  1'  => [],
            '[][c.c. 1'  => [],

            'c c [][] 0'  => ['before'],
            'c c.[][] 0'  => ['before'],
            'c.c [][[] 0' => ['before', 'concurrent'],
            'c.c.[][] 0'  => ['before'],
            'c [c ][] 0'  => ['before'],
            'c [c.][] 0'  => ['before'],
            'c.[c ][] 0'  => ['before'],
            'c.[[c.][] 0' => ['before', 'concurrent'],
            'c []c [] 0'  => ['before', 'between'],
            'c []c.[] 0'  => ['before', 'between'],
            'c.[]c [] 0'  => ['before', 'between'],
            'c.[]c.[] 0'  => ['before', 'between'],
            'c [][[c ] 0' => ['before', 'concurrent'],
            'c [][c.] 0'  => ['before'],
            'c.[][c ] 0'  => ['before'],
            'c.[][c.] 0'  => ['before'],
            'c [][]c  0'  => ['before', 'after'],
            'c [[][]c. 0' => ['before', 'after', 'concurrent'],
            'c.[][]c  0'  => ['before', 'after'],
            'c.[][]c. 0'  => ['before', 'after'],
            '[c c ][] 0'  => [],
            '[c c.][] 0'  => [],
            '[c[.c ][] 0' => ['concurrent'],
            '[c.c.][] 0'  => [],
            '[c ]c [] 0'  => ['between'],
            '[c ]c.[] 0'  => ['between'],
            '[c.]c [] 0'  => ['between'],
            '[c.[]c.[] 0' => ['between', 'concurrent'],
            '[c ][c ] 0'  => [],
            '[c ][c.] 0'  => [],
            '[c.][c ] 0'  => [],
            '[c.][c.] 0'  => [],
            '[c ][[]c  0' => ['after', 'concurrent'],
            '[c ][]c. 0'  => ['after'],
            '[c.][]c  0'  => ['after'],
            '[c.][]c. 0'  => ['after'],
            '[]c c [] 0'  => ['between'],
            '[[]c c.[] 0' => ['between', 'concurrent'],
            '[]c.c [] 0'  => ['between'],
            '[]c.c.[] 0'  => ['between'],
            '[]c [c ] 0'  => ['between'],
            '[]c [c.] 0'  => ['between'],
            '[]c.[[c ] 0' => ['between', 'concurrent'],
            '[]c.[c.] 0'  => ['between'],
            '[]c []c  0'  => ['between', 'after'],
            '[]c []c. 0'  => ['between', 'after'],
            '[]c.[]c  0'  => ['between', 'after'],
            '[[]c.[]c. 0' => ['between', 'after', 'concurrent'],
            '[][c c ] 0'  => [],
            '[][c c.] 0'  => [],
            '[][c.c ] 0'  => [],
            '[][c.c.] 0'  => [],
            '[][[c ]c  0' => ['after', 'concurrent'],
            '[][c ]c. 0'  => ['after'],
            '[][c.]c  0'  => ['after'],
            '[][c.]c. 0'  => ['after'],
            '[][]c c  0'  => ['after'],
            '[[][]c c. 0' => ['after', 'concurrent'],
            '[][]c.c  0'  => ['after'],
            '[][]c.c. 0'  => ['after'],
            'c c [][] 1'  => ['1', 'before'],
            'c c.[][] 1'  => ['1', 'before'],
            'c.c [][[] 1' => ['1', 'before', 'concurrent'],
            'c.c.[][] 1'  => ['1', 'before'],
            'c [c ][] 1'  => ['1', 'before'],
            'c [c.][] 1'  => ['1', 'before'],
            'c.[c ][] 1'  => ['1', 'before'],
            'c.[c[.][] 1' => ['1', 'before', 'concurrent'],
            'c []c [] 1'  => ['1', 'before', 'between'],
            'c []c.[] 1'  => ['1', 'before', 'between'],
            'c.[]c [] 1'  => ['1', 'before', 'between'],
            'c.[]c.[] 1'  => ['1', 'before', 'between'],
            'c [][[c ] 1' => ['1', 'before', 'concurrent'],
            'c [][c.] 1'  => ['1', 'before'],
            'c.[][c ] 1'  => ['1', 'before'],
            'c.[][c.] 1'  => ['1', 'before'],
            'c [][]c  1'  => ['1', 'before', 'after'],
            'c [][[]c. 1' => ['1', 'before', 'after', 'concurrent'],
            'c.[][]c  1'  => ['1', 'before', 'after'],
            'c.[][]c. 1'  => ['1', 'before', 'after'],
            '[c c ][] 1'  => ['1'],
            '[c c.][] 1'  => ['1'],
            '[[c.c ][] 1' => ['1', 'concurrent'],
            '[c.c.][] 1'  => ['1'],
            '[c ]c [] 1'  => ['1', 'between'],
            '[c ]c.[] 1'  => ['1', 'between'],
            '[c.]c [] 1'  => ['1', 'between'],
            '[c[.]c.[] 1' => ['1', 'between', 'concurrent'],
            '[c ][c ] 1'  => ['1'],
            '[c ][c.] 1'  => ['1'],
            '[c.][c ] 1'  => ['1'],
            '[c.][c.] 1'  => ['1'],
            '[c[][]c  1'  => ['1', 'after', 'concurrent'],
            '[c ][]c. 1'  => ['1', 'after'],
            '[c.][]c  1'  => ['1', 'after'],
            '[c.][]c. 1'  => ['1', 'after'],
            '[]c c [] 1'  => ['1', 'between'],
            '[]c c.[[] 1' => ['1', 'between', 'concurrent'],
            '[]c.c [] 1'  => ['1', 'between'],
            '[]c.c.[] 1'  => ['1', 'between'],
            '[]c [c ] 1'  => ['1', 'between'],
            '[]c [c.] 1'  => ['1', 'between'],
            '[[]c.[c ] 1' => ['1', 'between', 'concurrent'],
            '[]c.[c.] 1'  => ['1', 'between'],
            '[]c []c  1'  => ['1', 'between', 'after'],
            '[]c []c. 1'  => ['1', 'between', 'after'],
            '[]c.[]c  1'  => ['1', 'between', 'after'],
            '[]c.[[]c. 1' => ['1', 'between', 'after', 'concurrent'],
            '[][c c ] 1'  => ['1'],
            '[][c c.] 1'  => ['1'],
            '[][c.c ] 1'  => ['1'],
            '[][c.c.] 1'  => ['1'],
            '[[][c ]c  1' => ['1', 'after', 'concurrent'],
            '[][c ]c. 1'  => ['1', 'after'],
            '[][c.]c  1'  => ['1', 'after'],
            '[][c.]c. 1'  => ['1', 'after'],
            '[][]c c  1'  => ['1', 'after'],
            '[][[]c c. 1' => ['1', 'after', 'concurrent'],
            '[][]c.c  1'  => ['1', 'after'],
            '[][]c.c. 1'  => ['1', 'after'],

            'c c [][][ 0'    => ['0', 'before'],
            'c c.[][][ 0'    => ['0', 'before'],
            'c.c [][][[ 0'   => ['0', 'before', 'concurrent'],
            'c.c.[][][ 0'    => ['0', 'before'],
            'c [c ][][ 0'    => ['0', 'before'],
            'c [c.][][ 0'    => ['0', 'before'],
            'c.[c ][][ 0'    => ['0', 'before'],
            'c.[[c.][][ 0'   => ['0', 'before', 'concurrent'],
            'c []c [][ 0'    => ['0', 'before', 'between'],
            'c []c.[][ 0'    => ['0', 'before', 'between'],
            'c.[]c [][ 0'    => ['0', 'before', 'between'],
            'c.[]c.[][ 0'    => ['0', 'before', 'between'],
            'c [][[c ][ 0'   => ['0', 'before', 'concurrent'],
            'c [][c.][ 0'    => ['0', 'before'],
            'c.[][c ][ 0'    => ['0', 'before'],
            'c.[][c.][ 0'    => ['0', 'before'],
            'c [][]c [ 0'    => ['0', 'before', 'between'],
            'c [][]c.[[ 0'   => ['0', 'before', 'between', 'concurrent'],
            'c.[][]c [ 0'    => ['0', 'before', 'between'],
            'c.[][]c.[ 0'    => ['0', 'before', 'between'],
            'c [][][c  0'    => ['0', 'before'],
            'c [][][c. 0'    => ['0', 'before'],
            'c.[[][][c  0'   => ['0', 'before', 'concurrent'],
            'c.[][][c. 0'    => ['0', 'before'],
            '[c c ][][ 0'    => ['0'],
            '[c c.][][ 0'    => ['0'],
            '[c.c ][][ 0'    => ['0'],
            '[c[.c.][][ 0'   => ['0', 'concurrent'],
            '[c ]c [][ 0'    => ['0', 'between'],
            '[c ]c.[][ 0'    => ['0', 'between'],
            '[c.]c [][ 0'    => ['0', 'between'],
            '[c.]c.[][ 0'    => ['0', 'between'],
            '[c ][[c ][ 0'   => ['0', 'concurrent'],
            '[c ][c.][ 0'    => ['0'],
            '[c.][c ][ 0'    => ['0'],
            '[c.][c.][ 0'    => ['0'],
            '[c ][]c [ 0'    => ['0', 'between'],
            '[c ][[]c.[ 0'   => ['0', 'between', 'concurrent'],
            '[c.][]c [ 0'    => ['0', 'between'],
            '[c.][]c.[ 0'    => ['0', 'between'],
            '[c ][][c  0'    => ['0'],
            '[c ][][c. 0'    => ['0'],
            '[[c.][][c  0'   => ['0', 'concurrent'],
            '[c.][][c. 0'    => ['0'],
            '[]c c [][ 0'    => ['0', 'between'],
            '[]c c.[][ 0'    => ['0', 'between'],
            '[]c.c [][ 0'    => ['0', 'between'],
            '[]c.c.[[][ 0'   => ['0', 'between', 'concurrent'],
            '[]c [c ][ 0'    => ['0', 'between'],
            '[]c [c.][ 0'    => ['0', 'between'],
            '[]c.[c ][ 0'    => ['0', 'between'],
            '[]c.[c.][ 0'    => ['0', 'between'],
            '[]c []c [[ 0'   => ['0', 'between', 'concurrent'],
            '[]c []c.[ 0'    => ['0', 'between'],
            '[]c.[]c [ 0'    => ['0', 'between'],
            '[]c.[]c.[ 0'    => ['0', 'between'],
            '[]c [][c  0'    => ['0', 'between'],
            '[]c [][c[. 0'   => ['0', 'between', 'concurrent'],
            '[]c.[][c  0'    => ['0', 'between'],
            '[]c.[][c. 0'    => ['0', 'between'],
            '[][c c ][ 0'    => ['0'],
            '[][c c.][ 0'    => ['0'],
            '[][c.c[][ 0'    => ['0', 'concurrent'],
            '[][c.c.][ 0'    => ['0'],
            '[][c ]c [ 0'    => ['0', 'between'],
            '[][c ]c.[ 0'    => ['0', 'between'],
            '[][c.]c [ 0'    => ['0', 'between'],
            '[[][c.]c.[ 0'   => ['0', 'between', 'concurrent'],
            '[][c ][c  0'    => ['0'],
            '[][c ][c. 0'    => ['0'],
            '[][c.][c  0'    => ['0'],
            '[][c.][c. 0'    => ['0'],
            '[][[]c c [ 0'   => ['0', 'between', 'concurrent'],
            '[][]c c.[ 0'    => ['0', 'between'],
            '[][]c.c [ 0'    => ['0', 'between'],
            '[][]c.c.[ 0'    => ['0', 'between'],
            '[][]c [c  0'    => ['0', 'between'],
            '[][]c [[c. 0'   => ['0', 'between', 'concurrent'],
            '[][]c.[c  0'    => ['0', 'between'],
            '[][]c.[c. 0'    => ['0', 'between'],
            '[][][c c  0'    => ['0'],
            '[][][c c. 0'    => ['0'],
            '[][][c[.c  0'   => ['0', 'concurrent'],
            '[][][c.c. 0'    => ['0'],
            'c c [][][ 1'    => ['before'],
            'c c.[][][ 1'    => ['before'],
            'c.c [][][ 1'    => ['before'],
            'c.c.[[][][ 1'   => ['before', 'concurrent'],
            'c [c ][][ 1'    => ['before'],
            'c [c.][][ 1'    => ['before'],
            'c.[c ][][ 1'    => ['before'],
            'c.[c.][][ 1'    => ['before'],
            'c []c [[][ 1'   => ['before', 'between', 'concurrent'],
            'c []c.[][ 1'    => ['before', 'between'],
            'c.[]c [][ 1'    => ['before', 'between'],
            'c.[]c.[][ 1'    => ['before', 'between'],
            'c [][c ][ 1'    => ['before'],
            'c [][c[.][ 1'   => ['before', 'concurrent'],
            'c.[][c ][ 1'    => ['before'],
            'c.[][c.][ 1'    => ['before'],
            'c [][]c [ 1'    => ['before', 'between'],
            'c [][]c.[ 1'    => ['before', 'between'],
            'c.[[][]c [ 1'   => ['before', 'between', 'concurrent'],
            'c.[][]c.[ 1'    => ['before', 'between'],
            'c [][][c  1'    => ['before'],
            'c [][][c. 1'    => ['before'],
            'c.[][][c  1'    => ['before'],
            'c.[][[][c. 1'   => ['before', 'concurrent'],
            '[c c ][][ 1'    => [],
            '[c c.][][ 1'    => [],
            '[c.c ][][ 1'    => [],
            '[c.c.][][ 1'    => [],
            '[c ]c [][[ 1'   => ['between', 'concurrent'],
            '[c ]c.[][ 1'    => ['between'],
            '[c.]c [][ 1'    => ['between'],
            '[c.]c.[][ 1'    => ['between'],
            '[c ][c ][ 1'    => [],
            '[c ][c.[][ 1'   => ['concurrent'],
            '[c.][c ][ 1'    => [],
            '[c.][c.][ 1'    => [],
            '[c ][]c [ 1'    => ['between'],
            '[c ][]c.[ 1'    => ['between'],
            '[c.][]c [[ 1'   => ['between', 'concurrent'],
            '[c.][]c.[ 1'    => ['between'],
            '[c ][][c  1'    => [],
            '[c ][][c. 1'    => [],
            '[c.][][c  1'    => [],
            '[c.][][c. 1'    => [],
            '[[]c c [][ 1'   => ['between', 'concurrent'],
            '[]c c.[][ 1'    => ['between'],
            '[]c.c [][ 1'    => ['between'],
            '[]c.c.[][ 1'    => ['between'],
            '[]c [c ][ 1'    => ['between'],
            '[]c [[c.][ 1'   => ['between', 'concurrent'],
            '[]c.[c ][ 1'    => ['between'],
            '[]c.[c.][ 1'    => ['between'],
            '[]c []c [ 1'    => ['between'],
            '[]c []c.[ 1'    => ['between'],
            '[[]c.[[]c [ 1'  => ['between', 'concurrent'],
            '[]c.[]c.[ 1'    => ['between'],
            '[]c [][c  1'    => ['between'],
            '[]c [][c. 1'    => ['between'],
            '[]c.[][c  1'    => ['between'],
            '[[]c.[][c[. 1'  => ['between', 'concurrent'],
            '[][c c ][ 1'    => [],
            '[][c c.][ 1'    => [],
            '[][c.c ][ 1'    => [],
            '[][c.c.][ 1'    => [],
            '[][c[]c [ 1'    => ['between', 'concurrent'],
            '[][c ]c.[ 1'    => ['between'],
            '[][c.]c [ 1'    => ['between'],
            '[][c.]c.[ 1'    => ['between'],
            '[][c ][c  1'    => [],
            '[][c[][[c. 1'   => ['concurrent'],
            '[][c.][c  1'    => [],
            '[][c.][c. 1'    => [],
            '[][]c c [ 1'    => ['between'],
            '[][]c c.[ 1'    => ['between'],
            '[][]c.c [ 1'    => ['between'],
            '[[[][[]c.c.[ 1' => ['between', 'concurrent'],
            '[][]c [c  1'    => ['between'],
            '[][]c [c. 1'    => ['between'],
            '[][]c.[c  1'    => ['between'],
            '[][]c.[c. 1'    => ['between'],
            '[][[][c c  1'   => ['concurrent'],
            '[][][c c. 1'    => [],
            '[][][c.c  1'    => [],
            '[][][c.c. 1'    => [],

            // Complex and wrong
            '[][c.c.c.c.]c.[c.c.c.c.c.c.]c[c]c[][c.c.c.c.] 1'   => ['1', 'between'],
            'c[][c.c.c.c.]c.[c.c.c.c.c.c.]c[c]c[][c.c.c.c.]c 1' => ['1', 'before', 'after', 'between'],
            '[][c.c.c.c.]c.[c.c.[c.c.c.c.]c[c]c[][c.c.c.c. 0'   => ['0', 'between', 'concurrent'],
            'c[][c.c.c.c.]c.[c.c.c.c.c.c.]c[c]c[][c.c.c.c.c 0'  => ['0', 'before', 'between'],
        ];

        $combined = [];
        foreach ($scenarios as $stateStr => $expectations) {
            if (
                $this->checker->realistic && (
                preg_match('/c\s*[^.]/', $stateStr) ||
                strpos($stateStr, '[') === false)
            ) {
                continue;
            }
            $ret[] = [$stateStr => $expectations];
            $combined[$stateStr] = $expectations;
        }

        $ret[] = $combined;

        return $ret;
    }

    /** @group database_transaction */
    public function testSaksPrd20180925(FunctionalTester $I)
    {
        // The consistency checker (v1.95.2) sent this email on 2018-09-25:
        //
        // > Bug: saks-prd chatting outside active sessions
        // > ----------------------------------------------
        // >
        // > We have 1 records of chat activity on saks-prd happening
        // > outside of active sessions. This is indicative of a bug: obviously
        // > a user has an active session if they're chatting with a customer.
        // > You can create artificial activity sessions to fix this
        // > inconsistency using the following query:
        // >
        // >     INSERT INTO sf_user_activity (type, source, user_id,
        // >     start_date, end_date) VALUES ('active session',
        // >     'getQueryToFixInactiveChattingUsers between', 2795,
        // >     SUBTIME('2017-08-22 20:01:42', '00:00:01'), ADDTIME('2017-10-09
        // >     16:38:47', '00:00:01'))
        //
        // However, when looking at the activity log by hand, I didn't see any
        // chats outside active sessions, and it doesn't look like adding this
        // session would help anything. Either I looked at it wrong, or there
        // was a bug in the consistency checker. This test was written to expose
        // that bug.
        $state = [
            '2795' => [
                'user_status' => 1,
                'activity' => [
                    ['id' => '395036', 'type' => 'active session', 'source' => 'backoffice', 'user_id' => '2795', 'start_date' => '2017-08-22 15:51:12', 'end_date' => '2017-09-07 15:49:22'],
                    ['id' => '395389', 'type' => 'chat', 'source' => 'chat', 'user_id' => '2795', 'start_date' => '2017-08-22 20:01:42', 'end_date' => '2017-08-22 20:15:33'],
                    ['id' => '508839', 'type' => 'active session', 'source' => 'backoffice', 'user_id' => '2795', 'start_date' => '2017-12-01 17:22:14', 'end_date' => null],
                    ['id' => '842208', 'type' => 'active session', 'source' => 'getQueryToFixInactiveChattingUsers between', 'user_id' => '2795', 'start_date' => '2017-08-15 19:33:06', 'end_date' => '2017-08-15 19:33:10'],
                    ['id' => '842209', 'type' => 'active session', 'source' => 'getQueryToFixInactiveChattingUsers between', 'user_id' => '2795', 'start_date' => '2017-10-10 18:46:12', 'end_date' => '2017-11-29 16:47:09'],
                ],
            ],
        ];

        $this->checker->setState($state);
        $this->checker->assertConsistent($I);
    }

    /** @group database_transaction */
    public function testOverlappingSessions(FunctionalTester $I)
    {
        $state = [ '843' => [
                'user_status' => 1,
                'activity' => [
                    ['id' => '558',   'type' => 'active session', 'source' => 'api',  'user_id' => '843', 'start_date' => '2016-06-23 18:48:33', 'end_date' => '2019-04-20 19:59:45'],
                    ['id' => '1643',  'type' => 'chat',           'source' => 'chat', 'user_id' => '843', 'start_date' => '2016-08-02 15:49:46', 'end_date' => '2016-08-03 13:48:38'],
                    ['id' => '63260', 'type' => 'active session', 'source' => 'api',  'user_id' => '843', 'start_date' => '2019-04-30 17:07:52', 'end_date' => null],
                ],
            ],
        ];

        $this->checker->setState($state);
        $this->checker->checkSessionOverlap($I, []);

        $state['843']['activity'][] = ['id' => '374', 'type' => 'active session', 'source' => 'backoffice', 'user_id' => '843', 'start_date' => '2016-06-17 19:11:56', 'end_date' => '2016-06-23 18:49:00'];
        $this->checker->setState($state);
        $this->checker->checkSessionOverlap($I, [[
            'a_id' => '374',
            'b_id' => '558',
            'a_end' => '2016-06-23 18:49:00',
            'b_start' => '2016-06-23 18:48:33',
            'b_end' => '2019-04-20 19:59:45',
        ]]);
    }
}
