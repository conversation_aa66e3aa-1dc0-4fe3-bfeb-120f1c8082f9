<?php

declare(strict_types=1);

namespace SF\functional\ShopPage;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\ShopPage\ShopPage as ShopPageService;

class ShopPageCest extends BaseFunctional
{
    /** @var ShopPageService $shopPageService */
    private $shopPageService;

    public function _before($I)
    {
        parent::_before($I);
        $this->shopPageService = $this->app['service.shop_page'];
    }

    /** @group database_transaction */
    public function testWhitelistHttpsStackoverflow(FunctionalTester $I)
    {
        $I->wantTo("Test whitelist");

        $result = $this->shopPageService->checkUrlInWhitelist("https://stackoverflow.com/questions/");
        $I->assertEquals(false, $result, "stackoverflow should not be on the list");
    }

    /** @group database_transaction */
    public function testWhitelistTrick(FunctionalTester $I)
    {
        $I->wantTo("Test https stackoverflow whitelist with facebook url");

        $result = $this->shopPageService->checkUrlInWhitelist("https://stackoverflow.com/questions/?u=https://www.facebook.com/");
        $I->assertEquals(false, $result, "stackoverflow with facebook in url should not be on the list");
    }

    /** @group database_transaction */
    public function testWhitelistWwwStackoverflow(FunctionalTester $I)
    {
        $I->wantTo("Test https www.stackoverflow whitelist");

        $result = $this->shopPageService->checkUrlInWhitelist("https://www.stackoverflow.com/questions/");
        $I->assertEquals(false, $result, "stackoverflow should not be on the list");
    }

    /** @group database_transaction */
    public function testWhitelistHttpsWwwFacebook(FunctionalTester $I)
    {
        $I->wantTo("Test https www.facebook");

        $result = $this->shopPageService->checkUrlInWhitelist("https://www.facebook.com/");
        $I->assertEquals(false, $result, "https facebook should be allowed");
    }

    /** @group database_transaction */
    public function testWhitelistHttpWwwFacebook(FunctionalTester $I)
    {
        $I->wantTo("Test http www.facebook");

        $result = $this->shopPageService->checkUrlInWhitelist("http://www.facebook.com/");
        $I->assertEquals(false, $result, "http www.facebook should be allowed");
    }

    /** @group database_transaction */
    public function testWhitelistHttpsFacebook(FunctionalTester $I)
    {
        $I->wantTo("Test https facebook");

        $result = $this->shopPageService->checkUrlInWhitelist("https://facebook.com/");
        $I->assertEquals(false, $result, "https facebook should be allowed");
    }

    /** @group database_transaction */
    public function testWhitelistHttpFacebook(FunctionalTester $I)
    {
        $I->wantTo("Test http facebook");

        $result = $this->shopPageService->checkUrlInWhitelist("http://facebook.com/");
        $I->assertEquals(false, $result, "http facebook should be allowed");
    }

    // Currently not handeled
    /** @group database_transaction */
    public function testWhitelistFacebookMiddleMan(FunctionalTester $I)
    {
        // This test uses facebook as a middel man to redirect to yet another site
        $I->wantTo("Test facebook as middelman");
        $result = $this->shopPageService->checkUrlInWhitelist("http://facebook.com/l.php?u=https://virus.com/");
        $I->assertEquals(false, $result, "facebook middleman should not be allowed");
    }

    /** @group database_transaction */
    public function testWhitelistSalesfloor(FunctionalTester $I)
    {
        $I->wantTo("Test salesfloor");

        $result = $this->shopPageService->checkUrlInWhitelist("http://salesfloor.net");
        $I->assertEquals(true, $result, "salesfloor should be allowed");
    }

    /** @group database_transaction */
    public function testWhitelistStorefront(FunctionalTester $I)
    {
        $I->wantTo("Test storefront");

        $result = $this->shopPageService->checkUrlInWhitelist("https://tests.dev.salesfloor.net");
        $I->assertEquals(true, $result, "tests.dev.salesfloor.net should be allowed");
    }

    /** @group database_transaction */
    public function testWhitelistExampleCom(FunctionalTester $I)
    {
        $I->wantTo("Test example.com");

        $result = $this->shopPageService->checkUrlInWhitelist("http://example.com");
        $I->assertEquals(true, $result, "example.com should be allowed");
    }

    /** @group database_transaction */
    public function testGet404RedirectPageWithInvalidUrlAndInvalidRep(FunctionalTester $I)
    {
        $I->wantTo("Test redirect user to 404 page");

        $result = $this->shopPageService->getWhitelistFailedRedirectUrl(null);
        $I->assertEquals('/404', $result, "user will be redirected to 404 page with invalid url and invalid rep");
    }
}
