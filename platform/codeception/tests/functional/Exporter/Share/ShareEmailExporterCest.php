<?php

declare(strict_types=1);

namespace SF\functional\Exporter\Share;

use Codeception\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\Share\ShareEmailExporter;

class ShareEmailExporterCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testExportShareEmailWithPhotos(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with photos");

        $this->insertFixtureGroup($I, 'share-email-general');
        $this->insertFixtureGroup($I, 'share-email-photos');

        $filePath = __DIR__ . '/files/full_export_share_email_photos.csv';
        list($files, $exporter) = $this->process('2019-06-14', '2019-06-14');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportShareEmailWithPhotosByTimeRangEmpty(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with photos - should empty");

        $this->insertFixtureGroup($I, 'share-email-general');
        $this->insertFixtureGroup($I, 'share-email-photos');

        $filePath = __DIR__ . '/files/full_export_share_email_photos_empty.csv';
        list($files, $exporter) = $this->process('2019-06-19', '2019-06-23');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportShareEmailWithAssets(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with assets");

        $this->insertFixtureGroup($I, 'share-email-general');
        $this->insertFixtureGroup($I, 'share-email-assets');

        $filePath = __DIR__ . '/files/full_export_share_email_assets.csv';

        list($files, $exporter) = $this->process('2019-06-14', '2019-06-14');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportShareEmailWithProducts(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with products");

        $this->insertFixtureGroup($I, 'share-email-general');
        $this->insertFixtureGroup($I, 'share-email-products');

        $filePath = __DIR__ . '/files/full_export_share_email_products.csv';
        list($files, $exporter) = $this->process('2019-06-14', '2019-06-14');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportShareEmailWithCustomerTags(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with customer tags");

        $this->insertFixtureGroup($I, 'share-email-general');
        $this->insertFixtureGroup($I, 'share-email-customer-tags');
        $this->insertFixtureGroup($I, 'customer-tags');

        $filePath = __DIR__ . '/files/full_export_share_email_customer_tags.csv';
        list($files, $exporter) = $this->process('2019-06-14', '2019-06-14');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportShareEmailWithAll(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with all above info, this should be the last integrated test");

        $this->insertFixtureGroup($I, 'share-email-general');
        $this->insertFixtureGroup($I, 'share-email-photos');
        $this->insertFixtureGroup($I, 'share-email-assets');
        $this->insertFixtureGroup($I, 'share-email-products');
        $this->insertFixtureGroup($I, 'share-email-customer-tags');
        $this->insertFixtureGroup($I, 'customer-tags');

        $filePath = __DIR__ . '/files/full_export_share_email_all.csv';
        list($files, $exporter) = $this->process('2019-06-14', '2019-06-14');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportShareEmailWithHtml(FunctionalTester $I)
    {
        $I->wantTo("exporter - share email - with all html");

        $this->insertFixtureGroup($I, 'share-email-html');

        $filePath = __DIR__ . '/files/full_export_share_email_html.csv';
        list($files, $exporter) = $this->process('2019-06-14', '2019-06-14');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }


    /**
     * Main function, to process + test the exporter.
     *
     * @param null $startDate
     * @param null $endDate
     * @return array
     * @throws \Salesfloor\Services\Exporter\Exceptions\ExportException
     */
    private function process($startDate = null, $endDate = null)
    {
        $exporter = $this->getMockedShareEmailExporter();

        if ($startDate === null || $endDate === null) {
            //use date in test case
            $exporter->process();
        } else {
            $exporter->processByDateRange(
                $startDate,
                $endDate
            );
        }

        $files = $exporter->getExportFiles();

        return [$files, $exporter];
    }


    /**
     * Create a mocked exporter
     *
     * @return ShareEmailExporter
     * @throws \Exception
     */
    private function getMockedShareEmailExporter()
    {
        $exporter = Stub::construct(
            '\Salesfloor\Services\Exporter\Share\ShareEmailExporter',
            [$this->app],
            [
                'afterExport' => function () {
                    return true;
                },
                'cleanup'     => function () {
                    return true;
                }
            ]
        );

        return $exporter;
    }
}
