<?php

namespace SF\functional\Exporter\Request;

use Codeception\Stub;
use Salesfloor\Services\Exporter\ExportType\ZipExportType;
use Salesfloor\Services\Exporter\Request\RequestExporter;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\Base;

class RequestExporterCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testExportRequestDateRange(FunctionalTester $I)
    {
        $I->wantTo('request export for a given date range');

        $this->insertFixtureGroup($I, 'requests');

        /** @var \Salesfloor\Services\Exporter\Request\RequestExporter $exporter */
        $exporter = $this->app['exporter.request'];
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $exporter->processByDateRange('2017-06-10', '2017-06-20');

        $localPath = sys_get_temp_dir() . '/tmp-export-request-test.csv';
        file_exists($localPath) ? unlink($localPath) : null;
        $s3Path = 'outbound/dev/requests/DEV_TESTS_request_2017-06-10_2017-06-20.csv';

        $this->app['cloudstorage.download']($s3Path, $this->app, ['SaveAs' => $localPath]);
        $contents = file($localPath);
        $I->assertCount(2, $contents);
    }

    /** @group database_transaction */
    public function testExportFullRequest(FunctionalTester $I)
    {
        $I->wantTo('request export from beginning of time');

        $this->insertFixtureGroup($I, 'requests');

        /** @var \Salesfloor\Services\Exporter\Request\RequestExporter $exporter */
        $exporter = $this->app['exporter.request'];

        $exporter->process();

        $localPath = sys_get_temp_dir() . '/tmp-export-request-test.zip';
        file_exists($localPath) ? unlink($localPath) : null;
        $s3Path = 'Exports/request/DEV_TESTS_request.zip';

        $this->app['cloudstorage.download']($s3Path, $this->app, ['SaveAs' => $localPath]);
        $unzippedPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid();

        $z = new \ZipArchive();
        $z->open($localPath);
        $I->assertTrue($z->extractTo($unzippedPath), 'failed to unzip file');
        $z->close();
        $contents = file($unzippedPath . '/DEV_TESTS_request_2017.csv');
        $I->assertCount(2, $contents);
        $I->assertTrue(strpos($contents[1], '2017-06-15 20:31:00') !== false);
        $contents = file($unzippedPath . '/DEV_TESTS_request_2018.csv');
        $I->assertCount(2, $contents);
        $I->assertTrue(strpos($contents[1], '2018-06-15 20:31:00') !== false);

        // Compare full export for each year

        // 2017
        $I->assertFileContentsAreIdentical($unzippedPath . '/DEV_TESTS_request_2017.csv', __DIR__ . '/files/full_export_request_1-2017.csv');

        // 2018
        $I->assertFileContentsAreIdentical($unzippedPath . '/DEV_TESTS_request_2018.csv', __DIR__ . '/files/full_export_request_1-2018.csv');
    }

    public function testExportFullRequestZipSplit(FunctionalTester $I)
    {
        $I->wantTo('request export from beginning of time');

        $this->insertFixtureGroup($I, 'requests');

        /** @var \Salesfloor\Services\Exporter\Request\RequestExporter $exporter */
        $exporter = $this->app['exporter.request'];

        $exporter->setSplitBy(ZipExportType::MONTHLY);
        $exporter->process();

        $localPath = sys_get_temp_dir() . '/tmp-export-request-test.zip';
        file_exists($localPath) ? unlink($localPath) : null;
        $s3Path = 'Exports/request/DEV_TESTS_request.zip';

        $this->app['cloudstorage.download']($s3Path, $this->app, ['SaveAs' => $localPath]);
        $unzippedPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid();

        $z = new \ZipArchive();
        $z->open($localPath);
        $I->assertTrue($z->extractTo($unzippedPath), 'failed to unzip file');
        $z->close();

        $contents = file($unzippedPath . '/DEV_TESTS_request_2017-06.csv');
        $I->assertCount(2, $contents);
        $I->assertTrue(strpos($contents[1], '2017-06-15 20:31:00') !== false);

        $contents = file($unzippedPath . '/DEV_TESTS_request_2018-06.csv');
        $I->assertCount(2, $contents);
        $I->assertTrue(strpos($contents[1], '2018-06-15 20:31:00') !== false);

        // Compare full export for each year-month
        // 2017-06
        $I->assertFileContentsAreIdentical($unzippedPath . '/DEV_TESTS_request_2017-06.csv', __DIR__ . '/files/full_export_request_1-2017.csv');
        // 2018-06
        $I->assertFileContentsAreIdentical($unzippedPath . '/DEV_TESTS_request_2018-06.csv', __DIR__ . '/files/full_export_request_1-2018.csv');
    }

    /**
     * This is not perfect, since the data are from fixtures and not from a real "request" - "reply"
     *
     * @group database_transaction
     *
     * @param FunctionalTester $I
     *
     * @throws \Exception
    */
    public function testExportFullRequestWithReplies(FunctionalTester $I)
    {
        $I->wantTo('exporter - request - full - with replies');

        $this->insertFixtureGroup($I, 'requestWithReplies');

        $exporter = $this->getMockedExportRequest();

        $exporter->process();

        $files = $exporter->getExportFiles();

        $I->assertEquals(2, count($files));

        //I'm not 100% sure about the order of the file that are added to the array
        $I->assertFileContentsAreIdentical($files[0], __DIR__ . '/files/full_export_request_2-2018.csv');
        $I->assertFileContentsAreIdentical($files[1], __DIR__ . '/files/full_export_request_2-2017.csv');

        // Since we mocked the cleanup() function, so we could test the tmp file
        $exporter->export->cleanup();
    }

    /**
     * This is not perfect, since the data are from fixtures and not from a real "request" - "reply"
     *
     * @group database_transaction
     *
     * @param FunctionalTester $I
     *
     * @throws \Exception
     */
    public function testDailyExportYesterdayRequestsWithReplies(FunctionalTester $I)
    {
        $I->wantTo('exporter - request - daily - with replies');

        $this->insertFixtureGroup($I, 'yesterdayRequestsWithReplies');

        $dailyExporter = $this->getMockedExportRequest();
        $dailyExporter->processDaily();
        $dailyFiles = $dailyExporter->getExportFiles();
        $dailyContents = file($dailyFiles[0]);
        $I->assertCount(4, $dailyContents);
        // Since we mocked the cleanup() function, so we could test the tmp file
        $dailyExporter->export->cleanup();
    }

    /**
     * Create a mocked exporter
     *
     * @return RequestExporter
     * @throws \Exception
     */
    private function getMockedExportRequest()
    {
        $exporter = Stub::construct(
            '\Salesfloor\Services\Exporter\Request\RequestExporter',
            [$this->app],
            [
                'afterExport' => function () {
                    return true;
                },
                'cleanup'     => function () {
                    return true;
                }
            ]
        );

        return $exporter;
    }
}
