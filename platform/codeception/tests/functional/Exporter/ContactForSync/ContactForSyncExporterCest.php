<?php

namespace SF\functional\Exporter\ContactForSync;

use Salesfloor\Services\Exporter\Contact\ContactForSyncExporter;
use SF\functional\Exporter\BaseExporterFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\Base;
use Codeception\Util\Stub;

class ContactForSyncExporterCest extends BaseExporterFunctional
{
    /** @group database_transaction */
    public function testExportContactFull(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'contact');

        /** @var ContactForSyncExporter $exporter */
        $exporter = $this->mockupExporter($this->app['exporter.contactForSync']);
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $I->wantTo("Test the full contact export");

        $exporter->process();
        $this->assertFull($I, $exporter);
    }

    /** @group database_transaction */
    public function testExportContactDateRangeCase1(FunctionalTester $I)
    {
        $startDate = '2018-08-15';
        $endDate   = '2018-10-02';
        $I->wantTo("Contact export for a given date range from $startDate to $endDate");

        $exporter = $this->mockupExporterForDateRange($I);
        $exporter->processByDateRange($startDate, $endDate);
        $this->assertDateRange($I, $exporter, $startDate, $endDate);
    }

    /** @group database_transaction */
    public function testExportContactDateRangeCase2(FunctionalTester $I)
    {
        $startDate = '2017-04-01';
        $endDate   = '2018-10-01';
        $I->wantTo("Contact export for a given date range from $startDate to $endDate");

        $exporter = $this->mockupExporterForDateRange($I);
        $exporter->processByDateRange($startDate, $endDate);
        $this->assertDateRange($I, $exporter, $startDate, $endDate);
    }

    /** @group database_transaction */
    public function testExportIncrementalCase1(FunctionalTester $I)
    {
        $startDate = '2017-04-01 00:00:00';
        $endDate   = '2018-08-15 23:59:59';

        $I->wantTo("Contact export for a given date range from $startDate to $endDate");
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = $this->mockupExporterForIncremental($I, $startDate, $endDate);
        $exporter->processIncremental();
        $this->assertIncremental($I, $exporter, $startDate, $endDate);

        $I->canSeeInDatabase('sf_customer', ['entity_last_export' => $endDate]);
        $I->canSeeInDatabase('sf_deleted_customer', ['entity_last_export' => $endDate]);
    }

    /** @group database_transaction */
    public function testExportIncrementalCase2(FunctionalTester $I)
    {
        $startDate = '2018-08-16 00:00:00';
        $endDate   = '2018-10-01 23:59:59';

        $I->wantTo("Contact export for a given date range from $startDate to $endDate");
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = $this->mockupExporterForIncremental($I, $startDate, $endDate);
        $exporter->processIncremental();
        $this->assertIncremental($I, $exporter, $startDate, $endDate);
    }

    /** @group database_transaction */
    public function testExportIncrementalCase3(FunctionalTester $I)
    {
        $startDate = '2018-10-02 00:00:00';
        $endDate   = '2018-10-02 23:59:59';

        $I->wantTo("Contact export for a given date range from $startDate to $endDate");
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = $this->mockupExporterForIncremental($I, $startDate, $endDate);
        $exporter->processIncremental();
        $this->assertIncremental($I, $exporter, $startDate, $endDate);
    }

    /** @group database_transaction */
    public function testExportIncrementalCase4(FunctionalTester $I)
    {
        $startDate = '2018-09-24 23:59:59';
        $endDate   = '2018-09-25 23:59:59';

        $I->wantTo("Contact export for a given date range from $startDate to $endDate");
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = $this->mockupExporterForIncremental($I, $startDate, $endDate);
        $exporter->processIncremental();

        $this->assertIncremental($I, $exporter, $startDate, $endDate);
    }

    /** @group database_transaction */
    public function testExportContactWithLongNote(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'contact_long_note');

        /** @var ContactForSyncExporter $exporter */
        $exporter = $this->mockupExporter($this->app['exporter.contactForSync']);
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $exporter->process();
        $location = $exporter->export->getFile();

        $shouldBeFilePath = __DIR__ . "/export.long_note.csv";
        // ignore lines 3&4
        $I->assertFileContentsAreIdenticalWithException($shouldBeFilePath, $location, [
            'skip' => [2,3], // line offset; not line number
        ]);
        $exporter->export->cleanup();
    }

    private function mockupExporterForIncremental(FunctionalTester $I, $incrementStartTime, $incrementEndTime)
    {
        $this->insertFixtureGroup($I, 'contact');

        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = Stub::construct(
            '\Salesfloor\Services\Exporter\Contact\ContactForSyncExporter',
            [$this->app],
            [
                'afterExport'   => function () {
                    return null;
                },
                'cleanup'   => function () {
                    return null;
                },
                'getIncrementStartTime' => function () use ($incrementStartTime) {
                    return $incrementStartTime;
                },
                'getIncrementEndTime'   => function () use ($incrementEndTime) {
                    return $incrementEndTime;
                }
            ]
        );
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        return $exporter ;
    }

    /**
     * This mockup exporter won't trigger logLastExport, and won't update entity_last_export column
     * @param FunctionalTester $I
     * @return \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter
     * @throws \Exception
     */
    private function mockupExporterForDateRange(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'contact');

        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */
        $exporter = Stub::construct(
            '\Salesfloor\Services\Exporter\Contact\ContactForSyncExporter',
            [$this->app],
            [
                'afterExport'   => function () {
                    return null;
                },
                'logLastExport' => function () {
                    return null;
                },
                'cleanup'     => function () {
                    return true;
                }
            ]
        );
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        return $exporter;
    }

    private function assertFull(FunctionalTester $I, ContactForSyncExporter $exporter)
    {
        $location = $exporter->export->getFile();

        $shouldBeFilePath = __DIR__ . "/export.full.should_be.csv";
        // We need to skip the two last row in this case because the generated database add timestamp at the time of
        // generation and we can't guess them to create the final file so we skip these rows during the final comparison
        $I->assertFileContentsAreIdenticalWithException($shouldBeFilePath, $location, [
            'skip' => [5, 6],
        ]);
        $exporter->export->cleanup();
    }

    private function assertDateRange(FunctionalTester $I, $exporter, $startDate, $endDate)
    {
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */

        $location = $exporter->export->getFile();

        $shouldBeFilePath = __DIR__ . "/export.$startDate--$endDate.should_be.csv";

        $I->assertFileContentsAreIdentical($shouldBeFilePath, $location);
        $exporter->export->cleanup();
    }

    private function assertIncremental(FunctionalTester $I, $exporter, $startDate, $endDate)
    {
        /** @var \Salesfloor\Services\Exporter\Contact\ContactForSyncExporter $exporter */

        $location = $exporter->export->getFile();

        $startDateTime =  date('Y-m-d-His', strtotime($startDate));
        $endDateTime =  date('Y-m-d-His', strtotime($endDate));

        $shouldBeFilePath = __DIR__ . "/increment_should_be/export.$startDateTime--$endDateTime.csv";

        $I->assertFileContentsAreIdentical($shouldBeFilePath, $location);
        $exporter->export->cleanup();
    }
}
