<?php

declare(strict_types=1);

namespace SF\functional\Exporter\Lookbook;

use Codeception\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\Lookbook\LookbookExporter;

class LookbookExporterCest extends BaseFunctional
{
    /**
     * @group database_transaction
     *
     * @param FunctionalTester $I
     * @return void
     * @throws \Exception
     */
    public function testExportLookbooks(FunctionalTester $I): void
    {
        $I->wantTo("Export lookbooks of previous day");

        $this->app['configs']['exporter.lookbook.daily.enabled'] = false;

        $this->insertFixtureGroup($I, 'add-lookbook-data');
        $this->insertFixtureGroup($I, 'add-events');

        $filePath = __DIR__ . '/files/export_lookbook.csv';
        list($files, $exporter) = $this->process();

        $I->assertEquals(1, count($files));
        //$I->assertFileContentsAreIdentical($filePath, $files[0]);
        $contents = file($files[0]);

        $I->assertCount(3, $contents);

        $lookbooks = array_map('str_getcsv', $contents);

        $yesterday = gmdate('Y-m-d', strtotime('-1 day'));

        // Test CSV file fields
        $I->assertEquals('Lookbook ID', $lookbooks[0][0]);
        $I->assertEquals('1', $lookbooks[1][0]);

        $I->assertEquals('Message IDs', $lookbooks[0][1]);
        $I->assertEquals('SFID5d095de3095ff9.39200140,SFID5d0960079ec8c0.55724627,SFID5d0960fa65c4a7.20553673,SFID5d099b3192db48.28542692', $lookbooks[1][1]);

        $I->assertEquals('Date Published (UTC)', $lookbooks[0][2]);
        $I->assertEquals('2019-06-18 21:55:47', $lookbooks[1][2]);

        $I->assertEquals('Date Modified (UTC)', $lookbooks[0][3]);
        $I->assertEquals($yesterday . ' 23:34:20', $lookbooks[1][3]);

        $I->assertEquals('Store ID', $lookbooks[0][4]);
        $I->assertEquals('1003', $lookbooks[1][4]);

        $I->assertEquals('Retailer Store ID', $lookbooks[0][5]);
        $I->assertEquals('lololol', $lookbooks[1][5]);

        $I->assertEquals('Attached Products', $lookbooks[0][11]);
        $I->assertEquals('570243209,570229115,570227948,570239112', $lookbooks[1][11]);
        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /**
     * Main function, to process + test the exporter.
     *
     * @return array
     * @throws \Exception
     */
    private function process()
    {
        $exporter = $this->getMockedLookbookExporter();
        $exporter->processDaily();

        $files = $exporter->getExportFiles();

        return [$files, $exporter];
    }


    /**
     * Create a mocked exporter
     *
     * @return LookbookExporter
     * @throws \Exception
     */
    private function getMockedLookbookExporter()
    {
        $exporter = Stub::construct(
            '\Salesfloor\Services\Exporter\Lookbook\LookbookExporter',
            [$this->app],
            [
                'afterExport' => function () {
                    return true;
                },
                'cleanup'     => function () {
                    return true;
                }
            ]
        );

        return $exporter;
    }
}
