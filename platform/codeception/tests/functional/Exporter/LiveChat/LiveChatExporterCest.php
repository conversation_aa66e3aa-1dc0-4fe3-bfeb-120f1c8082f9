<?php

namespace SF\functional\Exporter\LiveChat;

use SF\functional\Exporter\BaseExporterFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\Base;

class LiveChatExporterCest extends BaseExporterFunctional
{
    /** @group database_transaction */
    public function testExportLiveChatDateRangeWithoutLog(FunctionalTester $I)
    {
        /** @var \Salesfloor\Services\Exporter\LiveChat\LiveChatExporter $exporter */
        $exporter = $this->mockupExporter($this->app['exporter.liveChat']);
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $I->wantTo('LiveChat export for a given date range and without chat log');
        $this->insertFixtureGroup($I, 'liveChats');
        $exporter->processByDateRange('2018-08-15', '2018-08-16');

        $shouldBeFilePath = __DIR__ . "/files/testExportLiveChatDateRangeWithoutLog.csv";
        $I->assertFileContentsAreIdentical($shouldBeFilePath, $exporter->getCurrentFile());

        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportLiveChatDateRangeWithSimpleLog(FunctionalTester $I)
    {
        /** @var \Salesfloor\Services\Exporter\LiveChat\LiveChatExporter $exporter */
        $exporter = $this->mockupExporter($this->app['exporter.liveChat']);
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $I->wantTo('LiveChat export for a given date range and with chat log which only contain simple message');

        $this->insertFixtureGroup($I, 'liveChats');
        $this->insertFixtureGroup($I, 'liveChatsSimpleLog');

        $exporter->processByDateRange('2018-08-15', '2018-08-16');
        $shouldBeFilePath = __DIR__ . "/files/testExportLiveChatDateRangeWithSimpleLog.csv";
        $I->assertFileContentsAreIdentical($shouldBeFilePath, $exporter->getCurrentFile());

        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportLiveChatDateRangeWithJsonLog(FunctionalTester $I)
    {
        /** @var \Salesfloor\Services\Exporter\LiveChat\LiveChatExporter $exporter */
        $exporter = $this->mockupExporter($this->app['exporter.liveChat']);
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $I->wantTo('LiveChat export for a given date range and with chat log which message might be simple data or json data');

        $this->insertFixtureGroup($I, 'liveChats');
        $this->insertFixtureGroup($I, 'liveChatsSimpleLog');
        $this->insertFixtureGroup($I, 'liveChatsJsonLog');

        $exporter->processByDateRange('2018-08-15', '2018-08-16');

        $shouldBeFilePath = __DIR__ . "/files/testExportLiveChatDateRangeWithJsonLog.csv";
        $I->assertFileContentsAreIdentical($shouldBeFilePath, $exporter->getCurrentFile());

        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExportLiveChatDateRangeWithUserLoginRemapped(FunctionalTester $I)
    {
        /** @var \Salesfloor\Services\Exporter\LiveChat\LiveChatExporter $exporter */
        $exporter = $this->mockupExporter($this->app['exporter.liveChat']);

        $this->insertFixtureGroup($I, 'liveChatRemappedUserLogin');

        $exporter->process();

        $md5 = md5_file($exporter->getCurrentFile());

        // This should probably be private, but now it's public so use it for cleanup tmp file
        // Nothing was uploaded to s3
        $exporter->export->cleanup();

        // Make sure this file is was we expect (via md5)
        // We expect to have the new user_login based on the history table
        /**
         * Source,"Chat ID","Live Chat Date (UTC)","Live Chat Start Time (UTC)","Selected Store","Selected Specialty","Customer Name","Customer Email","Email Subscription","User Name","User Login",Messages,"Attached Products",Flagged,"Contact Record ID","Retailer Store ID"
         * sidebar,-LJxR9r8g4jWBeOr6mz1,2018-08-15,11:55:23,"TEST-BRAND - Fake Mall",1,test-name,<EMAIL>,Yes,"Testy Rep0 T.",reggie1002," [00:00:00 UTC]: ",,Yes,,
         */
        $I->assertEquals($md5, '4a294527cff221d3be00bac47e83491e');
    }
}
