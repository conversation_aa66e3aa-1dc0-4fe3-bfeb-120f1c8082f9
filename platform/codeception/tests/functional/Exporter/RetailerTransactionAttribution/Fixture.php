<?php

declare(strict_types=1);

namespace SF\functional\Exporter\RetailerTransactionAttribution;

use Codeception\Util\Fixtures;

class Fixture
{
    public function addBasicDataset()
    {
        Fixtures::add('retailer-transaction-attribution-context', [
            'sf_retailer_transaction'             => [
                [
                    'id'                    => 1,
                    'customer_id'           => '9991',
                    'trx_thread_id'         => '',
                    'trx_type'              => 'Sale',
                    'trx_date'              => '2020-01-01 00:00:01',
                    'trx_id'                => 'trx_id_11',
                    'location'              => 'kekeke',
                    'pos_id'                => 'x11',
                    'trx_total'             => 222.0000,
                    'currency'              => 'USD',
                    'created_at'            => '2024-01-15 22:03:06',
                    'updated_at'            => null,
                    'fulfillment_order'     => '',
                    'external_last_updated' => null,
                    'employee_id'           => '111',
                ],
                [
                    'id'                    => 7,
                    'customer_id'           => '9992',
                    'trx_thread_id'         => '',
                    'trx_type'              => 'Sale',
                    'trx_date'              => '2020-01-01 00:00:00',
                    'trx_id'                => 'trx_id_22',
                    'location'              => 'hahaha',
                    'pos_id'                => '',
                    'trx_total'             => 111.0000,
                    'currency'              => 'USD',
                    'created_at'            => '2024-01-15 22:03:07',
                    'updated_at'            => null,
                    'fulfillment_order'     => '',
                    'external_last_updated' => null,
                    'employee_id'           => '222',
                ]
            ],
            'sf_retailer_customers'               => [
                [
                    'id'                 => 1,
                    'customer_id'        => '9991',
                    'first_name'         => '',
                    'last_name'          => '',
                    'email'              => '<EMAIL>',
                    'created_at'         => '2024-01-15 22:03:57',
                    'limited_visibility' => 0,
                ],
                [
                    'id'                 => 2,
                    'customer_id'        => '9992',
                    'first_name'         => '',
                    'last_name'          => '',
                    'email'              => '<EMAIL>',
                    'created_at'         => '2024-01-15 22:04:01',
                    'limited_visibility' => 0,
                ]
            ],
            'sf_rep_transaction'                  => [
                [
                    'id'              => 1,
                    'user_id'         => 0,
                    'trx_id'          => 'trx_id_1111111',
                    'trx_date'        => '2024-01-17 21:17:13',
                    'trx_type'        => 'sale',
                    'trx_apply_total' => 0.0000,
                    'trx_total'       => 0.0000,
                    'status'          => 1,
                    'received_date'   => '2024-01-17 21:17:13',
                    'currency'        => 'USD',
                ]
            ],
            'sf_store'                            => [
                [
                    'store_id'          => 1001,
                    'name'              => 'Bogus Plaza',
                    'latitude'          => 43.7182,
                    'longitude'         => -79.3781,
                    'country'           => 'CA',
                    'region'            => 'ON',
                    'city'              => 'Toronto',
                    'address'           => '218 Yonge Street',
                    'postal'            => 'M5B 2H6',
                    'phone'             => '************',
                    'timezone'          => 'America/Toronto',
                    'locale'            => 'en_US',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'hahaha',
                    'sf_identifier'     => '1001',
                    'image_url'         => '',
                    'is_virtual'        => 0
                ],
                [
                    'store_id'          => 90013,
                    'name'              => 'Phony Shops',
                    'latitude'          => 40.7580,
                    'longitude'         => -73.9769,
                    'country'           => 'US',
                    'region'            => 'NY',
                    'city'              => 'New York',
                    'address'           => '611 5th Avenue',
                    'postal'            => '10022',
                    'phone'             => '************',
                    'timezone'          => 'America/New_York',
                    'locale'            => 'en_US',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'kekeke',
                    'sf_identifier'     => '1003',
                    'image_url'         => '',
                    'is_virtual'        => 0
                ]
            ]
        ]);

        Fixtures::add('retailer-transaction-attribution-email-open', [
            'sf_retailer_transaction_attribution' => [
                [
                    'id'                      => 23,
                    'retailer_transaction_id' => 1,
                    'attribution_table'       => 'sf_mail_events',
                    'attribution_table_id'    => 38703,
                    'attribution_type'        => 'email-open',
                    'attribution_time'        => '2019-12-30 19:15:18',
                    'attribution_user_id'     => 1,
                ],
                [
                    'id'                      => 24,
                    'retailer_transaction_id' => 7,
                    'attribution_table'       => 'sf_mail_events',
                    'attribution_table_id'    => 38587,
                    'attribution_type'        => 'email-open',
                    'attribution_time'        => '2019-12-30 14:27:33',
                    'attribution_user_id'     => 1,
                ],
            ],
        ]);

        Fixtures::add('retailer-transaction-attribution-email-click', [
            'sf_retailer_transaction_attribution' => [
                [
                    'id'                      => 25,
                    'retailer_transaction_id' => 1,
                    'attribution_table'       => 'sf_mail_events',
                    'attribution_table_id'    => 21762,
                    'attribution_type'        => 'email-click',
                    'attribution_time'        => '2019-12-29 00:00:01',
                    'attribution_user_id'     => 1,
                ],
                [
                    'id'                      => 26,
                    'retailer_transaction_id' => 2,
                    'attribution_table'       => 'sf_mail_events',
                    'attribution_table_id'    => 21762,
                    'attribution_type'        => 'email-click',
                    'attribution_time'        => '2019-12-29 00:00:01',
                    'attribution_user_id'     => 1,
                ]
            ],
        ]);

        Fixtures::add('retailer-transaction-attribution-text', [
            'sf_retailer_transaction_attribution' => [
                [
                    'id'                      => 200,
                    'retailer_transaction_id' => 1,
                    'attribution_table'       => 'whatever',
                    'attribution_table_id'    => 2001,
                    'attribution_type'        => 'text',
                    'attribution_time'        => '2019-12-29 00:10:01',
                    'attribution_user_id'     => 1,
                ],
            ],
        ]);

        Fixtures::add('retailer-transaction-attribution-interaction', [
            'sf_retailer_transaction_attribution' => [
                [
                    'id'                      => 300,
                    'retailer_transaction_id' => 1,
                    'attribution_table'       => 'whatever',
                    'attribution_table_id'    => 3001,
                    'attribution_type'        => 'interaction',
                    'attribution_time'        => '2019-12-29 00:30:00',
                    'attribution_user_id'     => 1,
                ],
            ],
        ]);

        Fixtures::add('retailer-transaction-attribution-return-transaction-context', [
            'sf_retailer_transaction' => [
                [
                    'id'                    => 1001,
                    'customer_id'           => '9991',
                    'trx_thread_id'         => '',
                    'trx_type'              => 'Return',
                    'trx_date'              => '2025-01-01 00:00:01',
                    'trx_id'                => 'trx_id_1001',
                    'location'              => 'kekeke',
                    'pos_id'                => 'x11',
                    'trx_total'             => 222.0000,
                    'currency'              => 'USD',
                    'created_at'            => '2024-01-15 22:03:06',
                    'updated_at'            => null,
                    'fulfillment_order'     => '',
                    'external_last_updated' => null,
                    'employee_id'           => '111',
                ],
                [
                    'id'                    => 1007,
                    'customer_id'           => '9992',
                    'trx_thread_id'         => '',
                    'trx_type'              => 'Return',
                    'trx_date'              => '2025-01-01 00:00:00',
                    'trx_id'                => 'trx_id_1007',
                    'location'              => 'hahaha',
                    'pos_id'                => '',
                    'trx_total'             => 111.0000,
                    'currency'              => 'USD',
                    'created_at'            => '2024-01-15 22:03:07',
                    'updated_at'            => null,
                    'fulfillment_order'     => '',
                    'external_last_updated' => null,
                    'employee_id'           => '222',
                ],
            ],
        ]);

        Fixtures::add('retailer-transaction-attribution-return-transaction-email-open', [
            'sf_retailer_transaction_attribution' => [
                [
                    'id'                      => 10023,
                    'retailer_transaction_id' => 1001,
                    'attribution_table'       => 'sf_mail_events',
                    'attribution_table_id'    => 38703,
                    'attribution_type'        => 'email-open',
                    'attribution_time'        => '2019-12-30 19:15:18',
                    'attribution_user_id'     => 1,
                ],
                [
                    'id'                      => 10024,
                    'retailer_transaction_id' => 1007,
                    'attribution_table'       => 'sf_mail_events',
                    'attribution_table_id'    => 38587,
                    'attribution_type'        => 'email-open',
                    'attribution_time'        => '2019-12-30 14:27:33',
                    'attribution_user_id'     => 1,
                ],
            ],
        ]);
    }
}
