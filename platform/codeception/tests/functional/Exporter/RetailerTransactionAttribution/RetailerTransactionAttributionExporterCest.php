<?php

declare(strict_types=1);

namespace SF\functional\Exporter\RetailerTransactionAttribution;

use Codeception\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\RetailerTransactionAttribution\RetailerTransactionAttributionExporter;

class RetailerTransactionAttributionExporterCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testExporterFullPriorityInteraction(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution interaction attribution is priority");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-click');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-text');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-interaction');

        $filePath = __DIR__ . '/files/full_export_retailer_transaction_attribution_priority_interaction.csv';
        list($files, $exporter) = $this->process();

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExporterByDateRangeIfHaveData(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution interaction attribution if transaction in date range");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-click');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-text');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-interaction');

        $filePath = __DIR__ . '/files/full_export_retailer_transaction_attribution_priority_interaction.csv';
        list($files, $exporter) = $this->processByDateRange('2019-12-31', '2020-01-03');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExporterByDateRangeIfNotHaveData(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution interaction attribution if transaction in date range");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-click');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-text');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-interaction');

        $filePath = __DIR__ . '/files/export_retailer_transaction_attribution_20200102_20200103.csv';
        list($files, $exporter) = $this->processByDateRange('2020-01-02', '2020-01-03');

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExporterFullPriorityText(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution text attribution is priority");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-click');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-text');

        $filePath = __DIR__ . '/files/full_export_retailer_transaction_attribution_priority_text.csv';
        list($files, $exporter) = $this->process();

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExporterFullPriorityEmailClick(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution email click attribution is priority");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-click');

        $filePath = __DIR__ . '/files/full_export_retailer_transaction_attribution_priority_email_click.csv';
        list($files, $exporter) = $this->process();

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExporterFullPriorityEmailOpen(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution email open attribution is the lowest priority");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');

        $filePath = __DIR__ . '/files/full_export_retailer_transaction_attribution_priority_email_open.csv';
        list($files, $exporter) = $this->process();

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /** @group database_transaction */
    public function testExporterForReturnTransaction(FunctionalTester $I)
    {
        $I->wantTo("Exporter - RetailerTransactionAttribution will have return transaction attribution");

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-email-open');

        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-return-transaction-context');
        $this->insertFixtureGroup($I, 'retailer-transaction-attribution-return-transaction-email-open');

        $filePath = __DIR__ . '/files/full_export_retailer_transaction_return_transaction_attribution.csv';

        /** @var \Salesfloor\Services\Exporter\RetailerTransactionAttribution\RetailerTransactionAttributionExporter $exporter */
        $exporter = $this->getMockedExporter();
        $exporter->setTransactionTypes(
            [
                \Salesfloor\Models\RetailerTransaction::TYPE_RETURN,
                \Salesfloor\Models\RetailerTransaction::TYPE_CANCELLATION
            ]
        );
        $exporter->process();
        $files = $exporter->getExportFiles();

        $I->assertEquals(1, count($files));
        $I->assertFileContentsAreIdentical($filePath, $files[0]);

        // Because we mocked the main cleanup, since we don't want it deleted when processed.
        $exporter->export->cleanup();
    }

    /**
     * Main function, to process + test the exporter.
     *
     * @return array
     * @throws \Salesfloor\Services\Exporter\Exceptions\ExportException
     */
    private function process()
    {
        $exporter = $this->getMockedExporter();
        $exporter->process();

        $files = $exporter->getExportFiles();

        return [$files, $exporter];
    }

    /**
     * Main function, to process + test the exporter.
     *
     * @return array
     * @throws \Salesfloor\Services\Exporter\Exceptions\ExportException
     */
    private function processByDateRange($startDay, $endDay)
    {
        $exporter = $this->getMockedExporter();
        $exporter->processByDateRange($startDay, $endDay);

        $files = $exporter->getExportFiles();

        return [$files, $exporter];
    }


    /**
     * Create a mocked exporter
     *
     * @return RetailerTransactionAttributionExporter
     * @throws \Exception
     */
    private function getMockedexporter()
    {
        $exporter = Stub::construct(
            '\Salesfloor\Services\Exporter\RetailerTransactionAttribution\RetailerTransactionAttributionExporter',
            [$this->app],
            [
                'afterExport' => function () {
                    return true;
                },
                'cleanup'     => function () {
                    return true;
                }
            ]
        );

        return $exporter;
    }
}
