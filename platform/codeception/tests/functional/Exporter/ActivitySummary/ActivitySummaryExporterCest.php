<?php

namespace SF\functional\Exporter\ActivitySummary;

use Codeception\Scenario;
use SF\functional\Exporter\BaseExporterFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\Exporter\Base;

class ActivitySummaryExporterCest extends BaseExporterFunctional
{
    /** @group database_transaction */
    public function testExportActivitySummary(FunctionalTester $I, Scenario $scenario)
    {
        $scenario->skip('Have to investigate the export results');
        /** @var \Salesfloor\Services\Exporter\ActivitySummary\RepActivitySummaryExporter $exporter */
        $exporter = $this->app['exporter.repActivitySummary'];
        $exporter->setExport(Base::EXPORT_TYPE_CSV);

        $I->wantTo('Activity Summary export for a given date range');
        $this->insertFixtureGroup($I, 'activitySummaryWithDateRange');
        $exporter->setDayStart('2020-09-15');
        $exporter->setDayEnd('2021-01-15');

        $result = $this->getMethod($exporter, "runEngineExportCsv")->invokeArgs($exporter, []);

        $filePath = __DIR__ . '/expected/20200915_20210115.csv';

        $I->assertEquals(file_get_contents($filePath), $result);
    }
}
