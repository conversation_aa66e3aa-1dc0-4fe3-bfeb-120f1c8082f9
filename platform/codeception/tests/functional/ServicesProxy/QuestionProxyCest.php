<?php

namespace SF\functional\ServicesProxy;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class QuestionProxyCest extends BaseFunctional
{
    private $requestData;

    /** @var  \Salesfloor\Services\ServicesProxy\QuestionProxy $questionProxy  */
    private $questionProxy;

    public function _before($I)
    {
        parent::_before($I);

        $this->questionProxy = new \Salesfloor\Services\ServicesProxy\QuestionProxy($this->app);

        $this->insertFixtureGroup($I, 'geonames_data');

        //correct data
        $this->requestData = [
            'data' =>
                [
                    'type'       => 'question',
                    'attributes' => [
                        'email'                    => '<EMAIL>',
                        'phone'                    => '+15144381234',
                        'postal_code'              => '98008',
                        'store_id'                 => 'hahahah',
                        'source_url'               => 'https://example.com/hello.html',
                        'source_title'             => 'Mobile app title',
                        'locale'                   => 'en_US',
                        'message'                  => 'I have a question',
                        'subscriber_flag'          => '1',
                        'specialty'                => 'Shoes',
                        'first_name'               => 'X1',
                        'last_name'                => 'X2',
                        'communication_preference' => 'email',
                    ],
                ],
        ];
    }

    /** @group database_transaction */
    public function testParseAndValidate(FunctionalTester $I)
    {
        $I->wantTo('basic parse and valid, should be valid');

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertEquals('on', $parse['autoSubscribe']);
        $I->assertCount(0, $errors);
    }

    /** @group database_transaction */
    public function testParseAndValidateText(FunctionalTester $I)
    {
        $I->wantTo('parse and valid, should be valid if config has text feature on as communication preference');

        $this->requestData['data']['attributes']['communication_preference'] = 'text';
        $this->app['configs']['messaging.text.enabled'] = true;
        $this->app['configs']['retailer.services.channel.text.enabled'] = true;

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(0, $errors);
    }

    /** @group database_transaction */
    public function testParseAndValidateInvalidSpecialty(FunctionalTester $I)
    {
        $I->wantTo('invalid if Specialty not exist');
        $this->requestData['data']['attributes']['specialty'] = 'unknown specialty';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }


    /** @group database_transaction */
    public function testParseAndValidateInvalidStoreId(FunctionalTester $I)
    {
        $I->wantTo('invalid if store id not exist');
        $this->requestData['data']['attributes']['store_id'] = '999';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }

    /** @group database_transaction */
    public function testParseAndValidateIfEmptyStoreIdWithCorrectZipCode(FunctionalTester $I)
    {
        $I->wantTo('valid if store id empty with correct zip code');

        unset($this->requestData['data']['attributes']['store_id']);

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(0, $errors);
    }

    /** @group database_transaction */
    public function testParseAndValidateIfEmptyStoreIdWithInvalidZipCode(FunctionalTester $I)
    {
        $I->wantTo('invalid if store id empty and zip code is invalid');

        unset($this->requestData['data']['attributes']['store_id']);
        $this->requestData['data']['attributes']['postal_code'] = '999';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }

    /** @group database_transaction */
    public function testParseAndValidateIfContactPreferenceInvalid(FunctionalTester $I)
    {
        $I->wantTo('invalid if communication_preference is not email nor text');

        $this->requestData['data']['attributes']['communication_preference'] = 'not email or text';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }

    public function testSubscriberFlagMissing(FunctionalTester $I)
    {
        $I->wantTo('should set autoSubscribe to off if flag is missing');

        unset($this->requestData['data']['attributes']['subscriber_flag']);

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $I->assertEquals('off', $parse['autoSubscribe']);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(0, $errors);
    }

    public function testInvalidSubscriberFlag(FunctionalTester $I)
    {
        $I->wantTo('invalid subscriber_flag should set autoSubscribe to off');

        $this->requestData['data']['attributes']['subscriber_flag'] = 22;

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $I->assertEquals('off', $parse['autoSubscribe']);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(0, $errors);
    }

    public function testSubscriberFlagWithInvalidAutoSubscribe(FunctionalTester $I)
    {
        $I->wantTo('when subscriber_flag and autoSubscribe are both supplied autoSubscribe takes priority');

        $this->requestData['data']['attributes']['subscriber_flag'] = 1;
        $this->requestData['data']['attributes']['autoSubscribe'] = 'potatoe';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $I->assertEquals('potatoe', $parse['autoSubscribe']);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }


    public function testAutoSubscribeOnWithoutFlag(FunctionalTester $I)
    {
        $I->wantTo('when autoSubscribe is supplies instead of subscriber_flag');

        unset($this->requestData['data']['attributes']['subscriber_flag']);
        $this->requestData['data']['attributes']['autoSubscribe'] = 'on';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $I->assertEquals('on', $parse['autoSubscribe']);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(0, $errors);
    }

    public function testEmptyAutoSubscribeOnWithoutFlag(FunctionalTester $I)
    {
        $I->wantTo('empty autoSubscribe value should be flagged as invalid');

        unset($this->requestData['data']['attributes']['subscriber_flag']);
        $this->requestData['data']['attributes']['autoSubscribe'] = '';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $I->assertEquals('', $parse['autoSubscribe']);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }

    public function testEmptyAutoSubscribeOnWithFlag(FunctionalTester $I)
    {
        $I->wantTo('empty autoSubscribe value should have priority over a defined subscriber_flag');

        $this->requestData['data']['attributes']['autoSubscribe'] = '';

        $request = new \Symfony\Component\HttpFoundation\ParameterBag($this->requestData);

        $parse = $this->questionProxy->parseDataFromIncomingHttpRequest($request);

        $I->assertEquals('', $parse['autoSubscribe']);

        $errors = $this->questionProxy->validate($parse);

        $I->assertCount(1, $errors);
    }
}
