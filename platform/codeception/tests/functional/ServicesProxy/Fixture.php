<?php

namespace SF\functional\ServicesProxy;

use Codeception\Util\Fixtures;
use SF\BaseFixture;

class Fixture extends BaseFixture
{
    public function populateGeonamesZipData()
    {
        Fixtures::add('geonames_data', [
            'geonames_zip' => [
                [
                    'id'           => 38460,
                    'country_code' => 'US',
                    'postal_code'  => '98008',
                    'place_name'   => 'Bellevue',
                    'admin_name1'  => 'Washington',
                    'admin_code1'  => 'WA',
                    'admin_name2'  => 'King',
                    'admin_code2'  => '033',
                    'admin_name3'  => '',
                    'admin_code3'  => '',
                    'latitude'     => 47.61150000,
                    'longitude'    => -122.11620000,
                    'accuracy'     => 4
                ],
            ],
            'sf_categories' => [
                [
                    'name' => 'Shoes',
                    'category_id' => 'category-id-1',
                    'language' => 'en_CA',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'category-id-1',
                    'parent_id' => '1',
                ],
            ],
        ]);
    }
}
