<?php

declare(strict_types=1);

namespace SF\functional\AI;

use Codeception\Util\Fixtures;

class Fixture
{
    /**
     * Setup fixtures for AI tests
     */
    public function setupAIFixtures(): void
    {
        // Setup mock Gemini chat completion response
        Fixtures::add('gemini_chat_response', [
            'candidates' => [
                [
                    'content' => [
                        'parts' => [
                            [
                                'text' => 'This is a test response from Gemini model.'
                            ]
                        ],
                        'role' => 'model'
                    ],
                    'finishReason' => 'STOP',
                    'index' => 0,
                    'safetyRatings' => [
                        [
                            'category' => 'HARM_CATEGORY_HARASSMENT',
                            'probability' => 'NEGLIGIBLE'
                        ]
                    ]
                ]
            ],
            'promptFeedback' => [
                'safetyRatings' => [
                    [
                        'category' => 'HARM_CATEGORY_HARASSMENT',
                        'probability' => 'NEGLIGIBLE'
                    ]
                ]
            ]
        ]);

        // Setup mock Gemini embeddings response
        Fixtures::add('gemini_embeddings_response', [
            'embeddings' => [
                [
                    'values' => [0.1, 0.2, 0.3, 0.4, 0.5],
                    'statistics' => [
                        'truncated' => false,
                        'tokenCount' => 8
                    ]
                ]
            ]
        ]);

        // Setup mock Gemini error response
        Fixtures::add('gemini_error_response', [
            'error' => [
                'code' => 400,
                'message' => 'Invalid request: Model not found',
                'status' => 'INVALID_ARGUMENT'
            ]
        ]);

        // Setup test service account for authentication
        Fixtures::add('test_service_account', [
            'type' => 'service_account',
            'project_id' => 'test-project-id',
            'private_key_id' => 'test-key-id',
            'private_key' => '-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC7VJTUt9Us8cKj\nMzEfYyjiWA4R4/M2bS1GB4t7NXp98C3SC6dVMvDuictGeurT8jNbvJZHtCSuYEvu\nNMoSfm76oqFvAp8Gy0iz5sxjZmSnXyCdPEovGhLa0VzMaQ8s+CLOyS56YyCFGeJZ\n-----END PRIVATE KEY-----\n',
            'client_email' => '<EMAIL>',
            'client_id' => '*********',
            'auth_uri' => 'https://accounts.google.com/o/oauth2/auth',
            'token_uri' => 'https://oauth2.googleapis.com/token',
            'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
            'client_x509_cert_url' => 'https://www.googleapis.com/robot/v1/metadata/x509/test-account%40test-project.iam.gserviceaccount.com',
            'universe_domain' => 'googleapis.com'
        ]);

        // Setup basic AI configuration parameters
        Fixtures::add('ai_config', [
            'project_id' => 'test-project-id',
            'location' => 'us-central1',
            'model' => 'gemini-2.0-flash-lite',
            'temperature' => 0.7,
            'max_tokens' => 1024
        ]);

        // Setup mock language detection AI response
        Fixtures::add('language_detection_ai_response', [
            'candidates' => [
                [
                    'content' => [
                        'parts' => [
                            [
                                'text' => json_encode([
                                    'detected_language' => 'fr',
                                    'final_language' => 'en',
                                    'confidence' => 0.95,
                                    'has_override' => true,
                                    'override_reason' => 'User explicitly requested English response',
                                    'locale_hint' => 'en-US',
                                    'detection_method' => 'ai-based'
                                ])
                            ]
                        ],
                        'role' => 'model'
                    ],
                    'finishReason' => 'STOP',
                    'index' => 0
                ]
            ]
        ]);

        // Setup language detection error response
        Fixtures::add('language_detection_error_response', [
            'error' => [
                'code' => 500,
                'message' => 'Language detection service temporarily unavailable',
                'status' => 'INTERNAL_ERROR'
            ]
        ]);

        // Setup language detection test cases
        Fixtures::add('language_detection_test_cases', [
            'english' => [
                'content' => 'Create a promotional email for our summer sale',
                'expected' => [
                    'detected_language' => 'en',
                    'final_language' => 'en',
                    'has_override' => false,
                    'confidence' => 0.5
                ]
            ],
            'french' => [
                'content' => 'Créez une campagne email pour notre vente d\'été',
                'expected' => [
                    'detected_language' => 'fr',
                    'final_language' => 'fr',
                    'has_override' => false,
                    'confidence' => 0.6
                ]
            ],
            'spanish' => [
                'content' => 'Crear una campaña de correo para nuestra venta',
                'expected' => [
                    'detected_language' => 'es',
                    'final_language' => 'es',
                    'has_override' => false,
                    'confidence' => 0.9
                ]
            ],
            'override_french_to_english' => [
                'content' => 'Créez un email mais respond in English please',
                'expected' => [
                    'final_language' => 'en',
                    'has_override' => true,
                    'confidence' => 0.95
                ]
            ],
            'chinese' => [
                'content' => '创建促销电子邮件活动',
                'expected' => [
                    'detected_language' => 'zh',
                    'final_language' => 'zh',
                    'has_override' => false,
                    'confidence' => 0.9
                ]
            ]
        ]);
    }
}
