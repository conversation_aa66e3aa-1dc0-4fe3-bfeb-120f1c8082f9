<?php

declare(strict_types=1);

namespace SF\functional\AI;

use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Salesfloor\Configs\Configs;
use Salesfloor\Services\AI\Config\AIConfig;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\ProviderAdapter\VertexAIAdapter;
use Salesfloor\Services\AI\Service;
use Salesfloor\Services\Auth\TokenService;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class AIServiceCest extends BaseFunctional
{
    private const TEST_PROJECT_ID = 'test-project-id';
    private const TEST_LOCATION = 'us-central1';
    private const TEST_MODEL = 'gemini-2.0-flash-lite';
    private const TEST_ACCESS_TOKEN = 'test-access-token';

    /**
     * @var Service
     */
    private $aiService;

    /**
     * @var MockHandler
     */
    private $mockHandler;

    /**
     * @var Client
     */
    private $mockHttpClient;

    /**
     * @var TokenService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mockTokenService;

    /**
     * Set up the test environment
     */
    public function _before($I)
    {
        parent::_before($I);

        // Create mock handler for HTTP requests
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);
        $this->mockHttpClient = new Client(['handler' => $handlerStack]);

        // Create mock token service
        $this->mockTokenService = $I->createMock(TokenService::class);
        $this->mockTokenService
            ->method('getGoogleAccessToken')
            ->willReturn(self::TEST_ACCESS_TOKEN);

        // Setup test configs
        $configs = $I->createMock(Configs::class);
        $configs->method('offsetGet')->willReturnCallback(function ($key) {
            return match ($key) {
                'ai.default_provider' => 'vertexai',
                'ai.vertexai.model' => self::TEST_MODEL,
                'ai.vertexai.project_id' => self::TEST_PROJECT_ID,
                'ai.vertexai.location' => self::TEST_LOCATION,
                'ai.vertexai.service_account' => base64_encode(
                    json_encode($this->getFixture('test_service_account'))
                ),
                'ai.vertexai.temperature' => 0.7,
                'ai.vertexai.max_tokens' => 1024,
                default => null,
            };
        });
        $configs->method('offsetExists')->willReturn(true);

        // Create AI service
        $this->aiService = new Service(
            $configs,
            $I->grabService('logger'),
            $this->mockTokenService,
            $this->mockHttpClient
        );
    }

    /**
     * Test service initialization and default provider
     */
    public function testServiceInitialization(FunctionalTester $I)
    {
        $I->assertEquals('vertexai', $this->aiService->getDefaultProvider());

        $adapter = $this->aiService->getAdapter();
        $I->assertInstanceOf(VertexAIAdapter::class, $adapter);
    }

    /**
     * Test generating chat completion with VertexAI
     */
    public function testGenerateChatCompletion(FunctionalTester $I)
    {
        $this->request('gemini_chat_response', 200);
        $messages = [
            ['role' => 'assistant', 'content' => 'You are a helpful assistant.'],
            ['role' => 'user', 'content' => 'Tell me about AI.'],
        ];

        $response = $this->aiService->generateChatCompletion($messages);

        $I->assertInstanceOf(AIResponse::class, $response);
        $I->assertEquals(
            'This is a test response from Gemini model.',
            $response->getContent()
        );
        $I->assertEquals(self::TEST_MODEL, $response->getModel());
        $I->assertEquals('vertexai', $response->getProvider());
    }

    /**
     * Test generating embeddings with VertexAI
     */
    public function testGenerateEmbeddings(FunctionalTester $I)
    {
        $mockAdapter = $I->createMock(VertexAIAdapter::class);
        $mockAdapter->method('getName')->willReturn('vertexai');
        $mockAdapter->method('isAvailable')->willReturn(true);
        $mockAdapter->method('initialize');

        $mockAdapter
            ->method('generateEmbeddings')
            ->willReturn([0.1, 0.2, 0.3, 0.4, 0.5]);

        $configs = $I->createMock(Configs::class);
        $configs->method('offsetGet')->willReturn('vertexai');
        $configs->method('offsetExists')->willReturn(true);

        $service = new Service(
            $configs,
            $I->grabService('logger'),
            $this->mockTokenService,
            $this->mockHttpClient
        );

        $service->addAdapter('vertexai', $mockAdapter);

        $reflectionClass = new \ReflectionClass($service);
        $activeAdapterProperty = $reflectionClass->getProperty('activeAdapter');
        $activeAdapterProperty->setAccessible(true);
        $activeAdapterProperty->setValue($service, $mockAdapter);

        $text = 'This is a sample text for embeddings.';
        $embeddings = $service->generateEmbeddings($text);

        $I->assertEquals([0.1, 0.2, 0.3, 0.4, 0.5], $embeddings);
    }

    /**
     * Test custom configuration
     */
    public function testCustomConfiguration(FunctionalTester $I)
    {
        // Create custom config
        $customConfig = AIConfig::forVertexAI(
            'gemini-2.0-flash', // Different model
            self::TEST_PROJECT_ID,
            self::TEST_LOCATION,
            $this->getFixture('test_service_account'),
            [
                'temperature' => 0.3, // Custom temperature
                'max_tokens' => 2048, // Custom token limit
            ]
        );

        // Use the custom config
        $this->aiService->useProvider('vertexai', $customConfig);

        $this->request('gemini_chat_response', 200);

        // The service should now use the custom config
        $messages = [
            ['role' => 'user', 'content' => 'Hello with custom config'],
        ];

        $response = $this->aiService->generateChatCompletion($messages);

        $I->assertEquals(
            'This is a test response from Gemini model.',
            $response->getContent()
        );
        $I->assertEquals('gemini-2.0-flash', $response->getModel()); // Should use custom model
    }

    /**
     * Test adding a custom adapter
     */
    public function testAddCustomAdapter(FunctionalTester $I)
    {
        $configs = $I->createMock(Configs::class);
        $configs->method('offsetGet')->willReturn('custom-provider');
        $configs->method('offsetExists')->willReturn(true);

        $service = new Service(
            $configs,
            $I->grabService('logger'),
            $this->mockTokenService,
            $this->mockHttpClient
        );

        $mockAdapter = $I->createMock(VertexAIAdapter::class);
        $mockAdapter->method('getName')->willReturn('custom-provider');
        $mockAdapter->method('isAvailable')->willReturn(true);

        $mockResponse = new AIResponse(
            'Response from custom adapter',
            'custom-model',
            'custom-provider',
            ['raw' => 'data'],
            [],
            ['total_tokens' => 50]
        );

        $mockAdapter
            ->method('generateChatCompletion')
            ->willReturn($mockResponse);
        $mockAdapter->method('initialize');
        $service->addAdapter('custom-provider', $mockAdapter);

        $reflectionClass = new \ReflectionClass($service);
        $activeAdapterProperty = $reflectionClass->getProperty('activeAdapter');
        $activeAdapterProperty->setAccessible(true);
        $activeAdapterProperty->setValue($service, $mockAdapter);

        $response = $service->generateChatCompletion([
            ['role' => 'user', 'content' => 'Test with custom adapter'],
        ]);

        $I->assertEquals(
            'Response from custom adapter',
            $response->getContent()
        );
        $I->assertEquals('custom-provider', $response->getProvider());
    }

    /**
     * Test checking available providers
     */
    public function testAvailableProviders(FunctionalTester $I)
    {
        $mockAdapter = $I->createMock(VertexAIAdapter::class);
        $mockAdapter->method('getName')->willReturn('vertexai');
        $mockAdapter->method('isAvailable')->willReturn(true);

        $configs = $I->createMock(Configs::class);
        $configs->method('offsetGet')->willReturn('vertexai');
        $configs->method('offsetExists')->willReturn(true);

        $this->aiService = new Service(
            $configs,
            $I->grabService('logger'),
            $this->mockTokenService,
            $this->mockHttpClient
        );

        $this->aiService->addAdapter('vertexai', $mockAdapter);

        $providers = $this->aiService->getAvailableProviders();
        $I->assertContains('vertexai', $providers);

        $I->assertTrue($this->aiService->isProviderAvailable('vertexai'));
        $I->assertFalse(
            $this->aiService->isProviderAvailable('nonexistent-provider')
        );
    }

    /**
     * Initiate a mocked request to a provider
     */
    private function request(
        string $fixtureKey,
        int $statusCode,
        string $provider = 'vertexai'
    ): void {
        $this->mockHandler->append(
            new Response(
                $statusCode,
                [],
                json_encode($this->getFixture($fixtureKey))
            )
        );

        // Only use provider if not already configured
        if (!$this->aiService->isProviderAvailable($provider)) {
            $this->aiService->useProvider($provider);
        }
    }
}
