<?php

declare(strict_types=1);

namespace SF\functional\AI;

use Salesfloor\API\Exceptions\Generic\Invalid\InvalidInputException;
use Salesfloor\API\Managers\AI\Prompt as PromptManager;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class PromptCest extends BaseFunctional
{
    private const TEST_USER_ID = 1;
    private const TEST_PROMPT_TITLE = 'Test Marketing Prompt';
    private const TEST_PROMPT_CONTENT = 'Create an engaging email about summer sales with personalized recommendations for {customer_name}';
    private const UPDATED_PROMPT_TITLE = 'Updated Marketing Prompt';
    private const UPDATED_PROMPT_CONTENT = 'Create a professional email about seasonal promotions with tailored offers for {customer_name}';

    /**
     * @var PromptManager
     */
    private $promptManager;

    /**
     * @var array Created prompt IDs for cleanup
     */
    private $createdPromptIds = [];

    /**
     * Set up the test environment
     */
    public function _before($I)
    {
        parent::_before($I);

        $this->promptManager = new PromptManager(
            $this->app['repositories.mysql'],
            null,
            '',
            [],
            null,
            $this->app['service.multilang'],
            $this->app['logger']
        );

        $this->createdPromptIds = [];
    }

    /**
     * Clean up created prompts after each test
     * @group database_transaction
     */
    public function _after($I)
    {
        // Clean up any created prompts
        foreach ($this->createdPromptIds as $promptId) {
            try {
                $prompt = $this->promptManager->getOneOrNull(['id' => $promptId]);
                if ($prompt) {
                    $this->promptManager->delete($prompt);
                }
            } catch (\Exception $e) {
                // Ignore cleanup errors
            }
        }
        parent::_after($I);
    }

    /**
     * Test creating a new prompt successfully
     * @group database_transaction
     */
    public function testCreatePromptSuccess(FunctionalTester $I)
    {
        $I->wantTo('create a new AI prompt successfully using manager');

        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'title' => self::TEST_PROMPT_TITLE,
            'prompt' => self::TEST_PROMPT_CONTENT,
        ];

        $prompt = $this->promptManager->create($createParameters);

        $I->assertNotNull($prompt);
        $I->assertEquals(self::TEST_USER_ID, $prompt->user_id);
        $I->assertEquals(self::TEST_PROMPT_TITLE, $prompt->title);
        $I->assertEquals(self::TEST_PROMPT_CONTENT, $prompt->prompt);

        if (isset($prompt->id) && $prompt->id) {
            $this->createdPromptIds[] = $prompt->id;

            $savedPrompt = $this->promptManager->getOne(['id' => $prompt->id]);
            $I->assertEquals($prompt->id, $savedPrompt->id);
            $I->assertEquals(self::TEST_PROMPT_TITLE, $savedPrompt->title);
            $I->assertEquals(self::TEST_PROMPT_CONTENT, $savedPrompt->prompt);
        }
    }

    /**
     * Test creating a prompt without user_id fails
     * @group database_transaction
     */
    public function testCreatePromptWithoutUserIdFails(FunctionalTester $I)
    {
        $I->wantTo('verify creating a prompt without user_id fails');

        $createParameters = [
            'title' => self::TEST_PROMPT_TITLE,
            'prompt' => self::TEST_PROMPT_CONTENT,
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test creating a prompt without title fails
     * @group database_transaction
     */
    public function testCreatePromptWithoutTitleFails(FunctionalTester $I)
    {
        $I->wantTo('verify creating a prompt without title fails');

        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'prompt' => self::TEST_PROMPT_CONTENT,
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test creating a prompt without prompt content fails
     * @group database_transaction
     */
    public function testCreatePromptWithoutContentFails(FunctionalTester $I)
    {
        $I->wantTo('verify creating a prompt without content fails');

        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'title' => self::TEST_PROMPT_TITLE,
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test creating a prompt with empty title fails
     * @group database_transaction
     */
    public function testCreatePromptWithEmptyTitleFails(FunctionalTester $I)
    {
        $I->wantTo('verify creating a prompt with empty title fails');

        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'title' => '',
            'prompt' => self::TEST_PROMPT_CONTENT,
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test creating a prompt with whitespace-only title fails
     * @group database_transaction
     */
    public function testCreatePromptWithWhitespaceOnlyTitleFails(FunctionalTester $I)
    {
        $I->wantTo('verify creating a prompt with whitespace-only title fails');

        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'title' => '   ',
            'prompt' => self::TEST_PROMPT_CONTENT,
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test creating a prompt with empty content fails
     * @group database_transaction
     */
    public function testCreatePromptWithEmptyContentFails(FunctionalTester $I)
    {
        $I->wantTo('verify creating a prompt with empty content fails');

        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'title' => self::TEST_PROMPT_TITLE,
            'prompt' => '',
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test creating maximum allowed prompts (5) and failing on 6th
     * @group database_transaction
     */
    public function testCreateMaximumPromptsLimit(FunctionalTester $I)
    {
        $I->wantTo('verify that users cannot create more than 5 prompts');

        // Create 5 prompts (the maximum allowed)
        for ($i = 1; $i <= 5; $i++) {
            $createParameters = [
                'user_id' => self::TEST_USER_ID,
                'title' => "Test Prompt {$i}",
                'prompt' => "This is test prompt content {$i}",
            ];

            $prompt = $this->promptManager->create($createParameters);
            $this->promptManager->save($prompt);
            $I->assertNotNull($prompt);
            if (isset($prompt->id) && $prompt->id) {
                $this->createdPromptIds[] = $prompt->id;
            }
        }

        // Verify we have created prompts for this user
        $userPrompts = $this->promptManager->getUserPrompts(self::TEST_USER_ID);
        $I->assertGreaterThanOrEqual(5, count($userPrompts));

        // Try to create a 6th prompt - should fail
        $createParameters = [
            'user_id' => self::TEST_USER_ID,
            'title' => 'Test Prompt 6',
            'prompt' => 'This is test prompt content 6',
        ];

        $I->expectThrowable(InvalidInputException::class, function () use ($createParameters) {
            $this->promptManager->create($createParameters);
        });
    }

    /**
     * Test retrieving user prompts
     * @group database_transaction
     */
    public function testGetUserPrompts(FunctionalTester $I)
    {
        $I->wantTo('retrieve list of user prompts');

        $prompts = $this->promptManager->getUserPrompts(self::TEST_USER_ID);
        $I->assertIsArray($prompts);
        $I->assertEmpty($prompts);

        $prompt1 = $this->createTestPrompt($I, 'First Prompt', 'First content');
        $prompt2 = $this->createTestPrompt($I, 'Second Prompt', 'Second content');

        $prompts = $this->promptManager->getUserPrompts(self::TEST_USER_ID);
        $I->assertGreaterThanOrEqual(2, count($prompts));

        if (isset($prompt1->id) && isset($prompt2->id) && $prompt1->id && $prompt2->id) {
            $foundPrompt1 = false;
            $foundPrompt2 = false;
            foreach ($prompts as $prompt) {
                if ($prompt->id == $prompt1->id) {
                    $foundPrompt1 = true;
                }
                if ($prompt->id == $prompt2->id) {
                    $foundPrompt2 = true;
                }
            }
            $I->assertTrue($foundPrompt1, 'First prompt should be found');
            $I->assertTrue($foundPrompt2, 'Second prompt should be found');
        }
    }

    /**
     * Test retrieving empty list when no prompts exist for user
     * @group database_transaction
     */
    public function testGetUserPromptsEmpty(FunctionalTester $I)
    {
        $I->wantTo('retrieve empty list when no prompts exist for user');

        $prompts = $this->promptManager->getUserPrompts(999); // Non-existent user
        $I->assertIsArray($prompts);
        $I->assertEmpty($prompts);
    }

    /**
     * Test retrieving a specific user prompt
     * @group database_transaction
     */
    public function testGetUserPrompt(FunctionalTester $I)
    {
        $I->wantTo('retrieve a specific prompt for a user');

        $createdPrompt = $this->createTestPrompt($I);

        if (isset($createdPrompt->id) && $createdPrompt->id) {
            $prompt = $this->promptManager->getUserPrompt(self::TEST_USER_ID, (int) $createdPrompt->id);

            $I->assertNotNull($prompt);
            $I->assertEquals($createdPrompt->id, $prompt->id);
            $I->assertEquals(self::TEST_PROMPT_TITLE, $prompt->title);
            $I->assertEquals(self::TEST_PROMPT_CONTENT, $prompt->prompt);
            $I->assertEquals(self::TEST_USER_ID, $prompt->user_id);
        } else {
            $I->markTestSkipped('Prompt creation did not assign ID, skipping getUserPrompt test');
        }
    }

    /**
     * Test retrieving non-existent prompt returns null
     * @group database_transaction
     */
    public function testGetNonExistentUserPrompt(FunctionalTester $I)
    {
        $I->wantTo('verify getting non-existent prompt returns null');

        $prompt = $this->promptManager->getUserPrompt(self::TEST_USER_ID, 99999);
        $I->assertNull($prompt);
    }

    /**
     * Test user cannot access other user's prompts
     * @group database_transaction
     */
    public function testUserIsolation(FunctionalTester $I)
    {
        $I->wantTo('verify users can only access their own prompts');

        $prompt1 = $this->createTestPrompt($I, 'User 1 Prompt', 'Content for user 1', self::TEST_USER_ID);

        $prompt2 = $this->createTestPrompt($I, 'User 2 Prompt', 'Content for user 2', 2);

        if (isset($prompt1->id) && isset($prompt2->id) && $prompt1->id && $prompt2->id) {
            $userPrompt = $this->promptManager->getUserPrompt(self::TEST_USER_ID, (int) $prompt1->id);
            $I->assertNotNull($userPrompt);
            $I->assertEquals($prompt1->id, $userPrompt->id);
            $userPrompt = $this->promptManager->getUserPrompt(self::TEST_USER_ID, (int) $prompt2->id);
            $I->assertNull($userPrompt);

            $userPrompt = $this->promptManager->getUserPrompt(2, (int) $prompt2->id);
            $I->assertNotNull($userPrompt);
            $I->assertEquals($prompt2->id, $userPrompt->id);

            $userPrompt = $this->promptManager->getUserPrompt(2, (int) $prompt1->id);
            $I->assertNull($userPrompt);
        } else {
            $I->markTestSkipped('Prompt creation did not assign IDs, skipping user isolation test');
        }
    }

    /**
     * Test deleting an existing prompt
     * @group database_transaction
     */
    public function testDeletePrompt(FunctionalTester $I)
    {
        $I->wantTo('delete an existing prompt');

        $prompt = $this->createTestPrompt($I);

        if (isset($prompt->id) && $prompt->id) {
            $existingPrompt = $this->promptManager->getUserPrompt(self::TEST_USER_ID, (int) $prompt->id);
            $I->assertNotNull($existingPrompt);

            // Delete the prompt
            $result = $this->promptManager->delete($prompt);
            $I->assertEquals(1, $result); // Should return 1 for successful deletion

            $this->createdPromptIds = array_filter($this->createdPromptIds, function ($id) use ($prompt) {
                return $id !== $prompt->id;
            });

            $deletedPrompt = $this->promptManager->getUserPrompt(self::TEST_USER_ID, (int) $prompt->id);
            $I->assertNull($deletedPrompt);
        } else {
            $I->markTestSkipped('Prompt creation did not assign ID, skipping delete test');
        }
    }

    /**
     * Test deleting non-existent prompt handles gracefully
     * @group database_transaction
     */
    public function testDeleteNonExistentPrompt(FunctionalTester $I)
    {
        $I->wantTo('verify deleting non-existent prompt handles gracefully');

        $result = $this->promptManager->delete(null);
        $I->assertEquals(0, $result);

        $emptyModel = new \stdClass();
        $result = $this->promptManager->delete($emptyModel);
        $I->assertEquals(0, $result);
    }

    /**
     * Test creating prompt after deleting one (should work within limit)
     * @group database_transaction
     */
    public function testCreateAfterDelete(FunctionalTester $I)
    {
        $I->wantTo('verify can create new prompt after deleting one');

        $promptIds = [];
        for ($i = 1; $i <= 5; $i++) {
            $prompt = $this->createTestPrompt($I, "Test Prompt {$i}", "Content {$i}");
            if (isset($prompt->id) && $prompt->id) {
                $promptIds[] = $prompt->id;
            }
        }

        $userPrompts = $this->promptManager->getUserPrompts(self::TEST_USER_ID);
        $createdCount = count($promptIds);

        if ($createdCount >= 5) {
            $I->assertGreaterThanOrEqual(5, count($userPrompts));

            $I->expectThrowable(InvalidInputException::class, function () {
                $createParameters = [
                    'user_id' => self::TEST_USER_ID,
                    'title' => 'Test Prompt 6',
                    'prompt' => 'Content 6',
                ];
                $this->promptManager->create($createParameters);
            });

            if (!empty($promptIds)) {
                $promptToDelete = $this->promptManager->getOne(['id' => $promptIds[0]]);
                if ($promptToDelete) {
                    $result = $this->promptManager->delete($promptToDelete);
                    $I->assertEquals(1, $result);

                    // Remove from cleanup list
                    $this->createdPromptIds = array_filter($this->createdPromptIds, function ($id) use ($promptIds) {
                        return $id !== $promptIds[0];
                    });

                    $createParameters = [
                        'user_id' => self::TEST_USER_ID,
                        'title' => 'Test Prompt 6',
                        'prompt' => 'Content 6',
                    ];

                    $prompt = $this->promptManager->create($createParameters);
                    $I->assertNotNull($prompt);
                    if (isset($prompt->id) && $prompt->id) {
                        $this->createdPromptIds[] = $prompt->id;
                    }
                }
            }
        } else {
            $I->markTestSkipped('Could not create enough prompts to test limit, skipping');
        }
    }

    /**
     * Test database transaction rollback on error
     * @group database_transaction
     */
    public function testTransactionRollbackOnError(FunctionalTester $I)
    {
        $I->wantTo('verify transaction rolls back on error during creation');

        // Fill prompt store with at least 4 prompts (one less than limit)
        $createdCount = 0;
        for ($i = 1; $i <= 4; $i++) {
            $prompt = $this->createTestPrompt($I, "Test Prompt {$i}", "Content {$i}");
            if (isset($prompt->id) && $prompt->id) {
                $createdCount++;
            }
        }

        $initialCount = $this->promptManager->count(['user_id' => self::TEST_USER_ID]);
        $I->assertEquals($createdCount, $initialCount);

        // Mocking a scenario where transaction would fail
        // once we exceed the limit which should trigger a rollback
        $I->expectThrowable(InvalidInputException::class, function () {
            // Create 2 more prompts quickly to test the transaction safety
            for ($i = 5; $i <= 6; $i++) {
                $createParameters = [
                    'user_id' => self::TEST_USER_ID,
                    'title' => "Test Prompt {$i}",
                    'prompt' => "Content {$i}",
                ];
                $prompt = $this->promptManager->create($createParameters);
                $this->promptManager->save($prompt);
            }
        });

        $finalCount = $this->promptManager->count(['user_id' => self::TEST_USER_ID]);
        $expectedCount = min($createdCount + 1, 5); // Should not exceed limit
        $I->assertEquals($expectedCount, $finalCount);
    }

    /**
     * Test that count method works correctly for tracking user limits
     * @group database_transaction
     */
    public function testUserPromptCount(FunctionalTester $I)
    {
        $I->wantTo('verify prompt count tracking works correctly');

        $count = $this->promptManager->count(['user_id' => self::TEST_USER_ID]);
        $I->assertEquals(0, $count);

        $actualCreated = 0;
        for ($i = 1; $i <= 3; $i++) {
            $prompt = $this->createTestPrompt($I, "Test Prompt {$i}", "Content {$i}");
            if (isset($prompt->id) && $prompt->id) {
                $actualCreated++;
            }
            $count = $this->promptManager->count(['user_id' => self::TEST_USER_ID]);
            $I->assertEquals($actualCreated, $count);
        }

        $userPrompts = $this->promptManager->getUserPrompts(self::TEST_USER_ID);
        if (!empty($userPrompts) && isset($userPrompts[0]->id) && $userPrompts[0]->id) {
            $this->promptManager->delete($userPrompts[0]);

            $count = $this->promptManager->count(['user_id' => self::TEST_USER_ID]);
            $I->assertEquals($actualCreated - 1, $count);

            $this->createdPromptIds = array_filter($this->createdPromptIds, function ($id) use ($userPrompts) {
                return $id !== $userPrompts[0]->id;
            });
        }
    }

    /**
     * Test prompt ordering by created_at DESC
     * @group database_transaction
     */
    public function testPromptOrdering(FunctionalTester $I)
    {
        $I->wantTo('verify prompts are ordered by created_at DESC');

        $prompt1 = $this->createTestPrompt($I, 'First Prompt', 'First content');
        usleep(100000);

        $prompt2 = $this->createTestPrompt($I, 'Second Prompt', 'Second content');
        usleep(100000);

        $prompt3 = $this->createTestPrompt($I, 'Third Prompt', 'Third content');

        $prompts = $this->promptManager->getUserPrompts(self::TEST_USER_ID);

        if (
            isset($prompt1->id) && isset($prompt2->id) && isset($prompt3->id) &&
            $prompt1->id && $prompt2->id && $prompt3->id
        ) {
            $I->assertGreaterThanOrEqual(3, count($prompts));

            // Find our prompts in the list
            $foundPrompts = [];
            foreach ($prompts as $prompt) {
                if ($prompt->id == $prompt1->id) {
                    $foundPrompts[1] = $prompt;
                }
                if ($prompt->id == $prompt2->id) {
                    $foundPrompts[2] = $prompt;
                }
                if ($prompt->id == $prompt3->id) {
                    $foundPrompts[3] = $prompt;
                }
            }

            $I->assertCount(3, $foundPrompts, 'All three prompts should be found');
        } else {
            $I->markTestSkipped('Prompt creation did not assign IDs, skipping ordering test');
        }
    }

    /**
     * Helper method to create a test prompt
     */
    private function createTestPrompt(
        FunctionalTester $I,
        string $title = null,
        string $content = null,
        int $userId = null
    ) {
        $title = $title ?: self::TEST_PROMPT_TITLE;
        $content = $content ?: self::TEST_PROMPT_CONTENT;
        $userId = $userId ?: self::TEST_USER_ID;

        $createParameters = [
            'user_id' => $userId,
            'title' => $title,
            'prompt' => $content,
        ];

        $prompt = $this->promptManager->create($createParameters);
        $this->promptManager->save($prompt);

        if ($userId === self::TEST_USER_ID && isset($prompt->id) && $prompt->id) {
            $this->createdPromptIds[] = $prompt->id;
        }

        return $prompt;
    }
}
