<?php

declare(strict_types=1);

namespace SF\functional\AI;

use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use Salesfloor\Services\AI\Agent\LanguageDetectionAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Service;
use Salesfloor\Services\Auth\TokenService;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

/**
 * Functional tests for LanguageDetectionAgent
 */
class LanguageDetectionAgentCest extends BaseFunctional
{
    private const TEST_ACCESS_TOKEN = 'test-access-token';

    /**
     * @var LanguageDetectionAgent
     */
    private $_languageDetectionAgent;

    /**
     * @var MockHandler
     */
    private $_mockHandler;

    /**
     * @var Client
     */
    private $_mockHttpClient;

    /**
     * @var TokenService|\PHPUnit\Framework\MockObject\MockObject
     */
    private $_mockTokenService;

    /**
     * @var Service|\PHPUnit\Framework\MockObject\MockObject
     */
    private $_mockAIService;

    /**
     * Set up the test environment
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function _before($I)
    {
        parent::_before($I);

        // Create mock handler for HTTP requests
        $this->_mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->_mockHandler);
        $this->_mockHttpClient = new Client(['handler' => $handlerStack]);

        // Create mock token service
        $this->_mockTokenService = $I->createMock(TokenService::class);
        $this->_mockTokenService
            ->method('getGoogleAccessToken')
            ->willReturn(self::TEST_ACCESS_TOKEN);
    }

    /**
     * Create a fresh mock AI service and agent for each test
     *
     * @param FunctionalTester $I
     * @return void
     */
    private function setupMockAIService($I)
    {
        $this->_mockAIService = $I->createMock(Service::class);

        $this->_languageDetectionAgent = new LanguageDetectionAgent(
            $this->_mockAIService,
            $I->grabService('logger')
        );
    }

    /**
     * Test language detection with English content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageEnglish(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz abc 123';

        $mockResponse = json_encode([
            'detected_language' => 'en',
            'final_language' => 'en',
            'confidence' => 0.95,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => null,
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('en', $result['detected_language']);
        $I->assertEquals('en', $result['final_language']);
        $I->assertFalse($result['has_override']);
        $I->assertGreaterThan(0.5, $result['confidence']);
    }

    /**
     * Test language detection with French content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageFrench(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz abc 456';

        $mockResponse = json_encode([
            'detected_language' => 'fr',
            'final_language' => 'fr',
            'confidence' => 0.92,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => null,
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('fr', $result['detected_language']);
        $I->assertEquals('fr', $result['final_language']);
        $I->assertFalse($result['has_override']);
        $I->assertGreaterThan(0.5, $result['confidence']);
    }

    /**
     * Test language detection with Spanish content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageSpanish(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz abc 789';

        $mockResponse = json_encode([
            'detected_language' => 'es',
            'final_language' => 'es',
            'confidence' => 0.89,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => null,
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('es', $result['detected_language']);
        $I->assertEquals('es', $result['final_language']);
        $I->assertFalse($result['has_override']);
    }

    /**
     * Test language detection with override (French to English)
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithOverride(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz abc override';

        $mockResponse = json_encode([
            'detected_language' => 'fr',
            'final_language' => 'en',
            'confidence' => 0.95,
            'has_override' => true,
            'override_reason' => 'User explicitly requested English response',
            'locale_hint' => 'en-US',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('fr', $result['detected_language']);
        $I->assertEquals('en', $result['final_language']);
        $I->assertTrue($result['has_override']);
        $I->assertNotEmpty($result['override_reason']);
    }

    /**
     * Test language detection with Chinese content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageChinese(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz abc chinese';

        $mockResponse = json_encode([
            'detected_language' => 'zh',
            'final_language' => 'zh',
            'confidence' => 0.98,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => 'zh-CN',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('zh', $result['detected_language']);
        $I->assertEquals('zh', $result['final_language']);
        $I->assertFalse($result['has_override']);
        $I->assertEquals('zh-CN', $result['locale_hint']);
    }

    /**
     * Test language detection with context
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithContext(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz hello world';
        $context = [
            'user_locale' => 'fr-CA',
            'preferred_language' => 'fr',
            'market' => 'canada'
        ];

        $mockResponse = json_encode([
            'detected_language' => 'en',
            'final_language' => 'fr',
            'confidence' => 0.85,
            'has_override' => true,
            'override_reason' => 'User preference and locale context',
            'locale_hint' => 'fr-CA',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content, $context);

        $I->assertEquals('en', $result['detected_language']);
        $I->assertEquals('fr', $result['final_language']);
        $I->assertTrue($result['has_override']);
        $I->assertEquals('fr-CA', $result['locale_hint']);
    }

    /**
     * Test language detection with empty content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithEmptyContent(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'abc';

        $mockResponse = json_encode([
            'detected_language' => 'en',
            'final_language' => 'en',
            'confidence' => 0.5,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => null,
            'detection_method' => 'fallback'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('en', $result['detected_language']);
        $I->assertEquals('en', $result['final_language']);
        $I->assertEquals(0.5, $result['confidence']);
    }

    /**
     * Test language detection with mixed languages
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithMixedContent(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz mixed text';

        $mockResponse = json_encode([
            'detected_language' => 'en',
            'final_language' => 'es',
            'confidence' => 0.88,
            'has_override' => true,
            'override_reason' => 'User requested Spanish response in mixed content',
            'locale_hint' => 'es-ES',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('en', $result['detected_language']);
        $I->assertEquals('es', $result['final_language']);
        $I->assertTrue($result['has_override']);
    }

    /**
     * Test language detection with German content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageGerman(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz abc german';

        $mockResponse = json_encode([
            'detected_language' => 'de',
            'final_language' => 'de',
            'confidence' => 0.94,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => 'de-DE',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('de', $result['detected_language']);
        $I->assertEquals('de', $result['final_language']);
        $I->assertFalse($result['has_override']);
    }

    /**
     * Test language detection with AI service error (falls back to rule-based)
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithAIError(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz error test';

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willThrowException(new AIAdapterException('AI service unavailable', 'vertexai'));

        // Should fall back to rule-based detection instead of throwing
        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertNotEmpty($result);
        $I->assertArrayHasKey('detected_language', $result);
        $I->assertArrayHasKey('detection_method', $result);
        $I->assertEquals('rule-based', $result['detection_method']);
    }

    /**
     * Test language detection with malformed AI response (falls back to rule-based)
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithMalformedResponse(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz malformed';

        // Malformed JSON response
        $aiResponse = new AIResponse(
            'invalid json response',
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        // Should fall back to rule-based detection on malformed response
        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertNotEmpty($result);
        $I->assertArrayHasKey('detected_language', $result);
        $I->assertArrayHasKey('detection_method', $result);
        $I->assertEquals('rule-based', $result['detection_method']);
    }

    /**
     * Test language detection with custom options
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageWithOptions(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz options';
        $options = [
            'temperature' => 0.5,
            'max_tokens' => 512
        ];

        $mockResponse = json_encode([
            'detected_language' => 'en',
            'final_language' => 'en',
            'confidence' => 0.87,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => null,
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content, [], $options);

        $I->assertEquals('en', $result['detected_language']);
        $I->assertEquals('en', $result['final_language']);
    }

    /**
     * Test getLanguageCode method
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testGetLanguageCode(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz simple';

        $mockResponse = json_encode([
            'detected_language' => 'en',
            'final_language' => 'en',
            'confidence' => 0.95,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => null,
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $languageCode = $this->_languageDetectionAgent->getLanguageCode($content);

        $I->assertEquals('en', $languageCode);
    }

    /**
     * Test hasLanguageOverride method
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testHasLanguageOverride(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        // Use content that will trigger rule-based override detection
        $content = 'please respond in english';

        $mockResponse = json_encode([
            'detected_language' => 'fr',
            'final_language' => 'en',
            'confidence' => 0.92,
            'has_override' => true,
            'override_reason' => 'User requested English response',
            'locale_hint' => 'en-US',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $hasOverride = $this->_languageDetectionAgent->hasLanguageOverride($content);

        $I->assertTrue($hasOverride);
    }

    /**
     * Test language detection with Japanese content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguageJapanese(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz japanese';

        $mockResponse = json_encode([
            'detected_language' => 'ja',
            'final_language' => 'ja',
            'confidence' => 0.97,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => 'ja-JP',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('ja', $result['detected_language']);
        $I->assertEquals('ja', $result['final_language']);
        $I->assertEquals('ja-JP', $result['locale_hint']);
    }

    /**
     * Test language detection with Portuguese content
     *
     * @param FunctionalTester $I
     * @return void
     */
    public function testDetectLanguagePortuguese(FunctionalTester $I)
    {
        $this->setupMockAIService($I);

        $content = 'xyz portuguese';

        $mockResponse = json_encode([
            'detected_language' => 'pt',
            'final_language' => 'pt',
            'confidence' => 0.91,
            'has_override' => false,
            'override_reason' => null,
            'locale_hint' => 'pt-BR',
            'detection_method' => 'ai-based'
        ]);

        $aiResponse = new AIResponse(
            $mockResponse,
            'gemini-2.0-flash-lite',
            'vertexai',
            [],
            [],
            []
        );

        $this->_mockAIService
            ->method('generateChatCompletion')
            ->willReturn($aiResponse);

        $result = $this->_languageDetectionAgent->detectLanguage($content);

        $I->assertEquals('pt', $result['detected_language']);
        $I->assertEquals('pt', $result['final_language']);
        $I->assertEquals('pt-BR', $result['locale_hint']);
    }
}
