<?php

namespace SF\functional\Auth;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Silex\Tests\FunctionalTest;

class AuthCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testLogin(FunctionalTester $I)
    {
        $this->app['configs']['retailer.prefix_numeric_usernames'] = true;

        $I->wantTo("validate a login");
        // The environment is tests, so autoprefix should use that env name.
        $login = 'sam_rep';
        $password = 'password';
        $params = [
            'user_login' => $login,
            'user_pass' => $password,
            'user_email' => "ppp+$<EMAIL>",
            'user_status' => '1',
        ];
        $I->directPOST($this->app, "reps?fields=specialties", $params);

        $I->assertStatusCode(200);

        $I->directPOST($this->app, "login", [
            'username' => $login,
            'password' => $password,
        ]);

        $I->assertStatusCode(200);
    }

    /** @group database_transaction */
    public function testLoginWithPrefix(FunctionalTester $I)
    {
        $this->app['configs']['retailer.prefix_numeric_usernames'] = true;

        $I->wantTo("validate a login that uses prefixing");
        // The environment is tests, so autoprefix should use that env name.
        $login = 'tests_19063';
        $password = 'password';
        $params = [
            'user_login' => $login,
            'user_pass' => $password,
            'user_email' => "ppp+$<EMAIL>",
            'user_status' => '1',
        ];
        $I->directPOST($this->app, "reps?fields=specialties", $params);

        $I->assertStatusCode(200);

        $I->directPOST($this->app, "login", [
            'username' => '19063',
            'password' => $password,
        ]);

        $I->assertStatusCode(200);
    }

    public function testLoginWithInvalidUserTypeRepMode(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;

        // change the test user to store mode
        $I->updateInDatabase('wp_users', [
            'type' => 'store'
        ], [
            'ID' => 1, // reggie
        ]);

        $I->directPOST($this->app, "login", [
            'username' => self::REGGIE_USER,
            'password' => self::REGGIE_PASSWORD,
        ]);

        $I->assertStatusCode(401);
    }

    public function testLoginWithInvalidUserTypeTeamMode(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;

        // change the test user to store mode
        $I->updateInDatabase('wp_users', [
            'type' => 'store'
        ], [
            'ID' => 1, // reggie
        ]);

        $I->directPOST($this->app, "login", [
            'username' => self::REGGIE_USER,
            'password' => self::REGGIE_PASSWORD,
        ]);

        $I->assertStatusCode(401);
    }
}
