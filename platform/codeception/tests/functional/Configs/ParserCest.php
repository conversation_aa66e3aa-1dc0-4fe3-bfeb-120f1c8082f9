<?php

namespace SF\functional;

use SF\FunctionalTester;

class ParserCest extends BaseFunctional
{
    private $notAvailableConfig = [
        'retailer.api_domain',
        'sf.import_unsubs.s3.provider',
        'retailer.cookie_domain',
    ];

    private $availableConfig = [
        "algolia.newretailer",
        "s3.bucket.encryption.enabled",
        "s3.bucket.encryption.type",
        "s3.bucket.signature",
        "retailer.application_start_date",
        "customerservice.can_forward",
        "customerservice.email",
        "retailer.services.channel.text.enabled",
        "retailer.rep_avatar_picture.random_default",
        "retailer.storepage_mode",
        "service.elasticsearch",
        "services.queue",
        "aws.queue.management_enabled",
        "services.queue.enabled",
        "aws.account.number",
        "gcp.queue.regions",
        "gcp.queue.management_enabled",
        "gcp.queue.max_messages",
        "retailer.sale_cookie_expire",
        "queue.byStore",
        "retailer.chat.routing.mode",
        "storefront.store_events.convert_event_times",
        "retailer.url.i18n",
        "retailer.num_top_picks",
        "retailer.num_deals",
        "retailer.num_product_shared",
        "retailer.trending_recommendations",
        "retailer.show.products_brand_name.backoffice",
        "retailer.pricePrecision",
        "retailer.pricePrecision.hide_empty_decimals",
        "retailer.social_networks",
        "retailer.facebook_app_id",
        "retailer.cookie_show_prefix",
        "retailer.services.hide_optional_phone_input",
        "retailer.services.appointment.is_enabled",
        "retailer.services.appointment.types",
        "retailer.services.carousel.show_names",
        "retailer.services.carousel.alternate_logo",
        "retailer.services.carousel.display_mode",
        "retailer.rep.display_name_tmpl",
        "mobile.retailer_can_browse_library",
        "mobile.retailer_can_share_from_browse_library",
        "mobile.retailer_has_tasks",
        "mobile.retailer_has_asset_management",
        "mobile.share_connected_services_enabled",
        "mobile.share_email_enabled",
        "mobile.share_facebook_enabled",
        "mobile.share_instagram_enabled",
        "mobile.share_pinterest_enabled",
        "mobile.products_modal_enabled",
        "mobile.retailer_can_change_retailer_id",
        "mobile.login.mdm.is_enabled",
        "mobile.login.store.is_enabled",
        "mobile.retailer.pricePrecision",
        "mobile.retailer.show.products_brand_name",
        "mobile.retailer.pricePrecision.hide_empty_decimals",
        "mysql.use_wpdb_slave",
        "retailer.email_domain",
        "retailer.instagram.account",
        "instagram.media.ttl",
        "storefront.instagram",
        "retailer.contacts.import_enabled",
        "stores.max_distance",
        "stores.max_distance.per_store.enable",
        "retailer.contacts.unsubscribe_automated.enabled",
        "retailer.contacts.unsubscribe_automated.days_threshold",
        "retailer.consent_required.mobile",
        "retailer.consent_required.desktop",
        "retailer.term_required.desktop",
        "retailer.emails.no_reply_address",
        "retailer.emails_exclusion_list.filter.enabled",
        "retailer.emails_exclusion_list.domain",
        "retailer.emails_filter.allow_email.any",
        "retailer.emails_filter.allow_email.domain_whitelist",
        "retailer.emails_filter.allow_email.address_suffix_whitelist",
        "retailer.clienteling.mode",
        "retailer.clienteling.customer.sync",
        "exporter.email.stats.daily.enabled",
        "retailer.clienteling.customer.limited_visibility.is_enabled",
        "retailer.clienteling.enabled.customer_stats",
        "retailer.clienteling.enabled.transactions",
        "retailer.clienteling.enabled.create_contact",
        "importer.rep_transaction.s3.path",
        "importer.rep_transaction.s3.filename_regexp",
        "importer.rep_transaction.ingestor",
        "importer.rep_transaction.fetcher",
        "sf.import_ci_customers.bulk_size",
        "sf.import_ci_transactions.bulk_size",
        "sf.import_ci_customers.c2c_split_size",
        "retailer.clienteling.matching.is_enabled",
        "retailer.clienteling.import.notification.emails",
        "messaging.text.enabled",
        "messaging.text.multiple-recipients.enabled",
        "messaging.text.multiple-recipients.max",
        "messaging.text.multiple-recipients.send_directly_limit",
        "importer.text_message_unsubscribe.enable",
        "importer.text_message_unsubscribe.s3.path",
        "importer.text_message_unsubscribe.s3.filename_regexp",
        "retailer.storefront.dynamic_content.is_active",
        "retailer.storefront.dynamic_content.max_num_posts",
        "retailer.storefront.dynamic_content.url",
        "retailer.i18n.is_enabled",
        "retailer.i18n.default_locale",
        "retailer.i18n.locale.fallback",
        "sf.i18n.locales",
        "importer.import-as-subscriber.email",
        "importer.import-as-subscriber.sms",
        "sf.task.automated.new_retailer_transaction_filtered.enabled",
        "sf.task.automated.new_retailer_transaction_filtered.days_search_back",
        "sf.task.automated.retailer_transaction_employee_assigned.enabled",
        "sf.task.automated.retailer_transaction_employee_assigned.days_search_back",
        "sf.task.automated.retailer_transaction_employee_assigned.emails_enabled",
        "sf.task.automated.retailer_transaction_employee_assigned.notifications_enabled",
        "sf.task.automated.retailer_transaction_employee_assigned.category",
        "sf.task.automated.retailer_transaction_employee_assigned.matching",
        "sf.task.automated.retailer_transaction_employee_assigned.max_per_owner",
        "sf.task.automated.new_retailer_transaction_filtered.max_per_owner",
        "sf.task.automated.new_retailer_transaction_filtered.any_store",
        "sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled",
        "sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back",
        "sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled",
        "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.enabled",
        "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.days_search_back",
        "sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner",
        "sf.task.automated.new_rep_transaction.enabled",
        "sf.task.automated.new_rep_transaction.days_search_back",
        "sf.task.automated.retailer_customer_stats_registry_event.enabled",
        "sf.task.automated.retailer_customer_stats_registry_followup.enabled",
        "sf.task.automated.new_rep_transaction.max_per_owner",
        "sf.task.automated.transaction.min",
        "sf.task.automated.transactions_distribution_by_stores.is_enabled",
        "sf.task.automated.transactions_distribution_by_stores.days_search_back",
        "sf.task.automated.transactions_distribution_by_stores.emails_enabled",
        "sf.task.automated.transactions_distribution_by_stores.notifications_enabled",
        "sf.task.automated.transactions_distribution_by_stores.category",
        "sf.task.automated.transactions_distribution_by_stores.max_per_owner",
        "sf.task.automated.transactions_distribution_by_stores.distance_radius",
        "sf.task.automated.cancelled_transaction_follow_up.enabled",
        "sf.task.automated.cancelled_transaction_follow_up.category",
        "sf.task.automated.cancelled_transaction_follow_up.emails_enabled",
        "sf.task.automated.cancelled_transaction_follow_up.notifications_enabled",
        "sf.task.automated.cancelled_transaction_follow_up.max_daily_threshold_per_associate",
        "sf.task.automated.cancelled_transaction_follow_up.import_day_threshold",
        "sf.task.automated.cancelled_transaction_follow_up.task_creation_criteria",
        "retailer.online_store_id",
        "inventory.lookup.retailer.online_store_name",
        "retailer.customer_tags.is_enabled",
        "retailer.customer_tags.is_read_only",
        "retailer.onboarding.step.add_contacts",
        "mobile_ios_appid",
        "mobile_ios_channel",
        "mobile_android_appid",
        "mobile_android_channel",
        "retailer.shoppage.tracking.custom_params",
        "retailer.sidebar.v3.enabled",
        "retailer.sidebar.v3.variant",
        "retailer.sidebar.v3.landingPosition.enabled",
        "retailer.sidebar.v3.landingPosition.horizontalPosition",
        "retailer.services.termconditions.enabled",
        "retailer.chat.option.video-chat",
        "retailer.chat.option.video-chat.2ways",
        "retailer.store.close_hour",
        "retailer.store.open_hour",
        "retailer.store.default_timezone",
        "retailer.chat.option.eject_after_hours",
        "retailer.corporate-email.required",
        "retailer.multiple_email_templates.enabled",
        "retailer.stand_down.forwarded_to_cs.is_enabled",
        "cleanup_operations.text_message.enabled",
        "exporter.transactions.daily.enabled",
        "exporter.transactions.daily.start_days_before",
        "exporter.transactions.daily.end_days_before",
        "exporter.activitySummary.rep.daily.enabled",
        "exporter.activitySummary.rep.daily.start_days_before",
        "exporter.activitySummary.rep.daily.end_days_before",
        "exporter.contacts.daily.enabled",
        "exporter.messages.daily.enabled",
        "exporter.share.email.daily.enabled",
        "exporter.lookbook.daily.enabled",
        "importer.reps.enabled",
        "importer.reps.s3.path",
        "importer.reps.s3.filename_regexp",
        "retailer.prefix_numeric_usernames",
        "elasticsearch.max_bulk_size",
        "retailer.shop_feed.enabled",
        "messaging-json-api.enabled",
        "products.expanded_variants.enabled",
        "products.expanded_variants.empty_attribute_position_holder",
        "importer.product.use_new_importer",
        "importer.product.s3.filename_regexp",
        "importer.product.s3.path",
        "importer.product.fetcher.direct.type",
        "autoresponder.ttl",
        "sidebar.tracking.enabled",
        "importer.c2c.s3.path",
        "unknown_incoming_email.tracking_and_reject.is_enabled",
        "notifications.late_messages.enabled",
        "notifications.late_requests.enabled",
        "notifications.near_appointments.enabled",
        "sidebar.pushing.interval",
        "gcp.queue.queue_ack_deadline_seconds",
        "gcp.queue.ack_deadline_seconds",
        "services.queue.ack-extend",
        "sidebar.pushing.bulk.size",
        "sidebar.message.hide",
        "importer.contacts.tag_delimiter",
        "assets.vendor_hash",
        "alerts.failed_import.emails",
        "alerts.salesfloor_admin_changed.emails",
        "inventory.lookup.is_enabled",
        "inventory.lookup.retailer_default_location.type",
        "inventory.lookup.retailer_default_location.id",
        "products.expanded_variants.priority_badge.enabled",
        "reps.find_reps.sort.name_alphabetically",
        "importer.rep_transactions.enabled",
        "importer.customer_attributes.s3.path",
        "importer.customer_attributes.s3.filename_regexp",
        "importer.attributes.validation",
        "retailer.services.appointment_management.is_enabled",
        "retailer.services.appointment_management.notify_without_consent",
        "retailer.services.appointment_management.all_customer.is_enabled",
        "retailer.services.appointment.save_to_device.is_enabled",
        "retailer.services.appointment.all.is_enabled",
        "retailer.services.appointment_reassignment.is_enabled",
        "security.pii.crypto.enabled",
        "retailer.clienteling.customers.communication.blackout.is_enabled",
        "retailer.clienteling.customers.communication.blackout.period",
        "mfa.authentication.is_enabled",
        "mfa.authentication.type",
        "mfa.authentication.otp.valid_period",
        "mfa.authentication.valid_window",
        "mfa.authentication.otp.length",
        "retailer.specialties.is_enabled",
        "retailer.specialties.can_select",
        "retailer.specialties.is_required",
        "retailer.password_update.is_enabled",
        "service.pushnotification",
        "fcm.key.secret_file",
        "fcm.version",
        "fcm.verification.url",
        "services.metrics",
        "redirect_whitelist.is_enabled",
        "redirect_whitelist.slack_notice.is_enabled",
        "redirect_whitelist.slack_notice.channel",
        "redirect_whitelist.domains.global",
        "redirect_whitelist.domains",
        "retailer.multi_domains.is_enabled",
        "retailer.multi_domains.short_name",
        "retailer.multi_domains.locale",
        "retailer.outfits.is_enabled",
        "retailer.unsubscribe_link",
        "retailer.sso.is_enabled",
        "retailer.chat.find_nearby_stores",
        "retailer.chat.find_nearby_stores.max_distance",
        "retailer.chat_availability_status.sidebar.enabled",
        "alerts.operations.emails",
        "chat.num_sec_before_heartbeat",
        "chat.request.maxduration",
        "chat.rep_pictures.number",
        "chat.rep_pictures.default",
        "chat.rep_pictures.blacklist",
        "retailer.associate",
        "service.live",
        "retailer.services.appointment.duration",
        "onboarding.choose_alias",
        "storefront.menu.isdynamic",
        "mobile.product_barcode_scanner",
        "mobile.barcode_scanner.contacts.enabled",
        "mobile.retailer_has_products_feed",
        "retailer.services.policy",
        "retailer.filter_new_leads_by_specialty",
        "retailer.has_personal_shopper",
        "products.defaultcomments.storemode",
        "products.defaultcomments.repmode",
        "product.panels.new-arrivals.nominated-products",
        "product.panels.top-picks.nominated-products",
        "mobile.specificities",
        "retailer.i18n.products.is_default_locale_fallback",
        "sf.task.automated.enabled",
        "sf.task.automated.new_retailer_transaction_filtered.emails_enabled",
        "sf.task.automated.new_retailer_transaction_filtered.notifications_enabled",
        "sf.task.automated.new_retailer_transaction_filtered.category",
        "sf.task.automated.retailer_customer_soon_to_lapse_filtered.category",
        "sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.category",
        "sf.task.automated.new_rep_transaction.category",
        "retailer.sidebar.v3.mode",
        "retailer.chat.option.transfer-to-cs",
        "retailer.chat.option.transfer-to-cs.text.required",
        "retailer.chat.option.transfer-to-cs.redirect.url",
        "retailer.virtual.option.video-chat",
        "retailer.virtual.option.video-chat.2ways",
        "boldchat.enabled",
        "boldchat.username",
        "boldchat.password",
        "exporter.request.daily.enabled",
        "exporter.request.daily.start_days_before",
        "exporter.request.daily.end_days_before",
        "exporter.sms.daily.enabled",
        "exporter.sms.daily.start_days_before",
        "exporter.sms.daily.end_days_before",
        "exporter.livechat.daily.enabled",
        "exporter.livechat.daily.start_days_before",
        "exporter.livechat.daily.end_days_before",
        "exporter.live_chat_metrics.rep.daily.enabled",
        "exporter.live_chat_metrics.rep.daily.start_days_before",
        "exporter.live_chat_metrics.rep.daily.end_days_before",
        "exporter.live_chat_metrics.store.daily.enabled",
        "exporter.live_chat_metrics.store.daily.start_days_before",
        "exporter.live_chat_metrics.store.daily.end_days_before",
        "exporter.activitySummary.store.daily.enabled",
        "exporter.activitySummary.store.daily.start_days_before",
        "exporter.activitySummary.store.daily.end_days_before",
        "exporter.sidebar_metric.daily.enabled",
        "exporter.sidebar_metric.daily.start_days_before",
        "exporter.sidebar_metric.daily.end_days_before",
        "retailer.specialties.has_holidays",
        "retailer.specialties.exclude",
        "services.metrics.create_alarms",
        "retailer_customer.associate_relationships"
    ];

    /** @group database_transaction */
    public function testParserOutput(FunctionalTester $I)
    {
        // $scenario->skip('This test is skipped');

        $configs = $this->app['service.config.parser']->parseDefaultConfig();

        $currentConfigNames = array_column($configs, 'name');

        $notDefined = array_diff($currentConfigNames, $this->availableConfig);
        // I'm not sure about the relevance of this test, as it seems to act like another log of configs.
        // we may consider a better way to check that the config object is being retrieved by the parser
        // without re-documenting configs.
        $I->assertCount(61, $notDefined, sprintf('[%s] are not in the allowed list', implode(',', $notDefined)));
    }
}
