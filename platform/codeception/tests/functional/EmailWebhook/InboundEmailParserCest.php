<?php

declare(strict_types=1);

namespace SF\functional\EmailWebhook;

use Aws\S3\S3Client;
use Psr\Log\LoggerInterface;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Codeception\Util\Stub;
use Codeception\Util\Fixtures;
use Salesfloor\Models\RejectedEmail;
use Salesfloor\Services\Image\Image;
use Salesfloor\Services\CDN\CloudinaryImage;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Salesfloor\Services\IncomingMail\InboundDomainService;
use Salesfloor\Services\Mail\SendgridClientMaster;

class InboundEmailParserCest extends BaseFunctional
{
    public function __construct()
    {
        parent::__construct();
    }

    /** @group database_transaction */
    public function testCanParseThreadRequest(FunctionalTester $I)
    {
        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('threadRequest');

        $this->insertFixtureGroup($I, 'webhook-customers');

        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $I->assertStatusCode(204);

        $I->seeInDatabase('sf_messages', [
            'user_id' => 1,
            'customer_id' => 2204,
            'title' => 'This is a subject',
            'message' => 'This is the body text!',
        ]);
    }

    /** @group database_transaction */
    public function testCanParseCustomerRequest(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'webhook-customers');

        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('customerRequest');


        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $I->assertStatusCode(204);

        $I->seeInDatabase('sf_messages', [
            'user_id' => 1,
            'customer_id' => 2204,
            'message' => 'This customer request',
            'title' => 'Hello do you have yellow hats',
        ]);
    }

    /** @group refresh_database */
    public function testTrackEmailForGroupTask(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupTasks');

        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('groupTaskRequest');

        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $groupTasks = $I->grabRowsFromDatabase('sf_group_tasks');
        $expected = [
            [
                "id" => "1",
                "store_id" => "1003",
                "title" => "Test Title",
                "details" => "Test Detail",
                "status" => "unresolved",
                "customer_id" => "1234",
                "preferred_user_id" => "255",
                "start_date" => "2023-04-01 00:00:23",
                "reminder_date" => "2023-03-01 00:01:02",
                "auto_dismiss_date" => "2024-01-01 00:01:02",
                "suggested_subject_line" => "Test Subject Line",
                "suggested_copy" => "Test Suggested Copy",
            ],
        ];
        $this->validateArray($expected, $groupTasks);

        $groupTaskActivities = $I->grabRowsFromDatabase('sf_group_task_activities');

        $expected = [
            [
                "id" => "1",
                "group_task_id" => "1",
                "user_id" => "255",
                "action" => "REPLY-TO-AN-EMAIL",
                "title" => "Group task subject",
                "details" => "This customer request",
            ],
        ];
        $this->validateArray($expected, $groupTaskActivities);
    }

    /**
     * We have some scenarios when the reply to
     * an existing thread comes from a unknown email
     * In that case we need to add the customer
     *
     * @param FunctionalTester $I
     * @return void
     *
     * @group refresh_database
     */
    public function testOldThreadForNewCustomer(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'webhook-customers');

        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('threadFromNonCustomer');

        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $I->assertStatusCode(204);

        $I->seeInDatabase('sf_customer', [
            "user_id" => 1,
            "email" => "<EMAIL>",
            "name" => "Frank Moore",
        ]);
    }

    /**
     *
     * @param FunctionalTester $I
     * @return void
     * @group refresh_database
     */
    public function newAppointmentRequest(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'new-ar-request');

        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('newAppointmentRequest');


        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $I->assertStatusCode(204);

        $I->seeInDatabase('sf_messages', [
            'user_id' => 266,
            'customer_id' => 6464,
            'message' => 'New time for appointment 12:05',
            'title' => 'New appointment time',
        ]);
    }

    /**
     *
     * @param FunctionalTester $I
     * @return void
     * @group refresh_database
     */
    public function canFilterOldThreads(FunctionalTester $I)
    {
        $request = Fixtures::get('replyToOldThread');
        $this->insertFixtureGroup($I, 'new-ar-request');

        $headers = new HeaderBag(['User-Agent' => 'Sendlib']);

        $inboundEmailParser = $this->app['service.inbound_email_parser'];
        $incomingEmails = $inboundEmailParser->parseWebhookRequest($request, $headers);
        $parsed = $inboundEmailParser->parseIncomingEmailEvent($incomingEmails[0]);

        $I->assertEquals(
            "Hello, Yes I would like to order but let me know how long For the\ndelivery to arrive. Thanks, Kurt. ",
            $parsed->getFilteredContent()
        );
    }

     /**
     *
     * @param FunctionalTester $I
     * @group refresh_database
     */
    public function testTrackEmailUnknownCustomerTeamMode(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['unknown_incoming_email.tracking_and_reject.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'IncomingEmailTeamMode');

        $msg = [
            'subject'    => 'Email Test',
            'text'       => "this is the text",
            'html'       => '<div>text</div>',
            'from_email' => '<EMAIL>',
            'from_name'  => 'Preet Acharya',
            'to_email'   => '<EMAIL>',
        ];

        $mandrill_events = '[{"event":"inbound","msg":{"text":' . json_encode($msg['text']) .
            ',"html":' . json_encode($msg['html']) . ',"from_email":' .
            json_encode($msg['from_email']) .
            ',"from_name":' . json_encode($msg['from_name']) .
            ',"to":[[' . json_encode($msg['to_email']) .
            ',null]],"subject":' . json_encode($msg['subject']) . '}}]';

        $headers = ['User-Agent' => 'Mandrill-Webhook'];
        $I->setHeaders($headers);

        $I->directPost(
            $this->app,
            "mailhook/parser",
            ['mandrill_events' => $mandrill_events]
        );

        $I->seeInDatabase('sf_messages', [
            'from_type' => 'customer',
            'from_email' => $msg['from_email'],
            'from_name' => $msg['from_name'],
            'title' => $msg['subject'],
            'message' => 'text'
        ]);
    }

    /**
     * @param FunctionalTester $I
     * @group refresh_database
     */
    public function testTrackEmailUnknownCustomerRepMode(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['unknown_incoming_email.tracking_and_reject.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'IncomingEmailRepMode');
        $msg = [
            'subject'    => 'Email Test',
            'text'       => "this is the text",
            'html'       => '<div>text</div>',
            'from_email' => '<EMAIL>',
            'from_name'  => 'Preet Acharya',
            'to_email'   => '<EMAIL>',
        ];

        $mandrill_events = '[{"event":"inbound","msg":{"text":' . json_encode($msg['text']) .
            ',"html":' . json_encode($msg['html']) . ',"from_email":' .
            json_encode($msg['from_email']) .
            ',"from_name":' . json_encode($msg['from_name']) .
            ',"to":[[' . json_encode($msg['to_email']) .
            ',null]],"subject":' . json_encode($msg['subject']) . '}}]';

        $headers = ['User-Agent' => 'Mandrill-Webhook'];
        $I->setHeaders($headers);

        $I->directPost(
            $this->app,
            "mailhook/parser",
            ['mandrill_events' => $mandrill_events]
        );

        $I->seeNumRecords(1, 'sf_messages');
        $I->seeInDatabase('sf_messages', [
            'from_type' => 'customer',
            'from_email' => $msg['from_email'],
            'from_name' => $msg['from_name'],
            'title' => $msg['subject'],
            'message' => 'text',
            'locale' => 'en_US'
        ]);
    }

    /**
     * @param FunctionalTester $I
     * @group refresh_database
     */
    public function testTrackRejectedEmailUnknownCustomer(FunctionalTester $I)
    {
        $this->app['configs']['unknown_incoming_email.tracking_and_reject.is_enabled'] = true;

        $msg = [
            'subject'    => 'Reject Email',
            'text'       => "this is the text",
            'html'       => '<div>text</div>',
            'from_email' => '<EMAIL>',
            'from_name'  => 'Name',
            // must be the email of an existing user
            // If a matching rep email doesn't exist, we'll reject it.
            'to_email'   => '<EMAIL>',
        ];

        $mandrill_events = '[{"event":"inbound","msg":{"text":' . json_encode($msg['text']) .
            ',"html":' . json_encode($msg['html']) . ',"from_email":' .
            json_encode($msg['from_email']) .
            ',"from_name":' . json_encode($msg['from_name']) .
            ',"to":[[' . json_encode($msg['to_email']) .
            ',null]],"subject":' . json_encode($msg['subject']) . '}}]';

        $headers = ['User-Agent' => 'Mandrill-Webhook'];
        $I->setHeaders($headers);

        $I->directPost(
            $this->app,
            "mailhook/parser",
            ['mandrill_events' => $mandrill_events]
        );

        $I->seeNumRecords(1, 'sf_rejected_emails');
        $I->seeInDatabase('sf_rejected_emails', [
            'subject' => $msg['subject'],
            'from_email' => $msg['from_email'],
            'to_email' => $msg['to_email'],
            'message' => '<div>text</div>',
            'reject_reason' => RejectedEmail::REJECTED_UNKNOWN_CUSTOMER
        ]);
    }

    /**
     * @group refresh_database
     */
    public function testParseEmailToCustomerDestination(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'new-customer-101');

        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('newCustomerRequest');


        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $I->assertStatusCode(204);

        $I->seeInDatabase('sf_messages', [
            'user_id' => 266,
            'customer_id' => 101,
            'message' => 'A new text for customer 101',
            'title' => 'A message for 101',
        ]);
    }

    public function testDropEmailsWithHighSpamScore(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'request_from_spam');

        $headers = ['User-Agent' => 'Sendlib'];
        $request = Fixtures::get('spamRequest');

        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );
        // we send OK even when SPAM to disable Sengrid to resend
        $I->assertStatusCode(204);
        $I->seeNumRecords(0, 'sf_messages');
    }

    public function testEmailWithAttachments(FunctionalTester $I)
    {
        $this->app['service.image'] = Stub::construct(
            Image::class,
            [
                $this->app['configs'],
                Stub::makeEmpty(LoggerInterface::class),
                Stub::make(S3Client::class, [
                    'upload' => function () use ($I) {
                        return new Class () {
                            public function get(string $objectUrl)
                            {
                                return 'https://www.salesfloor.net';
                            }
                        };
                    },
                    'getBucket' => function (): string {
                        return 'bucketName';
                    },
                ]),

                Stub::make(CloudinaryImage::class, [
                    'getProxyUrl' => function (string $objectUrl) use ($I) {
                        $I->assertEquals('https://www.salesfloor.net', $objectUrl);
                        return 'https://cdn.salesfloor.net';
                    },
                ]),
                $this->app['service.moderation.image']
            ]
        );

        $payload = Fixtures::get('attachmentsRequest');
        $this->insertFixtureGroup($I, 'webhook-customers');

        $filePath = 'tests/functional/EmailWebhook/attachments/wheel.jpeg';
        if (!file_exists($filePath)) {
            throw new \Exception('No such file');
        }
        $file = new UploadedFile(
            $filePath,
            basename($filePath),
            mime_content_type($filePath),
            UPLOAD_ERR_OK,
            true
        );

        $headers = ['User-Agent' => 'Sendlib', 'Content-type' => 'multipart/form-data'];
        $I->setHeaders($headers);
        $I->directPost($this->app, 'mailhook/parser', $payload, 'multipart/form-data', [$file]);

        $I->seeInDatabase('sf_email_attachments', ['url' => 'https://www.salesfloor.net']);
    }

    /**
     * The validation event is called when new Webmail hook is
     * being changed on the Mandrill platform
     * @param \SF\FunctionalTester $I
     * @return void
     */
    public function testMandrillValidationEvent(FunctionalTester $I)
    {
        $headers = ['User-Agent' => 'Mandrill-Webhook'];

        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            ['mandrill_events' => ''],
            $headers
        );

        $I->assertStatusCode(204);
    }

    /** @group database_transaction */
    public function testMandrillIncomingEvents(FunctionalTester $I)
    {
        $headers = ['User-Agent' => 'Mandrill-Webhook'];
        $request = Fixtures::get('mandrillEvents');
        $this->insertFixtureGroup($I, 'new-customer-mandrill');

        $I->setHeaders($headers);
        $I->directPost(
            $this->app,
            "mailhook/parser",
            $request,
            $headers
        );

        $I->assertStatusCode(204);

        $I->seeInDatabase('sf_messages', [
            'user_id' => 7345,
            'customer_id' => 3,
            'from_email' => '<EMAIL>',
            'title' => 'Re: test',
        ]);
    }

    public function testEmailNotificationMethodForSendgrid(FunctionalTester $I)
    {
        $sendgridClientSubuser = \Codeception\Stub::make(
            SendgridClientMaster::class,
            [
                'getWebhookParseSettings' => function () {
                    return [ 'result' => [
                        [
                            'hostname' => 'store.johnnywas.com',
                            'url' => 'https://api.store.johnnywas.com/mailhook/parser',
                            'spam_check' => false,
                            'send_raw' => false,
                        ],
                        [
                            'hostname' => 'storejohnnywas.com',
                            'url' => 'https://api.store.johnnywas.com/mailhook/parser',
                            'spam_check' => false,
                            'send_raw' => false,
                        ],
                    ]];
                },
                'getAllSubusers' => function () {
                    return [
                        ['username' => 'sf-test-dev', 'email' => '<EMAIL>'],
                        ['username' => 'sf-test-box03', 'email' => '<EMAIL>'],
                        ['username' => 'sf-test-prd', 'email' => '<EMAIL>'],
                    ];
                }
                ]
        );

        $mockedService = \Codeception\Stub::construct(
            InboundDomainService::class,
            [
                $this->app['logger'],
                $this->app['configs'],
                $sendgridClientSubuser,
                $this->app['service.mandrill'],
                $this->app['predis'],
                $this->app['sf.mail.client']->setProvider('sendgrid')->getProvider()
            ],
            [
                'resolveMXRecord' => function () {
                    return  "mx.sendgrid.net";
                },
            ]
        );

        $I->assertEquals('sendgrid', $mockedService->resolveMailProvider());
        $I->assertEquals(['store.johnnywas.com', 'storejohnnywas.com'], $mockedService->getSendgridParsedDomain());
        $I->assertTrue($mockedService->hasDomain('storejohnnywas.com', false));

        // Test with enabled cache
        $cache = $this->app['predis'];
        $I->assertTrue($mockedService->hasDomain('storejohnnywas.com', true));
        $I->assertEquals(
            ['store.johnnywas.com', 'storejohnnywas.com'],
            json_decode($cache->get($this->app['configs']['cache.prefix'] . ':inbound_parsed_domains'), true)
        );
    }
}
