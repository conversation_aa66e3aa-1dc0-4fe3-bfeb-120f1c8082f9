<?php

namespace SF\functional\Tasks;

use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\CancelledTransactionFollowUp;
use SF\FunctionalTester;

class CancelledTransactionAutomatedTaskCest extends TaskFunctional
{
    const TASK_ID = Task::AUTOMATED_TYPE_TRANSACTION_CANCELLED_FOLLOW_UP;
    const REP_MODE_FIXTURE = 'sf-cancelled-transactions-rep-mode';
    const TEAM_MODE_FIXTURE = 'sf-cancelled-transactions-team-mode';

    /** @group database_transaction */
    public function testNoTaskCreatedWhenFeatureDisabled(FunctionalTester $I)
    {
        $this->disableFeature();

        $threshold = 2;
        $this->setMaxDailyThresholdPerAssociate($threshold);

        $this->insertFixtureGroup($I, static::REP_MODE_FIXTURE);
        $tasksCreated = $this->runTaskScanner();

        $zeroTasksCreated = 0;
        $I->assertEquals($zeroTasksCreated, $tasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testExpectedTasksAreCreatedWhenImportThresholdIs1Day(FunctionalTester $I)
    {
        $this->enableFeature();
        $this->enableCustomerNotRequiredForTaskCreation();

        $maxThresholdPerAssociate = 5;
        $this->setMaxDailyThresholdPerAssociate($maxThresholdPerAssociate);
        $this->insertFixtureGroup($I, static::REP_MODE_FIXTURE);

        $daysThreshold = 1;
        $expectedTasksCreated = 5;
        $this->setImportDayThreshold($daysThreshold);
        $actualTasksCreated = $this->runTaskScanner();
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testExpectedTasksAreCreatedWhenImportThresholdIs3Days(FunctionalTester $I)
    {
        $this->enableFeature();
        $this->enableCustomerNotRequiredForTaskCreation();

        $maxThresholdPerAssociate = 5;
        $this->setMaxDailyThresholdPerAssociate($maxThresholdPerAssociate);
        $this->insertFixtureGroup($I, static::REP_MODE_FIXTURE);

        $daysThreshold = 3;
        $expectedTasksCreated = 3;
        $this->setImportDayThreshold($daysThreshold);
        $actualTasksCreated = $this->runTaskScanner();
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testExpectedTasksAreCreatedForExistingCustomers(FunctionalTester $I)
    {
        $this->enableFeature();
        $this->enableCustomerRequiredForTaskCreation();
        $fixtureMode = static::REP_MODE_FIXTURE;

        $actualTasksCreated = $this->_basicScanner($I, $fixtureMode);

        $expectedTasksCreated = 4;
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
        $I->dontSeeInDatabase('sf_customer', [
            'email' => Fixture::CUSTOMER_EMAIL_ANONYMOUS
        ]);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testNonExistingCustomerAreCreated(FunctionalTester $I)
    {
        $this->enableFeature();
        $this->enableCustomerNotRequiredForTaskCreation();
        $fixtureMode = static::REP_MODE_FIXTURE;

        $actualTasksCreated = $this->_basicScanner($I, $fixtureMode);

        $expectedTasksCreated = 5;
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
        $I->seeInDatabase('sf_customer', [
            'email' => Fixture::CUSTOMER_EMAIL_ANONYMOUS
        ]);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testTasksAreProperlyImportedAccordingWhenAssociateThresholdIs3(FunctionalTester $I)
    {
        $this->enableFeature();
        $this->enableCustomerNotRequiredForTaskCreation();

        $daysThreshold = 1;
        $maxThresholdPerAssociate = 3;
        $expectedTasksCreated = 3;

        $this->setMaxDailyThresholdPerAssociate($maxThresholdPerAssociate);
        $this->insertFixtureGroup($I, static::REP_MODE_FIXTURE);

        $this->setImportDayThreshold($daysThreshold);
        $actualTasksCreated = $this->runTaskScanner();
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testTasksAreProperlyImportedAccordingWhenAssociateThresholdIs2(FunctionalTester $I)
    {
        $this->enableFeature();
        $this->enableCustomerNotRequiredForTaskCreation();

        $daysThreshold = 1;
        $maxThresholdPerAssociate = 2;
        $expectedTasksCreated = 2;

        $this->setMaxDailyThresholdPerAssociate($maxThresholdPerAssociate);
        $this->insertFixtureGroup($I, static::REP_MODE_FIXTURE);

        $this->setImportDayThreshold($daysThreshold);
        $actualTasksCreated = $this->runTaskScanner();
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testTasksAreImportedProperlyForTeamMode(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;

        $this->enableFeature();
        $this->enableCustomerNotRequiredForTaskCreation();
        $fixtureMode = static::TEAM_MODE_FIXTURE;

        $actualTasksCreated = $this->_basicScanner($I, $fixtureMode);

        $expectedTasksCreated = 5;
        $I->assertEquals($expectedTasksCreated, $actualTasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @param string           $fixtureMode
     *
     * @return mixed
     */
    protected function _basicScanner(FunctionalTester $I, string $fixtureMode)
    {
        $daysThreshold = 1;
        $maxThresholdPerAssociate = 5;
        $this->setMaxDailyThresholdPerAssociate($maxThresholdPerAssociate);
        $this->insertFixtureGroup($I, $fixtureMode);
        $this->setImportDayThreshold($daysThreshold);
        $actualTasksCreated = $this->runTaskScanner();
        return $actualTasksCreated;
    }

    private function disableFeature()
    {
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.enabled'] = false;
    }

    private function enableFeature()
    {
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.enabled'] = true;
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.notifications_enabled'] = true;
    }

    private function setMaxDailyThresholdPerAssociate(int $threshold)
    {
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.max_daily_threshold_per_associate'] = $threshold;
    }

    private function setImportDayThreshold(int $daysAgo)
    {
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.import_day_threshold'] = $daysAgo;
    }

    private function enableCustomerRequiredForTaskCreation()
    {
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.task_creation_criteria'] =
            CancelledTransactionFollowUp::CRITERIA_CUSTOMER_REQUIRED;
    }

    private function enableCustomerNotRequiredForTaskCreation()
    {
        $this->app['configs']['sf.task.automated.cancelled_transaction_follow_up.task_creation_criteria'] =
            CancelledTransactionFollowUp::CRITERIA_CUSTOMER_NOT_REQUIRED;
    }
}
