<?php

namespace SF\functional\Tasks;

use Codeception\Util\Fixtures;
use Salesfloor\Models\Task as TaskModel;

class Fixture extends BaseTaskFixture
{
    public const STORE_USER_1       = 30;
    public const STORE_1_RETAILERID = 1004;
    public const STORE_2_RETAILERID = 1006;
    public const STORE_USER_2       = 31;
    public const USER_1_STORE_1     = 20;
    public const USER_2_STORE_1     = 23;
    public const USER_1_STORE_2     = 21;
    public const USER_2_STORE_2     = 22;
    public const CUSTOMER_1_USER_1_SUBSCRIBED  = 40;
    public const CUSTOMER_2_USER_1  = 41;
    public const CUSTOMER_FOR_STORE_USER_2 = 32;
    public const NON_EXISTING_CUSTOMER = 99999;
    public const CUSTOMER_EMAIL_ANONYMOUS = '<EMAIL>';

    public function customerinsights()
    {
        Fixtures::add('ci-transactions-rep-mode', array_merge(
            $this->getRepModeCustomers(),
            $this->getInsightsNewTransactions(),
            $this->getRepModeUsers(),
            $this->getStores()
        ));

        Fixtures::add('ci-transactions-team-mode', array_merge(
            $this->getTeamModeCustomers(),
            $this->getInsightsNewTransactions(),
            $this->getTeamModeUsers(),
            $this->getStores()
        ));

        Fixtures::add('ci-transactions-with-employee-assigned', array_merge(
            $this->getTransactionCustomers(),
            $this->getCustomerAndRetailerCustomerMeta(),
            $this->getInsightsNewTransactionsWithEmployeeAssigned(),
            $this->getRepModeUsersWithEmployeeId(),
            $this->getStores(),
            $this->getCustomerTags()
        ));

        Fixtures::add(
            'ci-transactions-with-employee-assigned-multiple-days',
            array_merge(
                $this->getMoreTransactionMultipleDays(),
            )
        );
    }

    public function retailercustomerevent()
    {
        Fixtures::add('retailer-customer-event-rep-mode', array_merge(
            $this->getRepModeCustomers(),
            $this->getCustomerEventsRepMode(),
            $this->getRepModeUsers(),
            $this->getStores(),
        ));
        Fixtures::add('retailer-customer-event-team-mode', array_merge(
            $this->getTeamModeCustomers(),
            $this->getCustomerEventsTeamMode(),
            $this->getTeamModeUsers(),
            $this->getStores(),
        ));
    }

    public function sftransaction()
    {
        Fixtures::add('sf-cancelled-transactions-rep-mode', array_merge(
            $this->getRepModeCustomers(),
            $this->getRepModeUsers(),
            $this->getStores(),
            $this->getInsightsNewTransactions(),
            $this->getSfCancelledTransactions()
        ));

        Fixtures::add('sf-cancelled-transactions-team-mode', array_merge(
            $this->getTeamModeCustomers(),
            $this->getTeamModeUsers(),
            $this->getStores(),
            $this->getInsightsNewTransactions(),
            $this->getSfTeamModeCancelledTransactions()
        ));

        Fixtures::add('sf-transactions-rep-mode', array_merge(
            $this->getRepModeCustomers(),
            $this->getRepModeUsers(),
            $this->getStores(),
            $this->getInsightsNewTransactions(),
            $this->getRepModeSfTransactions()
        ));

        Fixtures::add('sf-transactions-team-mode', array_merge(
            $this->getTeamModeCustomers(),
            $this->getTeamModeUsers(),
            $this->getStores(),
            $this->getInsightsNewTransactions(),
            $this->getTeamModeSfTransactions()
        ));
    }

    public function soontolapsetransaction()
    {
        Fixtures::add('ci-transactions-soon-to-lapse-rep-mode', array_merge(
            $this->getRepModeCustomers(),
            $this->getRepModeUsers(),
            $this->getStores(),
            $this->getInsightsSoonToLapseTransactions()
        ));

        Fixtures::add('ci-transactions-soon-to-lapse-rep-mode2', array_merge(
            $this->getRepModeCustomers(),
            $this->getRepModeUsers(),
            $this->getStores(),
            $this->getInsightsSoonToLapseTransactions2()
        ));

        Fixtures::add(
            'ci-transactions-soon-to-lapse-secondary-employee-assign-rep-mode',
            array_merge(
                $this->getRepModeCustomersSecondaryEmployeeAssign(),
                $this->getRepModeUsersWithEmployeeId(),
                $this->getStores(),
                $this->getInsightsSoonToLapseSecondaryEmployeeAssignTransactions()
            )
        );

        Fixtures::add('ci-transactions-soon-to-lapse-team-mode', array_merge(
            $this->getTeamModeCustomers(),
            $this->getTeamModeUsers(),
            $this->getStores(),
            $this->getInsightsSoonToLapseTransactions()
        ));

        Fixtures::add(
            'ci-transactions-soon-to-lapse-secondary-employee-assign-team-mode',
            array_merge(
                $this->getTeamModeCustomersSecondaryEmployeeAssign(),
                $this->getTeamModeUsers(),  // store user has already assign employee Id in this function for team mode
                $this->getStores(),
                $this->getTeamModeInsightsSoonToLapseSecondaryEmployeeAssignTransactions()
            )
        );

        Fixtures::add('ci-transactions-soon-to-lapse-secondary-rep-mode', array_merge(
            $this->getRepModeCustomers(),
            $this->getRepModeUsers(),
            $this->getStores(),
            $this->getInsightsSoonToLapseSecondaryTransactions()
        ));

        Fixtures::add('ci-transactions-soon-to-lapse-secondary-team-mode', array_merge(
            $this->getTeamModeCustomers(),
            $this->getTeamModeUsers(),
            $this->getStores(),
            $this->getInsightsSoonToLapseSecondaryTransactions()
        ));
    }

    public function populateTasks()
    {
        Fixtures::add('tasks', [
            'sf_task'            => [
                [
                    'id'        => 1,
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'test detail should be updated',
                    'parent_id' => 1,
                ],
                [
                    'id'        => 2,
                    'user_id'   => 2,
                    'status'    => 'dismissed',
                    'details'   => 'test detail should not be updated',
                    'parent_id' => 1,
                ],
                [
                    'id'        => 3,
                    'user_id'   => 3,
                    'status'    => 'resolved',
                    'details'   => 'test detail should not be updated',
                    'parent_id' => 1,
                ],
                [
                    'id'        => 4,
                    'user_id'        => 4,
                    'status'         => 'unresolved',
                    'details'        => 'this task should be delete as user_id is in valid',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
            ]
        ]);
    }

    public function populateCorporateTaskForRepMode()
    {
        Fixtures::add('corporate-task-for-create-child-tasks-rep-mode', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                ],
            ]
        ]);

        Fixtures::add('corporate-task-for-update-child-tasks-rep-mode', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail after updated",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                ],
            ],
            'sf_task'            => [
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'test detail should be updated',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 2,
                    'status'    => 'dismissed',
                    'details'   => 'test detail should not be updated',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 3,
                    'status'    => 'resolved',
                    'details'   => 'test detail should not be updated',
                    'parent_id' => 1,
                ],
                [
                    'user_id'        => 4,
                    'status'         => 'unresolved',
                    'details'        => 'this task should be delete as user_id is in valid',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
            ]
        ]);

        Fixtures::add('corporate-child-task-count-additional-data', [
            'sf_task'  => [
                [
                    'user_id'        => 30001,
                    'status'         => 'resolved',
                    'details'        => 'this task should not count as child count',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
                [
                    'user_id'        => 30002,
                    'status'         => 'dismissed',
                    'details'        => 'this task should not count as child count',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
            ],
            'wp_users' => [
                [
                    'ID'                  => 30001,
                    'user_login'          => 'salesfloor_test_child_task_counts_1',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall new rep',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
                [
                    'ID'                  => 30002,
                    'user_login'          => 'salesfloor_test_child_task_counts_2',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall new rep',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
        ]);

        Fixtures::add('corporate-task-for-delete-unmatched-specialties-child-tasks', [
            'sf_corporate_tasks'            => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail after updated",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                ],
            ],
            'sf_task'                       => [
                [
                    'user_id'        => 1,
                    'status'         => 'unresolved',
                    'details'        => 'test detail should be updated',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
                [
                    'user_id'        => 2,
                    'status'         => 'unresolved',
                    'details'        => 'test detail should not be updated',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
                [
                    'user_id'        => 3,
                    'status'         => 'resolved',
                    'details'        => 'test detail should not be updated',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
                [
                    'user_id'        => 4,
                    'status'         => 'dismissed',
                    'details'        => 'this task should be delete as user_id is in valid',
                    'parent_id'      => 1,
                    'type'           => 'automated',
                    'automated_type' => 'corporate'
                ],
            ],
            'sf_corporate_task_specialties' => [
                [
                    'corporate_task_id' => 1,
                    "specialty_id"      => "JtDN4T9R8pX3ruQMwhjNA6aMaTw",
                    "created_at"        => "2019-04-15 09:00:00",
                ],
            ],
            'sf_rep_product_selection_map'  => [
                [
                    'user_id'          => 1,
                    "term_taxonomy_id" => "JtDN4T9R8pX3ruQMwhjNA6aMaTw",
                    "term_id"          => "JtDN4T9R8pX3ruQMwhjNA6aMaTw",
                    "category_id"      => "JtDN4T9R8pX3ruQMwhjNA6aMaTw"
                ],
                [
                    'user_id'          => 2,
                    "term_taxonomy_id" => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "term_id"          => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "category_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I"
                ],
                [
                    'user_id'          => 3,
                    "term_taxonomy_id" => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "term_id"          => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "category_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I"
                ],
                [
                    'user_id'          => 4,
                    "term_taxonomy_id" => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "term_id"          => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "category_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I"
                ],
            ],
        ]);

        Fixtures::add('corporate-task-create-child-tasks-with-specialties', [
            'sf_corporate_tasks'            => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail after updated",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                ],
            ],
            'sf_corporate_task_specialties' => [
                [
                    'corporate_task_id' => 1,
                    "specialty_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "created_at"        => "2019-04-15 09:00:00",
                ],
            ],
            'sf_rep_product_selection_map'  => [
                [
                    'user_id'          => 1,
                    "term_taxonomy_id" => "JtDN4T9R8pX3ruQMwhjNA6aMaTw",
                    "term_id"          => "JtDN4T9R8pX3ruQMwhjNA6aMaTw",
                    "category_id"      => "JtDN4T9R8pX3ruQMwhjNA6aMaTw"
                ],
                [
                    'user_id'          => 2,
                    "term_taxonomy_id" => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "term_id"          => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "category_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I"
                ],
                [
                    'user_id'          => 3,
                    "term_taxonomy_id" => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "term_id"          => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "category_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I"
                ],
                [
                    'user_id'          => 4,
                    "term_taxonomy_id" => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "term_id"          => "9q8WZbyTorsq7L8CqZIChtlAM6I",
                    "category_id"      => "9q8WZbyTorsq7L8CqZIChtlAM6I"
                ],
            ],
        ]);

        Fixtures::add('corporate-task-for-delete-child-tasks-rep-mode', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail after delete",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 1,
                ],
            ],
            'sf_task'            => [
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'test detail1',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 2,
                    'status'    => 'dismissed',
                    'details'   => 'test detail2',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 3,
                    'status'    => 'resolved',
                    'details'   => 'test detail3',
                    'parent_id' => 1,
                ],
            ]
        ]);

        Fixtures::add('auto-dismiss-tasks-from-corporate-task', $this->getAutoDismissTasks());
    }

    public function populateCorporateTaskTestData()
    {
        Fixtures::add('corporate-task-test-suite-data', [
            'wp_users' => [
                [
                    'ID'                  => 399,
                    'user_login'          => 'fake-mall-new-rep',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall new rep',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ]
        ]);
    }

    public function populateCorporateTaskForTeamMode()
    {
        Fixtures::add('corporate-task-for-create-child-tasks-team-mode', [
            'sf_corporate_tasks' => [
                [
                    'id'                => 2,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 21,
                    "updated_by"        => 21,
                    "reminder_time"     => "2019-04-20 15:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                ],
            ],
            'sf_task'            => [
                [
                    'user_id'   => 335,
                    'status'    => 'unresolved',
                    'details'   => 'test detail1',
                    'parent_id' => 2,
                ],
                [
                    'user_id'   => 336,
                    'status'    => 'unresolved',
                    'details'   => 'test detail2',
                    'parent_id' => 2,
                ],
            ],
            'wp_users'           => [
                [
                    'ID'                  => 335,
                    'user_login'          => 'fake-mall',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
                [
                    'ID'                  => 336,
                    'user_login'          => 'other-fake-mall',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Other Mall',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2023-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 2003,
                    'type'                => 'store',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
            'sf_corporate_task_stores' => [
                [
                    'corporate_task_id' => '2',
                    'store_id' => '1003',
                    'created_at' => '2025-02-19 16:20:43',
                    'updated_at' => '2025-02-19 16:20:43',
                ]
            ]
        ]);
    }

    public function populateAutomatedTasksDistributionByStoresTestData()
    {
        Fixtures::add('automated-task-distribution-test-suite-data', [
            'sf_retailer_customers' => [
                [
                    'customer_id' => '1234',
                    'gender' => 'female',
                    'first_name' => 'Test',
                    'last_name' => 'RC',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => null,
                    'phone_label' => null,
                    'longitude' => null,
                    'latitude' => null,
                    'is_subscribed' => '0',
                ],
                [
                    'customer_id' => '1235',
                    'gender' => 'female',
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => null,
                    'phone_label' => null,
                    'longitude' => null,
                    'latitude' => null,
                    'is_subscribed' => '0',
                ],
                [
                    'customer_id' => '1236',
                    'gender' => 'female',
                    'first_name' => 'John1',
                    'last_name' => 'Doe1',
                    'email' => '<EMAIL>',
                    'email_label' => 'Personal',
                    'phone' => null,
                    'phone_label' => null,
                    'longitude' => null,
                    'latitude' => null,
                    'is_subscribed' => '0',
                ],
            ],
            'sf_retailer_customer_addresses' => [
                [
                    'customer_id' => '1236',
                    'address_line_1' => '350 Notre-Dame Ouest',
                    'address_line_2' => '',
                    'postal_code' => 'H3C 8H4',
                    'city' => 'Montreal',
                    'state' => 'QC',
                    'country' => 'Canada',
                    'label' => 'Home',
                    'is_default' => 1,
                ],
            ],
            'sf_retailer_transaction' => [
                [
                    'trx_thread_id' => '6549867356',
                    'trx_id' => '6549867356',
                    'customer_id' => '1234',
                    'location' => 'lololol',
                    'trx_type' => 'Sale',
                    'trx_date' => gmdate('Y-m-d', strtotime("-5 DAY")),
                    'trx_total' => 300,
                ],
                [
                    'trx_thread_id' => '6549867354',
                    'trx_id' => '6549867354',
                    'customer_id' => '1235',
                    'location' => 'lololol',
                    'trx_type' => 'Sale',
                    'trx_date' => gmdate('Y-m-d', strtotime("-5 DAY")),
                    'trx_total' => 200,
                ],
                [
                    'trx_thread_id' => '6549867355',
                    'trx_id' => '6549867357',
                    'customer_id' => '1236',
                    'location' => '0',
                    'trx_type' => 'Sale',
                    'trx_date' => gmdate('Y-m-d', strtotime("-5 DAY")),
                    'trx_total' => 200,
                ],
            ],
            'sf_store' => [
                [
                    'store_id'          => 100,
                    'name'              => 'Store One',
                    'timezone'          => 'America/Montreal',
                    'sf_identifier'     => 'store-one',
                    'retailer_store_id' => 74,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ],
                [
                    'store_id'          => 101,
                    'name'              => 'Store 2',
                    'timezone'          => 'America/Tonronto',
                    'sf_identifier'     => 'store-2',
                    'retailer_store_id' => 75,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 44.29550000,
                    'longitude'         => -76.42800000,
                ],
            ],
            'wp_users' => [
                [
                    'ID'                  => 9,
                    'user_login'          => 'test-store-new-rep',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'new rep',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 101,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
            'geonames_zip' => [
                [
                    'country_code' => 'CA',
                    'postal_code'   => 'H3C',
                    'admin_code1'   => 'QC',
                    'latitude'      => 45.49800000,
                    'longitude'     => -73.54720000,
                    'place_name'   => 'Bellevue',
                    'admin_name1'  => 'Washington',
                    'admin_name2'  => 'King',
                    'admin_code2'  => '033',
                    'admin_name3'  => '',
                    'admin_code3'  => '',
                    'accuracy'     => 4
                ]
            ],
        ]);
    }

    private function getTeamModeSfTransactions()
    {
        $trx = [
            // Sale transaction, multiple customers
            $this->makeSfTransaction(self::STORE_USER_1, 'TRX1', 40, 100),
            $this->makeSfTransaction(self::STORE_USER_1, 'TRX2', 43, 150),
            $this->makeSfTransaction(self::STORE_USER_1, 'TRX3', 44, 200),
            $this->makeSfTransaction(self::STORE_USER_1, 'TRX4', 45, 10),

            // Sale transaction, multiple from same customer
            $this->makeSfTransaction(self::STORE_USER_1, 'TRX5', 41, 5000),
            $this->makeSfTransaction(self::STORE_USER_1, 'TRX6', 41, 6000),

            // Return transaction - should be ignored
            $this->makeSfTransaction(self::STORE_USER_1, 'RET1', 40, 7000, null, 'return'),

            // Sale transaction different store from home store
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX7', 41, 400),

            // Sale transaction no retailer_customer_id on sf_customer
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX71', 33, 300),

            // Sale transaction for contact assigned to different store.
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX8', 47, 400),

            // Sale Transaction, different store, too soon
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX9', 32, 5000, gmdate('Y-m-d H:i:s', strtotime("-3 days"))),

            // Sale Transaction, different store, too late
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX10', 32, 5000, gmdate('Y-m-d H:i:s', strtotime("-10 days"))),
        ];

        return [
            'sf_rep_transaction' => $trx
        ];
    }

    private function getRepModeSfTransactions()
    {
        $oldTransactionDate = gmdate('Y-m-d H:i:s', strtotime("-10 days"));
        $oneDayOldTransactionReceivedDate = gmdate('Y-m-d H:i:s', strtotime("-1 day"));

        $trx = [
            // Sale transaction, multiple customers
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX1', 40, 100),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX2', 43, 150),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX3', 44, 200),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX4', 45, 10),

            // Sale transaction, multiple from same customer
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX5', 41, 5000),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX6', 41, 6000),

            // Sale transaction different store from home store
            $this->makeSfTransaction(self::USER_1_STORE_2, 'TRX7', 41, 400),

            // Return transaction
            $this->makeSfTransaction(self::USER_1_STORE_2, 'RET1', 41, 400, null, 'return'),

            // Sale transaction no retailer_customer_id on sf_customer
            $this->makeSfTransaction(self::USER_1_STORE_2, 'TRX71', 33, 300),

            // Sale transaction for contact assigned to different store.
            $this->makeSfTransaction(self::USER_1_STORE_2, 'TRX8', 47, 400),

            // Sale Transaction, different store, too soon
            $this->makeSfTransaction(self::USER_1_STORE_2, 'TRX9', 32, 5000, gmdate('Y-m-d H:i:s', strtotime("-3 days"))),

            // Sale Transaction, different store, too late
            $this->makeSfTransaction(self::USER_1_STORE_2, 'TRX10', 32, 5000, $oldTransactionDate),

            // Cancel Transaction
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX11', 34, 128, $oldTransactionDate, 'sale', $oldTransactionDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX11', 34, 128, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate)
        ];

        return [
            'sf_rep_transaction' => $trx
        ];
    }

    private function getSfCancelledTransactions()
    {
        $oldTransactionDate = gmdate('Y-m-d H:i:s', strtotime("-10 days"));
        $oneDayOldTransactionReceivedDate = gmdate('Y-m-d H:i:s', strtotime("-1 day"));
        $threeDayOldTransactionReceivedDate = gmdate('Y-m-d H:i:s', strtotime("-3 day"));

        $emailForNonExistingUser = self::CUSTOMER_EMAIL_ANONYMOUS;

        $trx = [
            // Sale transactions
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX1', self::CUSTOMER_1_USER_1_SUBSCRIBED, 100, $oldTransactionDate, 'sale', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX2', self::CUSTOMER_1_USER_1_SUBSCRIBED, 200, $oldTransactionDate, 'sale', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX3', self::NON_EXISTING_CUSTOMER, 900, $oldTransactionDate, 'sale', $oneDayOldTransactionReceivedDate, $emailForNonExistingUser),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX4', self::CUSTOMER_1_USER_1_SUBSCRIBED, 800, $oldTransactionDate, 'sale', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX5', self::CUSTOMER_2_USER_1, 300, $oldTransactionDate, 'sale', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX6', self::CUSTOMER_2_USER_1, 100, $oldTransactionDate, 'sale', $threeDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX7', self::CUSTOMER_1_USER_1_SUBSCRIBED, 200, $oldTransactionDate, 'sale', $threeDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX8', self::NON_EXISTING_CUSTOMER, 900, $oldTransactionDate, 'sale', $threeDayOldTransactionReceivedDate, $emailForNonExistingUser),

            // Cancel Transactions
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX1', self::CUSTOMER_1_USER_1_SUBSCRIBED, -30, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX1', self::CUSTOMER_1_USER_1_SUBSCRIBED, -70, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX2', self::CUSTOMER_1_USER_1_SUBSCRIBED, -200, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX3', self::NON_EXISTING_CUSTOMER, -200, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate, $emailForNonExistingUser),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX3', self::NON_EXISTING_CUSTOMER, -100, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate, $emailForNonExistingUser),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX3', self::NON_EXISTING_CUSTOMER, -600, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate, $emailForNonExistingUser),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX4', self::CUSTOMER_1_USER_1_SUBSCRIBED, -800, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX5', self::CUSTOMER_2_USER_1, -130, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX5', self::CUSTOMER_2_USER_1, -170, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),

            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX6', self::CUSTOMER_2_USER_1, -30, $oldTransactionDate, 'cancel', $threeDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX6', self::CUSTOMER_2_USER_1, -70, $oldTransactionDate, 'cancel', $threeDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX7', self::CUSTOMER_1_USER_1_SUBSCRIBED, -200, $oldTransactionDate, 'cancel', $threeDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX8', self::NON_EXISTING_CUSTOMER, -200, $oldTransactionDate, 'cancel', $threeDayOldTransactionReceivedDate, $emailForNonExistingUser),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX8', self::NON_EXISTING_CUSTOMER, -100, $oldTransactionDate, 'cancel', $threeDayOldTransactionReceivedDate, $emailForNonExistingUser),
            $this->makeSfTransaction(self::USER_1_STORE_1, 'TRX8', self::NON_EXISTING_CUSTOMER, -600, $oldTransactionDate, 'cancel', $threeDayOldTransactionReceivedDate, $emailForNonExistingUser)
        ];

        return [
            'sf_rep_transaction' => $trx
        ];
    }

    private function getSfTeamModeCancelledTransactions()
    {
        $oldTransactionDate = gmdate('Y-m-d H:i:s', strtotime("-10 days"));
        $oneDayOldTransactionReceivedDate = gmdate('Y-m-d H:i:s', strtotime("-1 day"));
        $threeDayOldTransactionReceivedDate = gmdate('Y-m-d H:i:s', strtotime("-3 day"));

        $trx = [
            // Sale transactions
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX1', self::CUSTOMER_FOR_STORE_USER_2, 100, $oldTransactionDate, 'sale', $oldTransactionDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX2', self::CUSTOMER_FOR_STORE_USER_2, 200, $oldTransactionDate, 'sale', $oldTransactionDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX3', self::CUSTOMER_FOR_STORE_USER_2, 900, $oldTransactionDate, 'sale', $oldTransactionDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX4', self::CUSTOMER_FOR_STORE_USER_2, 800, $oldTransactionDate, 'sale', $oldTransactionDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX5', self::CUSTOMER_FOR_STORE_USER_2, 300, $oldTransactionDate, 'sale', $oldTransactionDate),

            // Cancel Transactions
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX1', self::CUSTOMER_FOR_STORE_USER_2, -30, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX1', self::CUSTOMER_FOR_STORE_USER_2, -70, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX2', self::CUSTOMER_FOR_STORE_USER_2, -200, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX3', self::CUSTOMER_FOR_STORE_USER_2, -200, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX3', self::CUSTOMER_FOR_STORE_USER_2, -100, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX3', self::CUSTOMER_FOR_STORE_USER_2, -600, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX4', self::CUSTOMER_FOR_STORE_USER_2, -800, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX5', self::CUSTOMER_FOR_STORE_USER_2, -130, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),
            $this->makeSfTransaction(self::STORE_USER_2, 'TRX5', self::CUSTOMER_FOR_STORE_USER_2, -170, $oldTransactionDate, 'cancel', $oneDayOldTransactionReceivedDate),

        ];

        return [
            'sf_rep_transaction' => $trx
        ];
    }

    private function makeSfTransaction($userId, $trxId, $customerId, $value, $date = null, $type = 'sale', $receivedDate = null, $email = null)
    {
        return [
            'user_id'         => $userId,
            'trx_id'          => $trxId,
            'trx_date'        => $date === null ? gmdate('Y-m-d H:i:s', strtotime("-7 days")) : $date,
            'trx_type'        => $type,
            'customer_id'     => $customerId,
            'trx_apply_total' => $value,
            'trx_total'       => $value,
            'received_date'   => $receivedDate ?? gmdate('Y-m-d H:i:s'),
            'customer_email'  => $email
        ];
    }

    private function getInsightsSoonToLapseSecondaryTransactions()
    {
        $lapseDate = gmdate('Y-m-d H:i:s', strtotime("-365 days"));

        $ignoreIfNotBeginLapse = gmdate('Y-m-d 00:00:01', strtotime('-364 days'));
        $ignoreIfLapse = gmdate('Y-m-d 23:59:59', strtotime('-366 days'));

        return ['sf_retailer_transaction' => [
            // Old sale transaction at matching store with assigned rep.
            $this->makeInsightsTransaction(40, 'TRX1', 400, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(41, 'TRX2', 800, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(41, 'RET1', 900, self::STORE_1_RETAILERID, $lapseDate, 'Return'),

            // Old sale transaction, but there was a more recent one
            $this->makeInsightsTransaction(42, 'TRX3', 900, self::STORE_1_RETAILERID, $lapseDate),
            // this above transaction would be ignored when generated SoonToLapseSecondary since the below transaction just happen recently
            $this->makeInsightsTransaction(42, 'TRX4', 800, self::STORE_1_RETAILERID),

            // Old sale transaction, no "CI-assigned" rep
            $this->makeInsightsTransaction(33, 'NOASSIGN', 1000, self::STORE_2_RETAILERID, $lapseDate),

            // Two transactions that should be ignored
            $this->makeInsightsTransaction(43, 'TRX5', 800, self::STORE_1_RETAILERID, $ignoreIfNotBeginLapse),
            $this->makeInsightsTransaction(43, 'TRX6', 800, self::STORE_1_RETAILERID, $ignoreIfLapse),
        ]];
    }

    private function getInsightsSoonToLapseTransactions()
    {
        $lapseDate = gmdate('Y-m-d H:i:s', strtotime("-5 days"));

        return ['sf_retailer_transaction' => [
            // Old sale transaction at matching store with assigned rep.
            $this->makeInsightsTransaction(40, 'TRX1', 400, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(41, 'TRX2', 800, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(41, 'RET1', 900, self::STORE_1_RETAILERID, $lapseDate, 'Return'),

            // Old sale transaction, but there was a more recent one
            $this->makeInsightsTransaction(42, 'TRX3', 900, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(42, 'TRX4', 800, self::STORE_1_RETAILERID),

            // Old sale transaction, no "CI-assigned" rep
            $this->makeInsightsTransaction(33, 'NOASSIGN', 1000, self::STORE_2_RETAILERID, $lapseDate),
        ]];
    }

    private function getInsightsSoonToLapseTransactions2()
    {
        $lapseDate = gmdate('Y-m-d H:i:s', strtotime("-120 days"));
        $lapseDate2 = gmdate('Y-m-d H:i:s', strtotime("-240 days"));

        return ['sf_retailer_transaction' => [
            // Old sale transaction at matching store with assigned rep.
            $this->makeInsightsTransaction(40, 'TRX1', 400, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(41, 'TRX2', 800, self::STORE_1_RETAILERID, $lapseDate2),
            $this->makeInsightsTransaction(41, 'RET1', 900, self::STORE_1_RETAILERID, $lapseDate2, 'Return'),

            // Old sale transaction, but there was a more recent one
            $this->makeInsightsTransaction(42, 'TRX3', 900, self::STORE_1_RETAILERID, $lapseDate),
            $this->makeInsightsTransaction(42, 'TRX4', 800, self::STORE_1_RETAILERID),

            // Old sale transaction, no "CI-assigned" rep
            $this->makeInsightsTransaction(33, 'NOASSIGN', 1000, self::STORE_2_RETAILERID, $lapseDate),
        ]];
    }

    private function getInsightsSoonToLapseSecondaryEmployeeAssignTransactions()
    {
        $lapseDate = gmdate('Y-m-d H:i:s', strtotime("-5 days"));

        return [
            'sf_retailer_transaction' => [
                $this->makeInsightsTransaction(140, 'TRX1', 400, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 100),
                $this->makeInsightsTransaction(141, 'TRX2', 800, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 2),
                $this->makeInsightsTransaction(141, 'RET1', 900, self::STORE_1_RETAILERID, $lapseDate, 'Return', 3),

                // Old sale transaction, but there was a more recent one
                $this->makeInsightsTransaction(142, 'TRX3', 900, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 4),
                $this->makeInsightsTransaction(142, 'TRX4', 800, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 5),

                $this->makeInsightsTransaction(133, 'NOASSIGN', 1000, self::STORE_2_RETAILERID, $lapseDate, 'Sale', 6),
            ]
        ];
    }

    private function getTeamModeInsightsSoonToLapseSecondaryEmployeeAssignTransactions()
    {
        $lapseDate = gmdate('Y-m-d H:i:s', strtotime("-5 days"));

        return [
            'sf_retailer_transaction' => [
                $this->makeInsightsTransaction(140, 'TRX1', 400, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 1004),
                $this->makeInsightsTransaction(141, 'TRX2', 800, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 2),
                $this->makeInsightsTransaction(141, 'RET1', 900, self::STORE_1_RETAILERID, $lapseDate, 'Return', 3),

                // Old sale transaction, but there was a more recent one
                $this->makeInsightsTransaction(142, 'TRX3', 900, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 4),
                $this->makeInsightsTransaction(142, 'TRX4', 800, self::STORE_1_RETAILERID, $lapseDate, 'Sale', 5),

                $this->makeInsightsTransaction(133, 'NOASSIGN', 1000, self::STORE_2_RETAILERID, $lapseDate, 'Sale', 6),
            ]
        ];
    }

    private function makeInsightsTransaction(
        $customerId,
        $trxId,
        $total,
        $location,
        $date = null,
        $type = 'Sale',
        $employeeId = null
    ) {
        return [
            'customer_id'   => $customerId,
            'trx_thread_id' => $trxId,
            'trx_type'      => $type,
            'trx_date'      => $date === null ? gmdate('Y-m-d H:i:s', strtotime("-7 days")) : $date,
            'trx_id'        => $trxId,
            'location'      => $location,
            'trx_total'     => $total,
            'employee_id'   => $employeeId,
        ];
    }

    private function getInsightsNewTransactions()
    {
        return [
            'sf_retailer_transaction' => [
                // Sale transaction at matching store with assigned rep
                $this->makeInsightsTransaction(40, 'TRX1', 100, self::STORE_1_RETAILERID),
                $this->makeInsightsTransaction(43, 'TRX43', 220, self::STORE_1_RETAILERID),
                $this->makeInsightsTransaction(44, 'TRX44', 201, self::STORE_1_RETAILERID),
                $this->makeInsightsTransaction(45, 'TRX45', 150, self::STORE_2_RETAILERID),

                // Return - should be ignored
                $this->makeInsightsTransaction(40, 'RET1', 200, self::STORE_1_RETAILERID, null, 'Return'),

                // Sale transaction at matching store with assigned rep
                $this->makeInsightsTransaction(41, 'TRX2', 5000, self::STORE_1_RETAILERID),

                // Sale transaction at matching store with assigned rep, multi trx customer
                $this->makeInsightsTransaction(41, 'TRX3', 6000, self::STORE_1_RETAILERID),

                // Sale transaction at non-matching store with assigned rep
                // Despite this being the top valued transaction, it shouldn't create a task because it
                // doesn't match the store of the assigned rep.
                $this->makeInsightsTransaction(41, 'TRX4', 8000, 'online'),
                // Sale transaction at matching store with no rep
                $this->makeInsightsTransaction(54, 'TRX5', 50, self::STORE_1_RETAILERID),
                // Sale transaction at matching store with assigned rep - different rep same store
                $this->makeInsightsTransaction(46, 'TRX6', 25.5, self::STORE_1_RETAILERID),
                // Sale transaction at matching store with assigned rep - Too soon!
                $this->makeInsightsTransaction(47, 'TRX7', 99, self::STORE_1_RETAILERID, gmdate('Y-m-d H:i:s', strtotime("-3 days"))),

                // Sale transaction at matching store with assigned rep - Too late!
                $this->makeInsightsTransaction(47, 'TRX8', 345, 1008, gmdate('Y-m-d H:i:s', strtotime("-10 days")))
            ]
        ];
    }

    private function getInsightsNewTransactionsWithEmployeeAssigned()
    {
        return [
            'sf_retailer_transaction' => [
                // Sale transaction at matching store with assigned rep
                $this->makeInsightsTransaction(30, 'TRX1', 100, self::STORE_1_RETAILERID, null, 'sale', 100),
                $this->makeInsightsTransaction(31, 'TRX43', 220, self::STORE_1_RETAILERID, null, 'sale', 101),
                $this->makeInsightsTransaction(32, 'TRX44', 201, self::STORE_1_RETAILERID, null, 'sale', 102),
            ]
        ];
    }

    private function getTeamModeCustomers()
    {
        ///////////////////////
        /// Team Mode

        $sfCustomer                     = [];
        $sfRetailerCustomers            = [];
        $sfCustomersToRetailerCustomers = [];

        $this->createCustomer(
            40,
            1,
            self::STORE_USER_1,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            41,
            2,
            self::STORE_USER_1,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            42,
            3,
            self::STORE_USER_1,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            43,
            4,
            self::STORE_USER_1,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            44,
            5,
            self::STORE_USER_1,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            45,
            6,
            self::STORE_USER_1,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            46,
            7,
            self::STORE_USER_2,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            47,
            8,
            self::STORE_USER_2,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );

        $this->createCustomer(
            self::CUSTOMER_FOR_STORE_USER_2,
            13,
            self::STORE_USER_2,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createCustomer(
            33,
            14,
            self::STORE_USER_2,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers,
            false
        );
        $this->createCustomer(
            54,
            15,
            0,
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );

        return [
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ];
    }

    private function getTeamModeCustomersSecondaryEmployeeAssign()
    {
        ///////////////////////
        /// Team Mode

        $sfCustomer                     = [];
        $sfRetailerCustomers            = [];
        $sfCustomersToRetailerCustomers = [];

        $this->createCustomer(140, 1, self::STORE_USER_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(141, 2, self::STORE_USER_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(142, 3, self::STORE_USER_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(143, 4, self::STORE_USER_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(144, 5, self::STORE_USER_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(145, 6, self::STORE_USER_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(146, 7, self::STORE_USER_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(147, 8, self::STORE_USER_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);

        return [
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ];
    }

    private function getRepModeCustomers()
    {
        $sfCustomer                     = [];
        $sfRetailerCustomers            = [];
        $sfCustomersToRetailerCustomers = [];

        /////////////
        /// Rep mode

        $this->createCustomer(self::CUSTOMER_1_USER_1_SUBSCRIBED, 1, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, true, 1);
        $this->createCustomer(self::CUSTOMER_2_USER_1, 2, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(42, 3, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(43, 4, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(44, 5, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 2);
        $this->createCustomer(45, 6, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(46, 7, self::USER_2_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(47, 8, self::USER_2_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);

        $this->createCustomer(32, 13, self::USER_1_STORE_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(33, 14, self::USER_1_STORE_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false);
        $this->createCustomer(54, 15, 0, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);

        return [
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ];
    }

    private function getRepModeCustomersSecondaryEmployeeAssign()
    {
        $sfCustomer                     = [];
        $sfRetailerCustomers            = [];
        $sfCustomersToRetailerCustomers = [];

        /////////////
        /// Rep mode
        $this->createCustomer(140, 1, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(141, 2, self::USER_1_STORE_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(142, 3, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(143, 4, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(144, 5, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(145, 6, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(146, 7, self::USER_2_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);
        $this->createCustomer(147, 8, self::USER_2_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers, false, 0, true);

        return [
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ];
    }

    private function getTransactionCustomers()
    {
        $sfCustomer                     = [];
        $sfRetailerCustomers            = [];
        $sfCustomersToRetailerCustomers = [];

        /////////////
        /// Rep mode

        $this->createTransactionCustomer(40, 30, self::USER_1_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createTransactionCustomer(41, 31, self::USER_1_STORE_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createTransactionCustomer(42, 32, self::USER_2_STORE_1, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createTransactionCustomer(43, 33, self::USER_2_STORE_2, $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);

        return [
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ];
    }

    private function getMoreTransactionMultipleDays()
    {
        $pastDays = 27;
        return [
            'sf_retailer_transaction' => [
                [
                    'customer_id'   => '30',
                    'trx_thread_id' => 'TRX101',
                    'trx_type'      => 'Sale',
                    'trx_date'      => gmdate('Y-m-d H:i:s', strtotime("-$pastDays days")),
                    'trx_id'        => 'TRX101',
                    'location'      => '1004',
                    'pos_id'        => '',
                    'trx_total'     => 101.0000,
                    'currency'      => 'USD',
                    'created_at'    => '2021-12-15 20:15:08',
                    'employee_id'   => '100'
                ],
                [
                    'customer_id'   => '31',
                    'trx_thread_id' => 'TRX143',
                    'trx_type'      => 'Sale',
                    'trx_date'      => gmdate('Y-m-d H:i:s', strtotime("-$pastDays days")),
                    'trx_id'        => 'TRX143',
                    'location'      => '1004',
                    'pos_id'        => '',
                    'trx_total'     => 220.0000,
                    'currency'      => 'USD',
                    'created_at'    => '2021-12-15 20:15:08',
                    'employee_id'   => '101',
                ],
            ]
        ];
    }

    private function getCustomerAndRetailerCustomerMeta()
    {
        $sfCustomerMeta          = [];
        $sfRetailerCustomersMeta = [];

        /////////////
        /// Rep mode

        $this->createCustomerMeta(40, 30, $sfCustomerMeta, $sfRetailerCustomersMeta);
        $this->createCustomerMeta(41, 31, $sfCustomerMeta, $sfRetailerCustomersMeta);
        $this->createCustomerMeta(42, 32, $sfCustomerMeta, $sfRetailerCustomersMeta);
        $this->createCustomerMeta(43, 33, $sfCustomerMeta, $sfRetailerCustomersMeta);

        return [
            'sf_customer_meta'          => $sfCustomerMeta,
            'sf_retailer_customer_meta' => $sfRetailerCustomersMeta,
        ];
    }

    private function createCustomer(
        $customerId,
        $retailerCustomerId,
        $ownerUserId,
        &$sfCustomer,
        &$sfRetailerCustomers,
        &$sfCustomersToRetailerCustomers,
        $isMapped = true,
        $subscribeStatus = \Salesfloor\Models\Customer::SUBSCRIPTION_STATUS_NOT_SUBSCRIBED,
        $isSecondaryMapped = false
    ) {
        $customer = [
            'ID'                => $customerId,
            'user_id'           => $ownerUserId,
            'email'             => uniqid() . "@example.com",
            'name'              => "Test Customer",
            'localization'      => 'en',
            'first_name'        => 'Test',
            'last_name'         => 'Customer',
            'subcribtion_flag'                => $subscribeStatus,
            'sms_marketing_subscription_flag' => $subscribeStatus,
        ];

        if ($isMapped) {
            $customer['retailer_customer_id'] = $retailerCustomerId;
        }

        if ($isSecondaryMapped) {
            $customer['retailer_parent_customer_id'] = $retailerCustomerId;
        }

        $sfCustomer[] = $customer;

        $retailerCustomer      = [
            'id'          => $retailerCustomerId,
            'customer_id' => $customerId,
            'first_name'  => $customer['first_name'],
            'last_name'   => $customer['last_name'],
            'email'       => $customer['email'],
            'is_subscribed' => $subscribeStatus,
            'is_subscribed_sms_marketing' => $subscribeStatus,
        ];
        $sfRetailerCustomers[] = $retailerCustomer;

        if ($isMapped) {
            $sfCustomersToRetailerCustomers[] = [
                'customer_id'          => $customerId,
                'retailer_customer_id' => $retailerCustomerId,
                'comment'              => '[{"field":"retailer_customer_id","value":"' . $retailerCustomer['customer_id'] . '"},{"field":"default_email","value":"' . $customer['email'] . '"}]',
            ];
        }
        if ($isSecondaryMapped) {
            $sfCustomersToRetailerCustomers[] = [
                'customer_id'          => $customerId,
                'retailer_customer_id' => $retailerCustomerId,
                'comment'              => '[{"field":"retailer_parent_customer_id","value":"' . $retailerCustomer['customer_id'] . '"},{"field":"default_email","value":"' . $customer['email'] . '"}]',
            ];
        }
    }

    private function createTransactionCustomer(
        $customerId,
        $retailerCustomerId,
        $ownerUserId,
        &$sfCustomer,
        &$sfRetailerCustomers,
        &$sfCustomersToRetailerCustomers,
        $isMapped = true
    ) {
        $customer = [
            'ID'               => $customerId,
            'user_id'          => $ownerUserId,
            'email'            => uniqid() . "@example.com",
            'name'             => "Test Customer",
            'localization'     => 'en',
            'first_name'       => 'Test',
            'last_name'        => 'Customer',
            'subcribtion_flag' => 1,
        ];

        if ($isMapped) {
            $customer['retailer_customer_id'] = $retailerCustomerId;
        }

        $sfCustomer[] = $customer;

        $retailerCustomer      = [
            'customer_id' => $retailerCustomerId,
            'first_name'  => $customer['first_name'],
            'last_name'   => $customer['last_name'],
            'email'       => $customer['email'],
            'phone'       => '51456' . rand(10000, 90000),
            'is_subscribed' => 1,
        ];
        $sfRetailerCustomers[] = $retailerCustomer;

        if ($isMapped) {
            $sfCustomersToRetailerCustomers[] = [
                'customer_id'          => $customerId,
                'retailer_customer_id' => $retailerCustomerId,
                'comment'              => '[{"field":"retailer_customer_id","value":"' . $retailerCustomer['customer_id'] . '"},{"field":"default_email","value":"' . $customer['email'] . '"}]',
            ];
        }
    }

    private function createCustomerMeta($customerId, $retailerCustomerId, &$sfCustomerMeta, &$sfRetailerCustomerMeta)
    {
        $customerMeta = [
            'customer_id' => $customerId,
            'type'        => 'email',
            'value'       => uniqid() . "@example.com",
            'label'       => 'home',
            'position'    => 0,
        ];

        $sfCustomerMeta[] = $customerMeta;

        $retailerCustomerMeta = [
            'customer_id' => $retailerCustomerId,
            'type'        => 'email',
            'value'       => uniqid() . "@example.com",
            'label'       => 'home',
            'position'    => 0,
        ];

        $sfRetailerCustomerMeta[] = $retailerCustomerMeta;

        $retailerCustomerMeta = [
            'customer_id' => $retailerCustomerId,
            'type'        => 'phone',
            'value'       => mt_rand(10000000, 99999999),
            'label'       => 'home',
            'position'    => 0,
        ];

        $sfRetailerCustomerMeta[] = $retailerCustomerMeta;
    }

    private function getCustomerTags()
    {
        return [
            'sf_retailer_customer_tags_relationships' => [
                $this->createCustomerTagRelationship(32, 2, 0),
                $this->createCustomerTagRelationship(32, 3, 0),
                $this->createCustomerTagRelationship(32, 4, 0),
            ]
        ];
    }

    private function getStores()
    {
        $sfStore = [
            [
                'store_id'          => 100,
                'name'              => 'Store One',
                'timezone'          => 'America/New_York',
                'sf_identifier'     => 'store-one',
                'retailer_store_id' => self::STORE_1_RETAILERID,
                'store_user_id'     => self::STORE_USER_1,
                'country'           => 'CA',
                'region'            => 'QC',
                'city'              => 'Montreal',
                'address'           => '1455 Peel Streets',
                'postal'            => 'H3A 1T5',
                'phone'             => '************',
                'latitude'          => 45.4910,
                'longitude'         => -73.5658,
            ],
            [
                'store_id'          => 101,
                'name'              => 'Store Two',
                'timezone'          => 'America/New_York',
                'sf_identifier'     => 'store-two',
                'retailer_store_id' => self::STORE_2_RETAILERID,
                'store_user_id'     => 31,
                'country'           => 'CA',
                'region'            => 'QC',
                'city'              => 'Montreal',
                'address'           => '1455 Peel Streets',
                'postal'            => 'H3A 1T5',
                'phone'             => '************',
                'latitude'          => 45.4910,
                'longitude'         => -73.5658,
            ]
        ];

        return ['sf_store' => $sfStore];
    }

    private function getRepModeUsers()
    {
        return [
            'wp_users' => [
                $this->makeSellingModeUser(self::USER_1_STORE_1, 100),
                $this->makeSellingModeUser(self::USER_1_STORE_2, 101),
                $this->makeSellingModeUser(self::USER_2_STORE_2, 101),
                $this->makeSellingModeUser(self::USER_2_STORE_1, 100),
            ]];
    }

    private function getRepModeUsersWithEmployeeId()
    {
        return [
            'wp_users' => [
                $this->makeSellingModeUser(self::USER_1_STORE_1, 100, 'rep', 100),
                $this->makeSellingModeUser(self::USER_1_STORE_2, 100, 'rep', 101),
                $this->makeSellingModeUser(self::USER_2_STORE_2, 100, 'rep', 102),
                $this->makeSellingModeUser(self::USER_2_STORE_1, 100, 'rep', 103),
            ]];
    }

    private function getTeamModeUsers()
    {
        $users               = $this->getRepModeUsers();
        $users['wp_users'][] = $this->makeSellingModeUser(self::STORE_USER_1, 100, 'store', self::STORE_1_RETAILERID);
        $users['wp_users'][] = $this->makeSellingModeUser(self::STORE_USER_2, 101, 'store', self::STORE_2_RETAILERID);

        return $users;
    }

    private function getAutoDismissTasks()
    {
        $date     = new \DateTime(gmdate('Y-m-d H:i:00'), new \DateTimeZone('UTC'));
        $timezone = new \DateTimeZone('America/Montreal');
        $date->setTimezone($timezone);

        return [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail after updated",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => $date->format('Y-m-d H:i:s'),
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 0,
                ],
            ],
            'sf_task'            => [
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'test detail should be updated',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 8,
                    'status'    => 'unresolved',
                    'details'   => 'test detail should not be updated',
                    'parent_id' => 1,
                ],
            ],
            'wp_users'           => [
                [
                    'ID'                  => 8,
                    'user_login'          => 'fake-mall-new-rep',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall new rep',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 3003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 1,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
            'sf_store'           => [
                [
                    'store_id'          => 3003,
                    'name'              => 'Store',
                    'timezone'          => 'America/Los_Angeles',
                    'sf_identifier'     => 'store',
                    'retailer_store_id' => '515151',
                    'store_user_id'     => null,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ]
            ],
        ];
    }
    private function getCorpTasksWithEmptyStartDate()
    {

        return [
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail after updated",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "start_date"        => null,
                    "auto_dismiss_time" => "2019-04-23 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        => null,
                    "is_deleted"        => 0,
                ],
            ],
            'wp_users'           => [
                [
                    'ID'                  => 8,
                    'user_login'          => 'fake-mall-new-rep',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall new rep',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 3003,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 4,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
                [
                    'ID'                  => 9,
                    'user_login'          => 'fake-mall-new-admin-no-store',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'Fake Mall new rep',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'Fake Mall new admin',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 0,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 4,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0
                ],
            ],
            'sf_store'           => [
                [
                    'store_id'          => 3003,
                    'name'              => 'Store',
                    'timezone'          => 'America/Los_Angeles',
                    'sf_identifier'     => 'store',
                    'retailer_store_id' => '515151',
                    'store_user_id'     => null,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ]
            ],
        ];
    }

    public function populateCorporateTaskNoStartDate()
    {
        Fixtures::add('corporate-task-with-no-start', $this->getCorpTasksWithEmptyStartDate());
    }

    public function transactionRepMin()
    {
        $trx = [
            $this->makeSfTransaction(1, 'TRX1', 1, 5),
            $this->makeSfTransaction(1, 'TRX2', 1, 10),
            $this->makeSfTransaction(1, 'TRX3', 1, 20),
            $this->makeSfTransaction(1, 'TRX4', 1, 30),
            $this->makeSfTransaction(1, 'TRX5', 1, 100),
            $this->makeSfTransaction(1, 'TRX6', 1, 200),
            $this->makeSfTransaction(1, 'TRX7', 2, 100),
        ];

        Fixtures::add('sf_rep_transaction_min', [
            'sf_rep_transaction' => $trx
        ]);
    }

    public function transactionRetailerMin()
    {
        $trx = [
            $this->makeInsightsTransaction(40, 'TRX1', 10, 'lololol'),
            $this->makeInsightsTransaction(40, 'TRX2', 20, 'lololol'),
            $this->makeInsightsTransaction(40, 'TRX3', 100, 'lololol'),
            $this->makeInsightsTransaction(40, 'TRX4', 200, 'lololol'),
            $this->makeInsightsTransaction(41, 'TRX5', 100, 'lololol'),
        ];

        $this->createCustomer(40, 1, '1', $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);
        $this->createCustomer(41, 2, '1', $sfCustomer, $sfRetailerCustomers, $sfCustomersToRetailerCustomers);

        Fixtures::add('sf_retailer_transaction_min', [
            'sf_retailer_transaction'            => $trx,
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ]);
    }

    public function transactionRetailerEmployeeIdMin()
    {
        $trx = [
            $this->makeInsightsTransaction(12, 'TRX1', 10, 'lololol', null, 'Sale', '2546011'),
            $this->makeInsightsTransaction(12, 'TRX2', 20, 'lololol', null, 'Sale', '2546011'),
            $this->makeInsightsTransaction(12, 'TRX3', 101, 'lololol', null, 'Sale', '2546011'),
            $this->makeInsightsTransaction(12, 'TRX4', 200, 'lololol', null, 'Sale', '2546011'),
            $this->makeInsightsTransaction(22, 'TRX5', 100, 'lololol', null, 'Sale', '2546011'),
        ];

        $this->createTransactionCustomer(
            40,
            12,
            '1',
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );
        $this->createTransactionCustomer(
            41,
            22,
            '1',
            $sfCustomer,
            $sfRetailerCustomers,
            $sfCustomersToRetailerCustomers
        );

        Fixtures::add('sf_retailer_transaction_employee_min', [
            'sf_retailer_transaction'            => $trx,
            'sf_customer'                        => $sfCustomer,
            'sf_retailer_customers'              => $sfRetailerCustomers,
            'sf_customers_to_retailer_customers' => $sfCustomersToRetailerCustomers
        ]);
    }

    public function addDataForNewRepTransactions()
    {
        $folder = codecept_data_dir() . '/functional/NewRepTransactionCest';
        Fixtures::add(
            'test-team-mode-new-rep-transaction-tasks',
            json_decode(file_get_contents($folder . '/testTeamModeNewRepTransactionTasks.json'))
        );

        Fixtures::add(
            'test-rep-mode-new-rep-transaction-tasks',
            json_decode(file_get_contents($folder . '/testRepModeNewRepTransactionTasks.json'))
        );

        Fixtures::add(
            'test-team-mode-new-rep-transaction-tasks-with-new-retailer-transaction-filtered-active',
            json_decode(
                file_get_contents($folder
                    . '/testTeamModeNewRepTransactionTasksWithNewRetailerTransactionFilteredActive.json')
            )
        );

        Fixtures::add(
            'test-rep-mode-new-rep-transaction-tasks-with-new-retailer-transaction-filtered-active',
            json_decode(
                file_get_contents($folder
                    . '/testRepModeNewRepTransactionTasksWithNewRetailerTransactionFilteredActive.json')
            )
        );
    }

    public function addDataForNewRetailerTransactionFiltered()
    {
        $folder = codecept_data_dir() . '/functional/NewRetailerTransactionFilteredCest';
        Fixtures::add(
            'test-multiple-days',
            json_decode(file_get_contents($folder . '/testMultipleDays.json'))
        );

        Fixtures::add(
            'test-rep-mode-new-retailer-transaction-filtered',
            json_decode(file_get_contents($folder . '/testRepModeNewRetailerTransactionFiltered.json'))
        );

        Fixtures::add(
            'test-rep-mode-any-store-new-retailer-transaction-filtered',
            json_decode(file_get_contents($folder . '/testRepModeAnyStoreNewRetailerTransactionFiltered.json'))
        );

        Fixtures::add(
            'test-team-mode-new-retailer-transaction-filtered',
            json_decode(file_get_contents($folder . '/testTeamModeNewRetailerTransactionFiltered.json'))
        );

        Fixtures::add(
            'test-team-mode-any-store-new-retailer-transaction-filtered',
            json_decode(file_get_contents($folder . '/testTeamModeAnyStoreNewRetailerTransactionFiltered.json'))
        );
    }

    public function addDataForRetailerTransactionEmployeeAssignedCest()
    {
        $folder = codecept_data_dir() . '/functional/RetailerTransactionEmployeeAssignedCest';
        Fixtures::add(
            'test-with-matching-option',
            json_decode(file_get_contents($folder . '/testWithMatchingOption.json'))
        );

        Fixtures::add(
            'test-with-matching-option-multiple-days',
            json_decode(file_get_contents($folder . '/testWithMatchingOptionMultipleDays.json'))
        );

        Fixtures::add(
            'test-with-non-matching-option',
            json_decode(file_get_contents($folder . '/testWithNonMatchingOption.json'))
        );

        Fixtures::add(
            'test-with-non-matching-option-invalid-phone',
            json_decode(file_get_contents($folder . '/testWithNonMatchingOptionInvalidPhone.json'))
        );
    }

    public function addDataForTransactionMinCest()
    {
        $folder = codecept_data_dir() . '/functional/TransactionMinCest';
        Fixtures::add(
            'test-rep-transaction-min',
            json_decode(file_get_contents($folder . '/testRepTransactionMin.json'))
        );

        Fixtures::add(
            'test-retailer-transaction-min',
            json_decode(file_get_contents($folder . '/testRetailerTransactionMin.json'))
        );

        Fixtures::add(
            'test-retailer-transaction-employee-id-matching-min',
            json_decode(file_get_contents($folder . '/testRetailerTransactionEmployeeIdMatchingMin.json'))
        );
    }

    public function addDataForTransactionsDistributionByStoresTasksCest()
    {
        $folder = codecept_data_dir() . '/functional/TransactionsDistributionByStoresTasksCest';
        Fixtures::add(
            'test-equally-distribute-tasks',
            json_decode(file_get_contents($folder . '/testEquallyDistributeTasks.json'))
        );
    }

    public function insightsTransactionsDetails()
    {
        Fixtures::add('ci-transactions-rep-mode-details', [
            'sf_retailer_transaction_details' => [
                [
                    'trx_thread_id' => 'TRX7',
                    'trx_id'        => 'TRX7',
                    'trx_date'      => '2024-01-01 00:00:00',
                    'product_id'    => 123456,
                    'product_brand' => 'Brand 100',
                ],
            ],
        ]);
    }

    private function createTask($userId, $status, $automatedType, $createdDate, $reminderData)
    {
        return [
            "user_id" => $userId,
            "customer_id" => null,
            "task_category_id" => null,
            "status" => $status,
            "details" => "Some random task",
            "reminder_date" => $reminderData,
            "created_at" => $createdDate,
            "automated_type" => $automatedType,
        ];
    }

    public function addDataForExpiredTasks()
    {
        $daysAgo20 = gmdate('Y-m-d H:i:s', strtotime("-20 days"));
        $daysAgo9 = gmdate('Y-m-d H:i:s', strtotime("-9 days"));
        $daysAgo7 = gmdate('Y-m-d H:i:s', strtotime("-7 days"));
        $daysAgo4 = gmdate('Y-m-d H:i:s', strtotime("-4 days"));

        $taskType = [
            'nrt' => TaskModel::AUTOMATED_TYPE_NEW_REP_TRANSACTION,
            'nag_share' => TaskModel::AUTOMATED_TYPE_NAG_SHARE_UPDATE,
        ];

        Fixtures::add('expired-transactions', [
            'sf_store' => [
                $this->createStore(100, '1111', 2000),
                $this->createStore(101, '1112', 2001),
            ],
            'wp_users' => [
                $this->makeSellingModeUser(1000, 100, 'rep', 100, 'fr_CA'),
                $this->makeSellingModeUser(1001, 100, 'rep', 101, 'fr_CA'),
                $this->makeSellingModeUser(1002, 100, 'rep', 102, 'fr_CA'),
                $this->makeSellingModeUser(1003, 100, 'rep', 103, 'fr_CA'),
            ],
            'sf_task' => [
                // varying reminder dates
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo20, $daysAgo20),
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo20, $daysAgo9),
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo20, $daysAgo7),
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo20, $daysAgo4),

                // varying creation dates
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo4, $daysAgo7),
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo7, $daysAgo7),
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo9, $daysAgo7),
                $this->createTask(1000, 'unresolved', $taskType['nrt'], $daysAgo20, $daysAgo7),

                // another task type
                $this->createTask(1000, 'unresolved', $taskType['nag_share'], $daysAgo20, $daysAgo20),
                $this->createTask(1000, 'unresolved', $taskType['nag_share'], $daysAgo20, $daysAgo9),
                $this->createTask(1000, 'unresolved', $taskType['nag_share'], $daysAgo20, $daysAgo7),
                $this->createTask(1000, 'unresolved', $taskType['nag_share'], $daysAgo20, $daysAgo4),

                // resolved status type
                $this->createTask(1000, 'resolved', $taskType['nrt'], $daysAgo20, $daysAgo20),
                $this->createTask(1000, 'resolved', $taskType['nrt'], $daysAgo20, $daysAgo9),
                $this->createTask(1000, 'resolved', $taskType['nrt'], $daysAgo20, $daysAgo7),
                $this->createTask(1000, 'resolved', $taskType['nrt'], $daysAgo20, $daysAgo4),

                // dismissed status type
                $this->createTask(1000, 'dismissed', $taskType['nrt'], $daysAgo20, $daysAgo20),
                $this->createTask(1000, 'dismissed', $taskType['nrt'], $daysAgo20, $daysAgo9),
                $this->createTask(1000, 'dismissed', $taskType['nrt'], $daysAgo20, $daysAgo7),
                $this->createTask(1000, 'dismissed', $taskType['nrt'], $daysAgo20, $daysAgo4),
            ]
        ]);
    }

    private function getCustomerEventsRepMode()
    {
        $sfCustomerEvent = [
            [
                'id'             => 1000,
                'customer_id'    => 42,
                'label'          => 'birthday',
                'day'            => gmdate('d', strtotime(' + 1 day')),
                'month'          => gmdate('m', strtotime(' + 1 day')),
                'year'           => null,
                'created_at'     => '2024-03-03 12:00:00',
                'updated_at'     => '2024-03-03 12:00:00'
            ],
            [
                'id'             => 1001,
                'customer_id'    => 32,
                'label'          => 'other',
                'day'            => gmdate('d', strtotime(' + 2 day')),
                'month'          => gmdate('m', strtotime(' + 2 day')),
                'year'           => null,
                'created_at'     => '2024-03-04 12:00:00',
                'updated_at'     => '2024-03-04 12:00:00'
            ],
            [
                'id'             => 1002,
                'customer_id'    => 43,
                'label'          => 'birthday',
                'day'            => gmdate('d', strtotime(' + 1 day')),
                'month'          => gmdate('m', strtotime(' + 1 day')),
                'year'           => null,
                'created_at'     => '2024-03-03 12:00:00',
                'updated_at'     => '2024-03-03 12:00:00'
            ],
            [
                'id'             => 1003,
                'customer_id'    => 44,
                'label'          => 'birthday',
                'day'            => gmdate('d', strtotime(' + 1 day')),
                'month'          => gmdate('m', strtotime(' + 1 day')),
                'year'           => null,
                'created_at'     => '2024-03-03 12:00:00',
                'updated_at'     => '2024-03-03 12:00:00'
            ],
        ];

        return ['sf_customer_events' => $sfCustomerEvent];
    }

    private function getCustomerEventsTeamMode()
    {
        $sfCustomerEvent = [
            [
                'id'             => 1004,
                'customer_id'    => 40,
                'label'          => 'birthday',
                'day'            => gmdate('d', strtotime(' + 2 day')),
                'month'          => gmdate('m', strtotime(' + 2 day')),
                'year'           => null,
                'created_at'     => '2024-03-03 12:00:00',
                'updated_at'     => '2024-03-03 12:00:00'
            ],
            [
                'id'             => 1005,
                'customer_id'    => 46,
                'label'          => 'birthday',
                'day'            => gmdate('d', strtotime(' + 3 day')),
                'month'          => gmdate('m', strtotime(' + 3 day')),
                'year'           => null,
                'created_at'     => '2024-03-04 12:00:00',
                'updated_at'     => '2024-03-04 12:00:00'
            ],
        ];

        return ['sf_customer_events' => $sfCustomerEvent];
    }
}
