<?php

namespace SF\functional\Tasks;

use SF\FunctionalTester;
use Salesfloor\Models\Task;
use Codeception\Util\Fixtures;
use Salesfloor\Services\Tasks\Automated\BaseScanner;

/**
 * Tests for NewRetailerTransaction tasks.
 *
 * Moved from the api/services/TasksCest, as the api suite is unable to test in both team and rep mode
 * and we have to cover both scenarios now. Originally, we only had code for CI tasks for team mode
 * because of <PERSON>, but now anyone could have it so we can't make that assumption.
 *
 * See fixtures for explanation of the various scenarios created.
 */
class NewRetailerTransactionFilteredCest extends TaskFunctional
{
    const TASK_ID           = Task::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED;
    const REP_MODE_FIXTURE  = 'ci-transactions-rep-mode';
    const TEAM_MODE_FIXTURE = 'ci-transactions-team-mode';

    protected function runTaskScanner($I = null)
    {
        $scannerName = BaseScanner::SERVICE_BASE . Task::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE;
        $scanner = $this->app[$scannerName];

        return $scanner->process();
    }

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.any_store'] = false;
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 3;
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [7];
    }

    protected function setUpAnyStoreMode()
    {
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.any_store'] = true;
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 4;
    }

    /** @group database_transaction */
    public function testMultipleDays(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [3, 7];
        $tasksCreated = $this->runTaskScanner($I);

        // 1 from 3 days ago
        // 4 from 7 days ago
        $I->assertEquals(5, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $I->assertCount(5, $tasks);
        $I->assertCount(5, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-multiple-days');
        foreach ($tasks as $i => $task) {
            $tasksRetailerTransaction = $exceptedData[$i];
            $this->assertRetailerTransactionMatch($I, $task['id'], $tasksRetailerTransaction->trx_thread_id);
        }
    }

    /** @group database_transaction */
    public function testMultipleDaysDynamicRuleHappyPath(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [3, 7];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [];

        $tasksCreated = $this->runTaskScanner($I);

        // 1 from 3 days ago + 4 from 7 days ago
        $I->assertEquals(5, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $actuals = array_map(function (array $task) {
            return $task['trx_thread_id'];
        }, $tasks);
        $expected = [
            // 3 days ago
            'TRX7',
            // 7 days ago
            'TRX3',
            'TRX2',
            'TRX43',
            'TRX6',
        ];
        $I->assertEquals($expected, $actuals);
    }

    /** @group database_transaction */
    public function testMultipleDaysDynamicRuleConfigSwitchCombine(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [3];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [
            'whatever_key_here' => [
                'days_search_back' => [7],
                'min'              => 5500,
                'filter_function'  => null,
            ],
        ];
        $tasksCreated = $this->runTaskScanner($I);

        // 1 from 3 days ago + 1 from 7 days ago
        $I->assertEquals(2, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $actuals = array_map(function (array $task) {
            return $task['trx_thread_id'];
        }, $tasks);
        $expected = [
            // 3 days ago
            'TRX7',
            // 7 days ago
            'TRX3',
        ];
        $I->assertEquals($expected, $actuals);
    }

    /** @group database_transaction */
    public function testMultipleDaysDynamicRuleConfigSwitchDynamicRuleOnly(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [
            'default' => [
                'days_search_back' => [3],
            ],
            'whatever_key_here' => [
                'days_search_back' => [7],
                'min'              => 5500,
                'filter_function'  => null,
            ],
        ];
        $tasksCreated = $this->runTaskScanner($I);

        // 1 from 3 days ago + 1 from 7 days ago
        $I->assertEquals(2, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $actual = array_map(function (array $task) {
            return $task['trx_thread_id'];
        }, $tasks);
        $expected = [
            // 3 days ago
            'TRX7',
            // 7 days ago
            'TRX3',
        ];
        $I->assertEquals($expected, $actual);
    }

    /** @group database_transaction */
    public function testMultipleDaysDynamicRuleConfigSwitchDynamicRuleOnlyFilterSubscription1(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $retailerCustomerIds = [32, 33, 40, 41, 43, 44, 45, 46, 47, 54];
        foreach ($retailerCustomerIds as $retailerCustomerId) {
            $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => $retailerCustomerId]);
            $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => 2, 'is_subscribed_sms_marketing' => 2], ['customer_id' => $retailerCustomerId]);
        }
        // enable the 3 days back
        $idToAllow = 47;
        $subValue = 1;
        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => $subValue, 'sms_marketing_subscription_flag' => $subValue], ['id' => $idToAllow]);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => $subValue, 'is_subscribed_sms_marketing' => $subValue], ['customer_id' => $idToAllow]);

        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered_multiple.days_search_back'] = [];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [
            'default'           => [
                'days_search_back' => [3],
            ],
            'whatever_key_here' => [
                'days_search_back' => [7],
                'min'              => 5500,
                'filter_function'  => null,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        // 1 from 3 days ago
        $I->assertEquals(1, $tasksCreated);
    }

    /** @group database_transactionaa */
    public function testMultipleDaysDynamicRuleConfigSwitchDynamicRuleOnlyFilterSubscription2(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $retailerCustomerIds = [32, 33, 40, 41, 43, 44, 45, 46, 47, 54];
        foreach ($retailerCustomerIds as $retailerCustomerId) {
            $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => $retailerCustomerId]);
            $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => 2, 'is_subscribed_sms_marketing' => 2], ['customer_id' => $retailerCustomerId]);
        }
        // enable the 7 days back over min
        $idToAllow = 41;
        $subValue = 1;
        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => $subValue, 'sms_marketing_subscription_flag' => $subValue], ['id' => $idToAllow]);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => $subValue, 'is_subscribed_sms_marketing' => $subValue], ['customer_id' => $idToAllow]);

        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered_multiple.days_search_back'] = [];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [
            'default'           => [
                'days_search_back' => [3],
            ],
            'whatever_key_here' => [
                'days_search_back' => [7],
                'min'              => 5500,
                'filter_function'  => null,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        // 3 from 7 days ago
        $I->assertEquals(3, $tasksCreated);
    }

    /** @group database_transaction */
    public function testMultipleDaysDynamicRuleConfigSwitchDynamicRuleOnlyFilterSubscription3Null(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $retailerCustomerIds = [32, 33, 40, 41, 43, 44, 45, 46, 47, 54];
        foreach ($retailerCustomerIds as $retailerCustomerId) {
            $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => $retailerCustomerId]);
            $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => 2, 'is_subscribed_sms_marketing' => 2], ['customer_id' => $retailerCustomerId]);
        }
        // enable the 7 days back over min
        $idToAllow = 41;
        $subValue = 1;
        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => $subValue, 'sms_marketing_subscription_flag' => $subValue], ['id' => $idToAllow]);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => null, 'is_subscribed_sms_marketing' => null], ['customer_id' => $idToAllow]);

        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered_multiple.days_search_back'] = [];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [
            'default'           => [
                'days_search_back' => [3],
            ],
            'whatever_key_here' => [
                'days_search_back' => [7],
                'min'              => 5500,
                'filter_function'  => null,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        // 3 from 7 days ago
        $I->assertEquals(3, $tasksCreated);
    }


    /** @group database_transaction */
    public function testMultipleDaysDynamicRuleConfigSwitchCondition(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [];
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [
            'whatever_key_here'         => [
                'days_search_back' => [3],
                'filter_function'  => null,
                'min'              => 9999,
            ],
            'whatever_another_key_here' => [
                'days_search_back' => [7],
                'filter_function'  => null,
            ],
        ];
        $tasksCreated = $this->runTaskScanner($I);
        // 0 from 3 days ago + 4 from 7 days ago
        $I->assertEquals(4, $tasksCreated);

        // 7 days ago
        // note: this test is fragile since it's rely on auto increment primary key which could easily changed
        // I keep it since most of test case were wrote in this way
        $tasks = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $actual = array_map(function (array $task) {
            return $task['trx_thread_id'];
        }, $tasks);
        $expected = [
            'TRX3',
            'TRX2',
            'TRX43',
            'TRX6',
        ];
        $I->assertEquals($expected, $actual);
    }

    /** @group database_transaction */
    public function testRepModeNewRetailerTransactionFiltered(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $tasksCreated = $this->runTaskScanner($I);

        $I->assertEquals(4, $tasksCreated);
        // Validate correct task assignments.
        // We don't test the generation of the reminder time. That's tested elsewhere (TasksCest), and no changes have
        // been made to how that functions for this specific task.
        // We should see 4 total tasks
        // User 20 has 3 tasks, despite having more transactions and customers, due to the max per owner filter.

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        echo json_encode(['tasks' => $tasks, 'tasksRetailerTransactions' => $tasksRetailerTransactions]);

        $I->assertCount(4, $tasks);
        $I->assertCount(4, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-rep-mode-new-retailer-transaction-filtered');
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        foreach ($tasks as $i => $task) {
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $this->assertRetailerTransactionMatch($I, $task['id'], $tasksRetailerTransaction->trx_thread_id);
        }
    }

    /** @group database_transaction */
    public function testRepModeAnyStoreNewRetailerTransactionFiltered(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->setUpAnyStoreMode();
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(5, $tasksCreated);
        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        echo json_encode(['tasks' => $tasks, 'tasksRetailerTransactions' => $tasksRetailerTransactions]);

        $I->assertCount(5, $tasks);
        $I->assertCount(5, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-rep-mode-any-store-new-retailer-transaction-filtered');
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        foreach ($tasks as $i => $task) {
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $this->assertRetailerTransactionMatch($I, $task['id'], $tasksRetailerTransaction->trx_thread_id);
        }
    }

    /** @group database_transaction */
    public function testTeamModeNewRetailerTransactionFiltered(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(3, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        echo json_encode(['tasks' => $tasks, 'tasksRetailerTransactions' => $tasksRetailerTransactions]);

        $I->assertCount(3, $tasks);
        $I->assertCount(3, $tasksRetailerTransactions);
        $exceptedData = Fixtures::get('test-team-mode-new-retailer-transaction-filtered');
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        foreach ($tasks as $i => $task) {
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $this->assertRetailerTransactionMatch($I, $task['id'], $tasksRetailerTransaction->trx_thread_id);
        }
    }

    /** @group database_transaction */
    public function testTeamModeAnyStoreNewRetailerTransactionFiltered(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);
        $this->setUpAnyStoreMode();
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(5, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        echo json_encode(['tasks' => $tasks, 'tasksRetailerTransactions' => $tasksRetailerTransactions]);

        $I->assertCount(5, $tasks);
        $I->assertCount(5, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-team-mode-any-store-new-retailer-transaction-filtered');
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        foreach ($tasks as $i => $task) {
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $this->assertRetailerTransactionMatch($I, $task['id'], $tasksRetailerTransaction->trx_thread_id);
        }
    }
}
