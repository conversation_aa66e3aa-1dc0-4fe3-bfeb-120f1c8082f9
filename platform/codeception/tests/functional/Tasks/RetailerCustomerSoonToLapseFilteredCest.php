<?php

namespace SF\functional\Tasks;

use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use SF\functional\Tasks\Fixture as F;
use SF\FunctionalTester;

class RetailerCustomerSoonToLapseFilteredCest extends TaskFunctional
{
    const TASK_ID           = Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED;
    const REP_MODE_FIXTURE  = 'ci-transactions-soon-to-lapse-rep-mode2';
    const TEAM_MODE_FIXTURE = 'ci-transactions-soon-to-lapse-team-mode';

    protected function runTaskScanner($I = null)
    {
        $scannerName = BaseScanner::SERVICE_BASE . Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED_MULTIPLE;
        $scanner = $this->app[$scannerName];

        return $scanner->process();
    }

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [];
    }

    /** @group database_transaction */
    public function testTeamModeRetailerCustomerSoonToLapseFilteredTasksGeneration(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);

        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
            'default' => [
                'days_search_back' => [120],
                'max_per_owner'    => 3,
            ],
        ];

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $this->assertTaskCreated($I, 40, F::STORE_USER_1);
        $this->assertTaskCreated($I, 41, F::STORE_USER_1);

        $this->assertNoTaskCreated($I, 42, F::STORE_USER_1);
        $this->assertNoTaskCreated($I, 33, F::STORE_USER_2);
    }

    /** @group database_transaction */
    public function testRepModeRetailerCustomerSoonToLapseFilteredTasksGeneration(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
            'default' => [
                'days_search_back' => [120],
                'max_per_owner'    => 3,
            ],
            'secondary' => [
                'days_search_back' => [240],
                'max_per_owner'    => 3,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        $I->assertEquals(2, $tasksCreated);

        $this->assertTaskCreated($I, 40, F::USER_1_STORE_1);
        $this->assertTaskCreated($I, 41, F::USER_1_STORE_1); // The fixture is populating the user_id as 20, and here's a unmatch from the fixture.

        $this->assertNoTaskCreated($I, 42, F::USER_1_STORE_1);
        $this->assertNoTaskCreated($I, 33, F::USER_1_STORE_2);
    }
}
