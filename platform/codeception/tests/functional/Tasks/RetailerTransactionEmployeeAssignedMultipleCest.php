<?php

namespace SF\functional\Tasks;

use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use SF\FunctionalTester;
use SF\functional\Tasks\Fixture as F;

class RetailerTransactionEmployeeAssignedMultipleCest extends TaskFunctional
{
    const TASK_ID           = Task::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED;
    const REP_MODE_FIXTURE  = 'ci-transactions-with-employee-assigned';
    const MULTIPLE_DAYS_FIXTURE = 'ci-transactions-with-employee-assigned-multiple-days';

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 3;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 7;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
    }

    /** @group database_transaction */
    public function testWithMatchingOptionMultipleDays(FunctionalTester $I)
    {
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->setUpRepMode($I);
        $this->setUpMultipleDays($I);

        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [27, 7];

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(4, $tasksCreated);

        $I->assertNumberRowsInTable(2, 'sf_task', [
            'user_id'     => F::USER_1_STORE_1,
            'customer_id' => 40,
        ]);

        $I->assertNumberRowsInTable(2, 'sf_task', [
            'user_id'     => F::USER_1_STORE_2,
            'customer_id' => 41,
        ]);

        $I->seeInDatabase('sf_task_retailer_transaction', ['trx_thread_id' => 'TRX101',]);
        $I->seeInDatabase('sf_task_retailer_transaction', ['trx_thread_id' => 'TRX143',]);
        $I->seeInDatabase('sf_task_retailer_transaction', ['trx_thread_id' => 'TRX1',]);
        $I->seeInDatabase('sf_task_retailer_transaction', ['trx_thread_id' => 'TRX43',]);
    }

    /** @group database_transaction */
    public function testWithMatchingOptionMultipleDaysFilterSubscription(FunctionalTester $I)
    {
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->setUpRepMode($I);
        $this->setUpMultipleDays($I);
        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => 40]);

        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [27, 7];

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);
    }


    /** @group database_transaction */
    public function testWithMatchingOptionMultipleDaysFilterSubscriptionNull(FunctionalTester $I)
    {
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->setUpRepMode($I);
        $this->setUpMultipleDays($I);
        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => 40]);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => null, 'is_subscribed_sms_marketing' => null], ['id' => 1]);

        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [27, 7];

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);
    }

    protected function runTaskScanner($I = null)
    {
        $scannerName = BaseScanner::SERVICE_BASE . Task::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE;
        $scanner = $this->app[$scannerName];

        return $scanner->process();
    }
}
