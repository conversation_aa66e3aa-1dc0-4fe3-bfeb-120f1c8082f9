<?php

namespace SF\functional\Tasks;

use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use SF\FunctionalTester;

class RetailerCustomerSoonToLapseFilteredSecondaryEmployeeAssignedCest extends TaskFunctional
{
    const TASK_ID           = Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED;
    const REP_MODE_FIXTURE  = 'ci-transactions-soon-to-lapse-secondary-employee-assign-rep-mode';
    const TEAM_MODE_FIXTURE = 'ci-transactions-soon-to-lapse-secondary-employee-assign-team-mode';

    protected function getTaskScanner($I = null)
    {
        $scannerName = BaseScanner::SERVICE_BASE . Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED_MULTIPLE;
        $scanner = $this->app[$scannerName];

        return $scanner;
    }

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
            'default' => [
                'days_search_back' => [120],
                'max_per_owner'    => 3,
            ],
        ];
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled'] = true;
    }

    /** @group database_transaction */
    public function testTeamModeRetailerSoonToLapseFilteredTask(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);

        $scanner = $this->getTaskScanner();
        $rules = $scanner->getAllDynamicRuleSets();
        if (empty($rules)) {
            return;
        }

        foreach ($rules as $ruleKey => $ruleDetails) {
            $configDaysPerRule = $scanner->getMultipleDaysBackFromConfigPerRule($ruleKey);
            foreach ($configDaysPerRule as $daysBack) {
                $instance = $scanner->getSingleTaskScanner();
                $instance->setDaysBack($daysBack);
                $instance->bindRule($ruleDetails);

                $matchedTasks = $this->getMethod($instance, "processScanForEmployeeIdMatchTasks")->invokeArgs($instance, []);
                $I->assertCount(1, $matchedTasks);

                $noMatchedTasks = $this->getMethod($instance, "processScanForEmployeeIdNoMatchTasks")->invokeArgs($instance, []);
                $I->assertCount(2, $noMatchedTasks);

                $allTasks = $this->getMethod($instance, "mergeSecondaryEmployeeAssignTasks")->invokeArgs($instance, [$matchedTasks, $noMatchedTasks]);
                $I->assertCount(3, $allTasks);
            }
        }
    }

    /** @group database_transaction */
    public function testRepModeRetailerSoonToLapseFilteredTask(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $scanner = $this->getTaskScanner();
        $rules = $scanner->getAllDynamicRuleSets();
        if (empty($rules)) {
            return;
        }

        foreach ($rules as $ruleKey => $ruleDetails) {
            $configDaysPerRule = $scanner->getMultipleDaysBackFromConfigPerRule($ruleKey);
            foreach ($configDaysPerRule as $daysBack) {
                $instance = $scanner->getSingleTaskScanner();
                $instance->setDaysBack($daysBack);
                $instance->bindRule($ruleDetails);

                $matchedTasks = $this->getMethod($instance, "processScanForEmployeeIdMatchTasks")->invokeArgs($instance, []);
                $I->assertCount(1, $matchedTasks);

                $noMatchedTasks = $this->getMethod($instance, "processScanForEmployeeIdNoMatchTasks")->invokeArgs($instance, []);
                $I->assertCount(2, $noMatchedTasks);

                $allTasks = $this->getMethod($instance, "mergeSecondaryEmployeeAssignTasks")->invokeArgs($instance, [$matchedTasks, $noMatchedTasks]);
                $I->assertCount(3, $allTasks);
            }
        }
    }
}
