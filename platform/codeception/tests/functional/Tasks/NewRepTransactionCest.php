<?php

namespace SF\functional\Tasks;

use SF\FunctionalTester;
use Salesfloor\Models\Task;
use Codeception\Util\Fixtures;

class NewRepTransactionCest extends TaskFunctional
{
    public const TASK_ID           = Task::AUTOMATED_TYPE_NEW_REP_TRANSACTION;
    public const REP_MODE_FIXTURE  = 'sf-transactions-rep-mode';
    public const TEAM_MODE_FIXTURE = 'sf-transactions-team-mode';

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.new_rep_transaction.enabled'] = true;
        $this->app['configs']['sf.task.automated.new_rep_transaction.max_per_owner'] = 3;
        $this->app['configs']['sf.task.automated.new_rep_transaction.days_search_back'] = 7;
    }

    /** @group database_transaction */
    public function testTeamModeNewRepTransactionTasks(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(5, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRepTransactions = $I->grabRowsFromDatabase('sf_task_rep_transaction');

        $I->assertCount(5, $tasks);
        $I->assertCount(5, $tasksRepTransactions);

        $exceptedData = Fixtures::get('test-team-mode-new-rep-transaction-tasks');
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        // Validate the correct associations have been set up with TRX
        // This will check if we grabbed the right max value transaction.
        foreach ($tasks as $i => $task) {
            $tasksRepTransaction = $exceptedData->tasksRepTransactions[$i];
            $this->assertRepTransactionMatch($I, $task['id'], $tasksRepTransaction->trx_id);
        }
    }

    /** @group database_transaction */
    public function testRepModeNewRepTransactionTasksMinCount1(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        // this is default value of current file setup at applyGeneralConfigs
        // $this->app['configs']['sf.task.automated.new_rep_transaction.max_per_owner'] = 3;

        $tasksCreated = $this->runTaskScanner();

        // user_id(20) 3 + user_id(21) 1 = 4
        $I->assertEquals(4, $tasksCreated);
    }

    /** @group database_transaction */
    public function testRepModeNewRepTransactionTasksMinCount2(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_rep_transaction.max_per_owner'] = 4;

        $tasksCreated = $this->runTaskScanner();

        // user_id(20) 4 + user_id(21) 1 = 5
        $I->assertEquals(5, $tasksCreated);
    }

    /** @group database_transaction */
    public function testRepModeNewRepTransactionTasksBasic(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $tasksCreated = $this->runTaskScanner();

        // user_id(20) 3 + user_id(21) 1 = 4
        $I->assertEquals(4, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRepTransactions = $I->grabRowsFromDatabase('sf_task_rep_transaction');

        $I->assertCount(4, $tasks);
        $I->assertCount(4, $tasksRepTransactions);

        $exceptedData = Fixtures::get('test-rep-mode-new-rep-transaction-tasks');
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        // Validate the correct associations have been set up with TRX
        // This will check if we grabbed the right max value transaction.
        foreach ($tasks as $i => $task) {
            $tasksRepTransaction = $exceptedData->tasksRepTransactions[$i];
            $this->assertRepTransactionMatch($I, $task['id'], $tasksRepTransaction->trx_id);
        }
    }

    /** @group database_transaction */
    public function testTeamModeNewRepTransactionTasksWithNewRetailerTransactionFilteredActive(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.enabled'] = true;

        $tasksCreated = $this->runTaskScanner();
        $I->assertEquals(4, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRepTransactions = $I->grabRowsFromDatabase('sf_task_rep_transaction');

        $exceptedData = Fixtures::get(
            'test-team-mode-new-rep-transaction-tasks-with-new-retailer-transaction-filtered-active'
        );
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        // Validate the correct associations have been set up with TRX
        // This will check if we grabbed the right max value transaction.
        foreach ($tasks as $i => $task) {
            $tasksRepTransaction = $exceptedData->tasksRepTransactions[$i];
            $this->assertRepTransactionMatch($I, $task['id'], $tasksRepTransaction->trx_id);
        }
    }

    /** @group database_transaction */
    public function testRepModeNewRepTransactionTasksWithNewRetailerTransactionFilteredActive(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.enabled'] = true;

        $tasksCreated = $this->runTaskScanner();
        $I->assertEquals(4, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRepTransactions = $I->grabRowsFromDatabase('sf_task_rep_transaction');

        $I->assertCount(4, $tasks);
        $I->assertCount(4, $tasksRepTransactions);

        $exceptedData = Fixtures::get(
            'test-rep-mode-new-rep-transaction-tasks-with-new-retailer-transaction-filtered-active'
        );
        foreach ($exceptedData->tasks as $task) {
            $this->assertTaskCreated($I, $task->customer_id, $task->user_id);
        }

        // Validate the correct associations have been set up with TRX
        // This will check if we grabbed the right max value transaction.
        foreach ($tasks as $i => $task) {
            $tasksRepTransaction = $exceptedData->tasksRepTransactions[$i];
            $this->assertRepTransactionMatch($I, $task['id'], $tasksRepTransaction->trx_id);
        }
    }
}
