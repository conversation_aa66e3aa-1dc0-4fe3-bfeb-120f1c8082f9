<?php

namespace SF\functional\Tasks;

use Salesfloor\Models\Task;
use SF\FunctionalTester;
use SF\functional\Tasks\Fixture as F;

class SoonToLapseCest extends TaskFunctional
{
    const TASK_ID           = Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED;
    const REP_MODE_FIXTURE  = 'ci-transactions-soon-to-lapse-rep-mode';
    const TEAM_MODE_FIXTURE = 'ci-transactions-soon-to-lapse-team-mode';


    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled']          = true;
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner']    = 3;
        $this->app['configs']['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 120;
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @group database_transaction
     */
    public function testTeamModeSoonToLapseTask(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $this->assertTaskCreated($I, 40, F::STORE_USER_1);
        $this->assertTaskCreated($I, 41, F::STORE_USER_1);

        $this->assertNoTaskCreated($I, 42, F::STORE_USER_1);
        $this->assertNoTaskCreated($I, 33, F::STORE_USER_2);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @group database_transaction
     */
    public function testRepModeSoonToLapseTask(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $this->assertTaskCreated($I, 40, F::USER_1_STORE_1);
        $this->assertTaskCreated($I, 41, F::USER_1_STORE_1); // The fixture is populating the user_id as 20, and here's a unmatch from the fixture.

        $this->assertNoTaskCreated($I, 42, F::USER_1_STORE_1);
        $this->assertNoTaskCreated($I, 33, F::USER_1_STORE_2);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @group database_transaction
     */
    public function testRepModeSoonToLapseTaskFilterSubscription1(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => 40]);

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @group database_transaction
     */
    public function testRepModeSoonToLapseTaskFilterSubscription2(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => 41]);

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @group database_transaction
     */
    public function testRepModeSoonToLapseTaskFilterSubscription3Null(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => 41]);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => null, 'is_subscribed_sms_marketing' => null], ['customer_id' => '40']);

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);
    }
}
