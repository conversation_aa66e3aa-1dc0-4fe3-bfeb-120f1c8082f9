<?php

namespace SF\functional\Tasks;

use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use SF\FunctionalTester;

/**
 * Tests for RetailerCustomerEvent tasks.
 *
 * Moved from the api/services/TasksCest, as the api suite is unable to test in both team and rep mode
 * and we have to cover both scenarios now. Originally, we only had code for CI tasks for team mode
 * because of <PERSON>, but now anyone could have it so we can't make that assumption.
 *
 * See fixtures for explanation of the various scenarios created.
 */
class RetailerCustomerEventCest extends TaskFunctional
{
    const TASK_ID           = Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT;
    public const REP_MODE_FIXTURE  = 'retailer-customer-event-rep-mode';
    public const TEAM_MODE_FIXTURE = 'retailer-customer-event-team-mode';
    protected function runTaskScanner($I = null)
    {
        $scannerName = BaseScanner::SERVICE_BASE . Task::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT_MULTIPLE;
        $scanner = $this->app[$scannerName];

        return $scanner->process();
    }

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.retailer_customer_event.enabled'] = true;
        $this->app['configs']['sf.task.automated.retailer_customer_event.days_search_back'] = 0;
        $this->app['configs']['sf.task.automated.retailer_customer_event.dynamic_rule'] = [];
    }

    /** @group database_transaction */
    public function testRepModeRetailerCustomerEventTasksGeneration(FunctionalTester $I)
    {
        $this->setUpRepMode($I);

        $this->app['configs']['sf.task.automated.retailer_customer_event.dynamic_rule'] = [
            'birthday' => [
                'days_search_back' => [1], // 1 day after
                'max_per_owner'    => 2,   // Here max_per_owner is given 2, which will restrict tasks generation for birthday (2 instead 3)
            ],
            'other' => [
                'days_search_back' => [2], // 2 day after
                'max_per_owner'    => 2,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        $I->assertEquals(3, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');

        $I->assertCount(3, $tasks);

        $actuals = array_map(function (array $task) {
            return $task['user_id'];
        }, $tasks);
        $expected = [
            // rep's Ids
            20,
            20,
            21,
        ];
        $I->assertEquals($expected, $actuals);
    }

    /** @group database_transaction */
    public function testRepModeRetailerCustomerEventTasksGenerationFilterSubscription(FunctionalTester $I)
    {
        // similar test data as testRepModeRetailerCustomerEventTasksGeneration but customer subscription flag changes
        $I->wantToTest('RetailerCustomerEvent tasks generation filter subscription');

        $this->setUpRepMode($I);
        $I->updateInDatabase('sf_customer', ['subcribtion_flag' => 2, 'sms_marketing_subscription_flag' => 2], ['id' => 42]);

        $this->app['configs']['sf.task.automated.retailer_customer_event.dynamic_rule'] = [
            'birthday' => [
                'days_search_back' => [1], // 1 day after
                'max_per_owner'    => 2,   // Here max_per_owner is given 2, which will restrict tasks generation for birthday (2 instead 3)
            ],
            'other'    => [
                'days_search_back' => [2], // 2 day after
                'max_per_owner'    => 2,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        $I->assertEquals(2, $tasksCreated);
    }

    /** @group database_transaction */
    public function testTeamModeRetailerCustomerEventTasksGenerationDynamicRule(FunctionalTester $I)
    {
        $this->setUpTeamMode($I);

        $this->app['configs']['sf.task.automated.retailer_customer_event.dynamic_rule'] = [
            'default' => [
                'days_search_back' => [2], // 2 days after
                'max_per_owner'    => 2,
            ],
            'whatever_key_here' => [
                'days_search_back' => [3], // 3 days after
                'max_per_owner'    => 3,
            ],
        ];

        $tasksCreated = $this->runTaskScanner($I);

        $I->assertEquals(2, $tasksCreated);

        $tasks = $I->grabRowsFromDatabase('sf_task');

        $I->assertCount(2, $tasks);

        $actuals = array_map(function (array $task) {
            return $task['user_id'];
        }, $tasks);
        $expected = [
            // Store Ids
            30,
            31,
        ];
        $I->assertEquals($expected, $actuals);
    }
}
