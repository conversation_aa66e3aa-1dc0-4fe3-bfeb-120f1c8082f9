<?php

namespace SF\functional\Tasks;

use SF\FunctionalTester;
use Salesfloor\Models\Task;
use Codeception\Util\Fixtures;

class TransactionsDistributionByStoresTasksCest extends TaskFunctional
{
    const TASK_ID = Task::AUTOMATED_TYPE_TRANSACTION_DISTRIBUTION_BY_STORES;
    const REP_MODE_FIXTURE  = 'automated-task-distribution-test-suite-data';

    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.transactions_distribution_by_stores.max_per_owner'] = 5;
        $this->app['configs']['sf.task.automated.transactions_distribution_by_stores.days_search_back'] = 5;
        $this->app['configs']['sf.task.automated.transactions_distribution_by_stores.is_enabled'] = true;
        $this->app['configs']['sf.task.automated.transactions_distribution_by_stores.distance_radius'] = 99999;
        $this->app['configs']['sf.task.automated.transaction.min'] = 100;
    }

    /**
     * Equally distribute task created from the transactions.
     *
     * @param FunctionalTester $I
     * @group database_transaction
     */
    public function testEquallyDistributeTasks(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $tasksCreated = $this->runTaskScanner();
        $I->assertEquals(3, $tasksCreated); // two physical store tasks and 1 online store task.

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $customers = $I->grabRowsFromDatabase('sf_customer');

        $I->assertCount(3, $tasks);
        $I->assertCount(3, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-equally-distribute-tasks');

        // Remove current first two customers.
        $customers = array_slice($customers, 2);

        foreach ($customers as $i => $customer) {
            $task = $exceptedData->tasks[$i];
            $this->assertTaskCreated($I, $customer['ID'], $task->user_id);

            $task = $tasks[$i];
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $this->assertRetailerTransactionMatch(
                $I,
                $task['id'],
                $tasksRetailerTransaction->trx_thread_id
            );
        }
    }

    /**
     * Similar as testEquallyDistributeTasks with filter on subscription by change  sf_retailer_customers
     * @param FunctionalTester $I
     * @group database_transaction
     */
    public function testEquallyDistributeTasksFilterSubscription(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => 2, 'is_subscribed_sms_marketing' => 2], ['customer_id' => 1234]);

        $tasksCreated = $this->runTaskScanner();
        $I->assertEquals(2, $tasksCreated);
    }

    /**
     * Similar as testEquallyDistributeTasksFilterSubscription with null filter on subscription for sf_retailer_customers
     * @param FunctionalTester $I
     * @group database_transaction
     */
    public function testEquallyDistributeTasksFilterSubscriptionNull(FunctionalTester $I)
    {
        $this->setUpRepMode($I);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => null, 'is_subscribed_sms_marketing' => null], ['customer_id' => 1234]);
        $I->updateInDatabase('sf_retailer_customers', ['is_subscribed' => 2, 'is_subscribed_sms_marketing' => 2], ['customer_id' => 1235]);

        $tasksCreated = $this->runTaskScanner();
        $I->assertEquals(2, $tasksCreated);
    }
}
