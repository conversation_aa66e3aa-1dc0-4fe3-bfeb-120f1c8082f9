<?php

namespace SF\functional\Tasks;

use SF\FunctionalTester;
use Salesfloor\Models\Task;
use Codeception\Util\Fixtures;

class TransactionMinCest extends TaskFunctional
{
    // Only for Follow-up (not soon to lapse)
    public function _before($I)
    {
        // Important to be first, since it's loading app
        parent::_before($I);

        $this->app['configs']['sf.task.automated.transaction.min'] = 100;
    }

    /** @group database_transaction */
    public function testRepTransactionMin(FunctionalTester $I)
    {
        $I->wantTo("Test rep transaction task - with min threshold of 100");

        $this->app['configs']['sf.task.automated.new_rep_transaction.enabled'] = true;
        $this->app['configs']['sf.task.automated.new_rep_transaction.max_per_owner'] = 3;
        $this->app['configs']['sf.task.automated.new_rep_transaction.days_search_back'] = 7;

        // Load data
        $this->insertFixtureGroup($I, 'sf_rep_transaction_min');

        $results = $this->executeScanner(Task::AUTOMATED_TYPE_NEW_REP_TRANSACTION);

        // Max one per customer (1 => 200) and (2 => 100)
        $I->assertEquals(2, $results);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRepTransactions = $I->grabRowsFromDatabase('sf_task_rep_transaction');

        $I->assertCount(2, $tasks);
        $I->assertCount(2, $tasksRepTransactions);

        $exceptedData = Fixtures::get('test-rep-transaction-min');
        foreach ($exceptedData->tasks as $i => $task) {
            $I->seeInDatabase('sf_task', [
                'user_id' => $task->user_id,
                'customer_id' => $task->customer_id,
                'automated_type' => $task->automated_type,
                'details' => $task->details,
                'type' => $task->type,
                'status' => $task->status,
            ]);

            $task = $tasks[$i];
            $tasksRepTransaction = $exceptedData->tasksRepTransactions[$i];
            $I->seeInDatabase('sf_task_rep_transaction', [
                'task_id' => $task['id'],
                'trx_id' => $tasksRepTransaction->trx_id,
            ]);
        }
    }

    // Transaction filtered task has been refactored to use new_retailer_transaction_filtered_multiple as wrapper to process the logic
    /** @group database_transaction */
    public function testRetailerTransactionMin(FunctionalTester $I)
    {
        $I->wantTo("Test retailer transaction task - with min threshold of 100");

        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.any_store'] = false;

        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.enabled'] = true;
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 3;
        $this->app['configs']['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [7];


        // Load data
        $this->insertFixtureGroup($I, 'sf_retailer_transaction_min');

        $results = $this->executeScanner(Task::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE);

        // Validation created data
        $I->assertEquals(2, $results);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $I->assertCount(2, $tasks);
        $I->assertCount(2, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-retailer-transaction-min');
        foreach ($exceptedData->tasks as $i => $task) {
            $I->seeInDatabase('sf_task', [
                'user_id' => $task->user_id,
                'customer_id' => $task->customer_id,
                'automated_type' => $task->automated_type,
                'details' => $task->details,
                'type' => $task->type,
                'status' => $task->status,
            ]);

            $task = $tasks[$i];
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $I->seeInDatabase('sf_task_retailer_transaction', [
                'task_id' => $task['id'],
                'trx_thread_id' => $tasksRetailerTransaction->trx_thread_id,
            ]);
        }
    }

    /** @group database_transaction */
    public function testRetailerTransactionEmployeeIdMatchingMin(FunctionalTester $I)
    {
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 3;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 7;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;

        // Load data
        $this->insertFixtureGroup($I, 'sf_retailer_transaction_employee_min');

        $results = $this->executeScanner(Task::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE);

        // Validation created data (Since we can create more than 1 per customer with this)
        $I->assertEquals(3, $results);

        $tasks = $I->grabRowsFromDatabase('sf_task');
        $tasksRetailerTransactions = $I->grabRowsFromDatabase('sf_task_retailer_transaction');
        $I->assertCount(3, $tasks);
        $I->assertCount(3, $tasksRetailerTransactions);

        $exceptedData = Fixtures::get('test-retailer-transaction-employee-id-matching-min');
        foreach ($exceptedData->tasks as $i => $task) {
            $I->seeInDatabase('sf_task', [
                'user_id' => $task->user_id,
                'customer_id' => $task->customer_id,
                'automated_type' => $task->automated_type,
                'details' => $task->details,
                'type' => $task->type,
                'status' => $task->status,
            ]);

            $task = $tasks[$i];
            $tasksRetailerTransaction = $exceptedData->tasksRetailerTransactions[$i];
            $I->seeInDatabase('sf_task_retailer_transaction', [
                'task_id' => $task['id'],
                'trx_thread_id' => $tasksRetailerTransaction->trx_thread_id,
            ]);
        }
    }

    /**
     * I prefer not use runTaskScanner() since it's bind to TASK_ID (static).
     */
    private function executeScanner($type)
    {
        $scannerName = \Salesfloor\Services\Tasks\Automated\BaseScanner::SERVICE_BASE . $type;
        $scanner     = $this->app[$scannerName];

        return $scanner->process();
    }
}
