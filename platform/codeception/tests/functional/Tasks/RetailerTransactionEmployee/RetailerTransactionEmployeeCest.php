<?php

namespace SF\functional\Tasks\RetailerTransactionEmployee;

use Salesfloor\Models\Task;
use Salesfloor\Services\Tasks\Automated\BaseScanner;
use Salesfloor\Services\Tasks\Automated\RetailerTransactionEmployeeAssigned;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

/**
 * This is not perfect because we don't mock properly dependencies and at the end it's slower because
 * we need to index (ES) and add to the matching queue (still in AWS because queue are expired in GCP at the moment)
 *
 * @group database_transaction
 */
class RetailerTransactionEmployeeCest extends BaseFunctional
{
    protected function applyGeneralConfigs()
    {
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 5;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 7;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
    }

    /////////////////////////
    //// Team mode
    ///

    // Primary email
    public function testFollowupMatchingPrimaryEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- BAD => Sub and not matching (email is linked to another store user)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingPrimaryEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location even if employee id is matching another store. Location (retailer store id) has precedence.
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- BAD => Sub and not matching (email is linked to another store user)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingPrimaryEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- BAD => Match via employee Id + Same store + Unsubscribed
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingPrimaryEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // No tasks created on those 3 transactions
        // 1 => BAD => Different store between the employee Id and the customer (Unsubscribed)
        // 2 => BAD => Different store between the employee Id and the customer (Subscribed)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(0, $tasksCreated);

        $I->assertEquals(0, $I->grabNumRecords('sf_task'));
        $I->assertEquals(0, $I->grabNumRecords('sf_task_retailer_transaction'));
    }

    // Alternative email

    public function testFollowupMatchingAlternativeEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location via alternate)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- BAD => Sub and not matching (email is linked to another store user)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingAlternativeEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location via alternate) (Location has precedence over employee id)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- BAD => Sub and not matching (email is linked to another store user)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingAlternativeEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- BAD => Match via employee Id + Same store + Unsubscribed
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }
    public function testFollowupMatchingAlternativeEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // No tasks created on those 3 transactions
        // 1 => BAD => Different store between the employee Id and the customer (Unsubscribed)
        // 2 => BAD => Different store between the employee Id and the customer (Subscribed)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(0, $tasksCreated);

        $I->assertEquals(0, $I->grabNumRecords('sf_task'));
        $I->assertEquals(0, $I->grabNumRecords('sf_task_retailer_transaction'));
    }

    /////////////////
    ///  RCI

    public function testFollowupMatchingRCIEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location) (linked via RCI)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingRCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location via alternate) (Location has precedence over employee id)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- BAD => Sub and not matching (email is linked to another store user)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }
    public function testFollowupMatchingRCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- BAD => Match via employee Id + Same store + Unsubscribed
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }
    public function testFollowupMatchingRCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // No tasks created on those 3 transactions
        // 1 => BAD => Different store between the employee Id and the customer (Unsubscribed)
        // 2 => BAD => Different store between the employee Id and the customer (Subscribed)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(0, $tasksCreated);

        $I->assertEquals(0, $I->grabNumRecords('sf_task'));
        $I->assertEquals(0, $I->grabNumRecords('sf_task_retailer_transaction'));
    }

    // RPCI

    public function testFollowupMatchingRPCIEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => Subscribed + matching customer on location (linked via RPCI)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- GOOD => Subscribed + matching customer on location (linked via RPCI)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2001, // store user
            'customer_id' => 58,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    public function testFollowupMatchingRPCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => Subscribed + matching customer on location (linked via RPCI)
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- GOOD => Subscribed + matching customer on location (linked via RPCI)
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }
    public function testFollowupMatchingRPCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- BAD => Not subscribed
        // 2- GOOD => Subscribed + matching customer on location
        // 3- BAD => Unsub and not matching (email is linked to another store user)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- BAD => Match via employee Id + Same store + Unsubscribed
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 58,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }
    public function testFollowupMatchingRPCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // No tasks created on those 3 transactions
        // 1 => BAD => Different store between the employee Id and the customer (Unsubscribed)
        // 2 => BAD => Different store between the employee Id and the customer (Subscribed)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(0, $tasksCreated);

        $I->assertEquals(0, $I->grabNumRecords('sf_task'));
        $I->assertEquals(0, $I->grabNumRecords('sf_task_retailer_transaction'));
    }

    ////////////////////////////
    ///  Non-matching flag (Creating a contact if missing)

    public function testFollowupNonMatchingPrimaryEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        // For the non-matching section, I will reuse the matching fixture
        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed, but it's ok since ignored when flag is false
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (New contact created)
        // 4- GOOD => Sub and not matching (email is linked to another store user) (New contact created)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(14, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);
        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => 'john4 doe4',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);

        $resultTaskMeta = $I->grabRowsFromDatabase('sf_customer_meta');
        $resultTaskTags = $I->grabRowsFromDatabase('sf_customer_tags_relationships');

        $this->validateArray([[
            'customer_id' => $customerIds[0],
            'type' => 'email',
            'value' => '<EMAIL>',
            'label' => 'home',
            'position' => 0
        ], [
            'customer_id' => $customerIds[1],
            'type' => 'email',
            'value' => '<EMAIL>',
            'label' => 'home',
            'position' => 0
        ]], $resultTaskMeta);

        $this->validateArray([[
            'tag_id' => 1,
            'customer_id' => $customerIds[0],
            'created_by' => 0
        ], [
            'tag_id' => 2,
            'customer_id' => $customerIds[1],
            'created_by' => 0
        ]], $resultTaskTags);
    }

    public function testFollowupNonMatchingPrimaryEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed (Not important when config is set to false)
        // 2- GOOD => (subscribed + matching customer on location even if employee id is matching another store. Location (retailer store id) has precedence.
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (New contact created)
        // 4- GOOD => Sub and not matching (email is linked to another store user) (New contact created)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(14, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);
        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingPrimaryEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- GOOD => Not subscribed (Not important when config is set to false)
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (New contact created)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- GOOD => Match via employee Id + Same store + Unsubscribed (Not important when config is set to false)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(13, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(5, $tasksCreated);

        $I->seeNumRecords(5, 'sf_task');
        $I->seeNumRecords(5, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 54,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_5',
        ], [
            'task_id' => $resultTask[4]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingPrimaryEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingPrimaryEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // 1 => GOOD => Different store between the employee Id and the customer (Unsubscribed) (New contact created)
        // 2 => GOOD => Different store between the employee Id and the customer (Subscribed) (New contact created)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(14, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    // Alternative email

    public function testFollowupNonMatchingAlternativeEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed (Not important when config is false)
        // 2- GOOD => (subscribed + matching customer on location via alternate)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (new contact created)
        // 4- GOOD => Sub and not matching (email is linked to another store user) (new contact created)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(14, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingAlternativeEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed (Not important with this config)
        // 2- GOOD => (subscribed + matching customer on location via alternate) (Location has precedence over employee id)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (new contact created)
        // 4- GOOD => Sub and not matching (email is linked to another store user) (new contact created)
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(9, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingAlternativeEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- GOOD => Not subscribed (Not important when config is false)
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (new contact created)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- GOOD => Match via employee Id + Same store + Unsubscribed (Not important when config is false)
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(5, $tasksCreated);

        $I->seeNumRecords(5, 'sf_task');
        $I->seeNumRecords(5, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 54,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2001, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[4]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingAlternativeEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingAlternativeEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // 1 => GOOD => Different store between the employee Id and the customer (Unsubscribed) (new contact created)
        // 2 => GOOD => Different store between the employee Id and the customer (Subscribed) (new contact created)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(9, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    // RCI

    public function testFollowupNonMatchingRCIEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 3 transactions:
        // 1- GOOD => Not subscribed (Not important when config is set to false)
        // 2- GOOD => (subscribed + matching customer on location) (linked via RCI)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (New contact created)
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 51, // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingRCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed (Not important with this config)
        // 2- GOOD => (subscribed + matching customer on location via alternate) (Location has precedence over employee id)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (new contact created)
        // 4- GOOD => Sub and not matching (email is linked to another store user) (new contact created)
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(9, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingRCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- GOOD => Not subscribed (Not important when config is false)
        // 2- GOOD => (subscribed + matching customer on location)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (new contact created)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- GOOD => Match via employee Id + Same store + Unsubscribed (Not important when config is false)
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(5, $tasksCreated);

        $I->seeNumRecords(5, 'sf_task');
        $I->seeNumRecords(5, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 54,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2001, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[4]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }
    public function testFollowupNonMatchingRCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // 1 => GOOD => Different store between the employee Id and the customer (Unsubscribed) (new contact created)
        // 2 => GOOD => Different store between the employee Id and the customer (Subscribed) (new contact created)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        // This one has 10 customers
        $I->seeNumRecords(14, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    // RPCI

    public function testFollowupNonMatchingRPCIEmailTeamModeWithValidLocationSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithValidLocationSameStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed (Not important when the config is false)
        // 2- GOOD => Subscribed + matching customer on location (linked via RPCI)
        // 3- GOOD => Unsub and not matching (email is linked to another store user) (new contact created)
        // 4- GOOD => Subscribed + matching customer on location (linked via RPCI)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(13, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 51, // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 58, // because it's based on rpci and not email
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingRPCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithValidLocationDifferentStoreEmployeeId');

        // I have 4 transactions:
        // 1- GOOD => Not subscribed (Not important with this config)
        // 2- GOOD => Subscribed + matching customer on location (linked via RPCI) (Location has precedence over employee id)
        // 3- BAD => Unsub and not matching (RPCI is linked to another store user) (new contact created)
        // 4- GOOD => Subscribed + matching customer on location (linked via RPCI)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(13, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowupNonMatchingRPCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithInvalidLocationAndSameStoreEmployeeId');

        // I have 5 transactions:
        // 1- GOOD => Not subscribed (Not important when config is false)
        // 2- GOOD => Subscribed + matching customer on location
        // 3- GOOD => Unsub and not matching (RPCI is linked to another store user) (new contact created)
        // 4- GOOD => Match via employee Id + Same store + subscribed
        // 5- GOOD => Match via employee Id + Same store + Unsubscribed
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(13, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2000,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2000,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(5, $tasksCreated);

        $I->seeNumRecords(5, 'sf_task');
        $I->seeNumRecords(5, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2000, // store user
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000, // store user
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => 59,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2001, // store user
            'customer_id' => 58,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],[
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ],[
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[4]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }
    public function testFollowupNonMatchingRPCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowupMatchingRPCIEmailTeamModeWithInvalidLocationAndDifferentStoreEmployeeId');

        // 1 => GOOD => Different store between the employee Id and the customer (Unsubscribed) (new contact created)
        // 2 => GOOD => Different store between the employee Id and the customer (Subscribed) (new contact created)
        // 3 => Bad => Employee Id is invalid
        $tasksCreated = $this->runTaskScanner();

        // Because I created only 5 instead of 10 in fixture
        $I->seeNumRecords(9, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2001,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $I->seeInDatabase('sf_customer', [
            'user_id' => 2001,
            'email' => '<EMAIL>',
            'name' => '',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 2001,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2001, // store user
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001, // store user
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_5',
        ],[
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_4',
        ]], $resultTaskTransaction);
    }

    /////////////////////////
    //// Rep mode
    ///

    // matching

    public function testFollowUpRepModeMatchingPrimaryEmailToPrimary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailToPrimary');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC primary email to Customer primary
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMatchingPrimaryEmailToSecondary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailToSecondary');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC primary email to Customer secondary
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMatchingSecondaryEmailToPrimary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingSecondaryEmailToPrimary');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC secondary email to Customer primary
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMatchingSecondaryEmailToSecondary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingSecondaryEmailToSecondary');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC secondary email to Customer secondary
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMatchingPrimaryEmailInvalidLocation(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailInvalidLocation');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC primary email to Customer secondary
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMatchingPrimaryEmailInvalidEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailInvalidEmployeeId');

        // 1 => BAD => No match
        // 2 => BAD => No match
        // 3 => BAD => No match
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(0, $tasksCreated);

        $I->seeNumRecords(0, 'sf_task');
        $I->seeNumRecords(0, 'sf_task_retailer_transaction');
    }

    public function testFollowUpRepModeMatchingRCI(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingRCI');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC RCI
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMatchingRPCI(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingRPCI');

        // 1 => BAD => Unsub
        // 2 => GOOD => Matching via RC RCI
        // 3 => BAD => Doesn't match any customer from this rep
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    //////////
    ///  Non Matching

    public function testFollowUpRepModeNonMatchingPrimaryEmailToPrimary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailToPrimary');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);

        $resultTaskMeta = $I->grabRowsFromDatabase('sf_customer_meta');
        $resultTaskTags = $I->grabRowsFromDatabase('sf_customer_tags_relationships');

        $this->validateArray([[
            'customer_id' => $customerIds[0],
            'type' => 'email',
            'value' => '<EMAIL>',
            'label' => 'home',
            'position' => 0
        ]], $resultTaskMeta);

        $this->validateArray([[
            'tag_id' => 1,
            'customer_id' => $customerIds[0],
            'created_by' => 0
        ]], $resultTaskTags);
    }

    public function testFollowUpRepModeNonMatchingPrimaryEmailToSecondary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailToSecondary');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeNonMatchingSecondaryEmailToPrimary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingSecondaryEmailToPrimary');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeNonMatchingSecondaryEmailToSecondary(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingSecondaryEmailToSecondary');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeNonMatchingPrimaryEmailInvalidLocation(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailInvalidLocation');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeNonMatchingPrimaryEmailInvalidEmployeeId(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingPrimaryEmailInvalidEmployeeId');

        // 1 => BAD => No match (invalid employee id, nothing created)
        // 2 => BAD => No match (invalid employee id, nothing created)
        // 3 => BAD => No match (invalid employee id, nothing created)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(0, $tasksCreated);

        $I->seeNumRecords(0, 'sf_task');
        $I->seeNumRecords(0, 'sf_task_retailer_transaction');
    }

    public function testFollowUpRepModeNonMatchingRCI(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingRCI');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeNonMatchingRPCI(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeMatchingRPCI');

        // 1 => GOOD => Unsub (Not important when the config is false)
        // 2 => GOOD => Matching via primary to primary
        // 3 => GOOD => Doesn't match any customer from this rep (Will create a new one)
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(8, 'sf_customer');

        $I->seeInDatabase('sf_customer', [
            'user_id' => 1003,
            'email' => '<EMAIL>',
            'name' => 'john3 doe3',
            'subcribtion_flag' => 0,
            'origin' => 'ci-transaction',
        ]);

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'user_id' => 1003,
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_3',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeNonMatchingNoContactCreatedIfEmployeeIsDisabled(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeNonMatchingNoContactCreatedIfEmployeeIsDisabled');

        // 4x => BAD => Employee disabled, so no contact created or tasks
        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(0, $tasksCreated);
    }

    /////////////////
    /// Other scenarios
    ///

    public function testFollowUpBetweenDate(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpBetweenDate');

        $tasksCreated = $this->runTaskScanner();

        $I->seeNumRecords(7, 'sf_customer');

        $I->assertEquals(0, $tasksCreated);

        $I->seeNumRecords(0, 'sf_task');
        $I->seeNumRecords(0, 'sf_task_retailer_transaction');
    }

    public function testFollowUpTeamModeOrderBy(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpTeamModeOrderBy');

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(4, $tasksCreated);

        $I->seeNumRecords(4, 'sf_task');
        $I->seeNumRecords(4, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2000,
            'customer_id' => 52,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ], [
            'user_id' => 2001,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned'
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_3',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[3]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpRepModeOrderBy(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpRepModeOrderBy');

        $tasksCreated = $this->runTaskScanner();

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(3, $tasksCreated);

        $I->seeNumRecords(3, 'sf_task');
        $I->seeNumRecords(3, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => $customerIds[2], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1000,
            'customer_id' => $customerIds[1], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ], [
            'task_id' => $resultTask[2]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpTeamModeMaxPerStore(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = true;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 1;

        // We reuse another fixture
        $this->insertFixtureGroup($I, 'testFollowUpTeamModeOrderBy');

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 2000, // store user
            'customer_id' => 53,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 2001,
            'customer_id' => 51,
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_4',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ],], $resultTaskTransaction);
    }

    public function testFollowUpRepModeMaxPerRep(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 1;

        // Reused another fixture
        $this->insertFixtureGroup($I, 'testFollowUpRepModeOrderBy');

        $tasksCreated = $this->runTaskScanner();

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => $customerIds[2], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1003,
            'customer_id' => $customerIds[0], // new contact
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_3',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpDifferentLocale(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;
        $this->app['configs']['retailer.i18n.is_enabled'] = true;

        // Because we changed the locale, we cannot set the cache to true in tests.
        // We could reset it, but my guess is the performance improvement won't make a big difference.
        $this->app['configs']['retailer.i18n.default_locale'] = 'fr_CA';

        // 1 => GOOD => Matching + subscribed
        // 2 => Bad => Not matching
        // 3 => Bad => Not matching
        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpDifferentLocale');

        $tasksCreated = $this->runTaskScanner();

        $I->assertEquals(1, $tasksCreated);

        $I->seeNumRecords(1, 'sf_task');
        $I->seeNumRecords(1, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => 50,
            'type' => 'automated',
            'details' => 'Suivi de votre transaction de vente', // locale = fr_CA
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpContactCreationEmailOrPhoneMandatory(FunctionalTester $I)
    {
        $this->app['configs']['retailer.storepage_mode'] = false;
        $this->app['configs']['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

        $this->applyGeneralConfigs();

        $this->insertFixtureGroup($I, 'testFollowUpContactCreationEmailOrPhoneMandatory');

        // 1 => GOOD
        // 2 => GOOD
        // 3 => BAD => Since no phone / email
        $tasksCreated = $this->runTaskScanner();

        $customerIds = $I->grabColumnFromDatabase('sf_customer', 'id', [
            'origin' => 'ci-transaction',
        ]);

        $I->assertEquals(2, $tasksCreated);

        $I->seeNumRecords(2, 'sf_task');
        $I->seeNumRecords(2, 'sf_task_retailer_transaction');

        $resultTask = $I->grabRowsFromDatabase('sf_task');
        $resultTaskTransaction = $I->grabRowsFromDatabase('sf_task_retailer_transaction');

        // The order is because of the transaction_total (order by price desc)
        $this->validateArray([[
            'user_id' => 1000,
            'customer_id' => $customerIds[0],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ],[
            'user_id' => 1001,
            'customer_id' => $customerIds[1],
            'type' => 'automated',
            'details' => 'Transaction Follow-Up of your Sale', // locale = en_US
            'automated_type' => 'retailer_transaction_employee_assigned',
        ]], $resultTask);

        $this->validateArray([[
            'task_id' => $resultTask[0]['id'],
            'trx_thread_id' => 'thread_1',
        ], [
            'task_id' => $resultTask[1]['id'],
            'trx_thread_id' => 'thread_2',
        ]], $resultTaskTransaction);
    }

    public function testFollowUpDuplicateCheck(FunctionalTester $I)
    {
        $taskInstance = new RetailerTransactionEmployeeAssigned($this->app);

        $tasks = [
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
            [
                'internalTransactionId' => 1,
                'test' => 2,
            ],
            [
                'internalTransactionId' => 1,
                'test' => 3,
            ],
        ];

        $uniqueTasks = $this->getMethod($taskInstance, "duplicateCheck")->invokeArgs($taskInstance, [$tasks]);

        $I->assertEquals(1, count($uniqueTasks));

        $this->validateArray([
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
        ], $uniqueTasks);

        // Extra test

        $tasks = [
            [
                'internalTransactionId' => 1,
                'test' => 3,
            ],
            [
                'internalTransactionId' => 1,
                'test' => 2,
            ],
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
        ];

        $uniqueTasks = $this->getMethod($taskInstance, "duplicateCheck")->invokeArgs($taskInstance, [$tasks]);

        $I->assertEquals(1, count($uniqueTasks));

        $this->validateArray([
            [
                'internalTransactionId' => 1,
                'test' => 3,
            ],
        ], $uniqueTasks);

        // Extra test

        $tasks = [
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
            [
                'internalTransactionId' => 1,
                'test' => 2,
            ],
            [
                'internalTransactionId' => 2,
                'test' => 3,
            ],
        ];

        $uniqueTasks = $this->getMethod($taskInstance, "duplicateCheck")->invokeArgs($taskInstance, [$tasks]);

        $I->assertEquals(2, count($uniqueTasks));

        $this->validateArray([
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
            [
                'internalTransactionId' => 2,
                'test' => 3,
            ],
        ], $uniqueTasks);

        // extra test

        $tasks = [
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
            [
                'internalTransactionId' => 1,
                'test' => 2,
            ],
            [
                'internalTransactionId' => 2,
                'test' => 3,
                'ok' => 'nice',
                'random' => 'stuff'
            ],
        ];

        $uniqueTasks = $this->getMethod($taskInstance, "duplicateCheck")->invokeArgs($taskInstance, [$tasks]);

        $I->assertEquals(2, count($uniqueTasks));

        $this->validateArray([
            [
                'internalTransactionId' => 1,
                'test' => 1,
            ],
            [
                'internalTransactionId' => 2,
                'test' => 3,
                'ok' => 'nice',
                'random' => 'stuff'
            ],
        ], $uniqueTasks);
    }

    protected function runTaskScanner()
    {
        // We must use "multiple" even though we test only 1 date
        $scannerName = BaseScanner::SERVICE_BASE . Task::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE;
        $scanner = $this->app[$scannerName];

        return $scanner->process();
    }
}
