<?php

namespace SF\functional\Tasks;

use SF\FunctionalTester;
use Salesfloor\Models\Task;
use Salesfloor\Services\Event;
use Salesfloor\Services\Tasks\Tasks;
use Salesfloor\API\Managers\Task\Tasks as TasksManager;

/** @group database_transaction */
class AutoResolveTasksCest extends TaskFunctional
{
    public function testAutoResolveEnabled(FunctionalTester $I)
    {
        $I->wantTo('Test we can auto-resolve tasks.');

        $this->insertFixtureGroup($I, 'tasks');
        $this->app['configs']['retailer.tasks.auto-resolve.is_enabled'] = true;

        $source = 'test';
        /** @var Tasks $tasksService */
        $tasksService = $this->app['service.tasks'];
        $tasksService->autoResolve(1, $source);

        $groupTasks = $I->grabRowsFromDatabase('sf_task', [], [ 'id' => 1 ]);
        $I->assertCount(1, $groupTasks);
        $I->assertEquals(Task::STATUS_RESOLVED, $groupTasks[0]["status"]);

        $events = $I->grabRowsFromDatabase(
            'sf_events',
            [],
            [
                'type' => Event::SF_EVENT_TASK_MANUAL_RESOLVED,
                'source' => $source,
            ]
        );
        $I->assertCount(1, $events);

        // Make sure the cache get flushed.
        /** @var TasksManager $tasksManager   */
        $tasksManager = $this->app['tasks.manager'];
        /** @var Task $task */
        $task = $tasksManager->getById(1);
        $I->assertEquals(Task::STATUS_RESOLVED, $task->status);
    }

    public function testAutoResolveDisabled(FunctionalTester $I)
    {
        $I->wantTo('Test we can not auto-resolve tasks.');

        $this->insertFixtureGroup($I, 'tasks');
        $this->app['configs']['retailer.tasks.auto-resolve.is_enabled'] = false;

        $source = 'test';
        /** @var Tasks $tasksService */
        $tasksService = $this->app['service.tasks'];
        $tasksService->autoResolve(1, $source);

        $groupTasks = $I->grabRowsFromDatabase('sf_task', [], [ 'id' => 1 ]);
        $I->assertCount(1, $groupTasks);
        $I->assertEquals(Task::STATUS_UNRESOLVED, $groupTasks[0]["status"]);

        $events = $I->grabRowsFromDatabase(
            'sf_events',
            [],
            [
                'type' => Event::SF_EVENT_TASK_MANUAL_RESOLVED,
                'source' => $source,
            ]
        );
        $I->assertCount(0, $events);
    }
}
