<?php

declare(strict_types=1);

namespace SF\functional;

use Salesfloor\Services\Util;
use SF\FunctionalTester;

class BackofficeConnectionCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testBackofficeConnectionCreateWeeklyTransaction(FunctionalTester $I)
    {
        $I->wantTo("Test - Service BackofficeConnection - create weekly transaction");

        // This was a patch to download stuff from WP instead of migrate the code to the new stack.
        // Now the code is accessible from the new stack, but i'm not sure how much of it it's used at the moment.
        /** @var \Salesfloor\Services\BackofficeConnection $backoffice */
        $backoffice = $this->app['backoffice.connection'];
        $backoffice->login($this->app['configs']['sfadmin.user_login'], $this->app['configs']['sfadmin.password']);

        $validateUrls = [
            'marketing-store' => '/wp-admin/admin-ajax.php?user=all&timezone=America%2FNew_York&action=sfreports&date_from={{ date_from }}&date_to={{ date_to }}&type%5B%5D=get-store-marketing&type%5B%5D=get-store-sales-total&format=csv',
            'marketing' => '/wp-admin/admin-ajax.php?user=&timezone=America%2FNew_York&action=sfreports&type%5B0%5D=get-marketing&store=0&date_from={{ date_from }}&date_to={{ date_to }}',
            'chat-store' => '/wp-admin/admin-ajax.php?user=all&timezone=America%2FNew_York&action=sfreports&type%5B%5D=get-chat-store-metrics&date_from={{ date_from }}&date_to={{ date_to }}&format=csv',
            'transactions' => '/wp-admin/admin-ajax.php?user=all&timezone=America%2FNew_York&action=sfreports&period=all&type%5B%5D=get-sales&format=csv',
        ];

        foreach ($validateUrls as $name => $url) {
            $filename = Util::getRandomFilename("$name");
            $backoffice->sendGuzzleGetRequest($url, ['save_to' => $filename]);
            $I->assertFileExists($filename);
        }
    }
}
