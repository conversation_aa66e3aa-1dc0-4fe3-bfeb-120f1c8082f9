<?php

namespace SF\functional\Stores;

use Codeception\Util\Fixtures;
use Salesfloor\API\Managers\Stores;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class StoreManagerCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testIsStoreClosedStoreShouldBeClosed(FunctionalTester $I)
    {
        $closingHour = gmdate('H') - 1;
        if ($closingHour < 0) {
            $closingHour = 23;
        }

        $openingHour = gmdate('H') + 2;
        if ($openingHour > 23) {
            $openingHour = 3;
        }

        $this->app['configs']['retailer.store.open_hour'] = $openingHour;
        $this->app['configs']['retailer.store.close_hour'] = $closingHour;
        $I->haveInDatabase('sf_store', Fixtures::get('utc-store'));

        /** @var Stores $storeManager */
        $storeManager = $this->app['stores.manager'];
        $isClosed = $storeManager->isStoreClosedByStoreIdentifier('utc-store');
        $I->assertTrue($isClosed);
    }

    /** @group database_transaction */
    public function testIsStoreClosedStoreShouldBeOpen(FunctionalTester $I)
    {
        $openingHour = gmdate('H') - 1;
        if ($openingHour < 0) {
            $openingHour = 23;
        }

        $closingHour = gmdate('H') + 2;
        if ($closingHour > 23) {
            $closingHour = 3;
        }

        $this->app['configs']['retailer.store.open_hour'] = $openingHour;
        $this->app['configs']['retailer.store.close_hour'] = $closingHour;
        $I->haveInDatabase('sf_store', Fixtures::get('utc-store'));

        /** @var Stores $storeManager */
        $storeManager = $this->app['stores.manager'];
        $isClosed = $storeManager->isStoreClosedByStoreIdentifier('utc-store');
        $I->assertFalse($isClosed);
    }

    /** @group database_transaction */
    public function testGetSiblingStores(FunctionalTester $I)
    {
        $I->wantTo('Test the getSiblingsStore() function in the store manager');

        $this->insertFixtureGroup($I, 'stores_multibrand');

        /** @var Stores $storeManager */
        $storeManager = $this->app['stores.manager'];

        $stores = $storeManager->getSiblingsStore([1004]);

        $this->debug($stores);

        $I->assertEquals(1, count($stores));

        $stores = $storeManager->getSiblingsStore([1006]);

        $this->debug($stores);

        $I->assertEquals(1, count($stores));

        $stores = $storeManager->getSiblingsStore([1004], ['brand1', 'brand2']);

        $this->debug($stores);

        $I->assertEquals(1, count($stores));

        $stores = $storeManager->getSiblingsStore([1004], ['brand1']);

        $this->debug($stores);

        $I->assertEquals(1, count($stores));

        $stores = $storeManager->getSiblingsStore([1006], ['brand1', 'brand2']);

        $this->debug($stores);

        $I->assertEquals(1, count($stores));
    }
}
