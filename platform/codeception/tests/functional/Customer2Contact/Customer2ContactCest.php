<?php

declare(strict_types=1);

namespace SF\functional\Customer2Contact;

use SF\FunctionalTester;
use Salesfloor\Models\Customer;
use SF\functional\BaseFunctional;
use Salesfloor\Models\RetailerCustomer;
use Salesfloor\Models\RetailerCustomerMeta;
use Salesfloor\Models\RetailerCustomerEvent;
use Salesfloor\Models\RetailerCustomerAddress;
use Salesfloor\Models\RetailerCustomerSocialMedia;
use Salesfloor\Models\RetailerCustomerTagsRelationships;
use Salesfloor\API\Managers\CustomersToRetailerCustomers;
use Salesfloor\Services\CustomerInsights\Customer2Contact;
use Salesfloor\API\Managers\CustomerMeta as CustomerMetaManager;
use Salesfloor\API\Managers\CustomerEvents as CustomerEventsManager;
use Salesfloor\API\Managers\CustomerAddress as CustomerAddressManager;
use Salesfloor\API\Managers\CustomerSocialMedia as CustomerSocialMediaManager;
use Salesfloor\API\Managers\Client\RetailerCustomers\Legacy as RetailerCustomersManager;
use Salesfloor\API\Managers\CustomerTagsRelationships as CustomerTagsRelationshipsManager;

class Customer2ContactCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testCustomerMetaNormalizedPhone(FunctionalTester $I)
    {
        $I->wantTo('Test create contact from retailer customer retailer and meta phone normalized');

        $this->insertFixtureGroup($I, 'testCustomerMetaNormalizedPhone');

        $retailerCustomer = $this->app['retailer_customers.manager']->getOne([
            'customer_id' => '1234',
        ]);

        $customer = $this->app['service.customer2contact']->createCustomerFromRetailerCustomer(
            self::REGGIE_ID,
            $retailerCustomer,
            Customer::ORIGIN_REP_ADD_TO_CONTACT,
            CustomersToRetailerCustomers::MANUAL_MATCH_ORIGIN_ADD_TO_CONTACTS
        );

        $I->assertTrue(!empty($customer));

        // 2 phones and 1 email (1 phone is invalid and ignored)
        $customerMetas = $I->grabRowsFromDatabase('sf_customer_meta', [], [
            'customer_id' => $customer->getId()
        ]);

        $I->assertEquals(3, count($customerMetas));

        $this->validateArray([
            [
                'customer_id' => $customer->getId(),
                'type' => 'email',
                'value' => '<EMAIL>',
                'label' => 'Home',
                'position' => 0
            ],
            [
                'customer_id' => $customer->getId(),
                'type' => 'phone',
                'value' => '+18659103590',
                'label' => 'Home1',
                'position' => 0,
            ],
            [
                'customer_id' => $customer->getId(),
                'type' => 'phone',
                'value' => '+14349372209',
                'label' => 'Home2',
                'position' => 1,
            ],
        ], $customerMetas);
    }

    /**
     * Test the new appointment creation system
     *
     * @group database_transaction
     *
     * @param FunctionalTester $I
     * @throws \Exception
     */
    public function testCreateCustomerFromRetailerCustomer(FunctionalTester $I)
    {
        $I->wantTo('Test create appointment from manager views - contact');

        $this->insertFixtureGroup($I, 'retailer_customer_fixture_group');


        /** @var RetailerCustomersManager $retailerCustomersManager */
        $retailerCustomersManager = $this->app['retailer_customers.manager'];
        $retailerCustomersManager->reindex();

        /** @var RetailerCustomer $retailerCustomer */
        $retailerCustomer = $retailerCustomersManager->getOne([
            'customer_id' => '1234',
        ]);

        $this->debug($retailerCustomer);

        /** @var Customer2Contact $customer2ContactService */
        $customer2ContactService = $this->app['service.customer2contact'];
        $customer = $customer2ContactService->createCustomerFromRetailerCustomer(
            1,
            $retailerCustomer,
            Customer::ORIGIN_RETAILER_REP_APPOINTMENT,
            CustomersToRetailerCustomers::MANUAL_MATCH_ORIGIN_APPOINTMENT_BOOKING
        );

        $this->debug($customer);

        $I->assertTrue(!empty($customer));

        /** @var CustomerAddressManager $customerAddressesManager */
        $customerAddressesManager = $this->app['customer_addresses.manager'];

        /** @var RetailerCustomerAddress[] $retailerCustomerAddresses */
        $retailerCustomerAddresses = $customerAddressesManager->getAll([
            'customer_id' => $customer->getId(),
        ], 0, -1);

        $this->debug($retailerCustomerAddresses);

        $I->assertCount(1, $retailerCustomerAddresses);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'address_line_1' => '350 Notre-Dame Ouest',
            'address_line_2' => '',
            'postal_code' => 'H3C 8H4',
            'city' => 'Montreal',
            'state' => 'QC',
            'country' => 'Canada',
            'label' => 'Home',
            'is_default' => 1,
        ], $retailerCustomerAddresses[0]);

        /** @var CustomerEventsManager $customerEventsManager */
        $customerEventsManager = $this->app['customer_events.manager'];

        /** @var RetailerCustomerEvent[] $retailerCustomerEvents */
        $retailerCustomerEvents = $customerEventsManager->getAll([
            'customer_id' => $customer->getId(),
        ], 0, -1);

        $this->debug($retailerCustomerEvents);

        $I->assertCount(2, $retailerCustomerEvents);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'label' => 'Birthday',
            'year' => 1970,
            'month' => 01,
            'day' => 01,
        ], $retailerCustomerEvents[0]);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'label' => 'Other',
            'year' => 1999,
            'month' => 12,
            'day' => 21,
        ], $retailerCustomerEvents[1]);

        /** @var CustomerMetaManager $customerMetaManager */
        $customerMetaManager = $this->app['customersmeta.manager'];

        /** @var RetailerCustomerMeta[] $retailerCustomerMetas */
        $retailerCustomerMetas = $customerMetaManager->getAll([
            'customer_id' => $customer->getId(),
        ], 0, -1);

        $this->debug($retailerCustomerMetas);

        $I->assertCount(1, $retailerCustomerMetas);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'type' => 'email',
            'label' => 'Home',
            'position' => 1,
            'value' => '<EMAIL>',
        ], $retailerCustomerMetas[0]);

        /** @var CustomerSocialMediaManager $customerSocialMediaManager */
        $customerSocialMediaManager = $this->app['customer_social_media.manager'];

        /** @var RetailerCustomerSocialMedia[] $retailerCustomerSocialMedias */
        $retailerCustomerSocialMedias = $customerSocialMediaManager->getAll([
            'customer_id' => $customer->getId(),
        ], 0, -1);

        $this->debug($retailerCustomerSocialMedias);

        $I->assertCount(3, $retailerCustomerSocialMedias);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'social_media_network_id' => 1,
            'username' => 'testrcFb',
        ], $retailerCustomerSocialMedias[0]);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'social_media_network_id' => 2,
            'username' => 'testrcTw',
        ], $retailerCustomerSocialMedias[1]);
        $this->validateArray([
            'customer_id' => $customer->getId(),
            'social_media_network_id' => 3,
            'username' => 'testrcIg',
        ], $retailerCustomerSocialMedias[2]);

        /** @var CustomerTagsRelationshipsManager $customerTagsRelationshipsManager */
        $customerTagsRelationshipsManager = $this->app['customer_tags_relationships.manager'];

        /** @var RetailerCustomerTagsRelationships[] $retailerCustomerTagsRelationships */
        $retailerCustomerTagsRelationships = $customerTagsRelationshipsManager->getAll([
            'customer_id' => $customer->getId(),
        ], 0, -1);

        $this->debug($retailerCustomerTagsRelationships);

        $I->assertCount(2, $retailerCustomerTagsRelationships);
        $this->validateArray([
            'tag_id' => '1',
            'customer_id' => $customer->getId(),
        ], $retailerCustomerTagsRelationships[0]);
        $this->validateArray([
            'tag_id' => '2',
            'customer_id' => $customer->getId(),
        ], $retailerCustomerTagsRelationships[1]);

        // Check the c2c relationship
        $I->seeInDatabase('sf_customers_to_retailer_customers', [
            'customer_id' => $customer->getId(),
            'retailer_customer_id' => $retailerCustomer->getId(),
            'comment' =>
                CustomersToRetailerCustomers::MATCH_MANUAL
                . ':'
                . CustomersToRetailerCustomers::MANUAL_MATCH_ORIGIN_APPOINTMENT_BOOKING,
        ]);
    }

    /** @group database_transaction */
    public function testHistoryLogWhenCreateCustomerFromRetailerCustomer(FunctionalTester $I)
    {
        $I->wantTo('Test history Log when create customer/appointment from manager views - contact');

        $this->insertFixtureGroup($I, 'retailer_customer_fixture_group');


        /** @var RetailerCustomersManager $retailerCustomersManager */
        $retailerCustomersManager = $this->app['retailer_customers.manager'];
        /** @var RetailerCustomer $retailerCustomer */
        $retailerCustomer = $retailerCustomersManager->getOne([
            'customer_id' => '1234',
        ]);

        $this->debug($retailerCustomer);

        /** @var Customer2Contact $customer2ContactService */
        $customer2ContactService = $this->app['service.customer2contact'];
        $customer = $customer2ContactService->createCustomerFromRetailerCustomer(
            1,
            $retailerCustomer,
            Customer::ORIGIN_RETAILER_REP_APPOINTMENT,
            CustomersToRetailerCustomers::MANUAL_MATCH_ORIGIN_APPOINTMENT_BOOKING
        );

        $I->canSeeInDatabase(
            'sf_customer_field_history',
            [
                'source' => 'rep-appointment',
                'customer_id' => $customer->getId(),
                'old_value' => null,
                'new_value' => $customer->subcribtion_flag,
                'field_name' => 'subcribtion_flag'
            ]
        );
        $I->canSeeInDatabase(
            'sf_customer_field_history',
            [
                'source' => 'rep-appointment',
                'customer_id' => $customer->getId(),
                'old_value' => null,
                'new_value' => $customer->subcribtion_flag,
                'field_name' => 'email'
            ]
        );
        $I->canSeeInDatabase(
            'sf_customer_field_history',
            [
                'source' => 'rep-appointment',
                'customer_id' => $customer->getId(),
                'old_value' => null,
                'new_value' => $customer->sms_marketing_subscription_flag,
                'field_name' => 'sms_marketing_subscription_flag'
            ]
        );
        $I->canSeeInDatabase(
            'sf_customer_field_history',
            [
                'source' => 'rep-appointment',
                'customer_id' => $customer->getId(),
                'old_value' => null,
                'new_value' => $customer->phone,
                'field_name' => 'phone'
            ]
        );
    }
}
