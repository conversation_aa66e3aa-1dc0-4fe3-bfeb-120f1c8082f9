<?php

declare(strict_types=1);

namespace SF\functional\Reporting;

use Codeception\Util\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

/**
 * We cannot use "database_transaction" because the "transaction" include only a subset of the query.
 * Here, the fixture is added in a transaction and the "WP" stuff in another.
 * @group refresh_database
 */
class ReportingExportCsvCest extends BaseFunctional
{
    public function testExportCsvGetMarketingOnly(FunctionalTester $I)
    {
        ob_start();

        $this->getMockedReporting($this->prepareRequest([
            'get-marketing',
        ]));

        $output = ob_get_clean();
        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    public function testExportCsvDownloadLiveChatMetricsAssociates(FunctionalTester $I)
    {
        ob_start();

        $this->getMockedReporting($this->prepareRequest([
            'get-chat-user-metrics',
        ]));

        $output = ob_get_clean();

        $this->debug($output);

        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    public function testExportCsvDownloadLiveChatMetricsStores(FunctionalTester $I)
    {
        ob_start();

        $this->getMockedReporting($this->prepareRequest([
            'get-chat-store-metrics',
        ]));

        $output = ob_get_clean();

        $this->debug($output);

        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    public function testExportCsvDownloadAllActivitySummaryAssociatesWithData(FunctionalTester $I)
    {
        $this->app['configs']['retailer.group_tasks.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testExportCsvDownloadAllActivitySummaryAssociatesWithData');

        ob_start();

        $this->getMockedReporting($this->prepareRequest([
            'get-marketing',
            'get-sales-total',
            'get-marketing-revise',
            'get-chat-user-metrics',
        ]));

        $output = ob_get_clean();

        $this->debug($output);

        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    public function testExportCsvDownloadAllActivitySummaryStoresWithData(FunctionalTester $I)
    {
        $this->app['configs']['retailer.group_tasks.is_enabled'] = true;
        $this->insertFixtureGroup($I, 'testExportCsvDownloadAllActivitySummaryStoresWithData');

        ob_start();

        $this->getMockedReporting($this->prepareRequest([
            'get-store-marketing',
            'get-store-sales-total',
            'get-store-marketing-revise',
            'get-chat-store-metrics',
        ]));

        $output = ob_get_clean();

        $this->debug($output);

        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    public function testExportCsvDownloadAllActivitySummaryStoresWithPersonalShopperAndMessageTextDisabled(FunctionalTester $I)
    {
        $I->wantTo('export csv download all activity summary stores with personal shopper and message text disabled');
        $this->app['configs']['retailer.group_tasks.is_enabled'] = true;

        $this->app['configs']['retailer.has_personal_shopper'] = false;
        $this->app['configs']['messaging.text.enabled'] = false;


        $this->insertFixtureGroup($I, 'testExportCsvDownloadAllActivitySummaryStoresWithData');

        ob_start();

        $this->getMockedReporting($this->prepareRequest([
            'get-store-marketing',
            'get-store-sales-total',
            'get-store-marketing-revise',
            'get-chat-store-metrics',
        ]));

        $output = ob_get_clean();

        $this->debug($output);

        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    public function testExportCsvSidebarMetrics(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'testExportCsvSidebarMetrics');

        ob_start();

        // This metrics can't be download "on the fly" but from bucket.
        // To bypass this logic, force format to json.
        $output = $this->getMockedReporting($this->prepareRequest([
            'download-sidebar-metrics',
        ], 'json'));

        $output = ob_get_clean();

        $this->debug($output);

        $file = sprintf('%s/files/%s.csv', __DIR__, __FUNCTION__);
        $this->verifyCsv($I, $output, $file);
    }

    private function prepareRequest(array $type, $format = 'csv')
    {
        return [
            'user' => 'all',
            'timezone' => 'America\Montreal',
            'period' => '30days',
            'type' => $type,
            'format' => $format,
        ];
    }

    private function getMockedReporting(array $request): \Reporting
    {
        return Stub::construct(
            \Reporting::class,
            [
                $request,
                null,
                $this->app
            ],
            [

            ]
        );
    }

    private function verifyCsv(FunctionalTester $I, string $csv, string $snapshotFile)
    {
        if (getenv('snapshot')) {
            file_put_contents($snapshotFile, $csv);
        }
        $expected = file_get_contents($snapshotFile);
        $I->assertEquals($expected, $csv);
    }
}
