<?php

namespace SF\functional\Reporting;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\Services\SidebarStatsAggregator;

class AggregateSidebarDailyStatsCest extends BaseFunctional
{
    /** @var SidebarStatsAggregator $aggreggator */
    protected $aggregator;

    public function _before($I)
    {
        parent::_before($I);

        $this->aggregator = $this->app['service.sidebar.stats.aggregator'];
    }

    /** @group database_transaction */
    public function testAggregateSidebarDailyStatsWhenTableIsEmpty(FunctionalTester $I)
    {
        $I->wantTo("Test aggregate sidebar daily stats when table is empty");
        $this->insertFixtureGroup($I, 'SidebarTrackingWithoutDailyStatsTable');

        $I->dontSeeInDatabase('sf_sidebar_daily_stats', [
            'date' => '2019-04-04',
            'sidebar_mobile_unique_minimize' => 2,
        ]);

        $this->aggregator->aggregate('2019-04-04');

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date' => '2019-04-04',
            'sidebar_mobile_unique_minimize' => 2,
        ]);
    }

    /** @group database_transaction */
    public function testAggregateSidebarDailyStatsWhenTableIsNotEmpty(FunctionalTester $I)
    {
        $I->wantTo("Test aggregate sidebar daily stats when table is not empty");
        $this->insertFixtureGroup($I, 'SidebarTrackingWithDailyStatsTable');

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date' => '2019-04-04',
            'sidebar_mobile_unique_minimize' => 40,
        ]);

        $this->aggregator->aggregate('2019-04-04');

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date' => '2019-04-04',
            'sidebar_mobile_unique_minimize' => 2,
        ]);
    }

    /** @group database_transaction */
    public function testAggregateContextualWidgetDailyStats(FunctionalTester $I)
    {
        $I->wantTo("Test aggregate contextual widget daily stats from sf_sidebar_event_log table, when daily stats table(row) is not empty");
        $this->insertFixtureGroup($I, 'SidebarTrackingWithDailyStatsTable');
        $this->insertFixtureGroup($I, 'ContextualTrackingEventLog');

        $this->aggregator->aggregate('2019-04-04');

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date'                                  => '2019-04-04',
            'contextual_widget_mobile_view'         => 4,
            'contextual_widget_mobile_unique_view'  => 3,
            'contextual_widget_mobile_click'        => 2,
            'contextual_widget_mobile_unique_click' => 1,
        ]);

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date'                                   => '2019-04-04',
            'contextual_widget_desktop_view'         => 2,
            'contextual_widget_desktop_unique_view'  => 1,
            'contextual_widget_desktop_click'        => 4,
            'contextual_widget_desktop_unique_click' => 2,
        ]);
    }

    /** @group database_transaction */
    public function testAggregateDailyStatsByRotateSidebarEvent(FunctionalTester $I)
    {
        $I->wantTo("Test aggregate sidebar event into rotate sf_sidebar_event_log_yyyymmdd table, when daily stats table(row) is not empty");
        $this->insertFixtureGroup($I, 'SidebarTrackingWithDailyStatsTable');
        $this->insertFixtureGroup($I, 'ContextualTrackingEventLogRotate');

        $this->aggregator->aggregate('2024-02-08');

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date'                                  => '2024-02-08',
            'contextual_widget_mobile_view'         => 4,
            'contextual_widget_mobile_unique_view'  => 3,
            'contextual_widget_mobile_click'        => 2,
            'contextual_widget_mobile_unique_click' => 1,
        ]);

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date'                                   => '2024-02-08',
            'contextual_widget_desktop_view'         => 2,
            'contextual_widget_desktop_unique_view'  => 1,
            'contextual_widget_desktop_click'        => 4,
            'contextual_widget_desktop_unique_click' => 2,
        ]);
    }

    /** @group database_transaction */
    public function testAggregateDailyStatsActionId(FunctionalTester $I)
    {
        $I->wantTo(
            "Test aggregate sidebar event based on action id"
        );

        $this->insertFixtureGroup($I, 'testAggregateDailyStatsActionId');

        $this->aggregator->aggregate('2024-02-08');

        $I->seeInDatabase('sf_sidebar_daily_stats', [
            'date'                                  => '2024-02-08',
            'contextual_widget_mobile_view'         => 1,
            'contextual_widget_mobile_unique_view'  => 1,
            'contextual_widget_mobile_click'        => 2,
            'contextual_widget_mobile_unique_click' => 1,
        ]);
    }
}
