<?php

declare(strict_types=1);

namespace SF\functional\Reporting;

use DateTime;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class ProcessorUsersCest extends BaseFunctional
{
    public const REGGIE_ID = 1;

    /** @group database_transaction */
    public function testProcessorsUsersActiveUsersInCurrentMonth(FunctionalTester $I)
    {
        $I->wantTo("Test processors users active users in current month");

        $this->insertFixtureGroup($I, 'activeUserInDateRange');
        $now = '2020-02-03 12:34:56';

        $result = $this->executeInternalProcess($now, [1000, 1001, 1002]);

        $I->assertCount(2, $result);

        $activeUsers = array_filter($result, function ($user) {
            return $user['time_active'] > REPORT_DOWNLOADUSER_THRESHOLD_ACTIVE;
        });
        $I->assertCount(1, $activeUsers);

        $this->validateArray([
            'ID' => '1001',
            'user_registered' => '2019-02-11 14:42:37',
            'user_status' => '1',
            'selling_mode' => '1',
            'time_active' => '2505600',
            'total_deleted_phone_number' => '0',
            'active_phone_number' => '0'
        ], array_shift($activeUsers));
    }

    /** @group database_transaction */
    public function testProcessorsUsersActiveTextMessageWithActiveOnlyCurrentMonth(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in current month with active phone number in current month");

        $now = gmdate('Y-m-d');

        // Add to DB a phone to reggie with current month
        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $now,
        ]);

        $result = $this->executeInternalProcess($now);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 0,
            'active_phone_number'        => 1,
        ], $result[0]);
    }

    /** @group database_transaction */
    public function testProcessorsUsersActiveTextMessageWithActivePreviousMonth(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in current month with active phone number in previous month");

        /** @var \DateTime $lastMonth */
        $lastMonth = new DateTime();
        $lastMonth->modify('last day of previous month');
        $lastMonth = $lastMonth->format('Y-m-d');

        // Add to DB a phone to reggie with previous month
        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $lastMonth,
        ]);

        $now    = gmdate('Y-m-d');
        $result = $this->executeInternalProcess($now);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 0,
            'active_phone_number'        => 1, // We return 1 even if it's in the past !
        ], $result[0]);
    }

    /** @group database_transaction */
    public function testProcessorsUsersActiveTextMessageCurrentMonthWithActiveCurrentMonthWithDeletedCurrentMonth(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in current month with active phone number in current month with one deleted in current month");

        $now = gmdate('Y-m-d');

        // Add to DB a phone to reggie with current month
        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $now,
        ]);

        // Add deleted (current month) phone number (Can use manager since we want to do "current month")
        $this->app['user_phone_numbers.manager']->delete(self::REGGIE_ID, self::REGGIE_ID);

        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $now,
        ]);

        $result = $this->executeInternalProcess($now);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 1,
            'active_phone_number'        => 1,
        ], $result[0]);
    }


    /** @group database_transaction */
    public function testProcessorsUsersActiveTextMessageCurrentMonthWithActiveWithDeletedPreviousMonth(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in current month with active phone number in current month with one deleted in previous month");

        $now = gmdate('Y-m-d');

        /** @var \DateTime $twoMonthsAgo */
        $twoMonthsAgo = new DateTime();
        $twoMonthsAgo->modify('-2 month');
        $twoMonthsAgo = $twoMonthsAgo->format('Y-m-d');

        // Add to DB a phone to reggie with current month
        $id = $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $twoMonthsAgo,
        ]);

        // Add deleted (previous month) phone number
        $this->app['user_phone_numbers.manager']->delete(self::REGGIE_ID, self::REGGIE_ID);

        /** @var \DateTime $lastMonth */
        $lastMonth = new DateTime();
        $lastMonth->modify('last day of previous month');
        $lastMonth = $lastMonth->format('Y-m-d');

        $I->updateInDatabase('sf_deleted_user_phone_number', [
            'deleted_at' => $lastMonth
        ], [
            'id' => $id
        ]);

        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $now,
        ]);

        $result = $this->executeInternalProcess($now);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 0, // Because delete was in previous month
            'active_phone_number'        => 1,
        ], $result[0]);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @throws \ReflectionException
     *
     * @group database_transaction
    */
    public function testProcessorsUsersActiveTextMessageWithActiveCurrentAndDeletedPreviousDateRange(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in previous month with active phone number with one deleted before");

        /** @var \DateTime $yearAgo */
        $yearAgo = new DateTime();
        $yearAgo->modify('-12 month');
        $yearAgo = $yearAgo->format('Y-m-d');

        /** @var \DateTime $threeMonthsAgo */
        $threeMonthsAgo = new DateTime();
        $threeMonthsAgo->modify('-2 month');
        $threeMonthsAgo = $threeMonthsAgo->format('Y-m-d');

        /** @var \DateTime $twoMonthsAgo */
        $twoMonthsAgo = new DateTime();
        $twoMonthsAgo->modify('-2 month');
        $twoMonthsAgo = $twoMonthsAgo->format('Y-m-d');

        // Add to DB a phone to reggie with current month
        $id = $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $threeMonthsAgo,
        ]);

        // Add deleted (previous month) phone number
        $this->app['user_phone_numbers.manager']->delete(self::REGGIE_ID, self::REGGIE_ID);

        /** @var \DateTime $lastMonth */
        $lastMonth = new DateTime();
        $lastMonth->modify('last day of previous month');
        $lastMonth = $lastMonth->format('Y-m-d');

        $I->updateInDatabase('sf_deleted_user_phone_number', [
            'deleted_at' => $twoMonthsAgo
        ], [
            'id' => $id
        ]);

        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $yearAgo,
        ]);

        $result = $this->executeInternalProcess($lastMonth);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 0, // Because delete was in previous month
            'active_phone_number'        => 1,
        ], $result[0]);
    }

    /** @group database_transaction */
    public function testProcessorsUsersActiveTextMessageWithActiveCurrentAndDeletedCurrentDateRange(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in previous month with active phone number with one deleted during");

        /** @var \DateTime $yearAgo */
        $yearAgo = new DateTime();
        $yearAgo->modify('-12 month');
        $yearAgo = $yearAgo->format('Y-m-d');

        /** @var \DateTime $threeMonthsAgo */
        $threeMonthsAgo = new DateTime();
        $threeMonthsAgo->modify('-2 month');
        $threeMonthsAgo = $threeMonthsAgo->format('Y-m-d');

        /** @var \DateTime $twoMonthsAgo */
        $twoMonthsAgo = new DateTime();
        $twoMonthsAgo->modify('-2 month');
        $twoMonthsAgo = $twoMonthsAgo->format('Y-m-d');

        // Add to DB a phone to reggie with current month
        $id = $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $threeMonthsAgo,
        ]);

        // Add deleted (previous month) phone number
        $this->app['user_phone_numbers.manager']->delete(self::REGGIE_ID, self::REGGIE_ID);

        /** @var \DateTime $lastMonth */
        $lastMonth = new DateTime();
        $lastMonth->modify('last day of previous month');
        $lastMonth = $lastMonth->format('Y-m-d');

        $I->updateInDatabase('sf_deleted_user_phone_number', [
            'deleted_at' => $lastMonth
        ], [
            'id' => $id
        ]);

        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $yearAgo,
        ]);

        $result = $this->executeInternalProcess($lastMonth);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 1, // Because delete was in current month
            'active_phone_number'        => 1,
        ], $result[0]);
    }

    /** @group database_transaction */
    public function testProcessorsUsersActiveTextMessageWithActiveCurrentAndDeletedAfterDateRange(FunctionalTester $I)
    {
        $I->wantTo("Test Reporting users in previous month with active phone number with one deleted after");

        /** @var \DateTime $yearAgo */
        $yearAgo = new DateTime();
        $yearAgo->modify('-12 month');
        $yearAgo = $yearAgo->format('Y-m-d');

        /** @var \DateTime $now */
        $now = new DateTime();
        $now = $now->format('Y-m-d');

        // Add to DB a phone to reggie with current month
        $id = $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $now,
        ]);

        // Add deleted (previous month) phone number
        $this->app['user_phone_numbers.manager']->delete(self::REGGIE_ID, self::REGGIE_ID);

        /** @var \DateTime $lastMonth */
        $lastMonth = new DateTime();
        $lastMonth->modify('last day of previous month');
        $lastMonth = $lastMonth->format('Y-m-d');

        $I->updateInDatabase('sf_deleted_user_phone_number', [
            'deleted_at' => $now
        ], [
            'id' => $id
        ]);

        $I->haveInDatabase('sf_user_phone_number', [
            'user_id'            => self::REGGIE_ID,
            'phone_number'       => '15145082007',
            'created_by_user_id' => 1,
            'created_at'         => $yearAgo,
        ]);

        $result = $this->executeInternalProcess($lastMonth);

        $this->validateArray([
            'ID'                         => self::REGGIE_ID,
            'user_registered'            => '2001-01-01 00:00:00',
            'user_status'                => 1,
            'selling_mode'               => 1,
            'time_active'                => '',
            'total_deleted_phone_number' => 0, // Because delete was in after month
            'active_phone_number'        => 1,
        ], $result[0]);
    }

    /**
     * Execute internal process and return only Reggie's result
     *
     * @param $from
     * @param array $userIDs  The default [self::REGGIE_ID], test only on reggie
     * @return null|array
     * @throws \ReflectionException
     */
    private function executeInternalProcess($from, array $userIDs = [self::REGGIE_ID]): ?array
    {
        $db = $this->app['repositories.mysql'];

        // This reporting return always everyone anyway
        $timezone = 'America/Montreal';
        $dateFrom = $from;
        $dateTo = ''; // We don't care for this processor afaik
        $grouped = false; // I'm only testing rep metrics
        $completed = true;

        $class = new \ReportingProcessorUsers($userIDs, $timezone, $dateFrom, $dateTo, null, $grouped, $completed);

        $results = $this->getMethod($class, "internalProcess")->invokeArgs($class, [$db]);

        return array_filter($results, function (array $user) use ($userIDs) {
            return in_array($user['ID'], $userIDs);
        });
    }
}
