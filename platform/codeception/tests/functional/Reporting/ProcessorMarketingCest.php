<?php

namespace SF\functional\Reporting;

use SF\FunctionalTester;
use Salesfloor\Services\Util;

class ProcessorMarketingCest extends \SF\functional\BaseFunctional
{
    const REGGIE_ID = 1;
    private $db;
    private $dateFrom;
    private $dateTo;

    public function _before($I)
    {
        parent::_before($I);

        $this->db       = $this->app['repositories.mysql'];
        $this->dateFrom = date('Y-m-d H:i:s', strtotime('-12 hour'));
        $this->dateTo   = gmdate('Y-m-d H:i:s');
    }

    /** @group database_transaction */
    public function testTimeQuerying(FunctionalTester $I)
    {
        // Define scenarios for the test cases
        // Not able to access Fixture::getFixture so put data here
        $makeTimeStamp = function ($str) {
            return date('Y-m-d H:i:s', strtotime($str));
        };
        $scenarios = [
            [
                "dateFrom"      => $makeTimeStamp('-3 hour'),
                "dateTo"        => $makeTimeStamp('+3 hours'),
                "expectedCount" => 0,
                "timezone"      => "UTC",
            ],
            [
                "dateFrom"      => $makeTimeStamp('-6 hour'),
                "dateTo"        => $makeTimeStamp('+6 hours'),
                "expectedCount" => 5,
                "timezone"      => "UTC",
            ],
            [   // convert dateFrom / DateTo from eastern time (-5) to utc,
                "dateFrom"      => $makeTimeStamp('-11 hours'),
                "dateTo"        => $makeTimeStamp('+1 hours'),
                "expectedCount" => 5,
                "timezone"      => "EST",
            ]
        ];

        // Select a given scenario
        $selectScenario = function ($scenario) use ($scenarios, &$dateFrom, &$dateTo, &$timezone, &$expectedCount) {
            $dateFrom = $scenarios[$scenario]["dateFrom"];
            $dateTo = $scenarios[$scenario]["dateTo"];
            $expectedCount = $scenarios[$scenario]["expectedCount"];
            $timezone = $scenarios[$scenario]["timezone"];
        };

        $I->wantTo("Test applyUsersFiltering()");
        $this->insertFixtureGroup($I, 'SocialShopTracking');

        $db = $this->db;
        $class = $this->mockupClass($dateFrom, $dateTo); // date from and to does not matter in this case for this method
        $methodReflect = $this->getMethod($class, "applyDateRangeFilter");
        $methodReflect->setAccessible(true);


        /**
         * Fetch the count using the date range filter
         * @param $dateFrom
         * @param $dateTo
         * @param $timezoneField
         * @return int | null
         */
        $fetchCount = function ($dateFrom, $dateTo, $timezoneField, $timezone) use ($db, $class, $I, $methodReflect) {
            $queryBuilder = $db->getQueryBuilder();
            $queryBuilder->select('COUNT(*) AS count');
            $queryBuilder->from('sf_event_log');
            $methodReflect->invokeArgs($class, array($queryBuilder, 'created_at', $dateFrom, $dateTo, $timezoneField, $timezone));
            $result = $db->executeCustomQuery($queryBuilder);
            $I->debug($queryBuilder->getSql());
            if (!empty($result)) {
                $row = reset($result);
                return $row["count"];
            }
            return null;
        };

        foreach ($scenarios as $i => $details) {
            $selectScenario($i);
            $result = $fetchCount($dateFrom, $dateTo, null, $timezone);
            $I->assertEquals($expectedCount, $result);
        }
    }


    /** @group database_transaction */
    public function testGetSocialShopTrackingTotalVisit(FunctionalTester $I)
    {
        $I->wantTo("Test Get Social Shop Tracking Total Visit");
        $this->insertFixtureGroup($I, 'SocialShopTracking');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getSocialShopTrackingVisit")->invokeArgs($class, [$this->db]);

        $I->assertEquals(['socialshop_total_visit' => 3], $result);
    }

    /** @group database_transaction */
    public function testGetSocialShopTrackingUniqueVisit(FunctionalTester $I)
    {
        $I->wantTo("Test Get Social Shop Tracking Unique Visit");
        $this->insertFixtureGroup($I, 'SocialShopTracking');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getSocialShopTrackingVisit")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(['socialshop_unique_visit' => 2], $result);
    }

    /** @group database_transaction */
    public function testGetSocialShopTrackingStorefrontClickEvent(FunctionalTester $I)
    {
        $I->wantTo("Test Get Social Shop Tracking Storefront Click Event ");
        $this->insertFixtureGroup($I, 'SocialShopTracking');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getSocialShopTrackingStorefrontClickEvent")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(['socialshop_storefront_click' => 1], $result);
    }

    /** @group database_transaction */
    public function testGetSocialShopTrackingProductClickEvent(FunctionalTester $I)
    {
        $I->wantTo("Test Get Social Shop Tracking Product Click Event ");
        $this->insertFixtureGroup($I, 'SocialShopTracking');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getSocialShopTrackingProductClickEvent")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(['socialshop_product_click' => 1], $result);
    }

    /** @group database_transaction */
    public function testGetSocialShopTrackingClickEvent(FunctionalTester $I)
    {
        $I->wantTo("Test Get Social Shop Tracking Product + storefront Click Event ");
        $this->insertFixtureGroup($I, 'SocialShopTracking');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getSocialShopTrackingProductClickEvent")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(['socialshop_product_click' => 1, 'socialshop_product_click' => 1], $result);
    }

    /** @group database_transaction */
    public function testGetCorporateTaskMetricDeleteKpi(FunctionalTester $I)
    {
        $I->wantTo("Test Get corporate task  metric : tasks_corporate_deleted");
        $this->insertFixtureGroup($I, 'CorporateTaskMetric');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getTaskStatsByParentCorporateTask")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(['tasks_corporate_deleted' => 2], $result);
    }

    /** @group database_transaction */
    public function testGetCustomerSupportRequestKpi(FunctionalTester $I)
    {
        $I->wantTo("Test Get Customer Support Request Kpi");
        $this->insertFixtureGroup($I, 'CustomerSupportRequestsTracking');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getCustomerSupportRequestPerChannelStats")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(1, $result['ask_question_req_cs_email']);
        $I->assertEquals(1, $result['personal_shopper_req_cs_email']);

        $result = $this->getMethod($class, "getServicePerChannelStats")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals(1, $result['ask_question_req_email']);
        $I->assertEquals(1, $result['appointment_req_email']);
        $I->assertEquals(1, $result['personal_shopper_req_email']);
    }

    /** @group database_transaction */
    public function testGetReturnStatsKpi(FunctionalTester $I)
    {
        $I->wantTo("Test get return stats Kpi");
        $this->insertFixtureGroup($I, 'CustomerSupportRequestsTrackingRepTransaction');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getReturnStats")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals("300", $result['total_return_value']);
    }

    /** @group database_transaction */
    public function testGetVideoChatStatsKpi(FunctionalTester $I)
    {
        $I->wantTo("Test get video chat stats Kpi");
        $this->insertFixtureGroup($I, 'VideoChatEvents');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getVideoChatStats")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals("1", $result['video_chat_sessions']);
        $I->assertEquals("3600", $result['video_chat_duration']);
    }

    /** @group database_transaction */
    public function testGetVirutalAppointmentStatsKpi(FunctionalTester $I)
    {
        $I->wantTo("Test get virtual appointment stats Kpi");
        $this->insertFixtureGroup($I, 'VirtualAppointmentEvents');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getVirtualAppointmentStats")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals("1", $result['virtual_appointment_sessions']);
        $I->assertEquals("3600", $result['virtual_appointment_duration']);
    }

    /** @group database_transaction */
    public function testGetCancelledAppointments(FunctionalTester $I)
    {
        $I->wantTo("Test get CancelledAppointments Kpi");
        $this->insertFixtureGroup($I, 'CancelledAppointments');
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getCancelledAppointments")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals("2", $result['cancelled_appointment']);
    }

    /** @group database_transaction */
    public function testGetTrxStatsNormalCase(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - transaction stats - normal case");

        $this->insertFixtureGroup($I, 'getTrxStatsNormalCase');

        $class = $this->getMockUpProcessorMarketing($this->dateFrom, $this->dateTo, [1,2], true);
        $result = $this->getMethod($class, "getTrxStats")->invokeArgs($class, [$this->db, true]);

        $this->debug($result);

        $I->assertEquals([
            'n_sales_transactions' => 2,
            'total_order_value' => 350,
            'avg_order_value' => 175,
        ], $result['user-1']);

        $I->assertEquals([
            'n_sales_transactions' => 1,
            'total_order_value' => 350,
            'avg_order_value' => 350,
        ], $result['user-2']);
    }

    /** @group database_transaction */
    public function testGetTrxStatsEdgeCaseDupulicateUserIdZero(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - transaction stats - duplicate - user id zero");

        $this->insertFixtureGroup($I, 'getTrxStatsEdgeCaseDupulicateUserIdZero');

        $class = $this->getMockUpProcessorMarketing($this->dateFrom, $this->dateTo, [1,2], true);
        $result = $this->getMethod($class, "getTrxStats")->invokeArgs($class, [$this->db, true]);

        $this->debug($result);

        // The transaction with user_id=0 + same trx_id is ignored since not linked to same rep.
        $I->assertEquals([
            'n_sales_transactions' => 2,
            'total_order_value' => 340,
            'avg_order_value' => 170,
        ], $result['user-1']);

        $I->assertEquals([
            'n_sales_transactions' => 1,
            'total_order_value' => 350,
            'avg_order_value' => 350,
        ], $result['user-2']);
    }

    /** @group database_transaction */
    public function testGetTrxStatsEdgeCaseDuplicateUserIdDifferent(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - transaction stats - duplicate - user id different");

        $this->insertFixtureGroup($I, 'getTrxSTatsEdgeCaseDuplicateUserIdDifferent');

        $class = $this->getMockUpProcessorMarketing($this->dateFrom, $this->dateTo);
        $result = $this->getMethod($class, "getTrxStats")->invokeArgs($class, [$this->db, true]);

        $this->debug($result);

        // 2 times same transaction but different rep. Both will get it
        $I->assertEquals([
            'n_sales_transactions' => 2,
            'total_order_value' => 310,
            'avg_order_value' => 155,
        ], $result['user-1']);

        $I->assertEquals([
            'n_sales_transactions' => 1,
            'total_order_value' => 110,
            'avg_order_value' => 110,
        ], $result['user-2']);
    }

    /**
     * Something wring with this test.
     * @skip
     * @group database_transaction
     * */
    public function testGetLiveChatStatsKpi(FunctionalTester $I)
    {
        $I->wantTo("Test get live chat stats Kpi");
        $this->insertFixtureGroup($I, 'LiveChatEvents');

        // user described in mock is user_id 1 who did not answer a request.
        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        $result = $this->getMethod($class, "getChatAnswerRate")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals("0", $result['chat_answer_rate']);
    }

    public function testAvgResponseTimeIncludeOutgoingSms(FunctionalTester $I)
    {
        $I->wantTo("Test get average response time includes outgoing SMS from a request");
        $this->insertFixtureGroup($I, 'AvgResponseTimeOutgoingSms');

        $class = $this->mockupClass($this->dateFrom, $this->dateTo);

        // Should detect both outgoing SMS communications
        $queryBuilder = $this->getMethod($class, "getAvgInitForRequestQuery")->invokeArgs($class, [$this->db]);
        $result = $this->db->executeCustomQuery($queryBuilder);
        $I->assertEquals(2, count($result));
    }

    /**
     * Test Grouped Products KPI
     * @group database_transaction
     * */
    public function testGroupedProductsKpi(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'GroupedProducts');

        // user described in mock is user_id 1 who did not answer a request.
        $class = $this->mockupClass($this->dateFrom, $this->dateTo, true);
        $class->setApp($this->app);

        $mysqlFlagUpdated = $this->disableOnlyFullGroupBy();

        $result = $this->getMethod($class, "getGroupedProductsByEmailStats")->invokeArgs($class, [$this->db, true]);
        $combined = $class->updateAggregateValues($result['user-123']);
        $expectedEmailReturn = [
            'grouped_products_shared_email_total' => 4,
            'grouped_products_shared_email_total_products' => 15,
            'grouped_products_shared_email_avg_products' => 3.75,
        ];
        $data = array_intersect_key($combined, $expectedEmailReturn);

        $this->validateArray($expectedEmailReturn, $data);

        $result = $this->getMethod($class, "getGroupedProductsBySmsStats")->invokeArgs($class, [$this->db, true]);
        $combined = $class->updateAggregateValues($result['user-123']);
        $expectedSmsReturn = [
            'grouped_products_shared_sms_total' => 2,
            'grouped_products_shared_sms_total_products' => 10,
            'grouped_products_shared_sms_avg_products' => 5,
        ];
        $data = array_intersect_key($combined, $expectedSmsReturn);

        $this->validateArray($expectedSmsReturn, $data);

        // Get stores mode
        $class = $this->mockupClass($this->dateFrom, $this->dateTo, false, ['1003']);
        $class->setApp($this->app);

        $result = $this->getMethod($class, "getGroupedProductsByEmailStats")->invokeArgs($class, [$this->db, true ]);
        $combined = $class->updateAggregateValues($result);
        $data = array_intersect_key($combined, $expectedEmailReturn);
        $this->validateArray($expectedEmailReturn, $data);

        $result = $this->getMethod($class, "getGroupedProductsBySmsStats")->invokeArgs($class, [$this->db, true]);
        $combined = $class->updateAggregateValues($result);
        $data = array_intersect_key($combined, $expectedSmsReturn);
        $this->validateArray($expectedSmsReturn, $data);

        $this->enableOnlyFullGroupBy($mysqlFlagUpdated);
    }

    /**
     * @group database_transaction
     */
    public function testFlaggedChatConversations(FunctionalTester $I)
    {
        // Rep mode and team mode save both the user_id of the user itself (not difference)
        $I->wantTo("Test flagged chat conversations kpis");

        $this->insertFixtureGroup($I, 'testFlaggedChatConversations');

        $mysqlFlagUpdated = $this->disableOnlyFullGroupBy();

        // Default range is 12 hrs ; nothing is returned since first chat flagged is 24 hrs ago.
        $class = $this->getMockUpProcessorMarketing($this->dateFrom, $this->dateTo, [1,2]);

        $result = $this->getMethod($class, "getFlaggedChatConversations")->invokeArgs($class, [$this->db, true]);

        $I->assertEmpty($result);

        // Since the flagged chat is 24hrs ago ; this will catch it !
        $class = $this->getMockUpProcessorMarketing(date('Y-m-d H:i:s', strtotime('-36 hour')), $this->dateTo, [1,2]);

        $result = $this->getMethod($class, "getFlaggedChatConversations")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals([
            'flagged_chat_conversations' => 1
        ], $result['user-1']);

        $I->assertEquals([
            'flagged_chat_conversations' => 1
        ], $result['user-2']);

        // Since the flagged chat is 24hrs ago ; this will catch it !
        $class = $this->getMockUpProcessorMarketing(date('Y-m-d H:i:s', strtotime('-36 hour')), $this->dateTo, [1]);

        $result = $this->getMethod($class, "getFlaggedChatConversations")->invokeArgs($class, [$this->db, true]);

        $I->assertEquals([
            'flagged_chat_conversations' => 1
        ], $result['user-1']);

        // Since the flagged chat is 24hrs ago ; this will catch it !
        $class = $this->getMockUpProcessorMarketing(date('Y-m-d H:i:s', strtotime('-36 hour')), $this->dateTo, [1,2], false);

        $result = $this->getMethod($class, "getFlaggedChatConversations")->invokeArgs($class, [$this->db, true]);


        $I->assertEquals([
            'flagged_chat_conversations' => 2
        ], $result);

        $this->enableOnlyFullGroupBy($mysqlFlagUpdated);
    }

    /**
     * *Warning* This function may be misleading. Grouped=false => will sum all rep together. So if $userIDs
     * contains more than 1 id, they will all be sum() together. *Warning*
     *
     * @param $dateFrom
     * @param $dateTo
     *
     * @return \ReportingProcessorMarketing
     * @throws \Exception
     */
    private function mockupClass($dateFrom, $dateTo, $grouped = false, $stores = [])
    {
        // Test only on reggie
        $userIDs  = [self::REGGIE_ID]; // This reporting return always everyone anyway
        $timezone = 'America/Montreal';

        $completed = true;

        global $SF_19242_WORKAROUND;
        $SF_19242_WORKAROUND = true;

        $class = new \ReportingProcessorMarketing($userIDs, $timezone, $dateFrom, $dateTo, null, $grouped, $completed, $stores);

        return $class;
    }

    /**
     * This is really similar to mockupClass, but we can control userIds and grouped params.
     *
     * @param       $dateFrom
     * @param       $dateTo
     * @param array $userIds
     * @param bool  $grouped
     *
     * @return \ReportingProcessorMarketing
     * @throws \Exception
     */
    private function getMockUpProcessorMarketing($dateFrom, $dateTo, $userIds = [], $grouped = true)
    {
        $timezone = 'America/Montreal';

        // Not sure if it's needed but keep it for now
        global $SF_19242_WORKAROUND;
        $SF_19242_WORKAROUND = true;

        $class = new \ReportingProcessorMarketing($userIds, $timezone, $dateFrom, $dateTo, null, $grouped, true);

        return $class;
    }
}
