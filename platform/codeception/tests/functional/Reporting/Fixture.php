<?php

namespace SF\functional\Reporting;

use Carbon\Carbon;
use Salesfloor\Models\SidebarEventLog;
use SF\BaseFixture;
use Codeception\Util\Fixtures;
use Salesfloor\Models\GroupTask\GroupTaskActivity;
use Salesfloor\Services\Event;
use Salesfloor\Services\Queue\SidebarEventQueue as SidebarEventQueueService;

class Fixture extends BaseFixture
{
    public const REGGIE_ID = 1;

    public function userDetection()
    {
        $socialFeedProductId = 123;
        $socailFeedId = 234;

        Fixtures::add('MultipleStoreSwitchesSameDay', [
            'sf_store' => [[
                'store_id' => '777',
                'name' => 'UTC Store',
                'timezone' => 'Etc/UTC',
                'sf_identifier' => 'utc-store',
                'latitude'          => 45.4910,
                'longitude'         => -73.5658,
                'country'           => 'CA',
                'region'            => 'QC',
                'city'              => 'Montreal',
                'address'           => '1455 Peel Streets',
                'postal'            => 'H3A 1T5',
                'phone'             => '************',
                'timezone'          => 'America/Montreal',
            ]],
            'wp_users' => [$this->makeSellingModeUser(19063, 777)],
            'sf_wp_user_field_history' => [
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 1003,
                    'new_value' => 2003,
                    'field_modified_at' => $this->utcDateTime('-1 hour'),
                    'source' => 'ben'
                ],
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 2003,
                    'new_value' => 777,
                    'field_modified_at' => $this->utcDateTime('-5 minutes'),
                    'source' => 'ben'
                ]
            ]
        ]);

        Fixtures::add('MultipleStoreSwitchesOverLongPeriod', [
            'sf_store' => [[
                'store_id' => '777',
                'name' => 'UTC Store',
                'timezone' => 'Etc/UTC',
                'sf_identifier' => 'utc-store',
                'latitude'          => 45.4910,
                'longitude'         => -73.5658,
                'country'           => 'CA',
                'region'            => 'QC',
                'city'              => 'Montreal',
                'address'           => '1455 Peel Streets',
                'postal'            => 'H3A 1T5',
                'phone'             => '************',
            ]],
            'wp_users' => [$this->makeSellingModeUser(19063, 777)],
            'sf_wp_user_field_history' => [
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 1003,
                    'new_value' => 2003,
                    'field_modified_at' => $this->utcDateTime('-20 days'),
                    'source' => 'ben'
                ],
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 2003,
                    'new_value' => 777,
                    'field_modified_at' => $this->utcDateTime('-5 days'),
                    'source' => 'ben'
                ]
            ]
        ]);

        Fixtures::add('SocialShopTracking', [
            'sf_event_log' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_VIEW',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539455,
                    'action'      => 'SOCIALSHOP_VIEW',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_VIEW',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_PRODUCT_CLICK',
                    'action_id'   => $socialFeedProductId,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_STOREFRONT_CLICK',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
            ],
            'sf_shop_feed_product' => [
                [
                    'id'            => $socialFeedProductId,
                    'feed_id'       => $socailFeedId,
                    'product_id'    => 123456,
                    'created_at'    => '2018-09-04 19:11:11',
                    'updated_at'    => '2018-09-04 19:11:11',
                ],
            ],
            'sf_shop_feed' => [
                [
                    'id'            => $socailFeedId,
                    'user_id'       => self::REGGIE_ID,
                    'source'        => 'share',
                    'comment'       => 'comment 1',
                    'created_at'    => '2018-12-31 05:00:00',
                    'updated_at'    => '2018-12-31 05:00:00',
                    'created_by'    => 1,

                ],
            ],
        ]);

        Fixtures::add('CustomerSupportRequestsTracking', [
            'sf_questions' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
            ],
            'sf_personal_shopper' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF1234567',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-6 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                ],
            ],
            'sf_appointments' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF3456',
                    'date'    => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'event_duration' => 60,
                ],
            ],
            'sf_events' => [
                [
                    'type'        => 6,
                    'date'        => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'uniq_id'     => 'SF12345',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
                [
                    'type'        => 8,
                    'date'        => date('Y-m-d H:i:s', strtotime('-6 hour')),
                    'uniq_id'     => 'SF1234567',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
                [
                    'type'        => 7,
                    'date'        => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'uniq_id'     => 'SF3456',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
            ],
            'sf_messages'  => [
                [
                    "user_id"       => '1',
                    "owner_id"      => '1',
                    "customer_id"   => '1', // The is not the real customer that made the request
                    "thread_id"     => '1',
                    "from_type"     => 'user',
                    "from_email"    => '<EMAIL>',
                    "from_name"     => 'hummm',
                    "request_type"  => 'contact_me',
                    "request_id"    => '1',
                    "attachment"    => '',
                    "type"          => 'customer_request',
                    "date"          => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    "status"        => 'customer-support',
                    "category"      => 'inbox',
                    "last_category" => null,
                    "title"         => 'Contact Me Request',
                    "products"      => '',
                    "comment"       => 'test',
                    "locale"        => 'en_US',
                ],
                [
                    "user_id"       => '1',
                    "owner_id"      => '1',
                    "customer_id"   => '1', // The is not the real customer that made the request
                    "thread_id"     => '1',
                    "from_type"     => 'user',
                    "from_email"    => '<EMAIL>',
                    "from_name"     => 'hummm',
                    "request_type"  => 'personal_shopper',
                    "request_id"    => '1',
                    "attachment"    => '',
                    "type"          => 'customer_request',
                    "date"          => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    "status"        => 'customer-support',
                    "category"      => 'inbox',
                    "last_category" => null,
                    "title"         => 'Personal Shopper Request',
                    "products"      => '',
                    "comment"       => 'test',
                    "locale"        => 'en_US',
                ],
            ]
        ]);

        Fixtures::add('CustomerSupportRequestsTrackingRepTransaction', [
            'sf_rep_transaction' => [
                [
                    'user_id' => 1,
                    'trx_id'  => '77789835',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type' => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id' => 1,
                    'trx_id'  => '77740071',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'trx_type' => 'sale',
                    'trx_total' => 200.00,
                ],
                [
                    'user_id' => 1,
                    'trx_id'  => '77789835',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'trx_type' => 'cancel',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id' => 1,
                    'trx_id'  => '77740071',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'trx_type' => 'return',
                    'trx_total' => 200.00,
                ],
            ],
        ]);

        Fixtures::add('VideoChatEvents', [
            'sf_events' => [
                [
                    'type'        => 101,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF12345',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                ],
                [
                    'type'        => 102,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                ],
            ],
        ]);

        Fixtures::add('LiveChatEventsSameStoreAnswer', [
            'wp_users' => [$this->makeSellingModeUser(19063, 2003)],
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
            ],
        ]);
        Fixtures::add('LiveChatEventsDiffStoreAnswer', [
            'wp_users' => [$this->makeSellingModeUser(19063, 2003)],
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 19063,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 19063,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 19063,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
            ],
        ]);
        Fixtures::add('LiveChatEventsTeamModeSameStoreAnswer', [
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2000,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
            ],
        ]);
        Fixtures::add('LiveChatEventsTeamModeDiffStoreAnswer', [
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2000,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2002,
                    'customer_id' => 0,
                    'store_id'    => 1032,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2002,
                    'customer_id' => 0,
                    'store_id'    => 1032,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2002,
                    'customer_id' => 0,
                    'store_id'    => 1032,
                ],
            ],
        ]);
        $this->generateTeamModeCore();

        Fixtures::add('VirtualAppointmentEvents', [
            'sf_events' => [
                [
                    'type'        => 111,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'sf12345-virtual-appointment',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
                [
                    'type'        => 112,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'sf12345-virtual-appointment',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
            ],
        ]);

        // some data set to test Corporate Task kpi
        // for example: tasks_corporate_deleted
        Fixtures::add('CorporateTaskMetric', [
            'sf_task' => [
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'details',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 1,
                    'status'    => 'dismissed',
                    'details'   => 'details',
                    'parent_id' => 2,
                ],
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'details',
                    'parent_id' => 3,
                ],
            ],
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        =>  $this->utcDateTime('-6 hours'),
                    "is_deleted"        => 1
                ],
                [
                    'id'                => 2,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        =>  $this->utcDateTime('-6 hours'),
                    "is_deleted"        => 1
                ],
                [
                    'id'                => 3,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        =>  $this->utcDateTime('-6 hours'),
                    "is_deleted"        => 1
                ],
            ],
        ]);

        $sidebarStatsWithDailyStatsTable = array_merge(
            $this->getElbStatsAndSidebarEvents(),
            $this->getSidebarDailyStats()
        );

        Fixtures::add('SidebarTrackingWithoutDailyStatsTable', $this->getElbStatsAndSidebarEvents());
        Fixtures::add('SidebarTrackingWithDailyStatsTable', $sidebarStatsWithDailyStatsTable);
        Fixtures::add('ContextualTrackingEventLog', $this->getContextualWidgetEventLog());
        Fixtures::add('ContextualTrackingEventLogRotate', $this->getContextualWidgetEventLogRotate());

        Fixtures::add('CancelledAppointments', [
            'sf_appointments' => [
                [
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF3456',
                    'date'        => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'status'      => 'cancelled',
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                ],
                [
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF345667',
                    'date'        => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'status'      => 'cancelled',
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                ],
            ],
        ]);
    }

    public function addFixtureTransactionStatsKpis()
    {
        Fixtures::add('getTrxStatsNormalCase', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 250.00,
                ],
                [
                    'user_id'   => 2,
                    'trx_id'    => '77789837',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 350.00,
                ],
            ]
        ]);

        Fixtures::add('getTrxStatsEdgeCaseDupulicateUserIdZero', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 0,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 250.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 240.00,
                ],
                [
                    'user_id'   => 2,
                    'trx_id'    => '77789837',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 350.00,
                ],
            ]
        ]);

        Fixtures::add('getTrxSTatsEdgeCaseDuplicateUserIdDifferent', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 2,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 110.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 210.00,
                ],
            ]
        ]);
    }

    public function addFixtureActiveUsersInDateRange()
    {
        Fixtures::add('activeUserInDateRange', [
            'sf_store' => [
                [
                    'store_id' => '1000',
                    'name' => 'UTC Store',
                    'timezone' => 'Etc/UTC',
                    'sf_identifier' => 'utc-store',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                ]
            ],
            'wp_users' => [
                [
                    'ID' => '1000',
                    'user_login' => 'salesfloor_benrodier',
                    'user_pass' => '$P$BgHlNnk5eFepwz5Z3hQdtCJVt755Y50',
                    'user_email' => '<EMAIL>',
                    'user_registered' => '2019-02-11 02:03:22',
                    'user_status' => '1',
                    'store' => '1000',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '505327',
                    'group' => '1',
                    'selling_mode' => '1',
                    'isPhoto' => '1',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
                [
                    'ID' => '1001',
                    'user_login' => 'momahony',
                    'user_pass' => '$P$B.WyBplgTFUa0qqB4KfgXkKltat0dK0',
                    'user_email' => '<EMAIL>',
                    'user_registered' => '2019-02-11 14:42:37',
                    'user_status' => '1',
                    'store' => '1000',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '301495',
                    'group' => '4',
                    'selling_mode' => '1',
                    'isPhoto' => '0',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
                [
                    'ID' => '1002',
                    'user_login' => 'leon',
                    'user_pass' => '$P$B.WyBplgTFUa0qqB4KfgXkKltat0dK0',
                    'user_email' => '<EMAIL>',
                    'user_registered' => '2019-02-11 14:42:37',
                    'user_status' => '1',
                    'store' => '1000',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '301496',
                    'group' => '4',
                    'selling_mode' => '1',
                    'isPhoto' => '0',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
            ],
            'sf_user_activity' => [
                [
                    'id' => '1000',
                    'type' => 'active session',
                    'source' => 'ppp on 2019-03-13',
                    'user_id' => '1000',
                    'start_date' => '2020-01-01 14:04:46',
                    'end_date' => '2020-03-01 23:59:59',
                ],
                [
                    'id' => '1001',
                    'type' => 'active session',
                    'source' => 'ppp on 2019-03-13',
                    'user_id' => '1001',
                    'start_date' => '2020-01-01 14:04:46',
                    'end_date' => '2020-03-01 23:59:59',
                ],
                [
                    'id' => '1002',
                    'type' => 'active session',
                    'source' => 'ppp on 2019-03-13',
                    'user_id' => '1002',
                    'start_date' => '2020-02-04 14:04:46',
                    'end_date' => '2020-02-04 23:59:59',
                ]
            ],
        ]);
    }

    protected function getElbStatsAndSidebarEvents()
    {
        return [
            'sf_sidebar_event_log' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => 'SIDEBAR_MOBILE_VIEW',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => 'SIDEBAR_MOBILE_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_MAXIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => 'SIDEBAR_DESKTOP_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_VIEW',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => 'SIDEBAR_DESKTOP_CLICK',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
            ],
            'sf_elb_stats' => [
                [
                    'date'                      => '2019-04-03',
                    'desktop_retailer_requests' => 1,
                    'mobile_retailer_requests'  => 2,
                    'desktop_sidebar_requests'  => 3,
                    'mobile_sidebar_requests'   => 3,
                    'desktop_clickthru_count'   => 3,
                    'mobile_clickthru_count'    => 3,
                ],
                [
                    'date'                      => '2019-04-04',
                    'desktop_retailer_requests' => 1,
                    'mobile_retailer_requests'  => 2,
                    'desktop_sidebar_requests'  => 3,
                    'mobile_sidebar_requests'   => 3,
                    'desktop_clickthru_count'   => 3,
                    'mobile_clickthru_count'    => 3,
                ],
            ],
        ];
    }

    protected function getContextualWidgetEventLog()
    {
        return [
            'sf_sidebar_event_log' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377285,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106693,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106694,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106695,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
            ],
        ];
    }
    protected function getContextualWidgetEventLogRotate()
    {
        return [
            'sf_sidebar_event_log_20240201' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377285,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106693,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106694,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106695,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
            ],
        ];
    }

    protected function getSidebarDailyStats()
    {
        return [
            'sf_sidebar_daily_stats' => [
                [
                    'date'                              => '2019-04-03',
                    'sidebar_mobile_total_view'         => 100,
                    'sidebar_mobile_total_click'        => 50,
                    'sidebar_mobile_total_minimize'     => 70,
                    'sidebar_mobile_total_maximize'     => 60,
                    'sidebar_desktop_total_view'        => 80,
                    'sidebar_desktop_total_click'       => 40,
                    'sidebar_desktop_total_minimize'    => 30,
                    'sidebar_mobile_unique_view'        => 40,
                    'sidebar_mobile_unique_click'       => 20,
                    'sidebar_mobile_unique_minimize'    => 20,
                    'sidebar_mobile_unique_maximize'    => 50,
                    'sidebar_desktop_unique_view'       => 20,
                    'sidebar_desktop_unique_click'      => 10,
                    'sidebar_desktop_unique_minimize'   => 12,

                ],
                [
                    'date'                              => '2019-04-04',
                    'sidebar_mobile_total_view'         => 120,
                    'sidebar_mobile_total_click'        => 70,
                    'sidebar_mobile_total_minimize'     => 90,
                    'sidebar_mobile_total_maximize'     => 80,
                    'sidebar_desktop_total_view'        => 100,
                    'sidebar_desktop_total_click'       => 60,
                    'sidebar_desktop_total_minimize'    => 50,
                    'sidebar_mobile_unique_view'        => 60,
                    'sidebar_mobile_unique_click'       => 40,
                    'sidebar_mobile_unique_minimize'    => 40,
                    'sidebar_mobile_unique_maximize'    => 70,
                    'sidebar_desktop_unique_view'       => 40,
                    'sidebar_desktop_unique_click'      => 30,
                    'sidebar_desktop_unique_minimize'   => 32,
                ],
            ],
        ];
    }

    public function processorSale()
    {
        Fixtures::add('processorSaleStoreIdNull', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => gmdate('Y-m-d 12:00:00'), // We need to force the time, because it's timezone based
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => gmdate('Y-m-d 22:00:00'), // We need to force the time, because it's timezone based
                    'trx_type'  => 'sale',
                    'trx_total' => 110.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789837',
                    'trx_date'  => gmdate('Y-m-d 03:00:00'), // We need to force the time, because it's timezone based
                    'trx_type'  => 'sale',
                    'trx_total' => 120.00,
                ],
            ]
        ]);
    }

    public function inactiveFilteringCest()
    {
        // I'm still questioning if this was needed for those tests.
        // I could just have 1 kpi and it would be enough but since it's there, I will keep it.

        // We should have a different seed to make it better, but I don't want to update the test cases.
        $kpis1 = $this->generateDailyStats(1);
        $kpis2 = $this->generateDailyStats(2);
        $kpis3 = $this->generateDailyStats(3);
        $kpis4 = $this->generateDailyStats(4);

        $store1111 = $this->mergeDailyStats($kpis1, $kpis2);
        $store1112 = $this->mergeDailyStats($kpis3, $kpis4);
        $store0 = $this->mergeDailyStats($store1111, $store1112);

        Fixtures::add('InactiveFilteringCest', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1111,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 9,
                    'user_login'          => 'testuser336',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser336',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 0,
                    'user_alias'          => null,
                    'display_name'        => 'testuser336',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1111,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 10,
                    'user_login'          => 'testuser337',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser337',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser337',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1112,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 11,
                    'user_login'          => 'testuser338',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser338',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 0,
                    'user_alias'          => null,
                    'display_name'        => 'testuser338',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1112,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ]
            ],
            'sf_store'        => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
            ],
            'sf_user_daily_stats' => [
                array_merge(
                    [
                        'user_id' => 8, // active user / active store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis1,
                ),
                array_merge(
                    [
                        'user_id' => 9, // inactive user / active store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis2,
                ),
                array_merge(
                    [
                        'user_id' => 10, // active user / inactive store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis3,
                ),
                array_merge(
                    [
                        'user_id' => 11, // inactive user / inactive store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis4,
                ),
            ],
            'sf_store_daily_stats' => [
                array_merge(
                    [
                        'store_id' => 0,
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'GLOBAL',
                    ],
                    $store0,
                ),
                array_merge(
                    [
                        'store_id' => 1111,
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $store1111,
                ),
                array_merge(
                    [
                        'store_id' => 1112,
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $store1112,
                ),
            ],
            'sf_rep_transaction' => [
                [
                    'user_id'             => 8,
                    'retailer_rep_id'     => '631648',
                    'trx_id'              => '103224195',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1100.0000,
                    'trx_total'           => 1100.0000,
                    'status'              => 1,
                    'store_id'            => 1111,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 9,
                    'retailer_rep_id'     => '631649',
                    'trx_id'              => '103224196',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1200.0000,
                    'trx_total'           => 1200.0000,
                    'status'              => 1,
                    'store_id'            => 1111,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 10,
                    'retailer_rep_id'     => '631650',
                    'trx_id'              => '103224197',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1300.0000,
                    'trx_total'           => 1300.0000,
                    'status'              => 1,
                    'store_id'            => 1112,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 11,
                    'retailer_rep_id'     => '631651',
                    'trx_id'              => '103224198',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1400.0000,
                    'trx_total'           => 1400.0000,
                    'status'              => 1,
                    'store_id'            => 1112,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
            ],
            'sf_rep_transaction_detail' => [
                [
                    'trx_id'                 => '103224195',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1100.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224196',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1200.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224197',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1300.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224198',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1400.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
            ]
        ]);
    }

    public function testFilteringGetSalesTotalStores()
    {
        // Do not valid from/to since those could change (at least the to if we do the period "all")
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"2300.0000"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesTotalStoresResponse', json_decode($response, true));
    }

    public function testFilteringGetSalesTotalStoresUsers()
    {
        // Do not valid from/to since those could change (at least the to if we do the period "all")
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"1100.0000"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesTotalStoresUsersResponse', json_decode($response, true));
    }

    public function testFilteringGetSalesTotalUsers()
    {
        // Do not valid from/to since those could change (at least the to if we do the period "all")
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"2400.0000"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesTotalUsersResponse', json_decode($response, true));
    }

    public function testFilteringGetSalesTotal()
    {
        // Do not valid from/to since those could change (at least the to if we do the period "all")
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_total_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales_total":[{"user_id":"8","tot_comm":"0.0000000000","total":"5000.0000"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesTotalResponse', json_decode($response, true));
    }

    public function testFilteringGetSalesStores()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"9","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224196","total":"1200.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"},{"user_id":"8","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesStoresResponse', json_decode($response, true));
    }

    public function testFilteringGetSalesStoresUsers()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"8","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesStoresUsersResponse', json_decode($response, true));
    }

    public function testFilteringGetSalesUsers()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"10","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224197","total":"1300.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1112","store_name":"Fake Mall Inactive","retailer_store_id":"fakemall"},{"user_id":"8","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesUsersResponse', json_decode($response, true));
    }

    public function testFilteringGetSales()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"sales_dates":{"backfill":{"needed":"FALSE","from":null,"to":null},"query":{"needed":"TRUE"}},"sales":[{"user_id":"11","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224198","total":"1400.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1112","store_name":"Fake Mall Inactive","retailer_store_id":"fakemall"},{"user_id":"10","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224197","total":"1300.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1112","store_name":"Fake Mall Inactive","retailer_store_id":"fakemall"},{"user_id":"9","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224196","total":"1200.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"},{"user_id":"8","received_date":"Jul 20, 2023 00:00:00 (UTC-04:00)","utcdate":"2023-07-20 04:00:00","id":"103224195","total":"1100.0000","apply":"0.0000000000","status":"1","trx_type":"sale","origin":"tests","acquisition":"","attribution":null,"customer_name":null,"customer_email":null,"customer_id":null,"store_id":"1111","store_name":"Fake Mall","retailer_store_id":"fakemall"}]}}}}
DATA;

        Fixtures::add('testFilteringGetSalesResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingStores()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":3,"onboarding_end":5,"user_add":7,"chat_request":9,"chat_answer":11,"response_mail_sent":13,"mail_sent":15,"email_stats_open":17,"email_stats_click":19,"avg_init_resp_sum_times":21,"avg_init_resp_num_responses":23,"chat_answer_rate_requests":25,"chat_answer_rate_answers":27,"total_order_value":29,"n_sales_transactions":31,"feedback":33,"profile_update":35,"content_create":37,"content_update":39,"content_curate":41,"ask_question_req":43,"personal_shopper_req":45,"live_session_start":47,"live_session_end":49,"product_update":51,"deal_update":53,"unsubscribe":55,"new_sale":57,"subscribe":59,"retail_event":61,"raise_concern":63,"shopping_page":65,"unique_visitor":67,"page_hit":69,"sale_duplicate":71,"user_visit":73,"com_ref":75,"soc_ref":77,"retail_hit":79,"social_post":81,"soc_share":83,"moderate_lead":85,"customer_card":87,"help_useful":89,"livesession_register":91,"change_categories":93,"chatsession_register":95,"event_create":97,"event_update":99,"event_delete":101,"event_subscribe":103,"sidebar_view":105,"sidebar_click":107,"footer_view":109,"footer_click":111,"storefront_click":113,"transactional_mail_sent":115,"courtesy_mail_sent":117,"service_total":119,"traffic_total":121,"content_total":123,"number_seconds_available":125,"total_return_value":127,"recommendation_chat":129,"recommendation_compose_message":131,"recommendation_share_email":133,"recommendation_share_facebook":135,"recommendation_share_twitter":137,"recommendation_new_arrivals":139,"recommendation_top_picks":141,"click_top_picks":143,"click_latest_arrivals":145,"click_recommended":147,"avg_selected_top_picks":149,"avg_selected_new_arrivals":151,"salesfloor_visits":153,"text_messages_outbound_api":155,"text_messages_outbound_call":157,"text_messages_outbound_reply":159,"text_messages_inbound":161,"recommendation_text_message":163,"tasks_automated_created":165,"tasks_automated_resolved":167,"tasks_automated_dismissed":169,"tasks_manual_created":171,"tasks_manual_resolved":173,"tasks_manual_dismissed":175,"chat_abandoned":177,"chat_abandonment_time":179,"chat_answer_time":181,"chat_early_redirect":183,"chat_auto_redirect":185,"ask_question_req_email":187,"ask_question_req_text":189,"appointment_req_email":191,"appointment_req_text":193,"personal_shopper_req_email":195,"personal_shopper_req_text":197,"library_share_attempts":199,"lookbook_create":201,"lookbook_update":203,"received_chats_answered_by_other":205,"request_email_sent":207,"request_email_open":209,"request_email_click":211,"compose_email_sent":213,"compose_email_open":215,"compose_email_click":217,"share_email_sent":219,"share_email_open":221,"share_email_click":223,"total_share_sent":225,"chat_abandon_0_29":227,"chat_abandon_30_59":229,"chat_abandon_60_89":231,"chat_abandon_90_120":233,"ask_question_req_chat_handoff":235,"tasks_system_created":237,"tasks_system_resolved":239,"tasks_system_dismissed":241,"tasks_followup_created":243,"tasks_followup_resolved":245,"tasks_followup_dismissed":247,"tasks_corporate_created":249,"tasks_corporate_resolved":251,"tasks_corporate_dismissed":253,"tasks_manual_resolved_sum_time":255,"tasks_manual_dismissed_sum_time":257,"tasks_automated_resolved_sum_time":259,"tasks_automated_dismissed_sum_time":261,"tasks_system_resolved_sum_time":263,"tasks_system_dismissed_sum_time":265,"tasks_followup_resolved_sum_time":267,"tasks_followup_dismissed_sum_time":269,"tasks_corporate_resolved_sum_time":271,"tasks_corporate_dismissed_sum_time":273,"socialshop_post_created":275,"socialshop_total_visit":277,"socialshop_product_click":279,"socialshop_storefront_click":281,"socialshop_sales_count":283,"socialshop_sales_amount_total":285,"tasks_corporate_deleted":287,"ask_question_req_cs_email":289,"ask_question_req_cs_text":291,"appointment_req_cs_email":293,"appointment_req_cs_text":295,"personal_shopper_req_cs_email":297,"personal_shopper_req_cs_text":299,"scheduled_appointments":301,"cancelled_appointment":303,"video_chat_sessions":305,"video_chat_duration":307,"virtual_appointment_sessions":309,"virtual_appointment_duration":311,"avg_init_resp":"-","avg_init_resp_raw":0.9130434782608695,"avg_order_value":0.9354838709677419,"mail_click":1.2666666666666666,"mail_open":1.1333333333333333,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4739884393063585,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4685714285714286,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5508982035928143,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.544378698224852,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.100418410041841,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.099585062240664,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.089795918367347,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0890688259109311,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0796812749003983,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0790513833992095,"text_messages_sent":471,"text_messages_received":161,"chat_answer_rate":3,"chat_missed":-2,"chat_answer_rate_new":0,"chat_number_minutes_available":2,"chat_avg_answer_time":16.454545454545453,"chat_avg_availability":0,"net_sales":156,"n_recommendations":"1108","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":1152,"service_cs_total":1764}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingStoresResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingStoresUsers()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"chat_answer_rate":3.25,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"text_messages_sent":234,"text_messages_received":80,"service_exclude_live_chat_total":573,"service_cs_total":879}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingStoresUsersResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingUsers()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":4,"onboarding_end":6,"user_add":8,"chat_request":10,"chat_answer":12,"response_mail_sent":14,"mail_sent":16,"email_stats_open":18,"email_stats_click":20,"avg_init_resp_sum_times":22,"avg_init_resp_num_responses":24,"chat_answer_rate_requests":26,"chat_answer_rate_answers":28,"total_order_value":30,"n_sales_transactions":32,"feedback":34,"profile_update":36,"content_create":38,"content_update":40,"content_curate":42,"ask_question_req":44,"personal_shopper_req":46,"live_session_start":48,"live_session_end":50,"product_update":52,"deal_update":54,"unsubscribe":56,"new_sale":58,"subscribe":60,"retail_event":62,"raise_concern":64,"shopping_page":66,"unique_visitor":68,"page_hit":70,"sale_duplicate":72,"user_visit":74,"com_ref":76,"soc_ref":78,"retail_hit":80,"social_post":82,"soc_share":84,"moderate_lead":86,"customer_card":88,"help_useful":90,"livesession_register":92,"change_categories":94,"chatsession_register":96,"event_create":98,"event_update":100,"event_delete":102,"event_subscribe":104,"sidebar_view":106,"sidebar_click":108,"footer_view":110,"footer_click":112,"storefront_click":114,"transactional_mail_sent":116,"courtesy_mail_sent":118,"service_total":120,"traffic_total":122,"content_total":124,"number_seconds_available":126,"total_return_value":128,"recommendation_chat":130,"recommendation_compose_message":132,"recommendation_share_email":134,"recommendation_share_facebook":136,"recommendation_share_twitter":138,"recommendation_new_arrivals":140,"recommendation_top_picks":142,"click_top_picks":144,"click_latest_arrivals":146,"click_recommended":148,"avg_selected_top_picks":75,"avg_selected_new_arrivals":76,"salesfloor_visits":154,"text_messages_outbound_api":156,"text_messages_outbound_call":158,"text_messages_outbound_reply":160,"text_messages_inbound":162,"recommendation_text_message":164,"tasks_automated_created":166,"tasks_automated_resolved":168,"tasks_automated_dismissed":170,"tasks_manual_created":172,"tasks_manual_resolved":174,"tasks_manual_dismissed":176,"chat_abandoned":178,"chat_abandonment_time":180,"chat_answer_time":182,"chat_early_redirect":184,"chat_auto_redirect":186,"ask_question_req_email":188,"ask_question_req_text":190,"appointment_req_email":192,"appointment_req_text":194,"personal_shopper_req_email":196,"personal_shopper_req_text":198,"library_share_attempts":200,"lookbook_create":202,"lookbook_update":204,"received_chats_answered_by_other":206,"request_email_sent":208,"request_email_open":210,"request_email_click":212,"compose_email_sent":214,"compose_email_open":216,"compose_email_click":218,"share_email_sent":220,"share_email_open":222,"share_email_click":224,"total_share_sent":226,"chat_abandon_0_29":228,"chat_abandon_30_59":230,"chat_abandon_60_89":232,"chat_abandon_90_120":234,"ask_question_req_chat_handoff":236,"tasks_system_created":238,"tasks_system_resolved":240,"tasks_system_dismissed":242,"tasks_followup_created":244,"tasks_followup_resolved":246,"tasks_followup_dismissed":248,"tasks_corporate_created":250,"tasks_corporate_resolved":252,"tasks_corporate_dismissed":254,"tasks_manual_resolved_sum_time":256,"tasks_manual_dismissed_sum_time":258,"tasks_automated_resolved_sum_time":260,"tasks_automated_dismissed_sum_time":262,"tasks_system_resolved_sum_time":264,"tasks_system_dismissed_sum_time":266,"tasks_followup_resolved_sum_time":268,"tasks_followup_dismissed_sum_time":270,"tasks_corporate_resolved_sum_time":272,"tasks_corporate_dismissed_sum_time":274,"socialshop_post_created":276,"socialshop_total_visit":278,"socialshop_product_click":280,"socialshop_storefront_click":282,"socialshop_sales_count":284,"socialshop_sales_amount_total":286,"tasks_corporate_deleted":288,"ask_question_req_cs_email":290,"ask_question_req_cs_text":292,"appointment_req_cs_email":294,"appointment_req_cs_text":296,"personal_shopper_req_cs_email":298,"personal_shopper_req_cs_text":300,"scheduled_appointments":302,"cancelled_appointment":304,"video_chat_sessions":306,"video_chat_duration":308,"virtual_appointment_sessions":310,"virtual_appointment_duration":312,"avg_init_resp":"-","avg_init_resp_raw":0.9166666666666666,"avg_order_value":0.9375,"mail_click":1.25,"mail_open":1.125,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.471264367816092,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4659090909090908,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5476190476190477,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5411764705882354,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.0991735537190082,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.089430894308943,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0887096774193548,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0793650793650793,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.078740157480315,"text_messages_sent":474,"text_messages_received":162,"chat_answer_rate":2.8,"chat_missed":-2,"chat_answer_rate_new":0,"chat_number_minutes_available":2,"chat_avg_answer_time":15.166666666666666,"chat_avg_availability":0,"net_sales":158,"n_recommendations":"1116","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":1158,"service_cs_total":1770}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingUsersResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketing()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":10,"onboarding_end":14,"user_add":18,"chat_request":22,"chat_answer":26,"response_mail_sent":30,"mail_sent":34,"email_stats_open":38,"email_stats_click":42,"avg_init_resp_sum_times":46,"avg_init_resp_num_responses":50,"chat_answer_rate_requests":54,"chat_answer_rate_answers":58,"total_order_value":62,"n_sales_transactions":66,"feedback":70,"profile_update":74,"content_create":78,"content_update":82,"content_curate":86,"ask_question_req":90,"personal_shopper_req":94,"live_session_start":98,"live_session_end":102,"product_update":106,"deal_update":110,"unsubscribe":114,"new_sale":118,"subscribe":122,"retail_event":126,"raise_concern":130,"shopping_page":134,"unique_visitor":138,"page_hit":142,"sale_duplicate":146,"user_visit":150,"com_ref":154,"soc_ref":158,"retail_hit":162,"social_post":166,"soc_share":170,"moderate_lead":174,"customer_card":178,"help_useful":182,"livesession_register":186,"change_categories":190,"chatsession_register":194,"event_create":198,"event_update":202,"event_delete":206,"event_subscribe":210,"sidebar_view":214,"sidebar_click":218,"footer_view":222,"footer_click":226,"storefront_click":230,"transactional_mail_sent":234,"courtesy_mail_sent":238,"service_total":242,"traffic_total":246,"content_total":250,"number_seconds_available":254,"total_return_value":258,"recommendation_chat":262,"recommendation_compose_message":266,"recommendation_share_email":270,"recommendation_share_facebook":274,"recommendation_share_twitter":278,"recommendation_new_arrivals":282,"recommendation_top_picks":286,"click_top_picks":290,"click_latest_arrivals":294,"click_recommended":298,"avg_selected_top_picks":302,"avg_selected_new_arrivals":306,"salesfloor_visits":310,"text_messages_outbound_api":314,"text_messages_outbound_call":318,"text_messages_outbound_reply":322,"text_messages_inbound":326,"recommendation_text_message":330,"tasks_automated_created":334,"tasks_automated_resolved":338,"tasks_automated_dismissed":342,"tasks_manual_created":346,"tasks_manual_resolved":350,"tasks_manual_dismissed":354,"chat_abandoned":358,"chat_abandonment_time":362,"chat_answer_time":366,"chat_early_redirect":370,"chat_auto_redirect":374,"ask_question_req_email":378,"ask_question_req_text":382,"appointment_req_email":386,"appointment_req_text":390,"personal_shopper_req_email":394,"personal_shopper_req_text":398,"library_share_attempts":402,"lookbook_create":406,"lookbook_update":410,"received_chats_answered_by_other":414,"request_email_sent":418,"request_email_open":422,"request_email_click":426,"compose_email_sent":430,"compose_email_open":434,"compose_email_click":438,"share_email_sent":442,"share_email_open":446,"share_email_click":450,"total_share_sent":454,"chat_abandon_0_29":458,"chat_abandon_30_59":462,"chat_abandon_60_89":466,"chat_abandon_90_120":470,"ask_question_req_chat_handoff":474,"tasks_system_created":478,"tasks_system_resolved":482,"tasks_system_dismissed":486,"tasks_followup_created":490,"tasks_followup_resolved":494,"tasks_followup_dismissed":498,"tasks_corporate_created":502,"tasks_corporate_resolved":506,"tasks_corporate_dismissed":510,"tasks_manual_resolved_sum_time":514,"tasks_manual_dismissed_sum_time":518,"tasks_automated_resolved_sum_time":522,"tasks_automated_dismissed_sum_time":526,"tasks_system_resolved_sum_time":530,"tasks_system_dismissed_sum_time":534,"tasks_followup_resolved_sum_time":538,"tasks_followup_dismissed_sum_time":542,"tasks_corporate_resolved_sum_time":546,"tasks_corporate_dismissed_sum_time":550,"socialshop_post_created":554,"socialshop_total_visit":558,"socialshop_product_click":562,"socialshop_storefront_click":566,"socialshop_sales_count":570,"socialshop_sales_amount_total":574,"tasks_corporate_deleted":578,"ask_question_req_cs_email":582,"ask_question_req_cs_text":586,"appointment_req_cs_email":590,"appointment_req_cs_text":594,"personal_shopper_req_cs_email":598,"personal_shopper_req_cs_text":602,"scheduled_appointments":606,"cancelled_appointment":610,"video_chat_sessions":614,"video_chat_duration":618,"virtual_appointment_sessions":622,"virtual_appointment_duration":626,"avg_init_resp":"-","avg_init_resp_raw":0.92,"avg_order_value":0.9393939393939394,"mail_click":1.2352941176470589,"mail_open":1.1176470588235294,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4685714285714286,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.463276836158192,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.544378698224852,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5380116959064327,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.099585062240664,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.0987654320987654,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0890688259109311,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0883534136546185,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0790513833992095,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0784313725490196,"text_messages_sent":954,"text_messages_received":326,"chat_answer_rate":2.6363636363636362,"chat_missed":-4,"chat_answer_rate_new":0,"chat_number_minutes_available":4,"chat_avg_answer_time":14.076923076923077,"chat_avg_availability":0,"net_sales":320,"n_recommendations":"2248","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":2328,"service_cs_total":3552}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithUsersSelectedEnabled()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"text_messages_sent":234,"text_messages_received":80,"chat_answer_rate":3.25,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":18,"chat_avg_availability":0,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":573,"service_cs_total":879}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingUsersWithUsersSelectedEnabledResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithUsersSelectedDisabled()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingUsersWithUsersSelectedDisabledResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithStoreSelectedEnabled()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"text_messages_sent":234,"text_messages_received":80,"chat_answer_rate":3.25,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":18,"chat_avg_availability":0,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":573,"service_cs_total":879}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingUsersWithStoreSelectedEnabledResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithStoreSelectedDisabled()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":3,"onboarding_end":4,"user_add":5,"chat_request":6,"chat_answer":7,"response_mail_sent":8,"mail_sent":9,"email_stats_open":10,"email_stats_click":11,"avg_init_resp_sum_times":12,"avg_init_resp_num_responses":13,"chat_answer_rate_requests":14,"chat_answer_rate_answers":15,"total_order_value":16,"n_sales_transactions":17,"feedback":18,"profile_update":19,"content_create":20,"content_update":21,"content_curate":22,"ask_question_req":23,"personal_shopper_req":24,"live_session_start":25,"live_session_end":26,"product_update":27,"deal_update":28,"unsubscribe":29,"new_sale":30,"subscribe":31,"retail_event":32,"raise_concern":33,"shopping_page":34,"unique_visitor":35,"page_hit":36,"sale_duplicate":37,"user_visit":38,"com_ref":39,"soc_ref":40,"retail_hit":41,"social_post":42,"soc_share":43,"moderate_lead":44,"customer_card":45,"help_useful":46,"livesession_register":47,"change_categories":48,"chatsession_register":49,"event_create":50,"event_update":51,"event_delete":52,"event_subscribe":53,"sidebar_view":54,"sidebar_click":55,"footer_view":56,"footer_click":57,"storefront_click":58,"transactional_mail_sent":59,"courtesy_mail_sent":60,"service_total":61,"traffic_total":62,"content_total":63,"number_seconds_available":64,"total_return_value":65,"recommendation_chat":66,"recommendation_compose_message":67,"recommendation_share_email":68,"recommendation_share_facebook":69,"recommendation_share_twitter":70,"recommendation_new_arrivals":71,"recommendation_top_picks":72,"click_top_picks":73,"click_latest_arrivals":74,"click_recommended":75,"avg_selected_top_picks":76,"avg_selected_new_arrivals":77,"salesfloor_visits":78,"text_messages_outbound_api":79,"text_messages_outbound_call":80,"text_messages_outbound_reply":81,"text_messages_inbound":82,"recommendation_text_message":83,"tasks_automated_created":84,"tasks_automated_resolved":85,"tasks_automated_dismissed":86,"tasks_manual_created":87,"tasks_manual_resolved":88,"tasks_manual_dismissed":89,"chat_abandoned":90,"chat_abandonment_time":91,"chat_answer_time":92,"chat_early_redirect":93,"chat_auto_redirect":94,"ask_question_req_email":95,"ask_question_req_text":96,"appointment_req_email":97,"appointment_req_text":98,"personal_shopper_req_email":99,"personal_shopper_req_text":100,"library_share_attempts":101,"lookbook_create":102,"lookbook_update":103,"received_chats_answered_by_other":104,"request_email_sent":105,"request_email_open":106,"request_email_click":107,"compose_email_sent":108,"compose_email_open":109,"compose_email_click":110,"share_email_sent":111,"share_email_open":112,"share_email_click":113,"total_share_sent":114,"chat_abandon_0_29":115,"chat_abandon_30_59":116,"chat_abandon_60_89":117,"chat_abandon_90_120":118,"ask_question_req_chat_handoff":119,"tasks_system_created":120,"tasks_system_resolved":121,"tasks_system_dismissed":122,"tasks_followup_created":123,"tasks_followup_resolved":124,"tasks_followup_dismissed":125,"tasks_corporate_created":126,"tasks_corporate_resolved":127,"tasks_corporate_dismissed":128,"tasks_manual_resolved_sum_time":129,"tasks_manual_dismissed_sum_time":130,"tasks_automated_resolved_sum_time":131,"tasks_automated_dismissed_sum_time":132,"tasks_system_resolved_sum_time":133,"tasks_system_dismissed_sum_time":134,"tasks_followup_resolved_sum_time":135,"tasks_followup_dismissed_sum_time":136,"tasks_corporate_resolved_sum_time":137,"tasks_corporate_dismissed_sum_time":138,"socialshop_post_created":139,"socialshop_total_visit":140,"socialshop_product_click":141,"socialshop_storefront_click":142,"socialshop_sales_count":143,"socialshop_sales_amount_total":144,"tasks_corporate_deleted":145,"ask_question_req_cs_email":146,"ask_question_req_cs_text":147,"appointment_req_cs_email":148,"appointment_req_cs_text":149,"personal_shopper_req_cs_email":150,"personal_shopper_req_cs_text":151,"scheduled_appointments":152,"cancelled_appointment":153,"video_chat_sessions":154,"video_chat_duration":155,"virtual_appointment_sessions":156,"virtual_appointment_duration":157,"avg_init_resp":"-","avg_init_resp_raw":0.9230769230769231,"avg_order_value":0.9411764705882353,"mail_click":1.2222222222222223,"mail_open":1.1111111111111112,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4659090909090908,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4606741573033708,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5411764705882354,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5348837209302326,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.0991735537190082,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.098360655737705,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0887096774193548,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.088,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.078740157480315,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.078125,"text_messages_sent":240,"text_messages_received":82,"chat_answer_rate":2.5,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":13.142857142857142,"chat_avg_availability":0,"net_sales":81,"n_recommendations":"566","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":585,"service_cs_total":891}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingUsersWithStoreSelectedDisabledResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserEnabledFromEnabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":1,"onboarding_end":2,"user_add":3,"chat_request":4,"chat_answer":5,"response_mail_sent":6,"mail_sent":7,"email_stats_open":8,"email_stats_click":9,"avg_init_resp_sum_times":10,"avg_init_resp_num_responses":11,"chat_answer_rate_requests":12,"chat_answer_rate_answers":13,"total_order_value":14,"n_sales_transactions":15,"feedback":16,"profile_update":17,"content_create":18,"content_update":19,"content_curate":20,"ask_question_req":21,"personal_shopper_req":22,"live_session_start":23,"live_session_end":24,"product_update":25,"deal_update":26,"unsubscribe":27,"new_sale":28,"subscribe":29,"retail_event":30,"raise_concern":31,"shopping_page":32,"unique_visitor":33,"page_hit":34,"sale_duplicate":35,"user_visit":36,"com_ref":37,"soc_ref":38,"retail_hit":39,"social_post":40,"soc_share":41,"moderate_lead":42,"customer_card":43,"help_useful":44,"livesession_register":45,"change_categories":46,"chatsession_register":47,"event_create":48,"event_update":49,"event_delete":50,"event_subscribe":51,"sidebar_view":52,"sidebar_click":53,"footer_view":54,"footer_click":55,"storefront_click":56,"transactional_mail_sent":57,"courtesy_mail_sent":58,"service_total":59,"traffic_total":60,"content_total":61,"number_seconds_available":62,"total_return_value":63,"recommendation_chat":64,"recommendation_compose_message":65,"recommendation_share_email":66,"recommendation_share_facebook":67,"recommendation_share_twitter":68,"recommendation_new_arrivals":69,"recommendation_top_picks":70,"click_top_picks":71,"click_latest_arrivals":72,"click_recommended":73,"avg_selected_top_picks":74,"avg_selected_new_arrivals":75,"salesfloor_visits":76,"text_messages_outbound_api":77,"text_messages_outbound_call":78,"text_messages_outbound_reply":79,"text_messages_inbound":80,"recommendation_text_message":81,"tasks_automated_created":82,"tasks_automated_resolved":83,"tasks_automated_dismissed":84,"tasks_manual_created":85,"tasks_manual_resolved":86,"tasks_manual_dismissed":87,"chat_abandoned":88,"chat_abandonment_time":89,"chat_answer_time":90,"chat_early_redirect":91,"chat_auto_redirect":92,"ask_question_req_email":93,"ask_question_req_text":94,"appointment_req_email":95,"appointment_req_text":96,"personal_shopper_req_email":97,"personal_shopper_req_text":98,"library_share_attempts":99,"lookbook_create":100,"lookbook_update":101,"received_chats_answered_by_other":102,"request_email_sent":103,"request_email_open":104,"request_email_click":105,"compose_email_sent":106,"compose_email_open":107,"compose_email_click":108,"share_email_sent":109,"share_email_open":110,"share_email_click":111,"total_share_sent":112,"chat_abandon_0_29":113,"chat_abandon_30_59":114,"chat_abandon_60_89":115,"chat_abandon_90_120":116,"ask_question_req_chat_handoff":117,"tasks_system_created":118,"tasks_system_resolved":119,"tasks_system_dismissed":120,"tasks_followup_created":121,"tasks_followup_resolved":122,"tasks_followup_dismissed":123,"tasks_corporate_created":124,"tasks_corporate_resolved":125,"tasks_corporate_dismissed":126,"tasks_manual_resolved_sum_time":127,"tasks_manual_dismissed_sum_time":128,"tasks_automated_resolved_sum_time":129,"tasks_automated_dismissed_sum_time":130,"tasks_system_resolved_sum_time":131,"tasks_system_dismissed_sum_time":132,"tasks_followup_resolved_sum_time":133,"tasks_followup_dismissed_sum_time":134,"tasks_corporate_resolved_sum_time":135,"tasks_corporate_dismissed_sum_time":136,"socialshop_post_created":137,"socialshop_total_visit":138,"socialshop_product_click":139,"socialshop_storefront_click":140,"socialshop_sales_count":141,"socialshop_sales_amount_total":142,"tasks_corporate_deleted":143,"ask_question_req_cs_email":144,"ask_question_req_cs_text":145,"appointment_req_cs_email":146,"appointment_req_cs_text":147,"personal_shopper_req_cs_email":148,"personal_shopper_req_cs_text":149,"scheduled_appointments":150,"cancelled_appointment":151,"video_chat_sessions":152,"video_chat_duration":153,"virtual_appointment_sessions":154,"virtual_appointment_duration":155,"avg_init_resp":"-","avg_init_resp_raw":0.9090909090909091,"avg_order_value":0.9333333333333333,"mail_click":1.2857142857142858,"mail_open":1.1428571428571428,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.4767441860465116,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.471264367816092,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5542168674698795,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5476190476190477,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1008403361344539,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.1,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.0901639344262295,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.089430894308943,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.08,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.0793650793650793,"text_messages_sent":234,"text_messages_received":80,"chat_answer_rate":3.25,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":18,"chat_avg_availability":0,"net_sales":77,"n_recommendations":"550","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":573,"service_cs_total":879}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingStoresWithUserEnabledFromEnabledStoreResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserDisabledFromEnabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":2,"onboarding_end":3,"user_add":4,"chat_request":5,"chat_answer":6,"response_mail_sent":7,"mail_sent":8,"email_stats_open":9,"email_stats_click":10,"avg_init_resp_sum_times":11,"avg_init_resp_num_responses":12,"chat_answer_rate_requests":13,"chat_answer_rate_answers":14,"total_order_value":15,"n_sales_transactions":16,"feedback":17,"profile_update":18,"content_create":19,"content_update":20,"content_curate":21,"ask_question_req":22,"personal_shopper_req":23,"live_session_start":24,"live_session_end":25,"product_update":26,"deal_update":27,"unsubscribe":28,"new_sale":29,"subscribe":30,"retail_event":31,"raise_concern":32,"shopping_page":33,"unique_visitor":34,"page_hit":35,"sale_duplicate":36,"user_visit":37,"com_ref":38,"soc_ref":39,"retail_hit":40,"social_post":41,"soc_share":42,"moderate_lead":43,"customer_card":44,"help_useful":45,"livesession_register":46,"change_categories":47,"chatsession_register":48,"event_create":49,"event_update":50,"event_delete":51,"event_subscribe":52,"sidebar_view":53,"sidebar_click":54,"footer_view":55,"footer_click":56,"storefront_click":57,"transactional_mail_sent":58,"courtesy_mail_sent":59,"service_total":60,"traffic_total":61,"content_total":62,"number_seconds_available":63,"total_return_value":64,"recommendation_chat":65,"recommendation_compose_message":66,"recommendation_share_email":67,"recommendation_share_facebook":68,"recommendation_share_twitter":69,"recommendation_new_arrivals":70,"recommendation_top_picks":71,"click_top_picks":72,"click_latest_arrivals":73,"click_recommended":74,"avg_selected_top_picks":75,"avg_selected_new_arrivals":76,"salesfloor_visits":77,"text_messages_outbound_api":78,"text_messages_outbound_call":79,"text_messages_outbound_reply":80,"text_messages_inbound":81,"recommendation_text_message":82,"tasks_automated_created":83,"tasks_automated_resolved":84,"tasks_automated_dismissed":85,"tasks_manual_created":86,"tasks_manual_resolved":87,"tasks_manual_dismissed":88,"chat_abandoned":89,"chat_abandonment_time":90,"chat_answer_time":91,"chat_early_redirect":92,"chat_auto_redirect":93,"ask_question_req_email":94,"ask_question_req_text":95,"appointment_req_email":96,"appointment_req_text":97,"personal_shopper_req_email":98,"personal_shopper_req_text":99,"library_share_attempts":100,"lookbook_create":101,"lookbook_update":102,"received_chats_answered_by_other":103,"request_email_sent":104,"request_email_open":105,"request_email_click":106,"compose_email_sent":107,"compose_email_open":108,"compose_email_click":109,"share_email_sent":110,"share_email_open":111,"share_email_click":112,"total_share_sent":113,"chat_abandon_0_29":114,"chat_abandon_30_59":115,"chat_abandon_60_89":116,"chat_abandon_90_120":117,"ask_question_req_chat_handoff":118,"tasks_system_created":119,"tasks_system_resolved":120,"tasks_system_dismissed":121,"tasks_followup_created":122,"tasks_followup_resolved":123,"tasks_followup_dismissed":124,"tasks_corporate_created":125,"tasks_corporate_resolved":126,"tasks_corporate_dismissed":127,"tasks_manual_resolved_sum_time":128,"tasks_manual_dismissed_sum_time":129,"tasks_automated_resolved_sum_time":130,"tasks_automated_dismissed_sum_time":131,"tasks_system_resolved_sum_time":132,"tasks_system_dismissed_sum_time":133,"tasks_followup_resolved_sum_time":134,"tasks_followup_dismissed_sum_time":135,"tasks_corporate_resolved_sum_time":136,"tasks_corporate_dismissed_sum_time":137,"socialshop_post_created":138,"socialshop_total_visit":139,"socialshop_product_click":140,"socialshop_storefront_click":141,"socialshop_sales_count":142,"socialshop_sales_amount_total":143,"tasks_corporate_deleted":144,"ask_question_req_cs_email":145,"ask_question_req_cs_text":146,"appointment_req_cs_email":147,"appointment_req_cs_text":148,"personal_shopper_req_cs_email":149,"personal_shopper_req_cs_text":150,"scheduled_appointments":151,"cancelled_appointment":152,"video_chat_sessions":153,"video_chat_duration":154,"virtual_appointment_sessions":155,"virtual_appointment_duration":156,"avg_init_resp":"-","avg_init_resp_raw":0.9166666666666666,"avg_order_value":0.9375,"mail_click":1.25,"mail_open":1.125,"tasks_manual_resolved_avg_time":"< 1m","tasks_manual_resolved_avg_time_raw":1.471264367816092,"tasks_manual_dismissed_avg_time":"< 1m","tasks_manual_dismissed_avg_time_raw":1.4659090909090908,"tasks_automated_resolved_avg_time":"< 1m","tasks_automated_resolved_avg_time_raw":1.5476190476190477,"tasks_automated_dismissed_avg_time":"< 1m","tasks_automated_dismissed_avg_time_raw":1.5411764705882354,"tasks_system_resolved_avg_time":"< 1m","tasks_system_resolved_avg_time_raw":1.1,"tasks_system_dismissed_avg_time":"< 1m","tasks_system_dismissed_avg_time_raw":1.0991735537190082,"tasks_followup_resolved_avg_time":"< 1m","tasks_followup_resolved_avg_time_raw":1.089430894308943,"tasks_followup_dismissed_avg_time":"< 1m","tasks_followup_dismissed_avg_time_raw":1.0887096774193548,"tasks_corporate_resolved_avg_time":"< 1m","tasks_corporate_resolved_avg_time_raw":1.0793650793650793,"tasks_corporate_dismissed_avg_time":"< 1m","tasks_corporate_dismissed_avg_time_raw":1.078740157480315,"text_messages_sent":237,"text_messages_received":81,"chat_answer_rate":2.8,"chat_missed":-1,"chat_answer_rate_new":0,"chat_number_minutes_available":1,"chat_avg_answer_time":15.166666666666666,"chat_avg_availability":0,"net_sales":79,"n_recommendations":"558","click_top_picks_rate":1,"click_latest_arrivals_rate":1,"click_recommended_rate":1,"service_exclude_live_chat_total":579,"service_cs_total":885}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingStoresWithUserDisabledFromEnabledStoreResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserEnabledFromDisabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingStoresWithUserEnabledFromDisabledStoreResponse', json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserDisabledFromDisabledStore()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testFilteringGetMarketingExcludingStoresWithUserDisabledFromDisabledStoreResponse', json_decode($response, true));
    }

    public function testExportCsvDownloadAllActivitySummaryAssociatesWithData()
    {
        Fixtures::add('testExportCsvDownloadAllActivitySummaryAssociatesWithData', [
            'sf_user_daily_stats' => [
                [
                    'user_id'   => 1,
                    'date'    => gmdate("Y-m-d"),
                    'timezone'  => 'America/Montreal',
                    'chat_request' => 22, // Legacy
                    'chat_answer' => 11, // Legacy
                    'chat_answer_rate_requests' => 22,
                    'chat_answer_rate_answers' => 11,
                ],
            ]
        ]);
    }

    public function testExportCsvDownloadAllActivitySummaryStoresWithData()
    {
        Fixtures::add('testExportCsvDownloadAllActivitySummaryStoresWithData', [
            'sf_store_daily_stats' => [
               array_merge($this->generateDailyStatsFixValue(0), [
                    'store_id'   => 1003,
                    'date'    => gmdate("Y-m-d"),
                    'timezone'  => 'America/Montreal',
                    'chat_request' => 22, // Legacy
                    'chat_answer' => 11, // Legacy
                    'chat_answer_rate_requests' => 22,
                    'chat_answer_rate_answers' => 11,
                ]),
            ]
            ]);
    }

    public function requestReplyTime()
    {
        Fixtures::add('requestReplyTime', [
            'sf_appointments' => [
                [
                    'ID' => 1,
                    'customer_id'       => 1,
                    'user_id'           => self::REGGIE_ID,
                    'event_type'        => 'In-Store',
                    'event_duration'    => 60,
                    'date'              => '2023-01-01 01:00:00',
                    'status'            => 'accepted',
                    'location'          => '',
                    'notes'             => '',
                    'uniq_id'           => 'SFID5942ee9e8f16f5.00220543',
                    'timezone'          => 'America/New_York',
                    'creation'          => Carbon::now()->setTime(14, 0)->toDateTimeString(),
                    'cust_meeting_link' => null,
                    'rep_meeting_link'  => null,
                    'enddate'           => '2023-01-01 02:00:00',
                    'rep_comment'       => null,
                    'phone'             => '******-508-2007',
                    'store_id'          => 1003,
                    'loc_id'            => null,
                    'category_id'       => null,
                    'sub_category_id'   => null,
                    'flagged'           => null,
                    'unattached_id'     => null,
                    'source_url'        => null,
                    'source_title'      => null,
                    'source'            => 'sidebar',
                    'updated_at'        => '2023-01-01 21:31:00',
                ],
            ],
            'sf_text_thread' => [
                [
                    'id' => '162967',
                    'user_id' => self::REGGIE_ID,
                    'user_phone_number' => '+12896778026',
                    'customer_id' => '3083442',
                    'customer_phone_number' => '+16134510269',
                    'is_read' => '1',
                    'is_valid' => '1',
                    'is_active' => '1',
                    'is_subscribed' => '1',
                    'created_at' => '2023-01-10 21:38:04',
                    'updated_at' => '2023-05-17 18:38:07',
                ],
            ],
            'sf_text_message' => [
                [
                    'id' => '1577023',
                    'text_thread_id' => '162967',
                    'provider_message_id' => 'SM771c40cbe6d34ada4e2ccf0013',
                    'direction' => 'inbound-service',
                    'body' => 'Your Appointment',
                    'status' => 'queued',
                    'is_active' => '0',
                    'created_at' => Carbon::now()->setTime(14, 15)->toDateTimeString(),
                    'updated_at' => '2023-04-19 15:36:14',
                    'source' => 'sf_appointments',
                    'source_id' => '1',
                    'sending_user_id' => 1,
                ],
                [
                    'id' => '1584913',
                    'text_thread_id' => '162967',
                    'provider_message_id' => 'SM771c40cbe6d34ada4e2ccf0014',
                    'direction' => 'outbound-api',
                    'body' => 'The appointment has been cancelled',
                    'status' => 'queued',
                    'is_active' => '0',
                    'created_at' => Carbon::now()->setTime(14, 30)->toDateTimeString(),
                    'updated_at' => '2023-04-19 15:36:14',
                    'source' => 'sf_appointments',
                    'source_id' => '1',
                    'sending_user_id' => 1,
                ],
            ],
        ]);
    }

    public function styledLinkEmails()
    {
        Fixtures::add('styledLinkEmails', [
            'sf_messages' => [
                [
                    'ID' => 100,
                    'user_id' =>  '1',
                    'owner_id' =>  '1',
                    'customer_id' =>  '-1',
                    'thread_id' =>  '100',
                    'from_type' =>  'user',
                    'from_email' =>  '<EMAIL>',
                    'from_name' =>  '<EMAIL>',
                    'request_type' => null,
                    'request_id' =>  '0',
                    'attachment' =>  '',
                    'type' =>  'message',
                    'date' =>  Carbon::now('UTC')->toDateTimeString(),
                    'status' =>  'read',
                    'category' =>  'sent',
                    'last_category' => null,
                    'message' =>  'Preston',
                    'title' =>  'New Arrivals from your favorite brand RVCA',
                    'products' =>  '4570810100',
                    'comment' =>  '',
                    'locale' =>  'en_US                    ',
                ],
                [
                    'ID' => 101,
                    'user_id' =>  '1',
                    'owner_id' =>  '1',
                    'customer_id' =>  '-1',
                    'thread_id' =>  '101',
                    'from_type' =>  'user',
                    'from_email' =>  '<EMAIL>',
                    'from_name' =>  '<EMAIL>',
                    'request_type' => null,
                    'request_id' =>  '0',
                    'attachment' =>  '',
                    'type' =>  'message',
                    'date' =>  Carbon::now('UTC')->toDateTimeString(),
                    'status' =>  'read',
                    'category' =>  'sent',
                    'last_category' => null,
                    'message' =>  'Preston',
                    'title' =>  'New Arrivals from your favorite brand RVCA',
                    'products' =>  '4570810100',
                    'comment' =>  '',
                    'locale' =>  'en_US                    ',
                ],
            ],
            'sf_mail_events' => [
                [
                    'sg_event_id' => 'ZGVsaXZlcmVkLTAtMTc2NjA3ODQteDBnd0lqOHpRXzJ2enRwZFFodFZJZy0w',
                    'sf_events_uniq_id' => '100',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'ZGVsaXZlcmVkLTAtMTc2NjA3ODQteDBnd0lqOHpRXzJ2enRwZFFodFZJZy0h',
                    'sf_events_uniq_id' => '101',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
            ],
            'sf_events' => [
                [
                    'type' => '113',
                    'date' => Carbon::now('UTC')->toDateTimeString(),
                    'source' => 'sf_message:100',
                    'uniq_id' => '100',
                    'user_id' => '1',
                    'customer_id' => '-1',
                    'attributes' => '10',
                    'satisfied' => '0',
                    'event_id' => '0',
                    'acknowledged' => '0',
                    'store_id' => '1885',
                ],
                [
                    'type' => '113',
                    'date' => Carbon::now('UTC')->toDateTimeString(),
                    'source' => 'sf_share_mail',
                    'uniq_id' => '101',
                    'user_id' => '1',
                    'customer_id' => '-1',
                    'attributes' => '10',
                    'satisfied' => '0',
                    'event_id' => '0',
                    'acknowledged' => '0',
                    'store_id' => '1885',
                ],
            ],
        ]);
    }

    public function testMarketingOutstandingTeamMode()
    {
        Fixtures::add('testMarketingOutstandingTeamMode', [
            'wp_users' => [
                $this->makeSellingModeUser(10000, 1111, 'rep', 'emp_10000', 'en_US', 1),
                $this->makeSellingModeUser(10001, 1111, 'rep', 'emp_10001', 'en_US', 0),
                $this->makeSellingModeUser(10002, 1112, 'rep', 'emp_10002', 'en_US', 1),
                $this->makeSellingModeUser(10003, 1112, 'rep', 'emp_10003', 'en_US', 0),
                $this->makeSellingModeUser(998, 1111, 'store', 'fakemall', 'en_US', 1),
                $this->makeSellingModeUser(999, 1112, 'store', 'fakemalll', 'en_US', 1),
            ],
            'sf_customer' => [
                $this->createContact(50, 998, '<EMAIL>', null, null, 0),
                $this->createContact(51, 999, '<EMAIL>', null, null, 0),
            ],
            'sf_store' => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'store_user_id'     => '998',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'store_user_id'     => '999',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemalll',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
            ],
            'sf_questions' => [
                [
                    'ID'          => 10,
                    'customer_id' => 50,
                    'user_id'     => 998,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1111,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
                [
                    'ID'          => 11,
                    'customer_id' => 51,
                    'user_id'     => 999,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1112,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ]
            ],
            'sf_messages' => [
                [
                    'user_id'      => 998,
                    'owner_id'     => 998,
                    'customer_id'  => 50,
                    'thread_id'    => 1,
                    'from_type'    => 'customer',
                    'from_name'    => 'john',
                    'request_type' => 'contact_me',
                    'request_id'   => 10,
                    'date'         => '2018-08-30 14:43:11',
                    'type'         => 'customer_request',
                    'status'       => 'unread',
                    'category'     => 'inbox',
                    'message'      => 22,
                ],
                [
                    'user_id'      => 999,
                    'owner_id'     => 999,
                    'customer_id'  => 51,
                    'thread_id'    => 1,
                    'from_type'    => 'customer',
                    'from_name'    => 'john',
                    'request_type' => 'contact_me',
                    'request_id'   => 11,
                    'date'         => '2018-08-30 14:43:11',
                    'type'         => 'customer_request',
                    'status'       => 'unread',
                    'category'     => 'inbox',
                    'message'      => 22,
                ],
            ]
        ]);
    }

    public function testMarketingOutstandingRepMode()
    {
        Fixtures::add('testMarketingOutstandingRepMode', [
            'wp_users' => [
                $this->makeSellingModeUser(10000, 1111, 'rep', 'emp_10000', 'en_US', 1),
                $this->makeSellingModeUser(10001, 1111, 'rep', 'emp_10001', 'en_US', 0),
                $this->makeSellingModeUser(10002, 1112, 'rep', 'emp_10002', 'en_US', 1),
                $this->makeSellingModeUser(10003, 1112, 'rep', 'emp_10003', 'en_US', 0),
            ],
            'sf_customer' => [
                $this->createContact(50, 10000, '<EMAIL>', null, null, 0),
                $this->createContact(51, 10001, '<EMAIL>', null, null, 0),
                $this->createContact(52, 10002, '<EMAIL>', null, null, 0),
                $this->createContact(53, 10003, '<EMAIL>', null, null, 0),
                $this->createContact(100, 0, '<EMAIL>', null, null, 0),
                $this->createContact(101, 0, '<EMAIL>', null, null, 0),
            ],
            'sf_store'        => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
            ],
            'sf_questions' => [
                [
                    'customer_id' => 100,
                    'user_id'     => 0,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1111,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
                [
                    'customer_id' => 101,
                    'user_id'     => 0,
                    'uniq_id'     => 'SF123456',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1112,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
            ]
        ]);
    }

    public function testMarketingOutstandingAllRepMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":2,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":2,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingAllRepModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreRepMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":1,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":1,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingOneStoreRepModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreAndOneUserRepMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":1,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":1,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingOneStoreAndOneUserRepModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresRepMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":1,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":1,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingInactiveStoresRepModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveUsersRepMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":2,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":2,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingInactiveUsersRepModeResponse', json_decode($response, true));
    }



    public function testMarketingOutstandingAllTeamMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":2,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":2,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingAllTeamModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreTeamMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":1,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":1,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingOneStoreTeamModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreAndOneUserTeamMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":1,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":1,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingOneStoreAndOneUserTeamModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresTeamMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":1,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":1,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingInactiveStoresTeamModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveUsersTeamMode()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":"0","salesfloor_storefront_visits_unique":0,"outstanding_email_requests":2,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":2,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingInactiveUsersTeamModeResponse', json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresTeamModeSelected()
    {
        $response = <<<DATA
{"success":true,"data":{"user":{"all":{"marketing_dates":{"backfill":{"needed":"TRUE"},"query":{"needed":"FALSE"}},"marketing":{"salesfloor_visits_unique":0,"salesfloor_storefront_visits_unique":0,"outstanding_email_requests":0,"outstanding_appointments":0,"outstanding_personal_shopper":0,"outstanding_total":0,"socialshop_unique_visit":"0","appointment_req":0,"onboarding_end":0,"user_add":0,"chat_request":0,"chat_answer":0,"response_mail_sent":0,"mail_sent":0,"email_stats_open":0,"email_stats_click":0,"avg_init_resp_sum_times":0,"avg_init_resp_num_responses":0,"chat_answer_rate_requests":0,"chat_answer_rate_answers":0,"total_order_value":0,"n_sales_transactions":0,"feedback":0,"profile_update":0,"content_create":0,"content_update":0,"content_curate":0,"ask_question_req":0,"personal_shopper_req":0,"live_session_start":0,"live_session_end":0,"product_update":0,"deal_update":0,"unsubscribe":0,"new_sale":0,"subscribe":0,"retail_event":0,"raise_concern":0,"shopping_page":0,"unique_visitor":0,"page_hit":0,"sale_duplicate":0,"user_visit":0,"com_ref":0,"soc_ref":0,"retail_hit":0,"social_post":0,"soc_share":0,"moderate_lead":0,"customer_card":0,"help_useful":0,"livesession_register":0,"change_categories":0,"chatsession_register":0,"event_create":0,"event_update":0,"event_delete":0,"event_subscribe":0,"sidebar_view":0,"sidebar_click":0,"footer_view":0,"footer_click":0,"storefront_click":0,"transactional_mail_sent":0,"courtesy_mail_sent":0,"service_total":0,"traffic_total":0,"content_total":0,"number_seconds_available":0,"total_return_value":0,"recommendation_chat":0,"recommendation_compose_message":0,"recommendation_share_email":0,"recommendation_share_facebook":0,"recommendation_share_twitter":0,"recommendation_new_arrivals":0,"recommendation_top_picks":0,"click_top_picks":0,"click_latest_arrivals":0,"click_recommended":0,"avg_selected_top_picks":0,"avg_selected_new_arrivals":0,"salesfloor_visits":0,"text_messages_outbound_api":0,"text_messages_outbound_call":0,"text_messages_outbound_reply":0,"text_messages_inbound":0,"recommendation_text_message":0,"tasks_automated_created":0,"tasks_automated_resolved":0,"tasks_automated_dismissed":0,"tasks_manual_created":0,"tasks_manual_resolved":0,"tasks_manual_dismissed":0,"chat_abandoned":0,"chat_abandonment_time":0,"chat_answer_time":0,"chat_early_redirect":0,"chat_auto_redirect":0,"ask_question_req_email":0,"ask_question_req_text":0,"appointment_req_email":0,"appointment_req_text":0,"personal_shopper_req_email":0,"personal_shopper_req_text":0,"library_share_attempts":0,"lookbook_create":0,"lookbook_update":0,"received_chats_answered_by_other":0,"request_email_sent":0,"request_email_open":0,"request_email_click":0,"compose_email_sent":0,"compose_email_open":0,"compose_email_click":0,"share_email_sent":0,"share_email_open":0,"share_email_click":0,"total_share_sent":0,"chat_abandon_0_29":0,"chat_abandon_30_59":0,"chat_abandon_60_89":0,"chat_abandon_90_120":0,"ask_question_req_chat_handoff":0,"tasks_system_created":0,"tasks_system_resolved":0,"tasks_system_dismissed":0,"tasks_followup_created":0,"tasks_followup_resolved":0,"tasks_followup_dismissed":0,"tasks_corporate_created":0,"tasks_corporate_resolved":0,"tasks_corporate_dismissed":0,"tasks_manual_resolved_sum_time":0,"tasks_manual_dismissed_sum_time":0,"tasks_automated_resolved_sum_time":0,"tasks_automated_dismissed_sum_time":0,"tasks_system_resolved_sum_time":0,"tasks_system_dismissed_sum_time":0,"tasks_followup_resolved_sum_time":0,"tasks_followup_dismissed_sum_time":0,"tasks_corporate_resolved_sum_time":0,"tasks_corporate_dismissed_sum_time":0,"socialshop_post_created":0,"socialshop_total_visit":0,"socialshop_product_click":0,"socialshop_storefront_click":0,"socialshop_sales_count":0,"socialshop_sales_amount_total":0,"tasks_corporate_deleted":0,"ask_question_req_cs_email":0,"ask_question_req_cs_text":0,"appointment_req_cs_email":0,"appointment_req_cs_text":0,"personal_shopper_req_cs_email":0,"personal_shopper_req_cs_text":0,"scheduled_appointments":0,"cancelled_appointment":0,"video_chat_sessions":0,"video_chat_duration":0,"virtual_appointment_sessions":0,"virtual_appointment_duration":0,"avg_init_resp":"-","avg_init_resp_raw":0,"avg_order_value":0,"mail_click":0,"mail_open":0,"tasks_manual_resolved_avg_time":"-","tasks_manual_resolved_avg_time_raw":0,"tasks_manual_dismissed_avg_time":"-","tasks_manual_dismissed_avg_time_raw":0,"tasks_automated_resolved_avg_time":"-","tasks_automated_resolved_avg_time_raw":0,"tasks_automated_dismissed_avg_time":"-","tasks_automated_dismissed_avg_time_raw":0,"tasks_system_resolved_avg_time":"-","tasks_system_resolved_avg_time_raw":0,"tasks_system_dismissed_avg_time":"-","tasks_system_dismissed_avg_time_raw":0,"tasks_followup_resolved_avg_time":"-","tasks_followup_resolved_avg_time_raw":0,"tasks_followup_dismissed_avg_time":"-","tasks_followup_dismissed_avg_time_raw":0,"tasks_corporate_resolved_avg_time":"-","tasks_corporate_resolved_avg_time_raw":0,"tasks_corporate_dismissed_avg_time":"-","tasks_corporate_dismissed_avg_time_raw":0,"text_messages_sent":0,"text_messages_received":0,"chat_answer_rate":0,"chat_missed":0,"chat_answer_rate_new":0,"chat_number_minutes_available":0,"chat_avg_answer_time":0,"chat_avg_availability":0,"net_sales":0,"n_recommendations":"0","click_top_picks_rate":0,"click_latest_arrivals_rate":0,"click_recommended_rate":0,"service_exclude_live_chat_total":0,"service_cs_total":0}}}}}
DATA;

        Fixtures::add('testMarketingOutstandingInactiveStoresTeamModeSelectedResponse', json_decode($response, true));
    }

    public function testChatAvailabilityRateRangeWeek()
    {
        $data = [
            'sf_user_daily_stats' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-22',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-23',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-24',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-25',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-26',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeek', $data);
    }

    public function testChatAvailabilityRateRangeWeekAndWeekend()
    {
        $data = [
            'sf_user_daily_stats' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-20',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-21',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-22',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-23',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-24',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-25',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-26',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeekAndWeekend', $data);
    }

    public function testChatAvailabilityRateRangeWeekendEmptyMetrics()
    {
        $data = [
            'sf_user_daily_stats' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-20',
                    'timezone' => 'America/Montreal',
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-21',
                    'timezone' => 'America/Montreal',
                ],
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeekendEmptyMetrics', $data);
    }

    public function testChatAvailabilityRateRangeWeekendWithMetrics()
    {
        $blank = $this->generateDailyStatsFixValue(0);

        $data = [
            'sf_user_daily_stats' => [
                array_merge($blank, [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-20',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ]),
                array_merge($blank, [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-21',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ]),
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeekendWithMetrics', $data);
    }

    public function addGroupTasks()
    {
        Carbon::setTestNow();
        $now = Carbon::now('UTC');
        $fourMonthAgo = $now->copy()->subMonths(4);
        Fixtures::add('GroupTasks', [
            'wp_users'  => [
                [
                    'ID'              => 255,
                    'user_login'      => 'tests_19063',
                    'user_pass'       => 'password',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'samuel',
                    'store'           => 1003,
                    'type'            => 'rep',
                    'employee_id'     => 'test151515',
                    'group'           => 2,
                    'selling_mode'    => 1,
                    'locale'          => 'en_US',
                    'creation_source' => 'invite'
                ]
            ],
            'sf_store' => [
                [
                    'store_id'          => 1004,
                    'name'              => 'Store',
                    'timezone'          => 'America/New_York',
                    'sf_identifier'     => 'store',
                    'retailer_store_id' => 'test515151',
                    'store_user_id'     => null,
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                ]
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1004,
                    'is_default' => 1
                ],
            ],
            'sf_group_tasks' => [
                [
                    'id' => 1,
                    'store_id' => '1003',
                    'title' => 'Test Title1',
                    'details' => 'Test Detail1',
                    'start_date' => null,
                    'reminder_date' => '2023-03-01 00:01:02',
                    'auto_dismiss_date' => '2024-01-01 00:01:02',
                    'status' => 'unresolved',
                    'customer_id' => '1234',
                    'preferred_user_id' => '255',
                    'suggested_subject_line' => 'Test Subject Line1',
                    'suggested_copy' => 'Test Suggested Copy1',
                    'created_at' => $fourMonthAgo->toDateTimeString(),
                ],
                [
                    'id' => 2,
                    'store_id' => '1003',
                    'title' => 'Test Title2',
                    'details' => 'Test Detail2',
                    'start_date' => $fourMonthAgo->toDateTimeString(),
                    'reminder_date' => '2023-03-01 00:01:02',
                    'auto_dismiss_date' => '2024-01-01 00:01:02',
                    'status' => 'unresolved',
                    'customer_id' => '1234',
                    'preferred_user_id' => '255',
                    'suggested_subject_line' => 'Test Subject Line2',
                    'suggested_copy' => 'Test Suggested Copy',
                    'created_at' => $fourMonthAgo->toDateTimeString(),
                ],
                [
                    'id' => 3,
                    'store_id' => '1003',
                    'title' => 'Test Title3',
                    'details' => 'Test Detail3',
                    'start_date' => $fourMonthAgo->toDateTimeString(),
                    'reminder_date' => '2023-03-01 00:01:02',
                    'auto_dismiss_date' => '2024-01-01 00:01:02',
                    'status' => 'unresolved',
                    'customer_id' => '1234',
                    'preferred_user_id' => '255',
                    'suggested_subject_line' => 'Test Subject Line3',
                    'suggested_copy' => 'Test Suggested Copy',
                    'created_at' => $fourMonthAgo->toDateTimeString(),
                ]
            ],
            'sf_group_task_activities' => [
                // Task 1
                [
                    'id' => 1,
                    'group_task_id' => 1,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_EMAIL,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 2,
                    'group_task_id' => 1,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 3,
                    'group_task_id' => 1,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_RESOLVE,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                // Task 2
                [
                    'id' => 4,
                    'group_task_id' => 2,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_EMAIL,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 5,
                    'group_task_id' => 2,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_RESOLVE,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 6,
                    'group_task_id' => 2,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                // Task 3
                [
                    'id' => 7,
                    'group_task_id' => 3,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_EMAIL,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 8,
                    'group_task_id' => 3,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_AUTO_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 9,
                    'group_task_id' => 3,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
            ],
        ]);
    }

    public function addAvgInitResponseFixtures()
    {
        $i = 0;
        $data = [
            // Questions must be generated first since we are doing the incremental time
            // This is so the initial contact has a time < response time
            'sf_questions' => [
                [
                    "ID" => 62308,
                    "customer_id" => 6396135,
                    "user_id" => 1,
                    "uniq_id" => "SFID65b30397824663.67917157",
                    "question" => "chanel foundation",
                    "topic" => "",
                    "timezone" => "UTC",
                    "creation" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                    "phone" => "+17808855555",
                    "status" => "",
                    "loc_id" => 75589,
                    "category_id" => null,
                    "sub_category_id" => null,
                    "flagged" => null,
                    "store_id" => 1003,
                    "unattached_id" => null,
                    "source_url" => "https://services.shoppersdrugmart.ca/en_US/adrianams",
                    "source_title" => "Shop with Adriana Martinez Sanchez - Shoppers Drug Mart",
                    "locale" => "en_US",
                    "channel" => "text",
                    "source" => "storefront",
                    "chat_handoff" => 0
                ],
                [
                    "ID" => 62328,
                    "customer_id" => 6396135,
                    "user_id" => 1,
                    "uniq_id" => "SFID65b3cbc295f538.40504222",
                    "question" => "best mup spray",
                    "topic" => "",
                    "timezone" => "UTC",
                    "creation" => "2024-01-26 15:12:02",
                    "phone" => "+17808855555",
                    "status" => "",
                    "loc_id" => 75610,
                    "category_id" => null,
                    "sub_category_id" => null,
                    "flagged" => null,
                    "store_id" => 1003,
                    "unattached_id" => null,
                    "source_url" => "https://services.shoppersdrugmart.ca/en_US/adrianams",
                    "source_title" => "Shop with Adriana Martinez Sanchez - Shoppers Drug Mart",
                    "locale" => "en_US",
                    "channel" => "text",
                    "source" => "storefront",
                    "chat_handoff" => 0
                ]
            ],
            'sf_messages' => [
                 [
                     "ID" => 5656854,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5656854,
                     "from_type" => "customer",
                     "from_email" => "",
                     "from_name" => "martina ",
                     "request_type" => "contact_me",
                     "request_id" => 62308,
                     "attachment" => "",
                     "type" => "customer_request",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "inbox",
                     "last_category" => null,
                     "message" => "22",
                     "title" => "Contact Me/Email me Request",
                     "products" => "",
                     "comment" => "chanel foundation",
                     "locale" => "en_US"
                 ],
                 [
                     "ID" => 5656859,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5656859,
                     "from_type" => "user",
                     "from_email" => "",
                     "from_name" => "",
                     "request_type" => "contact_me",
                     "request_id" => 62308,
                     "attachment" => "",
                     "type" => "message",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "sent",
                     "last_category" => null,
                     "message" => "You replied to this request via SMS",
                     "title" => "system-message",
                     "products" => "",
                     "comment" => null,
                     "locale" => "en_US"
                 ],
                 [
                     "ID" => 5660175,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5660175,
                     "from_type" => "customer",
                     "from_email" => "",
                     "from_name" => "Martina ",
                     "request_type" => "contact_me",
                     "request_id" => 62328,
                     "attachment" => "",
                     "type" => "customer_request",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "inbox",
                     "last_category" => null,
                     "message" => "22",
                     "title" => "Contact Me/Email me Request",
                     "products" => "",
                     "comment" => "best mup spray",
                     "locale" => "en_US"
                 ],
                 [
                     "ID" => 5660176,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5660176,
                     "from_type" => "user",
                     "from_email" => "",
                     "from_name" => "",
                     "request_type" => "contact_me",
                     "request_id" => 62328,
                     "attachment" => "",
                     "type" => "message",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "sent",
                     "last_category" => null,
                     "message" => "You replied to this request via SMS",
                     "title" => "system-message",
                     "products" => "",
                     "comment" => null,
                     "locale" => "en_US"
                 ]
            ],
        ];
        Fixtures::add('AvgResponseTimeOutgoingSms', $data);
    }

    public function addGroupedProducts()
    {
        Carbon::setTestNow();
        $now = Carbon::now('UTC');

        Fixtures::add('GroupedProducts', [
            'wp_users'  => [
                [
                    'ID'              => 123,
                    'user_login'      => 'tests_123',
                    'user_pass'       => 'password',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'jona',
                    'store'           => 1003,
                    'type'            => 'rep',
                    'employee_id'     => 'test151515',
                    'group'           => 2,
                    'selling_mode'    => 1,
                    'locale'          => 'en_US',
                    'creation_source' => 'invite'
                ]
            ],

            'sf_events' => [
                [
                    'id' => 1,
                    'uniq_id' => 'SF01',
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_EMAIL,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '4', // number of shared products
                    'event_id' => '1111',
                    'store_id' => 1003
                ],
                [
                    'id' => 2,
                    'uniq_id' => 'SF02',
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_EMAIL,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '2',
                    'event_id' => '2222',
                    'store_id' => 1003
                ],
                [
                    'id' => 3,
                    'uniq_id' => 333333,
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_SMS,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '6',
                    'event_id' => '3333',
                    'store_id' => 1003
                ],
                [
                    'id' => 4,
                    'uniq_id' => 4444444,
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_SMS,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '4',
                    'event_id' => '3333',
                    'store_id' => 1003
                ],
                [
                    'id' => 5,
                    'uniq_id' => '4444',
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_EMAIL,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '5',
                    'event_id' => '4444',
                    'store_id' => 1003
                ],
            ],

            'sf_mail_events' => [
                [
                    'sg_event_id' => 'ZGVsaXZlcmVkLlkj90o34ujldkfgj3490j',
                    'sf_events_uniq_id' => 'SF01',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'fdkslfklh3498fjkdhs',
                    'sf_events_uniq_id' => 'SF01',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'klewdjspfldskjfklj34oiudklfj',
                    'sf_events_uniq_id' => 'SF02',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => '4ulvjlk349ufhj',
                    'sf_events_uniq_id' => 'SF03',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'j345980uvohjkklhj',
                    'sf_events_uniq_id' => '4444',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
            ],

            'sf_messages'  => [
                [
                    "ID"            => '4444',
                    "user_id"       => '1',
                    "owner_id"      => '1',
                    "customer_id"   => '1',
                    "thread_id"     => '1',
                    "from_type"     => 'user',
                    "from_email"    => '<EMAIL>',
                    "from_name"     => 'hummm',
                    "request_type"  => 'contact_me',
                    "request_id"    => '1',
                    "attachment"    => '',
                    "type"          => 'customer_request',
                    "date"          => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    "status"        => 'customer-support',
                    "category"      => 'inbox',
                    "last_category" => null,
                    "title"         => 'Contact Me Request',
                    "products"      => '',
                    "comment"       => 'test',
                    "locale"        => 'en_US',
                ],
            ],
        ],);
    }

    public function testFlaggedChatConversations()
    {
        Fixtures::add('testFlaggedChatConversations', [
            'sf_chat_flagged' =>
                [
                    [
                        'user_id' => self::REGGIE_ID,
                        'chat_thread_identifier' => '1234',
                        'customer_fingerprint' => '1234',
                        'customer_ip_address' => '*******',
                        'customer_user_agent' => 'test',
                        'chat_contents' => 'wtf',
                        'created_at' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
                    ],
                    [
                        'user_id' => 2, // different user, same store.
                        'chat_thread_identifier' => '12345',
                        'customer_fingerprint' => '1234',
                        'customer_ip_address' => '*******',
                        'customer_user_agent' => 'test',
                        'chat_contents' => 'wtf',
                        'created_at' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
                    ],
                    [
                        'user_id' => '1324123416', // this is an invalid, to make sure not part of the kpis
                        'chat_thread_identifier' => '123456',
                        'customer_fingerprint' => '1234',
                        'customer_ip_address' => '*******',
                        'customer_user_agent' => 'test',
                        'chat_contents' => 'wtf',
                        'created_at' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
                    ]
                ]
        ]);

        Fixtures::add('FlaggedChatConversations', [
            'wp_users' => [$this->makeSellingModeUser(136163, 2003)],
            'sf_chat_flagged' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'chat_thread_identifier' => "random1234"
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'chat_thread_identifier' => "random12345"
                ],
                [
                    'user_id' => 136163,
                    'chat_thread_identifier' => "random123456"
                ]
            ],
        ]);
    }

    public function abandonedStats()
    {
        Fixtures::add('LiveChatEventsWithAbandoned', [
            'wp_users' => [$this->makeSellingModeUser(1963, 2003)],
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-not-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                    'attributes'  => '',
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-not-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-not-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
            ],
        ]);
    }

    public function testAggregateDailyStatsActionId()
    {
        Fixtures::add('testAggregateDailyStatsActionId', [
            'sf_sidebar_event_log_20240201' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369204,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => 1,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369204,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => 2,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
            ]
        ]);
    }

    public function testAggregateDailyStatsMenu()
    {
        $date = Carbon::now('UTC')->subDays(1)->format('Y-m-d');

        Fixtures::add('testAggregateDailyStatsMenu', [
            SidebarEventLog::getEventTable($date) =>
                [
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    // Desktop
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],

                    // Mobile
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                ]
        ]);
    }

    public function testAggregateDailyStatsRequests()
    {
        $date = Carbon::now('UTC')->subDays(1)->format('Y-m-d');

        Fixtures::add('testAggregateDailyStatsRequests', [
            SidebarEventLog::getEventTable($date) =>
                [
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    // Desktop
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],

                    // Mobile
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                ]
        ]);
    }

    public function testExportCsvSidebarMetrics()
    {
        Fixtures::add('testExportCsvSidebarMetrics', [
            'sf_sidebar_daily_stats' => [
                $this->generateSidebarMetrics(Carbon::now('UTC') ->subDays(1)->format('Y-m-d')),
                $this->generateSidebarMetrics(Carbon::now('UTC') ->subDays(2)->format('Y-m-d')),
            ],
        ]);
    }

    private function generateSidebarMetrics(string $date)
    {
        $fields = [
            'date',
            'sidebar_mobile_total_view',
            'sidebar_mobile_total_click',
            'sidebar_mobile_total_minimize',
            'sidebar_mobile_total_maximize',
            'sidebar_mobile_total_tagline_minimize',
            'sidebar_desktop_total_view',
            'sidebar_desktop_total_click',
            'sidebar_desktop_total_minimize',
            'sidebar_desktop_total_maximize',
            'sidebar_mobile_unique_view',
            'sidebar_mobile_unique_click',
            'sidebar_mobile_unique_minimize',
            'sidebar_mobile_unique_maximize',
            'sidebar_mobile_unique_tagline_minimize',
            'sidebar_desktop_unique_view',
            'sidebar_desktop_unique_click',
            'sidebar_desktop_unique_minimize',
            'sidebar_desktop_unique_maximize',
            'contextual_widget_desktop_view',
            'contextual_widget_desktop_unique_view',
            'contextual_widget_desktop_click',
            'contextual_widget_desktop_unique_click',
            'contextual_widget_mobile_view',
            'contextual_widget_mobile_unique_view',
            'contextual_widget_mobile_click',
            'contextual_widget_mobile_unique_click',
            'sidebar_desktop_total_menu_livechat',
            'sidebar_desktop_total_menu_message',
            'sidebar_desktop_total_menu_appointment',
            'sidebar_desktop_total_menu_support',
            'sidebar_mobile_total_menu_livechat',
            'sidebar_mobile_total_menu_message',
            'sidebar_mobile_total_menu_appointment',
            'sidebar_mobile_total_menu_support',
            'sidebar_desktop_unique_menu_livechat',
            'sidebar_desktop_unique_menu_message',
            'sidebar_desktop_unique_menu_appointment',
            'sidebar_desktop_unique_menu_support',
            'sidebar_mobile_unique_menu_livechat',
            'sidebar_mobile_unique_menu_message',
            'sidebar_mobile_unique_menu_appointment',
            'sidebar_mobile_unique_menu_support',
            'sidebar_desktop_total_requests_livechat',
            'sidebar_desktop_total_requests_message',
            'sidebar_desktop_total_requests_appointment',
            'sidebar_mobile_total_requests_livechat',
            'sidebar_mobile_total_requests_message',
            'sidebar_mobile_total_requests_appointment',
            'sidebar_desktop_unique_requests_livechat',
            'sidebar_desktop_unique_requests_message',
            'sidebar_desktop_unique_requests_appointment',
            'sidebar_mobile_unique_requests_livechat',
            'sidebar_mobile_unique_requests_message',
            'sidebar_mobile_unique_requests_appointment',
        ];

        return array_combine(
            $fields,
            array_merge([
                $date,
            ], range(0, count($fields) - 2))
        );
    }
}
