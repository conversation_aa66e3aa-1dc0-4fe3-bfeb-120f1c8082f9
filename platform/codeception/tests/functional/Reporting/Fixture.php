<?php

namespace SF\functional\Reporting;

use Carbon\Carbon;
use Salesfloor\Models\SidebarEventLog;
use SF\BaseFixture;
use Codeception\Util\Fixtures;
use Salesfloor\Models\GroupTask\GroupTaskActivity;
use Salesfloor\Services\Event;
use Salesfloor\Services\Queue\SidebarEventQueue as SidebarEventQueueService;

class Fixture extends BaseFixture
{
    public const REGGIE_ID = 1;

    public function userDetection()
    {
        $socialFeedProductId = 123;
        $socailFeedId = 234;

        Fixtures::add('MultipleStoreSwitchesSameDay', [
            'sf_store' => [[
                'store_id' => '777',
                'name' => 'UTC Store',
                'timezone' => 'Etc/UTC',
                'sf_identifier' => 'utc-store',
                'latitude'          => 45.4910,
                'longitude'         => -73.5658,
                'country'           => 'CA',
                'region'            => 'QC',
                'city'              => 'Montreal',
                'address'           => '1455 Peel Streets',
                'postal'            => 'H3A 1T5',
                'phone'             => '************',
            ]],
            'wp_users' => [$this->makeSellingModeUser(19063, 777)],
            'sf_wp_user_field_history' => [
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 1003,
                    'new_value' => 2003,
                    'field_modified_at' => $this->utcDateTime('-1 hour'),
                    'source' => 'ben'
                ],
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 2003,
                    'new_value' => 777,
                    'field_modified_at' => $this->utcDateTime('-5 minutes'),
                    'source' => 'ben'
                ]
            ]
        ]);

        Fixtures::add('MultipleStoreSwitchesOverLongPeriod', [
            'sf_store' => [[
                'store_id' => '777',
                'name' => 'UTC Store',
                'timezone' => 'Etc/UTC',
                'sf_identifier' => 'utc-store',
                'latitude'          => 45.4910,
                'longitude'         => -73.5658,
                'country'           => 'CA',
                'region'            => 'QC',
                'city'              => 'Montreal',
                'address'           => '1455 Peel Streets',
                'postal'            => 'H3A 1T5',
                'phone'             => '************',
            ]],
            'wp_users' => [$this->makeSellingModeUser(19063, 777)],
            'sf_wp_user_field_history' => [
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 1003,
                    'new_value' => 2003,
                    'field_modified_at' => $this->utcDateTime('-20 days'),
                    'source' => 'ben'
                ],
                [
                    'user_id' => 19063,
                    'field_name' => 'store',
                    'old_value' => 2003,
                    'new_value' => 777,
                    'field_modified_at' => $this->utcDateTime('-5 days'),
                    'source' => 'ben'
                ]
            ]
        ]);

        Fixtures::add('SocialShopTracking', [
            'sf_event_log' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_VIEW',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539455,
                    'action'      => 'SOCIALSHOP_VIEW',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_VIEW',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_PRODUCT_CLICK',
                    'action_id'   => $socialFeedProductId,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 16455166539999,
                    'action'      => 'SOCIALSHOP_STOREFRONT_CLICK',
                    'action_id'   => self::REGGIE_ID,
                    'created_at'  => date('Y-m-d H:i:s', strtotime('-6 hour')),
                ],
            ],
            'sf_shop_feed_product' => [
                [
                    'id'            => $socialFeedProductId,
                    'feed_id'       => $socailFeedId,
                    'product_id'    => 123456,
                    'created_at'    => '2018-09-04 19:11:11',
                    'updated_at'    => '2018-09-04 19:11:11',
                ],
            ],
            'sf_shop_feed' => [
                [
                    'id'            => $socailFeedId,
                    'user_id'       => self::REGGIE_ID,
                    'source'        => 'share',
                    'comment'       => 'comment 1',
                    'created_at'    => '2018-12-31 05:00:00',
                    'updated_at'    => '2018-12-31 05:00:00',
                    'created_by'    => 1,

                ],
            ],
        ]);

        Fixtures::add('CustomerSupportRequestsTracking', [
            'sf_questions' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
            ],
            'sf_personal_shopper' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF1234567',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-6 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                ],
            ],
            'sf_appointments' => [
                [
                    'ID' => 1,
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF3456',
                    'date'    => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'event_duration' => 60,
                ],
            ],
            'sf_events' => [
                [
                    'type'        => 6,
                    'date'        => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'uniq_id'     => 'SF12345',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
                [
                    'type'        => 8,
                    'date'        => date('Y-m-d H:i:s', strtotime('-6 hour')),
                    'uniq_id'     => 'SF1234567',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
                [
                    'type'        => 7,
                    'date'        => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'uniq_id'     => 'SF3456',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
            ],
            'sf_messages'  => [
                [
                    "user_id"       => '1',
                    "owner_id"      => '1',
                    "customer_id"   => '1', // The is not the real customer that made the request
                    "thread_id"     => '1',
                    "from_type"     => 'user',
                    "from_email"    => '<EMAIL>',
                    "from_name"     => 'hummm',
                    "request_type"  => 'contact_me',
                    "request_id"    => '1',
                    "attachment"    => '',
                    "type"          => 'customer_request',
                    "date"          => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    "status"        => 'customer-support',
                    "category"      => 'inbox',
                    "last_category" => null,
                    "title"         => 'Contact Me Request',
                    "products"      => '',
                    "comment"       => 'test',
                    "locale"        => 'en_US',
                ],
                [
                    "user_id"       => '1',
                    "owner_id"      => '1',
                    "customer_id"   => '1', // The is not the real customer that made the request
                    "thread_id"     => '1',
                    "from_type"     => 'user',
                    "from_email"    => '<EMAIL>',
                    "from_name"     => 'hummm',
                    "request_type"  => 'personal_shopper',
                    "request_id"    => '1',
                    "attachment"    => '',
                    "type"          => 'customer_request',
                    "date"          => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    "status"        => 'customer-support',
                    "category"      => 'inbox',
                    "last_category" => null,
                    "title"         => 'Personal Shopper Request',
                    "products"      => '',
                    "comment"       => 'test',
                    "locale"        => 'en_US',
                ],
            ]
        ]);

        Fixtures::add('CustomerSupportRequestsTrackingRepTransaction', [
            'sf_rep_transaction' => [
                [
                    'user_id' => 1,
                    'trx_id'  => '77789835',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type' => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id' => 1,
                    'trx_id'  => '77740071',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'trx_type' => 'sale',
                    'trx_total' => 200.00,
                ],
                [
                    'user_id' => 1,
                    'trx_id'  => '77789835',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'trx_type' => 'cancel',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id' => 1,
                    'trx_id'  => '77740071',
                    'trx_date' => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'trx_type' => 'return',
                    'trx_total' => 200.00,
                ],
            ],
        ]);

        Fixtures::add('VideoChatEvents', [
            'sf_events' => [
                [
                    'type'        => 101,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF12345',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                ],
                [
                    'type'        => 102,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                ],
            ],
        ]);

        Fixtures::add('LiveChatEventsSameStoreAnswer', [
            'wp_users' => [$this->makeSellingModeUser(19063, 2003)],
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-answered-by-another',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
            ],
        ]);
        Fixtures::add('LiveChatEventsDiffStoreAnswer', [
            'wp_users' => [$this->makeSellingModeUser(19063, 2003)],
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 19063,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 19063,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 19063,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
            ],
        ]);
        Fixtures::add('LiveChatEventsTeamModeSameStoreAnswer', [
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2000,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-3 hour')),
                    'uniq_id'     => 'SF12345-team-answered-by-another',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
            ],
        ]);
        Fixtures::add('LiveChatEventsTeamModeDiffStoreAnswer', [
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2000,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2001,
                    'customer_id' => 0,
                    'store_id'    => 1031,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2002,
                    'customer_id' => 0,
                    'store_id'    => 1032,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2002,
                    'customer_id' => 0,
                    'store_id'    => 1032,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-4 hour')),
                    'uniq_id'     => 'SF123diffstore-ans-by-ano',
                    'user_id'     => 2002,
                    'customer_id' => 0,
                    'store_id'    => 1032,
                ],
            ],
        ]);
        $this->generateTeamModeCore();

        Fixtures::add('VirtualAppointmentEvents', [
            'sf_events' => [
                [
                    'type'        => 111,
                    'date'        => date('Y-m-d H:00:00', strtotime('-4 hour')),
                    'uniq_id'     => 'sf12345-virtual-appointment',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
                [
                    'type'        => 112,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'sf12345-virtual-appointment',
                    'user_id'     => 1,
                    'customer_id' => 1,
                    'store_id'    => 1001,
                    'attributes'  => '',
                ],
            ],
        ]);

        // some data set to test Corporate Task kpi
        // for example: tasks_corporate_deleted
        Fixtures::add('CorporateTaskMetric', [
            'sf_task' => [
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'details',
                    'parent_id' => 1,
                ],
                [
                    'user_id'   => 1,
                    'status'    => 'dismissed',
                    'details'   => 'details',
                    'parent_id' => 2,
                ],
                [
                    'user_id'   => 1,
                    'status'    => 'unresolved',
                    'details'   => 'details',
                    'parent_id' => 3,
                ],
            ],
            'sf_corporate_tasks' => [
                [
                    'id'                => 1,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        =>  $this->utcDateTime('-6 hours'),
                    "is_deleted"        => 1
                ],
                [
                    'id'                => 2,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        =>  $this->utcDateTime('-6 hours'),
                    "is_deleted"        => 1
                ],
                [
                    'id'                => 3,
                    "title"             => "test title",
                    "subject"           => "test subject",
                    "detail"            => "test detail",
                    "body"              => "test body",
                    "created_by"        => 1,
                    "updated_by"        => 1,
                    "reminder_time"     => "2019-04-20 09:00:00",
                    "auto_dismiss_time" => "2019-05-20 09:00:00",
                    "created_at"        => "2019-04-15 09:00:00",
                    "updated_at"        =>  $this->utcDateTime('-6 hours'),
                    "is_deleted"        => 1
                ],
            ],
        ]);

        $sidebarStatsWithDailyStatsTable = array_merge(
            $this->getElbStatsAndSidebarEvents(),
            $this->getSidebarDailyStats()
        );

        Fixtures::add('SidebarTrackingWithoutDailyStatsTable', $this->getElbStatsAndSidebarEvents());
        Fixtures::add('SidebarTrackingWithDailyStatsTable', $sidebarStatsWithDailyStatsTable);
        Fixtures::add('ContextualTrackingEventLog', $this->getContextualWidgetEventLog());
        Fixtures::add('ContextualTrackingEventLogRotate', $this->getContextualWidgetEventLogRotate());

        Fixtures::add('CancelledAppointments', [
            'sf_appointments' => [
                [
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF3456',
                    'date'        => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'status'      => 'cancelled',
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                ],
                [
                    'customer_id' => 1,
                    'user_id'     => 1,
                    'uniq_id'     => 'SF345667',
                    'date'        => date('Y-m-d H:i:s', strtotime('-5 hour')),
                    'store_id'    => 1003,
                    'channel'     => 'email',
                    'status'      => 'cancelled',
                    'event_type'          => 'Chat',
                    'event_duration'      => 60,
                ],
            ],
        ]);
    }

    public function addFixtureTransactionStatsKpis()
    {
        Fixtures::add('getTrxStatsNormalCase', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 250.00,
                ],
                [
                    'user_id'   => 2,
                    'trx_id'    => '77789837',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 350.00,
                ],
            ]
        ]);

        Fixtures::add('getTrxStatsEdgeCaseDupulicateUserIdZero', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 0,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 250.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 240.00,
                ],
                [
                    'user_id'   => 2,
                    'trx_id'    => '77789837',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 350.00,
                ],
            ]
        ]);

        Fixtures::add('getTrxSTatsEdgeCaseDuplicateUserIdDifferent', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 2,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 110.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => date('Y-m-d H:i:s', strtotime('-3 hour')),
                    'trx_type'  => 'sale',
                    'trx_total' => 210.00,
                ],
            ]
        ]);
    }

    public function addFixtureActiveUsersInDateRange()
    {
        Fixtures::add('activeUserInDateRange', [
            'sf_store' => [
                [
                    'store_id' => '1000',
                    'name' => 'UTC Store',
                    'timezone' => 'Etc/UTC',
                    'sf_identifier' => 'utc-store',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                ]
            ],
            'wp_users' => [
                [
                    'ID' => '1000',
                    'user_login' => 'salesfloor_benrodier',
                    'user_pass' => '$P$BgHlNnk5eFepwz5Z3hQdtCJVt755Y50',
                    'user_email' => '<EMAIL>',
                    'user_registered' => '2019-02-11 02:03:22',
                    'user_status' => '1',
                    'store' => '1000',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '505327',
                    'group' => '1',
                    'selling_mode' => '1',
                    'isPhoto' => '1',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
                [
                    'ID' => '1001',
                    'user_login' => 'momahony',
                    'user_pass' => '$P$B.WyBplgTFUa0qqB4KfgXkKltat0dK0',
                    'user_email' => '<EMAIL>',
                    'user_registered' => '2019-02-11 14:42:37',
                    'user_status' => '1',
                    'store' => '1000',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '301495',
                    'group' => '4',
                    'selling_mode' => '1',
                    'isPhoto' => '0',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
                [
                    'ID' => '1002',
                    'user_login' => 'leon',
                    'user_pass' => '$P$B.WyBplgTFUa0qqB4KfgXkKltat0dK0',
                    'user_email' => '<EMAIL>',
                    'user_registered' => '2019-02-11 14:42:37',
                    'user_status' => '1',
                    'store' => '1000',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '301496',
                    'group' => '4',
                    'selling_mode' => '1',
                    'isPhoto' => '0',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
            ],
            'sf_user_activity' => [
                [
                    'id' => '1000',
                    'type' => 'active session',
                    'source' => 'ppp on 2019-03-13',
                    'user_id' => '1000',
                    'start_date' => '2020-01-01 14:04:46',
                    'end_date' => '2020-03-01 23:59:59',
                ],
                [
                    'id' => '1001',
                    'type' => 'active session',
                    'source' => 'ppp on 2019-03-13',
                    'user_id' => '1001',
                    'start_date' => '2020-01-01 14:04:46',
                    'end_date' => '2020-03-01 23:59:59',
                ],
                [
                    'id' => '1002',
                    'type' => 'active session',
                    'source' => 'ppp on 2019-03-13',
                    'user_id' => '1002',
                    'start_date' => '2020-02-04 14:04:46',
                    'end_date' => '2020-02-04 23:59:59',
                ]
            ],
        ]);
    }

    protected function getElbStatsAndSidebarEvents()
    {
        return [
            'sf_sidebar_event_log' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => 'SIDEBAR_MOBILE_VIEW',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => 'SIDEBAR_MOBILE_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_MAXIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => 'SIDEBAR_DESKTOP_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_VIEW',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => 'SIDEBAR_MOBILE_MINIMIZE',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => 'SIDEBAR_DESKTOP_CLICK',
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
            ],
            'sf_elb_stats' => [
                [
                    'date'                      => '2019-04-03',
                    'desktop_retailer_requests' => 1,
                    'mobile_retailer_requests'  => 2,
                    'desktop_sidebar_requests'  => 3,
                    'mobile_sidebar_requests'   => 3,
                    'desktop_clickthru_count'   => 3,
                    'mobile_clickthru_count'    => 3,
                ],
                [
                    'date'                      => '2019-04-04',
                    'desktop_retailer_requests' => 1,
                    'mobile_retailer_requests'  => 2,
                    'desktop_sidebar_requests'  => 3,
                    'mobile_sidebar_requests'   => 3,
                    'desktop_clickthru_count'   => 3,
                    'mobile_clickthru_count'    => 3,
                ],
            ],
        ];
    }

    protected function getContextualWidgetEventLog()
    {
        return [
            'sf_sidebar_event_log' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377285,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106693,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106694,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106695,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2019-04-04 01:46:10',
                ],
            ],
        ];
    }
    protected function getContextualWidgetEventLogRotate()
    {
        return [
            'sf_sidebar_event_log_20240201' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 73310617377285,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106693,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106694,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106695,
                    'fingerprint' => 73310617377284,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_DESKTOP_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],

                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 96721650281998,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
            ],
        ];
    }

    protected function getSidebarDailyStats()
    {
        return [
            'sf_sidebar_daily_stats' => [
                [
                    'date'                              => '2019-04-03',
                    'sidebar_mobile_total_view'         => 100,
                    'sidebar_mobile_total_click'        => 50,
                    'sidebar_mobile_total_minimize'     => 70,
                    'sidebar_mobile_total_maximize'     => 60,
                    'sidebar_desktop_total_view'        => 80,
                    'sidebar_desktop_total_click'       => 40,
                    'sidebar_desktop_total_minimize'    => 30,
                    'sidebar_mobile_unique_view'        => 40,
                    'sidebar_mobile_unique_click'       => 20,
                    'sidebar_mobile_unique_minimize'    => 20,
                    'sidebar_mobile_unique_maximize'    => 50,
                    'sidebar_desktop_unique_view'       => 20,
                    'sidebar_desktop_unique_click'      => 10,
                    'sidebar_desktop_unique_minimize'   => 12,

                ],
                [
                    'date'                              => '2019-04-04',
                    'sidebar_mobile_total_view'         => 120,
                    'sidebar_mobile_total_click'        => 70,
                    'sidebar_mobile_total_minimize'     => 90,
                    'sidebar_mobile_total_maximize'     => 80,
                    'sidebar_desktop_total_view'        => 100,
                    'sidebar_desktop_total_click'       => 60,
                    'sidebar_desktop_total_minimize'    => 50,
                    'sidebar_mobile_unique_view'        => 60,
                    'sidebar_mobile_unique_click'       => 40,
                    'sidebar_mobile_unique_minimize'    => 40,
                    'sidebar_mobile_unique_maximize'    => 70,
                    'sidebar_desktop_unique_view'       => 40,
                    'sidebar_desktop_unique_click'      => 30,
                    'sidebar_desktop_unique_minimize'   => 32,
                ],
            ],
        ];
    }

    public function processorSale()
    {
        Fixtures::add('processorSaleStoreIdNull', [
            'sf_rep_transaction' => [
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789835',
                    'trx_date'  => gmdate('Y-m-d 12:00:00'), // We need to force the time, because it's timezone based
                    'trx_type'  => 'sale',
                    'trx_total' => 100.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789836',
                    'trx_date'  => gmdate('Y-m-d 22:00:00'), // We need to force the time, because it's timezone based
                    'trx_type'  => 'sale',
                    'trx_total' => 110.00,
                ],
                [
                    'user_id'   => 1,
                    'trx_id'    => '77789837',
                    'trx_date'  => gmdate('Y-m-d 03:00:00'), // We need to force the time, because it's timezone based
                    'trx_type'  => 'sale',
                    'trx_total' => 120.00,
                ],
            ]
        ]);
    }

    public function inactiveFilteringCest()
    {
        // I'm still questioning if this was needed for those tests.
        // I could just have 1 kpi and it would be enough but since it's there, I will keep it.

        // We should have a different seed to make it better, but I don't want to update the test cases.
        $kpis1 = $this->generateDailyStats(1);
        $kpis2 = $this->generateDailyStats(2);
        $kpis3 = $this->generateDailyStats(3);
        $kpis4 = $this->generateDailyStats(4);

        $store1111 = $this->mergeDailyStats($kpis1, $kpis2);
        $store1112 = $this->mergeDailyStats($kpis3, $kpis4);
        $store0 = $this->mergeDailyStats($store1111, $store1112);

        Fixtures::add('InactiveFilteringCest', [
            'wp_users' => [
                [
                    'ID' => 8,
                    'user_login'          => 'testuser335',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser335',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser335',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1111,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 9,
                    'user_login'          => 'testuser336',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser336',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 0,
                    'user_alias'          => null,
                    'display_name'        => 'testuser336',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1111,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 10,
                    'user_login'          => 'testuser337',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser337',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 1,
                    'user_alias'          => null,
                    'display_name'        => 'testuser337',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1112,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ],
                [
                    'ID' => 11,
                    'user_login'          => 'testuser338',
                    'user_pass'           => '$P$B5VpoQvRMjV2QR6ArZxbZnh5rz1H4C1',
                    'user_nicename'       => 'testuser338',
                    'user_email'          => '<EMAIL>',
                    'user_url'            => '',
                    'user_registered'     => '2001-01-01 00:00:00',
                    'user_activation_key' => '',
                    'user_status'         => 0,
                    'user_alias'          => null,
                    'display_name'        => 'testuser338',
                    'description'         => null,
                    'photo'               => null,
                    'last_login'          => null,
                    'localization'        => null,
                    'feature'             => null,
                    'status'              => null,
                    'store'               => 1112,
                    'type'                => 'rep',
                    'commission_rate'     => 0.00,
                    'employee_id'         => null,
                    'group'               => 2,
                    'selling_mode'        => 1,
                    'isPhoto'             => 0,
                    'locked_at'           => null,
                    'locale'              => null,
                    'creation_source'     => 'invite',
                    'shop_feed'           => 0,
                ]
            ],
            'sf_store'        => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
            ],
            'sf_user_daily_stats' => [
                array_merge(
                    [
                        'user_id' => 8, // active user / active store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis1,
                ),
                array_merge(
                    [
                        'user_id' => 9, // inactive user / active store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis2,
                ),
                array_merge(
                    [
                        'user_id' => 10, // active user / inactive store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis3,
                ),
                array_merge(
                    [
                        'user_id' => 11, // inactive user / inactive store
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $kpis4,
                ),
            ],
            'sf_store_daily_stats' => [
                array_merge(
                    [
                        'store_id' => 0,
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'GLOBAL',
                    ],
                    $store0,
                ),
                array_merge(
                    [
                        'store_id' => 1111,
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $store1111,
                ),
                array_merge(
                    [
                        'store_id' => 1112,
                        'date' => '2023-07-20', // Not important since I request "all"
                        'timezone' => 'America/Montreal',
                    ],
                    $store1112,
                ),
            ],
            'sf_rep_transaction' => [
                [
                    'user_id'             => 8,
                    'retailer_rep_id'     => '631648',
                    'trx_id'              => '103224195',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1100.0000,
                    'trx_total'           => 1100.0000,
                    'status'              => 1,
                    'store_id'            => 1111,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 9,
                    'retailer_rep_id'     => '631649',
                    'trx_id'              => '103224196',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1200.0000,
                    'trx_total'           => 1200.0000,
                    'status'              => 1,
                    'store_id'            => 1111,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 10,
                    'retailer_rep_id'     => '631650',
                    'trx_id'              => '103224197',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1300.0000,
                    'trx_total'           => 1300.0000,
                    'status'              => 1,
                    'store_id'            => 1112,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
                [
                    'user_id'             => 11,
                    'retailer_rep_id'     => '631651',
                    'trx_id'              => '103224198',
                    'trx_date'            => '2023-07-20 04:00:00',
                    'trx_type'            => 'sale',
                    'trx_apply_total'     => 1400.0000,
                    'trx_total'           => 1400.0000,
                    'status'              => 1,
                    'store_id'            => 1112,
                    'received_date'       => '2023-07-20 23:22:03',
                    'customer_name'       => null,
                    'customer_email'      => null,
                    'ip'                  => null,
                    'user_agent'          => null,
                    'acquisition'         => '',
                    'currency'            => 'USD',
                    'attribution'         => null,
                    'customer_id'         => null,
                    'deactivated_user_id' => null,
                    'fingerprint'         => null,
                    'origin'              => 'tests',
                ],
            ],
            'sf_rep_transaction_detail' => [
                [
                    'trx_id'                 => '103224195',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1100.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224196',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1200.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224197',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1300.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
                [
                    'trx_id'                 => '103224198',
                    'trx_detail_id'          => '',
                    'trx_detail_apply_total' => 0.0000,
                    'trx_detail_total'       => 1400.0000,
                    'product_id'             => '0400097228455',
                    'quantity'               => '1',
                    'units'                  => 'each',
                    'sku'                    => null
                ],
            ]
        ]);
    }

    public function testExportCsvDownloadAllActivitySummaryAssociatesWithData()
    {
        Fixtures::add('testExportCsvDownloadAllActivitySummaryAssociatesWithData', [
            'sf_user_daily_stats' => [
                [
                    'user_id'   => 1,
                    'date'    => gmdate("Y-m-d"),
                    'timezone'  => 'America/Montreal',
                    'chat_request' => 22, // Legacy
                    'chat_answer' => 11, // Legacy
                    'chat_answer_rate_requests' => 22,
                    'chat_answer_rate_answers' => 11,
                ],
            ]
        ]);
    }

    public function testExportCsvDownloadAllActivitySummaryStoresWithData()
    {
        Fixtures::add('testExportCsvDownloadAllActivitySummaryStoresWithData', [
            'sf_store_daily_stats' => [
               array_merge($this->generateDailyStatsFixValue(0), [
                    'store_id'   => 1003,
                    'date'    => gmdate("Y-m-d"),
                    'timezone'  => 'America/Montreal',
                    'chat_request' => 22, // Legacy
                    'chat_answer' => 11, // Legacy
                    'chat_answer_rate_requests' => 22,
                    'chat_answer_rate_answers' => 11,
                ]),
            ]
            ]);
    }

    public function requestReplyTime()
    {
        Fixtures::add('requestReplyTime', [
            'sf_appointments' => [
                [
                    'ID' => 1,
                    'customer_id'       => 1,
                    'user_id'           => self::REGGIE_ID,
                    'event_type'        => 'In-Store',
                    'event_duration'    => 60,
                    'date'              => '2023-01-01 01:00:00',
                    'status'            => 'accepted',
                    'location'          => '',
                    'notes'             => '',
                    'uniq_id'           => 'SFID5942ee9e8f16f5.00220543',
                    'timezone'          => 'America/New_York',
                    'creation'          => Carbon::now()->setTime(14, 0)->toDateTimeString(),
                    'cust_meeting_link' => null,
                    'rep_meeting_link'  => null,
                    'enddate'           => '2023-01-01 02:00:00',
                    'rep_comment'       => null,
                    'phone'             => '******-508-2007',
                    'store_id'          => 1003,
                    'loc_id'            => null,
                    'category_id'       => null,
                    'sub_category_id'   => null,
                    'flagged'           => null,
                    'unattached_id'     => null,
                    'source_url'        => null,
                    'source_title'      => null,
                    'source'            => 'sidebar',
                    'updated_at'        => '2023-01-01 21:31:00',
                ],
            ],
            'sf_text_thread' => [
                [
                    'id' => '162967',
                    'user_id' => self::REGGIE_ID,
                    'user_phone_number' => '+12896778026',
                    'customer_id' => '3083442',
                    'customer_phone_number' => '+16134510269',
                    'is_read' => '1',
                    'is_valid' => '1',
                    'is_active' => '1',
                    'is_subscribed' => '1',
                    'created_at' => '2023-01-10 21:38:04',
                    'updated_at' => '2023-05-17 18:38:07',
                ],
            ],
            'sf_text_message' => [
                [
                    'id' => '1577023',
                    'text_thread_id' => '162967',
                    'provider_message_id' => 'SM771c40cbe6d34ada4e2ccf0013',
                    'direction' => 'inbound-service',
                    'body' => 'Your Appointment',
                    'status' => 'queued',
                    'is_active' => '0',
                    'created_at' => Carbon::now()->setTime(14, 15)->toDateTimeString(),
                    'updated_at' => '2023-04-19 15:36:14',
                    'source' => 'sf_appointments',
                    'source_id' => '1',
                    'sending_user_id' => 1,
                ],
                [
                    'id' => '1584913',
                    'text_thread_id' => '162967',
                    'provider_message_id' => 'SM771c40cbe6d34ada4e2ccf0014',
                    'direction' => 'outbound-api',
                    'body' => 'The appointment has been cancelled',
                    'status' => 'queued',
                    'is_active' => '0',
                    'created_at' => Carbon::now()->setTime(14, 30)->toDateTimeString(),
                    'updated_at' => '2023-04-19 15:36:14',
                    'source' => 'sf_appointments',
                    'source_id' => '1',
                    'sending_user_id' => 1,
                ],
            ],
        ]);
    }

    public function styledLinkEmails()
    {
        Fixtures::add('styledLinkEmails', [
            'sf_messages' => [
                [
                    'ID' => 100,
                    'user_id' =>  '1',
                    'owner_id' =>  '1',
                    'customer_id' =>  '-1',
                    'thread_id' =>  '100',
                    'from_type' =>  'user',
                    'from_email' =>  '<EMAIL>',
                    'from_name' =>  '<EMAIL>',
                    'request_type' => null,
                    'request_id' =>  '0',
                    'attachment' =>  '',
                    'type' =>  'message',
                    'date' =>  Carbon::now('UTC')->toDateTimeString(),
                    'status' =>  'read',
                    'category' =>  'sent',
                    'last_category' => null,
                    'message' =>  'Preston',
                    'title' =>  'New Arrivals from your favorite brand RVCA',
                    'products' =>  '4570810100',
                    'comment' =>  '',
                    'locale' =>  'en_US                    ',
                ],
                [
                    'ID' => 101,
                    'user_id' =>  '1',
                    'owner_id' =>  '1',
                    'customer_id' =>  '-1',
                    'thread_id' =>  '101',
                    'from_type' =>  'user',
                    'from_email' =>  '<EMAIL>',
                    'from_name' =>  '<EMAIL>',
                    'request_type' => null,
                    'request_id' =>  '0',
                    'attachment' =>  '',
                    'type' =>  'message',
                    'date' =>  Carbon::now('UTC')->toDateTimeString(),
                    'status' =>  'read',
                    'category' =>  'sent',
                    'last_category' => null,
                    'message' =>  'Preston',
                    'title' =>  'New Arrivals from your favorite brand RVCA',
                    'products' =>  '4570810100',
                    'comment' =>  '',
                    'locale' =>  'en_US                    ',
                ],
            ],
            'sf_mail_events' => [
                [
                    'sg_event_id' => 'ZGVsaXZlcmVkLTAtMTc2NjA3ODQteDBnd0lqOHpRXzJ2enRwZFFodFZJZy0w',
                    'sf_events_uniq_id' => '100',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'ZGVsaXZlcmVkLTAtMTc2NjA3ODQteDBnd0lqOHpRXzJ2enRwZFFodFZJZy0h',
                    'sf_events_uniq_id' => '101',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
            ],
            'sf_events' => [
                [
                    'type' => '113',
                    'date' => Carbon::now('UTC')->toDateTimeString(),
                    'source' => 'sf_message:100',
                    'uniq_id' => '100',
                    'user_id' => '1',
                    'customer_id' => '-1',
                    'attributes' => '10',
                    'satisfied' => '0',
                    'event_id' => '0',
                    'acknowledged' => '0',
                    'store_id' => '1885',
                ],
                [
                    'type' => '113',
                    'date' => Carbon::now('UTC')->toDateTimeString(),
                    'source' => 'sf_share_mail',
                    'uniq_id' => '101',
                    'user_id' => '1',
                    'customer_id' => '-1',
                    'attributes' => '10',
                    'satisfied' => '0',
                    'event_id' => '0',
                    'acknowledged' => '0',
                    'store_id' => '1885',
                ],
            ],
        ]);
    }

    public function testMarketingOutstandingTeamMode()
    {
        Fixtures::add('testMarketingOutstandingTeamMode', [
            'wp_users' => [
                $this->makeSellingModeUser(10000, 1111, 'rep', 'emp_10000', 'en_US', 1),
                $this->makeSellingModeUser(10001, 1111, 'rep', 'emp_10001', 'en_US', 0),
                $this->makeSellingModeUser(10002, 1112, 'rep', 'emp_10002', 'en_US', 1),
                $this->makeSellingModeUser(10003, 1112, 'rep', 'emp_10003', 'en_US', 0),
                $this->makeSellingModeUser(998, 1111, 'store', 'fakemall', 'en_US', 1),
                $this->makeSellingModeUser(999, 1112, 'store', 'fakemalll', 'en_US', 1),
            ],
            'sf_customer' => [
                $this->createContact(50, 998, '<EMAIL>', null, null, 0),
                $this->createContact(51, 999, '<EMAIL>', null, null, 0),
            ],
            'sf_store' => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'store_user_id'     => '998',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'store_user_id'     => '999',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemalll',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
            ],
            'sf_questions' => [
                [
                    'ID'          => 10,
                    'customer_id' => 50,
                    'user_id'     => 998,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1111,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
                [
                    'ID'          => 11,
                    'customer_id' => 51,
                    'user_id'     => 999,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1112,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ]
            ],
            'sf_messages' => [
                [
                    'user_id'      => 998,
                    'owner_id'     => 998,
                    'customer_id'  => 50,
                    'thread_id'    => 1,
                    'from_type'    => 'customer',
                    'from_name'    => 'john',
                    'request_type' => 'contact_me',
                    'request_id'   => 10,
                    'date'         => '2018-08-30 14:43:11',
                    'type'         => 'customer_request',
                    'status'       => 'unread',
                    'category'     => 'inbox',
                    'message'      => 22,
                ],
                [
                    'user_id'      => 999,
                    'owner_id'     => 999,
                    'customer_id'  => 51,
                    'thread_id'    => 1,
                    'from_type'    => 'customer',
                    'from_name'    => 'john',
                    'request_type' => 'contact_me',
                    'request_id'   => 11,
                    'date'         => '2018-08-30 14:43:11',
                    'type'         => 'customer_request',
                    'status'       => 'unread',
                    'category'     => 'inbox',
                    'message'      => 22,
                ],
            ]
        ]);
    }

    public function testMarketingOutstandingRepMode()
    {
        Fixtures::add('testMarketingOutstandingRepMode', [
            'wp_users' => [
                $this->makeSellingModeUser(10000, 1111, 'rep', 'emp_10000', 'en_US', 1),
                $this->makeSellingModeUser(10001, 1111, 'rep', 'emp_10001', 'en_US', 0),
                $this->makeSellingModeUser(10002, 1112, 'rep', 'emp_10002', 'en_US', 1),
                $this->makeSellingModeUser(10003, 1112, 'rep', 'emp_10003', 'en_US', 0),
            ],
            'sf_customer' => [
                $this->createContact(50, 10000, '<EMAIL>', null, null, 0),
                $this->createContact(51, 10001, '<EMAIL>', null, null, 0),
                $this->createContact(52, 10002, '<EMAIL>', null, null, 0),
                $this->createContact(53, 10003, '<EMAIL>', null, null, 0),
                $this->createContact(100, 0, '<EMAIL>', null, null, 0),
                $this->createContact(101, 0, '<EMAIL>', null, null, 0),
            ],
            'sf_store'        => [
                [
                    'store_id'          => '1111',
                    'name'              => 'Fake Mall',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1111',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
                [
                    'store_id'          => '1112',
                    'name'              => 'Fake Mall Inactive',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'timezone'          => 'America/Montreal',
                    'shame_type'        => 'store',
                    'retailer_store_id' => 'fakemall',
                    'sf_identifier'     => 'test-fake-mall-1112',
                    'image_url'         => 'https://salesfloor.net/wp-content/uploads/2016/11/salesfloor.png',
                ],
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1111,
                    'is_default' => 1
                ],
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1112,
                    'is_default' => 1
                ],
            ],
            'sf_store_i18n' => [
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1111,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
                [
                    'locale'   => 'fr_CA',
                    'store_id' => 1112,
                    'name'     => 'Fake Mall FR',
                    'country'           => 'ADMIN_CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                ],
            ],
            'sf_questions' => [
                [
                    'customer_id' => 100,
                    'user_id'     => 0,
                    'uniq_id'     => 'SF12345',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1111,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
                [
                    'customer_id' => 101,
                    'user_id'     => 0,
                    'uniq_id'     => 'SF123456',
                    'creation'    => date('Y-m-d H:i:s', strtotime('-4 hour')),
                    'store_id'    => 1112,
                    'channel'     => 'email',
                    'question'    => 'junk',
                ],
            ]
        ]);
    }

    public function testChatAvailabilityRateRangeWeek()
    {
        $data = [
            'sf_user_daily_stats' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-22',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-23',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-24',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-25',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-26',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeek', $data);
    }

    public function testChatAvailabilityRateRangeWeekAndWeekend()
    {
        $data = [
            'sf_user_daily_stats' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-20',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-21',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-22',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-23',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-24',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-25',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-26',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ],
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeekAndWeekend', $data);
    }

    public function testChatAvailabilityRateRangeWeekendEmptyMetrics()
    {
        $data = [
            'sf_user_daily_stats' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-20',
                    'timezone' => 'America/Montreal',
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-21',
                    'timezone' => 'America/Montreal',
                ],
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeekendEmptyMetrics', $data);
    }

    public function testChatAvailabilityRateRangeWeekendWithMetrics()
    {
        $blank = $this->generateDailyStatsFixValue(0);

        $data = [
            'sf_user_daily_stats' => [
                array_merge($blank, [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-20',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ]),
                array_merge($blank, [
                    'user_id' => self::REGGIE_ID,
                    'date' => '2024-01-21',
                    'timezone' => 'America/Montreal',
                    'number_seconds_available' => 1000,
                ]),
            ]
        ];

        Fixtures::add('testChatAvailabilityRateRangeWeekendWithMetrics', $data);
    }

    public function addGroupTasks()
    {
        Carbon::setTestNow();
        $now = Carbon::now('UTC');
        $fourMonthAgo = $now->copy()->subMonths(4);
        Fixtures::add('GroupTasks', [
            'wp_users'  => [
                [
                    'ID'              => 255,
                    'user_login'      => 'tests_19063',
                    'user_pass'       => 'password',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'samuel',
                    'store'           => 1003,
                    'type'            => 'rep',
                    'employee_id'     => 'test151515',
                    'group'           => 2,
                    'selling_mode'    => 1,
                    'locale'          => 'en_US',
                    'creation_source' => 'invite'
                ]
            ],
            'sf_store' => [
                [
                    'store_id'          => 1004,
                    'name'              => 'Store',
                    'timezone'          => 'America/New_York',
                    'sf_identifier'     => 'store',
                    'retailer_store_id' => 'test515151',
                    'store_user_id'     => null,
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                ]
            ],
            'sf_store_locale' => [
                [
                    'locale'     => 'en_US',
                    'store_id'   => 1004,
                    'is_default' => 1
                ],
            ],
            'sf_group_tasks' => [
                [
                    'id' => 1,
                    'store_id' => '1003',
                    'title' => 'Test Title1',
                    'details' => 'Test Detail1',
                    'start_date' => null,
                    'reminder_date' => '2023-03-01 00:01:02',
                    'auto_dismiss_date' => '2024-01-01 00:01:02',
                    'status' => 'unresolved',
                    'customer_id' => '1234',
                    'preferred_user_id' => '255',
                    'suggested_subject_line' => 'Test Subject Line1',
                    'suggested_copy' => 'Test Suggested Copy1',
                    'created_at' => $fourMonthAgo->toDateTimeString(),
                ],
                [
                    'id' => 2,
                    'store_id' => '1003',
                    'title' => 'Test Title2',
                    'details' => 'Test Detail2',
                    'start_date' => $fourMonthAgo->toDateTimeString(),
                    'reminder_date' => '2023-03-01 00:01:02',
                    'auto_dismiss_date' => '2024-01-01 00:01:02',
                    'status' => 'unresolved',
                    'customer_id' => '1234',
                    'preferred_user_id' => '255',
                    'suggested_subject_line' => 'Test Subject Line2',
                    'suggested_copy' => 'Test Suggested Copy',
                    'created_at' => $fourMonthAgo->toDateTimeString(),
                ],
                [
                    'id' => 3,
                    'store_id' => '1003',
                    'title' => 'Test Title3',
                    'details' => 'Test Detail3',
                    'start_date' => $fourMonthAgo->toDateTimeString(),
                    'reminder_date' => '2023-03-01 00:01:02',
                    'auto_dismiss_date' => '2024-01-01 00:01:02',
                    'status' => 'unresolved',
                    'customer_id' => '1234',
                    'preferred_user_id' => '255',
                    'suggested_subject_line' => 'Test Subject Line3',
                    'suggested_copy' => 'Test Suggested Copy',
                    'created_at' => $fourMonthAgo->toDateTimeString(),
                ]
            ],
            'sf_group_task_activities' => [
                // Task 1
                [
                    'id' => 1,
                    'group_task_id' => 1,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_EMAIL,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 2,
                    'group_task_id' => 1,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 3,
                    'group_task_id' => 1,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_RESOLVE,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                // Task 2
                [
                    'id' => 4,
                    'group_task_id' => 2,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_EMAIL,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 5,
                    'group_task_id' => 2,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_RESOLVE,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 6,
                    'group_task_id' => 2,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                // Task 3
                [
                    'id' => 7,
                    'group_task_id' => 3,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_EMAIL,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 8,
                    'group_task_id' => 3,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_AUTO_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
                [
                    'id' => 9,
                    'group_task_id' => 3,
                    'user_id' => 1,
                    'action' => GroupTaskActivity::ACTION_DISMISS,
                    'details' => 'Test Activity Details',
                    'created_at' => $now->toDateTimeString(),
                ],
            ],
        ]);
    }

    public function addAvgInitResponseFixtures()
    {
        $i = 0;
        $data = [
            // Questions must be generated first since we are doing the incremental time
            // This is so the initial contact has a time < response time
            'sf_questions' => [
                [
                    "ID" => 62308,
                    "customer_id" => 6396135,
                    "user_id" => 1,
                    "uniq_id" => "SFID65b30397824663.67917157",
                    "question" => "chanel foundation",
                    "topic" => "",
                    "timezone" => "UTC",
                    "creation" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                    "phone" => "+17808855555",
                    "status" => "",
                    "loc_id" => 75589,
                    "category_id" => null,
                    "sub_category_id" => null,
                    "flagged" => null,
                    "store_id" => 1003,
                    "unattached_id" => null,
                    "source_url" => "https://services.shoppersdrugmart.ca/en_US/adrianams",
                    "source_title" => "Shop with Adriana Martinez Sanchez - Shoppers Drug Mart",
                    "locale" => "en_US",
                    "channel" => "text",
                    "source" => "storefront",
                    "chat_handoff" => 0
                ],
                [
                    "ID" => 62328,
                    "customer_id" => 6396135,
                    "user_id" => 1,
                    "uniq_id" => "SFID65b3cbc295f538.40504222",
                    "question" => "best mup spray",
                    "topic" => "",
                    "timezone" => "UTC",
                    "creation" => "2024-01-26 15:12:02",
                    "phone" => "+17808855555",
                    "status" => "",
                    "loc_id" => 75610,
                    "category_id" => null,
                    "sub_category_id" => null,
                    "flagged" => null,
                    "store_id" => 1003,
                    "unattached_id" => null,
                    "source_url" => "https://services.shoppersdrugmart.ca/en_US/adrianams",
                    "source_title" => "Shop with Adriana Martinez Sanchez - Shoppers Drug Mart",
                    "locale" => "en_US",
                    "channel" => "text",
                    "source" => "storefront",
                    "chat_handoff" => 0
                ]
            ],
            'sf_messages' => [
                 [
                     "ID" => 5656854,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5656854,
                     "from_type" => "customer",
                     "from_email" => "",
                     "from_name" => "martina ",
                     "request_type" => "contact_me",
                     "request_id" => 62308,
                     "attachment" => "",
                     "type" => "customer_request",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "inbox",
                     "last_category" => null,
                     "message" => "22",
                     "title" => "Contact Me/Email me Request",
                     "products" => "",
                     "comment" => "chanel foundation",
                     "locale" => "en_US"
                 ],
                 [
                     "ID" => 5656859,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5656859,
                     "from_type" => "user",
                     "from_email" => "",
                     "from_name" => "",
                     "request_type" => "contact_me",
                     "request_id" => 62308,
                     "attachment" => "",
                     "type" => "message",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "sent",
                     "last_category" => null,
                     "message" => "You replied to this request via SMS",
                     "title" => "system-message",
                     "products" => "",
                     "comment" => null,
                     "locale" => "en_US"
                 ],
                 [
                     "ID" => 5660175,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5660175,
                     "from_type" => "customer",
                     "from_email" => "",
                     "from_name" => "Martina ",
                     "request_type" => "contact_me",
                     "request_id" => 62328,
                     "attachment" => "",
                     "type" => "customer_request",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "inbox",
                     "last_category" => null,
                     "message" => "22",
                     "title" => "Contact Me/Email me Request",
                     "products" => "",
                     "comment" => "best mup spray",
                     "locale" => "en_US"
                 ],
                 [
                     "ID" => 5660176,
                     "user_id" => 1,
                     "owner_id" => 1,
                     "customer_id" => 6396135,
                     "thread_id" => 5660176,
                     "from_type" => "user",
                     "from_email" => "",
                     "from_name" => "",
                     "request_type" => "contact_me",
                     "request_id" => 62328,
                     "attachment" => "",
                     "type" => "message",
                     "date" => date('Y-m-d H:i:s', strtotime('-6 hour + ' . (++$i) . ' mins')),
                     "status" => "resolved",
                     "category" => "sent",
                     "last_category" => null,
                     "message" => "You replied to this request via SMS",
                     "title" => "system-message",
                     "products" => "",
                     "comment" => null,
                     "locale" => "en_US"
                 ]
            ],
        ];
        Fixtures::add('AvgResponseTimeOutgoingSms', $data);
    }

    public function addGroupedProducts()
    {
        Carbon::setTestNow();
        $now = Carbon::now('UTC');

        Fixtures::add('GroupedProducts', [
            'wp_users'  => [
                [
                    'ID'              => 123,
                    'user_login'      => 'tests_123',
                    'user_pass'       => 'password',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'jona',
                    'store'           => 1003,
                    'type'            => 'rep',
                    'employee_id'     => 'test151515',
                    'group'           => 2,
                    'selling_mode'    => 1,
                    'locale'          => 'en_US',
                    'creation_source' => 'invite'
                ]
            ],

            'sf_events' => [
                [
                    'id' => 1,
                    'uniq_id' => 'SF01',
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_EMAIL,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '4', // number of shared products
                    'event_id' => '1111',
                    'store_id' => 1003
                ],
                [
                    'id' => 2,
                    'uniq_id' => 'SF02',
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_EMAIL,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '2',
                    'event_id' => '2222',
                    'store_id' => 1003
                ],
                [
                    'id' => 3,
                    'uniq_id' => 333333,
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_SMS,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '6',
                    'event_id' => '3333',
                    'store_id' => 1003
                ],
                [
                    'id' => 4,
                    'uniq_id' => 4444444,
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_SMS,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '4',
                    'event_id' => '3333',
                    'store_id' => 1003
                ],
                [
                    'id' => 5,
                    'uniq_id' => '4444',
                    'type' => Event::SF_EVENT_STYLED_LINK_SHARED_EMAIL,
                    'date' => $now->toDateTimeString(),
                    'source' => 'grouped_products',
                    'user_id' => 123,
                    'attributes' => '5',
                    'event_id' => '4444',
                    'store_id' => 1003
                ],
            ],

            'sf_mail_events' => [
                [
                    'sg_event_id' => 'ZGVsaXZlcmVkLlkj90o34ujldkfgj3490j',
                    'sf_events_uniq_id' => 'SF01',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'fdkslfklh3498fjkdhs',
                    'sf_events_uniq_id' => 'SF01',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'klewdjspfldskjfklj34oiudklfj',
                    'sf_events_uniq_id' => 'SF02',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => '4ulvjlk349ufhj',
                    'sf_events_uniq_id' => 'SF03',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
                [
                    'sg_event_id' => 'j345980uvohjkklhj',
                    'sf_events_uniq_id' => '4444',
                    'email' => '<EMAIL>',
                    'timestamp_marked' => '2024-05-20 15:49:53',
                    'bounce' => '0',
                    'click' => '0',
                    'deferred' => '0',
                    'delivered' => '1',
                    'dropped' => '0',
                    'open' => '0',
                    'processed' => '0',
                    'spamreport' => '0',
                    'unsubscribe' => '0',
                    'group_unsubscribe' => '0',
                    'group_resubscribe' => '0',
                    'raw_event' => '{"category":["Compose"]}',
                ],
            ],

            'sf_messages'  => [
                [
                    "ID"            => '4444',
                    "user_id"       => '1',
                    "owner_id"      => '1',
                    "customer_id"   => '1',
                    "thread_id"     => '1',
                    "from_type"     => 'user',
                    "from_email"    => '<EMAIL>',
                    "from_name"     => 'hummm',
                    "request_type"  => 'contact_me',
                    "request_id"    => '1',
                    "attachment"    => '',
                    "type"          => 'customer_request',
                    "date"          => date('Y-m-d H:i:s', strtotime('-2 hour')),
                    "status"        => 'customer-support',
                    "category"      => 'inbox',
                    "last_category" => null,
                    "title"         => 'Contact Me Request',
                    "products"      => '',
                    "comment"       => 'test',
                    "locale"        => 'en_US',
                ],
            ],
        ],);
    }

    public function testFlaggedChatConversations()
    {
        Fixtures::add('testFlaggedChatConversations', [
            'sf_chat_flagged' =>
                [
                    [
                        'user_id' => self::REGGIE_ID,
                        'chat_thread_identifier' => '1234',
                        'customer_fingerprint' => '1234',
                        'customer_ip_address' => '*******',
                        'customer_user_agent' => 'test',
                        'chat_contents' => 'wtf',
                        'created_at' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
                    ],
                    [
                        'user_id' => 2, // different user, same store.
                        'chat_thread_identifier' => '12345',
                        'customer_fingerprint' => '1234',
                        'customer_ip_address' => '*******',
                        'customer_user_agent' => 'test',
                        'chat_contents' => 'wtf',
                        'created_at' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
                    ],
                    [
                        'user_id' => '1324123416', // this is an invalid, to make sure not part of the kpis
                        'chat_thread_identifier' => '123456',
                        'customer_fingerprint' => '1234',
                        'customer_ip_address' => '*******',
                        'customer_user_agent' => 'test',
                        'chat_contents' => 'wtf',
                        'created_at' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
                    ]
                ]
        ]);

        Fixtures::add('FlaggedChatConversations', [
            'wp_users' => [$this->makeSellingModeUser(136163, 2003)],
            'sf_chat_flagged' => [
                [
                    'user_id' => self::REGGIE_ID,
                    'chat_thread_identifier' => "random1234"
                ],
                [
                    'user_id' => self::REGGIE_ID,
                    'chat_thread_identifier' => "random12345"
                ],
                [
                    'user_id' => 136163,
                    'chat_thread_identifier' => "random123456"
                ]
            ],
        ]);
    }

    public function abandonedStats()
    {
        Fixtures::add('LiveChatEventsWithAbandoned', [
            'wp_users' => [$this->makeSellingModeUser(1963, 2003)],
            'sf_events' => [
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-not-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                    'attributes'  => '',
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-not-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 45,
                    'date'        => date('Y-m-d H:00:20', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-not-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1963,
                    'customer_id' => 0,
                    'store_id'    => 2003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 1,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 44,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
                [
                    'type'        => 87,
                    'date'        => date('Y-m-d H:00:00', strtotime('-3 hour')),
                    'uniq_id'     => 'CPD-951-request-abandoned',
                    'user_id'     => 2,
                    'customer_id' => 0,
                    'store_id'    => 1003,
                ],
            ],
        ]);
    }

    public function testAggregateDailyStatsActionId()
    {
        Fixtures::add('testAggregateDailyStatsActionId', [
            'sf_sidebar_event_log_20240201' => [
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369203,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_VIEW,
                    'action_id'   => null,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369204,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => 1,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
                [
                    'timestamp'   => 1553106692,
                    'fingerprint' => 37687365369204,
                    'action'      => SidebarEventQueueService::SF_EVENT_CONTEXTUAL_WIDGET_MOBILE_CLICK,
                    'action_id'   => 2,
                    'created_at'  => '2024-02-08 01:46:10',
                ],
            ]
        ]);
    }

    public function testAggregateDailyStatsMenu()
    {
        $date = Carbon::now('UTC')->subDays(1)->format('Y-m-d');

        Fixtures::add('testAggregateDailyStatsMenu', [
            SidebarEventLog::getEventTable($date) =>
                [
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    // Desktop
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],

                    // Mobile
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_MENU',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_MENU_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_SUPPORT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                ]
        ]);
    }

    public function testAggregateDailyStatsRequests()
    {
        $date = Carbon::now('UTC')->subDays(1)->format('Y-m-d');

        Fixtures::add('testAggregateDailyStatsRequests', [
            SidebarEventLog::getEventTable($date) =>
                [
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_VIEW',
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    // Desktop
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],

                    // Mobile
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_LIVECHAT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_MESSAGE],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                    [
                        'timestamp' => 1553106692, // We rely on created_at not timestamp for kpis aggregation
                        'fingerprint' => 16455166539999,
                        'action' => 'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        'action_id' => SidebarEventQueueService::SF_EVENT_CONNECT2_REQUESTS_SUB_ACTION[SidebarEventQueueService::SF_EVENT_CONNECT2_APPOINTMENT],
                        'created_at' => Carbon::now('UTC')->subDays(1)->format('Y-m-d H:i:s'),
                    ],
                ]
        ]);
    }

    public function testExportCsvSidebarMetrics()
    {
        Fixtures::add('testExportCsvSidebarMetrics', [
            'sf_sidebar_daily_stats' => [
                $this->generateSidebarMetrics(Carbon::now('UTC') ->subDays(1)->format('Y-m-d')),
                $this->generateSidebarMetrics(Carbon::now('UTC') ->subDays(2)->format('Y-m-d')),
            ],
        ]);
    }

    private function generateSidebarMetrics(string $date)
    {
        $fields = [
            'date',
            'sidebar_mobile_total_view',
            'sidebar_mobile_total_click',
            'sidebar_mobile_total_minimize',
            'sidebar_mobile_total_maximize',
            'sidebar_mobile_total_tagline_minimize',
            'sidebar_desktop_total_view',
            'sidebar_desktop_total_click',
            'sidebar_desktop_total_minimize',
            'sidebar_desktop_total_maximize',
            'sidebar_mobile_unique_view',
            'sidebar_mobile_unique_click',
            'sidebar_mobile_unique_minimize',
            'sidebar_mobile_unique_maximize',
            'sidebar_mobile_unique_tagline_minimize',
            'sidebar_desktop_unique_view',
            'sidebar_desktop_unique_click',
            'sidebar_desktop_unique_minimize',
            'sidebar_desktop_unique_maximize',
            'contextual_widget_desktop_view',
            'contextual_widget_desktop_unique_view',
            'contextual_widget_desktop_click',
            'contextual_widget_desktop_unique_click',
            'contextual_widget_mobile_view',
            'contextual_widget_mobile_unique_view',
            'contextual_widget_mobile_click',
            'contextual_widget_mobile_unique_click',
            'sidebar_desktop_total_menu_livechat',
            'sidebar_desktop_total_menu_message',
            'sidebar_desktop_total_menu_appointment',
            'sidebar_desktop_total_menu_support',
            'sidebar_mobile_total_menu_livechat',
            'sidebar_mobile_total_menu_message',
            'sidebar_mobile_total_menu_appointment',
            'sidebar_mobile_total_menu_support',
            'sidebar_desktop_unique_menu_livechat',
            'sidebar_desktop_unique_menu_message',
            'sidebar_desktop_unique_menu_appointment',
            'sidebar_desktop_unique_menu_support',
            'sidebar_mobile_unique_menu_livechat',
            'sidebar_mobile_unique_menu_message',
            'sidebar_mobile_unique_menu_appointment',
            'sidebar_mobile_unique_menu_support',
            'sidebar_desktop_total_requests_livechat',
            'sidebar_desktop_total_requests_message',
            'sidebar_desktop_total_requests_appointment',
            'sidebar_mobile_total_requests_livechat',
            'sidebar_mobile_total_requests_message',
            'sidebar_mobile_total_requests_appointment',
            'sidebar_desktop_unique_requests_livechat',
            'sidebar_desktop_unique_requests_message',
            'sidebar_desktop_unique_requests_appointment',
            'sidebar_mobile_unique_requests_livechat',
            'sidebar_mobile_unique_requests_message',
            'sidebar_mobile_unique_requests_appointment',
        ];

        return array_combine(
            $fields,
            array_merge([
                $date,
            ], range(0, count($fields) - 2))
        );
    }
}
