<?php

namespace SF\functional\Reporting;

use Codeception\Util\Fixtures;
use Codeception\Util\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

/**
 * @group database_transaction
 */
class ProcessorMarketingOutstandingCest extends BaseFunctional
{
    private $db;
    private $dateFrom;
    private $dateTo;

    public function _before($I)
    {
        parent::_before($I);

        $this->db       = $this->app['repositories.mysql'];
        $this->dateFrom = date('Y-m-d H:i:s', strtotime('-12 hour'));
        $this->dateTo   = gmdate('Y-m-d H:i:s');
    }

    ///////////////
    /// Rep mode
    ///

    public function testMarketingOutstandingAllRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - All stores/Users");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingAllRepModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - One store");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreRepModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreRepModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreAndOneUserRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - one store / one user");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(10000, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserRepModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10001, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserRepModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10002, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserRepModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10003, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserRepModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - inactive store excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveStoresRepModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveStoresRepModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveUsersRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - inactive users excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveUsersRepModeResponse'), json_decode($response, true));
    }

    ///////////////
    /// Team mode
    ///

    public function testMarketingOutstandingAllTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - All stores/Users");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingAllTeamModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - One store");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreTeamModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreTeamModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreAndOneUserTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - one store / one user");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(10000, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserTeamModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10001, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserTeamModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10002, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserTeamModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10003, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $this->validateArray(Fixtures::get('testMarketingOutstandingOneStoreAndOneUserTeamModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - inactive store excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveStoresTeamModeResponse'), json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveStoresTeamModeResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresTeamModeSelected(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - inactive store excluded - inactive store selected");

        // Currently, in the UI we cannot select this scenario but keeping it just in case we can.

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 1112, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 0 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveStoresTeamModeSelectedResponse'), json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveUsersTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - inactive users excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $this->validateArray(Fixtures::get('testMarketingOutstandingInactiveUsersTeamModeResponse'), json_decode($response, true));
    }

    ////////////
    /// Mocked Class section
    ///

    private function getMockedReporting($users, $stores, string $period, $active, array $type)
    {
        global $SF_19242_WORKAROUND;
        $SF_19242_WORKAROUND = false;

        // default for fetchResultsFrom is backfill-only
        return Stub::construct(
            \Reporting::class,
            [
                [
                    "user" => $users,
                    "store" => $stores,
                    "exclude_inactive" => $active,
                    "type" => $type,
                    "timezone" => "America/New_York",
                    "period" => $period,
                ],
                null,
                $this->app
            ]
        );
    }
}
