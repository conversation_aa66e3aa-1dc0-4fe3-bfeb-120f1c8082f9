<?php

namespace SF\functional\Reporting;

use Carbon\Carbon;
use SF\FunctionalTester;
use Codeception\Util\Stub;
use SF\functional\BaseFunctional;

/**
 * @group database_transaction
 */
class ProcessorMarketingOutstandingCest extends BaseFunctional
{
    ///////////////
    /// Rep mode
    ///

    public function testMarketingOutstandingAllRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - All stores/Users");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - One store");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreAndOneUserRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - one store / one user");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(10000, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10001, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10002, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10003, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - inactive store excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveUsersRepMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - rep mode - inactive users excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingRepMode');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    ///////////////
    /// Team mode
    ///

    public function testMarketingOutstandingAllTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - All stores/Users");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - One store");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingOneStoreAndOneUserTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - one store / one user");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(10000, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10001, 1111, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10002, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(10003, 1112, 'all', [], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding request
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - inactive store excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 1111, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 1 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveStoresTeamModeSelected(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - inactive store excluded - inactive store selected");

        // Currently, in the UI we cannot select this scenario but keeping it just in case we can.

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 1112, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 0 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    public function testMarketingOutstandingInactiveUsersTeamMode(FunctionalTester $I)
    {
        $I->wantTo("Test kpis marketing - outstanding - team mode - inactive users excluded");

        $this->insertFixtureGroup($I, 'testMarketingOutstandingTeamMode');

        $this->app['configs']['retailer.storepage_mode'] = true;

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        // 2 outstanding requests
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->validateResponse($jsonFile, json_decode($response, true));
    }

    ////////////
    /// Mocked Class section
    ///

    private function getMockedReporting($users, $stores, string $period, $active, array $type)
    {
        global $SF_19242_WORKAROUND;
        $SF_19242_WORKAROUND = false;

        // default for fetchResultsFrom is backfill-only
        return Stub::construct(
            \Reporting::class,
            [
                [
                    "user" => $users,
                    "store" => $stores,
                    "exclude_inactive" => $active,
                    "type" => $type,
                    "timezone" => "America/New_York",
                    "period" => $period,
                ],
                null,
                $this->app
            ]
        );
    }

    private function validateResponse(string $jsonFile, array $data)
    {
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($data, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $now = Carbon::now('UTC')->endOfDay();

        foreach (['sales_total_dates', 'sales_dates', 'marketing_dates'] as $dateType) {
            foreach (['backfill', 'query'] as $queryType) {
                if ($expected['data']['user']['all'][$dateType][$queryType]['to'] !== null) {
                    $expected['data']['user']['all'][$dateType][$queryType]['to'] = $now->toDateTimeString();
                }
            }
        }
        $this->validateArray($expected, $data);
    }
}
