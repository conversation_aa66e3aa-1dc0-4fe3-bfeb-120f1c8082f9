<?php

namespace SF\functional\Reporting;

use Carbon\Carbon;
use SF\FunctionalTester;
use Salesfloor\Services\Reporting\Processor;

class ProcessorServiceCest extends \SF\functional\BaseFunctional
{
    /** @group database_transaction */
    public function testFindValidUsers(FunctionalTester $I)
    {
        /** @var Processor $processor */
        $processor = $this->app['reporting.processor'];
        $users = $processor->getValidUsersForDate(0);
        $I->assertCount(7, $users);
        foreach ($users as $user) {
            $I->assertEquals(1003, $user['store_id']);
            $I->assertEquals('Fake Mall', $user['name']);
            $I->assertEquals('America/Montreal', $user['timezone']);
            $I->assertEquals(date('Y-m-d'), $user['dateToCalculate']);
            $I->assertArrayHasKey('user_id', $user);
            $I->assertArrayHasKey('user_login', $user);
        }
    }

    /** @group database_transaction */
    public function testFindValidUsersOnDayUserSwitchedStoresMultipleTimes(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'MultipleStoreSwitchesSameDay');

        /** @var Processor $processor */
        $processor = $this->app['reporting.processor'];
        $users = $processor->getValidUsersForDate(0);
        $I->assertCount(8, $users);

        foreach ($users as $user) {
            $isChangedUser = $user['user_id'] == 19063;
            $I->assertEquals($isChangedUser ? 777 : 1003, $user['store_id']);
            $I->assertEquals($isChangedUser ? 'UTC Store' : 'Fake Mall', $user['name']);
            $I->assertEquals($isChangedUser ? 'Etc/UTC' : 'America/Montreal', $user['timezone']);
        }
    }

    /** @group database_transaction */
    public function testFindValidUsersGapsBetweenStoreSwitches(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'MultipleStoreSwitchesOverLongPeriod');

        $daysBack = 10;

        /** @var Processor $processor */
        $processor = $this->app['reporting.processor'];
        $users = $processor->getValidUsersForDate($daysBack);
        $I->assertCount(8, $users);


        $changedUsers = array_filter($users, function ($user) {
            return $user['user_id'] == 19063;
        });
        $I->assertCount(1, $changedUsers);
        $changedUser = array_shift($changedUsers);

        $I->assertEquals(2003, $changedUser['store_id']);
        $I->assertEquals('Other Mall', $changedUser['name']);
        $I->assertEquals('America/Montreal', $changedUser['timezone']);
        $I->assertEquals('user_19063', $changedUser['user_login']);
        $I->assertEquals($this->dateInUtc("-{$daysBack} days"), $changedUser['dateToCalculate']);

        $daysBack = 25;
        $users = $processor->getValidUsersForDate($daysBack);
        $I->assertCount(8, $users);

        $changedUsers = array_filter($users, function ($user) {
            return $user['user_id'] == 19063;
        });
        $I->assertCount(1, $changedUsers);
        $changedUser = array_shift($changedUsers);

        $I->assertEquals(1003, $changedUser['store_id']);
        $I->assertEquals('Fake Mall', $changedUser['name']);
        $I->assertEquals('America/Montreal', $changedUser['timezone']);
        $I->assertEquals('user_19063', $changedUser['user_login']);
        $I->assertEquals($this->dateInUtc("-{$daysBack} days"), $changedUser['dateToCalculate']);
    }

    protected function dateTimeInUtc($timeDescription)
    {
        return Carbon::parse($timeDescription, 'UTC')->toDateTimeString();
    }

    protected function dateInUtc($timeDescription)
    {
        return Carbon::parse($timeDescription, 'UTC')->toDateString();
    }
}
