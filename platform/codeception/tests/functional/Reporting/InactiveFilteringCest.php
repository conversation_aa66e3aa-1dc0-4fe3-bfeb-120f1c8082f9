<?php

namespace SF\functional\Reporting;

use Codeception\Util\Fixtures;
use Codeception\Util\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

/**
 * Keep in mind that the daily stats value are not "accurate" since some (rep vs store) can overlap (e.g chat related kpis)
 *
 * The idea here is only to test if inactive filtering is working as expected.
 *
 * @group database_transaction
 */
class InactiveFilteringCest extends BaseFunctional
{
    public function testFilteringGetSalesTotalStores(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1
        ], [
            'get-sales-total'
        ]);

        $response = ob_get_clean();

        // The main one to test is the "total". user_id is undefined.
        // If this test failed in the future because of user_id, we can probably just remove it.
        // No idea why sales_total return a user_id, probably a default of implementation.

        // 2 valid users : 2300$
        $this->validateArray(Fixtures::get('testFilteringGetSalesTotalStoresResponse'), json_decode($response, true));
    }

    public function testFilteringGetSalesTotalStoresUsers(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
            'user' => 1,
        ], [
            'get-sales-total'
        ]);

        $response = ob_get_clean();

        // 1 valid user = 1100$
        $this->validateArray(Fixtures::get('testFilteringGetSalesTotalStoresUsersResponse'), json_decode($response, true));
    }

    public function testFilteringGetSalesTotalUsers(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1,
        ], [
            'get-sales-total'
        ]);

        $response = ob_get_clean();

        // 2 valid user = 2400$
        $this->validateArray(Fixtures::get('testFilteringGetSalesTotalUsersResponse'), json_decode($response, true));
    }

    public function testFilteringGetSalesTotal(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
        ], [
            'get-sales-total'
        ]);

        $response = ob_get_clean();

        // 4 valid user = 5000$
        $this->validateArray(Fixtures::get('testFilteringGetSalesTotalResponse'), json_decode($response, true));
    }

    public function testFilteringGetSalesStores(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1
        ], [
            'get-sales'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetSalesStoresResponse'), json_decode($response, true));
    }

    public function testFilteringGetSalesStoresUsers(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
            'user' => 1,
        ], [
            'get-sales'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetSalesStoresUsersResponse'), json_decode($response, true));
    }

    public function testFilteringGetSalesUsers(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1
        ], [
            'get-sales'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetSalesUsersResponse'), json_decode($response, true));
    }

    public function testFilteringGetSales(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
        ], [
            'get-sales'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetSalesResponse'), json_decode($response, true));
    }


    // This is used by kpis and dashboard (getkpis) section.
    public function testFilteringGetMarketingStores(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingStoresResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingStoresUsers(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'store' => 1,
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingStoresUsersResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingUsers(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingUsersResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketing(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, 0, 'all', [
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithUsersSelectedEnabled(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting([8], 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingUsersWithUsersSelectedEnabledResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithUsersSelectedDisabled(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting([9], 0, 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->debug($response);

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingUsersWithUsersSelectedDisabledResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithStoreSelectedEnabled(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, "1111", 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingUsersWithStoreSelectedEnabledResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingUsersWithStoreSelectedDisabled(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting(0, "1112", 'all', [
            'user' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingUsersWithStoreSelectedDisabledResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserEnabledFromEnabledStore(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting([8], 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingStoresWithUserEnabledFromEnabledStoreResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserDisabledFromEnabledStore(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting([9], 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingStoresWithUserDisabledFromEnabledStoreResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserEnabledFromDisabledStore(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting([10], 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingStoresWithUserEnabledFromDisabledStoreResponse'), json_decode($response, true));
    }

    public function testFilteringGetMarketingExcludingStoresWithUserDisabledFromDisabledStore(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'InactiveFilteringCest');

        ob_start();
        $this->getMockedReporting([11], 0, 'all', [
            'store' => 1,
        ], [
            'get-marketing'
        ]);

        $response = ob_get_clean();

        $this->validateArray(Fixtures::get('testFilteringGetMarketingExcludingStoresWithUserDisabledFromDisabledStoreResponse'), json_decode($response, true));
    }

    /**
     * GetKpis is using reporting engine directly, no idea why but this will be tested in the rest section
     *
     * @return void
     */
    private function getMockedReporting($users, $stores, string $period, $active, array $type)
    {
        // default for fetchResultsFrom is backfill-only
        return Stub::construct(
            \Reporting::class,
            [
                [
                    "user" => $users,
                    "store" => $stores,
                    "exclude_inactive" => $active,
                    "type" => $type,
                    "timezone" => "America/New_York",
                    "period" => $period,
                ],
                null,
                $this->app
            ]
        );
    }
}
