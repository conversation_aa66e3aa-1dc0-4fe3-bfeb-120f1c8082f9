<?php

declare(strict_types=1);

namespace SF\functional\ProductPanel\Panels;

use Salesfloor\API\Managers\Products\ProductPanel;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

abstract class BasePanels extends BaseFunctional
{
    /**
     * @return ProductPanel
     */
    abstract protected function getManager();

    abstract protected function getNumberSlotInPanel();

    protected function validateConfig(FunctionalTester $I)
    {
        $I->assertEquals(static::EXPECTED_SLOT_IN_PANEL, $this->getNumberSlotInPanel());
    }

    protected function normalizePanel(array $panels): array
    {
        return array_map(function ($panel) {
            return [
                'ID' => $panel['ID'],
                'user_id' => $panel['user_id'],
                'product_sku' => $panel['product_sku'],
                'position' => $panel['position'],
            ];
        }, $panels);
    }

    protected function sortResults(array &$results): void
    {
        usort($results, function ($a, $b) {
            return is_array($a) ? $a['ID'] <=> $b['ID'] : $a->id <=> $b->id;
        });
    }

    protected function sortResultsByPosition(array &$results): void
    {
        usort($results, function (array $a, array $b) {
            return $a['position'] <=> $b['position'];
        });
    }
}
