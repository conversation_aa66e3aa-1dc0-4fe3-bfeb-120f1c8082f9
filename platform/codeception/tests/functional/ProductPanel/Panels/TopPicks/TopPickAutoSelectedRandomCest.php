<?php

declare(strict_types=1);

namespace SF\functional\ProductPanel\Panels\TopPicks;

use SF\FunctionalTester;

class TopPickAutoSelectedRandomCest extends Base
{
    /** @group database_transaction */
    public function testTopPickAutoSelectedRandomWithStorefrontSlot(FunctionalTester $I)
    {
        // This is not using the random logic, but adding a test to make sure custom behaviour is also working
        $I->wantTo("test - top pick - auto selected - random - with storefront slot");

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testTopPickAutoSelectedRandomWithStorefrontSlot");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // This is the only one not selected from default logic, because already part of the panel
        $I->canSeeInDatabase('sf_product_category_map', [
            'product_id' => $results[6]['product_sku'],
            'storefront_slot' => 0
        ]);

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        $this->sortResults($results);
        // Those 7 products will always be selected because of storefront_slot logic
        // Position are also enforced so they can be tested
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 0,
                'product_sku' => '0cb4b8b709e00325461bc0c4688be637',
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 1,
                'product_sku' => '174d4437287e61204b5bc859a6c6f824',
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '2697848788fd5ef0715e2c3e4ae8ac0d',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => '2f97af6978a16c081cc0a094b73016bc',
            ],
            [
                'ID' => $results[4]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 4,
                'product_sku' => '363f075ba54f1c415b30d8f96a2ea5b0',
            ],
            [
                'ID' => $results[5]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 5,
                'product_sku' => '508927971f1252415e34bdb256efa6a1',
            ],
            [
                'ID' => $results[6]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 6,
                // This position will be random, so cannot test product_sku value
            ],
            [
                'ID' => $results[7]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 7,
                'product_sku' => '5e88a0d9359bd586d6dab6ee362ba330',
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickAutoSelectedRandomWithSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - top pick - auto selected - random - with specialties");

        // force storefront_slot to 0 so only random logic is used
        $I->updateInDatabase('sf_product_category_map', [
            'storefront_slot' => 0
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testTopPickAutoSelectedRandomWithSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        // Those 6 are from specialties, only position is random because of the shuffle()
        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '508927971f1252415e34bdb256efa6a1', // from specialties
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '508927971f1252415e34bdb256efa6a2', // from specialties
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '174d4437287e61204b5bc859a6c6f824', // from specialties
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '174d4437287e61204b5bc859a6c6f824', // from specialties
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'd961d23a31d503f8e75c03d4da0933c7', // from specialties
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'ef31b778dae31914b41ef1a17effe166', // from specialties
        ]);

        // the last 2 are not from sf_products_random since not enough product
        $I->dontSeeInDatabase('sf_products_random', [
            'product_id' => $results[6]['product_sku']
        ]);

        $I->dontSeeInDatabase('sf_products_random', [
            'product_id' => $results[7]['product_sku']
        ]);

        $this->sortResultsByPosition($results);

        $expectedResult = [];
        for ($i = 0; $i < 8; $i++) {
            $expectedResult[$i] = [
                'ID' => $results[$i]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickAutoSelectedRandomWithoutSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - top pick - auto selected - random - without specialties");

        $this->app['configs']['retailer.specialties.is_enabled'] = false;

        // force storefront_slot to 0 so only random logic is used
        $I->updateInDatabase('sf_product_category_map', [
            'storefront_slot' => 0
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testTopPickAutoSelectedRandomWithoutSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        // Those 6 are from random table, only position is random because of the shuffle()
        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '508927971f1252415e34bdb256efa6a1', // from random table
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '508927971f1252415e34bdb256efa6a2', // from random table
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '174d4437287e61204b5bc859a6c6f824', // from random table
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '174d4437287e61204b5bc859a6c6f824', // from random table
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'd961d23a31d503f8e75c03d4da0933c7', // from random table
        ]);

        $I->canSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'ef31b778dae31914b41ef1a17effe166', // from random table
        ]);

        // the last 2 are not from sf_products_random since not enough product
        // $I->dontSeeInDatabase('sf_products_random', [
        //     'product_id' => $results[6]['product_sku']
        // ]);

        // $I->dontSeeInDatabase('sf_products_random', [
        //     'product_id' => $results[7]['product_sku']
        // ]);

        $this->sortResultsByPosition($results);

        $expectedResult = [];
        for ($i = 0; $i < 8; $i++) {
            $expectedResult[$i] = [
                'ID' => $results[$i]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }
        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testNewArrivalAutoSelectedRandomFallbackWitSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - top pick - auto selected - random(fallback) - specialties");

        // force storefront_slot to 0 so only random logic is used
        $I->updateInDatabase('sf_product_category_map', [
            'storefront_slot' => 0
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testNewArrivalAutoSelectedRandomFallbackWitSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);


        // All products are not from sf_products_random since not enough product
        foreach ($results as $result) {
            $I->dontSeeInDatabase('sf_products_random', [
                'product_id' => $result['product_sku']
            ]);
        }

        $this->sortResultsByPosition($results);
        $expectedResult = [];
        for ($i = 0; $i < 8; $i++) {
            $expectedResult[$i] = [
                'ID' => $results[$i]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }
        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testNewArrivalAutoSelectedRandomFallbackWithoutSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - top pick - auto selected - random(fallback) - without specialties");

        $this->app['configs']['retailer.specialties.is_enabled'] = false;

        // force storefront_slot to 0 so only random logic is used
        $I->updateInDatabase('sf_product_category_map', [
            'storefront_slot' => 0
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testNewArrivalAutoSelectedRandomFallbackWithoutSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_product', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);


        // All products are not from sf_products_random since not enough product
        foreach ($results as $result) {
            $I->dontSeeInDatabase('sf_products_random', [
                'product_id' => $result['product_sku']
            ]);
        }

        $this->sortResultsByPosition($results);
        $expectedResult = [];
        for ($i = 0; $i < 8; $i++) {
            $expectedResult[$i] = [
                'ID' => $results[$i]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }
        $this->validateArray($expectedResult, $results);
    }
}
