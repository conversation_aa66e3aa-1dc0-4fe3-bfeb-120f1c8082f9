<?php

declare(strict_types=1);

namespace SF\functional\ProductPanel\Panels\TopPicks;

use SF\FunctionalTester;

/**
 * Disclaimer: storefront slot logic is deprecated after but the code logic still use it and the dump still have data
 * linked to it. So will test still rely on this data.
 */
class TopPickRefreshAutoSelectedCest extends Base
{
    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedFull(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick [Full]");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedFull");

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        foreach ($results as $result) {
            if ($result['autoselected']) {
                $I->dontSeeInDatabase('sf_product', [
                    'ID' => $result['ID']
                ]);
            } else {
                $I->canSeeInDatabase('sf_product', [
                    'ID' => $result['ID']
                ]);
            }
        }

        $I->seeNumRecords(4, 'sf_product', [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => $results[0]['position'],
            ],
            [
                'ID' => $results[1]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => $results[1]['position'],
            ],
            [
                'ID' => $results[2]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'position' => $results[2]['position'],
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
            ],
            [
                'ID' => $results[3]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'position' => $results[3]['position'],
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[3],
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedPartial(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick [Partial]");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedPartial");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $I->seeNumRecords(2, 'sf_product', [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);

        $I->seeNumRecords(2, 'sf_product', [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 0
        ]);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);
        $this->sortResults($results);
        // Auto selected refreshed with the next 2 products
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 2,
            ],
            [
                'ID' => $results[1]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 3,
            ],
        ];

        $this->validateArray($expectedResult, $results);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 0
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedDuplicate(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick [Duplicate]");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedDuplicate");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 0
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
        ];

        $this->validateArray($expectedResult, $results);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);
        $this->sortResultsByPosition($results);
        // Auto selected refreshed with the next 2 products
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 2,
            ],
            [
                'ID' => $results[1]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'position' => 3,
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedNone(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick [None]");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedNone");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $I->dontSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
                'autoselected' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
                'autoselected' => 0,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'autoselected' => 0,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'autoselected' => 0,
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedWithoutUserId(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedWithoutUserId");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts();

        $I->seeNumRecords(4, 'sf_product', [
            'autoselected' => 0
        ]);

        $I->seeNumRecords(12, 'sf_product', [
            'autoselected' => 1
        ]);

        // I usually think the return will be based on the insert order (ID) but there's on guarantee
        // Right now, I get 19,17,18 when I select X from Y. We should be careful writing test relying on this
        // assumption.

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);
        $this->sortResults($results);
        $this->validateArray([
            [
                'ID' => $results[0]['ID'], // 17,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 1,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[1]['ID'], // 18,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 2,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 19,
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'autoselected' => 1,
            ]
        ], $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedAllNominatedProducts(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick [Full] - All nominated products");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedAllNominatedProducts");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->dontSeeInDatabase('sf_product', [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 0
        ]);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);
        $this->sortResultsByPosition($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "faef61620314e74c8aa1ad336a4985c7",
                'position' => 0,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[1]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "6c4882e22e27e51c53b55e616cb5c120",
                'position' => 1,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b0b077719c0038ddec22f9eee08dd510',
                'position' => 2,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[3]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '2f97af6978a16c081cc0a094b73016bc',
                'position' => 3,
                'autoselected' => 1,
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedPartialAllNominatedProducts(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - Top Pick [Partial] - All nominated products");

        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedPartialAllNominatedProducts");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 0
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
                'autoselected' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
                'autoselected' => 0,
            ],
        ];

        $this->validateArray($expectedResult, $results);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '6c4882e22e27e51c53b55e616cb5c120',
                'position' => 2,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[1]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'faef61620314e74c8aa1ad336a4985c7',
                'position' => 3,
                'autoselected' => 1,
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testTopPickRefreshAutoSelectedMoreThanMax(FunctionalTester $I)
    {
        $I->wantTo("test refreshAutoSelectedProducts() - New Arrival [full] - More than max");

        // This will create 10 top picks when the limit is 4
        $this->insertFixtureGroup($I, "testTopPickRefreshAutoSelectedMoreThanMax");

        $manager = $this->getManager();

        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1
        ]);
        $this->sortResultsByPosition($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 11,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "2f97af6978a16c081cc0a094b73016bc",
                'position' => 0,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[1]['ID'], // 12,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "0cb4b8b709e00325461bc0c4688be637",
                'position' => 1,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 13,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "508927971f1252415e34bdb256efa6a1",
                'position' => 2,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[3]['ID'], // 14,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "5e88a0d9359bd586d6dab6ee362ba330",
                'position' => 3,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[4]['ID'], // 15,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '363f075ba54f1c415b30d8f96a2ea5b0',
                'position' => 4,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[5]['ID'], // 16,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "174d4437287e61204b5bc859a6c6f824",
                'position' => 6,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[6]['ID'], // 17,
                'user_id' => self::REGGIE_ID,
                // product_sku cannot be tested here since random
                // we rely on storefront_slot old "feature" that test are still using
                // that's why next position is not random, because this slot is available
                'position' => 7,
                'autoselected' => 1,
            ],
            [
                'ID' => $results[7]['ID'], // 18,
                'user_id' => self::REGGIE_ID,
                'product_sku' => "2697848788fd5ef0715e2c3e4ae8ac0d",
                'position' => 8,
                'autoselected' => 1,
            ],
        ];

        $this->validateArray($expectedResult, $results);
    }
}
