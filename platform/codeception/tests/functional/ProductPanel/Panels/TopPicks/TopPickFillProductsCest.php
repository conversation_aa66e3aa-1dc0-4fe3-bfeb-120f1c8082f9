<?php

declare(strict_types=1);

namespace SF\functional\ProductPanel\Panels\TopPicks;

use Salesfloor\Services\CatalogImporter\NominatedProductsImporter;
use SF\FunctionalTester;

class TopPickFillProductsCest extends Base
{
    ////////////////////////////////////
    /// Nominated Products tests

    /** @group database_transaction */
    public function testFillProductWithPartialDataNominatedProductPartial(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Partial] - Nominated Product [Partial]");

        $manager = $this->getManager();

        // This will add 1 nominated products + 2 top picks
        $this->insertFixtureGroup($I, "testFillProductWithPartialDataNominatedProductPartial");

        $this->validateConfig($I);

        /**
         * SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m
         * INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1
         * INNER JOIN sf_siteurl su ON su.product_id = m.product_id
         * INNER JOIN sf_images i ON i.product_id = m.product_id
         * LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 1
         * LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 1
         * INNER JOIN sf_categories c ON pcm.category_id = c.category_id
         * INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id
         * INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 1
         * WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1')
         * AND (m.product_id NOT IN ('899580ec5fd857886a2767a98b44a0c1', '54640cd49acae0386f777604ce15ab92'))
         * AND (pcm.storefront_slot > 0 AND pcm.storefront_slot <= 8) GROUP BY pcm.storefront_slot LIMIT 8
         */

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1', // Previously populated
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92', // Previously populated
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '6c4882e22e27e51c53b55e616cb5c120', // Only one nominated product
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[3],
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[4],
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);

        // Next slot should be nominated product
        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[2]['product_sku']
        ]);

        // Last 5 are based on the top pick / storefront_slot logic
    }

    /** @group database_transaction */
    public function testFillProductWithPartialDataNominatedProductFull(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Partial] - Nominated Product [Full]");

        $manager = $this->getManager();

        // This will add 6 nominated products + 2 top picks
        $this->insertFixtureGroup($I, "testFillProductWithPartialDataNominatedProductFull");

        $this->validateConfig($I);

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '6c4882e22e27e51c53b55e616cb5c120',
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'faef61620314e74c8aa1ad336a4985c7',
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b0b077719c0038ddec22f9eee08dd510',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '2f97af6978a16c081cc0a094b73016bc',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c097e37f1beb7eedbacb2d0955d5a2fc',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'd14508bf3f8d41fe999b7cacf6dd3a23',
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);

        // last 6 should be nominated product (in order of priority)
        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[2]['product_sku']
        ]);

        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[3]['product_sku']
        ]);

        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[4]['product_sku']
        ]);

        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[5]['product_sku']
        ]);

        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[6]['product_sku']
        ]);

        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_TOP_PICKS,
            'product_id' => $results[7]['product_sku']
        ]);
    }

    ////////////////////////////////////
    /// Data related tests (Top pick logic)

    /** @group database_transaction */
    public function testFillProductWithEmptyData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Empty] - Nominated Product [None]");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[3],
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[4],
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[5],
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[6],
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[7],
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithPartialData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Partial] - Nominated Product [None]");

        // This will add 2 top picks
        $this->insertFixtureGroup($I, "testFillProductWithPartialData");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[3],
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[4],
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[5],
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithFullData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Full] - Nominated Product [None]");

        // This will add 8 top picks
        $this->insertFixtureGroup($I, "testFillProductWithFullData");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '744cff8f6e1e52f7765aeb8db6bd77f2',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'a983fce78fcc1205705c55ec98cd3711',
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithOverfullData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Overfull] - Nominated Product [None]");

        // This will add 12 top picks
        $this->insertFixtureGroup($I, "testFillProductWithOverfullData");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(12, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '744cff8f6e1e52f7765aeb8db6bd77f2',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'a983fce78fcc1205705c55ec98cd3711',
                'position' => 7,
            ],
        ];


        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    ////////////////////////////////////
    /// Duplicate related tests

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataBeforeEmpty(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Full] - Nominated Product [None] - Duplicate [before]");

        // This will add 8 top picks
        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataBeforeEmpty");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 2,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '744cff8f6e1e52f7765aeb8db6bd77f2',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'a983fce78fcc1205705c55ec98cd3711',
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataAfterEmpty(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Full] - Nominated Product [None] - Duplicate [after]");

        // This will add 2 top picks
        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataAfterEmpty");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'position' => 1,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '744cff8f6e1e52f7765aeb8db6bd77f2',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'a983fce78fcc1205705c55ec98cd3711',
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataBeginning(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Full] - Nominated Product [None] - Duplicate [beginning]");

        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataBeginning");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 1,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '744cff8f6e1e52f7765aeb8db6bd77f2',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'a983fce78fcc1205705c55ec98cd3711',
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataEnding(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Full] - Nominated Product [None] - Duplicate [ending]");

        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataEnding");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 3,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 4,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '744cff8f6e1e52f7765aeb8db6bd77f2',
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'a983fce78fcc1205705c55ec98cd3711',
                'position' => 6,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    ////////////////////////////////////
    /// Product related tests

    /** @group database_transaction */
    public function testFillProductWithInvalidProductId(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Partial] - Nominated Product [None] - Invalid product id");

        $this->insertFixtureGroup($I, "testFillProductWithInvalidProductId");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[3],
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 9,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[4],
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 10,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[5],
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 11,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[6],
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 12,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[7],
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithUnavailableProductId(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - Top Pick [Partial] - Nominated Product [None] - Unavailable product");

        $this->insertFixtureGroup($I, "testFillProductWithUnavailableProductId");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the top pick main logic, not the random logic
        // Keep in mind about specialties and storefront_slot

        // This will populate the missing slots (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/top-picks
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(8, 'sf_product', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_product', null, [
            'user_id' => self::REGGIE_ID
        ]);
        usort($results, function ($a, $b) {
            return $a['ID'] - $b['ID'];
        });

        $expectedResult = [
            [
                'ID' => $results[0]['ID'], // 1,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'], // 2,
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'], // 5,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[0],
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'], // 6,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[1],
                'position' => 3,
            ],
            [
                'ID' => $results[4]['ID'], // 7,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[2],
                'position' => 4,
            ],
            [
                'ID' => $results[5]['ID'], // 8,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[3],
                'position' => 5,
            ],
            [
                'ID' => $results[6]['ID'], // 9,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[4],
                'position' => 6,
            ],
            [
                'ID' => $results[7]['ID'], // 10,
                'user_id' => self::REGGIE_ID,
                'product_sku' => self::PRODUCTS_STOREFRONT_SLOTS[5],
                'position' => 7,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    // TODO : Team mode
    // TODO : Variants
}
