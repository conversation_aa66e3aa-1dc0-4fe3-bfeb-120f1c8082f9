<?php

declare(strict_types=1);

namespace SF\functional\ProductPanel\Panels\NewArrivals;

use SF\FunctionalTester;

class NewArrivalAutoSelectedRandomCest extends Base
{
    /** @group database_transaction */
    public function testNewArrivalAutoSelectedRandomWithSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - new arrival - auto selected - random - last resort with specialties");

        // change arrival_date to force "random" logic
        $I->updateInDatabase('sf_products', [
            'arrival_date' => '2100-10-10'
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testNewArrivalAutoSelectedRandomWithSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        $I->canSeeInDatabase('sf_deal', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31', // from specialties
        ]);

        $I->canSeeInDatabase('sf_deal', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'a983fce78fcc1205705c55ec98cd3711', // from specialties
        ]);

        $I->canSeeInDatabase('sf_deal', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08', // from specialties
        ]);

        $this->sortResultsByPosition($results);
        // Cannot test product_sku/position because they are random
        $expectedResult = [];
        for ($i = 0; $i < 4; $i++) {
            $expectedResult[$i] = [
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }
        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testNewArrivalAutoSelectedRandomWithoutSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - new arrival - auto selected - random - last resort without specialties");

        $this->app['configs']['retailer.specialties.is_enabled'] = false;

        // change arrival_date to force "random" logic
        $I->updateInDatabase('sf_products', [
            'arrival_date' => '2100-10-10'
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');

        $this->insertFixtureGroup($I, "testNewArrivalAutoSelectedRandomWithoutSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        // position could change because of double random (mysql+shuffly)
        // data can be tested since sf_products_random is small but cannot test position since it's shuffled too.
        $I->canSeeInDatabase('sf_deal', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31', // from specialties
        ]);

        $I->canSeeInDatabase('sf_deal', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => 'a983fce78fcc1205705c55ec98cd3711', // from specialties
        ]);

        $I->canSeeInDatabase('sf_deal', [
            'user_id' => self::REGGIE_ID,
            'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08', // from specialties
        ]);

        $this->sortResultsByPosition($results);
        // The last one of sf_products_random don't match logic (storefront_slot and already used)
        // so it will use the fallback also here
        $I->dontSeeInDatabase('sf_products_random', [
            'product_id' => $results[3]['product_sku']
        ]);

        // Cannot test product_sku/position because they are random
        $expectedResult = [];
        for ($i = 0; $i < 4; $i++) {
            $expectedResult[$i] = [
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testNewArrivalAutoSelectedRandomFallbackWithSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - new arrival - auto selected - random(fallback) - last resort with specialties");

        // change arrival_date to force "random" logic
        $I->updateInDatabase('sf_products', [
            'arrival_date' => '2100-10-10'
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');
        $this->insertFixtureGroup($I, "testNewArrivalAutoSelectedRandomFallbackWithSpecialties");

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        $this->sortResultsByPosition($results);
        // Cannot test product_sku/position because they are random
        $expectedResult = [];
        for ($i = 0; $i < 4; $i++) {
            $expectedResult[$i] = [
                'ID' => $results[$i]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }

        $this->validateArray($expectedResult, $results);
    }

    /** @group database_transaction */
    public function testNewArrivalAutoSelectedRandomFallbackWithoutSpecialties(FunctionalTester $I)
    {
        $I->wantTo("test - new arrival - auto selected - random(fallback) - without specialties");

        // change arrival_date to force "random" logic
        $I->updateInDatabase('sf_products', [
            'arrival_date' => '2100-10-10'
        ]);

        // One of the migration is to create/populate sf_products_random, so we need to truncate it for our test
        $I->emptyTableFromDatabase('sf_products_random');
        $this->insertFixtureGroup($I, "testNewArrivalAutoSelectedRandomFallbackWithoutSpecialties");

        $this->app['configs']['retailer.specialties.is_enabled'] = false;

        $manager = $this->getManager();
        $manager->refreshAutoSelectedProducts(self::REGGIE_ID);

        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID,
            'autoselected' => 1,
        ]);

        // Last resort enforce storefront_slot to 0
        foreach ($results as $result) {
            $I->canSeeInDatabase('sf_product_category_map', [
                'product_id' => $result['product_sku'],
                'storefront_slot' => 0
            ]);
        }

        // You can't select same value as before
        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '899580ec5fd857886a2767a98b44a0c1'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '54640cd49acae0386f777604ce15ab92'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => '91d26b5bd099ebd475e7c506886124d4'
        ]);

        $I->dontSeeInDatabase('sf_deal', [
            'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c'
        ]);

        $this->sortResultsByPosition($results);
        // Cannot test product_sku/position because they are random
        $expectedResult = [];
        for ($i = 0; $i < 4; $i++) {
            $expectedResult[$i] = [
                'ID' => $results[$i]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => $i,
            ];
        }

        $this->validateArray($expectedResult, $results);
    }
}
