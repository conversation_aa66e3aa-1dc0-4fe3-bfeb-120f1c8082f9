<?php

declare(strict_types=1);

namespace SF\functional\ProductPanel\Panels\NewArrivals;

use Salesfloor\Services\CatalogImporter\NominatedProductsImporter;
use SF\FunctionalTester;

class NewArrivalFillProductsCest extends Base
{
    ////////////////////////////////////
    /// Nominated Products tests

    /** @group database_transaction */
    public function testFillProductWithPartialDataNominatedProductPartial(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Partial] - Nominated Product [Partial]");

        $manager = $this->getManager();

        // This will add 1 nominated products + 2 deals
        $this->insertFixtureGroup($I, "testFillProductWithPartialDataNominatedProductPartial");

        $this->validateConfig($I);

        // This will populate the missing slot (if any)
        // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        $resultsGetAll = $manager->getAll([
            'user_id' => self::REGGIE_ID,
        ], 0, -1);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $I->assertCount(4, $results);

        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '6c4882e22e27e51c53b55e616cb5c120',
                'position' => 2,
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 3,
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);

        // Next slot should be nominated product
        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_NEW_ARRIVALS,
            'product_id' => $results[2]['product_sku']
        ]);

        // last one "random"
    }

    /** @group database_transaction */
    public function testFillProductWithPartialDataNominatedProductFull(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Partial] - Nominated Product [Full]");

        $manager = $this->getManager();

        // This will add 2 nominated products + 2 deals
        $this->insertFixtureGroup($I, "testFillProductWithPartialDataNominatedProductFull");

        $this->validateConfig($I);

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);

        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '6c4882e22e27e51c53b55e616cb5c120',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'faef61620314e74c8aa1ad336a4985c7',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);

        // last 2 should be nominated product (in order of priority)
        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_NEW_ARRIVALS,
            'product_id' => $results[2]['product_sku']
        ]);

        $I->canSeeInDatabase('sf_nominated_products', [
            'type' => NominatedProductsImporter::TYPE_NEW_ARRIVALS,
            'product_id' => $results[3]['product_sku']
        ]);
    }

    ////////////////////////////////////
    /// Data related tests (New arrival logic)

    /** @group database_transaction */
    public function testFillProductWithEmptyData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Empty] - Nominated Product [None]");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        /**
         * SELECT DISTINCT m.product_id as product_sku, pcm.storefront_slot FROM sf_products m
         * INNER JOIN sf_product_category_map pcm ON m.product_id = pcm.product_id AND pcm.category_id != 1
         * INNER JOIN sf_siteurl su ON su.product_id = m.product_id INNER JOIN sf_images i ON i.product_id = m.product_id
         * LEFT JOIN sf_product p ON p.product_sku = m.product_id AND p.user_id = 1 LEFT JOIN sf_deal d ON d.product_sku = m.product_id AND d.user_id = 1
         * INNER JOIN sf_categories c ON pcm.category_id = c.category_id INNER JOIN sf_category_tree ct ON c.category_id = ct.category_id
         * INNER JOIN sf_rep_product_selection_map rpsm ON ct.parent_id = rpsm.term_id AND rpsm.user_id = 1
         * WHERE (p.ID IS NULL) AND (d.ID IS NULL) AND (m.product_id <> '') AND (m.available = '1') AND (m.arrival_date <= NOW()) AND (m.deal_ratio = 100)
         * ORDER BY m.arrival_date DESC, m.product_id DESC LIMIT 4
         */
        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithPartialData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Partial] - Nominated Product [None]");

        // This will add 2 deals
        $this->insertFixtureGroup($I, "testFillProductWithPartialData");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);

        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithFullData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None]");

        // This will add 2 deals
        $this->insertFixtureGroup($I, "testFillProductWithFullData");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithOverfullData(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Overfull] - Nominated Product [None]");

        // This will add 2 deals
        $this->insertFixtureGroup($I, "testFillProductWithOverfullData");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(12, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    ////////////////////////////////////
    /// Duplicate related tests

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataBeforeEmpty(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None] - Duplicate [before]");

        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataBeforeEmpty");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 2,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataAfterEmpty(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None] - Duplicate [after]");

        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataAfterEmpty");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 1,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataBeginning(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None] - Duplicate [beginning]");

        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataBeginning");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 1,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductFullDataWithDuplicateDataEnding(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None] - Duplicate [ending]");

        $this->insertFixtureGroup($I, "testFillProductFullDataWithDuplicateDataEnding");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '91d26b5bd099ebd475e7c506886124d4',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    ////////////////////////////////////
    /// Product related tests

    /** @group database_transaction */
    public function testFillProductWithInvalidProductId(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None] - Invalid product id");

        $this->insertFixtureGroup($I, "testFillProductWithInvalidProductId");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => 'c5fdf5d2b068cdab85a9800b7987fb31',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    /** @group database_transaction */
    public function testFillProductWithUnavailableProductId(FunctionalTester $I)
    {
        $I->wantTo("test fillMissingProducts() - New Arrival [Full] - Nominated Product [None] - Unavailable product");

        $this->insertFixtureGroup($I, "testFillProductWithUnavailableProductId");

        $manager = $this->getManager();

        $this->validateConfig($I);

        // Will use the new arrival main logic, not the random logic
        // Keep in mind about specialties and arrival_date + deal_ratio logic

        // This will populate the missing slot (if any)
        $resultsGetAll = $manager->getAll([
            'user_id' => 1, // this is mandatory if not using the controller (e.g: /reps/1/products/new-arrivals
        ], 0, -1);

        // The extra (over) are not deleted
        $I->seeNumRecords(4, 'sf_deal', [
            'user_id' => self::REGGIE_ID
        ]);

        // We should now have EXPECTED_SLOT_IN_PANEL
        $results = $I->grabRowsFromDatabase('sf_deal', null, [
            'user_id' => self::REGGIE_ID
        ]);
        $this->sortResults($results);
        $expectedResult = [
            [
                'ID' => $results[0]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '899580ec5fd857886a2767a98b44a0c1',
                'position' => 0,
            ],
            [
                'ID' => $results[1]['ID'],
                'user_id' => self::REGGIE_ID,
                'product_sku' => '54640cd49acae0386f777604ce15ab92',
                'position' => 1,
            ],
            [
                'ID' => $results[2]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 2,
                'product_sku' => 'b2c1b175fdbbac5c5b33d2628042967c',
            ],
            [
                'ID' => $results[3]['ID'],
                'user_id' => self::REGGIE_ID,
                'position' => 3,
                'product_sku' => '8b22196d3cb34f69ef0c5f5e06cb1c08',
            ],
        ];

        // Generic DB content validation
        $this->validateArray($expectedResult, $results);

        // array_change_key_case() is needed because ID is only when querying the DB. The model is lowercase
        $this->validateArray(array_map(function ($item) {
            return array_change_key_case($item);
        }, $expectedResult), $resultsGetAll);
    }

    // TODO : Team mode
    // TODO : Variants
}
