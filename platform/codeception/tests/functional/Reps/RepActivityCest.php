<?php

declare(strict_types=1);

namespace SF\functional\Reps;

use Carbon\Carbon;
use Salesfloor\API\Managers\Reps;
use Salesfloor\Models\Rep;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class RepActivityCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testRepActivityStartedOnCreation(FunctionalTester $I)
    {
        $I->wantTo('validate rep activity is started on rep creation');

        /** @var Rep $rep */
        $rep = $this->makeUser();

        // codeception seeInDatabase has no way to provide IS NOT NULL
        // So validation is a bit roundabout.
        // If there is only one matching row, and that row doesn't have a
        // null start date, then we're good.

        $I->seeNumRecords(
            1,
            'sf_user_activity',
            [
                'type'     => 'active session',
                'source'   => null,
                'user_id'  => $rep->getId(),
                'end_date' => null,
            ]
        );

        $I->seeNumRecords(
            0,
            'sf_user_activity',
            [
                'type'       => 'active session',
                'source'     => null,
                'user_id'    => $rep->getId(),
                'end_date'   => null,
                'start_date' => null,
            ]
        );
    }

    /** @group database_transaction */
    public function testRepActivityStoppedOnDisable(FunctionalTester $I)
    {
        $I->wantTo('validate rep activity is stopped on rep disable');

        $this->insertFixtureGroup($I, 'userActivities');
        /** @var Reps $repManager */
        $repManager = $this->app['reps.manager'];

        /** @var Rep $rep  */
        $rep = $repManager->getOne(['ID' => 100]);
        $rep->user_status = 0;
        $repManager->save($rep);

        $I->seeNumRecords(
            1,
            'sf_user_activity',
            [
                'type'     => 'active session',
                'source'   => null,
                'user_id'  => $rep->getId(),
            ]
        );

        $I->seeNumRecords(
            0,
            'sf_user_activity',
            [
                'type'       => 'active session',
                'source'     => null,
                'user_id'    => $rep->getId(),
                'end_date'   => null,
            ]
        );

        $I->seeNumRecords(
            0,
            'sf_user_activity',
            [
                'type'       => 'active session',
                'source'     => null,
                'user_id'    => $rep->getId(),
                'start_date' => null,
            ]
        );

        $I->seeInDatabase('sf_user_activity', [
            'type' => 'chat',
            'source'     => null,
            'user_id'    => $rep->getId(),
            'end_date'   => Carbon::now()->toDateTimeString(),
        ]);
    }

    /** @group database_transaction */
    public function testRepActivityStartedOnEnable(FunctionalTester $I)
    {
        $I->wantTo('validate rep activity is started on rep enable');

        /** @var Rep $rep */
        $rep        = $this->makeUser();
        $repManager = $this->app['reps.manager'];

        // You need to "patch" because original model doesn't have user_registered date, since populated via MySQL.
        // However, we can't set it to null during "update" ; we ignore null only on "insert"

        $rep->user_status = 0;
        $repManager->save($rep, true);

        $rep->user_status = 1;
        $repManager->save($rep, true);

        $I->seeNumRecords(
            2,
            'sf_user_activity',
            [
                'type'     => 'active session',
                'source'   => null,
                'user_id'  => $rep->getId(),
            ]
        );

        $I->seeNumRecords(
            1,
            'sf_user_activity',
            [
                'type'       => 'active session',
                'source'     => null,
                'user_id'    => $rep->getId(),
                'end_date'   => null,
            ]
        );

        $I->seeNumRecords(
            0,
            'sf_user_activity',
            [
                'type'       => 'active session',
                'source'     => null,
                'user_id'    => $rep->getId(),
                'start_date'   => null,
            ]
        );
    }

    private function makeUser()
    {
        /** @var Reps $repManager */
        $repManager = $this->app['reps.manager'];
        $model      = $repManager->create(
            [
                'user_login'   => 'test',
                'user_pass'    => 'password',
                'user_email'   => '<EMAIL>',
                'user_nicename' => '', // cannot be null
                'user_alias'   => 'testalias',
                'store'        => 1003,
                'group'        => 1,
                'selling_mode' => 1,
                'user_status'  => 1,
            ]
        );

        $repManager->save($model);

        return $model;
    }
}
