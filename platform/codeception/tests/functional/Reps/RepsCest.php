<?php

namespace SF\functional\Reps;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\API\Managers\Reps as RepsManager;

class RepsCest extends BaseFunctional
{
    /** @var  RepsManager $repsManager */
    private $repsManager;

    public function _before($I)
    {
        parent::_before($I);

        $this->repsManager = $this->app['reps.manager'];
    }

    /** @group database_transaction */
    public function testBaseIsEligibleCorpEmailRequired(FunctionalTester $I)
    {
        $I->wantTo('test basic to verify if corp email required config is enabled then all type of user should receive email notifications');

        $this->app['configs']->offsetSet('retailer.corporate-email.required', true);

        $this->insertFixtures($I, [
            'RepUserSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepUserNonSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepCorpAdminSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepCorpAdminNonSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepAdminNonSellingMode' => 'wp_users',
        ]);

        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_USER_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_USER_NON_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_CORP_ADMIN_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_CORP_ADMIN_NON_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_ADMIN_NON_SELLING_MODE));
    }

    /** @group database_transaction */
    public function testBaseIsEligibleNotCorpEmailRequiredAdmin(FunctionalTester $I)
    {
        $I->wantTo('test basic to verify if corp email required config is false then admin should receive email notifications');

        $this->app['configs']->offsetSet('retailer.corporate-email.required', false);

        $this->insertFixtures($I, [
            'RepCorpAdminSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepCorpAdminNonSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepAdminNonSellingMode' => 'wp_users',
        ]);

        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_CORP_ADMIN_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_CORP_ADMIN_NON_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_ADMIN_NON_SELLING_MODE));
    }

    /** @group database_transaction */
    public function testBaseIsEligibleNotCorpEmailRequiredNonSellingMode(FunctionalTester $I)
    {
        $I->wantTo('test basic to verify if corp email required config is false then non selling mode users should receive email notifications');

        $this->app['configs']->offsetSet('retailer.corporate-email.required', false);

        $this->insertFixtures($I, [
            'RepUserNonSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepCorpAdminNonSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'RepAdminNonSellingMode' => 'wp_users',
        ]);

        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_USER_NON_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_CORP_ADMIN_NON_SELLING_MODE));
        $I->assertTrue($this->repsManager->isEligibleForEmailNotification(Fixture::REP_ADMIN_NON_SELLING_MODE));
    }

    /** @group database_transaction */
    public function testBaseIsEligibleNotCorpEmailRequiredRepSellingMode(FunctionalTester $I)
    {
        $I->wantTo('test basic to verify if corp email required config is false then reps with selling mode on should not receive email notifications');

        $this->app['configs']->offsetSet('retailer.corporate-email.required', false);

        $this->insertFixtures($I, [
            'RepUserSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'StoreManagerSellingMode' => 'wp_users',
        ]);

        $this->insertFixtures($I, [
            'ManagerSellingMode' => 'wp_users',
        ]);

        $I->assertFalse($this->repsManager->isEligibleForEmailNotification(Fixture::REP_USER_SELLING_MODE));
        $I->assertFalse($this->repsManager->isEligibleForEmailNotification(Fixture::STORE_MANAGER_SELLING_MODE));
        $I->assertFalse($this->repsManager->isEligibleForEmailNotification(Fixture::MANAGER_SELLING_MODE));
    }

    /** @group database_transaction */
    public function testGetRepsToNotify(FunctionalTester $I)
    {
        $I->wantTo('test function of get reps to notify, for multi-brand & team mode');

        $this->app['configs']['retailer.storepage_mode']  = true;
        $this->app['configs']['multibrand.is_active']     = true;
        $this->app['configs']['retailer.brand']           = 'bru';
        $this->app['configs']['retailer.alternate_brand'] = 'tru';


        $this->insertFixtureGroup($I, 'notifyRepsFixtureGroup');

        // this will return reps of brands 875 only
        $reps = $this->repsManager->getRepsToNotify('875', true);
        $I->assertCount(2, $reps);

        // TODO: Please view the function: platform/api/app/src/be/Managers/Stores.php::getSiblingsStore()
        // It uses sf_store.sf_identifier to find siblings stores, but it's unique.
        // we should investigate this function.

        // this will return reps of all brands
        $reps = $this->repsManager->getRepsToNotify('876', true);
        $I->assertCount(0, $reps);

        // this will return reps w/o multi-brand (876 only)
        $reps = $this->repsManager->getRepsToNotify('876');
        $I->assertCount(0, $reps);
    }

    /**
     * @param FunctionalTester $I
     * @return void
     * @throws \Exception
     *
     * @group database_transaction
     */
    public function testGetDateBeginTime(FunctionalTester $I)
    {
        $I->wantTo('test function "getDateBeginTime"');
        // Set default time zone to UTC
        date_default_timezone_set('UTC');

        $datetime = \DateTime::createFromFormat('Y-m-d H:i:s', '2021-01-01 00:00:00');

        // Test user with valid store
        $utcDatetime = $this->repsManager->getDateBeginTime(1, $datetime);
        $this->I->assertEquals('2020-12-31 05:00:00', $utcDatetime);

        // Test user with invalid store
        $this->insertFixtureGroup($I, 'UserWithInvalidStoreId');
        $utcDatetime = $this->repsManager->getDateBeginTime(100, $datetime);
        $this->I->assertEquals('2021-01-01 00:00:00', $utcDatetime);
    }

    /** @group database_transaction */
    public function testGetStoreUserIdsFromRepIds(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'usersInTeamMode');
        $storeUserIds = $this->repsManager->getStoreUserIdsFromRepIds([100, 101]);
        $I->assertEquals([102], $storeUserIds);
    }
}
