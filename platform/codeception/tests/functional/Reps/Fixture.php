<?php

namespace SF\functional\Reps;

use Carbon\Carbon;
use Codeception\Util\Fixtures;
use Salesfloor\Services\GroupPermissionService;
use SF\BaseFixture;

class Fixture extends BaseFixture
{
    public const REPUSERSELLINGMODE         = '12001';
    public const REPUSERNONSELLINGMODE      = '12002';
    public const STOREMANAGERSELLINGMODE    = '12003';
    public const STOREMANAGERNONSELLINGMODE = '12004';
    public const MANAGERSELLINGMODE         = '12005';
    public const MANAGERNONSELLINGMODE      = '12006';
    public const REPCORPADMINSELLINGMODE    = '12007';
    public const REPCORPADMINNONSELLINGMODE = '12008';
    public const REPADMINNONSELLINGMODE     = '12009';

    public function recommendations()
    {
        Fixtures::add('RepUserSellingMode', [
            'ID'           => self::REPUSERSELLINGMODE,
            'user_login'   => self::REPUSERSELLINGMODE,
            'user_email'   => self::REPUSERSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::USER,
            'selling_mode' => '1',
            'store'        => 1003,
        ]);

        Fixtures::add('RepUserNonSellingMode', [
            'ID'           => self::REPUSERNONSELLINGMODE,
            'user_login'   => self::REPUSERNONSELLINGMODE,
            'user_email'   => self::REPUSERNONSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::USER,
            'selling_mode' => '0',
            'store'        => 1003,
        ]);

        Fixtures::add('StoreManagerSellingMode', [
            'ID'           => self::STOREMANAGERSELLINGMODE,
            'user_login'   => self::STOREMANAGERSELLINGMODE,
            'user_email'   => self::STOREMANAGERSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::STORE_MANAGER,
            'selling_mode' => '1',
            'store'        => 1003,
        ]);

        Fixtures::add('StoreManagerNonSellingMode', [
            'ID'           => self::STOREMANAGERNONSELLINGMODE,
            'user_login'   => self::STOREMANAGERNONSELLINGMODE,
            'user_email'   => self::STOREMANAGERNONSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::STORE_MANAGER,
            'selling_mode' => '0',
            'store'        => 1003,
        ]);

        Fixtures::add('ManagerSellingMode', [
            'ID'           => self::MANAGERSELLINGMODE,
            'user_login'   => self::MANAGERSELLINGMODE,
            'user_email'   => self::MANAGERSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::MANAGER,
            'selling_mode' => '1',
            'store'        => 1003,
        ]);

        Fixtures::add('ManagerNonSellingMode', [
            'ID'           => self::MANAGERNONSELLINGMODE,
            'user_login'   => self::MANAGERNONSELLINGMODE,
            'user_email'   => self::MANAGERNONSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::MANAGER,
            'selling_mode' => '0',
            'store'        => 1003,
        ]);

        Fixtures::add('RepCorpAdminSellingMode', [
            'ID'           => self::REPCORPADMINSELLINGMODE,
            'user_login'   => self::REPCORPADMINSELLINGMODE,
            'user_email'   => self::REPCORPADMINSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::CORP_ADMIN,
            'selling_mode' => '1',
            'store'        => 1003,
        ]);

        Fixtures::add('RepCorpAdminNonSellingMode', [
            'ID'           => self::REPCORPADMINNONSELLINGMODE,
            'user_login'   => self::REPCORPADMINNONSELLINGMODE,
            'user_email'   => self::REPCORPADMINNONSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::CORP_ADMIN,
            'selling_mode' => '0',
            'store'        => 1003,
        ]);

        Fixtures::add('RepAdminNonSellingMode', [
            'ID'           => self::REPADMINNONSELLINGMODE,
            'user_login'   => self::REPADMINNONSELLINGMODE,
            'user_email'   => self::REPADMINNONSELLINGMODE . '@gmail.com',
            'group'        => GroupPermissionService::SF_ADMIN,
            'selling_mode' => '0',
            'store'        => 1003,
        ]);
    }

    public function userHistory()
    {
        Fixtures::add('SwitchUser', [
            'wp_users' => [$this->makeSellingModeUser(19063, 2003)],
        ]);

        Fixtures::add('MultipleStoreSwitches', [
            'wp_users'                 => [$this->makeSellingModeUser(19063, 1003)],
            'sf_wp_user_field_history' => [
                [
                    'user_id'           => 19063,
                    'field_name'        => 'store',
                    'old_value'         => 1003,
                    'new_value'         => 2003,
                    'field_modified_at' => $this->utcDateTime('-20 days'),
                    'source'            => 'ben',
                ],
                [
                    'user_id'           => 19063,
                    'field_name'        => 'store',
                    'old_value'         => 2003,
                    'new_value'         => 1003,
                    'field_modified_at' => $this->utcDateTime('-5 days'),
                    'source'            => 'ben',
                ],
            ],
        ]);

        Fixtures::add('MultipleStoreSwitchesSameDay', [
            'sf_store'                 => [
                [
                    'store_id'      => '777',
                    'name'          => 'UTC Store',
                    'timezone'      => 'Etc/UTC',
                    'sf_identifier' => 'utc-store',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ],
            ],
            'wp_users'                 => [$this->makeSellingModeUser(19063, 2003)],
            'sf_wp_user_field_history' => [
                [
                    'user_id'           => 19063,
                    'field_name'        => 'store',
                    'old_value'         => 1003,
                    'new_value'         => 777,
                    'field_modified_at' => $this->utcDateTime('-1 hour'),
                    'source'            => 'ben',
                ],
                [
                    'user_id'           => 19063,
                    'field_name'        => 'store',
                    'old_value'         => 777,
                    'new_value'         => 2003,
                    'field_modified_at' => $this->utcDateTime('-5 minutes'),
                    'source'            => 'ben',
                ],
            ],
        ]);
    }

    public function onBoarding()
    {
        Fixtures::add('StartOnBoardingFirstTime', [
            'sf_rep_onboarding' => [
                [
                    'token'                => '12221',
                    'retailer_rep_id'      => '12221',
                    'rep_first_name'       => 'Jo',
                    'rep_last_name'        => 'Ma',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 1,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
            ],
        ]);

        Fixtures::add('StartOnBoardingSecondTime', [
            'wp_users'                 => [
                [
                    'ID'              => 115,
                    'user_login'      => 'user_whatever1',
                    'user_pass'       => 'pass',
                    'user_nicename'   => 'user_whatever1',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'user_whatever1',
                    'display_name'    => 'user_whatever1',
                    'store'           => 875,
                    'type'            => 'rep',
                    'group'           => 1,
                    'selling_mode'    => 1,
                    'user_registered' => gmdate('Y-m-d H:i:s'),
                    'locale'          => 'en_US',
                    'employee_id'     => 12221
                ],
            ],
            'sf_rep_onboarding' => [
                [
                    'token'                => '12221',
                    'retailer_rep_id'      => '12221',
                    'rep_first_name'       => 'Jo',
                    'rep_last_name'        => 'Ma',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 1,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                    'wp_user_id' => 115,
                    'rep_login' => 'user_whatever1',
                ],
            ],
        ]);

        Fixtures::add('OnBoardingBeforePromote', [
            'sf_rep_onboarding' => [
                [
                    'token'                => '12221',
                    'retailer_rep_id'      => '12221',
                    'rep_first_name'       => 'Jo',
                    'rep_last_name'        => 'Ma',
                    'rep_email'            => '<EMAIL>',
                    'store'                => 1003,
                    'group'                => 1,
                    'selling_mode'         => 1,
                    'onboarding_completed' => 0,
                ],
            ],
        ]);

        Fixtures::add('CustomerWithLocaleEnUS', [
            'sf_customer' => [
                [
                    'user_id'                => null,
                    'email'                  => '<EMAIL>',
                    'name'                   => 'JoJo',
                    'subcribtion_flag'       => 0,
                    'retailer_customer_id'   => null,
                    'unassigned_employee_id' => '12221',
                    'first_name'             => 'Jo',
                    'last_name'              => 'Mall',
                    'locale'                 => 'en_US',
                ],
            ],
        ]);

        Fixtures::add('CustomerWithLocaleNull', [
            'sf_customer' => [
                [
                    'user_id'                => null,
                    'email'                  => '<EMAIL>',
                    'name'                   => 'JoJo',
                    'subcribtion_flag'       => 0,
                    'retailer_customer_id'   => null,
                    'unassigned_employee_id' => '12221',
                    'first_name'             => 'Jo',
                    'last_name'              => 'Mall',
                    'locale'                 => null,
                ],
            ],
        ]);
    }

    public function notifyRepsFixture()
    {
        Fixtures::add('notifyRepsFixtureGroup', [
            'sf_store'                 => [
                [
                    'store_id'          => '875',
                    'name'              => 'Hamilton',
                    'timezone'          => 'America/Toronto',
                    'store_user_id'     => 2264,
                    'retailer_store_id' => 3503,
                    'brand'             => 'tru',
                    'sf_identifier'     => 'hamilton',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ],
                [
                    'store_id'          => '876',
                    'name'              => 'Hamilton',
                    'timezone'          => 'America/Toronto',
                    'store_user_id'     => 2265,
                    'retailer_store_id' => 3503,
                    'brand'             => 'bru',
                    'sf_identifier'     => 'hamilton2',
                    'country'           => 'CA',
                    'region'            => 'QC',
                    'city'              => 'Montreal',
                    'address'           => '1455 Peel Streets',
                    'postal'            => 'H3A 1T5',
                    'phone'             => '************',
                    'latitude'          => 45.4910,
                    'longitude'         => -73.5658,
                ],
            ],
            'wp_users'                 => [
                [ // Notifications enabled
                    'ID'              => 2429,
                    'user_login'      => 'user_whatever1',
                    'user_pass'       => 'pass',
                    'user_nicename'   => 'user_whatever1',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'user_whatever1',
                    'display_name'    => 'user_whatever1',
                    'store'           => 875,
                    'type'            => 'rep',
                    'group'           => 1,
                    'selling_mode'    => 1,
                    'user_registered' => gmdate('Y-m-d H:i:s', strtotime("-self::STORE_USER_ONE days")),
                    'locale'          => 'en_US',
                    'employee_id'     => 109395213
                ],
                [ // Notifications not set (enabled by default)
                    'ID'              => 2430,
                    'user_login'      => 'user_whatever2',
                    'user_pass'       => 'pass',
                    'user_nicename'   => 'user_whatever2',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'user_whatever2',
                    'display_name'    => 'user_whatever2',
                    'store'           => 875,
                    'type'            => 'rep',
                    'group'           => 1,
                    'selling_mode'    => 1,
                    'user_registered' => gmdate('Y-m-d H:i:s', strtotime("-self::STORE_USER_ONE days")),
                    'locale'          => 'en_US',
                    'employee_id'     => 109395213
                ],
                [ // Notifications disabled
                    'ID'              => 2431,
                    'user_login'      => 'user_whatever3',
                    'user_pass'       => 'pass',
                    'user_nicename'   => 'user_whatever3',
                    'user_email'      => '<EMAIL>',
                    'user_status'     => 1,
                    'user_alias'      => 'user_whatever3',
                    'display_name'    => 'user_whatever3',
                    'store'           => 875,
                    'type'            => 'rep',
                    'group'           => 1,
                    'selling_mode'    => 1,
                    'user_registered' => gmdate('Y-m-d H:i:s', strtotime("-self::STORE_USER_ONE days")),
                    'locale'          => 'en_US',
                    'employee_id'     => 109395213
                ],
            ],
            'wp_usermeta' => [
                [
                    'user_id'           => 2429,
                    'meta_key'          => 'mail_notifications_salesfloor',
                    'meta_value'        => '1',
                ],
                [
                    'user_id'           => 2431,
                    'meta_key'          => 'mail_notifications_salesfloor',
                    'meta_value'        => '0',
                ],
            ],
        ]);
    }

    public function getDateBeginTime()
    {
        Fixtures::add('UserWithoutStoreId', [
            'wp_users' => [
                [
                    'ID' => '100',
                    'user_login' => 'user100',
                    'user_pass' => '$P$BOw26SLsnuT4j8I3oLOhzED/sovsRP1',
                    'user_email' => '<EMAIL>',
                    'user_activation_key' => '$P$BeVBLo6ClVmamoRr7hudBZEVOh9jPb/',
                    'user_status' => '1',
                    'user_alias' => 'user100',
                    'type' => 'rep',
                    'commission_rate' => '0.00',
                    'employee_id' => '012a34x',
                    'group' => '5',
                    'selling_mode' => '1',
                    'isPhoto' => '1',
                    'creation_source' => 'invite',
                    'shop_feed' => '0',
                    'sso_auth' => '0',
                ],
            ],
        ]);
    }

    public function getChatLogsInUserActivity()
    {
        Carbon::setTestNow('2023-01-01 01:02:03');
        Fixtures::add('userActivities', [
            'wp_users' => [
                [
                    'ID' => 100,
                    'user_login'   => 'test',
                    'user_pass'    => 'password',
                    'user_email'   => '<EMAIL>',
                    'user_alias'   => 'testalias',
                    'store'        => 1003,
                    'group'        => 1,
                    'selling_mode' => 1,
                    'user_status'  => 1,
                ]
            ],
            'sf_user_activity' => [
                [
                    'id' => 100,
                    'type' => 'chat',
                    'source' => 'chat',
                    'user_id' => '100',
                    'start_date' => Carbon::now()->subMinutes(10)->toDateTimeString(),
                    'end_date' => null,
                ],
                [
                    'id' => 101,
                    'type' => 'active session',
                    'source' => 'active session',
                    'user_id' => '100',
                    'start_date' => Carbon::now()->subMinutes(20)->toDateTimeString(),
                    'end_date' => null,
                ],
            ],
        ]);
    }

    public function getUsersInTeamMode()
    {
        Fixtures::add('usersInTeamMode', [
            'wp_users' => [
                [
                    'ID' => 100,
                    'user_login'   => 'test100',
                    'user_pass'    => 'password',
                    'user_email'   => '<EMAIL>',
                    'user_alias'   => 'testalias100',
                    'store'        => 100,
                    'group'        => 1,
                    'selling_mode' => 1,
                    'user_status'  => 1,
                    'type' => 'rep',
                ],
                [
                    'ID' => 101,
                    'user_login'   => 'test101',
                    'user_pass'    => 'password',
                    'user_email'   => '<EMAIL>',
                    'user_alias'   => 'testalias101',
                    'store'        => 100,
                    'group'        => 1,
                    'selling_mode' => 1,
                    'user_status'  => 1,
                    'type' => 'rep',
                ],
                [
                    'ID' => 102,
                    'user_login'   => 'test102',
                    'user_pass'    => 'password',
                    'user_email'   => '<EMAIL>',
                    'user_alias'   => 'testalias102',
                    'store'        => 100,
                    'group'        => 1,
                    'selling_mode' => 1,
                    'user_status'  => 1,
                    'type' => 'store',
                ],
            ],
            'sf_store'  => [
                [
                    'store_id'          => '100',
                    'name'              => 'Hamilton',
                    'timezone'          => 'America/Toronto',
                    'store_user_id'     => 102,
                    'retailer_store_id' => 3503,
                    'brand'             => 'tru',
                    'sf_identifier'     => 'hamilton',
                ],
            ]
        ]);
    }
}
