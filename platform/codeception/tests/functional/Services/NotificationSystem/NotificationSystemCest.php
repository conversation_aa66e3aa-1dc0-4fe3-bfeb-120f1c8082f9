<?php

namespace SF\functional\Services\NotificationSystem;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use SF\functional\Services\Fixture;
use Salesfloor\Models\Message;

class NotificationSystemCest extends BaseFunctional
{
    /** @var Salesfloor\Managers\Reps; */
    private $repsManager;

    /** @group database_transaction */
    public function testBaseCreateSimpleNotificationContent(FunctionalTester $I)
    {
        $I->wantTo('test to create a simple content for notification system message');

        $notificationService = $this->app['service.notification-system']();

        $notificationService->setTitle('title.sale');
        $notificationService->setBodyListInfo('customer.name', 'name');
        $notificationService->setBodyListInfo('customer.email', 'email');

        $I->assertEquals('You just made a sale!', $notificationService->getTitle('en_US'));
        $I->assertEquals('Vous venez de faire une vente!', $notificationService->getTitle('fr_CA'));

        $I->assertEquals('<p><b>Name</b>: name</p><p><b>Email</b>: email</p>', $notificationService->getBodyList('en_US'));
        $I->assertEquals('<p><b>Nom</b>: name</p><p><b>Adresse courriel</b>: email</p>', $notificationService->getBodyList('fr_CA'));
    }

    /** @group database_transactionaa */
    public function testBaseCreateSimpleNotification(FunctionalTester $I)
    {
        $I->wantTo('test to create a simple notification content');

        $notificationService = $this->app['service.notification-system']();

        $notificationService->setTitle('title.sale');
        $notificationService->setBodyListInfo('customer.name', 'name');
        $notificationService->setBodyListInfo('customer.email', 'email');

        $userId = '1';

        $this->app['messages.manager']->createNotificationSystemMessage(
            $userId,
            $notificationService->getTitle('en_US'),
            $notificationService->getBodyList('en_US')
        );

        $I->seeInDatabase('sf_messages', [
            'user_id'      => $userId,
            'owner_id'     => $this->app['reps.manager']->getUserIdAccordingToRetailerMode($userId),
            'from_type'    => Message::FROM_TYPE_USER,
            'from_email'   => $this->app['configs']['retailer.emails.no_reply_address'],
            'from_name'    => $this->app['configs']['retailer.emails.no_reply_name'],
            'request_type' => null,
            'request_id'   => null,
            'type'         => Message::TYPE_MESSAGE,
            'status'       => Message::STATUS_UNREAD,
            'category'     => Message::CATEGORY_INBOX,
            'title'        => 'You just made a sale!',
            'comment'      => Message::NOTIFICATION_SYSTEM_MESSAGE_NOTE,
            'message'      => '<p><b>Name</b>: name</p><p><b>Email</b>: email</p>',
        ]);
    }
}
