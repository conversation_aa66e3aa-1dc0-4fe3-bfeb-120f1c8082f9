<?php

namespace SF\functional\Services\NotificationSystem;

use Salesfloor\Models\RetailerCustomerCommunication;
use Salesfloor\Services\RetailerCustomerNotificationBlackout;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;
use Salesfloor\API\Managers\RetailerCustomerCommunication as RetailerCustomerCommunicationManager;

class NotificationBlackoutCest extends BaseFunctional
{
    /** @var RetailerCustomerNotificationBlackout $notificationService */
    private $notificationService;

    /** @var RetailerCustomerCommunicationManager  $retailerCustomerCommunicationManager*/
    private $retailerCustomerCommunicationManager;

    public function _before($I)
    {
        parent::_before($I);

        $this->notificationService = $this->app['service.retailer.customer.blackout'];
        $this->retailerCustomerCommunicationManager = $this->app['retailer_customer_communication.manager'];
        $this->app['configs']['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
        $this->app['configs']['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24 * 7;
        $this->app['configs']['messaging.text.multiple-recipients.enabled'] = true;
    }

    /**
     * @group database_transaction
     */
    public function testNotBlackoutContactsFromNewRetailerRecordsByEmailShare(FunctionalTester $I)
    {
        $I->wantTo('test to not blackout contacts with new retailer records by email share');

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'CustomerMatchingMatchTable');

        $addresses = [
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ]
        ];

        $args = [
            1,
            ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            RetailerCustomerCommunication::SOURCE_EMAIL
        ];
        $blackoutContacts = $this->getMethod($this->notificationService, 'getBlackoutContacts')->invokeArgs($this->notificationService, $args);
        // no customer should be blackout
        $I->assertEquals(0, count($blackoutContacts));

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_EMAIL);

        $filteredAddresses = [
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ]
        ];
        $this->validate($I, $filteredAddresses, $addresses);
        $I->assertEmpty($blackoutRecipients);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'email'], 0, -1);

        $I->assertEquals(2, count($records));

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12345',
            'source' => 'email',
        ]);

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12346',
            'source' => 'email',
        ]);
    }

    /**
     * @group database_transaction
     */
    public function testNotBlackoutContactsFromNewRetailerRecordsByOneToManySms(FunctionalTester $I)
    {
        $I->wantTo('test to not blackout contacts with new retailer records by one to many sms');

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'CustomerMatchingMatchTable');

        $addresses = [
            [
                'customer_phone_number' => '+14572345561',
                'customer_id'           => 1001,
            ],
            [
                'customer_phone_number' => '+14572345562',
                'customer_id'           => 1002,
            ],
        ];

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_TEXT);

        $filteredAddresses = [
            [
                'customer_phone_number' => '+14572345561',
                'customer_id'           => 1001,
            ],
            [
                'customer_phone_number' => '+14572345562',
                'customer_id'           => 1002,
            ],
        ];
        $this->validate($I, $filteredAddresses, $addresses);
        $I->assertEmpty($blackoutRecipients);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'text'], 0, -1);

        $I->assertEquals(2, count($records));

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12345',
            'source' => 'text',
        ]);

        /** @var RetailerCustomerCommunication $record1 */
        $record1 = $this->retailerCustomerCommunicationManager->getOneOrNull(['retailer_customer_id' => '12345', 'source' => 'text']);
        $I->assertStringContainsString(gmdate('Y-m-d H'), $record1->last_share_time);

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12346',
            'source' => 'text',
        ]);

        /** @var RetailerCustomerCommunication $record2 */
        $record2 = $this->retailerCustomerCommunicationManager->getOneOrNull(['retailer_customer_id' => '12346', 'source' => 'text']);
        $I->assertStringContainsString(gmdate('Y-m-d H'), $record2->last_share_time);
    }

    /**
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromEmailShare(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from email share -> primary/secondary matching');

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'CustomerMatchingMatchTable');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');

        $stubData = $this->getStubDataEmail();

        $addresses         = $stubData['addresses'];
        $filteredAddresses = $stubData['filteredAddresses'];
        $blackoutAddresses = $stubData['blackoutAddresses'];

        $args = [
            1,
            ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            RetailerCustomerCommunication::SOURCE_EMAIL
        ];
        $blackoutContacts = $this->getMethod($this->notificationService, 'getBlackoutContacts')->invokeArgs($this->notificationService, $args);
        // 1 customer(s) should be blackout, and 1 customer(s) start communication
        $I->assertEquals(1, count($blackoutContacts));

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_EMAIL);

        $this->validate($I, $filteredAddresses, $addresses);
        $this->validate($I, $blackoutAddresses, $blackoutRecipients);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'email'], 0, -1);

        $I->assertEquals(3, count($records));

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12345',
            'source' => 'email',
            'last_share_time' => gmdate('Y-m-d 00:00:00', strtotime('-6 day')),
        ]);

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12346',
            'source' => 'email',
        ]);

        /** @var RetailerCustomerCommunication $record1 */
        $record1 = $this->retailerCustomerCommunicationManager->getOneOrNull(['retailer_customer_id' => '12346', 'source' => 'email']);
        $I->assertStringContainsString(gmdate('Y-m-d H'), $record1->last_share_time);

        // 1 customer(s) start communication
        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12347',
            'source' => 'email',
        ]);

        /** @var RetailerCustomerCommunication $record2 */
        $record2 = $this->retailerCustomerCommunicationManager->getOneOrNull(['retailer_customer_id' => '12347', 'source' => 'email']);
        $I->assertStringContainsString(gmdate('Y-m-d H'), $record2->last_share_time);
    }

    /**
     * @param FunctionalTester $I
     * @throws \Exception
     *
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromEmailShareAllMatched(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from email share -> all matched customers');

        $this->app['configs']['retailer.clienteling.customers.communication.blackout.strategy'] = RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER;

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'CustomerMatchingMatchTable');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');

        $stubData = $this->getStubDataEmail();

        $addresses = $stubData['addresses'];

        $args = [
            1,
            ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            RetailerCustomerCommunication::SOURCE_EMAIL
        ];
        $blackoutContacts = $this->getMethod($this->notificationService, 'getBlackoutContacts')->invokeArgs($this->notificationService, $args);

        // 3 customer(s) should be shared and 1 customer(s) get blackout
        $I->assertEquals(1, count($blackoutContacts));

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_EMAIL);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'email'], 0, -1);

        // 3 records which is primary/secondary + 1 new record of all matched (customer_id = 12348)
        $I->assertEquals(3 + 1, count($records));

        // other 3 records already test in the testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromEmailShare()

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12348',
            'source'               => 'email',
        ]);
    }

    /**
     * @param FunctionalTester $I
     * @throws \Exception
     *
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromEmailShareCustomerOnlyMatched(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from email share -> only need matched customers for retailer_customer_id or parent_retailer_customer_id');

        $this->app['configs']['retailer.clienteling.customers.communication.blackout.strategy'] = RetailerCustomerNotificationBlackout::STRATEGY_CUSTOMER_DATA_ONLY;

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');

        $stubData = $this->getStubDataEmail();

        $addresses = $stubData['addresses'];

        $args = [
            1,
            ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            RetailerCustomerCommunication::SOURCE_EMAIL
        ];
        $blackoutContacts = $this->getMethod($this->notificationService, 'getBlackoutContacts')->invokeArgs($this->notificationService, $args);

        // 2 customer(s) should be updated communication, 1 customer should add communication, 1 customer get blackout(no communication updated)
        $I->assertEquals(1, count($blackoutContacts));

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_EMAIL);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'email'], 0, -1);

        // 3 records which is primary/secondary
        $I->assertEquals(3, count($records));

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12346',
            'source'               => 'email',
        ]);

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12347',
            'source'               => 'email',
        ]);
    }

    /**
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromEmailShareAllMatchedWithoutMatchTableMissed(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from email share -> all matched customers without match table updated');

        $this->app['configs']['retailer.clienteling.customers.communication.blackout.strategy'] = RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER;

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');

        $stubData = $this->getStubDataEmail();

        $addresses = $stubData['addresses'];

        $args = [
            1,
            ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            RetailerCustomerCommunication::SOURCE_EMAIL
        ];
        $blackoutContacts = $this->getMethod($this->notificationService, 'getBlackoutContacts')->invokeArgs($this->notificationService, $args);

        // 0 customer(s) should be updated communication, 2 customers get blackout(no communication updated)
        $I->assertEquals(0, count($blackoutContacts));

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_EMAIL);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'email'], 0, -1);

        // 2 emails rows already exist in db per fixture 'CustomerMatching'
        $I->assertEquals(2, count($records));
    }

    /**
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromEmailShareAllMatchedWithoutMatchTableFound(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from email share -> all matched customers without match table updated');

        $this->app['configs']['retailer.clienteling.customers.communication.blackout.strategy'] = RetailerCustomerNotificationBlackout::STRATEGY_CUSTOMER_DATA_ONLY;

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');

        $stubData = $this->getStubDataEmail();

        $addresses = $stubData['addresses'];

        $args = [
            1,
            ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            RetailerCustomerCommunication::SOURCE_EMAIL
        ];
        $blackoutContacts = $this->getMethod($this->notificationService, 'getBlackoutContacts')->invokeArgs($this->notificationService, $args);

        // 1 customer(s) should be updated communication, 1 customer(s) get blackout(no communication updated) and 1 customer(s) should be added to communication
        $I->assertEquals(1, count($blackoutContacts));

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_EMAIL);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'email'], 0, -1);

        // 2 email communication rows already exist in db per fixture 'CustomerMatching', and
        // 1 record which is primary/secondary in sf_customer is matched (customer_id = 12347) and added to communication
        $I->assertEquals(3, count($records));
    }

    /**
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromOneToManySms(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from one to many sms -> primary/secondary matching');

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'CustomerMatchingMatchTable');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');

        $stubData          = $this->getStubDataText();
        $addresses         = $stubData['addresses'];
        $filteredAddresses = $stubData['filteredAddresses'];

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_TEXT);

        $this->validate($I, $filteredAddresses, $addresses);
        $I->assertEmpty($blackoutRecipients);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'text'], 0, -1);

        $I->assertEquals(3, count($records));

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12345',
            'source' => 'text',
            'last_share_time' => gmdate('Y-m-d 00:00:00', strtotime('-6 day')),
        ]);

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12346',
            'source' => 'text',
        ]);

        /** @var RetailerCustomerCommunication $record1 */
        $record1 = $this->retailerCustomerCommunicationManager->getOneOrNull(['retailer_customer_id' => '12346', 'source' => 'text']);
        $I->assertStringContainsString(gmdate('Y-m-d H'), $record1->last_share_time);

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12347',
            'source' => 'text',
        ]);

        /** @var RetailerCustomerCommunication $record2 */
        $record2 = $this->retailerCustomerCommunicationManager->getOneOrNull(['retailer_customer_id' => '12347', 'source' => 'text']);
        $I->assertStringContainsString(gmdate('Y-m-d H'), $record2->last_share_time);
    }

    /**
     * @group database_transaction
     */
    public function testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromOneToManySmsAllMatched(FunctionalTester $I)
    {
        $I->wantTo('test to blackout part of the contacts within blackout period from one to many sms -> all matched customers');

        $this->app['configs']['retailer.clienteling.customers.communication.blackout.strategy'] = RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER;

        $this->insertFixtureGroup($I, 'CustomerMatching');
        $this->insertFixtureGroup($I, 'CustomerMatchingMatchTable');
        $this->insertFixtureGroup($I, 'retailerRecordWithinBlackoutPeriod');
        $this->insertFixtureGroup($I, 'CustomerMatchingAnyOtherMatch');

        $stubData          = $this->getStubDataText();
        $addresses         = $stubData['addresses'];
        $filteredAddresses = $stubData['filteredAddresses'];

        list($addresses, $blackoutRecipients) = $this->notificationService->processBlackout($addresses, 1, RetailerCustomerCommunication::SOURCE_TEXT);

        $records = $this->retailerCustomerCommunicationManager->getAll(['source' => 'text'], 0, -1);

        // 3 records which is primary/secondary + 1 new record of all matched (customer_id = 12348)
        $I->assertEquals(3 + 1, count($records));

        // other 3 records already test in the testBlackoutContactsFromExistingRetailerRecordsWithinBlackoutPeriodFromOneToManySms()

        $I->seeInDatabase('sf_retailer_customer_communication', [
            'retailer_customer_id' => '12348',
            'source'               => 'text',
        ]);
    }

    /**
     * @return array[]
     */
    private function getStubDataEmail(): array
    {
        $addresses = [
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
        ];

        $filteredAddresses = [
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
        ];

        $blackoutAddresses = [
            [
                'first_name' => 'John',
                'email'      => '<EMAIL>',
            ],
        ];

        return [
            'addresses'         => $addresses,
            'filteredAddresses' => $filteredAddresses,
            'blackoutAddresses' => $blackoutAddresses
        ];
    }

    /**
     * @return array[]
     */
    private function getStubDataText(): array
    {
        $addresses = [
            [
                'customer_phone_number' => '+14572345561',
                'customer_id'           => 1001,
            ],
            [
                'customer_phone_number' => '+14572345562',
                'customer_id'           => 1002,
            ],
            [
                'customer_phone_number' => '+14572345563',
                'customer_id'           => 1003,
            ],
            [
                'customer_phone_number' => '+14572345564',
                'customer_id'           => 1004,
            ],
        ];

        $filteredAddresses = [
            [
                'customer_phone_number' => '+14572345561',
                'customer_id'           => 1001,
            ],
            [
                'customer_phone_number' => '+14572345562',
                'customer_id'           => 1002,
            ],
            [
                'customer_phone_number' => '+14572345563',
                'customer_id'           => 1003,
            ],
            [
                'customer_phone_number' => '+14572345564',
                'customer_id'           => 1004,
            ],
        ];

        return [
            'addresses'         => $addresses,
            'filteredAddresses' => $filteredAddresses,
        ];
    }
}
