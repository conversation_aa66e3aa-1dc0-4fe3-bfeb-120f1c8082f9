<?php

namespace SF\functional\Services\NotificationSystem;

use Codeception\Util\Fixtures;

class Fixture
{
    public function services()
    {
        Fixtures::add('CustomerMatching', [
            'sf_customer' => [
                [
                    'ID'                   => 1001,
                    'user_id'              => 1,
                    'email'                => '<EMAIL>',
                    'phone'                => '+14572345561',
                    'localization'         => null,
                    'geo'                  => '',
                    'comment'              => '',
                    'subcribtion_flag'     => 1,
                    'retailer_customer_id' => '12345',
                    'first_name'           => '<PERSON>',
                    'last_name'            => 'Doe',
                ],
                [
                    'ID'                          => 1002,
                    'user_id'                     => 1,
                    'email'                       => '<EMAIL>',
                    'phone'                       => '+14572345562',
                    'localization'                => null,
                    'geo'                         => '',
                    'comment'                     => '',
                    'subcribtion_flag'            => 1,
                    'retailer_parent_customer_id' => '12346',
                    'first_name'                  => '<PERSON>',
                    'last_name'                   => 'Doe',
                ],
                [
                    'ID'                          => 1003,
                    'user_id'                     => 1,
                    'email'                       => '<EMAIL>',
                    'phone'                       => '+14572345563',
                    'localization'                => null,
                    'geo'                         => '',
                    'comment'                     => '',
                    'subcribtion_flag'            => 1,
                    'retailer_parent_customer_id' => '12347',
                    'first_name'                  => 'John',
                    'last_name'                   => 'Doe',
                ],
                [
                    'ID'               => 1004,
                    'user_id'          => 1,
                    'email'            => '<EMAIL>',
                    'phone'            => '+14572345564',
                    'localization'     => null,
                    'geo'              => '',
                    'comment'          => '',
                    'subcribtion_flag' => 1,
                    'first_name'       => 'John',
                    'last_name'        => 'Doe',
                ],
                [
                    'ID'               => 1005,
                    'user_id'          => 1,
                    'email'            => '<EMAIL>',
                    'phone'            => '+14572345565',
                    'localization'     => null,
                    'geo'              => '',
                    'comment'          => '',
                    'subcribtion_flag' => 1,
                    'first_name'       => 'John',
                    'last_name'        => 'Doe',
                ],
            ],
            'sf_retailer_customers' => [
                [
                    'ID'          => 1111,
                    'customer_id' => '12345',
                    'first_name'  => 'John',
                    'last_name'   => 'Doe',
                    'email'       => '<EMAIL>',
                ],
                [
                    'ID'          => 1112,
                    'customer_id' => '12346',
                    'first_name'  => 'John',
                    'last_name'   => 'Doe',
                    'email'       => '<EMAIL>',
                ],
                [
                    'ID'          => 1113,
                    'customer_id' => '12347',
                    'first_name'  => 'John',
                    'last_name'   => 'Doe',
                    'email'       => '<EMAIL>',
                ],
                [
                    'ID'          => 1114,
                    'customer_id' => '12348',
                    'first_name'  => 'John',
                    'last_name'   => 'Doe',
                    'email'       => '<EMAIL>',
                    'phone'       => '+14572345565',
                ],
            ],
        ]);

        Fixtures::add('CustomerMatchingMatchTable', [
            'sf_customers_to_retailer_customers' => [
                [
                    'customer_id'          => 1001,
                    'retailer_customer_id' => 1111,
                    'comment'              => 'retailer_customer_id: 12345',
                ],
                [
                    'customer_id'          => 1002,
                    'retailer_customer_id' => 1112,
                    'comment'              => 'retailer_customer_id: 12346',
                ],
                [
                    'customer_id'          => 1003,
                    'retailer_customer_id' => 1113,
                    'comment'              => 'retailer_customer_id: 12346',
                ],
                [
                    'customer_id'          => 1005,
                    'retailer_customer_id' => 1114,
                    'comment'              => 'default email: <EMAIL>',
                ],
            ]
        ]);

        Fixtures::add('CustomerMatchingAnyOtherMatch', [
            'sf_customers_to_retailer_customers' => [
                [
                    'customer_id'          => 1004,
                    'retailer_customer_id' => 1114,
                    'comment'              => 'any guessed relation',
                ],
            ]
        ]);

        Fixtures::add('retailerRecordWithinBlackoutPeriod', [
            'sf_retailer_customer_communication' => [
                [
                    'retailer_customer_id' => '12345',
                    'source' => 'email',
                    'last_share_time' => gmdate('Y-m-d 00:00:00', strtotime('-6 day')),
                ],
                [
                    'retailer_customer_id' => '12346',
                    'source' => 'email',
                    'last_share_time' => gmdate('Y-m-d 00:00:00', strtotime('-8 day')),
                ],
                [
                    'retailer_customer_id' => '12345',
                    'source' => 'text',
                    'last_share_time' => gmdate('Y-m-d 00:00:00', strtotime('-6 day')),
                ],
                [
                    'retailer_customer_id' => '12346',
                    'source' => 'text',
                    'last_share_time' => gmdate('Y-m-d 00:00:00', strtotime('-8 day')),
                ],
            ],
        ]);
    }
}
