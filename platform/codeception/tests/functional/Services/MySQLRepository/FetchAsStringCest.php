<?php

namespace SF\functional\Services\MySQLRepository;

use DoctriFunctionalTesterAliasne\DBAL\Query\QueryBuilder;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

/** @group database_transaction */
class FetchAsStringCest extends BaseFunctional
{
    public function testFetchAsStringCodeception(FunctionalTester $I)
    {
        // executeQuery() is overwritten codeception/tests/_support/Helper/Driver/Salesfloor.php:37
        $results = $I->grabColumnFromDatabase('sf_customer', 'user_id');

        $this->validateArray(["1", "1"], $results, [], true);

        $results = $I->grabRowsFromDatabase('sf_customer');

        $this->validateArray([
            [
                "ID" => "1",
                "subcribtion_flag" => "0",
            ]
        ], $results, [], true);
    }

    public function testFetchAsStringManager(FunctionalTester $I)
    {
        $customersManager = $this->app['customers.manager'];

        $results = $customersManager->getAll([], 0, -1, false);

        $this->validateArray([
            [
                'ID' => "1",
                'user_id' => "1",
                'name' => '<PERSON>',
                'subcribtion_flag' => "0",
            ],
            [
                'ID' => "2",
                'user_id' => "1",
                'name' => 'Joseph Mallette',
                'subcribtion_flag' => "0",
            ],
        ], $results, [], true);
    }

    public function testFetchAsStringQueryBuilder(FunctionalTester $I)
    {
        /** @var QueryBuilder $qB */
        $qB = $this->app['repositories.mysql']->getQueryBuilder();

        $qB->select('*')
            ->from('sf_customer');

        $results = $qB->executeQuery()->fetchAllAssociative();

        $this->validateArray([
            [
                'ID' => "1",
                'user_id' => "1",
                'name' => 'Joseph Mallette',
                'subcribtion_flag' => "0",
            ],
            [
                'ID' => "2",
                'user_id' => "1",
                'name' => 'Joseph Mallette',
                'subcribtion_flag' => "0",
            ],
        ], $results, [], true);
    }
}
