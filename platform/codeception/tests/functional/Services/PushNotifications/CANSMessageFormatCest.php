<?php

declare(strict_types=1);

namespace SF\functional\Services\PushNotifications;

use Codeception\Util\Fixtures;
use Codeception\Util\Stub;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class CANSMessageFormatCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testBuildRepMessagesObjHappy(FunctionalTester $I)
    {
        $I->wantTo('Test receive valid array of message objects keyed by the rep id');

        $this->insertFixtureGroup($I, 'storeRepData');
        $data = Fixtures::get('storeRepData');
        $repId = $data['wp_users'][0]['ID'];

        $cansPushNoticeData = [
            'ids' => "$repId",
            'type' => 'rep',
            'message' => 'chat_request_sidebar',
            'fromCans' => true,
            'source' => 'sidebar',
            'inApp' => ['event_action' => 'new_chat']
        ];

        $expected = [$repId => json_encode([
            'msg' => $cansPushNoticeData['message'],
            'data' => ['store' => $data['sf_store'][0]['name']]
        ])];

        $result =  $this->app['service.push']->buildRepMessagesObj(
            explode(',', $cansPushNoticeData['ids']),
            $cansPushNoticeData['message'],
            $cansPushNoticeData['source']
        );

        $I->assertSame($expected, $result);
    }
    /** @group database_transaction */
    public function testBuildRepMessagesObjSad(FunctionalTester $I)
    {
        $I->wantTo('Test receive array of message objects keyed by an unknown rep id');

        $this->insertFixtureGroup($I, 'storeRepData');
        $data = Fixtures::get('storeRepData');
        $repId = 7777;

        $cansPushNoticeData = [
            'ids' => "$repId",
            'type' => 'rep',
            'message' => 'chat_request_sidebar',
            'fromCans' => true,
            'source' => 'sidebar',
            'inApp' => ['event_action' => 'new_chat']
        ];


        $result =  $this->app['service.push']->buildRepMessagesObj(
            explode(',', $cansPushNoticeData['ids']),
            $cansPushNoticeData['message'],
            true
        );

        $I->assertEmpty($result);
    }

    /** @group database_transaction */
    public function testBuildMessagesObjForOriginStoreMode(FunctionalTester $I)
    {
        $I->wantTo('Test receive valid array of message objects keyed by the rep id when origin store mode is enabled');


        $cansPushNoticeData = [
            'message' => 'chat_request_sidebar',
            'destination' => 'fake Store',
        ];

        $expected = [
            1 => json_encode([
            'msg' => $cansPushNoticeData['message'],
            'data' => ['store' => $cansPushNoticeData['destination']]
            ]),
            2 => json_encode([
                'msg' => $cansPushNoticeData['message'],
                'data' => ['store' => $cansPushNoticeData['destination']]
            ])];

        $result =  $this->app['service.push']->buildMessagesObjFor(
            [1,2],
            $cansPushNoticeData['message'],
            $cansPushNoticeData['destination']
        );

        $I->assertSame($expected, $result);
    }

    public function testBuildMessageForOriginStoreWhenShowOriginLocationConfigEnabled(FunctionalTester $I)
    {
        $I->wantTo('Test message objects sets store to the origin store name when routing config to show origin location is enabled');
        $fakeService = Stub::make($this->app['service.push'], [
            'publish' => function (
                $message,
                $inapp,
                $endpoints
            ) use ($I) {
                $expected = [
                    1 => '{"msg":"chat_request_sidebar","data":{"store":"carry go"}}',
                    2 => '{"msg":"chat_request_sidebar","data":{"store":"carry go"}}'
                ];
                $I->assertEquals($expected, $message);
                return;
            },
            'getRepEndpoints' => []
        ]);
        $configs = $this->app['configs'];
        $configs['retailer.chat.routing.notification.show_origin_location'] = true;
        $pQueue = Stub::make($this->app['service.push.publish.queue'], [
            'pushService' => $fakeService,
            'configs' => $configs,
            'redis' => Stub::makeEmpty($this->app['predis'], ['exists' => function ($id) {
                return false;
            }, 'setex' => function ($id) {
            }]),
        ]);

        $data = [
            'type' => 'rep',
            'ids' => '1,2',
            'message' => 'chat_request_sidebar',
            'inapp' => [],
            'unique_id' => 'XYZ',
            'fromCans' => true,
            'destination' => 'carry go',
            'source' => 'sidebar'
          ];

        $pQueue->handleQueueData($this->app['repositories.mysql'], $data);
    }

    public function testBuildMessageForRepStoreWhenShowOriginLocationConfigDisabled(FunctionalTester $I)
    {
        $I->wantTo('Test message objects sets store to the rep store name when routing config to show origin location is disabled');
        $this->insertFixtureGroup($I, 'storeRepData');

        $fakeService = Stub::make($this->app['service.push'], [
            'publish' => function (
                $message,
                $inapp,
                $endpoints
            ) use ($I) {
                $data = Fixtures::get('storeRepData');
                $repId1 = $data['wp_users'][0]['ID'];
                $repId2 = $data['wp_users'][1]['ID'];
                $expected = [
                    $repId1 => '{"msg":"chat_request_sidebar","data":{"store":"Yawa Store"}}',
                    $repId2 => '{"msg":"chat_request_sidebar","data":{"store":"Away Store"}}'
                ];
                $I->assertEquals($expected, $message);
                return;
            },
            'getRepEndpoints' => [],
            'db' => $this->app['repositories.mysql']
        ]);

        $configs = $this->app['configs'];
        $configs['retailer.chat.routing.notification.show_origin_location'] = false;
        $pQueue = Stub::make($this->app['service.push.publish.queue'], [
            'pushService' => $fakeService,
            'configs' => $configs,
            'redis' => Stub::makeEmpty($this->app['predis'], ['exists' => function ($id) {
                return false;
            }, 'setex' => function ($id) {
            }]),
        ]);

        $data = [
            'type' => 'rep',
            'ids' => '9001,9000',
            'message' => 'chat_request_sidebar',
            'inapp' => [],
            'unique_id' => 'XYZ',
            'fromCans' => true,
            'destination' => 'carry go',
            'source' => 'sidebar'
        ];

        $pQueue->handleQueueData($this->app['repositories.mysql'], $data);
    }
    public function testBuildMessageForRepStoreWhenShowOriginLocationConfigEnabledAndForStoreFront(FunctionalTester $I)
    {
        $I->wantTo('Test message objects sets store to the rep store name when routing config to show origin location is enabled and request is storefront');
        $this->insertFixtureGroup($I, 'storeRepData');
        $configs = $this->app['configs'];
        $fakeService = Stub::make($this->app['service.push'], [
            'configs' =>   $configs,
            'publish' => function (
                $message,
                $inapp,
                $endpoints
            ) use ($I) {
                $data = Fixtures::get('storeRepData');
                $repId1 = $data['wp_users'][0]['ID'];
                $expected = [
                    $repId1 => '{"msg":"chat_request_storefront","data":{"name":"Loi owl"}}',
                ];
                $I->assertEquals($expected, $message);
                return;
            },
            'getRepEndpoints' => [],
            'db' => $this->app['repositories.mysql']
        ]);

        $configs['retailer.chat.routing.notification.show_origin_location'] = true;
        $pQueue = Stub::make($this->app['service.push.publish.queue'], [
            'pushService' => $fakeService,
            'configs' => $configs,
            'redis' => Stub::makeEmpty($this->app['predis'], ['exists' => function ($id) {
                return false;
            }, 'setex' => function ($id) {
            }]),
        ]);

        $data = [
            'type' => 'rep',
            'ids' => '9000',
            'message' => 'chat_request_storefront',
            'inapp' => [],
            'unique_id' => 'XYZ',
            'fromCans' => true,
            'destination' => 'carry go',
            'source' => 'storefront'
        ];

        $pQueue->handleQueueData($this->app['repositories.mysql'], $data);
    }
}
