<?php

declare(strict_types=1);

namespace SF\functional\Services\PushNotifications;

use Codeception\Stub;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Salesfloor\Services\PushNotifications\SnsConfig;
use Salesfloor\Services\PushNotifications\SnsConfig as PushNotificationConfig;
use SF\FunctionalTester;
use SF\functional\BaseFunctional;
use Salesfloor\Services\PushNotifications\FCMBackend;
use Salesfloor\Services\PushNotifications\Service;
use SF\Helper\Traits\Asserts;

class PushNotificationServiceCest extends BaseFunctional
{
    use Asserts;

    /** @group database_transaction */
    public function testPublishWhenMessageIsString(FunctionalTester $I)
    {
        $I->wantTo('Test "publish" when message is a string');

        $this->insertFixtureGroup($I, 'storeRepData');
        $backend = Stub::make(FCMBackend::class, [
            'supportBatches' => function () {
                return true;
            },
            'createPublishObject' => function (
                $locale,
                array $payload,
                array $endpoints,
                array &$accumulator
            ) use ($I) {
                $I->assertEquals('en_US', $locale);
                $I->assertEquals(
                    [
                        'GCM' => [
                            'data' => [
                                'alert' => 'You have a new Ask & Answer Request',
                                'title' => 'Salesfloor',
                                'vibrate' => 'true',
                                'message' => 'You have a new Ask & Answer Request',
                                'event_action' => 'new_request',
                                'request_id' => 'contact_me_8275',
                                'alertBox' => 'false',
                                'chat_handoff' => 0,
                                'soundname' => 'default',
                            ],
                            'priority' => 'high',
                        ],
                        'APNS' => [
                            'aps' => [
                                'alert' => 'You have a new Ask & Answer Request',
                                'title' => 'Salesfloor',
                                'vibrate' => 'true',
                                'message' => 'You have a new Ask & Answer Request',
                                'event_action' => 'new_request',
                                'request_id' => 'contact_me_8275',
                                'alertBox' => 'false',
                                'chat_handoff' => 0,
                                'sound' => 'default',
                            ],
                        ],
                        'APNS_SANDBOX' => [
                            'aps' => [
                                'alert' => 'You have a new Ask & Answer Request',
                                'title' => 'Salesfloor',
                                'vibrate' => 'true',
                                'message' => 'You have a new Ask & Answer Request',
                                'event_action' => 'new_request',
                                'request_id' => 'contact_me_8275',
                                'alertBox' => 'false',
                                'chat_handoff' => 0,
                                'sound' => 'default',
                            ],
                        ],
                        'WEBPUSH' => [
                            'data' => [
                                'alert' => 'You have a new Ask & Answer Request',
                                'title' => 'Salesfloor',
                                'vibrate' => 'true',
                                'message' => 'You have a new Ask & Answer Request',
                                'event_action' => 'new_request',
                                'request_id' => 'contact_me_8275',
                                'alertBox' => 'false',
                                'chat_handoff' => 0,
                                'sound' => 'https://cdn.salesfloor.net/salesfloor-assets/salesfloor/note.mp3',
                            ],
                        ],
                    ],
                    $payload
                );
                $I->assertEquals(
                    [[
                      'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/eLGUFEa7R-4:....IQhRg57Kpe',
                      'token' => 'eLGUFEa7R-4:APA91bFqp8o54....XPy-HePAeZhh7HmvIQhRg57Kpe',
                      'device_token' => 'device-endpoint',
                      'badge' => 0,
                    ]],
                    $endpoints
                );
                $accumulator = [
                    'message',
                ];
                return;
            },
            'publishBatches' => function (array &$messagesByBadges) use ($I) {
                $I->assertEquals(['message'], $messagesByBadges);
                return [[],[]];
            },
            'updateValidatedAndCleanExpired' => function () {
            },
            'getDeviceEndpoint' => function () {
                return 'device-endpoint';
            },
        ]);

        $mysql = $this->app['mysql.importer'];
        $fakeService = Stub::construct(
            Service::class,
            [
                $backend,
                $mysql,
                $this->app
            ]
        );
        $fakeService->publish(
            'pn_you_have_new_email_me_request',
            [
                'event_action' => 'new_request',
                'request_id' => 'contact_me_8275',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                1 => [
                    'endpointIds' => [
                        [
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/eLGUFEa7R-4:....IQhRg57Kpe',
                            'token' => 'eLGUFEa7R-4:APA91bFqp8o54....XPy-HePAeZhh7HmvIQhRg57Kpe',
                        ],
                    ],
                    'badge' => 264,
                ],
            ]
        );
    }

    /** @group database_transaction */
    public function testPublishWhenMessageIsArray(FunctionalTester $I)
    {
        $I->wantTo('Test "publish" when message is an array');

        $this->insertFixtureGroup($I, 'storeRepData');
        $backend = Stub::make(FCMBackend::class, [
            'supportBatches' => function () {
                return true;
            },
            'createPublishObject' => function (
                $locale,
                array $payload,
                array $endpoints,
                array &$accumulator
            ) use ($I) {
                $I->assertEquals('en_US', $locale);
                $I->assertEquals(
                    [
                        'GCM' => [
                          'data' => [
                            'alert' => 'You have a new %emailMeRequestLabel%',
                            'title' => 'Salesfloor',
                            'vibrate' => 'true',
                            'message' => 'You have a new %emailMeRequestLabel%',
                            'event_action' => 'new_request',
                            'request_id' => 'contact_me_8275',
                            'alertBox' => 'false',
                            'chat_handoff' => 0,
                            'soundname' => 'default',
                          ],
                          'priority' => 'high',
                        ],
                        'APNS' => [
                          'aps' => [
                            'alert' => 'You have a new %emailMeRequestLabel%',
                            'title' => 'Salesfloor',
                            'vibrate' => 'true',
                            'message' => 'You have a new %emailMeRequestLabel%',
                            'event_action' => 'new_request',
                            'request_id' => 'contact_me_8275',
                            'alertBox' => 'false',
                            'chat_handoff' => 0,
                            'sound' => 'default',
                          ],
                        ],
                        'APNS_SANDBOX' => [
                          'aps' => [
                            'alert' => 'You have a new %emailMeRequestLabel%',
                            'title' => 'Salesfloor',
                            'vibrate' => 'true',
                            'message' => 'You have a new %emailMeRequestLabel%',
                            'event_action' => 'new_request',
                            'request_id' => 'contact_me_8275',
                            'alertBox' => 'false',
                            'chat_handoff' => 0,
                            'sound' => 'default',
                          ],
                        ],
                        'WEBPUSH' => [
                            'data' => [
                                'alert' => 'You have a new %emailMeRequestLabel%',
                                'title' => 'Salesfloor',
                                'vibrate' => 'true',
                                'message' => 'You have a new %emailMeRequestLabel%',
                                'event_action' => 'new_request',
                                'request_id' => 'contact_me_8275',
                                'alertBox' => 'false',
                                'chat_handoff' => 0,
                                'sound' => 'https://cdn.salesfloor.net/salesfloor-assets/salesfloor/note.mp3',
                            ],
                        ],
                    ],
                    $payload
                );
                $I->assertEquals(
                    [[
                      'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/eLGUFEa7R-4:....IQhRg57Kpe',
                      'token' => 'eLGUFEa7R-4:APA91bFqp8o54....XPy-HePAeZhh7HmvIQhRg57Kpe',
                      'device_token' => 'device-endpoint',
                      'badge' => 0,
                    ]],
                    $endpoints
                );
                $accumulator = [
                    'message',
                ];
                return;
            },
            'publishBatches' => function (array &$messagesByBadges) use ($I) {
                $I->assertEquals(['message'], $messagesByBadges);
                return [[],[]];
            },
            'updateValidatedAndCleanExpired' => function () {
            },
            'getDeviceEndpoint' => function () {
                return 'device-endpoint';
            },
        ]);

        $mysql = $this->app['mysql.importer'];
        $fakeService = Stub::construct(
            Service::class,
            [
                $backend,
                $mysql,
                $this->app
            ]
        );
        $fakeService->publish(
            [
                1 => '{"msg":"pn_you_have_new_email_me_request","data":{"store":"Fake Mall"}}',
            ],
            [
                'event_action' => 'new_request',
                'request_id' => 'contact_me_8275',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                1 => [
                    'endpointIds' => [
                        [
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/eLGUFEa7R-4:....IQhRg57Kpe',
                            'token' => 'eLGUFEa7R-4:APA91bFqp8o54....XPy-HePAeZhh7HmvIQhRg57Kpe',
                        ],
                    ],
                    'badge' => 264,
                ],
            ]
        );
    }

     /** @group database_transaction */
    public function testDeviceTokensCleaning(FunctionalTester $I)
    {
        $this->insertFixtureGroup($I, 'deviceTokens');

        $backend = Stub::construct(
            FCMBackend::class,
            [
               $this->app['configs']['fcm.project_id'],
               $this->app['configs']['fcm.key'],
               $this->app['configs']['fcm.key.secret_file'],
               $this->app['configs']['fcm.version'],
               $this->app['configs']['fcm.verification.url'],
               new PushNotificationConfig($this->app['configs']['env'], $this->app['configs']['retailer.short_name']),
               $this->app['logger'],
               $this->app['devices.manager'],
            ],
            [
            'createPublishObject' => function (
                $locale,
                array $payload,
                array $endpoints,
                array &$accumulator
            ) {
                return [];
            },
            'publishBatches' => function (array &$messagesByBadges) use ($I) {
                return [
                    ['A00001', 'A00002'],
                    ['B00001', 'B00002', 'B00003']];
            },
            'getDeviceEndpoint' => function () {
                return 'device-endpoint';
            },
            'supportBatches' => function () {
                return false;
            },
            ]
        );

        $mysql = $this->app['mysql.importer'];
        $fakeService = Stub::construct(
            Service::class,
            [
                $backend,
                $mysql,
                $this->app
            ],
        );

        $fakeService->publish(
            '{"msg":"pn_you_have_new_email_me_request","data":{"store":"Fake Mall"}}',
            [
                'event_action' => 'new_request',
                'request_id' => 'contact_me_8275',
                'alertBox' => 'false',
                'chat_handoff' => 0,
              ],
            [
                1 => [
                    'endpointIds' => [
                        [
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/eLGUFEa7R-4:....IQhRg57Kpe',
                            'token' => 'eLGUFEa7R-4:APA91bFqp8o54....XPy-HePAeZhh7HmvIQhRg57Kpe',
                        ],
                    ],
                    'badge' => 264,
                ],
              ]
        );
        // 2 tokens are updated and 3 are deleted
        $dateToday = date('Y-m-d');
        $result = $I->executeQuery("SELECT count(*) as count FROM sf_devices WHERE token = 'A00001' AND DATE(last_validated) = ?", [$dateToday]);
        $row = $result->fetch();
        $I->assertEquals(1, $row['count']);

        $result = $I->executeQuery("SELECT count(*) as count FROM sf_devices WHERE token = 'A00002' AND DATE(last_validated) = ?", [$dateToday]);
        $row = $result->fetch();
        $I->assertEquals(1, $row['count']);

        $I->dontSeeInDatabase('sf_devices', ['token' => 'B00001']);
        $I->dontSeeInDatabase('sf_devices', ['token' => 'B00002']);
        $I->dontSeeInDatabase('sf_devices', ['token' => 'B00003']);
    }

    /**
     * This is needed because leads-count is called via SalesfloorApiRepository and transaction aren't shared
     * across connection.
     *
     * @group refresh_database
     */
    public function testPublishToCorrectStoresOnMultipleTargets(FunctionalTester $I)
    {
        $I->wantTo('Test push notification message is correctly maps to the correct store on cross store messaging');

        $this->insertFixtureGroup($I, 'storeRepData');
        $configs = $this->app['configs'];
        $pushNotificationConfig = new SnsConfig($configs['env'], $configs['retailer.short_name']);
        $endPoints = [
            [
                'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                'token' => 'new_android_token',
                'device_token' => 'device-endpoint',
                'badge' => 0,
                'store' => 2000,
            ],
            [
                'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token_2',
                'token' => 'new_apple_token_2',
                'device_token' => 'device-endpoint',
                'badge' => 0,
                'store' => 1000,
            ],

        ];
        $data = [
            'alert' => 'Incoming Sidebar Chat Request for Yawa Store',
            'title' => 'Salesfloor',
            'vibrate' => 'true',
            'event_action' => 'new_chat',
            'request_id' => 'contact_me_19',
            'alertBox' => 'false',
            'chat_handoff' => '0',
            'sound' => 'www/popcorn.wav',
            'badge' => '0'
        ];
        $data['message'] = $data['alert'];
        $data2 = $data;
        $data2['alert'] = 'Incoming Sidebar Chat Request for Away Store';
        $data2['message'] = $data2['alert'];
        $gcmExtra = ['soundname' => 'popcorn', 'android_channel_id' => 'sf-live-chat-notification'];

        $gcmData = array_merge($data, $gcmExtra);
        unset($gcmData['sound']);
        $gcmData2 = array_merge($data2, $gcmExtra);
        unset($gcmData2['sound']);

        $webPushData = $data;
        $webPushData['sound'] = 'https://cdn.salesfloor.net/salesfloor-assets/salesfloor/popcorn.mp3';
        unset($webPushData['badge']);

        $webPushData2 = $webPushData;
        $webPushData2['alert'] = $webPushData2['message'] = $data2['alert'];

        $outcome = [
            'en_US_0_2000' => [
                'messageMultiDevices' => [
                    'default' => "" ,
                    'GCM' => [
                        'data' => $gcmData,
                        'priority' => 'high',
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                    'APNS' => [
                        'aps' => $data,
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                    'APNS_SANDBOX' => [
                        'aps' => $data,
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                    'WEBPUSH' => [
                        'data' => $webPushData,
                        'options' => [
                            'realtime' => true,
                        ],
                    ]
                ],
                'android' => [
                    'data' => $gcmData,
                    'payload' => [
                        'priority' => FCMBackend::ANDROID_PRIORITY_HIGH,
                        'notification' => [
                            'title' =>  $gcmData['title'],
                            'body' => $gcmData['message'],
                            'notification_count' => (int)$gcmData['badge'],
                            'sound' => $gcmData['soundname'],
                            'icon' => 'ic_notify',
                            'click_action' =>  'com.adobe.phonegap.push.background.MESSAGING_EVENT',
                            'channel_id' => 'sf-live-chat-notification',
                        ],
                        'ttl' => '0s',
                    ],
                    'endpoints' => array_slice($endPoints, 0, 1)
                ],

            ],
            'en_US_0_1000' => [
                'messageMultiDevices' => [
                    'default' => "" ,
                    'GCM' =>  [
                        'data' => $gcmData2,
                        'priority' => 'high',
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                    'APNS' => [
                        'aps' => $data2,
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                    'APNS_SANDBOX' => [
                        'aps' => $data2,
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                    'WEBPUSH' => [
                        'data' => $webPushData2,
                        'options' => [
                            'realtime' => true,
                        ],
                    ],
                ],
                'apns' => [
                    'data' => $data2,
                    'payload' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' =>  $data2['title'],
                                    'body' => $data2['message'],
                                ],
                                'badge' => $data2['badge'],
                                'sound' => $data2['sound'],
                                'interruption-level' => FCMBackend::APPLE_INTERRUPTION_LEVEL_TIME_SENSITIVE,
                            ],
                        ],
                        'headers' => [
                            'apns-priority' => 10,
                            'apns-expiration' => 0,
                        ],
                    ],
                    'endpoints' => array_slice($endPoints, 1, 1)
                ],
            ],
        ];
        $backend = Stub::make(FCMBackend::class, [
            'supportBatches' => function () {
                return true;
            },
            'publishBatches' => function (array &$messagesByBadges) use ($I, $outcome) {
                $I->assertEquals($outcome, $messagesByBadges);
                return [[], []];
            },
            'getDeviceEndpoint' => function () {
                return 'device-endpoint';
            },
            'updateValidatedAndCleanExpired' => function () {
            },
            'snsConfig' => $pushNotificationConfig
        ]);

        $mysql = $this->app['mysql.importer'];
        $fakeService = Stub::construct(
            Service::class,
            [
                $backend,
                $mysql,
                $this->app
            ]
        );
        $fakeService->publish(
            [
                9000 => '{"msg":"chat_request_sidebar","data":{"store":"Yawa Store"}}',
                9001 => '{"msg":"chat_request_sidebar","data":{"store":"Away Store"}}',
            ],
            [
                'event_action' => 'new_chat',
                'request_id' => 'contact_me_19',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                9000 => [
                    'endpointIds' => [
                        [
                            'store' => 2000,
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                            'token' => 'new_android_token',
                        ],
                    ],
                    'badge' => 0,
                ],
                9001 => [
                    'endpointIds' => [
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:apple:v1:1:com.salesfloor.salesfloor/new_apple_token_2',
                            'token' => 'new_apple_token_2',
                        ],
                    ],
                    'badge' => 0,
                ],
            ]
        );
    }

    public function testPushNotificationWebPushChat(FunctionalTester $I)
    {
        $I->wantTo('Test push notification - webpush - chat');

        $payload = [
            [
                9000 => '{"msg":"chat_request_sidebar","data":{"store":"Yawa Store"}}',
                9001 => '{"msg":"chat_request_sidebar","data":{"store":"Away Store"}}',
            ],
            [
                'event_action' => 'new_chat',
                'request_id' => 'contact_me_19',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                9000 => [
                    'endpointIds' => [
                        [
                            'store' => 2000,
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                            'token' => 'new_android_token',
                        ],
                    ],
                    'badge' => 0,
                ],
                9001 => [
                    'endpointIds' => [
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:web:v1:1:com.salesfloor.salesfloor/new_webpush_token',
                            'token' => 'new_webpush_token',
                        ],
                    ],
                    'badge' => 0,
                ],
            ]
        ];

        $this->mockPushNotification($I, __FUNCTION__, $payload);
    }

    public function testPushNotificationWebPushNotChat(FunctionalTester $I)
    {
        $I->wantTo('Test push notification - webpush - not chat');

        $payload = [
            '{"msg":"pn_you_have_new_email_me_request","data":{"store":"Fake Mall"}}',
            [
                'event_action' => 'new_request',
                'request_id' => 'contact_me_8275',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                1 => [
                    'endpointIds' => [
                        [
                            'endpoint_id' => 'fcm:web:v1:0:Salesfloor Mobile App/eLGUFEa7R-4:....IQhRg57Kpe',
                            'token' => 'eLGUFEa7R-4:APA91bFqp8o54....XPy-HePAeZhh7HmvIQhRg57Kpe',
                        ],
                    ],
                    'badge' => 264,
                ],
            ]
        ];

        $this->mockPushNotification($I, __FUNCTION__, $payload);
    }

    public function testPushNotificationWebPushNotChatTeamMode(FunctionalTester $I)
    {
        $I->wantTo('Test push notification - webpush - storefront request - team mode');

        // Message structure is different
        $payload = [
            'pn_you_have_new_email_me_request',
            [
                'event_action' => 'new_request',
                'request_id' => 'contact_me_8275',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                9000 => [
                    'endpointIds' => [
                        [
                            'store' => 2000,
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                            'token' => 'new_android_token',
                        ],
                    ],
                    'badge' => 0,
                ],
                9001 => [
                    'endpointIds' => [
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:web:v1:1:com.salesfloor.salesfloor/new_webpush_token',
                            'token' => 'new_webpush_token',
                        ],
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:web:v1:1:com.salesfloor.salesfloor/new_webpush_token2',
                            'token' => 'new_webpush_token2',
                        ],
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:web:v1:1:com.salesfloor.salesfloor/new_webpush_token3',
                            'token' => 'new_webpush_token2',
                        ],
                    ],
                    'badge' => 0,
                ],
            ]
        ];

        $this->mockPushNotification($I, __FUNCTION__, $payload);
    }

    /** @group database_transaction */
    public function testPushNotificationWebPushException400(FunctionalTester $I)
    {
        $capturedRequests = [];
        $mock = new MockHandler([
            new Response(200, [], json_encode([])), // Android call
            new Response(
                400,
                [],
                '[{   "error": {     "code": 400,     "message": "Request contains an invalid argument.",     "status": "INVALID_ARGUMENT",     "details": [       {         "@type": "type.googleapis.com/google.firebase.fcm.v1.FcmError",         "errorCode": "INVALID_ARGUMENT"       },       {         "@type": "type.googleapis.com/google.rpc.BadRequest",         "fieldViolations": [           {             "field": "message.token",             "description": "Invalid registration token"           }         ]       }     ]   } } ]'
            ),
            new Response(200, [], json_encode([])),
        ]);

        $service = $this->mockPushNotificationException($I, $mock, $capturedRequests);

        $payload = [
            [
                9000 => '{"msg":"chat_request_sidebar","data":{"store":"Yawa Store"}}',
                9001 => '{"msg":"chat_request_sidebar","data":{"store":"Away Store"}}',
            ],
            [
                'event_action' => 'new_chat',
                'request_id' => 'contact_me_19',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                9000 => [
                    'endpointIds' => [
                        [
                            'store' => 2000,
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                            'token' => 'new_android_token',
                        ],
                    ],
                    'badge' => 0,
                ],
                9001 => [
                    'endpointIds' => [
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:web:v1:1:com.salesfloor.salesfloor/new_webpush_token',
                            'token' => 'new_webpush_token',
                        ],
                    ],
                    'badge' => 0,
                ],
            ]
        ];

        $service->publish(...$payload);

        // $capturedRequests has now 3 entries
        // Android - 200
        // Webpush - 400 => change TTL
        // Webpush - 200 => with new TTL (1)

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson(array_map(function ($request) {
            $body = $request->getBody();
            $body->rewind();
            return $body->getContents();
        }, $capturedRequests), $jsonFile);
    }

    /** @group database_transaction */
    public function testPushNotificationWebPushException404(FunctionalTester $I)
    {
        $capturedRequests = [];
        $mock = new MockHandler([
            new Response(404, [], json_encode([])), // Android call
            new Response(
                404,
                [],
                '[{   "error": {     "code": 404,     "message": "Request contains an invalid argument.",     "status": "INVALID_ARGUMENT",     "details": [       {         "@type": "type.googleapis.com/google.firebase.fcm.v1.FcmError",         "errorCode": "INVALID_ARGUMENT"       },       {         "@type": "type.googleapis.com/google.rpc.BadRequest",         "fieldViolations": [           {             "field": "message.token",             "description": "Invalid registration token"           }         ]       }     ]   } } ]'
            ), // Webpush call
        ]);

        $service = $this->mockPushNotificationException($I, $mock, $capturedRequests);

        $payload = [
            [
                9000 => '{"msg":"chat_request_sidebar","data":{"store":"Yawa Store"}}',
                9001 => '{"msg":"chat_request_sidebar","data":{"store":"Away Store"}}',
            ],
            [
                'event_action' => 'new_chat',
                'request_id' => 'contact_me_19',
                'alertBox' => 'false',
                'chat_handoff' => 0,
            ],
            [
                9000 => [
                    'endpointIds' => [
                        [
                            'store' => 2000,
                            'endpoint_id' => 'fcm:android:v1:0:com.salesfloor.salesfloor/new_android_token',
                            'token' => 'new_android_token',
                        ],
                    ],
                    'badge' => 0,
                ],
                9001 => [
                    'endpointIds' => [
                        [
                            'store' => 1000,
                            'endpoint_id' => 'fcm:web:v1:1:com.salesfloor.salesfloor/new_webpush_token',
                            'token' => 'new_webpush_token',
                        ],
                    ],
                    'badge' => 0,
                ],
            ]
        ];

        $service->publish(...$payload);

        // $capturedRequests has now 3 entries
        // Android - 200
        // Webpush - 400 => change TTL
        // Webpush - 200 => with new TTL (1)

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson(array_map(function ($request) {
            $body = $request->getBody();
            $body->rewind();
            return $body->getContents();
        }, $capturedRequests), $jsonFile);
    }

    private function mockPushNotificationException($I, MockHandler $mock, &$capturedRequests)
    {
        $this->insertFixtureGroup($I, 'storeRepData');

        $configs = $this->app['configs'];

        $backend = Stub::construct(
            FCMBackend::class,
            [
                $configs['fcm.project_id'],
                $configs['fcm.key'],
                $configs['fcm.key.secret_file'],
                $configs['fcm.version'],
                $configs['fcm.verification.url'],
                new SnsConfig($configs['env'], $configs['retailer.short_name']),
                $this->app['logger'],
                $this->app['devices.manager'],
                $configs['firebase.service_account'] ?? null,
            ],
            [
                'supportBatches' => function () {
                    return true;
                },
                'getDeviceEndpoint' => function () {
                    return 'device-endpoint';
                },
                'updateValidatedAndCleanExpired' => function () {
                },
                'getAccessToken' => function () {
                    return '';
                }
            ]
        );

        $capture = function (array &$requests): callable {
            return function (callable $handler) use (&$requests) {
                return function ($request, array $options) use ($handler, &$requests) {
                    $requests[] = $request;
                    return $handler($request, $options);
                };
            };
        };

        // Hack to keep the middleware
        $method = $this->getMethod($backend, 'getClient');
        $client = $method->invokeArgs($backend, []);

        $stack = $client->getConfig('handler');

        $stack->push($capture($capturedRequests), 'capture');

        // Replace the base handler with the mock handler
        $reflection = new \ReflectionObject($stack);
        $handlerProp = $reflection->getProperty('handler');
        $handlerProp->setAccessible(true);
        $handlerProp->setValue($stack, $mock);

        $backend->setClient($client);

        $fakeService = Stub::construct(
            Service::class,
            [
                $backend,
                $this->app['mysql.importer'],
                $this->app
            ]
        );

        return $fakeService;
    }

    private function mockPushNotification($I, string $testName, array $payload)
    {
        $this->insertFixtureGroup($I, 'storeRepData');

        $configs = $this->app['configs'];

        $backend = Stub::make(FCMBackend::class, [
            'supportBatches' => function () {
                return true;
            },
            'publishBatches' => function (array &$messagesByBadges) use ($I, $testName) {

                $jsonFile = sprintf('%s/files/%s.json', __DIR__, $testName);
                $this->assertSnapshotAsJson($messagesByBadges, $jsonFile);

                // Good/Bad token not important
                return [[], []];
            },
            'getDeviceEndpoint' => function () {
                return 'device-endpoint';
            },
            'updateValidatedAndCleanExpired' => function () {
            },
            'snsConfig' =>  new SnsConfig($configs['env'], $configs['retailer.short_name'])
        ]);

        $fakeService = Stub::construct(
            Service::class,
            [
                $backend,
                $this->app['mysql.importer'],
                $this->app
            ]
        );

        $fakeService->publish(...$payload);
    }
}
