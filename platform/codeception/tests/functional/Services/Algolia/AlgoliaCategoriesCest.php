<?php

namespace SF\functional\Services\Algolia;

use SF\FunctionalTester;

/**
 * This is mostly to test category and category_id.
 * Those now accepted multiple categories and "10" nested category (configurable).
 * I don't validate that the category linked to the product is lower than 4 (limitation by product)
 *
 * @group database_transaction
 */
class AlgoliaCategoriesCest extends Base
{
    public function testAlgoliaCategoriesBasic(FunctionalTester $I)
    {
        $I->wantTo('test - algolia category - output from base dump');

        // Default DB is without variant
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = false;

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(31, count($products));
    }

    public function testAlgoliaCategoriesProductAny(FunctionalTester $I)
    {
        $I->wantTo('test - algolia category - product assigned to level 1');

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'testAlgoliaCategoriesProductAny');

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(32, count($products));
    }

    public function testAlgoliaCategoriesProductMultipleCategories(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'testAlgoliaCategoriesProductMultipleCategories');

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(32, count($products));
    }

    public function testAlgoliaCategoriesL4(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'testAlgoliaCategoriesL4');

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(32, count($products));
    }

    public function testAlgoliaCategoriesI18N(FunctionalTester $I)
    {
        // Current DB has some category with missing i18n categories.
        // Algolia will ignore them
        $I->wantTo('test - algolia category - I18N');

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = true;

        $this->insertFixtureGroup($I, 'testAlgoliaCategoriesI18N');

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(32, count($products));
    }

    public function testAlgoliaCategoriesNestedMax(FunctionalTester $I)
    {
        // If we reach the max threshold (10) (even though the limit is 4)
        // We will not add them in algolia, so it will be something like ...>L6>L5>L4
        $I->wantTo('test - algolia category - Max nested tree');

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'testAlgoliaCategoriesNestedMax');

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(32, count($products));
    }

    public function testAlgoliaCategoriesCircularReference(FunctionalTester $I)
    {
        // If you have a circular reference, (category already part of the path), it will stop
        $I->wantTo('test - algolia category - circular reference');

        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled'] = false;

        $this->insertFixtureGroup($I, 'testAlgoliaCategoriesCircularReference');

        $products = $this->getProcessedProducts();

        $this->validateFromSnapshot(sprintf('%s/files/%s.json', __DIR__, __FUNCTION__), $products);

        $I->assertEquals(32, count($products));
    }

    private function validateFromSnapshot($jsonFile, array $products)
    {
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($products, JSON_PRETTY_PRINT));
        }

        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $products);
    }
}
