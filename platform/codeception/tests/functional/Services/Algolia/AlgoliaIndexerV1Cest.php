<?php

namespace SF\functional\Services\Algolia;

use SF\FunctionalTester;

class AlgoliaIndexerV1Cest extends Base
{
    public function _before($I)
    {
        parent::_before($I);
        date_default_timezone_set('UTC');
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOnI18nOff(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['retailer.i18n.is_enabled']           = false;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($processedProducts, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $processedProducts);
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOnI18nOn(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['products.expanded_variants.priority_badge.enabled'] = true;
        $this->app['configs']['retailer.i18n.is_enabled']           = true;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        if (getenv('snapshot')) {
            file_put_contents($jsonFile, json_encode($processedProducts, JSON_PRETTY_PRINT));
        }
        $expected = json_decode(file_get_contents($jsonFile), true);
        $this->validateArray($expected, $processedProducts);
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOffI18nOff(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled']           = false;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();

        $this->validateArray([
            'product_id'       => '0cb4b8b709e00325461bc0c4688be637',
            'name'             => 'BACARI - SCARLET VELVET',
            'brand'            => '',
            'description'      => 'The cool-girl essential bootie is distinguished by way of a super-sleek silhouette, which is accentuated by a slightly rounded pointed-toe (the new shape of the season) and a statement-making block heel. Transition effortlessly from day to night with cropped trousers and a statement coat. Heel height: 2 ¾&quot; Shaft height: 5 ½&quot; Back zipper Leather insole Rubber sole',
            'price'            => '698.00',
            'regular_price'    => '698.00',
            'sale_price'       => '698.00',
            'old_price'        => null,
            'deal_start_date'  => null,
            'deal_end_date'    => '2037-01-01',
            'available'        => '1',
            'publication_date' => 1356998400,
            'image_url'        => 'http://www.stuartweitzman.ca/assets/item/regular/bacari_scavel_12.jpg',
            'product_url'      => 'http://www.stuartweitzman.ca/eng/products/bacari/scarlet-velvet/',
            'thumbnail_url'    => 'https://res.cloudinary.com/salesfloor-net/image/fetch/s--TzlMzIVr--/f_auto,q_90,w_250/http://www.stuartweitzman.ca/assets/item/regular/bacari_scavel_12.jpg',
            'category'         => [
                'Booties',
                'Random cat 3',
            ],
            'category_id'      => 'dbf52747#Booties',
        ], $processedProducts[0]);

        $this->validateArray([
            'product_id'       => '174d4437287e61204b5bc859a6c6f824',
            'name'             => 'BAND - BLACK PATENT',
            'brand'            => '',
            'description'      => 'Inject your workweek wardrobe with &quot;wow&quot; with these new pointed-toe pumps. This perpetually polished silhouette is crafted from glossy leather and is set on a statement block heel finished with a sophisticated metallic embellishment. Try with a pleated shirtdress and a boxy top handle. Heel height : 2.5&quot; Leather insole Leather sole',
            'price'            => '498.00',
            'sale_price'       => '498.00',
            'old_price'        => null,
            'deal_start_date'  => null,
            'deal_end_date'    => '2037-01-01',
            'available'        => '1',
            'publication_date' => 1356998400,
            'image_url'        => 'http://www.stuartweitzman.ca/assets/item/regular/band_blapat_12.jpg',
            'product_url'      => 'http://www.stuartweitzman.ca/eng/products/band/black-patent/',
            'thumbnail_url'    => 'https://res.cloudinary.com/salesfloor-net/image/fetch/s--lfNkuHBB--/f_auto,q_90,w_250/http://www.stuartweitzman.ca/assets/item/regular/band_blapat_12.jpg',
            'category'         => [
                'New Arrivals Resort',
                'Random cat 2',
            ],
            'category_id'      => '2544b366#New Arrivals Resort',
        ], $processedProducts[1]);
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOffI18nOn(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled']           = true;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();

        // Because we get all products now (not variant based)
        $I->assertEquals(31, count($processedProducts));

        $this->validateArray([
            'product_id'        => '0cb4b8b709e00325461bc0c4688be637',
            'name'              => 'BACARI - SCARLET VELVET',
            'brand'             => '',
            'description'       => 'The cool-girl essential bootie is distinguished by way of a super-sleek silhouette, which is accentuated by a slightly rounded pointed-toe (the new shape of the season) and a statement-making block heel. Transition effortlessly from day to night with cropped trousers and a statement coat. Heel height: 2 ¾&quot; Shaft height: 5 ½&quot; Back zipper Leather insole Rubber sole',
            'price'             => '698.00',
            'sale_price'        => '698.00',
            'old_price'         => null,
            'deal_start_date'   => null,
            'deal_end_date'     => '2037-01-01',
            'available'         => '1',
            'publication_date'  => 1356998400,
            'image_url'         => 'http://www.stuartweitzman.ca/assets/item/regular/bacari_scavel_12.jpg',
            'product_url'       => 'http://www.stuartweitzman.ca/eng/products/bacari/scarlet-velvet/',
            'name_fr_CA'        => 'BACARI - SCARLET VELVET (fr_CA)',
            'description_fr_CA' => 'The cool-girl essential bootie is distinguished by way of a super-sleek silhouette, which is accentuated by a slightly rounded pointed-toe (the new shape of the season) and a statement-making block heel. Transition effortlessly from day to night with cropped trousers and a statement coat. Heel height: 2 ¾&quot; Shaft height: 5 ½&quot; Back zipper Leather insole Rubber sole (fr_CA)',
            'image_url_fr_CA'   => 'http://www.stuartweitzman.ca/assets/item/regular/bacari_scavel_12.jpg?locale=fr_CA',
            'product_url_fr_CA' => 'http://www.stuartweitzman.ca/eng/products/bacari/scarlet-velvet/?locale=fr_CA',
            'category'          => [
                'Booties',
                'Random cat 3',
            ],
            'category_fr_CA'    => [
                'Booties (fr_CA)',
                'Random cat 3 (fr_CA)',
            ],
            'category_id'       => 'dbf52747#Booties',
            'category_id_fr_CA' => 'dbf52747#Booties (fr_CA)',
        ], $processedProducts[0]);

        $this->validateArray([
            'product_id'        => '174d4437287e61204b5bc859a6c6f824',
            'name'              => 'BAND - BLACK PATENT',
            'brand'             => '',
            'description'       => 'Inject your workweek wardrobe with &quot;wow&quot; with these new pointed-toe pumps. This perpetually polished silhouette is crafted from glossy leather and is set on a statement block heel finished with a sophisticated metallic embellishment. Try with a pleated shirtdress and a boxy top handle. Heel height : 2.5&quot; Leather insole Leather sole',
            'price'             => '498.00',
            'sale_price'        => '498.00',
            'old_price'         => null,
            'deal_start_date'   => null,
            'deal_end_date'     => '2037-01-01',
            'available'         => '1',
            'publication_date'  => 1356998400,
            'image_url'         => 'http://www.stuartweitzman.ca/assets/item/regular/band_blapat_12.jpg',
            'product_url'       => 'http://www.stuartweitzman.ca/eng/products/band/black-patent/',
            'name_fr_CA'        => 'BAND - BLACK PATENT (fr_CA)',
            'description_fr_CA' => 'Inject your workweek wardrobe with &quot;wow&quot; with these new pointed-toe pumps. This perpetually polished silhouette is crafted from glossy leather and is set on a statement block heel finished with a sophisticated metallic embellishment. Try with a pleated shirtdress and a boxy top handle. Heel height : 2.5&quot; Leather insole Leather sole (fr_CA)',
            'image_url_fr_CA'   => 'http://www.stuartweitzman.ca/assets/item/regular/band_blapat_12.jpg?locale=fr_CA',
            'product_url_fr_CA' => 'http://www.stuartweitzman.ca/eng/products/band/black-patent/?locale=fr_CA',
            'category'          => [
                'New Arrivals Resort',
                'Random cat 2',
            ],
            'category_id'       => '2544b366#New Arrivals Resort',
            'category_fr_CA'    => [
                'New Arrivals Resort (fr_CA)',
                'Random cat 2 (fr_CA)',
            ],
            'category_id_fr_CA' => '2544b366#New Arrivals Resort (fr_CA)',
        ], $processedProducts[1]);
    }
}
