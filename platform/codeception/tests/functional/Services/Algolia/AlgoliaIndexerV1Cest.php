<?php

namespace SF\functional\Services\Algolia;

use SF\FunctionalTester;
use SF\Helper\Traits\Asserts;

class AlgoliaIndexerV1Cest extends Base
{
    use Asserts;

    public function _before($I)
    {
        parent::_before($I);
        date_default_timezone_set('UTC');
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOnI18nOff(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['retailer.i18n.is_enabled']           = false;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();

        usort($processedProducts, function ($a, $b) {
            return $a["variants"]["sku"] <=> $b["variants"]["sku"];
        });

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($processedProducts, $jsonFile, true);
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOnI18nOn(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->app['configs']['products.expanded_variants.priority_badge.enabled'] = true;
        $this->app['configs']['retailer.i18n.is_enabled']           = true;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();
        usort($processedProducts, function ($a, $b) {
            return $a["variants"]["sku"] <=> $b["variants"]["sku"];
        });

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($processedProducts, $jsonFile, true);
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOffI18nOff(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled']           = false;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();
        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($processedProducts, $jsonFile, true);
    }

    /** @group database_transaction */
    public function testProcessProductExtendedVariantOffI18nOn(FunctionalTester $I)
    {
        $this->app['configs']['products.expanded_variants.enabled'] = false;
        $this->app['configs']['retailer.i18n.is_enabled']           = true;

        $this->insertFixtureGroup($I, 'productVariantsWithAttributes');

        $processedProducts = $this->getProcessedProducts();
        // Because we get all products now (not variant based)
        $I->assertEquals(31, count($processedProducts));

        $jsonFile = sprintf('%s/files/%s.json', __DIR__, __FUNCTION__);
        $this->assertSnapshotAsJson($processedProducts, $jsonFile, true);
    }
}
