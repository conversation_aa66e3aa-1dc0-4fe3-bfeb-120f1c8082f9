<?php

namespace SF\functional\Services\Algolia;

use Codeception\Util\Fixtures;

/**
 *
 */
class Fixture
{
    public function productVariants()
    {
        Fixtures::add(
            'productVariantsWithAttributes',
            [
                'sf_product_variants'                => [
                    [
                        'product_id'      => '54640cd49acae0386f777604ce15ab92',
                        'sku'             => '11111',
                        'name'            => 'variant name',
                        'description'     => 'variant description',
                        'price'           => '10.00',
                        'available'       => 1,
                        'product_url'     => 'product_url',
                        'image_url'       => 'image_url',
                        'price_deal'      => '5.00',
                        'price_old'       => null,
                        'deal_start_date' => null,
                        'deal_end_date'   => null,
                        'brand'           => 'brand',
                        'is_default'      => '1',
                        'arrival_date'    => null,
                        'gtin'            => '12345',
                    ],
                    [
                        'product_id'      => '54640cd49acae0386f777604ce15ab92',
                        'sku'             => '22222',
                        'name'            => 'variant name',
                        'description'     => 'variant description',
                        'price'           => '20.00',
                        'available'       => 1,
                        'product_url'     => 'product_url',
                        'image_url'       => 'image_url',
                        'price_deal'      => '10.00',
                        'price_old'       => null,
                        'deal_start_date' => null,
                        'deal_end_date'   => null,
                        'brand'           => 'brand',
                        'is_default'      => '0',
                        'arrival_date'    => null,
                        'gtin'            => '54321',
                    ],
                ],
                'sf_product_variant_attributes'      => [
                    [
                        'sku'              => '11111',
                        'name'             => 'color',
                        'value'            => 'midnight blue',
                        'group'            => 'blue',
                        'swatch_image_url' => 'swatch_url',
                        'position'         => '1',
                    ],
                    [
                        'sku'              => '11111',
                        'name'             => 'size',
                        'value'            => '51',
                        'group'            => 'small',
                        'swatch_image_url' => '',
                        'position'         => '2',
                    ],
                    [
                        'sku'              => '11111',
                        'name'             => 'form',
                        'value'            => 'circle',
                        'group'            => 'circle',
                        'swatch_image_url' => '',
                        'position'         => '3',
                    ],
                    [
                        'sku'              => '22222',
                        'name'             => 'color',
                        'value'            => 'dark room',
                        'group'            => 'black',
                        'swatch_image_url' => '',
                        'position'         => '1',
                    ],
                ],
                'sf_product_variants_i18n'           => [
                    [
                        'locale'      => 'fr_CA',
                        'sku'         => '11111',
                        'name'        => 'name fr',
                        'description' => 'description fr',
                        'product_url' => 'product_url fr',
                        'image_url'   => 'image_url fr',
                    ],
                    [
                        'locale'      => 'fr_CA',
                        'sku'         => '22222',
                        'name'        => 'name fr',
                        'description' => 'description fr',
                        'product_url' => 'product_url fr',
                        'image_url'   => 'image_url fr',
                    ],
                ],
                'sf_product_variant_attributes_i18n' => [
                    [
                        'locale'           => 'fr_CA',
                        'sku'              => '11111',
                        'name'             => 'couleur',
                        'value'            => 'bleu de minuit',
                        'group'            => 'bleu',
                        'swatch_image_url' => '',
                        'position'         => '1',
                    ],
                    [
                        'locale'           => 'fr_CA',
                        'sku'              => '11111',
                        'name'             => 'grandeur',
                        'value'            => '51',
                        'group'            => 'petit',
                        'swatch_image_url' => '',
                        'position'         => '2',
                    ],
                    [
                        'locale'           => 'fr_CA',
                        'sku'              => '11111',
                        'name'             => 'forme',
                        'value'            => 'cercle',
                        'group'            => 'cercle',
                        'swatch_image_url' => '',
                        'position'         => '3',
                    ],
                    [
                        'locale'           => 'fr_CA',
                        'sku'              => '22222',
                        'name'             => 'couleur',
                        'value'            => 'pièce sombre',
                        'group'            => 'noir',
                        'swatch_image_url' => '',
                        'position'         => '1',
                    ],
                ],
            ]
        );
    }

    public function testAlgoliaDescriptionWithoutExtendedVariant()
    {
        $products = [
            'sf_products' => [
                [
                    'product_id'        => 'aabbcc',
                    'name'              => 'test name',
                    'description'       => 'WILL BE REPLACED',
                    'price'             => 50.00,
                    'deal_start_date'   => '2018-09-04 19:11:11',
                    'deal_end_date'     => '2018-09-04 19:11:11',
                    'available'         => 1,
                    'deal_ratio'        => 100,
                    'name2'             => 'test',
                ],
                [
                    'product_id'        => 'eeffgg',
                    'name'              => 'test name23',
                    'description'       => 'WILL BE REPLACED',
                    'price'             => 70.00,
                    'deal_start_date'   => '2018-09-04 19:11:11',
                    'deal_end_date'     => '2018-09-04 19:11:11',
                    'available'         => 1,
                    'deal_ratio'        => 100,
                    'name2'             => 'test2',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'dbf52747',
                    'storefront_slot' => 0,
                ],
                [
                    'product_id' => 'eeffgg',
                    'category_id' => 'dbf52747',
                    'storefront_slot' => 0,
                ],
            ],
            'sf_images' => [
                [
                    'product_id'        => 'aabbcc',
                    'url'              => 'example.com',
                    'language'       => 'en_CA',
                ],
                [
                    'product_id'        => 'eeffgg',
                    'url'              => 'example2.com',
                    'language'       => 'en_CA',
                ],
            ],
            'sf_siteurl' => [
                [
                    'product_id'        => 'aabbcc',
                    'url'              => 'site.example.com',
                    'language'       => 'en_CA',
                ],
                [
                    'product_id'        => 'eeffgg',
                    'url'              => 'site.example2.com',
                    'language'       => 'en_CA',
                ],
            ],
        ];

        Fixtures::add('testAlgoliaDescriptionWithoutExtendedVariant', $products);
    }

    public function testAlgoliaDescriptionWithExtendedVariant()
    {
        $products = [
            'sf_product_variants' => [
                [
                    'product_id' => '54640cd49acae0386f777604ce15ab92',
                    'sku' => '11111',
                    'name' => 'variant name',
                    'description' => 'variant description',
                    'price' => '10.00',
                    'available' => 1,
                    'product_url' => 'product_url',
                    'image_url' => 'image_url',
                    'price_deal' => '5.00',
                    'price_old' => null,
                    'deal_start_date' => null,
                    'deal_end_date' => null,
                    'brand' => 'brand',
                    'is_default' => '1',
                    'arrival_date' => null,
                    'gtin' => '12345',
                ],
                [
                    'product_id' => '54640cd49acae0386f777604ce15ab92',
                    'sku' => '22222',
                    'name' => 'variant name',
                    'description' => 'variant description',
                    'price' => '20.00',
                    'available' => 1,
                    'product_url' => 'product_url',
                    'image_url' => 'image_url',
                    'price_deal' => '10.00',
                    'price_old' => null,
                    'deal_start_date' => null,
                    'deal_end_date' => null,
                    'brand' => 'brand',
                    'is_default' => 0,
                    'arrival_date' => null,
                    'gtin' => '54321',
                ],
            ],
            'sf_product_variant_attributes' => [
                [
                    'sku' => '11111',
                    'name' => 'color',
                    'value' => 'midnight blue',
                    'group' => 'blue',
                    'swatch_image_url' => 'swatch_url',
                    'position' => '1',
                ],
                [
                    'sku' => '11111',
                    'name' => 'size',
                    'value' => '51',
                    'group' => 'small',
                    'swatch_image_url' => '',
                    'position' => '2',
                ],
                [
                    'sku' => '11111',
                    'name' => 'form',
                    'value' => 'circle',
                    'group' => 'circle',
                    'swatch_image_url' => '',
                    'position' => '3',
                ],
                [
                    'sku' => '22222',
                    'name' => 'color',
                    'value' => 'dark room',
                    'group' => 'black',
                    'swatch_image_url' => '',
                    'position' => '1',
                ],
            ],
            'sf_product_variants_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'sku' => '11111',
                    'name' => 'name fr',
                    'description' => 'description fr',
                    'product_url' => 'product_url fr',
                    'image_url' => 'image_url fr',
                ],
                [
                    'locale' => 'fr_CA',
                    'sku' => '22222',
                    'name' => 'name fr',
                    'description' => 'description fr',
                    'product_url' => 'product_url fr',
                    'image_url' => 'image_url fr',
                ],
            ],
            'sf_product_variant_attributes_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'sku' => '11111',
                    'name' => 'couleur',
                    'value' => 'bleu de minuit',
                    'group' => 'bleu',
                    'swatch_image_url' => '',
                    'position' => '1',
                ],
                [
                    'locale' => 'fr_CA',
                    'sku' => '11111',
                    'name' => 'grandeur',
                    'value' => '51',
                    'group' => 'petit',
                    'swatch_image_url' => '',
                    'position' => '2',
                ],
                [
                    'locale' => 'fr_CA',
                    'sku' => '11111',
                    'name' => 'forme',
                    'value' => 'cercle',
                    'group' => 'cercle',
                    'swatch_image_url' => '',
                    'position' => '3',
                ],
                [
                    'locale' => 'fr_CA',
                    'sku' => '22222',
                    'name' => 'couleur',
                    'value' => 'pièce sombre',
                    'group' => 'noir',
                    'swatch_image_url' => '',
                    'position' => '1',
                ],
            ],
        ];

        Fixtures::add('testAlgoliaDescriptionWithExtendedVariant', $products);
    }

    public function testAlgoliaCategoriesProductAny()
    {
        Fixtures::add('testAlgoliaCategoriesProductAny', array_merge($this->getAlgoliaCategoriesProductBase(), [
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'Lj_sKYHDe1hR9eTQjewTMC0Tg5I',  //Jewelry & Accessories (L1)
                ]
            ]
        ]));
    }

    public function testAlgoliaCategoriesProductMultipleCategories()
    {
        Fixtures::add('testAlgoliaCategoriesProductMultipleCategories', array_merge($this->getAlgoliaCategoriesProductBase(), [
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'Lj_sKYHDe1hR9eTQjewTMC0Tg5I',  //Jewelry & Accessories (L1)
                ],
                [
                    'product_id' => 'aabbcc',
                    'category_id' => '4hbUfEGFncYH2JWpmjr6UHwRLn0',  // For Her (L2) (Under beauty)
                ],
            ]
        ]));
    }

    public function testAlgoliaCategoriesL4()
    {
        Fixtures::add('testAlgoliaCategoriesL4', array_merge($this->getAlgoliaCategoriesProductBase(), [
            'sf_categories' => [
                [
                    'category_id' => 'L3',
                    'name' => 'L3',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L4',
                    'name' => 'L4',
                    'language' => 'en_CA',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'L3',
                    'parent_id' => '4hbUfEGFncYH2JWpmjr6UHwRLn0',
                ],
                [
                    'category_id' => 'L4',
                    'parent_id' => 'L3',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'Lj_sKYHDe1hR9eTQjewTMC0Tg5I',  //Jewelry & Accessories (L1)
                ],
                [
                    'product_id' => 'aabbcc',
                    'category_id' => '4hbUfEGFncYH2JWpmjr6UHwRLn0',  // For Her (L2) (Under beauty)
                ],
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'L4',  // For Her (L2) (Under beauty)
                ],
            ]
        ]));
    }

    public function testAlgoliaCategoriesI18N()
    {
        Fixtures::add('testAlgoliaCategoriesI18N', array_merge($this->getAlgoliaCategoriesProductBase(), [
            'sf_categories' => [
                [
                    'category_id' => 'L3',
                    'name' => 'L3',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L4',
                    'name' => 'L4',
                    'language' => 'en_CA',
                ],
            ],
            'sf_categories_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'category_id' => 'L3',
                    'name' => 'L3_FR',
                ],
                [
                    'locale' => 'fr_CA',
                    'category_id' => 'L4',
                    'name' => 'L4_FR',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'L3',
                    'parent_id' => '4hbUfEGFncYH2JWpmjr6UHwRLn0',
                ],
                [
                    'category_id' => 'L4',
                    'parent_id' => 'L3',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'Lj_sKYHDe1hR9eTQjewTMC0Tg5I',  //Jewelry & Accessories (L1)
                ],
                [
                    'product_id' => 'aabbcc',
                    'category_id' => '4hbUfEGFncYH2JWpmjr6UHwRLn0',  // For Her (L2) (Under beauty)
                ],
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'L4',  // For Her (L2) (Under beauty)
                ],
            ]
        ]));
    }

    public function testAlgoliaCategoriesNestedMax()
    {
        Fixtures::add('testAlgoliaCategoriesNestedMax', array_merge($this->getAlgoliaCategoriesProductBase(), [
            'sf_categories' => [
                [
                    'category_id' => 'L3',
                    'name' => 'L3',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L4',
                    'name' => 'L4',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L5',
                    'name' => 'L5',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L6',
                    'name' => 'L6',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L7',
                    'name' => 'L7',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L8',
                    'name' => 'L8',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L9',
                    'name' => 'L9',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L10',
                    'name' => 'L10',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L11',
                    'name' => 'L11',
                    'language' => 'en_CA',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'L3',
                    'parent_id' => '4hbUfEGFncYH2JWpmjr6UHwRLn0',
                ],
                [
                    'category_id' => 'L4',
                    'parent_id' => 'L3',
                ],
                [
                    'category_id' => 'L5',
                    'parent_id' => 'L4',
                ],
                [
                    'category_id' => 'L6',
                    'parent_id' => 'L5',
                ],
                [
                    'category_id' => 'L7',
                    'parent_id' => 'L6',
                ],
                [
                    'category_id' => 'L8',
                    'parent_id' => 'L7',
                ],
                [
                    'category_id' => 'L9',
                    'parent_id' => 'L8',
                ],
                [
                    'category_id' => 'L10',
                    'parent_id' => 'L9',
                ],
                [
                    'category_id' => 'L11',
                    'parent_id' => 'L10',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'L11',
                ],
            ]
        ]));
    }

    public function testAlgoliaCategoriesCircularReference()
    {
        Fixtures::add('testAlgoliaCategoriesCircularReference', array_merge($this->getAlgoliaCategoriesProductBase(), [
            'sf_categories' => [
                [
                    'category_id' => 'L3',
                    'name' => 'L3',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L4',
                    'name' => 'L4',
                    'language' => 'en_CA',
                ],
                [
                    'category_id' => 'L5',
                    'name' => 'L5',
                    'language' => 'en_CA',
                ],
            ],
            'sf_category_tree' => [
                [
                    'category_id' => 'L3',
                    'parent_id' => 'L4',
                ],
                [
                    'category_id' => 'L4',
                    'parent_id' => 'L3',
                ],
            ],
            'sf_product_category_map' => [
                [
                    'product_id' => 'aabbcc',
                    'category_id' => 'L4',
                ],
            ]
        ]));
    }

    private function getAlgoliaCategoriesProductBase()
    {
        return [
            'sf_products' => [
                [
                    'product_id' => 'aabbcc',
                    'name' => 'test name',
                    'description' => 'description',
                    'price' => 50.00,
                    'deal_start_date' => '2018-09-04 19:11:11',
                    'deal_end_date' => '2018-09-04 19:11:11',
                    'available' => 1,
                    'name2' => 'test',
                    'deal_ratio' => 100,
                ]
            ],
            'sf_products_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'product_id' => 'aabbcc',
                    'name' => 'test name (FR)',
                    'description' => 'description (FR)',
                    'vanity_data' => '',
                ]
            ],
            'sf_images' => [
                [
                    'product_id' => 'aabbcc',
                    'url' => 'example.com',
                    'language' => 'en_CA',
                ]
            ],
            'sf_images_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'product_id' => 'aabbcc',
                    'url' => 'example.com/fr',
                ]
            ],
            'sf_siteurl' => [
                [
                    'product_id' => 'aabbcc',
                    'url' => 'site.example.com',
                    'language' => 'en_CA',
                ]
            ],
            'sf_siteurl_i18n' => [
                [
                    'locale' => 'fr_CA',
                    'product_id' => 'aabbcc',
                    'url' => 'site.example.com/fr',
                ]
            ]
        ];
    }
}
