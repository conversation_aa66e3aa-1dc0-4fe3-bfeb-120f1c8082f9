<?php

namespace SF\functional\Services\Algolia;

use Algolia\AlgoliaSearch\Exceptions\BadRequestException;
use Algolia\AlgoliaSearch\SearchIndex;
use Codeception\Stub;
use SF\FunctionalTester;

/**
 * @group database_transaction
 */
class AlgoliaIndexerTooBigRecordCest extends Base
{
    public function testAlgoliaDescriptionWithoutExtendedVariantValidation(FunctionalTester $I)
    {
        $this->app['configs']['retailer.i18n.is_enabled'] = false;
        $I->executeQuery(
            'DELETE FROM sf_products',
            []
        );

        $this->insertFixtureGroup($I, 'testAlgoliaDescriptionWithoutExtendedVariant');

        $I->executeQuery(
            'UPDATE sf_products SET description = REPEAT("a", 102500) where product_id = "aabbcc"',
            []
        );

        $this->getMockedBulk(function ($products) use ($I) {
            foreach ($products as $product) {
                if ($product['product_id'] === 'aabbcc') {
                    // Total 102794 - 394 over - 100 buffer + 3 for "..."
                    $I->assertEquals(102303, mb_strlen(json_encode($product, JSON_NUMERIC_CHECK), '8bit'));
                }
            }
        });
    }

    public function testAlgoliaDescriptionWithExtendedVariantValidation(FunctionalTester $I)
    {
        $this->app['configs']['retailer.i18n.is_enabled'] = false;
        $this->app['configs']['products.expanded_variants.enabled'] = true;
        $this->insertFixtureGroup($I, 'testAlgoliaDescriptionWithExtendedVariant');

        $I->executeQuery(
            'UPDATE sf_product_variants SET description = REPEAT("a", 102500) where sku = "11111"',
            []
        );

        $this->getMockedBulk(function ($products) use ($I) {
            foreach ($products as $product) {
                if ($product['sku'] === '11111') {
                    // Description is cleaned up first, but the big description is only set in the variant.
                    $I->assertEquals(0, mb_strlen($product['description']));
                    $I->assertEquals(103320, mb_strlen(json_encode($product, JSON_NUMERIC_CHECK), '8bit'));
                }
            }
        });
    }

    public function testAlgoliaDescriptionWithoutExtendedVariantException(FunctionalTester $I)
    {
        $this->app['configs']['retailer.i18n.is_enabled'] = false;
        $I->executeQuery(
            'DELETE FROM sf_products',
            []
        );
        $this->insertFixtureGroup($I, 'testAlgoliaDescriptionWithoutExtendedVariant');

        $I->executeQuery(
            'UPDATE sf_products SET description = REPEAT("a", 102500) where product_id = "aabbcc"',
            []
        );

        $nb = -1;
        $size = [102303, 102303, 102303,102303,93206,93206,93206,93206,84109,84109,84109,84109, 0,0,0,0];

        $this->getMockedBulk(function ($products) use ($I, $size, &$nb) {
            $nb += 1;

            // Simulate no 'errors' from indexing.
            if (!isset($size[$nb])) {
                return;
            }

            // We removed the "too big product" so we are down from 2 to 1
            if ($size[$nb] === 0) {
                $I->assertEquals(1, count($products));
            }

            foreach ($products as $product) {
                if ($product['product_id'] === 'aabbcc') {
                    $I->assertEquals($size[$nb], mb_strlen(json_encode($product, JSON_NUMERIC_CHECK), '8bit'));
                }
            }

            // Otherwise, the last element it will crash because position 0 doesn't exist
            if ($size[$nb] > 0) {
                throw new BadRequestException("blabla position 0 blabla size=10000/1000 bytes blabla");
            }
        });
    }

    public function testAlgoliaDescriptionWithoutExtendedVariantMultipleException(FunctionalTester $I)
    {
        $this->app['configs']['retailer.i18n.is_enabled'] = false;
        $I->executeQuery(
            'DELETE FROM sf_products',
            []
        );
        $this->insertFixtureGroup($I, 'testAlgoliaDescriptionWithoutExtendedVariant');

        $I->executeQuery(
            'UPDATE sf_products SET description = REPEAT("a", 102500) where product_id = "aabbcc"',
            []
        );

        $I->executeQuery(
            'UPDATE sf_products SET description = REPEAT("a", 102500) where product_id = "eeffgg"',
            []
        );

        $nb = -1;
        $size1 = [
            102303, 102303, 102303, 102303,  // first loop
            93206, 93206, 93206, 93206,  // resized down
            84109, 84109, 84109, 84109, // resized down
            0, 0, 0, 0,     // Last loop, product is sliced
            0, 0, 0, 0,
            0, 0, 0, 0,
            0, 0, 0, 0,
        ];

        $size2 = [
            102303, 102303, 102303, 102303, // First product cleaned up, second not changed
            102303, 102303, 102303, 102303, // First product cleaned up, second not changed
            102303, 102303, 102303, 102303, // First product cleaned up, second not changed
            102303, 102303, 102303, 102303, // First loop, no resize is done
            93206, 93206, 93206, 93206,
            84109, 84109, 84109, 84109,
            0, 0, 0, 0,
        ];

        $this->getMockedBulk(function ($products) use ($I, $size1, $size2, &$nb) {
            $nb += 1;

            // Simulate no 'errors' from indexing.
            if (!isset($size1[$nb]) || !isset($size2[$nb])) {
                return;
            }

            $total = (int)(bool)$size1[$nb] + (int)(bool)$size2[$nb];

            // We removed the "too big product" so we are down from 2 to 1 to 0
            $I->assertEquals($total, count($products));

            foreach ($products as $product) {
                if ($product['product_id'] === 'aabbcc') {
                    $I->assertEquals($size1[$nb], mb_strlen(json_encode($product, JSON_NUMERIC_CHECK), '8bit'));
                }
                if ($product['product_id'] === 'eeffgg') {
                    $I->assertEquals($size2[$nb], mb_strlen(json_encode($product, JSON_NUMERIC_CHECK), '8bit'));
                }
            }

            // Otherwise, the last element it will crash because position 0 doesn't exist
            if ($size1[$nb] > 0 || $size2[$nb] > 0) {
                throw new BadRequestException("blabla position 0 blabla size=10000/1000 bytes blabla");
            }
        });
    }

    public function testAlgoliaDescriptionWithoutExtendedVariantMultipleExceptionNoSize(FunctionalTester $I)
    {
        $this->app['configs']['retailer.i18n.is_enabled'] = false;
        $I->executeQuery(
            'DELETE FROM sf_products',
            []
        );
        $this->insertFixtureGroup($I, 'testAlgoliaDescriptionWithoutExtendedVariant');

        $I->executeQuery(
            'UPDATE sf_products SET description = REPEAT("a", 102500) where product_id = "aabbcc"',
            []
        );

        $nb = -1;
        $size = [102303, 102303, 102303,102303];

        // Size we don't throw expected format exception, resize doesn't know what to know, so it will just
        // rethrow the exception.
        $I->expectThrowable(BadRequestException::class, function () use ($I, $nb, $size) {
            $this->getMockedBulk(function ($products) use ($I, $size, &$nb) {
                $nb += 1;

                // Simulate no 'errors' from indexing.
                if (!isset($size[$nb])) {
                    return;
                }

                // We removed the "too big product" so we are down from 2 to 1
                if ($size[$nb] === 0) {
                    $I->assertEquals(1, count($products));
                }

                foreach ($products as $product) {
                    if ($product['product_id'] === 'aabbcc') {
                        $I->assertEquals($size[$nb], mb_strlen(json_encode($product, JSON_NUMERIC_CHECK), '8bit'));
                    }
                }

                // Otherwise, the last element it will crash because position 0 doesn't exist
                if ($size[$nb] > 0) {
                    throw new BadRequestException("Random string, not equal to what ES would return");
                }
            });
        });
    }

    private function getMockedBulk(callable $assert)
    {
        $algolia = $this->getMockedAlgolia();

        $this->getMethod('\Salesfloor\Services\Algolia\Indexer\Base', 'bulk')->invokeArgs($algolia, [
            $this->getMethod('\Salesfloor\Services\Algolia\Indexer\Base', 'getProducts')->invoke($algolia),
            Stub::makeEmpty(SearchIndex::class),
            function ($products) {
                return $products; // process
            },
            function ($products) use ($assert) {
                $assert($products); // action
            },
        ]);
    }
}
