<?php

declare(strict_types=1);

namespace SF\functional\Services\CanChatMetric;

use Codeception\Util\Fixtures;
use Salesfloor\Providers\PushNotifications\PublishQueueServiceProvider;
use Salesfloor\Services\Chat\CansChatMetrics;
use Salesfloor\Services\PushNotifications\Service;
use Salesfloor\Services\Queue\CansChatMetricsQueue;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class CanChatMetricCest extends BaseFunctional
{
    /** @group database_transaction */
    public function testMetricWriteOnPushNotificationPublish(FunctionalTester $I)
    {
        $I->wantTo("Test the new chat request with routing mode metric logging");
        $this->app['configs']['services.record_chat_metrics_cans.enabled'] = true;
        $this->app['configs']['services.queue.enabled'] = false;
        $data = Fixtures::get('NewChatMetricRequest');

        $cansChatMetricsQueue = \Codeception\Stub::make(
            CansChatMetricsQueue::class,
            ['push' => \Codeception\Stub\Expected::once()]
        );

        $this->extractedServices($cansChatMetricsQueue, $I, $data);

        $this->app['service.push.publish.queue']->publishToReps($data['ids'], $data['message'], $data['inapp']);
    }
    /** @group database_transaction */
    public function testMetricDoNotWriteOnPushNotificationPublish(FunctionalTester $I)
    {
        $I->wantTo("Test the new chat request with routing mode metric logging disabled");
        $this->app['configs']['services.record_chat_metrics_cans.enabled'] = false;
        $this->app['configs']['services.queue.enabled'] = false;
        $data = Fixtures::get('NewChatMetricRequest');

        $cansChatMetricsQueue = \Codeception\Stub::make(
            CansChatMetricsQueue::class,
            ['push' => \Codeception\Stub\Expected::never()]
        );

        $this->extractedServices($cansChatMetricsQueue, $I, $data);

        $this->app['service.push.publish.queue']->publishToReps($data['ids'], $data['message'], $data['inapp']);
    }

    /**
     * @param $cansChatMetricsQueue
     * @param FunctionalTester $I
     * @param mixed $data
     * @return void
     */
    protected function extractedServices($cansChatMetricsQueue, FunctionalTester $I, mixed $data): void
    {
        unset(
            $this->app['queue.cans.chat.metrics'],
            $this->app['cans.chat.metrics'],
            $this->app['service.push'],
            $this->app['service.push.publish.queue'], // From the register()
        );

        $this->app['queue.cans.chat.metrics'] = function ($app) use ($cansChatMetricsQueue) {
            return $cansChatMetricsQueue;
        };

        $I->addStubToVerify($cansChatMetricsQueue);

        $this->app['cans.chat.metrics'] = function ($app) {
            return new CansChatMetrics(
                $this->app['queue.cans.chat.metrics'],
                $app['cans.chat.routing_mode'],
                $app['configs'],
                $app['logger'],
            );
        };

        $this->app['service.push'] = function ($app) {

            return \Codeception\Stub::make(Service::class, [
                'publish' => function () {
                    return true;
                },
                'getStoreEndpoints' => function ($ids) {
                    return [1 => []];
                },
                'getRepEndpoints' => function ($ids) {
                    return [1 => []];
                },
            ]);
        };
        $this->app->register(new PublishQueueServiceProvider());
    }
}
