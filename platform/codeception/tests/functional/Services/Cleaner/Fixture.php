<?php

namespace SF\functional\Services\Cleaner;

use Codeception\Util\Fixtures;
use SF\BaseFixture;

/**
 *
 */
class Fixture extends BaseFixture
{
    public function devices()
    {
        Fixtures::add('device', [
            'token' => 'device-token-1',
            'endpoint_id' => 'sns-endpoint-1',
            'user_id' => 115,
            'last_validated' => null
        ]);

        Fixtures::add('device-already-checked', [
            'token' => 'device-token-2',
            'endpoint_id' => 'sns-endpoint-2',
            'user_id' => 115,
            'last_validated' => gmdate('Y-m-d H:i:s', strtotime('-1 days'))
        ]);
    }

    public function textMessageNumbers()
    {
        Fixtures::add('DeletedNumbers', [ 'sf_deleted_user_phone_number' => [
            [
                'id' => 1,
                'user_id' => 1,
                'phone_number' => '+15141112222',
                'created_by_user_id' => 2,
                'deleted_by_user_id' => 2,
                'deleted_at' => gmdate('Y-m-d H:i:s', strtotime('-1 day')),
                'released' => 0
            ],
            [
                'id' => 2,
                'user_id' => 1,
                'phone_number' => '+15141113333',
                'created_by_user_id' => 2,
                'deleted_by_user_id' => 2,
                'deleted_at' => gmdate('Y-m-d H:i:s', strtotime('-10 day')),
                'released' => 0
            ],
            [
                'id' => 3,
                'user_id' => 1,
                'phone_number' => '+15141114444',
                'created_by_user_id' => 2,
                'deleted_by_user_id' => 2,
                'deleted_at' => gmdate('Y-m-d H:i:s', strtotime('-15 day')),
                'released' => 1
            ],
        ]]);
    }

    public function invalidTextUsers()
    {
        Fixtures::add('InvalidTextUsers', [
            'wp_users' => [
                $this->makeSellingModeUser(10, 1003),
                $this->makeNonSellingModeUser(11, 1003),
                $this->makeInactiveUser(12, 1003),
                $this->makeInactiveUser(13, 3003),
                $this->makeSellingModeUser(14, 3003),
            ],
            'sf_user_phone_number' => [
                [
                    'user_id' => 10,
                    'phone_number' => '+15142223333',
                    'created_by_user_id' => 1
                ],
                [
                    'user_id' => 11,
                    'phone_number' => '+15142224444',
                    'created_by_user_id' => 1
                ],
                [
                    'user_id' => 12,
                    'phone_number' => '+15142225555',
                    'created_by_user_id' => 1
                ],
                [
                    'user_id' => 13,
                    'phone_number' => '+15142226666',
                    'created_by_user_id' => 1
                ],
                [
                    'user_id' => 14,
                    'phone_number' => '+15142227777',
                    'created_by_user_id' => 1
                ],
                // store user
                [
                    'user_id' => 1,
                    'phone_number' => '+15142228888',
                    'created_by_user_id' => 1
                ]
            ],
            'sf_store' => [
                [
                    'store_id'              => 3003,
                    'name'                  => 'Null Store Id',
                    'country'               => 'US',
                    'store_user_id'         => null,
                    'sf_identifier'         => 'nullstore',
                    'region'                => 'QC',
                    'city'                  => 'Montreal',
                    'address'               => '1455 Peel Streets',
                    'postal'                => 'H3A 1T5',
                    'phone'                 => '************',
                    'latitude'              => 45.4910,
                    'longitude'             => -73.5658,
                ]
            ]
        ]);
    }
}
