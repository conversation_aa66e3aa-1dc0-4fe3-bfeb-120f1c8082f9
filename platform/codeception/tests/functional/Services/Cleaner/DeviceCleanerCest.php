<?php

namespace SF\functional\Services\Cleaner;

use Aws\Sns\Exception\InvalidParameterException;
use Aws\Sns\Exception\NotFoundException;
use Codeception\Stub;
use Codeception\Util\Fixtures;
use Salesfloor\Services\Cleaner\Devices;
use Salesfloor\Services\PushNotifications\Service;
use Salesfloor\Services\PushNotifications\SnsBackend;
use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class DeviceCleanerCest extends BaseFunctional
{
    /**
     * @param FunctionalTester $I
     * @return void
     *
     * @skip
     * @group database_transaction
     */
    public function testCleanDevicesValidDevice(FunctionalTester $I)
    {
        $I->wantToTest('a valid ARN is not removed from the table');
        $I->haveInDatabase('sf_devices', Fixtures::get('device'));

        $devices = $this->getDeviceService(function () {
            return true;
        });

        $removed = $devices->clean();
        $I->assertEquals(0, $removed);

        $lastValidated = $I->grabFromDatabase('sf_devices', 'last_validated', ['token' => 'device-token-1']);
        $I->assertNotEmpty($lastValidated);
    }

    /** @group database_transaction */
    public function testCleanDeviceDisabledArn(FunctionalTester $I)
    {
        $I->wantToTest('a disabled ARN is removed from the table');
        $I->haveInDatabase('sf_devices', Fixtures::get('device'));

        $devices = $this->getDeviceService(function () {
            return false;
        });

        $removed = $devices->clean();
        $I->assertEquals(1, $removed);

        $I->dontSeeInDatabase('sf_devices', [
            'token' => 'device-token-1'
        ]);
    }

    /** @group database_transaction */
    public function testCleanDeviceDisabledArnRecentlyChecked(FunctionalTester $I)
    {
        $I->wantToTest("a disabled ARN that was recently checked isn't rechecked yet");
        $I->haveInDatabase('sf_devices', Fixtures::get('device-already-checked'));
        $expected = Fixtures::get('device-already-checked');

        $devices = $this->getDeviceService(function () {
            return true;
        });

        $removed = $devices->clean();
        $I->assertEquals(0, $removed);

        $lastValidated = $I->grabFromDatabase('sf_devices', 'last_validated', ['token' => 'device-token-2']);
        $I->assertEquals($expected['last_validated'], $lastValidated);
    }

    /** @group database_transaction */
    public function testCleanDeviceInvalidArn(FunctionalTester $I)
    {
        $I->wantToTest("an invalid ARN is removed from the table");
        $I->haveInDatabase('sf_devices', Fixtures::get('device'));

        $devices = $this->getDeviceService(function () {
            return false;
        });

        $removed = $devices->clean();
        $I->assertEquals(1, $removed);

        $I->dontSeeInDatabase('sf_devices', [
            'token' => 'device-token-1'
        ]);
    }

    /** @group database_transaction */
    public function testCleanDeviceNonexistentArn(FunctionalTester $I)
    {
        $I->wantToTest("a nonexistent ARN is removed from the table");
        $I->haveInDatabase('sf_devices', Fixtures::get('device'));

        $devices = $this->getDeviceService(function () {
            return false;
        });

        $removed = $devices->clean();
        $I->assertEquals(1, $removed);

        $I->dontSeeInDatabase('sf_devices', [
            'token' => 'device-token-1'
        ]);
    }

    private function getDeviceService($validatedCallback)
    {
        /** @var SnsBackend $sns */
        $backend = Stub::makeEmpty(
            SnsBackend::class,
        );

        $service = Stub::construct(
            Service::class,
            [
                $backend,
                $this->app['mysql.importer'],
                $this->app,
            ]
        );

        return Stub::construct(
            Devices::class,
            [
                $service,
                $this->app['devices.manager'],
                $this->app['logger']
            ]
        );
    }
}
