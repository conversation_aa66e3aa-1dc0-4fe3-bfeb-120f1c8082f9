<?php

declare(strict_types=1);

namespace SF\functional\Services\Salesfloor\RecipientService;

use Codeception\Example;
use Salesfloor\Services\Messaging\Exceptions\InvalidRecipientException;
use Salesfloor\Services\Messaging\Recipient\RecipientService;
use SF\FunctionalTester;
use SF\functional\BaseFunctional;

class RecipientServiceCest extends BaseFunctional
{
    /**
     * @dataProvider recipientsProvider
     *
     * @group database_transaction
     * */
    public function testGetRecipients(FunctionalTester $I, Example $data)
    {
        $I->wantTo("test that we get recipients correctly");

        $this->insertFixtureGroup($I, 'Customer_create_SMS');

        [$requestData, $exception, $expectedResult] = $data;
        $service = $this->getService();

        if ($exception !== null) {
            $I->expectThrowable($exception, function () use ($service, $requestData) {
                $service->getRecipients($requestData);
            });
        } else {
            $result = $service->getRecipients($requestData);
            $I->assertEquals($expectedResult, $result);
        }
    }


    protected function recipientsProvider(): array
    {
        return [
            // all recipients (contacts) with customer_id and phone number
            [
                [
                    'recipients' => [
                        [
                            "customer_id" => 246,
                            "customer_phone_number" => "+15144321111",
                        ],
                        [
                            "customer_id" => 246,
                            "customer_phone_number" => "+15144321111",
                        ],
                    ]
                ],
                null,
                [
                    [
                        "customer_id" => 246,
                        "customer_phone_number" => "+15144321111",
                    ],
                ]
            ],
            // one recipient (contacts) without customer_id
            [
                [
                    'recipients' => [
                        [
                            "customer_phone_number" => "+15144321111",
                        ],
                        [
                            "customer_id" => 262,
                            "customer_phone_number" => "+15144321111",
                        ],
                    ]
                ],
                null,
                [
                    [
                        "customer_phone_number" => "+15144321111",
                    ],
                ]
            ],
            // all recipients(contacts) without customer_id
            [
                [
                    'recipients' => [
                        [
                            "customer_phone_number" => "+15144321111",
                        ],
                        [
                            "customer_phone_number" => "+15144321111",
                        ],
                    ]
                ],
                null,
                [
                    [
                        "customer_phone_number" => "+15144321111",
                    ],
                ]
            ],
            // for PII we obfuscate the customer_phone and we only process the customer_id
            [
                [
                    'recipients' => [
                        [
                            "customer_id" => 343,
                            "customer_phone_number" => "+151****001",
                        ],
                    ]
                ],
                null,
                [
                    [
                        "customer_id" => 343,
                        "customer_phone_number" => "+15145550001",
                    ],
                ]
            ],

            // all recipients(retailer customers)
            [
                [
                    'recipients' => [
                        [
                            "retailer_customer_id" => 728,
                            "retailer_customer_phone_number" => "5144321111",
                            "request_time" => "2023-10-10 01:02:03"
                        ],
                        [
                            "retailer_customer_id" => 728,
                            "retailer_customer_phone_number" => "5144321111",
                            "request_time" => "2023-10-10 01:02:03"
                        ],
                    ]
                ],
                null,
                [
                    [
                        "type" => RecipientService::TYPE_RETAILER_CUSTOMER,
                        "customer_id" => 728,
                        "customer_phone_number" => "+15144321111",
                        "request_time" => "2023-10-10 01:02:03"
                    ],
                ]
            ],
            // PII phone is being obfuscated
            [
                [
                    'recipients' => [
                        [
                            "retailer_customer_id" => 771,
                            "retailer_customer_phone_number" => "+151****878",
                            "request_time" => "2023-10-10 01:02:03"
                        ],
                    ]
                ],
                null,
                [
                    [
                        "type" => RecipientService::TYPE_RETAILER_CUSTOMER,
                        "customer_id" => 771,
                        "customer_phone_number" => "+15148887878",
                        "request_time" => "2023-10-10 01:02:03"
                    ],
                ]
            ],
            // No recipient
            [
                [
                    'recipients' => []
                ],
                InvalidRecipientException::class,
                null,
            ],
            // the recipients mixed with contact and retailer customers
            [
                [
                    'recipients' => [
                        [
                            "retailer_customer_id" => 728,
                            "retailer_customer_phone_number" => "5144321111",
                            "request_time" => "2023-10-10 01:02:03"
                        ],
                        [
                            "customer_phone_number" => "+15144321111",
                        ],
                    ]
                ],
                InvalidRecipientException::class,
                null,
            ],
        ];
    }

    private function getService(): RecipientService
    {
        $service = $this->app['service.messaging.recipient'];
        $reflectionClass = new \ReflectionClass($service);
        $property = $reflectionClass->getProperty('userId');
        $property->setAccessible(true);
        $property->setValue($service, 1);

        return $service;
    }
}
