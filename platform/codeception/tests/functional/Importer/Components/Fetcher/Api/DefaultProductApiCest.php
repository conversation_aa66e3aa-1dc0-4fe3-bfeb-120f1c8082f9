<?php

namespace SF\functional\Importer\Components\Fetcher\Api;

use SF\functional\BaseFunctional;
use SF\FunctionalTester;

class DefaultProductApiCest extends BaseFunctional
{
    public function _before($I)
    {
        parent::_before($I);
    }

    /** @group database_transaction */
    public function testImportPaginatedByPage(FunctionalTester $I)
    {
        $I->wantTo('Import data from a paginated API using page numbers.');

        // Define configs for fetcher
        $app = $this->app;
        $app['configs']['importer.product.fetcher.api.is_paginated'] = true;
        $app['configs']['importer.product.fetcher.api.url'] = 'http://jsonplaceholder.typicode.com/photos?_page={{pageNum}}&_limit={{perPage}}';
        $app['configs']['importer.product.fetcher.api.credentials'] = [];
        $app['configs']['importer.product.fetcher.api.per_page'] = 20;
        $app['configs']['importer.product.fetcher.api.start_page'] = 1;

        // Instantiate fetcher
        $importer = $app['importer.fetcher.api.product.default']($app);

        // Fetch data from external domain jsonplaceholder.typicode.com
        $loopCounter = 0;
        $lastId = 0;
        foreach ($importer->getStreamInfo() as $streamInfo) {
            $decoded = json_decode($streamInfo, true);
            if (!empty($decoded)) {
                $I->assertEquals(20, count($decoded), 'Per page was set');
                foreach ($decoded as $item) {
                    $id = $item['id'];
                    ++$lastId;
                    $I->assertEquals($lastId, $id, 'ID should be continuous');
                }
                $I->debug($decoded);
            }
            ++$loopCounter;

            // Keep test small
            if ($loopCounter > 50) {
                break;
            }
        }
        $I->assertGreaterOrEquals(1, $loopCounter, 'Iterate over multiple pages of data');
    }

    /** @group database_transaction */
    public function testImportPaginatedByOffset(FunctionalTester $I)
    {
        $I->wantTo('Import data from a paginated API using offset and limit.');

        // Define configs for fetcher
        $app = $this->app;
        $app['configs']['importer.product.fetcher.api.is_paginated'] = true;
        $app['configs']['importer.product.fetcher.api.url'] = 'http://jsonplaceholder.typicode.com/photos?_start={{from}}&_limit={{perPage}}';
        $app['configs']['importer.product.fetcher.api.credentials'] = [];
        $app['configs']['importer.product.fetcher.api.per_page'] = 5;
        $app['configs']['importer.product.fetcher.api.start_page'] = 1;

        // Instantiate fetcher
        $importer = $app['importer.fetcher.api.product.default']($app);

        // Fetch data from external domain jsonplaceholder.typicode.com
        $loopCounter = 0;
        $lastId = 0;
        foreach ($importer->getStreamInfo() as $streamInfo) {
            $decoded = json_decode($streamInfo, true);
            if (!empty($decoded)) {
                $I->assertEquals(5, count($decoded), 'Per page was set');
                foreach ($decoded as $item) {
                    $id = $item['id'];
                    ++$lastId;
                    $I->assertEquals($lastId, $id, 'ID should be continuous');
                }
                $I->debug($decoded);
            }
            ++$loopCounter;

            // Keep test small
            if ($loopCounter > 50) {
                break;
            }
        }
        $I->assertGreaterOrEquals(1, $loopCounter, 'Iterate over multiple pages of data');
    }

    /** @group database_transaction */
    public function testImportDefaultNonPaginated(FunctionalTester $I)
    {
        $I->wantTo('Import data from a non-paginated API using default fetcher.');

        // Define configs for fetcher
        $app = $this->app;
        $app['configs']['importer.product.fetcher.api.is_paginated'] = false;
        $app['configs']['importer.product.fetcher.api.url'] = 'http://jsonplaceholder.typicode.com/photos?_page=1&_limit=10';
        $app['configs']['importer.product.fetcher.api.credentials'] = [];

        // Instantiate fetcher
        $importer = $app['importer.fetcher.api.product.default']($app);

        // Fetch data from external domain jsonplaceholder.typicode.com
        $loopCounter = 0;
        $lastId = 0;
        foreach ($importer->getStreamInfo() as $streamInfo) {
            $decoded = json_decode($streamInfo, true);

            // Check for no gaps in the IDs
            foreach ($decoded as $item) {
                $id = $item['id'];
                ++$lastId;
                $I->assertEquals($lastId, $id, 'ID should be continuous');
            }
            ++$loopCounter;
            $I->debug($decoded);
        }
        $I->assertEquals(1, $loopCounter, 'Iterate over 1 page of data');
    }
}
