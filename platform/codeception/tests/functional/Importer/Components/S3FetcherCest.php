<?php

namespace SF\functional\Importer\Components;

use Salesfloor\Services\Importer\Components\Fetcher\S3\BaseS3Fetcher;
use Salesfloor\Services\Importer\Components\Fetcher\S3\S3FetcherFactory;
use SF\FunctionalTester;

class S3FetcherCest extends \SF\functional\BaseFunctional
{
    /** @group database_transaction */
    public function testS3Download(FunctionalTester $I)
    {
        $I->wantTo('validate files download and rotate in S3 using the downloader');

        $s3Prefix = 's3-download-tester';

        $this->app['configs']['importer.test.s3.path'] = $s3Prefix;

        $filename = uniqid('s3test');
        $path = sys_get_temp_dir() . '/' . $filename;
        touch($path);

        $this->app['configs']['importer.test.s3.filename_regexp'] = $filename;

        $content = "test file content\nmore data to read";
        file_put_contents($path, $content);

        $s3Path = $s3Prefix . '/' . $filename;

        $this->app['cloudstorage.upload'](
            $s3Path,
            $path,
            $this->app
        );

        /** @var S3FetcherFactory $factory */
        $factory = $this->app['importer.fetcher.s3'];
        /** @var BaseS3Fetcher $downloader */
        $downloader = $factory->create('test');

        $downloader->prepare();

        // Validate the file contains the expected data
        $downloadedFilePath = $downloader->getStreamInfo();
        $lines = file($downloadedFilePath);
        $I->assertEquals("test file content\n", $lines[0]);
        $I->assertEquals('more data to read', $lines[1]);

        $downloader->cleanup();
        $downloader->completed();

        // Validate the file was rotated to processed directory.
        $files = $this->app['cloudstorage.list']($s3Prefix);
        $keys = [];
        foreach ($files as $file) {
            $keys[] = $file['Key'];
        }

        $I->assertContains($s3Prefix . '/processed/' . $filename, $keys);

        // Clean up
        $this->app['cloudstorage.client']->deleteObject(
            [
                'Bucket' => $this->app['configs']['s3.bucket'],
                'Key'    => $s3Prefix . '/processed/' . $filename,
            ]
        );

        unlink($path);
    }

    /** @group database_transaction */
    public function testS3DownloaderWithForcedLocalFile(FunctionalTester $I)
    {
        $I->wantTo('validate S3 downloader can be forced to use a local file and not contact S3');

        // These configs are expected to exist, even though in the local file case they aren't referenced.
        $this->app['configs']['importer.test.s3.path'] = 'testpath';
        $this->app['configs']['importer.test.s3.filename_regexp'] = 'testregex';

        $filename = uniqid('s3test');
        $path = sys_get_temp_dir() . '/' . $filename;
        touch($path);
        $content = "test file content\nmore data to read";
        file_put_contents($path, $content);

        /** @var S3FetcherFactory $factory */
        $factory = $this->app['importer.fetcher.s3'];
        /** @var BaseS3Fetcher $downloader */
        $downloader = $factory->create(
            'test',
            $path,
            false,
            false,
            true
        );

        $downloader->prepare();

        // Validate the file contains the expected data
        $filename = $downloader->getStreamInfo();
        $lines = file($filename);
        $I->assertEquals("test file content\n", $lines[0]);
        $I->assertEquals('more data to read', $lines[1]);

        $downloader->cleanup();
        $downloader->completed();

        // Validate the file was not deleted after.
        $I->assertFileExists($path);
        // Now actually delete the file.
        unlink($path);
    }

    /** @group database_transaction */
    public function testProductFeedS3Downloader(FunctionalTester $I)
    {
        $I->wantTo('validate rotation/archival changes for downloading product feed files');

        $s3Prefix = 's3-download-tester';

        $this->app['configs']['importer.test.s3.path'] = $s3Prefix;
        $this->app['configs']['importer.test.s3.filename_regexp'] = 's3test-\w*';

        $filename = uniqid('s3test-older');
        $path = sys_get_temp_dir() . '/' . $filename;
        touch($path);

        $content = "test file content\nmore data to read";
        file_put_contents($path, $content);

        $s3PathOlder = $s3Prefix . '/' . $filename;

        $this->app['cloudstorage.upload'](
            $s3PathOlder,
            $path,
            $this->app
        );

        $filenameNewer = uniqid('s3test-newer');
        $path = sys_get_temp_dir() . '/' . $filenameNewer;
        touch($path);

        $content = "test file content\nmore data to read";
        file_put_contents($path, $content);

        $s3PathNewer = $s3Prefix . '/' . $filenameNewer;

        $this->app['cloudstorage.upload'](
            $s3PathNewer,
            $path,
            $this->app
        );

        /** @var S3FetcherFactory $factory */
        $factory = $this->app['importer.fetcher.s3.product'];
        /** @var BaseS3Fetcher $downloader */
        $downloader = $factory->create('test');

        $downloader->prepare();

        $I->assertStringContainsString('newer', $downloader->getImportFilename());

        // This will remove the tmp file (because not part of the main importer)
        $downloader->cleanup();
        // This will rotate s3 file to the backup
        $downloader->completed();

        // Validate the file was rotated to processed directory.
        $files = $this->app['cloudstorage.list']($s3Prefix);
        $keys = [];
        $foundArchiveFile = false;
        $archiveFile = null;

        foreach ($files as $file) {
            $keys[] = $file['Key'];

            if (preg_match('/archive-catalog-import-\d{14}-tests-' . $filenameNewer . '/', $file['Key'])) {
                $foundArchiveFile = true;
                $archiveFile = $file['Key'];
            }
        }

        $I->assertContains($s3PathNewer, $keys);
        $I->assertTrue($foundArchiveFile, 'archive file not created for feed');

        // Clean up
        $this->app['cloudstorage.client']->deleteObject(
            [
                'Bucket' => $this->app['configs']['s3.bucket'],
                'Key'    => $s3PathOlder,
            ]
        );
        $this->app['cloudstorage.client']->deleteObject(
            [
                'Bucket' => $this->app['configs']['s3.bucket'],
                'Key'    => $s3PathNewer,
            ]
        );
        $this->app['cloudstorage.client']->deleteObject(
            [
                'Bucket' => $this->app['configs']['s3.bucket'],
                'Key'    => $archiveFile,
            ]
        );

        unlink($path);
    }
}
