The snapshot of function: bulk
{
    "bulk": {
        "input": {
            "index": "sf_customer",
            "action": "index",
            "data": {
                "85": {
                    "id": "85",
                    "user_id": "1003",
                    "email": "<EMAIL>",
                    "first_name": "<PERSON><PERSON>",
                    "last_name": "doe3",
                    "full_name": "John3 doe3",
                    "type": "corporate",
                    "phone": "",
                    "subcribtion_flag": "0",
                    "sms_marketing_subscription_flag": "0",
                    "locale": "en_US",
                    "crm_id": null,
                    "related_crm_id": null,
                    "retailer_customer_id": null,
                    "created": "2025-07-05T14:36:15Z",
                    "last_contacted": null,
                    "alternateEmail": null,
                    "alternatePhone": null,
                    "addresses": null,
                    "notes": null,
                    "tags": null,
                    "attributes": null,
                    "favorite_contacts": null
                }
            }
        }
    }
}

