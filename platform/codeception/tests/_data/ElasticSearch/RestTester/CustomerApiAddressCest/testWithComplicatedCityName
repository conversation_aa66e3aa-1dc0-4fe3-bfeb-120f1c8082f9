{"createIndex":{"input":{"index":"sf_customer-2023-08-18-13-25-59","body":{"settings":{"number_of_shards":2,"refresh_interval":"1s","number_of_replicas":0}}},"output":{"acknowledged":true,"shards_acknowledged":true,"index":"6375c2cd7872b-tests-dev.sf_customer-2023-08-18-13-25-59"}}}
{"bulk":{"input":{"index":"sf_customer-2023-08-18-13-25-59","action":"index","data":[{"id":"1","user_id":"1","email":"<EMAIL>","first_name":"<PERSON>","last_name":"Mallette","full_name":"<PERSON>","type":"corporate","phone":"","subcribtion_flag":"0","sms_marketing_subscription_flag":"0","locale":"en_US","crm_id":"123456","related_crm_id":null,"retailer_customer_id":"123456","alternateEmail":null,"alternatePhone":null,"addresses":null,"notes":null,"tags":null,"attributes":null,"favorite_contacts":null},{"id":"2","user_id":"1","email":"<EMAIL>","first_name":"Joseph","last_name":"Mallette","full_name":"Joseph Mallette","type":"corporate","phone":"+15148467733","subcribtion_flag":"0","sms_marketing_subscription_flag":"0","locale":"fr_CA","crm_id":"1234567","related_crm_id":null,"retailer_customer_id":"1234567","alternateEmail":null,"alternatePhone":null,"addresses":null,"notes":null,"tags":null,"attributes":null,"favorite_contacts":null},{"id":"8","user_id":"1","email":"<EMAIL>","first_name":"Jordan","last_name":"Mallette","full_name":null,"type":"personal","phone":"","subcribtion_flag":"1","sms_marketing_subscription_flag":"0","locale":null,"crm_id":null,"related_crm_id":null,"retailer_customer_id":null,"alternateEmail":null,"alternatePhone":null,"addresses":[{"label":"home","address_line_1":"2691 Ave Van Horne","city":"Montreal","state":"QC","postal_code":"H3S1P6","country":"Canada"},{"label":"home","address_line_1":"212 US-20","city":"O' N\u00e9ill","state":"NE","postal_code":"68763","country":"United States"},{"label":"home","address_line_1":"1000 Ave Van Horne","city":"Montreal","state":"QC","postal_code":"H3S1P6","country":"Canada"}],"notes":null,"tags":null,"attributes":null,"favorite_contacts":null},{"id":"9","user_id":"1","email":"<EMAIL>","first_name":"Jordan","last_name":"Someone","full_name":null,"type":"personal","phone":"","subcribtion_flag":"2","sms_marketing_subscription_flag":"0","locale":null,"crm_id":null,"related_crm_id":null,"retailer_customer_id":null,"alternateEmail":null,"alternatePhone":null,"addresses":[{"label":"home","address_line_1":"212 some road","city":"Amsterdam","state":" ","postal_code":"68763","country":"Germany"},{"label":"home","address_line_1":"2691 Ave Van Horne","city":"Montreal","state":"qc","postal_code":"H3S1P6","country":"Canada"}],"notes":null,"tags":["1","2","3","4","5"],"attributes":null,"favorite_contacts":null}]}}}
{"updateSettings":{"index":"sf_customer-2023-08-18-13-25-59","settings":{"index":{"refresh_interval":"1s","number_of_replicas":0}}}}
{"switchAlias":{"index":"sf_customer-2023-08-18-13-25-59","alias":"sf_customer"}}
{"searchByFilters":{"input":{"index":"sf_customer","filters":{"must":[{"bool":{"should":[{"addresses.city":"O' N\u00e9ill"}]}}],"mustNot":[],"should":[]},"page":0,"perPage":10,"fields":null,"countOnly":false},"output":{"took":1,"timed_out":false,"_shards":{"total":2,"successful":2,"skipped":0,"failed":0},"hits":{"total":{"value":1,"relation":"eq"},"max_score":0,"hits":[{"_index":"6375c2cd7872b-tests-dev.sf_customer-2023-08-18-13-25-59","_id":"8","_score":0,"_source":{"retailer_customer_id":null,"alternateEmail":null,"addresses":[{"country":"Canada","city":"Montreal","address_line_1":"2691 Ave Van Horne","label":"home","state":"QC","postal_code":"H3S1P6"},{"country":"United States","city":"O' N\u00e9ill","address_line_1":"212 US-20","label":"home","state":"NE","postal_code":"68763"},{"country":"Canada","city":"Montreal","address_line_1":"1000 Ave Van Horne","label":"home","state":"QC","postal_code":"H3S1P6"}],"notes":null,"alternatePhone":null,"last_name":"Mallette","type":"personal","locale":null,"tags":null,"full_name":null,"related_crm_id":null,"user_id":"1","phone":"","crm_id":null,"favorite_contacts":null,"subcribtion_flag":"1","attributes":null,"sms_marketing_subscription_flag":"0","first_name":"Jordan","email":"<EMAIL>"}}]}}}}
{"searchByFilters":{"input":{"index":"sf_customer","filters":{"must":[{"bool":{"should":[{"addresses.city":"O' N\u00e9ill"}]}}],"mustNot":[],"should":[]},"page":0,"perPage":0,"fields":null,"countOnly":false},"output":{"took":0,"timed_out":false,"_shards":{"total":2,"successful":2,"skipped":0,"failed":0},"hits":{"total":{"value":1,"relation":"eq"},"max_score":null,"hits":[]}}}}
