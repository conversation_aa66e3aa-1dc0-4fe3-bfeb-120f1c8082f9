{"curlApi": {"input": {"url": "/suppression/unsubscribes?offset=0&limit=500", "method": "GET", "postFields": [], "useMasterKey": true, "onBehalfOf": "sf-rtb-dev"}, "output": {"result": [], "info": {"url": "https://api.sendgrid.com/v3//suppression/unsubscribes?offset=0&limit=500", "content_type": "application/json", "http_code": 200, "header_size": 1275, "request_size": 253, "filetime": -1, "ssl_verify_result": 0, "redirect_count": 0, "total_time": 0.269494, "namelookup_time": 2.4e-05, "connect_time": 0.038342, "pretransfer_time": 0.130538, "size_upload": 0, "size_download": 2, "speed_download": 7, "speed_upload": 0, "download_content_length": -1, "upload_content_length": -1, "starttransfer_time": 0.269209, "redirect_time": 0, "redirect_url": "", "primary_ip": "**************", "certinfo": [], "primary_port": 443, "local_ip": "**********", "local_port": 48302, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "HTTPS"}, "error": "", "curlError": "", "header": {"server": ["nginx"], "date": ["Fri, 18 Aug 2023 13:13:55 GMT"], "content-type": ["application/json"], "transfer-encoding": ["chunked"], "connection": ["keep-alive"], "access-control-allow-methods": ["OPTIONS, HEAD, GET, POST, DELETE"], "access-control-max-age": ["21600"], "access-control-expose-headers": ["Link, Location"], "access-control-allow-origin": ["*"], "access-control-allow-headers": ["AUTHORIZATION, Content-Type, On-behalf-of, x-sg-elas-acl, X-Recaptcha, X-Request-Source, Browser-Fingerprint"], "content-security-policy": ["default-src https://api.sendgrid.com; frame-src 'none'; object-src 'none'"], "x-content-type-options": ["nosniff"], "strict-transport-security": ["max-age=31536000", "max-age=600; includeSubDomains"], "x-client-ff": ["1000"], "x-ratelimit-remaining": ["599"], "x-ratelimit-limit": ["600"], "x-ratelimit-reset": ["1692364440"], "link": ["<https://api.sendgrid.com/v3/suppression/unsubscribes?limit=500&offset=0>; rel=\"next\"; title=\"1\", <https://api.sendgrid.com/v3/suppression/unsubscribes?limit=500&offset=0>; rel=\"prev\"; title=\"1\", <https://api.sendgrid.com/v3/suppression/unsubscribes?limit=500&offset=0>; rel=\"last\"; title=\"1\", <https://api.sendgrid.com/v3/suppression/unsubscribes?limit=500&offset=0>; rel=\"first\"; title=\"1\""], "twilio-request-id": ["RQ2U9v6Z1TfWpS9ctl4S7xi52cQLl"], "powered-by": ["<PERSON><PERSON>"]}}}}