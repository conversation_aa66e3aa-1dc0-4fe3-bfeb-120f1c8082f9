{"curlApi":{"input":{"url":"\/asm\/suppressions\/global","method":"POST","postFields":{"recipient_emails":["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]},"useMasterKey":true,"onBehalfOf":"sf-elguntors-dev"},"output":{"result":{"recipient_emails":["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]},"info":{"url":"https:\/\/api.sendgrid.com\/v3\/\/asm\/suppressions\/global","content_type":"text\/plain; charset=utf-8","http_code":201,"header_size":1001,"request_size":284,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.348274,"namelookup_time":0.012519,"connect_time":0.057325,"pretransfer_time":0.151278,"size_upload":1713,"size_download":1714,"speed_download":4925,"speed_upload":4922,"download_content_length":1714,"upload_content_length":1713,"starttransfer_time":0.199701,"redirect_time":0,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":43094,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"HTTPS"},"error":"","curlError":"","header":{"server":["nginx"],"date":["Tue, 16 Jan 2024 21:19:38 GMT"],"content-type":["text\/plain; charset=utf-8"],"content-length":["1714"],"connection":["keep-alive"],"t-request-id":["RQ2b3NojfAMgYgqFkybtL3IaCfaG7"],"access-control-allow-methods":["HEAD, GET, PUT, POST, DELETE, OPTIONS, PATCH"],"access-control-max-age":["21600"],"access-control-expose-headers":["Link, Location"],"access-control-allow-origin":["*"],"access-control-allow-headers":["AUTHORIZATION, Content-Type, On-behalf-of, x-sg-elas-acl, X-Recaptcha, X-Request-Source, Browser-Fingerprint"],"content-security-policy":["default-src https:\/\/api.sendgrid.com; frame-src 'none'; object-src 'none'"],"x-content-type-options":["nosniff"],"strict-transport-security":["max-age=31536000","max-age=600; includeSubDomains"],"x-client-ff":["1000"],"x-ratelimit-remaining":["599"],"x-ratelimit-limit":["600"],"x-ratelimit-reset":["1705440000"],"twilio-request-id":["RQ2b3NojfAMgYgqFkybtL3IaCfaG7"],"x-envoy-upstream-service-time":["95"],"powered-by":["Mako"]}}}}
{"curlApi":{"input":{"url":"\/suppression\/unsubscribes?offset=0&limit=500","method":"GET","postFields":[],"useMasterKey":true,"onBehalfOf":"sf-elguntors-dev"},"output":{"result":[{"email":"<EMAIL>","created":1698947000},{"email":"<EMAIL>","created":1698947000},{"email":"<EMAIL>","created":1686065094},{"email":"<EMAIL>","created":1650629530},{"email":"<EMAIL>","created":1650541214},{"email":"<EMAIL>","created":1650541214},{"email":"<EMAIL>","created":1650541214},{"email":"<EMAIL>","created":1650541214},{"email":"<EMAIL>","created":1649765751},{"email":"<EMAIL>","created":1649765751},{"email":"<EMAIL>","created":1649086651},{"email":"<EMAIL>","created":1649086651},{"email":"<EMAIL>","created":1648843552},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455},{"email":"<EMAIL>","created":1566414455}],"info":{"url":"https:\/\/api.sendgrid.com\/v3\/\/suppression\/unsubscribes?offset=0&limit=500","content_type":"application\/json","http_code":200,"header_size":919,"request_size":259,"filetime":-1,"ssl_verify_result":0,"redirect_count":0,"total_time":0.274883,"namelookup_time":3.1e-5,"connect_time":0.039553,"pretransfer_time":0.12592,"size_upload":0,"size_download":3437,"speed_download":12543,"speed_upload":0,"download_content_length":-1,"upload_content_length":-1,"starttransfer_time":0.274686,"redirect_time":0,"redirect_url":"","primary_ip":"************","certinfo":[],"primary_port":443,"local_ip":"**********","local_port":43098,"http_version":2,"protocol":2,"ssl_verifyresult":0,"scheme":"HTTPS"},"error":"","curlError":"","header":{"server":["nginx"],"date":["Tue, 16 Jan 2024 21:19:38 GMT"],"content-type":["application\/json"],"transfer-encoding":["chunked"],"connection":["keep-alive"],"vary":["Accept-Encoding,Origin"],"strict-transport-security":["max-age=15724800; includeSubDomains"],"link":["<https:\/\/api.sendgrid.com\/v3\/suppression\/unsubscribes?limit=500&offset=0>; rel=\"next\"; title=\"1\", <https:\/\/api.sendgrid.com\/v3\/suppression\/unsubscribes?limit=500&offset=0>; rel=\"prev\"; title=\"1\", <https:\/\/api.sendgrid.com\/v3\/suppression\/unsubscribes?limit=500&offset=0>; rel=\"last\"; title=\"1\", <https:\/\/api.sendgrid.com\/v3\/suppression\/unsubscribes?limit=500&offset=0>; rel=\"first\"; title=\"1\""],"twilio-request-id":["RQe44aab431b5c9a8c017d34f8b073c58f"],"x-envoy-upstream-service-time":["27"],"referrer-policy":["strict-origin-when-cross-origin"],"x-content-type-options":["nosniff"],"x-ratelimit-remaining":["599"],"x-ratelimit-reset":["22"],"x-ratelimit-limit":["600"],"powered-by":["SGGateway"]}}}}
