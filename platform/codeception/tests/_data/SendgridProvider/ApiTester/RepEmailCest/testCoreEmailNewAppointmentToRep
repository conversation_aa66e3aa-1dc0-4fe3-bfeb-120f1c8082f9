{"curlApi": {"input": {"url": "/mail/send", "method": "POST", "postFields": {"personalizations": [{"to": [{"email": "<EMAIL>"}]}], "from": {"email": "<EMAIL>", "name": "<PERSON>"}, "subject": "Appointment Request", "content": [{"type": "text/plain", "value": " \n\n You have a new Appointment Request - \n\n \t\t Go to my backoffice\n[https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&%24deeplink_path=%2F%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg]\n\n\n \t\t You have a new Appointment Request \n\nHI TEST,\n\n<PERSON> has requested a Chat appointment for \n\nTHURSDAY, JUNE 15, 2017 AT 9:00 PM\n\nCUSTOMER INFORMATION\n\nName: <PERSON> \n\nEmail: <EMAIL> \n\nPhone: ******-508-2007 \n\nCOMMENT:\n\n \t\t Accept\n[https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fsfadmin%2Fappointment.php%3Fmagicid%3DSFID5942ee9e8f16f5.00110543%26meetingid%3D3%26action%3Daccept%26customer%3Dfalse%26rep%3Dtestemail%26version%3D2&%24deeplink_path=%2Fstore-requests%2Fid%2Fbook_appointment_3%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg]\n\n\n \t\t Suggest new time\n[https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fsfadmin%2Fappointment.php%3Fmagicid%3DSFID5942ee9e8f16f5.00110543%26meetingid%3D3%26action%3Dchange%26version%3D2&%24deeplink_path=%2Fstore-requests%2Fnew-time%2Fid%2Fbook_appointment_3%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg]\n\n\n You are receiving Salesfloor notifications, sent on behalf of: \n @@retailerName@@, 651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9 \n This email was intended for Test. \n [GLOBAL_UNSUBSCRIBE] \n\n "}, {"type": "text/html", "value": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n  <title></title>\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width\">\n  \n  \n  \n  \n\n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n</head>\n<body style=\"-ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background: #f6f6f6; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; line-height: 19px; margin: 0; min-width: 100%; padding: 0; text-align: left; width: 100%;\">\n  <table class=\"body\" style=\"background: #f6f6f6; border-collapse: collapse; border-spacing: 0; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; height: 100%; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n    <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n      <td class=\"center main-td\" align=\"center\" valign=\"top\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n        <center style=\"min-width: 580px; width: 100%;\">\n        \n          \n<span class=\"hidden\" style=\"display: none; font-size: 0px; height: 0px; line-height: 0px; text-indent: -999999px; visibility: hidden; width: 0px;\">\n  \n  You have a new Appointment Request - \n\n</span>\n\t\n    \n\t\t<table class=\"row header\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n    <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n      <td class=\"center\" align=\"center\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n        <center style=\"min-width: 580px; width: 100%;\">\n\n          <table class=\"container\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: inherit; vertical-align: top; width: 580px;\">\n            <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n              <td class=\"wrapper last\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; position: relative; text-align: left; vertical-align: top;\">\n\n                <table class=\"twelve columns\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: left; vertical-align: top; width: 580px;\">\n                  <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                    <td class=\"header-logo-container\" align=\"center\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 10px 0 10px 0; text-align: center; vertical-align: top;\">\n                      \n                        <img class=\"header-logo-retina noreset\" src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/tests/logo_small.png\" style=\"-ms-interpolation-mode: bicubic; clear: both; display: inline; float: none; max-width: 100%; outline: none; text-decoration: none; width: auto;\">\n                      \n                    </td>\n                    <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n                  </tr>\n                </tbody></table>\n                <table class=\"twelve columns\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: left; vertical-align: top; width: 580px;\">\n                  <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                    <td class=\"header-back-office-container\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0 0 10px 0; text-align: right; vertical-align: middle;\">\n                      \n                        <a universal=\"true\" class=\"btn-back-office\" href=\"https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&amp;%24deeplink_path=%2F%3FretailerId%3Dtests-dev&amp;%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&amp;%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&amp;%243p=e_sg\" style=\"color: #2ba6cb; text-decoration: none;\">Go to my backoffice</a>\n                      \n                    </td>\n                    <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n                  </tr>\n                </tbody></table>\n\n              </td>\n            </tr>\n          </tbody></table>\n\n        </center>\n      </td>\n    </tr>\n  </tbody>\n</table>\n\n\t\n\n\t<table class=\"row \" style=\"border-collapse: collapse; border-spacing: 0; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n  \t<tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n    \t<td class=\"center\" align=\"center\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n      \t<center style=\"min-width: 580px; width: 100%;\">\n\n\n        <table class=\"container    \" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: inherit; vertical-align: top; width: 580px;\">\n          <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n            <td class=\"\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n          \n            \n\t\n<table class=\"row alert-container alert-container-success\" style=\"background: #20a536; border-collapse: collapse; border-left: 1px solid #e9e9e9; border-radius: 3px 3px 0 0; border-right: 1px solid #e9e9e9; border-spacing: 0; border-top: 1px solid #e9e9e9; display: block; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n  <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n    <td class=\" last\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; padding-right: 0px; text-align: left; vertical-align: top;\">\n        \n        <table class=\"twelve columns displayTable\" style=\"border-collapse: collapse; border-spacing: 0; display: table; margin: 0 auto; padding: 0; text-align: left; vertical-align: top; width: 580px;\">\n          <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n            <td class=\"alert-content\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #fff; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 15px; font-weight: 500; hyphens: manual; line-height: 19px; margin: 0; padding: 15px; text-align: center; vertical-align: top;\">\n                You have a new Appointment Request\n            </td>\n            <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n          </tr>\n        </tbody></table>\n    </td>\n  </tr>\n</tbody></table>\n\n  \n\n            <table class=\"row content-container no-radius\" style=\"background: #fff; border: 1px solid #e9e9e9; border-collapse: collapse; border-radius: 0px; border-spacing: 0; border-top: 0px; display: block; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n              <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                <td class=\"wrapper last  \" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; position: relative; text-align: left; vertical-align: top;\">\n                    \n                  <table class=\"twelve columns\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: left; vertical-align: top; width: 580px;\">\n                    <tbody><tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                      <td class=\"content-wrap\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 20px; text-align: left; vertical-align: top;\">\n\n                      \t\n\n  \t<h2 class=\"title-hello\" style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 23px; font-weight: normal; line-height: 1.3; margin: 0px 0 15px 0; margin-bottom: 10px; margin-top: 0; padding: 0; padding-top: 0; text-align: left; word-break: normal;\">Hi Test,</h2>\n\n\t<p style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; line-height: 19px; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">Roberto Reganito has requested a Chat appointment for</p>\n\t<h5 style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 15px; font-weight: normal; line-height: 1.3; margin: 8px 0; margin-bottom: 10px; margin-top: 10px; padding: 0; text-align: left; word-break: normal;\"><strong>Thursday, June 15, 2017 at 9:00 PM</strong></h5>\n\t\n\t\n  <h4 class=\"title\" style=\"border-bottom: 1px solid #CACACA; color: #666; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 18px; font-weight: normal; line-height: 1.3; margin: 15px 0; margin-bottom: 10px; margin-top: 10px; padding: 0; padding-bottom: 2px; text-align: left; word-break: normal;\">Customer Information</h4>\n  <p class=\"marg-btm-5\" style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; line-height: 19px; margin: 0; margin-bottom: 5px; padding: 0; padding-bottom: 0px; text-align: left;\">Name: Roberto Reganito</p>\n  <p class=\"marg-btm-5\" style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; line-height: 19px; margin: 0; margin-bottom: 5px; padding: 0; padding-bottom: 0px; text-align: left;\">Email: <EMAIL></p>\n  <p style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; line-height: 19px; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">Phone: ******-508-2007</p>\n\n\t<h4 class=\"title\" style=\"border-bottom: 1px solid #CACACA; color: #666; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 18px; font-weight: normal; line-height: 1.3; margin: 15px 0; margin-bottom: 10px; margin-top: 10px; padding: 0; padding-bottom: 2px; text-align: left; word-break: normal;\">Comment:</h4>\n  \t<p style=\"color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; line-height: 19px; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\"></p>\n\n\t<table class=\"btn-tools\" style=\"border-collapse: collapse; border-spacing: 0; margin-top: 30px; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t<td style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\n\t<table class=\"button btn-blue small-button\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 0px 10px 0; overflow: hidden; padding: 0; text-align: left; vertical-align: top;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t  <td class=\"\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background: #2178cf; border-collapse: collapse; border-radius: 3px; color: #ffffff; display: block; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0px 0; text-align: center; transition: background-color 0.5s ease; vertical-align: top; width: auto;\">\n\t\t    <a class=\"\" universal=\"true\" href=\"https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fsfadmin%2Fappointment.php%3Fmagicid%3DSFID5942ee9e8f16f5.00110543%26meetingid%3D3%26action%3Daccept%26customer%3Dfalse%26rep%3Dtestemail%26version%3D2&amp;%24deeplink_path=%2Fstore-requests%2Fid%2Fbook_appointment_3%3FretailerId%3Dtests-dev&amp;%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&amp;%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&amp;%243p=e_sg\" style=\"background: #2178cf; border: 1px solid #2178cf; border-radius: 3px; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 14px; padding: 8px 7px 7px 7px; text-decoration: none;\">Accept </a>\n\t\t  </td>\n\t\t</tr>\n\t</table>\n\n\t\t\t\t<table class=\"btn-spacer\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; height: 1px; overflow: hidden; padding: 0; text-align: left; vertical-align: top; width: 10px;\"><tr style=\"padding: 0; text-align: left; vertical-align: top;\"><td style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; display: block; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; height: 1px; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: auto;\"></td></tr></table>\n\t\t\t\t\n\t<table class=\"button btn-blue small-button\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 0px 10px 0; overflow: hidden; padding: 0; text-align: left; vertical-align: top;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t  <td class=\"\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background: #2178cf; border-collapse: collapse; border-radius: 3px; color: #ffffff; display: block; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0px 0; text-align: center; transition: background-color 0.5s ease; vertical-align: top; width: auto;\">\n\t\t    <a class=\"\" universal=\"true\" href=\"https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fsfadmin%2Fappointment.php%3Fmagicid%3DSFID5942ee9e8f16f5.00110543%26meetingid%3D3%26action%3Dchange%26version%3D2&amp;%24deeplink_path=%2Fstore-requests%2Fnew-time%2Fid%2Fbook_appointment_3%3FretailerId%3Dtests-dev&amp;%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&amp;%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&amp;%243p=e_sg\" style=\"background: #2178cf; border: 1px solid #2178cf; border-radius: 3px; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 14px; padding: 8px 7px 7px 7px; text-decoration: none;\">Suggest new time </a>\n\t\t  </td>\n\t\t</tr>\n\t</table>\n\n\t\t\t</td>\n\t\t</tr>\n\t</table>\n\n\n                        \n                      </td>\n                      <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n                    </tr>\n                  </tbody></table>\n                </td>\n              </tr>\n            </tbody></table>\n\n            \n              <table class=\"row footer\" style=\"border-collapse: collapse; border-spacing: 0; display: block; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n  <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n    <td class=\"wrapper last\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 10px 20px 0px 0px; padding-right: 0px; position: relative; text-align: left; vertical-align: top;\">\n      <table class=\"twelve columns\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: left; vertical-align: top; width: 580px;\">\n        <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n          <td align=\"left\" class=\"footer-content\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #999; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 12px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n            \n            You are receiving Salesfloor notifications, sent on behalf of:\n            <br>\n            @@retailerName@@, 651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9\n            <br>\n            This email was intended for Test.\n            \n            <div style=\"display: none\">\n              [GLOBAL_UNSUBSCRIBE]\n            </div>\n            <table width=\"150\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top; width: 150px;\">\n              <tr style=\"padding: 0; text-align: left; vertical-align: top;\"><td class=\"footer-logo-container\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 10px 0; text-align: left; vertical-align: top;\"><img class=\"footer-logo\" width=\"150\" src=\"https://cdn.salesfloor.net/salesfloor-assets/logo_internal_email.png\" style=\"-ms-interpolation-mode: bicubic; clear: both; display: block; float: left; height: auto; max-width: 150px; outline: none; text-decoration: none; width: 150px;\"></td><td width=\"150\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 150px;\" class=\"\"></td></tr>\n            </table>\n          </td>\n          <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-size: 13px; font-weight: normal; hyphens: manual; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n        </tr>\n      </table>\n    </td>\n  </tr>\n</table>\n\n               \n\n            </td>\n          </tr>\n        </tbody></table>\n      </center>\n    </td>\n  </tr>\n</tbody></table>   \n\n\n\n        </center>\n      </td>\n    </tr>\n  </table>\n</body>\n</html>"}], "tracking_settings": {"open_tracking": {"enable": false}, "click_tracking": {"enable": false, "enable_text": false}}, "mail_settings": {"bypass_list_management": {"enable": false}}, "categories": ["Other"], "reply_to": {"email": "<EMAIL>", "name": "<PERSON>"}, "attachments": [{"content": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "application/octet-stream", "filename": "invite.ics"}]}, "useMasterKey": false, "onBehalfOf": null}, "output": {"result": [], "info": {"url": "https://api.sendgrid.com/v3//mail/send", "content_type": null, "http_code": 202, "header_size": 530, "request_size": 239, "filetime": -1, "ssl_verify_result": 0, "redirect_count": 0, "total_time": 0.275688, "namelookup_time": 2.3e-05, "connect_time": 0.042441, "pretransfer_time": 0.148451, "size_upload": 26463, "size_download": 0, "speed_download": 0, "speed_upload": 96229, "download_content_length": 0, "upload_content_length": 26463, "starttransfer_time": 0.198003, "redirect_time": 0, "redirect_url": "", "primary_ip": "**************", "certinfo": [], "primary_port": 443, "local_ip": "**********", "local_port": 60126, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "HTTPS"}, "error": "", "curlError": "", "header": {"server": ["nginx"], "date": ["Thu, 17 Aug 2023 20:41:45 GMT"], "content-length": ["0"], "connection": ["keep-alive"], "x-message-id": ["uz2n77GBQwOUA30z0OfqIg"], "access-control-allow-origin": ["https://sendgrid.api-docs.io"], "access-control-allow-methods": ["POST"], "access-control-allow-headers": ["Authorization, Content-Type, On-behalf-of, x-sg-elas-acl"], "access-control-max-age": ["600"], "x-no-cors-reason": ["https://sendgrid.com/docs/Classroom/Basics/API/cors.html"], "strict-transport-security": ["max-age=600; includeSubDomains"]}}}}