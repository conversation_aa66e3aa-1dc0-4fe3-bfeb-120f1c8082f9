{"curlApi": {"input": {"url": "/mail/send", "method": "POST", "postFields": {"personalizations": [{"to": [{"email": "<EMAIL>"}]}], "from": {"email": "<EMAIL>", "name": "Fake Mall, @@retailerName@@"}, "subject": "Appointment Request", "content": [{"type": "text/plain", "value": " \n\n Test, @@retailerName@@), suggested a new appointment time - Store:\nFake Mall, 1455 Peel Street, Montreal QC H3A 1T5 \n\n \t\t\n[https://tests.dev.salesfloor.net/en_US/testemail?event_source=email] \n\n \t\t SHOP WITH ME\n[https://tests.dev.salesfloor.net/en_US/testemail?event_source=email] \n \t\t REQUEST APPOINTMENT\n[https://tests.dev.salesfloor.net/en_US/testemail?appointment=1&event_source=email]\n\n \t\t PERSONAL SHOPPER\n[https://tests.dev.salesfloor.net/en_US/testemail?shopper=1&event_source=email]\n\n \t\t EMAIL ME\n[https://tests.dev.salesfloor.net/en_US/testemail?ask=1&event_source=email]\n\n\n \t\t SHOP\n[https://tests.dev.salesfloor.net/en_US/testemail?event_source=email] \n \t\t APPOINTMENT\n[https://tests.dev.salesfloor.net/en_US/testemail?appointment=1&event_source=email]\n\n \t\t PERSONAL SHOPPER\n[https://tests.dev.salesfloor.net/en_US/testemail?shopper=1&event_source=email]\n\n \t\t EMAIL ME\n[https://tests.dev.salesfloor.net/en_US/testemail?ask=1&event_source=email]\n\n\n \t\t\n[https://tests.dev.salesfloor.net/en_US/testemail?event_source=email] \n TEST\n[https://tests.dev.salesfloor.net/en_US/testemail?event_source=email]\nsuggested a new appointment time \n\n Date and time:\n Thursday, June 15, 2017 at 9:00 PM \n\n Meeting type:\n Chat \n\n Name:\n Test \n\n Email:\n <EMAIL> \n\n COMMENT:\n\nFake Mall 1455 Peel Street, Montreal H3A 1T5 \n\nCLICK TO ACCEPT THE MEETING:\n\n \t\t Accept\n[https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5942ee9e8f16f5.00110543&meetingid=2&sf_locale=en_US&version=2&action=accept&customer=true&rep=testemail]\n\n\nCLICK WHEN YOU ARE READY TO START THE MEETING:\n\n \t\t Launch chat\n[https://tests.dev.salesfloor.net/en_US/testemail?live_service=1&version=2&sf_locale=en_US&event_source=email]\n\n\nCLICK TO RESCHEDULE:\n\n \t\t Reschedule\n[https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5942ee9e8f16f5.00110543&meetingid=2&sf_locale=en_US&version=2&action=change&rep=testemail]\n\n\nCLICK TO CANCEL:\n\n \t\t Cancel\n[https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5942ee9e8f16f5.00110543&meetingid=2&sf_locale=en_US&version=2&action=cancel&rep=testemail]\n\n\n TEST\n @@retailerName@@\n Montreal, QC \n Get My Updates\n[https://tests.dev.salesfloor.net/en_US/testemail/subscribe?event_source=email]\n\n\n \t\t [https://instagram.com/saks] \n \t\t [https://www.facebook.com/saks] \n \t\t [https://pinterest.com/saks] \n \t\t [https://twitter.com/saks] \n\n © 2023 Saks Fifth Avenue. All Rights Reserved. \n 651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9 \n\n \t\t Privacy Policy\n[https://tests.dev.salesfloor.net/shop?rep=testemail&sf_url=https://www.saksfifthavenue.com/c/content/privacy-policy&event_source=email]\n- Terms & Conditions\n[https://tests.dev.salesfloor.net/shop?rep=testemail&sf_url=https://www.saksfifthavenue.com/c/content/terms-and-conditions&event_source=email]\n\n\n \t\t Unsubscribe from this Style Advisor Updates\n[/[GLOBAL_UNSUBSCRIBE]] \n\n \t\t Unsubscribe from Saks Fifth Avenue Marketing Emails \n\n "}, {"type": "text/html", "value": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n  <title></title>\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width\">\n  \n  \n  \n  \n  \n    \n  \n\n\n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n</head>\n<body style=\"-ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background-color: #f8f8f8; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; min-width: 100%; padding: 0; text-align: left; width: 100%;\">\n  <table class=\"body\" style=\"background-color: #f8f8f8; border-collapse: collapse; border-spacing: 0; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; height: 100%; line-height: 19px; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n    <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n      <td class=\"center main-td\" align=\"center\" valign=\"top\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background-color: #f8f8f8; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-bottom: 90px; padding-top: 55px; text-align: left; vertical-align: top; width: 100%;\">\n        <center style=\"border: none; min-width: 560px; width: 100%;\">\n        \n          \n<span class=\"hidden\" style=\"display: none; font-size: 0px; height: 0px; line-height: 0px; text-indent: -999999px; visibility: hidden; width: 0px;\">\n  \n  Test, @@retailerName@@), suggested a new appointment time - Store: Fake Mall, 1455  Peel Street, Montreal QC H3A 1T5\n\n</span>\n\n\t\n  \n  \n\t<table class=\"row\" style=\"border: none; border-collapse: collapse; border-spacing: 0; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t<td style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t<center style=\"border: none; min-width: 560px; width: 100%;\">\n\t\t\t\t<table class=\"container\" style=\"background-color: #ffffff; border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: inherit; vertical-align: top; width: 600px;\">\n\t\t\t\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t<td class=\"logo-ctn\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-bottom: 1px solid #f5f5f5; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 15px 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t<center style=\"border: none; min-width: 560px; width: 100%;\">\n\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?event_source=email\" style=\"color: #222222; text-decoration: none;\">\n\t\t\t\t\t\t\t\t\t<img class=\"logo-img\" width=\"300\" height=\"100\" src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/saks/logo-new.png\" style=\"-ms-interpolation-mode: bicubic; border: none; clear: both; display: block; float: none; height: 100px; max-width: 100%; outline: none; text-decoration: none; width: 300px;\">\n\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t</center>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t\n\t\t\t\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t<td style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t<div class=\"header-menu-table__desktop\">\n\t\t\t\t\t\t\t<table width=\"100%\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<td class=\"header-menu\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-bottom: 1px solid #f5f5f5; border-bottom-width: 1px; border-collapse: collapse; border-top: 1px solid #f5f5f5; border-top-width: 0; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 16px 0 13px; text-align: left; vertical-align: top; width: auto;\">\n\t\t\t\t\t\t\t\t\t\t<center class=\"header-center\" style=\"border: none; min-width: 280px; width: 100%;\">\n\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-left\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-left: 20px; padding-right: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">SHOP WITH ME</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-inside\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0 0 0 25px; padding-left: 0; padding-right: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?appointment=1&event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">REQUEST APPOINTMENT</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-inside\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0 0 0 25px; padding-left: 0; padding-right: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?shopper=1&event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">PERSONAL SHOPPER</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-inside header-link--is-right\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0 0 0 25px; padding-left: 0; padding-right: 20px; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?ask=1&event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">EMAIL ME</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t\t\t</center>\t\t\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"header-menu-table__responsive\" style=\"display: none;\">\n\t\t\t\t\t\t\t<table width=\"100%\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<td class=\"header-menu\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-bottom: 1px solid #f5f5f5; border-bottom-width: 1px; border-collapse: collapse; border-top: 1px solid #f5f5f5; border-top-width: 0; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 16px 0 13px; text-align: left; vertical-align: top; width: auto;\">\n\t\t\t\t\t\t\t\t\t\t<center class=\"header-center\" style=\"border: none; min-width: 280px; width: 100%;\">\n\t\t\t\t\t\t\t\t\t\t\t<table width=\"100%\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-left\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-left: 20px; padding-right: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">SHOP</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-inside\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0 0 0 25px; padding-left: 0; padding-right: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?appointment=1&event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">APPOINTMENT</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-inside\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0 0 0 25px; padding-left: 0; padding-right: 0; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?shopper=1&event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">PERSONAL SHOPPER</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header-link header-link--is-inside header-link--is-right\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0 0 0 25px; padding-left: 0; padding-right: 20px; text-align: left; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a href=\"https://tests.dev.salesfloor.net/en_US/testemail?ask=1&event_source=email\" style=\"color: #222222; font-family: Times New Roman, Georgia, serif; font-size: 14px; text-decoration: none;\">EMAIL ME</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t\t\t</center>\t\t\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t\n\t\t\t\t</table>\n\t\t\t</center>\n\t\t</td>\n\t</tr>\n</table>\n\n\n  \n  <table class=\"row \" style=\"border: none; border-collapse: collapse; border-spacing: 0; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n    <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n    \t<td class=\"center\" align=\"center\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n      \t<center style=\"border: none; min-width: 560px; width: 100%;\">\n          <table class=\"twelve columns\" style=\"background-color: #ffffff; border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: left; vertical-align: top; width: 600px;\">\n            <tbody>\n              <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                <td class=\"content-container \" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border: none; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 20px 20px 10px; text-align: left; vertical-align: top;\">\n                  \n\t\n<center style=\"border: none; min-width: 560px; width: 100%;\">\n  <table class=\"avatar-wrapper\" style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top; width: 100%;\">\n    <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n      <td class=\"avatar-ctn\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border: none; border-collapse: collapse; border-radius: 100%; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; height: 85px; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top; width: 85px;\">\n        <a href=\"https://tests.dev.salesfloor.net/en_US/testemail?event_source=email\" style=\"color: #222222; text-decoration: none;\">\n          <img class=\"avatar-img\" src=\"https://res.cloudinary.com/salesfloor-net/image/upload/a_exif,c_fill,d_retailer_common_default_circle_2.jpg,g_face,h_250,w_250/v1692304904/dev/tests/testemail\" width=\"85\" height=\"85\" style=\"-ms-interpolation-mode: bicubic; border: none; border-radius: 100%; clear: both; display: block; float: none; height: 85px; max-width: 100%; outline: none; text-decoration: none; width: 85px;\">\n        </a>\n      </td>\n      <td class=\"avatar-text\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 10px 0 0 15px; text-align: left; vertical-align: top;\">\n        <a href=\"https://tests.dev.salesfloor.net/en_US/testemail?event_source=email\" style=\"color: #222222; text-decoration: none;\"><strong>Test</strong></a> \n        \t\n  \t\t\t    suggested a new appointment time\n  \t\t    \n          <table class=\"row\" style=\"border: none; border-collapse: collapse; border-spacing: 0; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n        \t  <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n  \t\t\t      <td style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n\n\n                  \n  \n\t\t      \t\t<!-- end of avatar template -->\n\t\t\t\t\t\t</td>\n\t      \t</tr>\n\t      </table>\n\t    </td>\n\t  </tr>\n\t</table>\n</center>\n<!-- END -->\n\n                </td>\n                <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n              </tr>\n              <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                <td class=\"content-container\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border: none; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 20px 20px 10px; text-align: left; vertical-align: top;\">\n                  \n\n\t\n<p style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">\n\tDate and time:<br>\n\tThursday, June 15, 2017 at 9:00 PM\n</p>\n<p style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">\n\tMeeting type:<br>\n\tChat\n</p>\n<p style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">\n\tName:<br>\n\tTest\n</p>\n<p style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">\n\tEmail:<br>\n\t<a href=\"mailto:<EMAIL>\" style=\"color: #222222; text-decoration: none;\"><EMAIL></a>\n</p>\n<p style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">\n\t<strong>Comment:</strong><br>\n\t<pre style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-weight: normal; line-height: 1.4; text-align: left;\"></pre>\n</p>\n<p style=\"color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; padding: 0; text-align: left;\">Fake Mall 1455  Peel Street, Montreal H3A 1T5</p>\n\n\t<h6 class=\"label-ctn\" style=\"clear: both; color: #222222; float: none; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 15px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; margin-top: 10px; padding: 0; text-align: left; word-break: normal;\">Click to accept the meeting:</h6>\n\n\t<table class=\"button btn-retailer small-button\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 0px 10px 0; overflow: hidden; padding: 0; text-align: left; vertical-align: top;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t  <td class=\"\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background: #2ba6cb; background-color: #222222; border-collapse: collapse; border-radius: 0; color: #ffffff; display: block; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 10px 25px; text-align: center; transition: background-color 0.5s ease; vertical-align: top; width: 106px;\">\n\t\t    <a class=\"\" href=\"https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5942ee9e8f16f5.00110543&amp;meetingid=2&amp;sf_locale=en_US&amp;version=2&amp;action=accept&amp;customer=true&amp;rep=testemail\" style=\"background-color: #222222; border: none; border-radius: 0; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 12px; padding: 0; text-decoration: none; text-transform: uppercase; width: auto;\">Accept </a>\n\t\t  </td>\n\t\t</tr>\n\t</table>\n\n<br clear=\"all\">\n\t<h6 class=\"label-ctn\" style=\"clear: both; color: #222222; float: none; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 15px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; margin-top: 10px; padding: 0; text-align: left; word-break: normal;\">Click when you are ready to start the meeting:</h6>\n\n\t<table class=\"button btn-retailer small-button\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 0px 10px 0; overflow: hidden; padding: 0; text-align: left; vertical-align: top;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t  <td class=\"\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background: #2ba6cb; background-color: #222222; border-collapse: collapse; border-radius: 0; color: #ffffff; display: block; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 10px 25px; text-align: center; transition: background-color 0.5s ease; vertical-align: top; width: 106px;\">\n\t\t    <a class=\"\" href=\"https://tests.dev.salesfloor.net/en_US/testemail?live_service=1&amp;version=2&amp;sf_locale=en_US&event_source=email\" style=\"background-color: #222222; border: none; border-radius: 0; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 12px; padding: 0; text-decoration: none; text-transform: uppercase; width: auto;\">Launch chat </a>\n\t\t  </td>\n\t\t</tr>\n\t</table>\n\n\n<br clear=\"all\">\n\n\n<br clear=\"all\">\n\n\n<h6 class=\"reschedule-info label-ctn\" style=\"clear: both; color: #222222; float: none; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 15px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; margin-top: 10px; padding: 0; text-align: left; word-break: normal;\">Click to reschedule:</h6>\n\n\t<table class=\"button btn-retailer small-button reschedule-info\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 0px 10px 0; overflow: hidden; padding: 0; text-align: left; vertical-align: top;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t  <td class=\"reschedule-info-link\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background: #2ba6cb; background-color: #222222; border-collapse: collapse; border-radius: 0; color: #ffffff; display: block; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 10px 25px; text-align: center; transition: background-color 0.5s ease; vertical-align: top; width: 106px;\">\n\t\t    <a class=\"reschedule-info-link\" href=\"https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5942ee9e8f16f5.00110543&amp;meetingid=2&amp;sf_locale=en_US&amp;version=2&amp;action=change&rep=testemail\" style=\"background-color: #222222; border: none; border-radius: 0; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 12px; padding: 0; text-decoration: none; text-transform: uppercase; width: auto;\">Reschedule </a>\n\t\t  </td>\n\t\t</tr>\n\t</table>\n\n<br clear=\"all\">\n\n<h6 class=\"label-ctn\" style=\"clear: both; color: #222222; float: none; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 15px; font-weight: normal; line-height: 1.4; margin: 0; margin-bottom: 10px; margin-top: 10px; padding: 0; text-align: left; word-break: normal;\">Click to cancel:</h6>\n\n\t<table class=\"button btn-retailer small-button\" align=\"left\" style=\"border-collapse: collapse; border-spacing: 0; margin: 0 0px 10px 0; overflow: hidden; padding: 0; text-align: left; vertical-align: top;\">\n\t\t<tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n\t\t  <td class=\"\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background: #2ba6cb; background-color: #222222; border-collapse: collapse; border-radius: 0; color: #ffffff; display: block; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 10px 25px; text-align: center; transition: background-color 0.5s ease; vertical-align: top; width: 106px;\">\n\t\t    <a class=\"\" href=\"https://tests.dev.salesfloor.net/sfadmin/appointment.php?magicid=SFID5942ee9e8f16f5.00110543&amp;meetingid=2&amp;sf_locale=en_US&amp;version=2&amp;action=cancel&amp;rep=testemail\" style=\"background-color: #222222; border: none; border-radius: 0; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 12px; padding: 0; text-decoration: none; text-transform: uppercase; width: auto;\">Cancel </a>\n\t\t  </td>\n\t\t</tr>\n\t</table>\n\n\t\n\n                </td>\n                <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n              </tr>\n            </tbody>\n          </table>\n        </center>\n      </td>\n      <td class=\"expander\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top; visibility: hidden; width: 0px;\"></td>\n    </tr>\n  </table> \n\n\n  \n\t\n\n<table class=\"row\" style=\"border: none; border-collapse: collapse; border-spacing: 0; padding: 0px; position: relative; text-align: left; vertical-align: top; width: 100%;\">\n  <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n    <td style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: left; vertical-align: top;\">\n      <center style=\"border: none; min-width: 560px; width: 100%;\">\n        <table class=\"container\" style=\"background-color: #ffffff; border-collapse: collapse; border-spacing: 0; margin: 0 auto; padding: 0; text-align: inherit; vertical-align: top; width: 600px;\">\n          \n          <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n            <td class=\"footer-rep\" align=\"center\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 40px 0 20px; position: relative; text-align: left; vertical-align: top;\">\n              <center style=\"border: none; min-width: 560px; width: 100%;\">\n                <a class=\"footer-rep-link\" href=\"https://tests.dev.salesfloor.net/en_US/testemail?event_source=email\" style=\"color: #222222; font-size: 12px; text-decoration: none;\">\n                  <strong>Test</strong><br>\n                  @@retailerName@@<br>\n                  Montreal, QC\n                </a><br>\n                <a href=\"https://tests.dev.salesfloor.net/en_US/testemail/subscribe?event_source=email\" class=\"footer-rep-link footer-rep-link--is-underlined\" style=\"color: #222222; font-size: 12px; text-decoration: underline;\">Get My Updates</a>\n              </center>\n            </td>\n          </tr>\n          \n          <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n          <td class=\"footer-bottom\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; background-color: #f8f8f8; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-top: 30px; text-align: left; vertical-align: top;\">\n          <center style=\"border: none; min-width: 560px; width: 100%;\">\n            \n            <table style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n              <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                <td class=\"socials-ctn\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: center; vertical-align: top; width: 65px;\">\n                  <a href=\"https://instagram.com/saks\" style=\"color: #222222; text-decoration: none;\">\n                    <img src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/saks/instagram.png\" height=\"21\" class=\"socials-img\" style=\"-ms-interpolation-mode: bicubic; border: none; clear: both; display: inline; float: none; height: 22px; max-width: 100%; outline: none; text-decoration: none; width: auto;\">\n                  </a>\n                </td>\n                <td class=\"socials-ctn\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: center; vertical-align: top; width: 65px;\">\n                  <a href=\"https://www.facebook.com/saks\" style=\"color: #222222; text-decoration: none;\">\n                    <img src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/saks/facebook.png\" height=\"21\" class=\"socials-img\" style=\"-ms-interpolation-mode: bicubic; border: none; clear: both; display: inline; float: none; height: 22px; max-width: 100%; outline: none; text-decoration: none; width: auto;\">\n                  </a>\n                </td> \n                <td class=\"socials-ctn\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: center; vertical-align: top; width: 65px;\">\n                  <a href=\"https://pinterest.com/saks\" style=\"color: #222222; text-decoration: none;\">\n                    <img src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/saks/pinterest.png\" height=\"21\" class=\"socials-img\n                  \" style=\"-ms-interpolation-mode: bicubic; border: none; clear: both; display: inline; float: none; height: 22px; max-width: 100%; outline: none; text-decoration: none; width: auto;\">\n                  </a>\n                </td> \n                <td class=\"socials-ctn\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; text-align: center; vertical-align: top; width: 65px;\">\n                  <a href=\"https://twitter.com/saks\" style=\"color: #222222; text-decoration: none;\">\n                    <img src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/saks/twitter.png\" height=\"21\" class=\"socials-img\" style=\"-ms-interpolation-mode: bicubic; border: none; clear: both; display: inline; float: none; height: 22px; max-width: 100%; outline: none; text-decoration: none; width: auto;\">\n                  </a>\n                </td> \n              </tr>\n            </table>\n                                 \n            <table style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n              <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                <td class=\"footer-address\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 14px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-top: 30px; text-align: left; vertical-align: top;\">\n                  <center style=\"border: none; min-width: 560px; width: 100%;\">\n                    <table style=\"border-collapse: collapse; border-spacing: 0; padding: 0; text-align: left; vertical-align: top;\">\n                      <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                        <td class=\"address-line\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-bottom: 10px; text-align: center; vertical-align: top;\">\n                          © 2023 Saks Fifth Avenue. All Rights Reserved.\n                          <br>\n                          651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9\n                        </td>\n                      </tr>\n                      <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                        <td class=\"address-line\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-bottom: 10px; text-align: center; vertical-align: top;\"></td>\n                      </tr>\n                      <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                        <td class=\"address-line policy-line\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-bottom: 5px; text-align: center; vertical-align: top;\">\n                          <a href=\"https://tests.dev.salesfloor.net/shop?rep=testemail&amp;sf_url=https://www.saksfifthavenue.com/c/content/privacy-policy&event_source=email\" target=\"_blank\" class=\"address-link\" style=\"color: #222222; text-decoration: underline;\">Privacy Policy</a> - <a href=\"https://tests.dev.salesfloor.net/shop?rep=testemail&amp;sf_url=https://www.saksfifthavenue.com/c/content/terms-and-conditions&event_source=email\" target=\"_blank\" class=\"address-link\" style=\"color: #222222; text-decoration: underline;\">Terms & Conditions</a>\n                        </td>\n                      </tr>\n                      <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                        <td class=\"address-line policy-line\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-bottom: 5px; text-align: center; vertical-align: top;\">\n                          <a href=\"[GLOBAL_UNSUBSCRIBE]\" target=\"_blank\" class=\"address-link\" style=\"color: #222222; text-decoration: underline;\">Unsubscribe from this Style Advisor Updates</a>\n                        </td>\n                      </tr>\n                      <tr style=\"padding: 0; text-align: left; vertical-align: top;\">\n                        <td class=\"address-line policy-line\" style=\"-moz-hyphens: manual; -webkit-hyphens: manual; border-collapse: collapse; color: #222222; font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 11px; font-weight: normal; hyphens: manual; line-height: 1.4; margin: 0; padding: 0; padding-bottom: 5px; text-align: center; vertical-align: top;\">\n                          <a href=\"\" target=\"_blank\" class=\"address-link\" style=\"color: #222222; text-decoration: underline;\">Unsubscribe from Saks Fifth Avenue Marketing Emails</a>\n                        </td>\n                      </tr>\n                    </table>\n                  </center>\n                </td>\n              </tr>\n            </table>\n            </center>\n          </td>\n        </tr>\n      </table>\n    </center>\n    </td>\n  </tr>\n</table>\n\n\n\n\t\n\n\n        </center>\n      </td>\n    </tr>\n  </table>\n</body>\n</html>"}], "tracking_settings": {"open_tracking": {"enable": false}, "click_tracking": {"enable": false, "enable_text": false}}, "mail_settings": {"bypass_list_management": {"enable": false}}, "categories": ["Other"], "reply_to": {"email": "<EMAIL>", "name": "Fake Mall, @@retailerName@@"}, "attachments": [{"content": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "application/octet-stream", "filename": "invite.ics"}]}, "useMasterKey": false, "onBehalfOf": null}, "output": {"result": [], "info": {"url": "https://api.sendgrid.com/v3//mail/send", "content_type": null, "http_code": 202, "header_size": 530, "request_size": 239, "filetime": -1, "ssl_verify_result": 0, "redirect_count": 0, "total_time": 0.288241, "namelookup_time": 6.3e-05, "connect_time": 0.037249, "pretransfer_time": 0.127326, "size_upload": 42862, "size_download": 0, "speed_download": 0, "speed_upload": 148826, "download_content_length": 0, "upload_content_length": 42862, "starttransfer_time": 0.183848, "redirect_time": 0, "redirect_url": "", "primary_ip": "**************", "certinfo": [], "primary_port": 443, "local_ip": "**********", "local_port": 57412, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "HTTPS"}, "error": "", "curlError": "", "header": {"server": ["nginx"], "date": ["Thu, 17 Aug 2023 20:41:44 GMT"], "content-length": ["0"], "connection": ["keep-alive"], "x-message-id": ["HHZhIJM_SxmF2wuvadEFlQ"], "access-control-allow-origin": ["https://sendgrid.api-docs.io"], "access-control-allow-methods": ["POST"], "access-control-allow-headers": ["Authorization, Content-Type, On-behalf-of, x-sg-elas-acl"], "access-control-max-age": ["600"], "x-no-cors-reason": ["https://sendgrid.com/docs/Classroom/Basics/API/cors.html"], "strict-transport-security": ["max-age=600; includeSubDomains"]}}}}