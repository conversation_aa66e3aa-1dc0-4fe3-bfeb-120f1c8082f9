{"curlApi": {"input": {"url": "/mail/send", "method": "POST", "postFields": {"personalizations": [{"to": [{"email": "<EMAIL>"}]}], "from": {"email": "<EMAIL>", "name": "Salesfloor"}, "subject": "Welcome to Salesfloor!", "content": [{"type": "text/plain", "value": " \n\n Congratulations on creating your new Salesfloor account. - Your\nprofile is live, and your storefront is available to customers here\n[https://tests.dev.salesfloor.net/en_US/random_user_login?event_source=email]\nA few things to remember: \n\n \t\t Go to my backoffice\n[https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&%24deeplink_path=%2F%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg]\n\n\n \t\t Congratulations on creating your new Salesfloor account. \n\nHI RANDOM_FIRST_NAME,\n\nYOUR PROFILE IS LIVE, AND YOUR STOREFRONT IS AVAILABLE TO CUSTOMERS\nHERE\n[https://tests.dev.salesfloor.net/en_US/random_user_login?event_source=email]\n\nA few things to remember: \n\n \t* Your username is random_user_login.\n \t* Remember the password you chose, write it down!\n \t* You can start responding to customer requests immediately and\naccess your account by logging in to your Backoffice.\n[https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&%24deeplink_path=%2F%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg]\n\n \t\t Don't forget to download the free mobile App now! \n\n \t\t [https://apps.apple.com/ca/app/salesfloor/id957494193] \n \t\t\n[https://play.google.com/store/apps/details?id=com.salesfloor.playstore]\n\n\nIf you have any questions or need help, please contact us for support\nat <EMAIL> \t\t\n\n You are receiving Salesfloor notifications, sent on behalf of:\n @@retailerName@@, 651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9\n This email was intended for random_first_name. \n [GLOBAL_UNSUBSCRIBE] \n\n "}, {"type": "text/html", "value": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n  <title></title>\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n  <meta name=\"viewport\" content=\"width=device-width\"/>\n  \n  \n  \n\n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n  \n\n<style type=\"text/css\">\nbody.outlook p {\ndisplay: inline !important;\n}\nbody {\nwidth: 100% !important; min-width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; margin: 0; padding: 0;\n}\n.ExternalClass {\nwidth: 100%;\n}\n.ExternalClass {\nline-height: 100%;\n}\n#backgroundTable {\nmargin: 0; padding: 0; width: 100% !important; line-height: 100% !important;\n}\nimg {\noutline: none; text-decoration: none; -ms-interpolation-mode: bicubic; width: auto; max-width: 100%; float: left; clear: both; display: block;\n}\nbody {\ncolor: #222222; font-family: \"Helvetica Neue\", \"Helvetica\", \"Arial\", sans-serif; font-weight: normal; padding: 0; margin: 0; text-align: left; line-height: 1.3;\n}\nbody {\nfont-size: 13px; line-height: 19px;\n}\na:hover {\ncolor: #2795b6;\n}\na:active {\ncolor: #2795b6;\n}\na:visited {\ncolor: #2ba6cb;\n}\nh1 a:active {\ncolor: #2ba6cb !important;\n}\nh2 a:active {\ncolor: #2ba6cb !important;\n}\nh3 a:active {\ncolor: #2ba6cb !important;\n}\nh4 a:active {\ncolor: #2ba6cb !important;\n}\nh5 a:active {\ncolor: #2ba6cb !important;\n}\nh6 a:active {\ncolor: #2ba6cb !important;\n}\nh1 a:visited {\ncolor: #2ba6cb !important;\n}\nh2 a:visited {\ncolor: #2ba6cb !important;\n}\nh3 a:visited {\ncolor: #2ba6cb !important;\n}\nh4 a:visited {\ncolor: #2ba6cb !important;\n}\nh5 a:visited {\ncolor: #2ba6cb !important;\n}\nh6 a:visited {\ncolor: #2ba6cb !important;\n}\ntable.secondary:hover td {\nbackground: #d0d0d0 !important; color: #555;\n}\ntable.secondary:hover td a {\ncolor: #555 !important;\n}\ntable.secondary td a:visited {\ncolor: #555 !important;\n}\ntable.secondary:active td a {\ncolor: #555 !important;\n}\ntable.success:hover td {\nbackground: #457a1a !important;\n}\ntable.alert:hover td {\nbackground: #970b0e !important;\n}\ntable.button:hover td {\nbackground: #2795b6;\n}\ntable.button:visited td {\nbackground: #2795b6;\n}\ntable.button:active td {\nbackground: #2795b6;\n}\ntable.button:hover td a {\ncolor: #fff;\n}\ntable.button:visited td a {\ncolor: #fff;\n}\ntable.button:active td a {\ncolor: #fff;\n}\ntable.button:hover td {\nbackground: #2795b6;\n}\ntable.tiny-button:hover td {\nbackground: #2795b6;\n}\ntable.small-button:hover td {\nbackground: #2795b6;\n}\ntable.medium-button:hover td {\nbackground: #2795b6;\n}\ntable.large-button:hover td {\nbackground: #2795b6;\n}\ntable.button:hover td a {\ncolor: #ffffff;\n}\ntable.button:active td a {\ncolor: #ffffff;\n}\ntable.button td a:visited {\ncolor: #ffffff;\n}\ntable.btn-blue td:hover {\nbackground: #1a68b6; color: #fff !important;\n}\ntable.btn-blue td a:hover {\nbackground: #1a68b6; color: #fff !important;\n}\ntable.btn-blue td a:active {\nbackground: #1a68b6; color: #fff !important; box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\ntable.button:hover td {\nbackground: #2795b6;\n}\ntable.button:visited td {\nbackground: #2795b6;\n}\ntable.button:active td {\nbackground: #2795b6;\n}\ntable.button:hover td a {\ncolor: #fff;\n}\ntable.button:visited td a {\ncolor: #fff;\n}\ntable.button:active td a {\ncolor: #fff;\n}\ntable.button:hover td {\nbackground: #2795b6;\n}\ntable.tiny-button:hover td {\nbackground: #2795b6;\n}\ntable.small-button:hover td {\nbackground: #2795b6;\n}\ntable.medium-button:hover td {\nbackground: #2795b6;\n}\ntable.large-button:hover td {\nbackground: #2795b6;\n}\ntable.button:hover td a {\ncolor: #ffffff;\n}\ntable.button:active td a {\ncolor: #ffffff;\n}\ntable.button td a:visited {\ncolor: #ffffff;\n}\ntable.btn-blue td:hover {\nbackground: #1a68b6; color: #fff !important;\n}\ntable.btn-blue td a:hover {\nbackground: #1a68b6; color: #fff !important;\n}\ntable.btn-blue td a:active {\nbackground: #1a68b6; color: #fff !important; box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n.table404__link:hover {\ntext-decoration: none;\n}\nbody {\nbackground: #f6f6f6;\n}\n@media only screen and (max-width: 600px) {\n  table[class=\"body\"] .img {\n    width: auto !important; height: auto !important;\n  }\n  table[class=\"body\"] center {\n    min-width: 0 !important;\n  }\n  table[class=\"body\"] .container {\n    width: 95% !important;\n  }\n  table[class=\"body\"] .row {\n    width: 100% !important; display: block !important;\n  }\n  table[class=\"body\"] .wrapper {\n    display: block !important; padding-right: 0 !important;\n  }\n  table[class=\"body\"] .columns {\n    table-layout: fixed !important; float: none !important; width: 100% !important; padding-right: 0px !important; padding-left: 0px !important; display: block !important;\n  }\n  table[class=\"body\"] .column {\n    table-layout: fixed !important; float: none !important; width: 100% !important; padding-right: 0px !important; padding-left: 0px !important; display: block !important;\n  }\n  table[class=\"body\"] .wrapper.first .columns {\n    display: table !important;\n  }\n  table[class=\"body\"] .wrapper.first .column {\n    display: table !important;\n  }\n  table.body table.columns > tr > td {\n    width: 100% !important;\n  }\n  table.body table.columns > tbody > tr > td {\n    width: 100% !important;\n  }\n  table.body table.column > tr > td {\n    width: 100% !important;\n  }\n  table.body table.column > tbody > tr > td {\n    width: 100% !important;\n  }\n  table[class=\"body\"] .columns td.one {\n    width: 8.333333% !important;\n  }\n  table[class=\"body\"] .column td.one {\n    width: 8.333333% !important;\n  }\n  table[class=\"body\"] .columns td.two {\n    width: 16.666666% !important;\n  }\n  table[class=\"body\"] .column td.two {\n    width: 16.666666% !important;\n  }\n  table[class=\"body\"] .columns td.three {\n    width: 25% !important;\n  }\n  table[class=\"body\"] .column td.three {\n    width: 25% !important;\n  }\n  table[class=\"body\"] .columns td.four {\n    width: 33.333333% !important;\n  }\n  table[class=\"body\"] .column td.four {\n    width: 33.333333% !important;\n  }\n  table[class=\"body\"] .columns td.five {\n    width: 41.666666% !important;\n  }\n  table[class=\"body\"] .column td.five {\n    width: 41.666666% !important;\n  }\n  table[class=\"body\"] .columns td.six {\n    width: 50% !important;\n  }\n  table[class=\"body\"] .column td.six {\n    width: 50% !important;\n  }\n  table[class=\"body\"] .columns td.seven {\n    width: 58.333333% !important;\n  }\n  table[class=\"body\"] .column td.seven {\n    width: 58.333333% !important;\n  }\n  table[class=\"body\"] .columns td.eight {\n    width: 66.666666% !important;\n  }\n  table[class=\"body\"] .column td.eight {\n    width: 66.666666% !important;\n  }\n  table[class=\"body\"] .columns td.nine {\n    width: 75% !important;\n  }\n  table[class=\"body\"] .column td.nine {\n    width: 75% !important;\n  }\n  table[class=\"body\"] .columns td.ten {\n    width: 83.333333% !important;\n  }\n  table[class=\"body\"] .column td.ten {\n    width: 83.333333% !important;\n  }\n  table[class=\"body\"] .columns td.eleven {\n    width: 91.666666% !important;\n  }\n  table[class=\"body\"] .column td.eleven {\n    width: 91.666666% !important;\n  }\n  table[class=\"body\"] .columns td.twelve {\n    width: 100% !important;\n  }\n  table[class=\"body\"] .column td.twelve {\n    width: 100% !important;\n  }\n  table[class=\"body\"] td.offset-by-one {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-two {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-three {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-four {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-five {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-six {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-seven {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-eight {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-nine {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-ten {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-eleven {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] table.columns td.expander {\n    width: 1px !important;\n  }\n  table[class=\"body\"] .right-text-pad {\n    padding-left: 10px !important;\n  }\n  table[class=\"body\"] .text-pad-right {\n    padding-left: 10px !important;\n  }\n  table[class=\"body\"] .left-text-pad {\n    padding-right: 10px !important;\n  }\n  table[class=\"body\"] .text-pad-left {\n    padding-right: 10px !important;\n  }\n  table[class=\"body\"] .hide-for-small {\n    display: none !important;\n  }\n  table[class=\"body\"] .show-for-desktop {\n    display: none !important;\n  }\n  table[class=\"body\"] .show-for-small {\n    display: inherit !important;\n  }\n  table[class=\"body\"] .hide-for-desktop {\n    display: inherit !important;\n  }\n  table[class=\"body\"] table.columns td.component-stats-th {\n    width: auto !important;\n  }\n  table[class=\"body\"] table.columns td.head-stats-td {\n    width: 20% !important;\n  }\n  table[class=\"body\"] table.columns td.component-list-sub-column {\n    width: 14% !important;\n  }\n  table[class=\"body\"] table.columns td.component-list-sub-column--name {\n    width: 34% !important;\n  }\n  table[class=\"body\"] table.columns .table_2_cols td.table_1_cols__td {\n    width: 100% !important;\n  }\n  table[class=\"body\"] table.columns .table_2_cols td.table_2_cols__td {\n    width: 50% !important;\n  }\n  table[class=\"body\"] table.columns .table_2_cols td.component-stats__store-list__store {\n    width: 50% !important;\n  }\n  table[class=\"body\"] .container.noresponsive {\n    width: 580px !important;\n  }\n  table[class=\"body\"] .avatar .img {\n    width: 40px !important; height: 40px !important;\n  }\n  .application-download__icons td {\n    display: block !important;\n  }\n  table[class=\"body\"] center {\n    min-width: 0 !important;\n  }\n  table[class=\"body\"] .container {\n    width: 95% !important;\n  }\n  table[class=\"body\"] .row {\n    width: 100% !important; display: block !important;\n  }\n  .content-container {\n    padding: 10px 0 !important;\n  }\n  table[class=\"body\"] .wrapper {\n    display: block !important; padding-right: 0 !important;\n  }\n  table[class=\"body\"] .columns {\n    table-layout: fixed; float: none; width: 100%; padding-right: 0px; padding-left: 0px; display: block;\n  }\n  table[class=\"body\"] .column {\n    table-layout: fixed; float: none; width: 100%; padding-right: 0px; padding-left: 0px; display: block;\n  }\n  table[class=\"body\"] .wrapper.first .columns {\n    display: table !important;\n  }\n  table[class=\"body\"] .wrapper.first .column {\n    display: table !important;\n  }\n  table.body table.columns > tr > td {\n    width: 100% !important;\n  }\n  table.body table.columns > tbody > tr > td {\n    width: 100% !important;\n  }\n  table.body table.column > tr > td {\n    width: 100% !important;\n  }\n  table.body table.column > tbody > tr > td {\n    width: 100% !important;\n  }\n  table[class=\"body\"] .columns td.one {\n    width: 8.333333% !important;\n  }\n  table[class=\"body\"] .column td.one {\n    width: 8.333333% !important;\n  }\n  table[class=\"body\"] .columns td.two {\n    width: 16.666666% !important;\n  }\n  table[class=\"body\"] .column td.two {\n    width: 16.666666% !important;\n  }\n  table[class=\"body\"] .columns td.three {\n    width: 25% !important;\n  }\n  table[class=\"body\"] .column td.three {\n    width: 25% !important;\n  }\n  table[class=\"body\"] .columns td.four {\n    width: 33.333333% !important;\n  }\n  table[class=\"body\"] .column td.four {\n    width: 33.333333% !important;\n  }\n  table[class=\"body\"] .columns td.five {\n    width: 41.666666% !important;\n  }\n  table[class=\"body\"] .column td.five {\n    width: 41.666666% !important;\n  }\n  table[class=\"body\"] .columns td.six {\n    width: 50% !important;\n  }\n  table[class=\"body\"] .column td.six {\n    width: 50% !important;\n  }\n  table[class=\"body\"] .columns td.seven {\n    width: 58.333333% !important;\n  }\n  table[class=\"body\"] .column td.seven {\n    width: 58.333333% !important;\n  }\n  table[class=\"body\"] .columns td.eight {\n    width: 66.666666% !important;\n  }\n  table[class=\"body\"] .column td.eight {\n    width: 66.666666% !important;\n  }\n  table[class=\"body\"] .columns td.nine {\n    width: 75% !important;\n  }\n  table[class=\"body\"] .column td.nine {\n    width: 75% !important;\n  }\n  table[class=\"body\"] .columns td.ten {\n    width: 83.333333% !important;\n  }\n  table[class=\"body\"] .column td.ten {\n    width: 83.333333% !important;\n  }\n  table[class=\"body\"] .columns td.eleven {\n    width: 91.666666% !important;\n  }\n  table[class=\"body\"] .column td.eleven {\n    width: 91.666666% !important;\n  }\n  table[class=\"body\"] .columns td.twelve {\n    width: 100% !important;\n  }\n  table[class=\"body\"] .column td.twelve {\n    width: 100% !important;\n  }\n  table[class=\"body\"] td.offset-by-one {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-two {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-three {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-four {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-five {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-six {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-seven {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-eight {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-nine {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-ten {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] td.offset-by-eleven {\n    padding-left: 0 !important;\n  }\n  table[class=\"body\"] table.columns td.expander {\n    width: 1px !important;\n  }\n  table[class=\"body\"] .right-text-pad {\n    padding-left: 10px !important;\n  }\n  table[class=\"body\"] .text-pad-right {\n    padding-left: 10px !important;\n  }\n  table[class=\"body\"] .left-text-pad {\n    padding-right: 10px !important;\n  }\n  table[class=\"body\"] .text-pad-left {\n    padding-right: 10px !important;\n  }\n  table[class=\"body\"] .hide-for-small {\n    display: none !important;\n  }\n  table[class=\"body\"] .show-for-desktop {\n    display: none !important;\n  }\n  table[class=\"body\"] .show-for-small {\n    display: inherit !important;\n  }\n  table[class=\"body\"] .hide-for-desktop {\n    display: inherit !important;\n  }\n  .content-container {\n    padding: 20px 0px 10px 0px;\n  }\n  video {\n    width: 100% !important;\n  }\n  video img {\n    width: 100% !important; height: auto !important;\n  }\n}\n@media only screen and (max-width: 579px) {\n  .avatar .img {\n    padding: 0px 10px 0px 0 !important;\n  }\n  .product-row-comment center {\n    min-width: 0 !important;\n  }\n}\n</style>\n</head>\n<body style=\"width: 100% !important; background: #f6f6f6; margin: 0; padding: 0; min-width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; text-align: left; line-height: 19px; font-size: 13px;\">\n  <table class=\"body\" style=\"border-spacing: 0; background: #f6f6f6; margin: 0; padding: 0; border-collapse: collapse; vertical-align: top; text-align: left; height: 100%; width: 100%; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\">\n    <tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n      <td class=\"center main-td\" align=\"center\" valign=\"top\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px; width: 100%;\">\n        <center style=\"width: 100%; min-width: 580px;\">\n        \n          \n<span class=\"hidden\" style=\"width: 0px; height: 0px; display: none; visibility: hidden; font-size: 0px; line-height: 0px; text-indent: -999999px;\">\n  \n  Congratulations on creating your new Salesfloor account. - Your profile is live, and your storefront is available to customers <a href=\"https://tests.dev.salesfloor.net/en_US/random_user_login?event_source=email\" style=\"color: #2ba6cb; text-decoration: none;\">here</a> A few things to remember:\n\n</span>\n\t\n    \n\t\t<table class=\"row header\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0px; width: 100%; position: relative;\">\n    <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n      <td class=\"center\" align=\"center\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px; width: 100%;\" valign=\"top\">\n        <center style=\"width: 100%; min-width: 580px;\">\n\n          <table class=\"container\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: inherit; padding: 0; margin: 0 auto; width: 580px;\">\n            <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n              <td class=\"wrapper last\" style=\"-webkit-hyphens: manual; margin: 0; padding: 10px 0px 0px; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; position: relative; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\">\n\n                <table class=\"twelve columns\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0; margin: 0 auto; width: 580px;\">\n                  <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n                    <td class=\"header-logo-container\" align=\"center\" style=\"-webkit-hyphens: manual; margin: 0; padding: 10px 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: center; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" valign=\"top\">\n                      \n                        <img class=\"header-logo-retina noreset\" src=\"https://cdn.salesfloor.net/salesfloor-emails/retailers/tests/logo_small.png\" style=\"outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; width: auto; max-width: 100%; float: none; clear: both; display: inline;\" align=\"none\" />\n                      \n                    </td>\n                    <td class=\"expander\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; visibility: hidden; width: 0px; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\"></td>\n                  </tr>\n                </tbody></table>\n                <table class=\"twelve columns\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0; margin: 0 auto; width: 580px;\">\n                  <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n                    <td class=\"header-back-office-container\" style=\"text-align: right; margin: 0; padding: 0 0 10px; vertical-align: middle; -webkit-hyphens: manual; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"right\" valign=\"middle\">\n                      \n                        <a universal=\"true\" class=\"btn-back-office\" href=\"https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&%24deeplink_path=%2F%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg\" style=\"color: #2ba6cb; text-decoration: none;\">Go to my backoffice</a>\n                      \n                    </td>\n                    <td class=\"expander\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; visibility: hidden; width: 0px; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\"></td>\n                  </tr>\n                </tbody></table>\n\n              </td>\n            </tr>\n          </tbody></table>\n\n        </center>\n      </td>\n    </tr>\n  </tbody>\n</table>\n\n\t\n\n\t<table class=\"row \" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0px; width: 100%; position: relative;\">\n  \t<tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n    \t<td class=\"center\" align=\"center\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px; width: 100%;\" valign=\"top\">\n      \t<center style=\"width: 100%; min-width: 580px;\">\n\n\n        <table class=\"container    \" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: inherit; padding: 0; margin: 0 auto; width: 580px;\">\n          <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n            <td class=\"\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\">\n          \n            \n\t\n<table class=\"row alert-container alert-container-success\" style=\"border-spacing: 0; background: #20a536; padding: 0px; border-collapse: collapse; vertical-align: top; text-align: left; display: block; width: 100%; position: relative; border-radius: 3px 3px 0 0; border-top-style: solid; border-top-width: 1px; border-top-color: #e9e9e9; border-left-width: 1px; border-left-color: #e9e9e9; border-left-style: solid; border-right-width: 1px; border-right-color: #e9e9e9; border-right-style: solid;\">\n  <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n    <td class=\" last\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0 0px 0 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\">\n        \n        <table class=\"twelve columns displayTable\" style=\"border-spacing: 0; margin: 0 auto; padding: 0; border-collapse: collapse; vertical-align: top; text-align: left; width: 580px; display: table;\">\n          <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n            <td class=\"alert-content\" style=\"-webkit-hyphens: manual; margin: 0; padding: 15px; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: center; color: #fff; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: 500; line-height: 19px; font-size: 15px;\" align=\"center\" valign=\"top\">\n                Congratulations on creating your new Salesfloor account.\n            </td>\n            <td class=\"expander\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; visibility: hidden; width: 0px; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\"></td>\n          </tr>\n        </tbody></table>\n    </td>\n  </tr>\n</table>\n\n  \n\n            <table class=\"row content-container no-radius\" style=\"border-spacing: 0; background: #fff; padding: 0px; border-color: #e9e9e9; border-style: solid; border-width: 0px 1px 1px; border-collapse: collapse; vertical-align: top; text-align: left; display: block; width: 100%; position: relative; border-radius: 0px;\">\n              <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n                <td class=\"wrapper last  \" style=\"-webkit-hyphens: manual; margin: 0; padding: 10px 0px 0px; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; position: relative; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\">\n                    \n                  <table class=\"twelve columns\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0; margin: 0 auto; width: 580px;\">\n                    <tbody><tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n                      <td class=\"content-wrap\" style=\"-webkit-hyphens: manual; margin: 0; padding: 20px; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\">\n\n                      \t\n\n  \t<h2 class=\"title-hello\" style=\"color: #222222; margin: 0 0 15px; padding: 0; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; text-align: left; line-height: 1.3; word-break: normal; font-size: 23px;\" align=\"left\">Hi random_first_name,</h2>\n\n\t\n\t<h4 style=\"color: #222222; margin: 15px 0; padding: 0; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; text-align: left; line-height: 1.3; word-break: normal; font-size: 18px;\" align=\"left\">Your profile is live, and your storefront is available to customers <a href=\"https://tests.dev.salesfloor.net/en_US/random_user_login?event_source=email\" style=\"color: #2ba6cb; text-decoration: none;\">here</a></h4>\n  \n\n\t<p style=\"color: #222222; margin: 0 0 10px; padding: 0; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; text-align: left; line-height: 19px; font-size: 13px;\" align=\"left\">A few things to remember:</p>\n\t<ul style=\"padding-left: 20px; margin-left: 0;\">\n\t\t<li>Your username is random_user_login.</li>\n\t\t<li>Remember the password you chose, write it down!</li>\n\t\t\n\t\t<li>You can start responding to customer requests immediately and access your account by logging in to your <a href=\"https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT?%24desktop_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fbackoffice&%24deeplink_path=%2F%3FretailerId%3Dtests-dev&%24ios_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%24android_url=https%3A%2F%2Ftests.dev.salesfloor.net%2Fmobileapp&%243p=e_sg\" style=\"color: #2ba6cb; text-decoration: none;\">Backoffice.</a></li>\n\t\t\n\t</ul>\n\n\t<br />\n\n\t\n\t<table class=\"applications-download\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; width: 100%; padding: 0;\">\n\t\t<tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n\t\t\t<td style=\"-webkit-hyphens: manual; margin: 0; padding: 5px 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: center; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 15px;\" align=\"center\" valign=\"top\">\n\t\t\t\tDon't forget to download the free mobile App now!\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n\t\t\t<td style=\"-webkit-hyphens: manual; margin: 0; padding: 5px 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: center; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 15px;\" align=\"center\" valign=\"top\">\n\t\t\t\t<table class=\"application-download__icons\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; width: 100%; padding: 0;\">\n\t\t\t\t\t<tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n\t\t\t\t\t\t<td style=\"-webkit-hyphens: manual; margin: 0; padding: 5px 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: center; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 15px;\" align=\"center\" valign=\"top\">\n\t\t\t\t\t\t\t<a href=\"https://apps.apple.com/ca/app/salesfloor/id957494193\" style=\"color: #2ba6cb; text-decoration: none;\"><img src=\"https://cdn.salesfloor.net/salesfloor-emails/app-store-en.png\" alt=\"Download on the App Store\" class=\"noreset application-download__icons-img\" width=\"150\" style=\"outline: none; border: none; text-decoration: none; -ms-interpolation-mode: bicubic; width: 150px; max-width: 150px; float: none; clear: both; display: inline;\" align=\"none\" /></a>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td style=\"-webkit-hyphens: manual; margin: 0; padding: 5px 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: center; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 15px;\" align=\"center\" valign=\"top\">\n\t\t\t\t\t\t\t<a href=\"https://play.google.com/store/apps/details?id=com.salesfloor.playstore\" style=\"color: #2ba6cb; text-decoration: none;\"><img src=\"https://cdn.salesfloor.net/salesfloor-emails/google-play-en.png\" alt=\"\" class=\"noreset application-download__icons-img\" width=\"150\" style=\"outline: none; border: none; text-decoration: none; -ms-interpolation-mode: bicubic; width: 150px; max-width: 150px; float: none; clear: both; display: inline;\" align=\"none\" /></a>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t</tr>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t</table>\n\t\n\n\t<br />\n\n\t<p style=\"color: #222222; margin: 0 0 10px; padding: 0; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; text-align: left; line-height: 19px; font-size: 13px;\" align=\"left\">If you have any questions or need help, please contact us for support at <a href=\"mailto:<EMAIL>\" style=\"color: #2ba6cb; text-decoration: none;\"><EMAIL></a></p>\n\n\n\n                        \n                      </td>\n                      <td class=\"expander\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; visibility: hidden; width: 0px; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\"></td>\n                    </tr>\n                  </tbody></table>\n                </td>\n              </tr>\n            </table>\n\n            \n              <table class=\"row footer\" style=\"border-spacing: 0; padding: 0px; border-collapse: collapse; vertical-align: top; text-align: left; display: block; width: 100%; position: relative;\">\n  <tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n    <td class=\"wrapper last\" style=\"-webkit-hyphens: manual; margin: 0; padding: 10px 0px 0px; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; position: relative; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\">\n      <table class=\"twelve columns\" style=\"border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0; margin: 0 auto; width: 580px;\">\n        <tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\">\n          <td align=\"left\" class=\"footer-content\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #999; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 12px;\" valign=\"top\">\n            You are receiving Salesfloor notifications, sent on behalf of:<br/>\n            @@retailerName@@, 651 Notre Dame Ouest, Suite 350, Montreal, Quebec H3C 1H9<br />\n            This email was intended for random_first_name.\n              <div style=\"display: none\">\n                  [GLOBAL_UNSUBSCRIBE]\n              </div>\n            <table width=\"150\" style=\"width: 150px; border-spacing: 0; border-collapse: collapse; vertical-align: top; text-align: left; padding: 0;\">\n              <tr style=\"vertical-align: top; text-align: left; padding: 0;\" align=\"left\"><td class=\"footer-logo-container\" style=\"-webkit-hyphens: manual; margin: 0; padding: 10px 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\"><img class=\"footer-logo\" width=\"150\" src=\"https://cdn.salesfloor.net/salesfloor-assets/logo_internal_email.png\" style=\"outline: none; text-decoration: none; -ms-interpolation-mode: bicubic; width: 150px; max-width: 150px; float: left; clear: both; display: block; height: auto;\" align=\"left\" /><td width=\"150\" style=\"width: 150px; margin: 0; padding: 0; -webkit-hyphens: manual; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" class=\"\" align=\"left\" valign=\"top\"></td></tr>\n            </table>\n          </td>\n          <td class=\"expander\" style=\"-webkit-hyphens: manual; margin: 0; padding: 0; -moz-hyphens: manual; hyphens: manual; border-collapse: collapse !important; vertical-align: top; text-align: left; visibility: hidden; width: 0px; color: #222222; font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif; font-weight: normal; line-height: 19px; font-size: 13px;\" align=\"left\" valign=\"top\"></td>\n        </tr>\n      </table>\n    </td>\n  </tr>\n</table>\n\n               \n\n            </td>\n          </tr>\n        </table>\n      </center>\n    </td>\n  </tr>\n</table>   \n\n\n\n        </center>\n      </td>\n    </tr>\n  </table>\n</body>\n</html>\n"}], "tracking_settings": {"open_tracking": {"enable": false}, "click_tracking": {"enable": false, "enable_text": false}}, "mail_settings": {"bypass_list_management": {"enable": false}}, "categories": ["Transactional"]}, "useMasterKey": false, "onBehalfOf": null}, "output": {"result": [], "info": {"url": "https://api.sendgrid.com/v3//mail/send", "content_type": null, "http_code": 202, "header_size": 530, "request_size": 239, "filetime": -1, "ssl_verify_result": 0, "redirect_count": 0, "total_time": 0.263664, "namelookup_time": 2.2e-05, "connect_time": 0.078891, "pretransfer_time": 0.144272, "size_upload": 39716, "size_download": 0, "speed_download": 0, "speed_upload": 151011, "download_content_length": 0, "upload_content_length": 39716, "starttransfer_time": 0.176682, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 443, "local_ip": "**********", "local_port": 54810, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "HTTPS"}, "error": "", "curlError": "", "header": {"server": ["nginx"], "date": ["Thu, 17 Aug 2023 20:43:42 GMT"], "content-length": ["0"], "connection": ["keep-alive"], "x-message-id": ["PqVYuphPTsCtI5LK6ytpEQ"], "access-control-allow-origin": ["https://sendgrid.api-docs.io"], "access-control-allow-methods": ["POST"], "access-control-allow-headers": ["Authorization, Content-Type, On-behalf-of, x-sg-elas-acl"], "access-control-max-age": ["600"], "x-no-cors-reason": ["https://sendgrid.com/docs/Classroom/Basics/API/cors.html"], "strict-transport-security": ["max-age=600; includeSubDomains"]}}}}