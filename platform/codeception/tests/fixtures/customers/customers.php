<?php

$params = [];

$params['create-simple'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL,
    'geo' => 'geo',
    'comment' => 'comment',
    'subcribtion_flag' => 0,
    'sms_marketing_subscription_flag' => 0,
];

$params['create-simple-by-phone'] = [
    'user_id' => 1,
    'phone' => '+15145556666',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>',
    'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL
];

$params['create-no-email-or-phone'] = [
    'user_id' => 1,
    'first_name' => 'John',
    'last_name' => 'Smith',
    'origin'    => \Salesfloor\Models\Customer::ORIGIN_REP_MANUAL
];

$params['update-simple'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John 2',
    'last_name' => 'Smith 2'
];

$params['create-complex'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677857',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
        [
            'email' => '<EMAIL>',
            'label' => 'Work',
            'position' => '1'
        ],
    ],
    'additionalPhones' => [
        [
            'phone' => '+15147466656',
            'label' => 'Home',
            'position' => '0'
        ],
        [
            'phone' => '+15147466657',
            'label' => 'Work',
            'position' => '1'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1',
    'origin' => 'customers2contacts',
    'contact_preference' => 'email',
];

$params['update-complex'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John 5',
    'last_name' => 'Smith 6',
    'phone' => '+15147466658',
    'label_email' => 'Test',
    'label_phone' => 'Work',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home 2',
            'position' => '0'
        ],
        [
            'email' => '<EMAIL>',
            'label' => 'Work 2',
            'position' => '1'
        ],
    ],
    'additionalPhones' => [
        [
            'phone' => '+15147466658',
            'label' => 'Home 3',
            'position' => '0'
        ],
        [
            'phone' => '+15147466659',
            'label' => 'Work 3',
            'position' => '1'
        ],
    ],
    'note' => 'This is a note modified',
    'subcribtion_flag' => '0'
];

$params['search-customer1'] = [
    'user_id' => '1',
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'name' => 'John Smith',
    'phone' => '+15147466660'
];

$params['search-customer2'] = [
    'user_id' => '1',
    'email' => '<EMAIL>',
    'first_name' => 'Jean',
    'last_name' => 'Dupont',
    'name' => 'Jean Dupont',
    'phone' => '+15147466661',
    'subcribtion_flag' => '1'
];

$params['search-customer3'] = [
    'user_id' => '1',
    'email' => '<EMAIL>',
    'first_name' => 'Marie',
    'last_name' => 'Tremblay',
    'name' => 'Marie Tremblay',
    'phone' => '+15147466662'
];

$params['search-customer4'] = [
    'user_id' => '1',
    'email' => '<EMAIL>',
    'first_name' => 'Bruce',
    'last_name' => 'Lee',
    'name' => 'Bruce Lee',
    'phone' => '+15147466663'
];

$params['search-customer5'] = [
    'user_id' => '2',
    'email' => '<EMAIL>',
    'first_name' => 'test',
    'last_name' => 'test',
    'name' => 'test test',
    'phone' => '+15147466889'
];

$params['search-customer6'] = [
    'user_id' => '2',
    'email' => '<EMAIL>',
    'first_name' => 'test',
    'last_name' => 'test',
    'name' => 'test test',
    'phone' => '+15147677886'
];
$params['search-customer7'] = [
    'user_id' => '2',
    'email' => '<EMAIL>',
    'first_name' => 'test',
    'last_name' => 'O\'Reilly',
    'name' => 'test test',
    'phone' => '+15147677892'
];
$params['search-customer8'] = [
    'user_id' => '2',
    'email' => '<EMAIL>',
    'first_name' => 'test',
    'last_name' => 'O`Reilly',
    'name' => 'test test',
    'phone' => '+15147677906'
];

$params['search-customer-meta1'] = [
    'type' => 'email',
    'value' => '<EMAIL>',
    'position' => '1',
    'label' => 'Work'
];

$params['search-customer-meta2'] = [
    'type' => 'phone',
    'value' => '+15147677916',
    'position' => '1',
    'label' => 'Personal'
];

$params['customer-matching-id'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'retailer_customer_id' => '12345'
];

$params['customer-matching-default-emails'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'retailer_customer_id' => '1234'
];

$params['customer-matching-alternate-emails'] = [
    'user_id' => 3,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'retailer_customer_id' => '111111'
];

$params['customer-matching-alternate-emails-meta'] = [
    'type' => 'email',
    'value' => '<EMAIL>',
    'position' => '1',
    'label' => 'Work'
];

// Related to the new duplicate section of tests

$params['create-complex-duplicate-alternate-to-alternate-emails'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677923',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
        [
            'email' => '<EMAIL>',
            'label' => 'Work',
            'position' => '1'
        ],
    ],
    'additionalPhones' => [],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-complex-duplicate-default-to-alternate-emails'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677956',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-complex-duplicate-check-reorder'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677957',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '1'
        ],
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '2'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-default-to-alternate'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677515',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-alternate-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677521',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-alternate-to-alternate-2'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677542',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-different-rep-default-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677563',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-different-rep-default-to-alternate-2'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677578',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-different-rep-alternate-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677588',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-duplicate-different-rep-alternate-to-alternate-2'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677599',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-lowercase-email'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677612',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-duplicate-case-insensitve-email-default-to-alternate-1'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677644',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-duplicate-case-insensitve-email-default-to-alternate-2'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677696',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-duplicate-case-insensitve-email-alternate-to-alternate-1'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677701',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-duplicate-case-insensitve-email-alternate-to-alternate-2'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677712',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-lowercase-emails'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John-updated',
    'last_name' => 'Smith',
    'phone' => '+15147677734',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-default-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677746',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-default-to-alternate-2'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John-updated',
    'last_name' => 'Smith',
    'phone' => '+15147677757',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'id' => 1,  // Force the id
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-alternate-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677768',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-alternate-to-alternate-2'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John-updated',
    'last_name' => 'Smith',
    'phone' => '+15147677779',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'id' => 1,  // Force the id
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-duplicate-default-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677780',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-duplicate-default-to-alternate-2'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John-updated',
    'last_name' => 'Smith',
    'phone' => '+15147677791',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'id' => 1,  // Force the id
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-duplicate-alternate-to-alternate-1'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677802',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-duplicate-alternate-to-alternate-2'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John-updated',
    'last_name' => 'Smith',
    'phone' => '+15147677813',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'id' => 1,  // Force the id
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-invalid-default-email'] = [
    'user_id' => 2,
    'email' => 'abcd',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677824',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-customer-invalid-alternate-email'] = [
    'user_id' => 2,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677835',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => 'abcd',
            'label' => 'Home',
            'position' => '0'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-create-duplicate-alternate-email'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677857',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [
        [
            'email' => '<EMAIL>',
            'label' => 'Home',
            'position' => '0'
        ]
    ],
    'additionalPhones' => [],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['update-customer-create-duplicate-alternate-phone'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'phone' => '+15147677857',
    'label_email' => 'Work',
    'label_phone' => 'Home',
    'additionalEmails' => [],
    'additionalPhones' => [
        [
            'phone' => '+15147677878',
            'label' => 'Home',
            'position' => '1'
        ],
    ],
    'note' => 'This is a note',
    'subcribtion_flag' => '1'
];

$params['create-subscription-flag-off'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'subcribtion_flag' => 0,
];

$params['create-subscription-flag-on'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Smith',
    'subcribtion_flag' => 1,
];

$params['update-subscription-flag-off'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John 2',
    'last_name' => 'Smith 2',
    'subcribtion_flag' => 0,
];

$params['update-subscription-flag-on'] = [
    'user_id' => 1,
    'email' => '<EMAIL>',
    'first_name' => 'John 2',
    'last_name' => 'Smith 2',
    'subcribtion_flag' => 1,
];

$params['customer_address'] = [
    [
      'id' => '1',
      'customer_id' => '1',
      'address_line_1' => 'Calle Tierra #28',
      'address_line_2' => null,
      'postal_code' => '077500',
      'state' => 'Quintana Roo',
      'city' => 'Cancun',
      'country' => 'Mexico',
      'label' => 'Home',
      'is_default' => '1',
      'created_at' => '2022-03-18 18:12:26',
      'updated_at' => '2022-03-18 18:12:26',
    ],
    [
      'id' => '2',
      'customer_id' => '1',
      'address_line_1' => 'Calle Tierra #29',
      'address_line_2' => 'Suite #3',
      'postal_code' => '077500',
      'state' => 'Quintana Roo',
      'city' => 'Cancun',
      'country' => 'Mexico',
      'label' => 'Office',
      'is_default' => '0',
      'created_at' => '2022-03-18 18:12:29',
      'updated_at' => null,
    ],
    [
      'id' => '3',
      'customer_id' => '1',
      'address_line_1' => 'Calle Tierra #30',
      'address_line_2' => 'Appartment #45',
      'postal_code' => '077500',
      'state' => 'Quintana Roo',
      'city' => 'Cancun',
      'country' => 'Mexico',
      'label' => 'Delivery',
      'is_default' => '0',
      'created_at' => '2022-03-18 18:12:30',
      'updated_at' => null,
    ],
];

return $params;
