<?php

require_once __DIR__ . '/../bootstrap_bin.php';

/**
 * This script will fix the issues caused by https://salesfloor.atlassian.net/browse/CPD-1698
 *
 * Important: just run it one time and will be removed from repo, please view https://salesfloor.atlassian.net/browse/CPD-1698 for more details
 *
 */

/** @var \Salesfloor\Services\MySQLRepository $mysqlRepository */
$mysqlRepository = $app['repositories.mysql'];

$qb = $mysqlRepository->getQueryBuilder()
    ->select('t.id, t.fingerprint')
    ->from('sf_rep_transaction', 't')
    ->where('t.received_date > :receivedDate')
    ->andWhere('t.customer_id is null')
    ->andWhere('t.fingerprint is not null')
    ->andWhere('t.origin = :origin')
    ->setParameters([
        'receivedDate' => '2024-09-09 23:11:20',
        'origin'       => 'VariableSetting',
    ]);

$transactions = $qb->executeQuery()->fetchAllAssociative();

foreach ($transactions as $transaction) {
    $transactionId = $transaction['id'];
    $app['sf.event.queue']->push([
        'action' => 'SERVER_RECORD_TRANSACTION',
        'action_id' => $transactionId,
        'fingerprint' => $transaction['fingerprint']
    ]);
    $app['logger']->info(sprintf("Pushed transaction [%s] to event queue", $transactionId));

    $app['service.data_processing.transaction_customer']->push((int)$transactionId);
    $app['logger']->info(sprintf("Pushed transaction [%s] to transaction-customer queue", $transactionId));
}
