<?php
/**
 * Sets up common requirements and configurations for bin scripts.
 */

date_default_timezone_set('UTC');
error_reporting(E_ALL ^ E_STRICT);

// setup some constants required by the config loader
define('PATH_NEW_CONFIGS', __DIR__ . '/../../../configs/configs');
define('PATH_VIEWS', '');
define('PATH_PLATFORM', realpath(__DIR__ . '/../../..'));

require_once __DIR__ . '/../../../vendor/autoload.php';
$app = require_once __DIR__ . '/../src/be/app.php';

if ($app['configs']['env'] === 'dev') {
    error_reporting(E_ALL);
    $app['debug'] = true; // False by default
}

// This is required for Silex SecurityServiceProvider to finish registering properly.
// Twig requires it, and the provider isn't ready without an app boot.
// Normally, the Silex request handles this, but this isn't a silex request context.
$app->boot();

// Let the command line decide of the log level
$errorLevel = getenv('cron_error_level');
if (
    $errorLevel &&
    in_array($errorLevel, [
        'debug',
        'info',
        'notice',
        'warning',
        'error',
        'critical',
    ])
) {
    // This is used by the base importer logger
    $app['monolog.level'] = Logger::toMonologLevel($errorLevel);
    // This is used by the generic (error_log) error handler
    $app['monolog.handler']->setLevel($errorLevel);
}
