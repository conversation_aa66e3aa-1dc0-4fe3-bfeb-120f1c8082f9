<?php

/**
 * This script builds and / or sends 30-day performance emails.
 *
 * By default, it will render the emails to html files in directory
 * /tmp/kpi-emails-YYYYMMDD-$env-$retailer, upload a tarball of that directory to s3,
 * and send a few emails to a demo address if that's in the retailer config.
 *
 * You can use a few environment variables to control the behaviour of the
 * script:
 *
 * SF_KPI_SENDFROM: if set, the script will skip the build step, and instead
 *                  send all messages found in this directory to their intended
 *                  recipients.
 *
 * SF_KPI_SENDNOW: if true, the script will send the emails to their intended
 *                 recipients instead of stopping after the build step. If too
 *                 many emails contain inconsistent data (more than 10%), they
 *                 won't be sent.
 *
 * SF_KPI_NOSEND: if true, the script won't send any emails.
 *
 * SF_KPI_NOARCHIVE: if true, the script won't save a tarball of the built emails
 *                   to s3.
 *
 * SF_KPI_KEEPDIR: if true, the script won't delete the directory containing the
 *                 emails after they've been sent to their intended recipients.
 *
 * SF_KPI_DATEFROM: If set, the date at which the report period starts.
 *                  The value may be absolute or relative:
 *                  '2018-01-01' , '29 days ago', etc.
 *
 * SF_KPI_DATETO:   If set, the date at which the report period ends.
 *                  The value may be absolute or relative:
 *                  '2018-01-02', '28 days ago', etc.
 *
 * SF_KPI_DELETOLD: If set, the script will delete the existing
 *                  destination directory if it exists already.
 *
 * Typical usage
 * -------------
 *
 * This is meant to be used in two steps:
 *
 * 1. Call with default options to build the emails and send a sample for QA
 *    (this is set on a cronjob already; it runs every Monday morning):
 *
 *   $ php /srv/www/sf_platform/current/api/app/crons/SendWeeklyPerformanceEmails.php bru-stg-bru
 *
 * 2. After QA validates that emails look good, send them to their intended recipients:
 *
 *   $ SF_KPI_SENDFROM=/tmp/kpi-emails-20160714 php /srv/www/sf_platform/current/api/app/crons/SendWeeklyPerformanceEmails.php bru-stg-bru
 *
 *
 * When we've gained confidence in the built-in sanity checks, we can skip the
 * human QA step and build & send in one run:
 *
 *   $ SF_KPI_SENDNOW=1 php /srv/www/sf_platform/current/api/app/crons/SendWeeklyPerformanceEmails.php bru-stg-bru
 */

// WESHOULD put most of this implementation inside a Service instead of this super-long file.

use Salesfloor\Services\GroupPermissionService;
use Salesfloor\Services\SalesfloorAPIRepository;
use Salesfloor\Services\OrderedList;
use Salesfloor\Services\Util;

$SF_19242_WORKAROUND = true;

// WESHOULD: this should be done in Twig such that
// we can use a localized interval format, but
// because the templates need to be nunjucks-compatible
// in the webapp we can't use http://twig.sensiolabs.org/doc/filters/date.html
// ... although maybe filters are only ever checked at runtime
// in nunjucks thus making " | date " safe?
function fmt_avg_response_time($seconds)
{
    if (!ctype_digit("$seconds")) {
        if (is_numeric($seconds)) {
            return "< 1m";
        }

        return $seconds;
    }

    $dt = new \DateTime();
    $dt->add(new \DateInterval("PT{$seconds}S"));
    $di = $dt->diff(new \DateTime());

    if ($di->d > 0) {
        return $di->format("%dd %hh %im");
    }
    if ($di->h > 0) {
        return $di->format("%hh %im");
    }

    return $di->format("%im");
}

function get_destination_email($app, $email)
{
    if ($app['configs']['kpi_email.email_override']) {
        return $app['configs']['kpi_email.email_override'];
    }

    return $email;
}

function cmp_rep_name($a, $b)
{
    $cmp = strcmp(mb_strtolower($a['last_name']), mb_strtolower($b['last_name']));
    if ($cmp == 0 && isset($a['full_name']) && isset($b['full_name'])) {
        $cmp = strcmp(mb_strtolower($a['full_name']), mb_strtolower($b['full_name']));
    }

    return $cmp;
}

function cmp_store_name($a, $b)
{
    return strcmp(mb_strtolower($a['name']), mb_strtolower($b['name']));
}

function cmp_name($a, $b)
{
    if (isset($a['full_name'])) {
        return cmp_rep_name($a, $b);
    }

    return cmp_store_name($a, $b);
}

function cmp_sales($a, $b)
{
    if ($a['sales']['v'] < $b['sales']['v']) {
        return 1;
    } elseif ($a['sales']['v'] > $b['sales']['v']) {
        return -1;
    }

    return cmp_name($a, $b);
}

function mk_cmp_number($k)
{
    return function (array $a, array $b) use ($k) {
      // larger is better
        return $b[$k] - $a[$k];
    };
}

function cmp_avg_init_resp(array $a, array $b)
{
    if ($a['avg_init_resp_num_responses'] == 0) {
        if ($b['avg_init_resp_num_responses'] == 0) {
            return 0;
        }
        return 1;
    } elseif ($b['avg_init_resp_num_responses'] == 0) {
        return -1;
    }
    $t_a = $a['avg_init_resp_sum_times'] / $a['avg_init_resp_num_responses'];
    $t_b = $b['avg_init_resp_sum_times'] / $b['avg_init_resp_num_responses'];

  // lower response time is better
    return $t_a - $t_b;
}

$kpis = [
  'KPI_TR'  => ['service_total'],
  'KPI_RAR' => ['appointment_req'],
  'KPI_EMR' => ['ask_question_req'],
  'KPI_PSR' => ['personal_shopper_req'],
  'KPI_LCR' => ['chat_request'],
  'KPI_LCA' => ['chat_answer_rate'],
  'KPI_SV'  => ['traffic_total'],
  'KPI_UV'  => ['unique_visitor'],
  'KPI_RRS' => ['com_ref'],
  'KPI_RCA' => ['user_add'],
  'KPI_CU'  => ['content_total'],
  'KPI_PRU' => ['product_update', 'deal_update'],
  'KPI_EU'  => ['event_create', 'event_update'],
  'KPI_POU' => ['content_create', 'content_update'],
  'KPI_ES'  => ['share_email_sent', 'compose_email_sent', 'request_email_sent', 'mail_sent'],
  'KPI_OR'  => ['share_email_open', 'compose_email_open', 'request_email_open', 'email_stats_open'],
  'KPI_CR'  => ['share_email_click', 'compose_email_click', 'request_email_click', 'email_stats_click'],
  'KPI_SP'  => ['social_post'],
  'KPI_C'   => ['soc_ref'],
  'KPI_AOV' => ['avg_order_value'],
  'KPI_NST' => ['n_sales_transactions'],
];

function set_kpi_params($myKpis, &$reportParams)
{
    global $app, $kpis;

    foreach ($kpis as $str => $ks) {
        $kpi = 0;
        foreach ($ks as $k) {
            if (isset($myKpis[$k])) {
                $kpi += $myKpis[$k];
            }
        }
        $reportParams[$str] = $kpi;
    }

    if (isset($myKpis['avg_init_resp'])) {
        $reportParams['KPI_IRT'] = $myKpis['avg_init_resp'];
    } else {
        $reportParams['KPI_IRT'] = '-';
    }

    // This is a special exception for KPI_OR and KPI_CR (open rate and click rate)
    // Since we don't have access to the rate directly from the report (Not added it the aggregate values) we do it here
    $reportParams['KPI_OR'] = empty($reportParams['KPI_ES']) ? 0 : ($reportParams['KPI_OR'] / $reportParams['KPI_ES']);
    $reportParams['KPI_CR'] = empty($reportParams['KPI_ES']) ? 0 : ($reportParams['KPI_CR'] / $reportParams['KPI_ES']);

    $ok = true;
    if (isset($reportParams['requests_received']) && $reportParams['KPI_TR'] != $reportParams['requests_received']['v']) {
        $ok = false;
        $app['logger']->error("total requests {$reportParams['KPI_TR']} != requests received {$reportParams['requests_received']['v']}");
    }
    if (isset($reportParams['response_time']) && $reportParams['KPI_IRT'] != $reportParams['response_time']['v'] && ($reportParams['KPI_IRT'] != '< 1m' || $reportParams['response_time']['v'] != '-')) {
        $ok = false;
        $app['logger']->error("inconsistent initial response time: {$reportParams['KPI_IRT']} != {$reportParams['response_time']['v']}");
    }
    if (isset($reportParams['unique_visitors']) && $reportParams['KPI_UV'] != $reportParams['unique_visitors']['v']) {
        $ok = false;
        $app['logger']->error("inconsistent unique count: {$reportParams['KPI_UV']} != {$reportParams['unique_visitors']['v']}");
    }
    if (isset($reportParams['chat']) && $reportParams['KPI_LCA'] != $reportParams['chat']['v']) {
        $ok = false;
        $app['logger']->error("inconsistent chat answer rate: " . $reportParams['KPI_LCA'] . " != " . $reportParams['chat']['v']);
    }

  // Temporary note to show we changed the value of sales total
    $reportParams['saks_note'] = $app['configs']['retailer.saks_note'];

    return $ok;
}

function get_message_dir($app)
{
    if ($dir = getenv('SF_KPI_SENDFROM')) {
        return $dir;
    }

    return '/tmp/kpi-emails-' . gmdate('Ymd') . '-' . $app['configs']['env'] . '-' . $app['configs']['retailer.idstr'];
}

function serialize_message($directory, $recipient, $userId, $subject, $body)
{
    // 248 length = 255 maxlength - '.html' (5) - '#' (twice, 2)
    $subjectMaxLength = 248 - strlen(urlencode($recipient)) - strlen($userId);
    $fileName = $directory . '/' . urlencode($recipient) . '#' . $userId . '#' . urlencode(mb_substr($subject, 0, $subjectMaxLength)) . '.html';
    if (file_put_contents($fileName, $body) !== strlen($body)) {
        throw new \Exception("failed to write message to '$fileName'");
    }

    return $fileName;
}

function deserialize_message($fileName)
{
    $body = file_get_contents($fileName);
    if ($body === false) {
        throw new \Exception("failed to read message from '$fileName'");
    }

    $i = strrpos($fileName, '/');
    if ($i !== false) {
        $fileName = substr($fileName, $i + 1);
    }
    $i = strpos($fileName, '.html');
    if ($i !== false) {
        $fileName = substr($fileName, 0, $i);
    }

    list ($recipient, $userId, $subject) = array_map('urldecode', explode('#', $fileName, 3));

    return [$recipient, $userId, $subject, $body];
}

function archive_messages($app)
{
    $tar_path = Util::archiveDir(realpath(get_message_dir($app)));
    $tar_leaf = pathinfo($tar_path, PATHINFO_BASENAME);
    $app['cloudstorage.upload']($tar_leaf, $tar_path, $app, [
    'ContentType' => 'application/x-xz',
    ]);
    unlink($tar_path);
}

function renameRankings(&$rankingSet)
{
    $metricSynonyms = [
    'chat-answer-rate' => 'chat',
    'unique-visitors' => 'unique_visitors',
    'response-time' => 'response_time',
    'requests-received' => 'requests_received',
    ];

    foreach ($rankingSet as $id => $rankings) {
        foreach ($rankings as $metric => $ranking) {
            if (isset($metricSynonyms[$metric])) {
                $rankingSet[$id][$metricSynonyms[$metric]] = $ranking;
            }
        }
    }
}

function setLabelParams($app, &$params)
{
    $labels = [
        '{LABEL_APPOINTMENT_REQUEST}' => $app['sf.services']->getAppointmentRequestLabel(),
        '{LABEL_EMAIL_ME_REQUEST}' => $app['sf.services']->getEmailMeRequestLabel(),
        '{LABEL_PERSONAL_SHOPPER_REQUEST}' => $app['sf.services']->getPersonalShopperRequestLabel(),
        '{LABEL_CHAT_REQUEST}' => $app['sf.services']->getChatRequestLabel(),
    ];
    foreach ($labels as $k => $v) {
        // ghetto pluralize
        // See https://salesfloor.atlassian.net/browse/SF-18092?focusedCommentId=54429&page=com.atlassian.jira.plugin.system.issuetabpanels%3Acomment-tabpanel#comment-54429
        if (mb_strtolower(mb_substr($v, -7)) == 'request') {
            $v .= 's';
        }
        $params[$k] = $v;
    }

    return $params;
}

/**
 * Returns the value of an environment variable @str
 * or @def, if the environment variable is not set.
 */
function getenvd($str, $def)
{
    $str = getenv($str);
    return $str ? $str : $def;
}

function render_emails($app)
{
    /** @var \Salesfloor\Services\Reporting\PerformanceEmail $performanceEmailService */
    $performanceEmailService = $app['reporting.performance-email'];

    // format backoffice link
    $isAppLoginPrimary = $app['configs']['retailer.backoffice_primary_login_through_app.enabled'];
    if ($isAppLoginPrimary) {
        $backofficeLink = str_replace('http://', $app['configs']['env.proto'] . '://', $app['configs']['salesfloor_storefront.host']) . '/app';
    } else {
        $backofficeLink = str_replace('http://', $app['configs']['env.proto'] . '://', $app['configs']['salesfloor_storefront.host']) . '/backoffice';
    }

    $nOk = 0;
    $nInconsistent = 0;
    $demoEmails = [];

    $utc = new \DateTimeZone('UTC');
    $end = new \DateTime(getenvd('SF_KPI_DATETO', 'now'), $utc);
    $start = new \DateTime(getenvd('SF_KPI_DATEFROM', '29 days ago 00:00'), $utc);
    if ($start > $end) {
        throw new \Exception("Invalid date range for KPIs report: " .
                                 $start->format('Y-m-d H:i:s') . " is after " .
                                 $end->format('Y-m-d H:i:s'));
    }
    $diff   = $start->diff($end);
    $ndays  = $diff->days;
    $ndays += (0 !== $diff->h) ? 1 : 0;


    $showRepLeaderboard = ($app['configs']['retailer.storepage_mode'] ? false : true);
    $showSales = ($app['configs']['retailers.current'] == 'bru' ? false : true);

    $msg_dir = get_message_dir($app);
    if (is_dir($msg_dir)) {
        if (!getenv('SF_KPI_DELETOLD')) {
            throw new \Exception("Can't use directory '$msg_dir': it already exists");
        }
        exec('rm -rf ' . escapeshellarg($msg_dir));
    }
    mkdir($msg_dir);

    $sfClient = new SalesfloorAPIRepository($app['configs']);
    $sfClient->setHost($app['configs']['retailer.rest_api_url']);

    try {
        $bigShots = $sfClient->select(['user_email', 'name', 'store'], 'reps', ["group" => [3, 4], "user_status" => "^0"], 0, 90000);
        $bigShots = reset($bigShots);
    } catch (Exception $e) {
        $bigShots = [];
    }

    $mgrs = [];

    $storeFilters = [];
    if ($app['configs']['retailer.brand']) {
        $storeFilters['brand'] = $app['configs']['retailer.brand'];
    }
    $stores = $app['stores.manager']->getAll($storeFilters, 0, 900000, true, ["m.city", "m.name"]);
    $storeIds = [];
    $storeNames = [];
    foreach ($stores as $store) {
        $storeIds[] = $store->store_id;
        $storeName = ((isset($store->city) && $store->city && $store->city != substr($store->name, 0, strlen($store->city))) ? $store->city . ' - ' : '' ) . $store->name;
        $storeNames[$store->store_id] = $storeName;
    }
    list($storeKPIs, $storeRankings, $nRankedStores) = get_report($app, $storeIds, null, $start, $end);
    $repLeaderboards = [];

    $repFields = ['full_name', 'last_name', 'group', 'user_email', 'selling_mode'];
    $reps = $sfClient->select($repFields, 'reps', ["user_status" => "^0", "type" => "rep"], 0, 9000000);
    $reps = reset($reps);

    $allReps = $sfClient->select($repFields, 'reps', ["user_status" => "^42", "type" => "rep"], 0, 9000000);
    $allReps = reset($allReps);

    $kpiReps = [];
    foreach ($allReps as $rep) {
        if (isset($rep['store']) && $rep['store']) {
            if (!isset($kpiReps[$rep['store']])) {
                $kpiReps[$rep['store']] = [];
            }
            $kpiReps[$rep['store']][] = $rep['ID'];
        }
    }

    foreach ($stores as $sk => $store) {
        $mgrs[$store->store_id] = [];
        if (!isset($kpiReps[$store->store_id])) {
            $app['logger']->info("No reps in store $store->store_id; skipping it.");
            continue;
        }
        try {
            $tz = new \DateTimeZone($store->timezone);
            list($userKPIs, $repRankings, $nRankedReps) = get_report($app, $kpiReps[$store->store_id], $store->store_id, $start, $end);
        } catch (\Exception $e) {
            $app['logger']->error("Skipping store $store->store_id because of this exception: " . $e->getMessage());
            continue;
        }

        $repLeaderboards[$store->store_id] = [];
        foreach ($reps as $rep) {
            if ($rep['group'] != GroupPermissionService::USER && $rep['store'] == $store->store_id) {
                $mgrs[$rep['store']][$rep['ID']] = $rep;
            }
            if (!isset($userKPIs[$rep['ID']]) || !isset($repRankings[$rep['ID']])) {
                continue;
            }
            if (!isset($rep['full_name'])) {
                $rep['full_name'] = $rep['first_name'] . ' ' . $rep['last_name'];
            }
            if ($rep['selling_mode']) {
                $repLeaderboards[$store->store_id][] = array_merge($repRankings[$rep['ID']], [
                "name" => $rep['full_name'],
                "last_name" => $rep['last_name'],
                ]);
            }
            if ($rep['group'] == GroupPermissionService::USER) {
                if (!$app['configs']['retailer.storepage_mode'] && $rep['selling_mode']) {
                    // The email is optional for people with "corporate email" disabled
                    if (!$performanceEmailService->haveLeaderboardReport($rep['ID']) && empty($rep['user_email'])) {
                        continue;
                    }

                    $reportParams = array_merge($repRankings[$rep['ID']], [
                    "show_sales"        => $showSales,
                    "n_rankees"         => $nRankedReps,
                    ]);
                    $ok = set_kpi_params($userKPIs[$rep['ID']], $reportParams);
                    if ($ok) {
                        $nOk += 1;
                    } else {
                        $nInconsistent += 1;
                    }

                    $emailParams = [
                        '{RETAILER_IDSTR}' => $app['configs']['retailers.current'] . ($app['configs']['retailer.brand'] ? '-' . $app['configs']['retailer.brand'] : ''),
                        '{BACKOFFICEPAGE}' => $backofficeLink,
                        '{USER_FIRST_NAME}' => $rep['full_name'],
                        '{RETAILER_PRETTY_NAME}' => $app['configs']['retailer.pretty_name'],
                    ];
                    setLabelParams($app, $emailParams);

                    // Detect the locale of the rep
                    $locale = $app['service.multilang']->getLocaleFromUserOrStore($rep['ID'], null);

                    $body = str_replace(
                        array_keys($emailParams),
                        array_values($emailParams),
                        $app['twig']->render($app['service.multilang']->addLocaleToEmailTemplate('emails/common/report_user.html', $locale), $reportParams)
                    );

                    // Generate subject line
                    $subject = $app['translator']->trans('api_day_report_subject', [
                        '{DAY_NUM}'  => $ndays,
                        '{REP_NAME}' => $rep['full_name'],
                    ], null, $locale);

                    $msgFile = serialize_message($msg_dir, $rep['user_email'], $rep['ID'], $subject, $body);

                    if (($app['configs']['kpi_email.demo_addresses'] !== null) && $app['configs']['kpi_email.demo_addresses'] && count($demoEmails) < 1) {
                        $demoEmails[] = $msgFile;
                    }
                }
            }
        }

        usort($repLeaderboards[$store->store_id], ($app['configs']['retailer.storepage_mode'] ? 'cmp_rep_name' : 'cmp_sales'));
    }

    $storeLeaderboard = [];
    foreach ($storeRankings as $storeId => $rankings) {
        $rankings['name'] = $storeNames[$storeId];
        $rankings['store_name'] = $storeNames[$storeId];
        $storeLeaderboard[] = $rankings;
        foreach ($mgrs[$storeId] as $mgr) {
            // Email is not mandatory anymore (based on the corporate email config)
            if ($performanceEmailService->haveLeaderboardReport($mgr['ID']) || !empty($mgr['user_email'])) {
                $reportParams = array_merge($rankings, [

                "show_sales"        => $showSales,
                "show_leaderboard"  => $showRepLeaderboard,
                "n_rankees"         => $nRankedStores,
                "leaderboard"       => $repLeaderboards[$storeId],
                ]);
                $ok = set_kpi_params($storeKPIs[$storeId], $reportParams);
                if ($ok) {
                    $nOk += 1;
                } else {
                    $nInconsistent += 1;
                }

                $emailParams = [
                    '{RETAILER_IDSTR}' => $app['configs']['retailer.idstr'],
                    '{BACKOFFICEPAGE}' => $backofficeLink,
                    '{USER_FIRST_NAME}' => $mgr['full_name'],
                    '{RETAILER_PRETTY_NAME}' => $app['configs']['retailer.pretty_name'],
                ];
                setLabelParams($app, $emailParams);

                // Detect the locale of the rep
                $locale = $app['service.multilang']->getLocaleFromUserOrStore($mgr['ID'], null);

                $body = str_replace(
                    array_keys($emailParams),
                    array_values($emailParams),
                    $app['twig']->render($app['service.multilang']->addLocaleToEmailTemplate('emails/common/report_store.html', $locale), $reportParams)
                );

                // Generate subject line
                $subject = $app['translator']->trans(
                    'api_day_report_subject',
                    [
                        '{DAY_NUM}'  => $ndays,
                        '{REP_NAME}' => $reportParams['store_name'],
                    ],
                    null,
                    $locale
                );
                $msgFile = serialize_message($msg_dir, $mgr['user_email'], $mgr['ID'], $subject, $body);

                if (($app['configs']['kpi_email.demo_addresses'] !== null) && $app['configs']['kpi_email.demo_addresses'] && count($demoEmails) < 2) {
                    $demoEmails[] = $msgFile;
                }
            }
        }
    }

    usort($storeLeaderboard, ($app['configs']['retailers.current'] == 'bru' ? 'cmp_store_name' : 'cmp_sales'));

    if (count($bigShots) > 0) {
        list($globalKPIs, $unused, $alsoUnused) = get_report($app, [], null, $start, $end);
        $reportParams = [
        'show_sales'  => $showSales,
        'top_stores'  => get_top_stores($storeRankings, $storeNames, $app['configs']['kpi_email.n_top_stores']),
        'leaderboard' => $storeLeaderboard,
        ];
        $ok = set_kpi_params($globalKPIs, $reportParams);
        if ($ok) {
            $nOk += 1;
        } else {
            $nInconsistent += 1;
        }

        foreach ($bigShots as $bigShot) {
            // Rep from group 3-4 shouldn't have selling_mode on, but keep the same logic just in case
            if ($performanceEmailService->haveLeaderboardReport($bigShot['ID']) || !empty($bigShot['user_email'])) {
                $emailParams = [
                    '{RETAILER_IDSTR}' => $app['configs']['retailers.current'] . ($app['configs']['retailer.brand'] ? '-' . $app['configs']['retailer.brand'] : ''),
                    '{BACKOFFICEPAGE}' => $backofficeLink,
                    '{USER_FIRST_NAME}' => isset($bigShot['full_name']) ? $bigShot['full_name'] : $bigShot['user_email'],
                    '{RETAILER_PRETTY_NAME}' => $app['configs']['retailer.pretty_name'],
                ];
                setLabelParams($app, $emailParams);

                // Detect the locale of the rep
                $locale = $app['service.multilang']->getLocaleFromUserOrStore($bigShot['ID'], null);

                $body = str_replace(
                    array_keys($emailParams),
                    array_values($emailParams),
                    $app['twig']->render($app['service.multilang']->addLocaleToEmailTemplate('emails/common/report_store_manager.html', $locale), $reportParams)
                );

                // Generate subject line
                $subject = $app['translator']->trans(
                    'api_day_report_subject',
                    [
                        '{DAY_NUM}'  => $ndays,
                        '{REP_NAME}' => $app['configs']['retailer.pretty_name'],
                    ],
                    null,
                    $locale
                );

                $msgFile = serialize_message($msg_dir, $bigShot['user_email'], $bigShot['ID'], $subject, $body);

                if (($app['configs']['kpi_email.demo_addresses'] !== null) && $app['configs']['kpi_email.demo_addresses'] && count($demoEmails) < 3) {
                    $demoEmails[] = $msgFile;
                }
            }
        }
    }

    if (!getenv('SF_KPI_NOARCHIVE')) {
        archive_messages($app);
    }

    return [
    'demoEmails' => $demoEmails,
    'nOk' => $nOk,
    'nInconsistent' => $nInconsistent,
    ];
}

function send_message($app, $recipient, $userId, $subject, $body, $recipientOverride)
{
    try {
        $app['reporting.performance-email']->send($recipient, $userId, $subject, $body, $recipientOverride);
    } catch (\Exception $e) {
        $app['logger']->error("Failed to send performance report to $userId [$recipient]: " . $e->getMessage() . $e->getTraceAsString());
    }
}

function send_from_file($app, $fileName, $recipientOverride = null)
{
    list($recipient, $userId, $subject, $body) = deserialize_message($fileName);
    if ($recipientOverride) {
        $recipient = $recipientOverride;
    } else {
        $recipient = get_destination_email($app, $recipient);
    }

    return send_message($app, $recipient, $userId, $subject, $body, $recipientOverride);
}

function get_report($app, array $ids, $storeId, \DateTime $start, \DateTime $end)
{
    $metrics = ['sales', 'response-time', 'requests-received', 'unique-visitors', 'chat-answer-rate', 'global'];

    $type = isset($storeId) ? 'rep' : 'store';
    $grouped = ($type != 'store' || $ids);

    $users = $stores = [];
    if ($type == 'store') {
        $report = 'get-store-marketing';
        $reportKey = 'marketing-store';
        $stores = $ids;
        $idsToRank = $ids;
    } else {
        $report = 'get-marketing';
        $reportKey = 'marketing';
        $users = $ids;
        $idsToRank = $app['rankings.manager']->getValidRepIds($storeId);
    }

    $re = new ReportingEngine([$report], $ids, 'UTC', $start->format('Y-m-d'), $end->format('Y-m-d'), null, true, $grouped, $stores);
    $report = $re->run($app['repositories.mysql'], $app);
    if (!is_array($report) || !isset($report[$reportKey]) || !is_array($report[$reportKey])) {
        throw new \Exception("Malformed $type report: " . json_encode($report));
    }
    $report = $report[$reportKey];

    if ($grouped) {
        $kpis = [];
        foreach ($report as $idString => $stats) {
            $prefix = ($type == 'store' ? 'store-' : 'user-');
            if (strpos($idString, $prefix) === 0) {
                $kpis[substr($idString, strlen($prefix))] = $stats;
            }
        }
        $rankings = $app['rankings.manager']->rankKPIs($type, $idsToRank, $metrics, $report);
        renameRankings($rankings);
    } else {
        $kpis = $report;
        $rankings = null;
    }

    return [$kpis, $rankings, count($idsToRank)];
}

function get_top_stores($storeRankings, $storeNames, $nTopStores)
{
    $topStores = [];
    foreach ($storeRankings as $storeId => $rankings) {
        if (!isset($storeNames[$storeId])) {
            continue;
        }
        foreach ($rankings as $k => $ranking) {
            $ranking['name'] = $storeNames[$storeId];
            if ($k == 'sales') {
              // normally number_format (i.e. rendering) should be called in the template, but because it's inker and there's a conflict
              // between nunjucks and twig, it can't be done. So I'm doing it here.
                $ranking['v'] = '$' . number_format($ranking['v'], 2);
            }
            if (!isset($topStores[$k])) {
                $topStores[$k] = [];
            }
          // insert before lesser store, if any
            for ($i = 0; $i < $nTopStores; $i++) {
                if (!isset($topStores[$k][$i])) {
                    array_push($topStores[$k], $ranking);
                    break;
                }
                if ($ranking['rank'] < $topStores[$k][$i]['rank']) {
                    array_splice($topStores[$k], $i, 0, [$ranking]);
                    break;
                }
            }
            while (count($topStores[$k]) > $nTopStores) {
                array_pop($topStores[$k]);
            }
        }
    }
    foreach ($topStores as $k => &$leaders) {
        while (count($leaders) < $nTopStores) {
            array_push($leaders, [ 'name' => '-', 'v' => '-' ]);
        }
    }

    return $topStores;
}


require_once 'bootstrap_cron.php';

$script = 'api/crons/' . basename(__FILE__);

try {
    $cansend = !getenv('SF_KPI_NOSEND');

    if (!getenv('SF_KPI_SENDFROM')) {
        $r = render_emails($app);
        if ($cansend && $app['configs']['kpi_email.demo_addresses'] !== null && $app['configs']['kpi_email.demo_addresses']) {
            foreach ($r['demoEmails'] as $emailFile) {
                foreach ($app['configs']['kpi_email.demo_addresses'] as $recipient) {
                    send_from_file($app, $emailFile, $recipient);
                }
            }
        }
        $nTotal = $r['nOk'] + $r['nInconsistent'];
        if ($nTotal) {
            $badPercent = (int) (100 * $r['nInconsistent'] / $nTotal);
            $app['logger']->error("{$r['nInconsistent']} / $nTotal ($badPercent%) of the messages are inconsistent.");
            if ($badPercent > 10) {
                throw new \Exception("That's above the 10% threshold; this will not send to reps.");
            }
        } else {
            throw new \Exception("0 emails to send? That's weird.");
        }
        if (!getenv('SF_KPI_SENDNOW')) {
            $cansend = false;
        }
    }

    if ($cansend) {
        $msg_dir = get_message_dir($app);
        $dh = opendir($msg_dir);
        if ($dh === false) {
            throw new \Exception("failed to open the '$msg_dir' directory for reading");
        }
        while (false !== ($entry = readdir($dh))) {
            if ($entry != '.' && $entry != '..') {
                send_from_file($app, "$msg_dir/$entry");
            }
        }
        closedir($dh);
        if (!getenv('SF_KPI_KEEPDIR')) {
            if (preg_match("/['\\\\]/", $msg_dir)) {
                throw new \Exception("what you are doing");
            }
            exec("rm -r '$msg_dir'");
        }

        $app['logger.success']->info('Sent');
    }

    $cronMetrics->recordScriptStatus(CRON_STATUS_SUCCESS, $script);
} catch (\Exception $e) {
    $app['logger']->critical($e);

    $cronMetrics->recordScriptStatus(CRON_STATUS_ERROR, $script);

    throw $e;
}
