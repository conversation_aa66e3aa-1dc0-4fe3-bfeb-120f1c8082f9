<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class SetNullableCreatedByTagsRelationship extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_customer_tags_relationships');

        $table
            ->changeColumn('created_by', 'integer', [
               'null' => true,
               'signed' => false,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_customer_tags_relationships');

        $table
            ->changeColumn('created_by', 'integer', [
                'null' => false,
                'signed' => false,
            ])
            ->update();
    }
}
