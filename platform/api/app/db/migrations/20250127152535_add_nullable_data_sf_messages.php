<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNullableDataSfMessages extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_messages');

        $table
            ->changeColumn('from_email', 'string', [
                'limit' => 250,
                'null' => true,
            ])
            ->changeColumn('from_name', 'string', [
                'limit' => 250,
                'null' => true,
            ])
            ->changeColumn('attachment', 'text', [
                'limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG,
                'null' => true,
            ])
            ->changeColumn('message', 'text', [
                'limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG,
                'null' => true,
            ])
            ->changeColumn('title', 'string', [
                'limit' => 250,
                'null' => true,
            ])
            ->changeColumn('products', 'text', [
                'limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG,
                'null' => true,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_messages');

        $table
            ->changeColumn('from_email', 'string', [
                'limit' => 250,
                'null' => false,
            ])
            ->changeColumn('from_name', 'string', [
                'limit' => 250,
                'null' => false,
            ])
            ->changeColumn('attachment', 'text', [
                'limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG,
                'null' => false,
            ])
            ->changeColumn('message', 'text', [
                'limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG,
                'null' => false,
            ])
            ->changeColumn('title', 'string', [
                'limit' => 250,
                'null' => false,
            ])
            ->changeColumn('products', 'text', [
                'limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG,
                'null' => false,
            ])
            ->update();
    }
}
