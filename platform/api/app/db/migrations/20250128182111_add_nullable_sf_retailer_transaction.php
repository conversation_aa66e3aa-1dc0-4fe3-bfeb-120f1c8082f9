<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNullableSfRetailerTransaction extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_retailer_transaction');

        $table
            ->changeColumn('location', 'string', [
                'limit' => 100,
                'null' => true,
            ])
            ->changeColumn('pos_id', 'string', [
                'limit' => 50,
                'null' => true,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_retailer_transaction');

        $table
            ->changeColumn('location', 'string', [
                'limit' => 100,
                'null' => false,
            ])
            ->changeColumn('pos_id', 'string', [
                'limit' => 50,
                'null' => false,
            ])
            ->update();
    }
}
