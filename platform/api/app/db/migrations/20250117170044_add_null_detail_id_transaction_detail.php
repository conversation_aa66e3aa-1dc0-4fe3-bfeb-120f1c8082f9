<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNullDetailIdTransactionDetail extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_rep_transaction_detail');

        $table
            ->changeColumn('trx_detail_id', 'string', [
                'null' => true,
                'length' => 45,
            ])
            ->update();

        $table = $this->table('sf_rep_transaction_detail_copy');

        $table
            ->changeColumn('trx_detail_id', 'string', [
                'null' => true,
                'length' => 45,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_rep_transaction_detail');

        $table
            ->changeColumn('trx_detail_id', 'string', [
                'null' => false,
                'length' => 45,
            ])
            ->update();

        $table = $this->table('sf_rep_transaction_detail_copy');

        $table
            ->changeColumn('trx_detail_id', 'string', [
                'null' => false,
                'length' => 45,
            ])
            ->update();
    }
}
