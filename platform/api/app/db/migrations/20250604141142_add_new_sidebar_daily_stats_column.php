<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNewSidebarDailyStatsColumn extends Migration
{
    public function change(): void
    {
        $this
            ->table('sf_sidebar_daily_stats')
            ->addColumn('sidebar_desktop_total_menu_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_total_menu_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_total_menu_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_total_menu_support', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_menu_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_menu_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_menu_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_menu_support', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_menu_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_menu_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_menu_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_menu_support', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_menu_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_menu_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_menu_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_menu_support', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_total_requests_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_total_requests_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_total_requests_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_requests_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_requests_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_total_requests_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_requests_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_requests_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_desktop_unique_requests_appointment', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_requests_livechat', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_requests_message', 'biginteger', [
                'null' => true
            ])
            ->addColumn('sidebar_mobile_unique_requests_appointment', 'biginteger', [
                'null' => true
            ])
            ->update();
        ;
    }
}
