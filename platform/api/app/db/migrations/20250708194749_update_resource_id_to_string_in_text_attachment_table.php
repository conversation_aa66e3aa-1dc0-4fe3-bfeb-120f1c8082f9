<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class UpdateResourceIdToStringInTextAttachmentTable extends Migration
{
    private const TABLE_NAME = 'sf_text_attachment';

    public function up(): void
    {
        $table = $this->table(self::TABLE_NAME)
            ->changeColumn(
                'resource_id',
                'string',
                [
                    'limit' => 32,
                    'null' => true,
                ]
            )
            ->update();
    }

    public function down(): void
    {
        $table = $this->table(self::TABLE_NAME)
            ->changeColumn(
                'resource_id',
                'biginteger',
                [
                    'null' => true,
                ]
            )
            ->update();
    }
}
