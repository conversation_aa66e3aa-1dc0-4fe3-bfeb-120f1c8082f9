<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNullCustomerFields extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_customer');

        $table
            ->changeColumn('geo', 'string', [
                'length' => 100,
                'null' => true,
            ])
            ->changeColumn('localization', 'enum', [
                'values' => [
                    'en',
                    'fr',
                ],
                'null' => true,
            ])
            ->changeColumn('comment', 'string', [
                'length' => 500,
                'null' => true,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_customer');

        $table
            ->changeColumn('geo', 'string', [
                'length' => 100,
                'null' => false,
            ])
            ->changeColumn('localization', 'enum', [
                'values' => [
                    'en',
                    'fr',
                ],
                'null' => false,
            ])
            ->changeColumn('comment', 'string', [
                'length' => 500,
                'null' => false,
            ])
            ->update();
    }
}
