<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNewSidebarEvents extends Migration
{
    public function up(): void
    {
        $updateEvents = function (\Phinx\Db\Table $table) {
            $table
                ->changeColumn(
                    'action',
                    'enum',
                    [
                        'values'  => array_merge($this->queryEnumValues($table->getName(), 'action'), [
                            'SIDEBAR_DESKTOP_CONNECT2_MENU',
                            'SIDEBAR_MOBILE_CONNECT2_MENU',
                            'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                            'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        ]),
                        'comment' => 'Action that was logged',
                        'null' => false,
                    ]
                )->save();
        };

        $updateEvents($this->table('sf_sidebar_event_log'));
        $updateEvents($this->table('sf_sidebar_event_log_20240201'));
    }

    public function down(): void
    {
        $updateEvents = function (\Phinx\Db\Table $table) {
            $table
                ->changeColumn(
                    'action',
                    'enum',
                    [
                        'values'  => array_diff($this->queryEnumValues($table->getName(), 'action'), [
                            'SIDEBAR_DESKTOP_CONNECT2_MENU',
                            'SIDEBAR_MOBILE_CONNECT2_MENU',
                            'SIDEBAR_DESKTOP_CONNECT2_REQUESTS',
                            'SIDEBAR_MOBILE_CONNECT2_REQUESTS',
                        ]),
                        'comment' => 'Action that was logged',
                        'null' => false,
                    ]
                )->save();
        };

        $updateEvents($this->table('sf_sidebar_event_log'));
        $updateEvents($this->table('sf_sidebar_event_log_20240201'));
    }
}
