<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class RemoveInvalidRowKpis extends Migration
{
    public function up(): void
    {
        $updateFields = function (\Phinx\Db\Table $table) {
            $name = $table->getTable()->getName();

            $this->execute('set session sql_mode = ""');
            // Some retailer (e.g: saks-qa05) had some bad data in DB
            $this->execute("DELETE FROM {$name} WHERE date = '0000-00-00'");
            $this->execute('set session sql_mode = "ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION"');
        };

        $updateFields($this->table('sf_user_daily_stats'));
        $updateFields($this->table('sf_store_daily_stats'));
    }

    public function down(): void
    {
        // NOP
    }
}
