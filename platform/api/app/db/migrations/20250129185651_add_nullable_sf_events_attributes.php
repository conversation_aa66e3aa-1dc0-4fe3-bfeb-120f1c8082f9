<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNullableSfEventsAttributes extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_events');

        $table
            ->changeColumn('attributes', 'string', [
                'limit' => 250,
                'null' => true,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_events');

        $table
            ->changeColumn('attributes', 'string', [
                'limit' => 250,
                'null' => false,
            ])
            ->update();
    }
}
