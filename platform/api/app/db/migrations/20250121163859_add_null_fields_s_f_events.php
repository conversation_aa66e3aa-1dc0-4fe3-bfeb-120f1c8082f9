<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AddNullFieldsSFEvents extends Migration
{
    public function up(): void
    {
        $table = $this->table('sf_events');

        $table
            ->changeColumn('source', 'text', [
                'null' => true,
            ])
            ->changeColumn('user_id', 'integer', [
                'null' => true,
            ])
            ->changeColumn('customer_id', 'integer', [
                'null' => true,
            ])
            ->changeColumn('event_id', 'integer', [
                'null' => true,
            ])
            ->changeColumn('satisfied', 'smallinteger', [
                'null' => true,
            ])
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('sf_events');

        $table
            ->changeColumn('source', 'text', [
                'null' => false,
            ])
            ->changeColumn('user_id', 'integer', [
                'null' => false,
            ])
            ->changeColumn('customer_id', 'integer', [
                'null' => false,
            ])
            ->changeColumn('event_id', 'integer', [
                'null' => false,
            ])
            ->changeColumn('satisfied', 'smallinteger', [
                'null' => false,
            ])
            ->update();
    }
}
