<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class SfPrompt extends Migration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('sf_prompts', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' =>
                'Custom AI prompts saved by users for message generation',
        ]);

        $table
            ->addColumn('id', 'integer', [
                'identity' => true,
                'signed' => false,
                'comment' => 'Primary key',
            ])
            ->addColumn('user_id', 'biginteger', [
                'signed' => false,
                'null' => false,
                'comment' => 'Reference to wp_users.ID',
            ])
            ->addColumn('title', 'string', [
                'limit' => 255,
                'null' => false,
                'comment' => 'Title/name for the prompt',
            ])
            ->addColumn('prompt', 'text', [
                'null' => false,
                'comment' => 'The custom prompt text entered by the user',
            ])

            ->addTimestamps()
            ->addIndex(['user_id'], ['name' => 'idx_user_id'])
            ->addIndex(
                ['user_id', 'created_at'],
                ['name' => 'idx_user_created']
            )
            ->addForeignKey('user_id', 'wp_users', 'ID', [
                'delete' => 'CASCADE',
                'update' => 'CASCADE',
                'constraint' => 'fk_sf_prompts_user_id',
            ])
            ->create();
    }
}
