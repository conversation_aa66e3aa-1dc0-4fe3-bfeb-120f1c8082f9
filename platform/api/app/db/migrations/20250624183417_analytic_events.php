<?php

declare(strict_types=1);

use Salesfloor\Services\Phinx\Migration;

final class AnalyticEvents extends Migration
{
    public function up(): void
    {
        $this->execute(<<<SQL
        CREATE TABLE IF NOT EXISTS sf_analytics_events (
            id          BIGINT UNSIGNED AUTO_INCREMENT COMMENT 'Primary key identifier for each analytics event',
            event       VARCHAR(255) NOT NULL COMMENT 'Name or type of the analytics event being tracked',
            dt          DATETIME     NOT NULL COMMENT 'Date and time when the event occurred',
            user_id     BIGINT UNSIGNED  NULL COMMENT 'ID of the user who triggered the event, if authenticated',
            device_type ENUM('web', 'mobile', 'tablet')  NULL COMMENT 'Type of device used when the event occurred',
            os_name         VARCHAR(32)        NULL COMMENT 'Operating system name (e.g., Windows, iOS, Android)',
            os_version      VARCHAR(32)        NULL COMMENT 'Version of the operating system',
            browser_name    VARCHAR(32)        NULL COMMENT 'Name of the browser used (e.g., Chrome, Firefox, Safari)',
            browser_version VARCHAR(32)        NULL COMMENT 'Version of the browser used',
            app_version     VARCHAR(20)        NULL COMMENT 'Version of the application when the event occurred',
            ip_address      VARCHAR(45)        NULL COMMENT 'IP address of the client (supports both IPv4 and IPv6)',
            user_agent      TEXT               NULL COMMENT 'Full user agent string from the client request',
            event_data      JSON               NULL COMMENT 'Additional event-specific data stored as JSON',
            created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when the record was inserted into the database',
            PRIMARY KEY (id, dt),
            INDEX idx_event_dt        (event, dt),
            INDEX idx_user_dt         (user_id, dt),
            INDEX idx_device_type     (device_type)
        ) ENGINE=InnoDB
        PARTITION BY RANGE COLUMNS(dt) (
            PARTITION p_2025  VALUES LESS THAN ('2026-01-01'),
            PARTITION p_future VALUES LESS THAN (MAXVALUE)
        );
        SQL);

        $this->execute('DROP PROCEDURE IF EXISTS rotate_partitions_analytics');

        $this->execute(<<<SQL
        CREATE PROCEDURE rotate_partitions_analytics()
        BEGIN
            DECLARE y INT DEFAULT YEAR(CURDATE());

            /* split the catch-all partition into the new year + a fresh p_future */
            SET @add := CONCAT(
                'ALTER TABLE sf_analytics_events ',
                'REORGANIZE PARTITION p_future INTO (',
                'PARTITION p_', y, ' VALUES LESS THAN ("', y + 1, '-01-01"), ',
                'PARTITION p_future VALUES LESS THAN (MAXVALUE))'
            );
            PREPARE stmt FROM @add; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        END;
        SQL);

        $this->execute('DROP EVENT IF EXISTS ev_rotate_partitions');

        $this->execute(<<<SQL
        CREATE EVENT ev_rotate_partitions
          ON SCHEDULE EVERY 1 YEAR
              STARTS '2026-01-01 00:05:00'
          DO CALL rotate_partitions_analytics();
        SQL);
    }

    public function down(): void
    {
        $this->execute('DROP EVENT IF EXISTS ev_rotate_partitions');
        $this->execute('DROP PROCEDURE IF EXISTS rotate_partitions_analytics');
        $this->execute('DROP TABLE IF EXISTS sf_analytics_events');
    }
}
