<?php

/**
 * Salesfloor API Application
 *
 * Copyright 2014 - Salesfloor
 */

use Salesfloor\Providers\Application\ConfigsServiceProvider;

ini_set('display_errors', 0);

// http://php.net/manual/en/ini.core.php#ini.default-charset
// In PHP 5.6 onwards, "UTF-8" is the default value
// mbstring.internal_encoding : This feature has been DEPRECATED as of PHP 5.6.0. Relying on this feature is highly discouraged
mb_internal_encoding('utf-8');

// Load application paths
require_once 'paths.php';

// Create Application
$app = new Silex\Application();

require_once PATH_SRC . '/cors.php';
// Since some class are not properly lazy loaded, we need to make sure configs are loaded before getting instantiated
// We must do it before service, because some need access to those first
require_once PATH_SRC . '/configs.php';

// If the retailer is invalid (doesn't exist), let's skip the platform boot and let silex default behaviour (404).
if (empty($app['configs'])) {
    return $app;
}

// For backward compatibility, some scripts (ExportSmsLog.php for instance) assumed $configs is defined at the top level.
// Some of these scripts have been fixed but who nows if there are others. So keep that to be on the safe side...
$configs = $app['configs'];

// Same behaviour as before
if ($app['configs']['env'] === 'dev') {
    error_reporting(E_ALL);
}

// This need to be done after loading configs itself
$app['debug'] = $app['configs']['debug'];

// Load application services
require_once PATH_SRC . '/services.php';

// Load application routes
require_once PATH_SRC . '/routes.php';

// Load application dependencies (needs to be routes.php)
require_once PATH_SRC . '/dependencies.php';

// Load error handlers
require_once PATH_SRC . '/errors.php';

// Load middlewares
require_once PATH_SRC . '/middlewares.php';

return $app;
