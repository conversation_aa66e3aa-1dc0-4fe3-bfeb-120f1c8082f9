<?php

declare(strict_types=1);

namespace Salesfloor\API\Controllers\AI\V1;

use Salesfloor\API\Managers\AI\Prompt as PromptManager;
use Salesfloor\Controllers\Base\JsonApiController;
use Salesfloor\API\Exceptions\Generic\Invalid\InvalidInputException;
use Salesfloor\Services\JsonApi\JsonApiResource;
use Salesfloor\API\Managers\Reps as RepsManager;
use Salesfloor\Exceptions\FeatureMissingException;
use Silex\Application;

/**
 * Class Prompt
 *
 * @property PromptManager $manager
 * @package Salesfloor\API\Controllers\AI\V1\Prompt
 */
class Prompt extends JsonApiController
{
    /**
     * Create a new prompt
     */
    public function create(Application $app)
    {
        if (!$app['configs']['ai.outreach.is_enabled']) {
            throw new FeatureMissingException('AI outreach is not enabled');
        }

        $userId = $this->getUserId($app);

        try {

            /** @var JsonApiResource $jsonApiResource */
            $jsonApiResource = $app['json-api.resource'];
            $attributes = $jsonApiResource->getAttributes();

            // Add user_id to the creation parameters
            $createParameters = $attributes;
            $createParameters['user_id'] = $userId;

            $model = $this->doCreate($createParameters);

            return $this->jsonApi($model, $this->getModel());
        } catch (InvalidInputException $e) {
            // Log the validation error
            $app["logger"]->error(
                "Prompt creation failed for user {$userId}: " .
                    $e->getMessage()
            );
            return $app->abort(422, $e->getMessage());
        } catch (\Exception $e) {
            // Log any other errors
            $app["logger"]->error(
                "Unexpected error in prompt creation for user {$userId}: " .
                    $e->getMessage()
            );
            return $app->abort(500, 'An unexpected error occurred');
        }
    }

    /**
     * Get list of prompts for current user
     */
    public function getList(Application $app)
    {
        if (!$app['configs']['ai.outreach.is_enabled']) {
            throw new FeatureMissingException('AI outreach is not enabled');
        }

        $prompts = $this->manager->getUserPrompts($this->getUserId($app));

        return $this->jsonApi($prompts, $this->getModel());
    }

    /**
     * Get a specific prompt (only if user owns it)
     */
    public function getOne(Application $app, $id)
    {
        if (!$app['configs']['ai.outreach.is_enabled']) {
            throw new FeatureMissingException('AI outreach is not enabled');
        }

        $prompt = $this->manager->getUserPrompt($this->getUserId($app), (int) $id);

        return $this->jsonApi($prompt, $this->getModel());
    }

    /**
     * Update a prompt (only if user owns it)
     */
    public function update(Application $app, $id)
    {
        if (!$app['configs']['ai.outreach.is_enabled']) {
            throw new FeatureMissingException('AI outreach is not enabled');
        }

        $prompt = $this->manager->getUserPrompt($this->getUserId($app), (int) $id);

        if (prompt) {
            /** @var JsonApiResource $jsonApiResource */
            $jsonApiResource = $app['json-api.resource'];
            $attributes = $jsonApiResource->getAttributes();

            // Don't allow changing user_id
            unset($attributes['user_id']);

            $this->doUpdate($app, $id, $attributes, true);
        }

        return $app->json([], 204);
    }

    /**
     * Delete a prompt (only if user owns it)
     */
    public function delete(Application $app, $id)
    {
        if (!$app['configs']['ai.outreach.is_enabled']) {
            throw new FeatureMissingException('AI outreach is not enabled');
        }

        $prompt = $this->manager->getUserPrompt($this->getUserId($app), (int) $id);

        if ($prompt) {
            $this->manager->delete($prompt);
        }

        return $app->json([], 204);
    }
}
