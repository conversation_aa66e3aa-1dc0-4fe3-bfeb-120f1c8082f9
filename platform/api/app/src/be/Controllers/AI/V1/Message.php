<?php

declare(strict_types=1);

namespace Salesfloor\API\Controllers\AI\V1;

use Salesfloor\API\Managers\AI\Message as MessageManager;
use Salesfloor\Controllers\Base\JsonApiController;
use Salesfloor\Exceptions\InvalidFieldException;
use Salesfloor\Exceptions\EmptyFieldException;
use Salesfloor\Models\Rep;
use Salesfloor\Services\AI\Exception\AIResponseException;
use Salesfloor\Services\JsonApi\JsonApiResource;
use Salesfloor\API\Managers\Reps as RepsManager;
use Salesfloor\Services\Storefront;
use Salesfloor\Exceptions\FeatureMissingException;
use Silex\Application;

/**
 * Message Controller
 *
 * Handles AI-powered marketing content generation and refinement requests.
 *
 * @property MessageManager $manager
 */
class Message extends JsonApiController
{
    /**
     * Max lenght of prompt acceptable in a single request.
     */
    private const PROMPT_MAX_LIMIT = 10000;

    /**
     * Generate Message operation
     */
    private const COMMAND_GEN = 'generateMessage';

    /**
     * Refine message operation
     */
    private const COMMAND_REF = 'refineMessage';

    /**
     * Generate AI marketing content
     *
     * Accepts a prompt and optional parameters to generate new marketing content.
     * Supports multiple content types (email, SMS, subject lines) and campaign types.
     *
     * @param Application $app
     * @return string JSON:API formatted response containing generated content
     * @throws AIResponseException When AI generation fails
     * @throws EmptyFieldException When required prompt field is missing
     */
    public function generate(Application $app): string
    {
        return $this->processRequest(
            $app,
            self::COMMAND_GEN,
            'AI generation failed'
        );
    }

    /**
     * Refine existing marketing content
     *
     * Analyzes and improves existing content for clarity, grammar, tone, and effectiveness.
     * Maintains original intent while optimizing for better engagement.
     *
     * @param Application $app Silex application instance
     * @return string JSON:API formatted response containing refined content
     * @throws AIResponseException When AI refinement fails
     * @throws EmptyFieldException When required prompt field is missing
     */
    public function refine(Application $app): string
    {
        return $this->processRequest(
            $app,
            self::COMMAND_REF,
            'AI refinement failed'
        );
    }

    /**
     * Process AI request (generate or refine)
     *
     * Validates input, builds context, and
     * delegates to the appropriate manager method.
     *
     * @param Application $app
     * @param string $operation The Manager method to call - refineMessage | generateMessage
     * @param string $errorLogPrefix Prefix for error logging messages
     * @return string JSON:API formatted response
     * @throws AIResponseException When AI processing fails
     * @throws EmptyFieldException When required fields are missing
     */
    private function processRequest(
        Application $app,
        string $operation,
        string $errorLogPrefix
    ): string {

        if (!$app['configs']['ai.outreach.is_enabled']) {
            throw new FeatureMissingException('AI outreach is not enabled');
        }

        /** @var JsonApiResource $jsonApiResource */
        $jsonApiResource = $app['json-api.resource'];
        $attributes = $jsonApiResource->getAttributes();
        $userId = null;

        try {
            $prompt = $this->validatePrompt($attributes);

            $this->validateContentType($attributes);

            $user = $this->getLoggedInUserOrFail($app);

            /** @var RepsManager $manager */
            $manager = $app['reps.manager'];

            $userId = (int) $manager->getUserIdFromContext($user->ID);
            $user = $manager->getById($userId);

            $context = $this->buildContext($app, $user, $attributes);

            $message = $this->manager->$operation($prompt, $userId, $context);

            return $this->jsonApi($message, $this->getModel());
        } catch (AIResponseException $e) {
            $app['logger']->error($errorLogPrefix . ': ' . $e->getMessage(), [
                'prompt' => substr($prompt, 0, 100) . '...', // Truncate for logging
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
            throw $e; // Re-throw AI specific exceptions
        } catch (\Exception $e) {
            $app['logger']->error($errorLogPrefix . ': ' . $e->getMessage(), [
                'prompt' => substr($prompt, 0, 100) . '...', // Truncate for logging
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new AIResponseException();
        }
    }

    /**
     * Constructs a comprehensive context array including organization details
     * and optional parameters that influence AI content generation behavior.
     *
     * Supported optional parameters:
     * - content_type: Type of content to generate (email, sms, subject_lines)
     * - tone: Desired tone (professional, casual, urgent, etc.)
     *
     * @param Application $app
     * @param Rep $user current logged in user - in team mode, this should be the store user
     * @param array $attributes Request attributes from JSON:API resource
     * @return array Structured context array for AI agents
     */
    private function buildContext(
        Application $app,
        Rep $user,
        array $attributes
    ): array {
        /** @var Storefront $storefront **/
        $storefront = $app['service.storefront'];
        $storefront_url = $storefront->getStoreFrontUrl($user->ID);

        $context = [
            'organization' => $app['configs']['retailer.brand_name'],
            'storefront_url' => $storefront_url,
            'subscription_url' => $storefront_url . '/subscribe',
            'appointment_url' => $storefront_url . '/appointment',
            // @TODO: add info about retailer speciality or profile and policy as system prompt to guard AI suggestions
            // 'organization_profile' => $app['configs']['ai.retailer.profile'],
            // 'organization_policy' => $app['configs']['ai.retailer.policy'],
        ];

        // Pass additional context parameters if provided
        $optionalParams = ['content_type', 'tone'];

        foreach ($optionalParams as $param) {
            if (isset($attributes[$param]) && !empty($attributes[$param])) {
                $context[$param] = $attributes[$param];
            }
        }

        return $context;
    }

    /**
     * Validate prompt field
     *
     * @param array $attributes Request attributes
     * @return string Validated prompt
     * @throws EmptyFieldException When prompt is missing or empty
     */
    private function validatePrompt(array $attributes): string
    {
        $prompt = $attributes['prompt'] ?? null;

        if (empty($prompt)) {
            throw (new EmptyFieldException())->addParameters('field', 'prompt');
        }

        // Validate prompt length (reasonable limits)
        if (strlen($prompt) > self::PROMPT_MAX_LIMIT) {
            throw (new InvalidFieldException())->addParameters(
                'field',
                'prompt too long (max 10000 characters)'
            );
        }

        return trim($prompt);
    }

    /**
     * Validate content type if provided
     *
     * @param array $attributes Request attributes
     * @throws EmptyFieldException When content type is invalid
     */
    private function validateContentType(array $attributes): void
    {
        if (
            !isset($attributes['content_type']) ||
            empty($attributes['content_type'])
        ) {
            return; // Content type is optional
        }

        $validContentTypes = ['email', 'sms', 'text'];
        $contentType = strtolower(trim($attributes['content_type']));

        if (!in_array($contentType, $validContentTypes)) {
            throw (new EmptyFieldException())->addParameters(
                'field',
                'content_type must be one of: ' .
                    implode(', ', $validContentTypes)
            );
        }
    }
}
