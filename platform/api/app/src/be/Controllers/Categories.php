<?php

  /**
   * Categories Controller
   *
   * Copyright 2014 - Salesfloor
   */

  namespace Salesfloor\API\Controllers;

  use Silex\Application;
  use Symfony\Component\HttpFoundation\Response;

  /**
   * Categories and Specialties Controller
   *
   * @property \Salesfloor\API\Managers\Categories $manager
   */
class Categories extends \Salesfloor\Controllers\Base\LegacyController
{
    /**
     * Retrieve a list of available categories for a list of reps
     *
     * @param Application $app An instance of an application
     * @param array $reps An array of reps
     * @return array A list of categories
     */
    private function getRepsCategories(Application $app, $reps)
    {
        $categories = [
          'available'   => [],
          'unavailable' => [],
        ];

        $allResults = $this->manager->getAll(['parent_id' => $app['configs']['products.root_category_id']], 0, 100);
        foreach ($allResults as $model) {
            $categories['unavailable'][$model->category_id] = $model->toArray(true);
        }

        foreach ($reps as $rep) {
            $results = $this->manager->getAll(['rep_login' => $rep], 0, 100);
            foreach ($results as $model) {
                if (isset($categories['unavailable'][$model->category_id])) {
                    unset($categories['unavailable'][$model->category_id]);
                }

                if (!isset($categories['available'][$model->category_id])) {
                    $categories['available'][$model->category_id] = $model->toArray(true);
                }
            }
        }

        foreach ($categories['available'] as $key => $value) {
            $categories['available'][] = $value;
            unset($categories['available'][$key]);
        }
        foreach ($categories['unavailable'] as $key => $value) {
            $categories['unavailable'][] = $value;
            unset($categories['unavailable'][$key]);
        }

        return $categories;
    }

    /**
     * @inheritdoc
     */
    public function getAll(Application $app, $withPagination = true, $decodeFilter = true)
    {
        $filter = $app['request']->get('filter', []);

        $showAll = (int) ($app['request']->get('showAll', true));

        if (isset($filter['reps'])) {
            $reps = explode(",", $filter['reps']);

            $categories = $this->getRepsCategories($app, $reps);
        } else {
            $categories = parent::getAll($app);
        }

        if (!$showAll) {
            if (isset($categories['available']) && isset($categories['unavailable'])) {
                $categories['available'] = array_values($this->manager->filterSpecialtiesFromCategories($categories['available']));
                $categories['unavailable'] = array_values($this->manager->filterSpecialtiesFromCategories($categories['unavailable']));
            } else {
                $categories = $this->manager->filterSpecialtiesFromCategories($categories);
            }
        }

        return $categories;
    }

    /**
     * Get corporate task specialties
     * This api is used for frontend to hide or show corporate task specialties section
     * If empty array is returned, specialties section will be hidden.
     *
     * @param Application $app
     * @param bool $withPagination
     * @param bool $decodeFilter
     * @return array
     */
    public function getAllCorporateTaskSpecialties(Application $app, $withPagination = true, $decodeFilter = true): array
    {
        if (!$app['configs']['retailer.specialties.can_select']) {
            return [];
        } else {
            return $this->getAll($app, true, true);
        }
    }

    private function filterSpecialtiesFromCategories($categories, $excluded = [], $allowOnly = [])
    {
        $excludedFilterFunction = function ($category) use ($excluded) {
            // Sometimes we exclude by name, sometimes by id. Depends on the retailer.
            if (isset($category['name']) && in_array($category['name'], $excluded)) {
                return false;
            }

            if (isset($category['category_id']) && in_array($category['category_id'], $excluded)) {
                return false;
            }

            return true;
        };

        $allowOnlyFilterFunction = function ($category) use ($allowOnly) {
            if (!in_array($category['category_id'], $allowOnly)) {
                return false;
            }

            return true;
        };

        if (!empty($excluded)) {
            $filterFunction = $excludedFilterFunction;
        } else {
            $filterFunction = $allowOnlyFilterFunction;
        }

        // Calling array values is necessary to ensure that numeric keys are sequential.
        return array_filter($categories, $filterFunction);
    }

    public function clearCache(Application $app)
    {
        $app['reps.controller']->invalidateCache($app);
        $this->manager->invalidateClassCache();

        return new Response("", 204);
    }
}
