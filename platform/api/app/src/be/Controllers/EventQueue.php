<?php

namespace Salesfloor\API\Controllers;

use Silex\Application;
use Symfony\Component\HttpFoundation\JsonResponse;
use Salesfloor\Services\Queue\SidebarEventQueue as SidebarEventQueueService;

/**
* Event Queue Controller (related to sf_event_log table)
*
* Copyright 2014 - Salesfloor
*/
class EventQueue extends \Salesfloor\Controllers\Base\LegacyController
{
    /**
     * Create an event that will be push to the Queue
     * @param  Application $app Appliction instance
     * @return JsonResponse
     */
    public function create(Application $app)
    {
        $received = $app['request_stack']->getCurrentRequest()->request->all();
        if (!empty($received)) {
            try {
                if (
                    in_array($received['action'], SidebarEventQueueService::SIDEBAR_WIDGET_ACTION) ||
                    in_array($received['action'], SidebarEventQueueService::SIDEBAR_WIDGET_CONNECT2_ACTION) ||
                    in_array($received['action'], SidebarEventQueueService::CONTEXTUAL_WIDGET_ACTION)
                ) {
                    $app['service.sidebar.event.queue']->push($received);
                } else {
                    $app['sf.event.queue']->push($received);
                }
            } catch (\Exception $pushingEventException) {
                return $app->abort(500, $pushingEventException->getMessage());
            }
        }
        return $app->json('Success', 200);
    }
}
