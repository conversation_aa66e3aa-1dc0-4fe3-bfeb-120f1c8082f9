<?php

declare(strict_types=1);

namespace Salesfloor\API\Controllers\Analytics\V1;

use Salesfloor\API\Exceptions\Security\AuthorizationException;
use Salesfloor\API\Managers\AnalyticsEvents as AnalyticsEventsManager;
use Salesfloor\Controllers\Base\JsonApiController;
use Salesfloor\Models\AnalyticsEvents as AnalyticsEventsModel;
use Salesfloor\Services\Util;
use Symfony\Component\HttpFoundation\JsonResponse;
use Salesfloor\Services\JsonApi\JsonApiResource;
use Silex\Application;

/**
 * Class AnalyticsEvents
 *
 * @property AnalyticsEventsManager
 * @package Salesfloor\API\Controllers\AnalyticsEvents
 */
class AnalyticsEvents extends JsonApiController
{
    /**
     * Create a new analytics event - always returns 204 status
     * @todo: Improve with use of queue when use case raises significant performance bottlenecks
     *
     * @param Application $app
     * @return JsonResponse
     */
    public function create(Application $app): JsonResponse
    {
        try {
            /** @var JsonApiResource $jsonApiResource */
            $jsonApiResource = $app['json-api.resource'];
            $data = $jsonApiResource->getAttributes();

            $model = new AnalyticsEventsModel();

            $enrichedData = $this->enrichEventData($data, $app);

            foreach ($enrichedData as $key => $value) {
                if (property_exists($model, $key)) {
                    $model->$key = $value;
                }
            }

            if (empty($model->dt)) {
                $model->dt = gmdate('Y-m-d H:i:s');
            }

            if (empty($model->event)) {
                throw new \InvalidArgumentException('Event name is required');
            }

            if (!$this->isGranted('create', $app, $model)) {
                throw new AuthorizationException();
            }

            $this->manager->save($model);
        } catch (\Throwable $e) {
            // Log all errors but don't break the response
            $app['logger']->error('Analytics event creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $data ?? null,
                'user_id' => $this->getUserId($app),
                'ip' => $app['request']->getClientIp(),
                'user_agent' => $app['request']->headers->get('User-Agent')
            ]);
        }

        // Always return 204 No Content as per requirements
        return $app->json([], 204);
    }

    /**
     * Enrich event data with environmental information
     *
     * @param array $data
     * @param Application $app
     * @return array
     */
    private function enrichEventData(array $data, Application $app): array
    {
        $enriched = $data;
        $request = $app['request'];

        try {
            $userId = $this->getUserId($app);
            if ($userId) {
                $enriched['user_id'] = $userId;
            }
        } catch (\Exception $e) {
            // User might not be logged in, continue without user data
        }


        $enriched['ip_address'] = $request->getClientIp();
        $userAgent = $request->headers->get('User-Agent');
        $enriched['user_agent'] = $userAgent;

        $userAgentInfo = Util::parseUserAgent($userAgent ?? '');
        $enriched = array_merge($enriched, $userAgentInfo);


        if ($request->headers->has('sf-app-version')) {
            $enriched['app_version'] = $request->headers->get('sf-app-version');
        }

        $referer = $request->headers->get('Referer');
        $eventDataFields = [];
        if ($referer) {
            $eventDataFields['referer'] = $referer;
        }

        if (isset($enriched['event_data'])) {
            $eventDataFields = array_merge($eventDataFields, $enriched['event_data']);
        }

        $enriched['event_data'] = json_encode($eventDataFields);
        foreach (array_keys($eventDataFields) as $field) {
            if ($field !== 'referer') { // referer was never in $enriched
                unset($enriched[$field]);
            }
        }

        return $enriched;
    }
}
