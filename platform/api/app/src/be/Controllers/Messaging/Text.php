<?php

namespace Salesfloor\API\Controllers\Messaging;

use Silex\Application;
use Salesfloor\Services\Event;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\Shortener;
use Salesfloor\Services\GroupTasks;
use Salesfloor\Services\Tasks\Tasks;
use Salesfloor\Exceptions\GlobalException;
use Salesfloor\Models\Messaging\Text\Thread;
use Symfony\Component\HttpFoundation\Response;
use Salesfloor\Services\Messaging\Text\Channel;
use Salesfloor\Models\Messaging\Text\Attachment;
use Salesfloor\Controllers\Base\LegacyController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Salesfloor\API\Managers\Messaging\Text\Threads;
use Salesfloor\API\Managers\ProductRecommendations;
use Salesfloor\Services\Obfuscation\PIIObfuscation;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Salesfloor\Services\Messaging\Recipient\RecipientService;
use Salesfloor\API\Exceptions\Security\AuthorizationException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Salesfloor\Services\Messaging\Exceptions\CustomerOptedOutException;
use Salesfloor\Services\Moderation\BaseModeration;
use Salesfloor\Services\Moderation\Exceptions\ModerationFailedException;

/**
 * Class Messaging\Text Controller
 * Controller to be used as entry point for most Messaging Text services
 *
 * @package Salesfloor\API\Controllers
 */
class Text extends LegacyController
{
    const PREFIX_KEY_CACHE_MESSAGE = 'text.message.sent.';

    const SEND_MESSAGE_FAILED = 'Failed';

    /**
     * Handle Text SMS Request (inbound messages) (POST)
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     * @return mixed                 Response by given text provider
     */
    public function handleSmsRequest(Application $app, $provider)
    {
        try {
            /** @var Channel $serviceMessagingText */
            $serviceMessagingText = $app['service.messaging.text'];
            return $serviceMessagingText->handleSmsRequest($provider, $this->getAllRequestData($app));
        } catch (\Exception $e) {
            return $app->abort(500, $this->translateService->trans('api_error_error') . ': ' . $e->getMessage());
        } catch (\Error $e) {
            return $app->abort(500, $this->translateService->trans('api_error_error') . ': ' . $e->getMessage());
        }
    }

    /**
     * Handle Text SMS NoReply Request (inbound messages) (POST)
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     * @return mixed                 Response by given text provider
     */
    public function handleSmsNoReplyRequest(Application $app, $provider)
    {
        try {
            return $app['service.messaging.text']->handleSmsNoReplyRequest($provider, $this->getAllRequestData($app));
        } catch (\Exception $e) {
            return $app->abort(500, $this->translateService->trans('api_error_error') . ': ' . $e->getMessage());
        }
    }

    /**
     * Handle Fallback from Text SMS Request (inbound messages) (POST)
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleSmsFallbackRequest(Application $app, $provider)
    {
        $app['logger']->debug($app['service.messaging.text']->handleSmsFallbackRequest($provider, $this->getAllRequestData($app)));
        return $app->json('Success', 200);
    }

    /**
     * Handle Status of Text SMS Request (inbound messages) (POST)
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleSmsStatusRequest(Application $app, $provider)
    {
        $app['logger']->debug($app['service.messaging.text']->handleSmsStatusRequest($provider, $this->getAllRequestData($app)));
        return $app->json('Success', 200);
    }

    /**
     * Handle Errors of SMS Request (inbound messages) (POST)
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleError(Application $app, $provider)
    {
        $app['logger']->error('Handle Text Messaging Error ' . json_encode($this->getAllRequestData($app)));
        return $app->json('Success', 200);
    }

    /**
     * @api        {post} messaging/private/text/guess-thread Guess thread id
     * @apiName    GuessThreadId
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     * @apiParam   {string} customer_phone_number Customer phone number
     * @apiExample {json} Example request
     *  {
     *    "customer_phone_number" : "************"
     *  }
     * @apiExample {json} Example response
     *  {
     *    "id":"1",
     *    "user_id":"115",
     *    "user_phone_number":"+12223334444",
     *    "customer_id":"61",
     *    "customer_phone_number":"+19998887777",
     *    "is_read":"1",
     *    "is_valid":"1",
     *    "is_active":"1",
     *    "is_subscribed":"1",
     *    "created_at":"2017-06-22 19:24:23",
     *    "updated_at":"2017-06-27 19:30:13",
     *    "last_message_date":"2017-06-27 20:33:09",
     *    "last_message_body":"Yo!",
     *    "name":"First Last",
     *    "first_name":"First",
     *    "last_name":"Last",
     *    "email":"<EMAIL>"
     *  }
     */
    public function guessThreadIdByPhoneNumber(Application $app)
    {
        $payload = $this->getAllRequestData($app);

        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($this->getLoggedInUserId($app));

        if (isset($payload['customer_id'])) {
            $customerPhoneNumber = $this->getPhoneFromCustomerId($app, $payload);
        } elseif (isset($payload['customer_phone_number'])) {
            // keep compatible with payload that contains only phone_number
            // the new payload will only include the customer_id / retailer_customer_id
            $customerPhoneNumber = preg_replace('/\D/', '', $payload['customer_phone_number']);
            if (strlen($customerPhoneNumber) < 10) {
                return $app->abort(Response::HTTP_BAD_REQUEST, $this->translateService->trans('api_error_invalid_payload_customer_phone_number'));
            }
        } else {
            return $app->abort(Response::HTTP_BAD_REQUEST, $this->translateService->trans('api_error_invalid_payload_customer_phone_number'));
        }

        /** @var Threads $threadManager */
        $threadManager = $app['messaging.text.threads.manager'];

        $threadId = $threadManager->guessThreadIdByPhoneNumber($customerPhoneNumber, $userId);

        if (!$threadId) {
            return $app->abort(Response::HTTP_NOT_FOUND, $this->translateService->trans('api_error_thread_not_found', ['%threadId%' => $threadId]));
        }

        $result = $threadManager->getThreads([$threadId]);
        $result = reset($result);

        $result['customer_phone_number'] = $this->obfuscateValue(
            $app,
            $result['customer_phone_number'],
            PIIObfuscation::OBFUSCATION_TYPE_PHONE
        );

        return $app->json($result);
    }

    /**
     * @api        {get} messaging/private/text/user/:userId/threads Get visible threads for a given user id
     * @apiName    GetThreadByUserId
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     * @apiParam   {Integer} per_page Amount of results per page
     * @apiExample {json} Example response
     *  [
     *    {
     *      "id":"1",
     *      "user_id":"115",
     *      "user_phone_number":"+12223334444",
     *      "customer_id":"61",
     *      "customer_phone_number":"+19998887777",
     *      "is_read":"1",
     *      "is_valid":"1",
     *      "is_active":"1",
     *      "is_subscribed":"1",
     *      "created_at":"2017-06-22 19:24:23",
     *      "updated_at":"2017-06-27 19:30:13",
     *      "last_message_date":"2017-06-27 20:33:09",
     *      "last_message_body":"Yo!",
     *      "name":"First Last",
     *      "first_name":"First",
     *      "last_name":"Last",
     *      "email":"<EMAIL>"
     *    },
     *    {
     *      "id":"2",
     *      "user_id":"115",
     *      "user_phone_number":"+12223334444",
     *      "customer_id":"61",
     *      "customer_phone_number":"+16661112222",
     *      "is_read":"0",
     *      "is_valid":"1",
     *      "is_active":"1",
     *      "is_subscribed":"1",
     *      "created_at":"2017-06-22 19:24:23",
     *      "updated_at":"2017-06-27 19:30:13",
     *      "last_message_date":"2017-06-27 20:33:09",
     *      "last_message_body":"Yo!",
     *      "name":"First Last",
     *      "first_name":"First",
     *      "last_name":"Last",
     *      "email":"<EMAIL>"
     *    },
     *  ]
     */
    public function getUserThreadsView(Application $app, $userId)
    {
        if ($this->getLoggedInUserId($app) != $userId) {
            return $app->abort(403, $this->translateService->trans('api_error_user_text_thread_mismatch'));
        }
        $data = $this->getAllRequestData($app);
        $page = (isset($data['page']) ? $data['page'] : 0);
        $perPage = (isset($data['per_page']) ? $data['per_page'] : 50);
        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($userId);
        $visibleThreads = $app['messaging.text.threads.manager']->getVisibleThreads($userId, $page, $perPage);

        foreach ($visibleThreads as &$thread) {
            $this->obfuscate($app, $thread);
        }

        return $app->json($visibleThreads, 200);
    }

    /**
     * @api        {get} messaging/private/text/user/:userId/threads-count Count unread threads for a given user id
     * @apiName    GetMessages
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     * @apiParam   {integer} per_page Number os results per page
     * @apiExample {curl} Example request
     *  https://retailer.api.dev.salesfloor.net/messaging/private/text/user/115/threads-count?status=unread
     * @apiExample {json} Example response
     *  {
     *    "count" : "1"
     *  }
     */
    public function getUserThreadsCountView(Application $app, $userId)
    {
        if ($this->getLoggedInUserId($app) != $userId) {
            return $app->abort(403, $this->translateService->trans('api_error_user_text_thread_mismatch'));
        }

        $data = $this->getAllRequestData($app);
        $status = (isset($data['status']) ? $data['status'] : null);
        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($userId);
        $count = $app['messaging.text.threads.manager']->getThreadsCount($userId, $status);
        return $app->json(['count' => $count], 200);
    }

    /**
     * @api        {get} messaging/protected/text/user/:userId/threads-count Count unread threads for a given user id
     * @apiName    GetMessages
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     * @apiParam   {integer} per_page Number os results per page
     * @apiExample {curl} Example request
     *  https://retailer.api.dev.salesfloor.net/messaging/private/text/user/115/threads-count?status=unread
     * @apiExample {json} Example response
     *  {
     *    "count" : "1"
     *  }
     */
    public function getUserThreadsCountViewProtected(Application $app, $userId)
    {
        $data = $this->getAllRequestData($app);
        $status = (isset($data['status']) ? $data['status'] : null);
        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($userId);
        $count = $app['messaging.text.threads.manager']->getThreadsCount($userId, $status);
        return $app->json(['count' => $count], 200);
    }

    /**
     * @api            {get} messaging/private/text/thread/:threadId/messages Get messages for a given thread id
     * @apiName        GetMessages
     * @apiDescription Returns the JSON data of the visible messages for a given $threadId.
     *                 Will check that the logged in user owns the given $threadId
     * @apiVersion     0.0.0
     * @apiGroup       TextMessaging
     * @apiParam       {string} status Current version supports "unread" only
     * @apiExample     {curl} Example request
     *  https://retailer.api.dev.salesfloor.net/messaging/private/text/thread/4/messages?per_page=200
     * @apiExample     {json} Example response
     *  {
     *    "id": "4",
     *    "user_id": "115",
     *    "user_phone_number": "+15556667777",
     *    "customer_id": "7",
     *    "customer_phone_number": "+12223334444",
     *    "is_read": "1",
     *    "is_valid": "1",
     *    "is_active": "1",
     *    "is_subscribed":"1",
     *    "created_at": "2017-06-28 20:33:46",
     *    "updated_at": "2017-06-30 15:07:38",
     *    "customer": {
     *      "ID": "7",
     *      "user_id": "115",
     *      "email": "<EMAIL>",
     *      "name": "First Last",
     *      "phone": "************",
     *      "localization": "en",
     *      "geo": "",
     *      "latitude":null,
     *      "longitude":null,
     *      "comment": "",
     *      "subcribtion_flag": "0",
     *      "first_name": "First",
     *      "last_name": "Last",
     *      "note": "",
     *      "created": "2017-06-28 12:56:08",
     *      "last_modified": "2017-06-28 13:28:33",
     *      "type": "personal",
     *      "retailer_customer_id":null,
     *      "label_email":null,
     *      "label_phone":null
     *    },
     *    "text_thread_id": "4",
     *    "messages": {
     *      "67":{
     *        "id": "67",
     *        "text_thread_id": "4",
     *        "provider_message_id": "SM71ab3f4079ad4db3a35g265ac0769380",
     *        "direction": "outbound-api",
     *        "body": "Yo!",
     *        "status": "queued",
     *        "is_active": "1",
     *        "created_at": "2017-06-28 20:33:46",
     *        "updated_at":null,
     *        "text_message_id": "67",
     *        "number_attachments": "0",
     *        "attachments":[]
     *      },
     *      "68":{
     *        "id": "68",
     *        "text_thread_id": "4",
     *        "provider_message_id": "SMf345680483a048f2aaa0b55af18c1a85",
     *        "direction": "inbound",
     *        "body": "Yo!",
     *        "status": "received",
     *        "is_active": "1",
     *        "created_at": "2017-06-28 20:33:54",
     *        "updated_at":null,
     *        "text_message_id": "68",
     *        "number_attachments": "1",
     *        "attachments": [
     *          {
     *            "id": "23",
     *            "text_message_id": "109",
     *            "url": "https:\/\/s3.amazonaws.com\/attachment-image",
     *            "type": "photo",
     *            "resource_id":null,
     *            "name":null,
     *            "link_url":null,
     *            "regular_price":null,
     *            "sale_price":null,
     *            "is_active": "1",
     *            "created_at": "2017-06-29 17:21:57",
     *            "updated_at":null,
     *            "text_attachment_id": "23"
     *          }
     *        ]
     *      }
     *    }
     *  }
     */
    public function getThreadMessagesView(Application $app, $threadId)
    {
        /** @var Threads $threadManager */
        $threadManager = $app['messaging.text.threads.manager'];

        // Sadly, since we have customization of the return value (not json api), i can't do like we usually do.
        // Here i getById() twice, so we can use the same voting system instead of a custom authorization system.

        // i can't use $this->manager, because it's not setup properly in route (╯°□°）╯ ┻━┻
        $thread = $threadManager->getById(($threadId));

        if (!$app['security.authorization_checker']->isGranted('view', $thread)) {
            throw new AuthorizationException();
        }

        try {
            $data = $this->getAllRequestData($app);
            $lastMessageId = (isset($data['last_message_id']) ? $data['last_message_id'] : null);
            $page = (isset($data['page']) ? $data['page'] : 0);
            $perPage = (isset($data['per_page']) ? $data['per_page'] : 50);

            /** @var Channel $textMessagingService */
            $textMessagingService = $app['service.messaging.text'];
            $messages = $textMessagingService->getCompiledMessagesForThread($threadId, $lastMessageId, $page, $perPage);

            // SF-26305
            // add is_sms_blacklisted flag.
            // TODO : this piece of code need to be moved to either parent class or trait as it is duplicated.
            if (!empty($messages)) {
                if (!empty($messages['customer_phone_number'])) {
                    $messages['is_sms_blacklisted'] = $app['sms_block_list.manager']->isBlocked($messages['customer_phone_number'], $messages['user_id']);
                    $messages['customer_phone_supports_text'] = $textMessagingService->checkPhoneNumberSupportsText($messages['customer_phone_number']); // bool|null
                }
                if ($app['configs']['retailer.pii.obfuscate.is_enabled']) {
                    if (!empty($messages['customer'])) {
                        $messages['customer'] = $this->obfuscate($app, $messages['customer']);
                    }
                    $messages['customer_phone_number'] = $app['service.obfuscation.pii']->obfuscate(PIIObfuscation::OBFUSCATION_TYPE_PHONE, $messages['customer_phone_number']);
                }
            }
            return $app->json($messages, 200);
        } catch (\Exception $getMessagesException) {
            $app['logger']->error('Error Getting Messages For Thread ' . $threadId . ' - ' . $getMessagesException->getMessage());
        }
        return $app->abort(500, $this->translateService->trans('api_error_error'));
    }

    /**
     * @api            {post} messaging/private/text/send Send a message
     * @apiName        SendMessage
     * @apiDescription Send a message through the current Text Messaging Provider.
     *                 POSTed fields are used to determine the proper thread and data
     *                 to send in the message.
     *                 Logged in user can only send to their threads
     * @apiVersion     1.0.0
     * @apiGroup       TextMessaging
     * @apiExample     {json} Example: sending text
     *  {
     *    "attachment": [],
     *    "body": "Yo!",
     *    "customer_id": 16,
     *    "customer_phone_number": "+15149967378",
     *    "user_id": 115,
     *    "locale": "en_US"
     *  }
     * @apiExample     {json} Example: attaching a product
     *  {
     *    "attachment": [
     *      {
     *        "type": "product",
     *        "resource_id": "0500087084611",
     *        "url": "http://cdn/image/0500087084611",
     *        "link_url": "http://www.site.com/webapp/super-product-0500087084611",
     *        "name": "Super Product",
     *        "regular_price": 18.5,
     *        "sale_price": 9.99
     *      }
     *    ],
     *    "body": "Yo!",
     *    "customer_id": 16,
     *    "customer_phone_number": "+15149967378",
     *    "user_id": 115,
     *    "locale": "en_US"
     *  }
     * @apiExample     {json} Example: attaching an asset
     *  {
     *    "attachment": [
     *      {
     *        "type": "asset",
     *        "resource_id": "6",
     *        "url": "http://s3.amazon.com/image/asset-6",
     *        "link_url": "http://retailer.dev.salesfloor.net/?sf-library=swjuly2016a",
     *      }
     *    ],
     *    "body": "Yo!",
     *    "customer_id": 16,
     *    "customer_phone_number": "+15149967378",
     *    "user_id": 115,
     *    "locale": "en_US"
     *  }
     * @apiExample     {json} Example response
     *  {
     *    "message": "Success",
     *    "text_message_id": "188",
     *    "text_thread_id": "6"
     *  }
     *
     * =================  this api also support multiple recipients =================
     *
     * @apiExample     {json} Example: sending text to multiple recipients
     *  {
     *    "attachment": [],
     *    "body": "Yo!",
     *    "recipients":[
     *       {
     *           "customer_id": 2,
     *           "customer_phone_number": "+15145610765"
     *       },
     *       {
     *           "customer_id": 2,
     *           "customer_phone_number": "+14383453986"
     *       }
     *    ],
     *    "user_id": 115,
     *    "locale": "en_US"
     *  }
     *
     * @apiExample     {json} Example response
     *  {
     *      "message": "Success",
     *  }
     * @throws \Exception
     */
    public function sendMessage(Application $app)
    {
        $data = $this->getAllRequestData($app);
        // Debug only and should be removed when ticket is done.
        // https://salesfloor.atlassian.net/browse/AC-217
        $this->debugAc217($app, $data);

        /** @var Multilang $multilang */
        $multilang = $app['service.multilang'];
        $requestLocale = isset($data['locale']) ? $data['locale'] : null;
        $locale = $multilang->sanitizeLocale($requestLocale);

        // Ensure that user_id being used to send is the logged in user
        $userId = $this->getLoggedInUserId($app);
        try {
            $parts = $this->getBodyAndAttachmentsFromData($app, $data);
        } catch (\Exception $e) {
            return $app->json($e->getMessage());
        }

        if ($this->configs['content.text.twilio_blocklist.is_enabled']) {
            $shouldBlock = false;

            if ($this->isModeratorFlagged($app, $parts['body'])) {
                $shouldBlock = true;
            }

            foreach ($data['attachment'] as $attachment) {
                $type = $attachment['type'] ?? null;

                if ($type == Attachment::TYPE_PRODUCT) {
                    if (
                        $this->isModeratorFlagged($app, $attachment['name'])
                        || $this->isModeratorFlagged($app, $attachment['link_url'])
                        || $this->isModeratorFlagged($app, $attachment['url'])
                    ) {
                        $shouldBlock = true;
                    }
                }
            }

            if ($shouldBlock) {
                $exception = new ModerationFailedException($this->translateService->trans('api_error_invalid_contain_block_word'));
                $app['logger']->error($exception);
                return $app->json(
                    [
                        'message' => self::SEND_MESSAGE_FAILED,
                        'code'    => Response::HTTP_NOT_ACCEPTABLE,
                        'details' => $exception->getMessage(),
                        // can't return 'text_thread_id' which not exist if new message
                    ],
                    Response::HTTP_NOT_ACCEPTABLE
                );
            }
        }

        $groupedProductsOn = $data['groupedProductsOn'] ?? false;

        /** @var RecipientService $recipientService */
        $recipientService = $app['service.messaging.recipient'];
        if (!empty($data['recipients'])) {
            $to = $recipientService->getRecipients($data);
        } else {
            // old api still send string as phone number
            $to = $recipientService->getRecipients([
                'recipients' => [
                    [
                        'customer_phone_number' => $data['customer_phone_number'],
                        'customer_id'           => (!empty($data['customer_id']) ? $data['customer_id'] : null),
                    ]
                ]
            ]);
        }

        // // If any contact is unsubscribed in the list , we'll need to filter them out.
        // $to = $this->filterUnsubscribedContacts($app, $to);

        // //some different customers might have same customer_phone_number, will remove duplicated phone
        // $to = Util::uniqueValuesByKey($to, 'customer_phone_number');

        /**
         * For one to many SMS, we'll track the contact's matching primary/secondary retailer customers' last timestamp
         * of received message, but we don't drop the contact if the contact's matched retailer customer has received
         * message during the blackout period(default is the last 7 days if blackout feature is enabled)
         *
         */
        // if (count($to) > 1) {
        //     /** @var RetailerCustomerNotificationBlackout $retailerCustomerBlackoutService */
        //     $retailerCustomerBlackoutService = $app['service.retailer.customer.blackout'];
        //     list($to, $blackoutRecipients) = $retailerCustomerBlackoutService->processBlackout(
        //         $to,
        //         (int)$userId,
        //         RetailerCustomerCommunication::SOURCE_TEXT
        //     );
        // }

        if (count($parts['attachments'])) {
            try {
                // PP-428 - Ensure attachments apply the correct locale.
                // This is applied to attachments as the rest of the data is localized by the client.
                // In the future, once we have customer level preferred locales, we'll need to account for that as well.
                foreach ($parts['attachments'] as &$attachment) {
                    $attachment['locale'] = $locale;
                }
                $parts['attachments'] = $this->validateAndSanitizePayloadAttachments(
                    $app,
                    $parts['attachments'],
                    $groupedProductsOn
                );
            } catch (HttpException $e) {
                $app->abort($e->getCode(), $e->getMessage());
            } catch (\Exception $e) {
                $app->abort(
                    Response::HTTP_INTERNAL_SERVER_ERROR,
                    $this->translateService->trans('api_error_internal_server_error')
                );
            }
        }

        // Check if phone numbers can accept text
        // remove invalid numbers from recipients list
        $originalRecipientCount = count($to);
        list($to, $invalidRecipientCount) = $this->filterRecipientsWhoCannotReceiveText($app, $to);
        $filteredRecipientCount = count($to);

        // Let the user know if any numbers were invalid
        $invalidNumbersMessage = null;
        if ($filteredRecipientCount == 0 && $invalidRecipientCount > 0) {
            // If all numbers are removed due to invalid phone type return error message
            $translationKey = ($originalRecipientCount == 1)
                ? 'api_error_user_text_customer_phone_all_invalid_1_to_1'
                : 'api_error_user_text_customer_phone_all_invalid_1_to_many';
            return $app->json(
                [
                    'message' => self::SEND_MESSAGE_FAILED,
                    'code'    => Response::HTTP_NOT_ACCEPTABLE,
                    'details' => $this->translateService->trans($translationKey),
                ],
                Response::HTTP_NOT_ACCEPTABLE
            );
        } elseif ($filteredRecipientCount > 0 && $invalidRecipientCount > 0) {
            // If only some of the numbers were invalid show message on success
            $translationKey = ($invalidRecipientCount == 1)
                ? 'api_error_user_text_customer_phone_partial_invalid_1_to_many_single'
                : 'api_error_user_text_customer_phone_partial_invalid_1_to_many_plural';
            $invalidNumbersMessage = $this->translateService->trans($translationKey, ['%invalid_count%' => $invalidRecipientCount]);
        }

        /** @var Channel $textMessagingService */
        $textMessagingService = $app['service.messaging.text'];

        $params = [
            // User ID Transition to store user when applicable
            'userId'         => $textMessagingService->getUserIdForContext($userId),
            'to'             => $to,
            'body'           => $parts['body'],
            'attachments'    => $parts['attachments'],
            'loggedInUserId' => $userId,
            // Mobile is sending us multiple call when we have attachment instead of only one (Legacy code). So to know
            // when it's the last message (since the backend doesn't know), mobile need to add an extra parameter
            // (is_last_message). If true and it's a "isToMany" we will send an opt-out message too.
            'isToMany'       => count($to) > 1,
            // If message contains body and attachments, two requests will be sent.
            // 'isLastMessage' is false for first one and true for last one.
            'isLastMessage'  => $data['is_last_message'] ?? true,
            'groupTaskId' => $data['group_task_id'] ?? null,
            'taskId' => $data['task_id'] ?? null,
            'invalidNumbersMessage' => $invalidNumbersMessage,
        ];

        if ($groupedProductsOn && $app['service.grouped_products']->isGroupedProductsEnabled()) {
            if (!$app['service.grouped_products']->isGroupedProductsWithinMinMax(count($parts['attachments']))) {
                throw new GlobalException('Invalid number of products');
            }
            $productsSKU = array_column($parts['attachments'], 'sku');
            $groupedProductsLink = $app['service.grouped_products']->getGroupedProductsURL($productsSKU, $userId);

            $params = array_merge($params, [
                'body' => join("\n", [
                    "Styled For You", // Probably to be replaced
                    $groupedProductsLink
                ]),
                'attachments' => [],
                //Used later to track event when the message is dequeued.
                'eventType' => Event::SF_EVENT_STYLED_LINK_SHARED_SMS,
                'eventAttribute' => count($parts['attachments']),
            ]);
        }

        if ($this->isMessageSent($app, $params)) {
            $message = sprintf('sendMessage() gets duplicated message: %s', json_encode($params));
            $app['logger']->error($message);
        }

        if (count($to) <= $this->configs['messaging.text.multiple-recipients.send_directly_limit']) {
            $response = $this->sendMessageDirectly($app, $params);
        } else {
            if (count($to) > $this->configs['messaging.text.multiple-recipients.max']) {
                // Don't need error details info since this only happen
                // when front-end recipients max limit has been bypass
                $app->abort(
                    Response::HTTP_INTERNAL_SERVER_ERROR,
                    $this->translateService->trans('api_error_internal_server_error')
                );
            }
            $response = $this->sendMessageByQueue($app, $params);
        }
        $this->cacheMessageSent($app, $params);

        return $response;
    }

    /**
     * Filter recipients who cannot receive SMS
     * Fetch this data from combo of DB and third party service
     * Save any newly discovered values to DB
     *
     * @param Application $app
     * @param array $to recipients array
     * @return array [new recipients, invalid count]
     */
    protected function filterRecipientsWhoCannotReceiveText(Application $app, array $to): array
    {
        /** @var Channel $textMessagingService */
        $textMessagingService = $app['service.messaging.text'];

        // Get array of unique phone numbers
        $phoneNumbers = [];
        foreach ($to as $recipient) {
            $phoneNumbers[$recipient['customer_phone_number']] = true;
        }
        $phoneNumbers = array_keys($phoneNumbers);

        // Look up if number supports texting
        $newTo = [];
        $invalidRecipientCount = 0;
        $assumeValueWhenUnknown = true;
        $phoneNumberSupportsTexting = $textMessagingService->checkPhoneNumbersSupportsText($phoneNumbers);
        foreach ($to as $recipientKey => $recipient) {
            $numberSupportsText = $phoneNumberSupportsTexting[$recipient['customer_phone_number']] ?? $assumeValueWhenUnknown;

            // remove recipients who don't support
            if (!$numberSupportsText) {
                ++$invalidRecipientCount;
            } else {
                $newTo[$recipientKey] = $recipient;
            }
        }

        return [$newTo, $invalidRecipientCount];
    }

    protected function debugAc217(Application $app, array $data): void
    {
        // Only log 1-1 text message in case log too much info.
        if (!(isset($data['recipients']) && count($data['recipients']) === 1)) {
            return;
        }
        $app['logger']->error('AC217', $data);
    }

    /**
     * If any contact is unsubscribed in the list , we'll need to filter them out.
     *
     * @param Application $app
     * @param $to
     *
     * @return array
     */
    // protected function filterUnsubscribedContacts(Application $app, $to)
    // {
    //     $customerIds = array_filter(array_column($to, 'customer_id'));

    //     /** @var Legacy $customerManager */
    //     $customerManager = $app['customers.manager'];

    //     if (empty($customerIds)) {
    //         return $to;
    //     }

    //     $unsubedCustomers = $customerManager->getAll(['ID' => $customerIds, 'sms_marketing_subscription_flag' => Customer::SMS_SUBSCRIPTION_STATUS_UNSUBSCRIBED], 0, -1, false);

    //     if (empty($unsubedCustomers)) {
    //         return $to;
    //     }

    //     $unsubedIds = [];
    //     foreach ($unsubedCustomers as $customer) {
    //         $unsubedIds[] = $customer->ID;
    //     }

    //     $validContacts = [];
    //     // We have to keep the phone consistent with the data returned from ES.
    //     foreach ($to as $customerData) {
    //         if (!in_array($customerData['customer_id'], $unsubedIds)) {
    //             $validContacts[] = $customerData;
    //         }
    //     }

    //     return $validContacts;
    // }

    /**
     * Send message directly via Twilio
     *
     * @param Application $app
     * @param array       $message
     *
     * @return JsonResponse|void
     */
    private function sendMessageDirectly(Application $app, array $message)
    {
        $toRecipients = $message['to'];
        /** @var  Channel $channel */
        $channel = $app['service.messaging.text'];

        foreach ($toRecipients as $singleRecipient) {
            try {
                $messageSent = $channel->sendBaseMessage(
                    $message['userId'],
                    $singleRecipient,
                    $message['body'],
                    $message['attachments'],
                    $message['loggedInUserId'],
                    $message['isToMany'],
                    $message['isLastMessage']
                );
            } catch (CustomerOptedOutException $exception) {
                $allResults['Failed'][] = [
                    'message' => self::SEND_MESSAGE_FAILED,
                    'code'    => Response::HTTP_FORBIDDEN,
                    'details' => $exception->getMessage()
                ];
                continue;
            }

            if (empty($messageSent)) {
                $allResults['Failed'][] = [
                    'message' => self::SEND_MESSAGE_FAILED,
                    'code'    => Response::HTTP_INTERNAL_SERVER_ERROR,
                    'details' => $this->translateService->trans('api_error_error')
                ];
                continue;
            }

            $this->trackAttachedProducts($app, $message['userId'], $message['attachments']);
            $this->trackGroupedProducts($app, $message);
            $this->trackGroupTask($app, $singleRecipient, $message);

            $allResults['Success'][] = [
                'message'         => 'Success',
                'text_message_id' => $messageSent->id,
                'text_thread_id'  => $messageSent->text_thread_id,
            ];
        }

        if (!empty($allResults['Success'])) {
            if ($message['taskId'] !== null) {
                /** @var Tasks $tasksService */
                $tasksService = $app['service.tasks'];
                $tasksService->autoResolve((int)$message['taskId'], __METHOD__);
            }

            if ($message['groupTaskId'] !== null) {
                /** @var GroupTasks $groupTaskService */
                $groupTaskService = $app['service.group-tasks'];
                $groupTaskService->autoResolve((int)$message['groupTaskId'], (int)$message['userId']);
            }
        }

        if (count($toRecipients) == 1) {
            // mobile front-end api backward compatible
            if (!empty($allResults['Failed'])) {
                $app->abort($allResults['Failed'][0]['code'], $allResults['Failed'][0]['details']);
            } else {
                $response = $allResults['Success'][0];
            }
        } else {
            // front-end doesn't need result details now
            $response = ['message' => 'Success'];
        }

        // add invalid numbers message if exists
        if ($message['invalidNumbersMessage'] !== null) {
            $response['invalid_numbers_message'] = $message['invalidNumbersMessage'];
        }
        return $app->json($response, 200);
    }

    /**
     * Send message content to queue
     *
     * @note sendMessageByQueue should apply only to multiple recipients, since we don't know text_message_id & text_thread_id which keep the text message conversation
     *
     * @param Application $app
     * @param $messages
     * @return mixed
     * @throws \Exception
     */
    private function sendMessageByQueue(Application $app, $messages)
    {
        /** @var  Channel $channel */
        $channel = $app['service.messaging.text'];

        // NOTE: for $messages, only the keys exist in \Salesfloor\Services\Messaging\Text\SendingQueue::$expectedFields will be persist to queue
        $isPushed = $channel->getSendingQueue()->pushMultiple($messages);

        $response = ['message' => 'Success'];

        // add invalid numbers message if exists
        if ($messages['invalidNumbersMessage'] !== null) {
            $response['invalid_numbers_message'] = $messages['invalidNumbersMessage'];
        }

        return $app->json($response, 200);
    }

    /**
     * @api            {post} messaging/protected/text/send/noreply Send a message
     * @apiName        SendMessageNoReply
     * @apiDescription Send a message through the current Text Messaging Provider.
     *                 This message cannot be replied to, and will use the retailer's
     *                 Alphanumeric Sender ID. This should be used for system notifications -
     *                 opt-out information is sent separately, as there's no way to respond
     *                 to these messages.
     *                 The phone number is expected to already be normalized to E.164.
     * @apiVersion     0.0.0
     * @apiGroup       TextMessaging
     * @apiExample     {json} Example: sending text
     *  {
     *    "body": "This is a message from <retailername>. Thanks for your request! You will receive a response shortly.",
     *    "customer_phone_number": "+15149967378"
     *  }
     */
    public function sendMessageNoReply(Application $app)
    {
        $data = $this->getAllRequestData($app);
        /** @var Channel $textService */
        $textService = $app['service.messaging.text'];
        try {
            $providerResponse = $textService->sendBaseRetailerNoReplyMessage(
                ['customer_phone_number' => $data['customer_phone_number']],
                $data['body']
            );
        } catch (CustomerOptedOutException $exception) {
            return $app->abort(Response::HTTP_FORBIDDEN, $exception->getMessage());
        }

        if (empty($providerResponse)) {
            return $app->abort(500, $this->translateService->trans('api_error_error'));
        }

        return $app->json([
            'message' => 'Success',
            'text_message_id' => $providerResponse['providerMessageId'],
        ], 200);
    }

    /**
     * @api            {post} messaging/private/text/thread/:threadId/send Reply to a specific thread
     * @apiName        ReplyToThread
     * @apiDescription Reply to a specific thread through the current Text Messaging Provider.
     *                 POSTed fields are used to determine the data to send in the message.
     *                 Logged in user can only send to their threads
     * @apiVersion     0.0.0
     * @apiGroup       TextMessaging
     * @apiExample     {json} Example: sending text
     *  {
     *    "attachment": [],
     *    "body": "Yo!",
     *    "customer_id": 16,
     *    "customer_phone_number": "+15149967378",
     *    "user_id": 115
     *  }
     * @apiExample     {json} Example: attaching a product
     *  {
     *    "attachment": [
     *      {
     *        "type": "product",
     *        "resource_id": "0500087084611",
     *        "url": "http://cdn/image/0500087084611",
     *        "link_url": "http://www.site.com/webapp/super-product-0500087084611",
     *        "name": "Super Product",
     *        "regular_price": 18.5,
     *        "sale_price": 9.99,
     *      }
     *    ],
     *    "body": "Yo!",
     *    "customer_id": 16,
     *    "customer_phone_number": "+15149967378",
     *    "user_id": 115
     *  }
     * @apiExample     {json} Example: attaching an asset
     *  {
     *    "attachment": [
     *      {
     *        "type": "asset",
     *        "resource_id": "6",
     *        "url": "http://s3.amazon.com/image/asset-6",
     *        "link_url": "http://retailer.dev.salesfloor.net/?sf-library=swjuly2016a",
     *      }
     *    ],
     *    "body": "Yo!",
     *    "customer_id": 16,
     *    "customer_phone_number": "+15149967378",
     *    "user_id": 115
     *  }
     * @apiExample     {json} Example response
     *  {
     *    "message": "Success",
     *    "text_message_id": "188",
     *    "text_thread_id": "6"
     *  }
     */
    public function replyToThread(Application $app, $threadId)
    {
        $thread = $this->getThreadByIdAndLoggedInUser($app, $threadId);
        if (empty($thread)) {
            $app->abort(403, $this->translateService->trans('api_error_user_text_thread_mismatch'));
        }
        $data = $this->getAllRequestData($app);
        try {
            $parts = $this->getBodyAndAttachmentsFromData($app, $data);
        } catch (\Exception $e) {
            return $app->json($e->getMessage());
        }

        $to = [
            'customer_phone_number' => $thread->customer_phone_number,
            'customer_id' => $thread->customer_id,
        ];

        if (count($parts['attachments'])) {
            try {
                $parts['attachments'] = $this->validateAndSanitizePayloadAttachments($app, $parts['attachments']);
            } catch (HttpException $e) {
                return $app->abort($e->getCode(), $e->getMessage());
            } catch (\Exception $e) {
                return $app->abort(Response::HTTP_INTERNAL_SERVER_ERROR, $this->translateService->trans('api_error_internal_server_error'));
            }
        }

        $message = $app['service.messaging.text']->sendBaseMessage(
            $thread->user_id,
            $to,
            $parts['body'],
            $parts['attachments'],
            $this->getLoggedInUserId($app)
        );
        if (empty($message)) {
            return $app->abort(500, $this->translateService->trans('api_error_error'));
        }
        return $app->json([
            'message' => 'Success',
            'text_message_id' => $message->id,
            'text_thread_id' => $message->text_thread_id,
        ], 200);
    }

    /**
     * Returns a simplified array representing the requested body
     * and attachments for a message based on the raw $data
     * @param  array  $data Raw data, usually the request contents
     * @return array        Simplified array
     */
    private function getBodyAndAttachmentsFromData(Application $app, array $data)
    {
        //$body = (!empty($data['body']) ? strip_tags($data['body']) : null);
        $body = (!empty($data['body']) ? $app['service.sanitize']->removeXss($data['body']) : null);
        $attachments = (!empty($data['attachment']) ? $data['attachment'] : []);

        array_filter($attachments);

        $attachmentDetails = [];

        foreach ($attachments as $attachment) {
            if (!empty($attachment)) {
                $attachmentDetails[] = $attachment;
            }
        }

        if (empty($body) && empty($attachments)) {
            throw new \Exception('api_error_no_message_body_or_attachments');
        }

        return ['body' => $body, 'attachments' => $attachmentDetails];
    }

    /**
     * @api        {post} /messaging/private/text/thread/:threadId/read Update one thread read status
     * @apiName    UpdateThreadReadStatus
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     *
     * @apiParam {Boolean} is_read New status value
     * @apiExample {json} Example request
     *  {
     *      "is_read" : true
     *  }
     * @apiExample {json} Example response
     *  {
     *      "id": 1,
     *      "user_id":  115,
     *      "user_phone_number": "+1231231231",
     *      "customer_id": 61,
     *      "customer_phone_number": "+9879879879",
     *      "is_read": true,
     *      "is_valid": true,
     *      "is_active": true,
     *      "is_subscribed": true,
     *      "created_at": "2017-06-14 15:03:48",
     *      "updated_at": "2017-06-15 22:27:49",
     *      "last_message_date": "2017-06-15 22:27:49",
     *      "last_message_body": "Yo!",
     *      "name": "FirstName LastName",
     *      "first_name": "FirstName",
     *      "last_name": "LastName",
     *      "email": "<EMAIL>"
     *  }
     */
    public function updateOneThreadReadStatus(Application $app, $threadId)
    {
        $results = $this->updateOneThreadStatus($app, $threadId, Thread::ATTR_IS_READ);

        return $app->json($results, Response::HTTP_ACCEPTED);
    }

    /**
     * @api        {post} /messaging/private/text/thread/active Update several threads read status
     * @apiName    UpdateManyThreadReadStatus
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     *
     * @apiParam {Number} id Thread primary key
     * @apiParam {Boolean} is_read New status value
     * @apiExample {json} Example request
     *  [
     *      {
     *          "id":  1,
     *          "is_read": true
     *      },
     *      {
     *          "id": 2,
     *          "is_read": true
     *      },
     *      {
     *          "id": 3",
     *          "is_read": true
     *      }
     *  ]
     * @apiExample {json} Example response
     *  [
     *      {
     *          "id":  1,
     *          "user_id":  115,
     *          "user_phone_number": "+1231231231",
     *          "customer_id": 61,
     *          "customer_phone_number": "+9879879879",
     *          "is_read": true,
     *          "is_valid": true,
     *          "is_active": true,
     *          "is_subscribed": true,
     *          "created_at": "2017-06-14 15:03:48",
     *          "updated_at": "2017-06-15 22:27:49",
     *          "last_message_date": "2017-06-15 22:27:49",
     *          "last_message_body": "Yo!",
     *          "name": "FirstName LastName",
     *          "first_name": "FirstName",
     *          "last_name": "LastName",
     *          "email": "<EMAIL>"
     *      },
     *      {
     *          "id": 2,
     *          "is_read": true,
     *          "code": 401,
     *          "message": "Thread does not belong to you"
     *      },
     *      {
     *          "id": 3,
     *          "is_read": true,
     *          "code": 500,
     *          "message": "Internal server error"
     *      }
     *  ]
     */
    public function updateManyThreadsReadStatus(Application $app)
    {
        $results = $this->updateManyThreadsStatus($app, Thread::ATTR_IS_READ);

        return $app->json($results, Response::HTTP_MULTI_STATUS);
    }

    /**
     * @api        {post} /messaging/private/text/thread/:threadId/active Update one thread active status
     * @apiName    UpdateThreadActiveStatus
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     *
     * @apiParam {Boolean} is_active New status value
     * @apiExample {json} Example request
     *  {
     *      "is_active" : false
     *  }
     * @apiExample {json} Example response
     *  {
     *      "id":  1,
     *      "user_id":  115,
     *      "user_phone_number": "+1231231231",
     *      "customer_id": 61,
     *      "customer_phone_number": "+9879879879",
     *      "is_read": false,
     *      "is_valid": true,
     *      "is_active": false,
     *      "is_subscribed": true,
     *      "created_at": "2017-06-14 15:03:48",
     *      "updated_at": "2017-06-15 22:27:49",
     *      "last_message_date": "2017-06-15 22:27:49",
     *      "last_message_body": "Yo!",
     *      "name": "FirstName LastName",
     *      "first_name": "FirstName",
     *      "last_name": "LastName",
     *      "email": "<EMAIL>"
     *  }
     */
    public function updateOneThreadActiveStatus(Application $app, $threadId)
    {
        $results = $this->updateOneThreadStatus($app, $threadId, Thread::ATTR_IS_ACTIVE);

        return $app->json($results, Response::HTTP_ACCEPTED);
    }

    /**
     * @api        {post} /messaging/private/text/thread/active Update several threads active status
     * @apiName    UpdateManyThreadActiveStatus
     * @apiVersion 0.0.0
     * @apiGroup   TextMessaging
     *
     * @apiParam {Number} id Thread primary key
     * @apiParam {Boolean} is_active New status value
     * @apiExample {json} Example request
     *  [
     *      {
     *          "id":  1,
     *          "is_active": false
     *      },
     *      {
     *          "id": 2,
     *          "is_active": false
     *      },
     *      {
     *          "id": 3,
     *          "is_active": false
     *      }
     *  ]
     * @apiExample {json} Example response
     *  [
     *      {
     *          "id":  1,
     *          "user_id":  115,
     *          "user_phone_number": "+1231231231",
     *          "customer_id": 61,
     *          "customer_phone_number": "+9879879879",
     *          "is_read": true,
     *          "is_valid": true,
     *          "is_active": false,
     *          "is_subscribed": true,
     *          "created_at": "2017-06-14 15:03:48",
     *          "updated_at": "2017-06-15 22:27:49",
     *          "last_message_date": "2017-06-15 22:27:49",
     *          "last_message_body": "Yo!",
     *          "name": "FirstName LastName",
     *          "first_name": "FirstName",
     *          "last_name": "LastName",
     *          "email": "<EMAIL>"
     *      },
     *      {
     *          "id": 2,
     *          "is_active": true,
     *          "code": 401,
     *          "message": "Thread does not belong to you"
     *      },
     *      {
     *          "id": 3,
     *          "is_active": true,
     *          "code": 500,
     *          "message": "Internal server error"
     *      }
     *  ]
     */
    public function updateManyThreadsActiveStatus(Application $app)
    {
        $results = $this->updateManyThreadsStatus($app, Thread::ATTR_IS_ACTIVE);

        return $app->json($results, Response::HTTP_MULTI_STATUS);
    }

    /**
     * Update several thread at one shot. The payload must have a collection of resources.
     *
     * @param Application $app
     * @param string      $statusKey Status attribute name, i.e: `is_read`, `is_active`, `is_valid`
     *
     * @return array
     */
    private function updateManyThreadsStatus(Application $app, $statusKey)
    {
        $payload = $this->getAllRequestData($app);

        $isAssociativeArray = array_keys($payload) !== range(0, count($payload) - 1);

        if (!$payload || $isAssociativeArray) {
            return $app->abort(Response::HTTP_BAD_REQUEST, $this->translateService->trans('api_error_payload_must_have_collection'));
        }

        list($validPayloadResources, $invalidPayloadResources) = $this->validatePayloadResources($app, $statusKey, $payload);

        // we will make two massive update transactions
        $markStatusTrueResourceIds = [];
        $markStatusFalseResourceIds = [];

        foreach ($validPayloadResources as $payloadResource) {
            if ($payloadResource[$statusKey]) {
                $markStatusTrueResourceIds[] = $payloadResource['id'];
            } else {
                $markStatusFalseResourceIds[] = $payloadResource['id'];
            }
        }

        $updatedObjects = [];

        try {
            // update to TRUE
            $updatedObjects += $this->handleUpdateManyThreadStatus($app, $markStatusTrueResourceIds, $statusKey, true);
        } catch (\Exception $exception) {
            $threadIds = implode(',', $markStatusTrueResourceIds);
            $app['logger']->error("Error updating `{$statusKey}` to TRUE, threads: {$threadIds} - {$exception->getMessage()}");
        }

        try {
            // update to FALSE
            $updatedObjects += $this->handleUpdateManyThreadStatus($app, $markStatusFalseResourceIds, $statusKey, false);
        } catch (\Exception $exception) {
            $threadIds = implode(',', $markStatusFalseResourceIds);
            $app['logger']->error("Error updating `{$statusKey}` to FALSE, threads: {$threadIds} - {$exception->getMessage()}");
        }

        // build the response following the same order from request

        $response = [];

        foreach ($payload as $payloadResource) {
            $payloadResourceId = $payloadResource['id'];

            // object got updated
            // apply mapper
            if ($validPayloadResources[$payloadResourceId] && $updatedObjects[$payloadResourceId]) {
                /** @var Thread $object */
                $object = $updatedObjects[$payloadResourceId];
                $response[] = $this->returnModel($object);
                continue;
            }

            // object did not get updated
            // return it back the way we got it from request payload
            if ($invalidPayloadResources[$payloadResourceId]) {
                $response[] = $invalidPayloadResources[$payloadResourceId];
                continue;
            }

            // something went wrong...
            if (!isset($payloadResource['code'])) {
                $payloadResource['code'] = Response::HTTP_INTERNAL_SERVER_ERROR;
                $payloadResource['error'] = 'Internal server error';
            }

            $response[] = $payloadResource;
        }

        return $response;
    }

    /**
     * Update a given status from a thread.
     *
     * @param Application $app
     * @param int|string  $threadId
     * @param string      $statusKey Attribute name, i.e.: `is_read`, `is_active`, `is_valid`
     *
     * @return array
     */
    protected function updateOneThreadStatus(Application $app, $threadId, $statusKey)
    {
        $payloadResource = $this->getAllRequestData($app);

        $isAssociativeArray = array_keys($payloadResource) !== range(0, count($payloadResource) - 1);

        if (!$payloadResource || !$isAssociativeArray) {
            return $app->abort(Response::HTTP_BAD_REQUEST, $this->translateService->trans('api_error_payload_must_have_single'));
        }

        // override the payloadResource id in order to validate the same way
        // is done for bulk endpoint
        $payloadResource['id'] = $threadId;

        list($validPayloadResources, $invalidPayloadResources) = $this->validatePayloadResources($app, $statusKey, [$payloadResource]);

        if (count($invalidPayloadResources)) {
            $payloadResource = reset($invalidPayloadResources);
            return $app->abort($payloadResource['code'], $payloadResource['error']);
        }

        $payloadResource = reset($validPayloadResources);
        $updatedObjects = $this->handleUpdateManyThreadStatus($app, [$threadId], $statusKey, $payloadResource[$statusKey]);

        // get the first object
        $updatedObject = reset($updatedObjects);

        // apply mapper
        return $this->returnModel($updatedObject);
    }

    /**
     * It will separate the payload resources in 4 categories:
     *  - $validPayloadResources     These resources have a valid data
     *  - $invalidPayloadResources   These resources have an invalid data
     *
     * @param Application $app
     * @param string      $statusKey Status key, i.e.: `is_read`, `is_active`, `is_valid`
     * @param array       $payload   Current payload
     *
     * @return array $validPayloadResources, $invalidPayloadResources, $markStatusTrueResourceIds, $markStatusTrueResourceIds
     */
    protected function validatePayloadResources(Application $app, $statusKey, $payload)
    {
        $validPayloadResources = [];
        $invalidPayloadResources = [];

        // split which rows will be updated to TRUE or FALSE
        foreach ($payload as $payloadResource) {
            try {
                $payloadResource = $this->validatePayloadResourceId($app, $payloadResource);
                $payloadResource = $this->validatePayloadResourceStatus($app, $statusKey, $payloadResource);
                $validPayloadResources[$payloadResource['id']] = $payloadResource;
            } catch (\Exception $exception) {
                $payloadResource['code'] = $exception->getCode();
                $payloadResource['error'] = $exception->getMessage();
                $invalidPayloadResources[$payloadResource['id']] = $payloadResource;
            }
        }

        return [$validPayloadResources, $invalidPayloadResources];
    }

    /**
     * @param Application $app
     * @param             $payloadAttachments
     *
     * @return array
     * @throws BadRequestHttpException
     * @throws HttpException
     * @throws \Exception
     */
    protected function validateAndSanitizePayloadAttachments(Application $app, $payloadAttachments, $groupedProductsOn = false)
    {
        if (count($payloadAttachments) > 1 && $groupedProductsOn === false) {
            throw new BadRequestHttpException(
                'api_error_limit_1_attachment',
                null,
                Response::HTTP_BAD_REQUEST
            );
        }

        foreach ($payloadAttachments as $idx => $payloadAttachment) {
            $payloadAttachments[$idx] = $this->validateAndSanitizePayloadAttachment($app, $payloadAttachment);
        }

        return $payloadAttachments;
    }

    /**
     * @param Application $app
     * @param             $payloadAttachment
     *
     * @return mixed
     * @throws HttpException
     */
    protected function validateAndSanitizePayloadAttachment(Application $app, $payloadAttachment)
    {
        $type = isset($payloadAttachment['type']) ? $payloadAttachment['type'] : null;
        $url = isset($payloadAttachment['url']) ? $payloadAttachment['url'] : null;
        $resourceId = isset($payloadAttachment['resource_id']) ? $payloadAttachment['resource_id'] : null;

        /** @var Shortener $shortenerService */
        $shortenerService = $app['service.shortener'];

        if (!$type || !in_array($type, Attachment::getTypes())) {
            $types = implode(', ', Attachment::getTypes());
            throw new BadRequestHttpException(
                $this->translateService->trans('api_error_attribute_type_error', ['%types%' => $types]),
                null,
                Response::HTTP_BAD_REQUEST
            );
        }

        if (!$url || !filter_var($url, FILTER_VALIDATE_URL)) {
            throw new BadRequestHttpException(
                'api_error_attribute_url_error',
                null,
                Response::HTTP_BAD_REQUEST
            );
        }

        if ($resourceId && !filter_var($resourceId, FILTER_SANITIZE_STRING)) {
            throw new BadRequestHttpException(
                'api_error_attribute_resource_id_error',
                null,
                Response::HTTP_BAD_REQUEST
            );
        }

        if (in_array($type, [Attachment::TYPE_PRODUCT, Attachment::TYPE_PRODUCT_VARIANT])) {
            $name         = isset($payloadAttachment['name']) ? $payloadAttachment['name'] : null;
            $regularPrice = isset($payloadAttachment['regular_price']) ? $payloadAttachment['regular_price'] : null;
            $salePrice    = isset($payloadAttachment['sale_price']) ? $payloadAttachment['sale_price'] : null;
            $linkUrl      = isset($payloadAttachment['link_url']) ? $payloadAttachment['link_url'] : null;
            $locale      = isset($payloadAttachment['locale']) ? $payloadAttachment['locale'] : null;

            // SF-18232 The variable $linkUrl is most probably getting production urls once
            //          1) the mobile app consumes Algolia data directly,
            //          2) our Algolia does not support different environments at this point in time
            //          Retrieving the product url from database guarantees that we fetch the
            //          environmental url (pointing to the retailer's qa environment, for instance)

            if ($type === Attachment::TYPE_PRODUCT) {
                $product = $app['products.manager']->getOneOrNull(['product_id' => $resourceId]);

                if (isset($product->productUrl) && $product->productUrl) {
                    $linkUrl = $product->productUrl;
                }
            } elseif ($type === Attachment::TYPE_PRODUCT_VARIANT) {
                $product = $app['product_variants.manager']->getOneOrNull(['sku' => $resourceId]);

                if (isset($product->product_url) && $product->product_url) {
                    $linkUrl = $product->product_url;
                }
            }

            if (!$app['service.multilang']->isValidRetailerLocale($locale)) {
                throw new BadRequestHttpException(
                    'api_error_attribute_locale_error',
                    null,
                    Response::HTTP_BAD_REQUEST
                );
            }

            if (!$name || !filter_var($url, FILTER_SANITIZE_STRING)) {
                throw new BadRequestHttpException(
                    'api_error_attribute_name_error',
                    null,
                    Response::HTTP_BAD_REQUEST
                );
            }

            if (!$regularPrice || !filter_var($regularPrice, FILTER_VALIDATE_FLOAT)) {
                throw new BadRequestHttpException(
                    'api_error_attribute_regular_price_error',
                    null,
                    Response::HTTP_BAD_REQUEST
                );
            }

            if ($salePrice && !filter_var($salePrice, FILTER_VALIDATE_FLOAT)) {
                throw new BadRequestHttpException(
                    'api_error_attribute_sale_price_error',
                    null,
                    Response::HTTP_BAD_REQUEST
                );
            }

            if (!$linkUrl || !filter_var($linkUrl, FILTER_VALIDATE_URL)) {
                throw new BadRequestHttpException(
                    'api_error_attribute_link_url_error',
                    null,
                    Response::HTTP_BAD_REQUEST
                );
            } else {
                $payloadAttachment['link_url'] = $shortenerService->shrink(
                    // User ID Transition to store user when applicable
                    $app['service.messaging.text']->getUserIdForContext($this->getLoggedInUserId($app)),
                    $linkUrl,
                    [
                        'track_click' => ['SERVER_CLICK_TEXT_PRODUCT', 'SF_EVENT_CLICK_TEXT_PRODUCT'],
                        'source'      => ['mobile'],
                        'product_id'  => $resourceId,
                    ]
                );
            }
        }

        // asset type validation
        if ($type == Attachment::TYPE_ASSET) {
            $linkUrl = isset($payloadAttachment['link_url']) ? $payloadAttachment['link_url'] : null;
            $assetLocale = isset($payloadAttachment['locale']) ? $payloadAttachment['locale'] : null;

            if (!$linkUrl || !filter_var($url, FILTER_VALIDATE_URL)) {
                throw new BadRequestHttpException(
                    'api_error_attribute_link_url_error',
                    null,
                    Response::HTTP_BAD_REQUEST
                );
            } else {
                $extraQueryParams = [
                    'track_click' => ['SERVER_CLICK_TEXT_ASSET'],
                ];

                if (!empty($assetLocale)) {
                    $extraQueryParams['template_locale'] = [$assetLocale];
                }

                $payloadAttachment['link_url'] = $shortenerService->shrink(
                    // User ID Transition to store user when applicable
                    $app['service.messaging.text']->getUserIdForContext($this->getLoggedInUserId($app)),
                    $linkUrl,
                    $extraQueryParams
                );
            }
        }

        return $payloadAttachment;
    }

    /**
     * Handle the process of updating several threads and their messages at same time.
     *
     * @param Application $app
     * @param             $threadIds
     * @param             $statusKey
     * @param             $newStatusValue
     *
     * @return array Updated objects
     */
    protected function handleUpdateManyThreadStatus(Application $app, $threadIds, $statusKey, $newStatusValue)
    {
        if (!count($threadIds)) {
            return [];
        }

        /** @var Threads $threadManager */
        $threadManager = $app['messaging.text.threads.manager'];

        // update status
        $threadManager->updateManyStatus($threadIds, $statusKey, $newStatusValue);

        // retrieve updated objects
        $updatedObjects = $threadManager->getThreads($threadIds, true);

        $result = [];

        foreach ($updatedObjects as $updatedObject) {
            $result[$updatedObject->id] = $updatedObject;
        }

        return $result;
    }

    /**
     * Validate payload resource's attribute `id`.
     *
     * @param Application $app
     * @param             $payloadResource
     *
     * @return array Parsed payload resource data
     * @throws BadRequestHttpException
     */
    protected function validatePayloadResourceId(Application $app, $payloadResource)
    {
        $threadId = isset($payloadResource['id']) ? $payloadResource['id'] : null;

        if (!$threadId) {
            throw new BadRequestHttpException(
                'api_error_attribute_id_error',
                null,
                Response::HTTP_BAD_REQUEST
            );
        }

        if (!$this->getThreadByIdAndLoggedInUser($app, $threadId)) {
            throw new BadRequestHttpException(
                'api_error_resource_not_belong_to_you',
                null,
                Response::HTTP_FORBIDDEN
            );
        }

        return $payloadResource;
    }

    /**
     * Validate payload resource's status, i.e.: `is_read`, `is_active`, `is_valid`
     *
     * @param Application $app
     * @param string      $statusKey
     * @param array       $payloadResource
     *
     * @return array Parsed payload resource data
     * @throws BadRequestHttpException
     */
    protected function validatePayloadResourceStatus(Application $app, $statusKey, $payloadResource)
    {
        $payloadResourceStatus = strtolower($payloadResource[$statusKey]);

        $isActive = null;

        if (is_null($payloadResourceStatus)) {
            // do nothing
        } elseif (is_bool($payloadResourceStatus)) {
            $isActive = $payloadResourceStatus;
        } elseif (in_array($payloadResourceStatus, [1, 'true', '1', 'yes', 'on'])) {
            $isActive = true;
        } elseif (in_array($payloadResourceStatus, [0, 'false', '0', 'no', 'off'])) {
            $isActive = false;
        }

        if (is_null($isActive)) {
            throw new BadRequestHttpException(
                $this->translateService->trans('api_error_attribute_status_key_error', ['%statusKey%' => $statusKey]),
                null,
                Response::HTTP_BAD_REQUEST
            );
        } else {
            $payloadResource[$statusKey] = $isActive;
        }

        return $payloadResource;
    }

    /**
     * Handle Voice Request from Text Provider (inbound call) (POST)
     * Will call function to playback basic message to caller
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleVoiceRequest(Application $app, $provider)
    {
        return $app['service.messaging.text']->handleVoiceRequest($provider, $this->getAllRequestData($app));
    }

    /**
     * Handle No reply Voice Request from Text Provider (inbound call) (POST)
     * Will call function to playback basic message to caller
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleVoiceNoReplyRequest(Application $app, $provider)
    {
        return $app['service.messaging.text']->handleVoiceNoReplyRequest($provider, $this->getAllRequestData($app));
    }

    /**
     * Handle Voice Request Fallback from Text Provider (inbound call) (POST)
     * Return 200 status Success message
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleVoiceFallbackRequest(Application $app, $provider)
    {
        $app['logger']->debug(__FUNCTION__ . json_encode($this->getAllRequestData($app)));
        return $app->json('Success', 200);
    }

    /**
     * Handle Voice Request Status from Text Provider (inbound call) (POST)
     * Return 200 status Success message
     *
     * @param  Application $app      Application instance
     * @param  string      $provider Provider to use i.e. 'twilio'
     */
    public function handleVoiceStatusRequest(Application $app, $provider)
    {
        $app['logger']->debug(__FUNCTION__ . json_encode($this->getAllRequestData($app)));
        return $app->json('Success', 200);
    }

    /**
     * Returns the Thread object for the given $threadId
     * Thread must belong to the logged in user
     * Returns null if not found
     * @param  Application $app      Application instance
     * @param  integer     $threadId Thread id to return messages for
     * @return Thread|null
     */
    public function getThreadByIdAndLoggedInUser(Application $app, $threadId)
    {
        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($this->getLoggedInUserId($app));
        return $app['messaging.text.threads.manager']->getOneOrNull([
            'id' => $threadId,
            'user_id' => $userId
        ]);
    }

    /**
     * Enable a given $userId's permission to Text Message
     * If user is not yet enabled for texting, a number will be reserved with
     * the Text Messaging provider and saved into the UserPhoneNumber table
     * @param  Application $app    Application instance
     * @param  integer     $userId User ID to enable for text messaging
     * @return JsonResponse
     */

    /**
     * @api            {post} /messaging/private/text/user/:userId/disable Enable SMS by associate
     * @apiName        EnableUser
     * @apiDescription Enable a given $userId's permission to Text Message.
     *                 If user is not yet enabled for texting, a number will be reserved with
     *                 the Text Messaging provider and saved into the UserPhoneNumber table
     * @apiVersion     0.0.0
     * @apiGroup       TextMessaging
     * @apiExample     {curl} Example request
     *  https://retailer.api.dev.salesfloor.net/messaging/private/text/user/115/enable
     * @apiExample     {json} Example response
     *   {
     *     'Success'
     *   }
     */
    public function enableUser(Application $app, $userId)
    {
        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($userId);
        if (!$this->loggedInUserHasPermission($app, 'set-rep-text-messaging')) {
            $app->abort(403, $this->translateService->trans('api_error_insufficient_permission'));
        } elseif (!$app['service.messaging.text']->userCanBeEnabled($userId)) {
            $app->abort(403, $this->translateService->trans('api_error_invalid_user'));
        }
        $enabled = $app['service.messaging.text']->enableUser($userId, $this->getLoggedInUserId($app));
        $userPhoneNumber = $app['user_phone_numbers.manager']->getOneOrNull(['user_id' => $userId]);
        if (!$enabled || empty($userPhoneNumber)) {
            return $app->abort(500, $this->translateService->trans('api_error_enabling_user_error'));
        }
        return $app->json(
            $userPhoneNumber->toArray(),
            200
        );
    }

    /**
     * @api            {post} /messaging/private/text/user/:userId/disable Disable SMS by associate
     * @apiName        DisableUser
     * @apiDescription Disable a given $userId's permission to Text Message
     *                 If user is enabled for texting, their number will be released by the
     *                 Text Messaging provider and the equivalent UserPhoneNumber will be "deleted"
     * @apiVersion     0.0.0
     * @apiGroup       TextMessaging
     * @apiExample     {curl} Example request
     *  https://retailer.api.dev.salesfloor.net/messaging/private/text/user/115/disable
     * @apiExample     {json} Example response
     *   {
     *     'Success'
     *   }
     */
    public function disableUser(Application $app, $userId)
    {
        // User ID Transition to store user when applicable
        $userId = $app['service.messaging.text']->getUserIdForContext($userId);
        if (!$this->loggedInUserHasPermission($app, 'set-rep-text-messaging')) {
            $app->abort(403, $this->translateService->trans('api_error_insufficient_permission'));
        }

        $disabled = $app['service.messaging.text']->disableUser($userId, $this->getLoggedInUserId($app));
        if (!$disabled) {
            return $app->abort(500, $this->translateService->trans('api_error_disabling_user_error'));
        }
        return $app->json(
            'Success',
            200
        );
    }

    /**
     * Returns a json array with the 'last_message_id' for the given $threadId
     * Queries based on visible threads with visible messages
     * @param  Application  $app      Application instance
     * @param  integer      $threadId Id of the thread to get the last message for
     * @return JsonResponse           Response json string {"last_message_id" : 12} on success, errors otherwise
     */
    public function getThreadLastMessageId(Application $app, $threadId)
    {
        $thread = $this->getThreadByIdAndLoggedInUser($app, $threadId);
        if (empty($thread)) {
            return $app->abort(403, $this->translateService->trans('api_error_user_text_thread_mismatch'));
        }
        try {
            $lastMessageId = $app['messaging.text.threads.manager']->getLastMessageIdForThreadId($thread->id, $thread->user_id);
            return $app->json(['last_message_id' => intval($lastMessageId)], 200);
        } catch (\Exception $getMessagesException) {
            $app['logger']->error('Error Getting Messages For Thread ' . $threadId . ' - ' . $getMessagesException->getMessage());
        }
        return $app->abort(500, $this->translateService->trans('api_error_error'));
    }

    /**
     * 1. Stores an event when rep texts with attached products
     * 2. Increments attached product's recommendation rating
     *
     * @param Application $app
     * @param string|int  $userId
     * @param array       $attachments
     */
    protected function trackAttachedProducts($app, $userId, $attachments)
    {
        if (empty($attachments)) {
            return;
        }

        $productIds = [];
        foreach ($attachments as $attachment) {
            $type = isset($attachment['type']) ? $attachment['type'] : null;

            if ($type == Attachment::TYPE_PRODUCT) {
                $productIds[] = $attachment['resource_id'];
            }
        }

        if (empty($productIds)) {
            return;
        }

        try {
            /** @var ProductRecommendations $manager */
            $manager = $app['product_recommendations.manager'];
            $manager->createTracking([
                'source'            => $app['configs']['origin'],
                'channels'          => [ProductRecommendations::TYPE_TEXT_KEY],
                'user_id'           => $userId,
                'product_id'        => $productIds,
                'n_recommendations' => 1,
            ]);
        } catch (\Exception $exception) {
            $app['logger']->error("Text Messaging error. Tracking product recommendation when texting broke - {$exception->getMessage()}");
        }
    }

    /**
     * Track share by SMS GroupedProducts event
     * The event attribute is the number of products
     *
     * @param Application $app
     * @param array $message
     * @return void
     */

    protected function trackGroupedProducts($app, array $message)
    {
        if (
            !isset($message['eventType'])
            || $message['eventType'] != Event::SF_EVENT_STYLED_LINK_SHARED_SMS
        ) {
            return;
        }

        $app['service.event']->trackEvent(
            'SF_EVENT_STYLED_LINK_SHARED_SMS',
            'grouped_products',
            null,
            $message['userId'],
            null,
            $message['eventAttribute']
        );
    }

    /**
     * Track group task activities
     * The event attribute is the number of products
     *
     * @param Application $app
     * @param array $singleRecipient
     * @param array $message
     * @return void
     */
    protected function trackGroupTask($app, array $singleRecipient, array $message)
    {
        // Generally, we send text message directly only when there's one recipient.
        // But in some cases, we may send text message to multiple recipients
        // It depends on the config 'messaging.text.multiple-recipients.send_directly_limit'
        // Set 'to' to single recipient to ensure it's same as that message when pop from queue.
        $message['to'] = $singleRecipient;
        /** @var GroupTasks groupTaskService */
        $groupTaskService = $app['service.group-tasks'];
        $groupTaskService->trackActivityForTextMessage($message);
    }

    /**
     * Check message is sent or not
     *
     * @return boolean
     */
    protected function isMessageSent($app, array $message): bool
    {
        $key = $this->getMessageCacheKey($message);
        return $app['predis']->exists($key);
    }

    /**
     *  Cache message in redis, default 300s (5m) expired.
     */
    protected function cacheMessageSent($app, array $message, int $ttl = 300): void
    {
        $key = $this->getMessageCacheKey($message);
        $app['predis']->set($key, true, "ex", $ttl);
    }

    protected function getMessageCacheKey(array $message): string
    {
        // All members in array $message have types: string, integer and bool.
        // Since FE sends integer with/without quotes randomly, Even for same payload.
        // So it's important to convert all int to string,
        array_walk_recursive($message, function (&$value) {
            if (is_int($value)) {
                $value = (string)$value;
            }
        });
        return self::PREFIX_KEY_CACHE_MESSAGE . md5(json_encode($message));
    }

    /**
     * Get the phone number from the customer_id / retailer_customer_id
     *
     * @param Application $app
     * @param array $payload
     * @return string | null
     */
    protected function getPhoneFromCustomerId(Application $app, array $payload): ?string
    {
        $customerManager = $app['customers.manager'];
        $retailerCustomerManager = $app['retailer_customers.manager'];

        if (isset($payload['customer_id'])) {
            $customer = $customerManager->getById($payload['customer_id']);
            return $customer ? $customer->phone : null;
        } elseif (isset($payload['retailer_customer_id'])) {
            $retailerCustomer = $retailerCustomerManager->getById($payload['retailer_customer_id']);
            return $retailerCustomer ? $retailerCustomer->phone : null;
        } else {
            return null;
        }
    }

    /**
     * Check if message is valid by checking if it's contain some keyword that should be blocked
     *
     * @param $app
     * @param $content
     * @return bool
     * @throws \Salesfloor\API\Exceptions\Manager\MissingRequiredFieldException
     */
    private function isModeratorFlagged($app, $content)
    {
        /** @var \Salesfloor\Services\Moderation\TextModeration $moderationService */
        $moderationService = $app['service.moderation.text_simple'];

        $moderationModel = $moderationService->moderate(
            strtolower($content),
            (int)$this->getLoggedInUserId($app),
            BaseModeration::MODERATION_SOURCE_TEXT,
            null,
        );

        return $moderationModel->is_moderator_flagged;
    }
}
