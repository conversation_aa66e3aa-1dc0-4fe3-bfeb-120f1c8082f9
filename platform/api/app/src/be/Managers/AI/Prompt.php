<?php

declare(strict_types=1);

namespace Salesfloor\API\Managers\AI;

use Salesfloor\Managers\Base;
use Salesfloor\API\Exceptions\Generic\Invalid\InvalidInputException;
use Salesfloor\Models\AI\Prompt as SalesfloorPrompt;

class Prompt extends Base
{
    protected $modelName = 'AI\Prompt';
    protected $className = 'sf_prompts';

    /**
     * Maximum number of prompts per user
     */
    const MAX_PROMPTS_PER_USER = 5;

    /**
     *  Store a prompt
     * @param array $createParameters input values. [user_id , title, prompt]
     */
    public function create(array $createParameters)
    {
        if (!isset($createParameters['user_id'])) {
            throw new InvalidInputException('user_id is required');
        }

        if (
            !isset($createParameters['title']) ||
            empty(trim($createParameters['title']))
        ) {
            throw new InvalidInputException('title is required');
        }

        if (
            !isset($createParameters['prompt']) ||
            empty(trim($createParameters['prompt']))
        ) {
            throw new InvalidInputException('prompt is required');
        }

        $userId = (int) $createParameters['user_id'];
        $createParameters['user_id'] = $userId;

        // Use database transaction to prevent race conditions
        $this->repository->beginTransaction();

        try {
            $currentCount = $this->count(['user_id' => $userId], null, false);
            if ($currentCount >= self::MAX_PROMPTS_PER_USER) {
                throw new InvalidInputException(
                    'You have reached the maximum limit of ' .
                        self::MAX_PROMPTS_PER_USER .
                        ' saved prompts. Please delete an existing prompt before saving a new one.'
                );
            }

            $model = parent::create($createParameters);
            $this->repository->commit();
            return $model;
        } catch (\Exception $e) {
            $this->repository->rollback();
            throw $e;
        }
    }

    /**
     * Get prompts for a specific user, ordered by created_at DESC
     */
    public function getUserPrompts(
        int $userId,
        int $page = 0,
        int $perPage = -1
    ): array {
        $result = $this->getAll(['user_id' => $userId], $page, $perPage, true, [
            '-created_at',
        ]);

        // Ensure we always return an array, even if empty
        return is_array($result) ? $result : [];
    }

    /**
     * Override delete to ensure only one prompt is deleted at a time
     */
    public function delete($model)
    {
        // Return gracefully if model doesn't exist
        if (!$model || empty($model->id)) {
            return 0;
        }

        return $this->deleteById($model->id);
    }

    /**
     * Get the provided prompt for the user
     */
    public function getUserPrompt(int $userId, int $promptId): ?SalesfloorPrompt
    {
        return $this->getOneOrNull([
            'id' => $promptId,
            'user_id' => $userId,
        ]);
    }

    protected function afterDelete($ids)
    {
        $this->invalidateClassCache();
    }

    protected function afterCreate($model)
    {
        $this->invalidateClassCache();
    }
}
