<?php

declare(strict_types=1);

namespace Salesfloor\API\Managers\AI;

use Salesfloor\Managers\Base\JsonApiManager;
use Salesfloor\Models\Virtuals\AI\Message as AIGeneratedMessage;
use Salesfloor\Services\AI\Agent\ContentAnalyzerAgent;
use Salesfloor\Services\AI\Agent\LanguageDetectionAgent;
use Salesfloor\Services\AI\Agent\RequestAnalyzerAgent;
use Salesfloor\Services\AI\Exception\AIAdapterException;
use Salesfloor\Services\AI\Model\AIResponse;
use Salesfloor\Services\AI\Util\Prompt;
use Salesfloor\Services\AI\ValueObject\ContentType;

/**
 * Message Manager
 *
 * Orchestrates AI agents to generate and refine text content.
 */
class Message extends JsonApiManager
{
    protected $modelName = 'Virtuals\AI\Message';

    private RequestAnalyzerAgent $requestAnalyzer;
    private ContentAnalyzerAgent $contentAnalyzer;
    private LanguageDetectionAgent $languageDetector;

    /**
     * Inject dependencies from the application container
     */
    public function injectDeps($app): void
    {
        $this->requestAnalyzer = $app['service.ai.agent.request_analyzer'];
        $this->contentAnalyzer = $app['service.ai.agent.content_analyzer'];
        $this->languageDetector = $app['service.ai.agent.language_detector'];
    }

    /**
     * Generate a marketing message based on user input
     *
     * @param string $input User input request
     * @param int $userId The ID of the user making the request
     * @param array $context Additional context for generation
     * @return AIGeneratedMessage Generated message model
     * @throws AIAdapterException If message generation fails
     */
    public function generateMessage(
        string $input,
        int $userId,
        array $context = []
    ): AIGeneratedMessage {
        // Run language detection and request analysis in parallel
        $parallelResults = $this->runParallelAnalysis($input, $context);

        // Merge results for content generation
        $analysis = $this->mergeAnalysisResults(
            $parallelResults['request_analysis'],
            $parallelResults['language_detection']
        );

        // Override content_type if specified in context
        if (isset($context['content_type'])) {
            $contentType = new ContentType($context['content_type']);
            $analysis['content_type'] = $contentType->getValue();
        }

        $contentResponse = $this->requestAnalyzer->generateContentFromAnalysis(
            $analysis
        );

        $content = $contentResponse->getContent();
        $contentType = new ContentType($analysis['content_type'] ?? 'email');
        $campaignType = $analysis['campaign_type'] ?? 'newsletter';

        $this->logger->info('Marketing content generated successfully', [
            'content_type' => $contentType->getValue(),
            'campaign_type' => $campaignType,
            'content_length' => strlen($content),
            'user_id' => $userId,
        ]);

        $message = $this->createMessageModel(
            $content,
            $contentType,
            $campaignType,
            $userId,
            $input
        );

        if ($contentType->isEmail()) {
            $this->addSubjectLines($message, $content);
        }

        return $message;
    }

    /**
     * Refine existing message content
     *
     * @param string $input Content to refine
     * @param int $userId The ID of the user making the request
     * @param array $context Additional context for refinement
     * @return AIGeneratedMessage Refined message model
     * @throws AIAdapterException If message refinement fails
     */
    public function refineMessage(
        string $input,
        int $userId,
        array $context = []
    ): AIGeneratedMessage {
        // Run language detection and content analysis in parallel for refinement
        $parallelResults = $this->runParallelContentAnalysis($input, $context);

        // Merge results for content refinement
        $analysis = $this->mergeContentAnalysisResults(
            $parallelResults['content_analysis'],
            $parallelResults['language_detection']
        );

        // Override content_type if specified in context
        if (isset($context['content_type'])) {
            $contentType = new ContentType($context['content_type']);
            $analysis['content_type'] = $contentType->getValue();
        }

        $options = [
            'temperature' =>
                $this->configs['ai.agent.refiner.override.temperature'],
        ];

        $contentResponse = $this->contentAnalyzer->refineContentFromAnalysis(
            $input,
            $analysis,
            $options
        );

        $content = $contentResponse->getContent();
        $contentType = new ContentType($analysis['content_type']);
        $campaignType = $analysis['campaign_type'];

        $message = $this->createMessageModel(
            $content,
            $contentType,
            $campaignType,
            $userId,
            $input
        );

        if ($contentType->isEmail()) {
            $this->addSubjectLines($message, $content);
        }

        return $message;
    }

    /**
     * Add subject lines to an email message
     *
     * @param AIGeneratedMessage $message The message to add subject lines to
     * @param string $emailContent The email content
     * @return void
     */
    private function addSubjectLines(
        AIGeneratedMessage $message,
        string $emailContent
    ): void {
        $defaultSubject = 'Announcement!';

        try {
            // Direct orchestration: Manager calls campaign agent for subject line generation
            $subjectResponse = $this->requestAnalyzer
                ->getCampaignAgent()
                ->generateSubjectLines($emailContent, []);

            $subjectLines = $this->parseSubjectLines($subjectResponse);
            $message->recommended_subject = !empty($subjectLines)
                ? $subjectLines[0]
                : $defaultSubject;

            $this->logger->info('Subject lines generated successfully', [
                'subject_count' => count($subjectLines),
                'selected_subject' => $message->recommended_subject,
            ]);
        } catch (\Exception $e) {
            $this->logger->warning(
                'Failed to generate subject lines: ' . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'fallback_subject' => $defaultSubject,
                ]
            );
            $message->recommended_subject = $defaultSubject;
        }
    }

    /**
     * Parse subject lines from AI response
     *
     * @param AIResponse $response The AI response containing subject lines
     * @return array Array of subject lines
     */
    private function parseSubjectLines(AIResponse $response): array
    {
        $content = $response->getContent();

        if (
            preg_match_all(
                '/(?:^|\n)(?:\d+\.\s*)?(.+?)(?:\n|$)/',
                $content,
                $matches
            )
        ) {
            return array_values(
                array_slice(array_filter(array_map('trim', $matches[1])), 0, 3)
            );
        }

        return [];
    }

    /**
     * Run language detection and request analysis in parallel
     *
     * @param string $input User input
     * @param array $context Request context
     * @return array Combined results from both analyses
     */
    private function runParallelAnalysis(string $input, array $context): array
    {
        $results = [];
        $errors = [];

        // Language Detection
        try {
            $startTime = microtime(true);
            $results['language_detection'] = $this->languageDetector->detectLanguage($input, $context);
            $this->logProcessingTime('language_detection', $startTime);
        } catch (\Exception $e) {
            $errors['language_detection'] = $e;
            $results['language_detection'] = $this->getFallbackLanguageDetection();
            $this->logger->warning('Language detection failed, using fallback: ' . $e->getMessage());
        }

        // Request Analysis
        try {
            $startTime = microtime(true);
            $results['request_analysis'] = $this->requestAnalyzer->analyze($input, $context);
            $this->logProcessingTime('request_analysis', $startTime);
        } catch (\Exception $e) {
            $errors['request_analysis'] = $e;
            throw new AIAdapterException('Request analysis failed: ' . $e->getMessage(), 0, $e);
        }

        return $results;
    }

    /**
     * Run language detection and content analysis in parallel for refinement
     *
     * @param string $content Content to analyze
     * @param array $context Analysis context
     * @return array Combined results from both analyses
     */
    private function runParallelContentAnalysis(string $content, array $context): array
    {
        $results = [];
        $errors = [];

        // Language Detection
        try {
            $startTime = microtime(true);
            $results['language_detection'] = $this->languageDetector->detectLanguage($content, $context);
            $this->logProcessingTime('content_language_detection', $startTime);
        } catch (\Exception $e) {
            $errors['language_detection'] = $e;
            $results['language_detection'] = $this->getFallbackLanguageDetection();
            $this->logger->warning('Language detection failed for content analysis, using fallback: ' . $e->getMessage());
        }

        // Content Analysis
        try {
            $startTime = microtime(true);
            $results['content_analysis'] = $this->contentAnalyzer->analyze($content, $context);
            $this->logProcessingTime('content_analysis', $startTime);
        } catch (\Exception $e) {
            $errors['content_analysis'] = $e;
            throw new AIAdapterException('Content analysis failed: ' . $e->getMessage(), 0, $e);
        }

        return $results;
    }

    /**
     * Merge request analysis and language detection results
     *
     * @param array $requestAnalysis Results from RequestAnalyzerAgent
     * @param array $languageDetection Results from LanguageDetectionAgent
     * @return array Merged analysis results
     */
    private function mergeAnalysisResults(array $requestAnalysis, array $languageDetection): array
    {
        $mergedContext = $requestAnalysis['context'] ?? [];

        // Add language detection results using utility function
        $mergedContext = Prompt::mergeLanguageContext($mergedContext, $languageDetection);

        // Update the analysis with merged context
        $requestAnalysis['context'] = $mergedContext;
        $requestAnalysis['language_detection'] = $languageDetection;

        return $requestAnalysis;
    }

    /**
     * Merge content analysis and language detection results for refinement
     *
     * @param array $contentAnalysis Results from ContentAnalyzerAgent
     * @param array $languageDetection Results from LanguageDetectionAgent
     * @return array Merged analysis results
     */
    private function mergeContentAnalysisResults(array $contentAnalysis, array $languageDetection): array
    {
        $mergedContext = $contentAnalysis['context'] ?? [];

        // Add language detection results using utility function
        $mergedContext = Prompt::mergeLanguageContext($mergedContext, $languageDetection);

        // Update the analysis with merged context
        $contentAnalysis['context'] = $mergedContext;
        $contentAnalysis['language_detection'] = $languageDetection;

        return $contentAnalysis;
    }

    /**
     * Get fallback language detection results
     *
     * @return array Default language detection
     */
    private function getFallbackLanguageDetection(): array
    {
        return [
            'detected_language' => 'en',
            'final_language' => 'en',
            'confidence' => 0.5,
            'has_override' => false,
            'override_reason' => null,
            'detection_method' => 'fallback'
        ];
    }

    /**
     * Log processing time for performance monitoring
     *
     * @param string $operation Operation name
     * @param float $startTime Start time
     */
    private function logProcessingTime(string $operation, float $startTime): void
    {
        $duration = microtime(true) - $startTime;
        $this->logger->info("Operation '{$operation}' completed", [
            'duration_ms' => round($duration * 1000, 2),
            'operation' => $operation
        ]);
    }

    /**
     * Create a message model with the provided data
     *
     * @param string $content The generated content
     * @param ContentType $contentType The content type
     * @param string $campaignType The campaign type
     * @param int $userId The user ID
     * @param string $prompt The original prompt
     * @return AIGeneratedMessage
     */
    private function createMessageModel(
        string $content,
        ContentType $contentType,
        string $campaignType,
        int $userId,
        string $prompt
    ): AIGeneratedMessage {
        $message = new AIGeneratedMessage();
        $message->content = $content;
        $message->content_type = $contentType->getValue();
        $message->campaign_type = $campaignType;
        $message->user_id = $userId;
        $message->prompt = $prompt;

        return $message;
    }
}
