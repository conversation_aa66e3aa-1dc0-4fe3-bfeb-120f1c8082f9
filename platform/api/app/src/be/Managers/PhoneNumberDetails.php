<?php

declare(strict_types=1);

namespace Salesfloor\API\Managers;

use Salesfloor\Managers\Base;
use Doctrine\DBAL\ArrayParameterType;
use Salesfloor\Services\ExtendedInsertQueryBuilder;
use Salesfloor\Models\PhoneNumberDetails as PhoneNumberDetailsModel;

class PhoneNumberDetails extends Base
{
    protected $modelName = 'PhoneNumberDetails';
    protected $className = 'sf_phone_number_details';

    protected $insertBuilderService;

    public function injectDeps($app)
    {
        parent::injectDeps($app);
        $this->insertBuilderService = $app['service.insert_query_builder'];
    }

    /**
     * Make insert builder
     *
     * @return ExtendedInsertQueryBuilder
     */
    private function makeInsertBuilder()
    {
        $builder = $this->insertBuilderService;
        return $builder();
    }

    /**
     * Get phone number types from numbers
     *
     * @param array $phoneNumbers
     * @return array [phoneNumber => type]
     */
    public function getPhoneNumberCarrierTypes(array $phoneNumbers): array
    {
        $qb = $this->repository->getQueryBuilder();
        $qb->select([
                'm.phone_number',
                'm.carrier_type'
            ])
            ->from($this->className, 'm')
            ->where($qb->expr()->isNotNull('m.carrier_type'))
            ->andWhere($qb->expr()->in('m.phone_number', ':phoneNumbers'))
            ->setParameter('phoneNumbers', $phoneNumbers, ArrayParameterType::STRING)
            ->setMaxResults(count($phoneNumbers));
        $results = $this->repository->executeCustomQuery($qb);

        // Map phone number to type
        $numberTypes = [];
        foreach ($results as $result) {
            $numberTypes[$result['phone_number']] = $result['carrier_type'];
        }

        return $numberTypes;
    }

    /**
     * Save phone number carrier types in bulk
     * Will insert or update phone number carrier types
     *
     * @param array $mappings [phoneNumber => phoneType]
     * @return void
     */
    public function savePhoneNumberCarrierTypesInBulk(array $mappings): void
    {
        $desiredFields = [
            'phone_number',
            'carrier_type',
        ];

        $insertBuilder = $this->makeInsertBuilder();
        $insertBuilder
            ->setTable($this->className)
            ->addInsertFields($desiredFields)
            ->addOnDuplicateUpdateFields([
                'carrier_type' => true,
                'updated_at' => gmdate('Y-m-d H:i:s'),
            ]);

        $shouldExecute = false;
        foreach ($mappings as $phoneNumber => $carrierType) {
            if (in_array($carrierType, PhoneNumberDetailsModel::VALID_CARRIER_TYPE_VALUES)) {
                $shouldExecute = true;
                $insertBuilder->addValuesSet([
                    'phone_number' => $phoneNumber,
                    'carrier_type' => $carrierType,
                ]);
            }
        }

        if ($shouldExecute) {
            $insertOrUpdate = $insertBuilder->prepare();
            $this->repository->executeQuery($insertOrUpdate['query'], $insertOrUpdate['parameters']);
        }
    }
}
