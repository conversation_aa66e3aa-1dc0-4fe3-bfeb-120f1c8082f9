<?php

/**
 * Application Services
 *
 * Copyright 2014 - Salesfloor
 */

use Pimple\Container;
use Predis\Silex\ClientServiceProvider;
use Salesfloor\Providers\AlgoliaIndexerServiceProvider;
use Salesfloor\Providers\AnalyticsTrackingServiceProvider;
use Salesfloor\Providers\Application\ManagerRequestServiceProvider;
use Salesfloor\Providers\Application\RoutingServiceProvider;
use Salesfloor\Providers\Application\VersioningServiceProvider;
use Salesfloor\Providers\AutoResponderServiceProvider;
use Salesfloor\Providers\BackofficeConnectionServiceProvider;
use Salesfloor\Providers\Backup\BackupServiceProvider;
use Salesfloor\Providers\Cache\CacheServiceProvider;
use Salesfloor\Providers\CalculateDailyStatsServiceProvider;
use Salesfloor\Providers\CatalogImporter\NominatedProductsServiceProvider;
use Salesfloor\Providers\CatalogImporterServiceProvider;
use Salesfloor\Providers\CdnImageServiceProvider;
use Salesfloor\Providers\Chat\CansChatMetricsServiceProvider;
use Salesfloor\Providers\ChatServiceProvider;
use Salesfloor\Providers\ClientRequestServiceProvider;
use Salesfloor\Providers\CloudStorage\CloudStorageProvider;
use Salesfloor\Providers\Config\ConfigParseServiceProvider;
use Salesfloor\Providers\CustomerActivityFeedServiceProvider;
use Salesfloor\Providers\CustomerUpdateServiceProvider;
use Salesfloor\Providers\DeepLinksServiceProvider;
use Salesfloor\Providers\Config\ConfigDescriptionServiceProvider;
use Salesfloor\Providers\DynamicContentServiceProvider;
use Salesfloor\Providers\ElasticSearchServiceProvider;
use Salesfloor\Providers\EncryptionServiceProvider;
use Salesfloor\Providers\ErrorHandlerServiceProvider;
use Salesfloor\Providers\EventImporterServiceProvider;
use Salesfloor\Providers\EventServiceProvider;
use Salesfloor\Providers\ExcelGeneratorServiceProvider;
use Salesfloor\Providers\Exporter\ActivitySummary\ActivitySummaryExporterServiceProvider;
use Salesfloor\Providers\Exporter\ActivitySummary\StoreActivitySummaryExporterServiceProvider;
use Salesfloor\Providers\Exporter\Contact\ContactExporterServiceProvider;
use Salesfloor\Providers\Exporter\Customer\CustomerExporterServiceProvider;
use Salesfloor\Providers\Exporter\Email\EmailStatsExporterServiceProvider;
use Salesfloor\Providers\Exporter\LiveChat\LiveChatExporterServiceProvider;
use Salesfloor\Providers\Exporter\LiveChat\LiveChatYearlyExporterServiceProvider;
use Salesfloor\Providers\Exporter\LiveChat\RepLiveChatMetricsExporterServiceProvider;
use Salesfloor\Providers\Exporter\LiveChat\StoreLiveChatMetricsExporterServiceProvider;
use Salesfloor\Providers\Exporter\Lookbook\LookbookExporterServiceProvider;
use Salesfloor\Providers\Exporter\Message\MessageExporterServiceProvider;
use Salesfloor\Providers\Exporter\Rep\RepExporterServiceProvider;
use Salesfloor\Providers\Exporter\Request\RequestExporterServiceProvider;
use Salesfloor\Providers\Exporter\RetailerService\WeeklySummaryServiceProvider;
use Salesfloor\Providers\Exporter\Share\ShareEmailExporterServiceProvider;
use Salesfloor\Providers\Exporter\Sidebar\SidebarMetricExporterServiceProvider;
use Salesfloor\Providers\Exporter\Text\TextExporterServiceProvider;
use Salesfloor\Providers\Exporter\Transaction\DailyTransactionDetailsExporterServiceProvider;
use Salesfloor\Providers\Exporter\Transaction\DailyTransactionExporterServiceProvider;
use Salesfloor\Providers\Exporter\RetailerTransactionAttribution\RetailerTransactionAttributionExporterServiceProvider;
use Salesfloor\Providers\Exporter\CustomizeReport\ExporterFactoryServiceProvider;
use Salesfloor\Providers\Exporter\Task\TaskExporterServiceProvider;
use Salesfloor\Providers\Exporter\GroupTask\GroupTaskExporterServiceProvider;
use Salesfloor\Providers\Exporter\RetailerCustomer\RetailerCustomerExporterServiceProvider;
use Salesfloor\Providers\ExtendedInsertQueryBuilderServiceProvider;
use Salesfloor\Providers\FileManagerServiceProvider;
use Salesfloor\Providers\FirebaseServiceProvider;
use Salesfloor\Providers\Gcp\StackDriverServiceProvider;
use Salesfloor\Providers\GeneratorServiceProvider;
use Salesfloor\Providers\GeoIPServiceProvider;
use Salesfloor\Providers\GeoZipServiceProvider;
use Salesfloor\Providers\GoutteServiceProvider;
use Salesfloor\Providers\Image\ImageServiceProvider;
use Salesfloor\Providers\Importer\Address\AddressImporterServiceProvider;
use Salesfloor\Providers\Importer\Customer\CustomerAttributesImporterServiceProvider;
use Salesfloor\Providers\Importer\Customer\CustomerImporterServiceProvider;
use Salesfloor\Providers\Importer\Customer\CustomerTagsImporterServiceProvider;
use Salesfloor\Providers\Importer\Customer\CustomerUnsubscribeServiceProvider;
use Salesfloor\Providers\Importer\Customer\CustomerSubscribeUnsubscribeServiceProvider;
use Salesfloor\Providers\Importer\CustomerInsightsProvider;
use Salesfloor\Providers\Importer\CustomersToContactsProvider;
use Salesfloor\Providers\Importer\RepsImporterServiceProvider;
use Salesfloor\Providers\Importer\Retailer\RetailerCustomersProvider;
use Salesfloor\Providers\Importer\Retailer\RetailerCustomersRemappingProvider;
use Salesfloor\Providers\Importer\Retailer\RetailerCustomersStatsInsightsProvider;
use Salesfloor\Providers\Importer\Retailer\RetailerCustomersTransactionProvider;
use Salesfloor\Providers\Importer\Retailer\RetailerStoreProvider;
use Salesfloor\Providers\Importer\Retailer\RetailerTransactionsCancelsAndReturnsServiceProvider;
use Salesfloor\Providers\Importer\TaskImporterServiceProvider;
use Salesfloor\Providers\Importer\TextMessage\TextMessageUnsubscribeImporterServiceProvider;
use Salesfloor\Providers\Importer\TransactionImporterServiceProvider;
use Salesfloor\Providers\IncomingEmailServiceProvider;
use Salesfloor\Providers\InstagramServiceProvider;
use Salesfloor\Providers\JsonApi\JsonApiRelationshipServiceProvider;
use Salesfloor\Providers\Mail\AlternateTemplateServiceProvider;
use Salesfloor\Providers\Mail\EmailSenderServiceProvider;
use Salesfloor\Providers\Mail\EmailThreadsServiceProvider;
use Salesfloor\Providers\Mail\EmailTrackingServiceProvider;
use Salesfloor\Providers\Mail\MailClientServiceProvider;
use Salesfloor\Providers\Mail\MailProductHelperServiceProvider;
use Salesfloor\Providers\Mail\MailRendererServiceProvider;
use Salesfloor\Providers\Mail\Templates\CustomerRequestEmailTemplateServiceProvider;
use Salesfloor\Providers\Mail\Templates\EmailTemplateFactoryServiceProvider;
use Salesfloor\Providers\Mail\Templates\Variables\GlobalVariablesServiceProvider;
use Salesfloor\Providers\MailQueueServiceProvider;
use Salesfloor\Providers\MapperServiceProvider;
use Salesfloor\Providers\MassMailerServiceProvider;
use Salesfloor\Providers\MatchCustomersRetailerCustomersServiceProvider;
use Salesfloor\Providers\MessageQueueServiceProvider;
use Salesfloor\Providers\Moderation\ModerationServiceProvider;
use Salesfloor\Providers\Monitoring\MetricsServiceProvider;
use Salesfloor\Providers\MySQL\RandomTableProvider;
use Salesfloor\Providers\MySQLImporterServiceProvider;
use Salesfloor\Providers\MySQLRepositoryServiceProvider;
use Salesfloor\Providers\NameSuggesterServiceProvider;
use Salesfloor\Providers\NormalizationPhoneNumberServiceProvider;
use Salesfloor\Providers\NotificationSystemServiceProvider;
use Salesfloor\Providers\Obfuscation\ObfuscationServiceProvider;
use Salesfloor\Providers\OutstandingRequestsProvider;
use Salesfloor\Providers\PaginationServiceProvider;
use Salesfloor\Providers\ProcessManagementServiceProvider;
use Salesfloor\Providers\Products\AutoSelectedRefreshedServiceProvider;
use Salesfloor\Providers\Products\DefaultCommentsServiceProvider;
use Salesfloor\Providers\Products\ProductPanelsServiceProvider;
use Salesfloor\Providers\ProductSearchFiltersServiceProvider;
use Salesfloor\Providers\QueueByStoreServiceProvider;
use Salesfloor\Providers\Reporting\PerformanceEmailServiceProvider;
use Salesfloor\Providers\RetailerCustomerBlackoutServiceProvider;
use Salesfloor\Providers\RetailerCustomersRemapping\RemapperServiceProvider;
use Salesfloor\Providers\S3ServiceProvider;
use Salesfloor\Providers\Salesfloor\CustomerServiceProvider;
use Salesfloor\Providers\Salesfloor\RepsServiceProvider;
use Salesfloor\Providers\SalesfloorAPIRepositoryServiceProvider;
use Salesfloor\Providers\SalesfloorServicesServiceProvider;
use Salesfloor\Providers\Security\ApiKeyAuthenticationServiceProvider;
use Salesfloor\Providers\Security\Cryptography\GnuPrivacyGuardServiceProvider;
use Salesfloor\Providers\Security\JWTAuthenticationServiceProvider;
use Salesfloor\Providers\Security\LockoutServiceProvider;
use Salesfloor\Providers\Security\MultiFactorAuthenticationServiceProvider;
use Salesfloor\Providers\Security\Oauth2ServiceProvider;
use Salesfloor\Providers\Security\OnBoardingTokenAuthenticationServiceProvider;
use Salesfloor\Providers\Security\RefreshServiceProvider;
use Salesfloor\Providers\Security\SFSecurityServiceProvider;
use Salesfloor\Providers\SendgridServiceProvider;
use Salesfloor\Providers\Seo\RobotsServiceProvider;
use Salesfloor\Providers\Services\AppointmentsCancelServiceProvider;
use Salesfloor\Providers\Services\AppointmentsServiceProvider;
use Salesfloor\Providers\Services\PersonalShopperServiceProvider;
use Salesfloor\Providers\Services\QuestionsProxyServiceProvider;
use Salesfloor\Providers\Services\QuestionsServiceProvider;
use Salesfloor\Providers\Services\SanitizeServiceProvider;
use Salesfloor\Providers\Services\PlainTextFormatterServiceProvider;
use Salesfloor\Providers\SFTPServiceProvider;
use Salesfloor\Providers\Share\ShareEmailServiceProvider;
use Salesfloor\Providers\Share\ShopFeedServiceProvider;
use Salesfloor\Providers\ShopifyServiceProvider;
use Salesfloor\Providers\SidebarStatsAggregatorServiceProvider;
use Salesfloor\Providers\SidebarStatusServiceProvider;
use Salesfloor\Providers\SlackServiceProvider;
use Salesfloor\Providers\StatsPanelUpdaterServiceProvider;
use Salesfloor\Providers\Storefront\Menu\StorefrontMenuServiceProvider;
use Salesfloor\Providers\StorefrontServiceProvider;
use Salesfloor\Providers\TablesDiffServiceProvider;
use Salesfloor\Providers\Tasks\TasksServiceProvider;
use Salesfloor\Providers\Transactions\TransactionESIndexProvider;
use Salesfloor\Providers\UpdateTrendingRecommendationsServiceProvider;
use Salesfloor\Providers\UserActivityServiceProvider;
use Salesfloor\Providers\UtilServiceProvider;
use Salesfloor\Providers\WidgetServiceProvider;
use Salesfloor\Providers\WpPasswordServiceProvider;
use Salesfloor\Services\Responsible\ResponsibleServiceProvider;
use Silex\Application;
use Silex\Provider\SecurityServiceProvider;
use Silex\Provider\ServiceControllerServiceProvider;
use Silex\Provider\ValidatorServiceProvider;
use Silex\Provider\TwigServiceProvider;
use Salesfloor\Providers\EventQueueServiceProvider;
use Salesfloor\Providers\CorporateTaskQueueServiceProvider;
use Salesfloor\Providers\SidebarEventQueueServiceProvider;
use Salesfloor\Providers\DataProcessing\AttributionServiceProvider;
use Salesfloor\Providers\DataProcessing\VisitsServiceProvider;
use Salesfloor\Providers\Mail\EmailBlockListServiceProvider;
use Salesfloor\Providers\Mail\Filter\ExclusionFilterServiceProvider;
use Salesfloor\Providers\Mail\Filter\EnvironmentFilterServiceProvider;
use Salesfloor\Providers\Aws\CloudWatchServiceProvider;
use Salesfloor\Providers\Aws\CloudWatchMetricsServiceProvider;
use Salesfloor\Providers\MessagingServiceProvider;
use Salesfloor\Providers\MobileBuildsServiceProvider;
use Salesfloor\Providers\ShortenerServiceProvider;
use Salesfloor\Providers\LangServiceProvider;
use Salesfloor\Providers\Security\CryptoServiceProvider;
use Salesfloor\Providers\Tasks\Automated\AllAutomatedTasksServiceProvider;
use Salesfloor\Providers\DataProcessing\TransactionCustomerServiceProvider;
use Salesfloor\Providers\RecordTransactionsServiceProvider;
use Salesfloor\Providers\BugAlertServiceProvider;
use Salesfloor\Providers\GuzzleHttpServiceProvider;
use Salesfloor\Providers\PushNotificationProvider;
use Salesfloor\Providers\AuthTokenProvider;
use Salesfloor\Providers\AI\AIProvider;
use Salesfloor\Providers\Notifications\NotificationLateMessagesProvider;
use Salesfloor\Providers\Notifications\NotificationLateRequestsProvider;
use Salesfloor\Providers\Notifications\NotificationNearAppointmentsProvider;
use Salesfloor\Providers\Services\RetailerInventoryLookupServiceProvider;
use Salesfloor\Providers\Customer2ContactServiceProvider;
use Salesfloor\Providers\ShopPageServiceProvider;
use Salesfloor\Providers\DashboardComponentMappingServiceProvider;
use Salesfloor\Providers\OutfitServiceProvider;
use Salesfloor\Providers\AppointmentHoursServiceProvider;
use Salesfloor\Providers\AppointmentsPublicApiServiceProvider;
use Salesfloor\Providers\PushNotifications\PublishQueueServiceProvider;
use Salesfloor\Providers\ShopByBrandServiceProvider;
use Salesfloor\Providers\FeedScannerServiceProvider;
use Salesfloor\Providers\RetailerCustomerServiceProvider;
use Salesfloor\Providers\PrepareShareServiceProvider;
use Salesfloor\Providers\Cleaner\AlgoliaIndiceCleanerServiceProvider;
use Salesfloor\Providers\Products\GroupedProductsServiceProvider;
use Salesfloor\Providers\Importer\UserStoresServiceProvider;
use Salesfloor\Providers\InboundDomainServiceProvider;

assert(isset($app) && $app instanceof Application);

// Register the versioning service
$app->register(new VersioningServiceProvider());

// It's important to put caching at the top. The warmup boot() should be the first
// to be called, so other services can use it directly and not rely on the fallback caching system.
$app->register(new CacheServiceProvider());

$app->register(new \Salesfloor\Providers\CompatibilityServiceProvider());

// Register a provider that extract parameters from request
$app->register(new ManagerRequestServiceProvider());

// Register a provider that create route and allow api versioning
$app->register(new RoutingServiceProvider());

// Register a provider so we can write controllers as services
$app->register(new ServiceControllerServiceProvider());

// Register a MySQL repository service provider
$app->register(new MySQLRepositoryServiceProvider());

// Output formatter service, currently only supports json
#$app->register(new ResponsibleServiceProvider());

// Register predis
$app->register(new ClientServiceProvider(), [
    'predis.parameters' =>
        'tcp://' .
        $app['configs']['redis.host'] .
        ':' .
        $app['configs']['redis.port'],
]);

// Register Goutte
$app->register(new GoutteServiceProvider());

// Register S3
$app->register(new S3ServiceProvider());

// Register CloudStorage Factory
$app->register(new CloudStorageProvider());

// Register MySQL Importer
$app->register(new MySQLImporterServiceProvider());

$app->register(new GeoIPServiceProvider());
$app->register(new GeoZipServiceProvider());

$app->register(new PaginationServiceProvider());

$app->register(new CustomerImporterServiceProvider());
$app->register(new CustomerUpdateServiceProvider());
$app->register(new CustomerTagsImporterServiceProvider());

$app->register(new AlgoliaIndexerServiceProvider());
$app->register(new ProductSearchFiltersServiceProvider());
$app->register(new TablesDiffServiceProvider());

$app->register(new BackofficeConnectionServiceProvider());

// Register Cloudinary provider,
// and tell the product manager (which needs it) about it.
// WESHOULD: figure out how to better inject this dependency
// without this ugly bit of code.
$app->register(new CdnImageServiceProvider());

// Register Twig template engine
$app->register(new TwigServiceProvider(), [
    'twig.path' => $app['configs']['twig.views_dir'],
    'twig.options' => (function (Application $app) {
        $options = [];
        if ($app['configs']['env'] == 'dev') {
            $options['debug'] = true;
        }

        return $options;
    })($app),
]);

$app->register(new EncryptionServiceProvider());

$app->register(new MailQueueServiceProvider());

$app->register(new WpPasswordServiceProvider());

$app->register(new UtilServiceProvider());
$app->register(new EventServiceProvider());
$app->register(new CustomerActivityFeedServiceProvider());
$app->register(new UserActivityServiceProvider());
$app->register(new NameSuggesterServiceProvider());
$app->register(new FirebaseServiceProvider());
$app->register(new ChatServiceProvider());
$app->register(new QueueByStoreServiceProvider());
$app->register(new SalesfloorAPIRepositoryServiceProvider());
$app->register(new InstagramServiceProvider());
$app->register(new CatalogImporterServiceProvider());
$app->register(new NominatedProductsServiceProvider());
$app->register(new EventImporterServiceProvider());
$app->register(new SlackServiceProvider());
$app->register(new MessageQueueServiceProvider());
$app->register(new ExcelGeneratorServiceProvider());
$app->register(new StoreFrontServiceProvider());
$app->register(new AutoResponderServiceProvider());
$app->register(new SidebarStatsAggregatorServiceProvider());
$app->register(new CustomerUnsubscribeServiceProvider());
$app->register(new CustomerSubscribeUnsubscribeServiceProvider());
$app->register(new CustomerAttributesImporterServiceProvider());
$app->register(new UpdateTrendingRecommendationsServiceProvider());
$app->register(new CalculateDailyStatsServiceProvider());
$app->register(new DefaultCommentsServiceProvider());
$app->register(new AutoSelectedRefreshedServiceProvider());
$app->register(new MapperServiceProvider());
$app->register(new FileManagerServiceProvider());
$app->register(new EventQueueServiceProvider());
$app->register(new CorporateTaskQueueServiceProvider());
$app->register(new SidebarEventQueueServiceProvider());
$app->register(new AttributionServiceProvider());
$app->register(new MatchCustomersRetailerCustomersServiceProvider());
$app->register(new RemapperServiceProvider());
$app->register(new VisitsServiceProvider());
$app->register(new EmailBlockListServiceProvider());
$app->register(new ExclusionFilterServiceProvider());
$app->register(new EnvironmentFilterServiceProvider());
$app->register(new LangServiceProvider());
$app->register(new SalesfloorServicesServiceProvider());
$app->register(new MobileBuildsServiceProvider());
$app->register(new ShortenerServiceProvider());
$app->register(new CustomerInsightsProvider());
$app->register(new NormalizationPhoneNumberServiceProvider());
$app->register(new MailClientServiceProvider());
$app->register(new EmailSenderServiceProvider());
$app->register(new CustomerRequestEmailTemplateServiceProvider());
$app->register(new MailProductHelperServiceProvider());
$app->register(new GlobalVariablesServiceProvider());
$app->register(new EmailThreadsServiceProvider());
$app->register(new MailRendererServiceProvider());
$app->register(new RepsServiceProvider());
$app->register(new CustomerServiceProvider());
$app->register(new DeepLinksServiceProvider());
$app->register(new RetailerTransactionsCancelsAndReturnsServiceProvider());
$app->register(new OutfitServiceProvider());
$app->register(new ShopByBrandServiceProvider());
$app->register(new TaskExporterServiceProvider());
$app->register(new GroupTaskExporterServiceProvider());
$app->register(new RetailerCustomerExporterServiceProvider());

/** Use ErrorLogHandler since we have an issue with file rights with ops work */
// Min level supported need to be a valid monolog level. "blocker" is not supported
$app->register(new Silex\Provider\MonologServiceProvider());
// This is our provider, which adds additional channels.
// You need both, as the base silex provider creates the monolog instance that we extend.
$app->register(new \Salesfloor\Providers\MonologServiceProvider());

$app->register(new ValidatorServiceProvider());
$app->register(new StatsPanelUpdaterServiceProvider());
$app->register(new SendgridServiceProvider());

//----------------------  Importer ----------------------//
$app->register(new RetailerCustomersProvider());
$app->register(new RetailerCustomersStatsInsightsProvider());
$app->register(new RetailerCustomersTransactionProvider());
$app->register(new RetailerCustomersRemappingProvider());
$app->register(new RetailerStoreProvider());
$app->register(new TextMessageUnsubscribeImporterServiceProvider());

//---------------------- Exporter -----------------------//
$app->register(new CustomerExporterServiceProvider());
$app->register(new TextExporterServiceProvider());
$app->register(new ActivitySummaryExporterServiceProvider());
$app->register(new StoreActivitySummaryExporterServiceProvider());
$app->register(new ContactExporterServiceProvider());
$app->register(new RepExporterServiceProvider());
$app->register(new RequestExporterServiceProvider());
$app->register(new LiveChatExporterServiceProvider());
$app->register(new LiveChatYearlyExporterServiceProvider());
$app->register(new DailyTransactionExporterServiceProvider());
$app->register(new DailyTransactionDetailsExporterServiceProvider());
$app->register(new RetailerTransactionAttributionExporterServiceProvider());
$app->register(new MessageExporterServiceProvider());
$app->register(new LookbookExporterServiceProvider());
$app->register(new EmailStatsExporterServiceProvider());
$app->register(new RepLiveChatMetricsExporterServiceProvider());
$app->register(new StoreLiveChatMetricsExporterServiceProvider());
$app->register(new ShareEmailExporterServiceProvider());
$app->register(new WeeklySummaryServiceProvider());
$app->register(new SidebarMetricExporterServiceProvider());
$app->register(new ExporterFactoryServiceProvider());

//---------------------- Security - Firewall - Authentication ----------------------//
$app->register(new SecurityServiceProvider());
$app->register(new SFSecurityServiceProvider()); // The order is important
$app->register(new ApiKeyAuthenticationServiceProvider());
$app->register(new JWTAuthenticationServiceProvider());
$app->register(new OnBoardingTokenAuthenticationServiceProvider());
$app->register(new RefreshServiceProvider());
$app->register(new LockoutServiceProvider());
$app->register(new CryptoServiceProvider());

//----------------------- Services / Request ----------------------//
$app->register(new QuestionsServiceProvider());
$app->register(new AppointmentsServiceProvider());
$app->register(new AppointmentsCancelServiceProvider());
$app->register(new PersonalShopperServiceProvider());
$app->register(new SanitizeServiceProvider());
$app->register(new PlainTextFormatterServiceProvider());

//----------------------- Services / Request Forward ----------------------//
$app->register(new QuestionsProxyServiceProvider());

//----------------------- Services / Products ----------------------//
$app->register(new ProductPanelsServiceProvider());

$app->register(new ElasticSearchServiceProvider());
$app->register(new MessagingServiceProvider());

$app->register(new TasksServiceProvider());
$app->register(new AllAutomatedTasksServiceProvider());

$app->register(new MessagingServiceProvider());

$app->register(new CloudWatchServiceProvider());
$app->register(new CloudWatchMetricsServiceProvider());
$app->register(new StackDriverServiceProvider());
$app->register(new MetricsServiceProvider());

$app->register(new OutstandingRequestsProvider());

$app->register(new EmailTrackingServiceProvider());

$app->register(new EmailTemplateFactoryServiceProvider());

$app->register(new SidebarStatusServiceProvider());

$app->register(new SFTPServiceProvider());

$app->register(new TransactionCustomerServiceProvider());

$app->register(new ErrorHandlerServiceProvider());

$app->register(new ContactExporterServiceProvider());

$app->register(new RobotsServiceProvider());

$app->register(
    new \Salesfloor\Providers\Security\PasswordPolicyServiceProvider()
);

$app->register(
    new \Salesfloor\Providers\Reporting\ChatMetricsServiceProvider()
);

/* SF-19971 Track ES/SQL Discrepancies */
$app->register(new \Salesfloor\Providers\EsSqlDiscrepancyDetectorProvider());

$app->register(new RecordTransactionsServiceProvider());

$app->register(new StorefrontMenuServiceProvider());

// PP-407 - Multilang for all!
$app->register(new \Salesfloor\Providers\MultilangServiceProvider());

$app->register(new \Salesfloor\Providers\TwigExtensionProvider());

$app->register(new \Salesfloor\Providers\Chat\ChatTranscriptServiceProvider());
$app->register(new \Salesfloor\Providers\Chat\ChatConnectorServiceProvider());

$app->register(new \Salesfloor\Providers\MandrillServiceProvider());

$app->register(new WidgetServiceProvider());

$app->register(
    new \Salesfloor\Providers\Chat\ChatLogPopulatorServiceProvider()
);

$app->register(new MassMailerServiceProvider());

$app->register(new CustomersToContactsProvider());

$app->register(new ExtendedInsertQueryBuilderServiceProvider());

$app->register(new NotificationSystemServiceProvider());
$app->register(new BugAlertServiceProvider());

$app->register(new GuzzleHttpServiceProvider());

$app->register(new ConfigParseServiceProvider());

$app->register(new ConfigDescriptionServiceProvider());

$app->register(new AnalyticsTrackingServiceProvider());

$app->register(new PerformanceEmailServiceProvider());

$app->register(new AlternateTemplateServiceProvider());

$app->register(new \Salesfloor\Providers\MonitoringServiceProvider());

$app->register(new ClientRequestServiceProvider());

$app->register(new \Salesfloor\Providers\Aws\SnsServiceProvider());

$app->register(new \Salesfloor\Providers\Cleaner\CleanerServiceProvider());

$app->register(
    new \Salesfloor\Providers\Importer\CsvSplitterFactoryServiceProvider()
);

$app->register(
    new \Salesfloor\Providers\Importer\CsvMergerFactoryServiceProvider()
);

$app->register(new ShopifyServiceProvider());

$app->register(new BackupServiceProvider());

$app->register(new RepsImporterServiceProvider());

$app->register(new ProcessManagementServiceProvider());

$app->register(new ShopFeedServiceProvider());

$app->register(new ShareEmailServiceProvider());

$app->register(new \Salesfloor\Providers\Reporting\ProcessorServiceProvider());

$app->register(
    new \Salesfloor\Providers\Importer\Components\ImporterComponentsServiceProvider()
);

$app->register(
    new \Salesfloor\Providers\Importer\ProductImporterServiceProvider()
);

$app->register(new IncomingEmailServiceProvider());

$app->register(new JsonApiRelationshipServiceProvider());

// SF-24759: Migrate code from mailProcessor in WP
$app->register(new AuthTokenProvider());
$app->register(new AIProvider());
$app->register(new PushNotificationProvider());
$app->register(new NotificationLateMessagesProvider());
$app->register(new NotificationLateRequestsProvider());
$app->register(new NotificationNearAppointmentsProvider());

$app->register(new RetailerInventoryLookupServiceProvider());

$app->register(new GeneratorServiceProvider());
$app->register(new \Salesfloor\Providers\AlertServiceProvider());

$app->register(new DynamicContentServiceProvider());

$app->register(new TransactionImporterServiceProvider());

$app->register(new TaskImporterServiceProvider());

$app->register(new GnuPrivacyGuardServiceProvider());

$app->register(new DashboardComponentMappingServiceProvider());

$app['controllers.factory'] = function () {
    return new \Salesfloor\Controllers\Shared\Factory('API');
};

$app['managers.factory'] = function () use ($app) {
    $cachePrefix =
        $app['configs']['env'] . ':' . $app['configs']['retailers.current'];
    if (null !== $app['configs']['retailer.brand']) {
        $cachePrefix .= ':' . $app['configs']['retailer.brand'];
    }
    return new \Salesfloor\Managers\Factory('API', $cachePrefix);
};

$app->register(new Customer2ContactServiceProvider());

$app->register(new RetailerCustomerBlackoutServiceProvider());
$app->register(new ShopPageServiceProvider());

$app->register(new MultiFactorAuthenticationServiceProvider());
$app->register(new PublishQueueServiceProvider());

$app->register(new Oauth2ServiceProvider());

$app->register(new ImageServiceProvider());
$app->register(new AlgoliaIndiceCleanerServiceProvider());

$app->register(new FeedScannerServiceProvider());
$app->register(new RetailerCustomerServiceProvider());

$app->register(new AppointmentHoursServiceProvider());
$app->register(new CansChatMetricsServiceProvider());

$app->register(new AppointmentsPublicApiServiceProvider());

$app->register(new RandomTableProvider());

$app->register(new PrepareShareServiceProvider());

$app->register(new GroupedProductsServiceProvider());

$app->register(new AddressImporterServiceProvider());

$app->register(new ObfuscationServiceProvider());

$app->register(new UserStoresServiceProvider());

$app->register(new TransactionESIndexProvider());

// TODO - Fix that patch for translation
$app['locale'] = 'en_US';

// This needs to be done after MultilangServiceProvider because it process $app['locale'].
use Silex\Provider\TranslationServiceProvider;

// Register Translation
$app->register(new TranslationServiceProvider(), [
    // Since our structure is
    'locale_fallbacks' => [
        // this is equal to en_US in default config (even when i18n is disabled. This is what we want, since
        // we renamed all the yml to en_US instead of en
        $app['configs']['retailer.i18n.locale.fallback'],
    ],
    'translator.cache_dir' => (function (Container $app) {
        $cache = null;

        if ($app['configs']['env'] !== 'dev') {
            $cache = '/tmp/translator_cache_dir/';
        }

        return $cache;
    })($app),
]);

// For backward compatibility
$app['request'] = $app->factory(function ($app) {
    return $app['request_stack']->getCurrentRequest();
});

// To replace tobiassjosten/responsible-service-provider ; since not supporting recent version of serializer.
$app->register(new ResponsibleServiceProvider());

$app->register(new ModerationServiceProvider());

$app->register(new InboundDomainServiceProvider());
