<?php

/**
 * Application Routes
 *
 * Copyright 2017 - Salesfloor
 */

assert(isset($app) && $app instanceof \Silex\Application);
$app['sf.routing']->addManagersFromConfigs(PATH_MANAGERS . '/default.php');

$app['sf.routing']->autoloadRouteFiles();

$app->get('/mobile-pkg-urls', function (
    \Silex\Application $app,
    \Symfony\Component\HttpFoundation\Request $req
) {
    return $app['mobile-builds.service']->getPackageUrls(
        $req->query->get('nameFilter')
    );
});
