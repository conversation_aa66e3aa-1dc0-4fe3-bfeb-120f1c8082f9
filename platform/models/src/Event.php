<?php

  /**
   * Event Model
   *
   * Copyright 2014 - Salesfloor
   */

  namespace Salesfloor\Models;

class Event extends Base
{
    /**
     * An id
     *
     * @var integer
     */
    public $id;

    /**
     * A type
     *
     * @var integer
     */
    public $type;

    /**
     * A date
     *
     * @var date
     */
    public $date;

    /**
     * A source
     *
     * @var string
     */
    public $source;

    /**
     * A unique event id
     *
     * @var string
     */
    public $uniq_id;

    /**
     * A user id
     *
     * @var integer
     */
    public $user_id;

    /**
     * A customer id
     *
     * @var integer
     */
    public $customer_id;

    /**
     * A list of Attributes
     *
     * @var string
     */
    public $attributes;

    /**
     * A satistied customer
     *
     * @var boolean
     */
    public $satisfied;

    /**
     * A related event id
     *
     * @var integer
     */
    public $event_id;

    /**
     * acknowledged
     *
     * @var boolean
     */
    public $acknowledged;

    public $store_id;

    /**
     * @inheritdoc
     */
    public $fields = array(
    'id'            => self::TYPE_PROTECTED,
    'type'          => self::TYPE_PUBLIC,
    'date'          => self::TYPE_PUBLIC,
    'source'        => self::TYPE_PUBLIC,
    'uniq_id'       => self::TYPE_PUBLIC,
    'user_id'       => self::TYPE_PUBLIC,
    'customer_id'   => self::TYPE_PUBLIC,
    'attributes'    => self::TYPE_PUBLIC,
    'satisfied'     => self::TYPE_PUBLIC,
    'event_id'      => self::TYPE_PUBLIC,
    'acknowledged'  => self::TYPE_PUBLIC,
    'store_id'      => self::TYPE_PUBLIC,
    );

    public const DB_ATTRIBUTES_MAX_LENGTH = 250;
}
