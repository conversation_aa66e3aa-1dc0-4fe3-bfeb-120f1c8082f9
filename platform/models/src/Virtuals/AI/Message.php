<?php

declare(strict_types=1);

namespace Salesfloor\Models\Virtuals\AI;

use Salesfloor\Models\Virtuals\BaseVirtual;
use Salesfloor\Models\Interfaces\JsonApiInterface;
use Salesfloor\Schemas\Virtual\AI\Message as MessageSchema;

/**
 * Virtual model for AI-generated marketing messages
 */
class Message extends BaseVirtual implements JsonApiInterface
{
    /**
     * Message ID
     *
     * @var string
     */
    public $id;

    /**
     * Message prompt
     *
     * @var string
     */
    public $prompt;

    /**
     * Message content
     *
     * @var string
     */
    public $content;

    /**
     * Content type (email, sms, etc.)
     *
     * @var string
     */
    public $content_type;

    /**
     * Campaign type (promotion, welcome, etc.)
     *
     * @var string
     */
    public $campaign_type;

    /**
     * User who generated the message
     *
     * @var int
     */
    public $user_id;

    /**
     * When the message was generated
     *
     * @var string
     */
    public $generated_at;

    /**
     * Array of subject line options (for emails only)
     *
     * @var array
     */
    public $subject_lines = [];

    /**
     * Recommended subject line (for emails only)
     *
     * @var string
     */
    public $recommended_subject = '';

    /**
     * Model field definitions
     *
     * @var array
     */
    public $fields = [
        'id' => self::TYPE_PUBLIC,
        'prompt' => self::TYPE_PUBLIC,
        'content' => self::TYPE_PUBLIC,
        'content_type' => self::TYPE_PUBLIC,
        'campaign_type' => self::TYPE_PUBLIC,
        'user_id' => self::TYPE_PROTECTED,
        'generated_at' => self::TYPE_PROTECTED,
        'recommended_subject' => self::TYPE_PUBLIC,
    ];

    /**
     * Constructor that generates a unique ID
     */
    public function __construct()
    {
        parent::__construct();
        $this->id = uniqid();
        $this->generated_at = date('Y-m-d H:i:s');
    }

    /**
     * Convert model to JsonAPI format
     *
     * @return array
     */
    public function toJsonApi(): array
    {
        $attributes = [
            'content' => $this->content,
            'content_type' => $this->content_type,
            'campaign_type' => $this->campaign_type,
            'generated_at' => $this->generated_at,
        ];

        // Add subject lines for email content
        if ($this->content_type === 'email' && !empty($this->subject_lines)) {
            $attributes['recommended_subject'] = $this->recommended_subject;
        }

        return [
            'type' => 'aimessage',
            'id' => $this->id,
            'attributes' => $attributes,
        ];
    }

    public function getSchemasProvider()
    {
        return [
            self::class => MessageSchema::class,
        ];
    }
}
