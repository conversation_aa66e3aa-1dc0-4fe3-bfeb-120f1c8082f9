<?php

namespace Salesfloor\Models;

use Salesfloor\Models\Interfaces\JsonApiInterface;
use Salesfloor\Models\JsonApi\Join\RelationJoin;
use Salesfloor\Models\JsonApi\JoinType\OneToOne;
use Salesfloor\Models\JsonApi\Relation;
use Salesfloor\Schemas\Task as TaskSchema;
use Salesfloor\Schemas\Customer as CustomerSchema;
use Salesfloor\Schemas\TaskCategory as TaskCategorySchema;

/**
* Tasks Model
*
* Copyright 2017 - Salesfloor
*/

/**
 * TODO: Dynamic properties will result in fatal error in php 9.
 * We have so many properties that are created dynamically, let's ignore them for now.
 */
#[\AllowDynamicProperties]
class Task extends Base implements JsonApiInterface
{
    const ID_FIELD     = 'id';
    const MAPPER_MODEL = 'Task';

    const STATUS_UNRESOLVED   = 'unresolved';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_DISMISSED = 'dismissed';
    const AUTO_DISMISS_NOTE = 'System Auto-Dismiss';

    const REMINDER_DUE = 'due';
    const REMINDER_OVERDUE = 'overdue';
    const REMINDER_UPCOMING = 'upcoming';

    // All Statuses
    public static $statuses = [
        self::STATUS_UNRESOLVED,
        self::STATUS_RESOLVED,
        self::STATUS_DISMISSED,
    ];
    // Statuses representation a type of resolution that should have resolution_date
    public static $resolutionStatuses = [
        self::STATUS_RESOLVED,
        self::STATUS_DISMISSED,
    ];

    const TYPE_MANUAL    = 'manual';
    const TYPE_AUTOMATED = 'automated';
    public static $types = [
        self::TYPE_MANUAL,
        self::TYPE_AUTOMATED,
    ];

    // ATTN: Adding new AUTOMATED_TYPE_* will require:
    // 1. Updating DB enum 2. creating the constant below 3. creating Scanner with TASK_TYPE set to new const
    const AUTOMATED_TYPE_NAG_SHARE_UPDATE = 'nag_share_update';
    const AUTOMATED_TYPE_NAG_UPDATE_STOREFRONT_PRODUCTS = 'nag_update_storefront_products';
    const AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION = 'new_retailer_transaction';
    const AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED = 'new_retailer_transaction_filtered';
    const AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE = 'new_retailer_transaction_filtered_multiple';
    const AUTOMATED_TYPE_NEW_REP_TRANSACTION = 'new_rep_transaction';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE = 'retailer_customer_soon_to_lapse';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED = 'retailer_customer_soon_to_lapse_filtered';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_SECONDARY_FILTERED = 'retailer_customer_soon_to_lapse_secondary_filtered';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_EVENT = 'retailer_customer_stats_registry_event';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_FOLLOWUP = 'retailer_customer_stats_registry_followup';
    const AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME = 'nag_onboarding_update_about_me';
    const AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC = 'nag_onboarding_upload_profile_pic';
    const AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA = 'nag_onboarding_connect_social_media';
    const AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS = 'nag_onboarding_add_contacts';
    const AUTOMATED_TYPE_RETAILER_CORPORATE = 'corporate';
    const AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED = 'retailer_transaction_employee_assigned';
    const AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE = 'retailer_transaction_employee_assigned_multiple';
    const AUTOMATED_TYPE_TRANSACTION_DISTRIBUTION_BY_STORES = 'transactions_distribution_by_stores';
    const AUTOMATED_TYPE_TRANSACTION_CANCELLED_FOLLOW_UP = 'cancelled_transaction_follow_up';
    const AUTOMATED_TYPE_RETAILER_TRANSACTION_IMPORTED = 'retailer_transaction_imported';
    const AUTOMATED_TYPE_REP_TRANSACTION_IMPORTED = 'rep_transaction_imported';
    const AUTOMATED_CORPORATE_IMPORTED = 'corporate_imported';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT = 'retailer_customer_event';
    const AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT_MULTIPLE = 'retailer_customer_event_multiple';

    // List of valid automated types. Used by the Automated Task scanners
    // Please dont put any non automated task type here, for example: self::AUTOMATED_TYPE_RETAILER_CORPORATE
    // and it will break the cron SearchForAutomatedTasks.php if added here.
    public static $automated_types = [
        self::AUTOMATED_TYPE_NAG_SHARE_UPDATE,
        self::AUTOMATED_TYPE_NAG_UPDATE_STOREFRONT_PRODUCTS,
        self::AUTOMATED_TYPE_NEW_REP_TRANSACTION,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_SECONDARY_FILTERED,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_EVENT,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_FOLLOWUP,
        self::AUTOMATED_TYPE_TRANSACTION_DISTRIBUTION_BY_STORES,
        self::AUTOMATED_TYPE_TRANSACTION_CANCELLED_FOLLOW_UP,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT,
    ];

    public static $automated_types_multiple = [
        self::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED_MULTIPLE,
        self::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED_MULTIPLE,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_EVENT_MULTIPLE,
    ];


    public const AUTOMATED_TYPES_IMPORT = [
        self::AUTOMATED_TYPE_RETAILER_TRANSACTION_IMPORTED,
        self::AUTOMATED_TYPE_REP_TRANSACTION_IMPORTED,
        self::AUTOMATED_CORPORATE_IMPORTED
    ];

    public const AUTOMATED_TYPES_SYSTEM = [
        self::AUTOMATED_TYPE_NAG_SHARE_UPDATE,
        self::AUTOMATED_TYPE_NAG_UPDATE_STOREFRONT_PRODUCTS,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_UPDATE_ABOUT_ME,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_UPLOAD_PROFILE_PIC,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_CONNECT_SOCIAL_MEDIA,
        self::AUTOMATED_TYPE_NAG_ONBOARDING_ADD_CONTACTS,
    ];

    public const AUTOMATED_TYPES_FOLLOW_UP = [
        self::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION,
        self::AUTOMATED_TYPE_NEW_RETAILER_TRANSACTION_FILTERED,
        self::AUTOMATED_TYPE_NEW_REP_TRANSACTION,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_FILTERED,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_SOON_TO_LAPSE_SECONDARY_FILTERED,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_EVENT,
        self::AUTOMATED_TYPE_RETAILER_CUSTOMER_STATS_REGISTRY_FOLLOWUP,
        self::AUTOMATED_TYPE_RETAILER_TRANSACTION_EMPLOYEE_ASSIGNED,
        self::AUTOMATED_TYPE_TRANSACTION_DISTRIBUTION_BY_STORES,
        self::AUTOMATED_TYPE_TRANSACTION_CANCELLED_FOLLOW_UP,
        self::AUTOMATED_TYPE_RETAILER_TRANSACTION_IMPORTED,
        self::AUTOMATED_TYPE_REP_TRANSACTION_IMPORTED,
    ];

    // for history data of importer task, it could be null/empty with automated type
    public const AUTOMATED_TYPES_CORPORATE = [
        self::AUTOMATED_TYPE_RETAILER_CORPORATE,
        self::AUTOMATED_CORPORATE_IMPORTED,
    ];

    /**
     * Id of the task
     * @var integer
     */
    public $id;

    /**
     * Rep ID that task belongs to
     * (In team mode will represent the FIRST store user ID)
     * @var integer
     */
    public $user_id;

    /**
     * OPTIONAL link to a contact's ID
     * @var integer
     */
    public $customer_id;

    /**
     * OPTIONAL link to a TaskCategory's ID
     * TaskCategories include entries such as 'Generic', 'Services', 'Promotions'
     * @var integer
     */
    public $task_category_id;

    /**
     * The status of the task
     * DEFAULT: 'unresolved' which tasks are created in
     * @var enum('unresolved', 'resolved', 'dismissed')
     */
    public $status;

    /**
     * The type of the task
     * In the future may include 'manager', 'imported'
     * DEFAULT: 'manual' (Created by user on mobile)
     * @var enum('manual', 'automatic')
     */
    public $type;

    /**
     * The type of 'automated' task. Represents the Scanner that was used to find this task
     * Should only be filled when task is type => 'automated'
     * DEFAULT: null
     * @var enum('nag_share_update','nag_update_storefront_products','new_retailer_transaction','new_retailer_transaction_filtered','retailer_customer_soon_to_lapse','retailer_customer_soon_to_lapse_filtered')
     */
    public $automated_type;

    /**
     * The description of the task work to be completed
     * (There is no title to the task so this is mandatory)
     * @var string
     */
    public $details;

    /**
     * Note that MUST be entered when transitioning task to 'resolved' status
     * If the task is in 'unresolved', or 'dismissed' it will be NULL
     * @var string
     */
    public $resolution_note;

    /**
     * When the task was transitioned from 'unresolved' to EITHER 'resolved' or 'dismissed'
     * @var timestamp
     */
    public $resolution_date;

    /**
     * When the system should notify task owner (user_id) about the tasks
     * Usually within the hour set. Cron should run hourly, reminder date should be
     * limited to HOUR precision
     * @var timestamp
     */
    public $reminder_date;

    /**
     * Keep track of the last time the cron ran, so we don't processed them twice and knows if we need to resend a
     * notification if the reminder_date change
     * @var timestamp
     */
    public $last_reminder_date;

    /**
     * Automatically filled when Task row is created
     * @var timestamp
     */
    public $created_at;

    /**
     * Should be updated everytime that row is updated
     * @var timestamp
     */
    public $updated_at;

    /**
     * Corporate tasks id if this task is generated from Corporate task
     * @var integer
     */
    public $parent_id ;

    /**
     * @inheritdoc
     */
    public $fields = [
        'id'                 => self::TYPE_PROTECTED,
        'user_id'            => self::TYPE_PUBLIC,
        'customer_id'        => self::TYPE_PUBLIC,
        'task_category_id'   => self::TYPE_PUBLIC,
        'status'             => self::TYPE_PUBLIC,
        'type'               => self::TYPE_PUBLIC,
        'details'            => self::TYPE_PUBLIC,
        'resolution_note'    => self::TYPE_PUBLIC,
        'resolution_date'    => self::TYPE_PUBLIC,
        'reminder_date'      => self::TYPE_PUBLIC,
        'last_reminder_date' => self::TYPE_PUBLIC,

        'created_at'         => self::TYPE_PROTECTED,
        'updated_at'         => self::TYPE_PROTECTED,
        'automated_type'     => self::TYPE_PUBLIC, // @WARNING order of this affects the importer, for some yet unknown reason
        'parent_id'         => self::TYPE_PUBLIC,
    ];

    public $defaults = [
        'status'           => self::STATUS_UNRESOLVED,
        'type'             => self::TYPE_MANUAL,
    ];

    public $required = [
        'user_id',
        'status',
        'type',
        'details',
        'reminder_date',
    ];

    public $availableVirtualFields = [
        'is_due',
        'is_overdue',
        'is_upcoming',
        'rep_transaction_trx_id',
        'retailer_transaction_thread_id',
        'repName',
        'storeName',
    ];

    protected $jsonApiIncludePaths = [
        CustomerSchema::TYPE,
        TaskCategorySchema::TYPE,
    ];

    protected $jsonApiFilteringExtraParameters = ['store_ids', 'user_ids', 'is_due', 'is_overdue', 'is_upcoming'];

    /**
     * Constructs the class name for the requested $automatedType with $namespace or without
     * @param string $automatedType
     * @param bool $namespace
     * @return string
     * @throws \Exception
     */
    public static function convertAutomatedTypeToClassName($automatedType, $namespace = true)
    {
        if (!in_array($automatedType, self::$automated_types)) {
            throw new \Exception('Invalid automated_type received: ' . $automatedType);
        }
        $location = "Salesfloor\\Services\\Tasks\\Automated\\";
        $fileName = str_replace(' ', '', ucwords(str_replace("_", " ", $automatedType)));
        return ($namespace ? $location : '') . $fileName;
    }

    public function getSchemasProvider()
    {
        return [
            Task::class => TaskSchema::class,
            Customer::class => CustomerSchema::class,
            TaskCategory::class => TaskCategorySchema::class,
        ];
    }
    protected function prepareRelations()
    {
        $customer = new Relation(
            new OneToOne(),
            "\Salesfloor\Models\Customer",
            CustomerSchema::TYPE,
            new RelationJoin(
                'customers.manager',
                [
                    [
                        'source'      => 'customer_id',
                        'destination' => 'ID',
                    ],
                ]
            )
        );

        $taskCategory = new Relation(
            new OneToOne(),
            "\Salesfloor\Models\TaskCategory",
            TaskCategorySchema::TYPE,
            new RelationJoin(
                'task_categories.manager',
                [
                    [
                        'source'      => 'task_category_id',
                        'destination' => 'id',
                    ],
                ]
            )
        );

        $this->relations = [
            CustomerSchema::TYPE => $customer,
            TaskCategorySchema::TYPE => $taskCategory,
        ];
    }
}
