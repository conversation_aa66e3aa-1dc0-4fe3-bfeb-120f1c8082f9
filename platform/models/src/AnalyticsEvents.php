<?php

declare(strict_types=1);

namespace Salesfloor\Models;

use Salesfloor\Models\Base;
use Salesfloor\Schemas\AnalyticsEvents as AnalyticsEventsSchema;

class AnalyticsEvents extends Base
{
    /**
     * An id
     *
     * @var int
     */
    public $id;

    /**
     * event
     *
     * @var string
     */
    public $event;

    /**
     * dt
     *
     * @var string
     */
    public $dt;

    /**
     * user_id
     *
     * @var int|null
     */
    public $user_id;

    /**
     * device_type
     *
     * @var string|null
     */
    public $device_type;

    /**
     * os_name
     *
     * @var string|null
     */
    public $os_name;

    /**
     * os_version
     *
     * @var string|null
     */
    public $os_version;

    /**
     * browser_name
     *
     * @var string|null
     */
    public $browser_name;

    /**
     * browser_version
     *
     * @var string|null
     */
    public $browser_version;

    /**
     * app_version
     *
     * @var string|null
     */
    public $app_version;

    /**
     * ip_address
     *
     * @var string|null
     */
    public $ip_address;

    /**
     * user_agent
     *
     * @var string|null
     */
    public $user_agent;

    /**
     * event_data
     *
     * @var array|null
     */
    public $event_data;

    /**
     * created_at
     *
     * @var string
     */
    public $created_at;

    /**
     * @inheritdoc
     */
    public $fields = [
        'id' => self::TYPE_PROTECTED,
        'event' => self::TYPE_PUBLIC,
        'dt' => self::TYPE_PUBLIC,
        'user_id' => self::TYPE_PUBLIC,
        'device_type' => self::TYPE_PUBLIC,
        'os_name' => self::TYPE_PUBLIC,
        'os_version' => self::TYPE_PUBLIC,
        'browser_name' => self::TYPE_PUBLIC,
        'browser_version' => self::TYPE_PUBLIC,
        'app_version' => self::TYPE_PUBLIC,
        'ip_address' => self::TYPE_PUBLIC,
        'user_agent' => self::TYPE_PUBLIC,
        'event_data' => self::TYPE_PUBLIC,
        'created_at' => self::TYPE_PROTECTED,
    ];

    /**
     * @inheritdoc
     */
    public $relationships = [
        // NOP
    ];

    //////////////////////////////////////////////////////////////////////////////////
    ///
    /// Json API information
    ///

    /**
     * Link model to Schema. Used by our 3rd party library (json-api)
     *
     * @return mixed
     */
    public function getSchemasProvider()
    {
        return [
            AnalyticsEvents::class => AnalyticsEventsSchema::class,
        ];
    }

    protected function prepareRelations()
    {
        // NOP
    }
}
