<?php

namespace Salesfloor\Models;

use Salesfloor\Schemas\PhoneNumberDetails as PhoneNumberDetailsSchema;

class PhoneNumberDetails extends Base
{
    const CARRIER_TYPE_MOBILE = 'mobile';
    const CARRIER_TYPE_LANDLINE = 'landline';
    const CARRIER_TYPE_VOIP = 'voip';
    const CARRIER_TYPE_UNKNOWN = 'unknown';

    const VALID_CARRIER_TYPE_VALUES = [
        self::CARRIER_TYPE_MOBILE,
        self::CARRIER_TYPE_LANDLINE,
        self::CARRIER_TYPE_VOIP,
        self::CARRIER_TYPE_UNKNOWN,
        null,
    ];

    /**
     * An id
     *
     * @var int
     */
    public $id;

    /**
     * phone number
     *
     * @var string
     */
    public $phone_number;

    /**
     * carrier type
     *
     * @var string
     */
    public $carrier_type;

    /**
     * created at
     *
     * @var string
     */
    public $created_at;

    /**
     * updated at
     *
     * @var string
     */
    public $updated_at;

    /**
     * @inheritdoc
     */
    public $fields = [
        'id' => self::TYPE_PUBLIC,
        'phone_number' => self::TYPE_PUBLIC,
        'carrier_type' => self::TYPE_PUBLIC,
        'created_at' => self::TYPE_PUBLIC,
        'updated_at' => self::TYPE_PUBLIC,
    ];

    //////////////////////////////////////////////////////////////////////////////////
    ///
    /// Json API information
    ///

    /**
     * Link model to Schema. Used by our 3rd party library (json-api)
     *
     * @return array
     */
    public function getSchemasProvider()
    {
        return [
            PhoneNumberDetails::class => PhoneNumberDetailsSchema::class,
        ];
    }
}
