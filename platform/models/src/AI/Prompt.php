<?php

declare(strict_types=1);

namespace Salesfloor\Models\AI;

use Salesfloor\Models\Base;
use Salesfloor\Schemas\AI\Prompt as PromptSchema;

class Prompt extends Base
{
    /**
     * An id
     *
     * @var int
     */
    public $id;

    /**
     * User ID who created the prompt
     *
     * @var int
     */
    public $user_id;


    /**
     * The custom prompt text
     *
     * @var string
     */
    public $title;

    /**
     * The custom prompt text
     *
     * @var string
     */
    public $prompt;

    /**
     * Created timestamp
     *
     * @var string
     */
    public $created_at;

    /**
     * Updated timestamp
     *
     * @var string
     */
    public $updated_at;

    /**
     * @inheritdoc
     */
    public $fields = [
        'id' => self::TYPE_PROTECTED,
        'user_id' => self::TYPE_PUBLIC,
        'title' => self::TYPE_PUBLIC,
        'prompt' => self::TYPE_PUBLIC,
        'created_at' => self::TYPE_PROTECTED,
        'updated_at' => self::TYPE_PROTECTED,
    ];

    /**
     * @inheritdoc
     */
    public $relationships = [
        // NOP
    ];

    //////////////////////////////////////////////////////////////////////////////////
    ///
    /// Json API information
    ///

    /**
     * Link model to Schema. Used by our 3rd party library (json-api)
     *
     * @return mixed
     */
    public function getSchemasProvider()
    {
        return [
            Prompt::class => PromptSchema::class,
        ];
    }

    protected function prepareRelations()
    {
        // NOP
    }
}
