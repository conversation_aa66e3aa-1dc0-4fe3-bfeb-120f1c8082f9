<?php

declare(strict_types=1);

use Salesfloor\Services\Application\Routing as R;

return [
    'aimessage-generate' => [
        R::PATTERN => '/ai/message',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Message:generate',
                R::VERSION_MANAGER => ['aimessage' => 'AI\Message'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['post'],
    ],

    'aimessage-refine' => [
        R::PATTERN => '/ai/message/refinement',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Message:refine',
                R::VERSION_MANAGER => ['aimessage' => 'AI\Message'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['post'],
    ],

    'aimessage-prompt' => [
        R::PATTERN => '/ai/prompt',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Prompt:create',
                R::VERSION_MANAGER => ['aiprompt' => 'AI\Prompt'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['post'],
    ],

    'aimessage-prompt-list' => [
        R::PATTERN => '/ai/prompt',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Prompt:getList',
                R::VERSION_MANAGER => ['aiprompt' => 'AI\Prompt'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['get'],
    ],

    'aimessage-prompt-get' => [
        R::PATTERN => '/ai/prompt/{id}',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Prompt:getOne',
                R::VERSION_MANAGER => ['aiprompt' => 'AI\Prompt'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['get'],
    ],

    'aimessage-prompt-update' => [
        R::PATTERN => '/ai/prompt/{id}',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Prompt:update',
                R::VERSION_MANAGER => ['aiprompt' => 'AI\Prompt'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['patch'],
    ],

    'aimessage-prompt-delete' => [
        R::PATTERN => '/ai/prompt/{id}',
        R::VERSIONS => [
            R::VERSION_NAME_DEFAULT => [
                R::VERSION_CONTROLLER => 'AI\V1\Prompt:delete',
                R::VERSION_MANAGER => ['aiprompt' => 'AI\Prompt'],
                R::VERSION_JSON_API => true,
            ],
        ],
        R::METHODS => ['delete'],
    ],
];
