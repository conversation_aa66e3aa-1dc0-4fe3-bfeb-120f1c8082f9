<?php

//////////////////////////////////////////////////////////////////////////////////
///
/// https://salesfloor.atlassian.net/wiki/spaces/DEV/pages/255131649/Secrets+Management+-+Secrets+Reference
///
/// Secrets are stored here from now on
///


//////////////////////////////////////////////////////////////////////////////////
///
/// Section INTERNAL
///

// ------------------- API Authentication -------------------
$configs['sf-api'] = [
    'apikey'  => [
        'key'      => '1234',            // easy token for test
        'header'   => 'SF-APIKEY',    // header we look for the key
    ],
    'jwt'     => [
        'secret' => '~2jq9G9!%tVQG#:s8Wa{>sg<aT-%*2LFxdD]_-u#]:D*xSm$FE;=T"WvJsvmJ7jvf4)]F:',
        'exp'    => 600,       // 10 minutes token life
        'ref'    => 86400 * 7 * 4 * 2,    // 8 weeks
        'header' => 'Authorization',
        'prefix' => 'Bearer',
    ],
    'cookie'  => [
        'name' => $configs['cookie_prefix'] . 'SF-TOKEN',   // When you log in wordpress, this cookie will be created with the token. It will be append to all xhr call in header
    ],
    'enabled' => true, // Security can now be set to true since the addition of applyAuthorization / applyDefaultAuthorization functions in the CurlHelper
];

// ------------------- Encrypt/Decrypt -------------------
$configs['sf.security.secretkey'] = 'def0000076a412725145a3fa9df7d03b0bbe165230ce52f451823e4e27fe813887b968a477b854df82cdb8187372c59582a53b5d6582f989cda862f33128c3231e7d278d';

// ------------------- Database -------------------
$configs['wordpress.db.password'] = 'AvFAN4LpKl1EYH';
$configs['mysql.password'] = 'AvFAN4LpKl1EYH';

// ------------------- Wordpress -------------------
$configs['wordpress.auth_key']         = '3lH!A3`[!m>(4AR<2[KFw|OayNGY*&2-xRO1%;R@R+)o]{-[*40$9>?etests-dev';
$configs['wordpress.secure_auth_key']  = '0ZujT%*OY_LE(g#2vHJ0aJb&r]K6nhZd[ zT+HOtOH6=e+6Mt~H*F7G-tests-dev';
$configs['wordpress.logged_in_key']    = '4.+3nuTM1^=4LT~A-HH5,/pM&i+ysFS-}@p!-g_JK#=?++M;MZ@}n]omtests-dev';
$configs['wordpress.nonce_key']        = 'Xy4| Tt#8w@2)~ns5yz+xAJ%dN01u4Q-k[u!Sun>b1kI<,]n  ]X$,mPtests-dev';
$configs['wordpress.auth_salt']        = 'a<ptuYkPVXv_wwx_Ez>o<w/C?%:dCmo-3gJj|sw_|-A`E82FSK&~KLy4tests-dev';
$configs['wordpress.secure_auth_salt'] = '5k^DA#+e]bE0TM5AU$g3qIe,+lzU}zex| K985IoMfa<6ED+d-_yQ@Cptests-dev';
$configs['wordpress.logged_in_salt']   = 'NuG(^*_*?aS%*3[se&+k$6Jg;|HO60G,wfy|y#%~x{RR-BUnYv.w1ckqtests-dev';
$configs['wordpress.nonce_salt']       = '0F72i}wxwmb?W*d!)fK)<<|Yk(3^{+<*~x;]+7m%bcD_U]FUs0v|x!D-tests-dev';

// ------------------- Onboarding -------------------
$configs['sf.onboarding.security.secretkey'] = '239f8hiuv204vihwfRiwoeifjGRG$wqewoek#$5';

//////////////////////////////////////////////////////////////////////////////////
///
/// Section EXTERNAL
///

// ------------------- Amazon AWS -------------------
$configs['aws.queue.key'] = '********************';
$configs['aws.queue.secret'] = 'z7To6SBbUPIFvUoU20umqH/Ow7e4Y+PXn7BnRqUs';

$configs['s3.key'] = '********************';
$configs['s3.secret'] = 'z7To6SBbUPIFvUoU20umqH/Ow7e4Y+PXn7BnRqUs';

$configs['sns.key'] = '********************';
$configs['sns.secret'] = 'B9gpJGW3HzXWh3txAlVf45A5uOHFXwyjXUN2EGnV';

$configs['logrotate.s3.key'] = '********************';
$configs['logrotate.s3.secret'] = 'GuLb3Xt9sErgerRuJxh6EhXsdCPNuBoCKe0Zz/ie';

// ------------------- Firebase -------------------
$configs['firebase.token'] = 'uX6phAd8lwarYsQDDq5z6loj1UK4bPtgQBkopOS4';

// ------------------- Google Maps -------------------
// https://console.developers.google.com/apis/credentials/key/0?project=salesfloor.net:api-project-945502930830
$configs['google.api_key'] = 'AIzaSyCruwzEM1-X5tiyisQym90z5yVKEZw7x0E';

// ------------------- LinkedIn -------------------
$configs['linkedin.key'] = '78ou0qano3vbtz';
$configs['linkedin.secret'] = 'iWefTgvCHYoezMcn';

// ------------------- Mandrill -------------------
// Everyone uses the same Mandrill key.
$configs['mandrill.api_key'] = '**********************';

// ------------------- MaxMind -------------------
$configs['maxmind.key'] = '4o4jogMkvhEKjFXf';

// ------------------- Oauth -------------------
$configs['oauth.io.key'] = 'U7Gw0dWc2hfk55sZjkyYcR2hhJU';
$configs['oauth.io.secret'] = 'haaOXGLsamrwL70dZ6RyReEqjbs';

// ------------------- Recaptcha -------------------
$configs['recaptcha.key'] = '6LdIUPASAAAAAO9BphszhxtbmX1-GEdwIVQYpl25';

// ------------------- SendGrid -------------------
// $configs['sf.sendgrid.api_key'] = '*********************************************************************';
$configs['sf.sendgrid.api_key'] = '*********************************************************************';

// ------------------- Slack -------------------
$configs['slack.key'] = '*******************************************************';

// ------------------- Twilio -------------------
// Keep previous value. We don't have twilio in tests.
// We must have a value here (even if it's not valid) because text we setup the client on construct
$configs['messaging.text.twilio.account_sid'] = 'FAKEACCOUNTSID';
$configs['messaging.text.twilio.api_key_id'] = 'FAKEAPIKEY';
$configs['messaging.text.twilio.api_key_secret'] = 'FAKEAPISECRET';

// ------------------- Twitter -------------------
$configs['twitter.key'] = '*************************';
$configs['twitter.secret'] = 'ks027rf2PEQEbNEEsp4oOuRdFuFuU9JWj7T1ApsTpPo1VK4KHk';

// ------------------- salesfloor_admin -------------------
$configs['sfadmin.user_login'] = 'salesfloor_admin';
$configs['sfadmin.password'] = 'bCaref00l!';

// ------------------- Branch deep linking -------------------
$configs['branch.base_url'] = 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT';

// ------------------- Algolia -------------------
// Algolia admin account with write permissions
$configs['algolia.admin.app_id'] = 'WNMJ4Q8002';
$configs['algolia.admin.api_key'] = '********************************';
// Algolia search account with read permissions only
$configs['algolia.read.app_id'] = 'WNMJ4Q8002';
$configs['algolia.read.api_key'] = '********************************';

// ------------------- Redshift -------------------
$configs['redshift.username'] = 'sfred';
$configs['redshift.password'] = 'wi0Paequii7uuChoh5bo1iedieJ1Uwei';

// ------------------- Sendgrid Master account -------------------
$configs['sf.sendgrid.master_api_key'] = '*********************************************************************';

// Credentials for services used by the store importer
$configs['locationiq.key'] = 'a0a704f11e4361'; // locationiq.com
$configs['geonames.user'] = 'salesfloor'; // geonames.org

// ------------------- Retailer API Configs -------------------
$configs['retailer.api.password'] = '$P$FnSAr5J.b4NcCGH/q1489oRJW/YoKY/';  // raw password: 1234


// SF-20489 Cloudinary secrets for mobile
$configs['mobile.cloudinary.apikey'] = '***************';
$configs['mobile.cloudinary.cloudName'] = 'salesfloor-net';

// SF-20489 AWS s3 secrets for mobile
$configs['mobile.s3.accessKeyId'] = '********************';
$configs['mobile.s3.secretAccessKey'] = 'NC+TV6AjQOimdDNhyhcgwDv9eYC2teDuvpUb6ERu';
$configs['mobile.s3.region'] = 'us-east-1';
$configs['geonames.token'] = 'LxT2JE5MhMwEvv6Vn2JwuRXjmYY';

// ------------------- Facebook -------------------
$configs['facebook.app_id'] = '909457515744007';
$configs['facebook.app_secret'] = '********************************';

// ------------------- Encryption -------------------
$configs['encryption.use_case.sodium_aead_chacha20poly1305.secret_key'] = 'VFGaTPYgOeX_NEyfdKaEsIWhdjGAEGSk2adRYwR1_K4';

// ------------------- Brands -------------------
$configs['shop_by_brand.services.username'] = 'swapl';
$configs['shop_by_brand.services.password'] = 'zGzrKBXyBP';

// ----- GCP ---
$configs['gcp.s3.key'] = 'GOOG1EVEPYLAUI6NOZ35NAXC32QB7OLTLXWCS4SU4L43XPDGCHWEEU5GPOGKI';
$configs['gcp.s3.secret'] = 'UR4iYLl5UTYME0SUGcd7vLUm+C+2uoZ7iNxwj7fj';

// ----- GCP HMAC S3 Compat Credentials---
$configs['gcp.s3.secret.access_key_file'] = '/opt/secrets/cloud-storage/access_key_id';
$configs['gcp.s3.secret.secret_access_key_file'] = '/opt/secrets/cloud-storage/secret_access_key';

// ----- FCM ---
$configs['fcm.key'] = 'AAAA-TB3gvw:APA91bGg96cBWPH12EXeSb8XxCnnhrdkVrlhqfDbtyRFO-fZ2YVKqwspf0dYFgNf8W0JVVFRWr_eo0kKsjGKX_HnpBWDXdXgCToe4BYanGIIujZHqzOiYASajSC87AeIV7pYhSAy1J8JXclPf8XtB9NB4VAiS4nNqg';
$configs['fcm.project_id'] = 'radiant-fire-9638';

// ----- Elasticsearch ---
$configs['gcp.elasticsearch.endpoint'] = 'https://sf-env-test-8.es.northamerica-northeast1.gcp.elastic-cloud.com:9243';
$configs['gcp.elasticsearch.username'] = 'elastic';
$configs['gcp.elasticsearch.password'] = 'u2LtT93yo4sGyLtZqJCbHk9p';
