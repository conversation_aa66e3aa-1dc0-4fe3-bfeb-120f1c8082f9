<?php

/**
 * Common Application Configurations
 *
 * Copyright 2014 - Salesfloor
 */

use Salesfloor\Models\Customer;
use libphonenumber\PhoneNumberFormat;
use Salesfloor\Services\NameSuggester;
use Salesfloor\Services\Oauth2\Oauth2Service;
use Salesfloor\Services\Oauth2\Provider\BaseOauth2Provider;
use Salesfloor\Services\Tasks\Automated\CancelledTransactionFollowUp;
use Symfony\Component\Validator\Constraints\Url;
use Symfony\Component\Validator\Constraints\Date;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Length;
use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Required;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Config\ConfigsLoader\ConfigsHelper;
use Salesfloor\Models\CustomerAttribute as CustomerAttributeModel;
use Salesfloor\Services\Importer\Product\Importer as ProductImporter;
use Salesfloor\Services\Importer\RepTransaction\Importer as TransactionImporter;
use Salesfloor\Models\Task as TaskModel;
use Salesfloor\Services\Tasks\Features\TaskAutoDismissProcessor;
use Salesfloor\Models\Services\Appointment;

/***************************************************
 *
 * Publish config to the .ben/config page
 *
 * How to:
 *  - Add doc block (Mandatory)
 *  - Add new tag called "show-config" with value "public" (mandatory)
 *  - Add summary / description (one or multi line) (optional)
 *  - Update functional test with new config (mandatory)
 *
 *  ******************************* WARNING *******************************
 *  *
 *  *   If you put show-config public on a secret, it will be displayed to all
 *  *   salesfloor_admin via the .ben section.
 *  *
 *  ***********************************************************************
 *
 **************************************************/

/**************************************************
 *
 * Note: Three configs are set before this file runs:
 * - 'retailer.short_name'
 * - 'env'
 * - 'source'
 *
 * They MUST NOT be added here since their values are dynamic.
 * But they can be used here. For instance:
 *
 * ```
 * $configs['chat.num_sec_before_heartbeat'] = $configs['env'] === Loader::ENV_STACK_PROD ? 1800 : 300;
 * ```
 *
 * See: services/src/Config/ConfigsLoader/DefaultConfigLoader.php
 */

 /**
  * Alias of 'retailer.short_name'.
  *
  * @deprecated use 'retailer.short_name' instead.
  */
$configs['retailers.current'] = $configs['retailer.short_name'];

/**
 * Yet another alias of 'retailer.short_name'.
 *
 * @deprecated use 'retailer.short_name' instead.
 */
$configs['retailer.idstr'] =  $configs['retailer.short_name'];

 /**
  * Alias of 'env'.
  *
  * @deprecated use 'env' instead.
  */
$configs['stack'] = $configs['env'];

$configs['retailer.brand'] = null; // TODO this should be removed when we get rid of multibrand.
$configs['retailer.brand2host'] = null; // TODO this should be removed when we get rid of multibrand.

$configs['retailer.addr'] = '127.0.0.1'; // TODO: only used in instance-webserver/src/salesfloor/sfadmin/generate_incoming_mail.php can be removed?

/******************************************************************************
 * Domains and URL configs
 */

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['wordpress.cookie_domain'] = 'salesfloor.net';

// Rest API

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.api_domain'] = implode(
    ConfigsHelper::getDelimiterUrls($env),
    [
        $configs['retailer.short_name'],
        'api',
        $configs['env'] . '.salesfloor.net'
    ]
);

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.rest_api_url'] = 'https://' . $configs['retailer.api_domain'];

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @deprecated use 'retailer.rest_api_url' instead.
 * @feature Infra
 */
$configs['salesfloor_rest_api.host'] = $configs['retailer.rest_api_url'];

// Widgets

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.widgets_domain'] = implode(
    ConfigsHelper::getDelimiterUrls($env),
    [
        $configs['retailer.short_name'],
        'widgets',
        $configs['env'] . '.salesfloor.net'
    ]
);

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.widgets_url'] = 'https://' . $configs['retailer.widgets_domain'];

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @deprecated use 'retailer.widgets_domain' instead.
 * @feature Infra
 */
$configs['retailer.widget.host'] = $configs['retailer.widgets_domain'];

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @deprecated use 'retailer.widgets_url' instead.
 * @feature Infra
 */
$configs['salesfloor_widgets.host'] = $configs['retailer.widgets_url'];

// Wordpress, storefront, backoffice

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.webserver_domain'] = implode(
    ConfigsHelper::getDelimiterUrls($env),
    [
        $configs['retailer.short_name'],
        $configs['env'] . '.salesfloor.net'
    ]
);

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.webserver_url'] = 'https://' . $configs['retailer.webserver_domain'];

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @deprecated use 'retailer.webserver_url' instead.
 * @feature Infra
 */
$configs['salesfloor_storefront.host'] = $configs['retailer.webserver_url'];

/******************************************************************************
 * Multi-domain configs. Assumes we have a domain for 'en' and a domain for 'fr' (HBC).
 */

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.domain_key'] = 'main';

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['retailer.domains'] = [
    'en' => [
        // Rest API
        'retailer.api_domain' => $configs['retailer.api_domain'],
        'retailer.rest_api_url' => $configs['retailer.rest_api_url'],
        'salesfloor_rest_api.host' => $configs['salesfloor_rest_api.host'],
        // Widgets
        'retailer.widgets_domain' => $configs['retailer.widgets_domain'],
        'retailer.widgets_url' => $configs['retailer.widgets_url'],
        'retailer.widget.host' => $configs['retailer.widget.host'],
        'salesfloor_widgets.host' => $configs['salesfloor_widgets.host'],
        // Wordpress
        'retailer.webserver_domain' => $configs['retailer.webserver_domain'],
        'retailer.webserver_url' => $configs['retailer.webserver_url'],
        'salesfloor_storefront.host' => $configs['salesfloor_storefront.host'],
        'wordpress.cookie_domain' => 'salesfloor.net'
    ],
    // On lower envs, domains for HBC French (labaie vs. thebay) use salesfloor-ecom.net instead of salesfloor.net
    // On prod, the right domains are set by InfraConfigLoader
    'fr' => [
        // Rest API
        'retailer.api_domain' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.api_domain']),
        'retailer.rest_api_url' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.rest_api_url']),
        'salesfloor_rest_api.host' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['salesfloor_rest_api.host']),
        // Widgets
        'retailer.widgets_domain' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.widgets_domain']),
        'retailer.widgets_url' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.widgets_url']),
        'retailer.widget.host' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.widget.host']),
        'salesfloor_widgets.host' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['salesfloor_widgets.host']),
        // Wordpress
        'retailer.webserver_domain' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.webserver_domain']),
        'retailer.webserver_url' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['retailer.webserver_url']),
        'salesfloor_storefront.host' => str_replace('salesfloor.net', 'salesfloor-ecom.net', $configs['salesfloor_storefront.host']),
        'wordpress.cookie_domain' => 'salesfloor-ecom.net'
    ]
];

/**
 * This config is overridden by infraDerived
 *
 * @show-config public
 * @feature Infra
 */
$configs['retailer.host'] = '__dynamic__';

$configs['cache.prefix'] = $configs['env'] . ':' . $configs['retailer.short_name'];

// Dependencies
$configs['core.dependencies.use-container-app'] = true;

// Twig
$configs['twig.views_dir'] = PATH_VIEWS;

// Filters
$configs['algolia.filters.prices.options'] = [
    '0-50' => '$0 - $50',
    '50-150' => '$50 - $150',
    '150-300' => '$150 - $300',
    '300-500' => '$300 - $500',
    '500-1000' => '$500 - $1000',
    '1000-2000' => '$1000 - $2000',
    '>2000' => '$2000+',
];

$configs['algolia.filters.discounts.options'] = [
    '1' => 'On sale',
];

$configs['algolia.filters.ttl'] = 3600 * 12; // 12h

// Index generation configs
$configs['algolia.tablediff'] = 'sf_table_diff'; // Table that contains the diff
$configs['algolia.maxrecords'] = -1; // -1 is unlimited
// Arbitrarily chosen. A record is (from empirical testing)
$configs['algolia.recordsbybatch'] = 500;
$configs['algolia.index.reset'] = false; // If false, we use diff
// Algolia relevancyStrictness
$configs['algolia.relevancy_strictness'] = 90;
/**
 * If true, this stack will have own algolia database
 *
 * This feature should be true only for retailer that are not yet on production
 * @show-config public
 * @feature algolia
 */
$configs['algolia.newretailer'] = false; // If true, we create an index per env, otherwise we use the production index

// ********************************************

// https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html
$configs['aws.arn.account-id'] = '************';

/**
 * This config is overridden by InfraConfigLoader
 *
 * @show-config public
 * @feature Infra
 */
$configs['s3.bucket_name_prefix'] = 'sf-dev-';

/**
 * This config is overridden by infraDerived
 *
 * @show-config public
 * @feature Infra
 */
$configs['s3.bucket'] = '__dynamic__';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['s3.key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['s3.secret'] = 'fake';

$configs['s3.bucket.provider'] = 'gcp';
$configs['s3.bucket.region'] = 'us-east-1';

$configs['s3.image-bucket'] = 'salesfloor-assets';
$configs['s3.image-bucket.provider'] = 'aws';
$configs['s3.image-bucket.region'] = 'us-east-1';

// Remember that mobile has his own secret/key/region in the secret repo/folder.
// Search: "mobile.s3.region" for example.

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['mobile.s3.accessKeyId'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['mobile.s3.secretAccessKey'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['mobile.s3.region'] = 'us-east-1';

/**
 * For the mobile dashboard cutoff tasks reminder is older than this number of days back
 * @show-config public
 * @feature mobile-dashboard
 */
$configs['mobile.dashboard.task_cutoff_days_back'] = 30;

/**
 * Number of ms until cached api result expires
 * @show-config public
 * @feature mobile-caching
 */
$configs['mobile.api_cache_duration'] = 3 * 60 * 1000;

/**
 * If true, the retailer has SSE-KMS (encryption) enabled
 * @show-config public
 * @feature AWS/S3
 */
$configs['s3.bucket.encryption.enabled'] = false;

/**
 * Possible type of encryption. "AES256" or "aws:kms".
 *
 * For sonoma, devops decided not to only enforce it in the policy but set it to be used automatically.
 * In fact, this code is not required anymore.
 * We will keep it in case, we need it for kms (not tested) and if the bucket policy enforced it but,
 * the bucket doesn't apply it automatically.
 *
 * @show-config public
 * @feature AWS/S3
 */
$configs['s3.bucket.encryption.type'] = 'AES256';

/**
 * Defined signature used. Encrypted bucket MUST be v4.
 * https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingKMSEncryption.html
 *
 * @show-config public
 * @feature AWS/S3
 */
$configs['s3.bucket.signature'] = null;

// The main reason is we may have different bucket / signature / region (e.g: image-bucket)
// s3.signature is now s3.bucket.signature
// s3.region is now s3.bucket.region

/**
 *
 * https:// will be prefixed in php code
 * it will also prefix it with the bucket name such as
 * https://bucketname.s3.amazonaws.com
 */
$configs['s3.host'] = 'amazonaws.com';

// GCP Public domain
$configs['gcp.host'] = 'storage.googleapis.com';

$configs['s3.exports.prefix'] = 'Exports/';
$configs['s3.exports.monthly_prefix'] = 'MonthlyCsvs/';

/**
 * ONLY used for peru in CreateTransactionExports.php
 * the yearly transaction_yyyy.csv&transactionDetails_yyyy.csv will be uploaded to default bucket outbound folder
 */
$configs['s3.exports.want_unzipped'] = false;

$configs['s3.exports.columns.get-sales'] = null;
$configs['s3.exports.columns.get-sales-details'] = null;

/**
 * As Salesfloor internal' usage, we set a private bucket which could:
 * a. The files used for backoffice download
 * b. The un-encryption files to followup/debug with retailer's data issue
 * NOTE: Right now this feature is only enabled for some retailers as migrating gradually, and at the end it will set valid value to all retailers
 * ref:  https://salesfloor.atlassian.net/browse/PS-3649
 *
 * This config is overridden by infraDerived
 *
 * @show-config public
 * @feature Infra
 */
$configs['exporter.private.bucket'] = '__dynamic__';

$configs['salesfloor_storefront.addr'] = '127.0.0.1';

$configs['retailer.limit_rep_searches_to_country'] = true;
$configs['retailer.hq_address'] = "";
$configs['retailer.feed_url'] = "";
$configs['retailer.can_share_an_update'] = true;

$configs['products.default_img'] = 'http://res.cloudinary.com/salesfloor-net/image/upload/v1507828679/ann/Image-Coming-Soon-Placeholder.png';
$configs['products.root_category_id'] = "1";
$configs['catalog.min_expected_products'] = 1;
$configs['importer.buffer_size'] = 52428800; // 50 MiB

$configs['store_users_is_still_a_thing'] = false;

/**
 * TODO: configs 'retailer.num_top_picks'/'storefront.num_top_picks'/'storefront.available_product_grids' need to be centralized
 */
$configs['storefront.num_top_picks'] = 8;
$configs['storefront.have_secondary_products'] = true;
$configs['storefront.top_picks_strategies'] = ['DEALS'];
$configs['storefront.can_edit_events'] = false;
$configs['storefront.can_edit_posts'] = false;
$configs['use_new_arrivals_list'] = false;

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['allowed_origins'] = [
    $configs['retailer.widgets_domain'],
    $configs['salesfloor_storefront.host'],
    'https://127.0.0.1:8555', // I'm not sure where this is coming from (probably old local dev)
    'https://0.0.0.0:8555'
];
$configs['origin'] = null;

$configs['throttle.sidebar'] = false;

$configs['debug'] = false;

$configs['kpi_email.n_top_stores'] = 3;

$configs['kpi_email.email_override'] = null;
$configs['kpi_email.demo_addresses'] = null;

$configs['defaults.rep.selling_mode'] = [
    Perms::USER => '1',
    Perms::STORE_MANAGER => '1',
    Perms::MANAGER => '0',
    Perms::CORP_ADMIN => '0',
    Perms::SF_ADMIN => '0',
];

Perms::$permissionsMap = [
    'user-management' => Perms::STORE_MANAGER,
    'set-rep-status' => Perms::STORE_MANAGER,
    'store-management' => Perms::MANAGER,
    'create-user' => Perms::CORP_ADMIN,
    'view-admin-tab' => Perms::CORP_ADMIN,
    'show-admin-cols' => Perms::SF_ADMIN,
    'show-corp-admin-cols' => Perms::CORP_ADMIN,
    'see-ambassadors' => Perms::SF_ADMIN,
    'set-bulk-text-messaging' => Perms::CORP_ADMIN,
    'set-rep-text-messaging' => Perms::CORP_ADMIN,
    'set-rep-social-shop' => Perms::CORP_ADMIN,
    'see-all-tasks' => Perms::CORP_ADMIN,
    'unlock-rep' => Perms::CORP_ADMIN,
    'report.admin.all-associates' => Perms::CORP_ADMIN,
    'set-sidebar-outstanding-threshold' => Perms::CORP_ADMIN,
    'access-blocked-incoming-email-addresses' => Perms::SF_ADMIN,
    'delete-invite' => Perms::CORP_ADMIN,
    'corporate-task' => Perms::CORP_ADMIN,
    'reassign-appointment' => Perms::STORE_MANAGER,
    'asset-management' => Perms::CORP_ADMIN,
    'dynamic-routing-management' => Perms::SF_ADMIN,
    'feed-validation' => Perms::SF_ADMIN,
    'contextual-widgets-kpi' => Perms::CORP_ADMIN,
    'transactions-attribution' => Perms::CORP_ADMIN,
];

/**
 * Onboarding (retailer) initial date.
 *
 * This is used to know which date to display in the export dropdown. Using sf_user_activity table (dynamic) could
 * be good, but not necessarily the good value. To make it easier/quicker, just hardcode the start date.
 *
 * @show-config public
 */
$configs['retailer.application_start_date'] = '2016-03-01 00:00:00';

$configs['retailer.max_reps_per_store'] = 0; // 0 means unlimited

/**
 * @deprecated try to use config: ['retailer.rep.display_name_tmpl'], ['retailer.username_tmpl'] or ['retailer.alias_tmpl'] if possible
 */
$configs['retailer.findrep_name_fmt'] = 'fn_li';

/**
 * If true, you can foward to CS
 * @show-config public
 * @feature CS
 */
$configs['customerservice.can_forward'] = false;

/**
 * The email of the retailer CS
 * @show-config public
 * @feature CS
 */
$configs['customerservice.email'] = '<EMAIL>';

$configs['retailer.label_question'] = 'Ask & Answer';
$configs['retailer.label_appointment'] = 'Book Appointment';
$configs['retailer.label_finder'] = 'Personal Shopper';

// You must have this config and "is-smb" to have a request made via a phone number
/**
 * If true, and txt is enabled and have new SMS style, you get new channel (text) in service
 * NOTE: usually configs 'messaging.text.enabled' and 'retailer.services.channel.text.enabled' should have same value
 * @show-config public
 * @feature SMS
 */
$configs['retailer.services.channel.text.enabled'] = false;
$configs['retailer.services.channel.text.generate_reserve_and_save_number_for_noreply'] = true;

/**
 * All US retailer must set this as true because of the new A2P 10DLC regulation
 * @link https://support.twilio.com/hc/en-us/articles/1260800720410-What-is-A2P-10DLC-
 * @feature SMS
 */
$configs['retailer.services.channel.text.a2p10dlc.is_required'] = false;

/**
 * All US retailer must has a valid messaging service SID
 * This is message_service for rep
 * @feature SMS
 */
$configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = null;


/**
 * All US retailer must has a valid messaging service SID
 * This is message_service for no reply
 * @feature SMS
 */
$configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = null;

$configs['retailer.avatar_transform'] = [
    "width" => 250,
    "height" => 250,
    "crop" => "fill",
    "gravity" => "face",
];

$configs['retailer.multibrand'] = false;

/**
 * 'onboarding.allow_dupe_emails' is used to allow onboarding an user
 * when there is already an inactive user with the same email.
 *
 * By default it is set to false.
 * Retailers with SSO enabled should have this config set to true.
 */
$configs['onboarding.allow_dupe_emails'] = false;

$configs['update_products.sanity_check'] = null;

$configs['monolog.path'] = '/srv/www/sf_platform/current/log/';

/**
 * Number of seconds before sending notification by the app to the associates
 * without activity, asking if they are still available for Live chat.
 *
 * @show-config public
 */
$configs['chat.num_sec_before_heartbeat'] = $configs['env'] === Loader::ENV_STACK_PROD ? 1800 : 300;
$configs['chat.num_sec_disconnect_rep_not_confirm'] = 60;
/**
 * How old does a chat request have to be before we delete it from Firebase
 * @show-config public
 * @feature chat
 */
$configs['chat.request.maxduration'] = 300; // 5 minutes (A chat request duration is usually 2 minutes).

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['firebase.token'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader.
 * This is passed by Infra but not in dev, so the default value must be the dev one
 *
 * @feature Infra
 */
$configs['firebase.url'] = 'https://amber-heat-9378.firebaseio.com/';

/**
 * This config is overridden by InfraConfigLoader.
 * This is passed by Infra but not in dev, so the default value must be the dev one.
 *
 * This is used by FCM to get the access token. We cannot use the access token from the
 * service account from the main project (Even if it has permission) because it doesn't work
 * for batchImport.
 *
 * @feature Infra
 */
$configs['firebase.service_account'] = null;

/**
 * This config is overridden by InfraConfigLoader.
 * This is passed by Infra but not in dev, so the default value must be the dev one.
 *
 * @feature Infra
 */
$configs['common_firebase.service_account'] = null;

/**
 * This config is overridden by InfraConfigLoader.
 * Token for common firebase
 *
 * @feature Infra
 */
$configs['common_firebase.token'] = 'FrKImESGm4Hol1G095CVX3Al5YuVPznNoxAeifsF';

/**
 * This config is overridden by InfraConfigLoader.
 * Url for common firebase
 *
 * @feature Infra
 */
$configs['common_firebase.url'] = 'https://sf-common-firebase-default-rtdb.firebaseio.com/';

/**
 * Decide wither to override the current namespace we are fetching the config descriptions from
 * Options: ['default', 'dev', 'common', 'common_backup'] See ALLOWED_FIREBASE_NAMESPACES for full list
 * @feature config-descriptions
 */
$configs['config_descriptions.namespace'] = 'default';

/**
 * Number of reps to display
 * @show-config public
 * @feature chat
 */
$configs['chat.rep_pictures.number'] = 3;

/**
 * List possible default images for a rep
 * @show-config public
 * @feature chat
 */
$configs['chat.rep_pictures.default'] = [
    // 0 => 'http://images.com/test.jpg'
];

/**
 * Random default images list for reps who has not avatar uploaded
 * If this config is empty, then a default avatar($retailerName.'defaultrep.jpg') in cloudinary will be used
 * @show-config public
 */
$configs['retailer.rep_avatar_picture.random_default'] = [
    'retailer_common_default_circle_1.jpg',
    'retailer_common_default_circle_2.jpg',
    'retailer_common_default_circle_3.jpg',
    'retailer_common_default_circle_4.jpg',
    'retailer_common_default_circle_5.jpg',
];

/**
 * Force a random photo for a rep
 * @show-config public
 * @feature chat
 */
$configs['chat.rep_pictures.blacklist'] = [
    // 'INSERT_USERNAME_HERE' => true // for each name to black list
];
/**
 * How long to cache the rep's image when fetching all reps
 */
$configs['chat.rep_pictures.ttl'] = 21600; // 6h
/**
 * How long to cache the rep's image in queue
 */
$configs['chat.queue_rep_pictures.ttl'] = 60; // 1 minute

$configs['multibrand.is_active'] = false;
$configs['multibrand.other_brand_firebase'] = null;

/**
 * If true, we are in team mode
 * @show-config public
 */
$configs['retailer.storepage_mode'] = false;
$configs['retailer.url'] = 'http://www.example.com';

$configs['stats_db.host'] = null;
$configs['stats_db.user'] = null;
$configs['stats_db.name'] = null;
$configs['stats_db.port'] = null;


$configs['sf.import_unsubs.s3.provider'] = 'gcp';


/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 * @secret
 */
$configs['geonames.token'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['geonames.user'] = 'salesfloor';

$configs['geoip.s3bucket'] = 'sf-deployment-tarballs';
$configs['geoip.s3bucket.provider'] = 'gcp';
$configs['geoip.s3bucket.region'] = 'us-west-1';
$configs['geoip.s3key'] = 'maxmind-geolite2-city.mmdb.tar.gz';

/**
 * This should not be changed, this is the only valid database we have in the based image now. (ci/docker-base)
 * We don't rely on install/refresh maxmind database script anymore.
 */
$configs['geoip.local_filename'] = 'GeoLite2-City.mmdb';


/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 * This is not used anymore because the cron (refreshmaxminddb.php) isn't use to refresh the DB anymore.
 * Those files are part of the base container image now.
 *
 * @secret
 * @feature Infra
 */
$configs['maxmind.key'] = 'fake';

$configs['product_img_size'] = 250;
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['slack.key'] = 'fake';
$configs['slack.alerts.channel'] = $configs['env'] === Loader::ENV_STACK_PROD ? '#alerts-success' : '#alerts-success-tests';

/**
 * If aws, will use AWS ElasticSearch
 * else would use the gcp.elasticsearch parameters
 *
 * Possible values: aws|gcp
 * @show-config public
 */
$configs['service.elasticsearch'] = 'gcp';

/**
 * Elasticseach URL. This config is overridden by InfraConfigLoader. Default value is for dev only.
 *
 * @feature Infra
 */
$configs['gcp.elasticsearch.endpoint'] = 'https://sf-env-dev-8.es.northamerica-northeast1.gcp.elastic-cloud.com:9243';
/**
 * Elasticseach username. This config is overridden by InfraConfigLoader. Default value is for dev only.
 *
 * @feature Infra
 */
$configs['gcp.elasticsearch.username'] = 'elastic';
/**
 * Elasticseach username. This config is overridden by InfraConfigLoader. Default value is for dev only.
 *
 * @secret
 * @feature Infra
 */
$configs['gcp.elasticsearch.password'] = 'enOzNU5iMzBL91SYhEQEZprX';

$configs['amazon.elasticsearch.endpoint'] = null;
$configs['amazon.elasticsearch.region'] = 'us-east-1';

/**
 * Possible values: aws|gcp
 * @show-config public
 */
$configs['services.queue'] = 'gcp';

/**
 * Flags to allow/disallow AWS Queue creations.
 *
 * If set to true php will create / manage the queue.
 * If set to false devops deployment tooling would create
 * and manage the queues.
 *
 * Possible values: true|false
 *
 * WARNING: Do not set this value to false unless
 * you check with DevOps if they have implemented
 * queue creation as part of the deployment process.
 * this flag is here so the functionality is available
 * if we choose to leverage QueueCreation from an
 * external tool such as terraform while bootstrapping
 * a new retailer/future queue creations.
 *
 * @show-config public
 */
$configs['aws.queue.management_enabled'] = true;

/**
 * If true, will use the queue vs processing directly the actions.
 * This is only used for push notification queue atm.
 * @show-config public
 */
$configs['services.queue.enabled'] = true;

/**
 * If true, will log legacy chat request using the CANS
 * Note: This config is for Beta test only and will be removed after CANS is deployed
 */
$configs['services.record_chat_metrics_cans.enabled'] = false;

/**
 * For SQS Queue's Url formatting we need
 * the account number where the queue is located.
 *
 * @show-config public
 */
$configs['aws.account.number'] = ************;

/**
 * Limit queue data to specific regions. Ie: US Only do not send data in EU.
 * https://cloud.google.com/compute/docs/regions-zones
 *
 * https://cloud.google.com/compute/docs/regions-zones
 *
 * @show-config public
 */
$configs['gcp.queue.regions'] = ['us-central1','us-east1','us-east4'];

/**
 * Flags to allow/disallow GCP Queue creations.
 *
 * If set to true php will create / manage the queue.
 * If set to false devops deployment tooling would create
 * and manage the queues.
 *
 * GCP Have bad performance if we enable this feature in production.
 * Queue Creation / Get Topic / GetSubscription performance is slow
 * Leave this false unless required.
 *
 * Possible values: true|false
 *
 * @show-config public
 */
$configs['gcp.queue.management_enabled'] = false;


/**
 * Maximum number of messages to grab from Queue
 *
 * @show-config public
 */
$configs['gcp.queue.max_messages'] = 1000;

// ----- GCP ---
/**
 * Google CLoud project ID. This config is overridden by InfraConfigLoader. Default value is for dev only.
 *
 * @feature Infra
 */
$configs['gcp.project_id'] = 'sf-dev-1b4e41f578';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['gcp.s3.secret'] = '';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['gcp.s3.key'] = '';

$configs['gcp.s3.secret.access_key_file'] = '/opt/secrets/cloud-storage/access_key_id';
$configs['gcp.s3.secret.secret_access_key_file'] = '/opt/secrets/cloud-storage/secret_access_key';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['google.api_key'] = 'fake';

// ------------------- Amazon AWS -------------------
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['aws.queue.key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['aws.queue.secret'] = 'fake';

$configs['aws.queue.region'] = 'us-east-1';
$configs['aws.queue.version'] = 'latest';

$configs['aws.queue.ack_deadline_seconds'] = 120;

/**
 * TODO:  PS-2769 if retailer not use old importer anymore
 * * 1. Remove this config and also remove from general.php.tpl
 * * 2. Clean legacy  product importer which is not used anymore
 */
$configs['update_products.importer.class'] = null;
$configs['update_products.input_dir'] = null;
$configs['update_products.archive_input'] = true;
$configs['update_products.keep_input_file'] = false;
$configs['update_products.multi_file_dump'] = false;
$configs['update_products.skip_diff'] = true;
$configs['update_products.input_file'] = null;
$configs['update_products.max_errors'] = null;
$configs['update_products.min_products'] = 100;
$configs['update_products.min_categories'] = 3;

$configs['cron.event_updater.class'] = null;

$configs['rankings.global_metrics'] = ['sales', 'response-time', 'requests-received', 'chat-answer-rate'];

// SF-15164 Expose Store Events to new Storefront Widget
$configs['retailer.events_from_feed'] = false;

$configs['elb_logs.region'] = 'us-east-1';
$configs['elb_logs.elb_names'] = [];
$configs['redshift.host'] = 'sf-red-1.cdpjytthwjdw.us-east-1.redshift.amazonaws.com';
$configs['redshift.port'] = 5439;
$configs['redshift.db'] = 'elb';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['redshift.password'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @feature Infra
 */
$configs['redshift.username'] = 'sfred';

$configs['storefrontmenu.config.path'] = false;

// Translator
$configs['translator.default_language'] = 'en';
$configs['translator.available_languages'] = array('en');

$configs['salesfloor_storefront.addr'] = '127.0.0.1';

$configs['salesfloor_mpos_proxy.host'] = 'https://mpos-proxy.salesfloor.net';

$configs['retailer.show_store_search_field'] = true;

$configs['retailer.rep_name_format'] = NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['retailer.additional_tracking'] = [];

// Frontend Expiry

/**
 * Cookie duration (in ms) to track sales following initial SF interaction
 *
 * When customer interact with sidebar, footer, storefront or else, we set tracking cookie and record sale when customer complete an order.
 *
 * @show-config public
 * @feature Sales Tracking
 */
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 14; // 14 days in ms
$configs['retailer.acquisition_cookie_expire'] = 86400 * 1000 * 30; // 30 days in ms

$configs['retailer.session_cookie_expire'] = 0; // 0 second
$configs['retailer.remove_cookie_expire'] = 1;
$configs['retailer.cookie_domain'] = 'salesfloor.net';

// Backend Expiry
$configs['retailer.customer_cookie_expire'] = time() + 3600; // 1 hour
$configs['retailer.attached_services'] = '';

$configs['retailer.notification_on_sale'] = true;

$configs['retailer.welcome_chat_message'] = 'Hi, my name is %%user%%, how can I help you today?';
$configs['retailer.tracker.method'] = null;
$configs['retailer.tracker.url'] = null;

$configs['retailer.disable_cookies'] = false;

/*
* For stand down sales tracking integration
* Will be used in sf_widget_configs.utils.shouldStandDownSalesTracking()
* @show-config public
* @feature Modular Connect
*/
$configs['retailer.modular_connect.stand_down_sales_tracking_cookies.is_enabled'] = false;

$configs['retailer.avatar_transform'] = [
    "width" => 250,
    "height" => 250,
    "crop" => "fill",
    "gravity" => "face",
];
$configs['lookbook.customer_name_parts'] = ['first', 'last'];

/**
 * Chat mode : Queue vs Broadcast
 *
 * If the value is "All" => Queue
 * If the value if "[]" => Broadccast
 *
 * @show-config public
 */
$configs['queue.byStore'] = [];

/**
 * Dynamic availability routing mode
 *
 * Possible values for this mvp implementation:
 * - legacy: for legacy code
 * - dynamic: for the new routing mode
 *
 * This config will be the only source of truth of the retailer chat routing mode in the future,
 * ie "queue.byStore" will be removed, and the possible values for this config will be:
 * - dynamic
 * - broadcast
 * - ext-broadcast
 * - queue
 *
 * @show-config public
 * @feature Dynamic Availability Routing
 * @group retailer.chat
 */

$configs['retailer.chat.routing.mode'] = 'dynamic';

/**
 * Determine if the DAR should be restricted to route request to only a single store or multiple stores.
 * A value of false means DAR will contact all stores within proximity to the target store
 * while honouring the rules of its pool sizing and priority modes.
 *
 * Pool sizing and priority modes are dynamically configurable using the backoffice 2
 * tools -> User Management settings -> Dynamic Routing page
 *
 * @summary Broadcast mode
 * @show-config public
 * @feature Dynamic Availability Routing
 * @group retailer.chat
 */
$configs['retailer.chat.routing.boundary.single-store'] = false;

/**
 * Display incoming push notification message as a message for the receiving store
 * or the origin store (i.e the preferred store the user intends to contact)
 *
 * @summary Push notification mode
 * @show-config public
 * @feature Dynamic Availability Routing
 * @group retailer.chat
 */
$configs['retailer.chat.routing.notification.show_origin_location'] = false;

$configs['multibrand.is_active'] = false;
$configs['multibrand.storefront_host'] = null;
$configs['multibrand.widgets_host'] = null;
$configs['multibrand.other_brand'] = null;
$configs['multibrand.other_brand_firebase'] = null;

$configs['services.version'] = 2;

// SF-15161 Configure which product grids to show on Widget Storefront
// Use max_products to restrict number of visible products in grid
// Use min_products to hide the complete grid section if min is not reached
// Use show_comments to specify if comments show or not
// TODO: configs 'retailer.num_top_picks'/'storefront.num_top_picks'/'storefront.available_product_grids' need to be centralized
$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 8, 'show_comments' => 1],
    ['type' => 'specials', 'max_products' => 8, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 8, 'show_comments' => 1],
    ['type' => 'favorites', 'max_products' => 8, 'show_comments' => 1],
    /*['type' => 'recommended', 'max_products' => 8, 'min_products' => 2, 'show_comments' => 0]*/
];

// SF-15164 Configure Store Events visibility on Storefront
// Use active to determine the ON/OFF status of the whole Store Events section
// Use min_events to determine the minimum number of events to show section. Set to 0 if no minimum
// Use max_events to determine the maximum number of events to show at a given time
// Use show_button to determine whether or not the usual "Add To Calendar" button will show
$configs['storefront.store_events'] = ['active' => true, 'min_events' => 0, 'max_events' => 3, 'show_button' => 1];

/**
 * If true, will convert store event times using store time zone from utc before returning
 *
 * TruBru has no timezones associated to events because reasons.
 *
 * SF-23812
 *
 * @show-config public
 */
$configs['storefront.store_events.convert_event_times'] = true;

// SF-16082 Custom feature running only for Ralph Lauren.
//          Show brand logos based on slugified version of `name2` column.
$configs['storefront.product_brand_logos'] = [
    'active' => false,
    'path' => '', // any url
    'file_prefix' => '', // file prefix if needed
    'file_suffix' => '', // file suffix including extension
];

// SF-15166 Configure if static content for storefront
$configs['storefront.static_content'] = ['active' => false, 'has_title' => true];

// === WEBSERVER ===

if (!defined('PATH_ROOT')) {
    define('PATH_ROOT', dirname(dirname(__DIR__)));
}
if (!defined('SF_USERNAME_TMPL_FI_LN')) {
    define('SF_USERNAME_TMPL_FI_LN', 1);
}
if (!defined('SF_USERNAME_TMPL_FN_LI')) {
    define('SF_USERNAME_TMPL_FN_LI', 2);
}
if (!defined('SF_USERNAME_TMPL_FN_LN')) {
    define('SF_USERNAME_TMPL_FN_LN', 3);
}
if (!defined('SF_USERNAME_TMPL_NONE')) {
    define('SF_USERNAME_TMPL_NONE', 4);
}
if (!defined('SF_EMAIL_MODE_REP')) {
    define('SF_EMAIL_MODE_REP', 1);
}
if (!defined('SF_EMAIL_MODE_SALESFLOOR')) {
    define('SF_EMAIL_MODE_SALESFLOOR', 2);
}
if (!defined('SF_EMAIL_MODE_RETAILER')) {
    define('SF_EMAIL_MODE_RETAILER', 3);
}
if (!defined('REPORT_DOWNLOADUSER_THRESHOLD_ACTIVE')) {
    define('REPORT_DOWNLOADUSER_THRESHOLD_ACTIVE', 86400);
}
if (!defined('REPORT_CHAT_METRICS_AVERAGE_HOURS_PER_DAYS')) {
    define('REPORT_CHAT_METRICS_AVERAGE_HOURS_PER_DAYS', 8);
}

// $configs['sf.const.sf-username-tmpl-fi-ln'] = 1;
// $configs['sf.const.sf-username-tmpl-fn-li'] = 2;
// $configs['sf.const.sf-username-tmpl-fn-ln'] = 3;
// $configs['sf.const.sf-username-tmpl-none']  = 4;

$configs['sf.email.mode-rep']        = 1;
$configs['sf.email.mode-salesfloor'] = 2;
$configs['sf.email.mode-retailer']   = 3;

// $configs['sf.const.report-downloaduser-threshold-active']       = 86400;
// $configs['sf.const.report-chat-metrics-average-hours-per-days'] = 8;

$configs['retailer.name'] = 'retailername';
$configs['retailer.brand_name'] = 'Brand Name';
$configs['retailer.site'] = 'www.example.com';
$configs['retailer.url'] = 'http://www.example.com';

/**
 * This config are empty if there is no i18n enabled for retailer, otherwise set to relative url
 * - For now, this is only used for replacing reppage in the email stack
 * - Reppage fallback will be the retailers website, per locale
 *
 * @show-config public
 * @feature multilang
 *
 * TODO MULTILANG - ADD TO CONFIG DOCUMENTATION
 */
$configs['retailer.url.i18n'] = [];

/**
 * This config is used to map retailer's url to proper locale
 * 'en_US' => 'https://www.thebay.com',
 * 'fr_CA' => 'https://www.labaie.com'
 *
 * - Used in the mobile app to replace 'retailer_link' by proper value in chat quick responses
 *
 * @feature multilang
 */
$configs['retailer.url.locales'] = [];

$configs['retailer.product_url'] = 'http://www.example.com/products';
$configs['retailer.worldwide_catalog'] = false;
$configs['retailer.has_carousel'] = true;

$configs['retailer.mobile_host'] = 'm.example.com';
$configs['retailer.tablet_host'] = 't.example.com';
$configs['retailer.www_product_tmpl'] = '/source=(?P<id>\d{4,8})/';
$configs['retailer.mobile_product_tmpl'] = 'mobile-product-tmpl';
$configs['retailer.tablet_product_tmpl'] = 'tablet-product-tmpl';
$configs['retailer.use_device_host'] = true;

/**
 * Number of products in top picks
 *
 * TODO: configs 'retailer.num_top_picks'/'storefront.num_top_picks'/'storefront.available_product_grids' need to be centralized
 * @show-config public
 * @feature Storefront
 */
$configs['retailer.num_top_picks'] = 8;

/**
 * Define default sales representative reference
 * @show-config public
 * @feature Associate Reference
 */
$configs['retailer.associate'] = "associate";

/**
 * Number of products in new arrivals
 *
 * @show-config public
 * @feature Storefront
 */
$configs['retailer.num_deals'] = 4;

$configs['retailer.num_looks'] = 4;

/**
 * Number of display products for product used/shared
 * @show-config public
 */
$configs['retailer.num_product_shared'] = 30;

/**
 * @show-config public
 * @feature TrendingRecommendations
 */
$configs['retailer.trending_recommendations'] = false;

/**
 * @show-config public
 * @feature TrendingRecommendations
 */
$configs['retailer.trending_recommendations.min'] = 2;

/**
 * @show-config public
 * @feature TrendingRecommendations
 */
$configs['retailer.trending_recommendations.max'] = 8;

/**
 * @show-config public
 * @feature TrendingRecommendations
 */
$configs['retailer.trending_recommendations.min_recommendations'] = 3;

/**
 * @show-config public
 * @feature TrendingRecommendations
 */
$configs['retailer.trending_recommendations.title'] = 'Trending Recommendations';

/**
 * @show-config public
 * @feature TrendingRecommendations
 */
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = false;

$configs['storefront.use_thumbnails'] = true;

/**
 * Config for backoffice - the limit of contact list in the backoffice contact list page
 * Some retailer would get a long list of contacts even it would impact down the performance
 *
 *
 * @show-config public
 * @feature contacts Backoffice
 */
$configs['retailer.backoffice.contact_list.limit'] = 2000;

$configs['retailer.default_feed_url'] = 'http://example.com/feed';

/**
 * Legacy feature ; not used anymore (RSS)
 * @deprecated
 */
$configs['retailer.prepared_blog_url'] = ''; // This is probably not used anymore and should not be used.

$configs['retailer.default_product_array'] = '';
$configs['retailer.num_posts'] = 4;
$configs['retailer.default_secondary_products'] = 'LATEST_ARRIVALS';
$configs['retailer.products.html_encoded'] = false;

/**
 * Config for backoffice - display brand name of products
 * (similar to $configs['mobile.retailer.show.products_brand_name'])
 *
 * @show-config public
 */
$configs['retailer.show.products_brand_name.backoffice'] = true;

$configs['retailer.real_api_addr'] = '127.0.0.1';

$configs['retailer.commission_rate'] = 0;

$configs['sf.shoppage_pixel_offset'] = '-147px';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.store_source'] = true;

/**
 * If true /app will become the primary point of login for the backoffice
 * Platform login redirects will change to /app
 * Platform logout redirects will eventually redirect to /app/#/logout
 * Will alter the appearance of the mobile web app by adding login redirects and a button to log into the backoffice for select users.
 * Will alter which backoffice header-links/pages are available for group 2,3 selling mode users, selling mode links will be disabled to encourage users to use the mobile-web instead.
 *
 * If false /login will remain the point of login for the backoffice
 * The logout button will redirect to /login
 * No extra buttons will be added to the mobile app and login will redirect to dashboard as usuail
 *
 * @feature Mobile-web
 * @show-config public
 */
$configs['retailer.backoffice_primary_login_through_app.enabled'] = true;

/**
 * Number of decimals in product card on stofront/lookbook etc.
 * (similar config can be set for mobile: mobile.retailer.pricePrecision)
 *
 * @show-config public
 */
$configs['retailer.pricePrecision'] = 2;

/**
 * Hide product price decimals if they are .00
 * (similar config can be set for mobile: mobile.retailer.pricePrecision.hide_empty_decimals)
 *
 * @show-config public
 */
$configs['retailer.pricePrecision.hide_empty_decimals'] = false;

$configs['retailer.has_returns'] = false;

$configs['retailer.reports.commission'] = false;

/**
 * List of available social network to connect to.
 *
 * Facebook can't be used anymore. Only via share dialog
 *
 * @show-config public
 */
$configs['retailer.social_networks'] = array('twitter');

/**
 * Facebook app id
 * @show-config public
 */
$configs['retailer.facebook_app_id'] = '909457515744007';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['linkedin.key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['linkedin.secret'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['locationiq.key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['mandrill.api_key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['mobile.cloudinary.apikey'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @feature Infra
 */
$configs['mobile.cloudinary.cloudName'] = 'salesfloor-net';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['recaptcha.key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['twitter.key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['twitter.secret'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 * This is not used currently:
 * - Facebook connect is removed.
 * - Instagram API used hardcoded value at the moment.
 *
 * @secret
 * @feature Infra
 */

$configs['facebook.app_id'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 * This is not used currently:
 * - Facebook connect is removed.
 * - Instagram API used hardcoded value at the moment.
 *
 * @secret
 * @feature Infra
 */
$configs['facebook.app_secret'] = 'fake';

$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 14; // 14 days in ms
$configs['retailer.acquisition_cookie_expire'] = 86400 * 1000 * 30; // 30 days in ms
$configs['retailer.attached_storefront'] = false;
$configs['retailer.prevent_redirect'] = false;

$configs['retailer.storage_method'] = 'cookie';

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['cookie_prefix'] = '';

/**
 * Display prefix for production cookies.
 *
 * In this case, the prefix is `{$retailer_short_name}_`
 * Used to differenciate 2 retailers with the same domain
 * Eg. Chicos/ChicosCA & WHBM/WHBMCA
 *
 * @show-config public
 */
$configs['retailer.cookie_show_prefix'] = false;

$configs['retailer.grid'] = "container";

/**
 * Enable list of services forms
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['service.live'] = true;
$configs['service.book_appointment'] = true;
$configs['service.personal_shopper'] = true;
$configs['service.contact_us'] = true;

$configs['service.book_appointment.duration'] = true;

// **************  CONFIG service labels ************************
/**
 * If true, retailer services will hide optional phone inputs if text message option is NOT selected
 *
 * @show-config public
 * @feature
 */
//--------------------------------------------
// @TODO investigate to see if these are still being used at all?
// Upon quick investigation they seem to be dead but TBD
$configs['retailer.label.chat'] = 'Live Chat';
$configs['retailer.label.appointment'] = 'Appointment Request';
$configs['retailer.label.personal-shopper'] = 'Personal Shopper';
$configs['retailer.label.email-me'] = 'Ask & Answer';
$configs['retailer.label.email-me.reports'] = 'Ask & Answer';
$configs['retailer.label.about-me'] = 'About Me';
$configs['retailer.label.inscription'] = 'Get My Updates';
$configs['retailer.label.report-concern'] = 'Report A Concern';
$configs['retailer.label.find-rep'] = 'Find Advisor';
//--------------------------------------------

$configs['retailer.label_appointment.textarea.placeholder'] = 'e.g. "Can you recommend a product for this…?" or "I am looking for a product that has the following features…?"';
$configs['retailer.label_finder.textarea.placeholder'] = 'e.g. "Can you recommend a product for this…?" or "I am looking for a product that has the following features…?"';
$configs['retailer.label_question.textarea.placeholder'] = 'e.g. "Can you reserve this item for pick up at your store?"';

// SF-15178 Label for the deals / latest arrivals / specials section in the KPI report (from storefront)
$configs['retailer.label_kpi_deals'] = 'New Arrivals'; // Can be null (to not show KPI), or "Latest Arrivals" "Deals" "Specials"
$configs['retailer.label_kpi_top_picks'] = 'Top Picks';

$configs['service.widget'] = true;
$configs['widget.restricted_hours'] = true;
$configs['widget.restricted_range'] = 5000;
$configs['widget.id'] = 'etvasticky';

//--------------------------------------------
// @TODO investigate to see if these are still being used at all?
// Upon quick investigation they seem to be dead but TBD
$configs['service.video'] = false;
$configs['service.chat'] = true;
$configs['service.phone'] = true;
$configs['service.instore'] = true;
$configs['service.inperson'] = false;
//--------------------------------------------

/**
 * If true, retailer services will hide optional phone inputs if text message option is NOT selected
 *
 * @show-config public
 */
$configs['retailer.services.hide_optional_phone_input'] = false;

/**
 * Book an Appointment
 *
 * If true, retailer has appointment hub on mobile
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.services.appointment.is_enabled'] = true;

/**
 * Auto-accept an appointment request from customer
 * For team-mode only.
 *
 * If true, the request get accepted and a confirmation email/sms sent.
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.services.appointment.auto-accept.is_enabled'] = false;

/**
 * Default appointment duration in minutes, 60 minutes for all retailers.
 * The Value should be either 15, 30 or 60.
 * The duration is applied to all meeting types
 *
 * @show-config public
 */
$configs['retailer.services.appointment.duration'] = 60;

/**
 * Different appointment types available for a retailer
 *
 * Remove a line to remove a type
 * 'is_enabled' => false will show the type but visually disable it
 * 'is_default' => true will select the type by default
 * 'position' will order element in numerical order
 * 'use_alternate_template' => true will use an alternate label from the string files
 *
 * Note: 'type' => 'virtual' to be used for Virtual Appointments
 *
 * @show-config public
 */
$configs['retailer.services.appointment.types'] = [
    ['type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false],
    ['type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false],
    ['type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false],
    ['type' => 'virtual', 'is_enabled' => false, 'is_default' => false, 'position' => 0, 'use_alternate_label' => false],
];

/**
 * If true, the names of reps under carousel in services forms will show
 *
 * @show-config public
 */
$configs['retailer.services.carousel.show_names'] = true;

/**
 * Alternate logo for landing page and service forms.
 * Should be defined if logo for sideber and service forms is different.
 * If null, the carousel logo will follow ($configs['retailer.sidebar.v3.logopath'])
 *
 * @show-config public
 */
$configs['retailer.services.carousel.alternate_logo'] = null;

/**
 * If null, the carousel mode will follow the sidebar 3 config ($configs['retailer.sidebar.v3.mode'])
 * otherwise, the options are: carousel, single or logo
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.services.carousel.display_mode'] = null;

$configs['retailer.mainSearchUrl'] = 'http://example.com/search';

$configs['retailer.email_mode'] = SF_EMAIL_MODE_SALESFLOOR;

$configs['products.root_category_id'] = "1";
$configs['product.comment_name_fmt'] = "fn";

$configs['chat.custTimeOut'] = 33;
$configs['chat.repTimeOut'] = 30;


// Redis config

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['redis.host'] = 'redis.salesfloor';
/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['redis.port'] = '6379';

// MySQL config

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['mysql.host'] = 'mysql.salesfloor';
/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['mysql.port'] = '3306';
/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['mysql.username'] = 'wordpress';
/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['mysql.db'] = 'wordpress_' . $configs['retailer.short_name'];
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['mysql.password'] = 'AvFAN4LpKl1EYH';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.db.password'] = $configs['mysql.password'];
$configs['mysql.charset'] = 'utf8mb4';
$configs['mysql.collate'] = '';

// logstash config

/**
 * This config is overridden by InfraConfigLoader.
 *
 * @feature Infra
 */
$configs['logstash.topic'] = 'fake';

// WordPress configs

$configs['wordpress.shell_dir']          = '/opt/salesfloor/shell';
$configs['wordpress.cache_dir']          = '/tmp/cache';
$configs['wordpress.word_filter_list']   = 'filter/filter_words.txt';
$configs['wordpress.string_filter_list'] = 'filter/filter_strings.txt';

$configs['wordpress.debug'] = 0;
$configs['wordrpess.debug_log'] = 0;

$configs['wordpress.js.prodApi'] = 1;

/**
 * This enables/disables the cron feature integrated to WordPress
 * @feature wordpress
 */
$configs['wordpress.disable_cron'] = 1;

$configs['wordpress.js.menuPosition'] = 44;

$configs['wordpress.js.anchorLabels.defaultState.defaultLabel'] = 'More';
$configs['wordpress.js.anchorLabels.defaultState.openedLabel'] = 'Less';

$configs['wordpress.js.searchCategories.exclude'] = [];
$configs['wordpress.js.searchCategories.excludeDeals'] = 'Current Offers';
$configs['wordpress.js.defaultSpeedAnimation'] = 400;
$configs['wordpress.js.defaultOverlayColor'] = '#000000';

$configs['wordpress.js.iframes.appointment.height'] = 940;
$configs['wordpress.js.iframes.finder.height'] = 855;
$configs['wordpress.js.iframes.question.height'] = 624;
$configs['wordpress.js.iframes.inscription.height'] = 640;
$configs['wordpress.js.iframes.concern.height'] = 630;

$configs['wordpress.templates_dir'] = !defined('PATH_WORDPRESS_SRC') ? null : (PATH_WORDPRESS_SRC . '/templates');
$configs['wordpress.properties_dir'] = !defined('PATH_WORDPRESS_SRC') ? null : (PATH_WORDPRESS_SRC . '/properties');

$configs['wordpress.rep_status_poll_interval'] = 30000;


/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.auth_key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.auth_salt'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.logged_in_key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.logged_in_salt'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.nonce_key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.nonce_salt'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.secure_auth_key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['wordpress.secure_auth_salt'] = 'fake';

$configs['publisher.maxRecipients'] = 100;

$configs['content.help_link_url'] = "https://salesfloor.zendesk.com/hc/en-us";
$configs['content.help_link_url_query'] = "https://salesfloor.zendesk.com/hc/en-us/search?utf8=%E2%9C%93&query={query}&commit=Search";

$configs['content.text_blacklist'] = null;
$configs['content.whitelist_links'] = false;

$configs['onboarding.choose_own_username'] = true;
$configs['onboarding.alias_match_username'] = true; // WARNING: must be true if names are expected to contain non [a-zA-Z0-9_] characters
$configs['onboarding.alias_template'] = SF_USERNAME_TMPL_NONE;
$configs['onboarding.example_username_full'] = true;

/**
 * Add alias name for the rep in the URL. No alias for team mode.
 * By default, it will be firstname and first letter of lastname together.
 *
 * @show-config public
 * @feature Storefront
 */
$configs['onboarding.choose_alias'] = true;

/**
 *  * the template of generating alias for db:wp_users
 */
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LN;
/**
 *  * the template of generating username for db:wp_users
 */
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
/**
 * DEPRECATED @TODO should be replaced with name_fmt
 * the template of display rep name (in email/UI etc)
 * mobile code still uses this
 *
 * @show-config public
 * @feature NameSuggester
 */
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;
$configs['onboarding.allow_about_me'] = true;

// SNS Push Notifications Configs
$configs['sns_endpoint'] = [
    'android' => [
        'applicationArn' => 'arn:aws:sns:us-west-1:************:app/GCM/Salesfloor-GCM',
    ],
];

$configs['retailer.time_fmt'] = 24;

$configs['msg_center.max_recipients'] = 20;

$configs['retailer.max_reps_per_store'] = 0;
$configs['retailer.report_concern_mailboxes'] = [];
$configs['retailer.events_from_feed'] = false;
$configs['retailer.avatar_transform'] = [
    "width" => 250,
    "height" => 250,
    "crop" => "fill",
    "gravity" => "face",
];

$configs['retailer.tracker.method'] = '';
$configs['retailer.tracker.url'] = '';

$configs['retailer.label_appointment_special'] = '';
$configs['retailer.label_question_special'] = '';

$configs['retailer.chat_delay'] = [
    'queue' => 120000,
    'broadcast' => 60000,
    'dynamic' => 60000,
];

$configs['multibrand.is_active'] = false;

$configs['service.version'] = 2;

// SF-13766 CONTACT_US_EMAIL not defined
$configs['retailer.email.contact_us'] = '';

$configs['retailer.services.casl'] = false;
$configs['retailer.services.casl.v2'] = false;

/**
 * CASL format used for canadian chico's brands (WHBMCA/CHICOCA)
 * Toggles between a CASL for email and one for text message
 * Also affects the bottom mention block below Start a Conversation button on chat pages
 * as requested on ticket SF-26739
 */
$configs['retailer.services.casl.v3'] = false;

// S3 Keys
/**
 * NOTE: 'Exports/' folder should be used only for exporter(csv) for downloading from backoffice in 'private-bucket'
 * If we share file to retailer access via ftp, we should use 'default-bucket/outbound/' folder
 *
 */
$configs['s3.exports.prefix'] = 'Exports/';
$configs['s3.exports.monthly_prefix'] = 'MonthlyCsvs/';

$configs['retailer.single_category'] = false;
$configs['cache.storefront.ttl'] = 12 * 3600;

/**
 * Display scrapped dynamic menu on storefront header
 *
 * This is important if SF can't scrape the menu, Or if retailer prefer to give us API endpoint.
 *
 * @show-config public
 * @feature Storefront
 */
$configs['storefront.menu.isdynamic'] = false;

$configs['autoresponder.send_normal_response_too'] = true;

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sns.key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sns.secret'] = 'fake';
$configs['sns.region'] = 'us-west-1';
$configs['sns.bugs.region'] = 'us-east-1';
$configs['sns.bugs.topic'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'arn:aws:sns:us-east-1:************:bugs' : null;

// Mobile

// This seems to be never used in mobile. Not sure what they are doing.
// Will keep it here until future cleanup.
$configs['mobile.app_download_urls'] = $configs['env'] === Loader::ENV_STACK_PROD ?
    [
        'ios' => 'https://itunes.apple.com/ca/app/salesfloor/id957494193?mt=8',
        'android' => 'market://details?id=com.salesfloor.appstore'
    ] : [
        'ios' => 'https://build.phonegap.com/apps/1221243/download/ios',
        'android' => 'https://build.phonegap.com/apps/1221243/download/android'
    ];
$configs['mobile.app_version_urls'] = $configs['env'] === Loader::ENV_STACK_PROD ?
    [
        'ios' => 'https://cdn.salesfloor.net/mobile-app-version/configs.version.ios.json',
        'android' => 'https://cdn.salesfloor.net/mobile-app-version/configs.version.android.json'
    ] : null;

$configs['mobile.camera_max_height'] = 1500;
$configs['mobile.camera_max_width']  = 1500;
$configs['mobile.camera_quality']    = 100;
$configs['mobile.camera_max_photos'] = 5;
$configs['mobile.email_me_label'] = "Email Me Request";
$configs['mobile.loader_delay'] = 1500;
$configs['mobile.loader_message_interval'] = 4000;


$configs['mobile.fb_max_reconnection_time'] = 500;
$configs['mobile.fb_show_disconnect_message'] = false;

/**
 * BC scanner integerated into Product Library, allows Reps to scan a barcode of an item directly in the store using mobile app,
 *
 * @show-config public
 * @feature Product Feed
 */
$configs['mobile.product_barcode_scanner'] = true;

$configs['mobile.retailer_has_chat'] = true;
$configs['mobile.retailer_has_deals'] = false;
$configs['mobile.retailer_has_lookbooks'] = true; # Disable for rep mode
$configs['mobile.retailer_has_new_arrivals'] = true;
$configs['mobile.retailer_has_personal_shopper'] = true;
$configs['mobile.retailer_has_specialties'] = false;
$configs['mobile.retailer_has_chat_appointment'] = true;
$configs['mobile.retailer_has_mobile_checkout'] = false;

/**
 * Display scan button inside of search input for Contacts and Customers on Mobile App.
 * @show-config public
 * @feature share
 **/
$configs['mobile.barcode_scanner.contacts.enabled'] = false;

/**
 * when false 'call' CTA button will be hidden from Contact profiles and Tasks
 **/
$configs['mobile.phone_call.is_enabled'] = true;

/**
 * Whether users can access the "my library" feature in the mobile app
 * @show-config public
 * @feature share
 */
$configs['mobile.retailer_can_browse_library'] = false;

/**
 * Whether users can natively share items from the "my library" section of the mobile app
 * @show-config public
 * @feature Social Networks
 */
$configs['mobile.retailer_can_share_from_browse_library'] = false;

/**
 * Display tasks on mobile app
 * @show-config public
 * @feature task
 */
$configs['mobile.retailer_has_tasks'] = true;

/**
 * Display Asset Management on old and new Backoffice
 * @show-config public
 * @feature asset management
 */
$configs['mobile.retailer_has_asset_management'] = true;

/**
 * Display Feed Validation on old and new Backoffice
 * @show-config public
 * @feature feed validation
 */
$configs['mobile.retailer_has_feed_validation'] = true;

/**
 * Detect if missing columns are missing in feed validation
 * Might wish to disable if pre-processor is not yet compatible
 * @show-config public
 * @feature feed validation
 */
$configs['feed_validation.detect_missing_columns.enabled'] = true;

/**
 * Specifies how many sample values should be shown for each issue.
 * This also keeps the report size small, preventing the result from scaling with the number of rows
 * @show-config public
 * @feature feed validation
 */
$configs['feed_validation.data_sample_limit'] = 20;

/**
 * Disable Products on mobile app
 * @show-config public
 * @feature products feed
 */
$configs['mobile.retailer_has_products_feed'] = true;

/**
 * If true, mobile compose email will have WYSIWYG enabled
 * @show-config public
 * @feature compose email
 */
$configs['mobile.compose_wysiwyg_enabled'] = true;

/**
 * If true, mobile share will have WYSIWYG enabled
 * @show-config public
 * @feature share
 */
$configs['mobile.share_wysiwyg_enabled'] = true;

/**
 * If true, backoffice compose email will have WYSIWYG enabled
 * @show-config public
 * @feature compose email
 */
$configs['backoffice.compose_wysiwyg_enabled'] = true;

/**
 * If true, backoffice share will have WYSIWYG enabled
 * @show-config public
 * @feature share
 */
$configs['backoffice.share_wysiwyg_enabled'] = true;

/**
 * TODO
 * @show-config public
 * @feature share
 */
$configs['mobile.share_connected_services_enabled'] = true;

/**
 * If true, can share via email
 * @show-config public
 * @feature Social Networks
 */
$configs['mobile.share_email_enabled'] = true;

/**
 * If true, can share via facebook on mobile (and backoffice)
 * @show-config public
 * @feature Social Networks
 */
$configs['mobile.share_facebook_enabled'] = false;

/**
 * If true, can share via instagram on mobile
 * @show-config public
 * @feature Social Networks
 */
$configs['mobile.share_instagram_enabled'] = false;
/**
 * If true, can share via pinterest on mobile
 * @show-config public
 * @feature Social Networks
 */
$configs['mobile.share_pinterest_enabled'] = false;

$configs['mobile.retailer_can_edit_events'] = false;

/**
 * If true, enable the draft system on the mobile compose UI
 * @show-config public
 * @feature Draft
 */
$configs['mobile.draft.compose_enabled'] = true;

/**
 * If true, enable the draft system on the mobile share UI
 * @show-config public
 * @feature Draft
 */
$configs['mobile.draft.share_enabled'] = true;

/**
 * Wait X ms after last user-input before auto-saving the current draft
 * @show-config public
 * @feature Draft
 */
$configs['mobile.draft.auto_save_interval'] = 5000;

/**
 * Quick SMS responses
 * @show-config public
 * @feature Chat Responses
 */
$configs['mobile.sms.quick_responses'] = [
    [
        'short' => [
            'en_US' => 'Insert Storefront Link',
            'en_IE' => 'Insert Storefront Link',
            'en_IN' => 'Insert Storefront Link',
            'fr_CA' => 'Insérer le lien du magasin',
            'ja_JP' => 'ストアリンクを挿入'
        ],
        'full' => [
            'en_US' => 'Here\'s the link to my Storefront: {store_link}',
            'en_IE' => 'Here\'s the link to my Storefront: {store_link}',
            'en_IN' => 'Here\'s the link to my Storefront: {store_link}',
            'fr_CA' => 'Voici le lien vers ma vitrine: {store_link}',
            'ja_JP' => 'これが私のストアフロントへのリンクです：{store_link}',
        ],
    ],
    [
        'short' => [
            'en_US' => 'Insert Subscribe Link',
            'en_IE' => 'Insert Subscribe Link',
            'en_IN' => 'Insert Subscribe Link',
            'fr_CA' => 'Insérer le lien d\'abonnement',
            'ja_JP' => '購読リンクを挿入',
        ],
        'full' => [
            'en_US' => 'Here\'s the link to subscribe: {subscribe_link}',
            'en_IE' => 'Here\'s the link to subscribe: {subscribe_link}',
            'en_IN' => 'Here\'s the link to subscribe: {subscribe_link}',
            'fr_CA' => 'Voici le lien pour vous inscrire: {subscribe_link}',
            'ja_JP' => '購読するためのリンクは次のとおりです。: {subscribe_link}',
        ]
    ],
    [
        'short' => [
            'en_US' => 'Appointment Link',
            'en_IE' => 'Appointment Link',
            'en_IN' => 'Appointment Link',
            'fr_CA' => 'Lien de rendez-vous',
        ],
        'full' => [
            'en_US' => 'Here\'s the link to book an appointment: {appointment_link}',
            'en_IE' => 'Here\'s the link to book an appointment: {appointment_link}',
            'en_IN' => 'Here\'s the link to book an appointment: {appointment_link}',
            'fr_CA' => 'Voici le lien pour prendre rendez-vous: {appointment_link}',
            'ja_JP' => '予約するためのリンクはこちら: {appointment_link}',
        ],
    ],
];

/**
 * Quick mobile responses
 * @show-config public
 * @feature Chat Responses
 */
$configs['mobile.quick_responses'] = [[
    'short' => [
      'en_US' => 'Give me a moment',
      'en_IE' => 'Give me a moment',
      'en_IN' => 'Give me a moment',
      'fr_CA' => 'Un instant',
      'ja_JP' => '少しお時間をください'
    ],
    'full' => [
      'en_US' => 'Please give me a moment and I will look into this for you.',
      'en_IE' => 'Please give me a moment and I will look into this for you.',
      'en_IN' => 'Please give me a moment and I will look into this for you.',
      'fr_CA' => 'S\'il vous plaît, laissez-moi un instant et je vais m\'en occuper pour vous.',
      'ja_JP' => '少しお時間をください、調査させていただきます。'
    ],
], [
    'short' => [
      'en_US' => 'Thank you and goodbye',
      'en_IE' => 'Thank you and goodbye',
      'en_IN' => 'Thank you and goodbye',
      'fr_CA' => 'Merci et au revoir',
      'ja_JP' => 'ありがとうございました。さようなら'
    ],
    'full' => [
      'en_US' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
      'en_IE' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
      'en_IN' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
      'fr_CA' => 'Merci de votre demande. Il me fera plaisir de vous servir à nouveau. Passez une bonne journée.',
      'ja_JP' => 'リクエストありがとうございます。再び、お役に立てることを嬉しく思います。良い一日をお過ごしください。'
    ],
], [
    'short' => [
      'en_US' => 'Out of stock',
      'en_IE' => 'Out of stock',
      'en_IN' => 'Out of stock',
      'fr_CA' => 'Rupture de stock',
      'ja_JP' => '在庫切れ'
    ],
    'full' => [
      'en_US' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href="retailer_link" target="_blank">here</a>',
      'en_IE' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href="retailer_link" target="_blank">here</a>',
      'en_IN' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking <a href="retailer_link" target="_blank">here</a>',
      'fr_CA' => 'Malheureusement, nous sommes en rupture de stock en magasin, mais vous pouvez l\'acheter en ligne en cliquant <a href="retailer_link" target="_blank">ici</a>',
      'ja_JP' => '残念ながら、現在この製品の在庫はありませんが、<a href="retailer_link" target="_blank">こちら</a>をクリックして、オンラインでご購入になれます'
    ],
], [
    'short' => [
      'en_US' => 'My Storefront Link',
      'en_IE' => 'My Storefront Link',
      'en_IN' => 'My Storefront Link',
      'fr_CA' => 'Lien de ma vitrine web',
    ],
    'full' => 'storefront_link',
], [
    'short' => [
      'en_US' => 'Subscribe Link',
      'en_IE' => 'Subscribe Link',
      'en_IN' => 'Subscribe Link',
      'fr_CA' => 'Lien d\'abonnement',
    ],
    'full' => 'subscribe_link',
], [
    'short' => [
      'en_US' => 'Appointment Link',
      'en_IE' => 'Appointment Link',
      'en_IN' => 'Appointment Link',
      'fr_CA' => 'Lien de rendez-vous',
    ],
    'full' => "appointment_link",
]];

$configs['mobile.extra_quick_responses'] = [];

/**
 * If true, the products modal directive is available in app
 * @show-config public
 * @feature share
 */
$configs['mobile.products_modal_enabled'] = false;

/**
 * If true, the retailer has access to the 'Change retailer ID' link on the mobile app lohin page
 * @show-config public
 */
$configs['mobile.retailer_can_change_retailer_id'] = false;

/**
 * If true, the retailer can use the "mdm" mobile app to connect.
 *
 * @show-config public
 * @feature mobile-login
 */
$configs['mobile.login.mdm.is_enabled'] = true;

/**
 * If true, the retailer can use the "store" mobile app to connect.
 *
 * @show-config public
 * @feature mobile-login
 */
$configs['mobile.login.store.is_enabled'] = true;

$configs['mobile.app_upgrade_notification.is_enabled'] = true;
$configs['mobile.app_upgrade_notification.timeout'] = 86400; // 1 day in seconds
$configs['mobile.app_upgrade_notification.message'] = [
    'en_US' => '<p>IMPORTANT: This Android version of Salesfloor is no longer supported and should be replaced with the latest Salesfloor app.</p><p>Please download it here: <a href="https://salesfloor.net/android" onclick="window.open(\'https://salesfloor.net/android\', \'_system\')">salesfloor.net/android</a></p><p>Thanks.</p>',
    'fr_CA' => "<p>IMPORTANT: Cette version Android de Salesfloor n'est plus supportée and doit être remplacée par la plus récente application Salesfloor.</p><p>Téléchargez-la ici: <a href='https://salesfloor.net/android' onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a> et n'oubliez pas d'effacer l'ancienne version de votre appareil ! Vos informations ne seront pas perdues.</p><p>Merci.</p>",
    'ja_JP' => "<p>重要：このAndroidバージョンのSalesfloorはサポート停止となりました。最新のSalesfloorアプリに更新してください。</p><p>ここからダウンロードしてください： <a href='https://salesfloor.net/android' onclick=\"window.open('https://salesfloor.net/android', '_system')\">salesfloor.net/android</a></p><p>よろしくお願いいたします。</p>"
];

/**
 * Config for mobileApp - number of decimals in prices (product KPI etc)
 * (similar to $configs['retailer.pricePrecision'])
 *
 * @show-config public
 */
$configs['mobile.retailer.pricePrecision'] = 2;

/**
 * Config for mobileApp - display the brand name of products
 * (similar to $configs['retailer.show.products_brand_name.backoffice'])
 *
 * @show-config public
 */
$configs['mobile.retailer.show.products_brand_name'] = true;

/**
 * Config for mobileApp - hide product price decimals if they are .00
 * (similar to $configs['retailer.pricePrecision'])
 *
 * @show-config public
 */
$configs['mobile.retailer.pricePrecision.hide_empty_decimals'] = false;

// === ADDED - Missing defaults ===

$configs['retailer.alternate_brand'] = null;
$configs['retailer.alternate_name'] = null;
$configs['mysql.slaves'] = null;

/**
 * If true, some read-only queries in wordpress will run against the slave db.
 *
 * If you turn on this config, don't forget to set mysql.slave-1.* first ! This is mandatory and
 * there's no validation at the moment.
 *
 * @show-config public
 */
$configs['mysql.use_wpdb_slave'] = false;

// Config keys that were found after and missing defaults
$configs['bloom.api_key'] = null;
$configs['customer_ftp.host'] = null;
$configs['customer_ftp.password'] = null;
$configs['customer_ftp.user'] = null;
$configs['firebase.defaultMultibrandPath'] = null;
$configs['multibrand.other_brand_host'] = null;
$configs['mysql.slave-1.db'] = null;
$configs['mysql.slave-1.host'] = null;
$configs['mysql.slave-1.port'] = null;
$configs['mysql.slave-1.username'] = null;
$configs['payroll.email'] = null;

/**
 * Only used for email check when generating "from" and "reply to" email addresses
 * @show-config public
 */
$configs['retailer.email_domain'] = null;

$configs['retailer.contacturl'] = null;
$configs['retailer.google.host'] = null;
$configs['retailer.google.id'] = null;
$configs['retailer.google.uid'] = null;

/**
 * The name of the retailer instagram account
 * @show-config public
 * @feature instagram
 */
$configs['retailer.instagram.account'] = null;

/**
 * The TTL on the instagram section on the storefront (1 day)
 * @show-config public
 * @feature instagram
 */
$configs['instagram.media.ttl'] = $configs['env'] === Loader::ENV_STACK_PROD ? 60 * 60 * 24 : 7 * 60 * 60 * 24;

/**
 * Main config to enable instagram section (min/max) on the storefront
 * @show-config public
 * @feature Storefront
 */
$configs['storefront.instagram'] = ['active' => false/*, 'min_posts' => 0, 'max_posts' => 10*/];

$configs['retailer.label_unique_chat'] = null;
$configs['retailer.live.url'] = null;
$configs['retailer.multibrand.mapping'] = null;
$configs['retailer.onboarding_logo'] = null;
$configs['retailer.outlook.id'] = null;
$configs['retailer.services.logo'] = null;

/**
 * Enable Privacy policy opt-in in Services form
 *
 * @show-config public
 * @feature Privacy Policy
 */
$configs['retailer.services.policy'] = null;

$configs['retailer.yahoo.consumer_key'] = null;
$configs['retailer.yahoo.consumer_secret'] = null;
$configs['stats_db.db'] = null;
$configs['stats_db.username'] = null;
$configs['update_products.categories_entry'] = null;
$configs['update_products.categories_exclude'] = null;
$configs['update_products.categories_selectors'] = null;

// New config for retailers
/**
 * New lead by speciality
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.filter_new_leads_by_specialty'] = false;

$configs['retailer.has_live_chat_appointment'] = true;
$configs['retailer.transfer_store_name_to_id'] = false;
$configs['retailer.saks_note'] = '';
$configs['retailer.inscription_policy'] = false;

$configs['retailer.bo_logo_is_landscape'] = false;
$configs['retailer.bo_logo_url'] = null;
$configs['retailer.show_publisher_in_bo'] = true;
$configs['retailer.publisher_email_option_none'] = true;
$configs['retailer.publisher_social_sharing'] = true;
$configs['retailer.publisher_storefront'] = true;
$configs['retailer.notifications_deals_current'] = true;
$configs['retailer.notifications_posts_current'] = false;
$configs['retailer.notifications_send_update'] = true;
$configs['retailer.notifications_create_event'] = true;
$configs['retailer.notifications_create_description'] = true;
$configs['retailer.typekit_freight_display_pro'] = false;
$configs['retailer.google_tag_manager'] = false;
$configs['retailer.sort_user_by_username'] = false;

/**
 * Enable Personal Shopper
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.has_personal_shopper'] = true;

$configs['retailer.has_kpi_deals'] = true;
$configs['retailer.has_latest_arrivals'] = true;
$configs['retailer.remove_cookie_domains'] = null;
$configs['retailer.load_typekit'] = [];

// === Google Analytics ===
$configs['retailer.google.ga_backoffice_web']    = 'UA-92296294-3';
$configs['retailer.google.ga_backoffice_mobile'] = 'UA-92296294-4';
$configs['retailer.google.ga_services']          = 'UA-92296294-6';
$configs['retailer.google.ga_storefront']        = 'UA-92296294-7';
$configs['retailer.google.ga_socialshop']        = 'UA-92296294-10';
$configs['retailer.google.ga_backoffice_web_v2'] = 'UA-92296294-11';

/**
 * Allow contacts import in backoffice via "add new contacts" (cloudsponge) or via mobile app.
 *
 * @show-config public
 */
$configs['retailer.contacts.import_enabled'] = false;

$configs['monolog.level'] = $configs['env'] === Loader::ENV_STACK_PROD ? \Monolog\Logger::ERROR : \Monolog\Logger::DEBUG;
$configs['logger.slack.channel.errors']  = $configs['env'] === Loader::ENV_STACK_PROD ? '#alerts-errors' : '#alerts-errors-tests';
$configs['logger.slack.channel.success'] = $configs['env'] === Loader::ENV_STACK_PROD ? '#alerts-success' : '#alerts-success-tests';
$configs['logger.slack.channel.account_monitor'] = $configs['env'] === Loader::ENV_STACK_PROD ? '#alerts-account-monitor' : '#alerts-account-monitor-tests';
$configs['throw_exception.empty_filters_by_get_one'] = $configs['env'] != 'prd';

$configs['retailer.default_currency'] = 'USD';

// Allowed_currencies can be used to restrict which currency retailer will allow
// For now only set on global level to match DB allowed enums
$configs['retailer.allowed_currencies'] = ['USD', 'CAD'];

$configs['retailer.storefront.is-new-stack'] = false;

/**
 * The sidebar radius in km, It's used in getStoreUsers() to create team.users for storefront jumbotron
 *
 * This config should not be empty
 * @show-config public
 */
$configs['stores.max_distance'] = '35';

/**
 * Radius around store to include in IP targeting for sidebar
 *
 * The Sidebar Widget is only displayed if a user's IP address is considered "within range" for a given store, The range is defined by the radius around the store.
 * If set to true, then value in db, sf_store:max_distance will over-write the default global value of $configs['stores.max_distance']
 *
 * @show-config public
 * @feature SF Connect
 */
$configs['stores.max_distance.per_store.enable'] = false;

$configs['stores.available_hours'] = [
    '0' => true,
    '1' => true,
    '2' => true,
    '3' => true,
    '4' => true,
    '5' => true,
    '6' => true,
];

// Set linkshare on storefront view (From commit 810b9ae1)
$configs['linkshare_on_storefront_view'] = false;

// Show default comments for every product
// that does not already have a comment
$configs['products.defaultcomment_display.is_enabled'] = true;

/**
* Default comments for teammode retailers displayed in the storefront for the displayed products can be turned off using this configuration.
* @show-config public
* @feature Storefront
 */
$configs['products.defaultcomments.storemode'] = [
    "api_products_defaultcomments_storemode_1",
    "api_products_defaultcomments_storemode_2",
];

/**
* Default comments for repmode retailers displayed in the storefront for the displayed products can be turned off using this configuration.
* @show-config public
* @feature Storefront
 */
$configs['products.defaultcomments.repmode'] = [
    "api_products_defaultcomments_repmode_1",
    "api_products_defaultcomments_repmode_2",
    "api_products_defaultcomments_repmode_3",
];

$configs['product.panels'] = [
    'top-picks' => 'TopPicks',
    'new-arrivals' => 'NewArrivals',
    'specials' => 'Specials',
];

/**
 * To define the default set of products to be displayed in the storefront under 'New Arrivals' section by enabling this configuration.
 *
 * Client is expected to provide the nominated product list once this config is enabled
 *
 * @show-config public
 * @feature Product Feed
 */
$configs['product.panels.new-arrivals.nominated-products'] = false;

/**
 * To define the default set of products to be displayed in the storefront under 'Top Picks' section by enabling this configuration.
 *
 * Client is expected to provide the nominated product list once this config is enabled
 *
 * @show-config public
 * @feature Product Feed
 */
$configs['product.panels.top-picks.nominated-products'] = false;

$configs['product.panels.refresh.autoselected.frequency'] = 14;

$configs['products.variants.groupby'] = null;

$configs['products.is-price-displayed'] = true;

$configs['customers.labels'] = [
    'email' => array('Home', 'Work', 'Other'),
    'phone' => array('Home', 'Work', 'Mobile', 'Other'),
];

/**
 * Different mobile specicifities
 * @show-config public
 */
$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
      "token": "true"
    },
    "create-user": {
      "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
      "password": "true",
      "confirm": "true"
    },
    "enter-email": {
      "email": "email",
      "phone": "true",
      "alias": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()",
      "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
      "firstname": "firstname || ''",
      "lastname": "lastname || ''"
    },
    "pick-store": {
      "store": "store",
      "introduction": "\"Hello! I am your dedicated Sales Associate. I can provide personalized services, answer your questions and meet with you in store or online to help you find what you desire.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "true",
    "import-contact": "false",
    "connect-social": {
      "twitter": "true"
    },
    "congrats": "true"
  },
  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
      "enter-email": "false",
      "out-of-store": "false",
      "take-picture": "true",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }]
}
JSON;

// SF-16011 Sendgrid configurations

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sf.sendgrid.api_key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sf.sendgrid.master_api_key'] = 'fake';
$configs['sf.sendgrid.api_url'] = 'https://api.sendgrid.com/v3/';
$configs['sf.sendgrid.percentage'] = 100;
// SF-16011 Add CASL Compliance retailer address
// If after loading all retailer configs sf.casl.address is still empty, then it
//   will get the retailer.hq_address config (assigned in calculated.php)
$configs['sf.casl.address'] = '';
$configs['sf.sendgrid.queue_sends'] = true;

/**
 * Wether or not to update email/text subscription flag to 'unsubscribed' if a contact is old enough
 * @show-config public
 */
$configs['retailer.contacts.unsubscribe_automated.enabled'] = false;

/**
 * Update email/text subscription flag to 'unsubscribed' if a contact is older than specific days
 * @show-config public
 */
$configs['retailer.contacts.unsubscribe_automated.days_threshold'] = 180;

/**
 * Force consent to be captured when creating/editing a contact in mobile app
 * @show-config public
 */
$configs['retailer.consent_required.mobile'] = false;

/**
 * Force consent to be captured on services forms
 * @show-config public
 */
$configs['retailer.consent_required.desktop'] = false;

/**
 * Force consent term&conditions checkbox to be captured on services forms
 * @show-config public
 */
$configs['retailer.term_required.desktop'] = false;

/**
 * Email address from which notification emails are sent
 * @show-config public
 */
$configs['retailer.emails.no_reply_address'] = "noreply@{$configs['retailer.short_name']}-{$configs['env']}.salesfloor.net";
$configs['retailer.emails.no_reply_name'] = 'Salesfloor';

/**
 * feature of blocking all email with 'Excluded' type in 'sf_email_block_list'
 * @show-config public
 */
$configs['retailer.emails_exclusion_list.filter.enabled'] = false;

/**
 * List of email domain for devops feature of blocking email which not open recently and belong to those domains
 * @show-config public
 */
$configs['retailer.emails_exclusion_list.domain'] = ['gmail'];

$configs['retailer.emails_exclusion_list.start_days_before'] = 14;
$configs['retailer.emails_exclusion_list.end_days_before']   = 0;

/**
 * Block/allow send email in no-prd(block as default) environment
 * If value set to false: some real customer email will be blocked, only <EMAIL> email will be sent
 * If value set to true:  all email will be sent (except blocking email)
 * For prod env, this config not effect, not matter true/false
 * @show-config public
 */
$configs['retailer.emails_filter.allow_email.any'] = true;  // temp allow all emails until all teams get aligned. Should be: $configs['env'] === Loader::ENV_STACK_PROD

/**
 * When our application blocks emails in test env by set $configs['retailer.emails_filter.allow_email.any'] = false
 * This config is used to send notification/email to rep let them know that such emails are blocked
 */
$configs['retailer.emails_filter.allow_email.block_notification'] = $configs['env'] !== Loader::ENV_STACK_PROD;

/**
 * Allow emails send to the domain of the list in no-prd environment
 * For example:
 * ['salesfloor.net'] will enable all 'salesfloor.net' email receive email in no-prd env: <EMAIL>
 *
 * @show-config public
 */
$configs['retailer.emails_filter.allow_email.domain_whitelist'] = ['salesfloor.net'];

/**
 * Allow emails send to the address with specific suffix in no-prd environment
 * For example:
 * ['__salesfloor_net'] will enable address to receive email in no-prd env: <EMAIL>
 *
 * @show-config public
 */
$configs['retailer.emails_filter.allow_email.address_suffix_whitelist'] = [];

// SF-25506 Enable or disable email links encoding.
$configs['retailer.email.encoded.link'] = true;

// SF-16416 Config to denote status of clienteling for retailer

/**
 * General CI config
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.mode'] = false;

/**
 * If set to true, we will export incremental for customer
 *
 * This is used also, so old retailer (without CI) that want to use CRM import without the full loop (import/export)
 *
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.customer.sync'] = false;

/**
 * Export email stats daily stats on the previous day.
 *
 * @show-config public
 * @feature email stats exporter
 */
$configs['exporter.email.stats.daily.enabled'] = false;

// SF-26110 Config to enable/disable limited visibility of contact info for clienteling.
/**
 * if true, limited visibility is enabled
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.customer.limited_visibility.is_enabled'] = false;

// SF-21344 Config to enable/disable certain sections of clienteling.
/**
 * if true, CI stat is enabled
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.enabled.customer_stats'] = true;
$configs['sf.import_ci_customers.encrypted'] = false;
$configs['retailer.clienteling.stats.importer.class'] =
    \Salesfloor\Services\Importer\RetailerCustomerStatsInsights\RetailerCustomerStatsInsightsImporter::class;
$configs['importer.customer_stats.fetcher'] = 'importer.fetcher.s3.retailer_stats';

/**
 * if true, CI transaction is enabled
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.enabled.transactions']   = true;

/**
 * If true, CI transaction attribution calculation will be enabled:
 *   a. calculate the attribution of the transaction
 *   b. exporter the retailer transaction attribution
 *
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.transactions.attribution.is_enabled'] = true;

/**
 * Define addition transaction types to scan/exporter in the attribution feature.
 * By default, this value is empty, since we only export sale transaction attributions, and NO addition file will be exported.
 * Some retailers(Belk) ask export Cancellation/Return transaction attribution as an additional file Each array is a separate file to be exported.
 * NOTE: this value must be 2 dimension array
 * ex:
 * [
 *     [Salesfloor\Models\RetailerTransaction::TYPE_RETURN, Salesfloor\Models\RetailerTransaction::TYPE_CANCELLATION]
 * ]
 *
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.transactions.attribution.addition_transaction_type'] = [];

/**
 * A config that decide if the boundary date should be included in the attribution calculation
 * CI transaction attribution will be scanned from -x days ago util now
 * If true,  include the x days and today's events [x,0]
 * If false, include the x+1 days ago until yesterday's events [-(x+1), -1]
 *
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.transactions.attribution.include_trx_date'] = true;


/**
 * A config that decide since when(time in milliseconds) should the attribution start calculation
 * CI transaction attribution will be scanned from -x days ago until now
 * If this value is null or empty, we will use 'retailer.sale_cookie_expire' value as fallback
 * Note: This value's unit is milliseconds, so  same unit with 'retailer.sale_cookie_expire'
 *       But transactions attribution could only be calculated with granularity of day
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.transactions.attribution.milliseconds_search_back'] = null;


$configs['sf.import_ci_transactions.encrypted'] = false;
$configs['retailer.clienteling.transactions.importer.class'] =
    \Salesfloor\Services\Importer\RetailerTransaction\RetailerCustomerTransactionImporter::class;
$configs['importer.transaction.fetcher'] = 'importer.fetcher.s3.retailer_transaction';
$configs['importer.transaction.loader'] = 'importer.retailer_customers_transaction.loader.id';

/**
 * If true, CI customer will create c2c files during importing
 * then ci:import:contacts could retrieve this file and import
 * @show-config public
 * @feature CI
 */
$configs['retailer.clienteling.enabled.create_contact'] = true;
$configs['retailer.clienteling.stats.manager.class']    = 'CustomerInsights\Stats\Panels';
$configs['sf.import_ci_stats.encrypted'] = false;

$configs['retailer.clienteling.customers.importer.class'] =
    \Salesfloor\Services\Importer\RetailerCustomer\RetailerCustomerImporter::class;
$configs['retailer.clienteling.plan.importer.class'] =
    \Salesfloor\Services\Importer\CustomerInsights\Importer::class;
$configs['retailer.clienteling.customers2contacts.class'] =
    \Salesfloor\Services\Importer\RetailerCustomer\CustomersToContacts::class;
$configs['importer.customer.fetcher'] = 'importer.fetcher.s3.retailer_customer';
$configs['importer.customer.loader'] = 'importer.retailer_customers.loader.id';

// Default config for ci data that should be used when possible
$configs['sf.import_ci_customers.s3.path'] = 'inbound/{env}/ci-customers';
$configs['sf.import_ci_customers.s3.filename_regexp'] = '{retailer}-customers-\d{8}-\d{6}\.csv';

// ------------------- Shopify Importer -------------------
/**
 * Retailer's shopify store url if Shopify importer product/customer/transaction is applied
 * @feature shopify-importer
 */
$configs['shopify.url']         = null;  // to be replaced at retailer's config

/**
 * Explicitly specify Shopify api version to avoid break changes
 * @feature shopify-importer
 */
$configs['shopify.api_version'] = '2024-07';

/**
 * Delete a contact who without the primary pr secondary assignment
 * @feature customer-deletion
 */
$configs['retailer.contacts.delete_unassociated_ci_imported.is_enabled'] = false;

/**
 * Delete customers who were not changed(created_at && updated_at) after last successful import
 * @feature customer-deletion
 */
$configs['retailer.retailer_customers.delete_not_changed_recently.is_enabled'] = false;

/**
 * Safety guard parameters for Delete customers who were not changed
 * A very high value will disable the Safety guard as default
 *
 * @feature customer-deletion
 */
$configs['retailer.retailer_customers.delete_not_changed_recently.safety_guard'] = [
    'allow_delete_count' => 10000,
    'allow_delete_ratio' => 1.01,
];

/**
 * Safety guard parameters for Delete contacts who is not match to retailer customers
 * A very high value will disable the Safety guard as default
 *
 * @feature customer-deletion
 */
$configs['retailer.retailer_customers.delete_contact_with_orphan_retailer_customer.safety_guard'] = [
    'allow_delete_count' => 5000,
    'allow_delete_ratio' => 1.01,
];

/**
 * ci customer id remapping, the config of path/file on cloud storage for downloading
 * @feature CI
 */
$configs['importer.customer_remapping.s3.path'] = 'inbound/{env}/crm-remapping';
$configs['importer.customer_remapping.s3.filename_regexp'] = '{retailer}-ci-customer-remapping-\d{8}-\d{6}\.csv';
$configs['importer.customer_remapping.encrypted'] = false;

/**
 * the csv file delimiter for CI retailer customer,
 * if not be setup in config or value is empty, will use default value ','
 */
$configs['importer.customer.ingestor.csv.delimiter'] = null;

$configs['sf.import_ci_stats.s3.path'] = 'inbound/{env}/ci-stats';
$configs['sf.import_ci_stats.s3.filename_regexp'] = '{retailer}-statistics-\d{8}-\d{6}\.csv';
/**
 * the csv file delimiter for CI retailer stats,
 * if not be setup in config or value is empty, will use default value ','
 */
$configs['importer.customer_stats.ingestor.csv.delimiter'] = null;

$configs['sf.import_ci_transactions.s3.path'] = 'inbound/{env}/ci-transactions';
$configs['sf.import_ci_transactions.s3.filename_regexp'] = '{retailer}-transactions-\d{8}-\d{6}\.csv';
/**
 * the csv file delimiter for CI retailer transactions,
 * if not be setup in config or value is empty, will use default value ','
 */
$configs['importer.transaction.ingestor.csv.delimiter'] = null;
/**
 * Rep transaction importer S3 path
 * @show-config public
 */
$configs['importer.rep_transaction.s3.path'] = 'inbound/{env}/transactions';
/**
 * Rep transaction importer S3 filename pattern
 * @show-config public
 */
$configs['importer.rep_transaction.s3.filename_regexp'] = '{retailer}-transactionupdates-\d{8}-\d{6}.csv';
/**
 * Rep transaction importer ingestor service
 * @show-config public
 */
$configs['importer.rep_transaction.ingestor'] = 'importer.ingestor.csv.transactions';

/**
 * Rep transaction importer fetcher service
 * @show-config public
 */
$configs['importer.rep_transaction.fetcher'] = 'importer.fetcher.s3.transaction';

/**
 * How many CI customers are inserted per insert query.
 * @show-config public
 * @feature CI
 */
$configs['sf.import_ci_customers.bulk_size'] = 500;

/**
 * How many CI transactions are inserted per insert query.
 * @show-config public
 * @feature CI
 */
$configs['sf.import_ci_transactions.bulk_size'] = 1000;

/**
 * How many lines per file to split for CustomersToContacts. If null, no split will be done.
 * Usually, we don't need to split c2c file. Sadly, if it crash, we need to start from the beginning.
 * Splitting will help us having data quicker AND no need to reprocess again if it crash.
 * 300k takes 8hrs to process (if no load). Don't put too small because we have some overhead.
 *
 * @show-config public
 * @feature CI
 */
$configs['sf.import_ci_customers.c2c_split_size'] = 300000;


/**
 * Which customer fields could be changed during update process
 * A dirty patch for Credobeauty Shopify importer,  more reason could refer config comments at credobeauty.php
 * @show-config public
 * @feature CI
 */
$configs['sf.import_ci_customers.update_fields_excluded'] = [];

// SF-16685 Attribution Assignment, per retailer window size
// Will be set to default 30 for now to match the existing acquisition Cookie in the widget
$configs['retailer.rules.attribution_days'] = 30;


/**
 * Is the task importer enabled?
 * @show-config public
 * @feature Task
 */
$configs['importer.tasks.is_enabled'] = false;

/**
 * Where are the S3 files for the task importer
 */
$configs['importer.tasks.s3.path'] = 'inbound/{env}/task/';

/**
 * How should the name of the file to be imported be formatted?
 */
$configs['importer.tasks.s3.filename_regexp'] = '{retailer}-task-\d{8}-\d{6}.csv';


// SF-16901 - Matching rule between contact<=>customer
// Those value should match the one in the database under the column "comment"
// This config is applied at the moment on both side (contact => customer AND customer => contact)

// Since in widgets we share the config, this result in a fatal error since this class doesn't exist
if (class_exists("\Salesfloor\API\Managers\CustomersToRetailerCustomers")) {
    $configs['retailer.clienteling.matching'] = [
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_CUSTOMER_ID,
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL,
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL,
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_ALTERNATE_EMAIL_TO_DEFAULT_EMAIL,
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_DEFAULT_EMAIL_TO_ALTERNATE_EMAIL,
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_RETAILER_PARENT_CUSTOMER_ID,
        \Salesfloor\API\Managers\CustomersToRetailerCustomers::MATCH_MANUAL,
    ];
}
/**
 * Toggle customer matching
 * @show-config public
 * @feature CI
 * @link https://salesfloor.atlassian.net/browse/DEVOPS-6449
 */
$configs['retailer.clienteling.matching.is_enabled'] = true;

/**
 * Who to send CI import notifications to
 * @show-config public
 * @feature CI
 * @link https://salesfloor.atlassian.net/browse/DEVOPS-1042
 */
$configs['retailer.clienteling.import.notification.emails'] = [];

// SF-17530 Allow to set reasonable nags suggestions per retailer
$configs['retailer.nags_eligible_suggestions'] = [
    'onboarding_description',
    'onboarding_social',
    'onboarding_picture',
    'onboarding_emails',
    'email',
    'share',
    'product',
    'deal',
    'new_arrivals',
];

// SF-12726 Point product url to retailer's QA environment when in staging
$configs['retailer.qa_product_url_parser.pattern'] = null;
$configs['retailer.qa_product_url_parser.replace'] = null;

/****************************************************
 ***** SF-17265 Trigger Alarms For All Queues   *****
 ***** START  Configs For AWS CloudWatch Alarms *****
 ****************************************************/

// List of the SNS (Notification) Topics (Channels) available to use with the CloudWatch Alarms
$configs['sns.topics.all'] = ['action-required', 'operations', 'pager-duty'];
// LOW PRIORITY Send <NAME_EMAIL>
$configs['sns.topics.operations'] = 'arn:aws:sns:us-east-1:************:operations';
// HIGH PRIORITY Send notification that needs to be acted upon to Pager-Duty (slack)
$configs['sns.topics.pager-duty'] = 'arn:aws:sns:us-east-1:************:pager-duty';
// LOW PRIORITY Send <NAME_EMAIL>
$configs['sns.topics.action-required'] = 'arn:aws:sns:us-east-1:************:action-required';

// List of alarms to be enabled. These values will be used below in configs
$configs['alarms.enabled'] = [
    'events_ApproximateAgeOfOldestMessage',
    'events-dead-letter_ApproximateNumberOfMessagesVisible',
    'events-dead-letter_ApproximateAgeOfOldestMessage',
    'share-an-update_ApproximateAgeOfOldestMessage',
    'cakemail-relays_ApproximateAgeOfOldestMessage',
    'cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible',
    'messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible',
    'record-transactions_ApproximateAgeOfOldestMessage',
    'record-transactions-dead-letter_ApproximateNumberOfMessagesVisible',
];

// Descriptions / Reference for each of the required configs can be found in:
// http://docs.aws.amazon.com/aws-sdk-php/v2/api/class-Aws.CloudWatch.CloudWatchClient.html#_putMetricAlarm
// HIGH PRIORITY Pager-Duty - Events queue has messages older than 16h
// Description will be autogenerated based on context if not set / null
$configs['alarms.events_ApproximateAgeOfOldestMessage.alarm_description'] = 'Messages lingering in Events Queue. Activate more ReadEventQueue workers on Supervisor. Look into possible reasons for server slow downs or increased events';
$configs['alarms.events_ApproximateAgeOfOldestMessage.actions_enabled'] = true;
$configs['alarms.events_ApproximateAgeOfOldestMessage.ok_actions_configs'] = ['action-required'];
$configs['alarms.events_ApproximateAgeOfOldestMessage.alarm_actions_configs'] = ['action-required'];
$configs['alarms.events_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs'] = [];
// See for metrics http://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/sqs-metricscollected.html
$configs['alarms.events_ApproximateAgeOfOldestMessage.metric_name'] = 'ApproximateAgeOfOldestMessage';
$configs['alarms.events_ApproximateAgeOfOldestMessage.namespace'] = 'sqs';
$configs['alarms.events_ApproximateAgeOfOldestMessage.statistic'] = 'Average';
$configs['alarms.events_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs'] = ['queue.events.full_name'];
$configs['alarms.events_ApproximateAgeOfOldestMessage.period_in_seconds'] = 5 * 60; // 5 Minutes
$configs['alarms.events_ApproximateAgeOfOldestMessage.unit'] = 'Seconds';
$configs['alarms.events_ApproximateAgeOfOldestMessage.evaluation_periods'] = 1;
$configs['alarms.events_ApproximateAgeOfOldestMessage.threshold'] = 57600; // 57,600 seconds = 16 hours
$configs['alarms.events_ApproximateAgeOfOldestMessage.comparison_operator'] = 'GreaterThanThreshold';

// LOW PRIORITY Action-Required (email) - Dead Letter Events queue has more than 100 messages visible in average across 15 minutes
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description'] = 'Too many messages in Dead Letter Events Queue. Look into reason for growing number of dead letter events. Database Issues / Errors are the usual reason for being placed in dead letter';
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.actions_enabled'] = true;
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs'] = [];
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs'] = ['action-required'];
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs'] = [];
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.metric_name'] = 'ApproximateNumberOfMessagesVisible';
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.namespace'] = 'sqs';
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.statistic'] = 'Average';
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs'] = ['queue.events.dead_letter_name'];
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds'] = 15 * 60; // 15 Minutes
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.unit'] = 'Count';
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods'] = 1;
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.threshold'] = 100;
$configs['alarms.events-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator'] = 'GreaterThanThreshold';

// LOW PRIORITY Action-Required (email) - Dead Letter Events queue email contains emails that are older than 10 days (Expiry at 14 days)
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.alarm_description'] = 'Messages are going to expire in 4 days from Dead Letter Queue. Evaluate if there is an important number of messages that will expire or not and act upon them';
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.actions_enabled'] = true;
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.ok_actions_configs'] = [];
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.alarm_actions_configs'] = ['action-required'];
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs'] = [];
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.metric_name'] = 'ApproximateAgeOfOldestMessage';
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.namespace'] = 'sqs';
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.statistic'] = 'Average';
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs'] = ['queue.events.dead_letter_name'];
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.period_in_seconds'] = 15 * 60; // 15 Minutes
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.unit'] = 'Seconds';
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.evaluation_periods'] = 4;
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.threshold'] = 10 * 24 * 60 * 60; // Oldest message is > 10 days (seconds)
$configs['alarms.events-dead-letter_ApproximateAgeOfOldestMessage.comparison_operator'] = 'GreaterThanThreshold';

// HIGH PRIORITY Pager-Duty - share-an-update queue has messages older than 30 minutes
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.alarm_description'] = 'Messages lingering in share-an-update Queue. Look into why emails are not being popped off the queue / sent by ESP';
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.actions_enabled'] = true;
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.ok_actions_configs'] = ['action-required'];
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.alarm_actions_configs'] = ['action-required'];
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs'] = [];
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.metric_name'] = 'ApproximateAgeOfOldestMessage';
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.namespace'] = 'sqs';
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.statistic'] = 'Average';
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs'] = ['queue.share-an-update'];
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.period_in_seconds'] = 5 * 60; // 5 Minutes
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.unit'] = 'Seconds';
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.evaluation_periods'] = 1;
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.threshold'] = 1800;
$configs['alarms.share-an-update_ApproximateAgeOfOldestMessage.comparison_operator'] = 'GreaterThanThreshold';

// HIGH PRIORITY Pager-Duty - email queue has messages older than 30 minutes
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.alarm_description'] = 'Messages lingering in cakemail-relays Queue. Look into why emails are not being popped off the queue / sent by ESP';
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.actions_enabled'] = true;
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.ok_actions_configs'] = ['action-required'];
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.alarm_actions_configs'] = ['action-required'];
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs'] = [];
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.metric_name'] = 'ApproximateAgeOfOldestMessage';
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.namespace'] = 'sqs';
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.statistic'] = 'Average';
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs'] = ['queue.cakemail-relays'];
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.period_in_seconds'] = 5 * 60; // 5 Minutes
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.unit'] = 'Seconds';
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.evaluation_periods'] = 1;
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.threshold'] = 1800; // 30 minutes
$configs['alarms.cakemail-relays_ApproximateAgeOfOldestMessage.comparison_operator'] = 'GreaterThanThreshold';

// SF-18249
// Low priority - there are messages in the Dead-Letter Mail Queue
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description'] = 'Unprocessable mail';
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.enabled'] = true;
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs'] = [];
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs'] = ['action-required'];
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs'] = [];
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.metric_name'] = 'ApproximateNumberOfMessagesVisible';
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.namespace'] = 'sqs';
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.statistic'] = 'Average';
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs'] = ['queue.cakemail-relays-dead-letter'];
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds'] = 60 * 60; // 1h
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.unit'] = 'Count';
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods'] = 1;
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.threshold'] = 1;
$configs['alarms.cakemail-relays-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator'] = 'GreaterThanOrEqualToThreshold';

// HIGH PRIORITY Pager-Duty - There are messages lingering in the Record Transactions queue
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.alarm_description'] = 'Old messages are piling up in the Record Transactions queue. Look into why transactions are not being recorded';
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.actions_enabled'] = true;
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.ok_actions_configs'] = ['action-required'];
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.alarm_actions_configs'] = ['action-required'];
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.insufficient_data_actions_configs'] = [];
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.metric_name'] = 'ApproximateAgeOfOldestMessage';
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.namespace'] = 'sqs';
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.statistic'] = 'Maximum';
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.dimensions_queue_name_configs'] = ['queue.record-transactions.full_name'];
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.period_in_seconds'] = 5 * 60; // 5 Minutes
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.unit'] = 'Seconds';
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.evaluation_periods'] = 1;
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.threshold'] = 30 * 60; // 30 Minutes
$configs['alarms.record-transactions_ApproximateAgeOfOldestMessage.comparison_operator'] = 'GreaterThanThreshold';

// Low priority - there are messages in the Record Transactions Dead-Letter Queue
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description'] = 'Unprocessable transaction events';
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.enabled'] = true;
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs'] = [];
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs'] = ['action-required'];
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs'] = [];
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.metric_name'] = 'ApproximateNumberOfMessagesVisible';
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.namespace'] = 'sqs';
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.statistic'] = 'Average';
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs'] = ['queue.record-transactions.dead_letter_name'];
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds'] = 60 * 60; // 1h
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.unit'] = 'Count';
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods'] = 1;
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.threshold'] = 1;
$configs['alarms.record-transactions-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator'] = 'GreaterThanOrEqualToThreshold';

/****************************************************
 ***** SF-17265 Trigger Alarms For All Queues   *****
 ***** FINISH Configs For AWS CloudWatch Alarms *****
 ****************************************************/

// If you have only 1 ips, you don't need to specific any threshold. By default, the key is 0
$configs['sf.sendgrid.threshold.ips'] = $configs['env'] === Loader::ENV_STACK_PROD ? [
    // [0 - 80[ => extra bad (Do nothing, just send pager duty)
    '80' => '*************',    # bad [80 - 90[
    '90' => '**************',   # unknown [90 - 95[
    '95' => '**************',   # good [95-
] : [
    '**************',
];

// How much offset we won't trigger a change of ips. This is used so we don't change ip every time we move over/under the threshold
$configs['sf.sendgrid.threshold.leeway'] = 1;

$configs['sf.pagerduty.integrationkey.v1'] = '54f872e74aa148dabc064a4fda8c41a2';

// Can't find any usage of those two configs in the project
$configs['messaging.email.enabled'] = false;
$configs['messaging.social.enabled'] = false;

/**
 * If true, txt messaging is enabled
 *
 * Allow associates to recommend products, add photos and attach assets without the need for messaging apps or cellular plans.
 * NOTE: usually configs 'messaging.text.enabled' and 'retailer.services.channel.text.enabled' should have same value
 * @show-config public
 * @feature SMS
 */
$configs['messaging.text.enabled'] = false;

/**
 * txt/sms message will not send if some keywords exist
 *
 * @feature SMS
 */
$configs['content.text.twilio_blocklist.is_enabled'] = true;

$configs['messaging.text.twilio.override_callback_host'] = null;
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['messaging.text.twilio.account_sid'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['messaging.text.twilio.api_key_id'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['messaging.text.twilio.api_key_secret'] = 'fake';
// SF-19132 - SMS Services
$configs['messaging.text.default_country_code'] = '+1';
$configs['messaging.text.noreply_address'] = null;

/**
 * If true, txt messaging could send to multiple recipients from associate
 *
 * @show-config public
 * @feature SMS
 */
$configs['messaging.text.multiple-recipients.enabled'] = false;

//If true, associate can send an email to multiple recipients from mobileApp and the BO
$configs['messaging.email.multiple-recipients.enabled'] = true;

/**
 * Maximum txt messaging recipients could send from associate
 *
 * @show-config public
 * @feature SMS
 */
$configs['messaging.text.multiple-recipients.max'] = 40;

/**
 * if multiple-recipients equal or less than this value, send message directly instead of using queue
 * Default value is 1, If only 1 recipient then send to Twillio directly(not via queue), If more then 1 recipient then send to queue
 *
 * @show-config public
 * @feature SMS
 */
$configs['messaging.text.multiple-recipients.send_directly_limit'] = 1;

// Friendly Names of Twilio configured 'applications'
// Could be used for another provider as well
// An application represents a set of configurations that can be applied to a number when receiving text / call
$configs['messaging.text.application.messaging'] = 'Messaging';
$configs['messaging.text.application.noreply'] = 'NoReply';

// SF-17460 Enable Disable SMS by Associate
// HIGH PRIORITY Pager-Duty - The dead letter queue for Enabling Text Messages is starting to fill. Act on it manually
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.alarm_description'] = 'Messages in messaging-text-enable-dead-letter Queue. Either numbers are not being reserved with twilio / saved in DB OR released with twilio / deleted in DB. Check Supervisor processes. Empty queue and mark as resolved.';
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.actions_enabled'] = true;
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.ok_actions_configs'] = ['action-required'];
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.alarm_actions_configs'] = ['action-required'];
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.insufficient_data_actions_configs'] = [];
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.metric_name'] = 'ApproximateNumberOfMessagesVisible';
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.namespace'] = 'sqs';
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.statistic'] = 'Average';
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.dimensions_queue_name_configs'] = ['queue.text_messaging_enable.dead_letter_name'];
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.period_in_seconds'] = 5 * 60; // 5 Minutes
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.unit'] = 'Count';
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.evaluation_periods'] = 1;
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.threshold'] = 1;
$configs['alarms.messaging-text-enable-dead-letter_ApproximateNumberOfMessagesVisible.comparison_operator'] = 'GreaterThanOrEqualToThreshold';

/**
 * Importer a csv from S3 daily for unsubscribe customer
 *
 * @show-config public
 * @feature SMS
 */
$configs['importer.text_message_unsubscribe.enable'] = false;

/**
 * S3 path for text message unsubscribe importer
 *
 * @show-config public
 * @feature SMS
 */
$configs['importer.text_message_unsubscribe.s3.path'] = 'inbound/{env}/unsubscribes/text/';

/**
 * S3 file name for text message unsubscribe importer
 *
 * @show-config public
 * @feature SMS
 */
$configs['importer.text_message_unsubscribe.s3.filename_regexp'] = '{retailer}-textmessage_unsubscribes-\d{8}-\d{6}.csv';
$configs['importer.text_message_unsubscribe.s3.filename.header'] = true;

$configs['deep_links_enabled'] = true;

// LATEST CONFIGS

/**
 * If true, Dynamic content is enabled (storefront)
 *
 * @show-config public
 * @feature dynamic-content
 */
$configs['retailer.storefront.dynamic_content.is_active'] = false;

/**
 * Force dynamic content size. If null, we return everything from api (parse logic)
 *
 * @show-config public
 * @feature dynamic-content
 */
$configs['retailer.storefront.dynamic_content.max_num_posts'] = null;

/**
 * Api url, splitted per locale
 *
 * @show-config public
 * @feature dynamic-content
 */
$configs['retailer.storefront.dynamic_content.url'] = [];

// Multilingue - i18n
/**
 * If true, Multilang is enabled OR:
 * Even Multilang is disabled, but the retailer default language is not 'en_US', this value also need set to true
 * @show-config public
 * @feature multilang
 */
$configs['retailer.i18n.is_enabled'] = false;
/**
 * Default locale of the retailer
 *
 * @show-config public
 * @feature multilang
 */
$configs['retailer.i18n.default_locale'] = 'en_US'; // default retailer locale

/**
 * Default fallback locale of the retailer
 *
 * @show-config public
 * @feature multilang
 */
$configs['retailer.i18n.locale.fallback'] = 'en_US'; // fallback retailer locale

// Country code of the retailer
// Saks -> US
// Harry Rosen -> CA
// We use it to generate no reply phone number in the correct country for instance
$configs['retailer.country.code'] = 'US';

// SF-22658 - Fallback country codes to attempt for phone normalization.
// US and Canada are the defaults because North Americans don't put the country code
// on phone numbers by default, and they both share +1 anyway.
$configs['retailer.country.code.fallbacks'] = ['US', 'CA'];

/**
 * Array of method(s) used to sync widget with retailer website locale
 * -> widgets/app/bin/get_grunt_config.php
 * -> widgets/app/src/fe/widget/utils/widget.utils.locale.js
 * @feature multilang
 *
 * 1. ['url']
 * - Specify a matching url for a specified Locale
 *
 * Example:
 * - $configs['retailer.i18n.widgets.sync.url.fr_CA'] = '&lang=fr';
 * - $configs['retailer.i18n.widgets.sync.url.en_US'] = '&lang=en';
 *
 *
 * 2. ['cookie']
 * - Specify a matching cookie name for a specified Locale
 *
 * Example:
 * - $configs['retailer.i18n.widgets.sync.cookie.name'] = 'sk_locale';
 * - $configs['retailer.i18n.widgets.sync.cookie.fr_CA'] = 'fr_CA';
 * - $configs['retailer.i18n.widgets.sync.cookie.en_US'] = 'en_CA';
 *
 *
 * - Specify a function to evaluate Locale on retailer's website for a specified Locale
 *
 * Example:
 * - $configs['retailer.i18n.widgets.sync.jsGlobal.fr_CA'] = 'fr_CA';
 * - $configs['retailer.i18n.widgets.sync.jsGlobal.en_US'] = 'en_US';
 *
 * Function type, only one supported for now (Looks at land="" value on retailer's website)
 * - $configs['retailer.i18n.widgets.sync.jsGlobal.function'] = 'documentLang';
 *
 * Fallback used if value is empty
 * - $configs['retailer.i18n.widgets.sync.jsGlobal.fallback'] = 'en';
 */
$configs['retailer.i18n.widgets.sync.method'] = null;

// SF-17895 In case we can't show the correct locale because we don't support it,
//          at least we try to retrieve the matching language.
$configs['retailer.i18n.default_language_fallback'] = [
    'en' => 'en_US',
    'fr' => 'fr_CA',
];

// SF-18097 Get a per retailer configurable locale display name.
//          Structure = {locale}_{inLanguage}
$configs['retailer.i18n.locale_display_name'] = [
    'en_US_en' => 'English',
    'en_US_fr' => 'Anglais',
    'fr_CA_en' => 'French',
    'fr_CA_fr' => 'Français',
];

/**
 * Default fallback locale of the retailer's product inventory
 *
 * NOTE: Very IMPORTANT for retailer who has only one locale but not 'en_US'
 * If we don't enable this, then product wouldn't be retrieved correctly
 *
 * @show-config public
 * @feature multilang
 */
$configs['retailer.i18n.products.is_default_locale_fallback'] = false;
/**
 * All locales globally available to the retailer.
 *
 * @show-config public
 * @feature multilang
 */
$configs['sf.i18n.locales'] = ['en_US']; // all locales globally available

// === Localization - l10n  ===
/**
 * this config is used for html element of phone input/display
 */
$configs['sf.l10n.phone_format'] = [
    'place_holder' => '************',
    'format'       => 'ddd-ddd-dddd',
];

/**
 * Whether-or-not to import contacts as email subscribers when running the CRM Import Tool
 * See https://salesfloor.atlassian.net/browse/DEVOPS-112
 *
 * @show-config public
 * @feature CI Importer
 */
$configs['importer.import-as-subscriber.email'] = 1;


/**
 * Whether-or-not to import contacts as SMS subscribers when running the CRM Import Tool
 *
 * @show-config public
 * @feature CI Importer
 */
$configs['importer.import-as-subscriber.sms'] = 0;


// SF-17783 Reporting week (0 = sunday, 1 = monday, ... 6 = saturday)
$configs['retailer.reporting.begin_week'] = 0;

// SF-17629 Use a config in case in the futur we want to add the minute to the frequency of the cron for reminder task
$configs['sf.task.reminder.cron'] = 5;

/**
 * START: Refactored Automated / Manual Task Configs
 */
$configs['sf.task.manual.enabled'] = true; // No code is checking this as of yet
$configs['sf.task.manual.emails_enabled'] = false;
$configs['sf.task.manual.notifications_enabled'] = true;

/**
 * Enable / disable automated tasks
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.enabled'] = true;

$configs['sf.task.automated.emails_enabled'] = false;
$configs['sf.task.automated.notifications_enabled'] = true;

/**
 * Enable / disable auto-dismissal for automated tasks
 * @show-config public
 * @feature Task
 */
$configs['sf.task.auto_dismiss.enabled'] = false;

/**
 * Number of days after the reminder to auto-dismiss a task
 * @show-config public
 * @feature Task
 */
$configs['sf.task.auto_dismiss.settings'] = [
    [
        'happen_after_event' => TaskAutoDismissProcessor::HAPPEN_AFTER_REMINDER,
        'days_after_event'   => TaskAutoDismissProcessor::DEFAULT_DAYS_AFTER_EVENT,
        'automated_types'    => TaskAutoDismissProcessor::DEFAULT_AUTOMATED_TYPES
    ],
];


// SF-18099 Storefront Related Automated Tasks
$configs['sf.task.automated.nag_update_storefront_products.enabled'] = true;
$configs['sf.task.automated.nag_update_storefront_products.days_search_back'] = 30;
$configs['sf.task.automated.nag_update_storefront_products.days_recur_after'] = 30;
$configs['sf.task.automated.nag_update_storefront_products.emails_enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.notifications_enabled'] = true;
$configs['sf.task.automated.nag_update_storefront_products.category'] = null;

// SF-18099 Storefront Related Automated Tasks
$configs['sf.task.automated.nag_share_update.enabled'] = true;
$configs['sf.task.automated.nag_share_update.days_search_back'] = 30;
$configs['sf.task.automated.nag_share_update.days_recur_after'] = 30;
$configs['sf.task.automated.nag_share_update.emails_enabled'] = false;
$configs['sf.task.automated.nag_share_update.notifications_enabled'] = true;
$configs['sf.task.automated.nag_share_update.category'] = null;

// PP-28 Automated Task - New Customer Purchase - Filtered to TOP 5 transactions per store
/**
 * Enable the New CI Transaction (TOP X) task
 * Docs: https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1176862721/i.+CI+Sale+Transaction+Follow-Up+Primary+Associate+Store
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;

/**
 * X days back to look for matching transactions for New CI Transactions TOP X
 * TODO : this config could be removed/deprecated
 * IMPORTANT NOTE: if we are using config of new_retailer_transaction_filtered.dynamic_rule, this config should be set empty value
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 30 ];

/**
 * When automated task is enable,  ex:NewRetailerTransactionFilteredMultiple on BenBridge
 * the retailer require switching filter condition during runtime for a same type of task
 * Config value is a rule of details List:
 * [
 *     //MANDATORY if 'sf.task.automated.new_retailer_transaction_filtered.days_search_back' is remove/empty value
 *     'default'   => [
 *        'days_search_back' => [10],      // MANDATORY: an array of multiple days that wil be scanned
 *        'max_per_owner'                  // MANDATORY: a replace of sf.task.automated.new_retailer_transaction_filtered.max_per_owner'
 *     ],
 *    'rule_key_xxxx' => [                  // rule short description as a key
 *        'days_search_back' => [180],      // MANDATORY: an array of multiple days that wil be scanned
 *          // ALERT: if a same day exist in different rulesetS and in 'sf.task.automated.new_retailer_transaction_filtered.days_search_back'
 *          // then task for a same day would be generated multiple times, and max_per_owner applying per rule set,
 *          // WESHOULD NOT LET THIS HAPPEN IN CONFIG
 *        'max_per_owner'                   // sf.task.automated.new_retailer_transaction_filtered.max_per_owner'
 *        'min'                             // min transaction ( We should not change this value since it is global for all transaction task
 *                                             List here only give capacity if really a need)
 *        'task_details_key'                // translation key
 *        'filter_function'                 // a callable function implement per different retailer
 *    ],
 * ]
 *
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.dynamic_rule'] = [];

/**
 * Send emails notifying reps of new tasks
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.emails_enabled'] = false;
/**
 * Send notifications notifying reps of new tasks
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.notifications_enabled'] = true;

/**
 * Task category name to assign to each task
 * Category with this name MUST exist in sf_task_category or else NULL will be used.
 * Valid default options: ['Generic', 'Promotions', 'Services']
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.category'] = 'Services';

/**
 * The maximum number of tasks to generate per task owner
 *
 * In team mode, the task owner is the store. In rep mode, it's a rep.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 5;

/**
 * When the Any Store configuration is set, the Primary Employee/Store will get the task assigned REGARDLESS of the
 * store in which the transaction occurred.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_retailer_transaction_filtered.any_store'] = false;

/**
 * Enable the creation of automated tasks from CI transactions with employee assigned.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = false;

/**
 * Search CI transactions with employee assigned that created only X days ago.
 * @note days_search_back could be also array to support task run multiple times ex: [5], [5,30,90]
 *
 * @show-config public
 * @feature Task
 */

$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 5;
/**
 * Enable email notification for automated tasks from CI transactions with employee assigned
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.emails_enabled'] = false;

/**
 * Enable phone notification for automated tasks from CI transactions with employee assigned
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.notifications_enabled'] = true;

/**
 * Task category for CI transactions with employee assigned
 * Category with this name MUST exist in sf_task_category or else NULL will be used.
 * Valid default options: ['Generic', 'Promotions', 'Services']
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.category'] = 'Services';

/**
 * Config to switch tasks creation of CI transactions with employee assigned from option1 to option2.
 *
 * option1(config is true): if there's no matching contact record, task will not be created.
 * option2(config is false): if there's no matching contact record, a new contact record will be created and
 * task will be created based on the new contact record created.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;

/**
 * Only 5 tasks are allowed to be created from CI transactions with employee assigned
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 5;

/**
 * When automated task is enable,  ex:RetailerTransactionEmployeeAssignedMultiple on BenBridge
 * the retailer require switching filter condition during runtime for a same type of task
 * Config value is a rule of details List:
 * [
 *     //MANDATORY if 'sf.task.automated.retailer_transaction_employee_assigned.days_search_back' is remove/empty value
 *     'default'   => [
 *        'days_search_back' => [10],      // MANDATORY: an array of multiple days that wil be scanned
 *        'max_per_owner'                  // MANDATORY: a replace of sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'
 *     ],
 *    'rule_key_xxxx' => [                  // rule short description as a key
 *        'days_search_back' => [180],      // MANDATORY: an array of multiple days that wil be scanned
 *          // ALERT: if a same day exist in different rulesets and in 'sf.task.automated.retailer_transaction_employee_assigned.days_search_back'
 *          // then task for a same day would be generated multiple times, and max_per_owner applying per rule set,
 *          // WE SHOULD NOT LET THIS HAPPEN IN CONFIG
 *        'max_per_owner'                   // sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'
 *        'min'                             // min transaction ( We should not change this value since it is global for all transaction task
 *                                             List here only give capacity if really a need)
 *        'task_details_key'                // translation key
 *        'filter_function'                 // a callable function implement per different retailer
 *    ],
 * ]
 *
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.dynamic_rule'] = [];

/**
 * X days back to look for matching transactions for New CI Transactions TOP X
 * TODO : this config could be removed/deprecated
 * IMPORTANT NOTE: if we are using config of retailer_transaction_employee_assigned.dynamic_rule, this config should be set empty value
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [ 30 ];

// PP-29 Automated Task - Soon To Lapse (Customer) - Filtered to TOP 5 transactions per store
/**
 * Generate tasks for customers whose last transaction was X days ago, and hasn't had any since.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;

/**
 * Enabled/Disabled sending an email as a reminder
 * @show-config public
 * @feature GroupTask
 */
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.emails_enabled'] = false;

/**
 * Send notifications notifying reps of new tasks
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.notifications_enabled'] = true;

/**
 * Task category name to assign to each task
 * Category with this name MUST exist in sf_task_category or else NULL will be used.
 * Valid default options: ['Generic', 'Promotions', 'Services']
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.category'] = 'Services';

/**
 * if true then assign tasks based on Secondary Employee of transaction related customer, instead of Primary Employee(default setting)
 * which means this config default value is false
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled'] = false;

/**
 * When automated task is enable,  ex:RetailerCustomerSoonToLapseFilteredMultiple
 * the retailer require switching filter condition during runtime for a same type of task
 * Config value is a rule of details List:
 * [
 *     'default'   => [
 *        'days_search_back' => [1],        // MANDATORY: How many days ago the last transaction has to be before it's considered "soon to lapse", Array of multiple days to scan
 *        'max_per_owner'                   // MANDATORY: The maximum number of tasks to generate per task owner
 *        'task_details_key'                // OPTIONAL: translation key
 *        'filter_function'                 // OPTIONAL: a callable function implement per different retailer
 *     ],                   // Can also add different rules like default if retailers have specific asks
 *    'rule_key_xxxx' => [                  // rule short description as a key
 *        'days_search_back' => [5],        // MANDATORY: How many days ago the last transaction has to be before it's considered "soon to lapse", Array of multiple days to scan
 *        'max_per_owner'                   // MANDATORY: The maximum number of tasks to generate per task owner
 *        'task_details_key'                // OPTIONAL: translation key
 *        'filter_function'                 // OPTIONAL: a callable function implement per different retailer
 *    ],
 * ]
 *
 * Example of how to enable this Config value for a retailer with dynamic rules:
 * $configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
 *   'default' => [
 *       'days_search_back' => [1],
 *       'max_per_owner'    => 2,
 *       'task_details_key' => 'api_automated_tasks_detail_retailer_customer_soon_to_lapse_filtered',   // OPTIONAL
 *      ],
 *   'secondary' => [
 *       'days_search_back' => [30],
 *       'max_per_owner'    => 2,
 *       'task_details_key' => 'api_automated_tasks_detail_retailer_customer_soon_to_lapse_filtered',   // OPTIONAL
 *      ],
 * ];
 *
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
    'default' => [
        'days_search_back' => $configs['env'] === Loader::ENV_STACK_PROD ? [120] : [5],
        'max_per_owner'    => 5,
    ],
];

// PP-19 Onboarding Related Automated Tasks
$configs['sf.task.automated.nag_onboarding_update_about_me.enabled'] = true;
$configs['sf.task.automated.nag_onboarding_update_about_me.days_search_back'] = 30;
$configs['sf.task.automated.nag_onboarding_update_about_me.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_update_about_me.notifications_enabled'] = true;
$configs['sf.task.automated.nag_onboarding_update_about_me.category'] = null;

$configs['sf.task.automated.nag_onboarding_upload_profile_pic.enabled'] = true;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.days_search_back'] = 30;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.notifications_enabled'] = true;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.category'] = null;

$configs['sf.task.automated.nag_onboarding_connect_social_media.enabled'] = true;
$configs['sf.task.automated.nag_onboarding_connect_social_media.days_search_back'] = 30;
$configs['sf.task.automated.nag_onboarding_connect_social_media.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.notifications_enabled'] = true;
$configs['sf.task.automated.nag_onboarding_connect_social_media.category'] = null;

$configs['sf.task.automated.nag_onboarding_add_contacts.enabled'] = true;
$configs['sf.task.automated.nag_onboarding_add_contacts.days_search_back'] = 30;
$configs['sf.task.automated.nag_onboarding_add_contacts.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.notifications_enabled'] = true;
$configs['sf.task.automated.nag_onboarding_add_contacts.category'] = null;

// SF-20474 Automated Task - New  Customer Purchase linked to an Associate
/**
 * Generate tasks for new SF transactions
 *
 * If CI is enabled, will only create tasks for retailer assigned users.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_rep_transaction.enabled'] = false;
/**
 * Numbers of days back to look for SF transactions
 *
 * This is for transactions on a single day, X days ago.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 30;
$configs['sf.task.automated.new_rep_transaction.emails_enabled'] = false;
$configs['sf.task.automated.new_rep_transaction.notifications_enabled'] = true;
/**
 * Task category name to assign to each task
 * Category with this name MUST exist in sf_task_category or else NULL will be used.
 * Valid default options: ['Generic', 'Promotions', 'Services']
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_rep_transaction.category'] = 'Services';

/**
 * Generate tasks for new retailer customer registry future event by stats
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_stats_registry_event.enabled'] = false;

/**
 * Generate tasks for new retailer customer registry follow up task by stats
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_stats_registry_followup.enabled'] = false;
/**
 * The maximum number of tasks to generate per task owner
 *
 * In team mode, the task owner is the store. In rep mode, it's a rep.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 5;

/**
 * The minimum value of the transaction considered in the task creation.
 * If 0, it's considered disabled.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transaction.min'] = 0;

/**
 * Enable the creation of automated tasks from CI transactions for stores.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transactions_distribution_by_stores.is_enabled'] = false;

/**
 * Search CI transactions that created only X days ago for stores distribution.
 *
 * @show-config public
 * @feature Task
 */

$configs['sf.task.automated.transactions_distribution_by_stores.days_search_back'] = 5;
/**
 * Enable email notification for automated tasks from CI transactions for stores distribution
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transactions_distribution_by_stores.emails_enabled'] = false;

/**
 * Enable phone notification for automated tasks from CI transactions for stores distribution
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transactions_distribution_by_stores.notifications_enabled'] = true;

/**
 * Task category for CI transactions for stores distribution
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transactions_distribution_by_stores.category'] = 'Services';

/**
 * Only 5 tasks are allowed to be created from CI transactions for stores distribution
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transactions_distribution_by_stores.max_per_owner'] = 5;

/**
 * The radius within that we'll find the closed store from a given customer's address.
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.transactions_distribution_by_stores.distance_radius'] = 99999;

$configs['sf.task.automated.transactions_distribution_by_stores.min_trx_total'] = 0;

/**
 * Enable/Disable cancellation transaction follow up task
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.enabled'] = false;

/**
 * Specify the category for the cancellation transaction follow up task
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.category'] = null;

/**
 * Enable/Disable emails for cancellation transaction follow up task
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.emails_enabled'] = false;

/**
 * Enable/Disable notifications for cancellation transaction follow up task
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.notifications_enabled'] = false;

/**
 * Maximum tasks to be created per associate per day
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.max_daily_threshold_per_associate'] = 5;

/**
 * Task will only be created if the transaction is imported before the following day threshold

 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.import_day_threshold'] = 1;

/**
 * Select the criteria for task creation. Available criteria are defined in the CancelledTransactionFollowUp.
 * They are as following:
 * 1. CRITERIA_CUSTOMER_REQUIRED :              Task will only be created when the corresponding transaction contains an existing
 *                                              subscribed customer.
 * 2. CRITERIA_CUSTOMER_NOT_REQUIRED :          Task will be created whether the customer exists or not. One of the side
 *                                              effects of using this strategy is that the customer would be automatically
 *                                              created if the record doesn't exist.
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.cancelled_transaction_follow_up.task_creation_criteria'] = CancelledTransactionFollowUp::CRITERIA_CUSTOMER_REQUIRED;

/**
 * Enable the Retailer Customer Event task
 * Docs: https://salesfloor.atlassian.net/wiki/spaces/SB/pages/42975920134/Automated+Task+-+Retailer+Customer+Event
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_event.enabled'] = false;

/**
 * Task category name to assign to each task
 * Category with this name MUST exist in sf_task_category or else NULL will be used.
 * Valid default options: ['Generic', 'Promotions', 'Services']
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_event.category'] = 'Services';

/**
 * Enabled/Disabled sending an email as a reminder
 * @show-config public
 * @feature GroupTask
 */
$configs['sf.task.automated.retailer_customer_event.emails_enabled'] = false;

/**
 * Send notifications notifying reps of new tasks
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_event.notifications_enabled'] = true;

/**
 * TODO & NOTE: this config could be removed/deprecated
 * IMPORTANT NOTE: if we are using config of new_retailer_transaction_filtered.dynamic_rule, this config should be set empty value
 *
 * @show-config public
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_event.days_search_back'] = 0;

/**
 * When automated task is enable,  ex:RetailerCustomerEventMultiple
 * the retailer require switching filter condition during runtime for a same type of task
 * Config value is a rule of details List:
 * [
 *     'default'   => [
 *        'days_search_back' => [1],        // MANDATORY: an array of multiple days that wil be scanned
 *        'max_per_owner'                   // MANDATORY: The maximum number of tasks to generate per task owner
 *        'task_details_key'                // MANDATORY: translation key
 *        'filter_function'                 // OPTIONAL: a callable function implement per different retailer
 *     ],                   // Can also add different rules like default if retailers have specific asks
 *    'rule_key_xxxx' => [                  // rule short description as a key
 *        'days_search_back' => [5],        // MANDATORY: an array of multiple days that wil be scanned
 *        'max_per_owner'                   // The maximum number of tasks to generate per task owner
 *        'task_details_key'                // translation key
 *        'filter_function'                 // a callable function implement per different retailer
 *    ],
 * ]
 *
 * Example of how to enable this Config value for a retailler with dynamic rules:
 * $configs['sf.task.automated.retailer_customer_event.dynamic_rule'] = [
 *   'filterByBirthday' => [
 *       'days_search_back' => [1],
 *       'max_per_owner'    => 2,
 *       'task_details_key' => 'api_automated_tasks_detail_retailercustomerevent',
 *       'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Generic\RetailerCustomerEvent::filterByBirthday',
 *     ],
 *     'filterByAnniversary' => [
 *       'days_search_back' => [30],
 *       'max_per_owner'    => 2,
 *       'task_details_key' => 'api_automated_tasks_detail_retailercustomerevent',
 *       'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Generic\RetailerCustomerEvent::filterByAnniversary',
 *     ],
 *     'filterByMiscDate' => [
 *       'days_search_back' => [2],
 *       'max_per_owner'    => 2,
 *       'task_details_key' => 'api_automated_tasks_detail_retailercustomerevent_miscdate',
 *       'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Generic\RetailerCustomerEvent::filterByMiscDate',
 *     ],
 * ];
 *
 *
 * @feature Task
 */
$configs['sf.task.automated.retailer_customer_event.dynamic_rule'] = [];


/**
 * Retailer's online store id
 *
 * Set this value as string '0' because when the type is not sure and comparing string with integer, the string will be
 * casted to 0. For example, 'fsfdsa' == 0 returns true. In addition, in our system, retailer store id can be string and
 * not numeric, for example, the retailer_store_id column in sf_store table and location column in sf_retailer_transaction table.
 *
 * @show-config public
 */
$configs['retailer.online_store_id'] = '0';

/**
 * Online Inventory name fallback
 *
 * @show-config public
 * @feature Inventory Lookup
 */
$configs['inventory.lookup.retailer.online_store_name'] = 'Web';

/**
 * END: Refactored Automated / Manual Task Configs
 */

// SF-18086 Log Rotation Configs

/**
 * This config is overridden by infraDerived
 *
 * @show-config public
 * @feature Infra
 */
$configs['logrotate.s3.bucket'] = '__dynamic__';

// DEFAULT s3 Provider
$configs['logrotate.s3.provider'] = 'gcp';
$configs['logrotate.s3.region']   = 'us-east-1';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['logrotate.s3.key'] = 'fake';
/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['logrotate.s3.secret'] = 'fake';

// DEVOPS-3013 Log Rotation Config for client uploads
$configs['logrotate_client.s3.bucket'] = '';
$configs['logrotate_client.s3.provider'] = 'gcp';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['logrotate_client.s3.secret'] = '';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['logrotate_client.s3.key'] = '';

// default provider if none specified
$configs['s3.provider'] = 'aws';

// SF-18042 Lockout account when login failed multiple time
$configs['retailer.lockout.duration'] = 20; // Nb of minutes you are locked out
$configs['retailer.lockout.try'] = 5; // Nb of tries before being locked out
$configs['retailer.lockout.try.duration'] = 30; // Duration (In minute) before resetting to 0 (nb of tries)

// SF-18198 API dynamic strings multilang support
$configs['api.locale_dir'] = PATH_ROOT . '/locale';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sf.onboarding.security.secretkey'] = 'fake';
$configs['sf.onboarding.security.method'] = 'AES-128-CTR';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sf.security.secretkey'] = 'fake';

$configs['retailer.services.findarep.is_enabled'] = true;

// Testing
$configs['tests.routing.messagev0'] = 'message 0';
$configs['tests.routing.messagev1'] = 'bar';
$configs['tests.routing.messagev2'] = 'message 2';
$configs['tests.routing.messagev3'] = 'message 3';

$configs['retailer.is-storename-identifier'] = false;

/**
 * This feature allows retailer to regroup contact in sub group.
 *
 * Tags are only imported from retailers to SF (SF do not provide UI for tag creation)
 * Retailers can define unlimited number of tags per customer
 *
 * @show-config public
 * @feature Tags
 */
$configs['retailer.customer_tags.is_enabled'] = false;

/**
 * If true, customer tags are read only
 *
 * @show-config public
 * @feature Tags
 */
$configs['retailer.customer_tags.is_read_only'] = false;

// SF-18743
$configs['rep-export.s3.bucket'] = null;
// default s3 bucket. ie: saks upload to saks bucket so it need to stay on aws
$configs['rep-export.s3.provider'] = 'gcp';

$configs['customer-import.sftp.server'] = null;
$configs['customer-import.sftp.username'] = null;
$configs['customer-import.sftp.password'] = null;

// Customer Activity Feed
$configs['retailer.customer_activity_feed.is_enabled'] = true;
$configs['retailer.customer_activity_feed.max_num_entries'] = 300;

$configs['sf.phone-number-format'] = PhoneNumberFormat::E164;

/**
 * If set to true, the onboarding-backoffice will have a step to load contact with cloudsponge.
 *
 * @show-config public
 */
$configs['retailer.onboarding.step.add_contacts'] = false;

$configs['retailer.onboarding.step.add_categories'] = true;

/**
 * The iOS app id
 *
 * @show-config public
 */
$configs['mobile_ios_appid']       = $configs['env'] === Loader::ENV_STACK_PROD ? 'com.salesfloor.appstore' : 'com.salesfloor.salesfloor';
/**
 * The iOS app distribution channel
 *
 * @show-config public
 */
$configs['mobile_ios_channel']     = $configs['env'] === Loader::ENV_STACK_PROD ? 'official' : 's3';
/**
 * This Anddroid app id
 *
 * @show-config public
 */
$configs['mobile_android_appid']   = $configs['env'] === Loader::ENV_STACK_PROD ? 'com.salesfloor.enterprise' : 'com.salesfloor.salesfloor';
/**
 * This Anddroid app distribution channel
 *
 * @show-config public
 */
$configs['mobile_android_channel'] = 's3';
$configs['mobile_s3_bucket']       = $configs['env'] === Loader::ENV_STACK_PROD ? 'mobile-salesfloor' : 'mobile-app-version';
$configs['mobile_s3_bucket.provider']       = 'aws';

// The region is important since we now generate
// proper link to download the file
//
// via url like this:
//    https://bloom-qa06.salesfloor.net/mobileapp/package.php?key=com.salesfloor.enterprise-Salesfloor-20200921-rel-1.132.0.apk
// Which would return a dowload link such as:
// For AWS:
//    https://{bucket}.s3.{region}.amazonaws.com/com.salesfloor.enterprise-Salesfloor-20200921-rel-1.132.0.apk
//    https://mobile-app-version.s3.us-west-1.amazonaws.com/com.salesfloor.enterprise-Salesfloor-20200921-rel-1.132.0.apk
// For GCP:
//    https://storage.googleapis.com/mobile-app-version/com.salesfloor.enterprise-Salesfloor-20200921-rel-1.132.0.apk
$configs['mobile_s3_bucket.region'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'us-east-1' : 'us-west-1';

$configs['customer-import.sftp.dir'] = null;

// PP-201
// 'legacy' = No policy, assume handled by frontend. Kept by request of product so we can roll out gradually.
// 'owasp' = Uses the example policies from OWASP. Not great, but implemented as an option during discussion.
// 'zxcvbn' = Uses the zxcvbn password entropy validation policy. The best option, and the default.
// Disabled until UX rework.
$configs['security.policy.password'] = 'legacy';
$configs['security.policy.password.zxcvbn.strength'] = 3;

// PP-202 - Combine stores in the US and Canada
// If the customer is geolocated to the US or Canada, display ALL stores from US and Canada.
// Both countries must be mapped, as they might not be bidirectional. Array key should be ALL CAPS
// Example from BTS:
// $configs['retailer.combine_countries'] = [
//    'US' => ['US', 'CA'],
//    'CA' => ['US', 'CA'],
// ];
$configs['retailer.combine_countries'] = false;

/**
 * If $configs['retailer.combine_countries'] set to false, this value is not applicable
 *
 * If $configs['retailer.combine_countries'] set to a valid value:
 *      if this config set to true, will get stores in any distance
 *      if this config set to false, will get stores in allow distance  ( $configs['stores.max_distance'] or per store by db if $config['stores.max_distance.per_store.enable'] )
 */
$configs['retailer.combine_countries_all_distance'] = true;

/* SF-19971 Track discrepancies between MySQL and Elasticsearch */
// DEVOPS-575
$configs['aws.cloudwatch.metrics.namespace'] = 'Salesfloor';

$configs['importer.trxs.purchase_type'] = 'imported';

$configs['importer.transactions.cancels_and_returns.classname'] = null;
$configs['importer.transactions.cancels_and_returns.s3.filename.prefix'] = 'Salesfloor_Returns_Cancels';
$configs['importer.transactions.cancels_and_returns.s3.filename.extension'] = null;
$configs['importer.transactions.cancels_and_returns.s3.filename.header'] = true;
$configs['importer.transactions.cancels_and_returns.match-transaction-only'] = true;
$configs['importer.transactions.cancels_and_returns.s3.sub-folder'] = 'inbound/' . $configs['env'] . '/transactions_cancels_and_returns/';
$configs['importer.transactions.cancels_and_returns.s3.archive-folder'] = '';

$configs['retailer.i18n.products.sync'] = false;

// PP-331 - Broadcast chat to X nearby stores
$configs['chat.broadcast.closest_stores'] = 1;

// SF-20318 - Display different country listing based on context
// Since twilio doesn't accept international SMS, enable only CA/US for SMS/Text request
$configs['retailer.countries.text.available'] = ['CA', 'US'];
$configs['retailer.countries.text.preferred'] = ['CA', 'US'];

// Minimum value of "available" countries, before displaying the preferred section for text since we don't
// want to display countries 2 times (e.g : ca/us | ca/us)
$configs['retailer.countries.preferred.threshold'] = 5;

// General countries accepted in the application (mobile/backoffice)
$configs['retailer.countries.application.preferred'] = ['CA', 'US'];
$configs['retailer.countries.application.available'] = [
    'AU',
    'AT',
    'BE',
    'BR',
    'CA',
    'CL',
    'HR',
    'CZ',
    'DK',
    'EE',
    'FI',
    'FR',
    'GT',
    'DE',
    'HU',
    'HK',
    'IE',
    'IL',
    'IT',
    'LV',
    'LT',
    'MY',
    'MX',
    'NL',
    'NO',
    'PH',
    'PL',
    'PT',
    'PR',
    'SG',
    'ZA',
    'KR',
    'ES',
    'SE',
    'CH',
    'TW',
    'GB',
    'US',
    'JP',
    'IN'
];

// SF-20305 append additional tracking parameters like sf_associd and
// sf_storeid to the redirect url
$configs['retailer.shoppage.tracking.additional_params'] = false;

// SF-20305 should we make the store_id fixed width?
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

/**
 * This configuration contains a set of parameters to add be to the url when the customer is redirected
 * through the Shop Page.
 *
 * There are 4 types of params:
 * - Static
 *   (e.g: ['my_param' => 'my_value'])
 *   Where my_value in this case is a litaral value which is used.
 *
 * - Placeholder
 *   (e.g: ['my_param' => \Salesfloor\Services\ShopPage\ShopPage::RETAILER_DYNAMIC_PARAM_RETAILER_STORE_ID] )
 *   Here the value is a constant which is a placeholder for a dynamic value which will be used.
 *
 * - Mixed
 *   (e.g: ['my_param' => "prefix_static_value_" . "{". \Salesfloor\Services\ShopPage\ShopPage::RETAILER_DYNAMIC_PARAM_RETAILER_STORE_ID . "}"] )
 *   This option gives us the ability to add a prefix before the string which is search replaced.
 *   When adding a prefix (ex. abc_), always put dynamic value in curley braces {} for it to work.
 *
 * - Function
 *   (e.g: ['channelStoreId' => '\Salesfloor\Services\ShopPage\RetailerRule\Belk\CustomUrlParam::base64EncodedRetailerStoreId'] )
 *   This option makes use of a function reference.
 *   This allows us to define a custom function to generate 1 or many params to add to the url.
 *   The configured function must accept ($name, $data, $deps) and will return array of params which will get added to the url.
 *
 * @show-config public
 * @feature ShopPage
 */
$configs['retailer.shoppage.tracking.custom_params'] = [];

/**
 * Url replacement without encoding url fully
 * ex: ["\'" => "%27"]    change url ' to %27
 * @feature ShopPage
 */
$configs['retailer.shoppage.tracking.redirect_url_encode_chars'] = [];


$configs['rollback_on_inconsistent_migrations'] = $configs['env'] != 'prd';

/**
 * If true, sidebar 3 is enabled
 *
 * @show-config public
 */
$configs['retailer.sidebar.v3.enabled'] = false;

/**
 * Choosing a different mode for the Landing Page will also change it on the Service Forms
 *
 * @show-config public
 * @feature SF connect Widget
 */
$configs['retailer.sidebar.v3.mode'] = 'carousel'; // carousel | single | logo

$configs['retailer.sidebar.v3.logopath'] = null; // logo absolute url
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';// left | right
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = false;
$configs['retailer.sidebar.v3.minimize.mobile'] = false;
$configs['retailer.sidebar.v3.minimize.icon_url'] = 'https://res.cloudinary.com/salesfloor-net/image/upload/v1553103646/generic/assets/sidebar/chat-bubble.svg';

$configs['retailer.sidebar.v3.media.desktop.bottom'] = 20; // default for browser link information
$configs['retailer.sidebar.v3.media.desktop.width'] = 230; // if mode is 'single', value will be mobile.width (see get_grunt_config.php)
$configs['retailer.sidebar.v3.media.desktop.height'] = 140; // if mode is 'single', value will be mobile.height (see get_grunt_config.php)
$configs['retailer.sidebar.v3.media.mobile.bottom'] = 0;
$configs['retailer.sidebar.v3.media.mobile.width'] = 275;
$configs['retailer.sidebar.v3.media.mobile.height'] = 64; // Sidebar image is 54px height + 5px + 5px (padding)

$configs['retailer.sidebar.v3.animation.enabled'] = false;
$configs['retailer.sidebar.v3.animation.type'] = 'sidebarSpinner'; // sidebarSpinner | sidebarBlinker
$configs['retailer.sidebar.v3.animation.delay'] = 15; // seconds
$configs['retailer.sidebar.v3.alternateTaglines'] = [];

/**
 * Allows retailer to use Sidebar 3 Variant template
 *
 * When turning this on, you will need to use sidebar3-variant SCSS template
 *
 * Here are the widths and heights to use for the variant:
 * $configs['retailer.sidebar.v3.media.desktop.width'] = 280;
 * $configs['retailer.sidebar.v3.media.desktop.height'] = 60;
 * $configs['retailer.sidebar.v3.media.mobile.width'] = 280;
 * $configs['retailer.sidebar.v3.media.mobile.height'] = 60;
 *
 * @show-config public
 */
$configs['retailer.sidebar.v3.variant'] = false;


/**
 * Allow retailer to decide which side to open the landing from widget
 *
 * @show-config public
 */
$configs['retailer.sidebar.v3.landingPosition.enabled'] = true;

/**
 * Side to open the landing page from Sidebar3
 * Null takes value of $configs['retailer.sidebar.v3.horizontalPosition'] by default, left | right
 *
 * @show-config public
 */
$configs['retailer.sidebar.v3.landingPosition.horizontalPosition'] = null;

// by Default, the popup is on top of browser
$configs['retailer.sidebar.v3.landingPosition.atBottom'] = false;

// === Contextual Widget ===
/**
 * Enabled/Disabled contextual widget events recoding
 * TODO : this config might need to set to false to those retailer who don't have contextual widgets
 * @show-config public
 * @feature SF Connect Widget / Contextual Widget
 */
$configs['retailer.contextual_widget_events_recording.is_enabled'] = true;

$configs['retailer.services.subscription.enabled'] = true;
/**
 * Enable second checkbox from services forms
 * Template example retailer: shiseido
 *
 * @show-config public
 */
$configs['retailer.services.termconditions.enabled'] = false;

$configs['messages.max_displayed'] = 250;

/**
 * Enable the ability to transfer to customer service from chat
 *
 * @show-config public
 * @feature chat-options
 */
$configs['retailer.chat.option.transfer-to-cs'] = false;

/**
 * Require handoff message to provide context to the Customer Service agent
 *
 * @show-config public
 * @feature chat-options
 */
$configs['retailer.chat.option.transfer-to-cs.text.required'] = false;

/**
 * Url to redirect user to for customer service
 *
 * @show-config public
 * @feature chat-options
 */
$configs['retailer.chat.option.transfer-to-cs.redirect.url'] = null;

/**
 * If true, it enables video chat feature for the retailer for Chat
 *
 * Reps can communcicate with customers with online video sessions
 *
 * @show-config public
 */
$configs['retailer.chat.option.video-chat'] = false;

/**
 * If true, it enables 2-ways video chat feature for the retailer for Chat
 *
 * @show-config public
 */
$configs['retailer.chat.option.video-chat.2ways'] = false;

/**
 * If true, it enables video chat feature for the retailer for Virtual Appointment
 *
 * @show-config public
 */
$configs['retailer.virtual.option.video-chat'] = false;

/**
 * If true, it enables 2-ways video chat feature for the retailer for Virtual Appointment
 *
 * @show-config public
 */
$configs['retailer.virtual.option.video-chat.2ways'] = false;

// SF-21042 - Used for generation of the storefront menu (Only lilly for now)
$configs['retailer.storefront.menu.min.categories'] = 1;
$configs['retailer.storefront.menu.max.categories'] = 10;

/**
 * Enable BoldChat as an external Customer Service
 *
 * @show-config public
 * @feature BoldChat
 */
$configs['boldchat.enabled'] = false;
/**
 * BoldChat Service account username
 *
 * @show-config public
 * @feature BoldChat
 */
$configs['boldchat.username'] = 'bold_chat';
/**
 * BoldChat Service account password
 * This must be a hashed and encoded password. Use api/app/bin/generate_encoded_basic_auth_password.php to generate.
 * @show-config public
 * @feature BoldChat
 */
$configs['boldchat.password'] = 'not-valid';

$configs['retailer.api.username'] = $configs['retailer.short_name'];

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['retailer.api.password'] = 'fake';

/**
 * Close hour used with eject feature
 * operating hours - live chat
 *
 * @show-config public
 * @feature eject
 */
$configs['retailer.store.close_hour'] = 22;

/**
 * Open hour used with eject feature
 * operating hours - live chat
 *
 * @show-config public
 * @feature eject
 */
$configs['retailer.store.open_hour'] = 8;

/**
 * Default timezone for stores
 *
 * @show-config public
 */
$configs['retailer.store.default_timezone'] = 'America/Montreal';

/**
 * Retail store open&close hour of local time zone
 * appointments
 * is_available flag will mark all the appointment hours as unavailable
 * until the custom appointment hours are enabled
 */
$configs['retailer.store_appointment_hours'] = [
    [
        'open'  => '9',
        'close' => '22',
        'is_available' => '1', // 0 or 1
    ],
];

/**
 * Retail store hours for texting
 */
$configs['retailer.store_text_hours'] = [
    [
        'open'  => '9',
        'close' => '22'
    ],
];

/**
 * If true, you get ejected from the chat request queue (if not currently in an active chat)
 *
 * SF-21326 - Eject users from chat queue after hours.
 *
 * @show-config public
 * @feature eject
 */
$configs['retailer.chat.option.eject_after_hours'] = false;

/**
 * If false, corporate email is not required and admins don't need to give reps emails at account creation time.
 *
 * This feature is used to make sure we don't send report/internal emails to private emails.
 * SF-21483 - Invites for non-corporate email addresses.
 *
 * @show-config public
 */
$configs['retailer.corporate-email.required'] = true;


// === Features (filename => activate/not config name) ===
$configs['retailer.features'] = [
    "multiple_email_templates" => 'retailer.multiple_email_templates.enabled',
    "shopify_ci_pipeline"      => 'retailer.clienteling.shopify_importer.enabled',
];

/**
 * If true, we can specify which template to use when we share/compose
 *
 * SF-21473 - Main config to enable the feature. Don't set any config related to that here, but in proper config file
 *
 * @show-config public
 */
$configs['retailer.multiple_email_templates.enabled'] = false;

$configs['retailer.importer.customer.validation'] = [];

// === Exporters/Importer ===
/**
 * CONFIG RENAME:
 * sf.gpg.private_key -> crypto.inbound.general.private_key
 * sf.gpg.public_key  -> crypto.outbound.general.public_key
 *
 * As time being, Sometimes the keys are complementary pair, sometimes keys are not for different retailers,ex:
 *
 * Scenario A: The retailer enable PII(&CI Importer), and(or not yet) exporter encryption (ex: shoppers)
 *      sf.gpg.private_key vs. sf.gpg.public_key are NOT complementary paris(unsure it was a mistake or by purpose),
 *      robo encrypt()/decrpt() can't work since default keys:sf.gpg.private_key/sf.gpg.public_key are not complementary
 *      then when test internally in our stg/qa0x env
 *          pii: robo encrypt command have to use private key(before this pr) to do encryption(gpg library support encrypt/decrypt by same private key)
 *          exporter: Retailer send us exporter.encryption.public_key only and we can't decrypt any export files
 * Scenario B: The retailer enable PII(&CI Importer) only (ex: hbc):
 *      Devops put sf.gpg.public_key/private_key as complementary pair
 *      then when test internally in our stg/qa0x env:
 *          pii: robo encrypt use private key(before this pr), but it should use public key
 *
 * Config value is Where private key is stored. Don't use show-config for this config.
 * @feature crypto
 */
$configs['crypto.inbound.general.private_key']  = PATH_PLATFORM . '/services/keys/sf-gpg-private.key';

/**
 * General inbound case public key for all retailer
 *
 * @feature crypto
 */
$configs['crypto.inbound.general.public_key']   = PATH_PLATFORM . '/services/keys/sf-gpg-public.key';

/**
 * General outbound case private key for all retailer
 *
 * @feature crypto
 */
$configs['crypto.outbound.general.private_key'] = PATH_PLATFORM . '/services/keys/sf-gpg-private.key';

/**
 * General outbound case public key for all retailer
 *
 * @feature crypto
 */
$configs['crypto.outbound.general.public_key']  = PATH_PLATFORM . '/services/keys/sf-gpg-public.key';


// SF-21746 - Exact product variants in transactions
$configs['importer.products.has_variants'] = false;
$configs['importer.products.variant_attributes'] = [];

$configs['sf.report-performance-email.path'] = 'report/email';

// If not null, will use a regular expression to identify a product feed on S3.
$configs['importer.products.feed-match-regex'] = null;
$configs['importer.products.file_pattern'] = null;

/**
 * If true, we won't track request that were forwarded to CS.
 *
 * For now, rep/store mode, all requests will be dropped. Only new request will be tracked.
 *
 * @show-config public
 * @feature Sales Tracking
 */
$configs['retailer.stand_down.forwarded_to_cs.is_enabled'] = false;

// SF-22453
$configs['cleanup_operations.devices.days_between_validations'] = 1;
/**
 * If true, enables cleanup of text message phone numbers after X days.
 *
 * If false, numbers are never released.
 *
 * @show-config public
 */
$configs['cleanup_operations.text_message.enabled'] = true;


$configs['exporter.encryption.type'] = 'gpg'; // will affect all exporters

/**
 * Copy the raw file into a salesfloor-private bucket
 * so: Backoffice could download un-encrypted files and the retailer's ftp will only contain the encryption files
 * If true or 'exporter.encryption' is on, our application copy file and upload to cloudStorage's retailer-private bucket
 * If false(default), only file is the default bucket, no file is upload to retailer-private bucket
 * By doing this we could regularly move all files into new bucket, at the end move all /Exports folder to new private bucket
 */
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

/**
 * upload the file into a salesfloor-default(public like) bucket
 * temporary config during public->private migration, after all Exports file move into private bucket, this config/related code could be removed
 */
$configs['exporter.public_bucket_duplicated.is_enabled'] = false;


$configs['exporter.daily_transaction.encryption.enabled'] = false;
$configs['exporter.daily_transaction_details.encryption.enabled'] = false;
$configs['exporter.text.encryption.enabled'] = false;
$configs['exporter.share_email.encryption.enabled'] = false;
$configs['exporter.weekly_summary.encryption.enabled'] = false;
$configs['exporter.request.encryption.enabled'] = false;
$configs['exporter.message.encryption.enabled'] = false;
$configs['exporter.lookbook.encryption.enabled'] = false;
$configs['exporter.live_chat.encryption.enabled'] = false;
$configs['exporter.email_stats.encryption.enabled'] = false;
$configs['exporter.customer.encryption.enabled'] = false;

// === EXPORTER Encryption - ASSOCIATE REPORT ===
/**
 * cron: php ExportAllRepAssociateReportExporter.php {retailer}-dev
 * @feature Exporter Encryption AssociateReport
 * @show-config public
 */
$configs['exporter.associate_report.encryption.is_enabled'] = false;

// === EXPORTER Encryption - USER MANAGEMENT ===
/**
 * cron: php ExportAllRepUserManagementExporter.php {retailer}-dev
 * @feature Exporter Encryption UserManagement
 * @show-config public
 */
$configs['exporter.user_management.encryption.is_enabled'] = false;

/**
 * Exporter of transactions attribution to default bucket, outbound folder, which retailer has ftp acess
 * (feature refer group 'retailer.clienteling.transactions.attribution')
 *
 * @show-config public
 * @feature Exporter CI
 */
$configs['exporter.transactions.attribution.outbound.enabled'] = false;

/**
 * Exporter of transactions attribution incremental files per date range
 * (feature refer group 'retailer.clienteling.transactions.attribution')
 *
 * @show-config public
 * @feature Exporter CI
 */
$configs['exporter.transactions.attribution.daily.enabled'] = false;

/**
 * Exporter of transactions attribution incremental files per date range start -4 days ago
 *
 * @show-config public
 * @feature Exporter CI
 */
$configs['exporter.transactions.attribution.daily.start_days_before'] = -4;

/**
 * Exporter of transactions attribution incremental files per date range end -4 days ago
 *
 * @show-config public
 * @feature Exporter CI
 */
$configs['exporter.transactions.attribution.daily.end_days_before'] = -4;

/**
 * Exporter of transactions attribution encryption setting
 * (feature refer group 'retailer.clienteling.transactions.attribution')
 *
 * @show-config public
 * @feature Exporter CI
 */
$configs['exporter.transactions.attribution.outbound.encryption.enabled'] = false;

/**
 * @feature contacts exporter
 */
$configs['exporter.contact_for_sync.encryption.enabled'] = false;

/**
 * All contact Exporter Encryption(disabled by default)
 * NOTE: all contact Exporter is used for backoffice download(except hbc/bloom), so it is can't be encrypted
 * For hbc/bloom, the same exporter need run one more time with encryption, and put it to default bucket
 * @feature contacts exporter
 */
$configs['exporter.contact.encryption.enabled'] = false;

/**
 * Encryption config for reporter of activitySummary,
 * In backoffice report panel, a similar reporter without encryption could be downloaded
 */
$configs['exporter.rep_activity_summary.encryption.enabled'] = false;
$configs['exporter.store_activity_summary.encryption.enabled'] = false;

/**
 * Encryption config for reporter of liveChatMetric
 * In backoffice report panel, a similar reporter without encryption could be downloaded
 */
$configs['exporter.rep_live_chat_metrics.encryption.enabled'] = false;
$configs['exporter.store_live_chat_metrics.encryption.enabled'] = false;

$configs['exporter.legacy.sms.daily'] = false;





/**
 * Enable export all requests daily
 *
 * @show-config public
 * @feature all requests exporter
 */
$configs['exporter.request.daily.enabled'] = false;

/**
 * Export all requests daily - start days back
 * default value is '-1
 *
 * @show-config public
 * @feature all requests exporter
 */
$configs['exporter.request.daily.start_days_before'] = -1;

/**
 * Export all requests daily - end days back
 * default value is '-1
 *
 * @show-config public
 * @feature all requests exporter
 */
$configs['exporter.request.daily.end_days_before'] = -1;

/**
 * Exporter for download zip file Backoffice
 * For some retailers,  csv need to split per specific date
 * \Salesfloor\Services\Exporter\ExportType\ZipExportType::DAILY|WEEKLY|MONTHLY|YEARLY|'' could be used
 * Default empty value will split by year or all in one file depend on the specific exporter
 * @show-config public
 * @feature all requests exporter
 */$configs['exporter.request.zip_split_by'] = '';


/**
 * Enable export sms daily
 *
 * @show-config public
 * @feature sms exporter
 */
$configs['exporter.sms.daily.enabled'] = false;

/**
 * Export sms daily - start days back
 * default value is '-1
 *
 * @show-config public
 * @feature sms exporter
 */
$configs['exporter.sms.daily.start_days_before'] = -1;

/**
 * Export sms daily - end days back
 * default value is '-1
 *
 * @show-config public
 * @feature sms exporter
 */
$configs['exporter.sms.daily.end_days_before'] = -1;

/**
 * Range of file split in zip file when download All sms zip from backoffice
 * Default empty value will split by year or all in one file depend on the specific exporter
 * @show-config public
 * @feature sms exporter
 */
$configs['exporter.sms.all.zip_split_by'] = '';


/**
 * Enable live chat daily export
 *
 * @show-config public
 * @feature live chat exporter
 */
$configs['exporter.livechat.daily.enabled'] = false;
/**
 * Export live chat daily - start days back
 * default value is '-1
 *
 * @show-config public
 * @feature live chat exporter
 */
$configs['exporter.livechat.daily.start_days_before'] = -1;
/**
 * Export live chat daily - end days back
 * default value is '-1
 *
 * @show-config public
 * @feature live chat exporter
 */
$configs['exporter.livechat.daily.end_days_before'] = -1;

/**
 * range of file split in zip file when download All Live Chat Logs zip from backoffice
 * Default empty value will split by year or all in one file depend on the specific exporter
 * @show-config public
 * @feature live chat exporter
 */
$configs['exporter.live_chat.all.zip_split_by'] = '';


/**
 * Enable export rep live chat metrics daily
 *
 * @show-config public
 * @feature rep live chat mertics exporter
 */
$configs['exporter.live_chat_metrics.rep.daily.enabled'] = false;
/**
 * Rep live chat metrics daily - start days back
 * default value is '-1'
 *
 * @show-config public
 * @feature rep live chat mertics exporter
 */
$configs['exporter.live_chat_metrics.rep.daily.start_days_before'] = -1;
/**
 * Rep Live chat metrics daily - end days back
 * default value is '-1'
 *
 * @show-config public
 * @feature rep live chat mertics exporter
 */
$configs['exporter.live_chat_metrics.rep.daily.end_days_before'] = -1;




/**
 * Enable export store live chat metrics daily
 *
 * @show-config public
 * @feature store live chat mertics exporter
 */
$configs['exporter.live_chat_metrics.store.daily.enabled'] = false;
/**
 * Store live chat metrics daily - start days back
 * default value is '-1'
 *
 * @show-config public
 * @feature store live chat mertics exporter
 */
$configs['exporter.live_chat_metrics.store.daily.start_days_before'] = -1;
/**
 * Store Live chat metrics daily - end days back
 * default value is '-1'
 *
 * @show-config public
 * @feature store live chat mertics exporter
 */
$configs['exporter.live_chat_metrics.store.daily.end_days_before'] = -1;




/**
 * Export transactions daily for the previous date range.
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.daily.enabled'] = false;

/**
 * Export transactions daily for the previous date range,
 * Start with this days before, it is a negative value, default value is '-1',
 * will export transaction happen from date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.daily.start_days_before'] = -1;

/**
 * Export transactions daily for the previous date range,
 * Start with this days before, it is a negative value, default value is '-1',
 * will export transaction happen until the date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.daily.end_days_before'] = -1;


/**
 * Export transactions weekly for the previous week as a date range.
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.weekly.enabled'] = false;

/**
 * Export transactions weekly for the previous weekly date range,
 * A start date at last week, it is a negative value, calculating with config:cron_date_of_week
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.weekly.start_days_before'] = -8;

/**
 * Export transactions weekly for the previous weekly date range ,
 * An end date at last week, it is a negative value, calculating with config:cron_date_of_week
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.weekly.end_days_before'] = -2;

/**
 * In which day of the week to export the weekly transactions.
 * ex: 1 => at Monday, weekly export will be invoked, and date range is from last Sunday(-8) to last Saturday(-2)
 *
 * @show-config public
 * @feature transactions exporter
 */
$configs['exporter.transactions.weekly.cron_date_of_week'] = 1;


/**
 * Export activity summary daily for the previous date range.
 *
 * @show-config public
 * @feature activitySummary exporter
 */
$configs['exporter.activitySummary.rep.daily.enabled'] = false;

/**
 * Export activity summary daily for the previous date range,
 * Start with this days before, it is a negative value, default value is '-1',
 * will export ctivity summary happen from date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature activitySummary exporter
 */
$configs['exporter.activitySummary.rep.daily.start_days_before'] = -1;

/**
 * Export activity summary daily for the previous date range,
 * Start with this days before, it is a negative value, default value is '-1',
 * will export activity summary happen until the date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature activitySummary exporter
 */
$configs['exporter.activitySummary.rep.daily.end_days_before'] = -1;






/**
 * Export store activity summary daily for the previous date range.
 *
 * @show-config public
 * @feature storeActivitySummary exporter
 */
$configs['exporter.activitySummary.store.daily.enabled'] = false;

/**
 * Export store activity summary daily for the previous date range,
 * Start with these days before, it is a negative value, default value is '-1',
 * will export activity summary happen from date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature storeActivitySummary exporter
 */
$configs['exporter.activitySummary.store.daily.start_days_before'] = -1;

/**
 * Export store activity summary daily for the previous date range,
 * Start with these days before, it is a negative value, default value is '-1',
 * will export activity summary happen until the date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature storeActivitySummary exporter
 */
$configs['exporter.activitySummary.store.daily.end_days_before'] = -1;


/**
 * Export sidebar metric daily for the previous date range.
 *
 * @show-config public
 * @feature sidebarMetric exporter
 */
$configs['exporter.sidebar_metric.daily.enabled'] = false;

/**
 * Export sidebar metric daily for the previous date range,
 * Start with this date before, it is a negative value, default value is '-1',
 * will export sidebar metric happen from date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature sidebarMetric exporter
 */
$configs['exporter.sidebar_metric.daily.start_days_before'] = -1;

/**
 * Export sidebar metric daily for the previous date range,
 * End within this date, it is a negative value, default value is '-1',
 * will export sidebar metric happen until the date('Y-m-d', strtotime('-1 days'))
 *
 * @show-config public
 * @feature sidebarMetric exporter
 */
$configs['exporter.sidebar_metric.daily.end_days_before'] = -1;

/**
 * Exporter for download zip file Backoffice
 * For some retailers,  contacts.csv need to split per customer creation date
 * \Salesfloor\Services\Exporter\ExportType\ZipExportType::DAILY|WEEKLY|MONTHLY|YEARLY|'' could be used
 * Default empty value will split by year or all in one file depend on the specific exporter
 * @show-config public
 * @feature contact exporter
 */
$configs['exporter.contacts.all.zip_split_by'] = '';

/**
 * If export all contacts by sql query with pagination
 * For some retailers(shoppers),  contacts.csv is too huge and server could not handle it in one query and abort full process
 * Default false value will export all contacts in one query
 * TODO: if pagination logic is feasible, we could remove no-pagination logic and also remove this config since default behavior is by pagination
 * @feature contact exporter
 */
$configs['exporter.contacts.all.query_pagination'] = false;

/**
 * Export contacts created/updated/deleted on the previous day.
 *
 * @show-config public
 * @feature contact exporter
 */
$configs['exporter.contacts.daily.enabled'] = false;

/**
 * Export All contacts to cloudstorage(default is outound folder)
 * NOTE: On cloud storage, we always upload to /Exports folder
 *       This config is also send to outound folder so in the future we could move /Exports bucket easily)
 * @show-config public
 * @feature contact exporter
 */
$configs['exporter.all_contacts_outbound.daily.enabled'] = false;

/**
 * Export All contacts to cloudstorage, this config could still use to swith folder (default is outound folder)
 * NOTE: this config is only for hbc, and we will try to still send file to outbound folder and remove this config in the future
 * @feature contact exporter
 */
$configs['exporter.all_contacts_outbound.daily.path'] = 'outbound';

/**
 * Export messages from the previous day.
 *
 * @show-config public
 * @feature message exporter
 */
$configs['exporter.messages.daily.enabled'] = false;

/**
 * Export share email from the previous day.
 *
 * @show-config public
 * @feature shareEmail exporter
 */
$configs['exporter.share.email.daily.enabled'] = false;

/**
 * Export lookbook daily stats on the previous day.
 *
 * @show-config public
 * @feature lookbook exporter
 */
$configs['exporter.lookbook.daily.enabled'] = false;

/**
 * Export tasks daily stats on the previous day.
 *
 * @show-config public
 * @feature task exporter
 */
$configs['exporter.task.daily.enabled'] = false;

/**
 * Export tasks daily for the previous date range,
 * Start with this two days before, it is a negative value, default value is '-2',
 * will export tasks happen from date('Y-m-d', strtotime('-2 days'))
 *
 * @show-config public
 * @feature task exporter
 */
$configs['exporter.task.daily.start_days_before'] = -2;

/**
 * Export tasks daily for the previous date range,
 * Start with this two days before, it is a negative value, default value is '-2',
 * will export tasks happen until the date('Y-m-d', strtotime('-2 days'))
 *
 * @show-config public
 * @feature task exporter
 */
$configs['exporter.task.daily.end_days_before'] = -2;

/**
 * Export retailer customer daily data created at given data range
 *
 * @show-config public
 * @feature retailer customer exporter
 */
$configs['exporter.retailer_customer.daily.enabled'] = false;

/**
 * Customize Report is enabled/disabled
 * Ex: for Bloom/Macys required ROPO(researched online purchased offline or O2O)
 *
 * @show-config public
 * @feature DynamicExporter
 */
$configs['exporter.dynamic_exporter.daily.is_enabled'] = false;


/**
 * Which types of exporter are used for the retailer
 * Ex:
 *     ['predefined/ropo.interaction'] are generating exporter using meta in templates/predefined/ropo.interaction
 *     ['predefined/ropo.interaction', 'predefined/ropo.text','retailer/{retailer}/other_key']
 *         are generating exporters using meta in:
 *         templates/predefined/ropo.interaction/, templates/predefined/ropo.text/ and templates/retailer/{retailer}/other_key/
 *
 * @feature DynamicExporter
 */
$configs['exporter.dynamic_exporter.exporters'] = [];

// SF-22859
/**
 * If true, enables the retailer rep management feed.
 *
 * If this is true, the onboarding UI "activate" button is available when an invite is created via feed.
 *
 * SF-22859
 *
 * @show-config public
 * @feature rep importer
 */
$configs['importer.reps.enabled'] = false;
/**
 * Path where the retailer rep feed file is dropped
 *
 * SF-22859
 *
 * @show-config public
 * @feature rep importer
 */
$configs['importer.reps.s3.path'] = 'inbound/{env}/users/';
/**
 * Regular expression to find the retailer rep feed in the s3 path.
 *
 * SF-22859
 *
 * @show-config public
 * @feature rep importer
 */
$configs['importer.reps.s3.filename_regexp'] = '{retailer}-users-\d{8}-\d{6}\.csv';

/**
 * Is the user feed being imported encrypted?
 */
$configs['sf.import_users.encrypted'] = false;

/**
 * If true, enables prefixing numeric usernames with the retailer short name, joined with _
 *
 * @show-config public
 * @feature rep importer
 */
$configs['retailer.prefix_numeric_usernames'] = false;

$configs['importer.shutdown_function.enabled'] = true;

/**
 * The index of elasticsearch contains nested documents, there's a limit on the number of them.
 * If the number of nested documents exceeds this limit, the following error returned:
 *
 *  The number of nested documents has exceeded the allowed limit of [50000].
 *  This limit can be set by changing the [index.mapping.nested_objects.limit] index level setting.
 *
 * @show-config public
 * @feature ES
 */
$configs['elasticsearch.index.mapping.nested_objects.limit'] = 50000;

/**
 * When reindexing elasticsearch, how many records to send in a single bulk call.
 * The number was increased because now, they are done in parallel in chunk of 1000 records (like before)
 *
 * @show-config public
 * @feature ES
 */
$configs['elasticsearch.max_bulk_size'] = 20000;

/**
 * When reindexing elasticsearch, we only add transactions which xx-month-old or less.
 * The number is the max monthes we are using to filter transactions.
 *
 * @show-config public
 * @feature ES
 */
$configs['elasticsearch.index.transactions.threshold_month'] = 18;

/**
 * If true, shop feed (instagram) is enabled
 *
 * This feature provides store associates a way to share shoppable social posts with their customers via their SocialShop feed.
 * Associates can link a great photo or asset with direct product links that point to the ecomm site.
 *
 * @show-config public
 * @feature Socialshop
 */
$configs['retailer.shop_feed.enabled'] = false;

/**
 * If true, json-api is used in messaging stack (wp+api)
 *
 * @show-config public
 */
$configs['messaging-json-api.enabled'] = false;


/**
 * This is temporary to fix/investigate SF-23468
 */
$configs['retailer.transaction.use_mail_relay_queue'] = true;

/////////////
// PHP7 Compatibility configs
$configs['php7.disable_warn_method_declaration'] = true;
$configs['php7.disable_countable_warning'] = true;
/////////////

// Defines generic fetcher/ingestors for S3Importer.
$configs['importer.fetcher.s3'] = 'importer.fetcher.s3';
$configs['importer.ingestor.csv'] = 'importer.ingestor.csv';

/**
 * If true, expanded product variants will be used to generate swatches and allow sharing by sku
 *
 * This is gated behind a config as enabling it requires updating the product feed to absorb
 * expanded variant data. This requires using the new base product feed.
 *
 * @show-config public
 */
$configs['products.expanded_variants.enabled'] = false;

/**
 * For most of product variants, sf_product_variant_attributes have color(position:1) and size(position:2),
 * but some product doesn't need these attributes
 *
 * If a product doesn't color attribute but have size attribute, for some retailer, we need this config to skip position in sf_product_variant_attributes table
 * so the feature of priority_variant_count(SF-25388) is still available
 *
 * If set to true, then a product is allow to skip color attribute but have size attribute as position:2 in sf_product_variant_attributes
 *
 * @show-config public
 * @feature product catalog
 */
$configs['products.expanded_variants.empty_attribute_position_holder'] = false;

/**
 * Set to true to enable the new importer even if we don't have variant enabled
 *
 * @show-config public
 * @feature product catalog
 */
$configs['importer.product.use_new_importer'] = false;

/**
 * Regular expression to find the retailer product feed in the s3 path.
 *
 * SF-23491
 *
 * @show-config public
 * @feature product catalog
 */
$configs['importer.product.s3.filename_regexp'] = '{retailer}-products-\d{8}-\d{6}.csv';

/**
 * Path where the retailer rep feed file is dropped
 *
 * SF-23491
 *
 * @show-config public
 * @feature product catalog
 */
$configs['importer.product.s3.path'] = 'inbound/{env}/products/';

$configs['importer.product.fetcher'] = 'importer.fetcher.s3.product';
$configs['importer.product.ingestor'] = 'importer.ingestor.csv.products';

// log file even when on dev
$configs['logrotate.is_enabled'] = false;

// For use with 'importer.fetcher.api.product.default'
$configs['importer.product.fetcher.api.is_paginated'] = true;
$configs['importer.product.fetcher.api.per_page'] = 10;
$configs['importer.product.fetcher.api.start_page'] = 1;
$configs['importer.product.fetcher.api.url'] = '';
$configs['importer.product.fetcher.api.credentials'] = null;

/**
 * Specify the type of fetcher for the product feed. It's applicable when we need to use a direct file downloader fetcher.
 *
 * TD-164
 *
 * @show-config public
 * @feature product catalog
 */
$configs['importer.product.fetcher.direct.type'] = null;

// Amended product import column validations which are merged into and override the default column validation.
// services/src/Importer/Product/Importer.php:169
$configs['importer.product.validation.amend'] = [];

$configs['importer.product.validation'] = [
    ProductImporter::COL_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_PARENT_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_GTIN => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 255]),
    ],
    ProductImporter::COL_TITLE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 250]),
    ],
    ProductImporter::COL_DESCRIPTION => [
        new NotBlank(),
        new Required()
    ],
    ProductImporter::COL_BRAND => [
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_LINK => [
        new NotBlank(),
        new Url(),
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_IMAGE_LINK => [
        new NotBlank(),
        new Url(),
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_AVAILABLE => [
        new NotBlank(),
        new Required(),
        new Choice(['choices' => ['0', '1', 0, 1]]),
    ],
    ProductImporter::COL_PRICE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 11]),
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^[0-9]+(\.\d+)?$/']),
    ],
    ProductImporter::COL_SALE_PRICE => [
        new Length(['max' => 11]),
        // The additional part of the regex is to pass validation if no sale price is provided.
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^[0-9]+(\.\d+)?$|^$/']),
    ],
    ProductImporter::COL_SALE_START_DATE => [
        new Date(),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_SALE_END_DATE => [
        new Date(),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL1_ID => [
        new Length(['max' => 30]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL1_TITLE => [
        new Length(['max' => 50]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_ID => [
        new Length(['max' => 30]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_TITLE => [
        new Length(['max' => 50]),
    ],
    ProductImporter::COL_ARRIVAL_DATE => [
        new Regex(['pattern' => '/^(?!0000-00-00)\d{4}-\d{2}-\d{2}( \d\d?:\d\d?:\d\d?)?$/']),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_LANGUAGE_CODE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 10]),
    ],
    ProductImporter::COL_ATTR_1_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_1_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_1_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_1_SWATCH => [
        new Length(['max' => 500]),
        new Url(),
    ],
    ProductImporter::COL_ATTR_2_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_2_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_2_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_2_SWATCH => [
        new Length(['max' => 500]),
        new Url(),
    ],
    ProductImporter::COL_ATTR_3_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_3_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_3_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_3_SWATCH => [
        new Length(['max' => 500]),
        new Url(),
    ],
    ProductImporter::COL_IS_DEFAULT => [
        new NotBlank(),
        new Required(),
        new Choice(['choices' => ['0', '1', 0 , 1]]),
    ],
];

// Path to list of data from root of response
$configs['importer.product.ingestor.api.nested_data_path']  = ['data'];

$configs['importer.transaction.validation'] = [
    TransactionImporter::COL_PARENT_ID => [
        new Length(['max' => 45]),
    ],
    // Move Transaction id required check to validateAndClean()
    // and it could be empty when retailer supports parent id for transactions reconciliation
    TransactionImporter::COL_TRX_ID => [
        new Length(['max' => 45]),
    ],
    TransactionImporter::COL_TRX_TYPE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 255]),
        new Choice(['choices' => ['sale', 'cancel', 'return', 'delete', 'reassign']]),
    ],
    TransactionImporter::COL_TRX_DATE => [
        new NotBlank(),
        new Required(),
        // new \Symfony\Component\Validator\Constraints\DateTime(),
        new Regex(['pattern' => '/^\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2}([+-]\d{2}:\d{2})?$/']),
        new Length(['max' => 25]),
    ],
    TransactionImporter::COL_EMPLOYEE_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    TransactionImporter::COL_STORE_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 45]),
    ],
    TransactionImporter::COL_CUSTOMER_EMAIL => [
        new Length(['max' => 128]),
    ],
    TransactionImporter::COL_CUSTOMER_NAME => [
        new Length(['max' => 128]),
    ],
    TransactionImporter::COL_CURRENCY => [
        new Length(['max' => 3]),
    ],
    TransactionImporter::COL_TRANSACTION_TOTAL => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 12]),
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^[0-9]+(\.\d+)?$/']),
    ],
    TransactionImporter::COL_PRODUCT_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    TransactionImporter::COL_UNIT_PRICE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 12]),
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^[0-9]+(\.\d+)?$/']),
    ],
    TransactionImporter::COL_UNITS => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 12]),
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^\d+$/']),
    ],
];

$configs['devops-1420.kludge'] = false;

/**
 * Number of seconds until we resend a new "OOO" (autorespond) email to the customer
 *
 * @show-config public
 */
$configs['autoresponder.ttl'] = 3600;


/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['algolia.admin.app_id'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['algolia.admin.api_key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['algolia.read.app_id'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['algolia.read.api_key'] = 'fake';

/**
 * In prod, only secret/key are set and appended in calculated.php.
 * In dev, this is overridden by dev secrets
 *
 * @secret
 */
$configs['sf-api'] = [
    'apikey'  => [
        'key'      => 'fake',
        'header'   => 'SF-APIKEY',
    ],
    'jwt'     => [
        'secret' => 'fake',
        'exp'    => 600,
        'ref'    => 86400 * 7 * 4 * 2,
        'header' => 'Authorization',
        'prefix' => 'Bearer',
    ],
    'cookie'  => [
        'name' => 'SF-TOKEN',
    ],
    'enabled' => true,
];


// ------------------- salesfloor_admin -------------------

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sfadmin.user_login'] = 'salesfloor_admin';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['sfadmin.password'] = 'fake';

/**
 * If enabled, sidebar event like view, display, minimize, maximize will be tracked
 * @show-config public
 */
$configs['sidebar.tracking.enabled'] = true;

/**
 * Path where CRM Contact files are uploaded
 *
 * SF-24523
 *
 * @show-config public
 * @feature importer contact
 */
$configs['importer.c2c.s3.path'] = 'inbound/{env}/c2c/';
// crm-import-2019-05-09-5cd4461e062a2-0001.csv
$configs['importer.c2c.s3.filename_regexp'] = 'c2c-import-parent-\d+-\d{4}-\d{2}-\d{2}-.+-\d{4}.csv';

/**
 * If true, logs rejected emails to the sf_rejected_emails table
 * @show-config public
 */
$configs['unknown_incoming_email.tracking_and_reject.is_enabled'] = true;

/**
 * If true, notifications are sent for messages that haven't been answered in more than 2 hours and less than 72 hours
 * @show-config public
 */
$configs['notifications.late_messages.enabled'] = true;

/**
 * If true, notifications are sent for requests that haven't been answered in more than 2 hours and less than 72 hours
 * @show-config public
 */
$configs['notifications.late_requests.enabled'] = true;

/**
 * If true, notifications are sent for appointments that are happening soon
 * @show-config public
 */
$configs['notifications.near_appointments.enabled'] = true;

/**
 * Interval(in second) of bulk insert. Sometimes we want the bulk insert to be faster instead of
 * having to reach to the bulk size, then push to the db. We use this variable to push the bulk of
 * events every 600 seconds. If it takes more than 600 seconds to accumulate bulk size of events, than
 * the bulk of events will be pushed to db right away.
 * will be used in service SidebarEventQueue.php
 *
 * Warning: GCP will extend ack messages at half, or Do not make this over 530sec due to GCP Max message Ack
 *          see services/src/Queue/SidebarEventQueue.php#reachedHalfPushingInterval
 *
 * FOR GCP: Queue elements are kept for only 600 seconds unless services.queue.ack-extend
 * services.queue.ack-extend would re-extend the message gathered from 50% of sidebar.pushing.interval
 * @show-config public
 */
$configs['sidebar.pushing.interval'] = 500;

// ------------------- GCP Queue  --------------------

/**
 *
 * GCP Pub/Sub Subscription AcknowledgeDeadline
 *
 * acknowledgeDeadline: How long Pub/Sub waits for the subscriber to acknowledge receipt before resending the message.
 *
 * If GCP Pub/Sub thinks it has sent the message to us it will mark it as being 'outstanding', and then retry only in
 * X seconds. Though if we set this value to 10 minutes, some message might takes 10 or even 20+ minute before we might
 * receive it.
 *
 * There is some cases where we did not receive the message. GCP seems to have Implemented a fire and forget mechanism
 * and does not wait to mark it as 'outstanding/in process', which make sense if they want to have high throughput queue system.
 *
 * Minimum of 10 sec and maximum of 600 seconds (10 minutes, but not recommanded).
 *
 * We should keep this value below 30 seconds ideally, to ensure we have message resent by GCP to our queue every 30 seconds
 * unless we have sent to GCP an changeMessageVisibility, to extend it.
 *
 * This only gets modified if gcp.queue.management_enabled is true
 *
 * @show-config public
 */
$configs['gcp.queue.queue_ack_deadline_seconds'] = 30;

/**
 * GCP Ack deadline before GCP return element to Queue for Retry.
 * Minimum of 10 sec and maximum of 600 seconds (10 minutes).
 * This need to be > than sidebar.pushing.interval else it will have double entries in DB
 * Can be extended by using the services.queue.ack-extend parameter
 *
 * @show-config public
 */
$configs['gcp.queue.ack_deadline_seconds'] = 600;

/**
 * Extend the bulk collected elements. Prevent return to queue before we processed the elements.
 * Mostly for GCP. If sidebar processing takes time to process the meesages
 * It will extend the gathered messages by batches when reaching 50% of the sidebar.pushing.interval
 *
 * @show-config public
 */
$configs['services.queue.ack-extend'] = true;

/**
 * size of bulk insert sidebar events to sf_sidebar_event_log(_yyyymmdd) table
 * will be used in service SidebarEventQueue.php
 *
 * @show-config public
 */
$configs['sidebar.pushing.bulk.size'] = 900;

/**
 * time(in seconds) that message will be hidden in the queue during the bulk insert. It's long enough to
 * guarantee the message won't be back to the queue before finishing the bulk process.
 * will be used in service SidebarEventQueue.php
 *
 * WARNING: This only works in AWS. For GCP We implemented a modifyAckDeadline
 *          at half the DB Insertion time, so messages will not be returned to queue
 *
 * @show-config public
 */
$configs['sidebar.message.hide'] = 900;

/**
 * The delimiter used to separate tags in a contact importer csv file.
 *
 * @show-config public
 */
$configs['importer.contacts.tag_delimiter'] = [','];

/**
 * If enabled, the vendor static assets will have an build hash instead of
 * usin the cacheBustingKey. ie: vendor.min.{hash}.js
 *
 * @show-config public
 */
$configs['assets.vendor_hash'] = true;

/**
 * List of emails to notify on a failed import
 *
 * @show-config public
 */
$configs['alerts.failed_import.emails'] = $configs['env'] === Loader::ENV_STACK_PROD ? [
    '<EMAIL>'
] : ($configs['env'] == 'dev' ? ['<EMAIL>'] : ['<EMAIL>']);

/**
 * List of emails to notify on changed salesfloor_admin
 *
 * @show-config public
 */
$configs['alerts.salesfloor_admin_changed.emails'] = $configs['env'] === Loader::ENV_STACK_PROD ? ['<EMAIL>'] : ['<EMAIL>'];

/**
 * The email address that user ID 1 should have
 */
$configs['alerts.salesfloor_admin_changed.expected_email'] = '<EMAIL>';

/**
 * To enable the inventory lookup feature based on retailers

 * @show-config public
 * @feature Inventory Lookup
 */
$configs['inventory.lookup.is_enabled'] = false;
$configs['inventory.lookup.retailer.endpoint'] = '';

/**
 * Inventory lookup retailer default location types:
 *
 * 1 - User's Current Store
 * 2 - Online Web Store
 * 3 - Warehouse Store
 *
 * @show-config public
 * @feature Inventory Lookup
 *
 */
$configs['inventory.lookup.retailer_default_location.type'] = 1;

/**
 * Inventory lookup retailer default location store id (in string format)
 * and only needs to be set when the value of config 'inventory.lookup.retailer.default.location.type' is 3(Warehouse Store):
 *
 * @show-config public
 * @feature Inventory Lookup
 */
$configs['inventory.lookup.retailer_default_location.id'] = '';

// Empty values on prod so we don't get Salesfloor address by mystake for a real retailer
$configs['retailer.hq_address.street'] = $configs['env'] === Loader::ENV_STACK_PROD ? '' : "651 Notre Dame Ouest, Suite 350";
$configs['retailer.hq_address.city'] = $configs['env'] === Loader::ENV_STACK_PROD ? '' : "Montreal";
$configs['retailer.hq_address.region'] = $configs['env'] === Loader::ENV_STACK_PROD ? '' : "QC";
$configs['retailer.hq_address.isoCountry'] = $configs['env'] === Loader::ENV_STACK_PROD ? '' : "CA";
$configs['retailer.hq_address.postalCode'] = $configs['env'] === Loader::ENV_STACK_PROD ? '' : "H3C 1H9";
$configs['retailer.hq_address.customerName'] = $configs['env'] === Loader::ENV_STACK_PROD ? '' : "Salesfloor";

/**
 * If true, the tracking are refreshed (expiration) on every load
 * @show-config public
 * @feature Sales Tracking
 */
$configs['retailer.sale_cookie.refreshed'] = true;


// BOF feature: products.expanded_variants.priority_badge
/**
 * If enabled, will only display the "has variants" badge if there's more than one value for attribute #no(position)
 * @note if the feature is on, all variants must have same attribute position order or toggle config['products.expanded_variants.empty_attribute_position_holder'] on
 *
 * @show-config public
 */
$configs['products.expanded_variants.priority_badge.enabled'] = false;

/**
 * Only applicable if above config:'products.expanded_variants.priority_badge.enabled' set to true,
 * Use default position:1, to calculate attribute distinct count of this position per product, by retrieving data from sf_product_variant_attributes table
 */
$configs['products.expanded_variants.priority_badge.position'] = 1;
// EOF feature: products.expanded_variants.priority_badge

/**
 * A config for sort of rep when 'find a rep' in widget
 * If value is true, rep is sort by name alphabetically without considering same one store or not
 * If this value set to false, reps will sort by store' distance and shuffle rep in each store
 *
 * @show-config public
 */
$configs['reps.find_reps.sort.name_alphabetically'] = true;

/**
 * If enabled, Retailer rep transactions will be imported
 *
 * @show-config public
 */
$configs['importer.rep_transactions.enabled'] = false;

/**
 * Retailer like Chicos has performance issue because of data volume.
 * Skip the CalculateDailyStats so rep_transactions could still be imported
 * NOTE: it is a temporary config, should be removed if performance issue on stats could be solved
 */
$configs['importer.rep_transactions.skip_stats'] = false;

/**
 * S3 path of customer-extend-attributes
 *
 * @show-config public
 * @feature importer customer-extend-attributes
 */
$configs['importer.customer_attributes.s3.path'] = 'inbound/{env}/customer-extend-attributes';

/**
 * S3 file regex of customer-extend-attributes
 *
 * @show-config public
 * @feature importer customer-extend-attributes
 */
$configs['importer.customer_attributes.s3.filename_regexp'] = 'customer-extend-attributes-\d{8}-\d{6}.csv';

/**
 * This config enables assigned stores for managers.
 * Where they can only filter for stores they are assigned.
 *
 * @show-config public
 * @feature user-stores
 */
$configs['user-stores.user_assigned_stores.is_enabled'] = false;

/**
 * S3 path of user-stores
 *
 * @show-config public
 * @feature importer user-stores
 */
$configs['importer.user_stores.s3.path'] = 'inbound/{env}/userstores';

/**
 * S3 file regex of user-stores
 *
 * @show-config public
 * @feature importer user-stores
 */
$configs['importer.user_stores.s3.filename_regexp'] = '{retailer}-userstores-\d{8}-\d{6}.csv';

/**
 * pre-defined attributes validation rule set
 *
 * @show-config public
 * @feature importer customer-extend-attributes
 */
$configs['importer.attributes.validation'] = [
    CustomerAttributeModel::VALIDATION_RULE_NONE             => [],
    CustomerAttributeModel::VALIDATION_ALPHANUMERIC          => [
        new Symfony\Component\Validator\Constraints\Regex([
            'pattern' => '/^[\w\-\s]+$/'
        ]),
    ],
    CustomerAttributeModel::VALIDATION_ALPHANUMERIC_NO_SPACE => [
        new Symfony\Component\Validator\Constraints\Regex([
            'pattern' => '/^[a-zA-Z0-9\-_]+$/'
        ]),
    ],
    CustomerAttributeModel::VALIDATION_NUMERIC_ONLY          => [
        new Symfony\Component\Validator\Constraints\Regex([
            'pattern' => '/^[0-9]+$/'
        ]),
    ],
    // note: '12345' is invalid, 12345 is valid for this rule
    CustomerAttributeModel::VALIDATION_INT                   => [
        new Symfony\Component\Validator\Constraints\NotBlank(),
        new Symfony\Component\Validator\Constraints\Type(
            [
                'type' => 'int'
            ]
        )
    ],
    CustomerAttributeModel::VALIDATION_DECIMAL               => [
        new Symfony\Component\Validator\Constraints\NotBlank(),
        new Symfony\Component\Validator\Constraints\Regex(
            [
                'pattern' => '/^-?\d*\.?\d*$/'
            ]
        ),
    ],
    CustomerAttributeModel::VALIDATION_BOOLEAN               => [
        new Symfony\Component\Validator\Constraints\NotBlank(),
        new Symfony\Component\Validator\Constraints\Regex(
            [
                'pattern' => '/^[01]{1}$/'
            ]
        ),
    ],
    CustomerAttributeModel::VALIDATION_ALPHA_ONLY            => [
        new Symfony\Component\Validator\Constraints\Regex([
            'pattern' => '/^[a-zA-Z]+$/'
        ]),
    ],
    CustomerAttributeModel::VALIDATION_DATE                  => [
        new Symfony\Component\Validator\Constraints\Date()
    ],
    CustomerAttributeModel::VALIDATION_TIME                  => [
        //string A "H:i:s" formatted value
        new Symfony\Component\Validator\Constraints\Time()
    ],
    CustomerAttributeModel::VALIDATION_DATETIME              => [
        new Symfony\Component\Validator\Constraints\DateTime()
    ],
    CustomerAttributeModel::VALIDATION_URL                   => [
        // note: front-end use regex to validate url, which has difference in some edge cases, let's keep simple at Back-end by now
        // js regex: '/(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#/%=~_|$?!:,.]*\)|[A-Z0-9+&@#/%=~_|$])/igm'
        new Symfony\Component\Validator\Constraints\Url(),
    ],
    CustomerAttributeModel::VALIDATION_EMAIL                 => [
        // note: front-end use regex to validate email, which has difference in some edge cases, let's keep simple at Back-end by now
        // js regex: '/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'
        new Symfony\Component\Validator\Constraints\Email(),
    ],
    CustomerAttributeModel::VALIDATION_EXTEND                => [
        //ANY regex here
    ]
];

/**
 * When the config is turned On, the ability to book appointments for a customer from the mobile app
 *
 * This feature allows Associates to book an In-Store, Live Chat or Phone/Video Appointment on behalf of one of their Contacts.
 *
 * @show-config public
 * @feature appointment-management
 */
$configs['retailer.services.appointment_management.is_enabled'] = false;

/**
 * When the config is turned On, the ability to book appointments for a customer/contact and send System/Event notifications
 * irrespective of customer consent settings is permitted. Note Only System/Event Notifications will be sent
 *
 * @show-config public
 * @feature appointment-management
 */
$configs['retailer.services.appointment_management.notify_without_consent'] = true;

/**
 * When the config is turned On, the ability to book appointments from the retailer customer list who are NOT necessarily in their contact book
 *
 * @show-config public
 * @feature appointment-management
 */
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = false;

/**
 * If true, the mobile will let Reps add appointments to their device’s local calendar application
 *
 * @show-config public
 * @feature appointment
 */
$configs['retailer.services.appointment.save_to_device.is_enabled'] = false;

/**
 * If true, group 1 user, namely Reps can view all appointments (under All Appointments tab) for their store within the Salesfloor mobile application
 *
 * @show-config public
 * @feature appointment-management
 */
$configs['retailer.services.appointment.all.is_enabled'] = false;

/**
 * When the config is turned On, Manager can reassign appointments from one associate to another associate in the same store via the SF mobile app
 *
 * @show-config public
 * @feature appointment-management
 */
$configs['retailer.services.appointment_reassignment.is_enabled'] = false;

/**
 * To enable the rep transaction PII data decryption
 *
 * @show-config public
 */
$configs['security.pii.crypto.enabled'] = false;

// Twilio available phone numbers search radius, in miles
$configs['twilio.available.phone.numbers.distance'] = 5;


/**
 * If true, system will track retailer customers' last timestamp of received share email or 1 to many sms,
 * and stops sending the share email or 1 to many sms within the blackout period.
 * There are different strategy as sub setting of this feature, see below section
 * @show-config public
 * @feature clienteling-blackout
 */
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = false;

/**
 * Different levels of blackout strategies can be applied to different retailers
 *
 * 1. Blackout strategy with the most requirements for customer to be considered blacked out.
 *    If blackout feature is on, this is the default strategy.
 *    Customers will be blacked out under the conditions:
 *    1) match table sf_customers_to_retailer_customers has data AND
 *    2) primary/secondary sf_customer
 *    config value: \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER_PRIMARY_SECONDARY
 *
 * 2. Customers will be blacked out under the condition:
 *    match table sf_customers_to_retailer_customers has data
 *    config value: \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER
 *
 * 3. Customers will be blacked out under the condition:
 *    Customer with primary/secondary field of the sf_customer table
 *    (match table sf_customers_to_retailer_customers is ignored with this strategy)
 *    config value: \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_CUSTOMER_DATA_ONLY
 *
 *   TODO tech debt: These different strategies exist due to unstable data in matching table sf_customers_to_retailer_customers
 *   To clean up this feature/strategies, we need to clean sf_customers_to_retailer_customers and maintain it properly
 *
 * @show-config public
 * @feature clienteling-blackout
 */
$configs['retailer.clienteling.customers.communication.blackout.strategy'] = \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER_PRIMARY_SECONDARY;

/**
 * Retailer customer blackout period in seconds(default is 7 days).
 * Only used when config retailer.clienteling.customers.communication.blackout.is_enabled is enabled.
 *
 * @show-config public
 */
$configs['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24 * 7;

/**
 * If true, system will do multi-factor authentication after the user login the system.
 *
 * @show-config public
 * @feature Multi-Factor Authentication
 */
$configs['mfa.authentication.is_enabled'] = false;

/**
 * Display state or province on retailer's storefront, about me, get my updates, etc
 */
$configs['retailer.storefront.display_state'] = true;

/**
 * The type of multi-factor authentication and it could be email based, text based, third party, etc.
 *
 * @show-config public
 * @feature Multi-Factor Authentication
 */
$configs['mfa.authentication.type'] = 'email';

/**
 * Multi-factor authentication one time password valid period (default is 10 minutes expressed in second).
 *
 * @show-config public
 * @feature Multi-Factor Authentication
 */
$configs['mfa.authentication.otp.valid_period'] = 60 * 10;

/**
 * Multi-factor authentication valid window (default is 30 days expressed in second).
 * After user passed successful MFA authentication, MFA won't be triggered again within this time window
 *
 * @show-config public
 * @feature Multi-Factor Authentication
 */
$configs['mfa.authentication.valid_window'] = 3600 * 24 * 30;

/**
 * Multi-factor authentication one time password length (default is 8 digits).
 *
 * @show-config public
 * @feature Multi-Factor Authentication
 */
$configs['mfa.authentication.otp.length'] = 8;

/**
 * If enabled, the PII related sections such as contacts, message centre, new leads, publisher, ect. will be shown in
 * Backoffice, otherwise, these sections will be completely removed.
 */
$configs['retailer.backoffice.pii.is_visible'] = true;

/**
 * Config for backoffice Reports Page
 * Restrict the number of stores selection in the filter for performance reasons
 * IMPORTANT: we need also make sure \ReportingProcessor::MAX_STORES_REPORTS is greater than max config value of all retailers
 * @show-config public
 */
$configs['retailer.backoffice.reports.filter_maximum_stores'] = 50;

/**
 * Our Proxy servers (load balancers) ip range which Symfony requires to generate the correct client ip addresses
 * https://symfony.com/doc/3.2/components/http_foundation/trusting_proxies.html
 *
 * This config is overridden by InfraConfigLoader.
 * @feature Infra
 */
$configs['trustee.proxy.ip_range'] = [];

/**
 * Used to decide if retailers onboarding routes needs to be public or not.
 */
$configs['retailer.routing.configs.is_public'] = false;

/**
 * Do reps have specialties
 *
 * Enabling speciality will list the speciality field selection in sidebar service forms
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.specialties.is_enabled'] = false;

/**
 * Allow selection of specialties in services
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.specialties.can_select'] = false;

/**
 * Is specialty required in service
 *
 * Required will show (Required) and will be 'Select a Specialty' by default
 * Not required will not show (Required) and will be 'Any Specialties' by default
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.specialties.is_required'] = false;

/**
 * Display special holiday specialty?
 *
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.specialties.has_holidays'] = false;

/**
 * Specialties filter from category tree algorithm
 * it decides which config to to use either 'retailer.specialties.exclude' or 'retailer.specialties.include_only'
 * @show-config public
 * @feature Specialties
 */
$configs['retailer.specialties.filter'] = null;

/**
 * Specialties to be excluded
 * NOTE: The config could be applied without re-import the feed, it will apply to the current categories tree in db
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.specialties.exclude'] = [];

/**
 * Specialties only from this list will be display (no local is needed for the array value)
 * NOTE:
 * 1. If 'retailer.specialties.include_only' values and categories values have an empty conjunction, then none specialties will be displayed.
 * 2. The config could be applied without re-import the feed, it will apply to the current categories tree in db
 * @show-config public
 * @feature SF Connect Widget
 */
$configs['retailer.specialties.include_only'] = [];

/**
 * Force to mapper category with a different name for specialties, only for HBC by now
 * This support multi-lang in widget/storefront, English only in Backoffice,
 * NOTE : even for the no multi-lang retailer, the specialties need put into default local, ex  ['en_US' => [data here]]
 * ex: $configs['retailer.specialties.mapping'] = [
        'en_US' => [
            '57732734' => [
                'name' => 'Women’s Clothing',
            ],
        ],
        'fr_CA' => [
            '57732734' => [
                'name' => 'Vêtements pour femme',
            ],
    ] ;
 */
$configs['retailer.specialties.mapping'] = [];

/**
 * Force to add some category into db and display for specialties, only for HBC by now
 * This supports multi-lang in widget/storefront, English only in Backoffice, format ex:
 * NOTE:
 * 1. Even for the no multi-lang retailer, the specialties need put into default local, ex  ['en_US' => [data here]]
 * 2. The config would work only after re-import the feed
 * $configs['retailer.specialties.appending'] = [
    'en_US' => [
        'gift_registry'      => [
            "id"     => "gift_registry",
            "name"   => "Gift Registry",
            "parent" => null,
        ],
    ],
    'fr_CA' => [
        'gift_registry'      => [
            "id"     => "gift_registry",
            "name"   => "Registre de cadeaux",
            "parent" => null,
        ],
    ],
 ];
 */
$configs['retailer.specialties.appending'] = [];

/**
 * If true, system will allow user to update password from settings, and user feed will ignore the subsequent
 * password update from the feed.
 * WARNING: Never turn this ON if SSO is enabled. We rely on the password field being empty to know it's SSO.
 *
 * @show-config public
 */
$configs['retailer.password_update.is_enabled'] = false;

/**
 * Push Notification service provider.
 * Before we were using SNS to send push notifications,
 * In order to support multi-cloud we now send via FCM
 * Directly. If we prefer to keep SNS for some providers
 * we could use sns in the field.
 *
 * possible values: fcm | aws
 *
 * @show-config public
 * @feature FCM
 */
$configs['service.pushnotification'] = 'fcm';

/**
 * Define if the fcm.key is a docker secret
 * ie: secret file mounted in the container at runtime
 * and managed by kubernetes or external resources.
 *
 * possible values: true | false
 *
 * @show-config public
 * @feature FCM
 */
$configs['fcm.key.secret_file'] = false;

/**
 * The Firebase Cloud Messaging Server Key.
 * If fcm.key.secret_file is set to true,
 *   This system will try to read the value from
 *   the file on the file system based on the
 *   content of this variable.
 * Else
 *   The content of this variable is taken as-is.
 *   It is not recommended to store secrets in git.
 *
 *
 * Currently, firebase token are only functional if using
 * the prod firebase project 'radiant-fire-9638'
 *
 * Yikes, the token must be the prod one
 * fcm.key is stored inside the configs/secrets
 *
 * FCM auth is done now via service account, key isn't supported anymore.
 *
 * This config is overridden by InfraConfigLoader
 *
 * @secret
 * @feature FCM
 * @deprecated
 */
$configs['fcm.key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader.
 *
 * In dev, we will use "Test alt app" (For push notification)
 *
 * @feature Infra
 */
$configs['fcm.project_id'] = 'radiant-fire-9638';

/**
 * The Firebase Cloud Messaging Server Key.
 * If we update certificates / auth keys
 * it is possible that the device token has to be
 * upgraded / re-imported. We added the version id
 * to allow to perform upgades if needed.
 *
 * @show-config public
 * @feature FCM
 */
$configs['fcm.version'] = 'v1';

/**
 * Google FCM verification url to get informations about
 * the mobile mobile identifier in FCM
 *
 * @show-config public
 * @feature FCM
 */
$configs['fcm.verification.url'] = 'https://iid.googleapis.com/iid/';

/**
 * Metric services either to use AWS CLoud Watch of stackdriver
 *
 * possible values: aws | stackdriver
 *
 * @show-config public
 * @feature FCM
 */
$configs['services.metrics'] = 'stackdriver';

/**
 * Metric services to create / manage the alarms
 *
 * possible values: true | false
 *
 * @show-config public
 * @feature Monitoring
 */
$configs['services.metrics.create_alarms'] = false;

/**
 * The slack channel where we said the "hard-to-reproduce" debug information.
 */
$configs['logger.slack.channel.debug'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'alert-debug' : 'alert-debug-tests';

/**
 * List of tickets we want to debug. This should NOT be used as a logging system.
 */
$configs['monolog.debug.tickets'] = [];

/**
 * Enable redirect whitelist
 * Restricts URLs allowed for redirection to prevent man-in-the-middle attacks
 *
 * @show-config public
 * @feature Redirect Whitelist
 */
$configs['redirect_whitelist.is_enabled'] = false;


/**
 * Enable Slack notices for redirect whitelist
 * When an invalid is attempted to be redirected to log the details in slack
 *
 * @show-config public
 * @feature Redirect Whitelist
 */
$configs['redirect_whitelist.slack_notice.is_enabled'] = true;

/**
 * The Slack Channel for redirect whitelist notices
 *
 * @show-config public
 * @feature Redirect Whitelist
 */
$configs['redirect_whitelist.slack_notice.channel'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'security-notification' : 'security-notification-test';


/**
 * Global whitelisted Domain/Sub-domains
 * Users will be allowed to be redirected to these domains
 *
 * https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1378877441/Redirect+White+List
 * @show-config public
 * @feature Redirect Whitelist
 */
$configs['redirect_whitelist.domains.global'] = [
    'salesfloor.net',
];

/**
 * Whitelisted Domain/Sub-domains and will be overridden from each retailer's white list domains config
 * if provided by retailer
 * Users will be allowed to be redirected to these domains
 *
 * @show-config public
 * @feature Redirect Whitelist
 */
$configs['redirect_whitelist.domains'] = [];

/**
 * If true, retailer site will have multi domains.
 *
 * For example: HBC will have labaie.com and thebay.com
 * and Salesfloor will have need to have multi domain for each stack, for example,
 * widgets.stores.thebay.com and widgets.magasins.labaie.com
 *
 * @show-config public
 */
$configs['retailer.multi_domains.is_enabled'] = false;

/**
 * Only set when retailer is multi domain
 *
 * This config is used to load different widget js based on retailer's website host
 * when retailer supports multi domain, For example: HBC is multi domain and we'll use
 * different short name for labaie.com and thebay.com to load widget js.
 *
 * thebay.com => widget.hbc.js
 * labaie.com => widget.labaie.js
 *
 * @show-config public
 */
$configs['retailer.multi_domains.short_name'] = '';

/**
 * Only set when retailer is multi domain
 *
 * This config is used to load different language version on widget iframe pages
 * based on the host, for example, widget landing page, etc.
 *
 * @show-config public
 */
$configs['retailer.multi_domains.locale'] = '';

/**
 * Is the outfits service enabled
 *
 * @show-config public
 */
$configs['retailer.outfits.is_enabled'] = false;

/**
 * The label of the section the outfits are displayed on the front end
 */
$configs['retailer.outfits.section_label'] = 'My Closet';

/**
 * External stylitics endpoint
 */
$configs['retailer.outfits.stylitics.endpoint'] = 'https://widget-api.stylitics.com/api/outfits';

/**
 * Name of the external service to use for outfits
 */
$configs['retailer.outfits.external_service'] = null;

/**
 * Name of the mapper to use
 * (the folder it is contained in)
 */
$configs['retailer.outfits.mapper'] = '';

/**
 * Account name for external outfits account
 */
$configs['retailer.outfits.account'] = null;

/**
 * Specifies max number of transactions that are queried per page
 */
$configs['retailer.outfits.mobile.transaction_per_page'] = 20;

// ------------------------------
// Encryption/Encoding Services (Unsubscription Email)
// ------------------------------
/**
 * A default list of the different encryption use cases
 */
$configs['encryption.use_case.default'] = ['email_unsubscribe'];

/**
 * Extra methods specific to a retailer
 * IE: Test can have a use case for every strategy for testing purposes
 */
$configs['encryption.use_case.extra'] = [];

/**
 * Enable the use of encryption when sending a user's email in unsub links
 */
$configs['encryption.use_case.email_unsubscribe.is_enabled'] = false;

/**
 * Encrpytion strategy to use when encrpyting a user's email in unsub links
 */
$configs['encryption.use_case.email_unsubscribe.strategy'] = 'Url';

/**
 * Secret key to be used in symmetric encryption of email unsub links
 * This is a 32 bit string base64urlsafe encoded
 */
$configs['encryption.use_case.email_unsubscribe.secret_key'] = null;

/**
 * External unsubscribe link used by the retailer in our SF email.
 *
 * The possible value are:
 * - null (not used)
 * - Array of locale
 * - string (locale will not affect the value)
 *
 * @show-config public
 * @feature email
 */
$configs['retailer.unsubscribe_link'] = null;

// ************** SSO ************************

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['oauth.io.key'] = 'fake';

/**
 * This config is overridden by InfraConfigLoader or dev secrets.
 *
 * @secret
 * @feature Infra
 */
$configs['oauth.io.secret'] = 'fake';

/**
 * if a retailer enable feature of OIDC for sso login
 *
 * @show-config public
 * @feature SSO-OIDC
 */
$configs['retailer.sso.is_enabled'] = false;

/**
 * the provider is Azure or Okta (google is also possible in the future)
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.type'] = Salesfloor\Services\Oauth2\Provider\AzureOauth2Provider::OAUTH2_PROVIDER;

/**
 * A space-separated list of scopes that you want the user to consent to.
 * The scope 'profile' is for UPN authentication.
 * ref: https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.scope'] = 'openid email profile';

/**
 * Decide what to prompt the SSO user when they click on the SSO button.
 * - Select account will display all logged-in users they can select from.
 * - Select login will just display the login prompt every time regardless of who logged in before.
 * @feature SSO-OIDC
 */
$configs['retailer.sso.login.prompt'] = BaseOauth2Provider::PROMPT_SELECT_ACCOUNT;


/**
 *  The default identity OOS uses to authenticate and match our salesfloor user,
 */
$configs['retailer.sso.identity'] = Oauth2Service::IDENTITY_EMAIL;
/**
 * a tenant id which get from oauth provider, ex: 1a49c38c-b87a-4ea8-9873-a29dc6b835a6
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.tenant'] = 'TO BE REPLACED';

/**
 * a client_id which get from oauth provider, ex: 6be18e91-b6a6-4ef0-bbea-dbbafe9fb43c
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.client_id'] = 'TO BE REPLACED';

/**
 * client_id on mobile which get from oauth provider, ex: 6be18e91-b6a6-4ef0-bbea-dbbafe9fb43c
 * config is only needed if SSO provider like OKTA require a different setting from web
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.client_id.mobile'] = ''; //  TO BE REPLACED

/**
 * OIDC provider server url, ex:  https://login.microsoftonline.com/1a49c38c-b87a-4ea8-9873-a29dc6b835a6;
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.server'] = 'TO BE REPLACED';

/**
 * the url(without host) of provider to authorize
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.authorize_endpoint'] = '/oauth2/v2.0/authorize';

/**
 * the url(without host of provider to get access token
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.token_endpoint'] = '/oauth2/v2.0/token';

/**
 * SSO token acquire endpoint version
 *
 * This is only used to validate id token issuer.
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.token_endpoint.version'] = 'v2.0';

/**
 * SSO token signing public keys endpoint
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.public_keys.endpoint'] = '/discovery/keys';

/**
 * the redirect_url for mobile, ex: http://localhost:9000/oauth/code
 * this value need configured in OIDC provider side also (Azure dashboard etc.)
 *
 * @feature SSO-OIDC
 * @note Not being used in the mobile side, this config logic implement also in mobile,
 * TODO: we need remove this config or remove mobile implementation
 */
$configs['retailer.sso.provider.redirect_url.mobile'] = 'salesfloor://oauth/code';

/**
 * The redirect_url for mobile web version, ex: http://saks.dev.salesfloor.net/app
 * This value needs configured in OIDC provider side also (Azure dashboard etc.)
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.redirect_url.web'] = $configs['retailer.webserver_url'] . '/app';

/**
 * the api used to get user info by access_token, so we know access_token os valid
 * for azure api, ex: 'https://graph.microsoft.com/oidc/userinfo'
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'TO BE REPLACED';

/**
 * A random secret generated by OIDC provider or share with provider, ex: '**********************************'
 * It is secret value, Azure also have secret_id we don't care about right now
 *
 * @feature SSO-OIDC
 */
$configs['retailer.sso.provider.client_secret'] = 'TO BE REPLACED';


/**
 * The config to enable the feature finding nearby stores with available chat on sidebar
 * if current store is not available.
 *
 * If true, the system will find near by stores within the configured Max distance.
 * If false, the system will only show the live chat availability from the current store.
 *
 * @show-config public
 */
$configs['retailer.chat.find_nearby_stores'] = false;

/**
 * The Max distance for finding nearby stores with available chat on sidebar
 * This is default value and needs to be configured for each retailer based on
 * product's requirements.
 *
 * @show-config public
 */
$configs['retailer.chat.find_nearby_stores.max_distance'] = '35';

/**
 * Display the availability status on Sidebar
 * Useful for lowering the number of active firebase connections (black Friday)
 *
 * @show-config public
 */
$configs['retailer.chat_availability_status.sidebar.enabled'] = true;

// if the import bucket is different than the default S3 bucket, this config needs to be overriden in
// retailer common config file under common/retailer.php
$configs['sf.import_unsubs.s3.bucket'] = 'sf-retailers';
$configs['sf.import_unsubs.s3.path'] = 'inbound/{env}/unsubscribes/email/';
$configs['sf.import_unsubs.s3.filename_regexp'] = '{retailer}-unsubscribes-\d{8}-\d{6}.csv';

// if the import bucket is different than the default S3 bucket, this config needs to be overriden in
// retailer common config file under common/retailer.php
/**
 * Path where retailer's CSV for Subscribe Unsubscribe Import will be uploaded
 *
 * @show-config public
 * @feature Importer
 */
$configs['sf.import_sub_unsubs.s3.path'] = 'inbound/{env}/subscribe-unsubscribe/email/';

/**
 * Naming convension for retailer's CSV for Subscribe Unsubscribe Import
 *
 * @show-config public
 * @feature Importer
 */
$configs['sf.import_sub_unsubs.s3.filename_regexp'] = '{retailer}-subscribe-unsubscribe-\d{8}-\d{6}.csv';

$configs['currency.symbol'] = '$';

/**================================
 *          Name Format
 * ================================
 *
 * Template Variables:
 * Ex: jack o'neil
 *  {fn}  => jack
 *  {Fn}  => Jack
 *  {fi}  => j
 *  {Fi}  => J
 *  {fi.} => j.
 *  {Fi.} => J.
 *  {Fl}  => Jack (for jAcK o'nEIl)
 *
 *  {ln}  => o'neil
 *  {Ln}  => O'Neil
 *  {li}  => o
 *  {Li}  => O
 *  {li.} => o.
 *  {Li.} => O.
 *  {Ll}  => O'Neil (for jAcK o'nEIl)
 * ================================
 */
// Defaults - Fallbacks for when the locale version is not specified
// See Docs: https://salesfloor.atlassian.net/wiki/spaces/SB/pages/2300510266/Name+Format
$configs["name_fmt.default"]                                                   = "{Fn}";
$configs["name_fmt.default.storefront_product_comment"]                        = "{Fn}";
$configs["name_fmt.default.global_email_username"]                             = "{Fn}";
$configs["name_fmt.default.rep_display_name"]                                  = "{Fn}";
$configs["name_fmt.default.rep_store_display_name"]                            = "{Fn} {Ln}";
$configs["name_fmt.default.store_display_name"]                                = "{Fn} {Ln}";
$configs["name_fmt.default.event_display_name"]                                = "{Fn}";
$configs["name_fmt.default.team_mode_rep_display_name"]                        = "{Fn}";
$configs["name_fmt.default.team_mode_email_from_name"]                         = "{Fn}";
$configs["name_fmt.default.rep_mode_email_from_name"]                          = "{Fn}";
$configs["name_fmt.default.findrep_name"]                                      = "{Fn}";
$configs["name_fmt.default.bo_services_rep_name"]                              = "{Fn}";
$configs["name_fmt.default.prepare_user_name"]                                 = "{Fn}";        // instance-webserver/src/salesfloor/wp-content/plugins/json-rest-api/lib/class-wp-json-users.php:$findrep_name when calling /api/users/me of wordpress
$configs["name_fmt.default.email_onboarding"]                                  = "{Fn}";
$configs["name_fmt.default.recover_password_rep_name"]                         = "{Fn}";
$configs["name_fmt.default.email_appt_calendar"]                               = "{Fn}";
$configs["name_fmt.default.customer_name_reschedule_appt"]                     = "{Fn} {Ln}";
$configs["name_fmt.default.customer_name_cancel_confirm_appt"]                 = "{Fn} {Ln}";
$configs["name_fmt.default.rep_mode_email_lookbook"]                           = "{Fn}";
$configs["name_fmt.default.rep_mode_email_lookbook_subject"]                   = "{Fn}";
$configs["name_fmt.default.team_mode_email_lookbook"]                          = "{Fn} {Ln}";
$configs["name_fmt.default.team_mode_email_lookbook_subject"]                  = "{Fn} {Ln}";
$configs["name_fmt.default.lookbook_hello_customer_name"]                      = "{Fn} {Ln}";
$configs["name_fmt.default.email_autoresponder"]                               = "{Fn}";
$configs["name_fmt.default.email_leads_forward_to_customer_rep_name"]          = "{Fn}";
$configs["name_fmt.default.email_leads_forward_to_customer_cs_rep_from_name"]  = "{Fn}";
$configs["name_fmt.default.email_leads_email_to_customer_rep_name"]            = "{Fn}";
$configs["name_fmt.default.mobile_dashboard"]                                  = "{Fi.} {Ln}";
$configs["name_fmt.default.mobile_contact"]                                    = "{Fn} {Ln}";
$configs["name_fmt.default.chat_rep_name"]                                     = "{Fn} {Li.}";
/**
 * Name Format Template Key for Customer Name Placeholder
 *
 * @show-config public
 * @feature Email
 */
$configs["name_fmt.default.placeholder_customer_name"]                         = "{Fn}";

/**
 * Display the graoup task rep name in proper order.
 *
 * @show-config public
 * @feature GroupTask
 */
$configs["name_fmt.default.group_task_rep_name"]                               = "{Fn} {Li.}";
$configs["name_fmt.default.chat_customer_name"]                                = "{Fn} {Ln}";
$configs["name_fmt.default.store_request_rep_name"]                            = "{Fn} {Ln}";
$configs["name_fmt.default.store_request_customer_name"]                       = "{Fn} {Ln}";
$configs["name_fmt.default.sidebar_carousel_rep_name"]                         = "{Fn}";

// Japanese defaults
$configs["name_fmt.ja_JP.storefront_product_comment"]                          = "{Ln}";
$configs["name_fmt.ja_JP.global_email_username"]                               = "{Ln}";
$configs["name_fmt.ja_JP.rep_display_name"]                                    = "{Ln}";
// asian cultures do not have concept of initials
$configs["name_fmt.ja_JP.rep_store_display_name"]                              = "{Fn} {Ln}";
$configs["name_fmt.ja_JP.store_display_name"]                                  = "{Fn} {Ln}";
$configs["name_fmt.ja_JP.event_display_name"]                                  = "{Ln}";
$configs["name_fmt.ja_JP.team_mode_rep_display_name"]                          = "{Ln}";
$configs["name_fmt.ja_JP.team_mode_email_from_name"]                           = "{Ln}";
$configs["name_fmt.ja_JP.rep_mode_email_from_name"]                            = "{Ln}";
$configs["name_fmt.ja_JP.findrep_name"]                                        = "{Fn} {Ln}";
$configs["name_fmt.ja_JP.bo_services_rep_name"]                                = "{Fn} {Ln}";
$configs["name_fmt.ja_JP.prepare_user_name"]                                   = "{Ln}";
$configs["name_fmt.ja_JP.email_onboarding"]                                    = "{Ln}";
$configs["name_fmt.ja_JP.recover_password_rep_name"]                         = "{Ln}";
$configs["name_fmt.ja_JP.email_appt_calendar"]                                 = "{Ln}";
$configs["name_fmt.ja_JP.customer_name_reschedule_appt"]                       = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.customer_name_cancel_confirm_appt"]                   = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.rep_mode_email_lookbook"]                             = "{Ln}";
$configs["name_fmt.ja_JP.rep_mode_email_lookbook_subject"]                     = "{Ln}";
$configs["name_fmt.ja_JP.team_mode_email_lookbook"]                            = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.team_mode_email_lookbook_subject"]                    = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.lookbook_hello_customer_name"]                        = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.email_autoresponder"]                                 = "{Ln}";
$configs["name_fmt.ja_JP.email_leads_forward_to_customer_rep_name"]            = "{Ln}";
$configs["name_fmt.ja_JP.email_leads_forward_to_customer_cs_rep_from_name"]    = "{Ln}";
$configs["name_fmt.ja_JP.email_leads_email_to_customer_rep_name"]              = "{Ln}";
$configs["name_fmt.ja_JP.mobile_dashboard"]                                    = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.mobile_contact"]                                      = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.chat_rep_name"]                                       = "{Ln}";
/**
 * Name Format Template Key for Customer Name Placeholder - Japanese
 *
 * @show-config public
 * @feature Email
 */
$configs["name_fmt.ja_JP.placeholder_customer_name"]                           = "{Ln}";
/**
 * Display the graoup task rep name in proper order for JP.
 *
 * @show-config public
 * @feature GroupTask
 */
$configs["name_fmt.ja_JP.group_task_rep_name"]                                 = "{Ln}";
$configs["name_fmt.ja_JP.chat_customer_name"]                                  = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.store_request_rep_name"]                              = "{Ln} {Fn}";
$configs["name_fmt.ja_JP.store_request_customer_name"]                         = "{Ln} {Fn}";

/**================================
 *         Sender Format
 * ================================
 *
 * Template Variables:
 *  {rep}, {retailer}, {store_name}
 * ================================
 */
$configs["sender_fmt.default"]                                                  = "Salesfloor";

$configs["sender_fmt.default.rep_mode_email_lookbook"]                          = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_email_lookbook"]                         = "{store_name}, {retailer}";

$configs["sender_fmt.default.rep_mode_compose_email"]                           = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_compose_email"]                          = "{rep}, {retailer}";

// new appointment time for customer
$configs["sender_fmt.default.rep_mode_new_apt_to_cust"]                         = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_new_apt_to_cust"]                        = "{store_name}, {retailer}";

//Appointment reschedule confirmed by rep
$configs["sender_fmt.default.rep_mode_apt_accept_by_rep"]                       = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_apt_accept_by_rep"]                      = "{store_name}, {retailer}";

//Rep accepted new time suggested
$configs["sender_fmt.default.rep_mode_apt_accept_resc_by_rep"]                  = "{retailer}";
$configs["sender_fmt.default.team_mode_apt_accept_resc_by_rep"]                 = "{retailer}";

//customer accepted new time suggested by
$configs["sender_fmt.default.rep_mode_apt_resc_success_customer"]               = "{retailer}";
$configs["sender_fmt.default.team_mode_apt_resc_success_customer"]              = "{retailer}";

//Appointment reminder tomorrow
$configs["sender_fmt.default.rep_mode_apt_remind_24h"]                          = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_apt_remind_24h"]                         = "{store_name}, {retailer}";

//Appointment reminder in 1h
$configs["sender_fmt.default.rep_mode_apt_remind_1h"]                           = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_apt_remind_1h"]                          = "{store_name}, {retailer}";

//Appointment reminder now
$configs["sender_fmt.default.rep_mode_apt_remind_now"]                          = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_apt_remind_now"]                         = "{store_name}, {retailer}";

$configs["sender_fmt.default.rep_mode_apt_cancel"]                              = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_apt_cancel"]                             = "{store_name}, {retailer}";

$configs["sender_fmt.default.rep_mode_apt_no_response"]                         = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_apt_no_response"]                        = "{store_name}, {retailer}";

$configs["sender_fmt.default.rep_mode_request_success"]                         = "{store_name}, {retailer}";
$configs["sender_fmt.default.team_mode_request_success"]                        = "{store_name}, {retailer}";

// product share
$configs["sender_fmt.default.rep_mode_publisher"]                               = "{rep}, {retailer}";
$configs["sender_fmt.default.team_mode_publisher"]                              = "{rep}, {retailer}";

// email add to calendar ics file
$configs["sender_fmt.default.rep_mode_ics_summary"]                             = "{retailer} - {store_name} - {rep}";
$configs["sender_fmt.default.team_mode_ics_summary"]                            = "{retailer} - {store_name}";

/** The path where files are to be validated
 *
 * @show-config public
 * @feature feed scanner
 */
$configs['feed_scanner.folder_path_template'] = 'inbound/{env}/{feedType}/scanner/';


// sf-userimage is on aws until further notice
$configs['s3.userimage'] = 'sf-userimage';
/**
 * This config is overridden by infraDerived
 *
 * @show-config public
 * @feature Infra
 */
$configs['s3.userimage'] = '__dynamic__';

$configs['s3.userimage.region'] = 'us-east-1';
$configs['s3.userimage.provider'] = 'gcp';

/**
 * Appointment Custom Hours feature
 */
$configs['retailer.store_appointment_hours.group_permissions'] = [2]; // group permission
$configs['retailer.store_appointment_hours.first_day_of_week'] = 0; // 0 - Sunday, 1 - Monday
$configs['retailer.store_appointment_hours.is_enabled'] = false; // disabled by default

 /** Emails for various email alerts.
 *
 * @show-config public
 */
$configs['alerts.operations.emails'] = ['<EMAIL>'];

/**
 * It determines whether a retailer customer's profile shows associate relationships or not.
 *
 * @show-config public
 */
$configs['retailer_customer.associate_relationships'] = true;
// Display a link to the chat instead of location of the store
// when appointments are scheduled to be remote
$configs['remote_appointment.isc.chat_link'] = false;

/**
 * Enable retailer to add a new unmatched customer in the All Customers view
 * to their Contacts
 */
$configs['retailer.add_customer_to_my_contacts.is_enabled'] = false;

/**
 * When one subscription flags (Email/SMS) of a customer is null,
 * what the subscription flag will be set to for the new contact.
 *
 * It can be any one of the following values:
 *    platform/models/src/Customer.php
 *          public const SUBSCRIPTION_STATUS_NOT_SUBSCRIBED = 0;
 *          public const SUBSCRIPTION_STATUS_SUBSCRIBED = 1;
 *          public const SUBSCRIPTION_STATUS_UNSUBSCRIBED = 2;
 */
$configs['retailer.add_customer_to_my_contacts.default_subscription_status'] =
    Customer::SUBSCRIPTION_STATUS_NOT_SUBSCRIBED;

/**
 * Related to the above configuration,
 * add the new contact as secondary through retailer_parent_customer_id
 */
$configs['retailer.add_customer_to_my_contacts.as_secondary'] = false;

/**
 * Some ZOOM config, not sure if that's really used...
 */
$configs['zoom.keys_dir']                = '/opt/salesfloor/keys';
$configs['zoom.api_dir']                 = '/opt/salesfloor/lib/zoom';
$configs['zoom.download_url']            = '/plugin/zoomusInstaller.pkg';

/**
 *  Url for cluster connection to CANS
 */
$configs['cans.internal.url'] =
    $configs['env'] === Loader::ENV_STACK_PROD
    ? 'http://cans.prd:3030' // In prod clusters, cans is in the 'prd' namespace while each platform deployment is in its own namespace (hbc-prd for instance)
    : 'http://cans:3030'; // In lower envs, cans and platform deployments are in the same namespace (box01, qa04 etc.)
$configs['cans.api.version'] = 'v1.1';

/**
 * This config must be replace with the "old" hostmap value for the main domain (storefront/bo).
 * TODO: This is temporary until the configs server is rewritten to handle alias for example.
 *
 * This is only used by MobileConfigs.php services (To get the bootstrap config)
 */
$configs['retailer.prd.sf_domain'] = null;

/**
 * Only used in api/app/crons/SendWeeklyPerformanceEmails.php
 */
$configs['env.proto'] = 'https';

// ------------------- Branch deep linking -------------------
$configs['branch.base_url'] = 'https://branch-g993dvyzae-test.salesfloor.net/a/key_test_degK1S1aWsfS6y0Tnb5qDjdkyAo5oGzT';

/**
 * Salesfloor Modular Connect
 * The valid values of tier:
 * ['basic', 'advanced', 'ultimate']. (See constant in FeatureConfigLoader)
 */
$configs['tier'] = null;

/**
 * Display/hide UI components or enable/disable some functionalities related to the storefront.
 * This is a generic config that might be used FE and BE throughout mobile and platform.
 * The valid values are true/false.
 * If true - componens/functionalities are visible/enabled.
 * This config is part of Modular connect.
 *
 * Ex: deactivate storefront link for widget footer or remove find associate for landing page.
 *
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.storefront.is_enabled'] = true;

/**
 * Display/hide UI components or enable/disable some functionalities related to the appointment request.
 * This is a generic config that might be used FE and BE throughout mobile and platform.
 * The valid values are true/false.
 * If true - componens/functionalities are visible/enabled.
 * This config is part of Modular connect.
 *
 * Ex: remove appointment request service from widget footer and landing page.
 *
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.appointment_request.is_enabled'] = true;

/**
 * Whether users can add a contact in mobile and BO
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.can_add_contacts'] = true;

/**
 * Whether permitted users can access corporate_tasks feature
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.corporate_tasks.is_enabled'] = true;

/**
 * Whether rep can update contact's communication consent in mobileApp.
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.can_change_communication_consent'] = true;


/**
 * Lure
 */
$configs['lure.cta_text'] = [[
    'en_US' => 'Connect with an associate',
    'fr_CA' => 'Joindre un associé',
]];

/**
 * Connect 2.0
 */
$configs['typhoon.url'] = 'https://prod-us.salesfloor.net/typhoon';
$configs['connect2.bot.integrationId'] = '';
$configs['connect2.conversation.entrypoint'] = '01.01. Lure/Sidebar: Default';
$configs['connect2.conversation.timeout'] = 1800000; // 30 mins
$configs['connect2.saas.organizationId'] = '';
$configs['connect2.saas.brandId'] = '';
$configs['connect2.enabled'] = false;

/**
 * Whether the users can access extended attributes for contacts/customers
 * Note: It should be kept the same value as other config "exporter.contacts.daily.enabled"
 *  because "./robo export:contacts" generates file which contains extented attributes
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.extended_attributes.is_enabled'] = true;

/**
 * Whether rep can run cron/robo command: ./robo export:contacts {retaler-env}
 * @show-config public
 * @feature Modular Connect
 */
$configs['retailer.modular_connect.can_export_contacts'] = true;

/**
 * Enable the GroupedProducts feature
 * When enabled, the reps can share multiple products as a single link
 *
 * @feature GroupedProducts
 * @show-config public
 */
$configs['retailer.grouped_products.is_enabled'] = false;

/**
 * The URL landing page of the GroupedProducts link
 * that is given by the Retailer
 *
 * @feature GroupedProducts
 * @show-config public
 */
$configs['retailer.grouped_products.url'] = 'url_to_be_replaced';

/**
 * The max number of products sent to GroupedProducts URL
 *
 * @feature GroupedProducts
 * @show-config public
 */
$configs['retailer.grouped_products.max_products'] = 10;

/**
 * The min number of products that are being sent
 * to the GroupedProducts URL
 *
 * @feature GroupedProducts
 * @show-config public
 */
$configs['retailer.grouped_products.min_products'] = 2;

/**
 * This config is for the Button Text which defaults to english
 * but is translated when the Retailer is multilang
 *
 * @feature GroupedProducts
 * @show-config public
 */
$configs['retailer.label.toggle-send-styled-link'] = 'Send Styled Link';

/**
 * fetcher/ingestor for the addresses importer.
 * @feature importer
 * @show-config public
 */
$configs['importer.addresses.fetcher'] = 'importer.fetcher.web.addresses';

/**
 * fetcher/ingestor for the addresses importer.
 * @feature importer
 * @show-config public
 */
$configs['importer.addresses.ingestor'] = 'importer.ingestor.csv';


/**
 * This config is for the Tab Text which defaults to english
 * but is translated when the Retailer is multilang
 *
 * @show-config public
 * @feature GroupTask
 */
$configs['retailer.label.group-tasks'] = 'Group Tasks';

/**
 * Is the group task enabled?
 * @show-config public
 * @feature GroupTask
 */
$configs['retailer.group_tasks.is_enabled'] = false;

/**
 * Where are the S3 files for the group task importer
 * @show-config public
 * @feature GroupTask
 */
$configs['importer.group_tasks.s3.path'] = 'inbound/{env}/group-tasks/';

/**
 * How should the name of the file to be imported be formatted?
 * @show-config public
 * @feature GroupTask
 */
$configs['importer.group_tasks.s3.filename_regexp'] = '{retailer}-group-tasks-\d{8}-\d{6}.csv';

/**
 * The interval in minutes the cron triggered.
 * @show-config public
 * @feature GroupTask
 */
$configs['sf.group_tasks.reminder.cron'] = 5;

/**
 * Enabled/Disabled sending an email as a reminder
 * @show-config public
 * @feature GroupTask
 */
$configs['sf.group_tasks.reminder.emails_enabled'] = false;

/**
 * Enabled/Disabled push a notification as a reminder
 * @show-config public
 * @feature GroupTask
 */
$configs['sf.group_tasks.reminder.notifications_enabled'] = false;


/**
 * If true, all PII information (in mobile mostly) will be obfuscated via some
 * custom logic.
 *
 * @show-config
 * @feature pii
 */
$configs['retailer.pii.obfuscate.is_enabled'] = false;


/**
 * The maximum amount when filtering contacts by transactions
 * it's set to 20000 by default
 *
 * @show-config public
 * @feature Elasticsearch
 */
$configs['retailer.search.filters.transaction.max'] = 20000;


/**
 * The buffer time in seconds to space out transactions for indexing
 *
 * @show-config public
 * @feature Elasticsearch
 */
$configs['transactions.indexer.buffer'] = 1800;

/**
 * When enabled, all messages from the customer (in chat) will be moderated via 3rd party.
 *
 * In theory, we could moderate other part of the app that come from the customer (e.g, email, request, sms)).
 * This will have it's own config if needed.
 *
 * The "text" was added, because there's some discussion about doing moderation on images too.
 *
 * @feature moderation
 */
$configs['retailer.moderation.text.is_enabled'] = false;

/**
 * The number of milliseconds until mobile will time out the call to platform.
 * The idea is not to add too much lag to the chat if hive/platform is slow to respond.
 *
 * @feature moderation
 */
$configs['retailer.moderation.text.timeout'] = 2000;

/**
 * This is not used by anyone but endpoint/service (via hive) is working.
 *
 * @feature moderation
 */
$configs['retailer.moderation.image.is_enabled'] = true;

/**
 * This is the key for dev only (Text model)
 *
 * @feature moderation
 */
$configs['hive.text.api_key'] = 'awQzeTfTGxF8u0vLFNwSwM64qF2UKmEc';

/**
 * This is the key for dev only (Image model)
 *
 * @feature moderation
 */
$configs['hive.image.api_key'] = 'kOgGS2rCmY8GWBJoWI76W7LwOEVWVhuF';

/**
 * Hive threshold for multilevel classes. Multilevel class have -1,0,1,2,3 as possible values.
 *
 * @feature moderation
 */
$configs['hive.text.multilevel.threshold'] = 2;


/**
 * For reference https://docs.thehive.ai/docs/visual-content-moderation
 * The threshold for visual moderation begins with 0.9 values.
 *
 * @feature moderation
 */
$configs['hive.image.score.threshold'] = 0.9;


/**
 * Hive threshold for each class possible currently.
 *
 * Only classes from$supportedClasses in \Salesfloor\Services\Moderation\Moderator\HiveModerator
 * can be specified here. Even if API/Hive support more.
 *
 * // Multi level
 * 'sexual' => 1-3,
 * 'hate' => 1-3,
 * 'violence' => 1-3,
 * 'bullying' => 1-3,
 *
 * // Binary
 * 'child_exploitation' => 0-3, // Warning: Don't put 1-2 ; because it will never be triggered.
 *
 * @feature moderation
 */
$configs['hive.text.custom.threshold'] = [];


/**
 * Same as above
 * Hive custom threshold for individual classes
 * for visual moderation
 *
 * @feature moderation
 */
$configs['hive.image.custom.threshold'] = [];


/**
 * Enable photo attachments from the Customers in chat
 *
 * @feature PhotoAttachments
 */
$configs['retailer.chat.photo_attachment.is_enabled'] = false;

/**
 * This config controls weither task/group-task are auto-resolved
 * when sending communications (email/sms/share) relating to that task.
 * @show-config public
 * @feature Tasks
 */
$configs['retailer.tasks.auto-resolve.is_enabled'] = false;

/**
 * Show UPC passed via product-attribute-1 in CI Transaction Feed in Transaction History
 *
 * @feature TransactionHistory
 */
$configs['retailer.transaction.show_product_color_as_upc'] = false;

/**
 * Configure the appointment request lead time
 * for Connect 1&2, Sidebar
 * The value is in minutes
 *
 * @show-config public
 * @feature AppointmentLeadTime
 */
$configs['retailer.appointment_lead_time.widget'] = [
    Appointment::MEETTING_TYPE_STORE => 240, // 4h
    Appointment::MEETTING_TYPE_VIRTUAL => 240,
    Appointment::MEETTING_TYPE_PHONE => 240,
];

/**
 * Configure the appointment request lead time for Connect2
 * The value is in minutes
 *
 * @show-config public
 * @feature AppointmentLeadTime
 */
$configs['retailer.appointment_lead_time.mobile_app'] = [
    Appointment::MEETTING_TYPE_STORE => 0,
    Appointment::MEETTING_TYPE_VIRTUAL => 0,
    Appointment::MEETTING_TYPE_PHONE => 0,
];

/**
 * The max level/depth allowed for any given product category import.
 * That is, L1...Ln
 *
 * @feature ProductCategoryLevels
 */
$configs['retailer.products.category.max.level'] = 4;

/**
 * Information needed for JS sdk
 * @feature firebase
 */
$configs['firebase.apps.web.configuration'] = [
    'apiKey' => "AIzaSyBQmu4YHCb0htRe4-vNWR2ZYT8maxGJz_8",
    'authDomain' => "radiant-fire-9638.firebaseapp.com",
    'databaseURL' => "https://radiant-fire-9638.firebaseio.com",
    'projectId' => "radiant-fire-9638",
    'storageBucket' => "radiant-fire-9638.appspot.com",
    'messagingSenderId' => "1070259995388",
    'appId' => "1:1070259995388:web:99af12bf27f5edaa408d79",
    'measurementId' => "G-S4185TM8FM"
];

/**
 * Voluntary Application Server Identification (vapid) key.
 * @feature firebase
 */
$configs['firebase.apps.web.vapid_key'] = 'BMkqKD0brFtzi9jg6-Wnt7RLA7piKubJS42iGYYEgHuUHNGAuNXIIqrBIBsURYP9R8reQ3ie3O99Sk2YJ7BRFkI';

/**
 * Add an special mobile configuration for a different LURE behavior
 * @feature Connect2 Webchat Lure
 */
$configs['lure.special.mobile'] = false;

/**
 * This config determines if the AI outreach assistant is enabled.
 *
 * @feature AI
 */
$configs['ai.outreach.is_enabled'] = false;

/**
 * This config determines the default AI provider.
 *
 * @feature AI
 */
$configs['ai.default_provider'] = 'vertexai';

/**
 * The resource id of the project for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.project_id'] = 'sf-sandbox-6d1bf97488';

/**
 * The region of the project resource on Google Cloud Platform for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.location'] = 'us-central1';

/**
 * Controls randomness in response generation (0.0-1.0) for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.temperature'] = 0.4;

/**
 * Maximum number of tokens in the generated response for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.max_tokens'] = 8192;

/**
 * The AI model to use for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.model'] = 'gemini-2.0-flash-lite';

/**
 * Controls diversity of results via nucleus sampling for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.top_p'] = 0.8;

/**
 * Limits vocabulary token selection for Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.top_k'] = 40;

/**
 * List of blocked terms/patterns for content moderation in Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.safety_settings.block_list'] = [];

/**
 * List of allowed terms/patterns for content moderation in Vertex AI provider.
 *
 * @feature AI
 */
$configs['ai.vertexai.safety_settings.allow_list'] = [];

/**
 * The authorized account to the project resource on Google Cloud Platform.
 * The value should be Base64 encoded JSON.
 * This will cause any available GAC to be ignored if provided. use only if needed.
 *
 * @feature AI
 */
$configs['ai.vertexai.service_account'] = '';

/**
 * Controls randomness in response generation (0.0-1.0) for Refiner agent.
 *
 * @feature AI
 */
$configs['ai.agent.refiner.override.temperature'] = 0.5;

/**
 * Recommended Prompts curated by retailer admins for AI assistant.
 * Associates can select from these predefined message ideas to generate brand-aligned outreach.
 *
 * JSON Structure:
 * [locale]:[[
 * - id: Unique identifier for the prompt
 * - title: Display name for the prompt
 * - description: Brief description of when to use this prompt
 * - prompt: The actual prompt text to be sent to AI
 * - category: Optional category for grouping prompts
 * - active: Whether the prompt is currently available
 * ]]
 *
 * @show-config
 * @feature AI
 */
$configs['ai.outreach.recommended_prompts'] = <<<JSON
{}
JSON;
