<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Moderation\Moderator\HiveModerator;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;

$configs['s3.userimage.provider'] = 'gcp';
$configs['customerservice.can_forward'] = true;
$configs['retailer.storefront.is-new-stack'] = true;
$configs['retailer.default_currency'] = 'USD';
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';
$configs['retailer.storepage_mode'] = false;
$configs['retailer.publisher_storefront'] = true;

$configs['stores.max_distance'] = '9001'; // "Nationwide"

// === RETAILER(S) NAME(S)
$configs['retailers.name'] = 'jwas';
$configs['retailer.name'] = "jwas";
$configs['retailer.short_name'] = 'jwas';
$configs['retailers.short'] = "jwas";
$configs['retailer.brand_name'] = "Johnny Was";
$configs['retailer.pretty_name'] = "Johnny Was";
$configs['firebase.retailername'] = 'jwas';

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.johnnywas.com';
$configs['retailers.url'] = "https://www.johnnywas.com";
$configs['retailer.site'] = 'https://www.johnnywas.com';
$configs['retailer.url'] = 'https://www.johnnywas.com';
$configs['retailer.domain'] = "johnnywas.com";
$configs['retailer.email_domain'] = '';
$configs['retailer.contacturl'] = "https://www.johnnywas.com/contact";

$configs['retailer.application_start_date'] = '2018-09-13 22:36:13';

$configs['retailer.hq_address'] = "712 South Olive Street, Los Angeles, CA 90014";
$configs['retailer.hq_address.street'] = "712 South Olive Street";
$configs['retailer.hq_address.city'] = "Los Angeles";
$configs['retailer.hq_address.region'] = "California";
$configs['retailer.hq_address.isoCountry'] = "US";
$configs['retailer.hq_address.postalCode'] = "90014";
$configs['retailer.hq_address.customerName'] = "Johnny Was";

$configs['retailer.store.default_timezone'] = 'America/Los_Angeles';

// === PRODUCTS ===
$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\Jwas';
$configs['importer.products.feed_url'] = 'https://johnnywas.com/media/google_pla_feed.txt';
$configs['update_products.max_errors'] = 100; // Currently have 1
$configs['update_products.min_products'] = 500; // Currently have 769 => 692
$configs['update_products.min_categories'] = 20;
$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 12;
$configs['storefront.num_top_picks'] = 12;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'min_products' => 12, 'max_products' => 12, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'min_products' => 8, 'max_products' => 8, 'show_comments' => 1],
];
$configs['retailer.associate'] = "Store Associate";
$configs['retailer.social_networks'] = array('twitter');
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 7; // 7 days in ms

// === INSTAGRAM ===
$configs['retailer.instagram.account'] = 'johnnywas';
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 0, 'max_posts' => 8];

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = false;

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/jwas/jwas-logo_new.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/jwas/jwas-logo_new.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = false;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// Chat moderation
$configs['retailer.moderation.text.is_enabled'] = true;
$configs['hive.text.custom.threshold'] = [
    HiveModerator::HIVE_CLASS_SEXUAL => 1,
    HiveModerator::HIVE_CLASS_CHILD_EXPLOITATION => 3
];
$configs['retailer.chat.photo_attachment.is_enabled'] = true;

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';
$configs['retailer.chat.routing.boundary.single-store'] = true;

// Legacy mode metric logging
$configs['services.record_chat_metrics_cans.enabled']  = false;

// === MOBILE ===
$configs['mobile.email_me_label'] = "Contact Me";
$configs['mobile.menu_logo_path'] = '/img/retailers/jwas/jwas_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/jwas/jwas-logo-onboarding.png';
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;
$configs['mobile.quick_responses'] = [[
  'short' => 'Give me a moment',
  'full' => 'Please give me a moment and I will look into this for you.',
], [
  'short' => 'Thank you and goodbye',
  'full' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
], [
  'short' => 'Out of stock',
  'full' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking retailer_link',
], [
  'short' => 'My Storefront Link',
  'full' => 'storefront_link',
], [
  'short' => 'Subscribe Link',
  'full' => 'subscribe_link',
], [
  'short' => 'Appointment Link',
  'full' => 'appointment_link',
]];
$configs['mobile.extra_quick_responses'] = [[
  'short' => 'Contact Customer Service',
  'full' => 'Our Customer service team can help you with this request. They are available via phone at ************ and by <NAME_EMAIL>, thank you.'
]];
$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "true",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Johnny Was Store Associate. I can answer your questions and help you find what you’re shopping for at your local store or online.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "true",
    "connect-social": {
        "twitter": "true"
    },
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
    }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.enabled'] = true;

$configs['chat.num_sec_before_heartbeat'] = 7200;

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 7;

// === SIDEBAR3 VARIANT ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 320;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 320;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = 'https://cdn.salesfloor.net/salesfloor-assets/jwas/jwas-sidebar-logo-new.png';
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;
$configs['retailer.services.carousel.display_mode'] = 'carousel';

// BOF === Widget ===
$configs['retailer.combine_countries'] = [
    'US' => ['US', 'CA'],
    'CA' => ['US', 'CA'],
];

// Get stores by applying default distance and store custom distance in db table
$configs['retailer.combine_countries_all_distance'] = false;
// EOF === Widget ===

//If true, txt messaging could send to multiple recipients from associate
$configs['messaging.text.multiple-recipients.enabled'] = true;

$configs['retailer.virtual.option.video-chat'] = false;
$configs['retailer.virtual.option.video-chat.2ways'] = false;

$configs['retailer.services.appointment.types'] = [
  [ 'type' => 'store', 'is_enabled' => true, 'is_default' => false, 'position' => 0, 'use_alternate_label' => false ],
  [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
  [ 'type' => 'virtual', 'is_enabled' => true, 'is_default' => true, 'position' => 2, 'use_alternate_label' => true ],
];

//When the config is turned On, the ability to book appointments for a customer from the mobile app
$configs['retailer.services.appointment_management.is_enabled'] = true;

//If true, group 1 user, namely rep can see all the appointments in his store
$configs['retailer.services.appointment.all.is_enabled'] = true;

//When the config is turned On, the ability to reassign appointments for a customer from the mobile app
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

//If true, the mobile will let you save your appointment to your device.
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;

$configs['retailer.store.open_hour'] = 9;
$configs['retailer.store.close_hour'] = 20;

$configs['retailer.chat.option.eject_after_hours'] = true;

// Video Chat feature
$configs['retailer.chat.option.video-chat'] = false;
$configs['retailer.chat.option.video-chat.2ways'] = false;

$configs['redirect_whitelist.domains'] = [
    'storejohnnywas.com',
    'jwas-m2.stage.guidance.com',
];

$configs['retailer.contacts.import_enabled'] = true;

$configs['importer.import-as-subscriber.email'] = 0;

// ******* CI IMPORT
$configs['retailer.clienteling.mode'] = true;

// ==== IMPORT CUSTOMERS ===
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'jwas-customers-\d{8}-\d{6}\.csv';

// ******* CI Transactions
$configs['sf.import_ci_transactions.s3.filename_regexp'] = 'jwas-transactions-\d{8}-\d{6}\.csv';

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

$configs['exporter.activitySummary.rep.daily.enabled'] = true;
$configs['exporter.activitySummary.rep.daily.start_days_before'] = -2;
$configs['exporter.activitySummary.rep.daily.end_days_before'] = -2;

$configs['retailer.clienteling.enabled.transactions'] = true;
$configs['retailer.clienteling.enabled.customer_stats'] = true;

$configs['sf.task.auto_dismiss.enabled'] = true;

$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 5 ];
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 5;
$configs['sf.task.automated.new_retailer_transaction_filtered.any_store'] = true;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 5;
$configs['sf.task.automated.retailer_transaction_employee_assigned_multiple.days_search_back'] = 5;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 5;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 60;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 3;

$configs['retailer.customer_tags.is_enabled'] = true;

$configs['retailer.store_appointment_hours.is_enabled'] = true;
$configs['retailer.store_appointment_hours.group_permissions'] = [2,3];
$configs['retailer.store_appointment_hours.first_day_of_week'] = 1;

$configs['service.personal_shopper'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;

// DEVOPS-2595 Covid-19 crisis lockout users
$configs['retailer.lockout.duration'] = 259200; // 180 days in minutes

// === CONNECT 2 ===
// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = 'BBMHV6Z130LgSZvhQjHP';
$configs['connect2.saas.brandId'] = 'vGhMLs0mEpASWaZKLJ2D';
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = 'eabef595-ee53-474d-ade4-584445f9c348';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = 'fdb1fd3a-aa62-414c-a80d-a40262ae2e6a';
        $configs['typhoon.url'] = 'https://box01.sandbox.salesfloor.net/typhoon';
        break;
    case 'box02':
        $configs['connect2.bot.integrationId'] = 'a0b718ae-95cc-4d8f-9257-b52afc65affd';
        $configs['typhoon.url'] = 'https://box02.sandbox.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = 'b62fcfac-41b1-46ec-bca8-e54c423a9a72';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = '93da834e-ac15-493d-a52d-9da9dc41db73';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'qa06':
        $configs['connect2.bot.integrationId'] = '784aa24d-c97e-4411-96fe-0de3d8b38eb1';
        $configs['typhoon.url'] = 'https://qa06.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = 'ab7ffc5c-9faa-4a69-9c6a-a8babf776df6';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web'] = 'UA-122806915-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-122806915-2';
    $configs['retailer.google.ga_services']          = 'UA-122806915-3';
    $configs['retailer.google.ga_storefront']        = 'UA-122806915-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-122806915-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-122806915-6';

    // ------------------- Branch deep linking -------------------
    $configs['branch.base_url'] = 'https://branch-g993dvyzae.salesfloor.net/a/key_live_fbjT6P7c5raRYBXVmpZDdfbdqyk3jIFq';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MG44db696b9400fc36335f721486ae121d';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MG45730e24f8cf8a20c4781795dd184b6e';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://store.johnnywas.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'johnnywas' : "{$configs['retailer.short_name']}-{$configs['env']}";
