<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Moderation\Moderator\HiveModerator;
use Salesfloor\Models\Services\Appointment;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

// S3 Provider Overrides
$configs['s3.userimage.provider'] = 'gcp';

$configs['s3.bucket.provider']              = 'gcp';
$configs['geoip.s3bucket.provider']         = 'gcp';
$configs['logrotate.s3.provider']           = 'gcp';
$configs['logrotate_client.s3.provider']    = 'gcp';
$configs['rep-export.s3.provider']          = 'gcp';
$configs['sf.import_unsubs.s3.provider']    = 'gcp';


$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 1; // 1 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'macys';
$configs['retailer.short_name'] = 'macys';
$configs['retailer.brand_name'] = 'Macys';
$configs['retailer.pretty_name'] = 'Macys';

$configs['retailers.name'] = 'macys';
$configs['retailers.short'] = 'macys';

$configs['firebase.retailername'] = 'macys';

$configs['retailer.application_start_date'] = '2021-06-03 00:00:00';

$configs['retailer.hq_address'] = '151 West 34th Street New York, NY, 10001';

$configs['retailer.hq_address.street'] = "151 West 34th Street";
$configs['retailer.hq_address.city'] = "New York";
$configs['retailer.hq_address.region'] = "NY";
$configs['retailer.hq_address.isoCountry'] = "US";
$configs['retailer.hq_address.postalCode'] = "10001";
$configs['retailer.hq_address.customerName'] = "Macy's";

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.macys.com/';
$configs['retailer.site'] = 'https://www.macys.com/';
$configs['retailer.domain'] = 'macys.com';
$configs['retailer.contacturl'] = 'https://www.customerservice-macys.com/app/home';
$configs['retailer.email_domain'] = 'macys.com';

$configs['retailers.url'] = 'https://www.macys.com/';
$configs['retailer.unsubscribe_link'] = 'https://emails.macys.com/pub/sf/ResponseForm?_ri_=X0Gzc2X%3DYQpglLjHJlTQGpaLMhSBKeu7UJzazdCzafHzavdWKzgc06LGVXMtX%3DYQpglLjHJlTQGnTYCzdO0LXj8hSzdvLkyzfyzc52Rp8wzdf&_ei_=Erqm3cG7gxV0Vsysz6FM8cg';

// === PRODUCTS ===
$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\\' . ucfirst('macys');
$configs['update_products.max_errors'] = 50000;
$configs['update_products.min_products'] = 500;
$configs['update_products.min_categories'] = 80;

$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['importer.product.fetcher'] = 'importer.fetcher.s3.product';
$configs['importer.product.ingestor'] = 'importer.ingestor.csv.products';

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;

$configs['importer.products.file_pattern'] = 'TO BE REPLACED';

// === STOREFRONT ===
$configs['retailer.num_deals'] = 4;
$configs['retailer.num_top_picks'] = 12;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'max_products' => 12, 'min_products' => 12, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Style Expert';
$configs['retailer.social_networks'] = array("twitter");
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

$configs['redirect_whitelist.domains'] = [
    'customerservice-macys.com',
    'visitmacysusa.com',
    'macysbackstage.com',
    'macysinc.com',
];

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = false;
$configs['retailer.trending_recommendations.max'] = 8;
$configs['retailer.trending_recommendations.min'] = 2;
$configs['retailer.trending_recommendations.title'] = "";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = false;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 0, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'macys';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/macys/macys-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/macys/macys-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// Chat moderation
$configs['retailer.moderation.text.is_enabled'] = true;
$configs['hive.text.custom.threshold'] = [
    HiveModerator::HIVE_CLASS_SEXUAL => 3,
    HiveModerator::HIVE_CLASS_CHILD_EXPLOITATION => 3
];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.appointment.types'] = [
    ['type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false],
    ['type' => 'virtual', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false],
    ['type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false],
];

$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

// === APPOINTMENT LEAD TIME ===
$configs['retailer.appointment_lead_time.widget'] = [
    Appointment::MEETTING_TYPE_STORE => 1440, // 24h
    Appointment::MEETTING_TYPE_VIRTUAL => 1440,
    Appointment::MEETTING_TYPE_PHONE => 1440,
];

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['mobile.retailer_has_specialties'] = true;
$configs['retailer.specialties.is_enabled'] = true;
$configs['retailer.specialties.can_select'] = true;
$configs['retailer.specialties.is_required'] = true;

$configs['retailer.specialties.filter'] = \Salesfloor\API\Managers\Categories::SPECIALTIES_FILTER_ALGORITHM_EXCLUDE;
$configs['retailer.specialties.exclude'] = [
    'misc', // Miscellaneous
];

$configs['retailer.specialties.appending'] = [
    'en_US' => [
        'registry'      => [
            "id"     => "registry",
            "name"   => "Registry",
            "parent" => 1,
        ],
    ],
];

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = false;
$configs['retailer.sidebar.v3.media.desktop.width'] = 280;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 280;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;

// === SIDEBAR3 ===
$configs['retailer.services.carousel.display_mode'] = 'single';

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Us';

$configs['mobile.menu_logo_path'] = '/img/retailers/macys/macys_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/macys/macys-logo-onboarding.png';

// === LIVE CHAT QUICK RESPONSES ===
$configs['mobile.quick_responses'] = [[
  'short' => "Give me a moment",
  'full' => "Give me a moment to review your question and I'll be right with you.",
], [
  'short' => "Thank you and goodbye",
  'full' => "Thank you for choosing Macy's! If you have any other questions, please feel free to contact us again.",
], [
  'short' => "Out of stock",
  'full' => "I apologize, we are currently out of stock of that item. I can show you other similar items. What drew you to this item so that I can locate something similar?",
], [
  'short' => "Out of stock in-store",
  'full'  => "I apologize, we are currently out of stock of that item in our store. You can buy it online, or I can show you other similar items that we currently have in stock.",
], [
  'short' => "Subscribe Link",
  'full' => "subscribe_link",
], [
  'short' => "My Storefront Link",
  'full' => "storefront_link",
]];

$configs['mobile.extra_quick_responses'] = [[
  'short' => "Customer support",
  'full' => "I'm unable to assist with this inquiry, however, you can find answers or get help by visiting our <a href='https://customerservice-macys.com/' target='_blank'>Customer Service page</a>."
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = true;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Macy's Style Expert. I can answer your questions and help you find what you're shopping for at your local store or online.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "true",
    "import-contact": "false",
    "connect-social": {
		"twitter": "true"
	},
    "congrats": "false"
  },

   "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;

// ==== IMPORT CUSTOMERS ===
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.encrypted'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'macys-customers-\d{8}-\d{6}\.csv\.pgp';

// ==== IMPORT USERS ===
$configs['sf.import_users.encrypted'] = true;
$configs['importer.reps.s3.filename_regexp'] = '{retailer}-users-\d{8}-\d{6}\.csv\.pgp';

$configs['retailer.rep_avatar_picture.random_default'] = [];

$configs['retailer.customer_tags.is_enabled'] = true;

$configs['service.personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;

// Social Shop feature
$configs['retailer.shop_feed.enabled'] = true;

// Video Chat feature
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs['stores.max_distance'] = '9001'; // National

// ******* CI IMPORT
$configs['retailer.clienteling.mode'] = true;

// ******* CI Transactions
$configs['retailer.clienteling.enabled.transactions'] = true;
$configs['sf.import_ci_transactions.encrypted'] = true;
$configs['sf.import_ci_transactions.s3.filename_regexp'] = 'macys-transactions-\d{8}-\d{6}\.csv\.pgp';

// ******* CI stats
$configs['retailer.clienteling.enabled.customer_stats'] = true;
$configs['sf.import_ci_stats.encrypted'] = true;
$configs['sf.import_ci_stats.s3.filename_regexp'] = 'macys-statistics-\d{8}-\d{6}\.csv\.pgp';

// === SSO CONFIGS ===
$configs['retailer.sso.is_enabled'] = true;
$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://graph.microsoft.com/oidc/userinfo';

$configs['retailer.sso.provider.tenant']    = '42ab9b91-5029-47c6-8f78-38a33f843520';
$configs['retailer.sso.provider.client_id'] = '16417f8e-2bb4-48be-907e-07afd3a93e0a';
$configs['retailer.sso.provider.client_secret'] = '****************************************';   //Secret ID: ba03bac5-010a-413a-87b3-39e7749f1886

$configs['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 14;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 4;

// DEVOPS-6305 Macys to use the existing bloom gpg keys
$configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/bloom/sf-gpg-private-bloom-stg.asc';

$configs['importer.reps.enabled'] = true;

$configs['importer.rep_transactions.enabled'] = true;

// DEVOPS-6362 - Macys problem enabling text messaging numbers
// Some stores could not find available phone numbers within the 5 miles, so need to
// enlarge the distance to guarantee the phone number availability
$configs['twilio.available.phone.numbers.distance'] = 25;

// === EXPORTER settings ===
$configs['exporter.request.daily.enabled'] = true;
$configs['exporter.request.daily.start_days_before'] = -2;
$configs['exporter.request.daily.end_days_before']   = -2;

$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

$configs['exporter.transactions.daily.enabled'] = true;
$configs['exporter.transactions.daily.start_days_before'] = -180;
$configs['exporter.transactions.daily.end_days_before'] = -1;

$configs['exporter.activitySummary.rep.daily.enabled'] = true;
$configs['exporter.activitySummary.rep.daily.start_days_before'] = -180;
$configs['exporter.activitySummary.rep.daily.end_days_before'] = -1;

$configs['exporter.dynamic_exporter.daily.is_enabled'] = true;
$configs['exporter.dynamic_exporter.exporters'] = ['predefined/ropo.interaction', 'predefined/ropo.text', 'predefined/ropo.email_click', 'predefined/ropo.email_open'];

$configs['retailer.storage_method'] = 'localstorage';
$configs['retailer.disable_cookies'] = true;

$configs['retailer.chat_availability_status.sidebar.enabled'] = false;

$configs['retailer.filter_new_leads_by_specialty'] = true;

// === Mobile app distribution ===
$configs['mobile_ios_appid']     = 'com.salesfloor.enterprise';
$configs['mobile_android_appid'] = 'com.salesfloor.enterprise';

$configs['retailer.virtual.option.video-chat'] = true;
$configs['retailer.virtual.option.video-chat.2ways'] = true;

/*
 * Mobile app distribution parameters
 */
$configs['mobile_ios_channel'] = 's3';

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    // TODO why most other retailers have the default value 'salesfloor.net' even on prod ????
    // Smells like this is not used, or most other retailers are broken on prod :thinking-face
    $configs['retailer.cookie_domain'] = 'store-macys.com';

    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-199955332-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-199955332-2';
    $configs['retailer.google.ga_services']          = 'UA-199955332-3';
    $configs['retailer.google.ga_storefront']        = 'UA-199955332-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-199955332-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-199955332-6';

    // DEVOPS-6305 Macys to use the existing bloom gpg keys
    $configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/bloom/sf-gpg-private-bloom-prd.asc';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MGfa333645309ab2a5a22bdbe5aed2d913';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MGf6a9c5ec5e87698c07f33955eec74797';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://store-macys.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'macys' : "{$configs['retailer.short_name']}-{$configs['env']}";
