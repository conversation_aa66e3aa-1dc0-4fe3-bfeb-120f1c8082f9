<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Tasks\Features\TaskAutoDismissProcessor;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::MANAGER;
Perms::$permissionsMap['corporate-task'] = Perms::MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'CAD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 7; // 7 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = false;
$configs['onboarding.allow_dupe_emails'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'pharmaprix';
$configs['retailer.short_name'] = 'pharmaprix';
$configs['retailer.brand_name'] = 'Pharmaprix';
$configs['retailer.pretty_name'] = 'Pharmaprix';

$configs['retailers.name'] = 'pharmaprix';
$configs['retailers.short'] = 'pharmaprix';

$configs['firebase.retailername'] = 'pharmaprix';
$configs['retailer.application_start_date'] = '2021-03-22 00:00:00';

$configs['retailer.hq_address'] = '400, avenue Sainte-Croix, Suite 10, Saint-Laurent, QC H4N 3L4';
$configs['retailer.hq_address.street'] = '400, avenue Sainte-Croix, Suite 10';
$configs['retailer.hq_address.city'] = 'Montreal';
$configs['retailer.hq_address.region'] = 'QC';
$configs['retailer.hq_address.isoCountry'] = 'CA';
$configs['retailer.hq_address.postalCode'] = 'H4N 3L4';
$configs['retailer.hq_address.customerName'] = 'Pharmaprix';

$configs['retailer.country.code'] = 'CA';

$configs['retailer.combine_countries_all_distance'] = false;
$configs['stores.max_distance'] = '9001'; // national

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://magasiner.pharmaprix.ca/';
$configs['retailer.site'] = 'https://magasiner.pharmaprix.ca/';
$configs['retailer.domain'] = 'pharmaprix.ca';
$configs['retailer.contacturl'] = 'https://magasiner.pharmaprix.ca/contactus';
$configs['retailer.email_domain'] = 'pharmaprix.ca';
$configs['retailer.mainSearchUrl'] = [
  'en_US' => 'https://magasiner.pharmaprix.ca/search?lang=en&text=',
  'fr_CA' => 'https://magasiner.pharmaprix.ca/search?lang=fr&text='
];

$configs['retailers.url'] = 'https://magasiner.pharmaprix.ca/';

// === PRODUCTS ===
$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\\' . ucfirst('pharmaprix');
$configs['update_products.max_errors'] = 3000;
$configs['update_products.min_products'] = 500;
$configs['update_products.min_categories'] = 50;

$configs['product_img_size'] = 250;

$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;
$configs['product.panels.refresh.autoselected.frequency'] = 28;

// === STOREFRONT ===
$configs['retailer.num_deals'] = 4;
$configs['retailer.num_top_picks'] = 8;

$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 8, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 4, 'show_comments' => 1],
    ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 0],
];

$configs['retailer.associate'] = 'Beauty Specialist';
$configs['retailer.social_networks'] = [];

$configs['retailer.publisher_storefront'] = true;

$configs['retailer.shoppage.tracking.custom_params'] = [
    'SF_emp' => Salesfloor\Services\ShopPage\ShopPage::RETAILER_DYNAMIC_PARAM_EMPLOYEE_ID,
];

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = false;

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "What’s Trending";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 8, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'pharmaprixbeaute';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/pharmaprix/pharmaprix-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/pharmaprix/pharmaprix-logo.png';

$configs['retailer.rep_avatar_picture.random_default'] = [];

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.appointment.types'] = [
    [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
    [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
];

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['retailer.available_services'] = [
  [ 'type' => 'chat', 'modal' => 'popup', 'maxHeight' => 800 ],
  [ 'type' => 'inscription', 'modal' => 'iframe', 'maxHeight' => 1550 ],
  [ 'type' => 'question', 'modal' => 'iframe', 'maxHeight' => 900 ],
  [ 'type' => 'finder', 'modal' => 'iframe', 'maxHeight' => 900 ],
  [ 'type' => 'appointment', 'modal' => 'iframe', 'maxHeight' => 1500 ],
  [ 'type' => 'profile', 'modal' => 'embedded', 'maxHeight' => 465 ],
];

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = false;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;

$configs['retailer.services.carousel.display_mode'] = 'carousel';
$configs['retailer.services.carousel.show_names'] = false;

$configs['retailer.footer.breakpoint'] = 960;

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/pharmaprix/pharmaprix_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/pharmaprix/pharmaprix-logo-onboarding.png';

$configs['mobile_ios_appid']     = 'com.salesfloor.enterprise';
$configs['mobile_android_appid'] = 'com.salesfloor.enterprise';

$configs['mobile.quick_responses'] = [[
    'short' => [
      'en_US' => 'Pharmaprix Customer Service',
      'fr_CA' => 'Service à la Clientèle Pharmaprix',
    ],
    'full' => [
      'en_US' => 'Our Customer Service team can help you with this request. They are available via phone at **************, thanks.',
      'fr_CA' => 'Notre équipe du service à la clientèle peut vous aider avec cette demande. L\'équipe est disponible par téléphone au **************, merci.',
    ],
], [
    'short' => [
      'en_US' => 'Give me a moment',
      'fr_CA' => 'Donnez-moi un instant',
    ],
    'full' => [
      'en_US' => 'Please give me a moment and I will look into this for you.',
      'fr_CA' => 'Veuillez me donner un instant et je ferais les recherches pour vous.',
    ],
], [
    'short' => [
      'en_US' => 'Thank you and goodbye',
      'fr_CA' => 'Merci et au revoir',
    ],
    'full' => [
      'en_US' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
      'fr_CA' => 'Merci pour votre demande! Je serai heureux(se) de vous servir à nouveau. Passez une excellente journée.',
    ],
], [
    'short' => [
      'en_US' => 'Out of stock',
      'fr_CA' => 'En rupture de stock',
    ],
    'full' => [
      'en_US' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking retailer_link',
      'fr_CA' => 'Malheureusement, ce produit n\'est pas encore disponible dans notre magasin, mais vous pouvez l\'acheter en ligne en cliquant retailer_link',
    ],
], [
    'short' => [
      'en_US' => 'My Storefront Link',
      'fr_CA' => 'Lien vers Ma vitrine',
    ],
    'full' => 'storefront_link',
], [
    'short' => [
      'en_US' => 'Subscribe Link',
      'fr_CA' => 'Lien d\'abonnement',
    ],
    'full' => 'subscribe_link',
]];

$configs['mobile.extra_quick_responses'] = [[
    'short' => [
        'en_US' => 'Online Beauty Customer Service',
        'fr_CA' => 'Service à la clientèle beauté en ligne',
    ],
    'full' => [
        'en_US' => 'Our Online Beauty Customer Service team can help you with this request. They are available via phone at **************.',
        'fr_CA' => 'Notre équipe du service à la clientèle beauté en ligne peut vous aider avec cette demande. L\'équipe est disponible par téléphone au **************.'
    ],
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"I am a Pharmaprix Beauty Specialist.I can help you with your beauty needs and assist you in finding what you're looking for.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
        "enter-email": {
            "alias": "false"
        },
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
        "enter-email": {
            "alias": "false"
        },
        "pick-store": "false",
        "take-picture": "false",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
        "enter-email": {
            "alias": "false"
        },
        "pick-store": "false",
        "take-picture": "false",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }]
}

JSON;

$configs['mobile.extra_specificities'] = [
    'pages' => [
        'pick-store' => [
            'introduction' => [
                'en_US' => "\"Hello! I am a Pharmaprix Beauty Specialist. I can help you with your beauty needs and assist you in finding what you're looking for\"",
                'fr_CA' => "\"Bonjour! Je suis spécialiste de la beauté à Pharmaprix. Je peux répondre à vos besoins en matière de beauté et vous aider à trouver ce que vous cherchez\"",
            ]
        ]
    ]
];

$configs['mobile.login.store.is_enabled'] = false;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;

///////////// ---- IMPORTER ----- //////////////////
$configs['products.expanded_variants.enabled'] = true;

// ==== IMPORT CONTACTS ===
$configs['importer.contacts.tag_delimiter'] = [';'];

// === Exporters ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

$configs['exporter.encryption.type'] = 'pgp'; // will affect all exporters
$configs['crypto.outbound.general.public_key'] =
    $configs['env'] == Loader::ENV_STACK_PROD
    ? PATH_PLATFORM . '/services/keys/shoppers/sf-pgp-public-shoppers-prod.asc'
    : PATH_PLATFORM . '/services/keys/shoppers/sf-pgp-public-shoppers-stg.asc';
$configs['exporter.contact_for_sync.encryption.enabled'] = true;

$configs['exporter.activitySummary.rep.daily.enabled']             = true;
$configs['exporter.activitySummary.rep.daily.start_days_before']   = -8;
$configs['exporter.activitySummary.rep.daily.end_days_before']     = -2;
$configs['exporter.rep_activity_summary.encryption.enabled']       = true;

$configs['exporter.activitySummary.store.daily.enabled']           = true;
$configs['exporter.activitySummary.store.daily.start_days_before'] = -8;
$configs['exporter.activitySummary.store.daily.end_days_before']   = -2;
$configs['exporter.store_activity_summary.encryption.enabled']     = true;

$configs['exporter.live_chat_metrics.rep.daily.enabled']           = true;
$configs['exporter.live_chat_metrics.rep.daily.start_days_before'] = -8;
$configs['exporter.live_chat_metrics.rep.daily.end_days_before']   = -2;
$configs['exporter.rep_live_chat_metrics.encryption.enabled']      = true;

$configs['exporter.live_chat_metrics.store.daily.enabled']           = true;
$configs['exporter.live_chat_metrics.store.daily.start_days_before'] = -8;
$configs['exporter.live_chat_metrics.store.daily.end_days_before']   = -2;
$configs['exporter.store_live_chat_metrics.encryption.enabled']      = true;


// php ExportRequest {retailer}-dev
$configs['exporter.request.daily.enabled']           = true;
$configs['exporter.request.daily.start_days_before'] = -8;
$configs['exporter.request.daily.end_days_before']   = -2;
$configs['exporter.request.encryption.enabled']      = true;

// php  ExportSmsLog.php {retailer}-dev
$configs['exporter.sms.daily.enabled']           = true;
$configs['exporter.sms.daily.start_days_before'] = -8;
$configs['exporter.sms.daily.end_days_before']   = -2;
$configs['exporter.text.encryption.enabled']     = true;

//  php ExportChatLogDaily.php {retailer}-dev
$configs['exporter.livechat.daily.enabled']           = true;
$configs['exporter.livechat.daily.start_days_before'] = -8;
$configs['exporter.livechat.daily.end_days_before']   = -2;
$configs['exporter.live_chat.encryption.enabled']     = true;

// php CreateTransactionDailyExports {retailer}-dev
$configs['exporter.transactions.daily.enabled']                   = true;
$configs['exporter.transactions.daily.start_days_before']         = -8;
$configs['exporter.transactions.daily.end_days_before']           = -2;
$configs['exporter.daily_transaction.encryption.enabled']         = true;
$configs['exporter.daily_transaction_details.encryption.enabled'] = true;

$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

// === Tasks ===
$configs['sf.task.auto_dismiss.enabled'] = true;
$configs['sf.task.auto_dismiss.settings'] = [
    [
        'happen_after_event' => TaskAutoDismissProcessor::HAPPEN_AFTER_REMINDER,
        'days_after_event'   => 30,
        'automated_types'    => TaskAutoDismissProcessor::DEFAULT_AUTOMATED_TYPES
    ],
];
$configs['sf.task.automated.transaction.min'] = 30;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 7;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 4;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 45;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 4;

$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 7 ];
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 4;

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 7;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 12;

$configs['sf.task.automated.transactions_distribution_by_stores.max_per_owner'] = 12;

/** Clienteling section */
$configs['retailer.clienteling.mode'] = true;

// CI Customers encrypted file
$configs['sf.import_ci_customers.encrypted'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'pharmaprix-customers-\d{8}-\d{6}\.csv\.pgp';

$configs['retailer.customer_tags.is_enabled'] = true;

$configs['retailer.backoffice.pii.is_visible'] = false;

$configs['importer.buffer_size'] = 2621440; // 2.5MiB

$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
$configs['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24 * 5;

$configs['redirect_whitelist.is_enabled'] = true;

$configs['redirect_whitelist.domains'] = [
    'shoppersdrugmart.ca',
    'beautyboutique.ca',
    'image.s5a.com',
    'thebay.com',
    'gcp-blue-shoppersdrugmart.lblw.cloud',
    'bb-preprod.lblw.cloud',
    'loblaw.ca',
    'pcoptimum.ca',
    'pchealth.ca',
    'shoppersphoto.ca',
    'lifebrand.ca',
];

$configs['retailer.i18n.is_enabled'] = true;
$configs['sf.i18n.locales'] = ['en_US', 'fr_CA'];

$configs['retailer.i18n.widgets.sync.method'] = ['jsGlobal'];
$configs['retailer.i18n.widgets.sync.jsGlobal.fr_CA'] = 'fr_CA';
$configs['retailer.i18n.widgets.sync.jsGlobal.en_US'] = 'en_US';
$configs['retailer.i18n.widgets.sync.jsGlobal.function'] = 'documentLang';
$configs['retailer.i18n.widgets.sync.jsGlobal.fallback'] = 'en';
$configs['importer.reps.enabled'] = true;


// === SSO CONFIGS ===
$configs['retailer.sso.is_enabled'] = true;
$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://graph.microsoft.com/oidc/userinfo';

$configs['retailer.sso.provider.tenant'] =
    $configs['env'] == Loader::ENV_STACK_PROD
    ? 'eaa6cb52-58d7-45cd-8bd6-b1d2a8e61312'
    : '55ab44f3-be48-4bbb-bb8d-a860780fd8a8';
$configs['retailer.sso.provider.client_id'] =
    $configs['env'] == Loader::ENV_STACK_PROD
    ? '94ea6e6f-7ee5-42a8-96e7-6c72015eb22a'
    : 'c1f54022-fe4b-4d44-a1c6-7d59c3b7f3ad';
$configs['retailer.sso.provider.client_secret'] =
    $configs['env'] == Loader::ENV_STACK_PROD
    ? '****************************************'
    : '****************************************';

$configs['wordpress.js.iframes.appointment.height'] = 1060;

$configs['sf.import_unsubs.s3.path'] = 'inbound/{env}/unsubscribes/';
$configs['sf.import_unsubs.s3.region'] = 'us-east-1';
$configs['sf.import_unsubs.s3.signature'] = 'v4';

$configs['importer.text_message_unsubscribe.s3.path'] = 'inbound/{env}/unsubscribes/';

$configs['retailer.sso.identity'] = \Salesfloor\Services\Oauth2\Oauth2Service::IDENTITY_UPN;

$configs['retailer.clienteling.customer.sync'] = true;

$configs['crypto.inbound.general.private_key'] =
    $configs['env'] == Loader::ENV_STACK_PROD
    ? PATH_PLATFORM . '/services/keys/sf-gpg-private.key'
    : PATH_PLATFORM . '/services/keys/shoppers/sf-gpg-private-shoppers-stg.asc';

$configs['retailer.store_appointment_hours.is_enabled'] = true;
$configs['retailer.store_appointment_hours.group_permissions'] = [2,3];
$configs['retailer.store_appointment_hours.first_day_of_week'] = 0;

// PS-5591 toggle off this feature temporarily to prove API is not leaking
// $configs['retailer.add_customer_to_my_contacts.is_enabled'] = true;

$configs['retailer_customer.associate_relationships'] = false;

$configs['mobile_ios_channel'] = 's3';

$configs['importer.customer_remapping.encrypted'] = true;

$configs['sf.import_unsubs.s3.bucket'] =
    $configs['env'] == Loader::ENV_STACK_PROD
    ? 'sf-pharmaprix'
    : 'sf-dev-pharmaprix';

// === Chat moderation ===
$configs['retailer.moderation.text.is_enabled'] = true;
$configs['hive.global.multilevel.threshold'] = 1;

// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = 'BBMHV6Z130LgSZvhQjHP';
$configs['connect2.saas.brandId'] = 'jLN7oH921LKnGdmvWgd2';
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = '2211191a-2fcd-4a97-aec9-76b71a50968b';
        $configs['typhoon.url'] = 'https://prod-shoppers.salesfloor.net/typhoon';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = '9be5e346-04f7-4d7e-a234-145e6276fc78';
        $configs['typhoon.url'] = 'https://box01.develop.salesfloor.net/typhoon';
        break;
    case 'box02':
        $configs['connect2.bot.integrationId'] = 'c4fdcac8-5355-4016-9205-50760b3249f7';
        $configs['typhoon.url'] = 'https://box02.develop.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = 'c04e3aa2-2b58-435c-863c-227880517da1';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = '1b8496c3-4b3b-4304-a1c7-7f0b1d50b796';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'qa06':
        $configs['connect2.bot.integrationId'] = 'fdc19655-1839-48e8-be77-205a15e8e76e';
        $configs['typhoon.url'] = 'https://qa06.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = '68a38f81-c28a-467a-83be-b88f00a45d93';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web'] = 'UA-194701786-5';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-194701786-6';
    $configs['retailer.google.ga_services'] = 'UA-194701786-4';
    $configs['retailer.google.ga_storefront'] = 'UA-194701786-8';
    $configs['retailer.google.ga_socialshop'] = 'UA-194701786-9';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-194701786-7';

    // === CUSTOMER INSIGHT
    $configs['importer.customer_remapping.s3.filename_regexp'] = '{retailer}-prd-mapping-\d{8}-\d{6}\.csv\.pgp';

    $configs['logrotate.s3.bucket'] = 'sf-shoppers-platform-logs';
    $configs['logrotate_client.s3.bucket'] = 'sf-shoppers-logs';
    $configs['logrotate_client.s3.key'] = '********************';
    $configs['logrotate_client.s3.provider'] = 'aws';
    $configs['logrotate_client.s3.region'] = 'us-east-1';
    $configs['logrotate_client.s3.secret'] = 'YILcd+etV23xu40lTHmWhYnKplxCBhYyAHYx4uTe';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://services.pharmaprix.ca';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'pharmaprix' : "{$configs['retailer.short_name']}-{$configs['env']}";
