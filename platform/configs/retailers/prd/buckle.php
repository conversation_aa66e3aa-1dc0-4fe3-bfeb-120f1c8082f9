<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Oauth2\Provider\BaseOauth2Provider;
use Salesfloor\Services\Tasks\Features\TaskAutoDismissProcessor;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 3; // 3 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = false;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'buckle';
$configs['retailer.short_name'] = 'buckle';
$configs['retailer.brand_name'] = 'Buckle';
$configs['retailer.pretty_name'] = 'Buckle';

$configs['retailers.name'] = 'buckle';
$configs['retailers.short'] = 'buckle';

$configs['firebase.retailername'] = 'buckle';

$configs['retailer.application_start_date'] = '2020-07-08 00:00:00';

$configs['retailer.hq_address'] = '2407 W. 24th Street, Kearney, NE 68845';
$configs['retailer.hq_address.street'] = '2407 W. 24th Street';
$configs['retailer.hq_address.city'] = 'Kearney';
$configs['retailer.hq_address.region'] = 'NE';
$configs['retailer.hq_address.isoCountry'] = 'US';
$configs['retailer.hq_address.postalCode'] = '68845';
$configs['retailer.hq_address.customerName'] = 'The Buckle, Inc';

$configs['stores.max_distance'] = '64.37'; // distance 64.37 km = 40 miles

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.buckle.com';
$configs['retailer.site'] = 'https://www.buckle.com';
$configs['retailer.domain'] = 'buckle.com';
$configs['retailer.contacturl'] = 'https://buckle.zendesk.com/hc/en-us/requests/new';
$configs['retailer.email_domain'] = 'buckle.com';
$configs['retailer.storefront.search_url'] = 'https://www.buckle.com/search:';

$configs['retailers.url'] = 'https://www.buckle.com';

// === PRODUCTS ===
$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\\' . ucfirst('buckle');
$configs['update_products.max_errors'] = 210000;
$configs['update_products.min_products'] = 10000;
$configs['update_products.min_categories'] = 80;

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;

$configs['importer.products.file_pattern'] = 'TO BE REPLACED';

$configs['products.expanded_variants.enabled'] = true;
$configs['importer.products.has_variants'] = true;

// === Grouped Products ===
$configs['retailer.grouped_products.is_enabled'] = true;
$configs['retailer.grouped_products.max_products'] = 10;
$configs['retailer.grouped_products.min_products'] = 2;
$configs['retailer.grouped_products.url'] = 'https://www.buckle.com/styled';

// === Inventory Lookup ===
$configs['inventory.lookup.bearer.token.url'] = '';
$configs['inventory.lookup.bearer.token.client_id'] = '';
$configs['inventory.lookup.bearer.token.client_secret'] = '';
$configs['inventory.lookup.bearer.token.grant_type'] = '';
$configs['inventory.lookup.basic.auth.username'] = 'salesfloor';
$configs['inventory.lookup.basic.auth.password'] = $configs['env'] === 'prd' ? 'jah3maiNge0bahm4' : 'ahsae8iYae4mo4ae';

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 12;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 12, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 8, 'show_comments' => 1],
    ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 0],
];

$configs['retailer.associate'] = 'Teammate';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "Going Fast";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 8, 'max_posts' => 8];

$configs['retailer.instagram.account'] = 'buckle';
$configs['retailer.instagram.media.count'] = 8;

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/buckle/buckle-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/buckle/buckle-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.appointment.types'] = [
    [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => true ],
    [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
];

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

$configs['retailer.services.findarep.is_enabled'] = true;

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;
$configs['retailer.services.carousel.display_mode'] = 'carousel';

// === MOBILE ===
$configs['mobile.phone_call.is_enabled'] = false;
$configs['mobile.email_me_label'] = 'Contact Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/buckle/buckle_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/buckle/buckle-logo-onboarding.png';

$configs['mobile.quick_responses'] = [[
  'short' => 'Contact Guest Services',
  'full' => "For this, I'd recommend reaching out to our Guest Services team. You can call them at **************. Let me know if other questions come up in the meantime!"
], [
  'short' => "Give me a moment",
  'full' => "Thanks for your patience, I'm checking into this right now.​",
], [
  'short' => "Thank you and goodbye",
  'full' => "Thank you for reaching out! Looking forward to shopping together again soon.",
], [
  'short' => "Out of stock",
  'full'  => "We don't currently have this in-store, but we do have it online! Shop it​ <a href='retailer_link' target='_blank'>here</a>.",
], [
  'short' => "My Storefront Link",
  'full' => "storefront_link",
], [
  'short' => "Subscribe Link",
  'full' => "subscribe_link",
], [
  'short' => "Appointment Link",
  'full' => "appointment_link",
]];

$configs['mobile.extra_quick_responses'] = [[
  'short' => 'Contact Guest Services',
  'full' => "For this, I'd recommend reaching out to our Guest Services team. You can call them at **************. Let me know if other questions come up in the meantime!"
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = true;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hey! I'm a teammate from Buckle. Please don't hesitate to ask me questions whether shopping in-store or here online!\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
   }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
   }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;
$configs['retailer.shoppage.tracking.custom_params'] = [
    'utm_source' => 'salesfloor',
];

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;

$configs['retailer.store_text_hours'] = [
  [
    'open'  => '8',
    'close' => '21'
  ],
];

// Customer Insights
$configs['retailer.clienteling.mode'] = true;
$configs['retailer.clienteling.enabled.customer_stats'] = true;
$configs['retailer.clienteling.enabled.transactions'] = true;

//The system stops sending the share email or 1 to many sms within the blackout period.
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
//Retailer customer blackout period in seconds
$configs['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24 * 10;
//Retailer CI transaction attribution search back in milliseconds => (x days * 24 * 3600 * 1000)
$configs['retailer.clienteling.transactions.attribution.milliseconds_search_back'] = 14 * 24 * 3600 * 1000;
$configs['retailer.clienteling.customers.communication.blackout.all_matched_customers'] = true;

// request from PS-7306
$configs['retailer.clienteling.customers.communication.blackout.strategy'] = \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER;

$configs['retailer.available_services'] = [
  [ 'type' => 'inscription', 'modal' => 'iframe', 'maxHeight' => 650 ], //get my updates
  [ 'type' => 'question', 'modal' => 'iframe', 'maxHeight' => 900 ], //contact form
  [ 'type' => 'finder', 'modal' => 'iframe', 'maxHeight' => 900 ], //personal shopper
  [ 'type' => 'appointment', 'modal' => 'iframe', 'maxHeight' => 1500 ], //appointment
  [ 'type' => 'profile', 'modal' => 'embedded', 'maxHeight' => 465 ], //about me
];

$configs['retailer.services.hide_optional_phone_input'] = true;


// Task
$configs['retailer.group_tasks.is_enabled'] = true;
$configs['sf.group_tasks.reminder.notifications_enabled'] = true;
$configs['retailer.label.group-tasks'] = 'Store Tasks';


$configs['sf.task.auto_dismiss.enabled'] = true;
$configs['sf.task.auto_dismiss.settings'] = [
    [
        'happen_after_event' => TaskAutoDismissProcessor::HAPPEN_AFTER_REMINDER,
        'days_after_event'   => 45,
        'automated_types'    => TaskAutoDismissProcessor::DEFAULT_AUTOMATED_TYPES
    ],
];
$configs['mobile.dashboard.task_cutoff_days_back'] = 45;

// Nag Task
$configs['sf.task.automated.nag_update_storefront_products.enabled']      = false;
$configs['sf.task.automated.nag_share_update.enabled']                    = false;
$configs['sf.task.automated.nag_onboarding_update_about_me.enabled']      = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.enabled']   = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.enabled']         = false;

// CI Transaction Follow-Up Task
$configs['sf.task.automated.retailer_customer_stats_registry_followup.enabled'] = true;
$configs['sf.task.automated.transaction.min'] = 50;
$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 3;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 14 ];

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 3;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 30;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.notifications_enabled'] = true;

// CI Soon-to-Lapse
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
    'default' => [
        'days_search_back' => [45],
        'max_per_owner'    => 5,
    ],
];

// === C2C TAG DELIMITER CONFIG === // Buckle uses semicolon for CI customer tags; this needs to be set to allow c2c importer to import tags to customers
$configs['importer.contacts.tag_delimiter'] = [';'];
$configs['retailer.customer_tags.is_enabled'] = true;
$configs['retailer.customer_tags.is_read_only'] = true;

$configs['retailer.contacts.unsubscribe_automated.enabled'] = true;
$configs['retailer.contacts.unsubscribe_automated.days_threshold'] = 45;

$configs['retailer.consent_required.mobile'] = true;
$configs['retailer.consent_required.desktop'] = true;

$configs['retailer.shop_feed.enabled'] = true;

// ==== SSO ====
$configs['importer.reps.enabled'] = true;
$configs['retailer.prefix_numeric_usernames'] = true;
$configs['retailer.sso.is_enabled'] = true;
$configs['retailer.sso.identity'] = \Salesfloor\Services\Oauth2\Oauth2Service::IDENTITY_UPN;
$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://graph.microsoft.com/oidc/userinfo';
$configs['retailer.sso.provider.tenant'] = 'bbc8a6d4-9728-46e7-a50d-4d1a955de989';

$configs['retailer.sso.login.prompt'] = BaseOauth2Provider::PROMPT_LOGIN;



// Application (client) ID
$configs['retailer.sso.provider.client_id'] =
    ($configs['env'] == Loader::ENV_STACK_PROD
        ? 'c81c7010-188e-4704-8e00-535a38b27083'
        : '3940ac70-3c18-4347-b22d-b83bf698e53f');

// Authentication Credentials Secret Value
$configs['retailer.sso.provider.client_secret'] =
    ($configs['env'] == Loader::ENV_STACK_PROD
        ? '****************************************'
        : '****************************************');


$configs['inventory.lookup.is_enabled'] = true;

$configs['retailer.contacts.consent_required.mobile'] = true;

$configs['redirect_whitelist.is_enabled'] = true;

$configs['redirect_whitelist.domains'] = [
    'bucklecontent.com',
    'buckle.zendesk.com',
    'dev.bke.io',
    'stg.bke.io',
    'd.comenity.net',
];

$configs['importer.tasks.is_enabled'] = true;

$configs['retailer.clienteling.customer.sync'] = true;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;
$configs['exporter.all_contacts_outbound.daily.enabled'] = true;
$configs['exporter.email.stats.daily.enabled'] = true;
$configs['exporter.contacts.daily.enabled'] = true;
$configs['exporter.messages.daily.enabled'] = true;
$configs['exporter.share.email.daily.enabled'] = true;
$configs['exporter.lookbook.daily.enabled'] = true;

$configs['exporter.task.daily.enabled'] = true;
$configs['exporter.task.daily.start_days_before'] = -1;
$configs['exporter.task.daily.end_days_before'] = -1;

$configs['exporter.retailer_customer.daily.enabled'] = true;

$configs['exporter.activitySummary.rep.daily.enabled'] = true;
$configs['exporter.activitySummary.rep.daily.start_days_before'] = -8;
$configs['exporter.activitySummary.rep.daily.end_days_before'] = -2;

$configs['exporter.activitySummary.store.daily.enabled'] = true;
$configs['exporter.activitySummary.store.daily.start_days_before'] = -8;
$configs['exporter.activitySummary.store.daily.end_days_before'] = -2;

$configs['exporter.livechat.daily.enabled'] = true;
$configs['exporter.livechat.daily.start_days_before'] = -8;
$configs['exporter.livechat.daily.end_days_before'] = -2;

$configs['exporter.live_chat_metrics.rep.daily.enabled'] = true;
$configs['exporter.live_chat_metrics.rep.daily.start_days_before'] = -8;
$configs['exporter.live_chat_metrics.rep.daily.end_days_before'] = -2;

$configs['exporter.live_chat_metrics.store.daily.enabled'] = true;
$configs['exporter.live_chat_metrics.store.daily.start_days_before'] = -8;
$configs['exporter.live_chat_metrics.store.daily.end_days_before'] = -2;

$configs['exporter.request.daily.enabled'] = true;
$configs['exporter.request.daily.start_days_before'] = -8;
$configs['exporter.request.daily.end_days_before'] = -2;

$configs['exporter.sms.daily.enabled'] = true;
$configs['exporter.sms.daily.start_days_before'] = -1;
$configs['exporter.sms.daily.end_days_before'] = -1;

$configs['exporter.transactions.daily.enabled'] = true;
$configs['exporter.transactions.daily.start_days_before'] = -8;
$configs['exporter.transactions.daily.end_days_before'] = -2;




$configs['importer.rep_transactions.enabled'] = true;

$configs['products.defaultcomment_display.is_enabled'] = false;

$configs['inventory.lookup.retailer.endpoint'] = 'https://salesfloor-inventory.stg.bke.io/api/inventory';

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    $configs['inventory.lookup.retailer.endpoint'] = 'https://salesfloor-inventory.prd.bke.io/api/inventory';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-173183064-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-173183064-2';
    $configs['retailer.google.ga_services']          = 'UA-173183064-3';
    $configs['retailer.google.ga_storefront']        = 'UA-173183064-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-173183064-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-173183064-6';

    // TODO: Are we keeping those "prd" debugging stuff ?! (Probably not)
    $configs['monolog.debug.tickets'] = [
        'SF-26243'
    ];

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MGd068b7a106a05ed2aa73a604c637c487';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MGd7efc5e3d228ebc69f5b280344f8fdae';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://mystore.buckle.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'buckle' : "{$configs['retailer.short_name']}-{$configs['env']}";
