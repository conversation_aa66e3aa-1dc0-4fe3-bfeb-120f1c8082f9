<?php

use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Tasks\Features\TaskAutoDismissProcessor;
use Salesfloor\Services\Oauth2\Provider\BaseOauth2Provider;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 7; // 7 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'belk';
$configs['retailer.short_name'] = 'belk';
$configs['retailer.brand_name'] = 'Belk';
$configs['retailer.pretty_name'] = 'Belk';

$configs['retailers.name'] = 'belk';
$configs['retailers.short'] = 'belk';

$configs['firebase.retailername'] = 'belk';

$configs['retailer.application_start_date'] = '2024-07-24 00:00:00';

$configs['retailer.hq_address'] = '2801 W. Tyvola Rd., Charlotte, NC 28217-4500';
$configs['retailer.hq_address.street'] = '2801 W. Tyvola Rd.';
$configs['retailer.hq_address.city'] = 'Charlotte';
$configs['retailer.hq_address.isoCountry'] = 'US';
$configs['retailer.hq_address.postalCode'] = 'NC 28217-4500';
$configs['retailer.country.code'] = 'US';
$configs['stores.max_distance'] = ''; // distance in km
$configs['stores.max_distance.per_store.enable'] = '@@maxDistanceRadiusPerStoreEnable@@';  // default set to false

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.belk.com/';
$configs['retailer.site'] = 'https://www.belk.com/';
$configs['retailer.domain'] = 'belk.com';
$configs['retailer.contacturl'] = 'https://www.belk.com/customer-service/';
$configs['retailer.email_domain'] = 'belk.com';
$configs['retailers.url'] = 'https://www.belk.com/';

// === PRODUCTS ===
$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\\' . ucfirst('belk');
$configs['update_products.max_errors'] = 2500;
//$configs['update_products.min_products'] = 5000; // todo: revert all Envs' value back to 5000 after we get full feed at UAT
$configs['update_products.min_products'] = $configs['env'] == Loader::ENV_STACK_PROD ? 5000 : 0;
$configs['update_products.min_categories'] = 25;
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['products.is-price-displayed'] = false;

// === STOREFRONT ===
$configs['retailer.num_deals'] = 4;
$configs['retailer.num_top_picks'] = 4;
$configs['use_new_arrivals_list'] = false;

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'belk.app.link',
    'belkcareers.com',
    'belk.cashstar.com',
    'synchrony.com',
];

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'min_products' => 4, 'max_products' => 4, 'show_comments' => 1],
  ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1],
];

$configs['products.defaultcomment_display.is_enabled'] = false;

$configs['retailer.associate'] = 'Associate';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 0, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'belk';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/belk/belk-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/belk/belk-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// ==== IMPORT USERS ===
$configs['importer.reps.enabled'] = true;
$configs['sf.import_users.encrypted'] = true;
$configs['importer.reps.s3.filename_regexp'] = 'belk-users-\d{8}-\d{6}\.csv\.pgp';

// === SSO CONFIGS ===
$configs['retailer.sso.is_enabled'] = true;
$configs['retailer.sso.login.prompt'] = BaseOauth2Provider::PROMPT_LOGIN;

// SSO Okta config
$configs['retailer.sso.provider.type'] = \Salesfloor\Services\Oauth2\Provider\OktaOauth2Provider::OAUTH2_PROVIDER;
$configs['retailer.sso.identity']      = \Salesfloor\Services\Oauth2\Provider\OktaOauth2Provider::IDENTITY_UPN;

if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['retailer.sso.provider.server']                = 'https://belk.okta.com';
    $configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://belk.okta.com/oauth2/v1/userinfo';
    $configs['retailer.sso.provider.client_id']             = '0oa1umcsfy38NKSFp1d8';
    $configs['retailer.sso.provider.client_id.mobile']      = '0oa1umcvd90FuAOyH1d8';
    $configs['retailer.sso.provider.client_secret']         = 'KJ5kRe7Vd01pacrz720CrTYBGF042YNJZGmo2wDZg6UZHCKoughyqUMLBZEkFolY';
} else { // all lower env
    $configs['retailer.sso.provider.server']                = 'https://belk.oktapreview.com';
    $configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://belk.oktapreview.com/oauth2/v1/userinfo';
    $configs['retailer.sso.provider.client_id']             = '0oajm15baeJNwaH0B1d7';
    $configs['retailer.sso.provider.client_id.mobile']      = '0oaj7nzelf5AQSFCp1d7';
    $configs['retailer.sso.provider.client_secret']         = 'KDBWOAvglSFWJTg1_MjQo648ayonIHnwj6rT6UpKIq8UMtfCrVK5KWUCzl8qDYw7';
}

// === SERVICES ===
$configs['queue.byStore'] = 'all';

$configs['retailer.services.appointment.types'] = [
  ['type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false],
  ['type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false],
];

// === APPOINTMENT MANAGEMENT ===
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
$configs['retailer.services.appointment.duration'] = 30;    // 30 min

$configs['retailer.store_appointment_hours'] = [
  [
    'open'  => '11',
    'close' => '19',
    'is_available' => '1', // 0 or 1
  ],
];

$configs['retailer.services.findarep.is_enabled'] = true;

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'right';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// === MOBILE ===
$configs['mobile.retailer_has_chat'] = false;
$configs['mobile.email_me_label'] = 'Contact Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/belk/belk_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/belk/belk-logo-onboarding.png';

$configs['mobile.quick_responses'] = [];

$configs['mobile.extra_quick_responses'] = [[
  'short' => 'Customer support',
  'full' => 'Our customerServiceTeamName team can help you with this request. Please give them a call at customerServicePhone. Thank you!'
], [
  'short' => 'Give me a moment',
  'full' => 'Please give me a moment and I will look into this for you.'
], [
  'short' => 'Thank you and goodbye',
  'full' => 'Thank you for reaching out to us. Have a wonderful day!'
], [
  'short' => 'Out of stock',
  'full' => 'Unfortunately this item is not currently available in our store, but you can buy it online here:'
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.barcode_scanner.contacts.enabled'] = false;
$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;

$configs['mobile.retailer_can_browse_library'] = false;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"As a Belk associate, I am passionate about connecting people to products that will make them look and feel great. I would love for you to come shop with me!\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "true",
    "import-contact": "false",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "true",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

$configs['mobile.retailer_has_specialties'] = true;
$configs['retailer.specialties.is_enabled'] = true;
$configs['retailer.specialties.can_select'] = true;

$configs['retailer.specialties.filter'] = \Salesfloor\API\Managers\Categories::SPECIALTIES_FILTER_ALGORITHM_INCLUDE_ONLY;

$configs['retailer.specialties.include_only'] = [
    'Jewelry',
    'Beauty',
    'Handbags',
];

$configs['retailer.specialties.appending'] = [
    'en_US' => [
        'Handbags' => [
            "id"     => "Handbags",
            "name"   => "Handbags",
            "parent" => 1,
        ],
    ],
];

$configs['retailer.add_customer_to_my_contacts.is_enabled'] = true;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;
$configs['retailer.shoppage.tracking.custom_params'] = [
    'channelStoreId'     => '\Salesfloor\Services\ShopPage\RetailerRule\Belk\CustomUrlParam::base64EncodedRetailerStoreId',
    'channelAssociateId' => '\Salesfloor\Services\ShopPage\RetailerRule\Belk\CustomUrlParam::base64EncodedAssociateId',
    'channelType'        => '\Salesfloor\Services\ShopPage\RetailerRule\Belk\CustomUrlParam::base64EncodedChannelType',
];

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;

// === CUSTOMER INSIGHT ===
$configs['retailer.clienteling.mode'] = true;
$configs['retailer.clienteling.customer.sync'] = true;

// === CUSTOMER COMMUNICATION BLACKOUT ===
// The system stops sending the share email or 1 to many sms within the blackout period.
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;

// Retailer customer blackout period in seconds
$configs['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24 * 3;

$configs['retailer.clienteling.customers.communication.blackout.all_matched_customers'] = true;

// === IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = true;
$configs['retailer.onboarding.step.add_contacts'] = false;
$configs['retailer.modular_connect.can_add_contacts'] = true;

$configs['retailer.customer_tags.is_enabled'] = true;
$configs['retailer.customer_tags.is_read_only'] = false;

// ==== IMPORT CUSTOMERS ===
$configs['sf.import_ci_customers.encrypted'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = "belk-customers-\d{8}-\d{6}\.csv\.pgp";

// ==== IMPORT CI Transactions ===
$configs['retailer.clienteling.enabled.transactions'] = true;
$configs['sf.import_ci_transactions.encrypted'] = true;
$configs['sf.import_ci_transactions.s3.filename_regexp'] = 'belk-transactions-\d{8}-\d{6}\.csv\.pgp';
$configs['retailer.transaction.show_product_color_as_upc'] = true;

// ==== IMPORT CI stats ===
$configs['retailer.clienteling.enabled.customer_stats'] = true;
$configs['sf.import_ci_stats.encrypted'] = true;
$configs['sf.import_ci_stats.s3.filename_regexp'] = 'belk-statistics-\d{8}-\d{6}\.csv\.pgp';

// == IMPORT Rep Transactions(Reconciliation)
$configs['importer.rep_transactions.enabled'] = true;
$configs['importer.rep_transactions.skip_stats'] = true;

$configs['importer.tasks.is_enabled'] = true;

// === EXPORTER settings ===
$configs['exporter.contacts.daily.enabled']    = true;
$configs['exporter.messages.daily.enabled']    = true;
$configs['exporter.share.email.daily.enabled'] = true;
$configs['exporter.email.stats.daily.enabled'] = true;

// === EXPORTERS ===
// 2-way Contact export
// php ExportContactForSync.php {retailer}-dev
$configs['exporter.contact_for_sync.encryption.enabled'] = true;

// Activity Summary - Associates CSV
// php ExportRepActivitySummary.php {retailer}-dev
$configs['exporter.activitySummary.rep.daily.enabled']             = true;
$configs['exporter.activitySummary.rep.daily.start_days_before']   = -2;
$configs['exporter.activitySummary.rep.daily.end_days_before']     = -2;
$configs['exporter.rep_activity_summary.encryption.enabled']       = true;

// Activity Summary - Stores CSV
// php ExportStoreActivitySummary.php {retailer}-dev
$configs['exporter.activitySummary.store.daily.enabled']            = true;
$configs['exporter.activitySummary.store.daily.start_days_before']  = -2;
$configs['exporter.activitySummary.store.daily.end_days_before']    = -2;
$configs['exporter.store_activity_summary.encryption.enabled']      = true;

// Live Chat Metrics - Associates CSV
// php ExportDailyRepLiveChatMetrics.php {retailer}-dev
$configs['exporter.rep_live_chat_metrics.encryption.enabled']      = true;
$configs['exporter.live_chat_metrics.rep.daily.start_days_before'] = -2;
$configs['exporter.live_chat_metrics.rep.daily.end_days_before']   = -2;
$configs['exporter.rep_live_chat_metrics.encryption.enabled']      = true;

// Download SMS Logs CSV
// php ExportSmsLog.php {retailer}-dev // cron is enabled as default
$configs['exporter.sms.daily.enabled']           = true;
$configs['exporter.sms.daily.start_days_before'] = -2;
$configs['exporter.sms.daily.end_days_before']   = -2;
$configs['exporter.text.encryption.enabled']     = true;

// Download Live Chat Logs CSV
// php ExportChatLogDaily.php {retailer}-dev
$configs['exporter.livechat.daily.enabled']           = true;
$configs['exporter.livechat.daily.start_days_before'] = -2;
$configs['exporter.livechat.daily.end_days_before']   = -2;
$configs['exporter.live_chat.encryption.enabled']     = true;

// Transaction summary
// php CreateTransactionDailyExports.php {retailer}-dev
$configs['exporter.transactions.daily.enabled']                   = true;
$configs['exporter.transactions.daily.start_days_before']         = -2;
$configs['exporter.transactions.daily.end_days_before']           = -2;
$configs['exporter.daily_transaction.encryption.enabled']         = true;
$configs['exporter.daily_transaction_details.encryption.enabled'] = true;

// Task Exporter
// php ExportTask.php {retailer}-dev
$configs['exporter.task.daily.enabled'] = true;
$configs['exporter.task.daily.start_days_before'] = -1;
$configs['exporter.task.daily.end_days_before'] = -1;

// Request Exporter
// php ExportRequest {retailer}-dev
$configs['exporter.request.daily.enabled'] = true;
$configs['exporter.request.encryption.enabled'] = true;

// Retailer Transaction Attribution Exporter
// ./robo ci:transaction-attribution:export {retailer}-dev
$configs['exporter.transactions.attribution.outbound.enabled'] = true;
$configs['exporter.transactions.attribution.outbound.encryption.enabled'] = true;

$configs['exporter.transactions.attribution.daily.enabled'] = true;

$configs['retailer.clienteling.transactions.attribution.addition_transaction_type'] = [
    [Salesfloor\Models\RetailerTransaction::TYPE_RETURN, Salesfloor\Models\RetailerTransaction::TYPE_CANCELLATION],
];

// === EXPORTER - ASSOCIATE REPORT ===
$configs['exporter.associate_report.encryption.is_enabled'] = true;

// === EXPORTER - USER MANAGEMENT ===
$configs['exporter.user_management.encryption.is_enabled'] = true;

// === VIDEO CHAT ===
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;
$configs['retailer.virtual.option.video-chat'] = false;
$configs['retailer.virtual.option.video-chat.2ways'] = false;

$configs['retailer.pii.obfuscate.is_enabled'] = true;

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://connect.belk.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'belk' : "{$configs['retailer.short_name']}-{$configs['env']}";

// === TASKS ===

// CI Sale Transaction Follow-Up (Attributed Associate/Store)
$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 5;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [];
$configs['sf.task.automated.retailer_transaction_employee_assigned.emails_enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [];
$configs['sf.task.automated.retailer_transaction_employee_assigned.dynamic_rule'] = [
    '2DaysBack' => [ // type 1 from PS-6231
        'days_search_back' => [2],
        'max_per_owner'    => 5,
        'task_details_key' => 'api_automated_tasks_detail_retailertransactionemployeeassigned_for_thank_you',
    ],
    '14DaysBack' => [ // type 2 from PS-6231
        'days_search_back' => [14],
        'max_per_owner'    => 5,
        'task_details_key' => 'api_automated_tasks_detail_retailertransactionemployeeassigned_for_check_satisfaction',
    ],
    'filterBySkincare' => [ // type 3 from PS-6231
        'days_search_back' => [30],
        'max_per_owner'    => 5,
        'task_details_key' => 'api_automated_tasks_detail_retailertransactionemployeeassigned_for_replenishment_follow_up',
        'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Belk\RetailerTransactionEmployeeAssigned::filterBySkinCare',
    ],
    'filterByMakeup' => [ // type 4 from PS-6231
        'days_search_back' => [60],
        'max_per_owner'    => 5,
        'task_details_key' => 'api_automated_tasks_detail_retailertransactionemployeeassigned_for_replenishment_follow_up',
        'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Belk\RetailerTransactionEmployeeAssigned::filterByMakeup',
    ],
    'filterByFragrance' => [ // type 5 from PS-6231
        'days_search_back' => [90],
        'max_per_owner'    => 5,
        'task_details_key' => 'api_automated_tasks_detail_retailertransactionemployeeassigned_for_replenishment_follow_up',
        'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Belk\RetailerTransactionEmployeeAssigned::filterByFragrance',
    ],
];

// https://salesfloor.atlassian.net/wiki/spaces/SB/pages/2476245005/iii.+New+Rep+Transaction+Task+SF+Transactions
$configs['sf.task.automated.new_rep_transaction.enabled']          = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 14;
$configs['sf.task.automated.new_rep_transaction.max_per_owner']    = 5;

// https://salesfloor.atlassian.net/wiki/spaces/SB/pages/1647312897/iv.+CI+Soon+to+lapse
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled']          = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 60;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner']    = 5;

// https://salesfloor.atlassian.net/wiki/spaces/SB/pages/42975920134/VII.+Automated+Task+-+Retailer+Customer+Event
$configs['sf.task.automated.retailer_customer_event.enabled']      = true;
$configs['sf.task.automated.retailer_customer_event.dynamic_rule'] = [
    'filterByBirthday'    => [
        'days_search_back' => [10],
        'max_per_owner'    => 2,
        'task_details_key' => 'api_automated_tasks_detail_retailercustomerevent',
        'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Generic\RetailerCustomerEvent::filterByBirthday',
    ],
    'filterByAnniversary' => [
        'days_search_back' => [20],
        'max_per_owner'    => 2,
        'task_details_key' => 'api_automated_tasks_detail_retailercustomerevent',
        'filter_function'  => '\Salesfloor\Services\Tasks\Automated\RetailerRule\Generic\RetailerCustomerEvent::filterByAnniversary',
    ],
];

// https://salesfloor.atlassian.net/wiki/spaces/SB/pages/42804969478/Task+Auto-dismissal
$configs['sf.task.auto_dismiss.enabled']  = true;
$configs['sf.task.auto_dismiss.settings'] = [
    [
        'happen_after_event' => TaskAutoDismissProcessor::HAPPEN_AFTER_REMINDER,
        'days_after_event'   => 30,
        'automated_types'    => TaskAutoDismissProcessor::DEFAULT_AUTOMATED_TYPES
    ],
];

// === CONNECT 2 ===
// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = 'BBMHV6Z130LgSZvhQjHP';
$configs['connect2.saas.brandId'] = 'Fy5GVNFfdrQ2iPANOJt5';
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = 'b5498372-696b-41ef-b1d9-cb5d52d6a2b1';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = 'ce6685c3-de59-4227-9a16-0d75ad575afb';
        $configs['typhoon.url'] = 'https://box01.sandbox.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = '1331f7db-d791-4588-9751-5e7b0f83a5b5';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = '87a591ec-11bb-4993-9be7-127a7a0a11d2';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'qa06':
        $configs['connect2.bot.integrationId'] = 'd267b965-bd85-47b8-b2c4-9a8a2cbbcba1';
        $configs['typhoon.url'] = 'https://qa06.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = '48251310-0f7f-446a-a27a-41f5c209c826';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // Send Importer Results
    $configs['retailer.clienteling.import.notification.emails'] = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'G-XFELSPFJ46';
    $configs['retailer.google.ga_backoffice_mobile'] = 'G-CBR9FMTS16';
    $configs['retailer.google.ga_services']          = 'G-BTZQB7SDWJ';
    $configs['retailer.google.ga_storefront']        = 'G-KG9C4EQ008';
    $configs['retailer.google.ga_socialshop']        = 'G-B6C0T56RN3';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'G-P7R94BD1B1';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required']               = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MGc637e2bc58f3413c021f7eb504316512';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply']  = 'MGb5eee6dfc1c3fc62f35923d6a0697e12';
}

if ($configs['env'] == 'stg') {
    // Send Importer Results
    $configs['retailer.clienteling.import.notification.emails'] = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required']               = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MGb9ef36588e229d8121a1a42063e4274f';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply']  = 'MGfc082829c43bfd2fb1d597f3355ddb3b';
}

$configs['crypto.inbound.general.private_key'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? PATH_PLATFORM . '/services/keys/belk/sf-gpg-inbound-private-belk-prd.asc'
        : PATH_PLATFORM . '/services/keys/belk/sf-gpg-inbound-private-belk-stg.asc';

$configs['crypto.inbound.general.public_key'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? PATH_PLATFORM . '/services/keys/belk/sf-gpg-inbound-public-belk-prd.asc'
        : PATH_PLATFORM . '/services/keys/belk/sf-gpg-inbound-public-belk-stg.asc';

$configs['crypto.outbound.general.public_key'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? PATH_PLATFORM . '/services/keys/belk/sf-pgp-outbound-belk-prd-public.asc'
        : PATH_PLATFORM . '/services/keys/belk/sf-pgp-outbound-belk-stg-public.asc';
