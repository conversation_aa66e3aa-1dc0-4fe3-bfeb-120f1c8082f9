<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Oauth2\Provider\BaseOauth2Provider;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = true;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 14; // 14 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = false;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'gnc';
$configs['retailer.short_name'] = 'gnc';
$configs['retailer.brand_name'] = 'GNC';
$configs['retailer.pretty_name'] = 'GNC';

$configs['retailers.name'] = 'gnc';
$configs['retailers.short'] = 'gnc';

$configs['firebase.retailername'] = 'gnc';

$configs['retailer.application_start_date'] = '2021-11-09 00:00:00';

$configs['retailer.hq_address'] = '75 Hopper Place, Pittsburgh, PA 15222, USA';
$configs['retailer.country.code'] = 'US';
$configs['retailer.hq_address.street'] = '75 Hopper Place';
$configs['retailer.hq_address.city'] = 'Pittsburgh';
$configs['retailer.hq_address.region'] = "Pennsylvania";
$configs['retailer.hq_address.isoCountry'] = 'US';
$configs['retailer.hq_address.postalCode'] = 'PA 15222';
$configs['retailer.hq_address.customerName'] = 'GNC';

$configs['stores.max_distance'] = '9001'; // National

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.gnc.com/';
$configs['retailer.site'] = 'https://www.gnc.com/';
$configs['retailer.domain'] = 'gnc.com';
$configs['retailer.contacturl'] = 'https://www.gnc.com/help/contact-us.html';
$configs['retailer.email_domain'] = 'TO BE REPLACED';
$configs['retailer.mainSearchUrl'] = 'https://www.gnc.com/search?q=';

$configs['retailers.url'] = 'https://www.gnc.com/';

// === PRODUCTS ===
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;

$configs['importer.products.file_pattern'] = 'TO BE REPLACED';

// === CUSTOMER INSIGHT
$configs['retailer.clienteling.customer.sync'] = true;

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 8;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
    ['type' => 'recommendations', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Coach';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = false;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'rangeme.com',
    'gnclivewellfoundation.org',
];

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 8;
$configs['retailer.trending_recommendations.min'] = 8;
$configs['retailer.trending_recommendations.title'] = "Trending Products";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 8, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'gnclivewell';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/gnc/gnc-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/gnc/gnc-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = "team";
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// ==== IMPORT USERS ===
$configs['importer.reps.enabled'] = true;

// === SSO CONFIGS ===
$configs['retailer.sso.is_enabled'] = true;
$configs['retailer.sso.identity'] = \Salesfloor\Services\Oauth2\Oauth2Service::IDENTITY_UPN;
$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://graph.microsoft.com/oidc/userinfo';
$configs['retailer.sso.provider.tenant'] = 'be78cf63-a876-4474-b824-e76797f248b3';

$configs['retailer.sso.login.prompt'] = BaseOauth2Provider::PROMPT_LOGIN;

// Application (client) ID
$configs['retailer.sso.provider.client_id'] =
    ($configs['env'] == Loader::ENV_STACK_PROD
        ? 'ad370540-ae1b-4c10-88a7-f5ece60be52b'
        : '75990a07-bd8e-47c1-890e-aaf448a992c7');

// Authentication Credentials Secret Value
$configs['retailer.sso.provider.client_secret'] =
    ($configs['env'] == Loader::ENV_STACK_PROD
        ? '****************************************'
        : '****************************************');


// Chat moderation
$configs['retailer.moderation.text.is_enabled'] = true;

$configs['retailer.chat.photo_attachment.is_enabled'] = true;

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.appointment.types'] = [
    [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
    [ 'type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
];

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['service.personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;

$configs['mobile.retailer_has_specialties'] = false;
$configs['retailer.specialties.is_enabled'] = false;
$configs['retailer.specialties.can_select'] = false;
$configs['retailer.specialties.is_required'] = false;

// === SIDEBAR3 VARIANT ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 270;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 270;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = 'https://cdn.salesfloor.net/salesfloor-assets/gnc/gnc_sidebar_animated.gif';
$configs['retailer.sidebar.v3.horizontalPosition'] = 'right';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = false;
$configs['retailer.sidebar.v3.minimize.desktop'] = false;
$configs['retailer.sidebar.v3.minimize.mobile'] = false;

// === MOBILE ===
$configs['mobile.phone_call.is_enabled'] = false;
$configs['mobile.login.store.is_enabled'] = false;

$configs['mobile.email_me_label'] = 'Contact Us';

$configs['mobile.menu_logo_path'] = '/img/retailers/gnc/gnc_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/gnc/gnc-logo-onboarding.png';

// === LIVE CHAT QUICK RESPONSES ===
$configs['mobile.quick_responses'] = [[
    'short' => [
        'en_US' => 'Give me a moment',
        'en_IE' => 'Give me a moment',
        'en_IN' => 'Give me a moment',
        'fr_CA' => 'Un instant',
        'ja_JP' => '少しお時間をください'
    ],
    'full' => [
        'en_US' => 'Please give me a moment and I will look into this for you.',
        'en_IE' => 'Please give me a moment and I will look into this for you.',
        'en_IN' => 'Please give me a moment and I will look into this for you.',
        'fr_CA' => 'S\'il vous plaît, laissez-moi un instant et je vais m\'en occuper pour vous.',
        'ja_JP' => '少しお時間をください、調査させていただきます。'
    ],
], [
    'short' => [
        'en_US' => 'Thank you and goodbye',
        'en_IE' => 'Thank you and goodbye',
        'en_IN' => 'Thank you and goodbye',
        'fr_CA' => 'Merci et au revoir',
        'ja_JP' => 'ありがとうございました。さようなら'
    ],
    'full' => [
        'en_US' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
        'en_IE' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
        'en_IN' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
        'fr_CA' => 'Merci de votre demande. Il me fera plaisir de vous servir à nouveau. Passez une bonne journée.',
        'ja_JP' => 'リクエストありがとうございます。再び、お役に立てることを嬉しく思います。良い一日をお過ごしください。'
    ],
], [
    'short' => [
        'en_US' => 'Out of stock',
        'en_IE' => 'Out of stock',
        'en_IN' => 'Out of stock',
        'fr_CA' => 'Rupture de stock',
        'ja_JP' => '在庫切れ'
    ],
    'full' => [
        'en_US' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
        'en_IE' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
        'en_IN' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
        'fr_CA' => 'Malheureusement, nous sommes en rupture de stock en magasin, mais vous pouvez l\'acheter en ligne en cliquant ici',
        'ja_JP' => '残念ながら、現在この製品の在庫はありませんが、こちら をクリックして、オンラインでご購入になれます'
    ],
], [
    'short' => [
        'en_US' => 'My Storefront Link',
        'en_IE' => 'My Storefront Link',
        'en_IN' => 'My Storefront Link',
        'fr_CA' => 'Lien de ma vitrine web',
    ],
    'full' => 'storefront_link',
], [
    'short' => [
        'en_US' => 'Subscribe Link',
        'en_IE' => 'Subscribe Link',
        'en_IN' => 'Subscribe Link',
        'fr_CA' => 'Lien d\'abonnement',
    ],
    'full' => 'subscribe_link',
]];

$configs['mobile.extra_quick_responses'] = [[
  'short' => 'Customer Service',
  'full' => 'For requests related to customer service, please visit www.gnc.com/help/contact-us.html#chat or call 1-877-GNC-4700.'
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;

$configs['mobile.retailer_can_browse_library'] = false;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "display": "none",
            "value": "username"
        },
        "storefront": "false"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a GNC associate. I can answer your questions and help you find what you’re shopping for at your local store or online.\""
    },
    "out-of-store": "false",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "true",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

// === APPOINTMENT MANAGEMENT ===
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// === VIDEO CHAT ===
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

// === CAROUSEL ===
$configs['retailer.services.carousel.alternate_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/gnc/gnc_logo_service_2x.png';

// == SERVICES GCP/AWS ===
$configs['services.queue'] = 'gcp';
$configs['services.metrics'] = 'stackdriver';
$configs['service.elasticsearch'] = 'gcp';

// === PushNotifications / FCM CONFIGS ===
$configs['service.pushnotification'] = 'fcm';

$configs['s3.bucket.provider']            = 'gcp';
$configs['geoip.s3bucket.provider']       = 'gcp';
$configs['logrotate.s3.provider']         = 'gcp';
$configs['logrotate_client.s3.provider']  = 'gcp';
$configs['rep-export.s3.provider']        = 'gcp';
$configs['sf.import_unsubs.s3.provider']  = 'gcp';

// leave on aws until its fully migrated
$configs['s3.image-bucket'] = 'salesfloor-assets';
$configs['s3.image-bucket.provider']        = 'aws';
$configs['mobile_s3_bucket.provider']       = 'aws';

// === EXPORTER settings ===
// Transaction summary daily
// php CreateTransactionDailyExports.php {retailer}-{stg}
$configs['exporter.transactions.daily.enabled']                     = true;
$configs['exporter.transactions.daily.start_days_before']           = -2;
$configs['exporter.transactions.daily.end_days_before']             = -2;

// ==== IMPORT CUSTOMERS ===
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'gnc-customers-\d{8}-\d{6}\.csv';

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// === TASK ===
$configs['sf.task.auto_dismiss.enabled'] = true;
$configs['sf.task.automated.nag_update_storefront_products.enabled'] = false;

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 14;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 2;

$configs['sf.task.automated.transaction.min'] = 30;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 14;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 4;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 30;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 2;

$configs['retailer.corporate-email.required'] = false;
$configs['update_products.max_errors'] = 125;

$configs['security.policy.password'] = 'zxcvbn';
$configs['security.policy.password.zxcvbn.strength'] = 2;

$configs['exporter.messages.daily.enabled'] = true;
$configs['exporter.share.email.daily.enabled'] = true;

$configs['retailer.chat.option.eject_after_hours'] = true;
$configs['retailer.store.open_hour'] = 8;
$configs['retailer.store.close_hour'] = 21;

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-225848058-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-225848058-2';
    $configs['retailer.google.ga_services']          = 'UA-225848058-3';
    $configs['retailer.google.ga_storefront']        = 'UA-225848058-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-225848058-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-225848058-6';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://coaches.gnc.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'gnc' : "{$configs['retailer.short_name']}-{$configs['env']}";


// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = "BBMHV6Z130LgSZvhQjHP";
$configs['connect2.saas.brandId'] = "0Gu5nB4wcBDfok07QYpY";
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = 'b3827ef9-a19d-408e-a57e-5653da27f681';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = '4e28223b-eb23-4ac1-b14a-560844a3ab68';
        $configs['typhoon.url'] = 'https://box01.develop.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = 'e8bc3ddd-97d3-48bf-95bc-759a12264739';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = '2d4f63c6-2bf9-4fff-9b81-505a99983a5c';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = '2da293c3-e08c-4086-9090-8d79531a8b61';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
$configs['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24 * 2; // 172800
$configs['retailer.clienteling.customers.communication.blackout.strategy'] = \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_CUSTOMER_DATA_ONLY;

$configs['retailer.customer_tags.is_enabled'] = true;
$configs['retailer.customer_tags.is_read_only'] = true;

$configs['retailer.pii.obfuscate.is_enabled'] = true;
