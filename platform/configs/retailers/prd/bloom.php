<?php

/**
 * Bloomindale's Configurations
 *
 * Copyright 2015 - Salesfloor
 */

// === API ===

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Symfony\Component\Validator\Constraints\Date;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\DateTime;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Required;
use Salesfloor\Services\Importer\Product\Importer as ProductImporter;
use Salesfloor\Services\Moderation\Moderator\HiveModerator as HiveModerator;
use Salesfloor\Models\Services\Appointment;

$configs['retailers.name']                  = 'bloom';
$configs['retailer.pretty_name']            = 'Bloomingdale\'s';
$configs['retailer.url']                    = 'http://www.bloomingdales.com';
$configs['retailer.store.default_timezone'] = 'America/New_York';
$configs['storefront.num_top_picks']        = 12;

$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\Bloom';
$configs['update_products.max_errors']     = 10;
$configs['update_products.min_products']   = 80000;
$configs['update_products.min_categories'] = 175;

$configs['bloom.api_key']            = '4gvmaryma5n5uk6z943k9v52';
$configs['cron.event_updater.class'] = '\Salesfloor\Services\BloomEventImporter';

$configs['firebase.retailername'] = 'bloom';
$configs['product_img_size']      = 430;

$configs['retailer.events_from_feed'] = false;
$configs['storefront.store_events'] = ['active' => false];
// We don't want to convert to store timezone
$configs['storefront.store_events.convert_event_times'] = false;
$configs['retailer.storefront.is-new-stack'] = true;

// === WIDGETS ===

/**
 * BLOOM Application Configurations
 *
 * Copyright 2014 - Salesfloor
 */

$configs['retailers.name']              = "bloom";
$configs['retailers.short']             = "bloom";
$configs['retailers.url']               = "http://www.bloomingdales.com";
$configs['retailer.sale_cookie_expire'] = 86400000; // 1 day
$configs['retailer.disable_cookies']    = true;

$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 12, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 4, 'show_comments' => 1],
    /*['type' => 'recommended', 'max_products' => 8, 'min_products' => 2, 'show_comments' => 0]*/
];

// === WEBSERVER ===

$configs['retailer.name']                  = "bloom";
$configs['retailer.brand_name']            = 'Bloomingdale\'s';
$configs['retailer.short_name']            = 'bloom';
$configs['retailer.site']                  = 'www.bloomingdales.com';
$configs['retailer.url']                   = 'http://www.bloomingdales.com/';
$configs['retailer.contacturl']            = 'https://www.customerservice-bloomingdales.com/app/contact/';
$configs['retailer.domain']                = 'bloomingdales.com';
$configs['retailer.email_domain']          = 'bloomingdales.com';
$configs['retailer.product_url']           = 'http://www.bloomingdales.com';
$configs['retailer.has_carousel']          = false;

$configs['retailer.num_top_picks']    = 12;
$configs['retailer.num_posts']        = 3;
$configs['retailer.default_feed_url'] = "http://bloomingdales.tumblr.com/rss";

$configs['retailer.default_product_array'] = '';

$configs['retailer.grid'] = "container-fluid";

$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl']    = SF_USERNAME_TMPL_FN_LI;

$configs['retailer.mainSearchUrl'] = 'https://www.bloomingdales.com/shop/search?keyword=';

$configs['retailer.social_networks'] = [];

$configs['wordpress.js.searchCategories.exclude'] = ['minage', 'maxage', 'gender'];

$configs['retailer.sale_cookie_expire'] = 86400000; // 1 day

$configs['wordpress.js.iframes.appointment.height'] = 1155;
$configs['wordpress.js.iframes.finder.height']      = 940;
$configs['wordpress.js.iframes.question.height']    = 770;
$configs['wordpress.js.iframes.concern.height']     = 760;
$configs['wordpress.js.iframes.inscription.height'] = 585;

$configs['retailer.prevent_redirect'] = true;

$configs['retailer.storage_method'] = 'localstorage';

$configs['onboarding.choose_alias']         = false;
$configs['onboarding.alias_match_username'] = false;

// ------------------- Api key used by dynamic content feature (storefront) -------------------
$configs['retailer.dynamic_content.api_key'] = 'qq4m44qqqfsf9xs7u9epab7r';

// **************  CONFIG service labels ************************
$configs['retailer.label.email-me'] = 'Email Me';

$configs['retailer.events_from_feed'] = true;

$configs['retailer.publisher_email_option_none'] = false;
$configs['retailer.publisher_social_sharing']    = false;
$configs['retailer.publisher_storefront']        = false;

$configs['retailer.services.carousel.show_names'] = false;

$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;

// === SERVICES ===
$configs['queue.byStore'] = [];

// Chat moderation
$configs['retailer.moderation.text.is_enabled'] = true;
$configs['hive.text.custom.threshold'] = [
    HiveModerator::HIVE_CLASS_SEXUAL => 3,
    HiveModerator::HIVE_CLASS_CHILD_EXPLOITATION => 3
];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

// === Mobile ===
$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.menu_logo_path']        = '/img/retailers/bloom/bloom_t.png';
$configs['mobile.onboarding_logo_path']  = '/img/retailers/bloom/bloom-logo-onboarding.png';
$configs['mobile.extra_quick_responses'] = [[
    'short' => 'Customer Service',
    'full'  => 'Thanks for chatting with me today. As a Style Expert, I\'m here to help you with any product or styling questions. Our Customer Service Team is better equipped to answer your service questions. You can chat with them <a href="https://customerservice-bloomingdales.com/chat#skillName=DCS_Core&url=https%3A%2F%2Fcustomerservice-bloomingdales.com%2Farticles%2Fcontact-us&referer=https%3A%2F%2Fwww.bloomingdales.com%2F&pageTitle=Contact%20Us%20%7C%20Bloomingdale\'s%20Customer%20Service" target="_blank">here</a> or give them a call at ************** and they will be happy to assist.',
]];

$configs['mobile.specificities']
    = <<<JSON
{
  "pages": {
    "enter-token": {
      "token": "true"
    },
    "create-user": {
      "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
      "password": "true",
      "confirm": "true"
    },
    "enter-email": {
      "email": "email",
      "phone": "true",
      "alias": {
        "disabled": "true",
        "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
      },
      "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
      "firstname": "firstname || ''",
      "lastname": "lastname || ''"
    },
    "pick-store": {
      "store": "store",
      "introduction": "\"Hello! I am your dedicated Bloomingdale's Store Associate. I can provide personalized services, answer your questions and meet with you in store or online to help you find what you desire.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "true",
    "import-contact": "true",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
      "enter-email": "false",
      "pick-store": "false",
      "out-of-store": "false",
      "take-picture": "true",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "out-of-store": "false",
      "take-picture": "true",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }]
}
JSON;

$configs['retailer.application_start_date'] = '2016-02-02 22:18:41';

# blank
// SF-16011 Add CASL Compliance retailer Address
// 'retailer.hq_address' is not different from 'sf.casl.address' . No need to specify
$configs['retailer.hq_address'] = '1000 Third Avenue, New York, NY 10022';

$configs['retailer.qa_product_url_parser.pattern'] = '/www\d?\.bloomingdales\.com/i';
$configs['retailer.qa_product_url_parser.replace'] = 'www.qa8codebloomingdales.fds.com';

$configs['mobile.quick_responses'] = [[
    'short' => 'One Moment',
    'full' => 'Please give me a minute and I will look into this for you.',
], [
    'short' => 'Patience',
    'full' => 'Thank you for your patience.  I am still looking into this for you.',
], [
    'short' => 'Not Available',
    'full' => 'Unfortunately, this item is no longer available, can I suggest a similar item for you?',
], [
    'short' => 'Storefront step 1',
    'full' => 'Thanks for connecting with me today! I\'d be happy to help you again.  Here is the link to my Storefront:',
], [
    'short' => 'Storefront step 2',
    'full' => 'storefront_link',
], [
    'short' => 'Subscriber step 1',
    'full' => 'Let\'s keep in touch!  Here is the link to receive my updates to stay in the know about everything at Bloomingdale\'s.',
], [
    'short' => 'Subscriber step 2',
    'full' => 'subscribe_link',
], [
    'short' => 'Loyalist',
    'full' => 'Sign up to become a Loyallist today and start earning points towards a reward card on all your purchases!  Click <a href="https://www.bloomingdales.com/creditservice/marketing/benefits" target="_blank">here</a> to enroll',
], [
    'short' => 'Credit Card',
    'full' => 'Shopping with a Bloomingdale\'s card is the fastest way to earn Loyallist rewards and the best way to shop!  Sign up <a href="https://www.bloomingdales.com/my-credit/gateway/guest" target="_blank">here</a> and receive your new account discount towards today\'s purchase.',
], [
    'short' => 'Returns',
    'full' => 'For information about returns and exchanges click <a href="https://customerservice-bloomingdales.com/articles/what-is-the-return-and-exchange-policy" target="_blank">here</a>',
], [
    'short' => 'Shipping & Delivery',
    'full' => 'For information about shipping and delivery click <a href="https://customerservice-bloomingdales.com/category/shipping-delivery" target="_blank">here</a>',
]];

$configs['customerservice.can_forward'] = true;

$configs['retailer.services.findarep.is_enabled'] = false;

$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;

$configs['security.policy.password'] = 'legacy';

// Menu Scrapper
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 14;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 2;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = true;

// === ShopPage ===
/**
 * Url replacement without encoding url fully
 * @feature ShopPage
 */
$configs['retailer.shoppage.tracking.redirect_url_encode_chars'] = [
    "\'" => "%27",
];

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = false;
$configs['retailer.sidebar.v3.minimize.desktop'] = false;
$configs['retailer.sidebar.v3.minimize.mobile'] = false;

$configs['retailer.sidebar.v3.media.desktop.bottom'] = 45;
$configs['retailer.sidebar.v3.media.mobile.bottom'] = 45;

$configs['retailer.sidebar.v3.media.desktop.width'] = 280;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 280;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;

$configs['retailer.services.carousel.display_mode'] = 'carousel';
$configs['retailer.services.carousel.show_names'] = true;

$configs['mobile.share_instagram_enabled'] = true;
$configs['retailer.shop_feed.enabled'] = true;

// Enable dynamic section
$configs['retailer.storefront.dynamic_content.is_active'] = true;
$configs['retailer.storefront.dynamic_content.max_num_posts'] = 3;
$configs['retailer.storefront.dynamic_content.url'] = [
    'en_US' => 'https://api.bloomingdales.com/discover/v1/search?pathname=/shop/fashion-lookbooks-videos-style-guide/lookbooks-guides&id=1006041&size=medium'
];

$configs['products.expanded_variants.enabled'] = true;
$configs['update_products.max_errors'] = 5000;
$configs['update_products.min_products'] = 250;
$configs['update_products.min_categories'] = 10;

$configs['importer.product.validation'] = [
    ProductImporter::COL_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_PARENT_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_GTIN => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 255]),
    ],
    ProductImporter::COL_TITLE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 250]),
    ],
    ProductImporter::COL_DESCRIPTION => [
        new NotBlank(),
        new Required()
    ],
    ProductImporter::COL_BRAND => [
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_LINK => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_IMAGE_LINK => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_AVAILABLE => [
        new NotBlank(),
        new Required(),
        new Choice(['choices' => ['0', '1']]),
    ],
    ProductImporter::COL_PRICE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 11]),
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^[0-9]+(\.\d+)?$/']),
    ],
    ProductImporter::COL_SALE_PRICE => [
        new Length(['max' => 11]),
        // The additional part of the regex is to pass validation if no sale price is provided.
        new \Symfony\Component\Validator\Constraints\Regex(['pattern' => '/^[0-9]+(\.\d+)?$|^$/']),
    ],
    ProductImporter::COL_SALE_START_DATE => [
        new DateTime(),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_SALE_END_DATE => [
        new DateTime(),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL1_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 30]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL1_TITLE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 50]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 30]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_TITLE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 50]),
    ],
    ProductImporter::COL_ARRIVAL_DATE => [
        new Regex(['pattern' => '/^(?!0000-00-00)\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$/']),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_LANGUAGE_CODE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 10]),
    ],
    ProductImporter::COL_ATTR_1_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_1_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_1_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_1_SWATCH => [
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_ATTR_2_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_2_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_2_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_2_SWATCH => [
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_ATTR_3_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_3_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_3_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_3_SWATCH => [
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_IS_DEFAULT => [
        new NotBlank(),
        new Required(),
        new Choice(['choices' => ['0', '1']]),
    ]
];

// SF-25508 Update file naming pattern for Bloom Variant Feed
$configs['importer.product.s3.filename_regexp'] = '{retailer}-products-\d{8}\d{6}.csv';

$configs['mobile.retailer_has_specialties']  = true;
$configs['retailer.specialties.is_enabled']  = true;
$configs['retailer.specialties.can_select']  = true;

$configs['retailer.specialties.filter'] = \Salesfloor\API\Managers\Categories::SPECIALTIES_FILTER_ALGORITHM_EXCLUDE;
$configs['retailer.specialties.exclude'] = [
    '56af804ebe242b7a36aaada3b9b762', // David Yurman > Mens > Shop All Mens
    '8a7afd6865d780e8650d81a849cd52', // Gifts > Gift Cards > Gift Cards
    '7b8793540713ecf8e5824854f075d1', // Handbags > Shop by Category > Belt Bags
    'df396df5cd362f2761ee0fc3b2cdfa', // Handbags > Shop by Category > Totes
    'c6f3a2c1d5a056ad9dd438908aaa2d', // Handbags > Shop by Category > Wallets & Card Cases
    'd4a9b7616938ecd4c168d1d5a78ec5', // HERMÈS > Women's Fragrances
    '7b724f65ac653687589499362886f2', // Home > Categories > Dining & Entertaining
    '83d5de4fb6ed154129e0a173983f1e', // Home > Categories > Home Decor
    'cc1e3a1fa87214d9d9247b19466db8', // Home > Kitchen > Gadgets & Tools > Prep Tools
    'accessories-fragrances1',
    'beauty-cosmetics-makeup-makeu1',
    'beauty-cosmetics-shop-by-cate1',
    'fine-jewelry1',
    'gifts-gift-cards-gift-cards1',
    'gifts-gift-cards1',
    'gifts1',
    'handbags-shop-by-category-bel1',
    'handbags-shop-by-category-tot1',
    'handbags-shop-by-category-wal1',
    'herm-s-women-s-fragrances1',
    'home-categories-dining-entert1',
    'home-categories-home-decor1',
    'home-kitchen-gadgets-tools-pr1',
    'men-cologne-grooming-cologne-1',
    'men-shoes1',
    'misc',
    'women-clothing-coats-jackets1',
    'women-shop-by-size-range-peti1',
    'women-shop-by-size-range-plus1',
    '5a650f59b4f9496e30f63d6c8a97eb', // Women > SHOP BY SIZE RANGE > Plus
    'b7c0cf074d5f2f1687eb0c9ed03dc4', // Women > SHOP BY SIZE RANGE > Petites
    'e6470e90b76835b46c943ce1af7ef5', // Men > Shoes
    'eeafa68f7f22d2e29b93c847fa0c9e', // Women > Clothing > Coats & Jackets
    'fc077cffcd86ee1756417909ecb667', // Men > Cologne & Grooming > Cologne & Grooming
    '400a2e5bb818abbae5abe7084971fa', // Gifts
    'd177d2da3228a2aaa2e7e1e1474347', // Sale
];

$configs['retailer.services.appointment.types'] = [
  [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
  [ 'type' => 'virtual', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
  [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
];

$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

// Store appointment hours of local time zone
$configs['retailer.store_appointment_hours'] = [
    [
        'open'  => '11',
        'close' => '20',
        'is_available' => '1',
    ],
];
$configs['retailer.store_appointment_hours.is_enabled'] = true;

// === APPOINTMENT LEAD TIME ===
$configs['retailer.appointment_lead_time.widget'] = [
    Appointment::MEETTING_TYPE_STORE => 1440, // 24h
    Appointment::MEETTING_TYPE_VIRTUAL => 1440,
    Appointment::MEETTING_TYPE_PHONE => 1440,
];

// === Mobile app distribution ===
$configs['mobile_ios_appid']     = 'com.salesfloor.enterprise';
$configs['mobile_android_appid'] = 'com.salesfloor.enterprise';

$configs['redirect_whitelist.domains'] = [
    'customerservice-bloomingdales.com',
    'retailology.com',
    'macysinc.com',
    'bloomingdalesassets.com',
    'acctbcom-16d.c4d.devops.fds.com',
    'shopstyle.com',
    'webcache.googleusercontent.com',
    'stuartweitzman.ca',
    'bcomexternal1012.fds.com',
    'qa11codebloomingdales.fds.com',
    'qa10codebloomingdales.fds.com',
    'bcomexternal1002.fds.com',
    'bcomexternal1015.fds.com',
    'qa7codebloomingdales.fds.com',
    'qa8codebloomingdales.fds.com',
    'mdev1.bcomexternal1018.fds.com',
    'qa9codebloomingdales.fds.com',
    'bcom-111.tbe.zeus.fds.com',
    'bcomexternal1001.fds.com',
    'tbe.zeus.fds.com',
    'bcom-165.tbe.zeus.fds.com',
];

$configs['retailer.chat.option.video-chat'] = true;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// === Exporters Encryption ===
$configs['exporter.encryption.type'] = 'pgp'; // will affect all exporters
// NOTE: 1 `Loader::ENV_STACK_PROD` will override PRD config  2. same key sets content with hbc,
// TODO: could we delete those keys since they are expired or not be used ??
//       keys/bloom/sf-pgp-public-bloom-stg.asc && keys/bloom/sf-pgp-private-bloom-stg.asc
//  &&   keys/bloom/sf-gpg-public-bloom-stg.asc && keys/bloom/sf-gpg-private-bloom-stg.asc
$configs['crypto.outbound.general.public_key'] = PATH_PLATFORM . '/services/keys/bloom/sf-pgp-outbound-2025-public-stg.asc';
// NOTE: 1 `Loader::ENV_STACK_PROD` will override PRD config  2. same key sets content with hbc,
$configs['crypto.outbound.general.private_key'] = PATH_PLATFORM . '/services/keys/bloom/common/sf-gpg-outbound-private-no-expire-bloom-test.asc';

$configs['exporter.all_contacts_outbound.daily.enabled'] = true;
$configs['exporter.contacts.all.zip_split_by']           = 'monthly';
$configs['exporter.contact.encryption.enabled']          = true;

$configs['exporter.contact_for_sync.encryption.enabled'] = true;

// Download All Activity Summary - Associates CSV
// robo run:cron ExportRepActivitySummary bloom-dev
$configs['exporter.activitySummary.rep.daily.enabled']              = true;
$configs['exporter.activitySummary.rep.daily.start_days_before']    = -2;
$configs['exporter.activitySummary.rep.daily.end_days_before']      = -2;
$configs['exporter.rep_activity_summary.encryption.enabled'] = true;

// Download All Activity Summary - Stores CSV
// robo run:cron ExportStoreActivitySummary bloom-dev
$configs['exporter.activitySummary.store.daily.enabled']            = true;
$configs['exporter.activitySummary.store.daily.start_days_before']  = -2;
$configs['exporter.activitySummary.store.daily.end_days_before']    = -2;
$configs['exporter.store_activity_summary.encryption.enabled'] = true;

// Download Live Chat Metrics - Associates CSV
// robo run:cron ExportDailyRepLiveChatMetrics bloom-dev
$configs['exporter.live_chat_metrics.rep.daily.enabled']            = true;
$configs['exporter.live_chat_metrics.rep.daily.start_days_before']  = -2;
$configs['exporter.live_chat_metrics.rep.daily.end_days_before']    = -2;
$configs['exporter.rep_live_chat_metrics.encryption.enabled'] = true;

// Download Live Chat Metrics - Stores CSV
// robo run:cron ExportDailyStoreLiveChatMetrics bloom-dev
$configs['exporter.live_chat_metrics.store.daily.enabled']              = true;
$configs['exporter.live_chat_metrics.store.daily.start_days_before']    = -2;
$configs['exporter.live_chat_metrics.store.daily.end_days_before']      = -2;
$configs['exporter.store_live_chat_metrics.encryption.enabled'] = true;

// Download All Customer Request Logs CSV
// robo run:cron ExportRequest bloom-dev
$configs['exporter.request.daily.enabled']           = true;
$configs['exporter.request.daily.start_days_before'] = -2;
$configs['exporter.request.daily.end_days_before']   = -2;
$configs['exporter.request.encryption.enabled']      = true;
$configs['exporter.request.zip_split_by']            = 'monthly';

// Download All SMS Logs CSV
// robo run:cron ExportSmsLog bloom-dev
$configs['exporter.sms.daily.enabled']                              = true;
$configs['exporter.sms.daily.start_days_before']                    = -2;
$configs['exporter.sms.daily.end_days_before']                      = -2;
$configs['exporter.text.encryption.enabled'] = true;

$configs['exporter.sms.all.zip_split_by'] = 'monthly';

// Download Live Chat Logs CSV
// robo run:cron ExportChatLogDaily bloom-dev
$configs['exporter.livechat.daily.enabled']                         = true;
$configs['exporter.livechat.daily.start_days_before']               = -2;
$configs['exporter.livechat.daily.end_days_before']                 = -2;
$configs['exporter.live_chat.encryption.enabled'] = true;

// Download All Live Chat Logs CSV from backoffice
// php ExportChatLog.php bloom-dev
$configs['exporter.live_chat.all.zip_split_by'] = 'monthly';

// Transaction summary
// robo run:cron CreateTransactionDailyExports bloom-dev
$configs['exporter.transactions.daily.enabled']                     = true;
$configs['exporter.transactions.daily.start_days_before']           = -120;
$configs['exporter.transactions.daily.end_days_before']             = -1;

$configs['exporter.dynamic_exporter.daily.is_enabled'] = true;
$configs['exporter.dynamic_exporter.exporters'] = ['predefined/ropo.interaction', 'predefined/ropo.text', 'predefined/ropo.email_click', 'predefined/ropo.email_open'];

$configs['retailer.clienteling.mode'] = true;
$configs['retailer.clienteling.enabled.transactions'] = true;
$configs['retailer.clienteling.enabled.customer_stats'] = true;

$configs['sf.import_ci_customers.encrypted'] = true;
$configs['sf.import_ci_transactions.encrypted'] = true;
$configs['sf.import_ci_stats.encrypted'] = true;
$configs['sf.import_users.encrypted'] = true;

$configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/bloom/sf-gpg-private-bloom-stg.asc';

$configs['importer.reps.s3.filename_regexp'] = '{retailer}-users-\d{8}-\d{6}\.csv\.pgp';
$configs['sf.import_ci_stats.s3.filename_regexp'] = 'bloom-statistics-\d{8}-\d{6}\.csv\.pgp';
$configs['sf.import_ci_transactions.s3.filename_regexp'] = 'bloom-transactions-\d{8}-\d{6}\.csv\.pgp';
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'bloom-customers-\d{8}-\d{6}\.csv\.pgp';

$configs['retailer.customer_tags.is_enabled'] = true;

$configs['stores.max_distance.per_store.enable'] = true;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 14;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 2;

// CI Soon-to-Lapse
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
    'default' => [
        'days_search_back' => [60],
        'max_per_owner'    => 1,
    ],
];
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled'] = true;

$configs['retailer.contacts.import_enabled'] = true;

$configs['retailer.clienteling.customer.limited_visibility.is_enabled'] = true;

$configs['importer.product.ingestor.csv.delimiter'] = '|';

$configs['importer.reps.enabled'] = true;

$configs['importer.rep_transactions.enabled'] = true;

// === SSO CONFIGS ===
$configs['retailer.sso.is_enabled'] = true;
$configs['importer.reps.enabled'] = true;
$configs['onboarding.allow_dupe_emails'] = true;

$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://graph.microsoft.com/oidc/userinfo';

$configs['retailer.sso.provider.tenant']    = '42ab9b91-5029-47c6-8f78-38a33f843520';
$configs['retailer.sso.provider.client_id'] = '16417f8e-2bb4-48be-907e-07afd3a93e0a';
// ------------------- SSO CONFIGS: Microsoft Azure Active Directory -------------------
$configs['retailer.sso.provider.client_secret'] = '****************************************';   //Secret ID: ba03bac5-010a-413a-87b3-39e7749f1886

// === LOGOS ===
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/bloom/logo.svg';

$configs['retailer.virtual.option.video-chat'] = true;
$configs['retailer.virtual.option.video-chat.2ways'] = true;


$configs['storefront.static_content'] = ['active' => true, 'has_title' => false];

///////////////////////
// OLD PRD value

// mobile.retailer-id: => How bad to have this "short" version ? We need to keep the logic for mobile anyway, so
// in theory, we could reuse this logic in calculated ?! TBD
//  < bloomingdales
//  > bloom

// What about stats_db.db (bloom-slave) ? Are we keeping it ?!

$configs['retailer.hq_address.street'] = "1000 Third Avenue";
$configs['retailer.hq_address.city'] = "New York";
$configs['retailer.hq_address.region'] = "New York";
$configs['retailer.hq_address.isoCountry'] = "US";
$configs['retailer.hq_address.postalCode'] = "10022";
$configs['retailer.hq_address.customerName'] = 'Bloomingdale\'s';

// Stores Configs
$configs['stores.max_distance'] = '9001'; // National
$configs['stores.available_hours'] = [
    '0' => true,
    '1' => true,
    '2' => true,
    '3' => true,
    '4' => true,
    '5' => true,
    '6' => true,
  ];

// DEVOPS-3921 - Bloom problem enabling text messaging numbers
// Some stores could not find available phone numbers within the 5 miles, so need to
// enlarge the distance to guarantee the phone number availability
$configs['twilio.available.phone.numbers.distance'] = 25;

/*
 * Mobile app distribution parameters
 */
$configs['mobile_ios_channel'] = 's3';

// DEVOPS-6284 disable soon to lapse tasks
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;
// TD-1613 TO BE REMOVED AFTER UAT
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.secondary_employee_assign.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 5;

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    // TODO why most other retailers have the default value 'salesfloor.net' even on prod ????
    // Smells like this is not used, or most other retailers are broken on prod :thinking-face
    $configs['retailer.cookie_domain'] = 'stores-bloomingdales.com';

    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    $configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/bloom/sf-gpg-private-bloom-prd.asc';
    $configs['crypto.outbound.general.public_key']  = PATH_PLATFORM . '/services/keys/bloom/sf-pgp-outbound-2025-public-prd.asc';
    $configs['exporter.encryption.private_key'] = null;

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web'] = 'UA-92043755-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-92043755-2';
    $configs['retailer.google.ga_services']          = 'UA-92043755-3';
    $configs['retailer.google.ga_storefront']        = 'UA-92043755-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-92043755-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-92043755-6';

    // === stats_db ===
    // Same as standard mysql connection, but use a different port (not sure why...)
    $configs['stats_db.host'] = $configs['mysql.host'];
    $configs['stats_db.username'] = $configs['mysql.username'];
    $configs['stats_db.password'] = $configs['mysql.password'];
    $configs['stats_db.db'] = $configs['mysql.db'];
    $configs['stats_db.port'] = '3307';

    // === Mobile ===
    $configs['branch.base_url'] = 'https://branch-g993dvyzae.salesfloor.net/a/key_live_fbjT6P7c5raRYBXVmpZDdfbdqyk3jIFq';

    $configs['mobile.app_download_urls'] = ['ios' => 'https://build.phonegap.com/apps/2357498/download/ios', 'android' => 'market://details?id=com.salesfloor.appstore'];
    $configs['mobile.app_version_urls'] = ['ios' => 'https://cdn.salesfloor.net/mobile-app-version/configs.version.bru.json', 'android' => 'https://cdn.salesfloor.net/mobile-app-version/configs.version.android.json'];

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MGc1a8a16c8a5f98717928134f9964367e';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MG621a748f64315e56a0f3a77fbb26e23e';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://stores-bloomingdales.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'bloomingdales' : "{$configs['retailer.short_name']}-{$configs['env']}";
