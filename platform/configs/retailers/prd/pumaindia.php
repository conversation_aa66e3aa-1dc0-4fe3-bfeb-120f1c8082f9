<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['s3.userimage.provider'] = 'gcp';

// ==== IMPORT CUSTOMERS ===
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'pumaindia-customers-\d{8}-\d{6}\.csv';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'INR';
$configs['retailer.storefront.is-new-stack'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'pumaindia';
$configs['retailer.short_name'] = 'pumaindia';
$configs['retailer.brand_name'] = 'Puma India';
$configs['retailer.pretty_name'] = 'Puma India';

$configs['retailers.name'] = 'pumaindia';
$configs['retailers.short'] = 'pumaindia';

$configs['firebase.retailername'] = 'pumaindia';

$configs['retailer.application_start_date'] = '2022-02-11 00:00:00';

$configs['retailer.hq_address'] = 'Puma Sports India Pvt Ltd, Ground floor No 496, Mahadevapura Main Road, next to Hewlett Packard Service Gate, Bengaluru, Karnataka 560048';
$configs['retailer.hq_address.street'] = 'Ground floor No 496, Mahadevapura Main Road, next to Hewlett Packard Service Gate';
$configs['retailer.hq_address.city'] = 'Bengaluru';
$configs['retailer.hq_address.region'] = 'Karnataka';
$configs['retailer.hq_address.isoCountry'] = 'IN';
$configs['retailer.hq_address.postalCode'] = '560048';
$configs['retailer.hq_address.customerName'] = 'Puma Sports India Pvt Ltd';

$configs['retailer.country.code'] = 'IN';
$configs['stores.max_distance'] = '100'; // distance in km
$configs['stores.max_distance.per_store.enable'] = true;  // default set to false

$configs['retailer.chat.find_nearby_stores'] = true;
$configs['retailer.chat.find_nearby_stores.max_distance'] = '500';

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://in.puma.com';
$configs['retailer.site'] = 'https://in.puma.com';
$configs['retailer.domain'] = 'in.puma.com';
$configs['retailer.contacturl'] = 'https://in.puma.com/in/en/help';
$configs['retailer.email_domain'] = 'puma.com';
$configs['retailer.mainSearchUrl'] = 'https://in.puma.com/in/en/search?q=';

$configs['retailers.url'] = 'https://in.puma.com';

// === PRODUCTS ===
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['update_products.max_errors'] = 2500;
$configs['update_products.min_products'] = 3000;
$configs['update_products.min_categories'] = 50;
$configs['retailer.pricePrecision'] = 0;

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.new-arrivals.top-picks'] = true;

$configs['importer.products.file_pattern'] = 'TO BE REPLACED';

// === FILTERS ==
$configs['algolia.filters.prices.options'] = [
    '0-1000'    => '₹0 - ₹1000',
    '1000-3000' => '₹1000 - ₹3000',
    '3000-5000' => '₹3000 - ₹5000',
    '5000-7000' => '₹5000 - ₹7000',
    '7000-9000' => '₹7000 - ₹9000',
    '>9000'     => '₹9000+',
];

$configs['currency.symbol'] = '₹';

// === MULTILANG i18n ===
$configs['sf.i18n.locales'] = ['en_IN'];
$configs['retailer.i18n.is_enabled'] = true;
$configs['retailer.i18n.default_locale'] = 'en_IN';
$configs['retailer.i18n.products.is_default_locale_fallback'] = true;

// === STOREFRONT ===
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1],
  ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Puma Sales Associate';
$configs['retailer.social_networks'] = array("twitter");
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'puma.clickpost.in',
    'pumatr.ac',
];

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 8, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'pumaindia';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/pumaindia/pumaindia-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/pumaindia/pumaindia-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = false;
$configs['onboarding.alias_match_username'] = false;

$configs['retailer.virtual.option.video-chat'] = true;
$configs['retailer.virtual.option.video-chat.2ways'] = true;

// === SERVICES ===
// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';
$configs['queue.byStore'] = [];

// Legacy mode metric logging
$configs['services.record_chat_metrics_cans.enabled']  = false;

$configs['retailer.services.appointment.types'] = [
  [ 'type' => 'virtual', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
  [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
  [ 'type' => 'store', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
];

// === SIDEBAR3 VARIANT ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = 'https://cdn.salesfloor.net/salesfloor-assets/pumaindia/pumaindia_default_avatar.jpg';
$configs['retailer.sidebar.v3.horizontalPosition'] = 'right';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 320;
$configs['retailer.sidebar.v3.media.mobile.width'] = 320;
$configs['retailer.sidebar.v3.media.desktop.height'] = 64;
$configs['retailer.sidebar.v3.media.mobile.height'] = 64;

$configs['retailer.services.carousel.display_mode'] = null;

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Puma Support center';

$configs['mobile.menu_logo_path'] = '/img/retailers/pumaindia/pumaindia_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/pumaindia/pumaindia-logo-onboarding.png';
$configs['mobile.retailer.pricePrecision'] = 0;
$configs['mobile.retailer_has_personal_shopper'] = false;

// === LIVE CHAT QUICK RESPONSES ===
$configs['mobile.quick_responses'] = [[
    'short' => [
        'en_US' => 'Customer support',
        'en_IE' => 'Customer support',
        'en_IN' => 'Customer support',
    ],
    'full' => [
        'en_US' => 'Our Puma Customer Support team can help you with this request. They are available via phone at 080 3535 3535, thanks.',
        'en_IE' => 'Our Puma Customer Support team can help you with this request. They are available via phone at 080 3535 3535, thanks.',
        'en_IN' => 'Our Puma Customer Support team can help you with this request. They are available via phone at 080 3535 3535, thanks.',
    ],
], [
    'short' => [
        'en_US' => 'Give me a moment',
        'en_IE' => 'Give me a moment',
        'en_IN' => 'Give me a moment',
    ],
    'full' => [
        'en_US' => 'Please give me a moment and I will look into this for you.',
        'en_IE' => 'Please give me a moment and I will look into this for you.',
        'en_IN' => 'Please give me a moment and I will look into this for you.',
    ],
], [
    'short' => [
        'en_US' => 'Thank you and goodbye',
        'en_IE' => 'Thank you and goodbye',
        'en_IN' => 'Thank you and goodbye',
    ],
    'full' => [
        'en_US' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
        'en_IE' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
        'en_IN' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
    ],
], [
    'short' => [
        'en_US' => 'Out of stock',
        'en_IE' => 'Out of stock',
        'en_IN' => 'Out of stock',
    ],
    'full' => [
        'en_US' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
        'en_IE' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
        'en_IN' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
    ],
], [
    'short' => [
        'en_US' => 'My Storefront Link',
        'en_IE' => 'My Storefront Link',
        'en_IN' => 'My Storefront Link',
    ],
    'full' => 'storefront_link',
], [
    'short' => [
        'en_US' => 'Subscribe Link',
        'en_IE' => 'Subscribe Link',
        'en_IN' => 'Subscribe Link',
    ],
    'full' => 'subscribe_link',
]];

$configs['mobile.extra_quick_responses'] = [];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = false;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = true;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "true",
            "value": "(firstname + lastname[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Puma Sales Associate. I can answer your questions and help you find what you’re shopping for at your local store or online.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "true",
    "connect-social": {
		"twitter": "true"
	},
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = false;
$configs['messaging.text.multiple-recipients.enabled'] = false;
$configs['retailer.services.channel.text.enabled'] = false;

// ==== IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = true;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// === APPOINTMENT MANAGEMENT ===
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;

$configs['retailer.customer_tags.is_enabled'] = true;
$configs['retailer.customer_tags.is_read_only'] = true;

// Social Shop feature
$configs['retailer.shop_feed.enabled'] = true;

// Video Chat feature
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs["name_fmt.en_IN"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.storefront_product_comment"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.sidebar_carousel_rep_name"]  = "{Fn} {Li.}";
$configs["name_fmt.en_IN.rep_display_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.mobile_dashboard"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.rep_mode_email_lookbook"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.rep_mode_email_lookbook_subject"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.email_onboarding"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.email_appt_calendar"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.rep_mode_email_from_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.email_leads_email_to_customer_rep_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.email_leads_forward_to_customer_cs_rep_from_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.email_leads_forward_to_customer_rep_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.email_autoresponder"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.prepare_user_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.bo_services_rep_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.findrep_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.event_display_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.store_display_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.global_email_username"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.rep_store_display_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.mobile_contact"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.store_request_rep_name"] = "{Fn} {Li.}";
$configs["name_fmt.en_IN.store_request_customer_name"] = "{Fn} {Li.}";

$configs['retailer.countries.text.available'] = ['CA', 'US', 'IN'];
$configs['retailer.countries.text.preferred'] = ['CA', 'US', 'IN'];
$configs['retailer.countries.application.preferred'] = ['IN'];

$configs['retailer.storefront.display_state'] = false;
$configs['retailer.services.hide_optional_phone_input'] = true;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.days_search_back'] = 210;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 5;

$configs['service.personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;

// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = "BBMHV6Z130LgSZvhQjHP";
$configs['connect2.saas.brandId'] = "XqpMxfqlczqTbrJV1uUZ";
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = '1a1f5e48-811c-4fde-8306-97927ad98922';
        $configs['typhoon.url'] = 'https://prod-in.salesfloor.net/typhoon';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = '6bf809b9-101b-4f63-845d-e413c93e6887';
        $configs['typhoon.url'] = 'https://box01.sandbox.salesfloor.net/typhoon';
        break;
    case 'box02':
        $configs['connect2.bot.integrationId'] = '39528aa4-422a-47b8-994e-bdef3d7dc798';
        $configs['typhoon.url'] = 'https://box02.sandbox.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = '10fc173b-511d-44ed-b16a-e94f87267740';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = '142b0c3a-1704-499f-8d54-e70013a3fd05';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'qa06':
        $configs['connect2.bot.integrationId'] = '53c7f007-d1b3-4135-adcc-29e95f103b4b';
        $configs['typhoon.url'] = 'https://qa06.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = '0428bdac-87d1-475c-a0a6-046d623f1629';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-221592121-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-221592121-2';
    $configs['retailer.google.ga_services']          = 'UA-221592121-3';
    $configs['retailer.google.ga_storefront']        = 'UA-221592121-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-221592121-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-221592121-6';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://retailstores.in.puma.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'pumaindia' : "{$configs['retailer.short_name']}-{$configs['env']}";
