<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Moderation\Moderator\HiveModerator;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 10; // 10 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = false;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'dxl';
$configs['retailer.short_name'] = 'dxl';
$configs['retailer.brand_name'] = 'DXL';
$configs['retailer.pretty_name'] = 'DXL';

$configs['retailers.name'] = 'dxl';
$configs['retailers.short'] = 'dxl';

$configs['firebase.retailername'] = 'dxl';

$configs['retailer.chat.option.eject_after_hours'] = true;
$configs['retailer.store.open_hour'] = 9;
$configs['retailer.store.close_hour'] = 20;

$configs['retailer.store_appointment_hours'] = [
    [
        'open'  => '13',
        'close' => '18'
    ],
];

$configs['retailer.store_text_hours'] = [
    [
        'open'  => '9',
        'close' => '22'
    ],
];
$configs['retailer.application_start_date'] = '2021-04-27 00:00:00';

$configs['retailer.hq_address'] = '555 Turnpike Street Canton, MA 02021';

$configs['retailer.hq_address.street'] = "555 Turnpike Street";
$configs['retailer.hq_address.city'] = "Canton";
$configs['retailer.hq_address.region'] = "Massachusetts";
$configs['retailer.hq_address.isoCountry'] = "US";
$configs['retailer.hq_address.postalCode'] = "02021";
$configs['retailer.hq_address.customerName'] = "Destination XL";

$configs['stores.max_distance'] = '9001'; // distance in km

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.dxl.com/';
$configs['retailer.site'] = 'https://www.dxl.com/';
$configs['retailer.domain'] = 'dxl.com';
$configs['retailer.contacturl'] = 'https://www.dxl.com/static/customerservice';
$configs['retailer.email_domain'] = 'dxl.com';
$configs['retailers.url'] = 'https://www.dxl.com/';
$configs['retailer.unsubscribe_link'] = 'https://www.dxl.com/unsubscribe?email={CUSTOMER_EMAIL_ENC}';

// === PRODUCTS ===
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['update_products.min_products'] = 50;
$configs['update_products.max_errors'] = 1000;

$configs['product_img_size'] = 250;

$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;

$configs['retailer.rep_avatar_picture.random_default'] = [];

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 8;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'max_products' => 8, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'max_products' => 8, 'show_comments' => 1],
  ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 0],
];

$configs['retailer.associate'] = 'In-store Fit Expert';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'onetrust.com',
];

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 0, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'destinationxl';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/dxl/dxl-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/dxl/dxl-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;
$configs['importer.reps.enabled'] = true;

// === SERVICES ===
$configs['queue.byStore'] = [];

// Chat moderation
$configs['retailer.moderation.text.is_enabled'] = true;
$configs['hive.text.custom.threshold'] = [
    HiveModerator::HIVE_CLASS_SEXUAL => 0
];

$configs['retailer.services.appointment.types'] = [
  [ 'type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
  [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
  [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
];

$configs['retailer.services.findarep.is_enabled'] = false;

$configs['mobile.retailer_has_specialties'] = false;
$configs['retailer.specialties.is_enabled'] = false;
$configs['retailer.specialties.can_select'] = false;
$configs['retailer.specialties.is_required'] = false;

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 250;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 250;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;
$configs['retailer.services.carousel.display_mode'] = 'carousel';
$configs['retailer.services.carousel.show_names'] = true;

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Email Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/dxl/dxl_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/dxl/dxl-logo-onboarding.png';

$configs['mobile.quick_responses'] = [[
  'short' => 'Customer Support',
  'full' => 'Our Customer Service Team can help you with this request. They are available to chat here https://home-c36.nice-incontact.com/inContact/ChatClient/ChatClient.aspx?poc=43f6a29f-b5a7-4c60-ae30-2e72e59698cb&bu=4600462&sf_source_origin=storefront or by phone at **************, thanks.'
], [
  'short' => 'Give me a moment',
  'full' => 'Please give me a moment and I will look into this for you.',
], [
  'short' => 'Thank you and goodbye',
  'full' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;

$configs['mobile.retailer_can_browse_library'] = false;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "(firstname + lastname).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname",
        "lastname": "lastname"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a DXL In-store Fit Expert. I can answer your questions and help you find what you’re shopping for at your local store or online.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
    }, {
    "rules": "group > 3",
    "override": {
        "enter-email": {
            "alias": "false"
        },
        "pick-store": "false",
        "take-picture": "false",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;

// ==== IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = true;

// ==== IMPORT CUSTOMERS ===
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'dxl-customers-\d{8}-\d{6}\.csv';

$configs['s3.bucket.provider']              = 'gcp';
$configs['geoip.s3bucket.provider']         = 'gcp';
$configs['logrotate.s3.provider']           = 'gcp';
$configs['logrotate_client.s3.provider']    = 'gcp';
$configs['rep-export.s3.provider']          = 'gcp';
$configs['sf.import_unsubs.s3.provider']    = 'gcp';

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// Transaction summary
// php CreateTransactionDailyExports {retailer}-dev
$configs['exporter.transactions.daily.enabled']           = true;
$configs['exporter.transactions.daily.start_days_before'] = -2;
$configs['exporter.transactions.daily.end_days_before']   = -2;

$configs['exporter.transactions.weekly.enabled']           = true;

// leave on aws until its fully migrated
$configs['s3.image-bucket.provider']  = 'aws';
$configs['mobile_s3_bucket.provider'] = 'aws';

//appointment management
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

$configs['retailer.chat.find_nearby_stores'] = true;

$configs['retailer.chat.find_nearby_stores.max_distance'] = '9001'; // Nationwide

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 7;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 2;

$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = true;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 7 ];
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 2;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 180;

$configs['sf.task.automated.transaction.min'] = 100;

$configs['sf.task.automated.nag_onboarding_update_about_me.days_search_back'] = 7;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.days_search_back'] = 7;
$configs['sf.task.automated.nag_onboarding_connect_social_media.enabled'] = false;
$configs['sf.task.automated.nag_share_update.enabled'] = false;

$configs['services.record_chat_metrics_cans.enabled'] = true;

// === CONNECT 2 ===
// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = 'BBMHV6Z130LgSZvhQjHP';
$configs['connect2.saas.brandId'] = '56gldTifHpWHGfQSsgrN';
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = '71743202-ed79-4542-9ab6-62341bb08152';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = '345eb56b-1326-4331-9973-9059ecc9a500';
        $configs['typhoon.url'] = 'https://box01.sandbox.salesfloor.net/typhoon';
        break;
    case 'box02':
        $configs['connect2.bot.integrationId'] = '4416be00-1849-4ae2-b9f6-35b994e6fd51';
        $configs['typhoon.url'] = 'https://box02.sandbox.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = '6cee0f58-fd18-4c4d-8ce5-55ef23aa14b1';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = '7308ce54-b8d7-459f-a8be-553df2c3da55';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'qa06':
        $configs['connect2.bot.integrationId'] = '400fa354-1839-46c3-a5dd-965a1d95c6c4';
        $configs['typhoon.url'] = 'https://qa06.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = '3d28427b-f2f2-446a-8f8f-e8d56fadc0c8';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web'] = 'UA-196190578-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-196190578-2';
    $configs['retailer.google.ga_services'] = 'UA-196190578-3';
    $configs['retailer.google.ga_storefront'] = 'UA-196190578-4';
    $configs['retailer.google.ga_socialshop'] = 'UA-196190578-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-196190578-6';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MG4c2e48bc04ecf56ea7c2e7fedae57e48';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MG3fb90844aaced7bbf52254767b1ae2a5';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://stylist.dxl.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'dxl' : "{$configs['retailer.short_name']}-{$configs['env']}";

$configs['tier'] = 'basic';
