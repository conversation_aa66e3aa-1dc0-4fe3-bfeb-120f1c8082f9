<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\NameSuggester;
use Salesfloor\Services\Tasks\Features\TaskAutoDismissProcessor;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 14; // 14 days in ms
$configs['retailer.storefront.is-new-stack'] = true;
$configs['retailer.storefront.display_state'] = false;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'allenedmonds';
$configs['retailer.short_name'] = 'allenedmonds';
$configs['retailer.brand_name'] = 'Allen Edmonds';
$configs['retailer.pretty_name'] = 'Allen Edmonds';

$configs['retailers.name'] = 'allenedmonds';
$configs['retailers.short'] = 'allenedmonds';

$configs['firebase.retailername'] = 'allenedmonds';

$configs['retailer.application_start_date'] = '2021-10-01 00:00:00';

$configs['retailer.hq_address'] = '8300 Maryland Avenue, St. Louis, Mo 63105';
$configs['retailer.hq_address.street'] = "8300 Maryland Avenue";
$configs['retailer.hq_address.city'] = "St. Louis";
$configs['retailer.hq_address.region'] = "Missouri";
$configs['retailer.hq_address.isoCountry'] = "US";
$configs['retailer.hq_address.postalCode'] = "63105";
$configs['retailer.hq_address.customerName'] = "Allen Edmonds";

$configs['retailer.country.code'] = 'US';
$configs['stores.max_distance'] = '9001'; // distance in km
$configs['stores.max_distance.per_store.enable'] = false;

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.allenedmonds.com';
$configs['retailer.site'] = 'https://www.allenedmonds.com';
$configs['retailer.domain'] = 'allenedmonds.com';
$configs['retailer.contacturl'] = 'https://allenedmonds.custhelp.com/app/ask/';
$configs['retailer.email_domain'] = 'allenedmonds.com';
$configs['retailer.mainSearchUrl'] = 'https://www.allenedmonds.com/search?q=';

$configs['retailers.url'] = 'https://www.allenedmonds.com';

// === IMPORT PRODUCTS ===
$configs['products.expanded_variants.enabled'] = true;
$configs['update_products.max_errors'] = 5000;
$configs['update_products.min_products'] = 200;

$configs['product_img_size'] = 250;

$configs['importer.products.file_pattern'] = 'TO BE REPLACED';

// ==== IMPORT USERS ===
$configs['importer.reps.enabled'] = true;
$configs['sf.import_users.encrypted'] = true;
$configs['importer.reps.s3.filename_regexp'] = '{retailer}-users-\d{8}-\d{6}\.csv\.pgp';

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 8;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
    ['type' => 'recommendations', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Allen Edmonds Expert';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

$configs['retailer.shoppage.tracking.custom_params'] = [
    // when adding a prefix value (ex. other_storefront_salesfloor_), always put dynamic value in curley braces {} for it to work
    'partnerid' => "other_storefront_salesfloor_" . "{" . Salesfloor\Services\ShopPage\ShopPage::RETAILER_DYNAMIC_PARAM_EMPLOYEE_ID . "}",
];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = false;

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'jobs.caleres.com',
    'allenedmonds.custhelp.com',
];

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 8;
$configs['retailer.trending_recommendations.min'] = 2;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 0, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'allenedmonds';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/allenedmonds/allenedmonds-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/allenedmonds/allenedmonds-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];
$configs['service.personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;

// Chat moderation
$configs['retailer.moderation.text.is_enabled'] = true;

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.appointment.types'] = [
    [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
    [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
    [ 'type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
];

$configs['retailer.modular_connect.can_add_contacts'] = false;

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = false;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;

// === SIDEBAR3 VARIANT ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 300;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 300;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'right';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;

// === MOBILE ===
$configs['mobile.phone_call.is_enabled'] = false;
$configs['mobile.email_me_label'] = 'Contact Us';

$configs['mobile.menu_logo_path'] = '/img/retailers/allenedmonds/allenedmonds_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/allenedmonds/allenedmonds-logo-onboarding.png';

// === LIVE CHAT QUICK RESPONSES ===
$configs['mobile.quick_responses'] = [[
    'short' => 'Give me a moment',
    'full' => 'Please give me a moment and I will look into this for you.',
], [
    'short' => 'Thank you and goodbye',
    'full' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
], [
    'short' => 'Out of stock',
    'full'  => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here',
], [
    'short' => 'My Storefront Link',
    'full' => 'storefront_link',
], [
    'short' => 'Subscribe Link',
    'full' => 'subscribe_link',
]];

$configs['mobile.extra_quick_responses'] = [[
    'short' => 'Customer Support',
    'full' => 'Our Customer Service Team can help you with this request. They are available via phone at **************, thanks.'
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;

$configs['mobile.retailer_can_browse_library'] = false;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Allen Edmonds Style Expert. I can answer your questions and help you find what you’re shopping for at your local store or online.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = false;
$configs['messaging.text.multiple-recipients.enabled'] = false;
$configs['retailer.services.channel.text.enabled'] = false;

$configs['retailer.customer_tags.is_enabled'] = true;

// ==== APPOINTMENT MANAGEMENT ===
$configs['retailer.services.hide_optional_phone_input'] = true;

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// Video Chat feature
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;
$configs['update_products.min_categories'] = 10;
$configs['retailer.chat.find_nearby_stores'] = true;
$configs['products.defaultcomment_display.is_enabled'] = false;

// === TASK ===
$configs['sf.task.automated.transaction.min'] = 50;

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 7;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 5;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [7,21];

$configs['sf.task.auto_dismiss.enabled'] = true;
$configs['sf.task.auto_dismiss.settings'] = [
  [
    'happen_after_event' => TaskAutoDismissProcessor::HAPPEN_AFTER_REMINDER,
    'days_after_event'   => 60,
    'automated_types'    => TaskAutoDismissProcessor::DEFAULT_AUTOMATED_TYPES
  ],
];

// CI Soon-to-Lapse
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
    'default' => [
        'days_search_back' => [120],
        'max_per_owner'    => 5,
    ],
];

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

$configs['exporter.transactions.daily.enabled'] = true;
$configs['retailer.corporate-email.required'] = false;

$configs['security.pii.crypto.enabled'] = true;
$configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/allenedmonds/sf-gpg-private-allenedmonds-stg.asc';

// Publish Chat Metric Logs for Broadcast and Queue modes to CANS
$configs['services.record_chat_metrics_cans.enabled'] = false;

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

// === Clienteling ===
$configs['retailer.clienteling.mode'] = true;
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
$configs['retailer.clienteling.customers.communication.blackout.period'] = 604800;

// === SSO CONFIGS ===
$configs['retailer.sso.is_enabled'] = true;
$configs['retailer.sso.provider.url.userinfo_endpoint'] = 'https://graph.microsoft.com/oidc/userinfo';
$configs['retailer.sso.identity'] = \Salesfloor\Services\Oauth2\Oauth2Service::IDENTITY_UPN;
$configs['retailer.sso.provider.tenant'] = 'c8944afa-31ea-4bbf-94dc-d454e97b5881';

$configs['retailer.sso.provider.client_id'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? 'eef4e5dd-b723-4f29-91fb-dcfcd064136b'
        : 'c04c6914-4f71-4ac4-880c-580c51d72207';

// prd env: Secret ID: edecd4c3-199d-4db9-91fc-d4002b818653
$configs['retailer.sso.provider.client_secret'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? '****************************************'
        : '****************************************';

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web'] = 'UA-210163063-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-210163063-2';
    $configs['retailer.google.ga_services'] = 'UA-210163063-3';
    $configs['retailer.google.ga_storefront'] = 'UA-210163063-4';
    $configs['retailer.google.ga_socialshop'] = 'UA-210163063-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-210163063-6';

    $configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/allenedmonds/sf-gpg-private-allenedmonds-prd.asc';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://mystore.allenedmonds.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'allenedmonds' : "{$configs['retailer.short_name']}-{$configs['env']}";

// === Connect V2.0 ===
$configs['connect2.bot.integrationId'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'ee421fa4-f0dc-42b0-af4d-84e8e653e197' : "1d3f82e6-6b5e-4561-af97-13340ec65624";
$configs['connect2.saas.organizationId'] = "BBMHV6Z130LgSZvhQjHP";
$configs['connect2.saas.brandId'] = "UpXMj7G7pLpAOP5mrrNw";
$configs['connect2.enabled'] = true;

// === LURE ===
$configs['lure.cta_text'] = [
    'en_US' => 'Chat with a Store Expert',
    'fr_CA' => 'Clavarder avec un expert en magasin',
];

// Enabling the green dot on the lure
$configs['retailer.chat_availability_status.sidebar.enabled'] = true;

// Enabling Eject After Hours
$configs['retailer.chat.option.eject_after_hours'] = true;
$configs['retailer.store.open_hour'] = 9;
$configs['retailer.store.close_hour'] = 21;
