<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep_name_format'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['name_fmt.default.rep_store_display_name'] = '{Fn} {Li.}';
$configs['name_fmt.default.store_display_name'] = '{Fn} {Li.}';
$configs['name_fmt.default.team_mode_email_lookbook'] = '{Fn} {Li.}';
$configs['name_fmt.default.team_mode_email_lookbook_subject'] = '{Fn} {Li.}';
$configs['name_fmt.default.mobile_dashboard'] = '{Fn} {Li.}';
$configs['name_fmt.default.chat_rep_name'] = '{Fn}';
$configs['name_fmt.default.store_request_rep_name'] = '{Fn} {Li.}';
$configs['name_fmt.default.sidebar_carousel_rep_name'] = '{Fn} {Li.}';

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = true;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 14; // 14 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = false;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'fabletics';
$configs['retailer.short_name'] = 'fabletics';
$configs['retailer.brand_name'] = 'Fabletics';
$configs['retailer.pretty_name'] = 'Fabletics';

$configs['retailers.name'] = 'fabletics';
$configs['retailers.short'] = 'fabletics';

$configs['firebase.retailername'] = 'fabletics';

$configs['retailer.application_start_date'] = '2022-12-21 00:00:00';

$configs['retailer.hq_address'] = '555 Aviation Boulevard El Segundo, California 90245';
$configs['retailer.hq_address.street'] = '555 Aviation Boulevard';
$configs['retailer.hq_address.city'] = 'El Segundo';
$configs['retailer.hq_address.region'] = "California";
$configs['retailer.hq_address.isoCountry'] = 'US';
$configs['retailer.hq_address.postalCode'] = 'CA 90245';

$configs['retailer.country.code'] = 'US';
$configs['stores.max_distance'] = '48'; // distance in km
$configs['stores.max_distance.per_store.enable'] = true;

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.fabletics.com';
$configs['retailer.site'] = 'https://www.fabletics.com';
$configs['retailer.domain'] = 'fabletics.com';
$configs['retailer.contacturl'] = 'https://help.fabletics.com/hc/en-us';
$configs['retailer.email_domain'] = 'fabletics.com';
$configs['retailers.url'] = 'https://www.fabletics.com';

// === PRODUCTS ===
$configs['update_products.importer.class'] = '\Salesfloor\Services\CatalogImporter\\' . ucfirst('fabletics');
$configs['update_products.max_errors'] = 2500;
$configs['update_products.min_products'] = 2000;
// temp config need reset to a big value, right now the small value only let qa/dev to check category tree by UI easily
$configs['update_products.min_categories'] = 5;
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;

// === STOREFRONT ===
$configs['retailer.num_deals'] = 4;
$configs['retailer.num_top_picks'] = 8;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'recommendations', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 0],
  ['type' => 'new-arrivals', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 0],
  ['type' => 'top-picks', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 0],
];

$configs['products.defaultcomment_display.is_enabled'] = false;

$configs['retailer.associate'] = 'Associate';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = false;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'myworkdayjobs.com',
    'techstyle.com',
];

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 8;
$configs['retailer.trending_recommendations.min'] = 8;
$configs['retailer.trending_recommendations.title'] = "Trending Products";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 8, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'fabletics';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/fabletics/fabletics-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/fabletics/fabletics-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = false;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['service.personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;

$configs['queue.byStore'] = [];

$configs['retailer.services.appointment.types'] = [
  ['type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false],
  ['type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false],
  ['type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false],
];

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['mobile.retailer_has_specialties'] = false;
$configs['retailer.specialties.is_enabled'] = false;
$configs['retailer.specialties.can_select'] = false;
$configs['retailer.specialties.is_required'] = false;

// === SIDEBAR3 VARIANT ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 250;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 250;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = 'https://cdn.salesfloor.net/salesfloor-assets/fabletics/fabletics_sidebar_logo.png';

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [
    'salesfloor-assets:fabletics:default_avatar_2025_04.jpg',
];

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/fabletics/fabletics_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/fabletics/fabletics-logo-onboarding.png';

// === LIVE CHAT QUICK RESPONSES ===
$configs['mobile.quick_responses'] = [[
  'short' => 'Customer support',
  'full' => 'For customer support & inquiries please visit us <a target="_blank" href="https://help.fabletics.com/hc/en-au">here</a>. For <strong>Yitty</strong> customer support & inquiries please visit us <a target="_blank" href="https://help-yitty.fabletics.com/hc/en-us">here</a>.',
], [
  'short' => 'Give me a moment',
  'full' => 'Please give me a moment and I will look into this for you.',
], [
  'short' => 'Thank you and goodbye',
  'full' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
], [
  'short' => 'Out of stock',
  'full' => 'Unfortunately we currently do not have this in stock in our store, but you can buy it online by clicking here.',
],[
  'short' => 'My Storefront Link',
  'full' => 'storefront_link',
], [
  'short' => 'Subscribe Link',
  'full' => 'subscribe_link',
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.barcode_scanner.contacts.enabled'] = false;

$configs['mobile.retailer_can_browse_library'] = false;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "true",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Fabletics associate. I am here to help find what you need online or in store!\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "true",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// ==== IMPORT CUSTOMERS ===
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = '{retailer}-customers-\d{8}-\d{6}\.csv';
$configs['importer.import-as-subscriber.sms'] = 1;
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
$configs['retailer.clienteling.customers.communication.blackout.period']     = 3600 * 24 * 4;

// ==== TASK ===
$configs['sf.task.auto_dismiss.enabled'] = true;

$configs['sf.task.automated.transaction.min'] = 100;

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 7;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 30;

$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = true;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 7 ];
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 30;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 30;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 5;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

$configs['exporter.contacts.daily.enabled'] = true;
$configs['exporter.messages.daily.enabled'] = true;
$configs['exporter.share.email.daily.enabled'] = true;

$configs['exporter.sms.daily.enabled']           = true;
$configs['exporter.sms.daily.start_days_before'] = -2;
$configs['exporter.sms.daily.end_days_before']   = -2;

$configs['exporter.activitySummary.store.daily.enabled']           = true;
$configs['exporter.activitySummary.store.daily.start_days_before'] = -2;
$configs['exporter.activitySummary.store.daily.end_days_before']   = -2;

$configs['exporter.livechat.daily.enabled']           = true;
$configs['exporter.livechat.daily.start_days_before'] = -2;
$configs['exporter.livechat.daily.end_days_before']   = -2;

$configs['exporter.live_chat_metrics.store.daily.enabled']           = true;
$configs['exporter.live_chat_metrics.store.daily.start_days_before'] = -2;
$configs['exporter.live_chat_metrics.store.daily.end_days_before']   = -2;

$configs['exporter.request.daily.enabled']           = true;
$configs['exporter.request.daily.start_days_before'] = -2;
$configs['exporter.request.daily.end_days_before']   = -2;

$configs['exporter.transactions.daily.enabled'] = true;
$configs['exporter.transactions.daily.start_days_before'] = -32;
$configs['exporter.transactions.daily.end_days_before'] = -2;

// === VIDEO CHAT ===
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs['retailer.virtual.option.video-chat'] = false;
$configs['retailer.virtual.option.video-chat.2ways'] = false;

$configs['retailer.customer_tags.is_enabled'] = true;

// === APPOINTMENT MANAGEMENT ===
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;

$configs['retailer.add_customer_to_my_contacts.is_enabled'] = true;

$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.hide_optional_phone_input'] = true;

$configs['retailer.store_appointment_hours.is_enabled'] = true;

// Mobile app distribution parameters
$configs['mobile_android_appid'] = 'com.salesfloor.enterprise';
$configs['mobile_ios_appid'] = 'com.salesfloor.enterprise';

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://mystore.fabletics.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'fabletics' : "{$configs['retailer.short_name']}-{$configs['env']}";

$configs['messaging.text.multiple-recipients.enabled'] = true;
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;

$configs['security.pii.crypto.enabled'] = true;
$configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/fabletics/sf-gpg-private-fabletics-stg.asc';

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-253168821-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-253168821-2';
    $configs['retailer.google.ga_services']          = 'UA-253168821-3';
    $configs['retailer.google.ga_storefront']        = 'UA-253168821-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-253168821-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-253168821-6';

    $configs['crypto.inbound.general.private_key'] = PATH_PLATFORM . '/services/keys/fabletics/sf-gpg-private-fabletics-prd.asc';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MG7b4c8aa9e5330d0d8c9f64a1ee7e9425';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MGbde68dfc7b8df7c94c70b2c187ca3b1c';
}
// make sure if-PROD section is at then end to avoid PROD config be overridden
