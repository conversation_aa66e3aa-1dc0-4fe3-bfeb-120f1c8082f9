<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Importer\Product\Importer as ProductImporter;
use Symfony\Component\Validator\Constraints\Length;
use Salesfloor\API\Managers\Import;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'credobeauty';
$configs['retailer.short_name'] = 'credobeauty';
$configs['retailer.brand_name'] = 'Credo Beauty';
$configs['retailer.pretty_name'] = 'Credo Beauty';

$configs['retailers.name'] = 'credobeauty';
$configs['retailers.short'] = 'credobeauty';

$configs['firebase.retailername'] = 'credobeauty';

$configs['retailer.sale_cookie_expire'] = **********; // 20 days in ms
$configs['retailer.application_start_date'] = '2022-01-12 00:00:00';

$configs['retailer.hq_address'] = '1169 Gorgas Ave, San Francisco, CA 94129';
$configs['retailer.hq_address.street'] = '1169 Gorgas Avenue';
$configs['retailer.hq_address.city'] = 'San Francisco';
$configs['retailer.hq_address.region'] = "California";
$configs['retailer.hq_address.isoCountry'] = 'US';
$configs['retailer.hq_address.postalCode'] = 'CA 94129';

$configs['retailer.country.code'] = 'US';

$configs['stores.max_distance'] = '9001'; // Nationwide
$configs['stores.max_distance.per_store.enable'] = true;
$configs['retailer.chat.find_nearby_stores'] = true;
$configs['retailer.chat.find_nearby_stores.max_distance'] = '9001'; // Nationwide

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://credobeauty.com';
$configs['retailer.site'] = 'https://credobeauty.com';
$configs['retailer.domain'] = 'credobeauty.com';
$configs['retailer.contacturl'] = 'https://credobeauty.com/pages/contact-1';
$configs['retailer.email_domain'] = 'credobeauty.com';
$configs['retailer.mainSearchUrl'] = 'https://credobeauty.com/pages/search-results-page?q=';

$configs['retailers.url'] = 'https://credobeauty.com';
$configs['retailer.unsubscribe_link'] = 'https://credo-beauty.myklpages.com/p/unsubscribe?a=HvASzF';

// === PRODUCTS ===
$configs['update_products.max_errors'] = 5000;
$configs['update_products.min_products'] = 1000;
$configs['update_products.min_categories'] = 5;
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['importer.product.validation.amend'] = [
    // Set Gtin as optional
    ProductImporter::COL_GTIN => [
        new Length(['max' => 255]),
    ],
];

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = false;

// === STOREFRONT ===
$configs['retailer.num_deals'] = 4;
$configs['retailer.num_top_picks'] = 12;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
    ['type' => 'top-picks', 'max_products' => 12, 'min_products' => 12, 'show_comments' => 1],
    ['type' => 'new-arrivals', 'max_products' => 4, 'max_products' => 4, 'show_comments' => 1],
    ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Clean Beauty Expert';

$configs['retailer.social_networks'] = array("twitter");
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "Trending";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'max_posts' => 8, 'min_posts' => 8];
$configs['retailer.instagram.account'] = 'credobeauty';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/credobeauty/logo_onboarding_2025.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/credobeauty/logo_2025.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['service.book_appointment'] = false;
$configs['service.personal_shopper'] = false;

$configs['retailer.services.appointment.is_enabled'] = false;
$configs['retailer.has_personal_shopper'] = false;

$configs['queue.byStore'] = [];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['mobile.retailer_has_specialties'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;

$configs['retailer.filter_new_leads_by_specialty'] = true;
$configs['mobile.retailer_has_specialties'] = true;
$configs['retailer.specialties.is_enabled'] = true;
$configs['retailer.specialties.can_select'] = true;

$configs['retailer.specialties.filter'] = \Salesfloor\API\Managers\Categories::SPECIALTIES_FILTER_ALGORITHM_EXCLUDE;
$configs['retailer.specialties.exclude'] = [
    'misc',
    '464b656ace0f8ba7b077c68256c267',   //accessories
    '8bca3219f8e2aa4e92efd030c76a72',   //bath and body
    '1b118f7455362fd6bcbfbcd586f77f',   //body oil
    'e4c5d727226621706755dd9bf85832',   //cleansers
    '7a1920d61156abc05a60135aefe8bc',   //Default
    'f19bd0844e53369373385609e28dbf',   //eye
    '9bbc9cafeb4245ccf8e23167fb4338',   //eye makeup
    'a24a768e413ef248fe5d5f7aab4537',   //face oil
    '1081ef16fd40b88eeb21f9d7ded46f',   //facial cleanser
    '7b5403d554fadd10b897d301d9763c',   //facial serum
    '5ead34d84ffd4939edc7a3315f0855',   //Gift Cards
    '1ab1fdec9dcbe5f7c71c24750fb43d',   //gift set
    '2c5ca38849d1b0b961844c065214ff',   //hair
    '7d20ad0c1b7288bf23f93c7c9e592b',   //ingestible
    'f6e924595f10c1d9b68b5ae6a448f0',   //lips
    '2ae04b8ed9779f2a4e8a1c400154c5',   //Makeup
    'b8c6911c8f6f7a23e342c44b99f6bf',   //skin care
    '3cc7a74537235f840651949729cd69',   //skincare kit
    'a8faf39e5f831c63809be4e8ba8ae7',   //sunscreen
];


// === SIDEBAR3 VARIANT ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 332;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 332;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = 'https://cdn.salesfloor.net/salesfloor-assets/credobeauty/credobeauty_sidebar_2023.png';
$configs['retailer.sidebar.v3.horizontalPosition'] = 'right';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = false;
$configs['retailer.sidebar.v3.minimize.desktop'] = false;
$configs['retailer.sidebar.v3.minimize.mobile'] = false;

$configs['retailer.services.carousel.display_mode'] = 'carousel';

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Email Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/credobeauty/credobeauty_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/credobeauty/credobeauty-logo-onboarding.png';

// === LIVE CHAT QUICK RESPONSES ===
$configs['mobile.quick_responses'] = [[
  'short' => "Customer Service",
  'full' => "Our client experience team can help you with this request. They are available via email at: <a href='mailto:<EMAIL>'><EMAIL></a>.",
], [
  'short' => "Give me a moment",
  'full' => "Please give me a moment and I will look into this for you.",
], [
  'short' => "Thank you and goodbye",
  'full' => "Thank you for chatting with us today! It will be my pleasure to help you with any clean beauty needs again! Thank you for choosing Credo and have a great day!",
], [
  'short' => "When a customer stops responding",
  'full' => "It looks like you've stepped away, I'm going to end this chat to open up the queue for other customers, but if you have further questions you can leave a message and we'd be more than happy to help you!",
], [
  'short' => "Busy in store",
  'full' => "I am so sorry, I have to step away to help an in-store customers, it suddenly got busy and my team needs some help! I will respond as soon as possible. Can't wait? You can close out of this chat and browser and start a new chat with another clean beauty expert, and they would be happy to help you!",
], [
  'short' => "Clean swap",
  'full' => "Welcome to your Clean Swap! Thank you for trusting us with your first step in switching over to clean. Credo bans over 2700+ ingredients with our Dirty List. Not only that but our entire company is based on The Credo Clean Standard which monitors safety, sourcing, ethics and sustainability of the products we carry. You really are in the BEST hands with Credo. This service is the perfect choice when swapping out your conventional products. Over the next 20-30 minutes we will do ALL the homework for you. Giving you recommendations that are the most similar to the products you're used to.",
], [
  'short' => "New to credo",
  'full' => "Welcome to Credo! Let's get you set up with an account so you can get 10% off your first purchase and start to collect loyalty points! Click here: <a target='_blank' href='https://credobeauty.com/account/register'>https://credobeauty.com/account/register</a> to create an account. You will then receive an email asking you to activate your account. Click that button and you are ready to go! You should receive your 10% off code within 5-10 minutes and you will need to enter this at checkout. Be sure to check your spam/junk folder!",
], [
  'short' => "Online return policy",
  'full' => "Online purchases that are gently used may be returned within 30 days for a refund. Please contact <a href='mailto:<EMAIL>'><EMAIL></a> with questions regarding the return process. If you need to return an item, can I help you select a better-suited item for you?",
], [
  'short' => "Store return policy",
  'full' => "For any products purchased in store, unused, within 30 days you are able to receive a refund. For any gently used products within 30 days of in store purchase, you are able to receive store credit in the form of a gift card. If you need to return an item that you purchased at one of our Credo locations, please contact that store directly to help you with the return process. The store contact information is listed on your receipt and on our stores tab of the website. If you need to return an item, can I help you select a better-suited item for you?",
], [
  'short' => "My Storefront Link",
  'full' => "storefront_link",
], [
  'short' => "Subscribe Link",
  'full' => "subscribe_link",
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = false;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;

$configs['mobile.retailer_can_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "(firstname + lastname[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Credo Clean Beauty Expert. Happy to provide a personal consultation for your skincare concerns, shade matching, or answer your questions and help you find you the best-in-clean products online or in store!\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "true",
    "import-contact": "false",
    "connect-social": {
    "twitter": "true"
  },
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === STOREFRONT Whitelist ===
$configs['redirect_whitelist.domains'] = [
    'jotform.com',
    'credobeauty.loopreturns.com',
    'accessible360.com',
    'next-world.myshopify.com',
];

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;

// ==== IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = true;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// == SERVICES GCP/AWS ===
$configs['services.queue'] = 'gcp';
$configs['services.metrics'] = 'stackdriver';
$configs['service.elasticsearch'] = 'gcp';

// === PushNotifications / FCM CONFIGS ===
$configs['service.pushnotification'] = 'fcm';

$configs['s3.bucket.provider']            = 'gcp';
$configs['geoip.s3bucket.provider']       = 'gcp';
$configs['logrotate.s3.provider']         = 'gcp';
$configs['logrotate_client.s3.provider']  = 'gcp';
$configs['rep-export.s3.provider']        = 'gcp';
$configs['sf.import_unsubs.s3.provider']  = 'gcp';

// Subscribe Unsubscribe Importer Configs (Listrack)
$configs['sf.import_sub_unsubs.s3.path'] = 'inbound/{env}/subscribe-unsubscribe/';
$configs['sf.import_sub_unsubs.s3.filename_regexp'] = '{retailer}-subscribe-unsubscribe-_\d{8}\d{6}.csv';
$configs['sf.import_unsubs.s3.region'] = 'us-east-1';
$configs['sf.import_unsubs.s3.signature'] = 'v4';
$configs['sf.import_unsubs.s3.bucket'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? 'sf-credobeauty'
        : 'sf-dev-credobeauty';

// leave on aws until its fully migrated
$configs['s3.image-bucket'] = 'salesfloor-assets';
$configs['s3.image-bucket.provider']        = 'aws';
$configs['mobile_s3_bucket.provider']       = 'aws';

$configs['crypto.inbound.general.private_key'] =
    $configs['env'] == Loader::ENV_STACK_PROD
        ? PATH_PLATFORM . '/services/keys/credobeauty/sf-gpg-private-credobeauty-prd.asc'
        : PATH_PLATFORM . '/services/keys/credobeauty/sf-gpg-private-credobeauty-stg.asc';

$configs['retailer.customer_tags.is_enabled'] = true;
$configs['retailer.customer_tags.is_read_only'] = true;

// Social Shop feature
$configs['retailer.shop_feed.enabled'] = true;

// Video Chat feature
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs["name_fmt.default.rep_display_name"]                                 = "{Fn} {Ln}";
$configs["name_fmt.default.storefront_product_comment"]                       = "{Fn} {Li.}";
$configs["name_fmt.default.rep_store_display_name"]                           = "{Fn} {Ln}";
$configs["name_fmt.default.rep_mode_email_from_name"]                         = "{Fn} {Ln}";
$configs["name_fmt.default.rep_mode_email_lookbook"]                          = "{Fn} {Ln}";
$configs["name_fmt.default.rep_mode_email_lookbook_subject"]                  = "{Fn} {Ln}";
$configs["name_fmt.default.email_autoresponder"]                              = "{Fn} {Ln}";
$configs["name_fmt.default.email_leads_forward_to_customer_rep_name"]         = "{Fn} {Ln}";
$configs["name_fmt.default.email_leads_forward_to_customer_cs_rep_from_name"] = "{Fn} {Ln}";
$configs["name_fmt.default.email_leads_email_to_customer_rep_name"]           = "{Fn} {Ln}";
$configs["name_fmt.default.chat_rep_name"]                                    = "{Fn} {Ln}";
$configs["name_fmt.default.chat_customer_name"]                               = "{Fn} {Ln}";
$configs["name_fmt.default.sidebar_carousel_rep_name"]                        = "{Fn} {Ln}";
$configs["sender_fmt.default.rep_mode_email_lookbook"]                        = "{rep}, {retailer}";

$configs['retailer.findrep_name_fmt'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

// ==== CI IMPORTERS ====
$configs['retailer.clienteling.mode'] = true;

// CI Customers
$configs['importer.' . Import::TYPE_CUSTOMER . '.fetcher']  = 'importer.fetcher.shopify_graphql.retailer_customer';
$configs['importer.' . Import::TYPE_CUSTOMER . '.ingestor'] = 'importer.ingestor.shopify_graphql.retailer_customer';
$configs['importer.' . Import::TYPE_CUSTOMER . '.loader']   = 'importer.retailer_customers.loader.earliest_external_date';

// CI Transactions
$configs['importer.' . Import::TYPE_TRANSACTION . '.fetcher']  = 'importer.fetcher.shopify_graphql.retailer_transaction';
$configs['importer.' . Import::TYPE_TRANSACTION . '.ingestor'] = 'importer.ingestor.shopify_graphql.retailer_transaction';
$configs['importer.' . Import::TYPE_TRANSACTION . '.loader']   = 'importer.retailer_customers_transaction.loader.earliest_external_date';

// ------------------- Shopify -------------------
$configs['shopify.url'] = 'Credo-Sandbox-Store.myshopify.com';
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['shopify.url'] = 'Next-World.myshopify.com';
}

/**
 * The following configs is only used by credobeauty: to overwrite the default values in the CI importers
 * $addOnDuplicateUpdateFieldsOverwritten at \Salesfloor\Services\Importer\RetailerCustomer\RetailerCustomerImporter::insertBulkRetailerCustomer
 * Because CredoBeauty use Shopify GraphQL importer, and they don't want change salesfloor 'is_subscribed' from this API
 */
$configs['sf.import_ci_customers.update_fields_excluded'] = ['is_subscribed'];

$configs['mobile_ios_appid']     = 'com.salesfloor.enterprise';
$configs['mobile_android_appid'] = 'com.salesfloor.enterprise';

// === CASL
$configs['retailer.contacts.delete_automated.enabled'] = true;

// === TASK ===
$configs['sf.task.automated.transaction.min'] = 15;

$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 14;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 4;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 10;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 3;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;

$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [5];
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 4;

$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 60;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 5;

$configs['retailer.add_customer_to_my_contacts.is_enabled'] = false;
$configs['retailer.add_customer_to_my_contacts.as_secondary'] = false;

$configs['importer.tasks.is_enabled'] = true;

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://credolive.credobeauty.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'credobeauty' : "{$configs['retailer.short_name']}-{$configs['env']}";

$configs['retailer.store.open_hour'] = 8;
$configs['retailer.store.close_hour'] = 21;
$configs['retailer.store.default_timezone'] = 'America/Chicago';

// Enable Blackout feature
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
$configs['retailer.clienteling.customers.communication.blackout.period'] = 3600 * 24;
$configs['retailer.clienteling.customers.communication.blackout.strategy'] = \Salesfloor\Services\RetailerCustomerNotificationBlackout::STRATEGY_MATCH_TABLE_AND_CUSTOMER;

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-219130482-2';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-219130482-3';
    $configs['retailer.google.ga_services']          = 'UA-219130482-4';
    $configs['retailer.google.ga_storefront']        = 'UA-219130482-5';
    $configs['retailer.google.ga_socialshop']        = 'UA-219130482-6';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-219130482-1';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MG51e4d79e5f6d1f9d4856c055d470b81c';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MG569b8979fdb7af43ccf09bec3ea5ff73';
}
