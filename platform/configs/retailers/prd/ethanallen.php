<?php

use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Importer\Product\Importer as ProductImporter;
use Symfony\Component\Validator\Constraints;
use Salesfloor\Models\Customer;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 30; // 30 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'ethanallen';
$configs['retailer.short_name'] = 'ethanallen';
$configs['retailer.brand_name'] = 'Ethan Allen';
$configs['retailer.pretty_name'] = 'Ethan Allen';

$configs['retailers.name'] = 'ethanallen';
$configs['retailers.short'] = 'ethanallen';

$configs['firebase.retailername'] = 'ethanallen';

$configs['retailer.application_start_date'] = '2024-10-18 00:00:00';

$configs['retailer.hq_address'] = 'P.O. Box 1966 Danbury, CT 06813';
$configs['retailer.hq_address.street'] = 'P.O. Box 1966 Danbury';
$configs['retailer.hq_address.city'] = 'Danbury';
$configs['retailer.hq_address.isoCountry'] = 'US';
$configs['retailer.hq_address.postalCode'] = 'CT 06813-1966';
$configs['retailer.country.code'] = 'US';
$configs['stores.max_distance'] = '9001'; // distance in km

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.ethanallen.com';
$configs['retailer.site'] = 'https://www.ethanallen.com';
$configs['retailer.domain'] = 'ethanallen.com';
$configs['retailer.contacturl'] = 'https://www.ethanallen.com/en_US/cs-landing.html';
$configs['retailer.email_domain'] = 'ethanallen.com';
$configs['retailers.url'] = 'https://www.ethanallen.com';

// === PRODUCTS ===
$configs['update_products.max_errors'] = 2500;
$configs['update_products.min_products'] = 2000;
$configs['update_products.min_categories'] = 6;
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;
$configs['importer.product.s3.filename_regexp'] = '{retailer}-product-\d{8}-\d{6}.csv';

$configs['importer.product.validation.amend'] = [
    // Set Gtin as optional
    ProductImporter::COL_GTIN                                      => [
        new Constraints\Length(['max' => 255]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_TITLE                     => [
        new Constraints\NotBlank(),
        // Not required because we'll put a default one if empty
        new Constraints\Length(['max' => 50]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_ID                        => [
        new Constraints\NotBlank(),
        // Not required because we'll put a default one if empty
        new Constraints\Length(['max' => 30]),
    ],
];

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 12;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'max_products' => 12, 'min_products' => 12, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'max_products' => 8, 'min_products' => 8, 'show_comments' => 1],
  ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Associate';
$configs['retailer.social_networks'] = [];
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 4;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => false, 'min_posts' => 0, 'max_posts' => 8];

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/ethanallen/ethanallen-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/ethanallen/ethanallen-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = false;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

$configs['retailer.services.appointment.types'] = [
    ['type' => 'virtual', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false],
    ['type' => 'store', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false],
];

// === APPOINTMENT MANAGEMENT ===
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['mobile.retailer_has_specialties'] = false;
$configs['retailer.specialties.is_enabled'] = false;
$configs['retailer.specialties.can_select'] = false;
$configs['retailer.specialties.is_required'] = false;

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'right';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;

// === VIDEO CHAT ===
$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/ethanallen/ethanallen_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/ethanallen/ethanallen-logo-onboarding.png';

$configs['mobile.quick_responses'] = [];
$configs['mobile.extra_quick_responses'] = [];
$configs['mobile.sms.quick_responses'] = [];

$configs['customerservice.can_forward'] = false;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.barcode_scanner.contacts.enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;
$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;

$configs['service.personal_shopper'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;
$configs['retailer.has_personal_shopper'] = false;

$configs['mobile.retailer_can_browse_library'] = false;
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['retailer.customer_tags.is_enabled'] = true;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "true",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

$configs['retailer.add_customer_to_my_contacts.is_enabled'] = true;
$configs['retailer.add_customer_to_my_contacts.default_subscription_status'] = Customer::SUBSCRIPTION_STATUS_SUBSCRIBED;
$configs['retailer.add_customer_to_my_contacts.as_secondary'] = true;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;

// === IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = false;
$configs['retailer.onboarding.step.add_contacts'] = false;

// ==== IMPORT CUSTOMERS ===
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = '{retailer}-customers-\d{8}-\d{6}\.csv';
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['retailer.clienteling.import.notification.emails'] = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];
}

// TASKS
$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 10;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 10;
$configs['sf.task.automated.transaction.min'] = 0;

// CI Transaction Followup (Attributed)
$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 10;

// CI Soon-to-Lapse
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
    'default' => [
        'days_search_back' => [180],
        'max_per_owner'    => 10,
    ],
];

$configs['user-stores.user_assigned_stores.is_enabled'] = true;

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://mydesigner.ethanallen.com';

// === CONNECT 2 ===
// === Connect V2.0 ===
$configs['connect2.enabled'] = true;
$configs['connect2.saas.organizationId'] = 'BBMHV6Z130LgSZvhQjHP';
$configs['connect2.saas.brandId'] = 'gOXkltJb4A9UQcWcYv8u';
switch ($configs['env']) {
    case Loader::ENV_STACK_PROD:
        $configs['connect2.bot.integrationId'] = '9762f6ce-8c8f-488c-92a3-8b76d7911e3e';
        break;
    case 'box01':
        $configs['connect2.bot.integrationId'] = '4680a09f-b982-4cc8-8a3d-1003f6c81ae5';
        $configs['typhoon.url'] = 'https://box01.sandbox.salesfloor.net/typhoon';
        break;
    case 'box02':
        $configs['connect2.bot.integrationId'] = 'de789a1b-861a-4d73-932e-a5249a781103';
        $configs['typhoon.url'] = 'https://box02.sandbox.salesfloor.net/typhoon';
        break;
    case 'box03':
        $configs['connect2.bot.integrationId'] = '861320ac-5c8d-490a-ad15-21adb48a92d4';
        $configs['typhoon.url'] = 'https://box03.sandbox.salesfloor.net/typhoon';
        break;
    case 'qa04':
        $configs['connect2.bot.integrationId'] = '472f6c83-36f1-46cc-adb3-7a3f805c20ad';
        $configs['typhoon.url'] = 'https://qa04.develop.salesfloor.net/typhoon';
        break;
    case 'qa05':
        $configs['connect2.bot.integrationId'] = 'bff4be12-aa26-4e37-9a03-3096841d0639';
        $configs['typhoon.url'] = 'https://qa05.develop.salesfloor.net/typhoon';
        break;
    case 'qa06':
        $configs['connect2.bot.integrationId'] = 'd805f592-4436-4f4d-a5eb-b7d6c1532f6a';
        $configs['typhoon.url'] = 'https://qa06.develop.salesfloor.net/typhoon';
        break;
    case 'stg':
    default:
        $configs['connect2.bot.integrationId'] = 'b1a0d40c-9d26-4f9f-b9f7-b4d56bba1cda';
        $configs['typhoon.url'] = 'https://stg.develop.salesfloor.net/typhoon';
        break;
}

if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'G-0YPSVSM7MX';
    $configs['retailer.google.ga_backoffice_mobile'] = 'G-CW23YEYV3E';
    $configs['retailer.google.ga_services']          = 'G-EGQF4XVRLL';
    $configs['retailer.google.ga_storefront']        = 'G-24VLBNRVWT';
    $configs['retailer.google.ga_socialshop']        = 'G-N54NE323W0';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'G-YD8GGMQT6Q';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required']               = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MG52ad55f41ac80a61ac199912189dda08';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply']  = 'MG08098c558aa51223e66bfcab14da0ac0';
}

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'ethanallen' : "{$configs['retailer.short_name']}-{$configs['env']}";
