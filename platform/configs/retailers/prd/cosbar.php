<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\GroupPermissionService as Perms;
use Salesfloor\Services\Importer\Product\Importer as ProductImporter;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Date;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Constraints\Required;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 30; // 30 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = true;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'cosbar';
$configs['retailer.short_name'] = 'cosbar';
$configs['retailer.brand_name'] = 'Cos Bar';
$configs['retailer.pretty_name'] = 'Cos Bar';

$configs['retailers.name'] = 'cosbar';
$configs['retailers.short'] = 'cosbar';

$configs['firebase.retailername'] = 'cosbar';

$configs['retailer.application_start_date'] = '2020-08-27 00:00:00';

$configs['retailer.hq_address'] = '1964 Westwood Blvd #235 Los Angeles, CA 90025';

$configs['retailer.hq_address.street'] = "1964 Westwood Blvd #235";
$configs['retailer.hq_address.city'] = "Los Angeles";
$configs['retailer.hq_address.region'] = "California";
$configs['retailer.hq_address.isoCountry'] = "US";
$configs['retailer.hq_address.postalCode'] = "90025";
$configs['retailer.hq_address.customerName'] = "Cos Bar USA, Inc.";

$configs['stores.max_distance'] = '9001'; // distance in km

$configs['retailer.chat.find_nearby_stores'] = true;
$configs['retailer.chat.find_nearby_stores.max_distance'] = '9001'; // Nationwide

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://www.cosbar.com';
$configs['retailer.site'] = 'https://www.cosbar.com';
$configs['retailer.domain'] = 'cosbar.com';
$configs['retailer.contacturl'] = 'https://www.cosbar.com/contacts';
$configs['retailer.email_domain'] = 'cosbar.com';
$configs['retailer.storefront.search_url'] = 'https://www.cosbar.com/search/?q=';

$configs['retailers.url'] = 'https://www.cosbar.com';

// === PRODUCTS ===
$configs['update_products.max_errors'] = 10000;
$configs['update_products.min_products'] = 2000;
$configs['update_products.min_categories'] = 100;

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = false;

$configs['products.expanded_variants.enabled'] = true;
$configs['importer.products.has_variants'] = true;

// @TODO: use `importer.product.validation.amend` instead when the code is merged (on the benbridge branch right now)
$configs['importer.product.validation'] = [
    ProductImporter::COL_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_PARENT_ID => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_GTIN => [
        new Length(['max' => 255]),
    ],
    ProductImporter::COL_TITLE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 250]),
    ],
    // Description not required because we'll replace it with the title if empty
    ProductImporter::COL_BRAND => [
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_LINK => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_IMAGE_LINK => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_AVAILABLE => [
        new NotBlank(),
        new Required(),
        new Choice(['choices' => ['0', '1']]),
    ],
    ProductImporter::COL_PRICE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 11]),
        new Regex(['pattern' => '/^[0-9]+(\.\d+)?$/']),
    ],
    ProductImporter::COL_SALE_PRICE => [
        new Length(['max' => 11]),
        // The additional part of the regex is to pass validation if no sale price is provided.
        new Regex(['pattern' => '/^[0-9]+(\.\d+)?$|^$/']),
    ],
    ProductImporter::COL_SALE_START_DATE => [
        new Date(),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_SALE_END_DATE => [
        new Date(),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL1_ID => [
        new NotBlank(),
        // Not required because we'll put a default one if empty
        new Length(['max' => 30]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL1_TITLE => [
        new NotBlank(),
        // Not required because we'll put a default one if empty
        new Length(['max' => 50]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_ID => [
        new NotBlank(),
        // Not required because we'll put a default one if empty
        new Length(['max' => 30]),
    ],
    ProductImporter::COL_CATEGORY_LEVEL2_TITLE => [
        new NotBlank(),
        // Not required because we'll put a default one if empty
        new Length(['max' => 50]),
    ],
    ProductImporter::COL_ARRIVAL_DATE => [
        new Regex(['pattern' => '/^(?!0000-00-00)\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$/']),
        new Length(['max' => 45]),
    ],
    ProductImporter::COL_LANGUAGE_CODE => [
        new NotBlank(),
        new Required(),
        new Length(['max' => 10]),
    ],
    ProductImporter::COL_ATTR_1_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_1_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_1_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_1_SWATCH => [
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_ATTR_2_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_2_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_2_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_2_SWATCH => [
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_ATTR_3_NAME => [
        new Length(['max' => 32]),
    ],
    ProductImporter::COL_ATTR_3_VALUE => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_3_GROUP => [
        new Length(['max' => 150]),
    ],
    ProductImporter::COL_ATTR_3_SWATCH => [
        new Length(['max' => 500]),
    ],
    ProductImporter::COL_IS_DEFAULT => [
        new NotBlank(),
        new Required(),
        new Choice(['choices' => ['0', '1']]),
    ]
];

// === STOREFRONT ===
$configs['retailer.num_deals'] = 8;
$configs['retailer.num_top_picks'] = 8;
$configs['use_new_arrivals_list'] = false;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'max_products' => 8, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'max_products' => 8, 'show_comments' => 1],
  ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 4, 'show_comments' => 1]
];

$configs['retailer.associate'] = 'Beauty Specialist';
$configs['retailer.social_networks'] = array("twitter");
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 8;
$configs['retailer.trending_recommendations.min'] = 2;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 8, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'cosbar';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/cosbar/cosbar-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/cosbar/cosbar-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

$configs['retailer.virtual.option.video-chat'] = true;
$configs['retailer.virtual.option.video-chat.2ways'] = true;

$configs['retailer.services.appointment.types'] = [
  [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
  [ 'type' => 'virtual', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false ],
  [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
];

$configs['retailer.services.hide_optional_phone_input'] = true;

$configs['retailer.services.findarep.is_enabled'] = true;
$configs['retailer.reps_have_specialties'] = false;
$configs['retailer.can_user_select_specialty'] = false;

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 290;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 290;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'logo';
$configs['retailer.sidebar.v3.logopath'] = 'https://res.cloudinary.com/salesfloor-net/image/upload/v1598971140/cosbar/salesfloor-assets/cosbar-sidebar-logo.png';
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = true;
$configs['retailer.sidebar.v3.minimize.mobile'] = true;
$configs['retailer.sidebar.v3.animation.enabled'] = true;
$configs['retailer.sidebar.v3.animation.type'] = 'sidebarBlinker';
$configs['retailer.sidebar.v3.alternateTaglines'] = ['Connect with a Beauty Specialist', 'Get Expert Beauty Help'];

$configs['retailer.services.carousel.display_mode'] = 'carousel';

// === CHAT/Widgets ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/cosbar/cosbar_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/cosbar/cosbar-logo-onboarding.png';

$configs['mobile.quick_responses'] = [[
  'short' => "Give me a moment",
  'full' => "Please give me a moment and I will look into this for you.",
], [
  'short' => "End Chat",
  'full' => "It was wonderful speaking with you today. If there's nothing else I can assist you with, I'll go ahead and close this chat. Thank you!",
], [
  'short' => "My Storefront Link",
  'full' => "storefront_link",
], [
  'short' => "Subscribe Link",
  'full' => "subscribe_link",
], [
  'short' => "Appointment Link",
  'full' => "appointment_link",
],[
  'short' => "Out of Stock",
  'full' => "Unfortunately, we currently do not have this item in stock. Can I make a suggestion for a similar item?",
]];

$configs['mobile.extra_quick_responses'] = [[
  'short' => 'Customer Support',
  'full' => "Our Client Care team can look into this for you. You can reach out to them <NAME_EMAIL> or calling ************. Client Care is available to you from 8 am - 5 pm PT Monday through Friday."
], [
  'short' => 'Customer stopped responding',
  'full' => "I haven't heard from you in a while. If you still need my help, please feel free to restart this chat at any time."
], [
  'short' => 'Need more time',
  'full' => "I'm still looking into this for you. Thank you for your patience."
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = true;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "((firstname || '') + (lastname || '')[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "\"Hello! I am a Cos Bar Beauty Specialist. I can answer your questions and help you find what you’re shopping for at your local store or online.\""
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "false",
    "import-contact": "true",
    "connect-social": {
		"twitter": "true"
	},
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
    }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
      "enter-email": {
        "alias": "false"
      },
      "pick-store": "false",
      "take-picture": "false",
      "choose-specialties": "false",
      "import-contact": "false",
      "connect-social": "false",
      "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;

// ==== IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = true;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// ==== APPOINTMENT MANAGEMENT ===
$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;

$configs['retailer.available_services'] = [
  [ 'type' => 'chat', 'modal' => 'popup', 'maxHeight' => 800 ],
  [ 'type' => 'inscription', 'modal' => 'iframe', 'maxHeight' => 650 ],
  [ 'type' => 'question', 'modal' => 'iframe', 'maxHeight' => 900 ],
  [ 'type' => 'appointment', 'modal' => 'iframe', 'maxHeight' => 1500 ],
  [ 'type' => 'profile', 'modal' => 'embedded', 'maxHeight' => 465 ],
];

$configs['retailer.footer.breakpoint'] = 1024;

// In case of merge conflict, overwrite this side.
// See https://salesfloor.atlassian.net/browse/SF-23908
$configs['firebase.retailername'] = 'cosbar';

$configs['importer.contacts.tag_delimiter'] = [';'];
$configs['retailer.customer_tags.is_enabled'] = true;

$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;

$configs['redirect_whitelist.domains'] = [
    'cosbarbeauty.myshopify.com',
    'image.s5a.com',
    'thebay.com',
];

$configs['retailer.store.close_hour'] = 17;
$configs['retailer.store.open_hour'] = 10;

$configs['sf.task.automated.enabled'] = true;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = [3, 21, 90];
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 10;

// CI Soon-to-Lapse
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = true;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.dynamic_rule'] = [
    'default' => [
        'days_search_back' => [365],
        'max_per_owner'    => 5,
    ],
];

// ******* CI Transactions
$configs['retailer.clienteling.enabled.transactions'] = true;

$configs['retailer.clienteling.transactions.attribution.include_trx_date'] = false;

// ******* CI stats
$configs['retailer.clienteling.enabled.customer_stats'] = true;

// ==== IMPORT CUSTOMERS ====
$configs['retailer.clienteling.mode'] = true;
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'cosbar-customers-\d{8}-\d{6}\.csv';
$configs['sf.import_ci_transactions.s3.filename_regexp'] = 'cosbar-transactions-\d{8}-\d{6}\.csv';

// Store appointment hours of local time zone
$configs['retailer.store_appointment_hours'] = [
    [
        'open'  => '10',
        'close' => '17',
        'is_available' => '1',
    ],
];
$configs['retailer.store_appointment_hours.is_enabled'] = true;
$configs['retailer.store_appointment_hours.group_permissions'] = [2,3];
$configs['retailer.store_appointment_hours.first_day_of_week'] = 0;

// DEVOPS-6375 turn off/change configs for system-generated tasks
$configs['sf.task.automated.nag_update_storefront_products.days_search_back'] = 28;
$configs['sf.task.automated.nag_update_storefront_products.days_recur_after'] = 28;
$configs['sf.task.automated.nag_share_update.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.enabled'] = false;

$configs['importer.customer_remapping.s3.path'] = 'inbound/{env}/crm-remapping';
$configs['importer.customer_remapping.s3.filename_regexp'] = '{retailer}-ci-customer-remapping-\d{8}-\d{6}\.csv';
$configs['importer.customer_remapping.encrypted'] = false;

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-177453853-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-177453853-2';
    $configs['retailer.google.ga_services']          = 'UA-177453853-3';
    $configs['retailer.google.ga_storefront']        = 'UA-177453853-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-177453853-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-177453853-6';

    // ------------------- twilio a2p10dlc setting -------------------
    $configs['retailer.services.channel.text.a2p10dlc.is_required'] = true;
    $configs['retailer.services.channel.text.a2p10dlc.message_service.associate'] = 'MG4c0bf3fe09d90eea8d428d2321c5666c';
    $configs['retailer.services.channel.text.a2p10dlc.message_service.no_reply'] = 'MG3d1aa07214ff45bf1d29bf888667ae54';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://connect.cosbar.com';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'cosbar' : "{$configs['retailer.short_name']}-{$configs['env']}";

// === LURE ===
$configs['lure.cta_text'] = [
    'en_US' => 'Connect with a Beauty Specialist',
    'fr_CA' => 'Joindre un Spécialiste Beauté',
];
$configs['lure.placement'] = [
    'mobile' => ['position' => 'left', 'offsetX' => 20, 'offsetY' => 45],
    'desktop' => ['position' => 'left', 'offsetX' => 40, 'offsetY' => 10],
];

// === Connect V2.0 ===
$configs['connect2.bot.integrationId'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'b19060bf-71db-4409-9973-d3c10b83d1b0' : "858bf4a3-605e-4f24-b28f-77659ec4f5f4";
$configs['connect2.saas.organizationId'] = "BBMHV6Z130LgSZvhQjHP";
$configs['connect2.saas.brandId'] = "wohfour2RY9k4mkIXQjy";
$configs['connect2.enabled'] = true;

// Enabling the green dot on the lure
$configs['retailer.chat_availability_status.sidebar.enabled'] = true;
