<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Silex\Application;
use Salesfloor\Services\GroupPermissionService as Perms;

// === HIGH LEVEL CONFIGS ===
Perms::$permissionsMap['create-user'] = Perms::STORE_MANAGER;
$configs['retailer.username_tmpl'] = SF_USERNAME_TMPL_FN_LN;
$configs['retailer.alias_tmpl'] = SF_USERNAME_TMPL_FN_LI;
$configs['retailer.rep.display_name_tmpl'] = \Salesfloor\Services\NameSuggester::SF_USERNAME_TMPL_FN_LN;

$configs['s3.userimage.provider'] = 'gcp';

$configs['retailer.storepage_mode'] = false;
$configs['retailer.default_currency'] = 'USD';
$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 7; // 7 days in ms
$configs['retailer.storefront.is-new-stack'] = true;

$configs['retailer.corporate-email.required'] = false;

// === RETAILER(S) NAME(S)
$configs['retailer.name'] = 'demosalesfloor';
$configs['retailer.short_name'] = 'demosalesfloor';
$configs['retailer.brand_name'] = 'demosalesfloor';
$configs['retailer.pretty_name'] = 'demosalesfloor';

$configs['retailers.name'] = 'demosalesfloor';
$configs['retailers.short'] = 'demosalesfloor';

$configs['firebase.retailername'] = 'demosalesfloor';

$configs['retailer.application_start_date'] = '2021-11-02 00:00:00';

$configs['retailer.hq_address'] = '651 Notre-Dame St W Suite 350, Montreal, Quebec H3C 1H9, Canada';
$configs['retailer.hq_address.street'] = "651 Notre-Dame St W Suite 350";
$configs['retailer.hq_address.city'] = "Montreal";
$configs['retailer.hq_address.region'] = "QC";
$configs['retailer.hq_address.isoCountry'] = "CA";
$configs['retailer.hq_address.postalCode'] = "H3C 1H9";
$configs['retailer.hq_address.customerName'] = "demosalesfloor";

$configs['retailer.country.code'] = 'CA';
$configs['stores.max_distance'] = '100'; // distance in km
$configs['stores.max_distance.per_store.enable'] = true;  // default set to false
$configs['retailer.combine_countries'] = [
    'US' => ['US', 'CA'],
    'CA' => ['US', 'CA'],
];

// === RETAILER(S) URL(S)
$configs['retailer.url'] = 'https://demo.salesfloor.net/';
$configs['retailer.site'] = 'https://demo.salesfloor.net/';
$configs['retailer.domain'] = 'salesfloor.net/';
$configs['retailer.contacturl'] = 'https://salesfloor.net/contact-us/';
$configs['retailer.email_domain'] = 'salesfloor.net';
$configs['retailer.mainSearchUrl'] = 'https://demo.salesfloor.net/search?q=';

$configs['retailers.url'] = 'https://demo.salesfloor.net/';

// === PRODUCTS ===
$configs['importer.products.has_variants'] = true;
$configs['products.expanded_variants.enabled'] = true;
$configs['update_products.max_errors'] = 100;
$configs['update_products.min_products'] = 100;
$configs['update_products.min_categories'] = 10;

$configs['product_img_size'] = 250;
$configs['product.panels.new-arrivals.nominated-products'] = false;

$configs['importer.products.file_pattern'] = 'TO BE REPLACED';

// === STOREFRONT ===
$configs['retailer.num_deals'] = 4;
$configs['retailer.num_top_picks'] = 4;
$configs['use_new_arrivals_list'] = true;

$configs['storefront.available_product_grids'] = [
  ['type' => 'top-picks', 'max_products' => 4, 'min_products' => 1, 'show_comments' => 1],
  ['type' => 'new-arrivals', 'max_products' => 4, 'min_products' => 1, 'show_comments' => 1],
  ['type' => 'recommendations', 'max_products' => 4, 'min_products' => 1, 'show_comments' => 1],
];

$configs['retailer.associate'] = 'Sales Associate';
$configs['retailer.social_networks'] = array("twitter");
$configs['retailer.publisher_storefront'] = true;

$configs['storefront.store_events'] = ['active' => false];
$configs['storefront.static_content'] = ['active' => false, 'has_title' => false];

// === STOREFRONT MENU ===
$configs['storefront.menu.isdynamic'] = true;
$configs['storefrontmenu.config.path'] = PATH_NEW_CONFIGS . '/common/storefrontmenu/' . $configs['retailers.current'] . '.php';

// === BACKOFFICE ===
$configs['retailer.trending_recommendations'] = true;
$configs['retailer.trending_recommendations.max'] = 4;
$configs['retailer.trending_recommendations.min'] = 2;
$configs['retailer.trending_recommendations.title'] = "Trending Recommendations";
$configs['retailer.trending_recommendations.backoffice_settings_panel'] = true;

// === INSTAGRAM ===
$configs['storefront.instagram'] = ['active' => true, 'min_posts' => 0, 'max_posts' => 8];
$configs['retailer.instagram.account'] = 'sfdemostore';

// === LOGOS ===
$configs['retailer.onboarding_logo'] = 'https://cdn.salesfloor.net/salesfloor-assets/demosalesfloor/demosalesfloor-new-logo.png';
$configs['retailer.bo_logo_url'] = 'https://cdn.salesfloor.net/salesfloor-assets/demosalesfloor/demosalesfloor-new-logo.png';

// === ONBOARDING ===
$configs['onboarding.choose_alias'] = true;
$configs['onboarding.alias_match_username'] = false;

// === SERVICES ===
$configs['queue.byStore'] = [];

// CANS Routing mode
$configs['retailer.chat.routing.mode'] = 'dynamic';

// Legacy mode metric logging
$configs['services.record_chat_metrics_cans.enabled']  = false;

$configs['retailer.services.appointment.types'] = [
    ['type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false],
    ['type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => false],
    ['type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false],
];

$configs['retailer.services.appointment_management.is_enabled'] = true;
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;
$configs['retailer.services.appointment.all.is_enabled'] = true;

$configs['retailer.services.findarep.is_enabled'] = true;

$configs['mobile.retailer_has_specialties'] = true;
$configs['retailer.specialties.is_enabled'] = true;
$configs['retailer.specialties.can_select'] = true;
$configs['retailer.specialties.is_required'] = false;

// === SIDEBAR3 ===
$configs['retailer.sidebar.v3.variant'] = true;
$configs['retailer.sidebar.v3.media.desktop.width'] = 280;
$configs['retailer.sidebar.v3.media.desktop.height'] = 60;
$configs['retailer.sidebar.v3.media.mobile.width'] = 280;
$configs['retailer.sidebar.v3.media.mobile.height'] = 60;
$configs['retailer.sidebar.v3.enabled'] = true;
$configs['retailer.sidebar.v3.mode'] = 'single';
$configs['retailer.sidebar.v3.logopath'] = null;
$configs['retailer.sidebar.v3.horizontalPosition'] = 'left';
$configs['retailer.sidebar.v3.tagline'] = true;
$configs['retailer.sidebar.v3.location'] = true;
$configs['retailer.sidebar.v3.minimize.desktop'] = false;
$configs['retailer.sidebar.v3.minimize.mobile'] = false;

// === MOBILE ===
$configs['mobile.email_me_label'] = 'Contact Us/Email Me';

$configs['mobile.menu_logo_path'] = '/img/retailers/demosalesfloor/demosalesfloor_t.png';
$configs['mobile.onboarding_logo_path'] = '/img/retailers/demosalesfloor/demosalesfloor-logo-onboarding.png';

$configs['mobile.extra_quick_responses'] = [[
  'short' => 'Customer Support',
  'full' => 'Our Customer Service Team can help you with this request. They are also available via phone at +************,'
], [
  'short' => 'Give me a moment',
  'full' => 'Please give me a moment and I will look into this for you.',
], [
  'short' => 'Thank you and goodbye',
  'full' => 'Thank you for your request. It will be my pleasure to serve you again. Have a great day.',
], [
  'short' => 'Out of stock',
  'full'  => 'Unfortunately we currently do not have this in stock, but you can buy it online by clicking here',
]];

$configs['customerservice.can_forward'] = true;

$configs['mobile.product_barcode_scanner'] = true;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_pinterest_enabled'] = true;
$configs['mobile.share_facebook_enabled']  = true;

$configs['mobile.retailer_can_browse_library'] = true;
$configs['mobile.retailer_can_share_from_browse_library'] = true;

$configs['mobile.retailer_can_change_retailer_id'] = false;

$configs['mobile.specificities'] = <<<JSON
{
  "pages": {
    "enter-token": {
        "token": "true"
    },
    "create-user": {
        "username": "((firstname || '') + (lastname || '')).replace(/\\\s/g, '').toLowerCase()",
        "password": "true",
        "confirm": "true"
    },
    "enter-email": {
        "email": "email",
        "phone": "true",
        "alias": {
            "disabled": "false",
            "value": "(firstname + lastname[0]).replace(/\\\s/g, '').toLowerCase()"
        },
        "storefront": "BaseUrl + '/' + alias"
    },
    "personal-info": {
        "firstname": "firstname || ''",
        "lastname": "lastname || ''"
    },
    "pick-store": {
        "store": "store",
        "introduction": "Hello! I am a Salesfloor Store Sales Associate. I can answer your questions and help you find what you’re shopping for at your local store or online."
    },
    "out-of-store": "true",
    "take-picture": "true",
    "choose-specialties": "true",
    "import-contact": "true",
    "connect-social": {
      "twitter": "true"
  },
    "congrats": "false"
  },

  "overrides": [{
    "rules": "selling_mode == 0",
    "override": {
        "enter-email": "false",
        "pick-store": "false",
        "out-of-store": "false",
        "take-picture": "true",
        "choose-specialties": "false",
        "import-contact": "false",
        "connect-social": "false",
        "details": "false"
    }
  }, {
    "rules": "selling_mode == 0 && creation_source == 'feed'",
    "override": {
      "enter-email": {
        "alias": "false"
      },
     "pick-store": "false",
     "out-of-store": "false",
     "take-picture": "true",
     "choose-specialties": "false",
     "import-contact": "false",
     "connect-social": "false",
     "details": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 1",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false"
    }
  }, {
    "rules": "group > 3 && selling_mode == 0",
    "override": {
    "enter-email": {
    "alias": "false"
    },
    "pick-store": "false",
    "take-picture": "false",
    "choose-specialties": "false",
    "import-contact": "false",
    "connect-social": "false",
    "details": "false"
    }
  }]
}

JSON;

// === SHOP PAGE TRACKING ===
$configs['retailer.shoppage.tracking.additional_params'] = false;
$configs['retailer.shoppage.tracking.leftpad_store_id'] = false;

// === TEXT MESSAGING ===
$configs['messaging.text.enabled'] = true;
$configs['messaging.text.multiple-recipients.enabled'] = true;
$configs['retailer.services.channel.text.enabled'] = true;

// ==== IMPORT CONTACTS ===
$configs['retailer.contacts.import_enabled'] = true;

$configs['retailer.customer_tags.is_enabled'] = true;

// === EXPORTER settings ===
$configs['exporter.public_bucket_duplicated.is_enabled']  = false;
$configs['exporter.private_bucket_duplicated.is_enabled'] = true;

// === DEFAULT AVATAR ===
$configs['retailer.rep_avatar_picture.random_default'] = [];

// Social Shop feature
$configs['retailer.shop_feed.enabled'] = true;

// Video Chat feature
$configs['retailer.chat.option.video-chat'] = true;

$configs['retailer.clienteling.mode'] = true;

// ==== IMPORT CUSTOMERS ===
$configs['sf.import_ci_customers.s3.filename_regexp'] = 'demosalesfloor-customers-\d{8}-\d{6}\.csv';

$configs['sf.import_ci_stats.s3.filename_regexp'] = 'demosalesfloor-stats-\d{8}-\d{6}\.csv';

$configs['sf.import_ci_transactions.s3.filename_regexp'] = 'demosalesfloor-transactions-\d{8}-\d{6}\.csv';

// == SERVICES GCP/AWS ===
$configs['services.queue'] = 'gcp';
$configs['services.metrics'] = 'stackdriver';
$configs['service.elasticsearch'] = 'gcp';

// === PushNotifications / FCM CONFIGS ===
$configs['service.pushnotification'] = 'fcm';

$configs['s3.bucket.provider']            = 'gcp';
$configs['geoip.s3bucket.provider']       = 'gcp';
$configs['logrotate.s3.provider']         = 'gcp';
$configs['logrotate_client.s3.provider']  = 'gcp';
$configs['rep-export.s3.provider']        = 'gcp';
$configs['sf.import_unsubs.s3.provider']  = 'gcp';

// leave on aws until its fully migrated
$configs['s3.image-bucket'] = 'salesfloor-assets';
$configs['s3.image-bucket.provider']        = 'aws';
$configs['mobile_s3_bucket.provider']       = 'aws';

$configs['product.panels.new-arrivals.nominated-products'] = true;
$configs['product.panels.top-picks.nominated-products'] = true;
$configs['retailer.customer_tags.is_enabled'] = true;
$configs['sf.import_ci_stats.s3.filename_regexp'] = 'demosalesfloor-statistics-\d{8}-\d{6}\.csv';

$configs['stores.max_distance'] = '9001'; // "Nationwide"
$configs['retailer.chat.find_nearby_stores.max_distance'] = '9001'; // Nationwide

$configs['sf.task.automated.transaction.min'] = .01;
// SF Sale Transaction Follow-Up (Attributed Associate/Store)
$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 1;
$configs['sf.task.automated.new_rep_transaction.max_per_owner'] = 10;
// CI Sale Transaction Follow-Up (Primary Associate/Store)
$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 1 ];
$configs['sf.task.automated.new_retailer_transaction_filtered.max_per_owner'] = 10;
// CI Sale Transaction Follow-Up (Attributed Associate/Store)
$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.days_search_back'] = 1;
$configs['sf.task.automated.retailer_transaction_employee_assigned.max_per_owner'] = 10;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;
//Soon-to Lapse Customer (Primary Associate/Store)
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 1;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.max_per_owner'] = 10;

$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs['mobile.retailer_can_change_retailer_id'] = true;

$configs['retailer.instagram.account'] = 'salesfloor.inc';

$configs['queue.byStore'] = [];

$configs['retailer.num_top_picks'] = 8;
$configs['retailer.num_deals'] = 8;

$configs['customerservice.email'] = '<EMAIL>';

$configs['product.panels.refresh.autoselected.frequency'] = 0;

$configs['retailer.multiple_email_templates.enabled'] = true;

$configs['user-stores.user_assigned_stores.is_enabled'] = true;

// Put all configs that change for production ONLY in this block
if ($configs['env'] == Loader::ENV_STACK_PROD) {
    $configs['customerservice.email'] = '<EMAIL>';
    $configs['retailer.emails.no_reply_address'] = '<EMAIL>';

    // === Google Analytics ===
    $configs['retailer.google.ga_backoffice_web']    = 'UA-*********-1';
    $configs['retailer.google.ga_backoffice_mobile'] = 'UA-*********-2';
    $configs['retailer.google.ga_services']          = 'UA-*********-3';
    $configs['retailer.google.ga_storefront']        = 'UA-*********-4';
    $configs['retailer.google.ga_socialshop']        = 'UA-*********-5';
    $configs['retailer.google.ga_backoffice_web_v2'] = 'UA-*********-6';
}

// This is a temporary patch for the configs server
$configs['retailer.prd.sf_domain'] = 'https://stores.demo.salesfloor.net';

// This is used in mobile for multiple sections (e.g: firebase).
// Some retailer doesn't have same value as the short name, so keep it in config for now.
$configs['mobile.retailer-id'] = $configs['env'] === Loader::ENV_STACK_PROD ? 'demosalesfloor' : "{$configs['retailer.short_name']}-{$configs['env']}";
