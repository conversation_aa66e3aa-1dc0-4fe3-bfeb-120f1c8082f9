<?php

/**
 * Configs shared by the Chicos retailer group.
 *
 * @see configs/configs/groups.php
 */

$configs['retailer.group'] = 'chicos';

// SF-22485
$configs['exporter.request.daily.enabled'] = true;
$configs['exporter.sms.daily.enabled'] = true;
$configs['exporter.livechat.daily.enabled'] = true;
$configs['exporter.transactions.daily.enabled'] = true;

$configs['retailer.clienteling.customer.sync'] = true;

$configs['retailer.corporate-email.required'] = false;
$configs['retailer.prefix_numeric_usernames'] = true;

$configs['importer.reps.enabled'] = true;

// Tasks
$configs['sf.task.automated.new_rep_transaction.enabled'] = true;
$configs['sf.task.automated.new_retailer_transaction_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;

$configs['sf.task.automated.new_rep_transaction.days_search_back'] = 10;
$configs['sf.task.automated.new_retailer_transaction_filtered.days_search_back'] = [ 10 ];
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.days_search_back'] = 10;

// Expanded variants feed (Since the new importer only use variant anyway) , we won't break the old one.
// THIS IS IMPORTANT DO NOT DELETE IT ON MERGE WITH VARIANT
$configs['importer.product.s3.filename_regexp'] = 'VARIANT-salesfloor-product-feed-\d{8}-\d{6}.csv';

$configs['stores.max_distance'] = '23456'; // "National"

$configs['exporter.messages.daily.enabled'] = true;
$configs['exporter.share.email.daily.enabled'] = true;

$configs['importer.contacts.tag_delimiter'] = [';'];

$configs['mobile.retailer_can_browse_library'] = true;

$configs['exporter.email.stats.daily.enabled'] = true;

$configs['exporter.lookbook.daily.enabled'] = true;

$configs['retailer.sale_cookie_expire'] = 86400 * 1000 * 5; // 5 days in ms

$configs['products.expanded_variants.enabled'] = true;

$configs['devops-1420.kludge'] = true;

$configs['products.expanded_variants.priority_badge.enabled'] = true;

$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = true;

$configs['importer.rep_transactions.enabled'] = true;

/////////////// ---- CI ----- /////////////////

// I'm not sure why, but chicos doesn't split their feed. They are all in the same folder.
$configs['sf.import_ci_stats.s3.path'] = 'inbound/{env}';
$configs['sf.import_ci_transactions.s3.path'] = 'inbound/{env}';
$configs['sf.import_ci_customers.s3.path'] = 'inbound/{env}';

$configs['retailer.services.hide_optional_phone_input'] = true;

$configs['retailer.services.appointment.types'] = [
  [ 'type' => 'store', 'is_enabled' => true, 'is_default' => true, 'position' => 0, 'use_alternate_label' => false ],
  [ 'type' => 'phone', 'is_enabled' => true, 'is_default' => false, 'position' => 1, 'use_alternate_label' => true ],
  [ 'type' => 'chat', 'is_enabled' => true, 'is_default' => false, 'position' => 2, 'use_alternate_label' => false ],
];

$configs['mobile.retailer_can_share_from_browse_library'] = true;
$configs['mobile.share_instagram_enabled'] = true;
$configs['mobile.share_facebook_enabled'] = true;

$configs['retailer.store.close_hour'] = 21;

/** All appointment management config temporarily are OFF  for all chicos brands */

//When the config is turned On, the ability to book appointments for a customer from the mobile app
$configs['retailer.services.appointment_management.is_enabled'] = true;

//When the config is turned On, the ability to book appointments for a customer from the retailer customer list
$configs['retailer.services.appointment_management.all_customer.is_enabled'] = true;

//If true, group 1 user, namely rep can see all the appointments in his store
$configs['retailer.services.appointment.all.is_enabled'] = true;

//When the config is turned On, the ability to reassign appointments for a customer from the mobile app
$configs['retailer.services.appointment_reassignment.is_enabled'] = true;

//If true, the mobile will let you save your appointment to your device.
$configs['retailer.services.appointment.save_to_device.is_enabled'] = true;

$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = true;
$configs['retailer.clienteling.customers.communication.blackout.period'] = 60 * 60 * 8;

//If true, txt messaging could send to multiple recipients from associate
$configs['messaging.text.multiple-recipients.enabled'] = true;

$configs['retailer.chat.option.video-chat'] = true;
$configs['retailer.chat.option.video-chat.2ways'] = true;

$configs['retailer.outfits.is_enabled'] = true;
$configs['retailer.outfits.external_service'] = 'stylitics';
$configs['retailer.outfits.mapper'] = 'Chicos';
$configs['retailer.outfits.account'] = 'chicos';
$configs['retailer.outfits.section_label'] = 'My Closet';

$configs['importer.rep_transactions.skip_stats'] = true;

// ------------------------------
// Encryption/Encoding Services (Unsubscription Email)
// ------------------------------
$configs['encryption.use_case.email_unsubscribe.is_enabled'] = true;
$configs['encryption.use_case.email_unsubscribe.strategy'] = 'Base64';

// Config changes for CHS group retailers requested in PS-4962
$configs['retailer.add_customer_to_my_contacts.is_enabled'] = true;
$configs['importer.import-as-subscriber.email'] = 0;

// CHS migration from rep to team mode.
$configs['retailer.storepage_mode'] = true;
