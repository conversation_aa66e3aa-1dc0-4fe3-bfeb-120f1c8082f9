<?php

$configs['retailer.customer_tags.is_enabled'] = false;

$configs['retailer.multiple_email_templates.enabled'] = false;

$configs['inventory.lookup.is_enabled'] = false;

//For mobile share to emails + BO hide share option
$configs['mobile.share_email_enabled'] = false;
$configs['retailer.can_share_an_update'] = false; // disable the menu item since there would be no share options

//CI
$configs['retailer.clienteling.mode'] = false;
$configs['retailer.clienteling.enabled.transactions']   = false;
$configs['retailer.clienteling.enabled.create_contact'] = false;
$configs['retailer.clienteling.matching.is_enabled'] = false;
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = false;
$configs['retailer.clienteling.customer.sync'] = false;
$configs['retailer.clienteling.customer.limited_visibility.is_enabled'] = false;
$configs['exporter.email.stats.daily.enabled'] = false;

//Stylitics
$configs['retailer.outfits.is_enabled'] = false;

//Automated tasks configs
$configs['sf.task.automated.enabled'] = false;
$configs['sf.task.automated.emails_enabled'] = false;
$configs['sf.task.automated.notifications_enabled'] = false;

/**
 * Additional Automated Tasks configs, to make sure that no automated tasks will be created
 */
// CI automated tasks
$configs['sf.task.automated.retailer_transaction_employee_assigned.notifications_enabled'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.emails_enabled'] = false;
//Enable the creation of automated tasks from CI transactions for stores
$configs['sf.task.automated.transactions_distribution_by_stores.is_enabled'] = false;
//Enable phone notification for automated tasks from CI transactions for stores distribution
$configs['sf.task.automated.transactions_distribution_by_stores.notifications_enabled'] = false;
//Enable email notification for automated tasks from CI transactions for stores distribution
$configs['sf.task.automated.transactions_distribution_by_stores.emails_enabled'] = false;

//customer tasks for new transaction
$configs['sf.task.automated.new_retailer_transaction_filtered.any_store'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.notifications_enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.emails_enabled'] = false;

//automated tasks for customers whose last transaction was X days ago
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.emails_enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.notifications_enabled'] = false;

//Storefront Related Automated Tasks
$configs['sf.task.automated.nag_update_storefront_products.enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.emails_enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.notifications_enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.category'] = null;

//Share Related Automated Tasks
$configs['sf.task.automated.nag_share_update.enabled'] = false;
$configs['sf.task.automated.nag_share_update.emails_enabled'] = false;
$configs['sf.task.automated.nag_share_update.notifications_enabled'] = false;
$configs['sf.task.automated.nag_share_update.category'] = null;

//Onboarding Related Automated Tasks
$configs['sf.task.automated.nag_onboarding_connect_social_media.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.notifications_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.category'] = null;
$configs['sf.task.automated.nag_onboarding_add_contacts.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.notifications_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.category'] = null;
$configs['sf.task.automated.nag_onboarding_update_about_me.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_update_about_me.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_update_about_me.notifications_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.notifications_enabled'] = false;

//Automated tasks
$configs['sf.task.automated.new_rep_transaction.enabled'] = false;
$configs['sf.task.automated.new_rep_transaction.emails_enabled'] = false;
$configs['sf.task.automated.new_rep_transaction.notifications_enabled'] = false;

$configs['sf.task.automated.retailer_customer_stats_registry_event.enabled'] = false;
$configs['sf.task.automated.retailer_customer_stats_registry_followup.enabled'] = false;

$configs['sf.task.automated.cancelled_transaction_follow_up.enabled'] = false;
//Enable/Disable emails for cancellation transaction follow up task
$configs['sf.task.automated.cancelled_transaction_follow_up.emails_enabled'] = false;
//Enable/Disable notifications for cancellation transaction follow up task
$configs['sf.task.automated.cancelled_transaction_follow_up.notifications_enabled'] = false;
