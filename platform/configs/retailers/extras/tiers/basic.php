<?php

$configs['retailer.modular_connect.storefront.is_enabled'] = false;
$configs['retailer.modular_connect.appointment_request.is_enabled'] = false;
$configs['retailer.modular_connect.corporate_tasks.is_enabled'] = false;

$configs['retailer.modular_connect.can_add_contacts'] = false;

$configs['retailer.customer_tags.is_enabled'] = false;

//For BO, if true Enable Personal Shopper
//For mobileAp, if true display personal shopper in filters on all request page
$configs['retailer.has_personal_shopper'] = false;
$configs['mobile.retailer_has_personal_shopper'] = false;


//For BO onboarding
$configs['retailer.social_networks'] = [];

//The ability to book appointments for a customer from the mobile app
$configs['retailer.services.appointment_management.is_enabled'] = false;

//for mobile, if false, removes appointments related push notifications
$configs['retailer.services.appointment.is_enabled'] = false;

$configs['retailer.virtual.option.video-chat'] = false;
$configs['retailer.store_appointment_hours.is_enabled'] = false;

$configs['mobile.retailer_has_lookbooks'] = false;
$configs['retailer.shop_feed.enabled'] = false;

//For mobile, if false, removes share an update from user hub and from the browse library
//For bo, if false removes share an update if this or $configs['mobile.share_email_enabled'] = false
$configs['retailer.can_share_an_update'] = false;

//For mobile, if false users cannot natively share items from the "my library" section of the mobile app
$configs['mobile.retailer_can_share_from_browse_library'] = false;

$configs['retailer.multiple_email_templates.enabled'] = false;
$configs['messaging.text.multiple-recipients.enabled'] = false;
$configs['messaging.email.multiple-recipients.enabled'] = false;

//For mobile share social networks
$configs['mobile.share_email_enabled'] = false;
$configs['mobile.share_instagram_enabled'] = false;
$configs['mobile.share_pinterest_enabled'] = false;
$configs['mobile.share_facebook_enabled']  = false;

//Communication Consent configs
$configs['retailer.consent_required.mobile'] = false;
$configs['retailer.modular_connect.can_change_communication_consent'] = false;
$configs['retailer.contacts.unsubscribe_automated.enabled'] = false;

//authentication
$configs['mfa.authentication.is_enabled'] = false;
$configs['retailer.sso.is_enabled'] = false;

$configs['importer.rep_transactions.enabled'] = false;
$configs['inventory.lookup.is_enabled'] = false;

$configs['exporter.email.stats.daily.enabled'] = false;

//Stylitics
$configs['retailer.outfits.is_enabled'] = false;

$configs['retailer.trending_recommendations'] = false;

// Task data integration
$configs['importer.tasks.is_enabled'] = false;

//CI
$configs['retailer.clienteling.mode'] = false;
$configs['retailer.clienteling.enabled.transactions']   = false;
$configs['retailer.clienteling.enabled.create_contact'] = false;
$configs['retailer.clienteling.matching.is_enabled'] = false;
$configs['retailer.clienteling.customers.communication.blackout.is_enabled'] = false;
$configs['retailer.clienteling.customer.sync'] = false;
$configs['retailer.clienteling.customer.limited_visibility.is_enabled'] = false;

//Automated tasks configs
$configs['sf.task.automated.enabled'] = false;
$configs['sf.task.automated.emails_enabled'] = false;
$configs['sf.task.automated.notifications_enabled'] = false;

/**
 * Additional Automated Tasks configs, to make sure that no automated tasks will be created
 */
// CI automated tasks
$configs['sf.task.automated.retailer_transaction_employee_assigned.notifications_enabled'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.matching'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.enabled'] = false;
$configs['sf.task.automated.retailer_transaction_employee_assigned.emails_enabled'] = false;
//Enable the creation of automated tasks from CI transactions for stores
$configs['sf.task.automated.transactions_distribution_by_stores.is_enabled'] = false;
//Enable phone notification for automated tasks from CI transactions for stores distribution
$configs['sf.task.automated.transactions_distribution_by_stores.notifications_enabled'] = false;
//Enable email notification for automated tasks from CI transactions for stores distribution
$configs['sf.task.automated.transactions_distribution_by_stores.emails_enabled'] = false;

//custome tasks for new transaction
$configs['sf.task.automated.new_retailer_transaction_filtered.any_store'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.notifications_enabled'] = false;
$configs['sf.task.automated.new_retailer_transaction_filtered.emails_enabled'] = false;

//automated tasks for customers whose last transaction was X days ago
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.emails_enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_filtered.notifications_enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.emails_enabled'] = false;
$configs['sf.task.automated.retailer_customer_soon_to_lapse_secondary_filtered.notifications_enabled'] = false;

//Storefront Related Automated Tasks
$configs['sf.task.automated.nag_update_storefront_products.enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.emails_enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.notifications_enabled'] = false;
$configs['sf.task.automated.nag_update_storefront_products.category'] = null;

//Share Related Automated Tasks
$configs['sf.task.automated.nag_share_update.enabled'] = false;
$configs['sf.task.automated.nag_share_update.emails_enabled'] = false;
$configs['sf.task.automated.nag_share_update.notifications_enabled'] = false;
$configs['sf.task.automated.nag_share_update.category'] = null;

//Onboarding Related Automated Tasks
$configs['sf.task.automated.nag_onboarding_connect_social_media.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.notifications_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_connect_social_media.category'] = null;
$configs['sf.task.automated.nag_onboarding_add_contacts.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.notifications_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_add_contacts.category'] = null;
$configs['sf.task.automated.nag_onboarding_update_about_me.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_update_about_me.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_update_about_me.notifications_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.emails_enabled'] = false;
$configs['sf.task.automated.nag_onboarding_upload_profile_pic.notifications_enabled'] = false;

//Automated tasks
$configs['sf.task.automated.new_rep_transaction.enabled'] = false;
$configs['sf.task.automated.new_rep_transaction.emails_enabled'] = false;
$configs['sf.task.automated.new_rep_transaction.notifications_enabled'] = false;

$configs['sf.task.automated.retailer_customer_stats_registry_event.enabled'] = false;
$configs['sf.task.automated.retailer_customer_stats_registry_followup.enabled'] = false;

$configs['sf.task.automated.cancelled_transaction_follow_up.enabled'] = false;
//Enable/Disable emails for cancellation transaction follow up task
$configs['sf.task.automated.cancelled_transaction_follow_up.emails_enabled'] = false;
//Enable/Disable notifications for cancellation transaction follow up task
$configs['sf.task.automated.cancelled_transaction_follow_up.notifications_enabled'] = false;

// Contacts/customers extended attributes
$configs['retailer.modular_connect.extended_attributes.is_enabled'] = false;

//Sales Tracking Stand-Down
$configs['retailer.modular_connect.stand_down_sales_tracking_cookies.is_enabled'] = false;
