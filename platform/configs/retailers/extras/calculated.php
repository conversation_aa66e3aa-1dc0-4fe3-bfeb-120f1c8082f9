<?php

use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Oauth2\Oauth2Service;
use Salesfloor\Services\Oauth2\Provider\AzureOauth2Provider;
use Salesfloor\Services\Oauth2\Provider\BaseOauth2Provider;

// === API ===

$configs['firebase.defaultpath'] = '/';
if ('tests' == $configs['retailers.current']) {
    $configs['firebase.defaultpath'] .= 'tests';
} else {
    $configs['firebase.defaultpath'] .= $configs['env'];
}
$configs['firebase.defaultpath'] .= '/' . $configs['firebase.retailername'];


$configs['queue.prepare-share'] = $configs['retailers.current'] . '-prepare-share-' . $configs['env'];
$configs['queue.share-an-update'] = $configs['retailers.current'] . '-share-an-update-' . $configs['env'];
$configs['queue.cakemail-relays'] = $configs['retailers.current'] . '-cakemail-relays-' . $configs['env'];
$configs['queue.cakemail-relays-dead-letter'] = $configs['retailers.current'] . '-cakemail-relays-dead-letter-' . $configs['env'];
$configs['queue.match-customers.customers'] = $configs['retailers.current'] . '-match-customers-' . $configs['env'];
$configs['queue.match-customers.retailer-customers'] = $configs['retailers.current'] . '-match-retailer-customers-' . $configs['env'];

// === WIDGETS ===

$configs['queue.cakemail-relays'] = $configs['retailers.current'] . '-cakemail-relays-' . $configs['env'];



// === WEBSERVER ===
// SF-17955 - Hack to support multi brand queue
if ($configs['retailer.multibrand']) {
    // Sadly, i can force retailer.short_name to be different for bru/tru since it's we have hardcoded comparaison in the code
    $configs['queue.share-an-update'] = $configs['retailer.brand'] . '-share-an-update-' . $configs['env'];
} else {
    $configs['queue.share-an-update'] = $configs['retailer.short_name'] . '-share-an-update-' . $configs['env'];
}

$configs['queue.cakemail-relays'] = $configs['retailer.short_name'] . '-cakemail-relays-' . $configs['env'];

// SF-16318 Record visit touchpoints (and more events)
$configs['queue.events.base_name'] = 'events';
$configs['queue.events.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.events.base_name'] . '-' . $configs['env'];
$configs['queue.events.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.events.base_name'] . '-dead-letter-' . $configs['env'];


// SF-16011 Add CASL Compliance Retailer Address
// Sometimes different from retailer.hq_address (Saks)
// When not specified fallback to retailer.hq_address
if (empty($configs['sf.casl.address'])) {
    $configs['sf.casl.address'] = $configs['retailer.hq_address'];
}

// SF-17460 Enable Disable SMS by Associate
// Queue set up for multiple enable/disable actions
$configs['queue.text_messaging_enable.base_name'] = 'messaging-text-enable';
$configs['queue.text_messaging_enable.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.text_messaging_enable.base_name'] . '-' . $configs['env'];
$configs['queue.text_messaging_enable.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.text_messaging_enable.base_name'] . '-dead-letter-' . $configs['env'];

// PP-8 Post sale contact creation/update
$configs['queue.transaction_customer.base_name'] = 'transaction-customer';
$configs['queue.transaction_customer.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.transaction_customer.base_name'] . '-' . $configs['env'];
$configs['queue.transaction_customer.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.transaction_customer.base_name'] . '-dead-letter-' . $configs['env'];

// Compensate for poor performance of transaction recording endpoint
$configs['queue.record-transactions.full_name'] = $configs['retailer.short_name'] . '-record-transactions-' . $configs['env'];
$configs['queue.record-transactions.dead_letter_name'] = $configs['retailer.short_name'] . '-record-transactions-dead-letter-' . $configs['env'];

// SF-24406 Record corporate task create and update
$configs['queue.corporate-tasks.base_name'] = 'corporate-tasks';
$configs['queue.corporate-tasks.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.corporate-tasks.base_name'] . '-' . $configs['env'];
$configs['queue.corporate-tasks.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.corporate-tasks.base_name'] . '-dead-letter-' . $configs['env'];

$configs['queue.sidebar-events.base_name'] = 'sidebar-events';
$configs['queue.sidebar-events.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.sidebar-events.base_name'] . '-' . $configs['env'];
$configs['queue.sidebar-events.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.sidebar-events.base_name'] . '-dead-letter-' . $configs['env'];

// SF-26302 1-to-many SMS text message
$configs['queue.text_message_sending.base_name'] = 'messaging-text-sending';
$configs['queue.text_message_sending.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.text_message_sending.base_name'] . '-' . $configs['env'];
$configs['queue.text_message_sending.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.text_message_sending.base_name'] . '-dead-letter-' . $configs['env'];

// Use queue for push notification because it's slow now with GCP. Moving the logic to the queue make sure the
// customer experience is not affected with it.
$configs['queue.push_notification.base_name'] = 'push-notification';
$configs['queue.push_notification.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.push_notification.base_name'] . '-' . $configs['env'];
$configs['queue.push_notification.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.push_notification.base_name'] . '-dead-letter-' . $configs['env'];

// GCP Queue messsage fetch is done in batches.
// If we receive 10 messages and do push notifications in
// sequence if each push notifications process takes 30 seconds
// we would send the last push notification in 10 minutes.
// Requests of 1 message at a time, in parallel resolve this issue.
$configs['queue.push_notification.number_of_messages'] = 1;

// Since we have low 'acknowledment deadline' ono the queue
// Once we received the message we mark an hold on this message
// for 120 seconds. If the process fails, it reset that value to
// 5 seconds see $hideMessage in the Salesfloor\Services\Queue\Base.php
// and changeMessageVisibility around line 123
$configs['queue.push_notification.hide_message_on_pull'] = 120;

// TD-1237 Sendgrid Resubscribe Queue
$configs['queue.sendgrid-resubscribe.base_name'] = 'sendgrid-resubscribe';
$configs['queue.sendgrid-resubscribe.full_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.sendgrid-resubscribe.base_name'] . '-' . $configs['env'];
$configs['queue.sendgrid-resubscribe.dead_letter_name'] = $configs['retailer.short_name'] . '-' . $configs['queue.sendgrid-resubscribe.base_name'] . '-dead-letter-' . $configs['env'];

// === SSO CONFIGS ===
if ($configs['retailer.sso.provider.type'] === Salesfloor\Services\Oauth2\Provider\AzureOauth2Provider::OAUTH2_PROVIDER) {
    $configs['retailer.sso.provider.server'] = 'https://login.microsoftonline.com/' . $configs['retailer.sso.provider.tenant'];
} // else : provider is Okta, and 'retailer.sso.provider.server' is change per retailer

// === CANS Microservice Url ===
$configs['cans.url'] = $configs['retailer.rest_api_url'] . '/microservice/cans';

// === COOKIE ===
if ($configs['env'] === Loader::ENV_STACK_PROD) {
    // If 'retailer.cookie_show_prefix' is set, set "{$retailer_id}_" as cookie prefix
    if ($configs['retailer.cookie_show_prefix']) {
        $configs['cookie_prefix'] = $configs['retailers.current'] . '_';
    } else {
        $configs['cookie_prefix'] = '';
    }
} elseif ($configs['env'] == 'trv') {
    $configs['cookie_prefix'] = 'int_' . $configs['retailers.current'] . '_';
} else {
    $configs['cookie_prefix'] = $configs['env'] . '_' . $configs['retailers.current'] . '_';
}

// === SECRETS ====
// infra configs is a flat JSON, the following entries must be copied in nested objects
$sfApi = $configs['sf-api'];
if (!empty($configs['sf-api.apikey.key'])) {
    $sfApi['apikey']['key'] = $configs['sf-api.apikey.key'];
}
if (!empty($configs['sf-api.jwt.secret'])) {
    $sfApi['jwt']['secret'] = $configs['sf-api.jwt.secret'];
}
$sfApi['cookie'] = [
    'name' => $configs['cookie_prefix'] . 'SF-TOKEN',   // When you log in wordpress, this cookie will be created with the token. It will be append to all xhr call in header
];
$configs['sf-api'] = $sfApi;

// We can't rely on short_name to populated mobile.retailer-id, so we will keep the config for now

// May be set by InfraConfigLoader. If not, defaults to $configs['mobile.retailer-id']
if (!isset($configs['retailers.current.id'])) {
    $configs['retailers.current.id'] = $configs['mobile.retailer-id'];
}

// SSO - Override for QA/Tests with "st-qa-1" (Only those using Azure)
if (
    $configs['retailer.sso.is_enabled'] === true &&
    $configs['retailer.sso.provider.type'] === AzureOauth2Provider::OAUTH2_PROVIDER &&
    !in_array($configs['env'], [
        Loader::ENV_STACK_PROD,
        Loader::ENV_STACK_STAGING,
    ])
) {
    $configs['retailer.sso.identity'] = Oauth2Service::IDENTITY_EMAIL;
    $configs['retailer.sso.provider.tenant'] = '1a49c38c-b87a-4ea8-9873-a29dc6b835a6';
    $configs['retailer.sso.provider.client_id'] = '642c8f82-1423-40fa-8311-20f162726c45';
    $configs['retailer.sso.provider.client_secret'] = '**********************************';
    $configs['retailer.sso.login.prompt'] = BaseOauth2Provider::PROMPT_SELECT_ACCOUNT;
    $configs['retailer.sso.provider.server'] = 'https://login.microsoftonline.com/' . $configs['retailer.sso.provider.tenant'];
}
