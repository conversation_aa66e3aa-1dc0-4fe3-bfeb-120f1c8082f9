<?php

use Salesfloor\Services\Application\Routing as R;

return [
    'StoreLocales' => [
        'alias' => 'store_locales',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Stores' => [
        'alias' => 'stores',
        'cache' => true,
    ],
    'RetailerStores' => [
        'alias' => 'retailer_stores',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products' => [
        'alias' => 'products',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'Variants' => [
        'alias' => 'variants',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Categories' => [
        'alias' => 'categories',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Reps' => [
        'alias' => 'reps',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'Transactions' => [
        'alias' => 'transactions',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TransactionItems' => [
        'alias' => 'transaction_items',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TransactionsCopy' => [
        'alias' => 'transactions_copy',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TransactionItemsCopy' => [
        'alias' => 'transaction_items_copy',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RetailerTransactions' => [
        'alias' => 'retailer_transactions',
        'cache' => false,
    ],
    'RetailerTransactionDetails' => [
        'alias' => 'retailer_transaction_details',
        'cache' => false,
    ],
    'RetailerTransactionAttributionStatus' => [
        'alias' => 'retailer_transaction_attribution_status',
    ],
    'RetailerTransactionAttribution' => [
        'alias' => 'retailer_transaction_attribution',
    ],
    'TrackingEvents' => [
        'alias' => 'tracking_events',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Leads' => [
        'alias' => 'leads',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'WpPosts' => [
        'alias' => 'wp_posts',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'WpPostMeta' => [
        'alias' => 'wp_postmeta',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Sessions' => [
        'alias' => 'sessions',
        'cache' => false,
    ],
    'Events' => [
        'alias' => 'events',
        'cache' => false,
    ],
    'Client\Customers\Legacy' => [
        'alias' => 'customers',
        'cache' => false,
    ],
    'Client\Customers\V1' => [
        'alias' => 'customers_v1',
        'cache' => false,
    ],
    'Client\RetailerCustomers\Legacy' => [
        'alias' => 'retailer_customers',
        'cache' => false,
    ],
    'Client\RetailerCustomers\V1' => [
        'alias' => 'retailer_customers_v1',
        'cache' => false,
    ],
    'CustomerMeta' => [
        'alias' => 'customersmeta',
        'cache' => false,
    ],
    'CustomerFieldHistory' => [
        'alias' => 'customer_field_history',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RetailerCustomerMeta' => [
        'alias' => 'retailer_customersmeta',
        'cache' => false,
    ],
    'RetailerCustomersToCustomerAttributes' => [
        'alias' => 'retailer_customers_to_customer_attributes',
        'cache' => false,
    ],
    'CustomersToRetailerCustomers' => [
        'alias' => 'customers-to-retailercustomers',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'RetailerCustomersRemapping' => [
        'alias' => 'retailer_customers_remapping',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Favorites' => [
        'alias' => 'favorites',
        'cache' => false,
    ],
    'Lookbooks' => [
        'alias' => 'lookbooks',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'Looks' => [
        'alias' => 'looks',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'MyLooks' => [
        'alias' => 'my_looks',
        'cache' => false,
    ],
    'LookProducts' => [
        'alias' => 'look_products',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'Storefront' => [
        'alias' => 'storefront',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'RepOnboarding' => [
        'alias' => 'rep_onboarding',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Rankings' => [
        'alias' => 'rankings',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'OauthSessions' => [
        'alias' => 'oauth_sessions',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Messages\Legacy' => [
        'alias' => 'messages',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Nags' => [
        'alias' => 'nags',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'UserActivity' => [
        'alias' => 'user_activity',
        'cache' => false,
    ],
    'UserMeta' => [
        'alias' => 'user_meta',
        'cache' => false,
    ],
    'StoreEvents' => [
        'alias' => 'store_events',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TrendingRecommendations' => [
        'alias' => 'trending_recommendations',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'ProductRecommendations' => [
        'alias' => 'product_recommendations',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TrendingRecommendationBans' => [
        'alias' => 'trending_bans',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'WpOptions' => [
        'alias' => 'wp_options',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products\TopPicks' => [
        'alias' => 'products.top-picks',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products\NewArrivals' => [
        'alias' => 'products.new-arrivals',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products\Specials' => [
        'alias' => 'products.specials',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TopPicks' => [
        'alias' => 'top_picks',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'Specials' => [
        'alias' => 'specials',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'LatestArrivals' => [
        'alias' => 'latest_arrivals',
        'cache' => R::CACHE_RELATION_AWARE_DEFAULT,
    ],
    'Products\ProductPanel' => [
        'alias' => 'products.panel',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'StatsPanel' => [
        'alias' => 'stats_panel',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products\NominatedProducts' => [
        'alias' => 'products.nominatedproducts',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products\AutoSelectedRefreshed' => [
        'alias' => 'products.autoselected-refreshed',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Products\ProductShared' => [
        'alias' => 'product_shared',
        'cache' => false
    ],
    'Import' => [
        'alias' => 'import',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'LastExport' => [
        'alias' => 'lastexport',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'UserPhoneNumbers' => [
        'alias' => 'user_phone_numbers',
        'cache' => false,
    ],
    'Messaging\Text\Threads' => [
        'alias' => 'messaging.text.threads',
        'cache' => false,
    ],
    'Messaging\Text\Messages' => [
        'alias' => 'messaging.text.messages',
        'cache' => false,
    ],
    'Messaging\Text\Attachments' => [
        'alias' => 'messaging.text.attachments',
        'cache' => false,
    ],
    'MessageProduct' => [
        'alias' => 'message_product',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Content' => [
        'alias' => 'content',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Task\Tasks' => [
        'alias' => 'tasks',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Task\V1' => [
        'alias' => 'task_v2',
        'cache' => false,
    ],
    'TaskCategories' => [
        'alias' => 'task_categories',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'GroupTask\GroupTasks' => [
        'alias' => 'group_tasks',
        'cache' => false,
    ],
    'GroupTask\GroupTaskProducts' => [
        'alias' => 'group_task_products',
        'cache' => false,
    ],
    'GroupTask\GroupTaskAssets' => [
        'alias' => 'group_task_assets',
        'cache' => false,
    ],
    'GroupTask\GroupTaskActivities' => [
        'alias' => 'group_task_activities',
        'cache' => false,
    ],
    'GroupTask\GroupTaskActivityProducts' => [
        'alias' => 'group_task_activity_products',
        'cache' => false,
    ],
    'GroupTask\GroupTaskActivityAssets' => [
        'alias' => 'group_task_activity_assets',
        'cache' => false,
    ],
    'ShortenedUrls' => [
        'alias' => 'shortened_urls',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'CustomerAddress' => [
        'alias' => 'customer_addresses',
        'cache' => false,
    ],
    'CustomerNotes' => [
        'alias' => 'customer_notes',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'CustomerTags' => [
        'alias' => 'customer_tags',
        'cache' => false,
    ],
    'CustomerTagsRelationships' => [
        'alias' => 'customer_tags_relationships',
        'cache' => false,
    ],
    'CustomerSocialMedia' => [
        'alias' => 'customer_social_media',
        'cache' => false,
    ],
    'SocialMediaNetwork' => [
        'alias' => 'social_media_network',
        'cache' => false,
    ],
    'CustomerEvents' => [
        'alias' => 'customer_events',
        'cache' => false,
    ],
    'CustomersToCustomerAttributes' => [
        'alias' => 'customers_to_customer_attributes',
        'cache' => false,
    ],
    'CustomerSignature' => [
        'alias' => 'customer_signature',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Concerns' => [
        'alias' => 'concerns',
        'cache' => true,
    ],
    'CustomerActivityFeed' => [
        'alias' => 'customer_activity_feed',
        'cache' => false,
    ],
    'ChatFlagged' => [
        'alias' => 'chat_flagged',
        'cache' => false,
    ],
    'ChatTransferred' => [
        'alias' => 'chat_transferred',
        'cache' => false,
    ],
    'ChatAbandoned' => [
        'alias' => 'chat_abandoned',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Services\Questions' => [
        'alias' => 'questions',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Services\Appointments' => [
        'alias' => 'appointments',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Services\V1\Appointments' => [
        'alias' => 'appointments_v1',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Services\PersonalShopper' => [
        'alias' => 'personal_shopper',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'PreCustomers' => [
        'alias' => 'pre_customers',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RetailerLocales' => [
        'alias' => 'retailer_locales',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'CustomerInsights\Stats\Panels\Legacy' => [
        'alias' => 'retailer_customer_stats_insights',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    // This is the first manager that doesn't have a legacy manager
    // I didn't use the legacy filename since there was no legacy manager.
    'CustomerInsights\Stats\Sections\V1' => [
        'alias' => 'retailer_customers_stats_sections',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'CustomerInsights\Stats\DataPoints\Legacy' => [
        'alias' => 'retailer_customer_stats_insights_datapoints',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'LanguageLocaleFallbacks' => [
        'alias' => 'language_locale_fallbacks',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RetailerCustomerTagsRelationships' => [
        'alias' => 'retailer_customer_tags_relationships',
        'cache' => false,
    ],
    'RetailerCustomerAddresses' => [
        'alias' => 'retailer_customer_addresses',
        'cache' => false,
    ],
    'RetailerCustomerEvents' => [
        'alias' => 'retailer_customer_events',
        'cache' => false,
    ],
    'RetailerCustomerSocialMedias' => [
        'alias' => 'retailer_customer_social_medias',
        'cache' => false,
    ],
    'EmailBlock\EmailBlockList' => [
        'alias' => 'email_block_list',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'EmailBlock\BlockedIncomingEmailAddresses' => [
        'alias' => 'blocked_incoming_email',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'SmsBlock\SmsBlockList' => [
        'alias' => 'sms_block_list',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Blacklist\Sms' => [
        'alias' => 'blacklistSms',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TaskTransactions' => [
        'alias' => 'task_rep_transactions',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'TaskRetailerTransactions' => [
        'alias' => 'task_retailer_transactions',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'ProductVariants' => [
        'alias' => 'product_variants',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'ProductVariantAttributes' => [
        'alias' => 'product_variant_attributes',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'UnattachedCustomers' => [
        'alias' => 'unattached_customers',
        'cache' => false,
    ],
    'Devices' => [
        'alias' => 'devices',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'DeletedCustomer' => [
        'alias' => 'deleted_customers',
        'cache' => false,
    ],
    'DeletedCustomerMeta' => [
        'alias' => 'deleted_customersmeta',
        'cache' => 'false',
    ],
    'Assets' => [
        'alias' => 'assets',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'ShopFeed\ShopFeed' => [
        'alias' => 'shop_feed',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'ShopFeed\ShopFeedAsset' => [
        'alias' => 'shop_feed.assets',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'ShopFeed\ShopFeedProduct' => [
        'alias' => 'shop_feed.products',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'ShopFeed\ShopFeedImage' => [
        'alias' => 'shop_feed.images',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Share\ShareGeneral' => [
        'alias' => 'share.general',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Share\ShareAsset'   => [
        'alias' => 'share.assets',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Share\ShareImage'   => [
        'alias' => 'share.images',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    // ShareProduct have be declared before as ProductShared
    'Share\ShareCustomerTag'   => [
        'alias' => 'share.customer_tags',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'CorporateTask\CorporateTask' => [
        'alias' => 'corporate_tasks',
        'cache' => true,
    ],
    'CorporateTask\CorporateTaskAsset' => [
        'alias' => 'corporate_task.assets',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'CorporateTask\CorporateTaskProduct' => [
        'alias' => 'corporate_task.products',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'CorporateTask\CorporateTaskSpecialty' => [
        'alias' => 'corporate_task.specialties',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'CorporateTask\CorporateTaskStore' => [
        'alias' => 'corporate_task.stores',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'UserFieldHistory' => [
        'alias' => 'user_field_history',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'SiteUrls' => [
        'alias' => 'site_urls',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Images' => [
        'alias' => 'images',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'ProductCategoryMap' => [
        'alias' => 'product_category_map',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RetailerProductIds' => [
        'alias' => 'retailer_product_ids',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'WpTerms' => [
        'alias' => 'wp_terms',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'WpTermTaxonomy' => [
        'alias' => 'wp_term_taxonomy',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RejectedEmails' => [
        'alias' => 'rejected_email',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'FileValidation' => [
        'alias' => 'file_validation',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'CustomerAttributes' => [
        'alias' => 'customer_attributes',
        'cache' => true,
    ],
    'CustomerAttributePanels' => [
        'alias' => 'customer_attribute_panels',
        'cache' => false,
    ],
    'Reps\V1' => [
        'alias' => 'reps_v1',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Dashboard\DashboardComponents' => [
        'alias' => 'dashboard_components',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Dashboard\DashboardComponentMappings' => [
        'alias' => 'dashboard_component_mappings',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Requests' => [
        'alias' => 'store_requests',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'RetailerCustomerCommunication' => [
        'alias' => 'retailer_customer_communication',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],

    // Virtual managers
    'Virtual\Filter\AppointmentFilter' => [
        'alias' => 'appointment_filter',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Virtual\Filter\TaskFilter' => [
        'alias' => 'task_filters',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Virtual\Filter\GroupTaskFilter' => [
        'alias' => 'group_task_filters',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Virtual\Filter\MessageBadge' => [
        'alias' => 'message_badge',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Virtual\Requests\NewLeads' => [
        'alias' => 'new_leads',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Virtual\Stores\StoreAvailabilities' => [
        'alias' => 'store_availabilities',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Virtual\Inventory' => [
        'alias' => 'inventory',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Virtual\ProductBrands' => [
        'alias' => 'product_brands',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Virtual\ProductCategories' => [
        'alias' => 'product_categories',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RetailerCustomerCommunication' => [
        'alias' => 'retailer_customer_communication',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'MfaAuthentication' => [
        'alias' => 'mfa_auth',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Outfit\Outfits' => [
        'alias' => 'outfits',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Outfit\OutfitItemNumbers' => [
        'alias' => 'outfit_item_numbers',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Outfit\OutfitItems' => [
        'alias' => 'outfit_items',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Brands\ShopByBrand' => [
        'alias' => 'shop_by_brand',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Virtual\CustomersBlocked' => [
        'alias' => 'customers_blocked',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'AppointmentHours\Status' => [
        'alias' => 'appointment_hours_status',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'AppointmentHours\TimeSlots' => [
        'alias' => 'appointment_hours_timeslots',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'AppointmentHours\DateOverrides' => [
        'alias' => 'appointment_hours_overrides',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'AppointmentHours\Bookings' => [
        'alias' => 'appointment_hours_bookings',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'FeedScanner\FeedScannerJobs' => [
        'alias' => 'feed_scanner_jobs',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'FeedScanner\CloudFiles' => [
        'alias' => 'feed_scanner_cloud_files',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'CustomerFavorites' => [
        'alias' => 'customer_favorites',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Specialties' => [
        'alias' => 'specialties',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'RepTransactionDecryptionFailure' => [
        'alias' => 'rep_transaction_decryption_failure'
    ],
    'Addresses\Countries' => [
        'alias' => 'countries',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Addresses\Cities' => [
        'alias' => 'cities',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Addresses\AdministrativeDivisions' => [
        'alias' => 'administrative_divisions',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Config\ConfigGroup' => [
        'alias' => 'config-group',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'Config\ConfigItem' => [
        'alias' => 'config-item',
        'cache' => R::CACHE_BASE_DEFAULT,
    ],
    'UserStores' => [
        'alias' => 'user_stores',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'Moderations\Moderations' => [
        'alias' => 'moderations',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'EmailAttachments\EmailAttachments' => [
        'alias' => 'email_attachments',
        'cache' => R::CACHE_V1_DEFAULT,
    ],
    'PhoneNumberDetails' => [
        'alias' => 'phone_number_details',
        'cache' => false,
    ],
];
