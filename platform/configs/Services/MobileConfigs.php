<?php

namespace Salesfloor\Configs;

use Monolog\Logger;
use Psr\Log\LoggerInterface;
use Salesfloor\Services\Algolia\Indexer\Base;
use Salesfloor\Services\AlgoliaIndexer;
use Salesfloor\Services\Config\ConfigsLoader\ConfigsHelper;
use Salesfloor\Services\Config\ConfigsLoader\Loader;
use Salesfloor\Services\Lang;
use Salesfloor\Services\Multilang;
use Salesfloor\Services\NameSuggester;
use Salesfloor\Services\Oauth2\Oauth2Service;
use Salesfloor\Services\SalesfloorServices;
use Symfony\Component\Translation\Translator;
use Salesfloor\Services\GroupPermissionService as Perms;

/**
 * SF-15102 - Salesfloor Mobile Configuration Service
 * Refactors Configurations (already retailer & stack & brand filtered) into a
 * Mobile App appropriate array.
 * Returns array as plain array or JSON
 */
class MobileConfigs
{
    private $configs;

    /** @var SalesfloorServices $sfServices */
    private $sfServices;

    /** @var Base $algolia */
    private $algoliaIndexer;

    private static $retailerIdMapping;

    /** @var Multilang $multilang */
    private $multilang;

    /** @var NameSuggester $nameSuggester */
    private $nameSuggester;

    /** @var Oauth2Service $oauth2Service */
    private $oauth2Service;

    /** @var LoggerInterface */
    private $logger;

    /**
     * contructor
     *
     * @param array          $configs The array of configs
     * @param LoggerInterface         $logger
     * @param Lang           $langService
     * @param AlgoliaIndexer $algolia
     */
    public function __construct(
        Configs $configs,
        LoggerInterface $logger,
        Base $algolia,
        Multilang $multilang,
        Translator $translator,
        NameSuggester $nameSuggester,
        Oauth2Service $oauth2Service
    ) {
        $this->setBaseConfigs($configs);

        // Instantiated without service container, as the configs are rejiggered on the fly for multibrand. Yuck.
        $this->sfServices     = new SalesfloorServices($configs, $logger, $multilang, $translator);
        $this->algoliaIndexer = $algolia;
        $this->multilang = $multilang;
        $this->nameSuggester = $nameSuggester;
        $this->oauth2Service = $oauth2Service;
        $this->logger = $logger;
    }

    /**
     * Save configs into class static property
     * @param array $configs The array of configs
     */
    public function setBaseConfigs($configs)
    {
        $this->configs = $configs;
    }

    /**
     * Get JSON version of the mobile configuration array
     * @return string String JSON representation of Mobile config array
     */
    public function getJSON($configArray)
    {
        return json_encode($configArray);
    }

    /**
     * Get computed mobile configuration array
     * @return array Computed mobile configuration array
     */
    public function getArray($afterBootstrap = false)
    {
        return $this->compileArray($afterBootstrap);
    }

    /**
     * Finds a specific value from within the orginal config array
     * by $key. If variable is NOT set, will return the $default value
     * @param  string $key     Key to locate within config array
     * @param  type   $default Default value if key is NOT set within config array
     * @return type            Found value for $key within config array, or $default
     */
    private function findConfig($key, $default = '')
    {
        return isset($this->configs[$key]) ? $this->configs[$key] : $default;
    }

    /**
     * This is a special exception to handle domain config during bootstrap.
     * Since configs server doesn't have infra config, we need atm to have a place where this value is stored
     * since we can't rely on hostmap.
     *
     * @param bool $afterBootstrap
     * @param $key
     * @param $default
     *
     * @return mixed|type|string
     */
    private function findConfigBootstrap(bool $afterBootstrap, $key, $default = ''): string
    {
        if ($afterBootstrap === true) {
            if (!ConfigsHelper::isConfigServer()) {
                $this->logger->warning(
                    sprintf(
                        'This should only be called from configs server. Otherwise, this is not a bootstrap'
                    )
                );
            }

            // Let's do some magic here
            if ($this->configs['env'] === Loader::ENV_STACK_PROD) {
                switch ($key) {
                    case 'salesfloor_storefront.host':
                        return $this->configs['retailer.prd.sf_domain'];
                    case 'retailer.rest_api_url':
                        $host = $this->configs['retailer.prd.sf_domain'];
                        $parsed = parse_url($host);

                        return implode('', [
                            $parsed['scheme'] . '://api.',
                            $parsed['host'],
                        ]);
                }
            }
        }

        return $this->findConfig($key, $default);
    }

    /**
     * Function compiles and returns an array that contains all the configs
     * that the mobile app is expecting, in the proper structure, based on
     * the inital raw configs received in the constructor
     * @param bool $bootstrapOnly should return only bootstrap configs
     * @return array Config array in mobile ready structure
     */
    private function compileArray($bootstrapOnly)
    {
        $env           = $this->findConfig('env', 'dev');
        $retailerShort = $this->findConfig('retailers.short');
        // List of max products per grid type
        $productMaxCounts = [
            'deals'           => $this->findConfig('retailer.num_deals', 0),
            'myLooks'         => $this->findConfig('retailer.num_looks', 0),
            'topPicks'        => $this->findConfig('retailer.num_top_picks', 0),
            'newArrivals'     => $this->findConfig('retailer.num_deals', 0),
            'recommendations' => $this->findConfig('retailer.trending_recommendations.max', 0),
        ];

        $productMinCounts = [
            'recommendations' => $this->findConfig('retailer.trending_recommendations.min', 0)
        ];

        // Translate on-boarding "about me"
        $specificities = $this->findConfig('mobile.specificities', '{}');
        $extraSpecificities = $this->findConfig('mobile.extra_specificities', '{}');

        $locale = $this->multilang->getLocale();
        if (!empty($extraSpecificities)) {
            $specificitiesDecoded = json_decode($this->findConfig('mobile.specificities', '{}'), true);
            if (!empty($extraSpecificities['pages']['pick-store']['introduction'][$locale])) {
                $specificitiesDecoded['pages']['pick-store']['introduction'] = $extraSpecificities['pages']['pick-store']['introduction'][$locale];
            }
            $specificities = json_encode($specificitiesDecoded, JSON_PRETTY_PRINT);
        }
        $ssoEnabled = $this->findConfig('retailer.sso.is_enabled');

        // These configs are populated before a user logs in
        // Should be kept to the minimal
        // Requires deployment to the Mobile Config Sever to alter
        $bootstrapConfigs = [
            // General Attributes
            'RetailerId'                 => $this->findConfig('mobile.retailer-id'),
            'Stack'                      => $env,
            'BaseUrl'                    => rtrim($this->findConfigBootstrap($bootstrapOnly, 'salesfloor_storefront.host'), '/'),
            'SfApiUrl'                   => rtrim($this->findConfigBootstrap($bootstrapOnly, 'retailer.rest_api_url'), '/') . '/', // apiUrl 'https://api.mybabiesrus.ca/'
            'SfWpApiUrl'                 => $this->findConfigBootstrap($bootstrapOnly, 'salesfloor_storefront.host') . '/api/',
            'SfMposProxyUrl'             => $this->findConfig('salesfloor_mpos_proxy.host'),
            'NameTemplate'               =>
                [
                    'MobileDashboard'   => $this->nameSuggester->getNameTemplateForLocale(
                        'mobile_dashboard',
                        $locale
                    ),
                    'MobileContact'     => $this->nameSuggester->getNameTemplateForLocale(
                        'mobile_contact',
                        $locale
                    ),
                    'ChatRepName'       => $this->nameSuggester->getNameTemplateForLocale(
                        'chat_rep_name',
                        $locale
                    ),
                    'GroupTaskRepName'  => $this->nameSuggester->getNameTemplateForLocale(
                        'group_task_rep_name',
                        $locale
                    ),
                    'ChatCustomerName'  => $this->nameSuggester->getNameTemplateForLocale(
                        'chat_customer_name',
                        $locale
                    ),
                ],

            // TaskAutoExpire
            'TaskAutoDismissEnabled'     => $this->findConfig('sf.task.auto_dismiss.enabled'),
            'TaskAutoDismissSettings'    => $this->findConfig('sf.task.auto_dismiss.settings'),

            // Onboarding Related
            'OnboardingLogoPath'         => $this->findConfig('mobile.onboarding_logo_path', null),
            'Specificities'              => str_replace(["\r", "\n", "\t"], '', $specificities),
            'i18nIsEnabled'               => $this->findConfig('retailer.i18n.is_enabled'),
            'defaultLocale'               => $this->findConfig('retailer.i18n.default_locale'),
            'globalLocales'               => $this->findConfig('sf.i18n.locales'),
            'RetailerPrefixNumericUsernames' => $this->findConfig('retailer.prefix_numeric_usernames'),

            // Login Page
            'RetailerCanChangeRetailerId' => $this->findConfig('mobile.retailer_can_change_retailer_id'),

            // Backoffice 2.0
            'CookieShowPrefix'            => $this->findConfig('retailer.cookie_show_prefix'),
            'RetailerShortName'           => $this->findConfig('retailer.short_name'),

            // Store Hours
            'StoreHours'                  => $this->findConfig('retailer.store_appointment_hours'), // DEPRECATED - use StoreAppointmentHours or StoreTextHours instead
            'StoreAppointmentHours'       => $this->findConfig('retailer.store_appointment_hours'),
            'StoreTextHours'              => $this->findConfig('retailer.store_text_hours'),

            // MFA
            'RetailerMFAEnabled' => $this->findConfig('mfa.authentication.is_enabled'),

            // Permit password update for user feed users
            'RetailerPasswordUpdateEnabled' => $this->findConfig('retailer.password_update.is_enabled'),

            // Retailer Logo Path
            'RetailerLogoPath' => 'https://cdn.salesfloor.net/salesfloor-assets/' . $this->findConfig('retailer.short_name') . '/logo.svg',

            // Mobile app upgrade notification
            'MobileAppUpgradeNotificationEnabled' => $this->findConfig('mobile.app_upgrade_notification.is_enabled'),
            'MobileAppUpgradeNotificationTimeout' => $this->findConfig('mobile.app_upgrade_notification.timeout'),
            'MobileAppUpgradeNotificationMessage' => $this->findConfig('mobile.app_upgrade_notification.message'),
            'BackofficePrimaryLoginThroughAppEnabled' => $this->findConfig('retailer.backoffice_primary_login_through_app.enabled'), // Required in bootstrap section for BO2 login attempts

            'isOauth2Enabled'        => $ssoEnabled,

            // If SSO is not enabled, getMobileAuthorizationUrl() and getMobileAccessTokenUrl() returns invalid an URLs (because the related configs are not properly set)
            // In theory it should not matter because `isOauth2Enabled` in the bootstrap config will be false so presumably the mobile/web should not use these URLs
            // But before when SSO was not enabled, these URLs were `null` in the bootstrap config, so we keep it as is.
            'Oauth2AuthorizationUrl' => $ssoEnabled ? $this->oauth2Service->getMobileAuthorizationUrl() : null,
            'Oauth2AccessTokenUrl'   => $ssoEnabled ? $this->oauth2Service->getMobileAccessTokenUrl() : null,

            'Oauth2RedirectUrlWeb'   => $ssoEnabled ? $this->findConfig('retailer.sso.provider.redirect_url.web') : null,
            'Oauth2ProviderType'     => $ssoEnabled ? $this->findConfig('retailer.sso.provider.type') : null,

            // some of Group Permissions Levels
            'PermissionsMap' => [
                'create-user' => Perms::$permissionsMap['create-user'],
            ],
        ];

        if ($bootstrapOnly) {
            return $bootstrapConfigs;
        }
        $locale = isset($_GET['sf_locale']) ? $_GET['sf_locale'] : null;

        // These configs will be delivered upon user login
        // The bulk of the mobile configs should exist here
        $nonBootstrapConfigs = [
            // Sharing
            'ShareConnectedServicesEnabled' => $this->findConfig('mobile.share_connected_services_enabled'),
            'ShareEmailEnabled'             => $this->findConfig('mobile.share_email_enabled'),
            'ShareInstagramEnabled'         => $this->findConfig('mobile.share_instagram_enabled'),
            'ShareFacebookEnabled'          => $this->findConfig('mobile.share_facebook_enabled'),
            'SharePinterestEnabled'         => $this->findConfig('mobile.share_pinterest_enabled'),
            'ShareWysiwygEnabled'           => $this->findConfig('mobile.share_wysiwyg_enabled'),

            'ComposeEmailWysiwygEnabled'    => $this->findConfig('mobile.compose_wysiwyg_enabled'),

            // Misc
            'AppDownloadUrls'            => $this->findConfig('mobile.app_download_urls'),
            'AppVersionUrls'             => $this->findConfig('mobile.app_version_urls'),

            // Retailer Attributes
            'RetailerLongName'           => $this->findConfig('retailer.name'), // retailerLongName 'neimanmarcus',
            'RetailerShortName'          => $this->findConfig('retailer.short_name'), // retailer -> bru
            'RetailerPrettyName'         => $this->findConfig('retailer.pretty_name'),
            'RetailerHomepageUrl'        => !empty($this->findConfig('retailer.url.locales')) ? $this->findConfig('retailer.url.locales') : $this->findConfig('retailer.url'), //url ->'https://mybabiesrus.ca',
            'RetailerCurrentBrand'       => $this->findConfig('retailer.brand'), // brand -> bru
            'RetailerBrandName'          => $this->findConfig('retailer.brand_name'),
            'RetailerAlternateBrand'     => $this->findConfig('retailer.alternate_brand'),
            'RetailerStorepageMode'      => $this->findConfig('retailer.storepage_mode', false) ? 'store' : 'rep', // mode-> store
            'RetailerGoogleAnalyticsUID' => $this->findConfig('retailer.google.ga_backoffice_mobile'),
            'RetailerGoogleAnalyticsWebBOUID' => $this->findConfig('retailer.google.ga_backoffice_web_v2'),
            'RetailerCorpEmailRequired'  => $this->findConfig('retailer.corporate-email.required', true),

            // Security configs
            'PasswordPolicyType' => $this->findConfig('security.policy.password'),
            'PasswordPolicyMinimumStrength' => $this->findConfig('security.policy.password.zxcvbn.strength'),

            // Country listing used in library intl-tel-input
            'RetailerDefaultCountry' => $this->findConfig('retailer.country.code'),
            'RetailerCountriesApplicationPreferred' => $this->findConfig('retailer.countries.application.preferred'),
            'RetailerCountriesApplicationAvailable' => $this->findConfig('retailer.countries.application.available'),

            // Products
            'ProductApiUrl'              => $this->findConfig('salesfloor_rest_api.host') . '/products/',
            'RetailerPricePrecision'     => $this->findConfig('mobile.retailer.pricePrecision'),
            'RetailerPricePrecisionHideEmptyDecimals' => $this->findConfig('mobile.retailer.pricePrecision.hide_empty_decimals'),
            'RetailerShowProductsBrandName'    => $this->findConfig('mobile.retailer.show.products_brand_name'),
            'RetailerShowProductColorAsUpc'    => $this->findConfig('retailer.transaction.show_product_color_as_upc'),

            'AlgoliaIndexKey' => $this->algoliaIndexer->getIndexName(),
            'AlgoliaAppId' => $this->findConfig('algolia.read.app_id'),
            'AlgoliaApiKey' => $this->findConfig('algolia.read.api_key'),

            'FirebaseUrl' => $this->findConfig('firebase.url'), // firebaseUrl -> 'https://radiant-fire-9638.firebaseio.com/',
            'FirebaseToken' => $this->findConfig('firebase.token'),

            'CANSUrl' => $this->findConfig('cans.url'),
            'CANSApiVersion' => $this->findConfig('cans.api.version'),

            // Cloudinary
            'CloudinaryApiKey' => $this->findConfig('mobile.cloudinary.apikey'),
            'CloudinaryCloudName' => $this->findConfig('mobile.cloudinary.cloudName'),

            // S3
            'S3AccessKeyId' => $this->findConfig('mobile.s3.accessKeyId'),
            'S3SecretAccessKey' => $this->findConfig('mobile.s3.secretAccessKey'),
            'S3Region' => $this->findConfig('mobile.s3.region'),

            // Chat
            'ChatQuickResponses'         => $this->findConfig('mobile.quick_responses', []),
            'SMSQuickChatResponses'      => $this->findConfig('mobile.sms.quick_responses', []),
            'ChatExtraQuickResponses'    => $this->findConfig('mobile.extra_quick_responses', []),

            //Customer service
            'ChatTransferToCs'               => $this->findConfig('retailer.chat.option.transfer-to-cs'),
            'ChatTransferToCsTextRequired'   => $this->findConfig('retailer.chat.option.transfer-to-cs.text.required'),

            // Clienteling
            'Clienteling'                  => $this->findConfig('retailer.clienteling.mode'),
            'RetailerHasLimitedVisibility' => $this->findConfig('retailer.clienteling.customer.limited_visibility.is_enabled'),
            'ClientelingBlackout'          => $this->findConfig('retailer.clienteling.customers.communication.blackout.is_enabled'),
            'ClientelingBlackoutPeriod'    => $this->findConfig('retailer.clienteling.customers.communication.blackout.period'),

            'isUserStoresEnabled'          => $this->findConfig('user-stores.user_assigned_stores.is_enabled'),

            // Text Messaging
            'TextMessaging'              => $this->findConfig('messaging.text.enabled'),
            'RetailerHasTextMessageChannel' => $this->findConfig('retailer.services.channel.text.enabled'),
            'RetailerCanSendSmsToMultipleRecipients' => $this->findConfig('messaging.text.multiple-recipients.enabled'),
            'RetailerCanSendEmailToMultipleRecipients' => $this->findConfig('messaging.email.multiple-recipients.enabled'),
            'RetailerMaximumSmsRecipients' => $this->findConfig('messaging.text.multiple-recipients.max'),

            // Customer Tags
            'RetailerHasCustomerTags'     => $this->findConfig('retailer.customer_tags.is_enabled'),
            'CustomerTagsAreReadOnly'     => $this->findConfig('retailer.customer_tags.is_read_only'),

            // Customer Activity Feed
            'RetailerHasCustomerActivityFeed'      => $this->findConfig('retailer.customer_activity_feed.is_enabled'),

            // Service labels
            'ServiceChatLabel'            => $this->sfServices->getChatLabel(),
            'ServiceAppointmentLabel'     => $this->sfServices->getAppointmentLabel(),
            'ServicePersonalShopperLabel' => $this->sfServices->getPersonalShopperLabel(),
            'ServiceEmailMeLabel'         => $this->sfServices->getEmailMeLabel(),
            'ServiceEmailMeReportsLabel'  => $this->sfServices->getEmailMeLabel('reports'),

            'ServiceChatRequestLabel'               => $this->sfServices->getChatRequestLabel(),
            'ServiceAppointmentRequestLabel'        => $this->sfServices->getAppointmentRequestLabel(),
            'ServicePersonalShopperRequestLabel'    => $this->sfServices->getPersonalShopperRequestLabel(),
            'ServiceEmailMeRequestLabel'            => $this->sfServices->getEmailMeRequestLabel(),
            'ServiceEmailMeReportsRequestLabel'     => $this->sfServices->getEmailMeRequestLabel('reports'),
            'ServiceEmailMeRequestChatHandoffLabel' => $this->sfServices->getEmailMeRequestChatHandoffLabel(),

             // Email/Sms subscription copy
            'SubscriptionEmailCheckboxLabel' => $this->sfServices->getSubscriptionEmailCheckboxLabel(null, $locale),
            'SubscriptionSMSCheckboxLabel'   => $this->sfServices->getSubscriptionSmsCheckboxLabel(null, $locale),

            // Out of office
            'OutOfOfficeMessage' => $this->sfServices->getOutOfOfficeMessage(null, $locale),

             // Services
            'RetailerServicesAppointmentManagementIsEnabled' => $this->findConfig('retailer.services.appointment_management.is_enabled'),
            'RetailerServicesAppointmentManagementAllCustomerIsEnabled' => $this->findConfig('retailer.services.appointment_management.all_customer.is_enabled'),
            'RetailerHasServicesAppointmentSaveToDevice' => $this->findConfig('retailer.services.appointment.save_to_device.is_enabled'),
            'RetailerServicesAppointmentReassignmentIsEnabled' => $this->findConfig('retailer.services.appointment_reassignment.is_enabled'),
            'RetailerServicesAppointmentTypes' => $this->findConfig('retailer.services.appointment.types'),

            // Mobile sidebar
            'RetailerCanEditEvents'      => $this->findConfig('mobile.retailer_can_edit_events'),

            // algolia
            'algoliaSearchableAttributes' => $this->algoliaIndexer->getSearchableAttributes(),

            // Storefront
            'RetailerHasChat'            => $this->findConfig('mobile.retailer_has_chat'),
            'RetailerHasDeals'           => $this->findConfig('mobile.retailer_has_deals'),
            'RetailerHasNewArrivals'     => $this->findConfig('mobile.retailer_has_new_arrivals'),
            'RetailerHasPersonalShopper' => $this->findConfig('mobile.retailer_has_personal_shopper'),
            'RetailerHasSpecialties'     => $this->findConfig('mobile.retailer_has_specialties'),
            'StorefrontMaxProductCounts' => $productMaxCounts, // storeProductCount -> ['deals' => 4, ....]
            'StorefrontMinProductCounts' => $productMinCounts,
            'StorefrontTrendingRecommendationsMode' => $this->findConfig('retailer.trending_recommendations', false),
            'RetailerHasChatAppointment' => $this->findConfig('mobile.retailer_has_chat_appointment'),
            'RetailerHasAppointments'    => $this->findConfig('retailer.services.appointment.is_enabled'),
            'RetailerHasStorefront'      => $this->findConfig('retailer.modular_connect.storefront.is_enabled'),
            //apointments request from the customer
            'RetailerHasAppointmentRequests'      => $this->findConfig('retailer.modular_connect.appointment_request.is_enabled'),
            'AppointmentRequestsAutoAcceptEnabled'      => $this->findConfig('retailer.services.appointment.auto-accept.is_enabled'),

            // Features
            'ContactsBarcodeScanner'                => $this->findConfig('mobile.barcode_scanner.contacts.enabled'),
            'ProductBarcodeScanner'                 => $this->findConfig('mobile.product_barcode_scanner'),
            'RetailerHasLookbooks'                  => $this->findConfig('mobile.retailer_has_lookbooks'),
            'RetailerCanBrowseLibrary'              => $this->findConfig('mobile.retailer_can_browse_library'),
            'RetailerCanShareFromBrowseLibrary'     => $this->findConfig('mobile.retailer_can_share_from_browse_library'),
            'RetailerHasTasks'                      => $this->findConfig('mobile.retailer_has_tasks'),
            'RetailerHasMultipleEmailTemplates'     => $this->findConfig('retailer.multiple_email_templates.enabled'),
            'RetailerEmailTemplatesLayouts'         => $this->findConfig('retailer.multiple_email_templates.layout'),
            'RetailerCanImportContacts'             => $this->findConfig('retailer.contacts.import_enabled'),
            'RetailerContactsConsentRequired'       => $this->findConfig('retailer.consent_required.mobile'),
            'RetailerCanChangeCommunicationConsent' => $this->findConfig('retailer.modular_connect.can_change_communication_consent'),
            'ProductsHasModal'                      => $this->findConfig('mobile.products_modal_enabled'),
            'RetailerHasInventoryLookup'            => $this->findConfig('inventory.lookup.is_enabled'),
            'RetailerHasPhoneCallEnabled'           => $this->findConfig('mobile.phone_call.is_enabled'),
            'RetailerCanAddCustomerToContacts'      => $this->findConfig('retailer.add_customer_to_my_contacts.is_enabled'),
            'RetailerHasAssetManagement'            => $this->findConfig('mobile.retailer_has_asset_management'),
            'RetailerHasFeedValidation'           => $this->findConfig('mobile.retailer_has_feed_validation'),
            'RetailerHasAssociateRelationships'     => $this->findConfig('retailer_customer.associate_relationships'),
            'RetailerHasProductsFeed'               => $this->findConfig('mobile.retailer_has_products_feed'),
            'RetailerCanAddContacts'                => $this->findConfig('retailer.modular_connect.can_add_contacts'),
            'RetailerCanShareAnUpdate'              => $this->findConfig('retailer.can_share_an_update'),
            'RetailerHasCorporateTasks'             => $this->findConfig('retailer.modular_connect.corporate_tasks.is_enabled'),
            'RetailerHasAutomatedTasks'             => $this->findConfig('sf.task.automated.enabled'),

            // Retailer attributes
            'RetailerBeginWeek'            => $this->findConfig('retailer.reporting.begin_week'),
            'RetailerChatMode'             => $this->findConfig('queue.byStore'),
            'RetailerChatDelay'            => $this->findConfig('retailer.chat_delay', '[]'),
            'RetailerChatRoutingMode'      => $this->findConfig('retailer.chat.routing.mode'),
            'RetailerHasExtendedAttributes' => $this->findConfig('retailer.modular_connect.extended_attributes.is_enabled'),

            // Misc
            'MenuLogoPath'               => $this->findConfig('mobile.menu_logo_path'),
            'CameraMaxHeight'            => $this->findConfig('mobile.camera_max_height'),
            'CameraMaxWidth'             => $this->findConfig('mobile.camera_max_width'),
            'CameraQuality'              => $this->findConfig('mobile.camera_quality'),
            'CameraMaxPhotos'            => $this->findConfig('mobile.camera_max_photos'),
            'BrandNeedsPadding'          => $this->findConfig('mobile.brand_needs_padding'),
            'EmailMeLabel'               => $this->findConfig('mobile.email_me_label'),
            'AvailableLabelOptions'      => $this->findConfig('customers.labels'),
            'LoaderDelay'                => $this->findConfig('mobile.loader_delay'),
            'LoaderMessageInterval'      => $this->findConfig('mobile.loader_message_interval'),
            'FirebaseMaxReconnectionTime' => $this->findConfig('mobile.fb_max_reconnection_time'),
            'FirebaseShowDisconnectMessage' => $this->findConfig('mobile.fb_show_disconnect_message'),
            'CanForwardToCS'             => $this->findConfig('customerservice.can_forward'),

            // Mobile Draft
            'DraftAutoSaveInterval'       => $this->findConfig('mobile.draft.auto_save_interval'),
            'DraftForShareEnabled'        => $this->findConfig('mobile.draft.share_enabled'),
            'DraftForComposeEnabled'      => $this->findConfig('mobile.draft.compose_enabled'),

            // Shop Feed/Instagram
            'isShopFeedEnabled' => $this->findConfig('retailer.shop_feed.enabled'),

            // mPOS
            'isMobileCheckoutEnabled' => $this->findConfig('mobile.retailer_has_mobile_checkout'),

            // Variants
            'ProductsExpandedVariantsEnabled' => $this->findConfig('products.expanded_variants.enabled'),
            'UsePriorityVariants' => $this->findConfig('products.expanded_variants.priority_badge.enabled'),

            // User Management
            'RetailerStartDate' => $this->findConfig('retailer.application_start_date'),

            //Mobile rep can see all appointments from his store
            'CanRepViewAllAppointments' => $this->findConfig('retailer.services.appointment.all.is_enabled'),

            'AssetTargetChannelLabels' => $this->sfServices->getAssetTargetChannelLabels(),

            // Video chat
            'isVideoChatEnabled' => $this->findConfig('retailer.chat.option.video-chat'),
            'isVirtualVideoChatEnabled' => $this->findConfig('retailer.virtual.option.video-chat'),

            'isMfaEnabled' => $this->findConfig('mfa.authentication.is_enabled'),

             // Outfits
            'isOutfitsEnabled' => $this->findConfig('retailer.outfits.is_enabled'),

            'outfitsSectionLabel' => $this->findConfig('retailer.outfits.section_label'),

            'outfitsTransactionsPerPage' => $this->findConfig('retailer.outfits.mobile.transaction_per_page'),

            'retailerChatFindNearbyStores' => $this->findConfig('retailer.chat.find_nearby_stores'),

            //Appointments custom hours
            'StoreAppointmentHoursIsEnabled' => $this->findConfig('retailer.store_appointment_hours.is_enabled'),
            'StoreAppointmentHoursGroupPermissions' => $this->findConfig('retailer.store_appointment_hours.group_permissions'),
            'StoreAppointmentHoursFirstDayOfWeek' => $this->findConfig('retailer.store_appointment_hours.first_day_of_week'),

            //Grouped products
            'GroupedProductsIsEnabled' => $this->findConfig('retailer.grouped_products.is_enabled'),
            'GroupedProductsURL' => $this->findConfig('retailer.grouped_products.url'),
            'GroupedProductsMaxProducts' => $this->findConfig('retailer.grouped_products.max_products'),
            'GroupedProductsMinProducts' => $this->findConfig('retailer.grouped_products.min_products'),
            'GroupedProductsSendStyledLinkLabel' => $this->sfServices->getSendStyledLinkLabel(),
            'GroupedProductsKpiStyledLinkLabel' => $this->sfServices->getGroupedProductsKpiStyledLinkLabel(),

            // Group tasks
            'GroupTasksIsEnabled' => $this->findConfig('retailer.group_tasks.is_enabled'),
            'GroupTasksLabel' => $this->sfServices->getGroupTasksLabel(),

            // Advanced search
            'AdvancedSearchTransactionMaxAmount' => $this->findConfig('retailer.search.filters.transaction.max'),
            // Api caching
            'ApiCacheExpiration' => $this->findConfig('mobile.api_cache_duration'),

            // Task configs
            'TaskQueryCutOffDaysBack' => $this->findConfig('mobile.dashboard.task_cutoff_days_back'),
            'AutoResolveTasksIsEnabled' => $this->findConfig('retailer.tasks.auto-resolve.is_enabled'),

            // PII Obfuscation/mask
            'PiiObfuscationIsEnabled' => $this->findConfig('retailer.pii.obfuscate.is_enabled'),

            // Contextual Widget
            'ContextualWidgetEventsRecordingEnabled' => $this->findConfig('retailer.contextual_widget_events_recording.is_enabled'),

            // Moderation
            'ModerationTextIsEnabled' => $this->findConfig('retailer.moderation.text.is_enabled'),
            'ModerationTextTimeout' => $this->findConfig('retailer.moderation.text.timeout'),

            //Appointments Lead Time
            'AppointmentLeadTime' => $this->findConfig('retailer.appointment_lead_time.mobile_app'),

            // Firebase configs
            'FirebaseWebAppConfiguration' => $this->findConfig('firebase.apps.web.configuration'),
            'FirebaseWebAppVapidKey' => $this->findConfig('firebase.apps.web.vapid_key'),

            // AI outreach
            'AiOutreachIsEnabled' => $this->findConfig('ai.outreach.is_enabled'),
            'AiOutreachRecommendedPrompts' => $this->findConfig('ai.outreach.recommended_prompts'),
        ];

        return array_merge($bootstrapConfigs, $nonBootstrapConfigs);
    }
}
