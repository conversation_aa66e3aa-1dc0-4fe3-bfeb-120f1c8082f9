<?php

declare(strict_types=1);

namespace Salesfloor\Schemas\AI;

use Salesfloor\Schemas\Base;

class Prompt extends Base
{
    const TYPE = 'aiprompt';

    protected $resourceType = self::TYPE;
    protected $selfSubUrl = '/ai/prompt';

    /**
     * Get the attributes that should be included in the JSON API response
     *
     * @param \Salesfloor\Models\AI\Prompt $resource
     * @return array
     */
    public function getAttributes($resource)
    {
        return [
            'title' => $resource->title,
            'prompt' => $resource->prompt,
        ];
    }
}
