<?php

namespace Salesfloor\Controllers\Base;

use Neomerx\JsonApi\Contracts\Encoder\EncoderInterface;
use Neomerx\JsonApi\Document\Document;
use Neomerx\JsonApi\Document\Link;
use Neomerx\JsonApi\Encoder\Encoder;
use Neomerx\JsonApi\Encoder\EncoderOptions;
use Neomerx\JsonApi\Encoder\Parameters\EncodingParameters;
use Neomerx\JsonApi\Factories\Factory;
use Salesfloor\API\Exceptions\Generic\Invalid\InvalidInputException;
use Salesfloor\API\Exceptions\Manager\MissingRequiredFieldException;
use Salesfloor\API\Exceptions\Security\AuthorizationException;
use Salesfloor\API\Security\User\User as SecurityUser;
use Salesfloor\Configs\Configs;
use Salesfloor\Exceptions\NotFoundException;
use Salesfloor\Exceptions\UnexpectedException;
use Salesfloor\Managers\Base as BaseManager;
use Salesfloor\Managers\Base\JsonApiManager;
use Salesfloor\Managers\Traits\Base\V1;
use Salesfloor\Models\Application\ManagerRequest;
use Salesfloor\Models\Base;
use Salesfloor\Models\Base as BaseModel;
use Salesfloor\Models\JsonApi\Join\BaseJoin;
use Salesfloor\Models\JsonApi\Join\ManyRelationJoin;
use Salesfloor\Models\JsonApi\Relation;
use Salesfloor\Models\JsonApi\Join\RelationJoin;
use Salesfloor\Models\Rep;
use Salesfloor\Services\Application\ManagerRequest as ManagerRequestService;
use Salesfloor\Services\GroupPermissionService;
use Salesfloor\Services\JsonApi\Exceptions\JsonApiBaseException;
use Salesfloor\Services\JsonApi\Exceptions\JsonApiRecursionLimitException;
use Salesfloor\Services\JsonApi\Exceptions\UnknownRelationException;
use Salesfloor\Services\JsonApi\JsonApiResource;
use Salesfloor\Services\MySQLRepository;
use Salesfloor\Services\SalesfloorServices;
use Salesfloor\Services\Util;
use Silex\Application;
use Symfony\Component\Translation\Translator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Salesfloor\API\Managers\Reps as RepsManager;

/**
 * Class JsonApiController
 *
 * @package Salesfloor\Controllers\Base
 */
class JsonApiController extends BaseController
{
    /** @var Translator $translateService */
    protected $translateService;

    /** An instance of this controller's manager's model, just to call its utility methods */
    protected $scratchModel;

    /**
     * Constructor
     *
     * @param JsonApiManager $manager A manager instance
     * @param Configs $configs
     * @param Translator $translateService
     */
    public function __construct($manager, $configs = null, $translateService = null)
    {
        $this->manager = $manager;
        $this->configs = $configs;
        $this->translateService = $translateService;
    }

    /**
     * @param $createParameters
     * @return BaseModel
     * @throws MissingRequiredFieldException
     * @throws \Exception
     */
    protected function doCreate($createParameters)
    {
        $model = $this->manager->create($createParameters);
        $this->beforeCreate($model);
        $this->manager->save($model);

        return $model;
    }

    /**
     * @param $createParameters
     * @return array
     * @throws MissingRequiredFieldException
     * @throws \Exception
     */
    protected function doCreateMany($createParameters)
    {
        $models = [];

        foreach ($createParameters as $params) {
            $model = $this->manager->create($params);
            $this->beforeCreate($model);
            $this->manager->save($model);
            $models[] = $model;
        }

        return $models;
    }

    /**
     * @param $id
     * @param $updateParameters
     * @param bool $patch
     * @return BaseModel
     * @throws MissingRequiredFieldException
     * @throws \Exception
     */
    protected function doUpdate(Application $app, $id, $updateParameters, $patch = false)
    {
        $scratchModel = $this->getModel();
        $originalModel = $this->manager->getOne([$scratchModel::ID_FIELD => $id]);
        $model = clone $originalModel;

        $this->manager->setFields($model, $updateParameters);
        $this->beforeUpdate($app, $originalModel, $model);

        $this->manager->save($model, $patch);

        $this->maskValues($model);

        return $model;
    }

    /**
     * Mask values before sending back data
     *
     * @param BaseModel $model A reference to an instance of a model
     */
    protected function maskValues(&$model)
    {
        // NOPE
    }

    /**
     * @return BaseModel
     */
    protected function getModel()
    {
        if (!isset($this->scratchModel)) {
            $modelClass = $this->manager->getFullModelName();
            $this->scratchModel = new $modelClass();
        }

        return $this->scratchModel;
    }

    /**
     * Get the connected user ID or throw an exception
     *
     * @param Application $app
     *
     * @return int|string
     */
    public function getLoggedInUserIdOrFail(Application $app)
    {
        $user = $app['user'];

        if (empty($user) || !($user instanceof SecurityUser)) {
            $app->abort(403, $this->translateService->trans('api_error_permission_denied'));
        }

        return $user->getId();
    }

    /**
     * Get the Rep model object for the currently logged in user or throw an exception
     * (In the context of API and security)
     *
     * @param  Application $app Application instance
     *
     * @return Rep The rep object when found
     */
    public function getLoggedInUserOrFail(Application $app)
    {
        $userId = $this->getLoggedInUserIdOrFail($app);
        $user   = $app['reps.manager']->getOneOrNull(['ID' => $userId]);

        if (empty($user)) {
            $app->abort(
                403,
                $this->translateService->trans('api_error_logged_in_user_not_exist', ['%userId%' => $userId])
            );
        }

        return $user;
    }

    /**
     * Get the connected user ID or null
     *
     * @param Application $app
     *
     * @return int|string|null
     * @deprecated Use getLoggedInUserIdOrFail() instead
     */
    public function getLoggedInUserId($app)
    {
        $user = $app['user'];
        return ($user && $user instanceof SecurityUser ? $user->getId() : null);
    }

    /**
     * Returns an array of the parsed request data
     * Can interpret json data as well as query string
     *
     * @param  Application $app Application instance
     *
     * @return array            Parsed data
     */
    protected function getAllRequestData(Application $app)
    {
        /** @var Request $currentRequest */
        $currentRequest = $app['request_stack']->getCurrentRequest();

        // If we want to support this new format, we need to update the available format
        $currentRequest->setFormat('json-api', 'application/vnd.api+json');

        $content = $currentRequest->getContent();
        if (in_array($currentRequest->getContentType(), ['json', 'json-api'])) {
            return json_decode($content, true);
        }
        parse_str($content, $parsed);
        if (!empty($parsed)) {
            return $parsed;
        }
        $query = $currentRequest->query->all();
        if (!empty($query)) {
            return $query;
        }
        return [];
    }

    /**
     * Check if the logged in user has permission
     *
     * @param Application $app
     * @param string      $permission  One permission from the $permissionsMap
     *
     * @return bool                    True if the user have access, otherwise false
     * @throws \Exception
     */
    public function loggedInUserHasPermission(Application $app, $permission)
    {
        // Get user from access token (JWT), Won't work for protected/public route
        $userId = $this->getLoggedInUserId($app);

        if (empty($userId)) {
            throw new AuthenticationException("You are not logged in");
        }

        $user = $app['reps.manager']->getOneOrNull(['ID' => $userId]);
        if (empty($user)) {
            throw new \Exception(sprintf("You are logged as userId [%s] but it doesn't exist anymore", $userId));
        }

        return GroupPermissionService::userHasPermission($user, $permission);
    }

    /**
     * Take a one model or an array of it, apply the schema (json-api) and return it
     * This is the new "returnModel()". Middleware will include the proper content-type
     *
     * @param mixed              $data    Model or array of models
     * @param BaseModel          $model   The model used, since $data could be empty
     * @param EncodingParameters $params  The data from the request needed to output the desired data
     * @param string             $version The data from the request needed to output the desired data
     * @param array              $meta    The data not included in the model
     *
     * @return string
     */
    protected function jsonApi($data, $model, EncodingParameters $params = null, $version = null, $meta = [])
    {
        $endpointUrl = $this->getEndpointUrl($version);
        $encoder     = Encoder::instance($model->getSchemasProvider(), new EncoderOptions(JSON_UNESCAPED_SLASHES, $endpointUrl));

        if (!empty($meta)) {
            $encoder->withMeta($meta);
        }

        $data = $encoder->encodeData($data, $params);

        return $data;

        // https://github.com/neomerx/json-api/issues/182
        // https://github.com/neomerx/json-api/wiki/Parsing-API-Parameters#http-responses

        //  $factory          = new Factory();
        //  $schemasContainer = $factory->createContainer($model->getSchemasProvider());
        //  $encoder          = $factory->createEncoder($schemasContainer, null);

        //  $response = new ApplicationResponse(
        //      new MediaType(MediaTypeInterface::JSON_API_TYPE, MediaTypeInterface::JSON_API_SUB_TYPE),
        //      new SupportedExtensions(),
        //      $encoder,
        //      $schemasContainer,
        //      $params
        //  );

        // This return 200
        //return $response->getContentResponse($data);

        // This return 201
        //return $response->getCreatedResponse($data);
    }

    /**
     * Extract the URL to prepend to all "self" and "related" of json api object
     *
     * @param null $version
     *
     * @return string
     */
    protected function getEndpointUrl($version = null)
    {
        $baseUrl = $this->configs['retailer.rest_api_url'];

        // We need to extract the version since i don't want to add it in the schema (because it's not versionized)
        return implode('/', array_filter([
            $baseUrl,
            $version !== 'default' ? $version : null,
        ]));
    }

    /**
     * Create JsonApi Encoder, so we can do some customization
     *
     * @param BaseModel   $model
     * @param string|null $version
     *
     * @return EncoderInterface
     */
    protected function getEncoder(BaseModel $model, $version = null): EncoderInterface
    {
        $endpointUrl = $this->getEndpointUrl($version);

        // This will only work if $model implement jsonapi.
        $encoder = Encoder::instance(
            $model->getSchemasProvider(),
            new EncoderOptions(JSON_UNESCAPED_SLASHES, $endpointUrl)
        );

        return $encoder;
    }

    /**
     * Encode $data as a json string
     *
     * @param EncoderInterface   $encoder
     * @param array              $data
     * @param EncodingParameters $params
     *
     * @return mixed
     */
    protected function encodeData($encoder, $data, $params)
    {
        $data = $encoder->encodeData($data, $params);

        return $data;
    }

    /**
     * Do we want a service or stay that here since only controller should need pagination
     *
     * @param Configs            $configs
     * @param EncodingParameters $encodingParameters
     * @param ManagerRequest     $managerRequest
     * @param BaseModel          $model
     * @param                    $count
     * @param string             $version (This is coming from $app['route.version'])
     *
     * @return array
     */
    protected function getPagination(
        Configs $configs,
        EncodingParameters $encodingParameters,
        ManagerRequest $managerRequest,
        BaseModel $model,
        $count,
        $version = null
    ) {
        $prev = $next = null;

        // EncodingParameters doesn't have default value for page/perPage, so use the ManagerRequest
        $page    = $managerRequest->getPage();
        $perPage = $managerRequest->getPerPage();
        $rootUrl = $configs['salesfloor_rest_api.host'];

        // http://jsonapi.org/format/#fetching-sparse-fieldsets
        // Note: The above example URI shows unencoded [ and ] characters simply for readability.
        // In practice, these characters must be percent-encoded, per the requirements in RFC 3986.

        $base = implode('/', array_filter([
            $rootUrl,
            $version,
            $model->getEndpoint(),
        ]));

        // This will remove extract "/" (Not perfect but working)
        $base = preg_replace('/([^:])(\/\/+)/', '$1/', $base);

        // TODO : Do we want to send the real nb of page (0 as a valid page) or 1 (So the UI never display 0)
        $nbPages = ceil($count / $perPage);

        $self  = $this->populatePaginationUrl($base, $encodingParameters, $page, $perPage);
        $first = $this->populatePaginationUrl($base, $encodingParameters, 0, $perPage);
        $last  = $this->populatePaginationUrl($base, $encodingParameters, $nbPages - 1, $perPage);

        // Since pagination starts at 0 and not one
        // Since we are using the same MySQLRepository, we won't change that afaik
        if ($page > 0) {
            $prev = $this->populatePaginationUrl($base, $encodingParameters, $page - 1, $perPage);
        }
        if ($page < ($nbPages - 1)) {
            $next = $this->populatePaginationUrl($base, $encodingParameters, $page + 1, $perPage);
        }

        // self/first/last will always exist, prev/next only if it's not the first/last page
        return [
            'links' => [
                Document::KEYWORD_SELF => new Link($self, null, true),
                Document::KEYWORD_FIRST => new Link($first, null, true),
                Document::KEYWORD_PREV => $prev ? new Link($prev, null, true) : null,
                Document::KEYWORD_NEXT => $next ? new Link($next, null, true) : null,
                Document::KEYWORD_LAST => new Link($last, null, true),
            ],
            'meta' => [
                'pages' => (int)$nbPages,
                'total' => (int)$count,
            ],
        ];
    }

    /**
     * Use during pagination to create self/first/prev/next/last
     * Use EncodingParameters from json-api Encoding parameters and repopulate the url
     * At the moment, it's not possible to have "custom" field since it's disabled in sf.request
     *
     * @param string             $base                                           base url of the pagination (with query
     *                                                                           string)
     * @param EncodingParameters $encodingParameters                             encoding parameters from json-api lib
     * @param int                $page                                           current page
     * @param int                $perPage                                        nb items per page
     *
     * @return string                                       the url of the page itself
     */
    protected function populatePaginationUrl($base, EncodingParameters $encodingParameters, $page, $perPage)
    {
        $queryString = [];

        // fields => array of array
        $fields = $encodingParameters->getFieldSets() ?: [];
        foreach ($fields as $key => $field) {
            $queryString["fields[$key]"] = implode(',', $field);
        }

        // filter => string
        $filters = $encodingParameters->getFilteringParameters() ?: [];
        foreach ($filters as $key => $field) {
            $queryString["filter[$key]"] = $field;
        }

        // Sort => array of object
        $sort            = $encodingParameters->getSortParameters() ?: [];
        $sortQueryString = [];
        foreach ($sort as $key => $field) {
            $prefix            = $field->isDescending() ? '-' : '';
            $sortQueryString[] = $prefix . $field->getField();
        }
        $queryString["sort"] = implode(',', $sortQueryString);

        // Include => array of string
        $include                = $encodingParameters->getIncludePaths();
        $queryString["include"] = implode(',', empty($include) ? [] : $include);

        $queryString["page[number]"] = $page;
        $queryString["page[size]"]   = $perPage;

        return $base . '?' . http_build_query($queryString);
    }

    /**
     * Same as getAll() but MUST have page[number] and page[size] in request
     *
     * @param Application $app
     *
     * @return string
     * @throws AuthorizationException
     * @throws \Exception
     */
    public function getList(Application $app)
    {
        // No need to process request (filters, page, sort, fields) since it's all done in the middleware now

        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        // Sometimes, we want to modify $managerRequest before sending it to the manager
        $this->beforeGetList($app, $managerRequest);

        // This doesn't support what i did for SF-19081. Getting last page when requesting an too far page
        $results = $this->manager->getList($managerRequest);

        if (!empty($results)) {
            foreach ($results as $result) {
                if (!$this->isGranted('view', $app, $result)) {
                    throw new AuthorizationException();
                }
            }
        }

        $count = $this->manager->count($managerRequest);

        // Some specific logic for change list
        $this->afterGetList($results);

        // Load relations
        $this->loadRelationships($app, $results, $managerRequest->getInclude(), $managerRequest->getVirtualFields());

        // Decided to not load them in the manager, for the same reason we don't load relationship in manager
        $this->loadVirtualFields($this->manager, $managerRequest->getVirtualFields(), $results);

        // Some specific logic for change list (Patch because of broken backward compatibility)
        $this->afterLoadVirtualFields($app, $results);

        // Obfuscate the PII data
        $this->obfuscateAll($app, $results);

        // Do we need to run the mask value here or we should let the schema take care of it ?

        // The pagination is only for getList()

        /** @var Encoder $encoder */
        $encoder = $this->getEncoder($this->manager->getModel(), $app['route.version']);

        $pagination = $this->getPagination(
            $app['configs'],
            $app['json-api.encoding-parameters'],
            $managerRequest,
            $this->manager->getModel(),
            $count,
            $app['route.version']
        );

        $encoder->withLinks($pagination['links']);
        $encoder->withMeta($pagination['meta']);

        // We can't call jsonApi directly, since we have custom params
        $jsonApiPayload = $this->encodeData($encoder, $results, $app['json-api.encoding-parameters']);

        // DEBUG: This is only for https://salesfloor.atlassian.net/browse/SF-26243
        if (empty($results) && ($this instanceof \Salesfloor\API\Controllers\Assets\V2)) {
            $request = $app['request'];

            $this->addDebug(
                $app,
                'SF-26243',
                sprintf(
                    "EMPTY result with request [%s], QUERY [%s], sf-app-id: [%s], sf-app-version: [%s]",
                    print_r($managerRequest, true),
                    $this->manager->getLastQuery(),
                    $request->headers->get('sf-app-id'),
                    $request->headers->get('sf-app-version')
                )
            );
        }

        return $jsonApiPayload;
    }

    /**
     * This will take model X and populate all relationship based on relations field in model.
     * JsonApi will not add them in include section except if specified in URL
     *
     * Relationships & included are only loaded if requested via the "include" field in query string.
     *
     * Specs says:
     *  "In addition, a resource object MAY contain any of these top-level members:
     *
     *   - attributes: an attributes object representing some of the resource’s data.
     *   - relationships: a relationships object describing relationships between the resource and other JSON:API resources.
     *   - links: a links object containing links related to the resource.
     *   - meta: a meta object containing non-standard meta-information about a resource that can not be represented as an attribute or relationship."
     *
     * At the moment, i decided not to included the relationship/include if not requested directly.
     * This could change in the future, but the logic is we need to query the DB anyway, so just return everything
     * If you don't want to use the included but query the api again, just ignore it
     * The structure of included is a bit special (v1.0) and we will need to stick with it for now.If we don't want
     * the client to handle it, just maybe another api call.
     *
     * Visibility was changed to protected because this function will be used in some subclass controllers.
     *
     * This will also load virtual fields. Since we are looping over them.
     *
     * More information here:
     *  - https://github.com/json-api/json-api/issues/1170
     *  - https://github.com/json-api/json-api/issues/1319
     *
     * @param Application    $app
     * @param                $results
     * @param                $include
     * @param                $virtualFields
     *
     * @param int            $level
     *
     * @return mixed
     */
    protected function loadRelationships(Application $app, &$results, $include, $virtualFields, $level = 0)
    {
        // failsafe, so we don't get in infinite loop (a->b->c->a->b->c->a->...)
        if ($level > Base::MAX_LIMIT_RELATIONSHIP_LEVEL) {
            throw new JsonApiRecursionLimitException(sprintf(
                "loadRelationships() reached the maximum depth [%s]. Current include [%s]. Possible circular relationship issue",
                Base::MAX_LIMIT_RELATIONSHIP_LEVEL,
                $include
            ));
        }

        // Handle obfuscation without relationship, I need access to $app, so I can't do it in the schema (jsonapi)
        // itself. This seems to be an "ok" place to do it.
        $obfuscate = function (&$model, $relation) use ($app) {
            $source = $model->{$relation->getInternalRelationship()} ?? null;

            if (empty($source)) {
                return $source;
            }

            if (is_array($source)) {
                foreach ($source as $m) {
                    $this->obfuscate($app, $m);
                }
            } elseif ($source instanceof Base) {
                $this->obfuscate($app, $source);
            }
        };

        $loadRelationship = function (&$model) use ($level, $app, $include, $virtualFields, $obfuscate) {
            $app['logger']->debug(
                sprintf(
                    "Loading relationship for model [%s] on level [%s]",
                    get_class($model),
                    $level
                )
            );

            // By default, model doesn't have any relations ([])
            $relations = $model->getRelations();

            // Level is just an way to prevent infinite loop and heavy relationship structure.
            // If you have more than 2 levels, you should probably make another API call.
            $nextLevel = ++$level;

            /** @var Relation $relation */
            foreach ($relations as $relation) {
                // Don't load relationship if not requested (Only necessary for first level relation)
                // All deeper relations coming from "getNextInclude()" are obviously requested.
                if (!$this->shouldIncludeRelationship($relation, $include, $app)) {
                    continue;
                }

                // Load into model, the relation. All the magic happens there.
                $relationshipResult = $app['json-api.relationship']->load($model, $relation);

                $obfuscate($model, $relation);

                $app['logger']->debug(
                    sprintf(
                        "Relation [%s] loaded with [%s] results",
                        $relation->getInternal(),
                        is_array($relationshipResult) ? count($relationshipResult) : (empty($relationshipResult) ? 0 : 1)
                    )
                );

                /** @var JsonApiManager $manager */
                $manager = $app[$relation->getRelationJoin()->getManager()];

                // It's important to use $relationshipResult (reference) and not  $model->{$relation->getInternal}
                // This way, we can support both if it's a paginate or not relationship
                $this->loadVirtualFields($manager, $virtualFields, $relationshipResult);

                // There's no reason trying to load next relation, if main one is empty
                if (empty($relationshipResult)) {
                    continue;
                }

                // Load the next relationship for this relationship and update include accordingly
                // If it's not inside the include, shouldIncludeRelationship() will skip them
                $this->loadRelationships($app, $relationshipResult, $this->getNextInclude($relation, $include), $virtualFields, $nextLevel);
            }
        };

        if (is_array($results)) {
            foreach ($results as &$result) {
                $loadRelationship($result);
            }
        } elseif ($results instanceof BaseModel) {
            $loadRelationship($results);
        } else {
            // Nothing to do here
        }

        // Keep the return here just in case you prefer to use it instead of the reference (parameter)
        return $results;
    }

    /**
     * This will add the virtual fields to the model (or array or models)
     *
     * @param JsonApiManager $manager At the moment not all of the manager are subclasses of JsonApiManager but they have the V1 trait which provides the same features
     * @param array          $virtualFields
     * @param mixed          $data
     *
     * @return mixed
     */
    protected function loadVirtualFields($manager, array $virtualFields, $data)
    {
        // If it's not using the trait, don't try to load virtual fields
        // $manager could use trait itself or extend from parent class which use that trait
        if (!$manager->useTrait(V1::class)) {
            return $data;
        }

        $baseModel = $manager->getModel();

        // Nothing to process if you don't have any virtual fields
        if (empty($baseModel->availableVirtualFields)) {
            return $data;
        }

        if (is_array($data)) {
            foreach ($data as $model) {
                $manager->bindVirtualFields($model, $manager->getVirtualFields($model), $virtualFields);
            }
        } elseif ($data instanceof Base) {
            $manager->bindVirtualFields($data, $manager->getVirtualFields($data), $virtualFields);
        } else {
            // NOP
        }

        // Keep the return here just in case you prefer to use it instead of the reference (parameter)
        return $data;
    }

    /**
     * Based on the include field in QS, check if relation should be loaded.
     * If not, we don't need to query the DB, because it wasn't requested.
     *
     * @param Relation $relation
     * @param          $include
     *
     * @return bool
     */
    private function shouldIncludeRelationship(Relation $relation, $include, $app)
    {
        if (is_string($include)) {
            $include = explode(',', $include);
        }

        // include will look like that
        // e.g: social-media.networks,notes,events,...

        $featuresExcluded = [
            SalesfloorServices::TYPE_APPOINTMENT =>
                empty($app['configs']['retailer.modular_connect.appointment_request.is_enabled']),
            SalesfloorServices::TYPE_PERSONAL_SHOPPER =>
                empty($app['configs']['retailer.has_personal_shopper']),
        ];

        foreach ($include as $includePart) {
            $split        = explode('.', $includePart, 2);
            $firstElement = reset($split);
            if ($firstElement === $relation->getInternal()) {
                return !(isset($featuresExcluded[$firstElement]) && $featuresExcluded[$firstElement]);
            }
        }
        return false;
    }

    /**
     * Since loadRelationships() is recursive, from current include, find the next (nested) include.
     * e.g: customers + include=social-media.networks
     * - Load first relation (social-media) because requested (shouldIncludeRelationship)
     * - On next loadRelationships() recursive call, we will pass via params the next include (networks)
     * - We can ignore any other include, since we are already in the "social-media" relation.
     *
     * @param Relation $relation
     * @param          $include
     *
     * @return array
     */
    private function getNextInclude(Relation $relation, $include): array
    {
        $nextInclude = [];

        if (is_string($include)) {
            $include = explode(',', $include);
        }

        foreach ($include as $includePart) {
            $split        = explode('.', $includePart, 2);
            $firstElement = reset($split);

            if ($firstElement === $relation->getInternal() && !empty($split[1])) {
                $nextInclude[] = $split[1];
            }
        }

        return $nextInclude;
    }

    /**
     * This is called just before get list. This is an easy way to modify managerRequest if needed without the need
     * to overwrite getList every time
     *
     * @param Application    $app
     * @param ManagerRequest $managerRequest
     */
    protected function beforeGetList(Application $app, ManagerRequest $managerRequest)
    {
        // NOP
    }

    /**
     * This is called just after get list, to modify results and count
     * for example: mapping/clean etc by php logic
     *
     * @param array $result
     */
    protected function afterGetList(array &$result)
    {
        // NOP
    }

    /**
     * This is called just after load virtual fields (after get list), to modify results and count
     * for example: mapping/clean etc by php logic
     *
     * @param Application $app
     * @param array $result
     */
    protected function afterLoadVirtualFields(Application $app, array &$result)
    {
        // NOP
    }

    /**
     * This is called just after create, to execute extra action
     * such as queue management, current use case id Corporate Task
     * Queue Management
     *
     * @param Application $app
     * @param BaseModel $model
     */
    protected function afterCreate(Application $app, BaseModel $model)
    {
        // NOP
    }

    /**
     * This is called just after update, to execute extra action
     * such as queue management, current use case id Corporate Task
     * Queue Management
     * @param Application $app
     * @param BaseModel $originalModel
     * @param BaseModel $model
     */
    protected function afterUpdate(Application $app, BaseModel $originalModel, BaseModel $model)
    {
        // NOP
    }

    /**
     * This should only be used in subrequest to gather relationship, otherwise always use getList.
     *
     * If fact, i'm not even sure if which case we will need getAll in controller.
     *
     * @param Application $app
     * @return array|string
     */
    public function getAll(Application $app)
    {
        // No need to process request (filters, page, sort, fields) since it's all done in the middleware now

        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        // TODO: MERGE - Do we will need that ?!?
        // You need to be careful with that, if you have too much data, it won't work (memory issue)
        $managerRequest->setPage(0);
        $managerRequest->setPerPage(-1);

        $results = $this->manager->getAll($managerRequest);

        // No pagination => No need to count
        // No mask() since jsonApi take care of it now
        // No relationship ?! i guess it's safer since it's a getAll

        return $this->jsonApi($results, $this->manager->getModel(), $app['json-api.encoding-parameters'], $app['route.version']);
    }

    /**
     * @param Application $app
     * @param $id
     * @return string|Response
     * @throws AuthorizationException
     * @throws \Exception
     */
    public function getOne(Application $app, $id)
    {
        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        // I'm still not sure if this should be here in the controllers or in the middleware or somewhere else
        $managerRequest->addToFilters([$this->manager->getIdField() => $id]);

        // manager (v1) doesn't try an exception if entity doesn't exist
        $results = $this->manager->getOne($managerRequest);

        if (!$this->isGranted('view', $app, $results)) {
            throw new AuthorizationException();
        }

        // https://jsonapi.org/format/#fetching-resources-responses-404
        if (empty($results)) {
            return $this->return404Response();
        }

        // Load relations. We decided not to load it in manager
        $this->loadRelationships($app, $results, $managerRequest->getInclude(), $managerRequest->getVirtualFields());

        $this->loadVirtualFields($this->manager, $managerRequest->getVirtualFields(), $results);

        // Create a temp result to apply the next actions/hooks
        // TODO: figure something better out.
        $tmpResults = [$results];

        // Some specific logic for change list
        $this->afterLoadVirtualFields($app, $tmpResults);

        $results = $tmpResults[0];

        $this->obfuscate($app, $results);

        return $this->jsonApi($results, $this->manager->getModel(), $app['json-api.encoding-parameters'], $app['route.version']);
    }

    ///////////////////////////////////////////////////////
    ///
    /// GET - Related&self route
    ///
    /// @link https://jsonapi.org/format/#fetching-relationships
    ///

    /**
     * Create self route (relationship)
     * e.g: "/customers/13/relationships/customer-social-media",
     *
     * @param Application $app
     * @param integer     $id       e.g: 13
     * @param string      $relation e.g: customer-social-media (match internal relation value (schema type))
     *
     * @return string
     * @throws UnknownRelationException
     */
    public function getRelationship(Application $app, $id, $relation)
    {
        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        /** @var BaseModel $baseModel */
        $baseModel = $this->manager->getModel();

        /** @var Relation $relationModel */
        $relationModel = $baseModel->getRelation($relation);

        if (empty($relationModel)) {
            throw new UnknownRelationException(
                sprintf(
                    "This relation [%s] for model [%s] doesn't exist",
                    $relation,
                    get_class($baseModel)
                )
            );
        }

        // Relationship are loaded via "include" however, relationships should be displayed (force)
        // even if not added in the "include" parameters. The current solution is to force it in the controller.
        // If we want to add it in the middleware, we would need to do some validation first to be sure it's a
        // real "relationships" route.

        // getInclude() is an array, so let's add the requested relation to it.
        $managerRequest->setInclude(
            array_unique(
                array_merge(
                    $managerRequest->getInclude(),
                    [$relation], // This is important, appending relationship to the include
                )
            )
        );

        $managerRequest->addToFilters([$this->manager->getIdField() => $id]);

        // TODO - If we want to have proper self/related we need to keep the same "parent" resource
        // We don't really need to load everything since we only need 1 relationships
        // However, relationship are always loaded for each entities
        // This could be improved (performance-wise) but keep it like this for now

        // I don't need to do getOneOrNull, since i'm using the v1 (manager request)
        $results = $this->manager->getOne($managerRequest);

        if (empty($results)) {
            throw new NotFoundException();
        }

        $this->loadRelationships($app, $results, $managerRequest->getInclude(), $managerRequest->getVirtualFields());

        ///////////////////////
        ///
        /// TODO
        /// We don't include any includes in the response when calling relationship route.
        /// They are not loaded and even passed to jsonapi encoder.
        ///
        /// Probably loop on relation data + loadRelationship for each of them.

        $encoder = $this->getEncoder($baseModel);
        $encoder
            ->withRelationshipSelfLink($results, $relation)
            ->withRelationshipRelatedLink($results, $relation)
        ;

        // We cannot rely on the "relation" name from the URL because of the prefix.
        $internalRelation = $relationModel->getInternalRelationship();

        $getRelationshipData = function ($data) use ($relationModel) {
            if (empty($data)) {
                if ($relationModel->getType()->isToOne()) {
                    return null;
                } else {
                    return [];
                }
            } else {
                return $data;
            }
        };

        if ($relationModel->getPaginate()) {
            $encoder->withMeta($results->{$internalRelation}->meta);

            // It seems like if it's a 1-1 => null, if 1-n => []
            // @link https://jsonapi.org/format/#fetching-relationships-responses-200
            $data = $encoder->encodeIdentifiers(
                $getRelationshipData($results->{$internalRelation}->data),
                $app['json-api.encoding-parameters']
            );
        } else {
            $data = $encoder->encodeIdentifiers(
                $getRelationshipData($results->{$internalRelation}),
                $app['json-api.encoding-parameters']
            );
        }

        return $data;
    }

    /**
     * Create related route (relationship)
     * e.g: "/customers/13/social-media"
     * This should return all social media for customer 13. Social media must be the main entity.
     *
     * "If present, a related resource link MUST reference a valid URL,
     * even if the relationship isn’t currently associated with any target resources.
     * Additionally, a related resource link MUST NOT change because its relationship’s content changes."
     *
     * https://github.com/json-api/json-api/issues/508
     * https://jsonapi.org/recommendations/#urls
     *
     * @param Application $app
     * @param integer     $id       e.g: 13
     * @param string      $relation e.g: social-media (match internal relation value)
     *
     * @return string
     * @throws UnknownRelationException
     * @throws \Exception
     */
    public function getRelated(Application $app, $id, $relation)
    {
        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        // We need the parent data, so we can build the child entity (based on the parent info).
        // We need to ignore the exception, otherwise we get a 500 "something bad happened".
        // The getOneV1() is returning null if not found, but we should handle both the same in the future.
        $parentModel = $this->manager->getOneOrNull([
            $this->manager->getIdField() => $id
        ]);

        // This should be done in the base, but will keep it here for now.
        if (empty($parentModel)) {
            throw new NotFoundException();
        }

        // The main manager is only used to gather the relationship data
        // relation become the main entity now

        // relationship & related are using the same manager as the parent one
        // We don't need it, in this case

        /** @var Relation $relationModel */
        $relationModel = $parentModel->getRelation($relation);

        if (empty($relationModel)) {
            throw new UnknownRelationException(
                sprintf(
                    "This relation [%s] for model [%s] doesn't exist",
                    $relation,
                    get_class($parentModel)
                )
            );
        }

        /** @var RelationJoin $relationJoin */
        $relationJoin = $relationModel->getRelationJoin();

        $managerJoinName = $relationJoin->getManager();

        if (empty($app[$managerJoinName])) {
            throw new \Exception("Unknown manager");
        }

        /** @var JsonApiManager $relatedManager */
        $relatedManager = $app[$managerJoinName];
        $relatedModel   = $relatedManager->getModel();

        /** @var array $join */
        $filters = [];
        foreach ($relationJoin->getJoin() as $join) {
            $filters[$join['destination']] = $parentModel->{$join['source']};
        }

        // getRelatedModel is expecting a "JsonApiManager" via type hinting. Even though it not mandatory
        // since we could only use the trait directly.
        if ($results = $this->getRelatedModel($managerRequest, $relatedManager, $relationModel, $filters)) {
            $this->loadRelationships($app, $results, $managerRequest->getInclude(), $managerRequest->getVirtualFields());
        }

        // The "links" section isn't used, so won't add it since not populated properly.
        $pagination = null;
        if ($relationModel->getPaginate()) {
            $count = $relatedManager->count($managerRequest);

            $pagination = $this->getPagination(
                $app['configs'],
                $app['json-api.encoding-parameters'],
                $managerRequest,
                $this->manager->getModel(),
                $count,
                $app['route.version']
            );
        }

        return $this->jsonApi($results, $relatedModel, $app['json-api.encoding-parameters'], $app['route.version'], $pagination['meta'] ?? []);
    }

    ///////////////////////////////////////////////////////
    ///
    /// POST - Related&self route
    ///

    /**
     * Create self route (relationship)
     * e.g: "/customers/13/relationships/social-media",
     *
     * @param Application $app
     * @param             $id
     * @param             $relation
     *
     * @return Response
     */
    public function postRelationship(Application $app, $id, $relation)
    {
        // TODO
        return new Response("get relationship");
    }

    /**
     * Create related route (relationship)
     * e.g: "/customers/13/social-media"
     *
     * @param Application $app
     * @param             $id
     * @param             $relation
     *
     * @return Response
     */
    public function postRelated(Application $app, $id, $relation)
    {
        // TODO
        return new Response("get related");
    }

    ///////////////////////////////////////////////////////
    ///
    /// PATCH - Related&self route
    ///
    /// @link https://jsonapi.org/format/#crud-updating-resource-relationships
    ///
    /// "A server MUST respond to PATCH, POST, and DELETE requests to a URL from a to-many relationship link as described below."
    ///
    /// "If a client makes a PATCH request to a URL from a to-many relationship link, the server MUST either
    ///  completely replace every member of the relationship, return an appropriate error response if some
    ///  resources can not be found or accessed, or return a 403 Forbidden response if complete replacement
    ///  is not allowed by the server."
    ///

    /**
     * Create self route (relationship)
     * e.g: "/customers/13/relationships/social-media",
     *
     * @param Application $app
     * @param             $id
     * @param             $relation
     *
     * @return Response
     */
    public function patchRelationship(Application $app, $id, $relation)
    {
        // TODO
        return new Response("patch relationship");
    }

    /**
     * Create related route (relationship)
     * e.g: "/customers/13/social-media"
     *
     * @param Application $app
     * @param             $id
     * @param             $relation
     *
     * @return Response
     */
    public function patchRelated(Application $app, $id, $relation)
    {
        // TODO
        return new Response("patch related");
    }

    ///////////////////////////////////////////////////////
    ///
    /// DELETE - Related&self route
    ///

    /**
     * Create self route (relationship)
     * e.g: "/customers/13/relationships/social-media",
     *
     * @param Application $app
     * @param             $id
     * @param             $relation
     *
     * @return Response
     */
    public function deleteRelationship(Application $app, $id, $relation)
    {
        // TODO
        return new Response("delete relationship");
    }

    /**
     * Create related route (relationship)
     * e.g: "/customers/13/social-media"
     *
     * @param Application $app
     * @param             $id
     * @param             $relation
     *
     * @return Response
     */
    public function deleteRelated(Application $app, $id, $relation)
    {
        // TODO
        return new Response("delete related");
    }

    ///////////////////////////////////////////////////////////////
    ///
    /// PUT: https://jsonapi.org/faq/#wheres-put
    ///
    /// "The correct method for partial updates, therefore, is PATCH, which is what JSON:API uses.
    ///  And because PATCH can also be used compliantly for full resource replacement, JSON:API hasn’t
    ///  needed to define any behavior for PUT so far. However, it may define PUT semantics in the future.
    ///


    ///////////////////////////////////////////////////////////////
    ///
    /// TODO
    ///
    /// We are NOT at the moment using the vnd.api+json structure used normally
    /// We are NOT updating relationships at the same time
    ///

    /**
     * Create an entity
     * http://jsonapi.org/format/#crud-creating
     *
     * Now we handle sideposting via custom support.
     *
     * WIP draft i based my code on:
     *  - https://github.com/json-api/json-api/pull/1197 (sideposting)
     *  - https://github.com/json-api/json-api/pull/1244 (lid)
     *
     * @TODO: JSONAPI 1.2
     *
     * @param Application $app
     *
     * @return Response
     * @throws \Exception
     */
    public function create(Application $app)
    {
        /** @var JsonApiResource $jsonApiResource */
        $jsonApiResource = $app['json-api.resource'];

        /** @var MySQLRepository $repo */
        $repo = $this->manager->getRepository();

        $repo->beginTransaction();

        try {
            // Since we handle side posting, we need to put everything here in a transaction
            $model = $this->executeActionMainEntity($jsonApiResource, $app['manager.request'], function ($data) use ($app) {
                $scratchModel = $this->getModel();

                if (!$this->isGranted('create', $app, $scratchModel)) {
                    throw new AuthorizationException();
                }

                return $this->doCreate($data);
            });

            // This will modify model and add new posted entities to it.
            $includedRelationship = $this->processRelationshipEntities($app, $model, $jsonApiResource->getRelationships());

            $this->afterCreate($app, $model);
            $repo->commit();
        } catch (\Exception $e) {
            $repo->rollback();

            // Let the middleware handle the exception
            throw $e;
        }

        // Since in POST there's no encoding parameters (include, fields, etc) we need to build
        // a custom one for the response, otherwise, the post won't include the new created relationship entities.
        $factory = new Factory();
        $params  = $factory->createQueryParameters($includedRelationship);

        return $this->returnJsonApiCreateResponse($model, $this->manager->getModel(), $params, $app['route.version']);
    }

    /**
     * Update an entity
     * http://jsonapi.org/format/#crud-updating
     *
     * @param Application $app
     * @param int         $id
     *
     * @return Response
     * @throws JsonApiBaseException
     */
    public function update(Application $app, $id)
    {
        /** @var JsonApiResource $jsonApiResource */
        $jsonApiResource = $app['json-api.resource'];

        // TODO - Should i put this in a middleware ?
        if ($jsonApiResource->getId() != $id) {
            throw new JsonApiBaseException('Route / id field doesn\'t match');
        }

        /** @var MySQLRepository $repo */
        $repo = $this->manager->getRepository();

        $repo->beginTransaction();

        $includedRelationship = [];

        try {
            $originModel = $this->manager->getOneOrNull([$this->getModel()->getIdField() => $id], null, false);

            if (!$this->isGranted('update', $app, $originModel)) {
                throw new AuthorizationException();
            }

            // Since we handle side patching, we need to put everything here in a transaction
            $model = $this->executeActionMainEntity($jsonApiResource, $app['manager.request'], function ($data) use ($id, $app) {
                return $this->doUpdate($app, $id, $data);
            });

            // This will modify model and add new posted entities to it.
            $includedRelationship = $this->processRelationshipEntities($app, $model, $jsonApiResource->getRelationships());

            $this->afterUpdate($app, $originModel, $model);
            $repo->commit();
        } catch (\Exception $e) {
            $repo->rollback();

            // Let the middleware handle the exception
            throw $e;
        }

        // Since in PATCH there's no encoding parameters (include, fields, etc) we need to build
        // a custom one for the response, otherwise, the post won't include the new created relationship entities.
        $factory = new Factory();
        $params  = $factory->createQueryParameters($includedRelationship);

        return $this->returnJsonApiCreateResponse($model, $this->manager->getModel(), $params, $app['route.version']);
    }

    private function executeActionMainEntity(JsonApiResource $jsonApiResource, ManagerRequest $managerRequest, callable $action)
    {
        // request is decode/parse in middleware
        $data = $jsonApiResource->getAttributes();

        // Create + beforeCreate => save
        /** @var BaseModel $model */
        $model = $action($data);

        // There's no validation at all, i can create a object with no data at all
        // MySQL have default value, so hit the DB to get the new data

        // getOne() and getAll() use __call() and can use the way (filters, sort, etc)
        // or the new way via a ManagerRequestModel
        $model = $this->manager->getOne(
            $managerRequest->setFilters(
                [$this->manager->getIdField() => $model->getId()]
            )
        );

        // This is ONLY used when doing getRelationshipsFromData, because
        // the structure (No include) is different if it's coming from a GET or not
        $model->setIsFromSidePosting(true);

        return $model;
    }

    /**
     * Generic preprocessing function that will handle all type of relation join.
     *
     * @param Application $app
     * @param BaseModel   $model
     * @param string      $type
     * @param array       $resources
     *
     * @throws JsonApiBaseException
     * @throws UnexpectedException
     * @throws InvalidInputException
     */
    private function preprocessingRelationship(Application $app, BaseModel $model, string $type, array $resources): void
    {
        if (empty($resources)) {
            throw new UnexpectedException("You don't have any relationship resources to process");
        }

        /** @var Relation $relation */
        $relation = $model->getRelation($type);

        if (empty($relation)) {
            throw new JsonApiBaseException(
                sprintf(
                    "Invalid relation [%s] for model [%s]",
                    $type,
                    $model->getClassLeaf()
                )
            );
        }

        /** @var BaseJoin $join */
        $join = $relation->getRelationJoin();

        // TODO : Not sure if a switch on relation join type is the best way to handle that.
        // When we will do n:m relation, we will have a better idea how it should be done. (e.g: customer tags)

        if ($join instanceof RelationJoin) {
            $this->processingRelationJoinRelationship($app, $model, $resources, $relation);
        } elseif ($join instanceof ManyRelationJoin) {
            // TODO
        }
    }

    /**
     * When processing relationship entities on UPDATE request, if resource is missing from the request payload, but
     * there are still resource entities in db, these entities are not deleted properly. Here we must do a post
     * processing to remove these entities from db since there are not in the request payload anymore.
     *
     * @param Application $app
     * @param BaseModel   $model
     * @param array       $jsonApiResources
     *
     * @throws InvalidInputException
     */
    private function postprocessingRelationship(Application $app, BaseModel $model, array $jsonApiResources): void
    {
        $relations = $model->getRelations();

        if (empty($relations)) {
            return;
        }

        $emptyRelationTypes = array_diff_key($relations, $jsonApiResources);

        foreach (array_keys($emptyRelationTypes) as $type) {
            $emptyRelation = $relations[$type];

            $join = $emptyRelation->getRelationJoin();

            if ($join instanceof RelationJoin) {
                $this->processingRelationJoinRelationship($app, $model, [], $emptyRelation);
            } elseif ($join instanceof ManyRelationJoin) {
                // TODO
            }
        }
    }

    /**
     * This handle a specify type of join with 1 manager + array of join field.
     * We use this join mostly for 1:N relation, but could be used for 1:1 relation too.
     *
     * Relationship inside a patch are more considered as a "PUT". It's a all inclusive.
     *
     * If you have a 1:n relation that include lots of data, this won't work properly.
     * We don't have any failsafe for that.
     *
     * @param Application $app
     * @param BaseModel   $model
     * @param array       $resources
     * @param Relation    $relation
     *
     * @throws InvalidInputException
     */
    private function processingRelationJoinRelationship(Application $app, BaseModel $model, array $resources, Relation $relation): void
    {
        $joinType = $relation->getType();

        // Handle 1:N relation only for now
        if (!$joinType->isToMany() || !$joinType->isFromOne()) {
            return;
        }

        /** @var BaseJoin $join */
        $join = $relation->getRelationJoin();

        /** @var JsonApiManager $manager */
        $manager = $app[$join->getManager()];

        $filters = [];
        foreach ($join->getJoin() as $condition) {
            $filters[$condition['destination']] = $model->{$condition['source']};
        }

        // This will fetch all current resource for this specific relation
        $currentResources = $manager->getAll($filters, 0, -1, false);

        $currentIds = array_column($currentResources, $manager->getIdField());

        // lid resource are new, id a updated, missing are deleted
        // can't use array_column, because id field is private !!
        $newIds = array_filter(array_map(function ($resource) {
            /** @var JsonApiResource $resource */
            return $resource->getId();
        }, $resources));

        $toDeleteIds = array_diff($currentIds, $newIds);

        $manager->deleteMany([
            $manager->getIdField() => $toDeleteIds
        ]);
    }

    /**
     * Create/Update all relationship from this jsonApi resource.
     *
     * @param Application $app
     * @param BaseModel   $model
     * @param array       $jsonApiResources
     *
     * @return array
     * @throws InvalidInputException
     * @throws JsonApiBaseException
     * @throws UnexpectedException
     * @throws UnknownRelationException
     */
    private function processRelationshipEntities(Application $app, BaseModel $model, array $jsonApiResources)
    {
        $includedRelationship = [];

        /**
         * Build en model from the attributes of the json api resource
         *
         * @param BaseModel       $model
         * @param Relation        $relation
         * @param JsonApiResource $resource
         *
         * @return array
         */
        $build = function (BaseModel $model, Relation $relation, JsonApiResource $resource) use ($app) {
            $baseAttributes = $resource->getAttributes();

            /** @var RelationJoin $join */
            $join = $relation->getRelationJoin();

            foreach ($join->getJoin() as $condition) {
                $baseAttributes[$condition['destination']] = $model->{$condition['source']};
            }

            return $baseAttributes;
        };

        /**
         * Create/Update the model itself + return it
         *
         * @param BaseModel       $model
         * @param Relation        $relation
         * @param JsonApiResource $resource
         *
         * @return BaseModel
         */
        $save = function (BaseModel $model, Relation $relation, JsonApiResource $resource) use ($app, $build): BaseModel {
            /** @var RelationJoin $join */
            $join = $relation->getRelationJoin();

            /** @var JsonApiManager $manager */
            $manager = $app[$join->getManager()];

            if ($resource->getId()) {
                // Simulate a doUpdate (without masking, since jsonapi will take care of it (mapping))
                $model = $manager->getById($resource->getId());
                $manager->setFields($model, $resource->getAttributes());
            } else {
                // Simulate a doCreate
                // Build field / data to pass to the manager (Include in field the 'linking' field from relation)
                $attributes = $build($model, $relation, $resource);
                $model      = $manager->create($attributes);
            }

            // This will handle create/update
            $manager->save($model);

            // This is to be sure we get all "real" data after save (since it can be modified in beforeSave)
            // I can't use RequestManager, since not all managers extend the new traits v1.
            $newModel = $manager->getOne(
                [
                    $model->getIdField() => $model->getId(),
                ]
            );

            // TODO: JsonApi 1.2 How should we return lid to client ?
            // Is it important for now ? Should we fork neomerx to include it ? or just wait ?
            // $newModel->lid = $resource->getLid();

            return $newModel;
        };

        foreach ($jsonApiResources as $type => $resources) {
            // The relationship are split by type now, so it's easier to do "pre" manipulation
            // since they are already grouped by. In this case, of this type of relation.

            if ($this->shouldProcessRelationship($app)) {
                $this->preprocessingRelationship($app, $model, $type, $resources);
            }

            /** @var JsonApiResource $resource */
            foreach ($resources as $resource) {
                // Create child relationship based on relation
                $type = $resource->getType();

                $includedRelationship[] = $type;

                /** @var Relation $relation */
                $relation = $model->getRelation($type);

                if (empty($relation)) {
                    throw new UnknownRelationException();
                }

                // TODO
                // I'm not sure how we will process those relationship on sideposting.
                // Most of are use case are 1:N.
                if ($relation->getType()->isFromMany()) {
                    throw new \Exception("This is not handled yet");
                } elseif ($relation->getType()->isToOne()) {
                    throw new \Exception("This is not handled yet");
                }

                // TODO
                // How to handle n:m relation / or 1:n that use extra table to do the relationship.
                // e.g: customer - customer-tag table + customer_tag_relationship table
                // Most of the time, we use extra table only when it's a n:m relation but it's not mandatory.

                // Create/Update the entity itself + getOne the new data (so it's up to date)
                $created = $save($model, $relation, $resource);

                // TODO : Multiple level relationship are not tested properly(WIP)
                // Relationship are only downward created and not upward.
                // We can't create relationship first and bind it the the parent after.
                // Good use case (1-n): Article -> Comment (Will add article_id to the comment (based on source/destination of the relation)
                // Base use case (n-1): Comment -> Author (We should create the author first, and link it in comment)
                $includedRelationshipChild = $this->processRelationshipEntities($app, $created, $resource->getRelationships());

                // Included relationship is a patch, so we can create a fake "json-api parameters" so the response
                // will include the 'included' section of the new entities created.
                $includedRelationship = array_merge($includedRelationship, $includedRelationshipChild);

                $internalRelation = $relation->getInternalRelationship();

                if ($relation->getType()->isToOne()) {
                    $model->{$internalRelation} = $created;
                } else {
                    $model->{$internalRelation}[] = $created;
                }
            }
        }

        //manipulation after processed relationships
        if ($this->shouldProcessRelationship($app)) {
            $this->postprocessingRelationship($app, $model, $jsonApiResources);
        }

        // This will be use in the jsonapi parameters, so we can include them in the response.
        return array_unique($includedRelationship);
    }

    /**
     * Check if we need to process relationship. This is used at the moment for 1:N relationship on update.
     * We will do a diff and delete entity (relation) that are not part of the request anymore.
     *
     * @param Application $app
     *
     * @return bool
     */
    private function shouldProcessRelationship(Application $app): bool
    {
        /** @var Request $request */
        $request = $app['request'];

        // Only PATCH (update) need preprocessing.
        if ($request->getMethod() === 'PATCH') {
            return true;
        }

        return false;
    }

    /**
     * Use filter to get the model linked to the parent
     *
     * @param ManagerRequest $managerRequest
     * @param JsonApiManager $relatedManager
     * @param Relation $relationModel
     * @param array $filters
     * @return array
     */
    private function getRelatedModel(
        ManagerRequest $managerRequest,
        BaseManager $relatedManager,
        Relation $relationModel,
        array $filters
    ) {
        if (empty($filters)) {
            return [];
        }
        $managerRequest->addToFilters($filters);
        if ($relationModel->getType()->isToOne()) {
            return $relatedManager->getOne($managerRequest);
        }

        if ($relationModel->getPaginate()) {
            // This is def. not perfect because the pagination information is not even returned to the client.
            // This is probably not used at the moment.
            return in_array(V1::class, Util::classUsesDeep(get_class($relatedManager), false)) ?
                 $relatedManager->getList($managerRequest) :
                 $relatedManager->getAll($managerRequest->getFilters(), $managerRequest->getPage(), $managerRequest->getPerPage());
        } else {
            // if pagination is not enabled, request params (perPage) and (page) will have no effects.
            // TODO: How can I pass perPage[x] but doing "-1" (all) if not set because of the default value.
            return in_array(V1::class, Util::classUsesDeep(get_class($relatedManager), false)) ?
                $relatedManager->getList($managerRequest->setPerPage(-1)) :
                $relatedManager->getAll($managerRequest->getFilters(), 0, -1);
        }
    }

    /**
     * Delete one resource
     * http://jsonapi.org/format/#crud-deleting
     *
     * @param Application $app
     * @param             $id
     * @return Response
     * @throws AuthorizationException
     */
    public function delete(Application $app, $id)
    {
        $model = $this->manager->getById($id);

        if (empty($model)) {
            return $this->response(404);
        }

        if (!$this->isGranted('delete', $app, $model)) {
            throw new AuthorizationException();
        }

        $count = $this->manager->deleteById($id);

        if ($count > 0) {
            return $this->response(204);
        } else {
            return $this->response(404);
        }
    }

    /**
     * "If a POST request did not include a Client-Generated ID and the requested resource has been created
     * successfully, the server MUST return a 201 Created status code."
     *
     * http://jsonapi.org/format/#crud-creating
     *
     * @param $data
     * @param $model
     * @param EncodingParameters $parameters
     * @param null $version
     *
     * @return Response
     */
    protected function returnJsonApiCreateResponse($data, $model, EncodingParameters $parameters, $version = null)
    {
        $data = $this->jsonApi($data, $model, $parameters, $version);

        $response = new Response();
        $response->setStatusCode(Response::HTTP_CREATED);
        $response->setContent($data);

        return $response;
    }

    /**
     * @param int $code
     * @param mixed $content
     *
     * @return Response
     */
    protected function response($code = 500, $content = null)
    {
        $response = new Response();
        $response->setStatusCode($code);
        $response->setContent($content);

        return $response;
    }

    /**
     * Get the logged in user or throw an AuthenticationException
     *
     * @param Application $app
     *
     * @return bool|Rep
     * @throws AuthenticationException
     */
    public function getLoggedInUser(Application $app)
    {
        $userId = $this->getLoggedInUserId($app);
        if (empty($userId)) {
            // This should never happen, since we passed the Authentication process
            throw new AuthenticationException();
        }

        // If a call to the API doesn't explicitly call this function, a deactivated / deleted rep still have access
        // to the api until the expiration of the token happens
        $user = $app['reps.manager']->getOneOrNull(['ID' => $userId, 'user_status' => 1]);
        if (empty($user)) {
            throw new AuthenticationException();
        }

        return $user;
    }

    /**
     * Since currently we don't have a solution to automatically convert managerRequest filter from
     * symfony request filter in the middleware, we'll convert them here.
     *
     * @param Application $app
     * @return ManagerRequest $managerRequest
     */
    protected function updateManagerRequestFilter(Application $app)
    {
        /** @var Request $req */
        $req = $app['request_stack']->getCurrentRequest();
        $filter = $req->get('filter', []);

        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        $managerRequest->setFilters($filter);

        return $managerRequest;
    }

    /**
     * Checks if the attributes are granted against the current authentication token and optionally supplied object.
     *
     * @param string $attribute
     * @param Application $app
     * @param BaseModel $model
     *
     * @return bool
     *
     */
    protected function isGranted($attribute, $app, $model)
    {
        return $app['security.authorization_checker']->isGranted($attribute, $model);
    }

    /**
     * Update multiple entities
     *
     * @param Application $app
     *
     * @return Response
     * @throws JsonApiBaseException
     */
    public function batchUpdate(Application $app)
    {
        /** @var JsonApiResource $jsonApiResource */
        $jsonApiResource = $app['json-api.resource'];

        /** @var ManagerRequest $managerRequest */
        $managerRequest = $app['manager.request'];

        /** @var MySQLRepository $repo */
        $repo = $this->manager->getRepository();

        $repo->beginTransaction();

        $includedRelationship = [];

        try {
            $attributes = $jsonApiResource->getAttributes();
            $ids = $attributes['ids'];
            $models = [];
            $originModels = $this->manager->getAll([$this->getModel()->getIdField() => $ids], null, false);

            $this->beforeBatchUpdate($app, $originModels);

            foreach ($ids as $id) {
                /** @var BaseModel $model */
                $model = $this->doUpdate($app, $id, $attributes['columns']);
                $model = $this->manager->getOne(
                    $managerRequest->setFilters(
                        [$this->manager->getIdField() => $model->getId()]
                    )
                );

                // This is ONLY used when doing getRelationshipsFromData, because
                // the structure (No include) is different if it's coming from a GET or not
                $model->setIsFromSidePosting(true);
                // This will modify model and add new posted entities to it.
                $includedRelationship = $this->processRelationshipEntities(
                    $app,
                    $model,
                    $jsonApiResource->getRelationships()
                );

                $models[] = $model;
            }
            $this->afterBatchUpdate($app, $originModels, $models);
            $repo->commit();
        } catch (\Exception $e) {
            $repo->rollback();
            // Let the middleware handle the exception
            throw $e;
        }

        // Since in PATCH there's no encoding parameters (include, fields, etc) we need to build
        // a custom one for the response, otherwise, the post won't include the new created relationship entities.
        $factory = new Factory();
        $params  = $factory->createQueryParameters($includedRelationship);

        return $this->returnJsonApiCreateResponse($models, $this->manager->getModel(), $params, $app['route.version']);
    }

    protected function beforeBatchUpdate(Application $app, array $models)
    {
        foreach ($models as $model) {
            if (!$this->isGranted('update', $app, $model)) {
                throw new AuthorizationException();
            }
        }
    }

    protected function afterBatchUpdate(Application $app, array $originModels, array $models)
    {
        // nothing
    }

    /**
    * Get the user id for the current session by context.
    * In team mode this will return the store user id.
    */
    protected function getUserId(Application $app)
    {
        /** @var RepsManager $manager */
        $manager = $app['reps.manager'];
        return (int) $manager->getUserIdFromContext($this->getLoggedInUserIdOrFail($app));
    }
}
