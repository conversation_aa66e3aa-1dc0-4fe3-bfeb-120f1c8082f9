https://salesfloor.atlassian.net/browse/CPD-2508
https://github.com/Salesfloor/platform/pull/17388

Add test for it.
==========================================================


ALTER TABLE `wp_users` 
CHANGE COLUMN `user_registered` `user_registered` DATETIME NOT NULL DEFAULT '2000-01-02 00:00:00' ;

update `sf_products`
set deal_start_date = null
where deal_start_date < '2000-01-01';

ALTER TABLE `wordpress_chicos`.`sf_task` 
CHANGE COLUMN `reminder_date` `reminder_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Date and time to send reminder for task (within hour)' ;



https://salesfloor.atlassian.net/browse/CPD-2452
----------------------------------------------------


find . -name "node_modules" -type d -exec rm -rf {} +

   ./robo generator:customer --size=1000000 --output=contacts.csv --feature=addresses --feature=tags --feature=2waysync buckle-dev
   ./robo generator:customer --size=10 --output=contacts.csv buckle-dev

   ./robo ci:import:contacts --filename=contacts.csv --no-delete buckle-dev

   ES Total time: 209
   DB Total time: 16

php ./api/app/crons/Devops1420Kludge.php

platform/codeception/tests/rest/Customer/CustomerAttributesJsonApiCest.php
platform/codeception/tests/rest/Customer/CustomerActivityFeedCest.php

------------------------------------------------------------------------------------------
./robo test:rest Customer/CustomerAttributesJsonApiCest.php:testUpdateCustomerAttributeValueValidSingle -- --env=ignore
./robo test:rest Customer/CustomerActivityFeedCest.php
./robo test:rest Tasks/TasksCest.php -- --env=capture
./robo test:rest Appointments/PublicApiCest.php
./robo test:rest Products/ProductFiltersCest.php
./robo test:rest Texts/TextSendCest.php
./robo test:rest Customer/CustomerApiCest.php:testCoreCustomerDirectGetWithEmailES -- --env=ignore
./robo test:rest Reps/RepsMeCest.php:testGetRepsMeTeamModeGroup4Attributes
./robo test:rest Customer/CustomerApiCest.php:testCoreCustomerDirectGetWithEmailES
./robo test:rest Products/ProductFiltersCest.php
./robo test:rest GroupTasks/GroupTasksCest.php:testCanResolveGroupTask
./robo test:rest Products/ProductFiltersCest.php:testGetProductSearchFilterWhenCategoryMultipleLevelOffI18n

./robo test:rest Services/AppointmentManagersCreateCest.php

./robo test:rest Tasks/CorporateTasksCest.php
./robo test:rest Tasks/ManagerTasksCest.php
./robo test:rest Tasks/TasksCest.php
./robo test:rest Customer/AdvancedSearchCest.php -- --env=ignore
./robo test:rest Requests/NewLeadsCest.php

./robo test:rest Reps/RepsMeCest.php
./robo test:rest Customer/CustomerV1ApiCest.php:testCustomerTags
./robo test:rest Customer/AdvancedSearchCest.php
./robo test:rest Texts/TextSendCest.php:testSendToCustomerKeywordBlockProduct

------------------------------------------------------------------------------------------
platform/codeception/tests/functional/Messaging/Text/TextChannelCest.php


./robo test:functional Messaging/Text/TextChannelCest.php -- --env=ignore
./robo test:functional Notifications/NearAppointmentsCest.php
./robo test:functional Reporting/InactiveFilteringCest.php

./robo test:functional Services/Algolia/AlgoliaIndexerV1Cest.php
./robo test:functional Tasks/NewRetailerTransactionFilteredCest.php
./robo test:functional Tasks/SoonToLapseSecondaryEmployeeAssignCest.php

./robo test:functional Tasks/RetailerTransactionEmployee/RetailerTransactionEmployeeCest.php
./robo test:functional Tasks/RetailerTransactionEmployeeAssignedMultipleCest.php
./robo test:functional Tasks/TransactionMinCest.php

snapshot=1 ./robo test:functional Exporter/ContactForSync/ContactForSyncExporterCest.php
./robo test:functional Tasks/SoonToLapseCest.php
./robo test:functional Tasks/SoonToLapseSecondaryCest.php

./robo test:functional Configs/ParserCest.php
./robo test:functional Tasks/RetailerTransactionEmployee/RetailerTransactionEmployeeCest.php
./robo test:functional Tasks/SoonToLapseCest.php

./robo test:functional Notifications/FCMBackendCest.php
./robo test:functional Notifications/NearAppointmentsCest.php
./robo test:functional ProductPanel/Panels/NewArrivals/NewArrivalAutoSelectedRandomCest.php


./robo test:functional ProductPanel/Panels/NewArrivals/NewArrivalFillProductsCest.php
./robo test:functional ProductPanel/Panels/TopPicks/TopPickFillProductsCest.php
./robo test:functional Reporting/InactiveFilteringCest.php


./robo test:functional Reporting/ProcessorServiceCest.php
./robo test:functional Reps/RepsCest.php
./robo test:functional Services/Algolia/AlgoliaCategoriesCest.php

./robo test:functional Services/Algolia/AlgoliaIndexerTooBigRecordCest.php
./robo test:functional Services/Algolia/AlgoliaIndexerV1Cest.php

------------------------------------------------------------------------------------------

platform/codeception/tests/api/importer/customer/ImportCustomerCest.php
platform/codeception/tests/api/services/ChatCest.php

./robo test:api customers/CustomerTagsCest.php -- --env=captue
./robo test:api customers/CustomerActivityFeedCest.php
./robo test:api customers/CustomerAddressesCest.php
./robo test:api customers/CustomerAddressesCest.php:testEditCustomerAddress -- --env=ignore

./robo test:api customers/CustomerCest.php
./robo test:api customers/CustomerEventsCest.php
./robo test:api exporter/ExporterContactCest.php

./robo test:api lookbook/LookbookCest.php
./robo test:api mail/EmailTrackingCest.php
./robo test:api products/ProductPanelCest.php

./robo test:api reporting/MarketingProcessorCest.php
./robo test:api reps/CreateRepCest.php
./robo test:api reps/SetRepAvatarUrlCest.php

./robo test:api sales/RecordTransactionCest.php
./robo test:api services/DataProcessing/TransactionCustomerCest.php
./robo test:api services/PhoneNumberNormalizationCest.php

./robo test:api transactions/TransactionsCest.php
./robo test:api customers/CustomerAddressesCest.php
./robo test:api reporting/MarketingProcessorCest.php


./robo test:api lookbook/LookbookCest.php

------------------------------------------------------------------------------------------
platform/codeception/tests/unit/Controllers/ChatTest.php
platform/codeception/tests/unit/Importer/Components/CsvIngestorTest.php
platform/codeception/tests/unit/JsonApi/ModelTest.php

./robo test:unit Controllers/ChatTest.php:testDispatchChatTranscriptToBoldChatMissingChatId
./robo test:unit Importer/Components/CsvIngestorTest.php
./robo test:unit JsonApi/ModelTest.php
./robo test:unit 
./robo test:unit 
./robo test:unit 
./robo test:unit 
./robo test:unit 
./robo test:unit 
./robo test:unit 

CPD-779-CPD-787-mockup-all-external-services-in-rest

CPD-779-codeception-test-improvement-phase-3



'An exception occurred while executing a query: SQLSTATE[42000]: Syntax error or access violation: 1055 Expression #4 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'wordpress_tests.c.retailer_customer_id' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by'


./robo run:cron ExportGroupTask buckle-dev "SF_TASK_EXPORT_START_DATE=2025-06-01 SF_TASK_EXPORT_END_DATE=2025-07-04"

./robo run:cron ExportGroupTask buckle-dev "SF_TASK_EXPORT_START_DATE=2025-06-01 SF_TASK_EXPORT_END_DATE=2025-07-04"