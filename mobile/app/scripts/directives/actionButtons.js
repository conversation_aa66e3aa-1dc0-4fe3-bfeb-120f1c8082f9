angular.module('sfmobileApp').directive('actionButtons', (
  $rootScope,
  navService,
  i18nService,
  intentService,
  messageService,
  feedbackService,
  activityLogService,
  localStorageService,
  featuresEnabledService,
  CONST_CONTACTS,
  CONST_TASKS
) => {
  return {
    restrict: 'E',
    templateUrl: 'views/directives/action-buttons.html',
    scope: {
      hasText: '=',
      hasTask: '=',
      taskId: '=',
      userData: '=',
      hasEmail: '=',
      hasPhone: '=',
      hasShare: '=',
      i18nOpts: '=',
      isFromCorpTask: '=',
      isFromGroupTask: '=',
      isAssetAvailable: '=',
      canAddToContacts: '=',
      taskShareDateTime: '=',
      altTextMessageAction: '&',
      addToMyContactsAction: '&'
    },
    link: (scope) => {
      const t = i18nService.t(scope.i18nOpts);

      scope.features = featuresEnabledService;

      scope.displayActionButtons = () =>
        (scope.userData?.ID && !scope.actionsAreEmpty()) ||
        scope.isFromCorpTask ||
        scope.isFromGroupTask ||
        (scope.canAddToContacts);

      scope.actionsAreEmpty = () => {
        return !scope.hasEmail && !scope.hasText && !scope.hasPhone && !scope.hasTask;
      };

      const { UNSUBSCRIBED } = CONST_CONTACTS.CONSENT_TYPE;
      const { TASK_TYPE } = CONST_TASKS;

      //For corp tasks we do not have userData info
      scope.isUnsubscribedFromEmails = () =>
        String(scope.userData?.subcribtion_flag) === UNSUBSCRIBED.value ||
        String(scope.userData?.is_subscribed) === UNSUBSCRIBED.value;

      scope.isUnsubscribedFromMessages = () =>
        String(scope.userData?.sms_marketing_subscription_flag) === UNSUBSCRIBED.value ||
        String(scope.userData?.is_subscribed_sms_marketing) === UNSUBSCRIBED.value;

      scope.isEmailPrefered = () =>
        scope.userData?.contact_preference === CONST_CONTACTS.PREFERENCE_TYPE.EMAIL.value &&
        !scope.isUnsubscribedFromEmails();

      scope.isPhonePrefered = () =>
        scope.userData?.contact_preference === CONST_CONTACTS.PREFERENCE_TYPE.PHONE.value;

      scope.isTextPrefered = () =>
        scope.userData?.contact_preference === CONST_CONTACTS.PREFERENCE_TYPE.TEXT_MESSAGE.value &&
        !scope.isUnsubscribedFromMessages();

      //show 'call' CTA if retailer enabled phone calls (RetailerHasPhoneCallEnabled = true)
      scope.showCallOption = () =>
        $rootScope.conf('RetailerHasPhoneCallEnabled') &&
        !scope.features.isPiiObfuscationEnabled();

      const goToCompose = (data) => {
        const search = { fromAction: 'true' };

        if (scope.isFromGroupTask) {
          search.taskType = TASK_TYPE.GROUP_TASK;
        }
        if (scope.taskId) {
          search.taskId = scope.taskId;
        }

        if (data) {
          return messageService.goToComposeMessage(data, search);
        }

        navService.goTo('store-messages/compose', search);
      };

      scope.goToComposeMessage = () => {
        if (scope.isUnsubscribedFromEmails()) {
          return false;
        }

        if (!scope.isFromCorpTask && !scope.isFromGroupTask && scope.hasEmail && scope.userData) {
          return goToCompose(scope.userData);
        }

        if (!scope.isAssetAvailable) {
          const options = {
            buttons: {
              confirm: 'OK',
              cancel: 'Cancel'
            }
          };
          return feedbackService.showPrompt(t('This Asset is not currently available. Continue without attaching this asset?'), options).then(() => {
            if (scope.isFromGroupTask) {
              removeGroupTaskAsset();
            } else {
              removeCorporateTaskAsset();
            }
            goToCompose();
          });
        }
        goToCompose();
      };

      const goToShare = () => navService.goTo('share', { fromAction: 'true', isFromGroupTask: scope.isFromGroupTask, taskId: scope.taskId});

      scope.goToShare = () => {
        if (!scope.isAssetAvailable) {
          const options = {
            buttons: {
              confirm: 'OK',
              cancel: 'Cancel'
            }
          };
          return feedbackService.showPrompt(t('This Asset is not currently available. Continue without attaching this asset?'), options).then(() => {
            if (scope.isFromGroupTask) {
              removeGroupTaskAsset();
            } else {
              removeCorporateTaskAsset();
            }
            goToShare();
          });
        }
        if (scope.taskShareDateTime) {
          return feedbackService.showPrompt(t("A 'Share' email originating from this Task has already been sent on _timestamp_. Are you sure you want to continue?", {
            timestamp: moment.utc(scope.taskShareDateTime).local().format('LLL')
          }),{
            buttons: {
              confirm: 'Yes',
              cancel: 'No'
            }
          })
            .then(() => {
              goToShare();
            });
        }
        goToShare();
      };

      const removeCorporateTaskAsset = () => {
        const corporateTaskData = localStorageService.get('sfCorporateTaskData');
        corporateTaskData.assets = [];
        localStorageService.set('sfCorporateTaskData', corporateTaskData);
      };
      const removeGroupTaskAsset = () => {
        const groupTaskData = localStorageService.get('sfGroupTaskData');
        groupTaskData.assets = [];
        localStorageService.set('sfGroupTaskData', groupTaskData);
      };

      // JIRA SF-29504 we dont prompt anymore to choose other number if contact has multiple
      scope.goToSendTextMessage = () => {
        if (scope.isUnsubscribedFromMessages()) {
          return false;
        }

        if (scope.isFromCorpTask || scope.isFromGroupTask) {
          return scope.altTextMessageAction();
        }

        const contact = scope.userData;

        const onSuccess = (choosenPhoneNumber) => {
          // tell text-messaging-compose controller which phone number
          // the user selected
          contact.choosenField = choosenPhoneNumber;

          // stash intent to run text-messaging-compose controller with
          // pre-populated data
          intentService.stash({
            data: {
              selectedContacts: [contact],
            },
            ...intentService.get()
          });

          const search = {};
          if (scope.taskId) {
            search.taskId = scope.taskId;
          }

          // route to new controller
          return navService.goTo('/text-messaging/compose', search);
        };

        return onSuccess(contact.phone);
      };

      scope.callPhoneNumber = () => {
        const contact = scope.userData;

        if (!window.plugins || !window.plugins.CallNumber) {
          return;
        }

        // Track the activity (call attempted)
        activityLogService.trackActivity({
          customer_id: contact.ID,
          user_id: $rootScope.currentUser.ID,
          type: 10, // Call attempted
          thread_id: null,
          preview: null,
          direction: 'outbond',
          status: 'read'
        });

        const onSuccess = () => {};
        const onError   = () => {
          return feedbackService.showError(t('Unable to call _number_ at the moment.', { number: contact.phone }));
        };
        return window.plugins.CallNumber.callNumber(onSuccess, onError, contact.phone, true);
      };

      /**
       * Store customer object and go to Task creation view
       */
      scope.goToTaskCreate = () => {
        let contact = scope.userData;

        // sfCurrentCustomer storage is used for contacts.js caching
        contact = !angular.equals({}, contact) ? contact : localStorageService.get('sfCurrentCustomer');

        let customer;

        if (contact) {
          customer = {
            ID:         contact.ID,
            name:       contact.name,
            email:      contact.email,
            phone:      contact.phone,
            last_name:  contact.last_name,
            first_name: contact.first_name
          };
        } else if (localStorageService.get('sfTaskData')) {
          customer = localStorageService.get('sfTaskData').customer;
        }

        // Object used to pre-populate customer informations
        localStorageService.set('sfTaskData', { customer: customer });

        navService.goTo('tasks/create');
      };
    }
  };
});
