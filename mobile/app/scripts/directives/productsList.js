angular.module('sfmobileApp').directive('productsList', (
  $rootScope,
  $location,
  feedbackService,
  localStorageService,
  i18nService,
  productsService,
  productService,
  configService,
  stringManipulationService,
  CONST_SHARE
) => {
  return {
    restrict: 'E',
    templateUrl: 'views/directives/products-list.html',
    scope: {
      type: '=',
      locale: '=',
      loading: '=',
      canEdit: '=',
      hideHandle: '=',
      hasComment: '=',
      goToComment: '&',
      hideFavorite: '=',
      hideViewLink: '=',
      productsData: '=',
      removeProduct: '&',
      preventSelect: '=',
      modifierClass: '@',
      selectedProducts: '=',
      toggleScrollable: '&',
      hideDetailsButton: '=',
      onProductDetailsClick: '&',
      showDetailsOnProductClick: '=',
      hideBottomDivider: '=',
      forceDetailsButton: '=',
      allowDuplicates: '='
    },
    link: (scope) => {
      scope.i18nOpts = { ns: 'share' };
      const t = i18nService.t(scope.i18nOpts);

      /**
       * Boolean for products brand
       */
      scope.productsHaveBrand = false;

      /**
       * Strip products with empty 'product_data'
       */
      // TODO: why we do this only when we have locales ?
      scope.getProductsFilter = (value) => {
        if (scope.locale) {
          return value.product_data !== undefined;
        }
        return true;
      };

      scope.getModifierClass = () =>
        scope.modifierClass || '';

      /**
       * Assets from Share/Library
       */
      scope.isAsset = () => {
        return scope.type === 'asset';
      };

      /**
       * Products from Share/Library
       */
      scope.isProduct = () => {
        return scope.type === 'product';
      };

      /**
       * Returns boolean to know if product is on sale
       */
      scope.isProductOnSale = productService.isProductOnSale;

      scope.isAssetDisabled = product => product.hasUnsupportedDestinationUrl;
      /**
       * Returns boolean to know if product is available
       */
      scope.isProductUnavailable = (product) => {
        const tempProduct = product.selectedVariant || product;

        return tempProduct.hasOwnProperty('available') && !parseInt(tempProduct.available);
      };

      const removeDuplicatesFromProductList = products =>
        products.filter((product, i, products) => products.findIndex(prod =>
          (scope.getProductSku(prod) === scope.getProductSku(product))) === i);

      //To prevent an empty page display,
      //if duplicates are not allowed, remove them from the displayed list
      scope.getProducts = products =>
        products?.length && !scope.allowDuplicates
          ? removeDuplicatesFromProductList(products)
          : products || [];

      /**
       * Returns boolean to know if product is selected
       */
      scope.isProductSelected = (product) => {
        if (scope.preventSelect) {
          return false;
        }

        const { findInProducts, containsDefaultVariantOfProduct } = productsService;

        return scope.isAsset()
          ? findInProducts(scope.selectedProducts, product)
          : findInProducts(scope.selectedProducts, product) || containsDefaultVariantOfProduct(scope.selectedProducts, product);
      };

      /**
       * Returns boolean to know if product is autoselected
       */
      scope.isProductAutoselected = productService.isAutoSelected;

      /**
       * Returns boolean to know if product is removed (For edit mode)
       */
      scope.isProductRemoved = (product) => {
        return product.from === 'removed';
      };

      /**
       * Toggle product state (scope.selectedProducts)
       */
      scope.toggleProduct = (product) => {
        if (scope.preventSelect) {
          return;
        }

        if (scope.isProductSelected(product)) {
          unselectProduct(product);
          return;
        }

        if (scope.isAsset()) {
          scope.selectedProducts.splice(0, scope.selectedProducts.length);
        }

        if (isSelectedProductsFull()) {
          const type = stringManipulationService.capitalize(scope.type);
          feedbackService.showError(t('You have reached the maximum number of _type_ you may select.', { type: t(type), count: scope.selectedProducts.length }));
          return;
        }

        addProduct(product);
      };

      /**
       * Handle product click depending on callee source
       */
      scope.handleProductClick = (product) => {
        if (scope.showDetailsOnProductClick) {
          scope.showProductDetails(product);
        } else {
          scope.toggleProduct(product);
        }
      };

      /**
       * Toggle product favorite state
       */
      scope.toggleFavorite = (product) => {
        product.favorite = !product.favorite;
        productsService.setFavoriteProduct($rootScope.currentUser.ID, product, product.favorite);
      };

      /**
       * Verify product count availability depending on the constant
       */
      const isSelectedProductsFull = () => {
        const currentCount = scope.selectedProducts.length;
        const type = scope.isAsset() ? 'asset' : 'product';
        let maxCount = localStorageService.get('sfMaxCountProducts') || CONST_SHARE['MAX_' + type.toUpperCase() + 'S'];

        return currentCount >= maxCount;
      };

      /**
       * Add product to selected array
       */
      function addProduct(product) {
        const {product_data} = product;
        // SF-24409 create a copy of the product to the original data
        // untouched
        product = angular.extend({}, product);
        if (product_data) {
          product.product_data = angular.extend({}, product_data);
        }

        if (productService.hasVariants(product)) {
          const variantId = productService.getVariantId(product);
          if (variantId) {
            product.variant_id = variantId;
            if (product_data) {
              product.product_data.variant_id = variantId;
            }
          }
        }

        scope.selectedProducts.push(product);
        saveProducts();
      }

      /**
       * Remove product from selected array
       */
      function unselectProduct(product) {
        productsService.removeFromProducts(scope.selectedProducts, product);
        saveProducts();
      }

      /**
       * Save products/asset data to local storage
       */
      function saveProducts() {
        if (scope.isAsset()) {
          return productsService.updateAssetStorage(scope.selectedProducts);
        }

        return productsService.updateProductStorage(scope.selectedProducts);
      }

      /**
       * Returns formatted product price
       */
      scope.getPrice = (product) => {
        var tempProduct = product.selectedVariant || product.product_data || product;
        var price = tempProduct.regularPrice || tempProduct.price;

        if (scope.isProductOnSale(tempProduct)) {
          price = tempProduct.salePrice || tempProduct.price_deal;
        }

        return $rootScope.utils.formatCurrency(price);
      };

      /**
       * Returns formatted original product price
       */
      scope.getOriginalPrice = (product) => {
        var price = product.regularPrice;

        if (product.selectedVariant) {
          price = parseInt(product.selectedVariant.price);
        }

        return $rootScope.utils.formatCurrency(price);
      };

      /**
       * Multilang helper for products informations
       */
      const getTranslation = (obj, field) => {
        return productService.getI18nField(obj, field, scope.locale);
      };

      /**
       * Get product brand (Normal products with brand only)
       */
      scope.getBrand = (product) => {
        var tempProduct = product.selectedVariant || product.product_data || product;

        return tempProduct.name2 || tempProduct.brand;
      };

      /**
       * Get product description
       */
      scope.getDescription = (product) => {
        if (scope.isAsset()) {
          return productService.getAssetLabel(product);
        }

        if (product.selectedVariant) {
          return getTranslation(product.selectedVariant, 'name');
        }

        if (scope.locale) {
          if (product.tmplDisplay) {
            return product.tmplDisplay.name;
          }

          // For Looks
          if (product.product_data) {
            return getTranslation(product.product_data, 'name');
          }
        }

        return product.subject || product.name;
      };

      scope.getShortDescription = (product) => {
        var tempProduct = product.selectedVariant || product.product_data || product;

        if (tempProduct.shortDescription) {
          return getTranslation(tempProduct, 'shortDescription');
        }
        return getTranslation(tempProduct, 'description');
      };

      /**
       * Get product comment (For all types)
       */
      scope.getComment = (product) => {
        if (product.from === 'autoselected') {
          return i18nService.t('Auto-Selected');
        }

        if (scope.locale) {
          if (product.tmplDisplay?.note) {
            return product.tmplDisplay.note;
          }

          // For Looks
          if (product.product_data) {
            return getTranslation(product, 'note');
          }
        }

        return product.note || product.comment;
      };

      /**
       * Get product image (For all types)
       */
      scope.getImage = (product) => {
        if (scope.isAsset()) {
          return productService.getAssetImage(product);
        }

        if (product.selectedVariant) {
          return product.selectedVariant.imageUrl ||
            product.selectedVariant.image_url ||
            getTranslation(product.selectedVariant, 'img250');
        }

        if (scope.locale) {
          if (product.tmplDisplay) {
            return productService.getImage(product.tmplDisplay);
          }

          // For Looks
          if (product.product_data) {
            return getTranslation(product.product_data, 'img250');
          }
        }

        return productService.getImage(product);
      };

      scope.getFullImage = product => productService.getFullImage(product);

      /**
       * Get url
       */
      scope.getUrl = (product) => {
        if (scope.isAsset()) {
          return productService.getAssetPermanentUrl(product);
        } else {
          return '';
        }
      };

      /**
       * Open new modal with product details
       */
      scope.openModal = (product) => {
        if (!configService.get('ProductsHasModal')) {
          return scope.viewProduct(product);
        }
        scope.toggleScrollable();
        scope.modalOpened = true;
        scope.$emit('productModalOpened', {
          image: scope.getImage(product),
          brand: scope.getBrand(product),
          description: scope.getDescription(product),
          shortDescription: scope.getShortDescription(product),
          price: scope.getPrice(product),
          originalPrice: scope.getOriginalPrice(product),
          isAsset: scope.isAsset(),
          isOnSale: scope.isProductOnSale(product),
          viewProduct: scope.viewProduct.bind(scope, product)
        });
      };

      /**
       * Close product modal
       */
      scope.$on('productModalClosed', () => {
        //SF-23629 if there are multiple productsList instances on a view,
        //multiple event listeners will exist, so we need to check this flag
        //here to make sure the correct instance is handling the event
        if (scope.modalOpened) {
          scope.toggleScrollable();
          scope.modalOpened = false;
        }
      });

      /**
       * Open new window with proper URL (For normal products & asset)
       */
      scope.viewProduct = (product) => {
        if (scope.locale) {
          if (product.tmplDisplay) {
            return $rootScope.utils.loadPreviewUrl(product.tmplDisplay.productUrl);
          }

          // For Looks
          if (product.product_data) {
            return $rootScope.utils.loadPreviewUrl(getTranslation(product.product_data, 'productUrl'));
          }
        }

        let url = product.assetUrl || product.productUrl || product.previewUrl || product.permanentUrl;

        if (url.indexOf('sf_rep') === -1) {
          const querySeparator = url.indexOf('?') > -1 ? '&' : '?';
          url += querySeparator + 'sf_rep=' + $rootScope.currentUser.nickname;
        }

        return $rootScope.utils.loadPreviewUrl(url);
      };

      function fixPositions(products) {
        return products.map((p, i) => {
          p.position = i + '';
          return p;
        });
      }

      /**
       * Reorder products from Sortable List directive
       */
      scope.reorderProduct = (startPos, newPos) => {
        const p = scope.productsData.splice(startPos, 1)[0];

        scope.productsData.splice(newPos, 0, p);
        scope.productsData = fixPositions(scope.productsData);
      };

      /**
       * Navigate to product details page
       */
      scope.showProductDetails = (product) => {
        const canTakeDefaultAction = scope.onProductDetailsClick({ product });

        if (canTakeDefaultAction === false) {
          return;
        }

        productService.setCurrentProduct(product);
        $location.path(`/share/product/${product.sku}`);
      };

      scope.showDetailsButton = product => (scope.forceDetailsButton || productService.hasVariants(product)) && !scope.hideDetailsButton;

      scope.getProductSku = (product) => {
        if (scope.isAsset()) {
          return productService.getAssetId(product);
        } else {
          return productService.getProductRetailerSku(product);
        }
      };

      scope.getFormattedStartDate = ({ startDate = '-' }) => {
        if (startDate === '-' || !startDate.includes('/')) { 
          return startDate;
        }
        let [day, month, year] = startDate.split('/');
        return `${month}/${day}/${year}`;
      };

      scope.getFormattedEndDate = ({ endDate = '-' }) => {
        if (endDate === '-' || !endDate.includes('/')) { 
          return endDate;
        }
        let [day, month, year] = endDate.split('/');
        return `${month}/${day}/${year}`;
      };

      scope.getTarget = ({ target_channel }) => target_channel
        ? scope.assetChannels.filter(asset => asset.identifier === target_channel)[0].label
        : '-';

      /**
      * Return true if this product (from algolia) has more than 1 variant
      *
      * @param product
      * @returns {boolean}
      */
      scope.hasMoreThanOneVariant = product =>
        productService.getVariantsCount(product) > 1;

      /**
       * Determines if current products list needs brand field
       */
      function lookForBrand(data) {
        for (let i = 0; i < data.length; i++) {
          const tempData = data[i].product_data || data[i];

          if (tempData.name2 && tempData.name2.trim()) {
            scope.productsHaveBrand = true;
            return;
          }
        }
      }

      scope.$watch('productsData', (data) => {
        if (data) {
          lookForBrand(data);
        }
      });

      if (scope.type === 'asset') {
        scope.assetChannels = configService.get('AssetTargetChannelLabels');
      }
    }
  };
});
