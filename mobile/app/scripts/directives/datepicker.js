'use strict';

// ## `datepicker` directive
// Usage:
//
//    <datepicker minDate="someDate" ng-model="dateModel"></datepicker>
//
// Where:
//
// - `someDate` is a date string in `YYYY-MM-DD` format;
// - `dateModel` is a model for saving the selected date.
angular.module('sfmobileApp').directive('datepicker', ($timeout, i18nService, feedbackService) => {
  return {
    // Restricting this directive to elements only
    restrict: 'E',
    // Directive will replace initial element with a template
    replace: true,
    require: 'ngModel',
    // Scope definition,
    // We are taking directive atributes and assigning to directive's scope
    scope: {
      minDate: '=mindate',
      maxDate: '=?maxdate',
      ngModel: '='
    },
    // Directive's template,
    // It's just a simple input date by default
    template: '<input min={{minDate}} ng-attr-max={{maxDate}} type="date" class="datepicker" />',
    // Link function,
    // We're passing `scope` and `elem` to link function
    // Since we bound all attributes to directive's scope we don't need to pass `attrs`.
    link: (scope, elem, attr, ngModelCtrl) => {
      // Angular 1.3+ insert a formater that force to set model to date object, otherwise throw exception. CPD-1077
      // Reset default angular formatters/parsers
      ngModelCtrl.$formatters.length = 0;
      ngModelCtrl.$parsers.length = 0;
      // Set default minDate to today
      // We need to parse the date to `YYYY-MM-DD` format to be able to compare dates
      var minDate = moment().format('YYYY-MM-DD');
      var maxDate = null;

      // ### `isValid` function
      // Returns true if date is valid or false if it's not
      function isValid(dateString) {
        var date = new Date(dateString);
        if (Object.prototype.toString.call(date) === '[object Date]') {
          // Ok, it's a date
          if (isNaN(date.getTime())) {
            // Date is not valid
            return false;
          } else {
            // Date is valid
            return true;
          }
        } else {
          // It's not a date at all
          return false;
        }
      }

      // ### `setMinDate` function
      // Updates the min date according to the element's `mindate` attribute
      // !TODO: Important, dates should be validated, right now there's no any kind of validation
      function setMinDate() {
        // If `minDate` is a valid date string
        if (!isValid(scope.minDate)) {
          // Reset `minDate` to current date
          scope.minDate = moment().format('YYYY-MM-DD');
        }
        // assign `minDate` attribute's value to local `minDate` variable
        minDate = scope.minDate;
      }

      function setMaxDate() {
        if (!isValid(scope.maxDate)) {
          scope.maxDate = null;
        }
        maxDate = scope.maxDate;
      }
      // Calculate `minDate` `maxDate` initialy
      setMinDate();
      setMaxDate();

      // Listen for blur event
      elem.on('blur', () => {
        // And alert user if the date is before `minDate`
        if (moment(elem.val()).unix() < moment(minDate).unix() && moment(elem.val()).unix() !== undefined) {
          const errorMsg = i18nService.t("You can't select a date before _date_", { date: minDate});
          feedbackService.showContent(errorMsg);
          elem.val('');
        }
        if (maxDate?.length) {
          if (moment(elem.val()).unix() > moment(maxDate).unix() && moment(elem.val()).unix() !== undefined) {
            const errorMsg = i18nService.t("You can't select a date after _date_", { date: maxDate });
            feedbackService.showContent(errorMsg);
            elem.val('');
          }
        }
        // Apply changes to the scope manually
        if (typeof(elem.val()) !== 'undefined') {
          scope.$apply(() => {
            scope.ngModel = elem.val();
          });
        }
      });

      // Watch `minDate` changes
      scope.$watch('minDate', (newValue) => {
        minDate = newValue;
      });

      // Check if `input[type=date]` is not supported
      // and show some JS datepicker
      const i = document.createElement('input');
      i.setAttribute('type', 'date');
      return i.type !== 'text';
    }
  };
});
