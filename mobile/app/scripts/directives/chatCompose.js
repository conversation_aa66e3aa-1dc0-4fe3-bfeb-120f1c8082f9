angular.module('sfmobileApp').directive('chatCompose', (
  $rootScope,
  $timeout,
  chatService,
  moderationService,
  loaderService,
  videoChatService,
  chatUtilsService,
  localStorageService,
  featuresEnabledService,
  i18nService,
  feedbackService,
  loggerService,
  CONST_CHAT_INPUT
) => {
  return {
    restrict: 'E',
    templateUrl: 'views/directives/chat-compose.html',
    scope: {
      isDisabled: '='
    },
    link: (scope) => {
      const t = i18nService.t({ ns: 'chat' });

      const init = () => {
        scope.chatMessage = localStorageService.get('sfChatMessage') || chatUtilsService.getChatMessage();
        localStorageService.remove('sfChatMessage');

        scope.elementsHeight = localStorageService.get('sfElementsHeight') ? Number(localStorageService.get('sfElementsHeight')) : CONST_CHAT_INPUT.LEVEL_0;
        localStorageService.remove('sfElementsHeight');

        chatUtilsService.retrieveProduct();
        chatUtilsService.retrieveAsset();

        updateCssData();
      };

      scope.showChatCompose = () => !loaderService.isLoading() && (!featuresEnabledService.hasVideoChat({ isVirtual: chatUtilsService.isVirtual() }) || !$rootScope.videoChat.visible);

      scope.toggleOverlay = ($event, bool) => {
        if (!videoChatService.checkingConditions()) {
          $event.stopPropagation && $event.stopPropagation();

          if (bool !== undefined) {
            scope.overlay = bool;
          } else {
            scope.overlay = !scope.overlay;
          }

          $rootScope.$broadcast('toggleChatQuickMenuOverlay', scope.overlay);
        }
      };

      const resetMessageAndCssData = () => {
        $timeout(() => {
          scope.chatMessage = '';
        });
        chatUtilsService.clearChatMessage();
        scope.elementsHeight = CONST_CHAT_INPUT.LEVEL_0;
        updateCssData();
      };

      const focusTextArea = () => {
        scope.$broadcast('focusTextArea');
      };

      scope.autoExpand = (e) => {
        const element = typeof e === 'object' ? e.target : document.getElementById(e);
        const scrollHeight = element.scrollHeight;
        let newHeight;

        // Erasing
        if (e.keyCode === 8) {
          // Last line
          if (scrollHeight < CONST_CHAT_INPUT.LEVEL_1) {
            newHeight = CONST_CHAT_INPUT.LEVEL_0;
          // 2 lines remaining
          } else if (scrollHeight < CONST_CHAT_INPUT.LEVEL_2) {
            newHeight = CONST_CHAT_INPUT.LEVEL_1;
          // 3 lines remaining
          } else {
            newHeight = CONST_CHAT_INPUT.LEVEL_2;
          }
        // Typing
        } else {
          // Going to 3 lines
          if (scrollHeight > CONST_CHAT_INPUT.LEVEL_1) {
            newHeight = CONST_CHAT_INPUT.LEVEL_2;
          // Going to 2 lines
          } else if (scrollHeight > CONST_CHAT_INPUT.LEVEL_0) {
            newHeight = CONST_CHAT_INPUT.LEVEL_1;
          // Staying at 1 line
          } else {
            newHeight = CONST_CHAT_INPUT.LEVEL_0;
          }
        }

        scope.elementsHeight = newHeight;
        updateCssData();
      };

      const updateCssData = () => {
        const wrapperHeightTotal   = scope.elementsHeight + 2;  // Textarea wrapper height plus borders (.chat-compose__wrapper)
        const quickMenuBottomTotal = scope.elementsHeight + 23; // Total .chat-room div height (.chat-quick-menu__overlay)
        const chatRoomHeightTotal  = quickMenuBottomTotal + 95;  // Total .chat-room div height plus .menu-header and .chat-header (.chat-room.scroller)

        scope.cssData = {
          wrapperHeight: {
            height: `${wrapperHeightTotal}px`
          },
          quickMenuBottom: {
            bottom: `${quickMenuBottomTotal}px`
          },
          chatRoomHeight: {
            height: `calc(100% - ${chatRoomHeightTotal}px - env(safe-area-inset-bottom))`
          }
        };
        $rootScope.$broadcast('updateCssData', scope.cssData);
      };

      const adjustInputHeightForQuickResponse = () => {
        if (window.innerWidth < 376) {
          /* Using third height level for smaller devices */
          scope.elementsHeight = CONST_CHAT_INPUT.LEVEL_2;
        } else if (window.innerWidth < 415) {
          /* Using second height level for medium devices */
          scope.elementsHeight = CONST_CHAT_INPUT.LEVEL_1;
        }
        updateCssData();
        chatUtilsService.scrollChat(100);
      };

      scope.handleTyping = () => {
        clearTimeout(scope.typingTimer);

        if (!scope.chatMessage?.length) {
          chatUtilsService.toggleRepTyping(false);
          return;
        }

        chatUtilsService.toggleRepTyping(true);

        scope.typingTimer = setTimeout(() => {
          chatUtilsService.toggleRepTyping(false);
        }, 3000);
      };

      scope.sendMessage = () => {
        if (!scope.chatMessage.length || scope.sendIsInProgress) {
          return;
        }

        scope.chatMessage = chatUtilsService.isQuickResponse(scope.chatMessage)
          ? scope.chatMessage
          : $rootScope.stripTags(scope.chatMessage);

        const messageData = createMesssgeData();

        if (featuresEnabledService.isModerationTextEnabled()) {
          scope.sendIsInProgress = true;

          chatUtilsService.toggleRepTyping(true);
          // to keep all the messages in order, all messages need to be added to chatMessagesQueue
          moderationService.addMessageToChatMessagesQueue(messageData);
        } else {
          addMessageToFirebase(messageData);
        }
      };

      const createMesssgeData = () => chatUtilsService.createMessage({
        type: 'rep',
        message: scope.chatMessage,
        allowUnsafeSymbols: chatUtilsService.isQuickResponse(scope.chatMessage),
      });

      const addMessageToFirebase = (messageObj) => {
        const messageData = messageObj || createMesssgeData();
        const { message } = messageData;

        if (chatService.isFirebaseConnected) {
          $rootScope.firebaseRefs.messages.ref
            .$add(messageData)
            .then(() => {
              chatUtilsService.toggleRepTyping(false);
              resetMessageAndCssData();
              focusTextArea();
              chatUtilsService.trackChat(message);
              scope.sendIsInProgress = false;
            })
            .catch((error) => {
              scope.sendIsInProgress = false;
              feedbackService.showError(t('An error occurred, the message could not be sent. Please try again.'));
              loggerService.logError('[CHAT] Failed to add message.', {
                timestamp: Date.now(),
                message: scope.chatMessage,
                error
              });
            });
        } else {
          feedbackService.showError(t('An error occurred, the message could not be sent. Please try again.'));
          scope.sendIsInProgress = false;
          loggerService.logError('[CHAT] Firebase lost connection.', {
            timestamp: Date.now(),
            message: scope.chatMessage,
          });
        }
      };

      //After the message was processed by the queue we can push it to FB
      scope.$on('repMessageIsCleared', (e, messageData = {}) => {
        addMessageToFirebase(messageData);
        chatUtilsService.toggleRepTyping(false);
      });

      scope.$on('toggleOverlay', (e, bool) => {
        scope.toggleOverlay(e, bool);
      });

      scope.$on('persistChatMessage', () => {
        localStorageService.set('sfChatMessage', scope.chatMessage);
        chatUtilsService.setChatMessage(scope.chatMessage);
      });

      scope.$on('adjustInputHeightForQuickResponse', () => {
        adjustInputHeightForQuickResponse();
      });

      scope.$on('saveElementsHeight', () => {
        localStorageService.set('sfElementsHeight', scope.elementsHeight);
      });

      scope.$on('resetMessageAndCssData', () => {
        resetMessageAndCssData();
      });

      scope.$on('setChatMessage', (e, message) => {
        scope.chatMessage = message;
      });

      scope.$watch('chatMessage', chatUtilsService.setChatMessage);

      init();
    }
  };
});
