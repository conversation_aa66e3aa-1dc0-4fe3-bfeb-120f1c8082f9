angular.module('sfmobileApp').factory('groupTaskService',  (
  $http,
  jsonapi,
  apiService,
  apiCache,
  configService,
  productsService,
  localStorageService,
  featuresEnabledService,
  CONST_PAGINATION,
  API_CACHE_KEYS,
) => {

  const { GROUP_TASK_LIST_DELETE_KEY, GROUP_TASK_ITEM_DELETE_KEY_PREFIX } = API_CACHE_KEYS; // anything which would affect the listing of tasks will be invaldiated at the same time


  const getQueryCutOffDateField = () => {
    return 'reminder_date';
  };

  const getQueryCutOffUtcDate = () => {
    let configCutoffDaysBack = parseInt(configService.get('TaskQueryCutOffDaysBack'));
    if (configCutoffDaysBack > 0) {
      return moment().utc().subtract(configCutoffDaysBack, 'd').format('YYYY-MM-DD');
    }

    return null;
  };


  /**
 * API Call to get Group Tasks data for listing view
 */
  const fetchGroupTaskList = ({
    page = 0,
    pageSize = CONST_PAGINATION.PAGE_SIZE,
    query
  }) => {
    query = query || 'filter[status]=unresolved&sort=reminder_date';
    const params = {
      [query]: null,
      page: {
        number: page,
        size: pageSize
      }
    };

    const endpoint = 'group-tasks';
    const dataKey = params;
    const deleteKeys = [GROUP_TASK_LIST_DELETE_KEY];
    const promiseToGetData = apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
      return jsonapi.get(endpoint, params).then((tasks) => {
        const tasksList = tasks.map(task =>
          Object.assign(jsonapi.getEntityData(task))
        );
        return {
          tasksList
        };
      });
    });

    return promiseToGetData;
  };

  const getRelatedTaskProducts = (task) => {
    return (task.getRelated('product') || task.getRelated('product_variant') || [])
      .map((product) => {
        const productData = jsonapi.getEntityData(product);
        return productData;
      })
      .filter(product => product);
  };
  /**
   * Helper function to extract group Task Assets
   */
  const getRelatedTaskAssets = task =>
    (task.getRelated('asset') || [])
      .map((item) => {
        const asset = productsService.normalizeAssets(item)[0];
        if (!asset) {
          return null;
        }
        return asset;
      })
      .filter(asset => asset);

  const getRelatedTaskActivities = task =>
    (task.getRelated('group-task-activity') || [])
      .map((item) => {
        const activity = jsonapi.getEntityData(item);
        if (!activity) {
          return null;
        }
        return activity;
      })
      .filter(activity => activity);

  const processTaskData = task => Object.assign(jsonapi.getEntityData(task), {
    assets: getRelatedTaskAssets(task),
    products: getRelatedTaskProducts(task),
    taskActivity: getRelatedTaskActivities(task),
  });

  const getGroupTaskDetails = (id) => {
    const params = {
      include: [
        featuresEnabledService.isProductVariantsEnabled() ? 'product_variant': 'product' ,
        'asset',
        'group-task-activity'
      ]
    };

    const endpoint = `group-tasks/${id}`;
    const dataKey = params;
    const deleteKeys = [`${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${id}`];
    const promiseToGetData = apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
      return jsonapi.get(endpoint, params).then(processTaskData);
    });

    return promiseToGetData;
  };

  const storeGroupTaskData = data =>
    localStorageService.set('sfGroupTaskData', data);

  const getGroupTaskData = () => {
    const groupTaskData = localStorageService.get('sfGroupTaskData');
    deleteGroupTaskData();
    return groupTaskData;
  };

  const deleteGroupTaskData = () =>
    localStorageService.remove('sfGroupTaskData');

  const updateGroupTask = ({ id, status, details, user_id }) => {
    apiCache.triggerDeleteKeys([GROUP_TASK_LIST_DELETE_KEY, `${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${id}`]);

    const payload = {
      'data': {
        'type': 'group-task',
        id,
        'attributes': {
          status
        },
        'relationships': {
          'group-task-activity': {
            'data':[{
              'type': 'group-task-activity',
              'lid': '1'
            }]
          }
        }
      },
      'included': [{
        'type': 'group-task-activity',
        'id': null,
        'lid': '1',
        'attributes': {
          user_id,  // The logged in rep id
          'action': status === 'dismissed' ? 'DISMISS' : 'RESOLVE', // TODO: move to enums
          details
        }
      }]
    };

    return jsonapi
      .post(jsonapi.getUrl(`group-tasks/${id}`), payload)
      .then(processTaskData);
  };

  const updateGroupTasks = ({ ids, status, user_id }) => {
    apiCache.triggerDeleteKeys([GROUP_TASK_LIST_DELETE_KEY, ...(ids.map(id => `${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${id}`))]);
    const payload = {
      'data': {
        'type': 'batch-update',
        'attributes': {
          ids,
          'columns': {
            status
          }
        },
        'relationships': {
          'group-task-activity': {
            'data': [{
              'type': 'group-task-activity',
              'lid': '1'
            }]
          }
        }
      },
      'included': [{
        'type': 'group-task-activity',
        'id': null,
        'lid': '1',
        'attributes': {
          user_id,
          'action': status === 'dismissed' ? 'DISMISS' : 'RESOLVE',
          'details': ''
        }
      }]
    };

    return jsonapi
      .post(jsonapi.getUrl('group-tasks/batch-update'), payload);
  };

  const getGroupTaskActivityLog = (group_task_id) => {
    const params = {
      filter: {
        group_task_id
      },
      include: ['group-task-activity-product','group-task-activity-asset'],
      page: {
        size: 200,
      },
    };

    const endpoint = 'group-task-activities';
    const dataKey = params;
    const deleteKeys = [`${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${group_task_id}`];
    const promiseToGetData = apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
      return jsonapi.get(jsonapi.getUrl(endpoint), params)
        .then((response) => {
          return response.map((activity) => {
            return Object.assign(jsonapi.getEntityData(activity), {
              assets: (activity.getRelated('group-task-activity-asset') || []).map(jsonapi.getEntityData),
              products: (activity.getRelated('group-task-activity-product') || []).map(jsonapi.getEntityData)
            });
          });
        });
    });

    return promiseToGetData;
  };

  const getFiltersAndCountsGroupedTasks = (
    storeIds = null,
    repIds = null,
    cutOffDate = null,
  ) => {

    let options = { params: {} };

    if (repIds && repIds.length > 0) {
      options.params['filter[preferred_user_ids]'] = repIds.join(',');
    } else if (storeIds) {
      options.params['filter[store_ids]'] = storeIds.join(',');
    }

    if (cutOffDate !== null) {
      const taskCutOffDateField = getQueryCutOffDateField();
      options.params[`filter[${taskCutOffDateField}][gte]`] = cutOffDate;
    }

    const endpoint = 'group-tasks/filters';
    const dataKey = options;
    const deleteKeys = [GROUP_TASK_LIST_DELETE_KEY];
    const promiseToGetData = apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
      return $http.get(apiService.getApiUrlV2(endpoint), options)
        .then(({ data: { data } }) => {
          const groupFilters = data.map((filter) => {
            const filterData = filter.attributes;
            filterData.type = filterData.filterType;
            return filterData;
          });
          return groupFilters;
        });
    });

    return promiseToGetData;
  };

  return {
    getQueryCutOffDateField,
    getQueryCutOffUtcDate,
    fetchGroupTaskList,
    getGroupTaskDetails,
    storeGroupTaskData,
    getGroupTaskData,
    deleteGroupTaskData,
    updateGroupTask,
    updateGroupTasks,
    getGroupTaskActivityLog,
    getFiltersAndCountsGroupedTasks
  };
});
