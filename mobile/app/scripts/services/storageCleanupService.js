angular.module('sfmobileApp').factory('storageCleanupService', (
  localStorageService,
  CONST_ADDRESS_BOOK,
  CONST_CONTACTS,
) => {
  // Object containing groups of localStorageService keys to reset
  const localStorageServiceKeysToReset = {
    // Keys to reset when visiting the dashboard
    dashboard: [
      // Message Compose
      'sfCompose',
      'sfComposeRecipients',
      'sfComposeTo',
      'sfContactsSelected',
      'sfContactsSelectedThreaded',
      'sfEmailGroupedProductsToggleStatus',

      // Share
      'sfShareObject',
      'sfFromEventId',
      'sfShareUpdateState',
      'sfShareFilterOptions',
      'sfShareCountQuery',
      'sfShareGroupedProductsToggleStatus',
      'shareWasSent',

      // Tasks
      'sfCorporateTaskData',
      'sfGroupTaskData',
      'sfTaskViewParams',

      // Asset
      'sfAsset',
      'sfAssetProps',
      'sfAssetPage',

      // Product
      'sfProduct',
      'sfProductProps',
      'sfProductPage',
      'sfProductList',
      'sfProductQueryString',
      'sfProductDepartments',
      'sfProductSortOrder',
      'sfMaxCountProducts',
      'sfFromContactsProducts',

      // Photo
      'sfPhotosList',
      'sfRepUploadedPhoto',

      // SMS
      'textMessageWasSent',

      // Lookbook
      'sfLookbookViewParams',
      'sfLookViewParams',

      // Contacts / Customers
      CONST_ADDRESS_BOOK.LS_KEY_CONTACTS_FILTERS,
      CONST_ADDRESS_BOOK.LS_KEY_CUSTOMERS_FILTERS,
      CONST_CONTACTS.LS_KEY_CURRENT_SEARCH_MODE,
      CONST_CONTACTS.LS_KEY_SEARCH_CUSTOMER_DATA,
      CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER,
      CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER_V2,
      CONST_CONTACTS.LS_KEY_CONTACT_STATS_DATA,
      CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA,
      CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH,

      // Misc
      'sfCurrentPage',
      'sfBrowseMode',
      'sfSecondSelectAfterBrowse',
      'sfAttachmentLang',
      'sfAvailableFacets',
      'textThreadId',
    ],
    backoffice: [
      // user/login
      'sfAccessToken',
      'currentUser',
      'sfUser',
      'sfUserAlias',
      'sfUserLogin'
    ]
  };

  /**
   * Reset a group of LocalStorageService values
   * Ex: in the event the app was closed and localstorage data remains, clean up when visiting dashboard
   * @param {string} groupKey indicates which group of keys to reset
   * @param {string} type indicates which storage remove the item - sessionStorage CONST_STORAGE.TYPE.SESSION or  localStorage CONST_STORAGE.TYPE.LOCAL
   */
  const resetLocalStorageServiceValues = (groupKey, type) => {
    if (localStorageServiceKeysToReset[groupKey]) {
      localStorageServiceKeysToReset[groupKey].forEach(key => type !== undefined ? localStorageService.remove(key, type) : localStorageService.remove(key));
    }
  };

  return {
    resetLocalStorageServiceValues,
  };
});
