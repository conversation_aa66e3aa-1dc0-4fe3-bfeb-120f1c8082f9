angular
  .module('sfmobileApp')
  .factory('loginService', function (
    $window,
    $rootScope,
    apiService,
    oauthService,
    authService,
    deeplinkService,
    localStorageService,
    secureStorageService,
    intentService,
    feedbackService,
    navService,
    storageService,
    loggerService,
    userService,
    i18nService,
    featuresEnabledService,
    permissionService,
    $http,
    CONST_FEEDBACK,
    CONST_HTTP,
    CONST_OAUTH,
    CONST_SF_APP,
    CONST_STORAGE,
    CONST_ONBOARDING
  ) {
    const t = i18nService.t({ ns: 'auth' });

    const REDIRECT = { status: 'REDIRECT' };

    const handleLoginException = (e) => {
      switch (e.status) {
      case REDIRECT.status:
        // DO NOT DISPLAY ANY ERRORS
        // PROMISE REJECTED WITH REDIRECT TO ONBORDING PROCESS OR OTA PAGE
        break;
      case CONST_HTTP.CODE_UNAUTHORIZED:
        feedbackService.showError(t(CONST_FEEDBACK.WRONG_CREDENTIALS));
        break;
      case CONST_HTTP.CODE_FORBIDDEN:
        feedbackService.showError(t(CONST_FEEDBACK.LOCKED_OUT));
        break;
      default:
        feedbackService.showError(t(CONST_FEEDBACK.SOMETHING_WRONG));
      }
    };

    const continueOnboarding = ({ onboardingToken, username, password }) => {
      const retailerId = $rootScope.conf('RetailerId');
      if (username !== null && password !== null) {
        $window.setUserFeedOnboardingMode(username, password);
      }
      deeplinkService.deeplink.handleOpenURL('rep-onboarding/create-user?token=' + onboardingToken + '&retailerId=' + retailerId, true);
    };
    
    const handleTokenLogin = (token) => {
      return new Promise((resolve, reject) => {
        $rootScope
          .setAccessToken(token)
          .then(() => {
            $rootScope.setAuthHeaderWithAccessToken(token);
            userService
              .getCurrentUser()
              .then((data) => {
                storageService.removeItem('onboarding-username');
                storageService.removeItem('onboarding-password');
        
                if (data.rep_onboarding && data.rep_onboarding.onboarding_completed === '0') {
                  reject({
                    onboardingToken: data.rep_onboarding.token,
                    isUserFeed: data.rep_onboarding.creation_source === CONST_ONBOARDING.USER_FEED
                  });
                  return;
                }
        
                // Login success callback
                if (typeof data.ID !== 'undefined') {
                  //remove all onboarding tokens from secure storage after successful login
                  secureStorageService.removeItem('ls.sfOnboardingAccessToken');
                  secureStorageService.removeItem('ls.sfOnboardingAuthentificationToken');

                  $rootScope.resetUser(data);
                  $rootScope
                    .init()
                    .then(async () => {
                      authService.update();
                      localStorageService.set('sfType', data.isStoreUser ? 'store' : 'rep');
                      localStorageService.set('sfStoreUserId', data.store.store_user_id);
                      // Decide wither to redirect to app or backoffice
                      await redirectOnUserLogin();                      
                      resolve(data);
                    });
                  
                } else {
                  reject({ status: CONST_HTTP.CODE_UNAUTHORIZED });
                }
              })
              .catch((e) => {
                loggerService.logError('[login.js] could not fetch current user', e);
                reject(e);
              });      
          });
      });   
    };

    /**
     * Redirect on User Login
     * Check the user group and selling mode to determine the correct action
     * wither to redirect to app or backoffice
     * @returns {Promise}
     * @resolves {undefined}
     */
    const redirectOnUserLogin = async () => {
      if (
        window.sfApp === CONST_SF_APP.DESKTOP 
        && featuresEnabledService.isBackofficePrimaryLoginThroughAppEnabled()
      ) {
        if (permissionService.isStoreManager() || permissionService.isManager() || permissionService.isCorpAdmin()) {
          // groups 2, 3, 4
          if (featuresEnabledService.isSellingMode()) {
            await redirectToBackofficeConditionallyElseDashboard();
          } else {
            await loginAndRedirectToBackoffice();
          }
        } else if (permissionService.isSFAdmin()) {
          // group 5
          await redirectToBackofficeConditionallyElseDashboard();
        } else {
          // group 1 or unrecognized group
          redirectToAppDashboard();
        }
      } else {
        // Mobile app always redirects as normal
        redirectToAppDashboard();
      }

      // Clear the stored redirect url for backoffice
      clearBackofficeRedirectUrl();
    };

    /**
     * Redirect to backoffce conditionally
     * If redirect url is set and is not the backoffice landing page
     * go to that url else load app dashboard
     * @returns {Promise}
     * @resolves {undefined}
     */
    const redirectToBackofficeConditionallyElseDashboard = async () => {
      const redirectTo = getBackofficeLoginRedirectUrl();
      if (redirectTo && shouldFollowBackofficeRedirectUrl(redirectTo)) {
        await loginAndRedirectToBackoffice();
      } else {
        redirectToAppDashboard();
      }
    };

    /**
     * Should redirect to backoffice
     * allow redirect to backoffice if the path is not '/backoffice'
     * will allow any other page besides the backoffice landing page
     * @param {string|null} redirectTo 
     * @returns {boolean}
     */
    const shouldFollowBackofficeRedirectUrl = (redirectTo) => {
      try {
        const parsedUrl = new URL(redirectTo);
        let path = parsedUrl.pathname;
    
        // Remove trailing slash if any
        if (path.length > 1 && path.endsWith('/')) {
          path = path.slice(0, -1);
        }
        
        // Dont allow direct redirect to backoffice landing page
        if (path === '/backoffice') {
          return false;
        }
        
        // Allow any other redirect url
        return true;
      } catch (error) {
        console.error('Could not parse url in shouldFollowBackofficeRedirectUrl', error);
      }

      return false;
    };

    /**
     * Redirect to dashboard or deeplink url
     * Continues with regular login
     */
    const redirectToAppDashboard = () => {
      // will trigger to show the prompt message when we redirected to dashboard
      $rootScope.showAvailableForChatPrompt = true;
                    
      // Resolve login promise and handle the redirect
      // Redirects :
      //  - No deeplink clicked => Dashboard
      //  - Deeplink clicked => deeplink handle url
      deeplinkService.promises.login.resolve();
    };

    /**
     * Login and redirect to backoffice using access token
     * @param {string} url - override redirect url
     * @returns {Promise}
     * @resolves {undefined}
     */
    const loginAndRedirectToBackoffice = async (url = null) => {
      const bearer = await $rootScope.getAccessTokenFromStorage();
      const accessToken = (bearer || '').slice(7); // remove 'Bearer ' prefix

      // set variable to prevent confirmation of leaving page
      window.isBackofficeLoginRedirecting = true;

      // Create a hidden container div
      var container = document.createElement('div');
      container.style.display = 'none';

      const baseUrl               = $rootScope.conf('BaseUrl');
      const redirectToDefaultUrl  = `${baseUrl}/backoffice`;
      const backofficeLoginUrl    = `${baseUrl}/login`;
      const redirectToUrl         = url || getBackofficeLoginRedirectUrl() || redirectToDefaultUrl;
    
      // Append the HTML form to the container
      container.innerHTML = `
        <form action="${backofficeLoginUrl}" method="post" id="backofficeLoginForm">
          <input type="text" name="action" value="token_login">
          <input type="text" name="token" value="">
          <input type="hidden" name="redirect_to" value="${redirectToUrl}">
        </form>
      `;
    
      // Append the container to the document body
      document.body.appendChild(container);
      
      // Populate the token field and submit
      const form = document.getElementById('backofficeLoginForm');
      form.querySelector('input[name="token"]').value = accessToken;
      form.submit();
    };

    /**
     * Logout of backoffice
     */
    const logoutOfBackoffice = () => 
      $http
        .get($rootScope.conf('BaseUrl') + '/login?action=force_logout')
        .catch(e => console.log('backoffice logout failed', e));

    /**
     * Set or clear redirect url when entering the login page
     */
    const setOrClearBackofficeRedirectUrl = () => {
      let redirectTo = null;
      try {
        const parsedUrl = new URL(window.location.href);
        redirectTo = parsedUrl.searchParams.get('redirect_to');
      } catch (error) {
        console.error('Could not parse url in setOrClearBackofficeRedirectUrl', error);
      }
      if (redirectTo) {
        localStorageService.set(CONST_STORAGE.BO_REDIRECT_TO, redirectTo);
      } else {
        // incase we visit a page with redirect url
        // but dont login, and then later login without the redirect param
        // clear the redirect url
        clearBackofficeRedirectUrl();
      }
    };

    /**
     * Clear the stored redirect url
     */
    const clearBackofficeRedirectUrl = () => localStorageService.remove(CONST_STORAGE.BO_REDIRECT_TO);

    /**
     * Get login redirect url
     * @returns {string|null} The redirect url to use after login
     */
    const getBackofficeLoginRedirectUrl = () => localStorageService.get(CONST_STORAGE.BO_REDIRECT_TO);

    return {
      REDIRECT,
      handleTokenLogin,
      handleLoginException,
      continueOnboarding,
      setOrClearBackofficeRedirectUrl,
      loginAndRedirectToBackoffice,
      logoutOfBackoffice,
      authentificate: ({
        username,
        password
      }) => {
        return new Promise((resolve, reject) => {
          apiService
            .getAccessToken(username, password)
            .then(
              (response) => {
                const { token, onboardingToken } = response.data.data;
                if (token) {
                  handleTokenLogin(token)
                    .then(resolve)
                    .catch((e) => {
                      const { onboardingToken, isUserFeed } = e;
                      if (onboardingToken) {
                        continueOnboarding({
                          onboardingToken,
                          username: isUserFeed ? username : null,
                          password: isUserFeed ? password : null
                        });
                        reject(REDIRECT);
                      } else {
                        reject(e);
                      }
                    });
                } else if (onboardingToken) {
                  continueOnboarding({ onboardingToken, username, password });
                  reject(REDIRECT);
                }
              },
              reject
            );    
        });
      },
      ssoAuthentificate: () => {
        return new Promise((resolve, reject) => {
          let oauthData = localStorageService.get(CONST_OAUTH.LS_KEY_ONBOARDING_OAUTH_DATA);
          let oauthDataPromise;
          if (oauthData) {
            localStorageService.remove(CONST_OAUTH.LS_KEY_ONBOARDING_OAUTH_DATA);
            delete $rootScope.onboardingTokenVerified; //we need to delete this one after onboarding or we will try to load secure URL
            oauthDataPromise = Promise.resolve(oauthData);
          } else {
            const { code, state } = intentService.pop();
            oauthDataPromise = oauthService.getAccessToken(code, state);
          }
          oauthDataPromise
            .then((oauth) => {
              apiService
                .getAccessTokenWithOAuth(oauth)
                .then((response) => {
                  const { token, onboardingToken } = response.data.data;
                  if (token) {
                    handleTokenLogin(token)
                      .then(resolve)
                      .catch((e) => {
                        const { onboardingToken } = e;
                        if (onboardingToken) {
                          continueOnboarding({ onboardingToken, username: '', password: '' });
                          reject(REDIRECT);
                        } else {
                          reject(e);
                        }
                      });
                  }
                  if (onboardingToken) {
                    localStorageService.set(CONST_OAUTH.LS_KEY_ONBOARDING_OAUTH_DATA, oauth);
                    // we need to set feed mode to CONST_ONBOARDING.USER_FEED for SSO
                    // that's why we pass empty username & password
                    continueOnboarding({ onboardingToken, username: '', password: '' });
                    reject(REDIRECT);
                  }
                  // TODO: do we handle the case when we do not receive one or another?
                })
                .catch(reject);
            })
            .catch(reject);
        });
      },
      mfaAuthentificate: ({
        username,
        password,
        deviceId
      }) => {
        return new Promise((resolve, reject) => {
          apiService
            .getMFAAccessToken(
              username,
              password,
              deviceId,
              'mobile'
            )
            .then(
              (response) => {
                const { mfa, token } = response.data.data;
                if (mfa) {
                  // If MFA is required go to MFA form
                  intentService.stash({
                    mfaToken: mfa.token,
                    username,
                    password
                  });
                  navService.goTo('login/mfa');
                  reject(REDIRECT);
                } else if (token) {
                  // If bearer token is returned, we can proceed with login
                  handleTokenLogin(token)
                    .then(resolve)
                    .catch((e) => {
                      const { onboardingToken, isUserFeed } = e;
                      if (onboardingToken) {
                        continueOnboarding({
                          onboardingToken,
                          username: isUserFeed ? username : null,
                          password: isUserFeed ? password : null
                        });
                        reject(REDIRECT);
                      } else {
                        reject(e);
                      }
                    });
                }
              },
              reject
            );
        });
      },
      isAuthenticated: () => {
        return new Promise((resolve, reject) => {
          $rootScope
            .getAccessTokenFromStorage()
            .then((token) => {
              // if we load app and we have token in storage 
              if (token) {
                $rootScope.accessToken = token;
                apiService.setAuthorizationHeader(token);
              }
              // check if we have $rootScope.accessToken && retailer configId
              if ($rootScope.verifyLogin()) {
                $rootScope.init().then(() => {
                  // auth can be injected as dependency
                  // into service or controller and currentUser will be availabel
                  // we can add any other required info here
                  resolve({
                    currentUser: $rootScope.currentUser
                  });
                });
              } else {
                reject('401');
              }
            });
        });
      },
    };
  });
