angular.module('sfmobileApp').factory('tasksService', ($resource, $http, configService, apiService, jsonapi, apiCache, CONST_PAGINATION, API_CACHE_KEYS) => {
  const filterKey = {
    DISMISSED: 'dismissed',
    DUE: 'due',
    OVERDUE: 'overdue',
    RESOLVED: 'resolved',
    UNRESOLVED: 'unresolved',
    UPCOMING: 'upcoming',
  };

  const filterType = {
    STATUS: 'status',
    CUSTOM: 'custom',
  };

  // anything which would affect the listing of tasks will be invalidated at the same time
  const { TASK_LIST_DELETE_KEY, TASK_ITEM_DELETE_KEY_PREFIX, TASK_CATEGORY_LIST_DELETE_KEY } = API_CACHE_KEYS;

  return {

    getQueryCutOffDateField() {
      return 'reminder_date';
    },

    getQueryCutOffUtcDate() {
      let configCutoffDaysBack = parseInt(configService.get('TaskQueryCutOffDaysBack'));
      if (configCutoffDaysBack > 0) {
        return moment().utc().subtract(configCutoffDaysBack, 'd').format('YYYY-MM-DD');
      }

      return null;
    },

    /**
     * API Call to get categories data for create/edit dropdown
     */
    getCategories(params) {
      const endpoint = 'task-categories';
      const dataKey = params;
      const deleteKeys = [TASK_CATEGORY_LIST_DELETE_KEY];
      return apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        const TaskCategories = $resource(apiService.getApiUrl(endpoint));
        return TaskCategories.get(params).$promise;
      });
    },

    /**
     * API Call to get filters and counts data for listing dropdown/dashboard reminders
     */
    getFiltersAndCounts(params, config = {}) {
      const endpoint = 'tasks/user/:id/filters?:filter';
      const dataKey = params;
      const deleteKeys = [TASK_LIST_DELETE_KEY];
      return apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        const TasksFilters = $resource(apiService.getApiUrl(endpoint), {} , { get: config });
        return TasksFilters.get(params).$promise;
      });
    },

    /**
     *  API Call to get due count
     */
    getDueCount(params) {
      const endpoint = 'tasks/user/:id/filters?fields=count_due';
      const dataKey = params;
      const deleteKeys = [TASK_LIST_DELETE_KEY];
      return apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        const TasksDueCount = $resource(apiService.getApiUrl(endpoint));
        return TasksDueCount.get(params).$promise;
      });
    },

    /**
     *  API Call to get unresolved count
     */
    getUnresolvedCount(params) {
      const endpoint = 'tasks/user/:id/filters?fields=count_unresolved';
      const dataKey = params;
      const deleteKeys = [TASK_LIST_DELETE_KEY];
      return apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        const TasksUnresolvedCount = $resource(apiService.getApiUrl(endpoint));
        return TasksUnresolvedCount.get(params).$promise;
      });
    },

    /**
     * API Call to get Tasks data for listing view
     */
    getListing(params) {
      const endpoint = 'tasks/user/:id?fields=task_category,customer&:filter';
      const dataKey = params;
      const deleteKeys = [TASK_LIST_DELETE_KEY];
      return apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        const TasksList = $resource(apiService.getApiUrl(endpoint));
        return TasksList.get(params).$promise;
      });
    },

    /**
     * API Call to get single Task data
     */
    getSingleTask(params) {
      const endpoint = 'task/:taskId?fields=is_due,task_category,customer';
      const dataKey = params;
      const deleteKeys = [`${TASK_ITEM_DELETE_KEY_PREFIX}${params.taskId}`];
      return apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        const Task = $resource(apiService.getApiUrl(endpoint));
        return Task.get(params).$promise;
      });
    },

    /**
     * API Call to create a task
     */
    createTask(params) {
      apiCache.triggerDeleteKeys([TASK_LIST_DELETE_KEY]);

      const Task = $resource(apiService.getApiUrl('task'));
      return Task.save(params).$promise;
    },

    /**
     * API Call to update a task
     */
    updateTask(taskId, params) {
      apiCache.triggerDeleteKeys([TASK_LIST_DELETE_KEY, `${TASK_ITEM_DELETE_KEY_PREFIX}${taskId}`]);

      const Task = $resource(apiService.getApiUrl(`task/${taskId}`), {}, {
        query: {
          method: 'PUT',
          isArray: false
        }
      });
      return Task.query(params).$promise;
    },

    /**
     * API Call to update many tasks status
     */
    updateTasks(params) {
      apiCache.triggerDeleteKeys([TASK_LIST_DELETE_KEY, ...(params.taskIds.map(taskId => `${TASK_ITEM_DELETE_KEY_PREFIX}${taskId}`))]);

      const TasksStatus = $resource(apiService.getApiUrl('tasks/status'), {}, {
        query: {
          method: 'PUT',
          params: {},
          isArray: false
        }
      });
      return TasksStatus.query(params).$promise;
    },

    /**
     * API call to fetch tasks for managers
     */
    fetchStoreTasks({ storeIds, repIds, query, page = 0, pageSize = CONST_PAGINATION.PAGE_SIZE, cutOffDate }) {
      if (repIds && repIds.length > 0) {
        storeIds = null;
      }

      query = query || 'filter[status]=unresolved';

      const params = {
        include: ['customer', 'task-category'],
        filter: {
          store_ids: storeIds,
          user_ids: repIds,
        },
        page: {
          number: page,
          size: pageSize,
        },
        [query]: null,
      };

      // Apply default sort if not specified in query
      if (!query.includes('sort=')) {
        params.sort = 'reminder_date';
      }

      if (cutOffDate) {
        params[`filter[${this.getQueryCutOffDateField()}][gte]=${cutOffDate}`] = null;
      }

      const endpoint = '/managers/tasks';
      const dataKey = params;
      const deleteKeys = [TASK_LIST_DELETE_KEY];
      const promiseToGetData = apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        return jsonapi.get(endpoint, params).then((tasks) => {
          const tasksList = tasks.map(task =>
            Object.assign(jsonapi.getEntityData(task), {
              customer: jsonapi.getEntityData(task.getRelated('customer')),
            })
          );
          return {
            tasksList,
            pagination: {
              page,
              per_page: pageSize,
              count: tasks.getMeta('total')
            }
          };
        });
      });

      return promiseToGetData;
    },

    /**
     * API call to fetch tasks filters for managers
     */
    fetchStoreTasksFilters(storeIds, repIds, cutOffDate) {
      if (repIds && repIds.length > 0) {
        storeIds = null;
      }

      const params = {
        filter: {
          store_ids: storeIds,
          user_ids: repIds,
        }
      };

      if (cutOffDate) {
        params[`filter[${this.getQueryCutOffDateField()}][gte]=${cutOffDate}`] = null;
      }

      const endpoint = '/managers/tasks/filters';
      const dataKey = params;
      const deleteKeys = [TASK_LIST_DELETE_KEY];
      const promiseToGetData = apiCache.promiseToGetData(endpoint, dataKey, deleteKeys, function () {
        return jsonapi.get(endpoint, params).then((filters) => {
          filters = filters.map((filter) => {
            const filterData = filter.toJSON().attributes;
            filterData.type = filterData.filterType;
            return filterData;
          });
  
          /**
           * filters ordering:
           * 1. unresolved
           * 2. "custom" type
           * 3. "status" type
           */
          filters.sort((filterX, filterY) => {
            if (filterX.key === filterKey.UNRESOLVED) {
              return -1;
            }
            if (filterY.key === filterKey.UNRESOLVED) {
              return 1;
            }
            if (filterX.filterType === filterY.filterType) {
              return 0;
            }
            if (filterX.filterType === filterType.CUSTOM) {
              return -1;
            }
            return 1;
          });
  
          return filters;
        });
      });

      return promiseToGetData;
    },
  };
});
