angular.module('sfmobileApp').factory('featuresEnabledService', ($rootScope) => {

  const featuresEnabledService = {
    hasTasks() {
      return $rootScope.conf('RetailerHasTasks');
    },

    hasFeedValidation() {
      return $rootScope.conf('RetailerHasFeedValidation');
    },

    hasAssetManagement() {
      return $rootScope.conf('RetailerHasAssetManagement');
    },

    hasProductsFeed() {
      return $rootScope.conf('RetailerHasProductsFeed');
    },

    hasAppointments() {
      return $rootScope.conf('RetailerHasAppointments');
    },
    //appointments request from the customer
    hasAppointmentRequests() {
      return $rootScope.conf('RetailerHasAppointmentRequests');
    },
    //appointments request from the customer are  automatically accepted - for team mode only
    areAppointmentRequestsAutoAccepted() {
      return $rootScope.conf('AppointmentRequestsAutoAcceptEnabled')
      && featuresEnabledService.isTeamMode();
    },

    hasAppointmentBooking() {
      return $rootScope.conf('RetailerServicesAppointmentManagementIsEnabled');
    },

    hasAppointmentBookingForAllCustomers() {
      return $rootScope.conf('RetailerServicesAppointmentManagementAllCustomerIsEnabled');
    },

    hasAllAppointmentsView() {
      return $rootScope.conf('CanRepViewAllAppointments') &&
        !featuresEnabledService.isTeamMode();
    },

    hasReassignAppointment() {
      return $rootScope.conf('RetailerServicesAppointmentReassignmentIsEnabled') &&
        !featuresEnabledService.isTeamMode();
    },

    hasSaveAppointmentToCalendar() {
      return $rootScope.conf('RetailerHasServicesAppointmentSaveToDevice');
    },

    hasAnyTypeOfAppointments() {
      return featuresEnabledService.hasAppointmentRequests() || featuresEnabledService.hasAppointmentBooking();
    },

    hasLookbooks() {
      return $rootScope.conf('RetailerHasLookbooks');
    },

    canEditEvents() {
      return $rootScope.conf('RetailerCanEditEvents');
    },

    hasBrowseLibrary() {
      return ($rootScope.conf('RetailerCanBrowseLibrary') && featuresEnabledService.isSellingMode());
    },

    hasClienteling() {
      return $rootScope.conf('Clienteling');
    },

    hasPhoneNumber() {
      return $rootScope.currentUser && $rootScope.currentUser.user_phone_number;
    },

    hasTextMessaging() {
      if (!featuresEnabledService.hasPhoneNumber()) {
        return false;
      }

      return $rootScope.conf('TextMessaging');
    },

    hasTextMessagingChannel() {
      return featuresEnabledService.hasTextMessaging() && $rootScope.conf('RetailerHasTextMessageChannel');
    },

    hasSpecialties() {
      return $rootScope.conf('RetailerHasSpecialties');
    },

    hasCorporateTasks() {
      return $rootScope.conf('RetailerHasCorporateTasks');
    },

    hasAutomatedTasks() {
      return $rootScope.conf('RetailerHasAutomatedTasks');
    },

    isAutoResolveTasksEnabled() {
      return $rootScope.conf('AutoResolveTasksIsEnabled');
    },

    isSellingMode() {
      if (!$rootScope.currentUser || $rootScope.currentUser.selling_mode === null) {
        return false;
      }

      return $rootScope.currentUser.selling_mode === '1';
    },

    isRetailerMode(mode) {
      return $rootScope.conf('RetailerStorepageMode') === mode;
    },

    isTeamMode() {
      return featuresEnabledService.isRetailerMode('store');
    },

    isRepMode() {
      return featuresEnabledService.isRetailerMode('rep');
    },

    isBroadcast() {
      return $rootScope.conf('RetailerChatMode') !== 'all';
    },

    isQueue() {
      return $rootScope.conf('RetailerChatMode') === 'all';
    },

    hasVideoChat({ isVirtual }) {
      return isVirtual
        ? $rootScope.conf('isVirtualVideoChatEnabled')
        : $rootScope.conf('isVideoChatEnabled');
    },

    isI18nEnabled() {
      return $rootScope.conf('i18nIsEnabled');
    },

    hasMultilang() {
      return $rootScope.conf('i18nIsEnabled') && $rootScope.currentUser.store.storeLocales && Object.keys($rootScope.currentUser.store.storeLocales).length > 1;
    },

    hasMultipleCurationTemplate() {
      return $rootScope.conf('RetailerHasMultipleEmailTemplates');
    },

    canSendEmailToMultipleRecipients() {
      return $rootScope.conf('RetailerCanSendEmailToMultipleRecipients');
    },

    hasBarcodeScanner() {
      return $rootScope.conf('ProductBarcodeScanner');
    },

    hasContactsBarcodeScanner() {
      return $rootScope.conf('ContactsBarcodeScanner');
    },

    hasCustomerActivityFeed() {
      return $rootScope.conf('RetailerHasCustomerActivityFeed');
    },

    hasCustomerTags() {
      return $rootScope.conf('RetailerHasCustomerTags');
    },

    hasSocialShop() {
      return $rootScope.conf('isShopFeedEnabled') && parseFloat($rootScope.currentUser.shop_feed);
    },

    hasMobileCheckout() {
      return $rootScope.conf('isMobileCheckoutEnabled');
    },

    hasInventoryLookup() {
      return $rootScope.conf('RetailerHasInventoryLookup') && featuresEnabledService.isSellingMode();
    },

    hasLimitedVisibility() {
      return $rootScope.conf('RetailerHasLimitedVisibility');
    },

    canSendSmsToMultiple() {
      return $rootScope.conf('RetailerCanSendSmsToMultipleRecipients') && $rootScope.conf('RetailerMaximumSmsRecipients') > 1;
    },

    hasNewArrivals() {
      return $rootScope.conf('RetailerHasNewArrivals', true);
    },

    hasDeals() {
      return $rootScope.conf('RetailerHasDeals', false);
    },

    hasRecommendedProducts() {
      return $rootScope.conf('StorefrontTrendingRecommendationsMode', false);
    },

    hasEvents() {
      return $rootScope.conf('RetailerHasEvents');
    },

    hasScrubbedShareRecipients() {
      return $rootScope.conf('ClientelingBlackout');
    },

    hasMFAEnabled() {
      return $rootScope.conf('RetailerMFAEnabled');
    },

    hasSSOEnabled() {
      return $rootScope.conf('isOauth2Enabled');
    },

    isOutfitsEnabled() {
      return $rootScope.conf('isOutfitsEnabled');
    },

    isPasswordUpdateEnabled() {
      return $rootScope.conf('RetailerPasswordUpdateEnabled');
    },

    isAppUpgradeNotificationEnabled() {
      return $rootScope.conf('MobileAppUpgradeNotificationEnabled');
    },

    isContactConsentRequired() {
      return $rootScope.conf('RetailerContactsConsentRequired');
    },

    canChangeCommunicationConsent() {
      return $rootScope.conf('RetailerCanChangeCommunicationConsent');
    },

    isStoreAppointmentHoursEnabled() {
      return $rootScope.conf('StoreAppointmentHoursIsEnabled');
    },

    hasStorefront() {
      return $rootScope.conf('RetailerHasStorefront');
    },

    canAddCustomerToContacts() {
      return $rootScope.conf('RetailerCanAddCustomerToContacts');
    },

    canAddContacts() {
      return $rootScope.conf('RetailerCanAddContacts');
    },

    canCreateAppointmentAndNotifyWithoutConsent() {
      return $rootScope.conf('RetailerServicesAppointmentManagementNotifyWithoutConsent');
    },

    hasAssociateRelationships() {
      return featuresEnabledService.hasClienteling() && $rootScope.conf('RetailerHasAssociateRelationships');
    },

    hasExtendedAttributes() {
      return $rootScope.conf('RetailerHasExtendedAttributes');
    },

    canShareAnUpdate() {
      return $rootScope.conf('RetailerCanShareAnUpdate');
    },

    canShareToSocialMedia() {
      return $rootScope.conf('ShareFacebookEnabled') || $rootScope.conf('ShareInstagramEnabled') || $rootScope.conf('SharePinterestEnabled');
    },

    canShareFromBrowseLibraryToExternalApp() {
      return $rootScope.conf('RetailerCanShareFromBrowseLibrary');
    },

    canShareToEmail() {
      return $rootScope.conf('ShareEmailEnabled');
    },

    isGroupedProductsEnabled() {
      return $rootScope.conf('GroupedProductsIsEnabled');
    },

    isGroupTasksEnabled() {
      return $rootScope.conf('GroupTasksIsEnabled');
    },
    isProductVariantsEnabled() {
      return $rootScope.conf('ProductsExpandedVariantsEnabled');
    },
    isPiiObfuscationEnabled() {
      return $rootScope.conf('PiiObfuscationIsEnabled');
    },

    isModerationTextEnabled() {
      return $rootScope.conf('ModerationTextIsEnabled');
    },

    isDraftForComposeEnabled() {
      return $rootScope.conf('DraftForComposeEnabled');
    },

    isDraftForShareEnabled() {
      return $rootScope.conf('DraftForShareEnabled');
    },

    isBackofficePrimaryLoginThroughAppEnabled() {
      return $rootScope.conf('BackofficePrimaryLoginThroughAppEnabled');
    },

    with(predicate) {
      if (angular.isString(predicate)) {
        predicate = featuresEnabledService[predicate];
      }
      return fn => (...args) => {
        if (!predicate()) {
          return;
        }
        return fn(...args);
      };
    },

    withSellingMode(fn) {
      return featuresEnabledService.with('isSellingMode')(fn);
    },

    check(...features) {
      if (features.length < 1) {
        return false;
      }
      const { checkPermissions } = window.permissionUtils;
      return checkPermissions(this, ...features);
    },
  };

  return featuresEnabledService;
});
