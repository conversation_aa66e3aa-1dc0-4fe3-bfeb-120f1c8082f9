/*
 * Push notification service
 */

angular
  .module('sfmobileApp')
  .factory(
    'pushNotificationService',
    function (
      $rootScope,
      $location,
      jsonapi,
      $route,
      $q,
      $filter,
      $timeout,
      localStorageService,
      messageService,
      soundService,
      textMessagingService,
      i18nService,
      featuresEnabledService,
      feedbackService,
      chatService,
      navService,
      tasksService,
      requestsService,
      deviceService,
      stringManipulationService,
      loggerService,
      CONST_REQUEST,
      CONST_SF_APP
    ) {
      const t = i18nService.t;
      let push = null;
      let registrationId = null; // in memory token to reduce read from storage
      const CHAT_NOTIFICATION_CHANNEL_ID = 'sf-live-chat-notification';
      const CUSTOMER_REQUEST = 'customer_request';

      function onRegistration(data) {
        if (data.registrationId) {
          // call our api with new token
          if (registrationId !== data.registrationId) {
            registrationId = data.registrationId;
            registerDeviceWithToken(data.registrationId);
          }
        }

        // remove event listener to avoid memory leak
        if (push !== null) {
          push.off('registration', onRegistration);
        }
      }

      function onNotification(data) {
        var pushNotificationData = angular.extend({}, data, data.additionalData);
        // corrects some args
        pushNotificationData.badge = data.count;

        const processButtonBack = () => {
          if ($location.path() === '/') {
            window.removeEventListener('popstate', processButtonBack);
            navService.replace('/dashboard');
          }
        };

        // add event to check if the app started through the push notification to process button back click
        if ($location.path() === '/') {
          window.addEventListener('popstate', processButtonBack);
        }
        
        handlePushNotification(pushNotificationData);
      }

      const registerDeviceWithServiceProvider = () => {
        // remove previously registered listeners
        if (push !== null) {
          push.off('notification', onNotification);
        }

        if (deviceService.isAndroid()) {
          window.PushNotification.createChannel(() => {}, () => {}, {
            id: CHAT_NOTIFICATION_CHANNEL_ID,
            description: 'Salesfloor live chat notification channel',
            importance: 5,
            visibility: 1,
            sound: soundService.PUSH_SOUND_FILE_NAME, // sound filename to play
            vibration: true,
          });
        }
            
        // register with service
        push = window.PushNotification.init({
          android: {
            icon: 'ic_notify',
            iconColor: '#008DEB'
          },
          ios: {
            alert: true,
            badge: true,
            sound: true
          }
        });
    
        push.on('registration', onRegistration);
        push.on('notification', onNotification);
        push.on('error', (e) => {
          // TODO: notify user about ability to receive push notifications ?
          // or try to rerigester ?
          loggerService.logError('[pushNotification] init: Failed to register device with service provider.', e);
        });
      };

      const onResume = () => {
        // https://github.com/havesource/cordova-plugin-push/blob/master/docs/API.md
        // Note: you will want to call PushNotification.init() each time your app starts.
        // The remote push service can periodically reset your registration ID so this ensures
        // you have the correct value.
        // we gonna do it each time app resumes
        // check for permissions first or we gonna end up with pause/resume loop if user will decline push permissions
        window.PushNotification.hasPermission((data) => {
          if (data.isEnabled) {
            registerDeviceWithServiceProvider();
          }
        });
      };

      function register() {
        if (
          !window.PushNotification ||
          !featuresEnabledService.isSellingMode()) {
          return;
        }

        // https://github.com/havesource/cordova-plugin-push/blob/master/docs/API.md
        // Note: you will want to call PushNotification.init() each time your app starts.
        // The remote push service can periodically reset your registration ID so this ensures
        // you have the correct value.
        // we gonna do it each time app resumes
        deviceService.addAppStateChangeListeners({ onResume });

        // in case app closed or we just logged in
        registerDeviceWithServiceProvider();
      }

      /**
       * Function registers the device with the backend using a token
       *
       * @param string token The device's token
       */
      const registerDeviceWithToken = token => 
        jsonapi
          .post('/pushNotifications/registerEndpoint', {
            type: 'device',
            data: {
              attributes: {
                token: token,
                virtual_fields: {
                  type: window.sfApp === CONST_SF_APP.DESKTOP ? 'web' : deviceService.isAndroid() ? 'android' : 'apple'
                }
              }
            }
          })
          .catch((data) => {
            // TODO: handle error and notify user ?
            registrationId = null;
            loggerService.logError('[pushNotification] registerDeviceWithToken: Could not register device (BE).', data);
          });
    
      function unregister() {
        if (
          !window.PushNotification ||
          !featuresEnabledService.isSellingMode()
        ) {
          return;
        }

        // remove event listener to avoid memory leak
        if (push !== null) {
          push.off('registration', onRegistration);
        }

        // remove attached listener
        deviceService.removeAppStateChangeListeners({ onResume });

        setAppIconBadgeNumber(0);

        push = null;
        registrationId = null;
      }

      function setAppIconBadgeNumber(value) {
        if (!push) {
          return;
        }
        try {
          value = parseInt(value);
          push.setApplicationIconBadgeNumber(angular.noop, angular.noop, value);
        } catch (e) {
          console.error(e);
        }
      }

      function buildAndStoreFilters(data, lsName) {
        var filters = [];

        for (var i in data) {
          if (data[i].query) {
            filters.push(data[i]);
          }
        }

        localStorageService.set(lsName, filters);
      }

      function refreshAppIconBadge(callSilently = false) {
        if (!featuresEnabledService.isSellingMode()) {
          // for non-selling user, we just clear the badge and done
          setAppIconBadgeNumber(0);
          return;
        }

        var userId = $rootScope.currentUser && $rootScope.currentUser.ID;
        if (!userId) {
          return;
        }

        var tasks = {
          messages: messageService.getStoreMessages({id: 'inbox'}, { silenceLoader: callSilently }),
          requests: messageService.getStoreRequests({status: 'unresolved'}, { silenceLoader: callSilently }),
        };

        var hasNewLeads = featuresEnabledService.isRetailerMode('rep');
        if (hasNewLeads) {
          tasks.leads = messageService.getLiveRequestsCount({}, { silenceLoader: callSilently });
        }

        var hasTextMessaging = featuresEnabledService.hasTextMessaging();
        if (hasTextMessaging) {
          tasks.texts = textMessagingService.getUnreadThreadsCount({ userId: userId }, { silenceLoader: callSilently });
        }

        var hasTasks = featuresEnabledService.hasTasks();
        if (hasTasks) {
          const getTaskCountParams = {id: userId};

          const taskCutOffDate = tasksService.getQueryCutOffUtcDate();
          if (taskCutOffDate !== null) {
            const taskCutOffDateField = tasksService.getQueryCutOffDateField();
            getTaskCountParams.filter = `filter[${taskCutOffDateField}][gte]=${taskCutOffDate}`;
          }

          tasks.tasks = tasksService.getFiltersAndCounts(getTaskCountParams, { silenceLoader: callSilently })
            .then((data) => {
              buildAndStoreFilters(data, 'sfTasksFilters');
              return data;
            });
        }

        var hasAppointments = featuresEnabledService.hasAppointments();
        if (hasAppointments) {
          tasks.appointments = requestsService.fetchRepAppointmentsFilters(userId, { silenceLoader: callSilently })
            .then((data) => {
              buildAndStoreFilters(data, CONST_REQUEST.LS_KEY_APPOINTMENTS_FILTERS);
              return data;
            });
        }

        $q.all(tasks).then(function (dataObj) {
          var filter = $filter('filter');
          $rootScope.unreadMessages = filter(dataObj.messages.messages, {status: 'unread'});
          $rootScope.unreadRequest = filter(dataObj.requests.storeRequests, {status: 'unread'});
          $rootScope.liveCount = hasNewLeads ? parseInt(dataObj.leads.count) : 0;
          $rootScope.unreadTextThreadsCount = hasTextMessaging ? parseInt(dataObj.texts.count) : 0;
          $rootScope.tasksCount = hasTasks ? parseInt(dataObj.tasks.unresolved.count) : 0;
          $rootScope.appointmentsCount = hasAppointments && dataObj.appointments.upcoming ? parseInt(dataObj.appointments.upcoming.count) : 0;

          var notificationsNb = $rootScope.unreadMessages.length + $rootScope.unreadRequest.length + $rootScope.liveCount + $rootScope.unreadTextThreadsCount;
          setAppIconBadgeNumber(notificationsNb);
        });
      }

      function playNotificationSound({ playSound = false, foreground, event_action, sound }) {
        const foregroundExceptions = ['new_chat', 'new_message', 'new_chat_message'];
        const backgroundExceptions = ['new_chat'];
        const isException = (foreground && foregroundExceptions.includes(event_action)) ||
          (!foreground && backgroundExceptions.includes(event_action));

        if (playSound && !isException && sound) {
          soundService.play(sound);
        }
      }

      /**
       * Function handles a push notification.
       *
       * Expects in data:
       *
       * - message      The message to display
       * - badge        (optional) A number to display in the badge
       * - title        (optional) An optional title
       * - sound        (optional) A sound to play when the notification is received
       * - disabled     (optional) A TRUE value indicates that the notification is disabled
       * - foreground   (optional) A "1" value indicates the notification was received while the app was in the foreground
       * - force_inapp  (optional) A TRUE value indicates that the notification should be displayed as an alert in the app, regardless of the value of foreground
       * - alertBox     (optional) A "false" value indicates that a foreground notification should not be displayed in an alert
       * - vibrate      (optional) A TRUE value indicates that the notification should trigger a vibration
       * - event_action (optional) An action to handle
       * - playSound    (optional) The flag to play sound. This is for mobile web push notifications
       * - showPrompt   (optional) The flag to show prompt when in the foreground. This is for mobile web push notifications
       *
       * Event actions include:
       *
       * - new_request
       * - new_message
       * - miss_chat
       * - new_lead
       * - new_chat_message
       * - heartbeat
       * - missedHeartbeat
       * - new_chat
       *
       * @param object data The notification data
       */
      function handlePushNotification(data) {
        if (!$rootScope.currentUser) {
          return;
        }
        if (data.foreground && data.disabled) {
          return;
        }
        if (data.foreground || data.force_inapp) {
          if (data.message && data.message.length > 0 && data.alertBox !== 'false') {
            navigator.notification.alert(data.message);
          }
          if (data.vibrate && isVibrateException(event) && navigator.vibrate) {
            navigator.vibrate(100);
          }
        }
        if (angular.isDefined(data.badge)) {
          setAppIconBadgeNumber(data.badge);
        }
        playNotificationSound(data);
        handleInappAction(data);
        notifyEventListeners(data);
      }

      /**
       * Returns TRUE if vibration should be ignored.
       */
      function isVibrateException() {
        return $location.path().indexOf('chat/room') === -1;
      }

      function redirectToRequest(requestId, search) {
        navService.goTo('/store-requests/id/' + requestId, search);
      }

      //
      // CUSTOMER REQUEST EVENT HANDLER
      //

      function handleNewRequest(event) {
        var requestId = event.request_id;
        var message = event.message;
        var requestType;
        if (message) {
          message = stringManipulationService.capitalize(message.toLowerCase());
        } else {
          requestType = getRequestType(requestId);
          message = 'You have a new ' + requestType + ' request.';
        }
        handleCustomerRequest(event, message);
      }

      function handleCancelAppointment(event) {
        handleCustomerRequest(event, 'An appointment has been cancelled');
      }

      function handleModifyAppointment(event) {
        handleCustomerRequest(event, 'An appointment has been modified');
      }

      function handleAcceptAppointment(event) {
        handleCustomerRequest(event, 'An appointment has been confirmed');
      }

      function handleCustomerRequest(event, message) {
        var requestId = event.request_id;
        if (event.foreground || event.showPrompt) {
          $timeout(function () {
            feedbackService.showPrompt(message, {
              buttons: {
                confirm: 'View',
                cancel: 'Ignore'
              }
            }).then(function () {
              redirectToRequest(requestId, {
                from: CUSTOMER_REQUEST
              });
            });
          });
        } else {
          redirectToRequest(requestId, {
            from: CUSTOMER_REQUEST
          });
        }
      }

      function getRequestType(requestId) {
        var emailMeLabel = $rootScope.conf('EmailMeLabel') !== 'Ask & Answer' ? 'Email Me' : $rootScope.conf('EmailMeLabel');
        var requestTypeMap = {
          'contact_me' : emailMeLabel,
          'book_appointment': 'Appointment',
          'personal_shopper': 'Personal Shopper'
        };

        for (var type in requestTypeMap) {
          if (requestId.indexOf(type) === 0) {
            return requestTypeMap[type];
          }
        }
      }

      function handleUnansweredRequest(event) {
        if (event.foreground || event.showPrompt) {
          $timeout(() => {
            feedbackService.showPrompt(event.message, {
              buttons: {
                confirm: 'View',
                cancel: 'Ignore'
              }
            }).then(() => navService.goTo('/store-requests'));
          });
        } else {
          navService.goTo('/store-requests');
        }
      }

      //
      // NEW MESSAGE EVENT HANDLER
      //

      function handleNewMessage(event) {
        if (event.foreground || event.showPrompt) {
          $timeout(function () {
            feedbackService.showPrompt('You just received a new customer email.', {
              type: 'newMessage',
              buttons: {
                confirm: 'View',
                cancel: 'Ignore'
              }
            }).then(function () {
              redirectToMessage(event.request_id);
            });
          });
        } else {
          redirectToMessage(event.request_id);
        }
      }

      function redirectToMessage(requestId) {
        navService.goTo('/store-messages/id/' + requestId);
      }

      //
      // CHAT EVENT HANDLES
      //
      function handleNewChat(event) {
        if (chatService.activeChat) {
          return;
        }

        if (!event.foreground) {
          // We need to store request type in order to show proper message
          chatService.setRequestType(event.requestOrigin);
          chatService.setPushIncomingRequest(true);            
        }        
      }

      function handleNewChatMessage(event) {
        feedbackService.closeFeedback();

        if ($location.path().indexOf('chat/room') > -1) {
          return;
        }

        if (event.foreground || event.showPrompt) {
          $timeout(function () {
            feedbackService.showPrompt(t('You have received a new chat message: ') + event.message, {
              type: 'newChatMessage',
              buttons: {
                confirm: 'View',
                cancel: 'Ignore'
              }
            }).then(chatService.returnToChat);
          });
        } else {
          chatService.returnToChat();
        }
      }

      //
      // NEW LEAD EVENT HANDLER
      //

      function handleNewLead(event) {
        var leadId = event.uniq_id;
        if (event.foreground || event.showPrompt) {
          $timeout(function () {
            feedbackService.showPrompt(event.message, {
              type: 'newLead',
              buttons: {
                confirm: 'View',
                cancel: 'Ignore'
              }
            }).then(function () {
              checkLeadAvailability(leadId);
            });
          });
        } else {
          checkLeadAvailability(leadId);
        }
      }

      function checkLeadAvailability(leadId) {
        return messageService.searchForLead({ id: leadId }).then((data) => {
          var request = data && data.messages && data.messages.storeRequest;
          if (request && request.user_id && request.user_id > 0) {
            return messageService.getUser({ id: request.user_id }).then((user) => {
              feedbackService.showPrompt(t('Sorry, but that Lead has already been claimed by _name_.', { name: user.first_name }), {
                type: 'new-lead-claimed',
                buttons: {
                  confirm: 'Dismiss',
                  cancel : 'All Leads'
                }
              }).then(function () {
                // dismissed, do nothing
              }, function () {
                navService.goTo('live');
              });
            }, function () {
              feedbackService.showError('Sorry, but that Lead has already been claimed by someone else.');
            });
          } else {
            redirectToNewLead(leadId);
          }
        }, function () {
          var subject = 'New leads lagging',
            html = ''
              + '<strong>Error in new leads :</strong> ' + leadId + '<br /><br />'
              + '<strong>User : </strong>' + JSON.stringify($rootScope.currentUser) + '<br /><br />'
              + '<strong>Stack : </strong>' + JSON.stringify($rootScope.getConfiguration()._data) + '<br /><br />';

          return $rootScope.utils.sendEmailToSupport(subject, html);
        });
      }

      function redirectToNewLead(leadId) {
        localStorageService.set('sfClaimLead', leadId);

        if ($route.current && $route.current.$$route.controller === 'live') {
          return $route.reload();
        }

        navService.goTo('/live');
      }

      //
      // NEW TEXT MESSAGE HANDLER
      //

      const clearAllCustomerTextInfo = () => {
        localStorageService.remove('sfTextCustomer');
        localStorageService.remove('sfTextMessageThreadData');
      };

      function handleNewTextMessage(event) {
        feedbackService.closeFeedback();
        $rootScope.$broadcast('new_text_message');

        var textThreadPath = '/text-messaging/thread/' + event.thread_id;

        if ($location.path().indexOf(textThreadPath) >= 0) {
          return;
        }

        $timeout(function () {
          feedbackService.showPrompt(event.message, {
            type: 'newTextMessage',
            buttons: {
              confirm: 'View',
              cancel: 'Ignore'
            }
          }).then(function () {
            //make sure to get the correct customer info and not the saved one
            clearAllCustomerTextInfo();
            navService.goTo(textThreadPath);
          });
        });
      }

      //
      // New Task reminder handler
      //

      function handleNewTaskReminder(event) {
        feedbackService.closeFeedback();
        const isGroupTask = event.event_action === 'group_task_reminder';
        const taskPath = `/tasks/view/${isGroupTask ? event.group_task_id :  event.task_id}`;
        if ($location.path().indexOf(taskPath) >= 0) {
          return;
        }
        $timeout(function () {
          feedbackService.showPrompt(event.message, {
            type: 'newTaskReminder',
            buttons: {
              confirm: 'View',
              cancel: 'Ignore'
            }
          }).then(function () {
            navService.goTo(
              taskPath,
              isGroupTask ? { type:'group-task' } : {});
          });
        });
      }

      //
      // ACTION HANDLERS
      //

      var eventHandlers = {
        new_request: handleNewRequest,
        cancel_appointment: handleCancelAppointment,
        modify_appointment: handleModifyAppointment,
        accept_appointment: handleAcceptAppointment,
        new_message: handleNewMessage,
        new_chat: handleNewChat,
        new_chat_message: handleNewChatMessage,
        miss_chat: function () {
          // we should not handle any UI from push notifications
          // leave to handle missed chat request to chatPresense service.
          // chatService.handleMissChat();
        },
        heartbeat: chatService.handleHeartbeat,
        missedHeartbeat: chatService.handleMissedHeartbeat,
        new_lead : handleNewLead,
        new_text_message: handleNewTextMessage,
        task_reminder: handleNewTaskReminder,
        unanswered_request: handleUnansweredRequest,
        group_task_reminder: handleNewTaskReminder
      };

      function handleInappAction(data) {
        var handler = eventHandlers[data.event_action];
        if (handler) {
          i18nService.init().then(function () {
            handler(data);
          });
        }
      }

      const eventListeners = {
        '*': [],
      };

      const addEventListener = (eventName, listener) => {
        if (angular.isFunction(eventName)) {
          listener = eventName;
          eventName = '*';
        }
        if (!angular.isFunction(listener)) {
          return;
        }
        const listeners = eventListeners[eventName] || [];
        listeners.push(listener);
        eventListeners[eventName] = listeners;
      };

      const removeEventListener = (eventName, listener) => {
        if (angular.isFunction(eventName)) {
          listener = eventName;
          eventName = '*';
        }
        if (!angular.isFunction(listener)) {
          return;
        }
        const listeners = eventListeners[eventName];
        if (!(listeners?.length > 0)) {
          return;
        }
        // eslint-disable-next-line no-constant-condition
        while (true) {
          const i = listeners.indexOf(listener);
          if (i < 0) {
            return;
          }
          listeners.splice(i, 1);
        }
      };

      const notifyEventListeners = (data) => {
        (eventListeners[data.event_action] || []).forEach(listener => listener(data));
        eventListeners['*'].forEach(listener => listener(data));
      };

      const getRegistrationToken = () => registrationId;

      const requestNotificationPermission = () => {
        return new Promise((resolve, reject) => {
          if (!('Notification' in window)) {
            loggerService.logError('[pushNotification.js] This browser does not support desktop notification');
            reject();
          } else if (Notification.permission === 'default') {
            // TODO: Add translations
            feedbackService.showPrompt(t('In order to receive push notifications you have to grant access'), {
              buttons: {
                confirm: 'OK',
                cancel: 'Cancel'
              }
            }).then(() => {
              Notification.requestPermission().then((permission) => {
                resolve(permission);
              });

              // For Safari browsers. For some reasons Safari doesn't fulfill Promise after requestPermission
              if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
                let checkPermission = setInterval(() => {
                  if (Notification.permission !== 'default') {
                    clearInterval(checkPermission);
                    resolve(Notification.permission);
                  }
                }, 1000);
              }
            }).catch(() => resolve(Notification.permission));
          } else {
            resolve(Notification.permission);
          }
        });
      };
      //
      // SERVICE OBJECT
      //
      return {
        register,
        unregister,
        refreshAppIconBadge,
        setAppIconBadgeNumber,
        addEventListener,
        removeEventListener,
        getRegistrationToken,
        registerDeviceWithToken,
        onNotification,
        requestNotificationPermission,
        onRegistration
      };
    });