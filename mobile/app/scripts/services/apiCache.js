angular.module('sfmobileApp').factory('apiCache', ($rootScope, storageService) => {
   
  const API_CACHE_KEY = 'apiCache';
  const API_CACHE_ITEM_KEY = `${API_CACHE_KEY}Item`;

  const cleanUpCycle = 10 * 60 * 1000; // 10 mins
  let cleanupInterval = null;

  /**
   * Wrap the console log 
   * only logs when apiCacheConsoleLoggingEnabled is enabled
   * 
   * @param  {...any} args 
   */
  function consoleLog(...args) {
    // Use global variable to enable console logging
    if (window.apiCacheConsoleLoggingEnabled) {
      console.log(...args);
    }
  }

  /**
   * Is cache enabled
   * 
   * @returns boolean
   */
  function isCacheEnabled() {
    return getCacheLife() > 0;
  }

  /**
   * Get the number of ms the cache will store a value for
   */
  function getCacheLife() {
    let cacheLife = 0; // default to cache disabled untill configs are loaded

    // Configs may not be loaded when this serivce is first initialized
    if ($rootScope && typeof($rootScope.conf) === 'function') {
      cacheLife = parseInt($rootScope.conf('ApiCacheExpiration'));
    }

    return cacheLife;
  }

  /**
   * Create the time of expiration
   * 
   * @returns int time in ms 
   */
  function makeExpirationTime() {
    return Date.now() + getCacheLife();
  }

  /**
   * Has the cache expired
   * 
   * @param int expireTime in ms
   * @returns 
   */
  function hasExpired(expireTime) {
    return expireTime <= Date.now();
  }

  /**
   * Add a cleanup interval if not already added
   */
  function updateCleanupCycle() {
    if (isCacheEnabled()) {
      if (cleanupInterval === null) {
        consoleLog(`cache start cleanup cycle in ${cleanUpCycle/1000} seconds`);
        cleanupInterval = setInterval(cleanup, cleanUpCycle);
      }
    } else {
      clearCleanupCycle();
    }
  }

  /**
   * Remove the cleanup interval
   */
  function clearCleanupCycle() {
    if (cleanupInterval !== null) {
      consoleLog('cache clearing cleanup cycle');
      clearInterval(cleanupInterval);
    }
  }

  /**
   * Convert params varaible to a string which will be used to identify unique query params
   * 
   * @param {*} params 
   * @returns 
   */
  function serializeDataKey(params) {
    if (typeof(params) === 'undefined') {
      return '';
    }

    if (typeof(params) === 'string') {
      return params;
    }

    return JSON.stringify(params);
  }

  /**
   * Ensure result is array of strings
   * 
   * @param string|array deleteKeys 
   * @returns 
   */
  function sanitizeDeleteKeys(deleteKeys) {
    if (typeof(deleteKeys) !== 'undefined') {
      return Array.isArray(deleteKeys) ? deleteKeys : [String(deleteKeys)];
    }

    return [];
  }

  /**
   * Makes a key to be use for each cache item
   * 
   * @param string endpoint 
   * @param string dataKey 
   * @returns 
   */
  function makeCacheKey(endpoint, dataKey) {
    return [API_CACHE_ITEM_KEY, endpoint, dataKey].join('|');
  }

  /**
   * Creates an empty object representing the cache
   * 
   * @returns object
   */
  function createInitialCacheStructure() {
    const cacheStructure = {
      deleteKeys: {},
      keyExpiration: {}
    };

    return cacheStructure;
  }

  /**
   * Get the cache structure/data from local storage
   * 
   * @returns object of cache structure
   */
  function getCacheStructure() {
    let cacheStructure = storageService.getItem(API_CACHE_KEY);

    if (cacheStructure) {
      cacheStructure = JSON.parse(cacheStructure);
    } else {
      cacheStructure = createInitialCacheStructure();
    }

    return cacheStructure;
  }

  /**
   * Save the cache to localstorage
   * 
   * @param object cacheStructure 
   */
  function saveCacheStructure(cacheStructure) {
    storageService.setItem(API_CACHE_KEY, JSON.stringify(cacheStructure));
  }

  /**
   * Get data from cache
   * 
   * @param string endpoint 
   * @param {*} dataKey 
   * @returns copy of data cached from initial fetch
   */
  function getData(endpoint, dataKey) {
    dataKey = serializeDataKey(dataKey);
    const cacheKey = makeCacheKey(endpoint, dataKey);
    const cachedValue = storageService.getItem(cacheKey);
    if (cachedValue !== null) {
      let cacheItem = JSON.parse(cachedValue);
      if (hasExpired(cacheItem.expire)) {
        consoleLog('cache expired', cacheKey);
        storageService.removeItem(cacheKey);
      } else {
        return cacheItem.data;
      }
    }

    return null;
  }

  /**
   * Save data retreived from endpoint
   * 
   * @param string endpoint 
   * @param {*} dataKey 
   * @param {*} data 
   * @param array deleteKeys 
   */
  function saveData(endpoint, dataKey, data, deleteKeys) {
    dataKey = serializeDataKey(dataKey);
    deleteKeys = sanitizeDeleteKeys(deleteKeys);
    const cacheKey = makeCacheKey(endpoint, dataKey);
    const expireTime = makeExpirationTime();

    // Cache data
    const cacheItem = JSON.stringify({
      expire: expireTime,
      data: data,
    });
    storageService.setItem(cacheKey, cacheItem);

    // Create garbage collection
    setTimeout(function () {
      consoleLog('cache check expiration', cacheKey);
      // past expiration will be deleted from localstorage
      getData(endpoint, dataKey);
      // Do not delete the data in cache structure the clean up will handle that
      // This is to keep the cleanup as light as possible
    }, getCacheLife() + 1000);
    
    let cacheStructure = getCacheStructure();

    // set key expiratations for cleanup should the garbage collection fail
    cacheStructure.keyExpiration[cacheKey] = expireTime;

    // set delete keys associated
    deleteKeys.forEach((deleteKey) => {
      if (typeof(cacheStructure.deleteKeys[deleteKey]) === 'undefined') {
        cacheStructure.deleteKeys[deleteKey] = {};
      }
      cacheStructure.deleteKeys[deleteKey][cacheKey] = 1;
    });

    saveCacheStructure(cacheStructure);
  }

  /**
   * Mass delete from cache
   * Will delete all endpoint data associated to this delete key
   * 
   * @param string|array deleteKey key to check to delete all associated endpoint data
   */
  function triggerDeleteKeys(deleteKeys) {
    consoleLog('cache triggerDeleteKeys', deleteKeys);
    deleteKeys = sanitizeDeleteKeys(deleteKeys);
    const cacheEnabled = isCacheEnabled();

    if (cacheEnabled) {
      let cacheStructure = getCacheStructure();
      let cacheChanged = false;
      deleteKeys.forEach((deleteKey) => {
        if (typeof(cacheStructure.deleteKeys[deleteKey]) !== 'undefined') {
          let cacheKeyMap = cacheStructure.deleteKeys[deleteKey];
          // Preform delete of cached data
          Object.keys(cacheKeyMap).forEach((cacheKey) => {
            if (typeof(cacheStructure.keyExpiration[cacheKey]) !== 'undefined') {
              consoleLog('cache delete', cacheKey);
              delete cacheStructure.keyExpiration[cacheKey];
              storageService.removeItem(cacheKey);
            }
          });
          delete cacheStructure.deleteKeys[deleteKey];
          cacheChanged = true;
        }
      });

      if (cacheChanged) {
        saveCacheStructure(cacheStructure);
      }
    }
  }

  /**
   * Promise to get data
   * Wrap fetch promise with another promise which checks the cache first
   * If caching disabled still returns the api response
   * 
   * @param string endpoint 
   * @param {*} dataKey params required to make query unique
   * @param array deleteKeys keys to delete this cached value if triggered
   * @param func queryPromiseFunc returns promise or data from api
   * @returns object json object of the query result (anything unserializable is removed)
   */
  function promiseToGetData(endpoint, dataKey, deleteKeys, queryPromiseFunc) {
    return new Promise((resolve, reject) => {
      const cacheEnabled = isCacheEnabled();
      dataKey = serializeDataKey(dataKey);
      const cacheKey = makeCacheKey(endpoint, dataKey);

      let cachedData = null;
      if (cacheEnabled) {
        cachedData = getData(endpoint, dataKey);
      }

      if (cachedData !== null) {
        consoleLog('cache hit', cacheKey, cachedData);
        resolve(cachedData);
      } else {
        const promiseToFetchData = Promise.resolve(queryPromiseFunc()); // typecast promise
        promiseToFetchData.then((data) => {
          // Sanitize data to be strictly serializable
          // promise results will no longer contain functions
          // keep all jsonapi logic inside the queryPromiseFunc method
          // This is also done to make mistakes more apparent and easily fixable
          let sanitizedData = null;
          try {
            sanitizedData = JSON.parse(JSON.stringify(data));
          } catch (e) {
            // data was undefined or circular
          } 

          consoleLog('cache miss query data', cacheKey, sanitizedData);

          if (cacheEnabled && sanitizedData !== null) {
            saveData(endpoint, dataKey, sanitizedData, deleteKeys);

            // start the clean up cycle if not already started
            updateCleanupCycle();
          }
          resolve(sanitizedData);
        }, reject);
      }
    });
  }

  /**
   * Clean up the expiration records
   */
  function cleanup() {
    consoleLog('cleanup cache');
    const cacheEnabled = isCacheEnabled();
    if (!cacheEnabled) {
      return;
    }

    // Process cached keys
    let cacheStructure = getCacheStructure();
    let cacheChanged = false;
    let deleteKeyModified = {};
    const deleteKeys = Object.keys(cacheStructure.deleteKeys);
    Object.keys(cacheStructure.keyExpiration).forEach((cacheKey) => {
      const expireTime = cacheStructure.keyExpiration[cacheKey];
      if (hasExpired(expireTime)) {
        consoleLog('cache cleanup delete', cacheKey);
        cacheChanged = true;

        // delete local storage cached item if not already deleted
        storageService.removeItem(cacheKey);

        // delete from list of cached keys
        delete cacheStructure.keyExpiration[cacheKey];

        // delete from delete keys
        deleteKeys.forEach((deleteKey) => {
          if (typeof(cacheStructure.deleteKeys[deleteKey][cacheKey]) !== 'undefined') {
            delete cacheStructure.deleteKeys[deleteKey][cacheKey];
            deleteKeyModified[deleteKey] = 1;
          }
        });
      }
    });

    // cleanup empty deleteKeys
    Object.keys(deleteKeyModified).forEach((deleteKey) => {
      if (Object.keys(cacheStructure.deleteKeys[deleteKey]).length === 0) {
        delete cacheStructure.deleteKeys[deleteKey];
      }
    });

    if (cacheChanged) {
      saveCacheStructure(cacheStructure);
    }
  }

  /**
   * Completly whipe and reset the cache
   */
  function reset() {
    consoleLog('reset cache');
    let cacheStructure = getCacheStructure();
    Object.keys(cacheStructure.keyExpiration).forEach((cacheKey) => {
      storageService.removeItem(cacheKey);
    });

    storageService.removeItem(API_CACHE_KEY);

    clearCleanupCycle();
  }

  /**
   * Reset cache from console 
   * COMMAND FOR DEV ONLY
   */
  window.resetApiCache = reset;

  /**
   * Toggle console logging
   * COMMAND FOR DEV ONLY
   */
  window.toggleApiCacheLogging = function () {
    window.apiCacheConsoleLoggingEnabled = !window.apiCacheConsoleLoggingEnabled;
    return window.apiCacheConsoleLoggingEnabled;
  };

  /**
   * Clean up cache
   * COMMAND FOR DEV ONLY
   */
  window.cleanupApiCache = cleanup;

  /**
   * Dump the cache keys
   * COMMAND FOR DEV ONLY
   */
  window.dumpApiCacheKeys = function () {
    let resetValue = window.apiCacheConsoleLoggingEnabled;
    window.apiCacheConsoleLoggingEnabled = true;
    consoleLog(JSON.stringify(getCacheStructure(), null, 2));
    window.apiCacheConsoleLoggingEnabled = resetValue;
  };

  return {
    triggerDeleteKeys,
    promiseToGetData,
    reset,
  };
});
  