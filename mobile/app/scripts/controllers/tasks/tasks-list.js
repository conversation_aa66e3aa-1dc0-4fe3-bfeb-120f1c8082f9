angular.module('sfmobileApp').controller('tasks-list', (
  $scope,
  $rootScope,
  $q,
  $location,
  $timeout,
  groupTaskService,
  corporateTaskService,
  facetFilterService,
  featuresEnabledService,
  feedbackService,
  i18nService,
  intentService,
  localStorageService,
  navService,
  permissionService,
  tasksService,
  stringManipulationService,
  CONST_PAGINATION,
  CONST_NAME_TEMPLATE,
  CONST_TASKS
) => {

  $scope.i18nOpts = { ns: 'tasks' };
  const t = i18nService.t($scope.i18nOpts);
  $scope.isFilterEnabled = false;
  /**
   * @param {bool} $scope.loaded
   * Indicates the initial loading is done
   */
  $scope.loaded = false;

  /**
   * @param {array} $scope.tasksList
   * List of available Tasks with data
   */
  $scope.tasksList = [];

  /**
   * @param {string} $scope.tasksGroup
   * Current selected Tasks group
   */
  $scope.tasksGroup = Number(localStorageService.get('sfTasksGroup')) || 0;

  /**
   * @param {array} $scope.filtersList
   *  Listing of available query with data
   */
  $scope.filtersList = [];

  /**
   * @param {bool} $scope.editMode
   * State of edit mode for Task listing
   */
  $scope.editMode = false;

  /**
   * @param {array} $scope.selectedTasks
   * Current selected Tasks array in Tasks listing mode
   */
  $scope.selectedTasks = [];

  /**
   * @param {array} $scope.managerMode
   * Present the list in readonly mode
   */
  $scope.managerMode = navService.isManagerModeRoute();
  $scope.isGroupTasksView = /^\/group-tasks/.test($location.path());
  $scope.showTaskDetails = false;
  $scope.taskViewParams = null;

  const defaultPagination = $scope.pagination = {
    page: 0,
    count: 0,
    per_page: CONST_PAGINATION.PAGE_SIZE
  };

  //display name template ({Fn Ln} / {Ln Fn})
  const customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);

  //display name template ({Fn} {Ln.})
  const repNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.REP.GROUP_TASK_REP_NAME);

  $scope.isGroupTasksEnabled = featuresEnabledService.isGroupTasksEnabled();

  $scope.isSellingMode = featuresEnabledService.isSellingMode();

  const isAutoResolveTasksEnabled = featuresEnabledService.isAutoResolveTasksEnabled();

  const { TASK_TYPE } = CONST_TASKS;

  const TASK_STATUS = {
    RESOLVED: 'resolved',
    UNRESOLVED: 'unresolved',
    DISMISSED: 'dismissed'
  };

  /**
   * Init function
   */
  $scope.init = function () {
    navService.addAndroidBackButtonListener($scope.clearDataAndGoBack);
    intentService.popManagerModeListControllerIntent($scope, defaultPagination);
    if ($scope.isGroupTasksEnabled) {
      facetFilterService.reset();
    }
    facetFilterService.setup($scope, t('Filter Tasks'), applyFacetFilter)
      .then(loadOrFetchTasksFilters)
      .then(loadOrFetchTasks)
      .finally(() => {
        $scope.loaded = true;
        //TODO implement the right condition
        $scope.isFilterEnabled = ($scope.managerMode || $scope.isGroupTasksView) && $scope.hasFacetFilter ||
        $scope.isGroupTasksEnabled && !$scope.isGroupTasksView && !permissionService.isUser();
      });
    $scope.clearStoredTaskData();
    $scope.toggleEditMode(false);
  };

  /**
   * Add Customer filter if from single customer view
   */
  $scope.populateCustomerFilter = () => {
    const filterData = localStorageService.get('sfTasksTempCustomerFilter');

    if (!filterData) {
      return;
    }

    const { name, count } = filterData;

    const newFilter = {
      key: 'customer',
      type: 'custom',
      query: `filter[status]=unresolved&sort=reminder_date&filter[customer_id]=${filterData.id}`,
      name,
      count
    };

    $scope.filterData = filterData;
    localStorageService.remove('sfTasksTempCustomerFilter');

    $scope.filtersList.unshift(newFilter);

    loadOrFetchTasks(newFilter.query);
  };

  const customerFilterIsLoaded = () => $scope.filtersList[0].type === 'custom' && $scope.filtersList[0].key === 'customer';

  /**
   * Load store tasks data for listing view
   */
  const fetchStoreTasks = (query) => {
    query = query || getCurrentFilterQuery();
    const { stores, associates, taskTypes } = facetFilterService.getSelection(true);
    const pageSize = taskTypes[0] === 'all-tasks' ? CONST_TASKS.LIST_SIZE : CONST_PAGINATION.PAGE_SIZE;
    const page = $scope.pagination.page || 0;
    return tasksService.fetchStoreTasks({ storeIds: stores, repIds: associates, query, page, pageSize})
      .then(({ tasksList = [], pagination = defaultPagination }) => {
        $scope.toggleEditMode(false);
        $scope.tasksList = tasksList.map((task) => {
          task.showOwner = task.storeName && task.repName;
          return task;
        });
        restoreTaskDetails();
        $scope.pagination = pagination;
      }, showLoadingError);
  };

  /**
   * Load group tasks data for listing view
   */
  const fetchGroupTasks = (query) => {
    query = query || getCurrentFilterQuery();
    return groupTaskService.fetchGroupTaskList({query, page: $scope.pagination.page, pageSize: CONST_TASKS.LIST_SIZE})
      .then(({ tasksList = [] }) => {
        $scope.toggleEditMode(false);
        $scope.tasksList = tasksList;
        restoreTaskDetails();
      }, showLoadingError);
  };

  /**
   * Load my tasks data for listing view
   */
  const fetchTasks = (query) => {
    const queryData = {
      id: $rootScope.currentUser.ID,
      filter: query || getCurrentFilterQuery() || 'filter[status]=unresolved&sort=reminder_date',
      per_page: CONST_TASKS.LIST_SIZE
    };
    return tasksService.getListing(queryData).then((data) => {
      //success
      $scope.tasksList = Object.values(data).filter(item => angular.isDefined(item.id));
      restoreTaskDetails();
      $scope.toggleEditMode(false);
      // Bypass if temporary customer filter is loaded
      if (!customerFilterIsLoaded()) {
        localStorageService.set('sfTasksList', $scope.tasksList);
        loadOrFetchTasksFilters();
      }
    }, showLoadingError);
  };

  /**
   * Call loading tasks function based on listing view mode (store tasks, group tasks, my tasks)
   */
  const loadOrFetchTasks = (query) => {
    const { taskTypes } = facetFilterService.getSelection(true);
    if (taskTypes && taskTypes.length) {
      if (taskTypes[0] === 'my-tasks') {
        return fetchTasks(query);
      } else {
        return fetchStoreTasks(query);
      }
    }

    if ($scope.managerMode) {
      return fetchStoreTasks(query);
    }
    if ($scope.isGroupTasksView) {
      return fetchGroupTasks(query);
    }
    return fetchTasks(query); //my-tasks
  };

  /**
   * Load store tasks filters and counts for listing dropdown
   */
  const fetchStoreTasksFiltersAndCounts = () => {
    const { stores, associates } = facetFilterService.getSelection(true);
    return tasksService.fetchStoreTasksFilters(stores, associates);
  };

  /**
   * Load group tasks filters and counts for listing dropdown
   */
  const fetchGroupTasksFiltersAndCounts = () => {
    const { stores, associates } = facetFilterService.getSelection(true);
    return groupTaskService.getFiltersAndCountsGroupedTasks(stores, associates)
      .then(data => data);
  };
  /**
   * Load my tasks filters and counts for listing dropdown
   */
  const fetchTasksFiltersAndCounts = () => {
    const query = {
      id: $rootScope.currentUser.ID
    };

    if ($scope.isFromContact()) {
      query.filter = `filter[customer_id]=${localStorageService.get('sfCurrentCustomer').ID}`;
    }

    return tasksService.getFiltersAndCounts(query).then(data =>
      Object.keys(data).reduce(function (filters, i) {
        return angular.isDefined(data[i].query) ? filters.concat(data[i]) : filters;
      }, []));
  };

  /**
   * Processing filters and counts data (store tasks, group tasks, my tasks)
   */
  const processingTasksFiltersAndCounts = (filtersList) => {
    $scope.filtersList = filtersList;
    localStorageService.set('sfTasksFilters', $scope.filtersList);

    const taskFilterKey =  $location.search().taskFilter;
    const taskFilterIndex = localStorageService.get('sfTasksGroup') ||
      filtersList.findIndex(({ key }) => key === taskFilterKey);
    $scope.tasksGroup = Math.max(taskFilterIndex, 0);

    $scope.populateCustomerFilter();
  };

  /**
   * Call loading tasks filters and counters function based on listing view mode (store tasks, group tasks, my tasks)
   */
  const loadOrFetchTasksFilters = () => {
    const { taskTypes } = facetFilterService.getSelection(true);
    if (taskTypes && taskTypes.length) {
      if (taskTypes[0] === 'my-tasks') {
        return fetchTasksFiltersAndCounts().then(processingTasksFiltersAndCounts, showLoadingError);
      } else {
        return fetchStoreTasksFiltersAndCounts().then(processingTasksFiltersAndCounts, showLoadingError);
      }
    }

    if ($scope.managerMode) {
      return fetchStoreTasksFiltersAndCounts().then(processingTasksFiltersAndCounts, showLoadingError);
    }
    if ($scope.isGroupTasksView) {
      return fetchGroupTasksFiltersAndCounts().then(processingTasksFiltersAndCounts, showLoadingError);
    }
    return fetchTasksFiltersAndCounts().then(processingTasksFiltersAndCounts, showLoadingError);
  };

  const getCurrentFilterQuery = () => {
    const filtersList = $scope.filtersList || [];
    const tasksGroup = Number($scope.tasksGroup) || 0;
    return (filtersList[tasksGroup] || {}).query;
  };

  const showLoadingError = () =>
    $scope.clearDataAndGoBack();

  $scope.getEmptyMessage = () =>
    t($scope.managerMode
      ? 'Your Store Tasks will appear here'
      : 'Your Tasks will appear here');

  /**
   * Formatting listing date
   */
  $scope.formatListingDate = (task) => {
    let format = 'MMMM DD, YYYY';

    if (i18nService.isLanguage('fr')) {
      format = 'D MMMM, YYYY';
    }

    if (i18nService.isLanguage('ja')) {
      format = 'YYYY年MMMD日';
    }

    if (!task.reminder_date) {
      return t('Added on _date_', { date: $rootScope.utils.formatDateWithTimezone(task.created_at, format) });
    }

    if ($scope.isTaskDueToday(task)) {
      return t('Due Today');
    }

    return t('Due _date_', { date: $rootScope.utils.formatDateWithTimezone(task.reminder_date, format) });
  };

  $scope.isTaskOverdue = ({ reminder_date }) => {
    const { today, date } = $rootScope.utils.getDateObjForComparison(reminder_date);

    return today > date;
  };

  $scope.isTaskDueToday = ({ reminder_date }) => {
    const { today, date } = $rootScope.utils.getDateObjForComparison(reminder_date);

    return today === date;
  };

  // This is needed to repopulate data on backing out, can't be used for single usage (like storeCurrentContact)
  const storeTempCustomerFilter = () => {
    if ($scope.filterData) {
      localStorageService.set('sfTasksTempCustomerFilter', $scope.filterData);
    }
  };

  // This will be used to populate Contact data when coming from Contact profile
  const storeCurrentContact = () => {
    if ($scope.filterData) {
      localStorageService.set('sfTasksCurrentContact', $scope.filterData);
    }
  };

  const reloadTaskDetails = () => {
    $scope.showTaskDetails = false;
    $timeout(() => {
      $scope.showTaskDetails = true;
    }, 100);
  };

  const initTaskDetails = (params) => {
    $scope.taskViewParams = {...params};
    localStorageService.set('sfTaskViewParams', $scope.taskViewParams);
    reloadTaskDetails();
  };

  const resetTaskDetails = () => {
    $scope.showTaskDetails = false;
    $scope.taskViewParams = null;
    localStorageService.remove('sfTaskViewParams');
  };

  const restoreTaskDetails = () => {
    const restoredTaskViewParams = localStorageService.get('sfTaskViewParams');
    const task = $scope.tasksList.find(task => task.id === restoredTaskViewParams?.taskId);

    if (task) {
      initTaskDetails(restoredTaskViewParams);
    } else {
      resetTaskDetails();
    }
  };

  /**
   * Edit task action
   */
  $scope.taskView = ({ id, type }) => {
    resetTaskDetails();
    storeTempCustomerFilter();
    intentService.stashManagerModeListControllerIntent($scope);
    const path = `tasks/view/${id}`;
    const search = {
      from: 'tasks',
      type: type,
      managerMode: $scope.managerMode ? 'true' : 'false',
    };

    if ($rootScope.isLargeScreenOrTabletLandscape()) {
      initTaskDetails({
        from: 'tasks',
        type: type,
        managerMode: $scope.managerMode,
        taskId: id,
        mode: 'view',
      });
    } else {
      return navService.goTo(path, search);
    }
  };

  $scope.taskCreate = () => {
    storeTempCustomerFilter();
    storeCurrentContact();
    return navService.goTo('tasks/create');
  };

  /**
   * Filtering tasks by selected group
   */
  $scope.filterByGroup = (key) => {
    if ($scope.filtersList[0].key === 'customer') {
      $scope.filtersList.shift();
      key--;
    }

    const { query } = $scope.filtersList[key];

    $scope.tasksGroup = key;
    $scope.filterData = null;
    localStorageService.set('sfTasksGroup', key);

    loadOrFetchTasks(query);
  };

  /**
   * Clear stored data and go back to dashboard
   */
  $scope.clearDataAndGoBack = () => {
    $scope.clearStoredListData();
    facetFilterService.reset();

    $scope.isFromContact()
      ? navService.goBack()
      : navService.goToDashboard();
  };

  /**
   * Remove stored list data
   */
  $scope.clearStoredListData = () => {
    localStorageService.remove('sfTasksList');
    localStorageService.remove('sfTasksGroup');
    corporateTaskService.deleteCorporateTaskData();
    groupTaskService.deleteGroupTaskData();
  };

  /**
   * Determines if is from contact view
   */
  $scope.isFromContact = () => !!localStorageService.get('sfCurrentCustomer');

  /**
   * Remove stored task data
   */
  $scope.clearStoredTaskData = () => {
    localStorageService.remove('sfTaskCachedData');
  };

  $scope.getDetails = ({ details }) => details;

  /**
   * Toggle task list edit mode
   */
  $scope.toggleEditMode = (bool) => {
    $scope.unselectAllTasks();

    if (bool !== undefined) {
      return $scope.editMode = bool;
    }

    return $scope.editMode = !$scope.editMode;
  };

  /**
   * Boolean letting you know if all tasks inc urrent listing are unresolved
   */
  $scope.tasksAreUnresolved = () => {
    for (let i = 0; i < $scope.tasksList.length; i++) {
      if ($scope.tasksList[i].status !== TASK_STATUS.UNRESOLVED) {
        return false;
      }
    }
    return true;
  };

  $scope.displaySelectBtn = () => {
    const { taskTypes } = facetFilterService.getSelection(true);
    const isAlltasks = taskTypes[0] === 'all-tasks';
    return !$scope.managerMode
      && !$scope.editMode
      && $scope.tasksList.length
      && $scope.tasksAreUnresolved()
      && !isAlltasks;
  };

  /**
   * Select all tasks in listing mode
   */
  $scope.selectAllTasks = () => {
    const taskArr = [];

    for (let i = 0; i < $scope.tasksList.length; i++) {
      taskArr.push($scope.tasksList[i].id);
    }

    $scope.selectedTasks = taskArr;
  };

  /**
   * Unselect all tasks in listing mode
   */
  $scope.unselectAllTasks = () => {
    $scope.selectedTasks = [];
  };

  /**
   * Bash update status of Tasks in listing mode dismissed/resolved
   */
  const updateTasksStatus = (status) => {
    if (!$scope.selectedTasks.length
      || (status === TASK_STATUS.RESOLVED && isAutoResolveTasksEnabled)) {
      return;
    }

    const statusLabel = status === TASK_STATUS.DISMISSED ?
      'dismiss' :
      status === TASK_STATUS.RESOLVED ?
        'resolve' : status;

    feedbackService.showPrompt(t(`Are you sure you want to ${statusLabel} _count_ Task? This action cannot be undone.`, {
      count: $scope.selectedTasks.length
    }), {
      buttons: {
        confirm: `${stringManipulationService.capitalize(statusLabel)} All`,
        cancel: 'Cancel'
      }
    }).then(() => {
      if ($scope.isGroupTasksView) {
        groupTaskService
          .updateGroupTasks({
            ids: $scope.selectedTasks,
            status,
            user_id: $rootScope.currentUser.ID
          })
          .then(loadOrFetchTasksFilters)
          .then(reloadTasksAfterManyStatusUpdate);
      } else {
        tasksService
          .updateTasks({
            status,
            taskIds: $scope.selectedTasks
          })
          .then(reloadTasksAfterManyStatusUpdate);
      }
    }).catch(() => {}); // we need empty catch to get rid of angular warning about possible rejection unhandling
  };

  $scope.goToPage = (page) => {
    $scope.pagination.page = page;
    loadOrFetchTasks();
  };

  const applyFacetFilter = () => {
    $scope.pagination = defaultPagination;
    loadOrFetchTasksFilters()
      .then(loadOrFetchTasks);
  };

  /**
   * Reloads and clear stashed data after mass dismiss of Tasks
   */
  const reloadTasksAfterManyStatusUpdate = () => {
    const currentFilters = localStorageService.get('sfTasksFilters');
    const currentFilterGroup = localStorageService.get('sfTasksGroup') || $scope.tasksGroup;
    const currentQuery = currentFilters[currentFilterGroup].query;

    localStorageService.remove('sfTasksFilters');

    loadOrFetchTasks(currentQuery);
  };

  $scope.getHeaderTitle = () => t($scope.hasViewModeSwitcher
    ? 'Tasks'
    : $scope.managerMode
      ? 'Store Tasks'
      : 'My Tasks');

  $scope.isTeamManager = !featuresEnabledService.isRepMode() && permissionService.isManager();
  const isRepManagement = featuresEnabledService.isRepMode() && (permissionService.isStoreManager() || permissionService.isManager());

  $scope.hasViewModeSwitcher = $scope.isGroupTasksEnabled ||
    ($scope.isSellingMode && ($scope.isTeamManager || isRepManagement));

  /**
   * Get label for the first tab
   */
  $scope.getFirstTabLabel = () => {
    if ($scope.isGroupTasksEnabled) {
      return t(permissionService.isUser() ? 'My Tasks' : 'Tasks');
    }
    if ($scope.isTeamManager) {
      return t('Store Tasks');
    }
    return t('My Tasks');
  };

  /**
   * Get label for the second tab
   */
  $scope.getSecondTabLabel = () => {
    if ($scope.isGroupTasksEnabled) {
      return $rootScope.conf('GroupTasksLabel');
    }
    return t('All Tasks');
  };

  /**
   * Click first tab handler for tasks
   */
  $scope.onClickFirstTab = () => $scope.isSellingMode
    ? navService.replace('tasks')
    : navService.replace('manager/tasks');

  /**
   * Click second tab handler for tasks
   */
  $scope.onClickSecondTab = () => $scope.isGroupTasksEnabled
    ? navService.replace('group-tasks')
    : navService.replace('manager/tasks');

  /**
   * Data for Add task button in the UI
   */
  $scope.ctaFooterAddTaskButton = [
    {
      styles: '',
      label: `+ ${t('Create New Task')}`,
      onClick: $scope.taskCreate
    }
  ];

  /**
   * Data for dismiss/resolve task buttons in the UI
   */
  $scope.ctaFooterDismissResolveButtons = [
    {
      styles: 'cta-footer-flex__button--is-outline',
      label: t('Dismiss'),
      onClick: updateTasksStatus.bind($scope, TASK_STATUS.DISMISSED)
    },
    {
      styles: isAutoResolveTasksEnabled ? 'cta-footer-flex__button--is-disabled' : '',
      label: t('Resolve'),
      onClick: updateTasksStatus.bind($scope, TASK_STATUS.RESOLVED)
    }
  ];

  $scope.isCustomerReply = ({ has_reply_from_customer }) => featuresEnabledService.isGroupTasksEnabled() && !!Number(has_reply_from_customer);

  $scope.getCustomerName = ({ customer, retailer_customer }) => {
    if (!customer && !retailer_customer) {
      return false;
    }

    const { name = '', first_name, last_name, email, phone } = customer || retailer_customer;

    if (customerNameDisplayTemplate && (first_name || last_name) ) {
      return `${t('Customer')} : ` + stringManipulationService.formatName(customerNameDisplayTemplate, first_name, last_name);
    }

    if (name && name.trim()) {
      return `${t('Customer')} : ${name}`;
    }

    if (first_name && first_name.trim()) {
      return last_name && last_name.trim()
        ? `${first_name} ${last_name}`
        : first_name;
    }

    return email || phone;
  };

  $scope.getTaskTypeName = ({ type, automated_type }) => {
    if (type === TASK_TYPE.AUTOMATED && automated_type?.indexOf('nag_') > -1) {
      return t('System Task');
    }

    return false;
  };

  $scope.showOwner = ({ showOwner }) => showOwner;

  $scope.taskCanHaveOwnerOrPreferredUser = ({ showOwner, type }) =>
    $scope.showOwner({ showOwner }) || type === TASK_TYPE.GROUP_TASK;

  $scope.hasPreferredRep = ({ preferred_user_id }) => featuresEnabledService.isGroupTasksEnabled() &&
    preferred_user_id &&
    preferred_user_id !== null;

  $scope.getStoreName = ({ storeName }) => `${t('Store')} : ${storeName}`;

  $scope.getRepName = ({ showOwner, repName, preferred_user, preferred_user_id}) => {
    if (showOwner) {
      return `${t('Assoc')} : ${repName}`;
    }

    if (preferred_user_id) {
      return stringManipulationService.formatName(repNameDisplayTemplate, preferred_user?.first_name, preferred_user?.last_name);
    }

    return false;
  };

  $scope.init();
});
