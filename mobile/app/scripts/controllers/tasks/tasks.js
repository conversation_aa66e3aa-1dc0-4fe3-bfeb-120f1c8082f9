angular.module('sfmobileApp').controller('tasks', (
  $scope,
  $rootScope,
  $location,
  $routeParams,
  $q,
  $route,
  contactService,
  corporateTaskService,
  featuresEnabledService,
  feedbackService,
  configService,
  i18nService,
  intentService,
  localStorageService,
  navService,
  productsService,
  shareService,
  tasksService,
  groupTaskService,
  stringManipulationService,
  CONST_CONTACTS,
  CONST_NAME_TEMPLATE,
  CONST_TASKS
) => {
  $scope.i18nOpts = { ns: 'tasks' };
  const t = i18nService.t($scope.i18nOpts);

  let format = `L [${t('at')}] LT`;

  const modes = {
    VIEW: 'view',
    EDIT: 'edit',
    CREATE: 'create',
  };

  $scope.fromDashboard = $location.search().fromDashboard === 'true';
  $scope.fromMainMenu = $location.search().fromMainMenu === 'true';

  /**
   * @param {object} $scope.intent
   * Data received from addressbook intent
   */
  $scope.intent = {};

  /**
   * @param {string} $scope.mode
   * Current mode (Edit/Create/Resolve, etc)
   */
  $scope.mode = '';

  /**
   * @param {boolean} $scope.invalidDate
   * Indication of an invalid reminder date
   */
  $scope.invalidDate = false;

  $scope.today = new Date();

  // Default value (now) and it's always the min value
  $scope.startDate = moment();

  /** @param {array} $scope.transactionData **/
  $scope.transactionData = [];

  /**
   * @param {bool} $scope.managerMode
   */
  $scope.managerMode = navService.isManagerModeRoute();

  $scope.isSellingModeUser = () => featuresEnabledService.isSellingMode();

  $scope.taskIsActionable = () => !$scope.task.readonly || ($scope.isGroupTask() && $scope.isSellingModeUser());

  //resolve btn should be disabled if a task needs action (send email/share/sms) and the feature autoResolveTasks is enabled
  $scope.resolveBtnIsDisabled = () =>
    featuresEnabledService.isAutoResolveTasksEnabled() && (!!$scope.user || $scope.isCorporateTask() || $scope.isGroupTask());

  /**
   * @param {object} $scope.task
   * Data for current Task
   */
  $scope.task = {
    details:  '',
    status:   '',
    notes:    '',
    contacts: [],
    reminder: '',
    created:  '',
    automated_type: '',
    auto_dismiss_time: ''
  };

  $scope.corporateTask = {
    assets: [],
    products: []
  };
  $scope.groupTask = {
    assets: [],
    products: []
  };

  // task type is used to identify group tasks
  $scope.taskType;
  $scope.taskActivityLog = [];
  $scope.taskId;
  $scope.from;
  $scope.taskViewIsEmbedded = false;

  $scope.features = featuresEnabledService;

  const { TASK_TYPE } = CONST_TASKS;

  //display name template ({Fn Ln} / {Ln Fn})
  $scope.customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);
  $scope.repNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.REP.GROUP_TASK_REP_NAME);

  const corpTaskHasProducts = () => $scope.corporateTask.products?.length;
  const groupTaskHasProducts = () => $scope.groupTask.products?.length;

  const corpTaskHasAssets = () => $scope.corporateTask.assets?.length;
  const groupTaskHasAssets = () => $scope.groupTask.assets?.length;

  $scope.displayProducts = () => corpTaskHasProducts() || groupTaskHasProducts();
  $scope.displayAssets = () => corpTaskHasAssets() || groupTaskHasAssets();

  $scope.getProductsData = () => corpTaskHasProducts() ? $scope.corporateTask.products : $scope.groupTask.products;
  $scope.getAssetsData = () => corpTaskHasAssets() ? $scope.corporateTask.assets : $scope.groupTask.assets;

  const corpTaskHasTitle = () => $scope.corporateTask.title?.length;
  const groupTaskHasTitle = () =>  $scope.groupTask.title?.length;

  $scope.displayTaskTitle = () =>
    corpTaskHasTitle() || groupTaskHasTitle();

  $scope.getTaskTitle = () =>
    corpTaskHasTitle()
      ? $scope.corporateTask.title
      : $scope.groupTask.title;

  const corpTaskHasSuggestedSubject = () => $scope.corporateTask.subject?.length;
  const groupTaskHasSuggestedSubject = () =>  $scope.groupTask.suggested_subject_line?.length;

  $scope.displayTaskSuggestedSubject = () =>
    corpTaskHasSuggestedSubject() || groupTaskHasSuggestedSubject();

  $scope.getTaskSuggestedSubject = () =>
    corpTaskHasSuggestedSubject()
      ? $scope.corporateTask.subject
      : $scope.groupTask.suggested_subject_line;

  const corpTaskHasSuggestedBody = () => $scope.corporateTask.body?.length;
  const groupTaskHasSuggestedBody = () =>  $scope.groupTask.suggested_copy?.length;

  $scope.displayTaskSuggestedBody = () =>
    corpTaskHasSuggestedBody() || groupTaskHasSuggestedBody();

  $scope.getTaskSuggestedBodyAsHtml = () => {
    let message = corpTaskHasSuggestedBody()
      ? $scope.corporateTask.body
      : $scope.groupTask.suggested_copy;

    return $rootScope.utils.convertToTrustedHtml(message);
  };

  $scope.createDraftTextFromTask = () => {
    if ($scope.isGroupTask()) {
      localStorageService.set('sfIsGroupTask', $scope.isGroupTask());
    }

    const {
      assets = [],
      products = [],
      body,
      suggested_copy,
      retailer_customer
    } = $scope.isCorporateTask() ? $scope.corporateTask : $scope.groupTask;

    const assetArr = assets.map(asset => ({ ...asset, type: 'asset' }));
    const productArr = products.map(product => ({ ...product, type: 'product' }));

    if (retailer_customer) {
      const { phone, id } = retailer_customer;
      retailer_customer.choosenField = phone;
      retailer_customer.retailer_customer_id = id;
      const customer = [].concat(retailer_customer);
      localStorageService.set('sfTextMessageSelectedContacts', customer);

      //stash customer data to get the thread in text-messaging-compose, if exists
      intentService.stash({
        data: {
          selectedContacts: customer,
        },
        ...intentService.get()
      });
      $scope.intentData = intentService.get();
    }
    productsService.updateAllStorages(assetArr.concat(productArr));
    localStorageService.set('sfCurrentTextInput', { text: body || suggested_copy || '' });
    navService.goTo('/text-messaging/compose', { taskId: $scope.taskId });
  };

  /**
   * Init function
   */
  $scope.init = () => {
    // $scope.taskViewParams may come from taskView.js directive
    $scope.taskType = $routeParams.type || $scope.taskViewParams?.type;
    $scope.taskId = $routeParams.taskId || $scope.taskViewParams?.taskId;
    $scope.managerMode = $scope.taskViewParams?.managerMode || navService.isManagerModeRoute();
    $scope.from = $routeParams.from || $scope.taskViewParams?.from;
    $scope.taskViewIsEmbedded = $scope.taskViewParams ? true : false;
    navService.addAndroidBackButtonListener($scope.clearValidateAndGoBack);

    $scope.setMode();

    handleIntent();

    if ($scope.fromDashboard || $scope.fromMainMenu) {
      $location.search('from', null);
    }

    if ($scope.mode === modes.EDIT || $scope.mode === modes.CREATE) {
      corporateTaskService.deleteCorporateTaskData();
      groupTaskService.deleteGroupTaskData();
    }

    fetchTaskData();
    getGroupTaskActivityLog();
  };

  /**
   * Set current view mode
   */
  $scope.setMode = () => {
    // $scope.taskViewParams may come from taskView.js directive
    if ($scope.taskViewParams?.mode) {
      return $scope.mode = $scope.taskViewParams.mode;
    }

    if ($location.path().indexOf('tasks/edit') > -1) {
      return $scope.mode = modes.EDIT;
    }

    if ($location.path().indexOf('tasks/create') > -1) {
      return $scope.mode = modes.CREATE;
    }

    if ($location.path().indexOf('tasks/view') > -1) {
      return $scope.mode = modes.VIEW;
    }
  };

  /**
   * Handle intent from previous controller.
   */
  const handleIntent = () => {
    $scope.intent = intentService.pop() || {};

    $scope.managerMode = $scope.managerMode || $scope.intent.managerMode;

    if ($scope.managerMode || $scope.mode === modes.VIEW || !$scope.intent.data) {
      return;
    }

    const {
      details = $scope.task.details,
      reminder = $scope.task.reminder,
      contacts = $scope.task.contacts,
      selectedContacts
    } = $scope.intent.data;

    angular.extend($scope.task, {
      details,
      reminder,
      contacts: selectedContacts.length ? selectedContacts : contacts
    });

    $scope.handleReminderSelect($scope.task.reminder);
  };

  /**
   * Trigger Address Book screen
   */
  $scope.openAddressBook = () => {
    intentService.stash({
      data: {
        location: $location.path(),
        details:  $scope.task.details,
        reminder: $scope.task.reminder,
        contacts: $scope.task.contacts,
        selectedContacts: []
      },
      options: {
        title: t('Contacts'),
        selectMax: 1,
        showSearch: true,
        showSelectedBar: false,
        showContactCheckbox: false,
        // Tasks should only should STORE contacts in the Address Book
        onlyStoreContact: 1
      }
    });
    navService.goTo('/address-book');
  };

  const getCorporateTaskDetails = id =>
    corporateTaskService.getCorporateTaskDetails(id)
      .then((corporateTask) => {
        $scope.corporateTask = corporateTask;
        corporateTaskService.storeCorporateTaskData($scope.corporateTask);
      })
      .then(() => shareService.getCorporateTaskShareDateTime($scope.corporateTask.id))
      .then(dateTime => $scope.corporateTask.sharedAt = dateTime);

  const getGroupTaskDetails = (taskId) => {
    groupTaskService.getGroupTaskDetails(taskId)
      .then((groupTask) => {
        buildTaskData(groupTask);
        $scope.groupTask = groupTask;
        $scope.activityLogMode = !!Number($scope.groupTask.has_reply_from_customer);
        groupTaskService.storeGroupTaskData($scope.groupTask);
        if (groupTask.customer_id && $scope.mode === modes.VIEW) {
          groupTask.retailer_customer.ID = groupTask.retailer_customer.id;
          $scope.user = groupTask.retailer_customer;
          return groupTask.retailer_customer;
        }
      });
  };

  /**
   * Load single Task data on edge cases
   */
  const loadSingleTask = () => {
    if (!$scope.taskId || $scope.intent === undefined || $scope.intent.data) {
      return;
    }

    if ($scope.isGroupTask()) {
      return getGroupTaskDetails($scope.taskId);
    }

    tasksService.getSingleTask({ taskId: $scope.taskId }).then((data) => {
      buildTaskData(data);

      // Task has a parent Corporate Task
      if (data.parent_id) {
        return getCorporateTaskDetails(data.parent_id);
      } else {
        // Load contact data if view mode with selected contact
        if (data.customer_id && $scope.mode === modes.VIEW) {
          return getCustomerData(data.customer_id);
        }
      }
    });
  };

  /**
   * Fetch stored task data if available or do API call
   */
  const fetchTaskData = () => {
    // From Contact single view
    if ($scope.mode === modes.CREATE) {
      const task = localStorageService.get('sfTaskData') || {};
      localStorageService.remove('sfTaskData');

      const customer = localStorageService.get('sfTasksCurrentContact');

      if (customer && !task.customer) {
        angular.extend(task, { customer });
        localStorageService.remove('sfTasksCurrentContact');
      }

      return buildTaskData(task);
    }

    return loadSingleTask();
  };

  /**
   * Task data builder
   */
  const buildTaskData = (data) => {
    if (data.customer) {
      $scope.task.contacts.push(data.customer);
    }

    // Load related retailer transactions info
    if (data.retailer_transaction_thread_id) {
      getSingleTransaction(data.retailer_transaction_thread_id);
    }

    // Load related Salesfloor transactions info
    if (data.rep_transaction_trx_id) {
      getSalesfloorTransactions(data.rep_transaction_trx_id);
    }

    const {
      status = $scope.task.status,
      details = $scope.task.details,
      reminder_date = $scope.task.reminder,
      created_date = $scope.task.created,
      automated_type = $scope.task.automated_type,
      resolution_note = $scope.task.notes,
      repName,
      storeName,
      user_id,
    } = data;


    // Decide task auto-dismiss date
    let auto_dismiss_time = null;
    if (configService.get('TaskAutoDismissEnabled')) {
      const possibleDismissDates = [];

      const taskAutoDismissSettings = configService.get('TaskAutoDismissSettings');
      taskAutoDismissSettings.forEach(function (rule) {
        // Automated type matches rule
        if (rule.automated_types.includes(automated_type)) {
          // Calculate Dismiss time
          const daysAfterEvent = rule.days_after_event;
          const afterEvent = rule.happen_after_event;
          switch (afterEvent) {
          case 'creation':
            if (created_date) {
              possibleDismissDates.push(moment.utc(created_date).add(daysAfterEvent, 'days').format());
            }
            break;
          case 'reminder':
          default:
            if (reminder_date) {
              possibleDismissDates.push(moment.utc(reminder_date).add(daysAfterEvent, 'days').format());
            }
            break;
          }
        }
      });

      // Sort dates ASC
      possibleDismissDates.sort(function (a, b) {
        let dateA = new Date(a);
        let dateB = new Date(b);
        return dateA - dateB;
      });

      // Take earliest auto dismiss time from the multiple rules
      if (possibleDismissDates[0]) {
        auto_dismiss_time = possibleDismissDates[0];
      }
    }


    angular.extend($scope.task, {
      notes: resolution_note,
      status,
      details,
      reminder: reminder_date,
      created: created_date,
      automated_type: automated_type,
      auto_dismiss_time,
      repName,
      storeName,
      showOwner: $scope.managerMode && repName && storeName,
      readonly: user_id !== (featuresEnabledService.isTeamMode()
        ? parseInt($rootScope.currentUser.store.store_user_id)
        : $rootScope.currentUser.ID)
    });

    if ($scope.mode === modes.EDIT) {
      $scope.cacheTaskData();
    }

    if ($scope.task.reminder) {
      $scope.formattedReminderDatetime = $scope.getBeautifulReminderDate(true);
    }
  };

  /**
   * Go to Edit view
   */
  $scope.goToEditTask = () => {
    navService.goTo(`tasks/edit/${$scope.taskId}`, {
      from: $scope.from
    });
  };

  const getSingleTransaction = (threadId) => {
    contactService.getSingleTransaction({ threadId }).then((response) => {
      $scope.transactionData = response;
    });
  };

  const getSalesfloorTransactions = (trxId) => {
    contactService.getSalesfloorTransactions({ trxId }).then((response) => {
      $scope.transactionData = response;
    });
  };

  /**
   * Fetch customer data
   */
  const getCustomerData = id =>
    contactService.getSingleCustomer(id)
      .then((data) => {
        $scope.user = data;
      });

  /**
   * Cache data for validation
   */
  $scope.cacheTaskData = () => {
    if (localStorageService.get('sfTaskCachedData')) {
      return $scope.cachedData = localStorageService.get('sfTaskCachedData');
    }
    $scope.cachedData = {
      customer: angular.copy($scope.task.contacts[0]),
      details:  angular.copy($scope.task.details),
      reminder: angular.copy($scope.task.reminder)
    };
    localStorageService.set('sfTaskCachedData', $scope.cachedData);
  };

  /**
   * Edit mode boolean
   */
  $scope.editMode = () => $scope.mode === modes.EDIT;

  /**
   * Handle setting reminder
   */
  $scope.handleReminderSelect = (datetime) => {
    $scope.task.reminder = datetime;
    $scope.formattedReminderDatetime = $scope.getBeautifulReminderDate(false);
  };

  const searchParams = () => {
    const params = {
      type: $scope.taskType,
      from: $scope.from
    };
    if ($scope.taskId) {
      params.taskId = $scope.taskId;
    }
    return params;
  };

  /**
   * Redirects to single contact view
   */
  $scope.goToViewContact = ({ ID }) => {
    navService.goTo(`contacts/view/${ID}`, searchParams());
  };

  /**
   * Return formatted full date/time for View mode
   */
  $scope.getBeautifulReminderDate = (isUTC = true) => {
    const reminder = $scope.task.reminder;

    if (!reminder) {
      return false;
    }

    if (i18nService.isLanguage('ja')) {
      format = `YYYY年MMMD日 [${t('at')}] LT`;
    }

    return $rootScope.utils.formatDateWithTimezone(reminder, format, isUTC);
  };

  $scope.getBeautifulAutoDismissDate = () => {
    let autoDismiss = $scope.corporateTask.auto_dismiss_time;

    if (!autoDismiss) {
      autoDismiss = $scope.task.auto_dismiss_time;
    }

    if (!autoDismiss) {
      return false;
    }

    return moment(autoDismiss).format(format);
  };

  $scope.convertToPlainText = message => $rootScope.utils.convertToPlainText(message);

  /**
   * Dismissing task action
   */
  $scope.taskDismiss = () => {
    const promptText = t('Are you sure you want to Dismiss this Task?');
    const promptOptions = {
      buttons: {
        confirm: t('Dismiss'),
        cancel: t('Cancel')
      }
    };
    return feedbackService.showPrompt(promptText, promptOptions).then(() => {
      $scope.updateTask('dismissed');
    });
  };

  /**
   * Parse contact info to display as a tag.
   *
   * @param contact
   * @returns {string}
   */
  $scope.parseContactTag = (contact) => {
    return stringManipulationService.formattedName(contact, $scope.customerNameDisplayTemplate) ||
      (contact.name || '').trim() ||
      (contact.email || '').trim() ||
      (contact.phone || '').trim();
  };

  $scope.getContactName = contact => stringManipulationService.formattedName(contact, $scope.customerNameDisplayTemplate);
  $scope.getRepName = contact => stringManipulationService.formattedName(contact, $scope.repNameDisplayTemplate);


  /**
   * Remove contact and its tag.
   *
   * @param contact
   */
  $scope.deselectContact = (contact) => {
    let index;

    for (let i = 0; i < $scope.task.contacts.length; i++) {
      if ($scope.task.contacts[i].ID === contact.ID) {
        index = i;
        break;
      }
    }
    $scope.task.contacts.splice(index, 1);
  };

  /**
   * Task form validation
   *
   * @param {bool}
   */
  $scope.isTaskFormDirty = () => {
    if (!$scope.task.details.length || !$scope.task.reminder.length) {
      return false;
    }

    if ($scope.mode === modes.EDIT) {
      const currentData = {
        customer: $scope.task.contacts[0],
        details:  $scope.task.details,
        reminder: $scope.task.reminder
      };
      $scope.cachedData = $scope.cachedData || localStorageService.get('sfTaskCachedData');

      if (angular.equals($scope.cachedData, currentData)) {
        return false;
      }
    }
    return true;
  };

  /**
   * Validate Task form and change location
   */
  $scope.clearValidateAndGoBack = function () {
    //clear the localstorage data when user clicks back button
    corporateTaskService.deleteCorporateTaskData();
    groupTaskService.deleteGroupTaskData();

    let promise = $q.when();

    if ($scope.isTaskFormDirty() && $scope.mode !== modes.VIEW) {
      promise = feedbackService.showPrompt(t('Discard your changes?'));
    }

    promise.then(() => {
      if ($scope.managerMode) {
        intentService.stash($scope.intent);
      }
      $scope.clearStoredData();

      $scope.fromDashboard || $scope.fromMainMenu
        ? navService.goToDashboard()
        : navService.goBack();
    });
  };

  // task.reminder is not always timezone based
  // If it's not modified on edit, it's UTC, so we should not convert it back to UTC again
  const getReminderDate = () => ($scope.cachedData && $scope.cachedData.reminder === $scope.task.reminder)
    ? $scope.task.reminder
    : $rootScope.utils.formatDateToUtc($scope.task.reminder, 'YYYY-MM-DD HH:mm:ss');

  /**
   * Query data builder for add/edit task
   */
  const buildQueryData = () => ({
    details: $scope.task.details,
    customer_id: $scope.task.contacts[0] ? $scope.task.contacts[0].ID || $scope.task.contacts[0].id : null,
    reminder_date: getReminderDate()
  });

  /**
   * Populate error message for Task creation
   */
  $scope.getErrorMessage = () => {
    let message;

    if (!$scope.task.details.length) {
      message = t('You must enter Details for your Task before creating it.');

      if (!$scope.task.reminder) {
        message = t('You must enter Details and a Reminder Date and Time for your Task before creating it.');
      }
    } else {
      if (!$scope.task.reminder) {
        message = t('You must enter a Reminder Date and Time for your Task before creating it.');
      }
    }

    return message;
  };

  /**
   * Creating a new Task
   */
  $scope.addTask = () => {
    if (!$scope.isTaskFormDirty()) {
      return feedbackService.showError($scope.getErrorMessage());
    }

    const queryData = {
      user_id: $rootScope.currentUser.ID
    };

    angular.extend(queryData, buildQueryData());

    tasksService.createTask(queryData).then(() => {
      //success
      $scope.clearStoredData();
      localStorageService.remove('sfTasksGroup');
      localStorageService.remove('sfTasksTempCustomerFilter');
      feedbackService.showSuccess(t('Task created successfully'));

      $scope.fromDashboard || $scope.fromMainMenu
        ? navService.goToDashboard()
        : navService.goBack();
    });
  };

  /**
   * Resolving a task
   */
  $scope.resolveTask = () => {
    if ($scope.resolveBtnIsDisabled()) {
      return;
    }
    $scope.updateTask('resolved');
  };

  /**
   * Updating an existing Task
   */
  $scope.updateTask = (status) => {
    if (!$scope.isTaskFormDirty() && !status) {
      return;
    }

    if ($scope.invalidDate) {
      return feedbackService.showError(t('You cannot set a reminder in the past.'));
    }

    const onError = (response) => {
      if (response.status === 405) {
        // Get the Status in Capitalized and translated form
        const status = t(stringManipulationService.capitalize(response.data.status));
        const date = $rootScope.utils.formatDateWithTimezone(response.data.resolution_date, 'YYYY-MM-DD hh:mm A');
        feedbackService.showError(t('Task unavailable. Status: _status_ on _date_', { status, date }));
        // Clearly the data on the app was old if this happened, so clear cache since at least 1 task changed status
        $scope.clearStoredData();
        return $scope.taskViewIsEmbedded ? $route.reload() : navService.goBack();
      }
    };

    if ($scope.isGroupTask()) {
      groupTaskService
        .updateGroupTask({
          id: $scope.taskId,
          status,
          details: $scope.task.notes,
          user_id: $rootScope.currentUser.ID
        })
        .then((data) => {
          groupTaskService.deleteGroupTaskData();
          
          if ($scope.mode === modes.EDIT) {
            buildTaskData(data);
          }

          localStorageService.remove('sfTasksTempCustomerFilter');
          feedbackService.showSuccess(t('Task _status_ successfully', { status: t(status || 'updated') }));
          $scope.taskViewIsEmbedded ? $route.reload() : navService.goBack();
        })
        .catch(onError);
    } else {
      tasksService
        .updateTask($scope.taskId, $scope.buildUpdateQuery(status))
        .then((data) => {
          //success
          $scope.clearStoredData();
    
          if ($scope.mode === modes.EDIT) {
            buildTaskData(data);
          }
    
          localStorageService.remove('sfTasksTempCustomerFilter');
          feedbackService.showSuccess(t('Task _status_ successfully', { status: t(status || 'updated') }));
          $scope.taskViewIsEmbedded ? $route.reload() : navService.goBack();
        },
        onError);
    }
  };

  /**
   * Query Data builder for updateTask function
   */
  $scope.buildUpdateQuery = (status) => {
    const queryData = {};

    if (status) { // Dismissed or resolved
      queryData.status = status;

      if (status === 'resolved') {
        queryData.resolution_note = $scope.task.notes;
      }
    } else { // Unresolved
      angular.extend(queryData, buildQueryData());
    }
    return queryData;
  };

  /**
   * Clear stored data related to listing and contacts search
   */
  $scope.clearStoredData = () => {
    localStorageService.remove('sfTasksList');
    localStorageService.remove('sfTaskCachedData');
    localStorageService.remove('sfCurrentCustomer');
    localStorageService.remove('sfContacts');
    localStorageService.remove('sfCurrentSearchMode');
    localStorageService.remove(CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA);
    localStorageService.remove('sfIsGroupTask');
  };

  /**
   * Boolean to see if task status is active
   */
  $scope.isActive = () => $scope.task.status === 'unresolved';

  /**
   * By default, the `$scope.corporateTask` object has 2 keys (products & asset)
   */
  $scope.isCorporateTask = () => Object.keys($scope.corporateTask).length > 2;
  $scope.isGroupTask = () => $scope.taskType === TASK_TYPE.GROUP_TASK;

  $scope.userCanShareTask = () =>
    $scope.isCorporateTask() ||
    ($scope.isGroupTask() && !$scope.groupTask.customer_id);

  $scope.getStartAndEndDate = () => {
    // There will only always be one asset
    const { start_date, end_date } = $scope.corporateTask.assets[0] || $scope.groupTask.assets[0];
    if (!start_date || !end_date) {
      return false;
    }

    const startDate = moment(start_date).format(format);
    const endDate = moment(end_date).format(format);
    let dateMsg;

    if (start_date && end_date) {
      dateMsg = 'Available from _startDate_ to _endDate_';
    } else if (start_date && !end_date) {
      dateMsg = 'Available from _startDate_';
    } else if (!start_date && end_date) {
      dateMsg = 'Available until _endDate_';
    }

    return t(dateMsg, { startDate, endDate, interpolation: { escapeValue: false } });
  };

  $scope.isAssetAvailable = () => {
    const asset = $scope.corporateTask.assets[0];

    if (asset && (asset.post_status !== 'publish' ||
      moment($scope.today).isBefore(asset.start_date) ||
      moment($scope.today).isAfter(asset.end_date))) {
      return false;
    }
    return true;
  };

  $scope.setActivityLogMode = (bool) => {
    if ($scope.activityLogMode === bool) {
      return;
    }
    $scope.activityLogMode = bool;
  };

  const getGroupTaskActivityLog = () => {
    if ($scope.taskType !== TASK_TYPE.GROUP_TASK ) {
      return;
    }

    groupTaskService.getGroupTaskActivityLog($scope.taskId)
      .then((logData) => {
        $scope.taskActivityLog = logData;
      });
  };

  $scope.getActivityLogIcon = ({ action }) => $scope.activityLogActions[action].class;
  $scope.getActivityLogTitle = ({ action }) => $scope.activityLogActions[action].title;

  $scope.getActivityLogTimeStamp = ({ created_at }) => $rootScope.utils.formatDateWithTimezone(created_at, 'lll');
  $scope.getActivityLogAssetImg = ({ assets }) => assets[0].thumbnail_url;
  $scope.getActivityLogProducts = ({ products }) => products;
  $scope.getActivityLogDisplayedName = ({ user, action }) =>
    action === 'REPLY-TO-AN-EMAIL'
      ? $scope.getContactName($scope.user)
      : $scope.getRepName(user);

  $scope.displayActivityLogSenderName = ({ action }) => action !== 'AUTO-DISMISS';

  $scope.activityLogActions = {
    'AUTO-DISMISS': {
      class: 'dismissed',
      title: t('The task was auto dismissed by the system')
    },
    'DISMISS': {
      class: 'dismissed',
      title: t('Dismissed the task')
    },
    'RESOLVE': {
      class: 'resolved',
      title: t('Resolved the task')
    },
    'SEND-AN-EMAIL': {
      class: 'direct-email',
      title: t('Sent an email')
    },
    'REPLY-TO-AN-EMAIL': {
      class: 'reply',
      title: t('Has replied')
    },
    'SHARE-AN-UPDATE': {
      class: 'share',
      title: t('Shared an update')
    },
    'SEND-A-TEXT-MESSAGE': {
      class: 'sms',
      title: t('Sent a text message')
    },
  };

  $scope.init();
});
