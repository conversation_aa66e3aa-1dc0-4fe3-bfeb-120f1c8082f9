angular.module('sfmobileApp').controller('text-messaging-thread', (
  $rootScope,
  $scope,
  $routeParams,
  configService,
  featuresEnabledService,
  feedbackService,
  i18nService,
  intentService,
  localStorageService,
  messageService,
  navService,
  productService,
  productsService,
  pushNotificationService,
  textMessagingService,
  stringManipulationService,
  CONST_TEXT_MESSAGE,
  CONST_NAME_TEMPLATE,
  CONST_SF_APP,
) => {

  $scope.i18nOpts = { ns: 'text-messaging' };
  const t = i18nService.t($scope.i18nOpts);

  configService.checkUpdates();

  /**
   * @param {boolean} $scope.readonly
   * Indicates readonly text thread
   */
  $scope.readonly = !!(intentService.get() || {}).readonlyTextThread;

  /** @param {string} $scope.selectedLang **/
  $scope.selectedLang = localStorageService.get('sfAttachmentLang') || i18nService.getStoreDefaultLocale();

  $scope.contact = void 0;

  /** @param {array} $scope.messagesList **/
  $scope.messagesList = [];

  /** @param {array} $scope.attachments **/
  $scope.attachments = [];

  /** @param {object} Css data for expanding chat input **/
  $scope.cssData = {};

  $scope.textMessage = {
    body: localStorageService.get('sfCurrentTextInput') ? localStorageService.get('sfCurrentTextInput').text : ''
  };

  /** @param {boolean} $scope.glued */
  $scope.glued = false;

  /** @param {int} $scope.lastMessageId */
  $scope.lastMessageId = null;

  $scope.isTeamMode = featuresEnabledService.isTeamMode();

  $scope.threadId = $routeParams.threadId || $scope.textMassageParams?.threadId;

  //display name template ({Fn Ln} / {Ln Fn})
  const customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);

  $scope.savedTextThreadId = localStorageService.get('textThreadId');

  /**
   * Initialize controller.
   */
  const init = () => {
    intentService.clear();
    navService.addAndroidBackButtonListener($scope.goBackToTextList);

    populateCustomerData();
    setBlacklistedOrInactiveState();

    return isThreadDataSaved()
      ? retrieveSavedTextMessageThreadData()
      : loadMessages();
  };

  const messageFromTaskWasSent = () => localStorageService.get('textMessageWasSent');

  $scope.goBackToTextList = () => {
    const inputData = localStorageService.get('sfCurrentTextInput');
    const hasMessage = !!((inputData || {}).text || '').length;
    const hasAttachment = !!($scope.attachments || []).length;
    const fromProductLibrary = localStorageService.get('sfBrowseMode');

    feedbackService.showPrompt(t('Discard your changes?'), {
      showOnlyIf: hasMessage || hasAttachment
    }).then(() => {
      feedbackService.showSuccess(t('Task is automatically resolved.'), {
        showOnlyIf: messageFromTaskWasSent()
      })
        .then(() => {
          localStorageService.remove('sfCurrentTextInput');
          localStorageService.remove('sfTextCustomer');
          localStorageService.remove('sfAttachmentLang');
          localStorageService.remove('sfTextGroupedProductsToggleStatus');
          localStorageService.remove('textMessageWasSent');

          if (!fromProductLibrary) {
            productsService.updateAllStorages();
          }
          navService.goBack();
        });
    });
  };

  /**
   * Fetch stored customer data if available
   */
  const populateCustomerData = (
    {
      customer_id: id,
      customer: {
        name,
        first_name,
        last_name
      } = {},
      customer_phone_number: phoneNumber,
      locale
    } = {}
  ) => {
    $scope.customer = id || phoneNumber ? {
      id,
      name,
      locale,
      last_name,
      first_name,
      phoneNumber
    } : localStorageService.get('sfTextCustomer') || {};

    if (locale) {
      $scope.storeTextMessageLang(locale);
    }
  };

  const setBlacklistedOrInactiveState = (data) => {
    // Block input without triggering the error message for blacklist
    // $scope.customer.is_sms_blacklisted only exist if is from the compose screen with selectedContacts
    $scope.userIsBlacklistedOrUnsubscribed = !!parseInt($scope.customer.is_sms_blacklisted);

    // No need to populate $scope.threadIsInactive if already blocked by $scope.userIsBlacklistedOrUnsubscribed
    if (!$scope.userIsBlacklistedOrUnsubscribed && data) {
      $scope.threadIsInactive = !parseInt(data.is_active) || !parseInt(data.is_valid) || !parseInt(data.is_subscribed);
      $scope.userIsBlacklistedOrUnsubscribed = !!parseInt(data.is_sms_blacklisted) || data.sms_marketing_subscription_flag === '2';
    }
  };

  /**
   * Mark thread status (is_read) to 1 (read)
   */
  const updateThreadsStatus = ({ id, is_read }) =>
    !parseInt(is_read) && textMessagingService.markThreadsStatus([{
      id,
      is_read: '1'
    }]).then(pushNotificationService.refreshAppIconBadge);

  /**
   * Load thread messages
   */
  const loadMessages = () => {
    const threadId = $scope.threadId;

    /* If no thread ID available, the page will */
    /* reload messages with incoming thread ID  */
    if (!threadId) {
      return;
    }

    $scope.glued = true;

    textMessagingService.loadMessages({ threadId })
      .then((response) => {
        /*
          Temporary solution, avoiding destructuring with rest operation.
          The destructuring with rest might be returned back after fix CPD-1069.
        */
        const messages = response.messages;
        const data = { ...response };
        $scope.unresolvedRequest = null;
        $scope.messagesList = Object.values(messages);
        $scope.messagesList.forEach(({ storeRequest }) => {
          if (!$scope.unresolvedRequest && storeRequest && !messageService.isResolved(storeRequest)) {
            $scope.unresolvedRequest = storeRequest;
          }
        });

        updateThreadsStatus(data);
        //If 2 contact have same phone num, both contact will have the same thread.
        //If we have contact details in local storage we should use those
        //and not the one that is associated with the thread.
        populateCustomerData(
          localStorageService.get('sfTextCustomer')
            ? ''
            : data
        );
        setBlacklistedOrInactiveState(data);

        // Team mode: store last message id displayed
        $scope.lastMessageId = messages[Object.keys(messages)[Object.keys(messages).length - 1]]['id'];

        // If team mode (without push-notification)
        if (featuresEnabledService.isTeamMode() || window.sfApp === CONST_SF_APP.DESKTOP) {
          refreshTeamThread();
        }
      })
      .catch(() => {
        $scope.goBack();
      })
      .finally(() => {
        $scope.glued = false;
      });
  };

  /**
   * Store selected lang,
   * used by live-lang-switcher.js
   */
  $scope.storeTextMessageLang = (lang) => {
    localStorageService.set('sfAttachmentLang', lang);
    $scope.selectedLang = lang;
  };

  /**
   * Handles back button.
   */
  $scope.goBack = () => {
    clearInterval($scope.checkTextsInterval);

    localStorageService.remove('sfAttachmentLang');
    localStorageService.remove('sfAttachmentLang');
    localStorageService.remove('sfTextCustomer');
    localStorageService.remove('sfTextGroupedProductsToggleStatus');
    localStorageService.remove('textMessageWasSent');

    navService.goBack();
  };

  /**
   * Setup contact name to display on thread header.
   *
   * @returns {string}
   */
  $scope.parseContactName = (customer = {}) => stringManipulationService.formattedName(customer, customerNameDisplayTemplate) || customer.name;

  /**
   * Setup contact phone number to display on thread header.
   *
   * @returns {string}
   */
  $scope.parseContactPhone = ({
    phoneNumber,
    name,
    first_name,
    last_name
  } = {}) => phoneNumber && (name || first_name || last_name)
    ? `(${phoneNumber})`
    : phoneNumber || '';

  /**
   * Link action when clicking on customer Name/Email in Text
   */
  $scope.goToContactPage = () => {
    if (!$scope.customer.id) {
      return;
    }

    navService.goTo(`contacts/view/${$scope.customer.id}`);
  };

  /**
   * Message parser used for line breaks and urls
   */
  $scope.parseMessage = (message) => {
    if (!message || !message.length) {
      return;
    }

    message = $rootScope.urlify(message).replace(/(?:\r\n|\r|\n)/g, '<br />');

    return $rootScope.htmlTags($rootScope.stripTags(message, '<a>,<br>'));
  };

  /**
   * Check if the given thread item's sender is the rep or not.
   *
   * @param item
   * @returns {boolean}
   */
  $scope.isRep = ({ direction }) => !/inbound/.test(direction); // Now we have inbound-service direction, that are "manually" created messages from services

  /**
   * Check if the given thread item is an attachment message.
   *
   * @param item
   * @returns {boolean}
   */
  $scope.isAttachment = ({ attachments = [] }) => !!attachments.length;

  /**
   * Check if the current thread item is a product message.
   *
   * @param item
   * @returns {boolean}
   */
  $scope.isProduct = ({ type }) => type === 'product';

  /**
   * Build bubble partial path based on its info.
   *
   * @param item
   * @returns {string}
   */
  $scope.getPartialPath = (item) => {
    let type = 'message';

    if (item.attachments && item.attachments.length) {
      type = 'attachment';

      //for grouped products that were sent, before we get the thread info from the BE
      if (item.attachments.length > 1 && $scope.isRep(item) ) {
        type = 'grouped-products';
      }
    } else if (item.storeRequest) {
      type = 'request';
    }
    return `views/text-messaging/_thread-bubble-${type}.html`;
  };

  /**
   * For smoothness and to avoid loading,
   * we push new rep messages directly in
   * the $scope.messagesList array
   */
  $scope.addLiveTextMessageToList = (data) => {
    const parsedBody = data.body ? $rootScope.urlify(data.body).replace(/(?:\r\n|\r|\n)/g, '<br />') : '';
    let newMsg = {
      body: parsedBody,
      direction: 'outbound',
      created_at: new Date(),
      // PP-337 Show rep name on outbound messages before refresh from server brings rep name back
      nice_display_name: $rootScope.currentUser.findrep_name
    };

    if (data.attachment.length) {
      newMsg.attachments = [];

      for (let i in data.attachment) {
        let attachment = {
          created_at: new Date(),
          url: data.attachment[i].url,
          type: data.attachment[i].type
        };

        if (data.attachment[i].type === 'product') {
          const productInfo = {
            name: data.attachment[i].name,
            sale_price: data.attachment[i].sale_price,
            regular_price: data.attachment[i].regular_price
          };

          attachment = {
            ...attachment,
            ...productInfo
          };
        }

        newMsg.attachments.push(attachment);
      }

      $scope.attachments = [];
    }

    retrieveSavedTextMessageThreadData();
    $scope.messagesList.push(newMsg);
  };

  const isThreadDataSaved = () => !!localStorageService.get('sfTextMessageThreadData');

  /**
   * Retrieve messages list data to prevent loading
   */
  const retrieveSavedTextMessageThreadData = () => {
    if (isThreadDataSaved()) {
      $scope.messagesList = localStorageService.get('sfTextMessageThreadData');
      localStorageService.remove('sfTextMessageThreadData');
    }
  };

  /**
   * Return a formated date, following designs
   */
  $scope.getFormatDate = timestamp =>
    moment.utc(timestamp).tz(moment.tz.guess()).format(i18nService.isLanguage('fr') ? 'MMM D, HH:mm' : 'MMM D, h:mma');

  /**
   * Saving view data before attachment
   *
   * Used from Attachment Dialog
   */
  $scope.saveData = () => {
    localStorageService.set('sfTextMessageThreadData', $scope.messagesList);
    localStorageService.set('sfTextCustomer', $scope.customer);
  };

  /**
   * Determine if current product is on sale
   */
  $scope.isProductOnSale = productService.isProductOnSale;

  /**
   * Load text messages in background when receiving new message notification
   * Using cloudinary to compress product/asset images
   */
  $scope.getCompressedImage = (imgUrl) => {
    const cloudName = $.cloudinary.config().cloud_name || $rootScope.conf('CloudinaryCloudName');

    /* The width logic follows the CSS logic for width */
    const width = window.innerWidth < 767 ? Math.ceil((window.innerWidth / 2) + 40) : Math.ceil((window.innerWidth / 2) - 100);

    return `https://res.cloudinary.com/${cloudName}/image/fetch/w_${width},c_lfill/${imgUrl}`;
  };

  /**
   * when on team mode, there is no push notification to refresh the thread
   * we have to check periodically if there is new message to display
   */
  const refreshTeamThread = () => {
    $scope.checkTextsInterval = setInterval(() => {
      getNewContent();
    }, CONST_TEXT_MESSAGE.REFRESH_RATE);
  };

  /*
   * Query new messages from api and compares last ids
   * refresh messages thread if new messages sent/received
   */
  const getNewContent = () => {
    const threadId  = $scope.threadId;

    // SF-22289 - If no thread id is available, don't make a request.
    // Otherwise, we get a barrage of "route doesn't exist" 400 errors in the logs.
    if (!threadId) {
      return;
    }

    textMessagingService.loadMessages({ threadId }).then((data) => {
      let currentLastMessageId = data.messages[Object.keys(data.messages)[Object.keys(data.messages).length - 1]]['id'];

      if (parseInt($scope.lastMessageId) < parseInt(currentLastMessageId)) {
        loadMessages();
      }
    });
  };

  $scope.$on('$destroy', () => {
    clearInterval($scope.checkTextsInterval);
  });

  const isPreCustomer = ({ sender: { type } = {} }) => type === 'Pre-Customer';

  $scope.resolveRequest = (request) => {
    const requestId = `${request.request_type}_${request.request_id}`;

    // If first Request of text message thread as a type of 'Pre-Customer'
    // Show prompt to view request and add to contact instead of resolvint directly
    // SF-26696
    if (isPreCustomer(request)) {
      return feedbackService.showPrompt(t('The Request you are resolving is not linked to a Contact in your Address Book. To link the request, click View Request then Add to Contacts from the request.'), {
        buttons: {
          confirm: t('View Request_small'),
          cancel: 'Cancel'
        }
      }).then(() => {
        navService.goTo(`/store-requests/id/${requestId}`);
      });
    }

    const status = 'resolved';

    messageService.setStoreMessageStatus({
      data: '',
      status,
      messages: [{ requestId }]
    }).then((response) => {

      if (response.status) {
        feedbackService.showSuccess(t('The request has been set as _status_', {
          status: t(status, { ns: 'messages' }),
          ns: 'messages'
        })).then(() => {
          if ($scope.textMassageParams?.threadId) {
            init();
          } else {
            $scope.goBack();
          }
        });

        request.status = status;
      }
    });
  };

  $scope.formatPrice = price => $rootScope.utils.formatCurrency(price, $scope.selectedLang);

  $scope.showImagePreview = attachment => feedbackService.showImagePreviewWithZoom(attachment);

  /**
   * Load text messages when app resumes
   */
  $scope.$on('resume', loadMessages);

  /**
   * Load text messages when receiving new message notification
   */
  $scope.$on('new_text_message', loadMessages);

  /**
   * Get cssData from compose-message for thread height
   */
  $scope.$on('newCssData', (e, data) => {
    $scope.cssData = data;
  });

  init();
});
