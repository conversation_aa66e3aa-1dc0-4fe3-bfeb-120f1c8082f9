angular.module('sfmobileApp').controller('compose-message', (
  $scope,
  $rootScope,
  $location,
  apiCache,
  feedbackService,
  flashMessageService,
  i18nService,
  localStorageService,
  navService,
  productService,
  productsService,
  textMessagingService,
  featuresEnabledService,
  groupedProductsValidatorService,
  intentService,
  configService,
  $routeParams,
  CONST_CHAT_INPUT,
  CONST_HTTP,
  API_CACHE_KEYS
) => {

  /**
   * This is a Sub-Controller that needs its parent to have to following to work.
   ***
   *
   * Attributes:
   *
   * @param {object} $scope.cssData
   ***
   *
   * Methods:
   *
   * $scope.updateCssData(int);
   *  This function will need a broadcast calling updateCssData
   */

  $scope.i18nOpts = { ns: 'text-messaging' };
  const t = i18nService.t($scope.i18nOpts);

  $scope.type = 'text-message';

  /**
   * @param {boolean} $scope.threadIsInactive
   * Indicates inactive thread
   */
  $scope.threadIsInactive = !!$scope.readonly;

  const attachmentTypes = {
    product: 'product',
    asset: 'asset',
    photo: 'photo'
  };

  $scope.isGroupedProductsFeatureEnabled = featuresEnabledService.isGroupedProductsEnabled();
  $scope.isGroupedProductsToggleEnabled = localStorageService.get('sfTextGroupedProductsToggleStatus') || false;
  $scope.groupedProductsSendStyledLinkLabel = configService.get('GroupedProductsSendStyledLinkLabel');
  const groupedProductsMaxProducts = configService.get('GroupedProductsMaxProducts');
  const groupedProductsMinProducts = configService.get('GroupedProductsMinProducts');

  const { TASK_LIST_DELETE_KEY,
    TASK_ITEM_DELETE_KEY_PREFIX,
    GROUP_TASK_LIST_DELETE_KEY,
    GROUP_TASK_ITEM_DELETE_KEY_PREFIX
  } = API_CACHE_KEYS;

  const setGroupedProductsToggleStatus = (statusValue) => {
    $scope.isGroupedProductsToggleEnabled = statusValue;
    localStorageService.set('sfTextGroupedProductsToggleStatus', statusValue);
  };

  $scope.getAttachmentsByType = attachmentType =>
    $scope.attachments.filter(({ type }) => type === attachmentType) || [];

  const areGroupedProductsAttachmentsValid = () => {
    const { product, asset, photo } = attachmentTypes;
    const attachments = {
      assets: $scope.getAttachmentsByType(asset),
      products: $scope.getAttachmentsByType(product),
      photos: $scope.getAttachmentsByType(photo)
    };
    return groupedProductsValidatorService.validateAttachments(attachments, true);
  };

  $scope.onToggleGroupedProducts = (statusValue) => {
    if (statusValue) {
      //if attachments are not valid, do not enable the toggle
      if (!areGroupedProductsAttachmentsValid()) {
        return;
      }
      setGroupedProductsToggleStatus(statusValue);
      //set max product to select
      localStorageService.set('sfMaxCountProducts', groupedProductsMaxProducts);
    } else {
      setGroupedProductsToggleStatus(statusValue);
      localStorageService.remove('sfMaxCountProducts');
    }
  };

  /**
   * Initialize controller.
   */
  const init = () => {
    retrievePhotos();
    retrieveAsset();
    retrieveProducts();

    calculateLastScrollheight();
  };

  /**
   * Retrieve selected Asset and send it
   */
  const retrieveAsset = () => {
    const selectedAsset = localStorageService.get('sfAssetProps');

    if (selectedAsset) {
      $scope.attachments.push(attachmentObjectBuilder('asset', selectedAsset));
    }
  };

  /**
   * Retrieve selected Product and send it
   */
  const retrieveProducts = () =>
    (localStorageService.get('sfProductList') || []).forEach((product) => {
      product = {
        ...product,
        ...product.tmplDisplay,
        ...product.selectedVariant,
      };
      delete product.product_data;
      delete product.tmplDisplay;
      delete product.selectedVariant;
      $scope.attachments.push(attachmentObjectBuilder('product', product));
    });

  const retrievePhotos = () =>
    (localStorageService.get('sfPhotosList') || []).forEach(item => $scope.attachments.push(attachmentObjectBuilder('photo', item)));

  /**
   * Return a properly built attachment object
   */
  const attachmentObjectBuilder = (type, object) => {
    let item;

    if (type === 'product' || type === 'variant') {
      item = {
        url: productService.getImage(object) || object.url, // this is needed to dispaly img aftet removeProduct()
        name: productService.getName(object),
        link_url: productService.getLink(object) || object.link_url,
        sale_price: productService.getSalePrice(object) || object.sale_price,
        resource_id: productService.getProductSku(object),
        regular_price: productService.getOriginalPrice(object) || object.regular_price,
        sku: productService.getProductRetailerSku(object),
        locale: object.locale,
      };
    }

    if (type === 'asset') {
      item = {
        url: productService.getAssetImage(object),
        link_url: productService.getAssetPermanentUrl(object),
        resource_id: productService.getAssetId(object)
      };
    }

    if (type === 'photo') {
      item = {
        url: object.url
      };
    }

    return {
      type,
      drawerPosition: $scope.attachments.length,
      ...item
    };
  };

  const getCurrentRecipients = () => {
    const customer = $scope.customer;
    return customer ? [customer] : $scope.selectedContacts || [];
  };
  $scope.isProductAttached = () => !!$scope.attachments.find(({ type }) => type === attachmentTypes.product);

  /**
   * Returns a boolean to enable/disable sendMessage
   */
  $scope.canSendMessage = () => {
    const hasContact    = !!getCurrentRecipients().length;
    const hasMessage    = $scope.textMessage.body && $scope.textMessage.body.length;
    const hasAttachment = $scope.attachments && $scope.attachments.length;

    return hasContact && (hasMessage || hasAttachment) && !$scope.showAttachmentDialog && !$scope.threadIsInactive && !$scope.userIsBlacklistedOrUnsubscribed;
  };

  const isFromCompose = () => $location.absUrl().indexOf('/text-messaging/compose') >= 0;

  const isFromThread = () => $location.absUrl().indexOf('/text-messaging/thread') >= 0 || $scope.threadId;

  $scope.isGroupTask = () => localStorageService.get('sfIsGroupTask');

  /**
   * Handle showing/hiding attachment dialog.
   */
  $scope.toggleAttachmentDialog = (bool) => {
    if (bool !== undefined) {
      return $scope.showAttachmentDialog = bool;
    }
    $scope.showAttachmentDialog = !$scope.showAttachmentDialog;
  };

  const getTotalMessages = () => ($scope.attachments || []).length + !!(($scope.textMessage || {}).body || '').length ? 1 : 0;

  let messages = {
    success: 0,
    error: 0,
    total: getTotalMessages()
  };

  const shouldAutoResolveTask = () => featuresEnabledService.isAutoResolveTasksEnabled() && !!taskId;

  const deleteCachedApi = () => {
    const deleteItemKey =  $scope.isGroupTask() ? `${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${taskId}` : `${TASK_ITEM_DELETE_KEY_PREFIX}${taskId}`;
    const deleteListKey =  $scope.isGroupTask() ? GROUP_TASK_LIST_DELETE_KEY : TASK_LIST_DELETE_KEY;
    apiCache.triggerDeleteKeys([deleteListKey, deleteItemKey]);
  };

  const sendTextMessage = (query) => {
    // Used for text message thread
    $scope.glued = true;

    if (isFromThread()) {
      $scope.addLiveTextMessageToList(query);
    }

    textMessagingService.sendTextMessage(query).then(async (data) => {
      // Success
      ++messages.success;

      // Only do once
      // We need this check as long as we use a loop to send text and attachmets separatly
      if (messages.success === 1) {
        // Clear local storage of all kinds
        productsService.updateAllStorages();

        // Clear input text and height on send
        localStorageService.remove('sfCurrentTextInput');

        intentService.clear();
        if (shouldAutoResolveTask()) {
          //if SMS is from a task and the AutoResolveTask is enabled
          //delete cached api so task and task list will be fetched again
          deleteCachedApi();
          // textMessageWasSent is needed in text-messaging/thread
          if (!$scope.isGroupTask()) {
            localStorageService.set('textMessageWasSent', true);
          }
        }

        //if sent from group task, go back to task after send sms
        if ($scope.isGroupTask()) {
          localStorageService.remove('sfIsGroupTask');
          localStorageService.remove('textMessageWasSent');
          feedbackService.showSuccess(t('Task is automatically resolved.'), {
            showOnlyIf: shouldAutoResolveTask(),
          })
            .then(() => navService.goBack());
        }

        if (isFromCompose()) {
          // If multiple recipients, go back to thread listing
          if (getCurrentRecipients().length > 1) {
            await feedbackService.showSuccess(t('Task is automatically resolved.'), {
              showOnlyIf: shouldAutoResolveTask(),
            });

            navService.goBack();

            // If some, but not all of the numbers do not support text messaging, show message
            if (data.invalid_numbers_message) {
              await feedbackService.showError(data.invalid_numbers_message);
            }

            return flashMessageService.showNotification(t('Your messages are being sent. Please note that it may take up to a minute.'));
          }
          // If only one recipient, redirect to thread
          navService.replace(`/text-messaging/thread/${data.text_thread_id}`);
        }
      }
    }, ({ data, status }) => {
      // Fail
      ++messages.error;

      if (data.details) {
        feedbackService.showError(data.details);
      }
      
      if (messages.error === messages.total && !messages.success && isFromCompose()) {
        const displayName = $scope.selectedContacts[0].name || `customer (${$scope.selectedContacts[0].choosenField})`;

        feedbackService.showError(t('Unable to send text message to _displayName_ at the moment. Please try again later.', { displayName: displayName }));
        navService.goBack();
      }

      if (isFromThread()) {
        $scope.messagesList.pop();

        if (status === CONST_HTTP.CODE_FORBIDDEN) {
          // The customer opted out
          $scope.threadIsInactive = true;
        }
      }
    }).finally(() => $scope.glued = false); // Used for text message thread
  };

  const getSelectedLang = () => $scope.selectedLang || localStorageService.get('sfAttachmentLang') || '';

  //taskId is passed when a user is coming from a regular/corp/group task to autoResolveTask
  //for groupTasks, we'll use groupTaskData.id, but will use taskId to delete the cache
  const { threadId, taskId } = $routeParams;

  $scope.sendMessage = () => {
    $scope.showAttachmentDialog = false;

    return $scope.canSendMessage() && feedbackService.showPrompt(t('This message will be sent to all recipients. Subsequent messages will need to be done on a 1-on-1 basis. Do you wish to SEND this message?'), {
      showOnlyIf: getCurrentRecipients().length > 1,
      buttons: {
        confirm: 'Yes',
        cancel: 'No'
      }
    }).then(async () => {
      const selectedLang = getSelectedLang();
      const queryData = {
        locale: selectedLang,
        recipients: getCurrentRecipients().map(({ id, ID, phoneNumber, choosenField, retailer_customer_id }) => {
          if ($scope.isGroupTask() && retailer_customer_id) {
            return ({
              retailer_customer_id,
              retailer_customer_phone_number: phoneNumber || choosenField,
              //request_time is needed to indicate group task related text messages
              request_time: moment(Date.now()).format('YYYY-MM-DD hh:mm:ss')
            });
          }
          return ({
            customer_id: id || ID,
            customer_phone_number: phoneNumber || choosenField,
          });
        })
      };
      if ($scope.isGroupTask()) {
        const groupTaskData = localStorageService.get('sfGroupTaskData');
        queryData.group_task_id = groupTaskData.id;
      } else if (taskId) {
        queryData.task_id = taskId;
      }
      if (threadId) {
        queryData.thread_id = threadId;
      }

      // We calculate the number of texts to send for the backend since each attachment is sent as a separate message.
      // This is implemented for SF-26575 as a way to know when the last message has been sent (on the backend side) so
      // we can send a custom message after.
      const totalMessages = getTotalMessages();
      let messageCount = 1;

      $scope.processedAttachments = $scope.attachments;

      // If GroupedProducts toggle enabled, all the product should be sent together,
      // rest of the attachments, will be sent one by one
      if ($scope.isGroupedProductsToggleEnabled && $scope.processedAttachments?.length) {
        const groupedProducts =  $scope.getAttachmentsByType(attachmentTypes.product);

        $scope.processedAttachments = $scope.attachments.filter(({ type }) => type !== attachmentTypes.product);

        sendTextMessage({
          ...queryData,
          body: '',
          attachment: groupedProducts,
          groupedProductsOn: true,
          is_last_message: messageCount === totalMessages
        });
        messageCount++;
      }
      // Loop to send attachments
      for (let item of $scope.processedAttachments) {
        const { type, locale, sku } = item;
        if ((type === 'product' || type === 'variant') && !$scope.isGroupedProductsToggleEnabled) {
          if (locale !== selectedLang) {
            try {
              const { variant } = await productService.getProductVariant(sku, selectedLang);
              item = attachmentObjectBuilder(type, { ...item, ...variant });
              delete item.locale;
              delete item.sku;
            } catch (e) {
              console.warn(`fetch product variant for lang ${selectedLang} failed`);
              console.warn(e);
            }
          }
        }

        sendTextMessage({
          ...queryData,
          body: '',
          attachment: [item],
          is_last_message: messageCount === totalMessages
        });

        messageCount++;
      }

      // Send message body (as a different message)
      if ($scope.textMessage.body && $scope.textMessage.body.length) {
        sendTextMessage({
          ...queryData,
          body: $scope.textMessage.body,
          attachment: [],
          is_last_message: messageCount === totalMessages
        });

        // Reset input
        $scope.textMessage.body = '';
        calculateNewCssValue(null, CONST_CHAT_INPUT.LEVEL_0);
      }

      localStorageService.remove('sfPhotosList');
      localStorageService.remove('sfTextGroupedProductsToggleStatus');
      localStorageService.remove('sfMaxCountProducts');
    });
  };

  /**
   * Calculations to expand the chat input
   */
  $scope.autoExpand = (e) => {
    const element = typeof e === 'object' ? e.target : document.getElementById(e);
    const scrollHeight = element.scrollHeight;
    let newHeight;

    // Erasing
    if (e.keyCode === 8) {
      // Last line
      if (scrollHeight < CONST_CHAT_INPUT.LEVEL_1) {
        newHeight = CONST_CHAT_INPUT.LEVEL_0;
      // 2 lines remaining
      } else if (scrollHeight < CONST_CHAT_INPUT.LEVEL_2) {
        newHeight = CONST_CHAT_INPUT.LEVEL_1;
      // 3 lines remaining
      } else {
        newHeight = CONST_CHAT_INPUT.LEVEL_2;
      }
    // Typing
    } else {
      // Going to 3 lines
      if (scrollHeight > CONST_CHAT_INPUT.LEVEL_1) {
        newHeight = CONST_CHAT_INPUT.LEVEL_2;
      // Going to 2 lines
      } else if (scrollHeight > CONST_CHAT_INPUT.LEVEL_0) {
        newHeight = CONST_CHAT_INPUT.LEVEL_1;
      // Staying at 1 line
      } else {
        newHeight = CONST_CHAT_INPUT.LEVEL_0;
      }
    }

    calculateNewCssValue(null, newHeight);
  };

  const calculateNewCssValue = ($event, scrollHeight) => {
    // 2px is for top and bottom border
    const wrapperHeightTotal = scrollHeight + 2;

    // 20px is for top and bottom padding of .chat-compose and 1px is for top border of .chat-compose
    const quickMenuBottomTotal = wrapperHeightTotal + 20 + 1;

    // 95px is for .main-header height + .compose-to height
    let chatRoomHeightTotal = quickMenuBottomTotal + 95;

    if ($scope.attachments && $scope.attachments.length && !$scope.showAttachmentDialog) {
      // 80px is .products-drawer-list height
      chatRoomHeightTotal += 80;
    }

    localStorageService.set('sfCurrentTextInput', {
      height: scrollHeight,
      text: $scope.textMessage.body
    });

    $scope.cssData = {
      wrapperHeight: {
        height: `${wrapperHeightTotal}px`
      },
      quickMenuBottom: {
        bottom: `${quickMenuBottomTotal}px`
      },
      chatRoomHeight: {
        height: `calc(100% - ${chatRoomHeightTotal}px - env(safe-area-inset-bottom))`
      }
    };

    $rootScope.$broadcast('newCssData', $scope.cssData);

    return $scope.cssData;
  };

  const calculateLastScrollheight = () => {
    const { height } = localStorageService.get('sfCurrentTextInput') || {};
    calculateNewCssValue(null, height || CONST_CHAT_INPUT.LEVEL_0);
  };

  $scope.$watchCollection('attachments', (data) => {
    if (!data || !data.length || data.length === 1) {
      calculateLastScrollheight();
    }
    // when products are removed and groupedProductsToggle is enabled,
    // disable the toggle if number of product is less than the min products allowed
    if ($scope.isGroupedProductsToggleEnabled &&
      $scope.getAttachmentsByType(attachmentTypes.product).length < groupedProductsMinProducts) {
      setGroupedProductsToggleStatus(false);
      localStorageService.remove('sfMaxCountProducts');
    }
  });

  $scope.$on('updateCssData', calculateNewCssValue);

  // When setting the blacklisted flag, if it's true, show prompt
  $scope.$watch('userIsBlacklistedOrUnsubscribed', (userIsBlacklistedOrUnsubscribed = false) => {
    const number = $scope.customer
      ? $scope.customer.phoneNumber
      : $scope.selectedContacts[0]
        ? $scope.selectedContacts[0].phone
        : '';
    return feedbackService.showError(t('_number_ is currently Unsubscribed. Please contact the customer via another channel and notify them to send a ‘START’ text message to your number. This will permit you to send them a text message.', {number}), {
      showOnlyIf: userIsBlacklistedOrUnsubscribed
    });
  });

  init();
});
