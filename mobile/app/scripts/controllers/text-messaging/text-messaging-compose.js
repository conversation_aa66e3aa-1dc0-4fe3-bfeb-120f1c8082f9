angular.module('sfmobileApp').controller('text-messaging-compose', (
  $rootScope,
  $scope,
  $location,
  $routeParams,
  localStorageService,
  textMessagingService,
  intentService,
  configService,
  i18nService,
  feedbackService,
  navService,
  productsService,
  featuresEnabledService,
  stringManipulationService,
  CONST_ADDRESS_BOOK,
  CONST_NAME_TEMPLATE,
) => {

  $scope.i18nOpts = { ns: 'text-messaging' };
  const t = i18nService.t($scope.i18nOpts);

  configService.checkUpdates();

  /** @param {string} $scope.selectedLang **/
  $scope.selectedLang = localStorageService.get('sfAttachmentLang') || i18nService.getStoreDefaultLocale();

  /** @param {object} $scope.intent **/
  $scope.intent = {};

  /** @param {array} $scope.selectedContacts **/
  $scope.selectedContacts = [];

  /** @param {array} $scope.attachments **/
  $scope.attachments = [];

  $scope.textMessage = {
    body: localStorageService.get('sfCurrentTextInput') ? localStorageService.get('sfCurrentTextInput').text : ''
  };

  $scope.fromDashboard = $location.search().fromDashboard === 'true';
  $scope.fromMainMenu = $location.search().fromMainMenu === 'true';
  $scope.taskId = $routeParams.taskId;

  let composeInputIsFocused = false;

  //display name template ({Fn Ln} / {Ln Fn})
  const customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);

  /**
   * Initialize controller.
   */
  const init = () => {
    navService.addAndroidBackButtonListener($scope.goBackToTextList);

    handleIntent();
  };

  const messageFromTaskWasSent = () => localStorageService.get('textMessageWasSent');

  $scope.goBackToTextList = () => {
    const hasContact = $scope.selectedContacts && $scope.selectedContacts.length;
    const inputData = localStorageService.get('sfCurrentTextInput');
    const hasMessage = inputData && inputData.text && inputData.text.length;
    const hasAttachment = $scope.attachments && $scope.attachments.length;
    const fromProductLibrary = localStorageService.get('sfBrowseMode');

    feedbackService.showPrompt(t('Discard your changes?'), {
      showOnlyIf: hasContact || hasMessage || hasAttachment
    }).then(() => {
      feedbackService.showSuccess(t('Task is automatically resolved.'), {
        showOnlyIf: messageFromTaskWasSent()
      })
        .then(()=> {
          localStorageService.remove('sfCurrentTextInput');
          localStorageService.remove('sfAttachmentLang');
          localStorageService.remove('sfIsGroupTask');
          localStorageService.remove('textMessageWasSent');
          //TD-1567 remove any stashed data
          intentService.clear();

          if (!fromProductLibrary) {
            productsService.updateAllStorages();
          }

          $scope.fromDashboard || $scope.fromMainMenu
            ? navService.goToDashboard()
            : navService.goBack();
        });
    });
  };

  /**
   * Store selected lang,
   * used by live-lang-switcher.js
   */
  $scope.storeTextMessageLang = (lang) => {
    localStorageService.set('sfAttachmentLang', lang);
    $scope.selectedLang = lang;
  };

  /**
   * Trigger Address Book screen
   */
  $scope.openAddressBook = () => {
    // To retrieve them if we cancel address book
    saveSelectedContacts();

    intentService.stash({
      data: {
        location: $location.path(),
        selectedContacts: $scope.selectedContacts
      },
      options: {
        title: t('Contacts'),
        selectMax: featuresEnabledService.canSendSmsToMultiple() ? $rootScope.conf('RetailerMaximumSmsRecipients') : 1,
        selectMin: 1,
        showSearch: true,
        closeOnSelect: !featuresEnabledService.canSendSmsToMultiple(),
        showSelectedBar: featuresEnabledService.canSendSmsToMultiple(),
        showContactCheckbox: featuresEnabledService.canSendSmsToMultiple(),
        chooseField: CONST_ADDRESS_BOOK.PICK_PHONE_NUMBER,
        emptyText: 'You Don\'t Have Any Contacts with Phone Number',
        listType: 'phone',
        mandatoryFields: 'phone',
        disabledType: (($scope.intent || {}).options || {}).disabledType || null
      }
    });
    navService.goTo('/address-book');
  };

  /**
   * Handle intent from previous controller.
   */
  const handleIntent = () => {
    // Bug SF-29762 pop method removes product from intent
    $scope.intent = intentService.get();

    // follow the regular path
    if (!$scope.intent?.data) {
      retrieveSelectedContacts();
      return;
    }

    retrieveSelectedContacts($scope.intent.data.selectedContacts);
    goToThreadIfExists();
  };

  /**
   * Detect if thread exists. If so, it will redirect to text-messaging-thread controller
   */
  const goToThreadIfExists = () => {
    if ($scope.selectedContacts.length !== 1) {
      return;
    }

    const {
      name,
      first_name,
      last_name,
      ID: id,
      choosenField: phoneNumber,
      is_sms_blacklisted,
      retailer_customer_id
    } = $scope.selectedContacts[0];

    const customerData = {};
    //only 1 param is needed to guess the thread
    if (retailer_customer_id) {
      customerData.retailer_customer_id = retailer_customer_id;
    } else if (id) {
      customerData.customer_id = id;
    } else {
      customerData.customer_phone_number = phoneNumber;
    }

    textMessagingService.guessThreadByPhoneNumber(customerData)
      .then(
        (data) => {
          localStorageService.set('sfTextCustomer', {
            id,
            name,
            last_name,
            first_name,
            phoneNumber,
            is_sms_blacklisted,
            retailer_customer_id
          });
          intentService.stash(Object.keys($scope.intent).reduce((filteredIntent, key) => {
            if (key !== 'data') {
              filteredIntent[key] = $scope.intent[key];
            }
            return filteredIntent;
          }, {}));
          const searchParams = {};
          if ($scope.taskId) {
            searchParams.taskId = $scope.taskId;
          }
          navService.replace(`/text-messaging/thread/${data.id}`, searchParams);
        }, () => {
          // Flag is only populated when thread is not active for contact, to prevent double prompt
          $scope.userIsBlacklistedOrUnsubscribed = !!$scope.selectedContacts.find(({ is_sms_blacklisted }) => !!parseInt(is_sms_blacklisted))
            || !!$scope.selectedContacts.find(({ sms_marketing_subscription_flag }) => sms_marketing_subscription_flag === '2');
        });
  };

  /**
   * Parse contact info to display as a tag.
   * @param contact
   * @returns {string}
   */
  $scope.parseContactTag = contact =>
    stringManipulationService.formattedName(contact, customerNameDisplayTemplate)
      ? `${stringManipulationService.formattedName(contact, customerNameDisplayTemplate)} <${contact.choosenField}>`
      : contact.choosenField;

  /**
   * Remove contact and its tag.
   * @param contact
   */
  $scope.deselectContact = contact =>
    $scope.selectedContacts.splice($scope.selectedContacts.findIndex(({ ID }) => ID === contact.ID), 1);

  const saveSelectedContacts = () => {
    localStorageService.set('sfTextMessageSelectedContacts', $scope.selectedContacts);
  };

  const removeSelectedContactsStorage = () => {
    localStorageService.remove('sfTextMessageSelectedContacts');
  };

  const retrieveSelectedContacts = (contacts = []) => {
    $scope.selectedContacts = contacts.length ? contacts : localStorageService.get('sfTextMessageSelectedContacts') || $scope.selectedContacts;
    removeSelectedContactsStorage();
  };

  $scope.showLiveLangSwitcher = () =>
    featuresEnabledService.hasMultilang() &&
    $scope.selectedContacts.length === 1 &&
    !$rootScope.isLoading;

  $scope.showAddressbookButton = () =>
    featuresEnabledService.canSendSmsToMultiple() ||
    !$scope.selectedContacts.length;

  $scope.toggleInputFocus = (bool) => {
    if (!featuresEnabledService.canSendSmsToMultiple() || $scope.selectedContacts.length <= 1) {
      return;
    }

    composeInputIsFocused = bool;
  };

  $scope.displayFocusedStyling = () => composeInputIsFocused && $scope.selectedContacts.length > 1;

  $scope.getMoreLinkCount = () => t('& _count_ more', { count: $scope.selectedContacts.length - 1 });

  /**
   * Saving view data before attachment
   *
   * Used from Attachment Dialog
   */
  $scope.saveData = () => {
    localStorageService.set('sfTextMessageSelectedContacts', $scope.selectedContacts);
  };

  $scope.$watchCollection('selectedContacts', (selectedContacts) => {
    if (selectedContacts[0]?.locale && !localStorageService.get('sfAttachmentLang')) {
      $scope.selectedLang = $scope.selectedContacts[0].locale;
    }
    // Clear blacklisted flag when emptying selectedContacts
    if (!selectedContacts.length && $scope.userIsBlacklistedOrUnsubscribed) {
      $scope.userIsBlacklistedOrUnsubscribed = false;
    }
  });

  init();
});
