angular.module('sfmobileApp').controller('messages-store', (
  $interval,
  $location,
  $q,
  $timeout,
  $rootScope,
  $routeParams,
  $scope,
  chatService,
  activityLogService,
  calendarService,
  contactService,
  facetFilterService,
  featuresEnabledService,
  feedbackService,
  i18nService,
  intentService,
  storageService,
  localStorageService,
  messageService,
  navService,
  firebaseService,
  permissionService,
  pushNotificationService,
  requestsService,
  shareService,
  storeMessageFilterFilter,
  userService,
  loggerService,
  CONST_ACTIVITY_FEED_FILTER_TYPES,
  CONST_ADDRESS_BOOK,
  CONST_PAGINATION,
  CONST_VIRTUAL_APPOINTMENT
) => {

  $scope.i18nOpts = { ns: 'messages' };
  const t = i18nService.t($scope.i18nOpts);
  const currentLocale = i18nService.currentLocale(true);

  // Page size for infinite scroll
  const RENDER_PAGE_SIZE = CONST_PAGINATION.PAGE_SIZE;

  // Pagination
  const defaultPagination = $scope.pagination = {
    page: 0,
    count: 0,
    per_page: CONST_PAGINATION.PAGE_SIZE
  };

  $scope.today = new Date();

  $scope.managerMode = navService.isManagerModeRoute();

  const { requestMessageTypes, requestStatuses } = messageService;

  $scope.requestStatuses = requestStatuses;

  $scope.isFromCustomerRequest = () =>
    $location.search().from === modes.CUSTOMER_REQUEST.id;

  $scope.isAppointment = () =>
    $scope.request && $scope.request.typeOfMessage === requestMessageTypes.APPOINTMENT;

  const modes = {
    MESSAGE: {
      id: 'message',
      url: 'store-messages',
    },
    CUSTOMER_REQUEST: {
      id: 'customer_request',
      url: 'store-requests'
    },
    NEW_LEAD: {
      id: 'new-lead',
      url: 'new-lead',
    },
  };

  $scope.canForwardToCS = $rootScope.conf('CanForwardToCS');

  const activePaths = {
    isStoreMessagesPath: /^\/store-messages/.test($location.path()),
    isStoreRequestsPath: /^(\/manager)?\/store-requests/.test($location.path()),
    isNewLeadPath: /^\/new-lead/.test($location.path())
  };

  const isNewLead = () => $scope.mode === modes.NEW_LEAD.id;

  $scope.nameIsLink = ({ sender: { ID, type }, readonly }) => ID && type !== 'Pre-Customer' && !readonly && !isNewLead();

  const decodeSource = (obj) => {
    if (obj.source?.url?.length) {
      try {
        obj.source.title = decodeURIComponent(obj.source.title);
      } catch (e) {
        console.warn(e);
      }

      try {
        obj.source.url = decodeURIComponent(obj.source.url);
      } catch (e) {
        console.warn(e);
      }
    }
    return obj;
  };

  const storeTempParams = () => {
    // set temporary params to restore list view on navigation
    storageService.setTempItem('tmpReqParams', {
      requestId: $scope.requestId,
      requestSubview: $scope.requestSubview
    });    
  };

  const clearSelectedRequest = () => {
    $scope.requestId = null;
    $scope.requestSubview = null;
  };

  const initController = () => {
    // route details
    $scope.id = typeof($routeParams.id) === 'undefined' ? $scope.requestId || '' : $routeParams.id;
    $scope.subview =typeof($routeParams.subview) === 'undefined' ? $scope.requestSubview || '' : $routeParams.subview;

    $scope.overlay = false;

    // filter by text search variable
    $scope.search = {
      text: '' ,
      delayCounter: 0,
      delayIntervalObj: void 0,
      delayToTriggerSearch: 900, // in milliseconds
    };

    // bool status of bulk mode
    $scope.editMode = false;

    // IDs of messages selected in bulk mode
    $scope.selectedMessages = [];

    // message list
    $scope.data = [];
    // Infinite scroll message list
    $scope.dataShortList = [];

    // messages or CR, mode of work
    $scope.mode = modes.MESSAGE.id;

    // messages or requests, url of current mode
    $scope.modeUrl = modes.MESSAGE.url;

    $scope.isStore = localStorageService.get('sfType');

    $scope.timepicker = [];
    $scope.timepickerIndex = {
      'start': null,
      'end': null
    };
  };

  $scope.getFormattedDate = date => moment(date).format('YYYY-MM-DD');

  const setMode = () => {
    let id;
    let url;

    // messages or requests setup
    if ($scope.requestMode === modes.MESSAGE.id || activePaths.isStoreMessagesPath) {
      ({ id, url } = modes.MESSAGE);
      $scope.emptySelectViewLabel = t('Select a conversation');
    } else if ($scope.requestMode === modes.CUSTOMER_REQUEST.id || activePaths.isStoreRequestsPath) {
      ({ id, url } = modes.CUSTOMER_REQUEST);
      $scope.emptySelectViewLabel = t('Select a request');
    } else if ($scope.requestMode === modes.NEW_LEAD.id || activePaths.isNewLeadPath) {
      ({ id, url } = modes.NEW_LEAD);
      $scope.emptySelectViewLabel = t('Select a lead');
    }

    $scope.mode = id;
    $scope.modeUrl = url;
  };

  let roomIsFreeToUse = false;

  const validateIfRoomIsFreeToUse = (id) => {
    if (!id) {
      return;
    }
    const appointmentRef = new Firebase(`${firebaseService.getFbBaseRetailerUrl(firebaseService.getGeneralFbInformations())}/virtual/${id}`);
    $scope.repsRef = firebaseService.getObjFromReference(appointmentRef.child('reps'), 'array');

    firebaseService.unregisterWatches(Object.values($rootScope.watches || {}));
    $rootScope.watches = {
      roomAvailability: $scope.repsRef.ref.$watch(() => {
        const repIsNotPresent = !$scope.repsRef.ref.length;
        // Comparing user_login is more safe than comparing ID
        // as team mode users will both use the same store_rep ID
        // SF-30341
        const repIsTheSame = $scope.repsRef.ref.length === 1 && $scope.repsRef.ref[0].user_login === $rootScope.currentUser.user_login;

        if (repIsNotPresent || repIsTheSame) {
          roomIsFreeToUse = true;
          return;
        }
        roomIsFreeToUse = false;
      })
    };

    // On first load, if there is no data available for Reps
    // we can assume that the room is free to use
    $scope.repsRef.ref.$loaded((reps) => {
      if (!reps.length) {
        roomIsFreeToUse = true;
      }
    });
  };

  $scope.$watch('request.virtualId', validateIfRoomIsFreeToUse);

  $scope.canJoinVirtualAppointment = () =>
    !$scope.isFromCustomerRequest() &&
    $scope.isAppointment() &&
    !$scope.canAccept() &&
    $scope.request.eventType === appointmentTypes.virtual.value;

  const storeCustomerDataForVirtualAppointment = () => {
    const customerData = !isRepMessageSender($scope.request)
      ? $scope.request.sender
      : $scope.request.receiver;

    const {
      ID,
      type,
      email,
      displayName: name
    } = customerData;

    // If is Pre-Customer, do not use ID, as the customer does not exist yet
    localStorageService.set(CONST_VIRTUAL_APPOINTMENT.LS_KEY_CUSTOMER_DATA, {
      ID: type === 'Pre-Customer' ? null : ID,
      email,
      name
    });
  };

  $scope.joinVirtualAppointment = () =>
    feedbackService.showPrompt(`<strong>${t('Admit Customer')}</strong><br>${t('In order to permit your customer to join the Virtual Appointment, they have to be admitted.')}<br>${t('Once admitted, they can then join from the waiting room.')}`, {
      showOnlyIf: $scope.appointmentCanBeJoined() && !chatService.isVirtualAppointmentActive(),
      buttons: {
        confirm: t('Admit'),
        cancel: 'Cancel'
      }
    }).then(() => {
      storeCustomerDataForVirtualAppointment();
      navService.goTo(`virtual-appointment/${$scope.request.virtualId}`);
    });

  const appointmentIsToday = () => {
    const utcDate = moment.utc($scope.request.dateOfAppointment.fullFormat).format();
    const currentTzDate = moment(utcDate).tz(moment.tz.guess()).format();
    return moment(currentTzDate).isSame(moment(), 'day');
  };

  const appointmentIsInactiveOrCurrent = () =>
    !chatService.isChatActive() || angular.equals(chatService.activeChat.meeting_id, $scope.request.virtualId);

  $scope.appointmentCanBeJoined = () =>
    roomIsFreeToUse &&
    appointmentIsToday() &&
    appointmentIsInactiveOrCurrent() &&
    $scope.request?.statusOfAppointment !== requestStatuses.CANCELLED;

  $scope.canAccept = () => {
    const isAcceptableAppointment =
      !$rootScope.isLoading &&
      $scope.status !== requestStatuses.RESOLVED &&
      $scope.isAppointment() &&
      $scope.request.status !== requestStatuses.ACCEPTED &&
      $scope.request.status !== requestStatuses.PENDING &&
      $scope.request.status !== requestStatuses.CANCELLED &&
      $scope.request.statusOfAppointment &&
      $scope.request.statusOfAppointment !== requestStatuses.CONFIRMED &&
      $scope.request.statusOfAppointment !== requestStatuses.ACCEPTED &&
      $scope.request.statusOfAppointment !== requestStatuses.CANCELLED &&
      !$scope.data.display.hasMeetingPassed &&
      $scope.data.messages.train[0].content !== '2' &&
      !featuresEnabledService.areAppointmentRequestsAutoAccepted();

    return isAcceptableAppointment;
  };

  $scope.getFolderStorageKey = () => {
    var keyMap = {
      message: {
        folder: 'sfLastMessageCategory',
        status: 'sfLastMessageStatus',
        defaultStatus: ''
      },

      customer_request: {
        folder: 'sfLastStoreRequestCategory',
        status: 'sfLastStoreRequestStatus',
        defaultStatus: 'unresolved'
      },

      'new-lead': {
        folder: 'sfLastStoreRequestCategory',
        status: 'sfLastStoreRequestStatus',
        defaultStatus: 'unresolved'
      },
    };

    return keyMap[$scope.mode];
  };

  $scope.getFolderCategoryFromStorage = () => {
    var keys = $scope.getFolderStorageKey();
    $scope.folderCategory = localStorageService.get(keys.folder);
    $scope.status = localStorageService.get(keys.status) || keys.defaultStatus;

    // Remove folder filter if looking at a single request or message
    if ($scope.subview === 'id') {
      $scope.folderCategory = $scope.getDefaultFolderCategory($scope.mode);
    }

    if (!$scope.validateOrDefaultFolder()) {
      $scope.saveFilters();
    }
  };

  $scope.getTitle = () => {
    if (isNewLead()) {
      return t('New Lead');
    }
    if (!$scope.data || !$scope.data.messages) {
      return;
    }
    return $rootScope.utils.extractRequestNumber($scope.data.messages.title);
  };

  $scope.getMessageThreadTitle = () => {
    if (!$scope.data || !$scope.data.messages) {
      return;
    }

    for (var message in $scope.data.messages.train) {
      var customerName = $scope.data.messages.train[message].sender.ID !== $rootScope.currentUser.ID ? $scope.data.messages.train[message].sender.displayName : $scope.data.messages.train[message].receiver.displayName;
      var customerEmail = $scope.data.messages.train[message].sender.ID !== $rootScope.currentUser.ID ? $scope.data.messages.train[message].sender.email : $scope.data.messages.train[message].receiver.email;

      if (customerName.indexOf('@') < 1) {
        var firstLetter = customerName[0];
        var lastName = customerName.lastIndexOf(' ') > 0 ? customerName.substr(customerName.lastIndexOf(' ') + 1, customerName.length) : '';

        return lastName.length > 1 ? firstLetter + '. ' + lastName : customerName;
      }
    }
    return customerEmail;
  };

  $scope.getMessageThreadLength = () => {

    if (!$scope.data || !$scope.data.messages) {
      return;
    }
    return ' (' + $scope.data.messages.train.length + ')';
  };

  $scope.getSubTitle = () => {
    if (!$scope.data || !$scope.data.messages) {
      return '';
    }
    if (isNewLead()) {
      return $scope.data.messages.titleShort;
    }
    return $scope.data.messages.title.replace($scope.getTitle(),'');
  };

  $scope.saveFilters = () => {

    var keys = $scope.getFolderStorageKey();
    $scope.validateOrDefaultFolder();
    localStorageService.set(keys.folder, $scope.folderCategory);
    localStorageService.set(keys.status, $scope.status);
  };

  $scope.validateFolderCategory = function (mode, folder) {
    var folderMap = {
      message: {
        inbox: true,
        sent: true,
        trash: true,
        archive: true
      },
      customer_request: {
        all: true,
        shopper: true,
        appointment: true,
        contactme: true,
      },
      'new-lead': {},
    };

    // HACK - empty string is a valid folder for customer requests...
    if (mode === modes.CUSTOMER_REQUEST.id && folder === null) {
      folder = 'all';
    }

    return folderMap[mode][folder] === true;
  };

  $scope.getDefaultFolderCategory = function (mode) {
    var defaultFolderMap = {
      message: 'inbox',
      customer_request: null
    };

    return defaultFolderMap[mode];
  };

  $scope.validateOrDefaultFolder = () => {
    var validate = $scope.validateFolderCategory($scope.mode, $scope.folderCategory);
    if (!validate) {
      $scope.folderCategory = $scope.getDefaultFolderCategory($scope.mode);
    }

    return validate;
  };

  $scope.defaultFolderCategory = function (mode) {
    var defaultMap = {
      message: 'inbox'
    };

    return defaultMap[mode];
  };

  // set search filter
  $scope.searchGroupe = function (i) {
    $scope.groupSearch = i;
  };

  // enables or disables bulk selection mode
  $scope.setEditMode = function (b) {
    if ($rootScope.isLoading) {
      return;
    }
    $scope.editMode = b;
  };

  // keeps folder category selected
  $scope.setFolderCategory = function (s) {
    $scope.folderCategory = s;
    $scope.folderFilterVisible = false;
    $scope.saveFilters();
    clearSelectedRequest();
    getMessages();
  };

  $scope.setStatus = function (s, noReload) {
    $scope.status = s;
    $scope.folderFilterVisible = false;
    $scope.saveFilters();
    clearSelectedRequest();
    if ($scope.mode !== modes.MESSAGE.id && !noReload) {
      getMessages();
    }
  };

  // on contact selected or deselected in bulk mode
  $scope.bulkChange = function (id) {
    // check for existence | insert if absent, splice if present
    var index = $scope.selectedMessages.indexOf(id);
    if (index === -1) {
      $scope.selectedMessages.push(id);
    } else {
      $scope.selectedMessages.splice(index, 1);
    }
    // localStorageService.set('sfMessagesBulk', angular.toJson($scope.selectedMessages));
  };

  // take bulk actions
  $scope.bulkAction = function (a) {
    var localMessagesObj = [];
    for (var i=0; i<$scope.selectedMessages.length; i++) {
      localMessagesObj.push({'ID': $scope.selectedMessages[i]});
    }

    if (a === 'markRead') {
      messageService.setStoreMessageStatus({'status': 'read', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback(true);
        });
    } else if (a === 'markUnread') {
      messageService.setStoreMessageStatus({'status': 'unread', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback(true);
        });
    } else if (a === 'delete') {
      messageService.setStoreMessageCategory({'category': 'trash', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback();
        });
    } else if (a === 'archive') {
      messageService.setStoreMessageCategory({'category': 'archive', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback();
        });
    } else if (a === 'inbox') {
      messageService.setStoreMessageCategory({'category': 'inbox', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback();
        });
    } else if (a === 'resolved') {
      messageService.setStoreMessageStatus({'status': 'resolved', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback();
        });
    } else if (a === 'unresolved') {
      messageService.setStoreMessageStatus({'status': 'unread', 'messages': localMessagesObj})
        .then(() => {
          $scope.bulkActionCallback();
        });
    } else {
      return;
    }

  };

  $scope.bulkActionCallback = (skipClearSelected = false) => {
    if (!skipClearSelected) {
      clearSelectedRequest();
    }
    $scope.setEditMode(false);
    $scope.overlay = false;
    $scope.selectedMessages = [];
    $scope.data = [];
    $scope.dataShortList = [];
    getMessages();
  };

  // show folder panel selector
  $scope.showFolderFilter = () => {
    $scope.folderFilterVisible = true;
  };

  $scope.toggleFilter = () => {
    $scope.folderFilterVisible = !$scope.folderFilterVisible;
  };

  // hide folder panel selector
  $scope.hideFolderFilter = () => {
    $scope.folderFilterVisible = false;
  };

  $scope.folderFilterClass = function (v) {
    return ($scope.folderFilter === v) ? true : false;
  };

  const { getRequestStatus, appointmentTypes } = requestsService;

  const getButtonMessageStatus = s => t(getRequestStatus(s));

  $scope.getStatusClass = request => getRequestStatus(request).split(' ')[0];

  $scope.getCurrentLang = () => {
    return currentLocale.substring(0, 2);
  };

  $scope.threadHasUnreadMessages = function (train) {
    if (!train.length || train.length === 0) {
      return false;
    }
    return train.some((message) => {
      return message.status === 'unread';
    });
  };

  const getRequestId = messageService.getRequestFullyQualifiedId;

  $scope.requestIdByType = request =>
    $scope.mode === modes.CUSTOMER_REQUEST.id ?
      messageService.getRequestFullyQualifiedId(request) :
      request.threadId;

  // helper function for rendering date
  $scope.getDate = function (i) {
    var day = moment.utc(i, 'YYYY-MM-DD HH:mm:ss');
    var offset = new Date().getTimezoneOffset();
    return moment(day).utcOffset(-1 * offset).fromNow();
  };

  $scope.formatDateTime = (datetime) => {
    var format = 'MMM D[,] h:mm a';

    if (i18nService.isLanguage('fr') || i18nService.isLanguage('nl')) {
      format = 'D MMMM[,] H:mm';
    }
    if (i18nService.isLanguage('ja')) {
      format = 'MMMD日[,] h:mm a';
    }
    return moment.utc(datetime).tz(moment.tz.guess()).format(format);
  };

  // helper function for rendering date
  $scope.getDateFromNow = function (i) {
    var offset = new Date().getTimezoneOffset();
    return moment.utc(i).utcOffset(-1 * offset).fromNow();
  };

  let timer;

  const mediaQueryForSmallScreen = $rootScope.mediaQueryForSmallScreenWidth;

  $scope.handleMouseEnter = () => {
    if (mediaQueryForSmallScreen.matches) {
      return;
    }
    clearTimeout(timer);
    if ($scope.selectedMessages?.length > 0 || $scope.data.messages.threadID) {
      $scope.overlay = true;
    }
  };

  $scope.handleMouseLeave = () => {
    if (mediaQueryForSmallScreen.matches) {
      return;
    }
    timer = setTimeout(() => {
      $scope.overlay = false;
      $scope.$apply();
    }, 300);
  };

  // hide single message options
  $scope.hideOverlay = () => {
    $scope.overlay = false;
  };

  // show overlay with single message options (reply, reply-all etc)
  $scope.showOverlay = (event) => {
    if (event) {
      event.stopPropagation();
    }
    $scope.overlay = true;
  };

  $scope.goToCompose = () => {
    storeTempParams();
    navService.goTo('/' + $scope.modeUrl + '/compose');
  };

  $scope.goToAddNote = () => {
    storeTempParams();
    navService.goTo('/' + $scope.modeUrl + '/add-note/id/' + $scope.id);
  };

  $scope.goToNewTime = () => {
    storeTempParams();
    navService.goTo('/' + $scope.modeUrl + '/new-time/id/' + $scope.id);
  };

  const isChannel = type => $scope.request?.channel === type;

  const displayInvalidEmailError = () =>
    feedbackService.showError(t('The related Contact record no longer has a valid email address.'), {
      buttons: {
        close: t('OK')
      }
    });

  const goToRequestReply = () =>
    navService.goTo(isChannel('text') && $scope.request?.textThreadId
      ? `/text-messaging/thread/${$scope.request.textThreadId}`
      : `/${$scope.modeUrl}/reply-request/id/${$scope.id}`);

  $scope.isRequestReplyDisabled = () => 
    isChannel('email')
      ? $scope.data.messages.owner.emailSubscribe === '2'
      : $scope.data.messages.owner.smsSubscribe === '2';

  $scope.replyToRequest = () => {
    if ($scope.isRequestReplyDisabled()) {
      return;
    }
    if (isChannel('email')) {
      if ($scope.data.display.customerEmail) {
        messageService.clearMessageSharingDataStorage();
      } else {
        displayInvalidEmailError();
      }
    }
    storeTempParams();
    goToRequestReply();
  };

  $scope.goToCancelAppointment = () => {
    const { fullFormat: appointmentDate } = $scope.request.dateOfAppointment;
    if (moment.utc(appointmentDate).isBefore()) {
      return feedbackService.showPrompt(t('Cancel appointment with _name_?', {
        name: $scope.data.display.customerName }), {
        buttons: {
          confirm: t('Proceed'),
          cancel: t('Abandon')
        }
      }).then(() => {
        const { requestId } = $scope.request;
        requestsService.cancelAppointment(parseInt(requestId))
          .then(init);
      });
    }
    storeTempParams();
    intentService.stash({
      data: {
        requestData: $scope.data,
        request: $scope.request,
      }
    });
    navService.goTo('requests/appointments/cancel');
  };

  const getCustomerFromThread = () => {
    const { receiver, sender } = $scope.data.messages.train[0];
    return receiver.type === 'Customer' ? receiver : sender;
  };

  $scope.isMessageReplyDisabled = () => {
    const customer = getCustomerFromThread();
    return customer.emailSubscribe === '2';
  };

  $scope.replyMessage = () => {
    if (!$scope.isMessageReplyDisabled()) {
      storeTempParams();
      const customer = getCustomerFromThread();
      return customer.email
        ? navService.goTo(`/${$scope.modeUrl}/compose/`, {thread: $scope.data.messages.threadID})
        : displayInvalidEmailError();
    }
  };

  $scope.forwardMessage = () => {
    storeTempParams();
    navService.goTo(`/${$scope.modeUrl}/compose/`, {thread: $scope.data.messages.threadID, forward: 1});
  };

  // will archive message
  $scope.archiveMessage = () => {
    messageService.setStoreMessageCategory({'category': 'archive', 'messages': [{'ID': $scope.id}]}).then(() => {
      $scope.overlay = false;
      feedbackService.showSuccess(t('Message Archived'));
      if ($rootScope.isLargeScreenOrTabletLandscape()) {
        if (typeof $scope.onAction === 'function') {
          $scope.onAction();
        }
      } else {
        $scope.goBack();
      }
    });
  };

  $scope.clearSelectionAndReloadList = () => {
    clearSelectedRequest();
    getMessages();
  };

  // will delete message
  $scope.deleteMessage = () => {
    feedbackService.showPrompt(t('Delete this message?'))
      .then(() => {
        messageService.setStoreMessageCategory({'category': 'trash', 'messages': [{'ID': $scope.id}]}).then(() => {
          $scope.overlay = false;
          feedbackService.showSuccess(t('Message Deleted'));
          if ($rootScope.isLargeScreenOrTabletLandscape()) {
            if (typeof $scope.onAction === 'function') {
              $scope.onAction();
            }
          } else {
            $scope.goBack();
          }
        });
      });
  };

  $scope.acceptRequest = function () {
    var magicId = $scope.request.uniqueId;
    var requestId = $scope.request.requestId;
    messageService.acceptRequest({
      requestId: requestId,
      magicId: magicId
    }).then((response) => {
      if (response.status === 'success') {
        messageService.getSingleStoreRequest({
          'id': $scope.id
        }).then((data) => {
          data.messages.train = $scope.setProductsThreadDisplay(data.messages.train);
          $scope.data = data;
          $scope.request = decodeSource(data.messages.storeRequest);
          $scope.data.display = getRequestDisplayData($scope.request);
        });

        feedbackService.showSuccess(t('You accepted the appointment'));
      } else {
        // display error feedback
        var errorMsg = response.message && response.message.length ? response.message : t('Could not accept appointment. Please try again later.');
        feedbackService.showError(errorMsg);
      }
    });
  };

  function refreshDashboardBadges() {
    pushNotificationService.refreshAppIconBadge();
  }

  $scope.switchStatus = (requestId, status) => {
    return messageService.setStoreMessageStatus({
      status,
      messages: [{requestId: requestId}],
      data: ''
    }).then((response) => {
      if (response.status === true) {
        refreshDashboardBadges();

        // display success feedback
        if (status === 'read' ) {
          return;
        }

        status = status === 'unread' ? 'unresolved' : status;
        feedbackService.showSuccess(t('The request has been set as _status_', { status: t(status) }));

        $scope.goBack();
      } else {
        // display error feedback
      }
    });
  };

  $scope.switchRequestStatus = (status) => {
    status = status || 'resolved';

    const requestId = $scope.getCurrentRequestId();
    return $scope.switchStatus(requestId, status);
  };

  $scope.showNonLinkedCustomerModal = () => {
    feedbackService.showPrompt(t('This Request is not linked to a Contact in your Address Book. To link the request, click Cancel then Add to Contacts. To continue, click Resolve.'), {
      buttons: {
        confirm: 'Resolve',
        cancel: 'Cancel'
      }
    }).then(() => {
      $scope.switchRequestStatus('resolved');
    });
  };

  $scope.confirmForwardCustomerService = () => {
    var id = getRequestId($scope.data.messages.storeRequest);
    feedbackService.showPrompt(t('Are you sure you want to forward this customer request to Customer Support?'))
      .then($scope.forwardCustomerService.bind($scope, id));
  };

  $scope.forwardCustomerService = function (request) {
    // Forward the customer request to
    messageService.sendRequestToCustomerService({'threadId': request}).then((data) => {
      if (data.success) {
        feedbackService.showSuccess(t('Customer has been notified that his/her request has been forwarded to Customer Support'));
        if ($rootScope.isLargeScreenOrTabletLandscape()) {
          if (typeof $scope.onAction === 'function') {
            $scope.onAction();
          }
        } else {
          $scope.goBack();
        }
      } else {
        feedbackService.showError(t('Sorry, sending message failed'));
      }
    });
  };

  $scope.forwardLeadToCustomerService = () => {
    feedbackService.showPrompt(t('Are you sure you want to forward this customer request to Customer Support?'))
      .then($scope.acceptLead.bind($scope, true));
  };

  $scope.confirmLead = () => {
    feedbackService.showPrompt(t('You are about to accept a customer request'))
      .then($scope.acceptLead);
  };

  $scope.acceptLead = function (forwardToCS) {
    messageService.claimLead({
      'data[requestId]': $scope.request.eventId,
      'data[dontClaim]': forwardToCS ? 'true' : 'false'
    }).then((data) => {
      refreshDashboardBadges();

      if (forwardToCS === true) {
        return $scope.forwardCustomerService(getRequestId(data));
      }

      var request_id = data['request_type'] + '_' + data['request_id'];
      var path = 'store-requests/id/' + request_id;
      if ($rootScope.isLargeScreenOrTabletLandscape() && $scope.requestSubview) {
        navService.goTo(path);
      } else {
        navService.replace(path);
      }
      navService.replace(path);
      feedbackService.showSuccess(t('You accepted the request'));
    }, function (data) {
      if (data.status === 400 && data.data[0].code === 'lead_rejected') {
        feedbackService.showError(t('Sorry, but that Lead has already been claimed by someone else.'));
      }
    });
  };

  $scope.getCurrentRequestId  = () => {
    return $scope.request.typeOfMessage + '_' + $scope.request.requestId;
  };

  const hasMeetingPassed = ({ dateOfAppointment: { fullFormat } } = $scope.request) => {
    const appointmentTime = moment.utc(fullFormat).tz(moment.tz.guess()).format('YYYY-MM-DD HH:mm:ss');
    return moment().isAfter(appointmentTime);
  };

  $scope.$watch('timepickerIndex.start', () => {
    if ($scope.timepickerIndex.start) {
      for (var i=0; i<$scope.timepicker.length; i++) {
        if ($scope.timepicker[i].value === $scope.timepickerIndex.start.value) {
          $scope.timepickerIndex.end = $scope.timepicker[i + 1];
        }
      }
    }
  });

  $scope.validateNewTime = () => {
    var message, validated = true;

    if (!$scope.newTime || !$scope.newTime.date) {
      message = t('Please provide valid date');
      validated = false;
    } else if ($scope.timepickerIndex.start === null) {
      message = t('Please provide a start time');
      validated = false;
    } else if ($scope.timepickerIndex.end === null) {
      message = t('Please provide an end time');
      validated = false;
    } else if ($scope.timepickerIndex.end.value <= $scope.timepickerIndex.start.value) {
      message = t('Please provide an end time greater than the start time');
      validated = false;
    } else {
      var dp = $scope.newTime.date.split('-');
      var tp = $scope.timepickerIndex.start.value.split(':');
      var dObj = new Date(dp[0], (dp[1] - 1), dp[2], tp[0], tp[1], tp[2]);

      if (dObj <= new Date()) {
        message = t('Please provide a start time in the future');
        validated = false;
      }
    }

    if (!validated) {
      feedbackService.showError(message);
    }

    return validated;
  };

  $scope.proposeNewTime = () => {
    if (!$scope.validateNewTime()) {
      return;
    }

    messageService.changeTime({
      meetingId: $scope.request.requestId,
      timezone: window.jstz.determine().name(),
      choosenDate: $scope.newTime.date,
      choosenTime: $scope.timepickerIndex.start.text,
      choosenDuration: $scope.timepickerIndex.end.text,
      extraInfo: $scope.newTime.message || ''
    }).then(() => {
      $scope.goBack();
      feedbackService.showSuccess(t('Appointment time updated'));
    });

  };

  // helper function that finds first URL in message string
  $scope._findUrls = function (text) {
    if (typeof text === 'undefined' || text === null) {
      return false;
    }
    var urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;<>]*[-A-Z0-9+&@#/%=~_|<>])/ig;
    var result = text.match(urlRegex);
    return (result === null) ? '' : result[0];
  };

  $scope.setProductsThreadDisplay = function (train) {
    for (var i = 0; i < train.length; ++i) {
      if (train[i].products === null) {
        continue;
      }

      // Iterate over all products to handle multiple products
      train[i].productDisplay = {};
      for (var j = 0; j < train[i].products.length; ++j) {
        var sku = train[i].products[j];

        if (!sku) {
          continue;
        }

        var getProduct = function (i, j) {
          shareService.parseUrl.query({
            id: sku
          }, function (data) {
            if (data.name) {
              train[i].productDisplay[j] = data;
            }
          });
        };
        // Enclose i to keep right reference
        getProduct(i, j);
      }
    }

    return train;

  };

  $scope.getDisplayName = function (msg, client) {
    if (msg[client].displayName.indexOf('@') === -1) {
      return msg[client].displayName;
    }

    return msg[client].email;
  };

  const initVars = () => {
    // initial folder to show, should be 'inbox' in live version; used for filtering
    $scope.folderFilter = '';

    // is folder panel selector visible?
    $scope.folderFilterVisible = false;

    // default values for filter, intializing from local storage to reconstruct view after returning from detailed view
    setMode();
    
    $scope.getFolderCategoryFromStorage();

    // store request specifict
    $scope.currentTrainElement = 0;

    $scope.disableInfiniteScroll = false;

    $scope.hasTextMessaging = featuresEnabledService.hasTextMessaging();
    $scope.hasSaveAppointmentToCalendar = featuresEnabledService.hasSaveAppointmentToCalendar();
    $scope.hasAnyTypeOfAppointments = featuresEnabledService.hasAnyTypeOfAppointments();
  };

  const initUserDetails = () => {
    userService.getCurrentUser()
      .then((data) => {
        $scope.me = data;
      })
      .catch((error) => {
        loggerService.logError('[message-store.js] could not fetch current user', error);
      });
  };

  $scope.markMessageAsRead = () => {
    if ($scope.data.messages.train.length && $scope.threadHasUnreadMessages($scope.data.messages.train)) {
      messageService.setStoreMessageStatus({
        'status': 'read', 'messages': [{
          'threadId': $scope.data.messages.threadID,
          'requestId' : '0'
        }]
      }).then(refreshDashboardBadges);
    }
  };

  $scope.goToRequest = (request) => {
    intentService.stashManagerModeListControllerIntent($scope);
    if ($rootScope.isLargeScreenOrTabletLandscape()) {
      clearSelectedRequest();
      $scope.requestMode = modes.CUSTOMER_REQUEST.id;
      $timeout(() => {  
        $scope.requestId = getRequestId(request);
        $scope.requestSubview = 'id';
        request.status = 'read'; // mark request as read
      }, 0);
    } else {
      navService.goTo(`/${$scope.modeUrl}/id/${getRequestId(request)}`, {
        managerMode: $scope.managerMode ? 'true' : 'false',
        from: modes.CUSTOMER_REQUEST.id
      });
    }
  };

  $scope.goToMessage = (message) => {
    if ($rootScope.isLargeScreenOrTabletLandscape()) {
      clearSelectedRequest();
      $timeout(() => {  
        $scope.requestId = message.threadId;
        $scope.requestSubview = 'id';
        message.status = 'read'; // mark request as read
      }, 0);
    } else {
      navService.goTo(`/${$scope.modeUrl}/id/${message.threadId}`);
    }
  };
    

  const getElementId = (message) => {
    if ($scope.mode === modes.CUSTOMER_REQUEST.id) {
      return 'AtRequest-' + (message.requestId || message.request_id);
    }
    return 'AtEmail-' + message.ID;
  };

  const getMessages = () => {
    let promise;

    // reset text search when switching listing mode
    $scope.search.text = loadSearchQuery();
    resetSearchByTextIntervalObj();

    if ($scope.mode === modes.MESSAGE.id) {
      promise = initMessageListing();
    } else if ($scope.mode === modes.CUSTOMER_REQUEST.id) {
      promise = facetFilterService.setup($scope, t('Filter Store Requests'), applyFacetFilter)
        .then(() => initRequestListing());
    } else if (isNewLead()) {
      promise = initNewLeadsListing();
    }

    return promise;
  };

  $scope.isRequestListing = () => {
    return $scope.mode === modes.CUSTOMER_REQUEST.id;
  };

  const initMessageListing = () => {
    if (!$scope.validateFolderCategory($scope.mode, $scope.folderCategory)) {
      $scope.folderCategory = 'inbox';
    }

    $scope.received = messageService.getStoreMessages({
      id: $scope.folderCategory,
      text: $scope.search.text,
    }).then((data) => {
      $scope.data = data.messages;
      $scope.filterData();
    });

    return $scope.received;
  };

  //TODO: remove this mapping when migrating old `getStoreRequests` to new endpoint
  const managerRequestTypes = {
    shopper: requestMessageTypes.SHOPPER,
    appointment: requestMessageTypes.APPOINTMENT,
    contactme: requestMessageTypes.CONTACT,
  };

  const getRequestType = type =>
    $scope.managerMode ? managerRequestTypes[type] : type;

  const initRequestListing = (excludeSearchTextWhenFiltering) => {
    if (!$scope.status) {
      $scope.status = requestStatuses.UNRESOLVED;
    }

    let promise;

    if ($scope.managerMode) {
      const { stores, associates } = facetFilterService.getSelection(true);
      promise = messageService.fetchStoreRequests(stores, associates, getRequestType($scope.folderCategory), $scope.status, $scope.search.text, $scope.pagination.page || 0);
    } else {
      const params = {
        mode: $scope.folderCategory,
        status: $scope.status,
        text: $scope.search.text,
      };
      promise = messageService.getStoreRequests(params).then(data => ({ requests: data.storeRequests }));
    }

    return promise.then(({ requests = [], pagination = defaultPagination }) => {
      if ($scope.search.delayIntervalObj) {
        return;
      }
      $scope.data = requests;
      $scope.pagination = pagination;
      $scope.filterData(excludeSearchTextWhenFiltering);
    });
  };

  /**
   * Resets the search interval and counter.
   */
  const resetSearchByTextIntervalObj = () => {
    $interval.cancel($scope.search.delayIntervalObj);
    $scope.search.delayIntervalObj = null;
    $scope.search.delayCounter = 0;
  };

  $scope.searchByTerm = () => {
    if (angular.isObject($scope.search.delayIntervalObj)) {
      resetSearchByTextIntervalObj();
    }

    $scope.search.delayIntervalObj = $interval(() => {
      // it's used to delay the search request
      $scope.search.delayCounter++;

      // the delay has no been reached yet
      // skip this run
      if ($scope.search.delayCounter <= Math.ceil($scope.search.delayToTriggerSearch / 100)) {
        return;
      }

      // delay time has been reached... it's time to send a request

      // trigger the visual feedback
      $scope.isSearchingByTerm = true;
      resetSearchByTextIntervalObj();

      // trigger the search logic
      let promise = $q.when({});

      if ($scope.mode === modes.MESSAGE.id) {
        promise = initMessageListing();
      } else if ($scope.mode === modes.CUSTOMER_REQUEST.id) {
        promise = initRequestListing(true)
          .catch(() => $scope.filterData(true));
      }

      promise.finally(() => {
        saveSearchQuery($scope.search.text);
        $scope.isSearchingByTerm = false;
      });
    }, 100);
  };

  $scope.filterData = (excludeSearchTextWhenFiltering) => {
    if ($scope.managerMode) {
      excludeSearchTextWhenFiltering = true;
    }
    const searchText = excludeSearchTextWhenFiltering ? '' : $scope.search.text;
    $scope.filteredData = storeMessageFilterFilter($scope.data || [], searchText, $scope.folderCategory, $scope.status, $scope.mode);
    $scope.dataShortList = [];
    $scope.loadMore();
  };

  $scope.loadMore = () => {
    if (!$scope.filteredData || $scope.subview) {
      return;
    }

    var len = $scope.dataShortList.length;

    if (len === $scope.filteredData.length) {
      return;
    }

    const messages = $scope.filteredData.slice(len, len + RENDER_PAGE_SIZE).map((message) => {
      message.display = {
        title: $rootScope.utils.to_trusted(requestsService.getRequestTitle(message)),
        content: $rootScope.utils.to_trusted(message.content || ''),
        date: $scope.getDate(message.date.fullFormat || message.date),
        senderName: requestsService.getRequestCustomerName(message),
        messageStatus: getButtonMessageStatus(message),
        elementId: getElementId(message),
        numberOfMessages: message.message_count || message.numberOfMessages,
      };
      if ($scope.managerMode) {
        Object.assign(message.display, {
          showOwner: true,
          repName: featuresEnabledService.isTeamMode()
            ? message.rep?.store?.name
            : message.rep_display_name || message.rep?.display_name,
          storeName: message.rep.store.name,
        });
      }
      return message;
    });

    Array.prototype.push.apply($scope.dataShortList, messages);

    $scope.disableInfiniteScroll = $scope.dataShortList.length >= $scope.filteredData.length;
  };

  $scope.getRequestsEmptyMessage = () =>
    t($scope.managerMode
      ? 'Your Store Requests will appear here'
      : 'Your Requests will appear here');

  $scope.$watch(() => {
    return angular.toJson({
      signature: $scope.search.text + $scope.folderCategory + $scope.status,
      status: $scope.status,
      searchText: $scope.search.text,
      folderCategory: $scope.folderCategory
    });
  }, function (newVal, oldVal) {
    newVal = angular.fromJson(newVal);
    oldVal = angular.fromJson(oldVal);

    const isSearchableListing =
      $scope.mode === modes.CUSTOMER_REQUEST.id ||
      $scope.mode === modes.MESSAGE.id;

    if (isSearchableListing && newVal.searchText !== oldVal.searchText) {
      $scope.searchByTerm();
    } else if (newVal.signature !== oldVal.signature) {
      saveSearchQuery();
      $scope.filterData();
    }
  });

  const initNewLeadsListing = () => {
    $scope.initEmptyPromise();
    return $q.when();
  };

  $scope.initEmptyPromise = () => {
    $scope.received = {
      $promise: $.Deferred().resolve()
    };
  };

  const processAttachments = (attachments) => {
    const processedAttachments = attachments.map(attachment => ({
      url: attachment,
      type: 'photo'
    }));
    return processedAttachments;
  };

  const getSingleMessage = (id) => {
    id = id || $scope.id;
    return messageService.getStoreSingleMessage({ id })
      .then((data) => {
        $scope.data = data;
        $scope.data.display = {
          threadTitle: $scope.getMessageThreadTitle(),
          threadLength: $scope.getMessageThreadLength(),
          showDeleteButton: $scope.showDeleteButton()
        };
        $scope.markMessageAsRead();
        $scope.data.messages.train = $scope.setProductsThreadDisplay(data.messages.train).map(function (msg) {
          msg.display = {
            date: $scope.formatDateTime(msg.date.fullFormat),
            customerName: $scope.getDisplayName(msg, 'sender'),
            title: $rootScope.utils.to_trusted(msg.title),
            content: $rootScope.utils.to_trusted(msg.content),
            senderIsRep: $scope.senderIsRep(msg),
            attachments: processAttachments(JSON.parse(msg.attachment) || [])
          };
          return msg;
        });
      }, () => {
        $scope.goBack();
      });
  };

  $scope.showImagePreview = attachment => feedbackService.showImagePreviewWithZoom(attachment);

  const getSingleRequest = () => {
    const id  = $scope.id;
    const {
      data: {
        requestData = {},
      } = {}
    } = intentService.get() || {};
    const {
      messages: {
        storeRequest
      } = {},
    } = requestData;

    if (storeRequest && getRequestId(storeRequest) === id) {
      processRequestData(requestData);
      return;
    }


    return messageService.getSingleStoreRequest({ id })
      .then(processRequestData, () => {
        $scope.setStatus('', true);
        $scope.goBack();
      });
  };

  const processRequestData = (data) => {
    $scope.data = data;
    $scope.request = decodeSource(data.messages.storeRequest);
    const repId = ($scope.request.reassignedUser || (
      $scope.request.sender.type === 'WordPress'
        ? $scope.request.sender
        : $scope.request.receiver
    )).ID;
    const isDifferentUser = repId !== $rootScope.currentUser.ID;
    const isManagerAndUp = !permissionService.isUser() && !permissionService.isStoreManager();
    $scope.request.readonly = featuresEnabledService.isTeamMode()
      ? (isManagerAndUp && !featuresEnabledService.isSellingMode())
      : ($scope.managerMode || isDifferentUser);
    $scope.data.display = getRequestDisplayData($scope.request);
    data.messages.train = $scope.setProductsThreadDisplay(data.messages.train);

    const hasUnreadMessages = $scope.request.status === 'unread' ||
      $scope.threadHasUnreadMessages(data.messages.train);

    if (hasUnreadMessages && !$scope.request.readonly) {
      $scope.switchRequestStatus('read');
    }

    if ($scope.isAppointment() && $scope.hasSaveAppointmentToCalendar) {
      calendarService.hasPermission().then((result) => {
        if (result) {
          calendarService.findAppointmentEvent($scope.request).then((event) => {
            if (event) {
              syncAppointmentCalendarEvent($scope.request, event);
            }
          });
        }
      });
    }
  };

  const getRequestDisplayData = (request) => {
    const requestDate = request.date.fullFormat;
    let appointmentDate;
    let meetingPassed;
    if (request.dateOfAppointment) {
      appointmentDate = request.dateOfAppointment.fullFormat;
      meetingPassed = hasMeetingPassed(request);
    }
    const repName = requestsService.getRequestUserName(request);
    const storeName = (request.store || {}).name;
    return {
      title: $scope.getTitle(),
      subTitle: $scope.getSubTitle(),
      date: $scope.formatDateTime(requestDate),
      appointmentDate: appointmentDate ? $scope.formatDateTime(appointmentDate) : '',
      hasMeetingPassed: meetingPassed,
      customerName: $rootScope.utils.getCustomerNameOrAssignGenericName($scope.data, $scope.mode),
      budget: request.minBudget ? $scope.utils.formatCurrency(request.minBudget, currentLocale) : '',
      isResolved: messageService.isResolved(request),
      repName,
      storeName,
      showOwner: request.readonly && repName && storeName,
      customerEmail: $scope.isRequestMadeByRep() ? request.receiver.email : request.sender.email,
      customerPhone: $scope.isRequestMadeByRep() ? request.receiver.phone : request.sender.phone
    };
  };

  $scope.isRequestMadeByRep = () => $scope.data.messages.owner && $scope.data.messages.owner.type !== 'Customer' && $scope.data.messages.owner.type !== 'Pre-Customer';

  $scope.getSingleNewLead = () => {
    return messageService.previewNewLead({
      type: $scope.requestType || $routeParams.type,
      id: $scope.id,
    }).then((data) => {
      $scope.data = data;
      $scope.request = decodeSource($scope.data.messages.storeRequest);

      if ($scope.request.notes && isNewLead()) {
        $scope.request.notes = $rootScope.utils.to_trusted($scope.request.notes);
      }

      if ($scope.request.requestType === 'appointment') {
        const appointmentDate = moment($scope.request.dateOfAppointment.fullFormat).utc();
        $scope.request.dateOfAppointment.fullFormat = appointmentDate.format('YYYY-MM-DD HH:mm:ss');
      }

      $scope.data.display = getRequestDisplayData($scope.request);
    }, () => {
      $scope.goBack();
    });
  };

  const requestTypeToId = ({ typeOfMessage }) => {
    if (typeOfMessage === requestMessageTypes.SHOPPER) {
      return CONST_ACTIVITY_FEED_FILTER_TYPES.PERSONAL_SHOPPER;
    } else if (typeOfMessage === requestMessageTypes.APPOINTMENT) {
      return CONST_ACTIVITY_FEED_FILTER_TYPES.APPOINTMENT;
    } else if (typeOfMessage === requestMessageTypes.CONTACT) {
      return CONST_ACTIVITY_FEED_FILTER_TYPES.EMAIL;
    } else {
      return 0;
    }
  };

  const initSingleView = () => {
    let promise;
    if ($scope.mode === modes.MESSAGE.id) {
      promise = getSingleMessage();
    } else if ($scope.mode === modes.CUSTOMER_REQUEST.id) {
      promise = getSingleRequest();
    } else if (isNewLead()) {
      promise = $scope.getSingleNewLead();
    }

    if ($scope.request && ($scope.currentUser.ID === $scope.request.receiver.ID || $scope.currentUser.store.store_user_id === $scope.request.receiver.ID)) {
      $q.when(promise).then(() => {
        activityLogService.trackActivityFeed({
          type: requestTypeToId($scope.request),
          status: 'read',
          chatId: $scope.request.requestId,
          userIdOrName: $scope.currentUser.ID,
          customerIdOrEmail: $scope.request.sender.ID
        });
      });
    }
  };

  const isSingleView = () => $scope.subview === 'id';

  $scope.getHeaderTitle = () => {
    if ($scope.mode === modes.MESSAGE.id) {
      return `${t('Emails')} (${t($scope.folderCategory)})`;
    }

    if ($scope.mode === modes.CUSTOMER_REQUEST.id) {
      if ($scope.hasViewModeSwitcher) {
        return t('Requests');
      }

      if ($scope.managerMode) {
        return t('Store Requests');
      }

      return t($scope.status === requestStatuses.RESOLVED
        ? 'Resolved'
        : 'Unresolved');
    }
  };

  $scope.isTeamManager = !featuresEnabledService.isRepMode() && permissionService.isManager();
  const isRepManagement = featuresEnabledService.isRepMode() && (permissionService.isStoreManager() || permissionService.isManager());

  $scope.hasViewModeSwitcher = featuresEnabledService.isSellingMode() &&
    ($scope.isTeamManager || isRepManagement) &&
    !activePaths.isStoreMessagesPath;

  $scope.setManagerMode = (bool) => {
    if ($scope.managerMode === bool) {
      return;
    }
    clearSelectedRequest();
    $scope.managerMode = bool;
    getMessages();
  };

  // controller init function
  const init = () => {    
    initController();
    initVars();

    if (isSingleView()) {
      localStorageService.remove('sfCurrentCustomer');
      localStorageService.set('sfCurrentPage', $location.$$path.substring(1));
    }

    initUserDetails();

    if (isSingleView()) {
      initSingleView();
    } else {
      // restore state only for list view.
      const storedParams = storageService.getTempItem('tmpReqParams');
      if (storedParams) {
        $scope.requestId = storedParams.requestId;
        $scope.requestSubview = storedParams.requestSubview;
      }
  
      intentService.popManagerModeListControllerIntent($scope, defaultPagination);
      getMessages();
    }

    $scope.timepicker = $rootScope.utils.buildTimePicker();
  };



  $scope.showDeleteButton = () =>
    $scope.data && $scope.data.messages && $scope.data.messages.train[0].category !== 'trash';

  $scope.senderIsRep = (msg) => {
    return msg.sender.ID === $rootScope.currentUser.ID && msg.sender.displayName === $rootScope.currentUser.first_name + ' ' + $rootScope.currentUser.last_name;
  };

  $scope.canReassignAppointment = (request) => {
    if (featuresEnabledService.isTeamMode()) {
      return false;
    }
    if (!request) {
      return false;
    }
    if (!request.readonly) {
      return false;
    }
    if (request.typeOfMessage !== requestMessageTypes.APPOINTMENT) {
      return false;
    }
    if (request.statusOfAppointment === requestStatuses.CANCELLED) {
      return false;
    }
    if (!request.receiver || !request.receiver.ID) {
      return false;
    }
    if (hasMeetingPassed(request)) {
      return false;
    }
    return permissionService.canReassignAppointment();
  };

  const clearData = () => {
    if (!isSingleView()) {
      clearSearchQuery();
      facetFilterService.reset();
    }
    firebaseService.unregisterWatches(Object.values($rootScope.watches || {}));
  };

  $scope.goBack = () => {
    clearData();
    navService.goBack();
  };

  $scope.goToDashboard = () => {
    clearData();
    navService.goToDashboard();
  };

  const clearCurrentCustomer = ({ sender, receiver }) => {
    const { ID } =  $scope.isRequestMadeByRep() ? receiver : sender;
    const currentCustomer = localStorageService.get('sfCurrentCustomer');

    if (currentCustomer && currentCustomer.ID !== ID) {
      localStorageService.remove('sfCurrentCustomer');
    }
  };

  const isRepMessageSender = ({ sender: { type }}) => type !== 'Customer' && type !== 'Pre-Customer';

  $scope.goToContact = (request) => {
    clearCurrentCustomer(request);
    storeTempParams();
    // Use receiver ID if customer did not send the appointment
    navService.goTo(`/contacts/view/${isRepMessageSender(request) ? request.receiver.ID : request.sender.ID}`);
  };

  $scope.getProperId = function (str) {
    str = str || 'all';

    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  $scope.addToContacts = () => {
    contactService.preCustomer = angular.extend({}, $scope.data.messages.owner, {
      requestType: $scope.request.typeOfMessage,
    });
    navService.goTo('/contacts/pre-customer-actions');
  };

  $scope.viewTextThread = function (request) {
    if (!request.textThreadId) {
      return;
    }
    intentService.stash({
      readonlyTextThread: request.readonly,
    });
    navService.goTo('/text-messaging/thread/' + request.textThreadId);
  };

  $scope.getRequestsOrdering = () => {
    if ($scope.status === 'resolved') {
      return ['-lastdate'];
    }

    return ['-new','-lastdate'];
  };

  $scope.goToPage = (page) => {
    $scope.pagination.page = page;
    initRequestListing();
  };

  const reassignAppointment = (request, selectedRep) => {
    if (!selectedRep) {
      return false;
    }

    const { receiver, reassignedUser } = request;
    const receiverId = parseInt((reassignedUser || receiver).ID);
    const repId = parseInt(selectedRep.ID);
    if (receiverId === repId) {
      return false;
    }

    return feedbackService.showPrompt(t('Reassign appointment to _associateName_?', {
      associateName: selectedRep.display_name,
    }))
      .then(() => {
        const { user_phone_number } = selectedRep;
        const repNoSms = request.channel === 'text' &&
          !(user_phone_number && user_phone_number.phone_number);
        const requestId = messageService.getRequestId(request);
        const currentUserId = $rootScope.currentUser.ID;

        return feedbackService.showPrompt(t("The appointment confirmation method is SMS, but this associate doesn't have an SMS number. Reassign anyway?"), {
          showOnlyIf: repNoSms
        }).then(() => {
          return requestsService.reassignAppointment(requestId, repId, currentUserId);
        });
      })
      .then(() => {
        intentService.clear(); // clear intent to let appointment details page reload
        navService.goBack();
        return false;
      }, ({ detail } = {}) => {
        if (detail) {
          feedbackService.showError(detail);
        }
        return false;
      });
  };

  $scope.openAddressBookToReassignAppointment = () => {
    intentService.stash({
      data: {
        requestData: $scope.data,
        request: $scope.request,
      },
      options: {
        title: t('Reassign Appointment'),
        mode: CONST_ADDRESS_BOOK.modes.ASSOCIATES,
        selectMax: 1,
        showSearch: false,
        showSelectedBar: false,
        showContactCheckbox: false,
        emptyText: t('No associates can be assigned to this appointment'),
        onSelect: reassignAppointment.bind(null, $scope.request),
      }
    });
    navService.goTo('/address-book');
  };

  const applyFacetFilter = () => {
    $scope.pagination = defaultPagination;
    clearSearchQuery();
    initRequestListing();
  };

  const LS_SEARCH = 'sfMessagesSearchQuery';

  const saveSearchQuery = (query) => {
    if (!query) {
      localStorageService.remove(LS_SEARCH);
      return;
    }
    localStorageService.set(LS_SEARCH, query);
  };

  const loadSearchQuery = () => localStorageService.get(LS_SEARCH) || '';

  const clearSearchQuery = () => {
    $scope.search.text = '';
    saveSearchQuery();
  };

  const requestCalendarPermission = () =>
    calendarService.requestPermission(t("If you'd like to save appointments to your calendar, please give access to Salesfloor on the next screen."));

  const syncAppointmentCalendarEvent = (appointment, event, createIfUnchanged) =>
    calendarService.isAppointmentChanged(appointment, event).then((appointmentChanged) => {
      if (appointmentChanged) {
        return appointment.statusOfAppointment === requestStatuses.CANCELLED
          ? feedbackService.showPrompt(t('This appointment has been cancelled. Remove it from your calendar?'), {
            buttons: {
              confirm: t('Remove Event'),
              cancel: t('Do Nothing'),
            }})
            .then(
              () => calendarService.deleteAppointmentEvent(appointment, event)
            )
          : feedbackService.showPrompt(t('This appointment has changed. Update your calendar?'), {
            buttons: {
              confirm: t('Update Event'),
              cancel: t('Save as New'),
            }})
            .then(
              () => calendarService.modifyAppointmentEvent(appointment, event),
              () => calendarService.createAppointmentEvent(appointment)
            );
      }
      if (createIfUnchanged) {
        return feedbackService.showPrompt(t('This appointment has already been saved to your calendar. Do you want to save a new copy?'), {
          buttons: {
            confirm: t('Save a Copy'),
            cancel: t('No'),
          },
        }).then(() => calendarService.createAppointmentEvent(appointment));
      }
    });

  $scope.saveAppointmentToCalendar = (appointment) => {
    requestCalendarPermission().then((hasPermission) => {
      if (hasPermission === null) {
        return;
      }
      if (!hasPermission) {
        feedbackService.showError(t("Salesfloor doesn't have permission to access the calendar. If you'd like to save appointments to your calendar, please give access to Salesfloor in the device settings."), {
          buttons: { close: 'OK' },
        });
        return;
      }
      calendarService.findAppointmentEvent(appointment)
        .then(event => event
          ? syncAppointmentCalendarEvent(appointment, event, true)
          : calendarService.createAppointmentEvent(appointment)
        );
    });
  };

  /**
   * Copy message to clipboard
   * @param {string} message
   * @returns {promise}
   * @resolves {undefined}
   */
  $scope.copyMessage = async (message) => {
    // Create the plain text content
    let plainTextContent = $rootScope.utils.htmlToPlainText(message);
    try {
      await $rootScope.utils.copyMessageToClipboard(plainTextContent, message);
      feedbackService.showSuccess(
        t('The contents of this message have been copied to your clipboard.'), {
          buttons: { close: t('Dismiss') },
        }
      );
    } catch (err) {
      console.error(err);
    }
  };

  init();
});
