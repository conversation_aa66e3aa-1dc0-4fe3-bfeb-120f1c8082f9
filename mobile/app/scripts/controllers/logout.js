angular.module('sfmobileApp').controller('logout', (
  $location,
  $window,
  $rootScope,
  apiService,
  firebaseService,
  chatService,
  productsService,
  pushNotificationService,
  facetFilterService,
  requestsPollingService,
  secureStorageService,
  draftService,
  loggerService,
  loginService,
  featuresEnabledService,
  sessionManagerService,
  CONST_STORAGE,
  CONST_SF_APP
) => {
  const processLogout = () => {
    // skip BO logout for dev environment
    if (window.sfApp === CONST_SF_APP.DESKTOP && $location.host() !== 'localhost') {
      onMobileWebLogout();
    }

    chatService.destroy();      
    loggerService.Sentry.setUser(null);
    loggerService.destroy();
    pushNotificationService.unregister();
    // we need to stop interval to call refreshAppIconBadge for previous user session.
    requestsPollingService.destroy();
    firebaseService.destroy();
    productsService.reset();
    facetFilterService.reset();
    sessionManagerService.destroy();

    if ($window.OAuth) {
      OAuth.clearCache('facebook');
      OAuth.clearCache('twitter');
    }

    $rootScope.initPromise = null;
    $rootScope.loggedIn = false;
    $rootScope.currentUser = null;
    $rootScope.accessToken = null;

    apiService.setAuthorizationHeader('');
    draftService.deleteAllDraftsOnDevice();
    $rootScope.postMessageSfApp(
      {
        source: 'sf-app',
        payload: {
          action: 'unregisterFirebaseCloudMessaging',
        },
      }
    );

    secureStorageService
      .removeItem(CONST_STORAGE.ACCESS_TOKEN)
      .then(() => {
        // storage will be reset when we will reload config
        $location
          .path('/connect')
          .replace();
      });
  };

  const onMobileWebLogout = () => {
    // Logout from the backoffice aswell
    if (featuresEnabledService.isBackofficePrimaryLoginThroughAppEnabled()) {
      loginService.logoutOfBackoffice();
    }
  };

  const init = async () => {
    // Fix race-condition when loading page directly from backoffice
    await $rootScope.waitForInitPromise();

    // log case when the route called without being authentificated.
    if (!$rootScope.loggedIn) {
      loggerService.logError('[logout] Logout route called without being authentificated.');
    }

    apiService
      .logout({
        device_token: pushNotificationService.getRegistrationToken()
      })
      .then(processLogout)
      .catch(processLogout);
  };

  init();
});
