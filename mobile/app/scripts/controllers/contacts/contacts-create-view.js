angular.module('sfmobileApp').controller('ContactsCreateView', (
  $scope,
  navService,
  $rootScope,
  i18nService,
  $routeParams,
  intentService,
  contactService,
  messageService,
  feedbackService,
  localStorageService,
  featuresEnabledService,
  stringManipulationService,
  CONST_CONTACTS,
  CONST_NAME_TEMPLATE
) => {
  const t = i18nService.t($scope.i18nOpts);

  const { PHONE, EMAIL, TEXT_MESSAGE } = CONST_CONTACTS.PREFERENCE_TYPE;

  $scope.Phone = PHONE.value;

  $scope.Email = EMAIL.value;

  $scope.TextMessage = TEXT_MESSAGE.value;

  $scope.taskId = $routeParams.taskId;

  $scope.isToggleFavoriteDisabled = false;

  $scope.isPiiObfuscationEnabled = featuresEnabledService.isPiiObfuscationEnabled();

  $scope.getMarketingText = form => t(`_type_ ${form}`, { type: t(featuresEnabledService.isTeamMode() ? 'Store' : 'Associate') });

  //display name template ({Fn Ln} / {Ln Fn})
  $scope.customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);

  $scope.getSubscriptionText = subscriptionStatus => contactService.getSubscriptionText(subscriptionStatus);

  $scope.getCustomerName = ({ first_name, last_name }) => {
    if ($scope.customerNameDisplayTemplate) {
      return stringManipulationService.formatName($scope.customerNameDisplayTemplate, first_name, last_name);
    }
    if (first_name) {
      return last_name ? `${first_name} ${last_name}` : first_name;
    }

    return false;
  };

  $scope.getEventLabel = ({ label }) => t(stringManipulationService.capitalize(label));

  $scope.getEventDate = ({ day, month, year }) => {
    let format;

    if (!month) {
      return;
    } else if (day && year) {
      format = 'LL';
    } else if (year) {
      format = i18nService.isLanguage('ja')
        ? 'YYYY年MMMM'
        : 'MMMM YYYY';
      day = 1;
    } else if (day) {
      format = i18nService.isLanguage('fr')
        ? 'D MMMM'
        : i18nService.isLanguage('ja')
          ? 'MMMMD日'
          : 'MMMM D';
      year = 2000;
    } else {
      format = 'MMMM';
      day = 1;
      year = 2000;
    }

    return moment(`${year}/${month}/${day}`).format(format);
  };

  $scope.goToSendMessage = (user, email) => {
    // always use just email for retailer customer
    if (user.type === 'retailer-customer') {
      email = user.email;
    }
    messageService.goToComposeMessage(email ? { email } : user);
  };

  $scope.goBackToContact = () => {
    localStorageService.remove('sfCurrentCustomerV2');
    navService.goBack();
  };

  $scope.addAddress = () => {
    navService.goTo(`/contacts/address/${$scope.user.id}/create`);
  };

  $scope.editAddress = (address) => {
    intentService.stash(address);
    navService.goTo(`/contacts/address/${$scope.user.id}/edit/${address.id}`);
  };

  $scope.editSocial = () => {
    const socials = $scope.user.socials;
    if (socials.length > 0) {
      intentService.stash({
        type: socials[0].type,
        socials,
      });
    }
    navService.goTo(`/contacts/social/${$scope.user.id}`);
  };

  $scope.editEvent = () => {
    navService.goTo(`/contacts/events/${$scope.user.id}`);
  };

  $scope.editAttributes = (panel) => {
    intentService.stash(panel);
    navService.goTo('/contacts/attributes');
  };

  $scope.openSocial = (url, username) => {
    const socialUrl = `${/^https?:\/\//.test(url) ? '' : 'https://'}${url}${username}`;
    window.open(socialUrl, '_blank', 'location=yes');
  };

  const updateStorageOnToggleFavorites = (isFavoriteContact) => {
    contactService.updateSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER, 'is_favorite_contact', isFavoriteContact);
    contactService.updateSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER_V2, 'is_favorite_contact', isFavoriteContact);
    contactService.clearSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA, 'data');
  };

  const updateCustomerFavorites = (params, isFavoriteContact) => {
    contactService.updateCustomerFavorites(params).then(() => {
      $scope.user.is_favorite_contact = isFavoriteContact;
      updateStorageOnToggleFavorites(isFavoriteContact);
    }).catch(() => {
      feedbackService.showError(t('Contact could not be added as a favorite, please try again.'));
    }).finally(() => {
      $scope.isToggleFavoriteDisabled = false;
    });
  };

  $scope.onToggleFavoriteContact = (isFavoriteContact) => {
    if (!$scope.user && !$scope.user.id) {
      return;
    }
    const params = {
      customer_id: Number($scope.user.id),
      is_favorite_contact: isFavoriteContact
    };

    $scope.isToggleFavoriteDisabled = true;
    updateCustomerFavorites(params, isFavoriteContact);
  };

  navService.addAndroidBackButtonListener($scope.goBackToContact);

  $scope.goToTags = () => navService.goTo('contacts/tags/' + $scope.user.id);
});
