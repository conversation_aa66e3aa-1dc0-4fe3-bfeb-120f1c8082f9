angular.module('sfmobileApp').controller('contacts', (
  $scope,
  $window,
  $location,
  $timeout,
  $routeParams,
  $rootScope,
  storageService,
  $q,
  $route,
  localStorageService,
  messageService,
  contactService,
  i18nService,
  featuresEnabledService,
  feedbackService,
  navService,
  configService,
  productsService,
  associateRelationshipsService,
  stringManipulationService,
  contactFacetFilterService,
  barcodeScannerService,
  loggerService,
  CONST_TASKS,
  CONST_SF_APP,
  CONST_CONTACTS,
  CONST_NAME_TEMPLATE,
  CONST_PAGINATION
) => {

  $scope.i18nOpts = { ns: 'contacts' };
  const t = i18nService.t($scope.i18nOpts);

  const outfitsSectionLabel = configService.get('outfitsSectionLabel');

  /**
   * Both Views Variables
  */

  $scope.fromDashboard = $location.search().fromDashboard === 'true';

  const { GROUP_TASK } = CONST_TASKS.TASK_TYPE;

  const { type, taskId } = $routeParams;

  $scope.taskId = taskId;

  const isFromGroupTask = () => type === GROUP_TASK;

  $scope.savedContactId = storageService.getTempItem('contactId', true);

  $scope.isInContactList = () => $scope.savedContactId && !!$scope.contactParams;

  $scope.id = $routeParams.id || $scope.savedContactId || $scope.contactParams?.contactId || 0;

  /**
   * Contact Listing Variables
  */

  $scope.search = {};
  $scope.favoritesFilterIsActive = false;

  $scope.isInitialSearch = {
    contacts: true,
    customers: true
  };

  $scope.groupList = null;

  $scope.editMode = false;

  $scope.selectedContacts = [];

  $scope.overlay = false;

  $scope.contactsList = [];

  $scope.viewMode = 'contacts';

  $scope.pagination = {};

  $scope.searchQuery = {};

  //display name template ({Fn Ln} / {Ln Fn})
  $scope.customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);
  /**
   * Single Contact Variables
  */

  $scope.user = {};

  $scope.contactStats = {};

  $scope.contactSectionLinks = [
    {
      label: t('Transaction History'),
      onClick() {
        const id = $scope.isFromContact() ? $scope.user.relatedRetailerCustomers[0].customer_id : $scope.user.customer_id;
        return navService.goTo(`contacts/transactions/history/${id}`);
      },
      display() {
        return $scope.features.hasClienteling() && (($scope.isFromContact() && $scope.contactIsLinked($scope.user)) || !$scope.isFromContact());
      },
      getCount() {
        return false;
      }
    }, {
      label: t('Salesfloor Transactions'),
      onClick() {
        const id = $scope.isFromContact() ? $scope.user.ID : $scope.user.relatedCustomers[0].ID;
        return navService.goTo(`contacts/transactions/default/${id}`);
      },
      display() {
        return $scope.isFromContact() || $scope.contactIsLinked($scope.user);
      },
      getCount() {
        return false;
      }
    }, {
      label: t('Tasks'),
      onClick() {
        const customer = {
          id:    $scope.user.ID,
          name:  $scope.user.name.trim() ? $scope.user.name : $scope.user.email || $scope.user.phone,
          firstName: $scope.user.first_name,
          lastName: $scope.user.last_name,
          count: $scope.user.count_tasks,
        };
        localStorageService.set('sfTasksTempCustomerFilter', customer);
        return navService.goTo('tasks');
      },
      display() {
        return $scope.user && $scope.isFromContact();
      },
      getCount() {
        return $scope.user.count_tasks;
      }
    }, {
      label: t('Notes'),
      onClick() {
        return navService.goTo(`contacts/notes/${$scope.user.ID}`);
      },
      display() {
        return $scope.user && $scope.isFromContact();
      },
      getCount() {
        return $scope.user.count_customer_notes;
      }
    }, {
      label: t('Activity Log'),
      onClick() {
        return navService.goTo('contacts/activity-log/' + $scope.user.ID);
      },
      display() {
        return $scope.user && $scope.isFromContact() && $scope.retailerHasCustomerActivityFeed;
      },
      getCount() {
        return false;
      }
    }
  ];

  //needed for properly display of the advanced search on a split view
  $rootScope.$on('advancedSearchIsVisible', (event, isVisible) => {
    $scope.advancedSearchIsVisible = isVisible;
  });

  $scope.isLimitedVisibility = false;

  $scope.hasPreCustomerData = !!contactService.preCustomer;

  $scope.showBarcode = featuresEnabledService.hasContactsBarcodeScanner;
  $scope.canAddContacts = featuresEnabledService.canAddContacts();

  $scope.isToggleFavoriteDisabled = false;

  let isLoadingContactsCustomers = false;

  const { UNSUBSCRIBED } = CONST_CONTACTS.CONSENT_TYPE;

  const handleAndroidBackButtonOnScan = () => navService.removeAndroidBackButtonListener(handleAndroidBackButtonOnScan);

  $scope.loadScanner = () => {
    navService.addAndroidBackButtonListener(handleAndroidBackButtonOnScan);
    barcodeScannerService.loadScanner({
      successCallback: ({ text }) => {
        text && navService.removeAndroidBackButtonListener(handleAndroidBackButtonOnScan);
        $scope.search.searchText = text;
        $scope.search.search();
      }
    });
  };

  const insertStyliticsSection = () => {
    if (!featuresEnabledService.isOutfitsEnabled()) {
      return;
    }
    const outfitsContactSection = {
      label: t(outfitsSectionLabel),
      onClick() {
        const id = $scope.isFromContact() ? $scope.user.relatedRetailerCustomers[0].customer_id : $scope.user.customer_id;
        navService.goTo(`contacts/looks/${id}`);
      },
      display() {
        return !$scope.isFromContact() || $scope.contactIsLinked($scope.user);
      },
      getCount() {
        return false;
      }
    };
    if ($scope.contactIsLinked($scope.user)) {
      return  $scope.contactSectionLinks.push(outfitsContactSection);
    } else if (!$scope.isFromContact()) {
      return  $scope.contactSectionLinks.splice(1, 0, outfitsContactSection);
    }
  };
  $scope.isSplitView = $rootScope.isLargeScreenOrTabletLandscape();
  const setIsSplitView = () => $scope.isSplitView = $rootScope.isLargeScreenOrTabletLandscape();

  const mediaQueryForSmallScreen = $rootScope.mediaQueryForSmallScreenWidth;
  const mediaQueryForLandscapeOrientation = $rootScope.mediaQueryForLandscapeOrientation;
  const mediaQueryForTabletWidth = $rootScope.mediaQueryForTabletWidth;
  const isTabletLandscapeOrientation = () => $rootScope.isTabletLandscapeOrientation();

  $scope.sfAppIsDesktop = $window.sfApp === CONST_SF_APP.DESKTOP;
  $scope.sfAppIsMobile = $window.sfApp === CONST_SF_APP.MOBILE;

  const onScreenResize = () => {
    $scope.$applyAsync(() => {
      if (($scope.sfAppIsDesktop && !mediaQueryForSmallScreen.matches)
        || ($scope.sfAppIsMobile && isTabletLandscapeOrientation())) {
        $scope.addContactImage = './images/contacts/contact-add-blue.png';
      } else {
        $scope.addContactImage = './images/contacts/contact-add.svg';
      }
      setIsSplitView();
    });
  };

  mediaQueryForSmallScreen.addEventListener('change', onScreenResize);
  mediaQueryForLandscapeOrientation.addEventListener('change', onScreenResize);
  mediaQueryForTabletWidth.addEventListener('change', onScreenResize);

  $scope.$on('$destroy', () => {
    mediaQueryForSmallScreen.removeEventListener('change', onScreenResize);
    mediaQueryForLandscapeOrientation.removeEventListener('change', onScreenResize);
    mediaQueryForTabletWidth.removeEventListener('change', onScreenResize);
  });

  /**
   * Both Views Methods
   */

  const init = () => {
    if ($scope.fromDashboard) {
      $location.search('from', null);
    }

    initFeatures();
    onScreenResize();
    return $scope.id === 0 ? initContactListing() : initSingleContact();
  };

  const from = {
    APPOINTMENTS: 'appointments',
    CHAT: 'chat',
    TASKS: 'tasks'
  };

  $scope.isFromAppointments = () => !$scope.fromDashboard && $location.search().from === from.APPOINTMENTS;


  const isFromChat = () =>
    $location.search().from === from.CHAT;

  const isFromTasks = () =>
    $location.search().from === from.TASKS;

  $scope.assignCustomerToAppointment = () => {
    contactFacetFilterService.reset();
    return navService.go($scope.isSplitView ? -1 : -2);
  };

  const initFeatures = () => {
    $scope.features = {
      hasTags: featuresEnabledService.hasCustomerTags,
      hasTasks: featuresEnabledService.hasTasks,
      hasMultilang: featuresEnabledService.hasMultilang,
      hasClienteling: featuresEnabledService.hasClienteling,
      hasTextMessaging: featuresEnabledService.hasTextMessaging,
      displayClientelingToggle(hasPreCustomerData = false) {
        return !hasPreCustomerData && featuresEnabledService.hasClienteling();
      },
      hasAppointmentBookingForAllCustomers: featuresEnabledService.hasAppointmentBookingForAllCustomers,
      canAddCustomerToContacts: featuresEnabledService.canAddCustomerToContacts,
      hasAssociateRelationships: featuresEnabledService.hasAssociateRelationships
    };

    $scope.retailerHasCustomerTags = featuresEnabledService.hasCustomerTags();
    $scope.retailerHasCustomerActivityFeed = featuresEnabledService.hasCustomerActivityFeed();
  };

  $scope.contactIsLinked = (contact) => {
    if ($scope.isCustomers() || !$scope.isFromContact()) {
      return !!contact.isRelatedCustomer;
    }

    if ($scope.isContacts()) {
      return !!contact.isRelatedRetailerCustomer;
    }
  };

  $scope.isContacts = () => $scope.viewMode === 'contacts';

  $scope.isCustomers = () => $scope.viewMode === 'customers';

  $scope.isFromContact = () => contactService.isFromContact();

  /**
   * Contacts Listing Methods
   */

  const initContactListing = () => {
    fetchListingData();
    navService.addAndroidBackButtonListener($scope.exit);
  };

  const fetchListingData = () => {
    // Important for device edge case
    // When using Android back button, the current customer
    // storage isn't cleared properly and will persist
    clearCurrentCustomerStorage();

    getGroups();

    if (localStorageService.get('sfFromAddContact')) {
      clearSearchDataStorage();
    }

    if (localStorageService.get('sfCurrentSearchMode')) {
      $scope.viewMode = localStorageService.get('sfCurrentSearchMode');
      localStorageService.remove(CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH);
    } else { // first init
      toggleMode($location.search().viewMode || $scope.viewMode);
    }

    // contact search was made and app crashed, prevent wrong empty state
    if (localStorageService.get(CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA)) {
      $scope.isInitialSearch.contacts = !localStorageService.get(CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA).searchText;
    }
    // customer search was made and app crashed, prevent wrong empty state
    if (localStorageService.get(CONST_CONTACTS.LS_KEY_SEARCH_CUSTOMER_DATA)) {
      $scope.isInitialSearch.customers = !localStorageService.get(CONST_CONTACTS.LS_KEY_SEARCH_CUSTOMER_DATA).searchText;
    }
  };

  // Grabs groups for logged in user
  const getGroups = () => {
    if (localStorageService.get('sfGroups')) {
      $scope.groupList = localStorageService.get('sfGroups');
      return $q.when($scope.groupList);
    }
    return contactService.getGroups().then((data) => {
      $scope.groupList = data;
      localStorageService.set('sfGroups', data);
    });
  };

  const clearSearchDataStorage = () => {
    contactService.clearSearchData();
    localStorageService.remove('sfCurrentSearchMode');
    localStorageService.remove('sfFromAddContact');
    if ($scope.search && $scope.search.reset) {
      $scope.search.reset();
    }
  };

  const toggleMode = (view) => {
    localStorageService.set('sfCurrentSearchMode', view);
    $scope.viewMode = view;
  };

  // Used on Mobile Checkout customer view, pre customer and appointments
  $scope.showBackButton = () => $location.search().showBackButton === 'true';

  const gotoCreateContact = () => {
    saveCurrentSearchData('contacts');
    navService.goTo('/contacts/create');
  };

  $scope.exit = () => {
    clearSearchDataStorage();

    $scope.showBackButton()
      ? navService.goBack()
      : navService.goToDashboard();
  };

  $scope.deselectAllContacts = () => {
    $scope.selectedContacts = [];
  };

  // Turn on or off contacts bulk edit mode
  $scope.setEditMode = (bool) => {
    if (!bool) {
      $scope.deselectAllContacts();
    }
    $scope.editMode = bool;
  };

  const clearScreenData = () => {
    $scope.pagination = {};
    $scope.contactsList = [];
  };

  $scope.setMode = (type) => {
    if (!type || $scope.viewMode === type) {
      return;
    }

    clearScreenData();
    $scope.setEditMode(false);
    toggleMode(type);
    removeContactParams();

    if (localStorageService.get(`sfSearch${stringManipulationService.capitalize(type)}Data`)) {
      return fetchLastSearchData(type);
    }

    $scope.searchQuery = {};

    // The timeout is used to put the customer search API call at
    // the end of queue to properly calculate the number of customers
    // we want per page. I looked online, and saw that this method was
    // preferred over the $$postDigest one.
    // Source: http://blogs.microsoft.co.il/choroshin/2014/04/08/angularjs-postdigest-vs-timeout-when-dom-update-is-needed
    $timeout(() => {
      $scope.search.clearAdvancedSearch();
      $scope.search.clearSearch();
    }, 0, false);
  };

  const fetchLastSearchData = (mode) => {
    //clean the $scope.searchQuery to remove search data from wrong mode
    $scope.searchQuery = {};
    const searchData = contactService.getSearchData(mode);
    angular.extend($scope.searchQuery, searchData.query);
    $scope.search.searchText = searchData.searchText;
    $scope.search.advancedSearch = searchData.advancedSearch;
    $scope.search.contactsGroup = searchData.contactsGroup;
    $scope.favoritesFilterIsActive = searchData.favorite_contacts;

    $scope.searchQuery.page = 0;
    $scope.loadContacts($scope.searchQuery);
  };

  const saveCurrentSearchData = (mode) => {
    if ($scope.from === 'shop-home-search') {
      return;
    }

    const searchData = {
      query: $scope.searchQuery,
      searchText: $scope.search.searchText,
      advancedSearch: $scope.search.advancedSearch,
      contactsGroup: $scope.search.contactsGroup
    };
      //favorite filter can be only on contacts page,
      //do not add it to customers
    if ($scope.isFromContact()) {
      //add to searchData, so when coming back to list after change, this filter will be saved
      searchData.favorite_contacts = $scope.searchQuery.favorite_contacts;
    } else {
      delete searchData.query.favorite_contacts;
    }

    // saving data only if it is different from previous search
    if (!angular.equals(searchData, contactService.getSearchData(mode))) {
      contactService.setSearchData(mode, searchData);
    }
  };

  $scope.getContainerModifier = () => {
    let modifier = 'main-wrapper--is';

    $scope.isFromAppointments()
      ? modifier += $scope.features.hasAppointmentBookingForAllCustomers() && $scope.features.displayClientelingToggle($scope.hasPreCustomerData) ? '-client' : ''
      : modifier += $scope.features.displayClientelingToggle($scope.hasPreCustomerData) ? '-client' : ''; //54
    modifier += !$scope.features.hasTags() && $scope.isContacts() ? '-select' : ''; //54
    modifier += $scope.isContacts() ? '-contacts' : ''; //203
    return modifier;
  };

  $scope.getContactNameWithEmailOrPhone = contact => stringManipulationService.formattedNameWithEmailOrPhone(contact, false, $scope.customerNameDisplayTemplate);
  $scope.getContactName = contact => stringManipulationService.formattedName(contact, $scope.customerNameDisplayTemplate);

  $scope.getEmptyStateTitle = () => {
    let title;

    if ($scope.isContacts()) {
      title = $scope.isInitialSearch.contacts ? 'You Don\'t Have Any Contacts' : 'There are no contacts matching your search criteria';
    } else if ($scope.isCustomers()) {
      title = $scope.isInitialSearch.customers ? 'There are no customers at the moment' : 'There are no customers matching your search criteria';
    }
    return t(title);
  };

  // Sub-controller handling the search and the subscription and address book filtering
  $scope.setContactsSearchController = (searchController) => {
    $scope.search = searchController;

    const searchMode = localStorageService.get('sfCurrentSearchMode');
    const searchData = searchMode && contactService.getSearchData(searchMode);

    // get search data if search was made previsouly
    if (searchData && !$scope.hasPreCustomerData) {
      $scope.viewMode = searchMode;
      fetchLastSearchData(searchMode);
    } else {
      $scope.search.searchText = $location.search().searchText || '';
      $scope.search.search();
    }
  };

  const wasDisplayedContactRemoved = newContactList =>
    $scope.contactDetailsParams && !newContactList.find(({ id, ID }) => {
      const contactId = id || ID;
      return contactId === $scope.contactDetailsParams.contactId;
    });

  const handleChangeInContactsList = (contactsList) => {
    if (wasDisplayedContactRemoved(contactsList)) {
      removeContactParams();
    }
  };

  $scope.isContactSelected = ({ id, ID }) => {
    if ((!id && !ID) || !$scope.contactDetailsParams) {
      return;
    }
    const contactId = id || ID;
    return contactId === $scope.contactDetailsParams.contactId;
  };

  /**
   * Performs a search against the back-end service.
   *
   * Arguments in the query:
   * - searchText: The text to search for which to return contacts.
   * - groups    : List of group IDs for which to return contacts.
   *
   * @param {object|null} query - A number of query parameters
   * @param {boolean|null} force - Forces the reloading of the contacts regardless of the current state
   * @return void
   */

  $scope.loadContacts = (query = {}) => {
    const savedCustomerSearchQuery = contactService.getSearchData('customers');

    if ($scope.isCustomers()) {
      //On first load query might have contacts filters - if there is no CustomerSearchQuery we need to remove it.
      if (!savedCustomerSearchQuery) {
        return loadCustomers();
      }
      delete query.favorite_contacts;
      return loadCustomers(query);
    }

    let queryArgs = angular.extend({
      user_id: $rootScope.currentUser.ID,
      page: 0,
      per_page: CONST_PAGINATION.CONTACTS_PAGE_SIZE
    }, query);

    angular.extend($scope.searchQuery, queryArgs);

    isLoadingContactsCustomers = true;

    return contactService.getCustomersLegacy(queryArgs).then(({ data, pagination }) => {
      // Success

      $scope.deselectAllContacts();
      $scope.isInitialSearch.contacts = !queryArgs.search && !queryArgs.tags && !queryArgs.subcribtion_flag && !queryArgs.state && !queryArgs.city;
      contactService.scrollToTheTopOfTheList(queryArgs.page);
      $scope.contactsList = contactService.mergeContactsListData({ currentData: $scope.contactsList, newData: data, pageNumber: queryArgs.page });
      $scope.pagination = pagination;

      handleChangeInContactsList($scope.contactsList);

      // Save contacts search data on succesful contacts search
      saveCurrentSearchData('contacts');

      // if from_msg parameter is provided, sender of that message will be filled with email and name
      const fromMsg = $location.search().from_msg;
      if (typeof fromMsg !== 'undefined') {
        messageService.getStoreSingleMessage.query({ id: fromMsg }, (data) => {
          const user = data.messages.owner;
          // try to reconstruct first and last name, because we are getting that as unique string
          $scope.user.first_name = user.displayName.split(' ').slice(0, -1).join(' ');
          $scope.user.last_name = user.displayName.split(' ').slice(-1).join(' ');
          $scope.user.phone = data.messages.phone;
          $scope.user.email = user.email;
        });
      }
    }).catch((error) => {
      $scope.setEditMode(false);
      loggerService.logError('[contacts.js] could not fetch contacts', error);
    }).finally(() => {
      isLoadingContactsCustomers = false;
    });
  };

  const loadCustomers = (query = {}) => {
    const queryArgs = angular.extend({
      user_id: $rootScope.currentUser.ID || localStorageService.get('sfId'),
      page: 0,
      per_page: CONST_PAGINATION.CONTACTS_PAGE_SIZE
    }, query);

    isLoadingContactsCustomers = true;
    contactService.getRetailerCustomers(queryArgs).then(({ data, pagination }) => {
      // Success
      $scope.isInitialSearch.customers = false;

      $scope.getRetailerCustoemrsLocations = () => contactService.getRetailerCustoemrsLocations();

      contactService.scrollToTheTopOfTheList(queryArgs.page);
      $scope.contactsList = contactService.mergeContactsListData({ currentData: $scope.contactsList, newData: data, pageNumber: queryArgs.page });
      $scope.pagination = pagination;
      handleChangeInContactsList($scope.contactsList);

      // Save customers search data on succesful customer search
      saveCurrentSearchData('customers');
    }).catch((error) => {
      loggerService.logError('[contacts.js] could not fetch customers', error);
    }).finally(() => {
      isLoadingContactsCustomers = false;
    });
  };

  // Return proper placeholder for contacts/customers listing
  $scope.getSearchPlaceholder = () => {
    if ($scope.pagination?.count && !$scope.search.searchText?.length) {
      if ($scope.isContacts()) {
        return t('Search in _count_ Contact', { count: $scope.pagination.count });
      }
      return t('Search in _count_ Customer', { count: $scope.pagination.count });
    }
    return t('Search');
  };

  // Format the page counter and returns it when ready
  $scope.formatContactsCount = () => {
    if (!$scope.pagination || isNaN($scope.pagination.current_page) || isNaN($scope.pagination.pages) || !$scope.pagination.pages) {
      return;
    }
    const currentPage = $scope.pagination.current_page + 1;
    return `${currentPage} ${t('of')} ${$scope.pagination.pages}`;
  };

  $scope.sendMessage = () => navService.goTo('/store-messages/compose/', { who: $scope.selectedContacts.toString() });

  $scope.toggleSelectedContact = (contact, contactDetailsPath) => {
    // if not in edit mode, open single contact info
    if (!$scope.editMode) {
      if ($scope.isFromContact() && $scope.hasPreCustomerData) {
        return $scope.goToContactEdit(contact);
      }
      return goToContactInfo(contact, contactDetailsPath);
    }

    const index = $scope.indexOfSelectedContact(contact);
    if (index < 0) {
      $scope.selectedContacts.push(contact.ID);
    } else {
      $scope.selectedContacts.splice(index, 1);
    }

    for (let i = 0; i < $scope.contactsList.length; i++) {
      if ($scope.contactsList[i].ID === $scope.selectedContacts[0]) {
        $scope.contactHasEmail = !!$scope.contactsList[i].email;
        $scope.contactIsUnsubscribed = String($scope.contactsList[i].subcribtion_flag) === UNSUBSCRIBED.value;
      }
    }
  };

  let timer = null ;

  $scope.handleMouseLeave = (status) => {
    if (mediaQueryForSmallScreen.matches) {
      return;
    }
    timer = setTimeout(() => {
      $scope.overlay = status;
      $scope.$apply();
    }, 300);
  };

  $scope.handleMouseEnter = (status) => {
    if (!$scope.editMode || mediaQueryForSmallScreen.matches || !$scope.selectedContacts?.length) {
      return;
    }
    if ($scope.selectedContacts?.length > 0) {
      $scope.overlay = status;
    }
    clearTimeout(timer);
  };

  $scope.toggleOverlay = (status, event) => {
    if (event) {
      event.stopPropagation();
    }
    if (!$scope.editMode || !$scope.selectedContacts?.length) {
      return;
    }
    $scope.overlay = status;
  };

  $scope.indexOfSelectedContact = (contact) => {
    let index = -1;
    for (let i = 0; i < $scope.selectedContacts.length && index < 0; i++) {
      if ($scope.selectedContacts[i] === contact.ID) {
        index = i;
      }
    }
    return index;
  };


  const resetContactScreen = (reloadPage = false) => {
    // Timeout is needed because elastic search
    // is not refreshing quick enough in that case
    $timeout(() => {
      $scope.searchQuery.page = 0;
      $scope.loadContacts($scope.searchQuery);
      if (reloadPage) {
        $route.reload();
      }
    }, 1500);
  };


  $scope.deleteContact = () => {
    const promptText = t('You are about to delete the selected customer', { count: $scope.selectedContacts.length });
    $scope.overlay = false;

    feedbackService.showPrompt(promptText).then(() => {
      const contactsId = $scope.selectedContacts;

      if (contactsId.length > 1) {
        return contactService.bulkDelete(null, { customers_ids: contactsId }).then(() => {
          resetContactScreen();
        });
      }
      contactService.deleteCustomer({ id: contactsId.toString() }).then(() => {
        localStorageService.remove('sfTasksFilters');
        resetContactScreen();
      });
    });
  };

  const removeContactParams = () => {
    storageService.removeItem('contactId');
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER);

    $scope.savedCustomerId = null;
    $scope.contactDetailsParams = null;
  };

  const reloadContactDetails = () => {
    $scope.showContactDetails = false;
    $timeout(() => $scope.showContactDetails = true, 100);
  };

  const goToContactInfo = (contact, contactDetailsPath) => {
    let contactId;
    //customer data needs to be removed if was added when in split view
    clearCurrentCustomerStorage();

    if ($scope.isCustomers()) {
      saveCurrentSearchData('customers');

      // Making sure the contact is linked to a customer and we have the customer ID
      if (useRelatedCustomer(contact)) {
        localStorageService.set(CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH, true);
        contactId = contact.relatedCustomers[0].ID;
      } else {
        contactId = contact.id;
      }
    } else {
      saveCurrentSearchData('contacts');
      contactId = contact.ID;
    }

    removeContactParams();
    if ($rootScope.isLargeScreenOrTabletLandscape()) {
      reloadContactDetails();
      $scope.contactDetailsParams = {
        contactId: contactId
      };
      storageService.setTempItem('contactId', $scope.contactDetailsParams.contactId);
    } else {
      const path = contactDetailsPath || 'contacts/view';
      storageService.clearTempStorage();
      navService.goTo(`${path}/${contactId}`);
    }
  };

  const useRelatedCustomer = contact =>
    contact.isRelatedCustomer &&
    contact.relatedCustomers &&
    contact.relatedCustomers[0].ID &&
    !parseInt(contact.limited_visibility);

  const searchParams = () => {
    const params = {
      type: $routeParams.type
    };
    if ($scope.taskId) {
      params.taskId = $scope.taskId;
    }
    return params;
  };

  $scope.goToContactEdit = ({ ID }) => !$scope.isLimitedVisibility &&
    navService.goTo(`/contacts/create/${ID}`, searchParams());

  $scope.goToContactNotes = ({ ID }) => navService.goTo(`contacts/notes/${ID}`);

  $scope.addContact = () => {
    const canImportContacts = configService.get('RetailerCanImportContacts');

    if (canImportContacts) {
      return feedbackService.showFeedback({
        template: 'views/directives/feedback-content-button-modal.html',
        context: [{
          action: () => {
            gotoCreateContact();
            feedbackService.closeFeedback();
          },
          display: true,
          text: t('Create a New Contact')
        },{
          action: () => {
            navService.goTo('contacts/import');
            feedbackService.closeFeedback();
          },
          display: true,
          text: t('Import From Your Contacts')
        }],
        buttons: {
          close: t('Cancel')
        }
      });
    }

    gotoCreateContact();
  };

  const clearCurrentCustomerStorage = () => {
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER);
    localStorageService.remove('sfCurrentCustomerV2');
    localStorageService.remove('sfCurrentTransactionHistory');
    localStorageService.remove('sfCurrentSalesfloorTransactions');
    localStorageService.remove('sfSocialMediaNetworksData');
    localStorageService.remove('sfContactAddressList');
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CONTACT_STATS_DATA);
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH);
  };

  /**
   * Single Contact Methods
   */

  const initSingleContact = () => {
    navService.addAndroidBackButtonListener($scope.goBackToContacts);

    // Clear products to prevent keeping products in drawer from Android back button
    productsService.updateProductStorage();

    fetchSingleData();
  };

  const fetchSingleData = () => {
    const storedData = localStorageService.get(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER);
    let getCurrentCustomerData = $scope.isFromContact()
      ? getSingleCustomer
      : getSingleRetailerCustomer;

    if (storedData) {
      getCurrentCustomerData = $q.when;
      populateContactData(storedData);
      if (!$scope.isFromContact()) {
        $scope.isLimitedVisibility = evaluateLimitedVisibility(storedData, true);
      }
    }

    if ($scope.savedContactId && $rootScope.isLargeScreenOrTabletLandscape()) {
      reloadContactDetails();
      $scope.contactDetailsParams = {
        contactId: $scope.savedContactId
      };
    }

    return getCurrentCustomerData()
      .then(() =>
        $q.all([
          getSingleRetailerCustomerStats(),
          insertStyliticsSection(),
          fetchAssociateRelationships()
        ]));
  };

  // To display the module, the config needs to be turned on
  // and needs to be a Retailer Customer OR a linked Contact
  $scope.displayAssociateRelationships = () =>
    $scope.features.hasAssociateRelationships() && (!$scope.isFromContact() || $scope.contactIsLinked($scope.user));

  // Associate Relationships component call
  // Only display if feature is enabled and is a Retailer Customer
  // or Matched Contact (using the Retailer Customer ID always)
  const fetchAssociateRelationships = () => {
    if (!$scope.displayAssociateRelationships()) {
      return $q.when();
    }

    return associateRelationshipsService.fetchAssociateRelationships(getRetailerCustomerId())
      .then(associateRelationshipsService.storeAssociateRelationships);
  };

  // This is to prevent sending 'undefined' to associate relationship directive
  $scope.$watch('user', (user) => {
    if (user && !!getRetailerCustomerId()) {
      $scope.retailerCustomerId = getRetailerCustomerId();
    }
  });

  const getRetailerCustomerId = () => $scope.isFromContact()
    ? $scope.user?.relatedRetailerCustomers?.[0]?.id
    : $scope.id;

  const getExtendedContactProperties = (contact = {}) => {
    return {
      subscriptionStatus: contactService.getSubscriptionStatus(contact),
      smsSubscriptionStatus: contactService.getSMSSubscriptionStatus(contact),
      hasSubscriptionFlag: contactService.hasSubscriptionFlag(contact),
      hasSMSSubscriptionFlag: contactService.hasSMSSubscriptionFlag(contact)
    };
  };

  const populateContactData = (data = {}, saveData = false) => {
    $scope.user = {...data, ...getExtendedContactProperties(data)};

    if (saveData) {
      localStorageService.set(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER, $scope.user);
    }
  };

  const getSingleCustomer = () =>
    contactService.getSingleCustomer($scope.id).then((data) => {
      populateContactData(angular.extend(data, $scope.user), true);
    }).catch((error) => {
      loggerService.logError('[contacts.js] could not fetch single contact', error);
    });

  const getSingleRetailerCustomer = () =>
    contactService.getSingleRetailerCustomerWithStats($scope.id).then((data) => {
      populateContactData(angular.extend(data, { ...$scope.user, ID: data.id }), true);
      $scope.isLimitedVisibility = evaluateLimitedVisibility(data, true);
    }).catch((error) => {
      loggerService.logError('[contacts.js] could not fetch single customer', error);
    });

  const getSingleRetailerCustomerStats = () => {
    if (localStorageService.get(CONST_CONTACTS.LS_KEY_CONTACT_STATS_DATA)) {
      $scope.contactStats = localStorageService.get(CONST_CONTACTS.LS_KEY_CONTACT_STATS_DATA);
      appendDynamicSections($scope.contactStats.sections);
      return $q.when();
    }

    if ($scope.isFromContact() && !$scope.contactIsLinked($scope.user)) {
      return $q.when();
    }

    const id = $scope.isFromContact() ? ($scope.user.relatedRetailerCustomers[0] ||{}).id : $scope.id;

    // This fallback is NOT a solution, it is just to prevent the breaking of the screen
    // SF-29685
    if (!id) {
      return $q.when();
    }

    return contactService.getSingleRetailerCustomerWithStats(id).then((response) => {
      /*
        Temporary solution, avoiding destructuring with rest operation.
        The destructuring with rest might be returned back after fix CPD-1069.
      */
      const { panels, sections, link_to_rep } = response;
      const data = { ...response };

      if ($scope.isFromContact() && !link_to_rep && !$scope.user.isRelatedRetailerCustomer) {
        populateContactData(angular.extend(data, { ID: data.id }), true);
        $scope.isLimitedVisibility = evaluateLimitedVisibility(data, false);
        return;
      }
      $scope.contactStats = { panels, sections };
      appendDynamicSections(sections);
      localStorageService.set(CONST_CONTACTS.LS_KEY_CONTACT_STATS_DATA, $scope.contactStats);
    });
  };

  const evaluateLimitedVisibility = ({ limited_visibility, link_to_rep }, verifyLinkToRep) => featuresEnabledService.hasLimitedVisibility() && !!parseInt(limited_visibility) && (!verifyLinkToRep || !link_to_rep);

  const appendDynamicSections = sections =>
    angular.forEach(sections, (section) => {
      $scope.contactSectionLinks.push({
        label: section.label,
        onClick() {
          return navService.goTo(`contacts/sections/${section.id}`);
        },
        display() {
          return true;
        },
        getCount() {
          return false;
        }
      });
    });

  // Filter method for `$scope.contactSectionLinks`
  $scope.displaySection = section => section.display();

  $scope.canAddCustomerToContacts = () =>
    !$scope.isFromContact() &&
    !$scope.isLimitedVisibility &&
    !isFromGroupTask() &&
    $scope.features.canAddCustomerToContacts();

  $scope.displayActionButtons = () => !$scope.isLimitedVisibility && $scope.isFromContact()
    ? !actionsAreEmpty()
    : $scope.features.canAddCustomerToContacts();

  const actionsAreEmpty = () => !$scope.isFromContact() && (!$scope.contactIsLinked($scope.user) || !$scope.user.email && !$scope.user.phone);

  $scope.displayPhoneOrText = contact => contact.phone && $scope.isFromContact();

  $scope.getContactTitle = (user) => {
    if (!user) {
      return;
    }

    if (user.first_name && user.last_name) {
      return $scope.customerNameDisplayTemplate
        ? stringManipulationService.formatName($scope.customerNameDisplayTemplate, user.first_name, user.last_name)
        : user.first_name[0] + '. ' + user.last_name;
    }

    if (user.first_name) {
      return user.first_name;
    }

    if (user.last_name) {
      return user.last_name;
    }

    return user.email;
  };

  $scope.goBackToContacts = () => {
    if (isFromChat() || isFromTasks() || isFromGroupTask()) {
      clearCurrentCustomerStorage();
    }
    navService.goBack();
  };

  $scope.hasStats = ({ retailer_customer_stats_insights: stats = [], panels = [] }) => stats.length || panels.length;

  $scope.hasToggleNav = () => $scope.isFromAppointments()
    ? $scope.features.hasAppointmentBookingForAllCustomers() && $scope.features.displayClientelingToggle($scope.hasPreCustomerData)
    : $scope.features.displayClientelingToggle($scope.hasPreCustomerData);

  const getAddToContactsPromptText = () => {
    const {
      email,
      phone,
      is_subscribed_sms_marketing,
      is_subscribed
    } = $scope.user;

    const defaultPromptText = t('Are you sure you\'d like to add this record to your Contacts?');
    const unsubscribedPromptText = t('Are you sure you\'d like to add this record to your Contacts? This customer is unsubscribed from receiving communications.');

    if (email && phone) {
      if (is_subscribed_sms_marketing === UNSUBSCRIBED.value && is_subscribed === UNSUBSCRIBED.value) {
        return unsubscribedPromptText;
      }
    } else {
      if ((phone && is_subscribed_sms_marketing === UNSUBSCRIBED.value) || (email && is_subscribed === UNSUBSCRIBED.value)) {
        return unsubscribedPromptText;
      }
    }
    return defaultPromptText;
  };

  $scope.addToMyContacts = () => {
    if (!$scope.user.email && !$scope.user.phone) {
      feedbackService.showError(t('Could not add contact. Contact needs a valid email address or phone number to be added to My Contacts.'));
      return;
    }

    feedbackService.showPrompt(getAddToContactsPromptText(), {
      buttons: {
        confirm: 'Add',
        cancel: 'Cancel'
      }
    })
      .then(() => {
        contactService.addCustomerToContacts($scope.user)
          .then(navigateToLinkedContactFromCustomer)
          .catch((error) => {
            feedbackService.showError(error?.title || t('There was an error, please try again.'));
          });
      });
  };

  const navigateToLinkedContactFromCustomer = (contactId) => {
    // Clear last contact and customer searchData to force a reload with new link data
    // but keep search query params
    contactService.clearSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_SEARCH_CUSTOMER_DATA, 'data');
    contactService.clearSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA, 'data');

    // Clear current customer data to avoid wrong data
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER);
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CONTACT_STATS_DATA);
    $scope.savedCustomerId = null;

    // Necessary to know that we are now in Contact mode
    localStorageService.set(CONST_CONTACTS.LS_KEY_CONTACT_FROM_MATCH, true);
    //if in split view, we need to navigate to the contact. for us to be able to comeback with a back btn
    if ($scope.isInContactList()) {
      navService.goTo(`contacts/view/${contactId}`);
    } else {
      // Replace URL to prevent adding more steps in navigation history (For back button behaviour)
      navService.replace(`contacts/view/${contactId}`);
    }
  };

  $scope.isContactOrLinkedCustomer = () => contactService.isContactOrLinkedCustomer();

  $scope.getDefaultAddress = () => $scope.user?.addresses?.find(address => address.is_default);

  $scope.getSubscriptionText = subscriptionStatus => contactService.getSubscriptionText(subscriptionStatus);

  $scope.hasContactInfo = () =>
    $scope.user.email ||
    $scope.user.phone ||
    $scope.user.addresses?.length;
  const updateStorageOnToggleFavorites = (isFavoriteContact) => {
    contactService.updateSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_CURRENT_CUSTOMER, 'is_favorite_contact', isFavoriteContact);
    contactService.clearSingleStorageKeyValue(CONST_CONTACTS.LS_KEY_SEARCH_CONTACT_DATA, 'data');
  };

  const updateCustomerFavorites = (params, isFavoriteContact) => {
    contactService.updateCustomerFavorites(params).then(() => {
      $scope.user.is_favorite_contact = isFavoriteContact;
      updateStorageOnToggleFavorites(isFavoriteContact);
      //if in spitView user updates contacts favorite status, we need to update the contacts list
      if ($scope.isInContactList() && $scope.isContacts()) {
        resetContactScreen(true);
      }
    }).catch(() => {
      feedbackService.showError(t('Contact could not be added as a favorite, please try again.'));
    }).finally(() => {
      $scope.isToggleFavoriteDisabled = false;
    });
  };

  $scope.onToggleFavoriteContact = (isFavoriteContact) => {
    if (!$scope.user && !$scope.user.ID) {
      return;
    }
    const params = {
      customer_id: Number($scope.user.ID),
      is_favorite_contact: isFavoriteContact
    };

    $scope.isToggleFavoriteDisabled = true;
    updateCustomerFavorites(params, isFavoriteContact);
  };

  $scope.onScrollContactsList = () => {
    if (isLoadingContactsCustomers) {
      return;
    }

    $scope.searchQuery.page = $scope.pagination.current_page + 1;

    if ($scope.isContacts()) {
      return $scope.loadContacts($scope.searchQuery);
    }
    // On customers page
    loadCustomers($scope.searchQuery);
  };

  init();
});
