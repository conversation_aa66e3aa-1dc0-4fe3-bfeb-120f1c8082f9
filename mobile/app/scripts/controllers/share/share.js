'use strict';

/**
 *
 * URLs:
 *
 * /share/search - Main search window
 * /share/search-dept/:parentDepartment - List categories for the parent department
 * /share/products/all/1?search=:search - Lists all products
 * /share/products/:department/1?search=:search - Lists all products for a department
 * /share/products/:department/:category/1?search=:search - Lists all products for a category
 *
 */

angular.module('sfmobileApp').controller('share', (
  $location,
  $q,
  $rootScope,
  $route,
  $routeParams,
  $sce,
  $scope,
  $window,
  apiCache,
  $timeout,
  authService,
  corporateTaskService,
  currentLocale,
  draftService,
  debounce,
  deviceService,
  featuresEnabledService,
  feedbackService,
  i18nService,
  localStorageService,
  miscService,
  navService,
  productsDrawerService,
  productService,
  productsService,
  repUploadedPhotosService,
  scrollPositionService,
  shareService,
  groupTaskService,
  groupedProductsValidatorService,
  userService,
  loggerService,
  contactFacetFilterService,
  utilsService,
  CONST_FEEDBACK,
  CONST_PAGINATION,
  CONST_SERVICE,
  CONST_SHARE,
  CONST_CONTACTS,
  API_CACHE_KEYS,
  CONST_SF_APP
) => {
  $scope.i18nOpts = { ns: 'share' };
  const t = i18nService.t($scope.i18nOpts);
  // global share object among sub-controllers
  $scope.share = {
    subject: '',
    message: '',
    photos: [],
    products: [],
    asset: null,
    network: 'email',
    photoCount: 0,
    emailCustomerTagIds: '',
    socialShop: true
  };

  $scope.subCtrl = null;

  $scope.isInitialized = false;

  const { TASK_LIST_DELETE_KEY,
    TASK_ITEM_DELETE_KEY_PREFIX,
    GROUP_TASK_LIST_DELETE_KEY,
    GROUP_TASK_ITEM_DELETE_KEY_PREFIX
  } = API_CACHE_KEYS;

  // Set the default selection
  $scope.currentLangTemplate = localStorageService.get('sfAttachmentLang') || $rootScope.currentUser.store.storeLocale;

  $scope.isDraftSystemEnabled = featuresEnabledService.isDraftForShareEnabled();
  $scope.isEmailEnabled = $rootScope.conf('ShareEmailEnabled');
  $scope.isInstagramEnabled = $rootScope.conf('ShareInstagramEnabled') && !$rootScope.isMobileWeb();
  $scope.isPinterestEnabled = $rootScope.conf('SharePinterestEnabled') && !$rootScope.isMobileWeb();
  $scope.isFacebookEnabled = $rootScope.conf('ShareFacebookEnabled') && !$rootScope.isMobileWeb();
  $scope.isAiMessagingEnabled = $rootScope.conf('AiOutreachIsEnabled');

  $scope.isGroupedProductsToggleEnabled = () => localStorageService.get('sfShareGroupedProductsToggleStatus');

  $scope.isGroupedProductsToggleEnabledFlag = $scope.isGroupedProductsToggleEnabled();

  // when local storage value changes update the flag
  const handleOnGroupedProductsToggleChanged = (newValue) => {
    if (newValue !== $scope.isGroupedProductsToggleEnabledFlag) {
      // Tell the UI a new value is being used
      // it wont update if we use the function instead in the UI
      $scope.isGroupedProductsToggleEnabledFlag = newValue;

      // trigger rerender 
      $scope.debouncedApply();
    }
  };

  $scope.$watch(() => $scope.isGroupedProductsToggleEnabled(), handleOnGroupedProductsToggleChanged);


  // object to receive Url Response from server
  $scope.parseUrlResponse = {
    'title': '',
    'description': ''
  };

  $scope.isScrollable = true;

  // by default, scroll is enabled. In cases where modal pops up, disable
  $scope.toggleScrollable = () => $scope.isScrollable = !$scope.isScrollable;

  // get subview
  $scope.subview = typeof($routeParams.subview) === 'undefined' ? '' : $routeParams.subview;
  $scope.subCategoryId = typeof($routeParams.subCategoryId) === 'undefined' ? '' : $routeParams.subCategoryId; //L3
  $scope.subCategoryChildId = typeof($routeParams.subCategoryChildId) === 'undefined' ? '' : $routeParams.subCategoryChildId; //L4
  $scope.subCategoryLevel = typeof($routeParams.level) === 'undefined' ? '' : $routeParams.level;

  // get additional parameter
  $scope.param_id = typeof($routeParams.id) === 'undefined' ? '' : $routeParams.id;

  // the department
  $scope.department_id = $routeParams.department || $routeParams.id || '';

  // the category ID
  $scope.category_id = $routeParams.category ? $routeParams.category : '';

  //taskId is passed when a user is coming from a regular/corp/group task to autoResolveTask
  //for groupTasks, we'll use  groupTaskData.id, but will use taskId to delete the cache
  const { taskId, type } = $routeParams;

  // the breadcrumbs
  $scope.breadcrumbs = {
    //L1
    department: {
      name: 'All',
      display: true
    },
    //L2
    category: {
      name: 'All',
      display: false
    },
    //L3
    subCategory: {
      name: 'All',
      display: false
    },
    //L4
    subCategoryChild: {
      name: 'All',
      display: false
    },
  };

  // get additional parameter
  $scope.page = typeof($routeParams.page) === 'undefined' ? 1 : Number($routeParams.page);
  $scope.pageToLoad = $scope.page;

  // Infinite Scroll
  $scope.isLoadingProducts = false;
  $scope.infiniteScrollContainer = null;

  // Details Page
  $scope.detailsPageVisible = false;
  $scope.productDetailsController = null;

  // holds result from server
  $scope.result = null;

  // list of available facets
  $scope.availableFacets = [];

  // facets for the filter page
  $scope.facets = [];

  // the selected filter on the filter page
  $scope.selectedFacet = null;

  // true if the filter pane is visible
  $scope.facetPageVisible = false;

  // Autocomplete containers
  $scope.matchingBrands     = [];
  $scope.matchingCategories = [];

  $scope.showGlobal = true;

  $scope.showAutocomplete = false;

  $scope.groupOverlay = false;

  $scope.searchLevel = 0;

  // Tasks
  let corporateTaskData = null;
  let groupTaskData = null;

  const getGroupTaskId = () => groupTaskData?.id;
  const getCorporateTaskId = () => corporateTaskData?.id;

  // If product is already defined, populate message box
  const localStorageProducts = localStorageService.get('sfProductList');
  $scope.products = localStorageProducts || [];

  const localStorageAssetProps = localStorageService.get('sfAssetProps');
  $scope.selectedAssetProps =  localStorageAssetProps ? [localStorageAssetProps] : [];

  // WYSIWYG
  $scope.plainText = '';
  $scope.isTableInMessage = false;
  $scope.wysiwygEditorControls = null;

  $scope.bindWysiwygControls = controls => $scope.wysiwygEditorControls = controls;

  $scope.insertHtml = (html) => {
    if ($scope.wysiwygEditorControls) {
      $scope.wysiwygEditorControls.insertHtml(html);
    }
  };

  $scope.getEditorLastChildText = () => {
    if ($scope.wysiwygEditorControls) {
      const lastChild = $scope.wysiwygEditorControls.getLastChild();
      if (lastChild) {
        return lastChild.textContent;
      }
    }
    return '';
  };

  $scope.isWysiwygEnabled = () => $rootScope.conf('ShareWysiwygEnabled') && $rootScope.utils.doesOsSupportWysiwyg();

  $scope.sunEditorConfig = $window.sfApp === CONST_SF_APP.MOBILE ?
    { minHeight: 200, maxHeight: 230 } :
    { minHeight: 230, maxHeight: 300 };
  
  $scope.getFormattedMessage = () => {
    if ($scope.isWysiwygEnabled() && $scope.wysiwygEditorControls) {
      return $scope.wysiwygEditorControls.formatHtmlBeforeSend($scope.share.message);
    }
    return $scope.share.message;  
  };

  // Draft
  const DRAFT_BLANK_KEY = 'blank';
  $scope.shouldAutoloadDraftOnReturn = false;

  $scope.triggerDraftAutoSave = async () => {
    if ($scope.autoSaveEnabled && $scope.isInitialized) {
      $scope.hasChangedSinceDraftSave = false;
      await draftService.triggerSave();
    }
  };

  $scope.triggerDraftAutoSaveDebounced = debounce(() => {
    $scope.triggerDraftAutoSave();
  }, draftService.getDraftAutoSaveInterval());

  $scope.saveInitialFormData = async () => {
    await draftService.triggerSaveCheckpoint(DRAFT_BLANK_KEY);
  };
  
  $scope.restoreInitialFormData = async () => {
    await draftService.restoreCheckpoint(DRAFT_BLANK_KEY);
  };

  $scope.loadCurrentDraft = async () => {
    await draftService.restoreDraft($scope.draftKey);
  };

  $scope.deleteCurrentDraft = async () => {
    await draftService.deleteDraft($scope.draftKey);
  };

  $scope.resetShareForm = () => {
    // If has content ask before deleting
    if (hasContent()) {
      feedbackService.showPrompt(t('Discard your changes?')).then(() => {
        $scope.restoreInitialFormData();
      });
    } else {
      $scope.restoreInitialFormData();
    }
  };

  const startDraftLifecycle = () => {
    $scope.autoSaveEnabled = true;
    draftService.startLifecycle();
  };

  // Triggered before navigating away from page
  // Save draft and set to draft to auto-load when comming back from chat
  const handleDraftOnLocationChange = async () => {    
    await $scope.triggerDraftAutoSave();

    // Disable auto saving past this point
    $scope.autoSaveEnabled = false;
    draftService.stopLifecycle();

    // You are being taken away from the page without pressing the cancel/home/<USER>
    // automatically resume draft when you return
    if ($scope.shouldAutoloadDraftOnReturn) {
      groupTaskService.storeGroupTaskData(groupTaskData);
      corporateTaskService.storeCorporateTaskData(corporateTaskData);

      // Set flag to autoload draft immediately when re-entering from chat
      $scope.share.autoload_draft = true;
      localStorageService.set('sfShareObject', $scope.share);
    }
  };

  const handleOnDataChange = () => {
    $scope.setHasContentFlagDebounced();
    $scope.hasChangedSinceDraftSave = true;
    $scope.triggerDraftAutoSaveDebounced();
  };

  const handleOnShareChanged = (newValue, oldValue) => {
    const wasNetworkChanged = newValue.network !== oldValue.network;

    // switching tabs does not change any data we would want to save
    if (!wasNetworkChanged) {
      handleOnDataChange();
    }
  };

  // watch anything we would want to save
  // important for drafts and maintaining various flags
  const attachOnDataChangeWatchers = () => {
    let watchers = [
      'filterSelected',
      'currentLangTemplate',
      'lastParsedLink',
      'selectedAssetProps',
      'parseUrlResponse',
      'isGroupedProductsToggleEnabledFlag',
    ];
    $scope.$watch('share', handleOnShareChanged, true);
    watchers.forEach((valueTowatch) => {
      $scope.$watch(valueTowatch, handleOnDataChange, true);
    });
  };
  
  const initDraftSystem = async () => {
    $scope.autoSaveEnabled = false;
    $scope.hasChangedSinceDraftSave = false;
    $scope.shouldAutoloadDraftOnReturn = true;
    $scope.draftKey = makeDraftKey();
    draftService.setDraftKey($scope.draftKey);
    draftService.setCreateFunc(createDraft);
    draftService.setRestoreFunc(restoreDataFromDraft);
    
    // handle navigating away like for chat or other requests
    $scope.$on('$locationChangeStart', handleDraftOnLocationChange);

    attachOnDataChangeWatchers();

    if ($scope.share.autoload_draft) {
      await $scope.loadCurrentDraft();
      startDraftLifecycle();
    } else {
      if (!$scope.isInitialState) {
        // info is being passed in from another UI through local/secure storage
        startDraftLifecycle();
      } else {
        // Save blank state as computed by the init method
        // This is so we can always reset to the initial state
        await $scope.saveInitialFormData();

        // Check if draft exists and ask to restore
        let draftExists = await draftService.existsDraft($scope.draftKey);
        if (!draftExists) {
          startDraftLifecycle();    
        } else {
          promptToRestoreDraft();
        }
      }
    }
  };

  const handleRestoreDraftAfterPrompt = async () => {
    startDraftLifecycle();
    await feedbackService.closeFeedback();
    await draftService.restoreDraft($scope.draftKey);
  };

  const promptToRestoreDraft = () => {
    feedbackService.showFeedback({
      template: 'views/directives/feedback-content-button-modal-horizontal.html',
      message: t('You have a draft existing already.<br/>Restore the draft?'),
      type: 'context',
      hideFooter: true,
      allowDismissFeedback: false, // prevent accidental dismisses
      context: [
        {
          text: t('Discard'),
          type: 'cancel',
          display: true,
          action: async () => {
            startDraftLifecycle();
            await feedbackService.closeFeedback();
            // draft will get overwritten if not restored
          }
        },
        {
          text: t('Restore'),
          type: 'prompt',
          display: true,
          action: handleRestoreDraftAfterPrompt,
        },
      ]
    });
  };

  /**
   * Responsible for what draft we are currently editing 
   * @returns {string} draft key
   */
  const makeDraftKey = () => {
    let mode = 'message';
    let id = 'new';

    if (corporateTaskData) {
      mode = 'corptask';
      id = getCorporateTaskId();
    } else if (groupTaskData) {
      mode = 'grouptask';
      id = getGroupTaskId();
    }

    return `share_${mode}_${id}`;
  };

  const createDraft = () => ({
    draftKey: $scope.draftKey,
    draftType: 'share',
    timestamp: moment().utc(),
    url: $location.url(), // include for navigating from draft folder when it's implemented
    data: {
      templateLocale: $scope.currentLangTemplate,
      subject: $scope.share.subject,
      message: $scope.share.message,
      isHtml: $scope.isWysiwygEnabled(),
      emailCustomerTagIds: $scope.share.emailCustomerTagIds,
      lastParsedLink: $scope.lastParsedLink || '',
      socialShop: $scope.share.socialShop || false,
      photos: $scope.share.photos,
      products: $scope.share.products,
      asset: $scope.share.asset,
      assetProp: $scope.selectedAssetProps,
      parseUrlResponse: $scope.parseUrlResponse,
      isGroupedProductsToggleEnabled: localStorageService.get('sfShareGroupedProductsToggleStatus'),
      contactFilterSettings: contactFacetFilterService.getAllSettings(),
      localStorage: {
        groupTaskData,
        corporateTaskData,
      },
    },
  });

  // This method will still require the proper data to be populated in local storage
  // for group tasks for example
  const restoreDataFromDraft = async (draft) => {
    const data = draft.data;
    if (!data) {
      return;
    }   

    // unpack any attr referenced more than once
    const { localStorage, isHtml, message, products } = data;

    // reload context varaibles from local storage
    if (localStorage) {
      groupTaskData = groupTaskData || localStorage.groupTaskData;
      corporateTaskData = corporateTaskData || localStorage.corporateTaskData;
    }

    // Detect if we trying to reset to blank
    let isResetToBlank = draft.draftKey.endsWith(`:${DRAFT_BLANK_KEY}`);
    if (isResetToBlank) {
      // Restore contact filters
      $scope.shouldResetContactFilters = true;
    } else {
      if (!data.contactFilterSettings?.sfShareFilterOptions) {
        $scope.shouldResetContactFilters = true;
      } else {
        // Repopulate contact filters
        $scope.contactFilterSettingsToLoad = data.contactFilterSettings;
      }
    }

    $scope.currentLangTemplate = data.templateLocale;
    $scope.share.emailCustomerTagIds = data.emailCustomerTagIds;
    $scope.share.subject = data.subject;
    if ($scope.isWysiwygEnabled()) {
      if (isHtml) {
        $scope.share.message = message;
      } else {
        $scope.share.message = $rootScope.utils.plainTextToWysiwyg(message);
      }
    } else {
      if (isHtml) {
        $scope.share.message = $rootScope.utils.htmlToPlainText(message);
      } else {
        $scope.share.message = message;
      }
    }

    // restore photo
    $scope.attachment = data.attachment;
    $scope.share.photos = data.photos;

    // restore assets
    $scope.share.asset = data.asset;
    $scope.assetProp = data.assetProp;

    // restore products overwriting existing
    $scope.share.products = []; 
    if (products && products.length > 0) {
      products.forEach((product) => {
        if (!productsService.findInProducts($scope.share.products, product)) {
          $scope.share.products.push(product);
        }
      });
    }
    localStorageService.set('sfShareGroupedProductsToggleStatus', data.isGroupedProductsToggleEnabled);
    $scope.parseUrlResponse = data.parseUrlResponse;
    
    // trigger rerender
    $scope.debouncedApply();
  };

  /**
   * Logic to trigger loading of filter settings when navigating to the share-email
   * Define getters and setters to avoid $scope.$parent.$parent hell
   */
  $scope.contactFilterSettingsToLoad = null;
  $scope.setContactFilterSettingsToLoad = value => $scope.contactFilterSettingsToLoad = value;
  $scope.getContactFilterSettingsToLoad = () => $scope.contactFilterSettingsToLoad;

  /**
   * Logic to trigger filter reset when navigating back to the share-email
   * Define getters and setters to avoid $scope.$parent.$parent hell
   */
  $scope.shouldResetContactFilters = false;
  $scope.setShouldResetContactFilters = value => $scope.shouldResetContactFilters = value;
  $scope.getShouldResetContactFilters = () => $scope.shouldResetContactFilters;

  /**
   * Is contact filters selected?
   */
  $scope.filterSelected = false;
  $scope.setIsContactFilterSelected = (value) => {
    $scope.filterSelected = value;
  };
  $scope.getIsContactFilterSelected = () => {
    return $scope.filterSelected;
  };
  
  $scope.debouncedApply = debounce(() => {
    $scope.$apply();
  }, 300);

  $scope.fromDashboard = localStorageService.get('sfComposeFromDashboard')  || $location.search().fromDashboard === 'true';
  $scope.fromMainMenu = $location.search().fromMainMenu === 'true';

  $scope.isFromEvent = () => localStorageService.get('sfFromEventId');

  $scope.displayHomeBtn = () =>
    !$scope.isFromEvent()
    && ($scope.fromDashboard
    || $scope.fromMainMenu)
    && !$scope.fromAction;

  const typeCastParamToBool = (mixed) => {
    if (typeof(mixed) === 'string') {
      if (mixed.toLowerCase() === 'true') {
        return true;
      } else if (mixed.toLowerCase() === 'false') {
        return false;
      }
    }
    return Boolean(mixed);
  };

  $scope.fromAction = $location.search().fromAction === 'true';
  $scope.fromGroupTask = typeCastParamToBool($location.search().isFromGroupTask); // convert "false" string to false when navigating back from product menu

  /* @param {bool} Used when from browse to hide elements */
  $scope.isFromBrowse = localStorageService.get('sfBrowseMode');

  // holds last parsed link, to avoid re-parsing with same parameter
  $scope.lastParsedLink = '';

  $scope.isCategoryBrowseOpen = true;

  // Used to change class of textarea / editor
  $scope.isFocused = false;

  $scope.toggleCategoryBrowse = () => $scope.isCategoryBrowseOpen = !$scope.isCategoryBrowseOpen;

  // Sharing networks
  const NETWORK_EMAIL = 'email';
  const NETWORK_INSTAGRAM = 'instagram';
  const NETWORK_PINTEREST = 'pinterest';
  const NETWORK_FACEBOOK = 'facebook';

  $scope.networks = [
    NETWORK_EMAIL,
    NETWORK_INSTAGRAM,
    NETWORK_PINTEREST,
    NETWORK_FACEBOOK
  ];

  $scope.focusTextarea = (bool) => {
    $timeout(() => {
      $scope.isFocused = bool;
    });
  };

  $scope.isNetworkAvailable = (network) => {
    switch (network) {
    case NETWORK_EMAIL:
      return $scope.isEmailEnabled;
    case NETWORK_INSTAGRAM:
      return $scope.isInstagramEnabled;
    case NETWORK_PINTEREST:
      return $scope.isPinterestEnabled;
    case NETWORK_FACEBOOK:
      return $scope.isFacebookEnabled;
    }
  };

  // find the first available network
  $scope.share.network = $scope.networks.find(network =>
    $scope.isNetworkAvailable(network));

  // bool: false - wasn't shared on that network, true - user shared this post on that network
  $scope.sharedOnNetwork = {
    email: false,
    instagram: false,
    pinterest: false,
    facebook: false,
    twitter: false
  };

  // disable the share btn after share was complite on specific network (facebook/email/insta ...).
  $scope.markNetworkAsMessageSent = (network) => {
    $scope.sharedOnNetwork[network] = true;
  };

  // if any change is done to post after sharing on one of the networks- set all networks to false (will enable all share btn)
  $scope.enableNetworkShareBtn = () => {
    angular.forEach($scope.sharedOnNetwork, (value, key) => {
      $scope.sharedOnNetwork[key] = false;
    });
  };

  $scope.assetCategories = {
    all: t('All'),
    mylibrary: t('My Library'),
    corporate: t('Corporate'),
    shared: t('Most Popular'),
    archives: t('Archives')
  };

  // string with content from searchbox for searching products
  $scope.productSearchString = '';

  // holds URL of selected product
  $scope.selectedProduct = '';

  // sub-page for filtering products visibility state
  $scope.facetPageVisible = false;

  // users own details
  $scope.me = authService.myData();

  // filter text
  $scope.searchText = '';

  // filter products settings
  $scope.shareBtnText = t('Share');

  $scope.currentPage = 0;
  $scope.hasMorePages = true;

  $scope.hasMultilang = featuresEnabledService.hasMultilang();
  $scope.hasProductsFeed = featuresEnabledService.hasProductsFeed();
  $scope.currentLocale = currentLocale;
  $scope.maxQuickAccessProducts = CONST_SHARE.MAX_QUICK_ACCESS_PRODUCTS;

  var updateUserDetails = () => {
    userService.getCurrentUser()
      .then((data) => {
        $scope.me = data;
      })
      .catch((error) => {
        loggerService.logError('[share.js] could not fetch current user', error);
      });
  };

  // indicator for picture uploader
  $scope.uploading = false;
  $scope.categoryData = {};

  $scope.goToPage = (page) => {
    if ($location.path().substring(0, 13) === '/share/assets') {
      localStorageService.set('sfAssetPage', page);
      scrollPositionService.deleteCurrentScrollPosition();
      $route.reload();
    }
  };

  $scope.gotoTop = () => {
    document.getElementById('shareProductListing').scrollTop=0;
  };

  // Details page
  $scope.onProductDetailsClick = (product) => {
    productService.setCurrentProduct(product);
    $scope.selectedProductId = product.sku;
    $scope.detailsPageVisible = true;
  };

  $scope.hideDetailsPage = () => {
    $scope.detailsPageVisible = false;
  };

  $scope.updateProductDrawer = () => {
    $scope.products = localStorageService.get('sfProductList') || [];
  };

  $scope.registerProductDetailsController = (controller) => {
    $scope.productDetailsController = controller;

    // Replace the controller's existing 'addProduct' function with one
    // that calls our 'updateProductDrawer' function as a callback
    $scope.productDetailsController.addProduct = $scope.productDetailsController.addProduct.bind(controller, null, $scope.updateProductDrawer);
  };

  // Functionality related to filters

  // Returns a list of selected facets
  var getSelectedFacetOptions = (facets) => {
    var selected = {};
    for (var i in facets) {
      for (var j in facets[i].options) {
        if (facets[i].options[j].selected) {
          if (!selected[facets[i].key]) {
            selected[facets[i].key] = [];
          }
          if (facets[i].options[j].subOptions && facets[i].options[j].subOptions.length > 0 && facets[i].key === 'category') {
            for (var k in facets[i].options[j].subOptions) {
              let option = facets[i].options[j].subOptions[k];
              let subOptions = option.subOptions; //L3
              if (subOptions) {
                subOptions.forEach((option) => {
                  if (option.subOptions) { //L4
                    option.subOptions.forEach((opt) => {
                      if (opt.selected) {
                        selected[facets[i].key].push(`${opt.id}#${opt.key}`);
                      }
                    });
                  }
                  if (option.selected) {
                    selected[facets[i].key].push(`${option.id}#${option.key}`);
                  }
                });
              }

              if (facets[i].options[j].subOptions[k].selected) {
                let selectedSubCategory = `${facets[i].options[j].subOptions[k].id}#${facets[i].options[j].subOptions[k].key}`;
                selected[facets[i].key].push(selectedSubCategory);
              }
            }
          } else {
            selected[facets[i].key].push(facets[i].options[j].key);
          }
        }
      }
    }
    return selected;
  };

  // Returns true if both lists have the same selected options
  var facetsEqualOptions = (listA, listB) => {
    var same = true;

    if (listA && listB && listA.length === listB.length) {
      for (var i in listA) {
        var facetA = listA[i];
        var facetB = listB[i];

        if (facetB && facetA.options.length === facetB.options.length) {
          for (var j in facetA.options) {
            var optionA = facetA.options[j];
            var optionB = facetB.options[j];

            if (!optionB || optionA.selected !== optionB.selected) {
              same = false;
              break;
            }

            if (optionA.subOptions) {
              if (!optionB.subOptions || optionA.subOptions.length !== optionB.subOptions.length) {
                same = false;
                break;
              } else {
                for (var k in optionA.subOptions) {
                  var subOptionA = optionA.subOptions[k];
                  var subOptionB = optionB.subOptions[k];

                  if (subOptionA.selected !== subOptionB.selected) {
                    same = false;
                    break;
                  }
                }
              }
            } else if (optionB.subOptions && !optionA.subOptions) {
              same = false;
              break;
            }
          }
        } else {
          same = false;
          break;
        }
      }
    } else {
      same = false;
    }
    return same;
  };

  $scope.hasSelectedFacets = () => {
    for (var i in $scope.availableFacets) {
      for (var j in $scope.availableFacets[i].options) {
        if ($scope.availableFacets[i].options[j].selected) {
          return true;
        }
      }
    }
    return false;
  };

  $scope.hasSelectedOption = (facet) => {
    if (facet.options) {
      for (var i in facet.options) {
        if (facet.options[i].selected) {
          return true;
        }
      }
    }
    return false;
  };

  $scope.showFacetPage = () => {
    if (!facetsEqualOptions($scope.facets, $scope.availableFacets)) {
      //clean facets which have more than 200 results
      $scope.availableFacets.forEach((facet) => {
        if (facet.options && facet.options.length > 200) {
          facet.hasTooMany = true;
          facet.options = facet.options.slice(0, 200);
        }
      });
      var facets = angular.copy($scope.availableFacets); // works with a copy of the array
      var selectedFacet = false;
      for (var i in facets) {
        if (facets[i].selected) {
          selectedFacet = facets[i];
          break;
        }
      }

      if (!selectedFacet && facets.length > 0) {
        selectedFacet = facets[0];

        for (i in facets) {
          if ($scope.hasSelectedOption(facets[i])) {
            selectedFacet = facets[i];
            break;
          }
        }
      }

      $scope.facets = facets;
      $scope.selectFacet(selectedFacet);
    }

    $scope.facetPageVisible = true;
  };

  $scope.hideFacetPage = (applyChanges) => {
    if (applyChanges && !facetsEqualOptions($scope.facets, $scope.availableFacets)) {
      localStorageService.set('sfProductSortOrder', ''); // reset sorting when we apply filtering
      localStorageService.set('sfAvailableFacets', $scope.facets);
      localStorageService.set('sfProductPage', 1);
      scrollPositionService.deleteCurrentScrollPosition();
      $route.reload();
    } else {
      $scope.facetPageVisible = false;
    }
  };

  $scope.selectFacet = (facet) => {
    if (facet.hasTooMany) {
      feedbackService.showError(t('Too many options for _facet_ filter. Showing first 200 - please narrow your search using the other filters.', { facet: t(facet.name) }));
    }
    var selected = null;
    for (var i in $scope.facets) {
      if ($scope.facets[i].key === facet.key) {
        if (!$scope.facets[i].selected) {
          $scope.facets[i].selected = true;
          selected = $scope.facets[i];
        }
      } else if ($scope.facets[i].selected) {
        $scope.facets[i].selected = false;
      }
    }
    if (selected && (!$scope.selectedFacet || selected.key !== $scope.selectedFacet.key)) {
      $scope.selectedFacet = selected;
    }
  };

  $scope.selectAllOptions = (facet) => {
    for (var i in facet.options) {
      facet.options[i].selected = true;
      if (facet.options[i].subOptions) {
        selectUnselectAllSubOptions(facet.options[i], true);
      }
    }
  };

  $scope.selectNoneOptions = (facet) => {
    for (var i in facet.options) {
      facet.options[i].selected = !$scope.category_id && $scope.department_id !== 'all' && facet.options[i].subOptions ? true : false;
      if (facet.options[i].subOptions) {
        selectUnselectAllSubOptions(facet.options[i], false);
      }
    }
  };

  $scope.resetAllFacets = () => {
    for (var i in $scope.facets) {
      for (var j in $scope.facets[i].options) {
        $scope.facets[i].options[j].selected = false;
        if ($scope.facets[i].options[j].subOptions) {
          selectUnselectAllSubOptions($scope.facets[i].options[j], false);
        }
      }
    }
  };

  $scope.hasChildren = categoryChildren =>
    categoryChildren && Object.keys(categoryChildren).length > 0;

  // helper function that finds first URL in message string
  $scope._findUrls = (text) => {
    if (typeof text === 'undefined' || text === null) {
      return false;
    }
    var urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;<>]*[-A-Z0-9+&@#/%=~_|<>])/ig;
    var result = text.match(urlRegex);
    return (result === null) ? '' : result[0];
  };

  $scope.getDepartmentsLink = () => {
    var queryParams = $scope.stringifyParams($location.search());
    return 'share/search' + queryParams;
  };

  $scope.getDepartmentProductsLink = (id, department) => {
    let subSetParProductUrl = encodeURIComponent(id);
    if (department.level === 2) {
      subSetParProductUrl = `${$scope.department_id}/${id}`;
    } else if (department.level === 3) {
      subSetParProductUrl = `${$scope.department_id}/${$scope.subCategoryId}/${id}`;
    }
    var queryParams = $scope.stringifyParams($location.search());
    return `share/products/${subSetParProductUrl}/1${queryParams}`;
  };

  $scope.getDepartmentCategoriesLink = (departmentId) => {
    $scope.searchLevel = 1;
    var queryParams = $scope.stringifyParams($location.search());
    return 'share/search-dept/' + encodeURIComponent(departmentId) + queryParams;
  };

  $scope.getCategoryProductsLink = (categoryId, parentId, category) => {
    let departmentId = parentId;
    let grandparentId = '';
    let subSetParProductUrl = `${encodeURIComponent(departmentId)}/${categoryId}`;

    $scope.searchLevel = 2;
    let queryParams = $scope.stringifyParams($location.search());
    //if category has subCategories nex page will be subCategories list
    if (category.subCategories && Object.keys(category.subCategories).length > 0) {
      // L2
      if (category.level === 2) {
        return `share/search-dept/${encodeURIComponent(departmentId)}/${categoryId}/L2${queryParams}`;
      } else if (category.level === 3) { // L3
        departmentId = category.department;
        subSetParProductUrl = `${encodeURIComponent(departmentId)}/${parentId}/${categoryId}`;
        return `share/search-dept/${encodeURIComponent(departmentId)}/${parentId}/${categoryId}/L3${queryParams}`;
      }
      //if category does not have subCategories next page will be product list
    } else if (category.level === 2) {
      subSetParProductUrl = `${encodeURIComponent(departmentId)}/${categoryId}`;
    }  else if (category.level === 3) {
      departmentId = category.department;
      subSetParProductUrl = `${encodeURIComponent(departmentId)}/${parentId}/${categoryId}`;
    }  else if (category.level === 4) {  // L4
      departmentId = category.department; // L1
      grandparentId = category.grandparentId; // L2
      subSetParProductUrl = `${encodeURIComponent(departmentId)}/${grandparentId}/${parentId}/${categoryId}`;
    }

    return `share/products/${subSetParProductUrl}/1${queryParams}`;
  };

  $scope.getRelativeLink = (absoluteLink) => {
    var link = absoluteLink,
      index = link.indexOf('#');

    var match = link.match(/(.+)\?/);
    if (match && match.length && match.length > 1) {
      link = match[1];
    }

    if (index > -1) {
      return link.slice(index + 1);
    }

    return link;
  };

  // defining hyperlink for department/category listing
  $scope.getLink = (id, departmentId) => {
    console.warn('Deprecated: code your own method or use one of the more specific (less contextual link methods)');

    // link for department specific page
    var queryParams = $scope.stringifyParams($location.search());
    var url = '';
    if ($scope.subview === '' && $scope.param_id === '') {
      url = '#/share/search-dept/' + id + queryParams;
    }
    // link for product page
    if ($scope.subview === 'search-dept') {
      url = '#/share/products/' + departmentId + '/' + id + '/1' + queryParams;
    }

    return url;
  };

  $scope.getLinkRelative = (id, departmentId) => {
    console.warn('Deprecated: code your own method or use one of the more specific (less contextual link methods)');

    var link =  $scope.getLink(id, departmentId),
      index = link.indexOf('#');

    var match = link.match(/(.+)\?/);
    if (match && match.length && match.length > 1) {
      link = match[1];
    }

    if (index > -1) {
      return link.slice(index + 1);
    }

    return link;
  };

  $scope.reachMaximumModal = () => feedbackService.showError(t('You have reached the maximum number of products you may select.'));

  $scope.stringifyParams = (params) => {
    var s = '?';
    for (var p in params) {
      s += (s.length > 1 ? '&' : '') + p + '=' + params[p];
    }
    return s.length === 1 ? '' : s;
  };

  const clearMiscStorage = (removeBrowseMode = false) => {
    localStorageService.remove('sfAvailableFacets');
    localStorageService.remove('sfProductQueryString');
    localStorageService.remove('sfMaxCountProducts');
    localStorageService.remove('sfProductDepartments');
    localStorageService.remove('sfSecondSelectAfterBrowse');
    localStorageService.remove('sfProductSortOrder');

    // This will remove the flag for products drawer to propose options.
    // When using the new dashboard, we skip the browse page for Products and Assets.
    // Therefor, we need to control the browse flag manually.
    // SF-28469 SF-28491 SF-28494
    if ($scope.isFromEvent() || $scope.fromDashboard || removeBrowseMode) {
      localStorageService.remove('sfBrowseMode');
    }
  };

  const clearProductData = () => {
    $scope.parseUrlResponse = null;
    $scope.selectedProduct = null;
    $scope.share.products = [];
    $scope.lastParsedLink = '';
    productsService.updateProductStorage();
  };

  const clearAssetData = () => {
    $scope.parseUrlResponse = null;
    $scope.selectedAssetProps = null;
    $scope.share.asset = null;
    $scope.lastParsedLink = '';
    productsService.updateAssetStorage();
  };

  const clearPhotoData = () => {
    $scope.share.photos = [];
  };

  $scope.removeProductReadyForShare = (i) => {
    $scope.share.products.splice(i, 1);

    if ($scope.share.products.length > 0) {
      $scope.parseUrlResponse = $scope.share.products[0];
      $scope.lastParsedLink = $scope.parseUrlResponse.productUrl;
    } else {
      clearProductData();
    }

    $scope.serializeShareObject();

    if ($scope.isGroupedProductsToggleEnabled() && !$scope.validateGroupedProductsAttachments(false)) {
      $scope.setGroupedProductsToggleStatus(false);
    }
  };

  $scope.removeAssetReadyForShare = () => {
    clearAssetData();
    $scope.serializeShareObject();
  };

  $scope.removePhotoReadyForShare = (i) => {
    $scope.share.photos.splice(i, 1);
    $scope.serializeShareObject();
  };

  $scope.removeAllObjectsReadyForShare = () => {
    clearProductData();
    clearAssetData();
    clearPhotoData();
    $scope.serializeShareObject();
  };

  $scope.onLanguageChange = () => {
    $scope.share.message = '';
    $scope.share.subject = '';
    $scope.removeAllObjectsReadyForShare();
  };

  $scope.reorderProducts = (startPos, newPos) => {
    var product = $scope.share.products.splice(startPos, 1)[0];
    $scope.share.products.splice(newPos, 0, product);

    $scope.serializeShareObject();
  };

  $scope.reorderPhotos = (startPos, newPos) => {
    var photos = $scope.share.photos;
    var photo = photos.splice(startPos, 1)[0];

    photos.splice(newPos, 0, photo);

    $scope.serializeShareObject();
  };

  // user clicks one of media channel checkboxes on main screen
  $scope.toggleNetwork = (n) => {
    $scope.share.network = n;
  };

  $scope.showBarcode = featuresEnabledService.hasBarcodeScanner;

  $scope.hideFilterSearch = () => {
    var searchText = $scope.searchText || localStorageService.get('sfProductQueryString');
    return (!$scope.availableFacets || $scope.availableFacets.length < 1) && !searchText;
  };

  $scope.hasSubject = () => {
    return String($scope.share.subject || '').length > 0;
  };

  $scope.hasMessage = () => {
    const messageContents = $scope.share.message || '';
    if ($scope.isWysiwygEnabled()) {
      const elm = document.createElement('div');
      elm.innerHTML = messageContents;
      const plainTextContents = String(elm.textContent || elm.innerText).trim();
      return plainTextContents.length > 0;
    } else {
      return messageContents.length > 0;
    }
  };

  const hasContent = () => $scope.hasMessage() || $scope.hasSubject() || $scope.hasProducts() || $scope.hasAsset() || $scope.hasPhotos() || $scope.getIsContactFilterSelected();

  $scope.hasContentFlag = false; // only for use in template, prevent execution of hasContent() every render

  $scope.setHasContentFlagDebounced = debounce(() => {
    $scope.hasContentFlag = hasContent();
    $scope.debouncedApply();
  }, 300);
  
  const deleteAndNavigateAway = () => {
    if ($scope.isDraftSystemEnabled) {
      $scope.autoSaveEnabled = false; // disable auto save when leaving page
      $scope.shouldAutoloadDraftOnReturn = false;
      draftService.stopLifecycle();
      $scope.deleteCurrentDraft();
    }

    productsDrawerService.repopulateAttachmentsStorage();
    clearStorageDataAndGoBack(true);
  };

  const keepChangesAndNavigateAway = async () => {
    feedbackService.closeFeedback();

    // save if any new changes from debounced ui input
    if ($scope.autoSaveEnabled && $scope.hasChangedSinceDraftSave) { 
      await $scope.triggerDraftAutoSave();
    }
    $scope.autoSaveEnabled = false; // disable auto save when leaving page
    $scope.shouldAutoloadDraftOnReturn = false;
    draftService.stopLifecycle();

    productsDrawerService.repopulateAttachmentsStorage();
    clearStorageDataAndGoBack(true);
  };

  const promptToDiscardDraft = () => {
    feedbackService.showFeedback({
      template: 'views/directives/feedback-content-button-modal-horizontal.html',
      message: t('Would you like to discard or save your changes?'),
      hideFooter: true,
      context: [
        {
          text: t('Discard'),
          type: 'cancel',
          display: true,
          action: () => {
            feedbackService.closeFeedback();
            deleteAndNavigateAway();
          },
        },
        {
          text: t('Save'),
          type: 'prompt',
          display: true,
          action: keepChangesAndNavigateAway,
        },
      ]
    });
  };

  const shouldAutoResolveTask = () =>
    featuresEnabledService.isAutoResolveTasksEnabled() && (!!taskId || type === 'group-task');

  $scope.cancelMessage = async () => {
    if ($scope.isDraftSystemEnabled && $scope.autoSaveEnabled) {
      await $scope.triggerDraftAutoSave();
    }

    feedbackService.showSuccess(t('Task is automatically resolved.'), {
      showOnlyIf: shouldAutoResolveTask() && localStorageService.get('shareWasSent'),
    })
      .then(() => {
        localStorageService.remove('shareWasSent');
        if (hasContent()) {
          if ($scope.isDraftSystemEnabled) {
            promptToDiscardDraft();
          } else {
            feedbackService.showPrompt(t('If you are done with sharing, tap Confirm. Warning: Any unshared changes will be discarded.')).then(deleteAndNavigateAway);
          }
        } else {
          // if form is empty clean up any junk saves
          deleteAndNavigateAway();
        }
      });

  };

  const resetShareButtonText = () => {
    $scope.shareBtnText = t('Share');
  };

  // Note: if any keys are added here also add to StorageCleanupService to handle cleanup on app close
  const clearStorageDataAndGoBack = () => {
    resetShareButtonText();

    $scope.share.message = '';
    $scope.parseUrlResponse = '';
    $scope.selectedProduct = '';
    $scope.lastParsedLink = '';
    $scope.uploaded = false;

    //clear filter selection
    contactFacetFilterService.reset();

    clearPhotoData();
    clearMiscStorage();
    localStorageService.remove('sfFromEventId');
    localStorageService.remove('sfShareUpdateState');
    localStorageService.remove('sfShareObject');
    localStorageService.remove('sfShareFilterOptions');
    localStorageService.remove('sfShareCountQuery');
    localStorageService.remove('sfAttachmentLang');
    localStorageService.remove('sfShareGroupedProductsToggleStatus');
    localStorageService.remove(CONST_CONTACTS.LS_KEY_CURRENT_SEARCH_MODE);
    localStorageService.remove('shareWasSent');

    productsDrawerService.removeProductsDrawerStorage();
    clearProductData();
    clearAssetData();

    const fromContactsProducts = localStorageService.get('sfFromContactsProducts');
    localStorageService.remove('sfFromContactsProducts');

    if (fromContactsProducts) {
      return navService.go(-2);
    }

    $scope.displayHomeBtn()
      ? navService.goToDashboard()
      : navService.goBack();
  };

  $scope.validateDefault = $scope.validate = () => {
    // will be implemented in selected tab
  };

  $scope.processShareDataDefault = $scope.processShareData = () => {};
  $scope.processShareResponseDefault = $scope.processShareResponse = () => feedbackService.showSuccess(t('Share successful'));

  $scope.convertMessage = (message) => {
    let result = message;
    if ($scope.isWysiwygEnabled()) {
      if ($scope.share.network !== 'email') {
        result = $rootScope.utils.htmlToPlainText(message);
      }
    }
    return result;
  };

  const deleteCachedApi = () => {
    const deleteItemKey = $scope.fromGroupTask ? `${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${getGroupTaskId()}` : `${TASK_ITEM_DELETE_KEY_PREFIX}${taskId}`;
    const deleteListKey = $scope.fromGroupTask ? GROUP_TASK_LIST_DELETE_KEY : TASK_LIST_DELETE_KEY;
    apiCache.triggerDeleteKeys([deleteListKey, deleteItemKey]);
  };

  // executes sharing, and interprets feedback from server.
  $scope.sendMessage = (message) => {
    if (!$scope.validate(true)) {
      return;
    }

    let isHtml = false;
    if ($scope.isWysiwygEnabled() && $scope.share.network === 'email') {
      isHtml = true;
    }

    $scope.shareBtnText = t('Sharing...');

    if (typeof(message) !== 'string') {
      message = $scope.getFormattedMessage();
    }

    const shareData = {
      url: $scope.lastParsedLink || '',
      text: message,
      isHtml: isHtml,
      attachment: $scope.share.photos.map(({ url }) => url),
      asset: $scope.share.asset,
      products: $scope.share.products.map((product) => {
        if (product.tmplDisplay) {
          product.tmplDisplay.selectedVariant = product.selectedVariant;
          return product.tmplDisplay;
        }
        return product;
      }),
      templateLocale: $scope.currentLangTemplate,
      [$scope.share.network]: true,
      ...(corporateTaskData && {
        source: {
          name: CONST_SHARE.SOURCE_CORPORATE_TASK,
          id: corporateTaskData.id
        }
      }),
      ...(groupTaskData && {
        group_task_id: groupTaskData.id
      })
    };

    if ($scope.share.socialShop !== undefined) {
      shareData.socialShop = $scope.share.socialShop;
    }

    shareData.text = shareData.text.replace(shareData.url, '');
    if (shareData.asset) {
      shareData.text = shareData.text.replace(shareData.asset.permanentUrl, '');
    }
    if (shareData.products && shareData.products.length === 1) {
      shareData.product = shareData.products[0];
      delete shareData.products;
    }
    //task_id is needed for autoResolve a task, for group task we'll have shareData.group_task_id
    if (taskId && !$scope.fromGroupTask) {
      shareData.task_id = taskId;
    }

    localStorageService.remove('sfProductProps');
    localStorageService.remove('sfAssetProps');

    $scope.processShareData(shareData);

    if ($scope.isDraftSystemEnabled) {
      // turn off autosave when sending
      $scope.shouldAutoloadDraftOnReturn = false;
      $scope.autoSaveEnabled = false;
    }

    return shareService.executeShare.query(shareData).$promise
      .then(async (response) => {
        if (shouldAutoResolveTask()) {
          deleteCachedApi();
          // ShareWasSent is needed to display a task was resolved message on back navigation
          // When from fromGroupTask, no need to set shareWasSent as user will be back to the task after share
          if (!$scope.fromGroupTask) {
            localStorageService.set('shareWasSent', true);
          }
        }

        $scope.processShareResponse(response);
        resetShareButtonText();
        $scope.markNetworkAsMessageSent($scope.share.network); // after seccesfull share - will disable the btn in the network so the same post would't be shared again.
        $scope.cachedSharedData = angular.copy($scope.share); // shared data is copied (cached) so we could monitor any changes in content.
        delete $scope.cachedSharedData.network;
        delete $scope.cachedSharedData.socialShop;
        delete $scope.cachedSharedData.emailCustomerTagIds;
        $scope.cachedSocialShopData = angular.copy($scope.share.socialShop); // monitor the socialShop share checkbox (Share this to my SocialShop feed)
        $scope.cachedEmailCustomerTagIds = $scope.share.emailCustomerTagIds; // monitoring change in shared recipients(email filter tags)

        // after share go back to the task if from group task
        if ($scope.fromGroupTask) {
          if ($scope.isDraftSystemEnabled) {
            await $scope.deleteCurrentDraft();
          }
          feedbackService.showSuccess(t('Task is automatically resolved.'), {
            showOnlyIf: shouldAutoResolveTask() && $scope.share.network !== NETWORK_EMAIL
          });
          clearStorageDataAndGoBack();
        } else if ($scope.isDraftSystemEnabled) {
          // remains on same page with message still populated
          $scope.shouldAutoloadDraftOnReturn = true;
          $scope.autoSaveEnabled = true;
        }
      })
      .catch(() => {
        resetShareButtonText();
      });
  };

  $scope.sendMessageThrottled = utilsService.throttle($scope.sendMessage, 1500);

  $scope.submitSearch = (department, category) => {
    localStorageService.set('sfAvailableFacets', null);
    localStorageService.set('sfProductSortOrder', '');
    localStorageService.set('sfProductQueryString', $scope.productSearchString);

    // add timestamp `t` to query string to signal this is a product search
    const query = $location.search();
    query.t = (new Date()).getTime();

    // Builds a department/category section for the product search
    department = department || 'all';
    var subSetPart = '';
    if (department) {
      subSetPart = department + (category ? '/' + category : '');
      if ($scope.subCategoryId) { //L3
        subSetPart = `${$scope.department_id}/${$scope.subCategoryId}/${department}`;
      } else if ($scope.department_id && $scope.department_id !== department) { //L2
        subSetPart = `${$scope.department_id}/${department}`;
      }
    }

    var searchPath = '/share/products/' + subSetPart + '/1' + $scope.stringifyParams(query);

    navService.goTo(searchPath);
  };

  $scope.submitSubSearch = () => {
    localStorageService.set('sfAvailableFacets', null);
    localStorageService.set('sfProductSortOrder', '');
    localStorageService.set('sfProductQueryString', $scope.productSearchString);
    localStorageService.set('sfProductPage', 1);

    $route.reload();
  };

  $scope.clearSearch = () => {
    $scope.productSearchString = '';
    localStorageService.set('sfAvailableFacets', null);
    localStorageService.set('sfProductSortOrder', '');
    localStorageService.set('sfProductQueryString', '');

    $route.reload();
  };

  $scope.cancelChanges = () => {
    scrollPositionService.deleteCurrentScrollPosition();
    localStorageService.remove('sfProductPage');
    $scope.goBack();
  };

  const setSecondSelectAfterBrowse = () => {
    if ($scope.isFromBrowse) {
      localStorageService.set('sfSecondSelectAfterBrowse', true);
    }
  };

  $scope.attachProduct = () => {
    if ($scope.isProductDisabled()) {
      return false;
    }

    var products  = $scope.share.products  || [];
    var count = $scope.getMaxNumberOfProducts() - products.length;
    localStorageService.set('sfMaxCountProducts', count);

    if (count === 0) {
      return feedbackService.showError(CONST_FEEDBACK.BLOCK_PRODUCT_DUE_MAX_LIMIT);
    }

    setSecondSelectAfterBrowse();
    $scope.serializeShareObject();
    groupTaskService.storeGroupTaskData(groupTaskData);
    corporateTaskService.storeCorporateTaskData(corporateTaskData);

    if ($scope.isDraftSystemEnabled) {
      $scope.shouldAutoloadDraftOnReturn = false;
    }

    // CPD-1931 give ios time to close virtual keyboard before redirect to the products page
    $timeout(() => navService.goTo('/share/search'), 100);
    localStorageService.set('sfShareUpdateState', 'product');
  };

  $scope.getMaxNumberOfProducts = $scope.getMaxNumberOfProductsDefault = () =>
    CONST_SHARE.MAX_PRODUCTS;

  $scope.attachAsset = () => {
    if ($scope.hasPhotos() && (!$scope.isGroupedProductsToggleEnabled() || $scope.share.network !== NETWORK_EMAIL)) {
      return feedbackService.showError(CONST_FEEDBACK.BLOCK_ASSET_DUE_MIXED);
    }

    if ($scope.isGroupedProductsToggleEnabled() && $scope.share.network === NETWORK_EMAIL) {
      return feedbackService.showError(groupedProductsValidatorService.errorMessageAssets);
    }

    if ($scope.isAssetDisabled()) {
      return false;
    }

    setSecondSelectAfterBrowse();
    $scope.serializeShareObject();
    groupTaskService.storeGroupTaskData(groupTaskData);
    corporateTaskService.storeCorporateTaskData(corporateTaskData);

    if ($scope.isDraftSystemEnabled) {
      $scope.shouldAutoloadDraftOnReturn = false;
    }

    localStorageService.set('sfShareUpdateState', 'asset');
    localStorageService.remove('sfAssetPage');

    // CPD-1931 give ios time to close virtual keyboard before redirect to the assets page
    $timeout(() => navService.goTo('/share/assets/all'), 100);
  };

  $scope.attachPhoto = (inputFiles = null) => {
    if ($scope.hasAsset()) {
      return feedbackService.showError(CONST_FEEDBACK.BLOCK_PHOTO_DUE_ASSET);
    }

    if ($scope.isGroupedProductsToggleEnabled() && $scope.isPhotoDisabled() && $scope.share.network === NETWORK_EMAIL) {
      return feedbackService.showError(groupedProductsValidatorService.errorMessageMultiplePhotos);
    }

    if ($scope.isPhotoDisabled()) {
      return false;
    }

    if (window.cordova) {
      repUploadedPhotosService.requestReadPermission();
    }

    if (inputFiles) {
      if (inputFiles.length === 0) {
        return;
      }
      const maxPhotos = $scope.getMaxNumberOfPhotos();
      if (inputFiles.length > maxPhotos) {
        return feedbackService.showError(t('You can attach up to _count_ photos', {
          count: maxPhotos
        }));
      }
      $scope.startPhotoSelection(CONST_SERVICE.REP_PHOTOS_TYPE_INPUT_FILES_DESKTOP, inputFiles);
      return;
    }

    $scope.serializeShareObject();

    feedbackService.showOptions('', {
      buttons: {
        confirm: CONST_SERVICE.REP_PHOTOS_BUTTON_CAMERA,
        cancel: CONST_SERVICE.REP_PHOTOS_BUTTON_GALLERY,
        confirmIconClassModifier: CONST_SERVICE.REP_PHOTOS_BUTTON_CAMERA_CLASS_MODIFIER,
        cancelIconClassModifier: CONST_SERVICE.REP_PHOTOS_BUTTON_GALLERY_CLASS_MODIFIER
      }
    }).then(() => {
      $scope.startPhotoSelection(CONST_SERVICE.REP_PHOTOS_TYPE_CAMERA);
    }, (result) => {
      if (result === false) {
        return;
      }
      $scope.startPhotoSelection(CONST_SERVICE.REP_PHOTOS_TYPE_CAMERA_ROLL_MULTI);
    });
  };

  $scope.startPhotoSelection = (type, inputFiles = null) => {
    var photosToAdd = [];

    $scope.uploading = true;
    localStorageService.set('sfShareUpdateState', 'photo');

    repUploadedPhotosService.selectAndUpload({
      max: $scope.getMaxNumberOfPhotos(),
      type,
      inputFiles,
      onSuccess: (data) => {
        // success
        if (data) {
          photosToAdd.push(data.url);
          if (type === CONST_SERVICE.REP_PHOTOS_TYPE_CAMERA_ROLL_MULTI ||
              type === CONST_SERVICE.REP_PHOTOS_TYPE_INPUT_FILES_DESKTOP
          ) {
            return;
          }
        }
        addPhotos(photosToAdd);
      },
      onFail: (error) => {
        // fail
        if (!$scope.share.photos) {
          // checks for the presence of an old image.
          localStorageService.set('sfShareUpdateState', '');
        }
        $scope.uploading = false;
        loggerService.logError('[share.js] could not select and upload images: ', error);
      }
    });
  };

  $scope.getMaxNumberOfPhotos = $scope.getMaxNumberOfPhotosDefault = () =>
    $scope.isGroupedProductsToggleEnabled() && $scope.share.network === NETWORK_EMAIL
      ? groupedProductsValidatorService.groupedProductsMaxPhotos
      : $rootScope.conf('CameraMaxPhotos', CONST_SHARE.MAX_PHOTOS);

  const addPhotos = (photosToAdd) => {
    var photos = $scope.share.photos;

    $scope.uploading = false;
    $scope.uploaded = true;

    checkMaxPhotos(photosToAdd).then(() => {
      var numPhotosToRemove = Math.max(photos.length + photosToAdd.length - $scope.getMaxNumberOfPhotos(), 0);
      photos.splice(0, numPhotosToRemove);
      if (photos.length < 1) {
        $scope.share.photoCount = 0;
      }
      photos = photos.concat(photosToAdd.map((url) => {
        var seq = ++$scope.share.photoCount;
        return {
          url: url,
          seq: seq,
          label: 'Photo ' + seq
        };
      }));
      $scope.share.photos = photos;
      $scope.serializeShareObject();
    });
  };

  const checkMaxPhotos = (toAdd) => {
    var maxPhotos = $scope.getMaxNumberOfPhotos();
    if ($scope.share.photos.length + toAdd.length > maxPhotos) {
      return feedbackService.showError(t('You can attach up to _count_ photos. Additional selections will replace previously selected ones.', {
        count: maxPhotos
      }));
    }
    return $q.when();
  };

  $scope.serializeShareObject = () => localStorageService.set('sfShareObject', $scope.share);

  $scope.getAutocompleteDepartment = () => {
    // On search-dept page, use title ($scope.topListItem) for department
    if ($scope.topListItem) {
      return $scope.topListItem;
    }

    // On share/products page, use breadcrumb logic to display autocomplete department
    if ($scope.breadcrumbs.category.display && $scope.breadcrumbs.category.name !== 'All') {
      return $scope.breadcrumbs.category.name;
    }
    if ($scope.department_id) {
      return $scope.breadcrumbs.department.name;
    }

    // Default to old value
    return 'all departments';
  };

  $scope.buildBreadcrumbs = () => {
    if ($scope.department_id) {
      var departments = localStorageService.get('sfProductDepartments');

      if (departments && departments[$scope.department_id]) {
        $scope.breadcrumbs.department.name = departments[$scope.department_id];
      }

      if ($scope.department_id !== 'all') {
        $scope.breadcrumbs.category.display = true;
      }
    }

    if ($scope.category_id) {
      shareService.getCategory($scope.category_id, (category) => {
        if (!category || !category.name) {
          return;
        }
        $scope.category = category;
        $scope.subCategory;
        let subCategoryChild;

        $scope.breadcrumbs.category.name = category.name;

        if ($scope.department_id !== 'all') {
          $scope.breadcrumbs.category.display = true;
        }
        if ($scope.hasChildren(category.children)) {
          //display 'all' in the breadcrumbs 
          $scope.breadcrumbs.subCategory.display = true;
        }

        if ($scope.subCategoryId) {
          $scope.subCategory = getSubCategories(category, $scope.subCategoryId);

          if ($scope.hasChildren($scope.subCategory.children)) {
            $scope.breadcrumbs.subCategoryChild.display = true;
          }

          $scope.breadcrumbs.subCategory.name = $scope.subCategory.name;
          if ($scope.subCategoryChildId) {
            subCategoryChild = getSubCategories($scope.subCategory, $scope.subCategoryChildId);
            $scope.breadcrumbs.subCategoryChild.name = subCategoryChild.name;
          }
        }

      });
    }
  };

  $scope.isDepartmentAll = () => $scope.department_id === 'all';

  $scope.retrieveAsset = (assetData) => {
    const asset = localStorageService.get('sfAssetProps') || assetData;
    if (!asset) {
      return;
    }

    const { imageUrl, subject, permanentUrl } = asset;

    angular.extend(asset, {
      image: imageUrl,
      title: $sce.trustAsHtml(subject).toString(),
      description: ''
    });

    $scope.lastParsedLink = permanentUrl;
    $scope.share.asset = asset;

    productsDrawerService.saveAttachmentsForDrawer('asset', asset, $scope.isFromBrowse);

    productsService.updateAssetStorage();

    $scope.serializeShareObject();
  };

  $scope.retrieveProduct = (productsData) => {
    const product = localStorageService.get('sfProductProps');
    const productsList = productsData || localStorageService.get('sfProductList') || [];

    if (!product && !productsList.length) {
      return;
    }

    const pushProduct = (product) => {
      const productIsSelected = productsService.findInProducts($scope.share.products, product);

      if (productIsSelected) {
        return;
      }

      product = {
        ...product,
        title: productService.getName(product),
        description: $sce.trustAsHtml(productService.getShortDescription(product) || '').toString().slice(0,100) + '...',
        image: productService.getImage(product),
        thumbnailImage: productService.getImage(product),
        product_ID: product.sku
      };

      $scope.parseUrlResponse = product;
      $scope.lastParsedLink = productService.getLink(product.tmplDisplay || product);

      $scope.share.products = $scope.share.products || [];
      $scope.share.products.push(product);

      $scope.serializeShareObject();

      localStorageService.remove('sfProductProps');
    };

    if (productsList && productsList.length) {
      productsList.forEach((product) => {
        localStorageService.set('sfProductProps', product);
        pushProduct(product);
      });
    } else if (product) {
      pushProduct(product);
    }

    productsDrawerService.saveAttachmentsForDrawer('product', productsList, $scope.isFromBrowse);

    localStorageService.remove('sfProductList');
  };

  $scope.getCurrentPage = () => localStorageService.get('sfCurrentPage') || '';

  $scope.isFavorite = () => /favorite/.test($location.path());

  $scope.getCurrentType = () => localStorageService.get('sfStorefrontCurrentType');

  $scope.goBack = () => navService.goBack();

  $scope.go = index => navService.go(index);

  $scope.gotoBarcode = () => navService.goTo('/scan');

  $scope.goToPreviousBreadCramsCategory = (clickedCategory) => {
    let addToIndex = 0;
    // When a user clicks on "ALL" in the categories selection (share/search-dept)
    // we need to add -1 to the $scope.go() to redirect to the right place
    if (($scope.breadcrumbs.category.display && $scope.breadcrumbs.category.name === 'All')
      || $scope.breadcrumbs.subCategory.display && $scope.breadcrumbs.subCategory.name === 'All'
      || $scope.breadcrumbs.subCategoryChild.display && $scope.breadcrumbs.subCategoryChild.name === 'All'
    ) {
      addToIndex = -1;
    }

    // L1
    if (clickedCategory === 'department') {
      if ($scope.subCategoryChildId) {
        return $scope.go(-3 + addToIndex);
      } else if ($scope.subCategoryId) {
        return $scope.go(-2 + addToIndex);
      } else if ($scope.category_id) {
        return $scope.go(-1 + addToIndex);
      } else {
        return  $scope.goBack();
      }
    }
    //L2
    if (clickedCategory === 'category') {
      if ($scope.subCategoryChildId) {
        return $scope.go(-2 + addToIndex);
      } else if ($scope.subCategoryId) {
        return $scope.go(-1 + addToIndex);
      } else {
        return  $scope.goBack();
      }
    }
    // L3
    if (clickedCategory === 'subCategory') {
      return $scope.goBack();
    }
    if (clickedCategory === 'all') {
      if ($scope.subCategoryChildId) {
        return $scope.go(-4 + addToIndex);
      }  else if ($scope.subCategoryId) {
        return $scope.go(-3 + addToIndex);
      } else if ($scope.category_id) {
        return $scope.go(-2 + addToIndex);
      } else {
        return $scope.go(-1 + addToIndex);
      }
    }
  };

  $scope.toggleProductSort = () => {
    $scope.isProducSortVisible = !$scope.isProducSortVisible;
  };

  $scope.onProductSortChange = (value) => {
    // hide drawer after selection
    $scope.isProducSortVisible = false;
    localStorageService.set('sfProductSortOrder', value);
    $route.reload();
  };

  $scope.gotoFavorites = () => {
    navService.goTo('/share/favorites');
  };

  $scope.changeLocation = (path) => {
    navService.goTo(path);
  };

  $scope.getDealsParam = () => {
    var type = $scope.getCurrentType();
    if (type === 'deals' || type === 'new-arrivals' || type === 'newArrivals') {
      return '1';
    }

    return '';
  };

  $scope.setUserInfo = () => {
    $scope.first_name = $rootScope.currentUser.first_name;
    $scope.last_name = $rootScope.currentUser.last_name;
    $scope.store_name = $rootScope.currentUser.store.name;
    $scope.avatar = $rootScope.currentUser.avatar;
    $scope.email = $rootScope.currentUser.user_email;
  };

  const getCurrentTmplLocale = () => localStorageService.get('sfAttachmentLang');

  $scope.onInitShareProductListingElem = () => {
    /*
     * Unfortunately, ng-infinite-scroll stops working
     * once the route is reloaded after applying new filters.
     * This is because the previous 'shareProductListing'
     * element (the element used to listen for scrolling)
     * may be destroyed after the controller is initialized.
     * So, we may not get the correct reference of the
     * 'shareProductListing' element if we attempted to find
     * it in the 'init' function.
     *
     * This is a safer way to grab the element (once it has
     * been initialized and rendered).
     */
    $timeout(() => {
      const productListingElem = angular.element('#shareProductListing');
      // Required to set the container for ng-infinite-scroll
      $scope.infiniteScrollContainer = productListingElem;
    });
  };

  $scope.infiniteScrollDown = () => {
    if (!$scope.isLoadingProducts) {
      $scope.isLoadingProducts = true;
      $scope.pageToLoad += 1;

      $scope.loadProducts().finally(() => {
        $scope.isLoadingProducts = false;
      });
    }
  };

  $scope.saveProductData = (data) => {
    $scope.hasMorePages = !!data.nextPage;

    if (data.products.length > 0) {
      let results = Array.isArray($scope.result) ? $scope.result : [];
      $scope.result = [...results, ...data.products];
    }
  };

  $scope.loadProducts = () => {
    const loadProductsDeferred = $q.defer();

    var selectedFacetOptions = getSelectedFacetOptions($scope.availableFacets);

    // First level of search
    // Department is all if you don't select a category
    if ($scope.department_id === 'all') {
      shareService.getAllProducts.query({
        'page'  : $scope.pageToLoad,
        'deals' : $scope.getDealsParam(),
        'query' : $scope.productSearchString,
        'facets': selectedFacetOptions,
        'sort'  : $scope.selectedProductSortOption
      }, function (data) {
        $scope.saveProductData(data);

        loadProductsDeferred.resolve();

        if (!$scope.availableFacets) {
          // Loads all filters that match the given facets
          var queryArgs = {};
          if ($scope.productSearchString) {
            queryArgs.facetsToKeep = data.facets;
          }
          shareService.getFacets.query(queryArgs, (facets) => {
            $scope.availableFacets = facets;
          });
        }
      }, () => {
        feedbackService.showError('There was an error, please try again.')
          .then(navService.goBack);
      }, getCurrentTmplLocale());
    // Goes to this else condition if you do select a category
    } else {
      shareService.getProducts.query({
        'query'        : $scope.productSearchString,
        'page'         : $scope.pageToLoad,
        'category_id'  : $scope.category_id,
        'department_id': $scope.department_id,
        'subcategory_id': $scope.subCategoryId,
        'subcategory_child_id': $scope.subCategoryChildId,
        'facets'       : selectedFacetOptions,
        'sort'         : $scope.selectedProductSortOption,

        // old API
        'id'        : $scope.categoryData.id,
        'path'      : 'category:' + $scope.category_id,
        'repname'   : $rootScope.currentUser.nickname,
        'deals'     : $scope.getDealsParam(),
      }, (data) => {
        $scope.saveProductData(data);

        loadProductsDeferred.resolve();

        // Facets are filters 
        if (!$scope.availableFacets) {
          // Loads all filters that match the given facets
          shareService.getFacets.query(
            { facetsToKeep: data.facets, departmentId: $scope.department_id }, 
            (facets) => {
              $scope.availableFacets = facets;
            }
          );
        }

        if ($scope.department_id) {
          for (var i in $scope.availableFacets) {
            if ($scope.availableFacets[i].key === 'category') {
              // Second level of search
              if (!$scope.category_id) {
                for (var j in $scope.availableFacets[i].options) {
                  if ($scope.availableFacets[i].options[j].id === $scope.department_id) {
                    $scope.availableFacets[i].options[j].hidden   = false;
                    $scope.availableFacets[i].options[j].selected = true;
                    $scope.availableFacets[i].options[j].locked   = true;
                  } else {
                    $scope.availableFacets[i].options[j].hidden = true;
                  }
                }
              // Third level of search
              } else {
                $scope.availableFacets[i].hidden = true;
              }
            }
          }
        }
      }, () => {
        feedbackService.showError('There was an error, please try again.')
          .then(navService.goBack);
      }, getCurrentTmplLocale());
    }

    if ($scope.param_id === 'search') {
      shareService.searchProducts.query({
        'id'   : $scope.productSearchString,
        'page' : $scope.pageToLoad,
        'deals': $scope.getDealsParam(),
        'sort' : $scope.selectedProductSortOption
      }, (data) => {
        $scope.saveProductData(data);
        loadProductsDeferred.resolve();
      });
    }

    return loadProductsDeferred.promise;
  };

  const populateShareObj = ({ subject = '', body = '' }) => {
    let message = body;
    let isMessageHtml = $rootScope.utils.containsHtml(message);
    if ($scope.isWysiwygEnabled()) {
      if (!isMessageHtml) {
        message = $rootScope.utils.plainTextToWysiwyg(message);
      }
    } else {
      if (isMessageHtml) {
        message = $rootScope.utils.htmlToPlainText(message);
      }
    }

    return {
      subject: subject,
      message: message,
    };
  };

  // Extracts the data from a corporate task
  const populateShareObjectWithTaskData = () => {
    if ($scope.fromGroupTask) {
      groupTaskData = groupTaskService.getGroupTaskData();
    } else {
      corporateTaskData = corporateTaskService.getCorporateTaskData();
    }

    if (!corporateTaskData && !groupTaskData) {
      return;
    }

    const shareObj = $scope.fromGroupTask
      ? populateShareObj({ subject: groupTaskData.suggested_subject_line, body: groupTaskData.suggested_copy })
      : populateShareObj(corporateTaskData);

    const taskProducts = $scope.fromGroupTask ? groupTaskData.products : corporateTaskData.products;
    const taskAssets = $scope.fromGroupTask ? groupTaskData.assets[0] : corporateTaskData.assets[0];

    $scope.retrieveProduct(taskProducts);
    $scope.retrieveAsset(taskAssets);

    // Dont replace with suggested copy if user has updated it
    const hasMessageOrSubject = $scope.hasMessage() || $scope.hasSubject();
    if (!hasMessageOrSubject) {
      $scope.share = angular.extend({}, $scope.share, shareObj);
    }
  };

  const getSubCategories = (categories, subCategoryId) => {
    const subCategoryItems = categories.subCategories || categories.children;
    const subCategories = Array.isArray(subCategoryItems) ? subCategoryItems : Object.values(subCategoryItems);
    return subCategories.find(({ id }) => id === subCategoryId);
  };

  // controller's main init
  $scope.init = async () => {
    // is first time loading email from blank state
    $scope.isInitialState = true;
    let shouldInitDraftSytem = true;

    // clear all local storage variables on click on Android back button
    navService.addAndroidBackButtonListener(clearStorageDataAndGoBack);

    $scope.setUserInfo();
    updateUserDetails();

    const path = $location.path();

    // Dealing with subviews
    // Subview is search department and no id parameter is provided
    if (/^\/share\/search/.test(path)) {
      // searching for product in menu not in edit screen
      shouldInitDraftSytem = false;

      $scope.showGlobal = false;
      shareService.getDepartments.query({}, (data) => {
        $scope.result = data;

        var departmentsObj = {};

        for (var i = 0; i < data.subCategories.length; i++) {
          departmentsObj[data.subCategories[i].id] = data.subCategories[i].name;
        }

        localStorageService.set('sfProductDepartments', departmentsObj);
      });

      $scope.topListItem = 'All Departments';
      localStorageService.remove('sfProductQueryString');
      localStorageService.remove('sfAvailableFacets');
      localStorageService.remove('sfProductSortOrder');

      shareService.getFacets.query({}, (facets) => {
        $scope.availableFacets = facets;
      });

      $q.all([
        productsService.getFavorites(),
        productsService.getSharedProducts($rootScope.currentUser.ID),
      ]).then((results) => {
        $scope.favoriteProducts = results[0];
        $scope.sharedProducts = results[1].filter((product) => {
          return !productsService.findInProducts($scope.favoriteProducts, product);
        }).slice(0, $scope.maxQuickAccessProducts);

        $scope.$watchCollection('favoriteProducts', () => {
          if (!$scope.favoriteProducts || $scope.favoriteProducts.length < 1) {
            return;
          }
          $scope.favoriteProducts.forEach((product) => {
            productsService.removeFromProducts($scope.sharedProducts, product);
          });
        });
      });
    }

    // subview with search department, and department id provided
    if ($scope.subview === 'search-dept' && !$scope.subCategoryLevel && $scope.param_id !== '') {
      // searching for product in menu not in edit screen
      shouldInitDraftSytem = false;

      shareService.getCategories.query({
        'id': $scope.param_id
      }, (data) => {
        $scope.topListItem = data.name;
        $scope.result = data;
      });

      localStorageService.remove('sfProductQueryString');
      localStorageService.remove('sfAvailableFacets');
      localStorageService.remove('sfProductSortOrder');
      localStorageService.remove('sfProductPage');

      shareService.getProducts.query({
        'department_id': $scope.param_id,
      }, (data) => {
        if ($scope.availableFacets && !$scope.availableFacets.length) {
          shareService.getFacets.query({ facetsToKeep: data.facets, departmentId: $scope.param_id }, (facets) => {
            $scope.availableFacets = facets;
          });
        }
      });
    }

    //L3 of categories - department(L1)  -> subCategory(L2) -> children of subCategory(L2)
    if ($scope.department_id && $scope.param_id !== '' && $scope.subCategoryLevel === 'L2') {
      shareService.getCategories.query({
        'id': $scope.department_id
      }, (data) => {
        const getCategories = getSubCategories(data, $scope.param_id);
        $scope.result = getCategories;
        $scope.topListItem = getCategories.name;
      });
    }

    if ($scope.department_id && $scope.param_id !== '' && $scope.subCategoryLevel === 'L3') {
      shareService.getCategories.query({
        'id': $scope.department_id
      }, (data) => {
        const parentSubCategories = getSubCategories(data, $scope.subCategoryId);
        const subCat = getSubCategories(parentSubCategories, $scope.param_id);
        $scope.result = subCat;
        $scope.topListItem = subCat.name;
      });
    }

    // subview for products listing
    if (path.substring(0, 15) === '/share/products') {
      // searching for product in menu not in edit screen
      shouldInitDraftSytem = false;
      $scope.productSearchString = localStorageService.get('sfProductQueryString');
      $scope.availableFacets = localStorageService.get('sfAvailableFacets');
      $scope.selectedProductSortOption = localStorageService.get('sfProductSortOrder') || '';
      $scope.page = $scope.page ? $scope.page : localStorageService.get('sfProductPage') || 1;
      $scope.pageToLoad = $scope.page;

      $scope.buildBreadcrumbs();
      $scope.loadProducts();
    }

    if (path.substring(0, 13) === '/share/assets') {
      // searching for product in menu not in edit screen
      shouldInitDraftSytem = false;

      $scope.page = localStorageService.get('sfAssetPage') || 0;
      $scope.categoryData = $scope.assetCategories[$scope.param_id];
      $scope.fetchAssets();
    }

    if (path.substring(0, 16) === '/share/favorites') {
      // searching for product in menu not in edit screen
      shouldInitDraftSytem = false;

      productsService.getFavorites().then((products) => {
        $scope.hasMorePages = false;
        $scope.currentPage = 1;
        $scope.result = [].concat(products);
        $scope.pageTitle = t('Favorites');
      });
    }

    if (path.substring(0,7) === '/share') {
      localStorageService.set('sfCurrentPage', 'share');
    }

    if (path === '/share') {
      shouldInitDraftSytem = true;

      let storageShareObject = localStorageService.get('sfShareObject');
      if (storageShareObject) {
        // flag that we are loading from local storage
        $scope.isInitialState = false;
      }

      $scope.share = storageShareObject || $scope.share;

      $scope.retrieveAsset();
      $scope.retrieveProduct();

      populateShareObjectWithTaskData();
    }

    $scope.hasContentFlag = hasContent();

    // can be false when in product/asset menu UI
    if ($scope.isDraftSystemEnabled && shouldInitDraftSytem) {
      await initDraftSystem();
    }
    
    $scope.isInitialized = true;
  };

  const selectUnselectAllSubOptions = (option, isSelected) => {
    if (!option) {
      return;
    }
    if (option.subOptions) {
      option.subOptions.forEach((opt) => {
        opt.selected = isSelected;
        if (opt.subOptions) {
          opt.subOptions.forEach((subOption) => {
            subOption.selected = isSelected;
            if (subOption.subOptions) {
              subOption.subOptions.forEach((subOptionChild) => {
                subOptionChild.selected = isSelected;
              });
            }
          });
        }
      });
    }
  };

  $scope.toggleOption = (option) => {
    if (option.locked) {
      return;
    }

    option.selected = !option.selected;
    if (option.subOptions) {
      selectUnselectAllSubOptions(option, option.selected);
    }
    return option;
  };

  $scope.toggleSubOption = (suboption) => {
    return suboption.selected = !suboption.selected;
  };

  $scope.isSubOptionSelected = (option) => {
    if (!option.subOptions) {
      return option.selected;
    }
    if ($scope.department_id === 'all') {
      option.selected = false;
    }

    for (var i in option.subOptions) {
      let subOptionsL3;
      if (option.subOptions[i].selected) {
        option.selected = true;
      }
      if (option.subOptions[i].subOptions) {
        subOptionsL3 = option.subOptions[i].subOptions;
        for (let j in subOptionsL3) {
          if (subOptionsL3[j].selected) {
            option.selected = true;
            option.subOptions[i].selected = true;
          }
          if (subOptionsL3[j].subOptions) {
            for (let k in subOptionsL3.subOptions) {
              if (subOptionsL3[k].selected) {
                option.selected = true;
                option.subOptions[i].selected = true;
                option.subOptions[i].subOptions[j].selected = true;
              }
            } //end of L4
          }
        } //end of L3
      } 
    }//end of L2
    return option.selected;
  };

  $scope.toggleSearchOverlay = (state) => {
    return $scope.showAutocomplete = state;
  };

  $scope.searchSuggestion = (suggestion) => {
    if (suggestion.search_id) {
      $scope.productSearchString = '';
      $scope.submitSearch(suggestion.search_id);
    } else {
      $scope.productSearchString = suggestion.search_name;
      const path = $location.path();
      if (path.substring(0, 18) === '/share/search-dept') {
        $scope.submitSearch($scope.result.id);
      } else if (path.substring(0, 13) === '/share/search') {
        $scope.submitSearch();
      } else if (path.substring(0, 15) === '/share/products') {
        $scope.submitSubSearch(1);
      }
    }
  };

  var sortByIndex = (a, b) => {
    return a.index - b.index;
  };

  var sortByLength = (a, b) => {
    return a.display_name.length - b.display_name.length;
  };

  $scope.onSearchTextChange = () => {
    var searchStr = $scope.productSearchString ? $scope.productSearchString.toLowerCase() : '';

    if (!searchStr || searchStr.length < 3 || (!isNaN(searchStr) && searchStr.length > 4)) {
      $scope.showAutocomplete = false;
    } else {
      $scope.showAutocomplete = true;
      var facets = $scope.availableFacets;

      for (var i in facets) {
        var matches = [];

        // When only one brand, do not show no result bubble
        if (facets[i].key === 'brand') {
          $scope.showBubble = facets[i].options.length > 1 ? true : false;
        }

        if ((facets[i].key === 'brand' && facets[i].options.length > 1) || facets[i].key === 'category') {
          for (var j in facets[i].options) {
            var item  = facets[i].options[j].value;
            var index = item.toLowerCase().indexOf(searchStr);
            var id    = facets[i].options[j].id ? facets[i].options[j].id : null;

            // Do not populate department alone in autocomplete for 2nd and 3rd search level
            if (facets[i].key !== 'category' || $location.path().substring(0, 18) !== '/share/search-dept' && ($location.path().substring(0, 15) !== '/share/products' || $scope.department_id === 'all')) {
              $scope.getMatches(searchStr, item, index, matches, id);
            }

            if (facets[i].options[j].subOptions) {
              for (var k in facets[i].options[j].subOptions) {
                var subitem = facets[i].options[j].subOptions[k].value;
                index = subitem.toLowerCase().indexOf(searchStr);
                id = facets[i].options[j].id + '/' + facets[i].options[j].subOptions[k].id;
                $scope.getMatches(searchStr, subitem, index, matches, id, item);
              }
            }
          }

          matches.sort(sortByIndex);
          matches = matches.length > 3 ? matches.slice(0, 3) : matches;

          if (facets[i].key === 'brand') {
            $scope.matchingBrands = matches;
          } else {
            matches.sort(sortByLength);
            $scope.matchingCategories = matches;
          }
        }
      }
    }
  };

  $scope.getMatches = (str, match, index, arr, id, category) => {
    if (index > -1) {
      var highlight = match.substr(0, index) + '<mark>' + match.substr(index, str.length) + '</mark>' + match.substr(index + str.length, match.length - 1);
      var display = category ? category + ' > ' + highlight : highlight;
      arr.push({ 'display_name': display, 'search_name': match, 'search_id': id, 'index': index });
    }
  };

  $scope.hasPhotos = () => ($scope.share.photos || []).length > 0;
  $scope.hasAsset = () => !!$scope.share.asset;
  $scope.hasProducts = () => ($scope.share.products || []).length > 0;

  $scope.isPhotoDisabledDefault = $scope.isPhotoDisabled = () =>
    $scope.hasAsset() || $scope.uploading;

  $scope.isAssetDisabledDefault = $scope.isAssetDisabled = () =>
    $scope.hasPhotos() || $scope.uploading;

  $scope.isProductDisabledDefault = $scope.isProductDisabled = () =>
    $scope.uploading || !$scope.hasProductsFeed;

  $scope.insertLink = (link) => {
    if ($scope.isWysiwygEnabled()) {
      // add space if not already one
      let spacer = '&nbsp;';
      const lastChildText = $scope.getEditorLastChildText();
      const isLineEmpty = lastChildText.length === 0;
      const isLastCharWhiteSpace = /\s$/.test(lastChildText);
      if (isLineEmpty || isLastCharWhiteSpace) {
        spacer = '';
      }
      $scope.insertHtml(spacer + $rootScope.utils.plainTextToWysiwyg(link) + '&nbsp;');
    } else {
      $scope.share.message = $rootScope.utils.formatMessageWithLink($scope.share.message, link);
    }
    $scope.serializeShareObject();
  };

  $scope.onClickAiMessaging = () => {
    $rootScope.handleAppSwitch('/assistant', {
      message: $rootScope.utils.htmlToPlainText($scope.share.message)
    });
  };

  const unregisterOnInsertAiMessage = $rootScope.$on('insertAiMessage', (_event, data) => {
    if ($scope.isWysiwygEnabled()) {
      $scope.share.message = data.message;
    } else {
      $scope.share.message = $rootScope.utils.htmlToPlainText(data.message);
    }
    $scope.$apply();
  });

  $scope.$on('$destroy', () => {
    unregisterOnInsertAiMessage();
  });

  $scope.getShareProductsTitle = () => {
    if ($scope.pageTitle) {
      return $scope.pageTitle;
    }
    return t(!$scope.isFromBrowse ? 'Select Products' : 'My Library');
  };

  $scope.getShareAssetTitle = () => {
    return t(!$scope.isFromBrowse ? 'Select Asset' : 'My Library');
  };

  $scope.updateAsset = () => {
    $scope.selectedAssetProps = localStorageService.get('sfAssetProps');
  };

  const discardAndClose = (clearAction) => {
    if (clearAction) {
      clearAction();
    }
    clearMiscStorage(true);

    $scope.fromDashboard || $scope.fromMainMenu
      ? navService.goToDashboard()
      : navService.goBack();
  };

  $scope.leaveLibrary = (type, clearAction) => {

    if (clearAction) {
      return feedbackService.showPrompt(t('You have _type_ remaining in your shelf, if you leave the product library they will be discarded.', {type: t(type)}))
        .then(() => discardAndClose(clearAction));
    }

    discardAndClose();
  };

  $scope.leaveProductLibrary = () => {
    const products = localStorageService.get('sfProductList');
    return $scope.leaveLibrary('Products', products && products.length > 0
      ? productsService.updateProductStorage
      : null);
  };

  $scope.leaveAssetLibrary = () => {
    var asset = localStorageService.get('sfAssetProps');
    localStorageService.remove('sfAssetPage');
    return $scope.leaveLibrary('Asset', (asset && asset.id)
      ? productsService.updateAssetStorage
      : null);
  };

  $scope.getButtonText = (text, count) => t(`${text} _count_`, { count });

  $scope.attachSelectedProducts = productsDrawerService.attachSelectedProducts;

  $scope.$watch('currentLangTemplate', () => {
    localStorageService.set('sfAttachmentLang', $scope.currentLangTemplate);
  });

  // compare currentShareData & $scope.cachedSharedData.
  // If the two are not equal (something was changed) -enableNetworkShareBtn() is called to enable all the share btn on all network
  $scope.$watch('share', () => {
    const currentShareData = angular.copy($scope.share);

    delete currentShareData.network;
    delete currentShareData.socialShop;
    delete currentShareData.emailCustomerTagIds;

    if (!angular.equals(currentShareData, $scope.cachedSharedData)) {
      $scope.enableNetworkShareBtn();
    }
    // checks if a change was made to share to socialShop checkmark - if yes - enable the instegram share btn only.
    if ($scope.share.socialShop !== $scope.cachedSocialShopData) {
      $scope.sharedOnNetwork.instagram = false;
    }
    // checks if a change was made to recipients(email tags) - if yes - enable the email share btn only.
    // important to check that $scope.share.emailCustomerTagIds is defined as long as changing between share networks sets this value first to "undefined"
    if ($scope.share.emailCustomerTagIds !== $scope.cachedEmailCustomerTagIds) {
      $scope.sharedOnNetwork.email = false;
    }
  }, true);

  $scope.$watch('share.message', (newValue) => {
    $timeout(() => {
      if ($scope.isWysiwygEnabled()) {
        $scope.plainText = $rootScope.utils.htmlToPlainText(newValue);
        const pattern = /<table\b[^>]*>/i;
        $scope.isTableInMessage = pattern.test(newValue);
      } else {
        $scope.plainText = newValue;
        $scope.isTableInMessage = false;
      }
    }, 0);
  });

  const hasOwnFunction = (obj, fn) =>
    obj.hasOwnProperty(fn) && angular.isFunction(obj[fn]);

  $scope.registerSubController = (ctrl) => {
    $scope.subCtrl = ctrl;

    // verify sub-controller has all required methods
    const valid = [
      'validate'
    ].every(fn => hasOwnFunction(ctrl, fn));

    if (!valid) {
      throw new Error('missing required methods');
    }

    [
      // Required methods must be overidden by sub-controllers
      'validate',

      // Optional methods can be overidden by sub-controllers
      'processShareData',
      'processShareResponse',
      'isPhotoDisabled',
      'isAssetDisabled',
      'isProductDisabled',
      'getMaxNumberOfPhotos',
      'getMaxNumberOfProducts',
      'validateGroupedProductsAttachments',
      'setGroupedProductsToggleStatus'
    ].forEach((fn) => {
      $scope[fn] = hasOwnFunction(ctrl, fn)
        ? ctrl[fn]
        : $scope[`${fn}Default`];
    });
  };

  $scope.fetchAssets = (targets, resetPage = false) => {
    const options = {
      filter: {
        query: $scope.productSearchString || '',
      },
      page: {
        size: CONST_PAGINATION.PAGE_SIZE,
        number: (!resetPage && localStorageService.get('sfAssetPage')) || 0 // 0 because jsonapi start at 0 instead of 1
      }
    };
    if (targets && targets.length > 0) {
      const filterKey = targets.map(target => `filter[target_channel][]=${target}`).join('&');
      options[filterKey] = null;
    }
    return productsService.getAssets(options).then(({ assets, pagination }) => {
      $scope.result = assets;
      $scope.pagination = pagination;
    });
  };

  $scope.$on('updateDrawerHeightStatus', (event, isMinimized) => {
    $scope.productsDrawerIsMinimized = isMinimized;
    if (!isMinimized) {
      $rootScope.$broadcast('stickToBottomIfNeeded', {
        duration: deviceService.isTallScreen() ? 150 : 300,
        offset: 55,
      });
    }
  });

  $scope.init();
});
