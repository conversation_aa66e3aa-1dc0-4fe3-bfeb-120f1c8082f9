angular.module('sfmobileApp').controller('ShareEmail', (
  $scope,
  $rootScope,
  $timeout,
  localStorageService,
  contactService,
  featuresEnabledService,
  i18nService,
  feedbackService,
  emailTemplateValidatorService,
  configService,
  groupedProductsValidatorService,
  contactFacetFilterService,
  CONST_CONTACTS
) => {

  $scope.i18nOpts = { ns: 'share' };
  const t = i18nService.t($scope.i18nOpts);
  const groupedProductsMaxProducts = configService.get('GroupedProductsMaxProducts');
  $scope.isGroupedProductsToggleEnabled = false;
  $scope.groupedProductsSendStyledLinkLabel = configService.get('GroupedProductsSendStyledLinkLabel');

  $scope.isGroupedProductsFeatureEnabled = featuresEnabledService.isGroupedProductsEnabled();
  $scope.isMultipleEmailTemplatesEnabled = featuresEnabledService.hasMultipleCurationTemplate();
  const retailerHasCustomerTags = featuresEnabledService.hasCustomerTags();

  const { REGION, CITY, TRANSACTION_DATE, TRANSACTION_AMOUNT, CATEGORY, BRAND } = CONST_CONTACTS.FILTERS;
  const { SUBSCRIBED_CONTACT } = CONST_CONTACTS.TYPE;

  //SF-21642 - Multiple email templates
  const getDefaultEmailTemplate = () => {
    const selectedTemplate = localStorageService.get('sfTemplateEmail');

    if (selectedTemplate) {
      return selectedTemplate;
    }

    const templatesList = $rootScope.conf('RetailerEmailTemplatesLayouts');
    const defaultTemplate = Object.keys(templatesList).find(t => templatesList[t].default);
    return Object.assign({}, templatesList[defaultTemplate]);
  };

  $scope.handleEmailTemplateChange = (template) => {
    $scope.templateEmail = template;
    localStorageService.set('sfTemplateEmail', template);
  };

  $scope.validate = (showFeedback) => {
    if ($scope.loading || $scope.uploading) {
      return false;
    }
    if (!$scope.emailRecip) {
      return false;
    }
    if ($scope.sharedOnNetwork.email) {
      return false;
    }

    const { message, asset, products, photos } = $scope.share;


    if (!message || !message.length || !$scope.hasMessage()) {
      return false;
    }

    const attachments = {
      asset: asset ? [asset] : [],
      photos,
      products
    };

    return $scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled
      ? emailTemplateValidatorService.validateMultipleTemplate($scope.templateEmail, attachments, showFeedback)
      : true;
  };

  const disableMultipleTemplateOptions = ({ max }, len) =>
    max === 0 || (max > 0 && len >= max) || $scope.uploading;

  $scope.isPhotoDisabled = () => {
    if ($scope.isMultipleEmailTemplatesEnabled) {
      return disableMultipleTemplateOptions($scope.templateEmail['photos'],
        $scope.share.photos.length) || $scope.isPhotoDisabledDefault();
    }
    if ($scope.isGroupedProductsToggleEnabled) {
      return  $scope.share.photos?.length > 0;
    }
    return $scope.isPhotoDisabledDefault();
  };

  $scope.isAssetDisabled = () => {
    if ($scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled) {
      return disableMultipleTemplateOptions($scope.templateEmail['assets'],
        $scope.hasAsset() ? 1 : 0) || $scope.isAssetDisabledDefault();
    }
    return $scope.isGroupedProductsToggleEnabled || $scope.isAssetDisabledDefault();
  };

  $scope.isProductDisabled = () => {
    if ($scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled) {
      return disableMultipleTemplateOptions($scope.templateEmail['products'],
        $scope.share.products.length);
    }
    if ($scope.isGroupedProductsToggleEnabled) {
      return $scope.share.products?.length >= groupedProductsMaxProducts;
    }
    return $scope.isProductDisabledDefault();
  };

  $scope.getMaxNumberOfPhotos = () => {
    if ($scope.isMultipleEmailTemplatesEnabled) {
      return $scope.templateEmail['photos'].max || 0;
    }
    return $scope.getMaxNumberOfPhotosDefault();
  };

  $scope.getMaxNumberOfProducts = () => {
    const groupedGroductsMax = $scope.isGroupedProductsToggleEnabled && groupedProductsMaxProducts;
    const templateEmailProductsMax = $scope.isMultipleEmailTemplatesEnabled && ($scope.templateEmail['products'].max || 0);
    return groupedGroductsMax || templateEmailProductsMax || $scope.getMaxNumberOfProductsDefault();
  };

  const isFilterOptionSelected = options => options.some(option => option.selected === true);

  const transactionFiltersKeys = [BRAND.ID, CATEGORY.ALIAS, TRANSACTION_AMOUNT.ALIAS, TRANSACTION_DATE.ALIAS];
  const addressFiltersKeys = [REGION.ALIAS, CITY.ID];

  const updatedShareDataWithFilters = (shareData, savedFilters, filter) => {
    const transactionFilter = findInFilters(transactionFiltersKeys, filter);
    const addressFilter = findInFilters(addressFiltersKeys, filter);

    if (transactionFilter === TRANSACTION_AMOUNT.ALIAS || transactionFilter === TRANSACTION_DATE.ALIAS) {
      const transKey = transactionFilter === TRANSACTION_AMOUNT.ALIAS ? 'total' : 'date';
      Object.entries(savedFilters[transactionFilter]).forEach(( [key, value]) => {
        if (value) {
          shareData.filters.transaction[transKey][key] = value;
        }
      });
      //remove the filter if empty
      if (!Object.keys(shareData.filters.transaction[transKey]).length) {
        delete shareData.filters.transaction[transKey];
      }
    }

    if (transactionFilter?.length && savedFilters[transactionFilter]?.length) {
      shareData.filters.transaction[transactionFilter] = savedFilters[transactionFilter].toString();
    }

    if (savedFilters[addressFilter]?.length) {
      shareData.filters.address[addressFilter] = savedFilters[addressFilter].toString();
      //remove the filter if empty
      if (!Object.keys(shareData.filters.address[addressFilter]).length) {
        delete shareData.filters.address[addressFilter];
      }
    }

    return shareData;
  };

  $scope.processShareData = (shareData) => {
    $scope.taskId = shareData.task_id || shareData.group_task_id;
    const savedFilters = localStorageService.get('sfShareCountQuery') || {};
    shareData.email_subject = $scope.share.subject;
    shareData.templateEmail = $scope.isMultipleEmailTemplatesEnabled
      ? $scope.templateEmail.id
      : '';

    if ($scope.isGroupedProductsToggleEnabled) {
      shareData.groupedProductsOn = $scope.isGroupedProductsToggleEnabled;
      shareData.templateEmail = '';
    }

    // Populate the email tag ids
    if (retailerHasCustomerTags) {
      shareData.email_customer_tag_ids = savedFilters.tags?.length ? [].concat(savedFilters.tags) : [];
    }

    shareData.favorite_contacts = savedFilters.favorite_contacts || null;

    shareData.filters = {};
    shareData.filters.transaction = {};
    shareData.filters.transaction.total = {};
    shareData.filters.transaction.date = {};
    shareData.filters.address = {};

    Object.keys(savedFilters).forEach((filter) => {
      updatedShareDataWithFilters(shareData, savedFilters, filter);
    });

    return shareData;
  };

  const findInFilters = (filterIds, filter) => filterIds.find(id => id === filter);

  $scope.populateCustomerCount = (query = {}) => {
    // Getting all the email recipients
    return contactService.getCustomerCount({
      ...query,
      sf_locale: $scope.currentLangTemplate,
      mandatory_fields: 'email'
    }).then(({ data }) => {
      $scope.emailRecip = parseInt(data);
      $scope.emailRecipients = t('Email recipient', { count: $scope.emailRecip });
    });
  };

  $scope.setIsAnyFilterSelected = (filters = []) => {
    $timeout(() => {
      let isSelected = filters.filter(({ options, model = {}, initialState = {} }) =>
        !angular.equals(model, initialState) || isFilterOptionSelected(options)).length > 0;

      $scope.$parent.setIsContactFilterSelected(isSelected);
    });
  };

  $scope.getSfShareFilterOptions = () => localStorageService.get('sfShareFilterOptions') || [];

  $scope.applyFilter = (query) => {
    query = query || {};

    localStorageService.set('sfShareCountQuery', query);
    $scope.populateCustomerCount(query);

    // Update tag IDs on share object
    $scope.share.emailCustomerTagIds = query.tags;

    angular.forEach(query, (value, key) => {
      $scope.share[key] = value;
    });
  };

  const shouldAutoResolveTask = () =>
    featuresEnabledService.isAutoResolveTasksEnabled() && $scope.taskId;

  const shareEmail = ({ data: { emails_blackout = 0, emails_sent = 0 }}) => {
    const taskResolvedText =  t('Task is automatically resolved.');
    let successText = shouldAutoResolveTask() ? t('Share successful. Task is automatically resolved.') : t('Share successful');
    const { hasScrubbedShareRecipients } = featuresEnabledService;
    localStorageService.remove('shareWasSent');

    if (hasScrubbedShareRecipients() && emails_blackout > 0) {
      const period = Math.round($rootScope.conf('ClientelingBlackoutPeriod') / (3600 * 24) * 10000) / 10000;
      const successToRecipientText = `${t('Share successful for _count_ recipient', { count: emails_sent })}`;
      let successMsg = shouldAutoResolveTask()
        ? `${successToRecipientText} ${shouldAutoResolveTask() && taskResolvedText}`
        :  successToRecipientText;

      successText = `${successMsg}<br /><br />
                     ${t('To avoid sending too many marketing emails to your customers, please note that _count_ recipient was removed from the list as it was sent a Share Email in the last _period_ days.', { count: emails_blackout, period })}`;
    }

    feedbackService.showSuccess(successText);
  };

  $scope.processShareResponse = shareEmail;

  $scope.resetContactFilters = () => {
    $timeout(async () => {
      contactFacetFilterService.reset();
      contactFacetFilterService.resetFilterSearch(SUBSCRIBED_CONTACT);
      $scope.applyFilter();
      $scope.setIsAnyFilterSelected($scope.getSfShareFilterOptions());
    });
  };

  // allow us to delay execution until filters ready
  $scope.promiseToSetupContactFilters = null;

  // Watch if parent tells us to reset the contact filters
  // this may trigger before the fitlers are fully initialized
  $scope.$watch('$parent.shouldResetContactFilters', (newVal) => {
    if (newVal) {
      $scope.$parent.setShouldResetContactFilters(false);
      $scope.promiseToSetupContactFilters.then(async () => {
        await $scope.resetContactFilters();
      });
    }
  });

  // Watch if parent tells us to load contact filters
  // this may trigger before the fitlers are fully initialized
  $scope.$watch('$parent.contactFilterSettingsToLoad', (newVal) => {
    if (newVal) {
      $scope.$parent.setContactFilterSettingsToLoad(null);
      $scope.promiseToSetupContactFilters.then(() => {
        contactFacetFilterService.restoreSettings(newVal);
        $scope.applyFilter(contactFacetFilterService.getQuery());
        $scope.setIsAnyFilterSelected($scope.getSfShareFilterOptions());
      });
    }
  });

  $scope.setupContactFilters = async () => {
    localStorageService.set(CONST_CONTACTS.LS_KEY_CURRENT_SEARCH_MODE, 'share');

    contactFacetFilterService.setupSubscribedContacts($scope, async () => {
      await $scope.applyFilter(contactFacetFilterService.getQuery());
      contactFacetFilterService.resetFilterSearch(SUBSCRIBED_CONTACT);
    });
    await contactFacetFilterService.loadSubscribedContactsFilterOptions();
    $scope.populateCustomerCount(contactFacetFilterService.getQuery());
    $scope.setIsAnyFilterSelected($scope.getSfShareFilterOptions());
  };

  $scope.init = async () => {
    if ($scope.isMultipleEmailTemplatesEnabled) {
      $scope.templateEmail = getDefaultEmailTemplate();
    }

    // setup the contact filters
    // store promise so we can delay manipulation of filters until is fully loaded
    // there should be no await statement before this point otherwise there will be issue calling .then
    $scope.promiseToSetupContactFilters = $scope.setupContactFilters();
    await $scope.promiseToSetupContactFilters;

    $scope.registerSubController($scope);

    $scope.isGroupedProductsToggleEnabled = localStorageService.get('sfShareGroupedProductsToggleStatus') || false;
    if ($scope.isGroupedProductsToggleEnabled && !$scope.validateGroupedProductsAttachments(false)) {
      $scope.setGroupedProductsToggleStatus(false);
    }
  };

  // get the initial language.
  $scope.savedLangTemplate = $scope.currentLangTemplate;
  // every time we come back to share-email, we compare $scope.savedLangTemplate & $scope.currentLangTemplate
  // only if changes were done - customerCount will be updated
  $scope.$watch('currentLangTemplate', () => {
    if ($scope.savedLangTemplate !== $scope.currentLangTemplate) {
      $scope.savedLangTemplate = $scope.currentLangTemplate;
      $scope.populateCustomerCount(contactFacetFilterService.getQuery());
    }
  });

  $scope.validateGroupedProductsAttachments = (showFeedback) => {
    const { asset, products, photos } = $scope.share;
    // validator expecting array of assets
    const assets = asset && [asset] || [];

    return groupedProductsValidatorService.validateAttachments({ products, assets, photos }, showFeedback);
  };

  $scope.setGroupedProductsToggleStatus = (statusValue) => {
    $scope.isGroupedProductsToggleEnabled = statusValue;
    localStorageService.set('sfShareGroupedProductsToggleStatus', statusValue);
  };

  $scope.onToggleGroupedProducts = (statusValue) => {
    if ($scope.isGroupedProductsToggleEnabled) {
      $scope.setGroupedProductsToggleStatus(statusValue);
      return;
    }

    // validate with feedback
    if (!$scope.validateGroupedProductsAttachments(true)) {
      return;
    }

    $scope.setGroupedProductsToggleStatus(statusValue);
  };

  $scope.init();
});
