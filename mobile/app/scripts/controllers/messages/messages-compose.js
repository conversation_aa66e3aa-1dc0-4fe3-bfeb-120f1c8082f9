angular.module('sfmobileApp').controller('messages-compose', (
  $scope,
  $rootScope,
  $routeParams,
  $location,
  storageService,
  $sce,
  $q,
  $timeout,
  $window,
  apiCache,
  localStorageService,
  messageService,
  contactService,
  draftService,
  debounce,
  repUploadedPhotosService,
  i18nService,
  feedbackService,
  featuresEnabledService,
  navService,
  productsService,
  productsDrawerService,
  corporateTaskService,
  contactFacetFilterService,
  emailTemplateValidatorService,
  stringManipulationService,
  configService,
  groupTaskService,
  groupedProductsValidatorService,
  loggerService,
  CONST_FEEDBACK,
  CONST_SERVICE,
  CONST_SHARE,
  CONST_PAGINATION,
  CONST_NAME_TEMPLATE,
  CONST_TASKS,
  CONST_CONTACTS,
  API_CACHE_KEYS,
  CONST_SF_APP
) => {

  $scope.i18nOpts = { ns: 'messages' };
  let t = i18nService.t($scope.i18nOpts);

  $scope.id = typeof($routeParams.id) === 'undefined' ? '' : $routeParams.id;

  // overlay state with contacts, must be false to trigger data load on switching to true
  $scope.contacts = false;

  // contact details
  $scope.contactData = null;

  // contact filters search query
  $scope.searchQuery = {};

  // groups available
  $scope.groupList = null;

  // on contact selection screen holds selected contacts
  $scope.potentialContacts = [];

  // parallel to contactsSelected, this one is for handling tags properly
  $scope.selectedContacts = [];

  // has the "select all contacts" button been clicked?
  $scope.allContactsSelected = false;

  const { TASK_TYPE } = CONST_TASKS;
  $scope.shareBtnText = t('Send message');

  /** @var {Object} $scope.attachment Holds html content of attached media **/
  $scope.attachment = {};

  /** @var {Array} $scope.attachment.content Holds storage service uploaded image url **/
  $scope.attachment.content = [];

  /** @var $scope.uploading Uploading status (used for notifying user what's happening) **/
  $scope.uploading = false;

  // is media uploaded?
  $scope.uploaded = false;

  // holds selected product
  $scope.product = '';
  $scope.productProp = '';

  const { CONTACT } = CONST_CONTACTS.TYPE;

  //taskId is passed when a user is coming from a regular/corp/group task to autoResolveTask
  //for groupTasks, we'll use group_task_id || groupTaskData?.id, but will use taskId to delete the cache
  const { taskId } = $routeParams;

  // For contacts filters
  $scope.viewMode = 'contacts';

  $scope.isInitialized = false;

  $scope.isStore = localStorageService.get('sfType');

  $scope.fromDashboard = localStorageService.get('sfComposeFromDashboard') || $location.search().fromDashboard === 'true';
  $scope.fromMainMenu = $location.search().fromMainMenu === 'true';

  const shouldGoBackToDashboard = () => $scope.fromDashboard || $scope.fromMainMenu;

  $scope.fromAction = $location.search().fromAction === 'true';

  $scope.isFromBrowse = localStorageService.get('sfBrowseMode');

  const shouldAutoResolveTask = () => featuresEnabledService.isAutoResolveTasksEnabled() && !!taskId;

  const { TASK_LIST_DELETE_KEY,
    TASK_ITEM_DELETE_KEY_PREFIX,
    GROUP_TASK_LIST_DELETE_KEY,
    GROUP_TASK_ITEM_DELETE_KEY_PREFIX
  } = API_CACHE_KEYS;

  // Init variables that will be set through localstorage
  // are not referenced in URL
  let groupTaskData;
  let corporateTaskData;
  const getCorporateTaskId = () => corporateTaskData?.id;

  $scope.shouldAutoloadDraftOnReturn = false;

  $scope.isGroupTaskType = () => $routeParams.taskType === TASK_TYPE.GROUP_TASK;

  const DRAFT_BLANK_KEY = 'blank';

  // Set the default selection
  $scope.currentLangTemplate = localStorageService.get('sfAttachmentLang') || $rootScope.currentUser.store.storeLocale;

  $scope.isDraftSystemEnabled = featuresEnabledService.isDraftForComposeEnabled();
  $scope.hasMultilang = featuresEnabledService.hasMultilang();
  $scope.isMultipleEmailTemplatesEnabled = featuresEnabledService.hasMultipleCurationTemplate();
  $scope.retailerHasCustomerTags = featuresEnabledService.hasCustomerTags();
  $scope.hasProductsFeed = featuresEnabledService.hasProductsFeed();
  $scope.canSendEmailToMultipleRecipients = featuresEnabledService.canSendEmailToMultipleRecipients();
  $scope.isGroupedProductsFeatureEnabled = featuresEnabledService.isGroupedProductsEnabled();
  const groupedProductsMaxProducts = configService.get('GroupedProductsMaxProducts');
  const groupedProductsMinProducts = configService.get('GroupedProductsMinProducts');
  $scope.groupedProductsSendStyledLinkLabel = configService.get('GroupedProductsSendStyledLinkLabel');
  const groupedProductsMaxPhotos = groupedProductsValidatorService.groupedProductsMaxPhotos;
  $scope.isAiMessagingEnabled = $rootScope.conf('AiOutreachIsEnabled');

  const hasReachedMaximumSelectedContacts = contacts => $scope.canSendEmailToMultipleRecipients ? contacts.length === 20 : contacts.length === 1;

  $scope.contactsSearchController = null;

  $scope.isGroupedProductsToggleEnabled = false;

  let isLoadingContactsCustomers = false;

  // WYSIWYG
  $scope.plainText = '';
  $scope.isTableInMessage = false;
  $scope.wysiwygEditorControls = null;

  $scope.bindWysiwygControls = controls => $scope.wysiwygEditorControls = controls;

  $scope.insertHtml = (html) => {
    if ($scope.wysiwygEditorControls) {
      $scope.wysiwygEditorControls.insertHtml(html);
    }
  };

  $scope.getEditorLastChildText = () => {
    if ($scope.wysiwygEditorControls) {
      const lastChild = $scope.wysiwygEditorControls.getLastChild();
      if (lastChild) {
        return lastChild.textContent;
      }
    }
    return '';
  };

  $scope.isWysiwygEnabled = () => $rootScope.conf('ComposeEmailWysiwygEnabled') && $rootScope.utils.doesOsSupportWysiwyg();

  $scope.sunEditorConfig = $window.sfApp === CONST_SF_APP.MOBILE ?
    { minHeight: 200, maxHeight: 230 } :
    { minHeight: 230, maxHeight: 300 };

  $scope.getFormattedMessage = () => {
    if ($scope.isWysiwygEnabled() && $scope.wysiwygEditorControls) {
      return $scope.wysiwygEditorControls.formatHtmlBeforeSend($scope.compose.message);
    }
    return $scope.compose.message;
  };

  //display name template ({Fn Ln} / {Ln Fn})
  const customerNameDisplayTemplate = stringManipulationService.getNameDisplayTemplate(CONST_NAME_TEMPLATE.CUSTOMER.CONTACT_NAME);
  const isReplyRequest = $location.path().indexOf('reply-request') > -1;
  const isPiiObfuscationEnabled = featuresEnabledService.isPiiObfuscationEnabled();

  // Used by message-language-selector directive
  $scope.clearContactsData = () => {
    $scope.selectedContacts = [];
    $scope.contactData = null;
  };

  $scope.isRequest = () => $scope.mode === 'customer_request';

  const isCustomerData = value => value.ID !== undefined;

  // will load contacts
  $scope.loadContacts = (query = {}) => {
    const queryArgs = angular.extend({
      user_id: $scope.currentUser.ID,
      page: 0,
      per_page: CONST_PAGINATION.CONTACTS_PAGE_SIZE,
      mandatory_fields: 'email'
    }, query);

    if (queryArgs.subcribtion_flag === null || queryArgs.subcribtion_flag === undefined) {
      angular.extend(queryArgs,
        { ...contactFacetFilterService.getSubscriptionFlag(CONTACT, { removeUnsubscribedFromAll: true }) }
      );
    }

    if ($scope.hasMultilang) {
      queryArgs.locale = $scope.currentLangTemplate;
    }

    angular.extend($scope.searchQuery, queryArgs);
    $scope.isSearch = !!queryArgs.search;

    isLoadingContactsCustomers = true;

    return contactService.getCustomersLegacy(queryArgs).then(({ data, pagination }) => {
      contactService.scrollToTheTopOfTheList(queryArgs.page);
      $scope.contactData = contactService.mergeContactsListData({ currentData: $scope.contactData, newData: data, pageNumber: queryArgs.page });
      $scope.pagination = pagination;
      // .text property is used by tags-input tag in the html
      angular.forEach($scope.contactData, (contact) => {
        contact.text = $scope.getContactName(contact);
      });
    }).catch((error) => {
      loggerService.logError('[message-compose.js] could not fetch contacts', error);
    }).finally(() => {
      isLoadingContactsCustomers = false;
    });
  };

  $scope.searchContacts = (query, pageSize) => {
    // We set default page size to be a relatively large number compared to the
    // required number of suggestions to (hopefully) avoid the situation that
    // we need to fetch again when we got fewer suggestions than the
    // requirement after filtering
    let defaultPageSize = CONST_PAGINATION.CONTACTS_SUGGESTION_PAGE_SIZE * 10;

    pageSize = pageSize || defaultPageSize;
    query = {
      ...contactFacetFilterService.getSubscriptionFlag(CONTACT, { removeUnsubscribedFromAll: true }),
      user_id: $scope.currentUser.ID,
      page: 0,
      per_page: pageSize,
      search: query,
      mandatory_fields: 'email'
    };

    if ($scope.hasMultilang) {
      query.locale = $scope.currentLangTemplate;
    }

    return contactService.getCustomersLegacy(query).then((response) => {
      let contacts = (response.data || []).filter(contact => contact.email);

      // If we don't get enough suggestions but response shows there are more
      // data, we'll set the pageSize to negative and fetch again. If pageSize
      // is already negative, it means we're on our second try, then just use
      // the result as is.
      if (contacts.length < CONST_PAGINATION.CONTACTS_SUGGESTION_PAGE_SIZE &&
        response.pagination.total > pageSize) {
        return $scope.searchContacts(query.search, response.pagination.total);
      }

      return contacts
        .slice(0, Math.min(CONST_PAGINATION.CONTACTS_SUGGESTION_PAGE_SIZE, contacts.length))
        .map((contact) => {
          return angular.extend(contact, {
            text: $scope.getContactName(contact),
            label: stringManipulationService.formattedName(contact, customerNameDisplayTemplate),
            emptyLabel: 'No Name',
            value: contact.email
          });
        });
    });
  };

  $scope.getSearchPlaceholder = () => {
    if ($scope.pagination && $scope.pagination.count) {
      return t('Search in _count_ Contact', {count: $scope.pagination.count});
    }
    return t('Search');
  };

  // function used in listing of contacts on contact selection screen, it will display person's full name or email, if
  // name is not available (if user's email is added on the spot)
  $scope.getContactName = contact => stringManipulationService.formattedNameWithEmailOrPhone(contact, false, customerNameDisplayTemplate);

  $scope.isContacts = () => !!$scope.contacts;

  // will open contact selection screen
  $scope.openAddressbook = async () => {
    await contactFacetFilterService.loadFilterOptions(contactFacetFilterService.types.EMAIL);
    if ($scope.groupList === null) {
      await contactService.getGroups().then((response) => {
        $scope.groupList = response;
      });
    }
    if ($scope.contactData === null) {
      await $scope.loadContacts();
    }
    $scope.potentialContacts = angular.copy($scope.selectedContacts || []);
    $scope.contacts = true;
    $scope.debouncedApply();
  };

  // Adds or removes the contact to the potential contact list
  $scope.togglePotentialContact = (contact) => {
    let index = $scope.indexOfPotentialContact(contact);
    if (index < 0) {
      if (hasReachedMaximumSelectedContacts($scope.potentialContacts)) {
        return feedbackService.showError(t('You have reached the maximum number of contacts you can add at one time'));
      }
      $scope.potentialContacts.push(contact);
    } else {
      $scope.potentialContacts.splice(index, 1);
    }
  };

  // Remove contacts that come from the address book
  $scope.removeContactsFromAddressBook = () => {
    let newContactsList = [];
    for (let index in $scope.selectedContacts) {
      if (!$scope.selectedContacts[index].addressbook) {
        newContactsList.push($scope.selectedContacts[index]);
      }
    }
    $scope.selectedContacts = newContactsList;
  };

  $scope.getNumberContactsSelected = () => {
    return $scope.potentialContacts.length + '\u002f20';
  };

  // returns the index of the contact in the potential contact list
  $scope.indexOfPotentialContact = (contact) => {
    let index = -1;
    for (let i = 0; i < $scope.potentialContacts.length && index < 0; i++) {
      if ($scope.potentialContacts[i].ID === contact.ID) {
        index = i;
      }
    }
    return index;
  };

  $scope.isContactSelected = () => {
    let selectedContact = false;
    angular.forEach($scope.contactData, (contact) => {
      let index = $scope.indexOfPotentialContact(contact);
      if (index !== -1) {
        selectedContact = true;
      }
    });
    return $scope.potentialContacts.length || selectedContact;
  };

  // Deselect all contact
  $scope.deselectAllContacts = () => {
    $scope.potentialContacts = [];
  };

  $scope.addContacts = () => {
    if (!$scope.potentialContacts.length) {
      return;
    }
    $scope.hideContacts();
    $scope.selectedContacts = angular.copy($scope.potentialContacts);
  };

  $scope.cancelBook = () => {
    $scope.hideContacts();
  };

  // hide contact selection panel
  $scope.hideContacts = () => {
    $scope.contacts = false;
  };
  // watching the potential contact list to see if Select All should be shown
  let checkAllContactsSelected = () => {
    if (!$scope.contactData) {
      return;
    }

    let foundNotSelected = false;
    let potentialContactIds = ($scope.potentialContacts || []).map((val) => {
      return val.ID;
    });

    if ($scope.contactData.length > potentialContactIds.length) {
      foundNotSelected = true;
    } else if (potentialContactIds.length > 0) {
      for (let i = 0; i < $scope.contactData.length && !foundNotSelected; i++) {
        let contact = $scope.contactData[i];
        if (potentialContactIds.indexOf(contact.ID) === -1) {
          foundNotSelected = true;
        }
      }
    }

    $scope.allContactsSelected = !foundNotSelected;
  };
  $scope.$watch('potentialContacts', checkAllContactsSelected, true);
  $scope.$watch('contactData', checkAllContactsSelected);

  // watching for changes in tags
  $scope.$watch('selectedContacts', (newVal) => {
    let threadedContacts = {};
    if ($scope.id) {
      let contacts = {};

      for (let i = newVal.length - 1; i >= 0; --i) {
        if (newVal[i].text in contacts) {
          newVal.splice(i, 1);
        }
        contacts[newVal[i].text] = true;
      }

      threadedContacts[$scope.id] = newVal;
      localStorageService.set('sfContactsSelectedThreaded', threadedContacts);
    }
  }, true);

  $scope.pushEmailAndSelect = (email, id, threadId) => {
    let r = Math.floor((Math.random() * 1000000000) + 1000000);

    if (isPiiObfuscationEnabled && (id || threadId)) {
      $scope.selectedContacts.push({'ID': id, 'email': email, 'text': email});
    } else {
      $scope.selectedContacts.push({'id': r, 'text': email});
    }
  };

  // Threaded storage is use to always push current contacts to localstorage,
  // but only retrieve them if they are from the correponding thread
  // It is cleared when accessed from a clean compose message (no thread)
  // Or it is cleared from any other thread when accessed from a reply screen
  // This is a major ugly hack but I couldn't find any better way given the
  // horrible architecture. - Mike
  $scope.manageThreadedContacts = () => {
    let contacts = localStorageService.get('sfContactsSelectedThreaded');
    if (contacts) {
      contacts = localStorageService.get('sfContactsSelectedThreaded')[$scope.id];
    }
    if ($scope.id !== '') {
      let storageContactThreaded = {};
      storageContactThreaded[$scope.id] = contacts;
      localStorageService.set('sfContactsSelectedThreaded', storageContactThreaded);
    }

    $scope.selectedContacts = localStorageService.get('sfContactsSelected') || contacts || [];
    localStorageService.remove('sfContactsSelected');
    localStorageService.remove('sfCompose');
  };

  $scope.initComposeContacts = () => {
    return new Promise((resolve) => {
      let isAsync = false;
      let persistedRecipients = localStorageService.get('sfComposeRecipients');

      if (persistedRecipients && persistedRecipients.length) {
        $scope.selectedContacts = persistedRecipients;
      } else if ($scope.isGroupTaskType()) {
        groupTaskData.retailer_customer.text = $scope.getContactName(groupTaskData.retailer_customer);
        $scope.selectedContacts.push(groupTaskData.retailer_customer);
      } else if (typeof($routeParams.who) !== 'undefined' && !$scope.isGroupTaskType()) {
        isAsync = true;
        // if we are sending message to someone, mark that person
        contactService.getCustomers({
          id: $rootScope.currentUser.ID,
          in: $routeParams.who
        }).then((customers) => {
          $scope.selectedContacts = customers.filter(isCustomerData).map(contact => ({
            ...contact,
            text: $scope.getContactName(contact)
          }));
          resolve();
        }, () => {
          resolve();
        });
      }

      if (typeof($routeParams.email) !== 'undefined') {
        $scope.pushEmailAndSelect($routeParams.email);
      }

      if (!isAsync) {
        resolve();
      }
    });
  };

  let validEmails = [];

  const isInValidEmails = email => validEmails.find(item => item === email);

  $scope.validateEmail = (tag) => {
    if ($scope.cancelIsClicked) {
      return false;
    }

    if ($scope.isErrorShowing) {
      return false;
    }

    if (hasReachedMaximumSelectedContacts($scope.selectedContacts)) {
      $scope.isErrorShowing = feedbackService.showError(t('You have reached the maximum number of contacts you can add at one time'))
        .then(() => {
          $scope.isErrorShowing = false;
        });
      return false;
    }

    let email = tag.email ? tag.email : tag.text;
    //if we have tag.email, the email is coming from the contact's data
    //if isPiiObfuscationEnabled, this data is is obfuscated, and we'll assume is valid
    if (isPiiObfuscationEnabled && tag.email) {
      validEmails.push(email);
    }
    if (email && !$rootScope.validateEmail(email) && !isInValidEmails(email)) {
      $scope.isErrorShowing = feedbackService.showError(t('Invalid email address: _email_', { email: email }))
        .then(() => {
          $scope.isErrorShowing = false;
        });
      return false;
    }

    return true;
  };

  $scope.initComposeObject = () => {
    $scope.compose = {
      'title': '',
      'to': {
        'customers':[],
        'emails': []
      },
      'cc': {
        'customers':[],
        'emails': []
      },
      'message': '',
      'threadId': '',
      'sendCopy': false,
      'request_type': '',
      'inLibrary': false,
      'autoload_draft': false,
      'products': [],
    };
  };

  $scope.hasMessage = () => {
    const messageContents = $scope.compose.message || '';
    if ($scope.isWysiwygEnabled()) {
      const elm = document.createElement('div');
      elm.innerHTML = messageContents;
      const plainTextContents = String(elm.textContent || elm.innerText).trim();
      return plainTextContents.length > 0;
    } else {
      return messageContents.length > 0;
    }
  };
  const hasTitle = () => $scope.compose.title && $scope.compose.title.length;
  const hasContact = () => $scope.selectedContacts && $scope.selectedContacts.length;
  const hasAttachmentStorage = () => localStorageService.get('sfAsset') || localStorageService.get('sfProduct');
  
  const hasContent = () => hasContact() || $scope.hasMessage() || hasTitle() || $scope.hasProducts() || $scope.hasAsset() || $scope.hasPhotos() || hasAttachmentStorage();

  $scope.hasContentFlag = false; // only for use in template, prevent execution of hasContent() every render

  $scope.setHasContentFlagDebounced = debounce(() => {
    $scope.hasContentFlag = hasContent();
    $scope.debouncedApply();
  }, 300);

  const deleteAndNavigateAway = () => {
    if ($scope.isDraftSystemEnabled) {
      $scope.shouldAutoloadDraftOnReturn = false;
      $scope.autoSaveEnabled = false; // disable auto save when leaving page
      draftService.stopLifecycle();
      $scope.deleteCurrentDraft();
    }

    productsDrawerService.repopulateAttachmentsStorage();
    $scope.clearStorageDataAndGoBack(true);
  };

  const keepChangesAndNavigateAway = async () => {
    feedbackService.closeFeedback();

    // save if any new changes from debounced ui input
    if ($scope.autoSaveEnabled && $scope.hasChangedSinceDraftSave) { 
      await $scope.triggerDraftAutoSave();
    }
    $scope.shouldAutoloadDraftOnReturn = false;
    $scope.autoSaveEnabled = false; // already saved dont save again
    draftService.stopLifecycle();

    productsDrawerService.repopulateAttachmentsStorage();
    $scope.clearStorageDataAndGoBack(true);
  };
  
  const promptToDiscardDraft = () => {
    feedbackService.showFeedback({
      template: 'views/directives/feedback-content-button-modal-horizontal.html',
      message: t('Would you like to discard or save your changes?'),
      hideFooter: true, // remove "stay button"
      context: [
        {
          text: t('Discard'),
          type: 'cancel',
          display: true,
          action: () => {
            feedbackService.closeFeedback();
            deleteAndNavigateAway();
          },
        },
        {
          text: t('Save'),
          type: 'prompt',
          display: true,
          action: keepChangesAndNavigateAway,
        },
      ],
      buttons: {
        close: t('Stay'),
      },
    });
  };

  // on cancel message - destroy mssage in local storage and redirect
  $scope.cancelMessage = async () => {
    if ($scope.isDraftSystemEnabled && $scope.autoSaveEnabled) {
      await $scope.triggerDraftAutoSave();
    }

    if (!hasContent()) {
      // if form is empty clean up any junk saves
      deleteAndNavigateAway();
    } else {
      if ($scope.isDraftSystemEnabled) {
        promptToDiscardDraft();
      } else {
        feedbackService.showPrompt(t('Discard your changes?', {ns: 'common'})).then(deleteAndNavigateAway);
      }
    }
  };

  $scope.clearStorageDataAndGoBack = (fromCancel) => {
    const storedReqParams = storageService.getTempItem('tmpReqParams', true);
    const isReplyRequest = $location.path().indexOf('reply-request') > -1;
    const fromContactsProducts = localStorageService.get('sfFromContactsProducts');

    $scope.clearStorageData(fromCancel);

    // On a request reply, we want to go back 2 in history to end up on listing
    if ((isReplyRequest || fromContactsProducts) && !fromCancel && !storedReqParams?.requestId) {
      return navService.go(-2);
    }

    if (($scope.isFromBrowse && !fromCancel) || shouldGoBackToDashboard()) {
      return navService.goTo('dashboard');
    }
    navService.goBack();
  };

  // Note: if any keys are added here also add to StorageCleanupService to handle cleanup on app close
  $scope.clearStorageData = (fromCancel) => {
    localStorageService.remove('sfCompose');
    localStorageService.remove('sfComposeRecipients');
    localStorageService.remove('sfComposeTo', '');
    localStorageService.remove('sfAttachmentLang');
    localStorageService.remove('sfContactsSelectedThreaded');
    localStorageService.remove('sfEmailGroupedProductsToggleStatus');
    !fromCancel && localStorageService.remove('sfBrowseMode');
    $scope.attachment = {};
    $scope.uploaded = false;
    messageService.clearMessageSharingDataStorage();
    if ($scope.contactsSearchController && $scope.contactsSearchController.reset) {
      $scope.contactsSearchController.reset();
    }
    localStorageService.remove('sfFromContactsProducts');
    localStorageService.remove('sfAsset');
    localStorageService.remove('sfAssetProps');
    localStorageService.remove('sfContactsSelected');
  };

  // will toggle 'send me a copy' variable
  $scope.toggleSendCopy = () => {
    $scope.compose.sendCopy = !$scope.compose.sendCopy;
  };

  // watching for changes in compose object (if user goes to product select subsection, we will be able to reconstruct his message later)
  $scope.$watch('compose', () => {
    localStorageService.set('sfCompose', $scope.compose);
  }, true);


  $scope.setThreadId = () => {
    if ($scope.compose.threadId !== '') {
      $location.search('thread', $scope.compose.threadId);
    } else if ($location.search().thread) {
      $scope.compose.threadId = $location.search().thread;
    } else {
      if ($scope.compose.inLibrary) {
        // Coming back from library
        $scope.compose.inLibrary = false;
      } else if ($scope.compose.inLibrary === false) {
        localStorageService.remove('sfContactsSelectedThreaded');
      }
    }
  };

  $scope.validate = (showFeedback) => {
    if ($rootScope.isLoading || $scope.uploading) {
      return false;
    }
    if (!$scope.selectedContacts.length) {
      return false;
    }

    const { message, title, assets, products } = $scope.compose;

    if (!message || !message.length) {
      return false;
    }
    if (!title || !title.length) {
      return false;
    }

    const attachments = {
      asset: assets,
      photos: $scope.attachment.content,
      products: products || []
    };

    return $scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled
      ? emailTemplateValidatorService.validateMultipleTemplate($scope.templateEmail, attachments, showFeedback)
      : true;
  };

  $scope.getPlaceholder = () => {
    if (!$scope.selectedContacts.length) {
      return t('Type email or pick from your contact list');
    }
  };

  $scope.$watchCollection('selectedContacts', () => {
    $scope.placeholder = $scope.selectedContacts.length > 0 ? '' : t('Type email or pick from your contact list');
    localStorageService.set('sfContactsSelected', $scope.selectedContacts);
  });

  const getGroupTaskId = () => $scope.compose.group_task_id || groupTaskData?.id;

  const successMessageSent = () =>
    shouldAutoResolveTask()
      ? t('Email sent. Task is automatically resolved.')
      : t('Message sent');


  const deleteCachedApi = () => {
    const deleteItemKey = $scope.isGroupTaskType() ? `${GROUP_TASK_ITEM_DELETE_KEY_PREFIX}${getGroupTaskId()}` : `${TASK_ITEM_DELETE_KEY_PREFIX}${taskId}`;
    const deleteListKey = $scope.isGroupTaskType() ? GROUP_TASK_LIST_DELETE_KEY : TASK_LIST_DELETE_KEY;
    apiCache.triggerDeleteKeys([deleteListKey, deleteItemKey]);
  };

  // will send message after composing
  $scope.sendMessage = () => {
    if (!$scope.validate(true)) {
      return;
    }

    let previousAutoSaveEnabledState = false;
    if ($scope.isDraftSystemEnabled) {
      // Disable auto save past this point
      // But allow it to be re-enbled should validation fail
      previousAutoSaveEnabledState = $scope.autoSaveEnabled;
      $scope.autoSaveEnabled = false;
    }

    // apply any missing styles
    $scope.compose.message = $scope.getFormattedMessage();

    $scope.shareBtnText = t('Sending...');
    $scope.compose.to.customers = [];
    $scope.compose.to.emails = [];
    $scope.compose.templateEmail = $scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled
      ? $scope.templateEmail.id : '';

    if ($scope.isGroupTaskType()) {
      $scope.compose.to.retailer_customers = [];

      if (!$scope.compose.group_task_id) {
        $scope.compose.group_task_id = groupTaskData?.id;
      }
    }

    let attachments = $scope.attachment.content || [];
    $scope.compose.attachment = attachments.map((photo) => {
      return photo.url;
    });
    if ($scope.compose.attachment.length > 0 ) {
      $scope.compose.attachment = JSON.stringify($scope.compose.attachment);
    } else {
      delete $scope.compose.attachment;
    }

    const asset = $scope.assetProp || ($scope.compose.assets || [])[0];

    if (asset) {
      $scope.compose.attachment = 'asset_' + asset.id;
      delete $scope.compose.assets;
    }

    let emails = [];

    angular.forEach($scope.selectedContacts, (contact) => {
      const { ID, email, text, id } = contact;
      const isCustomers = ID && email;
      const isRetailerCustomer = $scope.isGroupTaskType() && id && email;
      //if we have email, the email is coming from the contact's data
      //if isPiiObfuscationEnabled, this data is is obfuscated, and we'll assume is valid
      if (isPiiObfuscationEnabled && email) {
        validEmails.push(email);
      }
      const currentEmail = email || text;

      if (isCustomers) {
        $scope.compose.to.customers.push(ID);
      } else if (isRetailerCustomer) {
        $scope.compose.to.retailer_customers.push(id);
      } else {
        $scope.compose.to.emails.push(currentEmail);
      }

      emails.push(currentEmail);
    });

    if ($scope.compose.to.customers.length === 0) {
      delete $scope.compose.to.customers;
    }

    if ($scope.compose.to.emails.length === 0) {
      delete $scope.compose.to.emails;
    }

    for (const i in emails) {
      if (!isInValidEmails(emails[i]) && !$rootScope.validateEmail(emails[i])) {
        $scope.shareBtnText = t('Send message');
        feedbackService.showError(t('Invalid email address: _email_', { email: emails[i] }));
        if ($scope.isDraftSystemEnabled) {
          $scope.autoSaveEnabled = previousAutoSaveEnabledState;
        }
        return;
      }
    }

    let threadId = getThreadId();
    if (threadId) {
      $scope.compose.threadId = threadId;
    }
    //Create a new thread if the message was forwarded - TD-1395
    if ($scope.isForwardedMessage) {
      $scope.compose.threadId = '';
    }
    //task_id is needed for autoResolve a task, for group task we'll have $scope.compose.group_task_id
    if (taskId && !$scope.isGroupTaskType()) {
      $scope.compose.task_id = taskId;
    }

    // send message
    let originalMessage = $scope.compose.message;
    $scope.compose.message = $scope.compose.message.replace($scope.product, '');

    // locale for template
    $scope.compose.templateLocale = $scope.currentLangTemplate;

    if ($scope.compose.title.length === 0) {
      $scope.compose.title = t('New Message');
    }

    if ($scope.isGroupedProductsToggleEnabled) {
      $scope.compose.groupedProductsOn = $scope.isGroupedProductsToggleEnabled;
    } else {
      delete $scope.compose.groupedProductsOn;
    }

    $scope.compose.isHtml = $scope.isWysiwygEnabled();

    messageService.sendStoreMessage($scope.compose).then((data) => {
      productsService.setSharedProducts($rootScope.currentUser.ID, $scope.compose.products);
      // success
      if (data && data[0] && data[0].success === true) {
        $scope.parseUrlResponse = null;
        $scope.product = null;
        $scope.assetProp = null;
        localStorageService.set('sfAssetProps', '');
        localStorageService.set('sfComposeTo', '');
        if ($scope.isDraftSystemEnabled) {
          $scope.deleteCurrentDraft();
          $scope.shouldAutoloadDraftOnReturn = false;
        }

        if (shouldAutoResolveTask()) {
          //if email is from a task and the AutoResolveTask is enabled
          //delete cached api so task and task list will be fetched again
          deleteCachedApi();
        }
        $scope.clearStorageDataAndGoBack();
        feedbackService.showSuccess(successMessageSent());
      } else {
        feedbackService.showError(t('Sorry, sending message failed'));
        $scope.shareBtnText = t('Send message');
      }
    }, () => {
      $scope.compose.message = originalMessage;
      $scope.shareBtnText = t('Send message');
      if ($scope.isDraftSystemEnabled) {
        $scope.autoSaveEnabled = previousAutoSaveEnabledState;
      }
    });
  };
  const isMessageSentByContact = () => $scope.data.messages.train[0].sender.type === 'Customer';

  $scope.getSingleMessage = () => {
    return new Promise((resolve) => {
      const threadId = $scope.compose.threadId;
      messageService.getStoreSingleMessage({
        'id': threadId
      }).then((data) => {
        $scope.data = data;
        if ($location.search().forward) {
          $scope.isForwardedMessage = true;
          $scope.compose.title = 'Fwd: ' + $scope.data.messages.title;
          if (!$scope.hasMessage()) {
            let message = $scope.data.messages.train[0].content;
            let isMessageHtml = $rootScope.utils.containsHtml(message);
            if ($scope.isWysiwygEnabled()) {
              if (isMessageHtml) {
                $scope.compose.message = '<p><br/></p><p>Forwarded message:</p>' + message;
              } else {
                $scope.compose.message = $rootScope.utils.plainTextToWysiwyg('\nForwarded message:\n' + message);
              }
            } else {
              if (isMessageHtml) {
                $scope.compose.message = '\nForwarded message:\n' + $rootScope.utils.htmlToPlainText(message);
              } else {
                $scope.compose.message = '\nForwarded message:\n' + message;
              }
            }
          }
        } else {
          $scope.compose.title = 'Re: ' + $scope.data.messages.title;
          if ($scope.selectedContacts.length === 0) {
            const contactEmail = isMessageSentByContact() ? $scope.data.messages.train[0].sender.email : $scope.data.messages.train[0].receiver.email;
            const contactId = isMessageSentByContact() ? $scope.data.messages.train[0].sender.ID : $scope.data.messages.train[0].receiver.ID;

            $scope.pushEmailAndSelect(contactEmail, contactId, threadId);
          }
        }
        resolve();
      }, () => { 
        resolve(); 
      });
    });
  };

  const isRequestMadeByRep = () => $scope.data.messages.owner && $scope.data.messages.owner.type !== 'Customer';

  $scope.getSingleRequest = () => {
    return new Promise((resolve) => {
      messageService.getSingleStoreRequest({
        'id': $scope.id
      }).then((data) => {
        $scope.data = data;
        let request = data.messages.storeRequest;
        const threadId = data.messages.threadID;
        $scope.compose.title = request.localizedTitle || request.title;
  
        if (!$scope.selectedContacts.length) {
          const contactEmail = isRequestMadeByRep() ? request.receiver.email : request.sender.email;
          const contactId = isRequestMadeByRep() ? request.receiver.ID : request.sender.ID;
          $scope.pushEmailAndSelect(contactEmail, contactId, threadId);
        }
  
        if (request.cannedResponse && !$scope.hasMessage() && isReplyRequest) {
          let isWysiwyg = $scope.isWysiwygEnabled();
          let isMessageHtml = $rootScope.utils.containsHtml(request.cannedResponse);
          if (isWysiwyg) {
            if (isMessageHtml) {
              $scope.compose.message = request.cannedResponse;
            } else {
              $scope.compose.message = $rootScope.utils.plainTextToWysiwyg(request.cannedResponse);
            }
          } else {
            if (isMessageHtml) {
              $scope.compose.message = $rootScope.utils.htmlToPlainText(request.cannedResponse);
            } else {
              $scope.compose.message = request.cannedResponse;
            }
          }
        }
  
        if (data.messages.storeRequest.locale) {
          $scope.currentLangTemplate = data.messages.storeRequest.locale;
        }
        resolve();
      }, () => {
        resolve();
      });
    });
  };

  $scope.retrieveAsset = (assetData) => {
    const asset = localStorageService.get('sfAssetProps') || assetData;
    if (!asset) {
      return;
    }

    const { id, label, subject, imageUrl, permanentUrl } = asset;

    $scope.parseUrlResponse = {
      id,
      label,
      image: imageUrl,
      title: $sce.trustAsHtml(subject).toString(),
      description: ''
    };

    angular.extend(asset, $scope.parseUrlResponse);

    $scope.lastParsedLink = permanentUrl;
    $scope.compose.assets = [];
    $scope.compose.assets.push($scope.parseUrlResponse);

    productsDrawerService.saveAttachmentsForDrawer('asset', asset, $scope.isFromBrowse);

    productsService.updateAssetStorage();
  };

  $scope.retrieveProduct = (productsData) => {
    let product = localStorageService.get('sfProductProps');
    let productsList = productsData || localStorageService.get('sfProductList') || [];

    if (!product && !productsList.length) {
      return;
    }

    const pushProduct = (product) => {
      const productIsSelected = productsService.findInProducts($scope.compose.products, product);

      if (productIsSelected) {
        return;
      }

      product.title = product.selectedVariant
        ? product.selectedVariant.name
        : product.name;

      $scope.productProps = product;
      $scope.lastParsedLink = product.productUrl;
      $scope.lastFetchedProductData = product;

      product.description = $sce.trustAsHtml(product.shortDescription || product.description || '').toString().slice(0,100) + '...';
      product.image = product.selectedVariant && product.selectedVariant.imageUrl
        ? product.selectedVariant.imageUrl
        : product.thumbnailImage || product.image250 || product.img;
      product.product_ID = product.sku;
      product.thumbnailImage = product.image;

      $scope.parseUrlResponse = product;

      $scope.compose.products = $scope.compose.products || [];
      $scope.compose.products.push(product);

      localStorageService.remove('sfProductProps');
    };

    if (productsList && productsList.length) {
      productsList.forEach((product) => {
        localStorageService.set('sfProductProps', product);
        pushProduct(product);
      });
    } else if (product) {
      pushProduct(product);
    }

    productsDrawerService.saveAttachmentsForDrawer('product', productsList, $scope.isFromBrowse);

    localStorageService.remove('sfProductList');
  };

  let clearProductData = () => {
    $scope.parseUrlResponse = null;
    $scope.lastParsedLink = '';
    saveProducts(null);
    localStorageService.set('sfProductProps', '');
    localStorageService.set('sfAsset', '');
  };

  let clearAssetData = () => {
    // setting assetProp to null in order to fix issue with SF-30408
    $scope.assetProp = null;
    $scope.parseUrlResponse = null;
    localStorageService.set('sfAsset', '');
    localStorageService.set('sfAssetProps', '');
    $scope.lastParsedLink = '';
  };

  let clearPhotoData = () => {
    $scope.attachment.content = [];
    savePhotos(null);
  };

  $scope.removePhotoReadyForShare = (i) => {
    $scope.attachment.content.splice(i, 1);
    savePhotos();
    refreshButtonsStates();
  };

  // User removes object pepared for share (by clicking big red X button)
  $scope.removeProductReadyForShare = (i) => {
    clearProductData();

    $scope.compose.products.splice(i, 1);
    if ($scope.compose.products.length > 0) {
      $scope.parseUrlResponse = $scope.compose.products[0];
      $scope.lastParsedLink = $scope.parseUrlResponse.productUrl;
    }

    if (!$scope.compose.products.length) {
      $scope.compose.products = null;
      refreshButtonsStates();
    }

    if ($scope.isGroupedProductsToggleEnabled && $scope.compose.products?.length < groupedProductsMinProducts) {
      setGroupedProductsToggleStatus(false);
    }
  };

  $scope.removeAllObjectsReadyForShare = () => {
    clearAssetData();
    clearProductData();
    clearPhotoData();

    $scope.compose.assets = null;
    $scope.compose.products = null;
    localStorageService.remove('sfProductList');
    localStorageService.set('sfCompose', $scope.compose);
    refreshButtonsStates();
  };

  $scope.removeAssetReadyForShare = (i) => {
    clearAssetData();

    $scope.compose.assets.splice(i || 0, 1);
    if ($scope.compose.assets.length > 0) {
      $scope.parseUrlResponse = $scope.compose.assets[0];
      $scope.lastParsedLink = $scope.parseUrlResponse.productUrl;
    }

    if (!$scope.compose.assets.length) {
      $scope.compose.assets = null;
      refreshButtonsStates();
    }
  };

  $scope.getProducts = () => $scope.compose.products || [$scope.compose.product];

  $scope.getAssets = () => {
    if (!$scope.compose.assets) {
      return;
    }
    if ($scope.compose.assets.length > 1) {
      $scope.compose.assets.splice(0, $scope.compose.assets.length - 1);
    }
    return $scope.compose.assets;
  };

  const setSecondSelectAfterBrowse = () => {
    if ($scope.isFromBrowse) {
      localStorageService.set('sfSecondSelectAfterBrowse', true);
    }
  };

  $scope.attachProduct = () => {
    if ($scope.isProductDisabled()) {
      return false;
    }
    if ($scope.isDraftSystemEnabled) {
      $scope.shouldAutoloadDraftOnReturn = false;
    }
    let products = $scope.compose.products || [];
    let count = getMaxNumberOfProducts() - products.length;

    localStorageService.set('sfMaxCountProducts', count);

    if (count === 0) {
      feedbackService.showError(CONST_FEEDBACK.BLOCK_PRODUCT_DUE_MAX_LIMIT);
      return;
    }

    $scope.compose.inLibrary = true;
    localStorageService.set('sfComposeRecipients', $scope.selectedContacts);
    localStorageService.set('sfContactsSelected', $scope.selectedContacts);
    localStorageService.set('sfAttachmentLang', $scope.currentLangTemplate);
    groupTaskService.storeGroupTaskData(groupTaskData);
    corporateTaskService.storeCorporateTaskData(corporateTaskData);
    setSecondSelectAfterBrowse();

    // CPD-1931 give ios time to close virtual keyboard before redirect to the products page
    $timeout(() => navService.goTo('/share/search'), 100);
  };

  const getMaxNumberOfProducts = () => {
    const defaultProducts = CONST_SHARE.MAX_PRODUCTS;
    const groupedProductsMax = $scope.isGroupedProductsToggleEnabled && groupedProductsMaxProducts;
    const templateEmailProductsMax = $scope.isMultipleEmailTemplatesEnabled && $scope.templateEmail['products'].max;
    return groupedProductsMax || templateEmailProductsMax || defaultProducts;
  };

  const getMaxNumberOfPhotos = () => {
    const defaultPhotos = $rootScope.conf('CameraMaxPhotos', CONST_SHARE.MAX_PHOTOS);
    const groupedPhotosMax = $scope.isGroupedProductsToggleEnabled && groupedProductsMaxPhotos;
    const templateEmailPhotosMax = $scope.isMultipleEmailTemplatesEnabled && $scope.templateEmail['photos'].max;
    return groupedPhotosMax || templateEmailPhotosMax || defaultPhotos;
  };

  $scope.attachAsset = () => {
    if ($scope.isDraftSystemEnabled) {
      $scope.shouldAutoloadDraftOnReturn = false;
    }

    if ($scope.hasPhotos() && !$scope.isGroupedProductsToggleEnabled) {
      return feedbackService.showError(CONST_FEEDBACK.BLOCK_ASSET_DUE_MIXED);
    }

    if ($scope.isGroupedProductsToggleEnabled) {
      return feedbackService.showError(groupedProductsValidatorService.errorMessageAssets);
    }

    if ($scope.isAssetDisabled()) {
      return false;
    }

    $scope.compose.inLibrary = true;
    localStorageService.set('sfComposeRecipients', $scope.selectedContacts);
    localStorageService.set('sfContactsSelected', $scope.selectedContacts);
    localStorageService.set('sfAttachmentLang', $scope.currentLangTemplate);
    groupTaskService.storeGroupTaskData(groupTaskData);
    corporateTaskService.storeCorporateTaskData(corporateTaskData);

    localStorageService.remove('sfAssetPage');
    setSecondSelectAfterBrowse();

    // CPD-1931 give ios time to close virtual keyboard before redirect to the assets page
    $timeout(() => navService.goTo('/share/assets/all'), 100);
  };

  // user clicks Remove image for attached image
  $scope.removeImageFromAttachment = () => {
    $scope.uploaded = false;
    $scope.attachment = {};

    savePhotos(null);
    refreshButtonsStates();
  };

  $scope.attachPhoto = (inputFiles = null) => {
    if ($scope.isDraftSystemEnabled) {
      $scope.shouldAutoloadDraftOnReturn = false;
    }
    if (window.cordova) {
      repUploadedPhotosService.requestReadPermission();
    }
    if ($scope.hasAsset()) {
      return feedbackService.showError(CONST_FEEDBACK.BLOCK_PHOTO_DUE_ASSET);
    }
    if ($scope.isGroupedProductsToggleEnabled && $scope.isPhotoDisabled()) {
      return feedbackService.showError(groupedProductsValidatorService.errorMessageMultiplePhotos);
    }
    if ($scope.isPhotoDisabled()) {
      return false;
    }
    if (inputFiles) {
      if (inputFiles.length === 0) {
        return;
      }
      const maxPhotos = getMaxNumberOfPhotos();
      if (inputFiles.length > maxPhotos) {
        return feedbackService.showError(t('You can attach up to _count_ photos', {
          count: maxPhotos
        }));
      }
      $scope.startPhotoSelection(CONST_SERVICE.REP_PHOTOS_TYPE_INPUT_FILES_DESKTOP, inputFiles);
      return;
    }

    localStorageService.set('sfAttachmentLang', $scope.currentLangTemplate);

    feedbackService.showOptions('', {
      buttons: {
        confirm: CONST_SERVICE.REP_PHOTOS_BUTTON_CAMERA,
        cancel: CONST_SERVICE.REP_PHOTOS_BUTTON_GALLERY,
        confirmIconClassModifier: CONST_SERVICE.REP_PHOTOS_BUTTON_CAMERA_CLASS_MODIFIER,
        cancelIconClassModifier: CONST_SERVICE.REP_PHOTOS_BUTTON_GALLERY_CLASS_MODIFIER
      }
    }).then(() => {
      $scope.startPhotoSelection(CONST_SERVICE.REP_PHOTOS_TYPE_CAMERA);
    }, (result) => {
      if (result === false) {
        return;
      }
      $scope.startPhotoSelection(CONST_SERVICE.REP_PHOTOS_TYPE_CAMERA_ROLL_MULTI);
    });
  };

  $scope.startPhotoSelection = (type, inputFiles = null) => {
    let photosToAdd = [];

    $scope.uploading = true;
    repUploadedPhotosService.selectAndUpload({
      max: getMaxNumberOfPhotos(),
      type,
      inputFiles,
      onSuccess: (data) => {
        // success
        if (data) {
          photosToAdd.push(data.url);
          if (type === CONST_SERVICE.REP_PHOTOS_TYPE_CAMERA_ROLL_MULTI ||
              type === CONST_SERVICE.REP_PHOTOS_TYPE_INPUT_FILES_DESKTOP) {
            return;
          }
        }
        addPhotos(photosToAdd);
      },
      onFail: (error) => {
        // fail
        $scope.uploaded  = $scope.attachment.content ? true : false;
        $scope.uploading = false;
        loggerService.logError('[messages-compose.js] could not select and upload images: ', error);
      }
    });
  };

  const addPhotos = (photosToAdd) => {
    let photos = $scope.attachment.content || [];

    $scope.uploading = false;
    $scope.uploaded = true;

    checkMaxPhotos(photosToAdd).then(() => {
      let numPhotosToRemove = Math.max(photos.length + photosToAdd.length - getMaxNumberOfPhotos(), 0);
      photos.splice(0, numPhotosToRemove);
      if (photos.length < 1) {
        $scope.attachment.photoCount = 0;
      }
      photos = photos.concat(photosToAdd.map((url) => {
        let seq = ++$scope.attachment.photoCount;
        return {
          url: url,
          seq: seq,
          label: 'Photo ' + seq
        };
      }));
      $scope.attachment.content = photos;
      savePhotos();
    });
  };

  const checkMaxPhotos = (toAdd) => {
    let maxPhotos = getMaxNumberOfPhotos();
    if (($scope.attachment.content || []).length + toAdd.length > maxPhotos) {
      return feedbackService.showError(t('You can attach up to _count_ photos. Additional selections will replace previously selected ones.', {
        count: maxPhotos
      }));
    }
    return $q.when();
  };

  $scope.recoverRepUploadedPhotosFromStorage = () => {
    let photos = localStorageService.get('sfRepUploadedPhoto') || [];
    $scope.attachment.type = 'photo';
    $scope.attachment.content = photos;
    $scope.attachment.photoCount = photos.length;
    $scope.uploaded = photos.length > 0;
  };


  $scope.recoverProductsFromStorage = () => {
    // if product object is stored in memory, recover it
    if (localStorageService.get('sfProduct') || localStorageService.get('sfProductList')) {
      // get product
      $scope.product = localStorageService.get('sfProduct');
      $scope.productProp = localStorageService.get('sfProductProps');

      $scope.retrieveProduct();

      // destroy product in local storage
      saveProducts(null);
    }
  };

  $scope.recoverAssetsFromStorage = () => {
    const asset = localStorageService.get('sfAsset');
    if (!asset) {
      return;
    }
    const assetProp = localStorageService.get('sfAssetProps');

    $scope.asset = asset;
    $scope.assetProp = assetProp;

    $scope.retrieveAsset();
  };

  $scope.reorderProducts = (startPos, newPos) => {
    let product = $scope.compose.products.splice(startPos, 1)[0];
    $scope.compose.products.splice(newPos, 0, product);

    saveProducts();
  };

  $scope.reorderPhotos = (startPos, newPos) => {
    let photos = $scope.attachment.content;
    let photo = photos.splice(startPos, 1)[0];
    photos.splice(newPos, 0, photo);

    savePhotos();
  };

  const saveProducts = (products) => {
    if (products === undefined) {
      products = $scope.compose.products;
    }
    if (!products) {
      return localStorageService.remove('sfProduct');
    }
    localStorageService.set('sfProduct', products);
  };

  const savePhotos = (photos) => {
    if (photos === undefined) {
      photos = $scope.attachment.content;
    }
    if (!photos) {
      return localStorageService.remove('sfRepUploadedPhoto');
    }
    localStorageService.set('sfRepUploadedPhoto', photos);
  };

  $scope.setMode = () => {
    // messages or requests setup
    if ($location.path().substring(0, 15) === '/store-messages') {
      $scope.mode = 'message';
    } else if ($location.path().substring(0, 15) === '/store-requests') { // CR setup
      $scope.mode = 'customer_request';
    }
  };

  $scope.getDefaultEmailTemplates = () => {
    let templatesList = $rootScope.conf('RetailerEmailTemplatesLayouts');
    let defaultTemplate;
    let selectedTemplate = localStorageService.get('sfTemplateEmail');

    // Don't try to find the default template when this feature is not enabled
    // You still need to populate the default when you don't have "email selected" yet
    if (!$scope.isMultipleEmailTemplatesEnabled) {
      return null;
    }

    Object.keys(templatesList).forEach((elm) => {
      if (templatesList[elm].default) {
        defaultTemplate = elm;
      }
    });

    return (selectedTemplate) ? selectedTemplate : templatesList[defaultTemplate];
  };

  $scope.hasPhotos = () => ($scope.attachment.content || []).length > 0;
  $scope.hasAsset = () => ($scope.compose.assets || []).length > 0;
  $scope.hasProducts = () => ($scope.compose.products || []).length > 0;
  $scope.hasAttached = (type) => {
    const hasProducts = $scope.hasProducts();
    if (type === 'product') {
      return hasProducts;
    }

    const hasAsset = $scope.hasAsset();
    if (type === 'asset') {
      return hasAsset;
    }

    const hasPhotos = $scope.hasPhotos();
    if (type === 'photo') {
      return hasPhotos;
    }

    return hasProducts || hasAsset || hasPhotos;
  };


  const disableMultipleTemplateOptions = ({ max }, len) =>
    max === 0 || (max > 0 && len >= max) || $scope.uploading;

  $scope.isPhotoDisabled = () => {
    if ($scope.isMultipleEmailTemplatesEnabled) {
      return disableMultipleTemplateOptions($scope.templateEmail['photos'],
        ($scope.attachment.content || []).length) || $scope.hasAsset();
    }
    if ($scope.isGroupedProductsToggleEnabled) {
      return $scope.attachment.content?.length > 0;
    }
    return $scope.hasAsset() || $scope.uploading;
  };

  $scope.isAssetDisabled = () => {
    if ($scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled) {
      return disableMultipleTemplateOptions($scope.templateEmail['assets'],
        $scope.hasAsset() ? 1 : 0) || $scope.hasPhotos();
    }
    return $scope.isGroupedProductsToggleEnabled || $scope.hasPhotos() || $scope.uploading;
  };

  $scope.isProductDisabled = () => {
    if (!$scope.hasProductsFeed) {
      return true;
    }
    if ($scope.isMultipleEmailTemplatesEnabled && !$scope.isGroupedProductsToggleEnabled) {
      return disableMultipleTemplateOptions($scope.templateEmail['products'],
        ($scope.compose.products || []).length);
    }
    if ($scope.isGroupedProductsToggleEnabled) {
      return $scope.compose.products?.length >= groupedProductsMaxProducts;
    }
    return $scope.uploading;
  };

  $scope.handleEmailTemplateChange = (template) => {
    $scope.templateEmail = template;
    localStorageService.set('sfTemplateEmail', template);
  };

  $scope.getAllCustomerTags = () => {
    if (!$scope.retailerHasCustomerTags) {
      return $q.when(null);
    }

    if (!$scope.customerTagsPromise) {
      $scope.customerTagsPromise = contactService.getAllContactTags();
    }

    return $scope.customerTagsPromise;
  };

  // Extracts the data from a corporate task
  const populateComposeObjectWithTaskData = () => {
    if (!corporateTaskData && !groupTaskData) {
      return;
    }

    let messgeContents = corporateTaskData?.body || groupTaskData?.suggested_copy || '';
    let isMessageHtml = $rootScope.utils.containsHtml(messgeContents);
    if ($scope.isWysiwygEnabled()) {
      if (!isMessageHtml) {
        messgeContents = $rootScope.utils.plainTextToWysiwyg(messgeContents);
      }
    } else {
      if (isMessageHtml) {
        messgeContents = $rootScope.utils.htmlToPlainText(messgeContents);
      }
    }
    
    const composeObj = {
      title: corporateTaskData?.subject || groupTaskData?.suggested_subject_line || '',
      message: messgeContents,
    };

    $scope.retrieveProduct(corporateTaskData?.products || groupTaskData?.products );
    $scope.retrieveAsset(corporateTaskData?.assets[0] || groupTaskData?.assets[0]);

    $scope.compose = angular.extend({}, $scope.compose, composeObj);
  };

  // trigger rerender without intrupting async await
  // used at the end of functions to trigger ui update
  $scope.debouncedApply = debounce(() => {
    $scope.$apply();
  }, 300);

  $scope.init = async () => {
    $scope.setMode();

    // is first time loading email from blank state
    let isInitialState = false;

    localStorageService.set('sfCurrentPage', $location.$$path.substring(1));

    // Load context from local storage
    groupTaskData = groupTaskService.getGroupTaskData();
    corporateTaskData = corporateTaskService.getCorporateTaskData();
    
    // if compose object is stored in memory, recover it
    $scope.compose = localStorageService.get('sfCompose');
    if ($scope.compose) {
      $scope.setThreadId();
      $scope.manageThreadedContacts();
    } else {
      $scope.initComposeObject();
      populateComposeObjectWithTaskData();
      isInitialState = true;
    }

    // Get default email templates from configs
    // This needs to be executed before any await is called since emailTemplateSelector component needs this on init
    $scope.templateEmail = $scope.getDefaultEmailTemplates();

    // Then set the content of the compose object based on the URL
    if ($scope.mode === 'message') {
      if ($location.search().thread) {
        $scope.compose.threadId = $location.search().thread;
        await $scope.getSingleMessage();
      }
    } else if ($scope.mode === 'customer_request') {
      if ($scope.id) {
        await $scope.getSingleRequest();
      }
    }

    await $scope.initComposeContacts();

    $scope.recoverProductsFromStorage();
    $scope.recoverAssetsFromStorage();
    $scope.recoverRepUploadedPhotosFromStorage();
    $scope.isGroupedProductsToggleEnabled = localStorageService.get('sfEmailGroupedProductsToggleStatus') || false;

    $scope.hasContentFlag = hasContent();

    // Compose should be fully loaded by here
    if ($scope.isDraftSystemEnabled) {
      await initDraftSystem(isInitialState);
    }
    $scope.isInitialized = true;
  };

  $scope.triggerDraftAutoSave = async () => {
    if ($scope.autoSaveEnabled && $scope.isInitialized) {
      $scope.hasChangedSinceDraftSave = false;
      await draftService.triggerSave();
    }
  };

  $scope.triggerDraftAutoSaveDebounced = debounce(() => {
    $scope.triggerDraftAutoSave();
  }, draftService.getDraftAutoSaveInterval());

  // Save a version of the initial form to be able to restore it later
  const saveInitialFormData = async () => {
    await draftService.triggerSaveCheckpoint(DRAFT_BLANK_KEY);
  };

  $scope.restoreInitialFormData = async () => {
    await draftService.restoreCheckpoint(DRAFT_BLANK_KEY);
  };

  $scope.loadCurrentDraft = async () => {
    await draftService.restoreDraft($scope.draftKey);
  };

  $scope.deleteCurrentDraft = async () => {
    await draftService.deleteDraft($scope.draftKey);
  };
  
  $scope.resetComposeEmailForm = () => {
    // If has content ask before deleting
    if (hasContent()) {
      feedbackService.showPrompt(t('Discard your changes?', {ns: 'common'})).then(() => {
        $scope.restoreInitialFormData();
      });
    } else {
      $scope.restoreInitialFormData();
    }
  };

  const startDraftLifecycle = () => {
    $scope.autoSaveEnabled = true;
    draftService.startLifecycle();
  };

  // Triggered before navigating away from page
  // Save draft and set to draft to auto-load when comming back from chat
  const handleDraftOnLocationChange = async () => {    
    await $scope.triggerDraftAutoSave();

    // Disable auto saving past this point
    $scope.autoSaveEnabled = false;
    draftService.stopLifecycle();

    // You are being taken away from the page without pressing the cancel/home/<USER>
    // automatically resume draft when you return
    if ($scope.shouldAutoloadDraftOnReturn) {
      groupTaskService.storeGroupTaskData(groupTaskData);
      corporateTaskService.storeCorporateTaskData(corporateTaskData);

      // Set flag to autoload draft immediatly when re-entering from chat
      $scope.compose.autoload_draft = true;
      localStorageService.set('sfCompose', $scope.compose);
    }
  };

  const handleOnDataChange = () => {
    $scope.setHasContentFlagDebounced();
    if ($scope.isDraftSystemEnabled) {
      $scope.hasChangedSinceDraftSave = true;
      $scope.triggerDraftAutoSaveDebounced();
    }
  };

  // watch anything we would want to save
  // important for drafts and maintaining various flags
  const attachOnDataChangeWatchers = () => {
    let watchers = [
      'compose',
      'attachment',
      'assetProp',
      'selectedContacts',
      'contactData',
      'currentLangTemplate',
      'templateEmail',
      'isMultipleEmailTemplatesEnabled',
      'isGroupedProductsToggleEnabled',
    ];
    watchers.forEach((watcherKey) => {
      $scope.$watch(watcherKey, handleOnDataChange, true);
    });
  };

  const initDraftSystem = async (isInitialState) => {
    $scope.autoSaveEnabled = false;
    $scope.hasChangedSinceDraftSave = false;
    $scope.shouldAutoloadDraftOnReturn = true;
    $scope.draftKey = makeDraftKey();
    draftService.setDraftKey($scope.draftKey);
    draftService.setCreateFunc(createDraft);
    draftService.setRestoreFunc(restoreDataFromDraft);
    
    // handle navigating away like for chat or other requests
    $scope.$on('$locationChangeStart', handleDraftOnLocationChange);

    attachOnDataChangeWatchers();

    if ($scope.compose.autoload_draft) {
      await draftService.restoreDraft($scope.draftKey);
      startDraftLifecycle();
    } else {
      if (!isInitialState) {
        // info is being passed in from another UI through local/secure storage
        startDraftLifecycle();
      } else {
        // Save blank state as computed by the init method
        // This is so we can always reset to the initial state
        await saveInitialFormData();

        // Check if draft exists and ask to restore
        let draftExists = await draftService.existsDraft($scope.draftKey);
        if (!draftExists) {
          startDraftLifecycle();    
        } else {
          promptToRestoreDraft();
        }
      }
    }
  };

  const promptToRestoreDraft = () => {
    feedbackService.showFeedback({
      template: 'views/directives/feedback-content-button-modal-horizontal.html',
      message: t('You have a draft existing already.<br/>Restore the draft?'),
      type: 'context',
      hideFooter: true,
      allowDismissFeedback: false, // prevent accidental dismisses
      context: [
        {
          text: t('Discard'),
          type: 'cancel',
          display: true,
          action: async () => {
            startDraftLifecycle();
            await feedbackService.closeFeedback();
            // will get overwritten if not restored
            // we dont delete here to keep the blank in memeory
          }
        },
        {
          text: t('Restore'),
          type: 'prompt',
          display: true,
          action: async () => {
            startDraftLifecycle();
            await feedbackService.closeFeedback();
            await draftService.restoreDraft($scope.draftKey);
          }
        },
      ]
    });
  };
  
  const makeDraftKey = () => {
    let mode = 'compose';
    let id = 'new';

    const corporateTaskId = getCorporateTaskId();
    if ($scope.isGroupTaskType()) {
      mode = 'grouptask';
      id = getGroupTaskId();
    } else if (corporateTaskId) {
      mode = 'corptask';
      id = corporateTaskId;
    } else if ($scope.mode === 'customer_request') {
      mode = 'request';
      id = $scope.id;
    } else if ($scope.mode === 'message' &&
      $location.search().thread &&
      $location.search().thread.length > 0) {
      mode = $scope.isForwardedMessage ? 'thread_fwd' : 'thread';
      id = $location.search().thread;
    } else if ($location.search().who) {
      mode = 'contact';
      id = $location.search().who;
    }

    return `email_${mode}_${id}`;
  };

  const getThreadId = () => {
    let threadId = '';
    if ($scope.mode === 'customer_request') {
      threadId = $scope.id;
    } else if ($scope.mode === 'message'
      && $location.search().thread
      && $location.search().thread.length > 0) {
      threadId = $location.search().thread;
    }
    return threadId;
  };

  const createDraft = () => ({
    draftKey: $scope.draftKey,
    draftType: 'compose_email',
    timestamp: moment().utc(),
    url: $location.url(), // include for navigating from draft folder when it's implemented
    data: {
      templateLocale: $scope.currentLangTemplate,
      selectedContacts: $scope.selectedContacts || [],
      title: $scope.compose.title,
      message: $scope.compose.message,
      isHtml: $scope.isWysiwygEnabled(),
      threadId: getThreadId(),
      isMultipleEmailTemplatesEnabled: $scope.isMultipleEmailTemplatesEnabled,
      isGroupedProductsToggleEnabled: $scope.isGroupedProductsToggleEnabled,
      templateEmail: $scope.templateEmail,
      attachment: $scope.attachment,
      assetProp: $scope.assetProp,
      products: $scope.compose.products,
      parseUrlResponse: $scope.parseUrlResponse,
      assets: $scope.compose.assets,
      localStorage: {
        groupTaskData,
        corporateTaskData,
      }
    },
  });

  // This method will still require the proper data to be populated in local storage
  // for group tasks for example
  const restoreDataFromDraft = async (draft) => {
    const data = draft.data;
    if (!data) {
      return;
    }   
    
    // unpack any attr referenced more than once
    const { localStorage, isHtml, products, message, attachment } = data;

    // reload context varaibles from local storage
    if (localStorage) {
      groupTaskData = groupTaskData || localStorage.groupTaskData;
      corporateTaskData = corporateTaskData || localStorage.corporateTaskData;
    }

    $scope.selectedContacts = data.selectedContacts;

    $scope.compose.title = data.title;

    // Adjust message to editer
    if ($scope.isWysiwygEnabled()) {
      if (isHtml) {
        $scope.compose.message = message;
      } else {
        $scope.compose.message = $rootScope.utils.plainTextToWysiwyg(message);
      }
    } else {
      if (isHtml) {
        $scope.compose.message = $rootScope.utils.htmlToPlainText(message);
      } else {
        $scope.compose.message = message;
      }
    }

    $scope.isMultipleEmailTemplatesEnabled = data.isMultipleEmailTemplatesEnabled;
    $scope.currentLangTemplate = data.templateLocale;
    $scope.templateEmail = data.templateEmail;

    // restore photo
    $scope.attachment = attachment;
    if (attachment) {
      savePhotos($scope.attachment.content);
    }
    // restore asset
    $scope.compose.assets = data.assets;
    $scope.assetProp = data.assetProp;

    // restore products
    $scope.compose.products = []; 
    if (products && products.length > 0) {
      products.forEach((product) => {
        if (!productsService.findInProducts($scope.compose.products, product)) {
          $scope.compose.products.push(product);
        }
      });
    }
    setGroupedProductsToggleStatus(data.isGroupedProductsToggleEnabled);

    if ($scope.isGroupedProductsToggleEnabled && $scope.compose.products?.length < groupedProductsMinProducts) {
      setGroupedProductsToggleStatus(false);
    }

    $scope.parseUrlResponse = data.parseUrlResponse;

    refreshButtonsStates();
    $scope.debouncedApply();
  };

  const refreshButtonsStates = () => {
    $scope.display = {
      hasAttachedPhoto: $scope.hasAttached('photo'),
      hasAttachedAsset: $scope.hasAttached('asset'),
      hasAttachedProduct: $scope.hasAttached('product')
    };
  };

  $scope.insertLink = (link) => {
    if ($scope.isWysiwygEnabled()) {
      // add space if not already one
      let spacer = '&nbsp;';
      const lastChildText = $scope.getEditorLastChildText();
      const isLineEmpty = lastChildText.length === 0;
      const isLastCharWhiteSpace = /\s$/.test(lastChildText);
      if (isLineEmpty || isLastCharWhiteSpace) {
        spacer = '';
      }
      $scope.insertHtml(spacer + $rootScope.utils.plainTextToWysiwyg(link) + '&nbsp;');
    } else {
      $scope.compose.message = $rootScope.utils.formatMessageWithLink($scope.compose.message, link);
    }
  };

  $scope.onClickAiMessaging = () => {
    $rootScope.handleAppSwitch('/assistant', {
      message: $rootScope.utils.htmlToPlainText($scope.compose.message),
    });
  };

  const unregisterOnInsertAiMessage = $rootScope.$on('insertAiMessage', (_event, data) => {
    if ($scope.isWysiwygEnabled()) {
      $scope.compose.message = data.message;
    } else {
      $scope.compose.message = $rootScope.utils.htmlToPlainText(data.message);
    }
    $scope.$apply();
  });

  $scope.$on('$destroy', () => {
    unregisterOnInsertAiMessage();
  });

  $scope.focusTextarea = (bool) => {
    $timeout(() => {
      $scope.isFocused = bool;
    });
  };

  $scope.setContactsSearchController = controller =>
    $scope.contactsSearchController = controller;

  $scope.$watchCollection('selectedContacts', (selectedContacts) => {
    if (selectedContacts[0]?.locale && !localStorageService.get('sfAttachmentLang')) {
      $scope.currentLangTemplate = selectedContacts[0].locale;
    }
  });

  const setGroupedProductsToggleStatus = (statusValue) => {
    $scope.isGroupedProductsToggleEnabled = statusValue;
    localStorageService.set('sfEmailGroupedProductsToggleStatus', statusValue);
  };

  const validateGroupedProductsAttachments = (showFeedback) => {
    const { assets, products } = $scope.compose;
    const photos = $scope.attachment.content;

    return groupedProductsValidatorService.validateAttachments({ products, assets, photos }, showFeedback);
  };

  $scope.onToggleGroupedProducts = (statusValue) => {
    if ($scope.isGroupedProductsToggleEnabled) {
      setGroupedProductsToggleStatus(statusValue);
      return;
    }

    // validate with feedback
    if (!validateGroupedProductsAttachments(true)) {
      return;
    }

    setGroupedProductsToggleStatus(statusValue);
  };
  
  $scope.onScrollContactsList = () => {
    if (isLoadingContactsCustomers) {
      return;
    }

    $scope.searchQuery.page = $scope.pagination.current_page + 1;
    $scope.loadContacts($scope.searchQuery);
  };

  $scope.init();
});
