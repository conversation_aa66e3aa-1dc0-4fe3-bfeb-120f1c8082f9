'use strict';
// # `App.js`
// Main Javascript file.

// TODO: refactor and get rid of login promise
let loginPromise = $.Deferred();

window.deferredUpdatePathPromise = window.deferredUpdatePathPromise || $.Deferred();
window.deferredUpdatePath = function (path) {
  window.deferredUpdatePathPromise.then(function (updatePath) {
    updatePath(path);
  });
};

// Deeplink global handler
// Called when initializing the application
// Extract the link url and resolve the deeplink handler
window.handleOpenURL = function (url) {
  setTimeout(function () {
    const $body = angular.element(document.body);
    const $deeplinkService = $body.injector().get('deeplinkService');
    $deeplinkService.deeplink.handleOpenURL(url);
  });
};

window.setup = {};

// Module definition and injecting dependencies.
angular.module('sfmobileApp', [
  'algoliasearch',
  'ngCookies',
  'ngResource',
  'ngSanitize',
  'ngRoute',
  'LocalStorageModule',
  'configService',
  'miscService',
  'lookbookService',
  'ngTagsInput',
  'ngAnimate',
  'ngOnload',
  'firebase',
  'infinite-scroll',
  'luegg.directives',
  'jm.i18next',
  'ngIntlTelInput',
  'hmTouchEvents',
  'ngCroppie',
])

  .config(['tagsInputConfigProvider', (tagsInputConfigProvider) => {
    tagsInputConfigProvider.setActiveInterpolation('tagsInput', { placeholder: true }).setDefaults('tagsInput', { placeholder: ' ' });
  }])

  .config(['configServiceProvider', function (configServiceProvider) {
    configServiceProvider.setDefaultValues({
      // Camera
      // 'CameraMaxHeight'              : 1000, // we dont use this anymore
      'CameraMaxWidth'               : 1500,
      'CameraQuality'                : 100,

      // Features
      'Clienteling'                       : false,
      'ContactsBarcodeScanner'            : false,
      'ProductBarcodeScanner'             : false,
      'RetailerHasChat'                   : true,
      'RetailerHasLookbooks'              : true,
      'RetailerHasPersonalShopper'        : true,
      'RetailerHasSpecialties'            : false,
      'RetailerHasChatAppointment'        : true,
      'RetailerCanEditEvents'             : false,
      'RetailerCanBrowseLibrary'          : false,
      'RetailerCanShareFromBrowseLibrary' : false,
      'RetailerHasTasks'                  : false,
      'RetailerChatMode'                  : 'all',
      'RetailerHasPhoneCallEnabled'       : true,
      'RetailerHasProductsFeed'           : true,
      'RetailerHasStorefront'             : true,
      'RetailerCanAddContacts'            : true,
      'RetailerHasAppointmentRequests'    : true,
      'AppointmentRequestsAutoAcceptEnabled'    : false,
      'RetailerCanSendEmailToMultipleRecipients': true,
      'RetailerHasCorporateTasks'         : true,
      'RetailerHasAutomatedTasks'         : true,
      'RetailerCanChangeCommunicationConsent' : true,
      'RetailerHasExtendedAttributes'      : true,
      'ProductsExpandedVariantsEnabled'    : false,
      'PiiObfuscationIsEnabled'            : false, //for contact+customer
      'ModerationTextIsEnabled'            : false,
      'RetailerShowProductColorAsUpc'      : false,
      'BackofficePrimaryLoginThroughAppEnabled' : false,

      // Retailer Mode
      'RetailerStorepageMode'         : 'rep',

      // Share
      'ShareConnectedServicesEnabled' : true,
      'ShareEmailEnabled'             : true,
      'ShareInstagramEnabled'         : false,
      'ShareFacebookEnabled'          : false,
      'SharePinterestEnabled'         : false,
      'RetailerCanShareAnUpdate'      : true,
      'ShareWysiwygEnabled'           : false,

      // Compose Email
      'ComposeEmailWysiwygEnabled'    : false,

      // Environment
      'Stack'                        : 'prd',

      // SSO Authentication
      'Oauth2ProviderType'           : 'Azure',

      // Services
      'ServiceChatLabel'                    : 'Live Chat',
      'ServiceAppointmentLabel'             : 'Appointment Request',
      'ServicePersonalShopperLabel'         : 'Personal Shopper',
      'ServiceEmailMeLabel'                 : 'Ask & Answer',
      'ServiceEmailMeReportsLabel'          : 'Ask & Answer',
      'ServiceChatRequestLabel'             : 'Live Chat Request',
      'ServiceAppointmentRequestLabel'      : 'Appointment Request',
      'ServicePersonalShopperRequestLabel'  : 'Personal Shopper Request',
      'ServiceEmailMeRequestLabel'          : 'Ask & Answer Request',
      'ServiceEmailMeReportsRequestLabel'   : 'Ask & Answer Request',

      // Storefront
      'StorefrontTrendingRecommendationsMode'     : false,
      'StorefrontMaxProductCounts.topPicks'       : 16,
      'StorefrontMaxProductCounts.newArrivals'    : 4,
      'StorefrontMaxProductCounts.deals'          : 4,
      'StorefrontMaxProductCounts.recommendations': 8,
      'StorefrontMinProductCounts.recommendations': 2,

      'AppointmentLeadTime': { chat: 0, phone: 0, store: 0, virtual: 0 },

      'StoreTextHours': [{ open: 9, close: 22 }],

      //Group product retailer landing page
      'GroupedProductsIsEnabled'            : false,
      'GroupedProductsMinProducts'          : 2,
      'GroupedProductsMaxProducts'          : 10,
      'GroupedProductsSendStyledLinkLabel'  : 'Send Styled Link',

      //Tasks
      'TaskQueryCutOffDaysBack': 30,
      'AutoResolveTasksIsEnabled': false,

      //Group tasks
      'GroupTasksIsEnabled'   : false,
      'GroupTasksLabel'       : 'Group Tasks',

      // Advanced Customer Search
      'AdvancedSearchTransactionMaxAmount'  : 20000,

      // Draft System
      'DraftForShareEnabled'    : false,
      'DraftForComposeEnabled'  : false,
      'DraftAutoSaveInterval'   : 5 * 1000,

      // Misc
      'LoaderDelay'           : 1500,
      'LoaderMessageInterval' : 4000,
      'ApiCacheExpiration'    : 3 * 60 * 1000,
      'CanForwardToCS'        : false,
      'ModerationTextTimeout' : 2000,

      'AiOutreachIsEnabled' : false
    });

    configServiceProvider.setConfigGetFilter(function (key, value) {

      function addTrailingSlashToUri(uri) {
        if (typeof uri !== 'string') {
          return uri;
        }
        return uri?.endsWith('/') ? uri : uri + '/';
      }

      function getBooleanFromString(sourceString) {
        return sourceString === 'false' ? false : true;
      }

      if (key === 'SfApiUrl') {
        value = addTrailingSlashToUri(value);
      } else if (value === 'true' || value === 'false') {
        value = getBooleanFromString(value);
      }
      // what other value could this be?
      return value;
    });
  }])

  .config(['ngIntlTelInputProvider', (ngIntlTelInputProvider) => {
    ngIntlTelInputProvider.set({
      autoPlaceholder: 'aggressive',
      initialCountry: 'us',
      preferredCountries: ['ca', 'us'],
      onlyCountries: ['au', 'at', 'be', 'br', 'ca', 'cl', 'hr', 'cz', 'dk', 'ee', 'fi', 'fr', 'gt', 'de', 'hu', 'hk', 'ie', 'il', 'it', 'jp', 'lv', 'lt', 'my', 'mx', 'nl', 'no', 'ph', 'pl', 'pt', 'pr', 'sg', 'za', 'kr', 'es', 'se', 'ch', 'tw', 'gb', 'us']
    });
  }])

  .config(function ($httpProvider) {
    $httpProvider.defaults.headers.common['SF-Origin'] = 'mobile';
    $httpProvider.interceptors.push(function (
      $q,
      $rootScope,
      $location,
      $injector,
      localStorageService,
      feedbackService,
      loaderService,
      loggerService,
      CONST_HTTP,
      CONST_STORAGE
    ) {
      const isRetailerApiUrl = url =>
        url.indexOf($rootScope.conf('SfApiUrl')) === 0 ||
        /https:\/\/configs\.salesfloor\.net\/configs\/mobile/.test(url);
      const isWpApiUrl = url => url.indexOf($rootScope.conf('SfWpApiUrl')) === 0 || url.includes('/sfadmin/');
      let refreshTokenInProgress = null;

      return {
        'request' : function (request) {
          request.activeLoaderRequest = loaderService.startLoadingOnRequest(request);
          loggerService.logNetworkInfo(request.url);

          return new Promise((resolve) => {
            if (angular.isUndefined(request.url)) {
              //return request;
              resolve(request);
              return;
            }

            // NOTE: we can't inject i18nService here because of circular dependency
            const i18nService = $rootScope.i18nService;

            /*
          * Internationalization (i18n)
          *
          * Add locale to all requests if:
          *  1. It is a REST API or WORDRESS API request
          *  2. The i18n feature is enabled for the current retailer
          *  3. The current language has been set by i18next
          *  4. The current request has no `filter[locale]` set
          */

            if ((isRetailerApiUrl(request.url) || isWpApiUrl(request.url))
              && $rootScope.conf('i18nIsEnabled')
              && (i18nService.currentLocale(true) || localStorageService.get('currentLocale', CONST_STORAGE.TYPE.LOCAL))
              && !$rootScope.getQueryParam('filter[locale]', request.url)
            ) {
              var parser = new URL(request.url);
              var reserved = parser.search ? '&' : '?';

              var locale = i18nService.currentLocale(true) || localStorageService.get('currentLocale', CONST_STORAGE.TYPE.LOCAL);

              // If the api is calling for a specific locale despite the locale of the app then it should be used
              if (request.params && request.params.sf_locale) {
                locale = request.params.sf_locale;
                delete request.params.sf_locale;
              }

              if (parser.searchParams.get('sf_locale')) {
                locale = parser.searchParams.get('sf_locale');
                parser.searchParams.delete('sf_locale');
              }

              parser.search += reserved + 'sf_locale=' + locale;
              request.url = parser.href;
            }

            if (isWpApiUrl(request.url)) {
              request.withCredentials = $rootScope.isBackofficeApp;
            }

            /*
            * REST API accessToken process
            * Setup the header if it calls the rest api
            * Acces Secure Storage only if it's a retailer API
            * to avoid unnecessary reading of the token form securestorage
            */
            // TODO: check why after install it goes into loop
            // if (isRetailerApiUrl(request.url)) {
            $rootScope.getAccessTokenFromStorage().then((token) => {
              if (token && isRetailerApiUrl(request.url)) {
                request.headers[CONST_HTTP.HEADER_AUTHORIZATION] = token;
                request.headers[CONST_HTTP.HEADER_CONTENT_TYPE]  = 'application/json';
              }
              resolve(request);
            });
          });
        },
        'response': function (response) {
          loaderService.stopLoadingOnResponse(response.config.activeLoaderRequest);
          localStorageService.set('connectionLost', false);
          //Will only be called for HTTP up to 300
          return response;
        },
        'responseError': function (rejection) {
          loaderService.stopLoadingOnResponse(rejection.config.activeLoaderRequest);
          const { status, config: { url } } = rejection;

          if (status === CONST_HTTP.CODE_FORBIDDEN) {
            loggerService.logError('[app-configs] Permission denied error:', rejection.config);
            return $q.reject(rejection);
          }

          /*
           * REST API accessToken process
           *
           * It'll try to refresh the accessToken if getting 401 - UNAUTHORIZED on all api calls to
           * REST API. Except `login` or `refresh` endpoints.
           */
          if (status === CONST_HTTP.CODE_UNAUTHORIZED && (isRetailerApiUrl(url) || isWpApiUrl(url))) {
            const sfApiUrl = $rootScope.conf('SfApiUrl');

            // unauthorized while trying to login
            // just returns the rejection
            // we need to check if it's SSO login as well
            if (url.includes(`${sfApiUrl}login`) || url.includes(`${sfApiUrl}oauth/login`)) {
              return $q.reject(rejection);
            }

            // unauthorized while trying to refresh
            // redirect to login
            if (url.includes(`${sfApiUrl}refresh`)) {
              $location.path('/logout/no-refresh');
              return $q.reject(rejection);
            }

            // unauthorized while trying to verify mfa token
            if (url.includes(`${sfApiUrl}auth/mfa`)) {
              return $q.reject(rejection);
            }

            delete rejection.config.headers[CONST_HTTP.HEADER_AUTHORIZATION];

            if (refreshTokenInProgress === null) {
              refreshTokenInProgress = $rootScope
                .getAccessTokenFromStorage()
                .then(token => token !== null ?
                  $rootScope.refreshAccessToken() :
                  $location.path('/logout/no-refresh')
                );
            }

            return refreshTokenInProgress.then(function () {
              refreshTokenInProgress = null;
              return $injector.get('$http')(rejection.config);
            });
          }

          if (status === CONST_HTTP.CODE_INTERNAL_SERVER_ERROR ||
            status === CONST_HTTP.CODE_NOT_IMPLEMENTED ||
            status === CONST_HTTP.CODE_BAD_GATEWAY ||
            status === CONST_HTTP.CODE_SERVICE_UNAVAILABLE ||
            status === CONST_HTTP.CODE_GATEWAY_TIMEOUT ||
            status === CONST_HTTP.CODE_HTTP_VERSION_NOT_SUPPORTED
          ) {
            if (rejection.data !== null) { //prevents message from being displayed when app restarts/reloads
              loggerService.logError(`[app-configs] code ${status} error. Server Error`, rejection);
              feedbackService.showError('There was a problem contacting the server. Please try again.');
            }
          }
          // TODO: Investigate cause we have random error popup display which is confusing
          if (status === CONST_HTTP.CODE_REQUEST_INCOMPLETE) {
            if (rejection.data !== null) { //prevents message from being displayed when app restarts/reloads
              loggerService.logError('[app-configs] Incomplete request error.', rejection.config);
            }
          }

          return $q.reject(rejection);
        }
      };
    });
  })
  .config(($compileProvider) => {
    $compileProvider.imgSrcSanitizationWhitelist(/^\s*(https?|ftp|file|blob|ionic):|data:image\//);
  })
  .config(function (localStorageServiceProvider, CONST_STORAGE, CONST_SF_APP) {
    if (window.sfApp === CONST_SF_APP.DESKTOP) {
      localStorageServiceProvider.setStorageType(CONST_STORAGE.TYPE.SESSION);
    } else {
      localStorageServiceProvider.setStorageType(CONST_STORAGE.TYPE.LOCAL);
    }
  })
  .constant('CONST_HTTP', {
    HEADER_SF_APP_ID        : 'SF-App-Id',
    HEADER_SF_APP_VERSION   : 'SF-App-Version',
    HEADER_SF_ORIGIN        : 'SF-Origin',
    HEADER_SF_APIKEY        : 'SF-APIKEY',
    HEADER_AUTHORIZATION    : 'Authorization',
    HEADER_CONTENT_TYPE     : 'Content-Type',

    METHOD_GET              : 'GET',
    METHOD_POST             : 'POST',
    METHOD_PUT              : 'PUT',

    CODE_REQUEST_INCOMPLETE : 0,
    CODE_BAD_REQUEST        : 400,
    CODE_UNAUTHORIZED       : 401,
    CODE_FORBIDDEN          : 403,
    CODE_NOT_FOUND          : 404,
    CODE_METHOD_NOT_ALLOWED : 405,
    CODE_LOCKED             : 423,
    CODE_UNPROCCESSABLE_CONTENT : 422,
    CODE_INTERNAL_SERVER_ERROR : 500,
    CODE_NOT_IMPLEMENTED    : 501,
    CODE_BAD_GATEWAY        : 502,
    CODE_SERVICE_UNAVAILABLE: 503,
    CODE_GATEWAY_TIMEOUT    : 504,
    CODE_HTTP_VERSION_NOT_SUPPORTED: 505,
  })
  .constant('CONST_SERVICE', {
    REP_PHOTOS_TYPE_CAMERA: 'CAMERA',
    REP_PHOTOS_TYPE_CAMERA_ROLL: 'SAVEDPHOTOALBUM',
    REP_PHOTOS_TYPE_CAMERA_ROLL_MULTI: 'SAVEDPHOTOALBUM_MULTI',
    REP_PHOTOS_TYPE_INPUT_FILES_DESKTOP: 'INPUT_FILES_DESKTOP',
    REP_PHOTOS_BUTTON_TAKE: 'Take Photo',
    REP_PHOTOS_BUTTON_CAMERA: 'Camera',
    REP_PHOTOS_BUTTON_SELECT: 'Select Photo(s)',
    REP_PHOTOS_BUTTON_GALLERY: 'Gallery',
    REP_PHOTOS_BUTTON_CAMERA_CLASS_MODIFIER: 'camera',
    REP_PHOTOS_BUTTON_GALLERY_CLASS_MODIFIER: 'image'
  })
  .constant('CONST_FEEDBACK', {
    // authentication
    WRONG_CREDENTIALS  : 'Login failed. Incorrect username or password.',
    CHECK_CONNECTIVITY : 'Login failed. Please check your network connectivity.',
    DISABLE_SILENT_MODE: 'To enable chat please disable Silent Mode on your device.',
    LOCKED_OUT         : 'We\'ve temporarily locked your account after too many failed attempts to sign in. <br /><br/>Please talk to your Administrator to have your account unlocked.',
    PERMISSION_DENIED  : 'Permission denied. Please make sure you are allowed to access this resource.',

    // attachments
    BLOCK_PHOTO_DUE_ASSET       : 'Sorry, you can\'t attach a photo if you\'ve already included an asset in your message. Please remove the asset to continue.',
    BLOCK_PHOTO_DUE_PRODUCT     : 'Sorry, you can\'t attach a photo if you\'ve already included a product in your message. Please remove the product to continue.',
    BLOCK_PRODUCT_DUE_ASSET     : 'Sorry, you can\'t attach a product if you\'ve already included an asset in your message. Please remove the asset to continue.',
    BLOCK_PRODUCT_DUE_PHOTO     : 'Sorry, you can\'t attach a product if you\'ve already included a photo in your message. Please remove the photo to continue.',
    BLOCK_PRODUCT_DUE_MAX_LIMIT : 'Sorry, you can\'t attach more than 10 products at a time.',
    BLOCK_ASSET_DUE_MIXED       : 'Sorry, you can\'t attach an asset if you\'ve already included a photo in your message. Please remove the photo to continue.',

    // share
    BLOCK_SHARE_DUE_MIXED_RESOURCES : 'Sorry! You can’t attach photos and products when sharing across your social networks.',

    // generic
    SOMETHING_WRONG : 'There was an error, please try again'
  })
  .constant('CONST_ACTIVITY_FEED_FILTER_TYPES', {
    EMAIL             : '1',
    PERSONAL_SHOPPER  : '2',
    APPOINTMENT       : '3',
    TEXT              : '4',
    DIRECT_EMAIL      : '5',
    TRANSACTION       : '6',
    CHAT              : '7',
    LOOKBOOK          : '17',
    RETURN            : '8',
  })
  .constant('CONST_I18N', {
    SUPPORTED_LOCALES: [
      'en_US',
      'fr_CA',
      'nl_NL',
      'ja_JP'
    ],
    DEFAULT_LOCALES: {
      en: 'en_US',
      fr: 'fr_CA',
      nl: 'nl_NL',
      ja: 'ja_JP'
    },
    DEFAULT_LOCALE: 'en_US',
  })
  .constant('CONST_PAGINATION', {
    PAGE_SIZE: 20,
    CONTACTS_PAGE_SIZE: 25,
    CONTACTS_SUGGESTION_PAGE_SIZE: 5,
  })
  .constant('CONST_GA', {
    DEFAULT_UA: 'UA-115260684-1'
  })
  .constant('CONST_SHARE', {
    MAX_PRODUCTS: 10,
    MAX_PHOTOS: 5,
    MAX_ASSETS: 1,
    MAX_QUICK_ACCESS_PRODUCTS: 10,
    DEFAULT_TWITTER_COUNT: 250,
    SOURCE_CORPORATE_TASK: 'corporate_task'
  })
  .constant('CONST_TASKS', {
    TASK_TYPE: {
      GROUP_TASK: 'group-task',
      AUTOMATED: 'automated'
    },
    LIST_SIZE: 200,
  })
  .constant('CONST_SEARCH', {
    DEBOUNCE_DELAY: 500,
    SEARCHABLE_FILTERS: {
      PER_PAGE_MAX: 200,
      PER_PAGE_DEFAULT: 60
    }
  })
  .constant('CONST_ONBOARDING', {
    INVITE: 'invite',
    USER_FEED: 'feed'
  })
  .constant('CONST_TEXTAREA_LIMIT', {
    STOREFRONT: 90
  })
  .constant('CONST_STOREFRONT', {
    LS_EDIT_MODE_KEY: 'sfStorefrontCurrentMode',
    LS_CURRENT_TYPE_KEY: 'sfStorefrontCurrentType',
    MODE_EDIT: 'edit',
    MODE_CHANGE: 'change'
  })
  .constant('CONST_CHAT_INPUT', {
    LEVEL_0: 42,
    LEVEL_1: 56,
    LEVEL_2: 74
  })
  .constant('CONST_ADDRESS_BOOK', {
    PICK_EMAIL_ADDRESS: 'EMAIL_ADDRESS',
    PICK_PHONE_NUMBER: 'PHONE_NUMBER',
    SMS_UNSUBSCRIBED_TYPE: 'SMS_UNSUBSCRIBED',
    SMS_SUBSCRIBED_TYPE: 'SMS_SUBSCRIBED',
    modes: {
      CONTACTS: 'contacts',
      ASSOCIATES: 'associates',
    },
    LS_KEY_CONTACTS_FILTERS: 'sfContactsFacets',
    LS_KEY_CUSTOMERS_FILTERS: 'sfCustomersFacets'
  })
  .constant('CONST_TEXT_MESSAGE', {
    REFRESH_RATE: 50000,
    TRACKING: 'SERVER_CLICK_TEXT_STOREFRONT',
    PER_PAGE: 200
  })
  .constant('CONST_SOCIAL_SHOP', {
    BIO_MIN: 50,
    BIO_MAX: 2200
  })
  .constant('CONST_ELEMENT_HEIGHT', {
    MAIN_HEADER: 45,
    SEARCH_FILTER: 50,
    ADVANCED_SEARCH: 35,
    CONTACT_PAGINATION: 64,
    CI_TOGGLE: 44,
    FOOTER: 44,
    CONTACT_ITEM: 56
  })
  .constant('CONST_PERMISSIONS', {
    /**
     *  The permissions are the checkers defined in permissionsService.
     */
    canManageStore: 'canManageStore',
    canManageUser: 'canManageUser',
    canSeeAdminCols: 'canSeeAdminCols',
    isUser: 'isUser',
    isStoreManager: 'isStoreManager',
    isManager: 'isManager',
    isCorpAdmin: 'isCorpAdmin',
    isSFAdmin: 'isSFAdmin',
  })
  .constant('CONST_DATE_FORMAT', {
    NUMERAL: 'YYYYMMDD',
    REPORTING: 'ddd MMM D, YYYY',
    APPOINTMENT: 'dddd MMM D, YYYY',
    EVENT: 'MMM D, YYYY'
  })
  .constant('CONST_REQUEST', {
    LS_KEY_APPOINTMENTS_FILTERS: 'sfAppointmentsFilters',
    LS_KEY_STORE_APPOINTMENTS_FILTERS: 'sfStoreAppointmentsFilters',
    COMMENT_MAX: 160,
    APPOINTMENT_START_TIME: 540, // 9 am in minutes
    APPOINTMENT_END_TIME: 1320, // 10:00 pm in minutes
    APPOINTMENT_INTERVAL: 30
  })
  .constant('CONST_EVENTS', {
    LS_KEY_EVENTS_FILTERS: 'sfEventsFilters',
    LS_KEY_STORE_EVENTS_FILTERS: 'sfStoreEventsFilters',
    DATE_FORMAT: 'ddd, MMMM D',
    DATE_FORMAT_FR: 'ddd, D MMMM',
    EVENT_START_TIME: 0,
    EVENT_END_TIME: 1410,
    EVENT_INTERVAL: 30,
    EVENT_MIN_DURATION: 30,
    EVENT_MAX_DURATION: 360
  })
  .constant('CONST_CALENDAR', {
    LS_KEY_PERMISSION_REQUESTED: 'sfCalendarPermissionRequested',
  })
  .constant('CONST_CONTACTS', {
    LS_KEY_SEARCH_CONTACT_DATA: 'sfSearchContactsData',
    LS_KEY_SEARCH_CUSTOMER_DATA: 'sfSearchCustomersData',
    LS_KEY_CURRENT_CUSTOMER: 'sfCurrentCustomer',
    LS_KEY_CURRENT_CUSTOMER_V2: 'sfCurrentCustomerV2',
    LS_KEY_CONTACT_STATS_DATA: 'sfContactStatsData',
    LS_KEY_CONTACT_FROM_MATCH: 'sfContactFromMatch',
    LS_KEY_CURRENT_SEARCH_MODE: 'sfCurrentSearchMode',
    NOTE_LIMIT: 5000,
    PREFERENCE_TYPE: {
      EMAIL: { name: 'Email', value: 'email' },
      TEXT_MESSAGE: { name: 'Text Message', value: 'text' },
      NO_PREFERENCE: { name: 'No preference', value: null },
      PHONE: { name: 'Phone', value: 'phone' }
    },
    CONSENT_TYPE: {
      ONE_TO_ONE: {
        name: '1 to 1 only',
        value: '0',
        tagText: '1 to 1 only',
        classModifier: '1-to-1'
      },
      SUBSCRIBED: {
        name: 'Subscribed to Marketing',
        value: '1',
        tagText: 'Subscribed',
        classModifier: 'subscribed'
      },
      UNSUBSCRIBED: {
        name: 'Unsubscribed from All',
        value: '2',
        tagText: 'Unsubscribed',
        classModifier: 'unsubscribed'
      }
    },
    ACCEPTED_API_FILTERS: [
      'user_id',
      'search',
      'state',
      'city',
      'mandatory_fields',
      'subcribtion_flag',
      'sms_marketing_subscription_flag',
      'private',
      'corporate',
      'tags',
      'locale',
      'favorite_contacts',
      'transaction_date',
      'transaction_amount',
      'category_id',
      'brand',
      'sort'
    ],
    FILTERS: {
      SORT: {
        ID: 'sort'
      },
      SUBSCRIPTION: {
        ID: 'subscription'
      },
      FAVORITE: {
        ID: 'favorite'
      },
      REGION: {
        ID: 'region',
        ALIAS: 'state'
      },
      CITY: {
        ID: 'city'
      },
      TAG: {
        ID: 'tag'
      },
      TRANSACTION_DATE: {
        ID: 'transaction-date',
        ALIAS: 'transaction_date'
      },
      TRANSACTION_AMOUNT: {
        ID: 'transaction-amount',
        ALIAS: 'transaction_amount'
      },
      CATEGORY: {
        ID: 'category',
        ALIAS: 'category_id'
      },
      BRAND: {
        ID: 'brand'
      },
    },
    TYPE: {
      SUBSCRIBED_CONTACT: 'subscribed-contact',
      CONTACT: 'contact',
      CUSTOMER: 'customer'
    },
    DEBOUNCE_DELAY: 500,
  })
  .constant('CONST_PRODUCT_LOOKS', {
    PAGE_SIZE: 10
  })
  .constant('CONST_OAUTH', {
    LS_KEY_ONBOARDING_OAUTH_DATA: 'sfOnboardingOAuthData',
    LS_KEY_OAUTH_DATA: 'sfOAuthData',
    SSO_TYPE: {
      AZURE: 'Azure',
      OKTA: 'Okta',
    }
  })
  .constant('CONST_VIRTUAL_APPOINTMENT', {
    LS_KEY_CUSTOMER_DATA: 'sfCustomerDataForVirtualAppointment'
  })
  .constant('CONST_STORAGE', {
    ACCESS_TOKEN: 'ls.sfAccessToken',
    DEVICE_TOKEN: 'ls.deviceToken',
    REMEMBER_ME: 'sfRememberMe',
    BO_REDIRECT_TO: 'sfBoRedirectTo',
    USER_NAME: 'sfUsername',
    TYPE: {
      LOCAL: 'localStorage',
      SESSION: 'sessionStorage'
    },
    CLEANUP_KEY: {
      DASHBOARD: 'dashboard'
    }
  })
  .constant('CONST_NAME_TEMPLATE', {
    REP: {
      DASHBOARD_REP_NAME: 'MobileDashboard',
      CHAT_REP_NAME: 'ChatRepName',
      CUSTOMER_ASSOCIATE_RELATIONSHIPS_REP_NAME: 'CustomerAssociateRelationshipsRepName',
      GROUP_TASK_REP_NAME: 'GroupTaskRepName'
    },
    CUSTOMER: {
      CONTACT_NAME: 'MobileContact',
      CHAT_CUSTOMER_NAME: 'ChatCustomerName'
    }
  })
  .constant('CONST_APPOINTMENTS', {
    OVERRIDE_MODE: 'sfOverrideMode',
    BOOKING_SLOTS_CHAR_LIMIT: 3,
  })
  .constant('CONST_MESSAGE_SENDER_RECEIVER_TYPES' , {
    CUSTOMER: 'Customer',
    WORDPRESS: 'WordPress'
  })
  .constant('CONST_SF_APP' , {
    MOBILE: 'mobile',
    DESKTOP: 'desktop',
    BACKOFFICE: 'backoffice'
  })
  .constant('CONST_MEDIA', {
    BREAKPOINTS: {
      XS: 320,
      SM: 767,
      MD: 1024,
      LG: 1280,
      XL: 1440,
      HEIGHT: 703
    },
    ORIENTATION: {
      LANDSCAPE: 'landscape',
      PORTRAIT: 'portrait'
    }
  })
  .constant('API_CACHE_KEYS' , {
    TASK_LIST_DELETE_KEY: 'task_list',
    TASK_ITEM_DELETE_KEY_PREFIX: 'task_item_',
    GROUP_TASK_LIST_DELETE_KEY: 'group_task_list',
    GROUP_TASK_ITEM_DELETE_KEY_PREFIX: 'group_task_item_',
    TASK_CATEGORY_LIST_DELETE_KEY: 'task_category_list',
  })
  .run(['deeplinkService', function (deeplinkService) {
    deeplinkService.deeplink.initPromises({
      login: loginPromise,
    });
  }]);

angular.module('infinite-scroll').value('THROTTLE_MILLISECONDS', 250);
