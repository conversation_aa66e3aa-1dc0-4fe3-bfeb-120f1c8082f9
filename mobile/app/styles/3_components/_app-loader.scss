@import '2_mixins/misc';
@import '2_mixins/media-queries';

$app-loader-text-is-processing-color: #2167a3 !default;
$app-loader-text-is-success-color: #30B787 !default;
$app-loader-text-is-error-color: #CA4C31 !default;

@-webkit-keyframes animation-rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@-moz-keyframes animation-rotate {
  100% {
    -moz-transform: rotate(360deg);
  }
}
@-o-keyframes animation-rotate {
  100% {
    -o-transform: rotate(360deg);
  }
}
@keyframes animation-rotate {
  100% {
    transform: rotate(360deg);
  }
}

.app-loader {
  //layout
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  float: none;
  width: 100%;
  height: auto;
  z-index: 1013;
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  //design
  background-image: none;
  background-color: rgba(0,0,0,0.5);

  p {
    margin-top: 20px;
    color: white;
    font-weight: bold;
    text-align: center;
  }

  &:before {
    @include animation(animation-rotate, 750ms, linear, infinite);

    //layout
    width: 50px;
    height: 50px;
    content: '';

    //design
    border: rgba(255, 255, 255, 0.75) 2px solid;
    border-radius: 50%;
    background-clip: padding-box;
    border-top-color: #008DEB;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
  }
}

.app-loader--is-hidden {
  //layout
  display: none;
  visibility: hidden;

  //desig
  opacity: 0;
}

.app-loader__button {
  //layout
  position: relative;

  //events
  cursor: wait;

  &:before {
    @include animation(animation-rotate, 750ms, linear, infinite);

    //layout
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -13px 0 0 -13px;
    content: '';
    position: absolute;

    //design
    border: rgba(255, 255, 255, 0.25) 2px solid;
    border-radius: 24px;
    background-clip: padding-box;
    border-top-color: #ffffff;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
  }


  &--is-secondary {
    &:before {
      border: rgba(33, 103, 163, .25) 2px solid;
      border-top-color: #2167A3;
    }
  }

  &--is-small {
    &:before {
      width: 18px;
      height: 18px;
      margin: -9px 0 0 -9px;
    }
  }

  &--is-right {
    &:before {
      left: auto;
      right: 10px;
    }
  }

  &--is-left {
    &:before {
      left: 15px;
      right: auto;
    }
  }
}

  .app-loader--is-section {
    @extend .app-loader__button;

    //layout
    height: 39px;

    &:before {
      //layout
      top: calc(50% - 5px);

      //design
      border-top-color: #008DEB;
    }
  }

  .app-loader--has-content {
    @extend .app-loader__button;

    //layout
    margin-top: 10%;

    &::before {
      //layout
      width: 50px;
      height: 50px;

      //design
      border: #e1e1e1 4px solid;
      border-top-color: #008DEB
    }
  }

.app-loader__text{
  //typo
  font-size: 12px;

  &--is-processing {
    //typo
    color: $app-loader-text-is-processing-color;
  }

  &--is-success {
    //typo
    color: $app-loader-text-is-success-color;
  }

  &--is-error {
    //typo
    color: $app-loader-text-is-error-color;
  }
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .app-loader {
      //layout
      @include set-margin-max-width(1440px, auto);
    }
  }
}
