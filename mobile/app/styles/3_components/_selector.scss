@import '2_mixins/retina';
@import '2_mixins/media-queries';

.selector__wrapper {
  display: flex;
  padding: 0 10px;

  selector {
    display: flex;
    flex-grow: 1;
  }

  .selector {
    display: flex;
    flex-grow: 1;
    margin: 0 0 10px;
    width: 0;
  }

  &--has-filter {
    selector {
      margin-right: 10px;
    }
  }
}

.selector {
  //layout
  width: calc(100% - 20px);
  height: 44px;
  margin: 10px 10px 0;
  z-index: 56;
  position: relative;

  //design
  border-radius: 2px;
  background-color: #ffffff;
}

  .selector--is-inside-header {
    //layout
    width: 100%;
    height: 30px;
    margin: 0 0 10px;

    .selector__list {
      //layout
      min-height: 28px;

      &:not(.selector__list--is-open) {
        .selector__element__text {
          //layout
          max-width: calc(100% - 20px);

          //typo
          line-height: 28px
        }
      }

      .selector__list__element {
        //layout
        padding: 0 20px;

        //typo
        font-size: 12px;
      }

      .selector__element__text {
        //layout
        max-width: 100%;

        //typo
        line-height: 44px;
      }
    }
  }

  .selector-preview {
    //layout
    width: 80px;
    display: inline-block;
    vertical-align: top;

    //typo
    color: #2167a3;
    font-size: 12px;
    text-align: center;
    font-weight: 600;
  }

    .selector-preview__icon {
      //layout
      top: -1px;
      position: relative;
      max-width: 16px;
      margin-right: 3px;
    }

  .selector-preview--is-socialshop {
    //layout
    width: 120px;
    float: right;
    margin-bottom: 5px;
  }

  .selector-preview--is-product-zoom-in {
    //layout
    right: 0;
    width: 50px;
    height: 50px;
    bottom: 0;
    position: absolute;

    .selector-preview__icon {
      //layout
      top: auto;
      right: 0;
      margin: 0;
      bottom: 0;
      max-width: 24px;
      position: absolute;
    }
  }

  .product-header--is-shrinked {
    .selector-preview--is-product-zoom-in {
      //layout
      right: 10px;
      width: 30px;
      height: 30px;
    }
  }

  .selector-preview--is-disabled {
    .selector-preview__icon,
    .selector-preview__text {
      //design
      opacity: 0.4;
      -webkit-opacity: 0.4;
    }
  }

  .selector--is-form {
    //layout
    width: 100%;
    margin: 5px 0 20px;
    z-index: 2;

    &:not(.selector--is-special) {
      .selector__list {
        //design
        border-color: #979797;
      }

      .selector__list__element--is-active {
        @include retina('forms/selector-form-arrow');

        //design
        background-position: center right 12px;
      }
    }

    .selector__list__element {
      //typo
      padding: 13px 10px;
      font-size: 12px;
    }

    .selector__list__element--is-multiline {
      //layout
      padding: 0 10px 0 10px;
    }
  }

  .selector--is-last {
    //layout
    margin-bottom: 10px;
  }

  .selector--is-small {
    //layout
    height: 30px;

    .selector__list {
      //layout
      min-height: 30px;

      &:not(.selector__list--is-open) {
        .selector__list__element {
          //layout
          padding: 4px 30px 4px 10px;
        }
      }
    }

    .selector__element__text {
      //layout
      max-width: calc(100% - 40px);
    }
  }

  .selector--has-button {
    //layout
    width: calc(100% - 105px);
    display: inline-block;
    margin-bottom: 5px;
    vertical-align: top;

    .selector__list:not(.selector__list--is-open) {
      //design
      border-color: #eaeaea;
    }
  }

  .selector--is-disabled {
    //typo
    -webkit-text-fill-color: #4a4a4a;

    //events
    pointer-events: none;

    //design
    opacity: 0.75;
    -webkit-opacity: 0.75;
    background-color: #ffffff;

    .selector__list__element--is-active {
      //design
      background-image: none !important;
    }
  }


  .selector--is-single {
    //layout
    padding: 10px !important;
  }

  .selector--is-reversed {
    .selector__list--is-open {
      //layout
      bottom: 0;

      //animation
      transform: rotate(180deg);

      .selector__list__element {
        //animation
        transform: rotate(-180deg);

        &,
        &:first-child {
          //design
          border-top: 1px solid #eeeeee;
        }

        &:last-child {
          //design
          border-top: none;
        }
      }
    }

    &.selector--is-form {
      .selector__list--is-open {
        //layout
        bottom: -1px;
      }
    }
  }

  .selector--is-shorten {
    //layout
    width: calc(100% - 85px);
  }

  .selector--is-aligned {
    //layout
    display: inline-block;
    vertical-align: top;
  }

.selector__list,
.selector__list__element {
  //layout
  width: 100%;
  height: 100%;
}

.selector__list {
  //layout
  height: auto;
  min-height: 44px;

  //design
  border: solid 1px #c7c7c7;
  border-radius: 2px;
}

.selector__list__element {
  //layout
  display: none;
  padding: 11px 11px 11px 14px;

  //typo
  color: #969696;
  font-size: 14px;

  //design
  background-color: #ffffff;

  span {
    //events
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  &:last-child {
    //design
    border-bottom: none;
  }
}

  .selector__list__element--is-active {
    //layout
    display: list-item;

    //typo
    color: #4a4a4a;

    //design
    @include retina('forms/selector-arrow');
    background-position: center right 12px;
  }

  .selector__list__element--is-disabled {
    //typo
    color: #969696;
  }

  .selector__list__element--is-multiline {
    //layout
    height: 44px;
    padding: 0 11px 0 20px;
    align-items: center;
    justify-content: left;
  }

  .selector__list__element--is-multiline-active {
    //inheritance
    @extend .selector__list__element--is-active;

    //layout
    display: flex;
  }

  .selector__element__text {
    //layout
    height: 100%;
    display: inline-block;
    overflow: hidden;
    max-width: calc(100% - 30px);
    vertical-align: bottom;

    //typo
    line-height: 19px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .selector__element__count {
    //layout
    display: inline-block;
    max-width: 35px;
    padding-left: 3px;
  }

.selector__list--is-open {
  //layout
  z-index: 57;
  position: absolute;

  //design
  box-shadow: inset 0 1px 2px 0 rgba(199, 199, 199, 0.5);
  border-color: #979797;

  //animation
  transition: border-color 100ms;
  -webkit-transition: border-color 100ms;

  + .selector__overlay {
    //layout
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 55;
    position: fixed;

    //design
    opacity: 0.8;
    background-color: #f8f8f8;
  }

  .selector__list__element {
    //layout
    display: list-item;

    //design
    border-top: 1px solid #eeeeee;
    box-shadow: none;

    &:first-child {
      //design
      border-top: none;
    }
  }

  .selector__list__element--is-multiline {
    //layout
    display: flex;
  }

  .selector__list__element--is-active {
    //design
    background-image: none !important;
  }

  .selector__list__element--is-new-type {
    //design
    border-color: #a0a0a0;
  }
}

  .selector__rules-list {
    //layout
    padding: 0;
  }

    .selector__rules-list__item {
      //typo
      color: #8c8c8c;
      font-size: 12px;
    }

.form__container--is-padded {
  .selector {
    //layout
    width: 100%;
    margin: 5px 0 15px;
    z-index: 1;
  }

  .selector__list__element {
    //layout
    padding: 13px 10px;

    //typo
    font-size: 12px;
  }
}

.device--is-desktop {
  .selector__list,
  .selector-preview {
    &:hover,
    &:active {
      //events
      cursor: pointer;
    }
  }
}


