@import '2_mixins/retina';
@import '2_mixins/media-queries';

$header-txt-color-black: #1E1E1E;

.main-header {
  //layout
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  display: flex;
  padding: 0;
  position: absolute;
  align-items: center;
  justify-content: space-between;

  //design
  box-shadow: 0px 1px 0px 0px #0062A3;
  background-image: linear-gradient(0deg, #0071BC 0%, #008DEB 100%);

  //typo
  color: #ffffff;
  text-align: center;
  text-shadow: 0px 1px 0px #0C619F;
}

  .main-header--is-flex {
    position: relative;
  }

  .main-header--is-under {
    //layout
    z-index: initial;
  }

  .main-header--is-over {
    //layout
    z-index: 1000;
  }

  .main-header--is-tall {
    //layout
    height: 130px;
    align-items: initial;

    .main-header__title,
    .main-header__link {
      //layout
      height: 44px;
    }
  }

  .main-header--in-preview-mode {
    justify-content: flex-end;
    background-image: none !important;
    box-shadow: none !important;

    .main-header__title {
      left: 10px;
      transform: translateX(0);
    }
  }

.main-header__title {
  //layout
  left: 50%;
  height: 100%;
  margin: 0;
  display: flex;
  position: absolute;
  overflow: hidden;
  max-width: calc(100% - 180px);
  align-items: center;

  //typo
  font-size: 16px;
  text-align: center;
  font-weight: 600;
  line-height: 14px;
  text-shadow: 0px 1px 0px #0C619F;
  text-overflow: ellipsis;
  text-transform: capitalize;

  //animation
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
}

.main-header__title--is-flex {
  left: 0;
  margin: 0 10px;
  position: relative;
  transform: none;
}

.main-header__title--is-plain {
  @extend .main-header__title;

  //typo
  text-transform: none;
}

  .main-header__title__wrapper {
    //layout
    display: inline-block;
    overflow: hidden;
    padding-right: 5px;
    vertical-align: top;

    //typo
    text-align: left;
    line-height: 44px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

@media (max-width: 320px) {
  .main-header__title {
    //typo
    font-size: 14px;
  }
}

  .main-header__title--no-capitalize,
  .main-header__title--is-email {
    //typo
    text-transform: none;
  }

  .main-header__title--is-long {
    // layout
    width: 100%;
    max-width: calc(100% - 130px);
    justify-content: center;
  }

.main-header__link {
  //layout
  padding: 0;
  position: relative;

  //typo
  font-size: 16px;
  line-height: 44px;
  font-weight: 600;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &,
  &:hover,
  &:visited,
  &:active {
    //typo
    color: #ffffff;
    text-decoration: none;
  }
}

.main-header__link--is-left {
  @extend .main-header__link;

  //layout
  padding-left: 15px;

  //design
  background-position: left 13px;
}

.main-header__link--is-left-arrow {
  @extend .main-header__link--is-left;

  //layout
  min-width: 30px;
  padding-left: 7px;

  &::before {
    //layout
    speak: none;
    display: inline-block;
    content: "\70";
    transform: rotate(180deg);
    line-height: 1;
    margin-right: -1px;
    padding-bottom: 3px;

    //typo
    font-size: 18px;
    font-style: normal !important;
    font-family: "mobile-font-icon" !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;

    //animation
    -webkit-transform: rotate(180deg);
  }
}

.main-header__link--is-right {
  @extend .main-header__link;

  //layout
  margin-right: 15px;
}

.main-header__link--is-right-position {
  //layout
  right: 0;
  position: absolute;
}

  .main-header__link--is-right-facets {
    @extend .main-header__link--is-right;

    //layout
    width: 55px;
    height: 100%;

    @include retina('reporting/facets-icon');
  }

.main-header__link__star--is-unselected {
  @include retina('contacts/star-unselected', $position: center right 15px);

  //layout
  padding-right: 40px;
}

.main-header__link__star--is-selected {
  @include retina('contacts/star-selected', $position: center right 15px);

  //layout
  padding-right: 40px;
}

.main-header__link__star--is-disabled {
  //design
  pointer-events: none;
}

.main-header__link--has-long-text {
  //layout
  height: 100%;
  display: flex;
  max-width: 24%;
  align-items: center;

  //typo
  font-size: 13px;
  line-height: 13px;
}

.main-header__link--is-icon {
  //layout
  width: 50px;
  height: 100%;
  padding: 0;
}

@media (max-width: 414px) {
  .main-header__link--is-right {
    &.main-header__link--has-long-text {
      //layout
      justify-content: flex-end;
    }
  }
}

.main-header__link--is-disabled,
.main-header__button--is-disabled {
  //design
  opacity: 0.5;

  //events
  pointer-events: none;
}

.main-header__button {
  //layout
  padding: none;
  border-radius: none;

  //design
  background: none;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.main-header-listing {
  display: flex;
  padding: 10px;
  align-items: center;
  justify-content: space-between;
}

.main-header-listing__title {
  margin: 0;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  line-height: 14px;
  text-overflow: ellipsis;
  text-transform: capitalize;
}

.main-header-listing__button {
  color: #ffffff;
  border: 1px solid #008DEB;
  padding: 5px 22px;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  border-radius: 4px;
  background-color: #008DEB;

  &:hover,
  &:active {
    color: #ffffff;
    cursor: pointer;
    border: 1px solid #0766A4;
    background: #0766A4;
    text-decoration: none;
  };   
};

.main-header-listing__button--is-outline {
  color: #0071bc;
  border: 1px solid #0071bc;
  background-color: transparent;

  &:hover,
  &:active {
    color: #008DEB;
    background: #EAF7FF;
  }
};

.main-header-listing__button--is-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .main-header {
      //layout
      height: 64px;

      //typo
      color: $header-txt-color-black;
      box-shadow: 0px 1px 0px 0px #DEDEDE;
      text-shadow: none;

      //design
      background-image: linear-gradient(0deg, #ffffff 0%, #ffffff 100%);

      .main-header__link {
        //typo
        color: $header-txt-color-black;
      }

      .main-header__title {
        //typo
        text-shadow: none;
      }
    }

    .main-header__title--is-left-aligned {
      //layout
      left: 10px;
      transform: translateX(0);
    }

    .main-header--is-tall {
      //layout
      height: 130px;
    }

    .main-header__button {
      //layout
      padding: 5px 22px;

      //design
      border-radius: 4px;
      background: #008DEB;

      //typo
      color: #ffffff;
      font-size: 14px;
      line-height: 16px;

      &:hover,
      &:active {
        //design
        background: #0766A4;
      }

      &--is-outline {
        color: #0071bc;
        border: 1px solid #0071bc;
        background-color: transparent;
      
        &:hover,
        &:active {
          color: #008DEB;
          background: #EAF7FF;
        }
      }
    }

    .main-header__link__star--is-unselected {
      @include retina('contacts/star-unselected-grey', $position: center right 15px);
    }

    .main-header__link--is-right-facets {
      @include retina('reporting/filter-icon');
    }

    .main-header__button--is-disabled {
      //design
      background: #DCE3E8;

      //typo
      color: #788893;
      pointer-events: none;
    }
  }

  .main-header__button,
  .main-header__link--is-left {
    &:hover {
      //events
      cursor: pointer;
    }
  }

  .main-header__link {
    .main-header__img {
      &:hover {
        //events
        cursor: pointer;
      }
    }
  }

  .main-header__link__star--is-selected,
  .main-header__link__star--is-unselected,
  .main-header__link--is-right-facets {
    &:hover {
      //events
      cursor: pointer;
    }
  }

  .main-header__link--is-disabled {
    &:hover {
      //events
      pointer-events: none;
      color: blue;
    }
  }
}
