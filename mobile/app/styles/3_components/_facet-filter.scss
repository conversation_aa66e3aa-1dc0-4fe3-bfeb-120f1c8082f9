@import '2_mixins/retina';
@import '2_mixins/misc';
@import '2_mixins/media-queries';

.facet-filter {
  //layout
  top: 0;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 0;
  z-index: 1020;
  display: flex;
  position: absolute;
  flex-direction: column;

  //design
  background-color: #f8f8f8;
}

  .facet-filter__header {
    //design
    background-color: #ffffff;
  }

    .facet-filter__title {
      //layout
      height: 54px;

      //typo
      font-size: 14px;
      text-align: center;
      line-height: 54px;
      font-weight: 600;

      //design
      border-bottom: 1px solid #eaeaea;
    }

    .facet-filter__category {
      //layout
      height: 50px;

      //typo
      color: #4a4a4a;
      font-size: 14px;
      text-align: center;
      line-height: 50px;
      font-weight: normal;
    }

  .facet-filter__container {
    //layout
    top: 70px;
    left: 0;
    right: 0;
    height: calc(100% - 134px - env(safe-area-inset-bottom));
    display: flex;
    padding: 0 10px 10px;
    position: absolute;
    flex-grow: 1;
  }

    .facet-filter__wrapper {
      display:flex;
      flex-direction: column;
      width: 100%;
    }

    .facet-filter__left-bar {
      //layout
      width: 35%;
      height: 100%;
      display: flex;
      overflow-x: auto;
      flex-shrink: 0;
      flex-direction: column;

      //typo
      color: #4a4a4a;
    }

      .facet-filter__button:not(:last-child) {
        //design
        border-bottom: 1px dashed #7c7c7c66;
      }

        .facet-filter__button.facet-filter__button--is-active {
          border-bottom: 1px dashed transparent;
        }

        .facet-filter__button:has(+ .facet-filter__button--is-active) {
          border-bottom: 1px dashed transparent;
        }

      .facet-filter__button--is-header-action {
        @extend .form__button--is-contained;

        //layout
        width: auto;
        height: 24px;
        padding: 0 15px;
        line-height: 24px;

        //typo
        font-size: 12px;

        &:disabled {
          //design
          background-color: #d0e1ef;
        }
      }


      .facet-filter__button__label {
        //layout
        display: flex;
        padding: 10px 14px;

        //typo
        font-size: 12px;
        line-height: 15px;

        //events
        -webkit-user-select: none;
        -webkit-touch-callout: none;
      }

      .facet-filter__button__label--is-active {
        //design
        border: 1px solid #008deb;
        border-radius: 4px 0 0 4px;
        background-color: #008deb;

        //typo
        color: #ffffff;
      }

      .facet-filter__button__label--is-applied::after {
        @include retina('product-search/check-green');
        content: ' ';

        //layout
        width: 100%;
        display: block;
        margin-left: 5px;
        background-position: left center;
      }

      .facet-filter__button__label--is-disabled {
        //typo
        color: #747474;
        opacity: 0.5;

        //events
        pointer-events: none;
      }

    .facet-filter__list-wrapper {
      //layout
      display: flex;
      padding: 10px 0;
      flex-grow: 1;
      flex-direction: column;

      //design
      border: 1px solid #ffffff;
      box-shadow: 0px 1px 2px 0px #333f4633;
      border-radius: 4px;
      background-color: #ffffff;


      .facet-filter__list {
        //layout
        height: calc(100% - 38px);
        height: calc(100% - 38px - constant(safe-area-inset-bottom));
        height: calc(100% - 38px - env(safe-area-inset-bottom));
        position: static;
      }
    }

    .facet-filter__list-wrapper--is-products {
      padding: 0 0 10px;
    }

    .facet-filter__list {
      // @extend .facet-filter__list-wrapper;

      //layout
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

      .facet-filter__element {
        //layout
        height: 40px;
        margin: 0 10px;
        display: flex;
        align-items: center;

        //typo
        font-size: 14px;

        //design
        border-bottom: 1px solid rgba(199, 199, 199, 0.5);
      }

        .facet-filter__element--is-select-links {
          //layout
          margin: 0;
          padding: 0 15px;
          justify-content: space-between;

          //typo
          color: #0071bc;
          font-size: 12px;
        }

        .facet-filter__element--has-suboptions {
          //layout
          height: auto;
          align-items: stretch;
          flex-direction: column;

          .facet-filter__action,
          .facet-filter__sub-action {
            //layout
            height: 40px;
            display: flex;
            align-items: center;
          }
        }

        .facet-filter__action {
          //layout
          width: 100%;
          overflow: hidden;

          //typo
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;

          //events
          -webkit-user-select: none;
          -webkit-touch-callout: none;
        }

          .facet-filter__action--is-group-task {
            //layout
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .facet-filter__action--select-all {
            //typo
            color: #0071bc;
            font-weight: 600;
          }

          .facet-filter__action--is-left {
            //layout
            width: auto;
            padding-left: 0;
          }

          .facet-filter__action--is-right {
            //layout
            width: auto;
          }

          .facet-filter__action--is-selected {
            @include retina('product-search/select-checkmark');

            //typo
            color: #0071bc;

            //design
            background-size: 14px 10px;
            background-position: right 10px center;
          }

          .facet-filter__action--is-ban {
            @include retina('product-search/filter-banned');

            //layout
            margin-right: 10px;

            //typo
            color: #c7c7c7;
            font-weight: 300;

            //design
            background-size: 10px 10px;
            background-position: 100% 50%;
          }

          .facet-filter__action--is-multiple {
            //layout
            display: flex;
            overflow: initial;
            white-space: normal;
            align-items: center;
            justify-content: space-between;

            //typo
            font-size: 12px;
            line-height: 13px;

            //design
            background: none;
          }

        .facet-filter__sub-action {
          //layout
          overflow: hidden;
          padding-left: 30px;
          text-overflow: ellipsis;

          //typo
          font-size: 12px;
          white-space: nowrap;

          //design
          border-top: 1px solid rgba(199, 199, 199, 0.5);
        }

        .facet-filter__sub-action--is-L3 {
          //layout
          padding-left: 45px;
        }
        .facet-filter__sub-action--is-L4 {
          //layout
          padding-left: 60px;
        }

          .facet-filter__sub-action--is-selected {
            @include retina('product-search/suboption-select-dot');

            //typo
            color: #0071bc;

            //design
            background-size: 6px;
            background-position: right 14px center;
          }

  .facet-filter__list-header {
    //layout
    width: 100%;
    display: flex;
    padding: 0 10px;
    align-items: center;
    margin-bottom: 5px;
    justify-content: space-between;
  }

  .facet-filter__list-subheader {
    //layout
    padding: 0 10px;

    //typo
    color: #58666f;
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
  }

  .facet-filter__header-label {
    //typo
    font-size: 12px;
    font-weight: 600;
  }

  .facet-filter__header-label--select-all,
  .facet-filter__header-label--select-single {
    //typo
    color: #0071bc;
    font-size: 12px;
    font-weight: 600;
  }

  .facet-filter__footer {
    //layout
    width: 100%;
    height: 45px;
    bottom: 0;
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
    z-index: 1020;
    position: absolute;

    //typo
    text-align: center;
    line-height: 45px;

    //design
    border: 1px solid #c7c7c7;
    background-color: #ffffff;
  }

    .facet-filter__footer__action {
      //layout
      width: 32%;
      display: inline-block;

      &,
      &:hover,
      &:active {
        //typo
        color: #4a4a4a;
        text-decoration: none;
      }
    }

.transaction-amount__wrapper,
.transaction-date__datepicker {
  //layout
  padding: 0 10px;
}

.transaction-date__options {
  //layout
  margin-bottom: 20px;
}

.facet-filter__action--is-active {
  //typo
  color: #0071bc;
}

.facet-filter__action--is-radio-option {
  //layout
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  //typo
  font-size: 12px;
  font-weight: 400;
}

.facet-filter__action--is-radio-option input[type="radio"] {
  //lyaout
  margin: 0;

  //design
  accent-color: #0071bc;
}

.transaction-amount__inputs {
  //layout
  gap: 16px;
  display: flex;
}

.transaction-amount__input-group {
  //layout
  flex-grow: 1;
}

.facet-filter__sort-buttons-wrapper {
  //layout
  margin: 0 0 10px 0;
  padding: 0 10px;
  display: flex;
}

.facet-filter__sort-button {
  //layout
  gap: 4px;
  flex: 1;
  padding: 8px 14px;
  display: flex;
  align-items: center;
  justify-content: center;

  //design
  border: 1px solid #c7c7c7;
  border-radius: 4px;

  //typo
  font-size: 12px;
  text-align: center;
  line-height: 15px;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &,
  &:hover,
  &:active {
    //typo
    color: #4a4a4a;
    text-decoration: none;
  }
}

.facet-filter__sort-button--is-asc {
  //design
  border-right: none;
  border-radius: 4px 0 0 4px;
}

.facet-filter__sort-button--is-desc {
  //design
  border-left: none;
  border-radius: 0 4px 4px 0;
}

.facet-filter__sort-icon {
  //layout
  width: 18px;
  height: 16px;
}

.facet-filter__sort-icon--is-asc {
  @include retina('contacts/sort-asc-default');
}

.facet-filter__sort-icon--is-desc {
  @include retina('contacts/sort-desc-default');
}

.facet-filter__sort-icon--is-asc-active {
  @include retina('contacts/sort-asc-active');
}

.facet-filter__sort-icon--is-desc-active {
  @include retina('contacts/sort-desc-active');
}

.facet-filter__sort-button--is-active {
  //design
  border-color: #008deb;
  background-color: #008deb;

  &,
  &:hover,
  &:active {
    //typo
    color: #ffffff;
  }
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .facet-filter {
      @include set-margin-max-width(1440px, auto);
    }
  }
  .facet-filter__action,
  .facet-filter__button,
  .facet-filter__sort-button,
  .facet-filter__button--is-active,
  .facet-filter__header-label--select-all  {
    //events
    cursor: pointer;
  }

  .facet-filter__button {
    &:has(.facet-filter__button__label--is-disabled) {
      //events
      pointer-events: none;
    }
  }

  .facet-filter__button__label--is-disabled {
    //events
    pointer-events: none;
  }
}
