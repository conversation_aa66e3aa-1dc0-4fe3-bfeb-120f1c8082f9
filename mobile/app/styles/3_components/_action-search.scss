@import '2_mixins/retina';
@import '2_mixins/misc';
@import '2_mixins/media-queries';

.action-search__wrapper {
  //layout
  width: 100%;
  z-index: 10;
  padding: 10px;
  z-index: 999;
  margin-top: 45px;
  position: relative;

  .selector {
    //layout
    width: 100%;
    margin: 10px 0 0;
  }

  .selector--has-filter {
    display: flex;

    .action-search__filter {
      margin-left: 10px;
    }
  }

  .share-separator {
    //layout
    margin-bottom: 0;
  }

  .toggle-nav {
    //layout
    width: calc(100% + 20px);
    margin: 0 -10px;
  }
}

.action-search__wrapper--is-flex {
  margin-top: 0;
  padding: 0 10px;

  .action-search {
    margin: 0 0 10px;
  }
}

  .action-search__wrapper--has-toggle-nav {
    //layout
    padding-top: 0;

    .action-search__filter {
      //layout
      margin-top: 10px;
    }
  }

  .action-search__wrapper--is-in-panel {
    //layout
    padding: 20px 20px 0 20px;
    margin-top: 0;
  }

  .action-search__wrapper--is-independent {
    //layout
    padding: 0;
    margin-top: 0;
  }

  .action-search__wrapper--is-contacts {
    //layout
    padding-top: 0;
  }

  .action-search__wrapper--no-toggle {
    //layout
    padding-top: 10px;

    .action-search {
      //layout
      margin-top: 0;

      + .selector {
        //layout
        margin-top: 10px;
      }
    }

    .selector {
      //layout
      margin-top: 0;
    }
  }

.action-search__category {
  //layout
  height: 20px;

  //typo
  color: #4a4a4a;
  font-size: 14px;
  text-align: center;
  line-height: 30px;
  font-weight: normal;
}

  .action-search__category--is-single {
    //typo
    line-height: 20px;
  }

.action-search__input-ctn {
  //layout
  height: auto;
}

.action-search__sub-wrapper {
  flex-grow: 1;
  position: relative;;
}

  .action-search__sub-wrapper--is-for-customer-list {
    //layout
    width: calc(100% - 45px);
  }

  .action-search__sub-wrapper--is-wide {
    //layout
    width: 100%;
    display: block;
  }

  .action-search {
    //layout
    width: 100%;
    padding: 0;
    z-index: 50;
    display: block;
    position: relative;
    margin-top: 10px;

    //design
    background-color: #f8f8f8;
  }

    .action-search--has-filter {
      //layout
      width: calc(100% - 85px);
      display: inline-block;
      margin-top: 0;
    }

    .action-search__form {
      //layout
      //position: relative;
      display: flex;
      justify-content: space-between;

      .action-search__filter {
        margin-left: 10px;
      }

      .action-search__filter--is-small {
        //layout
        right: 43px;
      }

      .action-search__filter--is-for-customer-list {
        //layout
        right: 0;
        width: 35px;
      }

      .action-search__filter--is-favorites {
        //layout
        right: 0;
      }

      &.action-search__form--is-vertical {
        flex-direction: column;
      }

      .action-search__button {
        //layout
        margin-left: 10px;
      }
    }

    .action-search__input {
      @include retina('product-search/magnifying-glass');

      //layout
      width: 100%;
      height: 30px;
      margin: 0;
      display: block;
      padding: 0 20px 0 10px;
      position: relative;

      //typo
      color: #a8a8a8;
      font-size: 12px;
      font-weight: 300;
      line-height: 30px;

      //design
      border: solid 1px #c7c7c7;
      border-radius: 2px;
      background-size: 12px 12px;
      background-color: #ffffff;
      -webkit-appearance: none;
      background-position: right 10px top 50%;

      &:focus {
        //typo
        color: #000000;
        font-weight: normal;

        //design
        border-color: #0071bc;
        background-image: none;
        background-color: #ffffff;

        &::-webkit-input-placeholder {
          //typo
          color: transparent;
        }

        &:-moz-placeholder {
          //typo
          color: transparent;
        }

        &::-moz-placeholder {
          //typo
          color: transparent;
        }

        &:-ms-input-placeholder {
          //typo
          color: transparent;
        }

        &::-ms-input-placeholder {
          //typo
          color: transparent;
        }

        &::placeholder {
          //typo
          color: transparent;
        }

        + .action-search__ghost-text {
          //layout
          z-index: 1;
        }
      }
    }

      .action-search__input--has-barcode,
      .action-search__input--has-text {
        //design
        background-image: none !important;
      }

  .action-search__ghost-text {
    //layout
    top: 0;
    left: 11px;
    height: 30px;
    z-index: -1;
    position: absolute;

    //event
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;

    //typo
    color: #a8a8a8;
    font-size: 12px;
    line-height: 30px;
    font-weight: lighter;
  }

    .action-search__ghost-text--is-right {
      //layout
      left: auto;
      right: 30px;
      z-index: 0;

      //typo
      line-height: 30px;
    }

.action-search__clear {
  //layout
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  margin: 0;
  padding: 0;
  z-index: 999999;
  position: absolute;

  //design
  @include retina('product-search/search-remove');
}

.action-search__barcode-button {
  @include retina('product-search/search-button-scan');

  //layout
  top: 0;
  right: 0;
  width: 40px;
  height: 100%;
  z-index: 999;
  position: absolute;
}

  .action-search__barcode-button--is-attribute {
    //layout
    top: 22px;
    right: 25px;
    height: 44px;
  }

  .action-search__barcode-button--is-attribute-type-int {
    @extend .action-search__barcode-button--is-attribute;
    //layout
    top: 0;
  }

  .action-search__barcode-button--is-qr {
    @include retina('product-search/qr-btn');
  }

.action-search__button-wrapper {
  //layout
  display: inline-block;
  margin-top: 10px;
  margin-right: 10px;
}

// Refactor of filter
.action-search__button {
  @include ellipsis;

  //layout
  width: 75px;
  height: 30px;
  display: inline-block;
  padding: 5px 10px 5px 32px;

  //typo
  font-size: 12px;
  font-weight: normal;

  //design
  border: solid 1px #8c8c8c;
  border-radius: 2px;
  background-size: 16px 14px;
  background-color: #ffffff;
  background-position: 10px 50%;

  //event
  cursor: default;
  user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;

  &,
  &:hover,
  &:active {
    //typo
    color: #8c8c8c;
    text-decoration: none;
  }
}

  .action-search__button--is-store {
    //layout
    padding: 5px;

    //typo
    text-align: center;
    font-weight: 600;

    //design
    border: solid 1px #4a90e2;

    &,
    &:hover,
    &:active {
      //typo
      color: #4a90e2;
    }
  }

  .action-search__button--is-scanner {
    @include retina('product-search/search-button-scan');

    //design
    border-color: #0071bc;
    background-size: 16px 14px;
    background-position: 9px 50%;

    &,
    &:hover,
    &:active {
      //typo
      color: #0071bc;
      text-decoration: none;
    }
  }

  .action-search__button--is-sort {
    @include retina('product-search/sort-btn');

    //design
    width: 38px;
    padding: 0;
    border-color: #0071bc;
    background-size: 16px 14px;
    background-position: 9px 50%;

    &,
    &:hover,
    &:active {
      //typo
      color: #0071bc;
      text-decoration: none;
    }
  }

.action-search__filter {
  @include retina('product-search/search-filter-inactive');

  //layout
  float: right;
  width: 75px;
  height: 30px;
  display: inline-block;
  padding: 5px 10px 5px 32px;

  //typo
  font-size: 12px;
  font-weight: normal;

  //design
  border: solid 1px #8c8c8c;
  border-radius: 2px;
  background-size: 16px 14px;
  background-color: #ffffff;
  background-position: 9px 50%;

  //event
  cursor: default;
  user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;

  &,
  &:hover,
  &:active {
    //typo
    color: #8c8c8c;
    text-decoration: none;
  }
}

  .action-search__filter--no-label {
    width: auto;
    padding: 5px 10px 5px 24px;
  }

  .action-search__filter--is-selected {
    @include retina('product-search/search-filter-active');

    //typo
    color: #45a621;

    //design
    border-color: #45a621;
    background-size: 16px 14px;
    background-color: #ffffff;
    background-position: 9px 50%;
  }

  .action-search__filter--is-small {
    //layout
    width: 35px;
    padding: 0;
  }

  .action-search__filter--is-favorites {
     //design
    border-color: transparent;
    background-size: 35px 30px;
  }

  .action-search__filter--is-favorites-selected {
    @include retina('contacts/star-filter-active');
  }

  .action-search__filter--is-favorites-unselected {
    @include retina('contacts/star-filter-default');

    //design
     border-color: #c7c7c7;
  }

  .action-search__wrapper--has-shadow {
    //design
    box-shadow: 0 1px 4px 0 rgba(199, 199, 199, 1);
  }

.action-advanced-search {
  //layout
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  z-index: 1000;
  position: fixed;
  flex-direction: column;

  //design
  background-color: #f8f8f8;
}

  .action-advanced-search__header {
    //layout
    display: flex;
    padding: 20px 10px;
    align-items: center;
    justify-content: space-between;
  }

    .action-advanced-search__header--is-close {
      @include retina('contacts/advanced-search-close');

      //layout
      width: 24px;
      height: 24px;
    }

    .action-advanced-search__header--is-title {
      //layout
      margin: 0;

      //typo
      font-size: 16px;
      font-weight: 600;
    }

    .action-advanced-search__header--is-clear {
      //typo
      color: #0071bc;
      font-size: 12px;
      font-weight: 600;
    }

  .action-advanced-search__form {
    //layout
    padding: 0 10px;
    display: flex;
    flex-grow: 1;
    overflow-y: auto;
    margin-bottom: 65px;
    flex-direction: column;
  }

  .contacts-filter-wrapper {
    //layout
    padding: 0 10px;
    margin-bottom: 10px;
  }
  
  .device--is-desktop,
  .device--is-tablet-md-landscape {
    @include respond-to($breakpoint-sm, min-width) {
      .action-search__wrapper {
        //layout
        margin-top: 65px;
      }

      .action-search__wrapper--is-flex {
        margin-top: 0;
      }

      .action-advanced-search {
        //layout
        height: 100vh;
        position: absolute;
      }
    }

    @include respond-to($breakpoint-md, min-width) {
      .action-search__wrapper--is-hidden {
        //layout
        display: none;
      }
    }

    .action-search__button,
    .action-search__filter,
    .action-advanced-search__header--is-clear,
    .action-advanced-search__header--is-close,
    .action-search__barcode-button {
      //events
      -webkit-user-select: none;
      -webkit-touch-callout: none;

      &:hover,
      &:active {
        //events
        cursor: pointer;
      }
    }
  }
