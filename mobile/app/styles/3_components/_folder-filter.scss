@import '2_mixins/retina';
@import '2_mixins/media-queries';

.folder-filter {
  //layout
  width: 100%;
  position: relative;
  margin-top: 44px;
  margin-bottom: 10px;

  //design
  border-top: solid 1px #c7c7c7;
}

  .folder-filter__category {
    //layout
    width: 100%;
    height: 44px;
    padding: 0;
    display: inline-block;

    //typo
    color: #8c8c8c;
    font-size: 13px;
    text-align: center;
    line-height: 44px;
    text-transform: uppercase;

    //design
    background: #f8f8f8;
    border-bottom: solid 1px #c7c7c7;
  }

  .folder-filter__item {
    //layout
    height: 44px;

    a {
      //events
      -webkit-user-select: none;
      -webkit-touch-callout: none;
    }
  }

    .folder-filter__item--is-brands {
      @extend .folder-filter__item;

      //typo
      text-align: center;

      .folder-filter__link {
        //layout
        padding: 0;

        //design
        background-position: center !important;
      }
    }

      .folder-filter__link {
        //layout
        width: 100%;
        height: 44px;
        display: inline-block;
        padding-left: 60px;

        //typo
        font-size: 14px;
        font-weight: 600;
        line-height: 44px;

        //design
        border-bottom: solid 1px #c7c7c7;
        background-color: #ffffff;

        &,
        &:hover,
        &:active,
        &:focus,
        &:visited {
          //typo
          color: #4a4a4a;
          text-decoration: none;
        }
      }

        .folder-filter__link__arrow {
          //layout
          float: right;
          width: 25px;
          height: 100%;
          margin: 0 10px;
          display: block;

          //design
          @include retina('filters/filter-arrow-inactive');
        }

      .folder-filter__link--is-active {
        //design
        background-color: #008dea;

        &,
        &:hover,
        &:active,
        &:focus,
        &:visited {
          //typo
          color: #ffffff;
        }

        .folder-filter__link__arrow {
          @include retina("filters/filter-arrow-active");
        }
      }

      .folder-filter__link--is-sub-cat {
        @extend .folder-filter__link;

        //layout
        padding-left: 70px;
      }

    // Requests filters active/inactive
    .folder-filter__link--is-store-all {
      @extend .folder-filter__link;
      @include retina('filters/filter-all-requests-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-all-requests-active");
      }
    }

    .folder-filter__link--is-store-personal-shopper {
      @extend .folder-filter__link;
      @include retina('filters/filter-personal-shopper-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-personal-shopper-active");
      }
    }

    .folder-filter__link--is-store-appointment {
      @extend .folder-filter__link;
      @include retina('filters/filter-book-appointment-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-book-appointment-active");
      }
    }

    .folder-filter__link--is-store-question {
      @extend .folder-filter__link;
      @include retina('filters/filter-ask-answer-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-ask-answer-active");
      }
    }

    .folder-filter__link--is-unresolved {
      @extend .folder-filter__link;
      @include retina('filters/filter-unresolved-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-unresolved-active");
      }
    }

    .folder-filter__link--is-resolved {
      @extend .folder-filter__link;
      @include retina('filters/filter-resolved-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-resolved-active");
      }
    }

    // Messages filter active/inactive
    .folder-filter__link--is-inbox {
      @extend .folder-filter__link;
      @include retina('filters/filter-inbox-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-inbox-active");
      }
    }

    .folder-filter__link--is-sent {
      @extend .folder-filter__link;
      @include retina('filters/filter-sent-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-sent-active");
      }
    }

    .folder-filter__link--is-archived {
      @extend .folder-filter__link;
      @include retina('filters/filter-archived-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-archived-active");
      }
    }

    .folder-filter__link--is-trash {
      @extend .folder-filter__link;
      @include retina('filters/filter-deleted-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-deleted-active");
      }
    }

    .folder-filter__link--is-all {
      @extend .folder-filter__link;
      @include retina('filters/filter-all-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-all-active");
      }
    }

    .folder-filter__link--is-read {
      @extend .folder-filter__link;
      @include retina('filters/filter-read-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-read-active");
      }
    }

    .folder-filter__link--is-unread {
      @extend .folder-filter__link;
      @include retina('filters/filter-unread-inactive');

      &.folder-filter__link--is-active {
        @include retina("filters/filter-unread-active");
      }
    }

  .folder-filter__link {
    &,
    &.folder-filter__link--is-active {
      //design
      background-position: 20px center;
    }
  }

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .folder-filter {
      //layout
      margin-top: 64px;
    }
    .folder-filter--is-flex {
      .folder-filter {
        margin-top: 0;
      }
    }
  }

  .folder-filter__item {
    &:hover {
      //events
      cursor: pointer;
    }
  }
}
