@import '2_mixins/media-queries';

.cta-footer-flex {
  //layout
  left: 0;
  right: 0;
  width: 100%;
  height: 64px;
  bottom: 0;
  padding: 10px;
  z-index: 50;
  display: flex;
  position: absolute;
  margin-bottom: 0;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);

  //typo
  text-align: center;

  //design
  background-color: #f8f8f8;
}

  .cta-footer-flex--is-scrolling {
    //design
    box-shadow: 0 -2px 2px 0 rgba(199, 199, 199, 0.5);
  }

  .cta-footer-flex--is-contained {
    //layout
    height: auto;
    padding: 0;
    display: inline-block;
    position: static;
    vertical-align: bottom;
  }

.cta-footer-flex__button {
  //layout
  flex: 1;
  height: 44px;
  padding: 0;

  //typo
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  line-height: 44px;

  //design
  border: none;
  border-radius: 2px;
  background-color: #0071bc;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &:hover,
  &:focus {
    //typo
    text-decoration: none;
  }
}

.cta-footer-flex__button:not(:last-child) {
  //layout
  margin-right: 10px;
}

  .cta-footer-flex__button--is-outline {
    //typo
    color: #0071bc;

    //design
    border: 1px solid #0071bc;
    background-color: transparent;
  }

  .cta-footer-flex__button--is-validate {
    //design
    background-color: #4ec026;
  }

  .cta-footer-flex__button--is-cancel {
    //design
    background-color: #9b9b9b;
  }

  .cta-footer-flex__button--is-disabled {
    //design
    opacity: 0.5;
    background: #DCE3E8;

    //typo
    color: #788893;

    //event
    pointer-events: none;
  }

  .device--is-desktop,
  .device--is-tablet-md-landscape {
    @include respond-to($breakpoint-sm, min-width) {
      .cta-footer-flex {
        //layout
        margin-left: 10px;

        .cta-footer-flex__button {
          //layout
          flex: none;
          width: 25%;

          //design
          background: #008DEB;
          border-radius: 4px;
        }

        .cta-footer-flex__button--is-disabled {
          //design
          background: #DCE3E8;
        }
      }
      .cta-footer-flex--is-scrolling {
        //design
        box-shadow: none;
      }
    }
    .cta-footer-flex {
      .cta-footer-flex__button {
        &:hover,
        &:active {
          //typo
          color: #ffffff;

          //design
          background: #0766A4;
        }

        //events
        cursor: pointer;
      }

      .cta-footer-flex__button--is-outline {
        //design
        background: #FFFFFF;

        //typo
        color: #0071bc;

        &:hover {
          //typo
          color: #008DEB;

          //design
          background: #EAF7FF;
        }
      }
    }
  }

