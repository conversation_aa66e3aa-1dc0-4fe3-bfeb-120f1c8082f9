@import '2_mixins/misc';
@import '2_mixins/media-queries';

.main-wrapper {
  //layout
  margin-top: 45px;

  &,
  &.scroller {
    //layout
    height: calc(100% - 45px);
  }
}
  .main-wrapper--is-look-details {
    //design
    background-color: #ffff
  }

  .main-wrapper--has-tall-header {
    //layout
    z-index: 1001;
    position: relative;
    margin-top: 131px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 131px);
    }
  }

  .main-wrapper--has-pagination {
    &,
    &.scroller {
      //layout
      height: calc(100% - 109px);
    }

    &.main-wrapper--has-footer {
      //layout
      margin-bottom: 108px;

      &,
      &.scroller {
        //layout
        height: calc(100% - 153px);
      }
    }
  }

  .main-wrapper--has-tabbar {
    //layout
    margin: 45px 0 55px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 100px);
    }
  }

  // Header, footer & navbar modifiers
  .main-wrapper--has-footer {
    //layout
    margin: 45px 0 44px;
    margin: 45px 0 calc(44px + constant(safe-area-inset-bottom));
    margin: 45px 0 calc(44px + env(safe-area-inset-bottom));

    &,
    &.scroller {
      //layout
      height: calc(100% - 89px);
      height: calc(100% - 89px - constant(safe-area-inset-bottom));
      height: calc(100% - 89px - env(safe-area-inset-bottom));
    }
  }

  .main-wrapper--has-cta-footer {
    //layout
    margin: 45px 0 64px;
    padding-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 109px);
    }
  }

  .main-wrapper--has-navbar {
    //layout
    margin: 45px 0 80px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 125px);
    }
  }

  .main-wrapper--is-share {
    //layout
    padding: 15px 20px 20px;
    margin-top: 105px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 105px);
    }
  }

  // Page specific modifiers
  .main-wrapper--is-share-products {
    //layout
    padding: 0;
    margin-top: 0;

    &,
    &.scroller {
      //layout
      height: calc(100% - 95px);

      //animation
      transition: margin-bottom 0.3s ease-in 0s, height 0.3s ease-in 0s;
    }

    &.main-wrapper--has-products-drawer {
      &,
      &.scroller {
        //layout
        height: calc(100% - 155px);
      }

      &.main-wrapper--has-minimized-products-drawer {
        &,
        &.scroller {
          //layout
          height: calc(100% - 125px);
        }
      }
    }

    &.main-wrapper--has-no-search-form {
      //layout
      margin-top: 45px;
    }
  }

  .main-wrapper--is-share-asset {
    //layout
    padding: 0;
    margin-top: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 95px);

      //animation
      transition: margin-bottom 0.3s ease-in 0s, height 0.3s ease-in 0s;
    }

    .main-wrapper--is-products-list {
      //layout
      margin-top: -10px;
    }
  }

  .main-wrapper--has-products-drawer {
    //layout
    margin-bottom: 60px;

    //animation
    transition: margin-bottom 0.3s ease-in 0s, height 0.3s ease-in 0s;

    &,
    &.scroller {
      //layout
      height: calc(100% - 105px);
    }

    &.main-wrapper--has-minimized-products-drawer {
      //layout
      margin-bottom: 30px;

      &,
      &.scroller {
        //layout
        height: calc(100% - 75px);
      }
    }
  }

  @media (min-height: 732px) {
    .main-wrapper--is-share-products {
      &.main-wrapper--has-products-drawer {
        &,
        &.scroller {
          //layout
          height: calc(100% - 175px);

          //animation
          transition: margin-bottom 0.15s ease-in 0s, height 0.15s ease-in 0s;
        }
      }
    }

    .main-wrapper--has-products-drawer {
      //layout
      margin-bottom: 80px;

      //animation
      transition: margin-bottom 0.15s ease-in 0s, height 0.15s ease-in 0s;

      &,
      &.scroller {
        //layout
        height: calc(100% - 125px);
      }
    }
  }

  .main-wrapper--is-details {
    // layout
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    bottom: 0;
    margin: 0;
    padding: 0;
    z-index: 1051;
    position: fixed;
    background: #f8f8f8;
  }

  .main-wrapper--is-filter {
    //layout
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    bottom: 0;
    margin: 0;
    padding: 0;
    z-index: 1051;
    position: fixed;
  }

  .main-wrapper--is-reporting {
    //layout
    bottom: 0;
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
    padding: 0;
    position: relative;
    margin-top: 135px;
    margin-top: calc(135px + constant(safe-area-inset-bottom));
    margin-top: calc(135px + env(safe-area-inset-bottom));
    margin-bottom: 80px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 215px);
      height: calc(100% - 215px - constant(safe-area-inset-bottom));
      height: calc(100% - 215px - env(safe-area-inset-bottom));
    }

    &>div {
      display: flex;
      height: 100%;
      flex-direction: column;

      .empty-state {
        display: flex;
        flex-grow: 1;
        position: unset;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .main-wrapper--is-reporting-dashboard {
    //layout
    margin-top: 95px;
    margin-top: calc(95px + constant(safe-area-inset-bottom));
    margin-top: calc(95px + env(safe-area-inset-bottom));

    &,
    &.scroller {
      //layout
      height: calc(100% - 175px);
      height: calc(100% - 175px - constant(safe-area-inset-bottom));
      height: calc(100% - 175px - env(safe-area-inset-bottom));
    }
  }

  .main-wrapper--is-messages-list {
    //layout
    margin: 0;
    padding: 0 10px;
    padding-bottom: 10px;
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
    padding-bottom: calc(10px + env(safe-area-inset-bottom));

    &,
    &.scroller {
      //layout
      height: calc(100% - 139px);
    }
  }

  .main-wrapper--is-addressbook {
    //layout
    margin: 0;
    padding: 0 10px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 105px);
    }
  }

  .main-wrapper--is-addressbook-with-footer {
    &,
    &.scroller {
      //layout
      height: calc(100% - 148px);
    }
  }

  .main-wrapper--is-empty-addressbook {
    &,
    &.scroller {
      //layout
      height: calc(100% - 105px);
    }
  }

  .main-wrapper--is-empty-addressbook-with-footer {
    &,
    &.scroller {
      //layout
      height: calc(100% - 149px);
    }
  }

  .main-wrapper--is-contacts,
  .main-wrapper--is-client {
    //layout
    padding: 0 10px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 184px);
    }
  }

  .main-wrapper--is-multi-contacts,
  .main-wrapper--is-client-contacts {
    //layout
    padding: 0 10px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 228px);
    }
  }

  .main-wrapper--is-shop-customer {
    //layout
    bottom: 0;
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
    margin-top: 0;
    margin-top: constant(safe-area-inset-bottom);
    margin-top: env(safe-area-inset-bottom);
    margin-bottom: 0;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
    padding-bottom: 12px;
  }

  .main-wrapper--is-select-contacts {
    //layout
    padding: 0 10px;
    margin-bottom: 0;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 257px);
    }
  }

  .main-wrapper--is-client-multi-contacts,
  .main-wrapper--is-multi-select-contacts {
    //layout
    padding: 0 10px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 301px);
    }
  }

  .main-wrapper--is-client-select-contacts {
    //layout
    padding: 0 10px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 311px);
    }
  }

  .main-wrapper--is-client-multi-select-contacts {
    //layout
    padding: 0 10px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 355px);
    }
  }

  .main-wrapper--is-compose-contacts {
    //layout
    padding: 0 10px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &,
    &.scroller {
      //layout
      height: calc(100% - 184px);
    }
  }

  .main-wrapper-is--store-appointments {
    &.scroller {
      //layout
      height: calc(100% - 60px);
    }
  }

  .main-wrapper-is--store-appointments-edit {
    &.scroller {
      //layout
      height: calc(100% - 115px);
      height: calc(100% - 115px - constant(safe-area-inset-bottom));
      height: calc(100% - 115px - env(safe-area-inset-bottom));
    }
  }

  .main-wrapper-is--store-appointments-override {
    &.scroller {
      //layout
      height: calc(100% - 30px);
    }
  }

  // Generic padding modifiers
  .main-wrapper--is-1010 {
    //layout
    padding: 10px;
  }

  .main-wrapper--is-0010 {
    //layout
    padding: 0 10px;
  }

  .main-wrapper--is-0020 {
    //layout
    padding: 0 20px;
  }

  .main-wrapper--is-1015 {
    //layout
    padding: 10px 15px;
  }

  .main-wrapper--is-1000 {
    //layout
    padding: 10px 0;
  }

  .main-wrapper--is-products-list {
    //layout
    display: block;
    padding: 10px;
    overflow: hidden;
    position: relative;
    margin-bottom: 0;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);

    &.main-wrapper--is-bo-panel {
      //layout
      padding: 20px;
      min-height: 200px;
    }

    .form__button-wrapper {
      //layout
      padding: 0 5px;
    }

    .form__button {
      //layout
      margin: 10px 0 0;
    }

    .form__button--is-multiple {
      &:first-child {
        //layout
        margin-right: 5px;
      }

      &:last-child {
        //layout
        margin-left: 5px;
      }
    }
  }

  .main-wrapper--is-related-products {
    //layout
    border-top: .5px solid rgba(199, 199, 199, 0.5);
    padding-top: 20px;
    margin-bottom: 50px;
  }

  .main-wrapper--is-shop-products-scan-list {
    .main-wrapper--is-products-list {
      //layout
      margin-bottom: 0;
    }

    &,
    &.scroller {
      //layout
      height: calc(100% - 100px);
    }
  }

  .main-wrapper--is-list {
    //layout
    margin-top: 0;

    &,
    &.scroller {
      //layout
      height: calc(100% - 94px);
      height: calc(100% - 94px - constant(safe-area-inset-bottom));
      height: calc(100% - 94px - env(safe-area-inset-bottom));
    }

    &.main-wrapper--is-list-with-cta-footer {
      &,
      &.scroller {
        //layout
        height: calc(100% - 160px);
        height: calc(100% - 160px - constant(safe-area-inset-bottom));
        height: calc(100% - 160px - env(safe-area-inset-bottom));
      }
    }
  }

  .main-wrapper--is-list-with-footer,
  .main-wrapper--is-list-with-toggle-nav {
    //layout
    margin-top: 0;

    &,
    &.scroller {
      //layout
      height: calc(100% - 139px);
      height: calc(100% - 139px - constant(safe-area-inset-bottom));
      height: calc(100% - 139px - env(safe-area-inset-bottom));
    }
  }

  .main-wrapper--is-list-with-toggle-nav {
    &.main-wrapper--is-list-with-footer {
      &,
      &.scroller {
        //layout
        height: calc(100% - 183px);
        height: calc(100% - 183px - constant(safe-area-inset-bottom));
        height: calc(100% - 183px - env(safe-area-inset-bottom));
      }
    }

    &.main-wrapper--is-list-with-cta-footer {
      &,
      &.scroller {
        //layout
        height: calc(100% - 203px);
        height: calc(100% - 203px - constant(safe-area-inset-bottom));
        height: calc(100% - 203px - env(safe-area-inset-bottom));
      }
    }
  }

  .main-wrapper--is-1020 {
    //layout
    padding: 10px 20px;
  }

  .main-wrapper--is-1520 {
    //layout
    padding: 15px 20px;
  }

  .main-wrapper--is-1515 {
    //layout
    padding: 15px;
  }

  .main-wrapper--is-2020 {
    //layout
    padding: 20px;

    &.main-wrapper--has-separator {
      &:after {
        //layout
        left: 20px;
        right: 20px;
      }
    }
  }
  .main-wrapper--is-2000 {
    //layout
    padding: 20px 0px;
  }

  .main-wrapper--is-onboarding {
    //layout
    height: 100%;
    position: relative;
    margin-top: 0;
  }

  .main-wrapper--is-scrollable {
    //layout
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }

  .main-wrapper--has-floating-button {
    //layout
    padding-bottom: 60px;
  }

  .main-wrapper--is-shop-products {
    .action-search__wrapper--is-in-panel,
    .main-wrapper--is-products-list.main-wrapper--is-bo-panel {
      //layout
      padding: 10px;
    }
  }

.main-wrapper__title {
  //typo
  color: #4a4a4a;
  font-size: 13px;
  font-weight: 600;

  &:not(.main-wrapper__title--is-first) {
    //layour
    margin-top: 20px;
  }
}

.main-wrapper--is-share-social-shop {
  //layout
  margin-bottom: 20px;
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .main-wrapper {
      //layout
      margin-top: 65px;

      &,
      &.scroller {
        //layout
        height: calc(100% - 95px);
      }
    }

    .main-wrapper--is-share-asset {
      //layout
      margin-top: -12px;

      &.scroller {
        //layout
        height: calc(100% - 115px);
      }
    }

    .main-wrapper--is-share-products {
      //layout
      margin-top: 0;

      &.scroller {
        //layout
        height: calc(100% - 115px);
      }

      &.main-wrapper--has-products-drawer {
        &.scroller {
          //layout
          height: calc(100% - 195px);
        }
      }

      &.main-wrapper--has-minimized-products-drawer {
        &,
        &.scroller {
          //layout
          height: calc(100% - 145px);
        }
      }

      &.main-wrapper--has-no-search-form {
        //layout
        margin-top: 65px;
      }
    }

    .main-wrapper--is-onboarding {
      //layout
      margin-top: 95px;

      .flash-message {
        //layout
        top: -8%;
      }
    }

    .main-wrapper--is-filter {
      @include set-margin-max-width(1440px, auto);
    }

    .main-wrapper--is-scrollable {
      //layout
      overflow-y: auto;
    }

    .main-wrapper--has-pagination {

      &.main-wrapper--has-footer {
        //layout
        margin-bottom: 108px;

        &,
        &.scroller {
          //layout
          height: calc(100% - 173px);
        }
      }
    }

    .main-wrapper--has-footer {
      //layout
      margin: 65px 0 44px;
      margin: 65px 0 calc(44px + constant(safe-area-inset-bottom));
      margin: 65px 0 calc(44px + env(safe-area-inset-bottom));

      &,
      &.scroller {
        //layout
        height: calc(100% - 109px);
        height: calc(100% - 109px - constant(safe-area-inset-bottom));
        height: calc(100% - 109px - env(safe-area-inset-bottom));
      }
    }

    .main-wrapper--is-contacts,
    .main-wrapper--is-client {
      &.scroller {
        //layout
        height: calc(100% - 204px);
      }
    }

    .main-wrapper--is-multi-contacts,
    .main-wrapper--is-client-contacts {
      &.scroller {
        //layout
        height: calc(100% - 248px);
      }
    }

    .main-wrapper--is-addressbook {
      &.scroller {
        //layout
        height: calc(100% - 125px);
      }
    }

    .main-wrapper--is-compose-contacts {
      &.scroller {
        //layout
        height: calc(100% - 194px);
      }

    }

    .main-wrapper--is-list-with-footer,
    .main-wrapper--is-list-with-toggle-nav {
      &.scroller {
        //layout
        height: calc(100% - 159px);
        height: calc(100% - 159px - constant(safe-area-inset-bottom));
        height: calc(100% - 159px - env(safe-area-inset-bottom));
      }
    }

    .main-wrapper--is-list-with-toggle-nav {
      &.main-wrapper--is-list-with-footer {
        &,
        &.scroller {
          //layout
          height: calc(100% - 203px);
          height: calc(100% - 203px - constant(safe-area-inset-bottom));
          height: calc(100% - 203px - env(safe-area-inset-bottom));
        }
      }

      &.main-wrapper--is-list-with-cta-footer {
        &,
        &.scroller {
          //layout
          height: calc(100% - 223px);
          height: calc(100% - 223px - constant(safe-area-inset-bottom));
          height: calc(100% - 223px - env(safe-area-inset-bottom));
        }
      }
    }

    .main-wrapper--is-share {
      //layout
      margin-top: 125px;
      &,
      &.scroller {
        //layout
        height: calc(100% - 135px);
      }
    }

    .main-wrapper--is-reporting-dashboard {
      height: calc(100% - 140px);
      height: calc(100% - 140px - env(safe-area-inset-bottom));
      margin-top: 50px;
      margin-top: calc(50px + env(safe-area-inset-bottom));
    }

    .main-wrapper--is-reporting-kpi {
      height: calc(100% - 189px);
      height: calc(100% - 189px - env(safe-area-inset-bottom));
      margin-top: 70px;
      margin-top: calc(70px + env(safe-area-inset-bottom));
    }

    .main-wrapper--is-reporting-transactions {
      height: calc(100% - 170px);
      height: calc(100% - 170px - env(safe-area-inset-bottom));
      margin-top: 70px;
      margin-top: calc(70px + env(safe-area-inset-bottom));
    }
  }

  @include respond-to($breakpoint-md, min-width) {
    .main-wrapper--is-multi-contacts,
    .main-wrapper--is-client-contacts {
      &,
      &.scroller {
        //layout
        height: calc(100% - 167px);
      }
    }

  
    .main-wrapper--is-client {
      &,
      &.scroller {
        //layout
        height: calc(100% - 130px);
      }
    }

    .main-wrapper--is-full-hight {
      &,
      &.scroller {
        //layout
        height: 100%;
      }
    }
  }
}
