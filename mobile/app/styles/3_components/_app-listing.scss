@import '2_mixins/retina';

.app-listing__wrapper {
  //layout
  position: relative;
  overflow-y: scroll;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;

  &,
  &.scroller {
    //layout
    height: calc(100% - 95px);
  }
}

.action-search__wrapper {
  + .app-listing__wrapper {
    //design
    border-top: 1px solid #c7c7c7;
  }
}

  .app-listing__wrapper--has-footer {
    //layout
    margin-bottom: 44px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 139px);
    }
  }

  .app-listing__wrapper--has-drawer {
    //layout
    margin-bottom: 60px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 155px);
    }
  }

  .app-listing__wrapper--has-minimized-drawer {
    //layout
    margin-bottom: 30px;

    &,
    &.scroller {
      //layout
      height: calc(100% - 125px);
    }
  }

.app-listing {
  //design
  background-color: #ffffff;
  margin-bottom: 0;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

  .app-listing--is-share-search {
    //layout
    height: auto;

    //design
    border-top: 1px solid #c7c7c7;

    //animation
   transition: max-height 0.5s ease 0s;
  }

  .app-listing--is-share-search-container {
    //layout
    margin-top: 10px;

    //design
    border: 1px solid #D6D6D6;
    box-shadow: 0px 5px 10px 5px #D6D6D6;
    border-bottom: none;
  }

  .app-listing--is-share-search-closed {
    //layout
    max-height: 0;
    overflow-y: hidden;
  }

  .app-listing__collapse-button {
    @include retina('misc/arrow-dropdown');

    //layout
    float: right;
    width: 24px;
    height: 44px;

    //design
    transform: rotate(-90deg);

    //animation
    transition: transform .3s ease-in-out;
  }

  .app-listing__collapse-button--is-open {
    //design
    transform: rotate(0);
  }

  .app-listing--is-product-library {
    //layout
    margin-bottom: 30px;

    //design
    border-bottom: 1px solid #e4e4e4;

    .app-listing__element {
      //design
      border-top: 1px solid #e4e4e4;
      border-bottom: none;
    }

    .app-listing__label,
    .app-listing__link {
      //layout
      padding: 0 35px;
      max-width: 100%;

      //typo
      font-size: 12px;
      text-indent: 0;
      font-weight: normal;
      -webkit-text-stroke: 0.25px #979797;
    }

    .app-listing__link {
      //layout
      padding: 0 25px;
    }
  }

.app-listing__element {
  //layout
  height: 44px;
  line-height: 44px;

  //design
  border-bottom: 1px solid #c7c7c7;
}

.app-listing__element--is-share-search-header {
  //layout
  padding-right: 10px;

  //typo
  color: #4a4a4a;
  font-size: 13px;
  text-align: center;
  font-weight: bold;

  //design
  border: none;
}

.app-listing__element--is-settings {
  @extend .app-listing;

  //layout
  position: relative;

  &:first-child {
    //design
    border-top: 1px solid #c7c7c7;
  }
}

.app-listing__element--is-middle {
  @extend .app-listing__element--is-settings;

  //layout
  margin-bottom: 0;
}

.app-listing__element--is-selected {
  @include retina('contacts/checkmark-contact');

  //design
  background-size: 14px 10px;
  background-position: center right 20px;
}

.app-listing__element__date-input {
  //layout
  right: 0;
  margin: 0;
  height: 100%;
  padding: 0;
  position: absolute;

  //typo
  text-align: right;
  font-family: inherit;

  //design
  border: none;
  background-color: #ffffff;
  -webkit-appearance: none;

  &::-webkit-inner-spin-button,
  &::-webkit-clear-button {
    //layout
    display: none;
  }

  &::-webkit-date-and-time-value {
    //layout
    width: 100%;
  }
}

.app-listing__label,
.app-listing__link {
  //layout
  width: 100%;
  height: 100%;
  display: inline-block;
  overflow: hidden;
  max-width: calc(100% - 20px);

  //typo
  font-size: 14px;
  font-weight: 600;
  text-indent: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &,
  &:hover,
  &:active,
  &:visited {
    //typo
    color: #4a4a4a;
    text-decoration: none;
  }
}

.app-listing__link--is-settings {
  //typo
  font-weight: normal;
}

.app-listing__label {
  //layout
  max-width: unset;

  //typo
  font-weight: normal;
}

.app-listing__element--is-hidden {
  //layout
  display: none;
  visibility: hidden;

  //design
  opacity: 0;
}

.app-listing__toggle-switch-panel {
  //layout
  width: 100%;
  height: 44px;
  position: relative;
  line-height: 44px;
}

.app-listing__toggle-switch-panel--is-message-compose {
  //design
  border-top: 1px solid #c7c7c7;
  border-bottom: 1px solid #c7c7c7;
}

.device--is-desktop {
  .app-listing__element,
  .app-listing__element__date-input {
    //events
    cursor: pointer;
  }
}

