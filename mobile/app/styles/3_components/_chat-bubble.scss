.chat-bubble__container {
  //layout
  float: left;
  display: block;
  max-width: calc(50% + 50px);

  //typo
  color: #4a4a4a;

  &::after {
    //layout
    clear: both;
    content: "";
    display: table;
  }
}

  .chat-bubble__container--is-rep {
    //typo
    color: #ffffff;

    &,
    .chat-bubble__wrapper,
    .chat-bubble__info__date {
      //layout
      float: right;
    }

    .chat-bubble__info__attachment {
      //layout
      float: left;
    }
  }

  .chat-bubble__container--is-attachment {
    //layout
    max-width: 100%;
    min-height: 135px;
    margin-left: 0;
    margin-right: 0;

    &.chat-bubble__container--is-rep {
      //layout
      min-height: 135px;
      margin-right: 0;
    }

    + .chat-bubble__container--is-attachment {
      .chat-bubble--is-attachment {
        //layout
        margin-top: 2px;
      }
    }
  }

  .chat-bubble__container--is-request {
    //layout
    width: 100%;
    max-width: 100%;

    .chat-bubble__wrapper {
      //layout
      width: 100%;
      margin-bottom: 5px;
    }
  }

  .chat-bubble__wrapper {
    //layout
    float: left;
    line-height: 20px;

    //typo
    font-size: 12px;
  }

  .chat-bubble {
    //layout
    float: left;
    padding: 10px;
    position: relative;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 3px;

    //design
    border: none;
    border-radius: 10px 10px 10px 0;
    background-color: #ffffff;

    a {
      &,
      &:hover,
      &:active,
      &:visited {
        //typo
        color: #4a4a4a;
        text-decoration: underline;
      }
    }

    &::before,
    &::after {
      //layout
      width: 0;
      height: 0;
      content: ' ';
      position: absolute;
    }

    &::before {
      //layout
      left: -14px;
      bottom: 1px;

      //design
      border: 7px solid;
      border-color: #f8f8f8;
      border-radius: 0 0 10px 0;
    }

    &::after {
      //layout
      left: -9px;
      bottom: 0px;
      z-index: -1;

      //design
      border: 6px solid;
      border-color: transparent #ffffff #ffffff transparent;
    }
  }
    .chat-bubble--is-typing {
      //layout
      display: inline-block;
    }

    .chat-bubble--is-rep {
      //layout
      float: right;

      //design
      background-color: #008deb;
      border-radius: 10px 10px 0;

      a {
        &,
        &:hover,
        &:active,
        &:visited {
          //typo
          color: #ffffff;
        }
      }

      &::before {
        //layout
        left: auto;
        right: -14px;

        //design
        border-radius: 0 0 0 10px;
      }

      &::after {
        //layout
        left: auto;
        right: -9px;

        //design
        border-color: transparent transparent #008deb #008deb;
      }
    }

    .chat-bubble--is-attachment {
      //layout
      padding: 0;
      margin-top: 12px;

      //design
      border: 1px solid #c7c7c7;
      box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5);
      border-radius: 5px;
      background-color: #ffffff;

      &::before,
      &::after {
        //layout
        display: none;
      }
    }

    .chat-bubble--is-request {
      @extend .chat-bubble--is-attachment;

      //layout
      width: 100%;
      padding: 10px;
    }

    .chat-bubble__message {
      //layout
      word-break: break-word;
      white-space: pre-line;
    }

    .chat-bubble__message--is-product-name {
      //layout
      white-space: normal;
    }

    .chat-bubble__attachment__image {
      //layout
      width: 100%;
      min-height: 100px;

      //design
      border-radius: 3px;
    }

      .chat-bubble__attachement__image--is-product {
        //design
        border-radius: 3px 3px 0 0;
      }

    .chat-bubble__attachment__text-ctn {
      padding: 5px 10px;
      display: inline-block;

      //typo
      color: #4a4a4a;
      font-size: 12px;
    }

    .chat-bubble__attachment__name {
      //layout
      display: block;
      line-height: 16px;

      //typo
      font-weight: bold;
    }

    .chat-bubble__price--is-regular {
      //typo
      text-decoration: line-through;
    }

    .chat-bubble__price--is-sale {
      //layout
      padding-left: 5px;

      //typo
      font-weight: bold;
    }

    .chat-bubble__price__text {
      //layout
      display: none; // for all retailers except saks
    }

    .chat-bubble__attachment__price--is-saks { // saks only
      .chat-bubble__price--is-regular,
      .chat-bubble__price--is-sale {
        //typo
        text-decoration: none;

        .chat-bubble__price__text {
          //layout
          display: inline;
        }
      }
    }

    .chat-bubble__meta {
      //layout
      display: flex;
      margin-top: 10px;
      align-items: center;
      justify-content: space-between;
    }

    .chat-bubble__request-status {
      //typo
      font-weight: 700;
      text-transform: uppercase;
    }

    .chat-bubble__view-request {
      //typo
      color: #008dea;
      font-weight: 700;
    }

  .chat-bubble__info {
    //layout
    width: 100%;
    padding: 2px 0 0;
    display: inline-block;
    vertical-align: top;

    //typo
    color: #979797;
    font-size: 10px;
    text-align: center;
    font-weight: normal;
    letter-spacing: -0.2px;
  }

    .chat-bubble__info__date {
      //layout
      float: left;
    }

    .chat-bubble__info--is-name {
      //layout
      display: inline;
    }

  .chat-bubble__typing {
    //layout
    width: 32px;
  }

@media only screen and (min-width: 375px) {
  .chat-bubble__info {
    //typo
    letter-spacing: 0;
  }
}

.device--is-desktop {
  .chat-bubble__message {
    //events
    -webkit-user-select: text !important;
    -webkit-touch-callout: default !important;
  }
}

.device--is-desktop {
  .chat-bubble__attachment__image {
    //events
    cursor: pointer;
  }
  .chat-bubble__attachement__image--is-product {
    //events
    pointer-events: none;
  }
}
