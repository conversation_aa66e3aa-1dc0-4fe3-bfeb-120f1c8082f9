@import '2_mixins/retina';
@import '2_mixins/media-queries';

.toggle-nav {
  //layout
  width: 100%;
  height: 44px;
  z-index: 1;
  position: relative;
  margin-top: 45px;

  //design
  box-shadow: 0 0 4px 1px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

  .toggle-nav--is-inside {
    //layout
    margin-top: 0;
  }

.toggle-nav--is-reports {
  //layout
  display: none;
}

.toggle-nav__tab {
  //layout
  width: 50%;
  height: 100%;
  padding: 2.5px;
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  justify-content: center;

  //typo
  font-size: 14px;
  text-align: center;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &,
  &:hover,
  &:active,
  &:visited {
    //typo
    color: #8c8c8c;
    text-decoration: none;
  }
}

.toggle-nav__tab--is-active {
  //layout
  padding-top: 3.5px;

  //typo
  color: #0070bb;

  //design
  border-bottom: 1px solid #0070bb;

  &:hover,
  &:active,
  &:visited {
    //typo
    color: #0070bb;
  }
}

.toggle-nav__tab__text-wrapper {
  //typo
  line-height: 15px;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.main-wrapper--is-task-view {
  .toggle-nav {
    //design
    border-top: 1px solid #f0f0f0;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
  }
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .toggle-nav {
      //layout
      margin-top: 65px;

    }

    .toggle-nav--is-flex {
      margin-top: 0;
      flex-shrink: 0;
    }
    
    .mode-switcher--is-flex {
      .toggle-nav {
        margin-top: 0;
      }
    }

    .toggle-nav--is-reports {
      //layout
      display: block;
    }

    .toggle-nav__tab {
      //layout
      width: auto;
      padding: 2.5px 40px;
    }

    .toggle-nav__tab--is-active {
      //layout
      padding-top: 3.5px;
    }
  }

  .toggle-nav__tab {
    &:hover,
    &:active {
      //events
      cursor: pointer;
    }
  }
}
