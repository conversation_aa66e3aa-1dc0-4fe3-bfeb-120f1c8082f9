.share-list {
  //layout
  width: 100%;
  height: 48px;
  padding: 0;
  display: flex;

  //design
  border: solid 1px #c7c7c7;
  box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5);
  border-radius: 2px;
  background-color: #ffffff;
}

  .share-list__item {
    //layout
    width: 33.33333333%;
    display: flex;
    align-items: center;
    justify-content: center;

    //typo
    text-align: center;
  }

    .share-list__item--is-middle {
      //design
      border-left: solid 1px #c7c7c7;
      border-right: solid 1px #c7c7c7;
    }

    .share-list__item--is-active {
      //design
      background-image: linear-gradient(0deg, #ffffff 0%, #EDEDED 100%);

      .share-list__link {
        //typo
        color: #008dea;
      }
    }

    .share-list__item--is-disabled {
      //layout
      opacity: 0.25;
    }

    .share-list__link {
      //typo
      font-size: 14px;
      font-weight: 600;

      //events
      -webkit-user-select: none;
      -webkit-touch-callout: none;

      &,
      &:hover,
      &:active {
        //typo
        color: #4a4a4a;
        text-decoration: none;
      }
    }
.share-list__item--is-photo {
  //layout
  position: relative;
}

.share-list__input-file {
  //layout
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  position: absolute;
}

input[type="file"].share-list__input-file--is-disabled {
  //layout
  display: none;
}

.device--is-desktop {
  .share-list__item,
  .share-list__input-file  {
    //events
    cursor: pointer;
  }

  .share-list__item--is-disabled {
    //events
    pointer-events: none;
  }
}

