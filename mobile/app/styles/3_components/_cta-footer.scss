@import '2_mixins/media-queries';

.cta-footer {
  //layout
  left: 0;
  right: 0;
  width: 100%;
  height: 64px;
  bottom: 0;
  padding: 10px;
  z-index: 50;
  position: absolute;
  margin-bottom: 0;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);

  //typo
  text-align: center;

  //design
  background-color: #f8f8f8;
}

  .cta-footer--is-scrolling {
    //design
    box-shadow: 0 -2px 2px 0 rgba(199, 199, 199, 0.5);
  }

  .cta-footer--is-contained {
    //layout
    height: auto;
    padding: 0;
    display: inline-block;
    position: static;
    vertical-align: bottom;
  }

.cta-footer__button {
  //layout
  width: calc(50% - 5px);
  float: left;
  height: 44px;
  padding: 0;
  display: inline-block;

  //typo
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  line-height: 44px;

  //design
  border: none;
  border-radius: 2px;
  background-color: #0071bc;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  + .cta-footer__button {
    //layout
    float: right;
  }

  &:hover,
  &:focus {
    //typo
    text-decoration: none;
  }
}

  .cta-footer__button--is-outline {
    //typo
    color: #0071bc;

    //design
    border: 1px solid #0071bc;
    background-color: transparent;
  }

  .cta-footer__button--is-validate {
    //design
    background-color: #4ec026;
  }

  .cta-footer__button--is-cancel {
    //design
    background-color: #9b9b9b;
  }

  .cta-footer__button--is-disabled {
    //design
    opacity: 0.5;
    background: #DCE3E8;

    //typo
    color: #788893;
  }

  .cta-footer--is-task-view-embedded {
    display: flex;
    padding-right: 10px;
    justify-content: end;
  }

  .device--is-desktop,
  .device--is-tablet-md-landscape {
    @include respond-to($breakpoint-sm, min-width) {
      .cta-footer__button {
        //layout
        width: 18%;
        margin-left: 10px;

        border-radius: 4px;

        + .cta-footer__button {
          //layout
          float: left;
        }
      }
    }
    .cta-footer__button {
      //design
      background: #008DEB;

      &:hover,
      &:active {
        //events
        cursor: pointer;

        //typo
        color: #ffffff;

        //design
        background: #0766A4;
      }
    }

    .cta-footer__button--is-outline {
      //design
      background: #FFFFFF;

      &:hover {
        //typo
        color: #008DEB;

        //design
        background: #EAF7FF;
      }
    }

    .cta-footer__button--is-disabled {
      //design
      background: #DCE3E8;
  
      //typo
      color: #788893;
  
      //event
      pointer-events: none;
    }
  }
