@import '2_mixins/retina';
@import '2_mixins/media-queries';

.footer {
  //layout
  left: 0;
  right: 0;
  width: 100%;
  height: 44px;
  bottom: 0;
  display: flex;
  z-index: 50;
  display: flex;
  position: absolute;
  margin-bottom: 0;

  //typo
  text-align: center;

  //design
  background: #0071bc;
}

  .footer--is-flex {
    position: relative !important;
  }

  .footer--is-look {
    background: none;
  }

.footer__link {
  //layout
  height: 44px;
  padding: 0 20px;
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: center;

  //typo
  font-size: 14px;
  font-weight: 600;
  line-height: 12px;

  &,
  &:hover,
  &:active,
  &:visited {
    //typo
    color: #ffffff;
    text-decoration: none;
  }
}

@media (max-width: 375px) {
  .footer__link {
    //layout
    padding: 0 15px;

    //typo
    font-size: 13px;
    line-height: 12px;
  }
}

@media (max-width: 320px) {
  .footer__link {
    //layout
    padding: 0 12px;
  }
}

.footer__link--is-disabled {
  //design
  opacity: 0.5;
  pointer-events: none;
}

.footer__link--is-left {
  //layout
  justify-content: flex-start;

  //typo
  text-align: left;
}

.footer__link--is-right {
  //layout
  justify-content: flex-end;

  //typo
  text-align: right;
}

.footer--is-desktop {
  //layout
  display: none;
}

.footer--is-mobile {
  //layout
  display: block;
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .footer__link {
      //layout
      flex: none;
      width: auto;
      padding: 0 0 0 10px;
    }

    .footer {
      //design
      background: #ffffff;
    }

    .footer__button {
      //layout
      padding: 10px 22px;
      border-radius: 4px;

      //design
      background: #008DEB;

      //typo
      color: #ffffff;
      font-size: 14px;
      line-height: 16px;

      &:hover,
      &:active {
        //design
        background: #0766A4;
      }
    }

    .footer__link--is-disabled {
      //design
      background: #DCE3E8;

      //typo
      color: #788893;

      &:hover,
      &:active {
        //design
        background: #DCE3E8;
      }
    }

    .form__button--is-password,
    .form__button--is-login {
      //typo
      color: #ffffff;
    }

    .footer--is-desktop {
      //layout
      border: none;
      display: flex;
    }

    .footer--is-mobile {
      //layout
      display: none;
    }
  }

  @include respond-to($breakpoint-md, min-width) {
    .footer--is-lookbook-create-embedded,
    .footer--is-look-embedded {
      height: auto;
      padding: 6px 10px;
      justify-content: flex-end;
    }
  }

  .footer__button {
    //events
    cursor: pointer;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  .footer__link--is-disabled,
  .form__button--is-invalid {
    //events
    pointer-events: none;
  }
}
