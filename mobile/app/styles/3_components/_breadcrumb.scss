.breadcrumbs {
  //layout
  margin-bottom: 5px;

  //typo
  text-align: center;
}

  .breadcrumbs--is-products {
    //layout
    margin: 0;
    padding: 0 13px;

    //typo
    text-align: left;
    line-height: 14px;
  }

  .breadcrumbs--is-assets {
    @extend .breadcrumbs--is-products;

    //layout
    margin-top: 10px;
  }

  .breadcrumbs__link {
    //layout
    display: inline-block;

    &,
    &:hover,
    &:focus {
      //typo
      color: #4a4a4a;
      font-size: 10px;
      text-decoration: none;

      //design
      outline: 0;
    }

    + .breadcrumbs__link {
      &:before {
        //layout
        content: '>';
        margin: 0 4px;
      }
    }
  }

  .breadcrumbs__crumb {
    // layout
    width: 30px;
    height: 2px;
    display: inline-block;
    position: relative;
    margin-right: 1px;

    // design
    background-color: #d8d8d8;
  }

    .breadcrumbs__crumb--is-active {
      //design
      background-color: #2167a3;

      &::after {
        //layout
        left: 35%;
        width: 0;
        height: 0;
        position: absolute;

        //design
        border-top: 6px solid #2167a3;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;

        //typo
        content: "";
      }
    }

    .breadcrumbs__crumb--is-completed {
      //design
      background-color: #4ec026;
    }

.device--is-desktop {
  .breadcrumbs__link {
    //events
    cursor: pointer;
  }
}
