@import '2_mixins/retina';
@import '2_mixins/misc';
@import '2_mixins/media-queries';

.feedback {
  //layout
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5000;
  display: block;
  position: fixed;
}

  .feedback__overlay {
    //layout
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    position: absolute;

    //design
    background-color: rgba(0,0,0,0.4);
  }

  .feedback__wrapper {
    //layout
    top: 50%;
    left: 50%;
    width: 80%;
    margin: 0 0 20px 0;
    z-index: 2;
    display: inline-block;
    padding: 10px 10px 10px 10px;
    position: absolute;
    max-width: 375px;

    //design
    background: #ffffff;
    border-radius: 4px;
    -webkit-border-radius: 4px;

    //animation
    transform: translateX(-50%) translateY(-50%);
  }

  .feedback__wrapper--has-close {
    //layout
    padding-top: 35px;
  }

  .feedback__wrapper--is-socialshop-preview {
    //layout
    padding: 30px 20px 15px;
  }

  .feedback__wrapper--is-preview {
    //layout
    padding: 0;
  }

  .feedback__wrapper--is-full-size-img {
    //layout
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 30px 10px 10px;
    max-width: calc(100% - 40px);
    max-height: calc(100% - 100px);

    .feedback__header {
      //layout
      height: 100%;

      .feedback__header--horizontal {
        //layout
        gap: 10px;
        display: flex;
        flex-direction: column;
      }
    }

    .feedback__header__message {
      //layout
      height: 100%;
    }

    .feedback__preview {
      //layout
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .feedback__preview__img {
      //layout
      max-width: 100%;
      max-height: 100%;
    }

    .feedback__button {
      //layout
      display: none;
    }
  }

    .feedback__close {
      @include retina('feedback/close');

      //layout
      top: 0;
      right: 0;
      width: 40px;
      height: 40px;
      position: absolute;
    }

      .feedback__close--is-icon-close {
        @include retina('feedback/close_img_madal');
        //layout
        left: 0;
        width: 30px;
        height: 30px;
      }

    .feedback__header {
      //layout
      padding: 0;

      //typo
      font-size: 14px;
      line-height: 22px;
    }

      .feedback__header__message {
        @include ellipsis;

        //layout
        width: 100%;
        white-space: normal;

        //typo
        text-align: center;
      }

    .feedback__title {
      //typo
      font-size: 14px;
      font-weight: bold;
    }

      .feedback__title--is-chat-transfer {
        //layout
        margin-top: 0;

        &,
        + .feedback__note {
          //typo
          line-height: 20px;
        }
      }

    .feedback__list {
      //layout
      margin: 20px 0;
      padding: 0;

      //typo
      list-style: none;
    }

      .feedback__list__item {
        //layout
        display: block;

        //typo
        text-align: left;

        + .feedback__list__item {
          //layout
          margin: 15px 0 0;
        }
      }

      .feedback__item__title {
        //layout
        margin: 0 0 2px;

        //typo
        font-size: 14px;
        font-weight: bold
      }

      .feedback__item__text {
        //layout
        margin: 0;

        //typo
        font-size: 14px;
        text-align: left;
      }

      .feedback__note {
        //layout
        margin: 0;

        //typo
        font-size: 13px;
        text-align: center;
      }

    .feedback__button {
      //layout
      clear: both;
      margin: 9px 0 0 0;
      height: 44px;
      display: block;
      overflow: hidden;
      vertical-align: bottom;

      //typo
      font-size: 16px;
      text-align: center;
      line-height: 44px;
      font-weight: 600;

      //design
      border-radius: 4px;
      background-color: #4ec026;

      //animation
      transition: background ease-in .3s;
      -webkit-transition: background ease-in .3s;

      //events
      -webkit-user-select: none;
      -webkit-touch-callout: none;

      &,
      &:hover,
      &:active {
        //typo
        color: #ffffff;
        text-decoration: none;
      }

      &:active {
        //typo
        background: #7999ad;
      }
    }

    .feedback__button--is-prompt,
    .feedback__button--is-cancel {
      //layout
      width: 47%;
      display: inline-block;
    }

    .feedback__button--has-icon {
      &:before {
        //layout
        top: 2px;
        width: 16px;
        height: 16px;
        content: '';
        display: inline-block;
        position: relative;
        margin-right: 7px;
      }
    }

    .feedback__button--has-icon-camera {
      &:before {
        @include retina('feedback/camera');
      }
    }

    .feedback__button--has-icon-image {
      &:before {
        @include retina('feedback/picture');
      }
    }

      .feedback__button--is-main-bigger {
        //layout
        width: 64%;
      }

    .feedback__button--is-cancel {
      //layout
      margin-right: 4%;

      //design
      background-color: #B6BABD;

      &.feedback__button--is-main-bigger {
        //layout
        width: 30%;
      }
    }

    .feedback__button--is-warning {
      //design
      background-color: #c02828;
    }

    .feedback__button--is-invalid {
      //design
      background-color: #b6babd;
    }

    .feedback__button--is-subscription-consent {
      //layout
      flex: 1;
      margin: 0;
      height: 44px;

      //design
      border: none;

      &.feedback__button--is-cancel {
        //layout
        flex: none;
        width: 40%;
        margin-right: 10px;
      }
    }


.feedback--is-options {
  .feedback__wrapper {
    //layout
    padding: 20px 10px;
    min-height: 0;
  }

  .feedback__button {
    //layout
    margin: 0;

    &:first-child {
      //layout
      margin-right: 4%;
    }
  }
}

  .feedback__preview {

  }

    .feedback__preview__title {
      //layout
      margin: 0 0 20px;

      //typo
      color: #4a4a4a;
      font-size: 12px;
      text-align: center;
      font-weight: 600;
      line-height: 34px;

      //design
      box-shadow: 0 1px 0 0 #f0f0f0;
    }

    .feedback__preview__img {
      //layout
      margin: 0 auto;
      display: inline-block;
      max-width: 170px;
      max-height: 400px;
    }

    .feedback__button--is-link {
      //layout
      margin: 0;
      height: 55px;

      //typo
      font-size: 14px;
      font-weight: 600;
      line-height: 55px;
      letter-spacing: -0.27px;

      &,
      &:hover,
      &:focus {
        //typo
        color: #4a90e2;

        //design
        background: none;
      }
    }

    .feedback__content__btn {
      // layout
      display: block;

      // design
      border: 1px solid #c7c7c7;
      background: transparent;
      border-radius: 2px;

      // typo
      color: #4a4a4a;
      line-height: 44px;

      + .feedback__content__btn {
        // layout
        margin: 9px 0 0 0;
      }
    }


    .feedback__marketing-title {
      //layout
      margin-bottom: 20px;
    }

    .feedback__marketing-body {
      //typo
      color: #4a90e2;
      font-size: 13px;
      font-weight: 600;
    }

    .feedback__marketing-footer {
      //layout
      margin-top: 20px;
    }

    .device--is-desktop,
    .device--is-tablet-md-landscape {
      @include respond-to($breakpoint-sm, min-width) {
        .feedback {
          @include set-margin-max-width(1440px, auto);
        }
      }
      .feedback__button,
      .feedback__close {
        &:hover,
        &:active {
          //events
          cursor: pointer;
        }
      }
    }
