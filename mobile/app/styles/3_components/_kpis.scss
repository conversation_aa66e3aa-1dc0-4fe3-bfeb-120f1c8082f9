@import '2_mixins/retina';

.kpis {
  //layout
  padding: 10px;
  margin-bottom: 0;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

.kpis--is-dashboard {
  //layout
  padding: 10px 0 0;

  //design
  background-color: #ffffff;
}

.kpis__title {
  //layout
  margin: 0 0 20px 0;

  //typo
  color: #747474;
  font-size: 10px;
  text-align: center;
  font-weight: normal;

  .kpis__title__filter {
    //typo
    color: #4a4a4a;
    font-weight: 600;

    //design
    text-shadow: 0 1px 0 #ffffff;
  }
}

.kpis__headers {
  //layout
  width: 100%;
  padding: 0 20px 5px;

  //typo
  line-height: 10px;
}

.kpis__headers__item {
  //layout
  width: 22%;
  display: inline-block;

  //typo
  color: #4a4a4a;
  font-size: 9px;
  text-align: center;
  font-weight: 300;

  &:first-child {
    //layout
    width: 39%;

    //typo
    text-align: left;
  }

  &:last-child {
    //layout
    width: 17%;

    //typo
    text-align: right;
  }
}

.kpis__container,
.kpis__element {
  //layout
  width: 100%;

  //design
  border: solid 1px #ffffff;
  box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5);
  border-radius: 2px;
  background-color: #ffffff;
}

.kpis__container {
  //layout
  margin-bottom: 10px;
}

.kpis__element {
  //layout
  margin-bottom: 10px;

  &:last-child {
    //layout
    margin-bottom: 0px;
  }
}

.kpis__element__line,
.kpis__container__line {
  //layout
  height: 50px;
  display: table;

  //typo
  color: #4a4a4a;
  font-size: 11px;

  //design
  border-bottom: 1px solid #eeeeee;

  &:last-child {
    //design
    border-bottom: none;
  }
}

.kpis__container__line {
  //layout
  width: calc(100% - 40px);
  margin: 0 20px;
}

.kpis__element__line {
  //layout
  width: calc(100% - 40px);
  height: auto;
  margin: 20px 20px;

  .kpis__line__title {
    //layout
    width: 76%;
  }

  .kpis__line__stat {
    //layout
    width: 24%;

    //typo
    text-align: right;
  }
}

.kpis__line__title,
.kpis__line__stat,
.kpis__line__rank,
.kpis__line__variance {
  //layout
  display: table-cell;
  vertical-align: middle;
}

.kpis__line__title {
  //layout
  width: 39%;
}

.kpis__line__stat {
  //layout
  width: 22%;

  //typo
  color: #000000;
  font-size: 12px;
  text-align: center;
  font-weight: 600;
}

.kpis__line__rank {
  //layout
  width: 22%;

  //typo
  text-align: center;
}

.kpis__line__rank,
.kpis__line__variance {
  //typo
  color: #4a4a4a;
  font-size: 12px;
  font-weight: 600;
}

.kpis__line__variance {
  //layout
  width: 17%;
  padding-right: 15px;

  //typo
  text-align: right;
}

.kpis__line__variance--is-up {
  @include retina('reporting/arrow-up-icon');

  //typo
  color: #16b64d;
}

.kpis__line__variance--is-down {
  @include retina('reporting/arrow-down-icon');

  //typo
  color: #d0021b;
}

.kpis__line__variance--is-up,
.kpis__line__variance--is-down {
  //layout
  padding-right: 10px;

  //typo
  font-size: 10px;

  //design
  background-position: right center;
}

.kpis__element__title {
  &,
  &:first-child {
    @extend .kpis__headers__item;

    //layout
    width: 100%;
    margin: 18px 0 0;
    padding: 0 20px;

    //typo
    font-size: 12px;
    font-weight: 600;
  }
}

.kpis-cards-wrapper {
  gap: 10px;
  margin: 0 10px;
  display: grid;
  padding-bottom: 10px;
  grid-template-columns: 100%;

  &--is-dashboard {
    padding-bottom: 10px;
  }

  &--is-kpis {
    position: relative;
  }
}

.kpis-card {
  border: solid 1px #fff;
  box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5);
  border-radius: 4px;
  background-color: #fff;

  &--is-dashboard {
    margin: 0 10px 10px 10px;
  }

  &--is-kpis-main {
    grid-column: 1;
  }

  &--is-kpis-extra {
    border: none;
    box-shadow: none;
    border-radius: 0;
    background-color: transparent;
  }

  &__content {
    padding: 20px;

    &--is-kpis-extra {
      padding: 0;
    }
  }

  &__title {
    color: #000;
    display: flex;
    font-size: 14px;
    align-items: center;
    font-weight: 600;
    justify-content: space-between;

    &--is-kpis-extra {
      display: none;
    }
  }

  &__rank-badge {
    color: #58666f;
    border: 1px solid #dce3e8;
    padding: 5px;
    font-size: 10px;
    border-radius: 4px;

    & > :first-child {
      color: #008deb;
    }
  }

  &__info-icon {
    @include retina('reporting/information-icon');
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__graph {
    display: flex;
    margin-top: 20px;
    justify-content: center;

    & gauge-graph {
      width: 80%;
    }
  }

  &__more-less {
    color: #0071bc;
    cursor: pointer;
    padding: 10px 0;
    font-size: 12px;
    border-top: 1px solid #efefef;
    text-align: center;
    font-weight: 600;
  }
}

area-graph,
gauge-graph {
  width: 100%;
  display: block;
  aspect-ratio: 16 / 9;
}

.kpis-main-kpis-wrapper {
  padding: 15px 0;
}

.kpis-main-item {
  display: flex;
  padding: 3px 0;
  font-size: 12px;
  justify-content: space-between;
  
  &--is-total {
    font-weight: 600;
  }

  &__value {
    color: #788893;
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
  }

  &__bar {
    display: flex;
    min-width: 150px;
    align-items: center;
    padding-left: 5px;
    & > div {
      width: 100%;
      border: 1px solid #0089e4;
      height: 4px;
      border-radius: 5px;
      background-color: #0089e4;
    }
  }
}

.kpis-extra-kpis-wrapper {
  overflow: hidden;
  transform-origin: top;

  &--is-collapsed {
    max-height: 0;
  }
}

.kpis-extra-card {
  gap: 10px;
  border: 1px solid #dce3e8;
  padding: 5px 10px;
  display: flex;
  font-size: 12px;
  margin-bottom: 5px;
  border-radius: 4px;
  background-color: #f8f8f8;

  &:last-of-type {
    margin: 0;
  }

  &__title {
    width: 35%;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    padding-top: 5px;
  }

  &__items {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
  }
}

.kpis-extra-item {
  display: flex;
  padding: 5px 0;
  border-bottom: 1px solid #dce3e8;
  justify-content: space-between;

  &:last-child {
    border-bottom: none;
  }

  &__value {
    font-weight: 600;
    flex-shrink: 0;
    margin-left: 10px;
  }
}

.kpis-switch {
  width: 100%;
  display: flex;
  padding: 10px 10px 0 10px;
  margin-bottom: 10px;

  &__btn {
    cursor: pointer;
    border: 1px solid #adbbc5;
    padding: 5px;
    display: flex;
    font-size: 12px;
    flex-grow: 1;
    justify-content: center;

    //events
    -webkit-user-select: none;
    -webkit-touch-callout: none;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }
    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &--is-active {
      color: #ffffff;
      cursor: default;
      border-color: #008deb;
      background-color: #008deb;
    }
  }
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-md, min-width) {
    .kpis-switch {
      padding: 0 10px;
    }

    .kpis-cards-wrapper {
      grid-template-columns: calc(50% - 5px) calc(50% - 5px);
    }

    .kpis-card--is-active {
      border-color: #008deb;
    }

    .kpis-card--is-kpis-extra {
      border: solid 1px #fff;
      box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5);
      border-radius: 4px;
      background-color: #fff;
    }

    .kpis-card__title--is-kpis-extra {
      display: block;
      margin-bottom: 15px;
    }
    
    .kpis-card__content--is-kpis-extra {
      padding: 20px;
    }

    .kpis-extra-kpis-wrapper {
      top: 0;
      right: 0;
      width: calc(50% - 5px);
      display: none;
      position: absolute;
      padding-bottom: 10px;
    }

    .kpis-extra-kpis-wrapper--is-collapsed {
      max-height: none;
    }

    .kpis-extra-kpis-wrapper--is-displayed {
      display: block;
    }

    .kpis-card__more-less {
      display: none;
    }
  }
}
