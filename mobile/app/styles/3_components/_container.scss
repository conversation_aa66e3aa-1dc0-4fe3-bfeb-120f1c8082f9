@import '2_mixins/media-queries';

.container {
  @include notch-safe-height(100%);

  //layout
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

.container--is-flex {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: calc(env(safe-area-inset-top));
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.container-inner {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.container-outer {
  @include notch-safe-height(100%);
  @include notch-safe-top(0px);

  //layout
  left: 0;
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
  position: absolute;
}

  .container--is-main {
    @include notch-safe-top(0px);

    //layout
    left: 0;
    overflow: hidden;
    position: absolute;
  }

  .container--is-login {
    //layout
    margin: 0 auto;
    padding: 20px;
    max-width: 640px;
  }

  .container--has-tabbar {
    //layout
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom); //place content in the safe area of the viewport for <PERSON><PERSON><PERSON>
  }

  .container--is-compact {
    left: 60px;
    width: calc(100% - 60px);
  }

  .container--is-fixed {
    left: 280px;
    width: calc(100% - 280px);
  }

  .sf-app {
    &__container {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1000;
      border: 0;
      width: 100vw;
      height: 100vh;
      max-width: 1440px;
      display: none;
    }
  }