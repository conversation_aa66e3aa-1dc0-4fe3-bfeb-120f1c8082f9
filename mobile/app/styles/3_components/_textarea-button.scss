@import '2_mixins/retina';
@import '2_mixins/media-queries';

.textarea-button {
  //layout
  left: 5px;
  bottom: 5px;
  height: 16px;
  position: absolute;
  padding-left: 2px;
  z-index: 2;

  //design
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.9);
}

.textarea-button__select {
  //layout
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  bottom: 0;
  position: absolute;

  //typo
  font-size: 0;

  //design
  border: none;
  outline: none;
  background: none;
  appearance: none;
  border-radius: 2px;
  -webkit-appearance: none;
}

.textarea-button__text {
  //layout
  display: inline-block;
  margin-right: 5px;
  vertical-align: top;

  //typo
  color: #2167a3;
  font-size: 10px;
  line-height: 16px;
}

.textarea-button__icon {
  //layout
  display: inline-block;
  vertical-align: top;
}

.textarea-button__icon--is-copy {
  @include retina('share/clipboard-copy-button');

  //layout
  width: 16px;
  height: 16px;
}

.textarea-button__icon--is-link {
  @include retina('share/insert-link-button');

  //layout
  width: 20px;
  height: 10px;
  margin-top: 4px;
}

.textarea-buttons__div {
  //layout
  bottom: 0px;
  height: 45px;
  z-index: 1;
  padding: 5px;
  display: flex;
  justify-content: space-between;
  width: 98%;
  margin-top: 0px auto;
  margin-left: 1%;
  margin-right: 1%;

  //design
  border-top: 1px solid #c7c7c7;
}

.textarea-buttons {
  //layout
  flex: 1;
  gap: 5px;
  padding: 5px;
  height: 30px;

  //typo
  text-align: center;

  &:hover, &:focus {
    //design
    text-decoration: none;
  }

  &.disabled {
    //design
    opacity: 25%;

    //events
    cursor: not-allowed;
  }
}

.textarea-buttons__separator {
  //layout
  width: 1px;
  content: "|";
  height: auto;
  display: inline-block;
  margin: 0px 10px 0px 10px;

  //design
  border-right: 1px solid #c7c7c7;
  opacity: 50%;
}

.textarea-buttons-link {
  //layout 
  position: relative;
  z-index: 1;
}

.textarea-buttons-link__select {
  //layout
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  bottom: 0;
  position: absolute;

  //typo
  font-size: 0;

  //design
  border: none;
  outline: none;
  background: none;
  appearance: none;
  border-radius: 2px;
  -webkit-appearance: none;
}
    .textarea-button__icon--is-link {
      @include retina('share/insert-link-button');

      //layout
      width: 20px;
      height: 10px;
      margin-top: 4px;
    }

  .device--is-desktop,
  .device--is-tablet-md-landscape {
    //CPD-1874 only for windows machine
    .textarea-button__select,
    .textarea-buttons-link__select {
      //layout
      padding: 0 5px;

      //events
      cursor: pointer;
    }

    .textarea-button__select * {
      //typo
      font-size: 12px;
    }

    //PS-6216 WYSIWYG
    .textarea-buttons-link__select * {
      //typo
      font-size: 12px;
    }
  }
