@import '2_mixins/retina';
@import '2_mixins/misc';
@import '2_mixins/standard-icon';
@import '2_mixins/media-queries';

.form__wrapper {
  //layout
  padding: 10px 20px;
}

  .form__wrapper--is-contacts {
    @extend .form__wrapper;

    &.scroller {
      //layout
      height: calc(100% - 80px);
    }
  }

  .form__wrapper--is-tags {
    //layout
    padding: 10px 10px 10px;
  }

  .form__wrapper--is-radio-button {
    //layout
    display: flex;
  }

  .form__wrapper--is-disabled {
    //design
    opacity: .5;

    //events
    pointer-events: none;
  }

.form__container {
  //layout
  padding-top: 10px;
}

.form__container--is-onboarding {
  @extend .form__container;

  //layout
  width: 100%;
  bottom: 0;
  padding: 0 15px;
  position: absolute;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  //design
  background-color: #f8f8f8;

  .form__label[class*="icon-"] {
    //layout
    position: relative;
    padding-left: 15px;

    &::before {
      //layout
      top: 50%;
      left: 0;
      position: absolute;

      //animation
      transform: translateY(-50%);
    }
  }
}

.form__container--is-activate-account {
  @extend .form__container--is-onboarding;

  .form__button {
    //layout
    margin-bottom: 20vh;
  }
}

  .form__container--is-compose {
    //layout
    padding: 10px 20px;
  }

  .form__container--is-padded {
    //layout
    padding: 10px 20px 90px;
  }

  .form__container--is-second {
    //layout
    padding-top: 30px;
  }

  .form__container--has-train {
    //layout
    padding-bottom: 0;
  }

  .form__container--is-rep-details {
    //layout
    height: -webkit-fill-available;
  }

  .form__container--is-task {
    .form__textarea--is-tasks,
    .calendar-picker {
      //layout
      margin-bottom: 10px;
    }

    .form__input--is-special,
    .selector--is-form {
      //layout
      margin-bottom: 0;
    }
  }

.form__section {
  //layout
  position: relative;
}

  .form__section--has-separator {
    //layout
    margin-bottom: 25px;
    padding-bottom: 30px;

    //design
    box-shadow: 0 1px 0 0 #ffffff;
    border-bottom: solid 1px #c3c3c3;

    &:last-of-type {
      //design
      border-bottom: none;
    }

    .form__input:last-of-type {
      //layout
      margin: 0;
    }
  }

  .form__section--is-bio {
    //layout
    margin: 40px 10px 25px;

    .form__label {
      //layout
      margin-bottom: 10px;

      //typo
      font-size: 14px;
      font-weight: 600;
      text-indent: 10px;
      line-height: 24px;
    }

    .form__textarea {
      //layout
      height: 158px;
      padding: 8px 9px;

      //typo
      font-size: 13px;
      line-height: normal;
    }
  }

  .form__section--is-copy-field {
    //layout
    margin-top: 20px;
    padding-top: 8px;

    //design
    border-top: 1px solid #f0f0f0;

    + .form__section--is-copy-field {
      //layout
      margin-top: 0;
    }

    &:last-of-type {
      //layout
      margin-bottom: 15px;

      //design
      border-bottom: 1px solid #f0f0f0;
    }

    .form__text {
      //layout
      width: calc(100% - 45px);
      display: inline-block;
      margin-bottom: 8px;

      //typo
      color: #4a4a4a;
      font-size: 13px;
      word-wrap: break-word;
    }

    .form__image-wrapper {
      //layout
      width: 45px;
      height: calc(100% - 30px);
      display: inline-block;
      position: absolute;

      //typo
      text-align: center;
    }

    .form__image {
      //layout
      right: 50%;
      bottom: 9px;
      position: absolute;

      //animation
      transform: translateX(+50%);
    }
  }

  .form__section--is-20 {
    //layout
    padding: 0 20px;
  }

  .form__section--is-10 {
    //layout
    padding: 0 10px;

    .selector {
      //layout
      width: 100%;
      margin: 0;
    }
  }

  .form__section--is-1020 {
    //layout
    padding: 10px 20px;
  }

  .form__section--is-2020 {
    //layout
    padding: 20px 20px;
  }

  .form__section--is-settings {
    //layout
    padding-bottom: 15px;
  }

  .form__section--is-reminder-datetime {
    //layout
    padding: 25px 0 10px;
  }

  .form__section--is-custom-date-range {
    //layout
    padding: 40px 0 0;

    + .form__section--is-custom-date-range {
      //layout
      padding-top: 5px;
    }
  }

  .form__section--is-onboarding,
  .form__section--is-remember-me {
    //layout
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;

    //typo
    line-height: 30px;

    .form__link,
    .form__label {
      //typo
      color: #999999;
    }

    .form__link {
      //layout
      margin-bottom: 0;

      &,
      &:hover,
      &:active,
      &:visited {
        //typo
        text-decoration: underline;
      }
    }

    .form__label {
      //layout
      margin: 0;
      position: relative;
      padding-right: 28px;
    }
  }

  .form__section--is-remember-me {
    .form__link,
    .form__label {
      //typo
      line-height: 14px;
    }

    .form__label {
      //typo
      text-align: right;
    }
  }

  .form__section--is-onboarding {
    //layout
    margin-bottom: 5px;
  }

  .form__section--is-edit-attributes {
    .form__label {
      //layout
      position: relative;
    }
  }

  .form__section--is-appointment-cancel {
    //layout
    padding-bottom: 10px;
  }

  .form__section--is-appointment-comment {
    //layout
    padding: 10px 0;
  }

  .form__section--is-appointment-settings {
    //layout
    padding-top: 20px;
  }

  .form__section--is-inline {
    //layout
    display: flex;
  }

  .form__section--is-consent {
    //layout
    margin-top: 10px;
  }

  .form__button,
  .form__btn-link-wrapper {
    + .form__section--is-onboarding {
      //layout
      margin-top: -15px;
    }
  }

  .form__section--is-center {
    //layout
    padding-top: 10px;

    //typo
    text-align: center;
  }

  .form__section__title {
    //layout
    margin: 0 0 10px;

    //typo
    color: #4a4a4a;
    font-size: 13px;
    font-weight: 600;
  }

    .form__section__title--has-icon {
      //layout
      height: 30px;
      margin-top: -5px;
      padding-left: 35px;
      margin-bottom: 5px;

      //typo
      line-height: 30px;
    }

    .form__section__title--is-find-product {
      @include standard-icon('icons/open-box-grey');

      //design
      background-position-x: 5px;
    }

    .form__section__title--is-find-customer {
      @include standard-icon('icons/person-grey');

      //design
      background-position-x: 5px;
    }

    .form__section__title--is-suspended-orders {
      @include standard-icon('icons/suspended-orders-grey');

      //design
      background-position-x: 5px;
    }

    .form__section__title--is-appointment-settings {
      //layout
      margin: 0 0 15px;

      //typo
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

  .form__section__title-info {
    //typo
    color: #4a4a4a;
    font-size: 13px;
    font-weight: normal;
  }

  .form__section__date {
    //layout
    top: 0;
    right: 0;
    position: absolute;

    //typo
    color: #4a4a4a;
    font-size: 13px;
  }

  .form__section__footer-link {
    //layout
    width: 120px;
    margin: 0 auto;

    //typo
    color: #2167a3;
    font-size: 12px;
    text-align: center;
    line-height: 44px;
    text-decoration: underline;
  }

  .form__section--is-appointment-hours-default {
    //layout
    margin-bottom: 30px;

    //typo
    font-size: 12px;
    line-height: 16px;

    .form__section__title {
      //layout
      margin: 0;

      //typo
      font-size: 12px;
      font-weight: 600;
    }
  }

  .form__section--is-appointment-hours {
    .checkbox-field__label {
      //layout
      margin-bottom: 15px;
    }

    .checkbox-field__text {
      //layout
      width: auto;
    }
  }

  .form__section--is-empty-state-text {
    //layout
    padding-bottom: 10px;

    //typo
    color: #8C8C8C;
    font-size: 12px
  }

  .form__section__override-list--has-separator {
    //layout
    padding: 15px 0;

    //typo
    font-size: 12px;
    line-height: 16px;

    //design
    border-bottom: solid 1px #c3c3c3;

    &:last-of-type {
      //design
      border-bottom: none;
    }

    .form__section__title {
      //layout
      margin-bottom: 0;

      //typo
      font-size: 12px;
    }
  }

  .form__section__override-list-item {
    //layout
    width: calc(100% - 30px);
  }

  .form__section__title--is-set-appointment-hours {
    //layout
    margin: 0 10px 15px;

    //typo
    font-size: 12px;
    font-weight: 600;
  }

  .form__section__comment {
    //typo
    color: #979797;
    font-size: 12px;
    line-height: 17px;
  }

  .form__section--has-bottom-padding-20 {
    //layout
    padding-bottom: 20px;
  }

.form__title {
  //layout
  margin: 0 0 5px;

  //typo
  font-size: 18px;
  font-weight: bold;
}

.form__title-info {
  //layout
  margin: 0 0 20px;

  //typo
  color: #4a4a4a;
  font-size: 12px;
}

.form__link {
  //layout
  display: block;

  //typo
  color: #8c8c8c;
  font-size: 12px;
  text-decoration: underline;

  &,
  &:hover,
  &:active {
    //typo
    color: #8c8c8c;
    text-decoration: none;
  }
}

  .form__link--is-login {
    //layout
    white-space: nowrap;
    margin-top: 30px;

    //typo
    line-height: 30px;
    text-decoration: underline;

    &:first-of-type {
      //layout
      margin-top: 0;
    }
  }

  .form__link--is-target-stores {
    //layout
    margin-top: 20px;
    padding-left: 8px;

    //typo
    color: #0070bb;
    text-decoration: underline;

    &,
    &:hover,
    &:active {
      //typo
      color: #0070bb;
      text-decoration: underline;;

      //event
      cursor: pointer;
    }
  }

.form__label {
  //layout
  margin: 0 0 5px 0;
  display: block;

  //typo
  color: #9b9b9b;
  font-size: 12px;
  font-weight: normal;

  &.form__label--is-error {
    //typo
    color: #d0021b !important;
  }

  &.form__label--is-valid {
    //typo
    color: #4ec026;
  }

  &.form__label--is-active {
    //typo
    color: #0070bb;
  }

  &.form__label--is-inline {
    //layout
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &.form__label--is-bold {
    //typo
    font-weight: 600;
  }

  &.form__label--is-reset-color {
    //typo
    color: unset;
  }
}

  .form__sub-label {
    @extend .form__label;

    //layout
    margin-top: -5px;
    margin-bottom: 3px;

    //typo
    font-size: 10px;
  }

  .form__label--small-indent {
    //layout
    padding: 0 10px;
  }

  .form__label--big-indent {
    //layout
    padding: 0 20px;
  }

  .form__label--big-indent-100 {
    //layout
    padding-left: 100px;
  }

  .form__label--big-indent-80 {
    //layout
    padding-left: 80px;
  }

  .form__label--has-background {
    //design
    background-color: #ffffff;
  }

  .form__label--is-center {
    //layout
    margin-top: 20px;
    margin-bottom: 10px;

    //typo
    text-align: center;
  }

  .form__label--is-left {
    //typo
    text-align: left;
  }

  .form__label--is-chat-transfer {
    //layout
    margin-bottom: 10px;

    //typo
    line-height: 20px;
  }

  .form__label--is-radio-button {
    //display
    display: block;
    .form__input--is-radio {
      //display
      display: none;

      &:checked + .form__radio-button::before {
        //design
        background-color: #0070bb;
      }
    }
  }

  .form__label--is-title {
    margin-bottom: 10px;
  }

  .form__label--has-no-margin {
    //layout
    margin: 0;
  }

  .form__label--is-small {
    //typo
    font-size: 10px;
  }

  .form__radio-button {
    &::before {
      //layout
      top: 25%;
      width: 12px;
      height: 12px;
      content: " ";
      display: block;
      overflow: hidden;
      position: absolute;

      //design
      opacity: 1;
      box-shadow: inset 0 0 0 2px #d8d8d8;
      background: none;
      border-radius: 100%;
    }
  }

    .form__radio-button--is-channel {
      //layout
      position: relative;
      padding-left: 25px;
      vertical-align: top;

      //typo
      font-size: 13px;
      font-weight: normal;
      line-height: 10px;
    }

    .form__radio-button--is-settings {
      @extend .form__radio-button--is-channel;
      //layout
      vertical-align: middle;

      &::before {
        //layout
        top: 0;
      }
    }

    .form__radio-button--is-settings-label {
      //layout
      padding-left: 12px;
    }

    .form__radio-button--is-disabled {
      &::before {
        //design
        opacity: 0.4;
      }
    }

  .form__label--is-facebook {
    @include retina('contacts/social-facebook');

    &.form__label--is-active {
      @include retina('contacts/social-facebook-active');
    }
  }

  .form__label--is-instagram{
    @include retina('contacts/social-instagram');

    &.form__label--is-active {
      @include retina('contacts/social-instagram-active');
    }

  }

  .form__label--is-twitter {
    @include retina('contacts/social-twitter');

    &.form__label--is-active {
      @include retina('contacts/social-twitter-active');
    }
  }

  .form__label--is-other-social {
    @include retina('contacts/social-other');

    &.form__label--is-active {
      @include retina('contacts/social-other-active');
    }
  }

  .form__label--is-facebook,
  .form__label--is-twitter,
  .form__label--is-instagram,
  .form__label--is-other-social {
    //layout
    padding-left: 20px;

    &,
    &.form__label--is-active {
      //design
      background-position: left center;
    }
  }

  .form__label--is-inline {
    //layout
    display: inline-block;
  }

  .form__label--is-right-aligned {
    //layout
    float: right;
  }

.form__label__link {
  //layout
  float: right;

  //typo
  color: #0070bb;
}

  .form__label__link--has-icon {
    @extend .form__label__link;
    @include retina('contacts/social-link-icon');

    //layout
    padding-right: 15px;

    //design
    background-position: right center;
  }

.form__label__spec {
  //typo
  font-size: 10px;

  //layout
  float: right;
}

.form__label__action {
  //layout
  top: 0;
  right: 0;
  position: absolute;

  //typo
  color: #8c8c8c;
  font-size: 12px;
  line-height: 20px;
}

.form__label__action--is-link {
  //typo
  color: #0070bb;
  text-decoration: underline;

  &:hover,
  &:active,
  &:visited {
    //typo
    color: #0070bb;
  }
}

.form__label__wrapper--is-radio {
  //layout
  margin-right: 30px;
}

.form__label--is-edit {
  //layout
  width: auto;
  display: inline-block;
  position: relative;
  padding-right: 13px;

  //typo
  color: #0071bc;
  text-decoration: underline;
}

.form__input-ctn {
  //layout
  display: flex;
  justify-content: space-between;
}

  .form__input-wrapper {
    //layout
    display: inline-block;
    vertical-align: top;
  }


  .form__input-wrapper--is-small {
    //layout
    width: 37.5%;
  }

  .form__input-wrapper--is-large {
    //layout
    width: 57.5%;
  }

  .form__input-wrapper--is-half {
    //layout
    width: calc(50% - 10px);
  }

.form__input-wrapper--is-filter-search {
  //layout
  padding: 0 10px;
}

.form__input,
.form__textarea,
.form__select {
  //layout
  float: none;
  width: 100%;
  margin: 0 0 15px;
  display: block;
  box-sizing: border-box;

  //typo
  color: #4a4a4a;
  font-size: 12px;
  line-height: 18px;
  font-family: inherit;

  //design
  border: solid 1px #979797;
  border-radius: 2px;
  background-color: #ffffff;
  -webkit-appearance: none;

  &::-webkit-input-placeholder {
    //typo
    color: #9b9b9b;
  }

  &:focus {
    //design
    border-color: #0071bc;
  }
}

.form__textarea--is-attribute,
.form__input--is-attribute,
.form__select--is-attribute {
  //layout
  margin: 0;
}

.form__input,
.form__select {
  //layout
  height: 44px;
  padding: 0 10px;
  flex-shrink: 0;

  //design
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.form__input--is-filter {
  //layout
  height: 36px;
}

.form__input--is-attribute-type-string {
  //layout
  padding-right: 50px;
}

.form__input--is-attribute-type-int {
  //layout
  padding-right: 38px;
}

.form__section--is-hidden,
.form__input--is-hidden {
  //layout
  display: none;
  visibility: hidden;

  //design
  opacity: 0;
}

.form__textarea,
.form__input {
  &.form__input--is-error,
  &.input-validation-error {
    //design
    border-color: #d0021b;

    &:focus {
      //design
      border-color: #d0021b;
    }
  }

  &.form__input--is-valid {
    //design
    border-color: #4ec026;

    &:focus {
      //design
      border-color: #4ec026;
    }
  }
}

.form__input--is-100-w {
  //layout
  width: 100px;
}

.form__input--is-80-w {
  //layout
  width: 80px;
}

.form__input--is-inline {
  //layout
  display: inline-block;
}

.form__input--has-text-to-right {
  //typo
  text-align: right;
}

.form__input--is-settings {
  //layout
  margin-bottom: 0;
}

.form__select--no-arrow {
  //design
  background-image: none !important;

  &.form__select--no-selection {
    //typo
    color: #8c8c8c;
  }
}

.form__input {
  &.ng-dirty.ng-invalid {
    @extend .form__input--is-invalid;
  }

  &[type='date'] {
    //layout
    position: relative;

    &::-webkit-calendar-picker-indicator {
      //layout
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      bottom: 0;
      padding: 0;
      position: absolute;

      //design
      opacity: 0;
    }

    &::-webkit-inner-spin-button,
    &::-webkit-clear-button {
      //layout
      display: none;
      -webkit-appearance: none;
    }

    &.form__input--no-selection::-webkit-datetime-edit {
      //layout
      z-index: -1;

      //design
      opacity: 0;
    }
  }
}

.form__select--is-invalid,
.form__input--is-invalid {
  //typo
  color: #d0021b;

  //design
  background: #ffffff;
  border-color: #d0021b;
}

.form__input--is-payment-process-email {
  //layout
  padding-right: 45px;
}

.form__input-button {
  //layout
  right: 0;
  width: 44px;
  height: 44px;
  bottom: 0;
  position: absolute;
}

.form__input-button--is-arrow {
  @include standard-icon('forms/send-arrow-blue');

  &.form__input-button--is-invalid {
    @include standard-icon('forms/send-arrow-grey');
  }
}

.form__input--is-lookbook-language {
  //layout
  height: 25px;
  padding: 0;
  margin-bottom: 5px;

  //design
  border: none;
  outline: none;
  box-shadow: none;
  background-color: transparent;
}

.form__input__button {
  //layout
  top: 22px;
  right: 0;
  width: 50px;
  height: 44px;
  display: block;
  padding: 0;
  position: absolute;

  //typo
  text-align: center;
  line-height: 44px;

  //design
  border: none;
  box-shadow: none;
  background: none;
}

.form__input__button--is-addressbook {
  @include retina('contacts/phonebook-icon');
}

.form__input__button--show-password {
  @include retina('forms/show-password-icon');
}

.form__input__button--hide-password {
  @include retina('forms/hide-password-icon');
}

.form__select {
  @include retina('forms/selector-form-arrow');

  //design
  background-position: center right 12px;

  &:focus {
    //design
    outline: none;
  }
}

  .form__select--is-empty {
    //typo
    color: #8c8c8c;
  }

  .form__select--is-transparent {
    //typo
    color: transparent;
  }

  .form__select--is-month {
    //layout
    width: 27%;
  }

  .form__select--is-day {
    //layout
    width: calc(31% - 40px);
    margin: 0 20px;
  }

  .form__select--is-year {
    //layout
    width: 42%;
    float: right;
    padding: 12px 5px;
  }

  .form__select--is-month,
  .form__select--is-day,
  .form__select--is-year {
    //layout
    display: inline-block;

    //typo
    text-align-last:center;

    //design
    background-image: none;
  }

  .form__select--is-time {
    //layout
    padding: 12px 20px;

    //design
    border-width: 1px 0 1px;
  }

  .form__select--is-set-appointment-time {
    //layout
    width: 105px;
    margin: 0 10px 5px 0;
    display: inline-block;

    //design
    border-width: 1px;
  }

  .form__select--is-appointment-slots-container {
    //layout
    width: 260px;
  }

  .form__section--is-time-selector {
    //layout
    padding: 0 25px 15px;
  }

  .form__input--is-button {
    //layout
    padding: 0;

    .form__input__placeholder {
      //layout
      padding: 0 10px;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
    }
  }

  .form__input--is-email {
    //layout
    width: 95%;
    display: inline-block;
  }

.form__input--is-special,
.form__textarea--is-special,
.form__textarea-wrapper {
  //layout
  position: relative;

  //design
  border: solid 1px #c7c7c7;
  box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5);

  &:focus {
    //design
    box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5), inset 0 1px 0 0 rgba(0, 0, 0, 0.1);
    border-color: #0071bc;

    //animation
    transition: border-color 100ms, box-shadow 100ms, background 200ms;
    -webkit-transition: border-color 100ms, box-shadow 100ms, background 200ms;
  }
}

.form__textarea-wrapper {
  //layout
  margin-bottom: 15px;

  //design
  background: #ffffff;

  .form__textarea {
    //layout
    height: 112px;
  }

  .sun-editor {
    //design
    border: 0 solid transparent;
  }

  .sun-editor .se-toolbar {
    //design
    outline: 0px solid #dadada;
    border-bottom: 1px solid #dadada;
  }

  //hide tooltips on mobile
  .se-tooltip-inner {
    //layout
    display: none !important;
  }

  //custom plugin button style
  .sun-editor button span.se-btn-name-placeholder {
    //layout
    width: 32px;
    padding-top: 5px;
    padding-right: 1px;
  }

  //use inset shadow to emulate a background color while preventing editor from copying that style
  .sun-editor mark {
    //design
    box-shadow: inset 0 0 0 100px #ffff00aa;
    background: transparent;
  }
}

//this class is added when the <textarea> inside this div is focused
.form__textarea-wrapper--is-focused {
  //design
  box-shadow: 0 1px 2px 0 rgba(199, 199, 199, 0.5), inset 0 1px 0 0 rgba(0, 0, 0, 0.1);
  border-color: #0071bc;

  //animation
  transition: border-color 100ms, box-shadow 100ms, background 200ms;
  -webkit-transition: border-color 100ms, box-shadow 100ms, background 200ms;
}

.highlight-textarea {
  //layout
  position: relative;
  
  .form__textarea {
    //layout
    position: relative;
    z-index: 2;
    width: 100%;
    height: 112px;
    padding: 8px;
    overflow: auto;
  
    //design
    background-color: transparent; 
    font-family: inherit;
    font-size: 12px;
    line-height: inherit;
    letter-spacing: inherit;
  }
  
  .form__textarea--no-margin-bottom {
    //layout
    margin-bottom: 0px;
  }
}

.highlight-textarea__backdrop {
  //layout
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 112px;
  overflow: auto;
  z-index: 1;

  //events
  pointer-events: none;
}

.highlight-textarea__backdrop__highlights {
  //layout
  padding: 8px;
  
  //typo
  color: transparent;
  white-space: pre-wrap;
  word-wrap: break-word;

  //design
  font-family: inherit;
  font-size: 12px;
  line-height: inherit;
  letter-spacing: inherit;
  background: none;
  border: none;
}

.highlight-textarea__backdrop__highlights mark {
  //layout
  padding: 0;
  margin: 0;

  //typo
  color: transparent;

  //design
  background-color: yellow;
  border-radius: 2px;
}

.form__input__placeholder {
  //typo
  color: #999999;
  font-size: 12px;
}

  .form__input__placeholder--is-appointment {
    //typo
    color: #4a4a4a;
  }

  .form__input--is-login {
    //typo
    font-size: 16px;
  }

  .form__input--is-mfa-login {
    //layout
    margin: 10px 0 20px;

    //typo
    text-align: center;
  }

.form__input__icon {
  @include retina('contacts/phonebook-icon');

  //layout
  top: 0;
  right: 0;
  width: 50px;
  height: 42px;
  position: absolute;

  //typo
  line-height: 42px;

  //design
  box-sizing: border-box;
  background-color: transparent;
  background-position: center right 14px;
}

  .form__input__icon--is-selected {
    @include retina('contacts/contact-selected-icon');

    //design
    background-position: center right 14px;
  }

.form__input__tag-list {
  //layout
  height: 42px;
  padding: 10px;
}

.form__input__tag-list--is-filter {
  //layout
  height: auto;
  overflow-y: scroll;
  max-height: 60px;
  min-height: 45px;
  padding-top: 0;
}

.form__input__tag-item {
  //layout
  height: 25px;
  margin: 0 5px 0 0;
  display: inline-block;
  position: relative;
  max-width: 100%;

  //typo
  color: #ffffff;
  line-height: 25px;

  //design
  border-radius: 2px;
  background-color: #0070BB;
}

.form__input__tag-item__value {
  //layout
  padding: 0 16px 0 5px;
  display: inline-block;
  overflow: hidden;
  max-width: 100%;
  white-space: nowrap;

  //typo
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  text-overflow: ellipsis;
}

.form__input__tag-item__btn {
  //layout
  top: 0;
  right: 0;
  width: 18px;
  display: inline-block;
  position: absolute;
  text-align: center;

  //typo
  font-size: 14px;
}

.form__pre-input {
  //layout
  margin: 1px 0 12px;

  //typo
  color: #4a4a4a;
  font-size: 12px;
  line-height: 15px;
}

.form__pre-input--is-phone,
.form__pre-input--is-email {
  @include ellipsis;

  //layout
  margin: 0 0 15px;
  padding: 10px 0 12px;
  max-width: 100%;

  //design
  border-bottom: 1px solid #dfdfdf;
}

.form__pre-input--has-email-icon {
  @include retina('contacts/compose-small-icon');

  //layout
  padding-right: 20px;

  //design
  background-position: top 11px right;
}

.form__tag {
  //layout
  top: 38px;
  right: 10px;
  width: auto;
  height: 19px;
  padding: 0 4px;
  position: absolute;

  //typo
  font-size: 10px;
  line-height: 19px;
  text-transform: uppercase;

  //design
  border-style: solid;
  border-width: 1px;
  border-radius: 2px;
  background-color: #ffffff;
}

.form__tag--is-delete {
  @include retina('contacts/delete-input-icon');

  //layout
  top: 32px;
  left: 1px;
  width: 45px;
  height: 30px;
  padding: 0;
  z-index: 1;


  //design
  border: none;
  border-right: 1px solid #ccc;
  border-radius: 0;

  + .form__input {
    //layout
    padding-left: 55px;
  }
}

.form__tag--is-add {
  //layout
  z-index: 1;

  //typo
  color: #0070bb;

  //design
  border-color: #0070bb;

  + .form__input {
    //layout
    padding-right: 48px;
  }
}

  .form__select--is-hidden {
    //layout
    top: 0;
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;

    //design
    border: none;
    opacity: 0;
  }

.form__icon {
  //layout
  top: auto;
  right: 0;
  width: 30px;
  height: 44px;
  bottom: 0;
  position: absolute;
}

.form__icon--is-clear {
  @include retina('contacts/clear-input-icon');
}

.form__icon--is-add {
  @include retina('contacts/add-input-icon');

  //layout
  width: 38px;
}

.form__icon--is-remove {
  @include retina('forms/remove-input-icon');

  //layout
  top: 5px;
}

.form__select--is-appointment-slots-container {
  .form__icon--is-remove {
    //layout
    top: 0;
  }
}

tags-input.form__input {
  //design
  border: none;

  .tags {
    //design
    border: solid 1px #c7c7c7;

    &.focused {
      //design
      box-shadow: inset 0 1px 0 0 rgba(0, 0, 0, 0.1);
      border-color: #0071bc;

      //animation
      transition: border-color 100ms, box-shadow 100ms, background 200ms;
      -webkit-transition: border-color 100ms, box-shadow 100ms, background 200ms;
    }
  }
}

.form__textarea {
  //layout
  height: 132px;
  resize: none;
  padding: 10px;
  margin-bottom: 20px;
  -webkit-appearance: none;
}

.form__textarea--is-attribute {
  //layout
  margin-bottom: 0;
}

.form__input--is-attribute-type-text {
  //layout
  padding-right: 35px;
}

.form__paragraph {
  //layout
  padding: 10px;
  margin-bottom: 20px;

  //design
  -webkit-appearance: none;
}

  .form__textarea--is-small {
    //layout
    height: 100px;
  }

  .form__textarea--is-note {
    //layout
    height: 160px;
    margin-top: 10px;
    margin-bottom: 5px;
  }

  .form__textarea--is-tasks {
    //layout
    height: 170px;
    margin-bottom: 10px;
  }

  .form__textarea--is-comment {
    //layout
    height: 80px;
    margin-bottom: 10px;
  }

  .form__textarea--is-events {
    //layout
    margin-bottom: 15px;
  }

  .form__textarea--is-hidden {
    //layout
    left: -9999px;
    position: absolute;
  }

  .form__input--is-new,
  .form__textarea--is-new {
    //layout
    padding-left: 10px;
    margin-bottom: 10px;
  }

  .form__textarea--has-count {
    //layout
    margin-bottom: 0;
  }

.form__textarea--is-wrapped {
  //layout
  border: none;
  box-shadow: none;
}

.form__count {
  //layout
  margin-bottom: 15px;

  //typo
  font-size: 12px;
}

  .form__count--is-exceeded {
    //typo
    color: #d0021b;
  }


input[disabled].form__input--is-disabled,
textarea[disabled].form__textarea--is-disabled {
  //typo
  -webkit-text-fill-color: #4a4a4a;

  //design
  opacity: 0.75;
  -webkit-opacity: 0.75;
  background-color: #ffffff;
}

.form__textarea--has-count { //lines 918-929 are copied from SF-23681-share-remix branch, commit 7b63a466e3a89e947e975ea0a68757eb839b0414 when conflict those lines are the same
  //layout
  margin-bottom: 0;
}

.form__count {
  //layout
  margin-bottom: 15px;

  //typo
  font-size: 12px;
}

  .form__count--is-contained {
    //layout
    top: 0;
    right: 0;
    margin: 0;
    position: absolute;

    //typo
    color: #9b9b9b;
    font-size: 12px;
    font-weight: normal;
  }

  .form__count--is-exceeded {//lines 943-946 are copied from SF-23681-share-remix branch, commit 7b63a466e3a89e947e975ea0a68757eb839b0414 lines 931-934
    //typo
    color: #d0021b;
  }

.form__input,
.form__select {
  &[disabled] {
    //events
    touch-action: none;
  }
}

.form__select-wrapper {
  //layout
  width: calc(50% - 10px);
  display: inline-block;

  + .form__select-wrapper {
    //layout
    float: right;
  }
}

.form__text {
  //layout
  margin-bottom: 10px;

  //typo
  color: #9b9b9b;
  font-size: 10px;
  text-align: left;
}

  .form__text-block {
    //layout
    margin: 5px 0 20px;

    //typo
    color: #4a4a4a;
    font-size: 12px;
    word-break: break-word;
    line-height: 19.4px;
  }

  .form__text--is-readonly {
    //typo
    color: #2b2b2b;
    font-size: 12px;
  }

.form__btn-wrapper {
  //layout
  padding: 0 10px;
}

.form__btn-wrapper--is-20 {
  //layout
  padding: 20px;
}

.form__btn-wrapper--is-sticky {
  //layout
  left: 0;
  width: 100%;
  bottom: 0px;
  padding: 20px;
  position: fixed;
  box-sizing: border-box;
  margin-bottom: 0;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);

  .form__button {
    //layout
    margin: 0;
  }
}

.form__button {
  //layout
  width: 100%;
  height: 44px;
  padding: 0;
  display: block;
  margin-top: 5px;
  margin-bottom: 15px;

  //typo
  color: #ffffff;
  font-size: 16px;
  text-align: center;
  font-weight: normal;
  line-height: 44px;

  //design
  border: none;
  border-radius: 2px;
  background-color: #0070bb;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.form__button--is-sticky-10 {
  //layout
  width: calc(100% - 20px);
  bottom: 10px;
  position: absolute;
}

.form__button--is-sticky-15 {
  //layout
  width: calc(100% - 30px);
  bottom: 15px;
  position: absolute;
}

.form__button--is-sticky-20 {
  //layout
  width: calc(100% - 40px);
  bottom: 20px;
  position: absolute;
}

.form__button--is-contained {
  @extend .form__button;

  //layout
  margin: 0;
}

.form__button--is-outline {
  //typo
  color: #0070bb;
  line-height: 42px;

  //design
  border: 1px solid #0070bb;
  background-color: transparent;
}

.form__button--is-advanced-search {
  //layout
  z-index: 50;
  position: relative;
  margin-bottom: 0;

  //typo
  font-size: 12px;
  line-height: 28px;
  font-weight: 600;

  //design
  height: 30px;
  border-color: #c7c7c7;
}

.form__button--is-green {
  //design
  background-color: #4ec026;
}

.form__button--is-multiple {
  //layout
  width: calc(50% - 5px);
  display: inline-block;

  &:first-child {
    //layout
    margin-right: 5px;
  }

  &:last-child {
    //layout
    margin-left: 5px;
  }
}

.form__button--is-delete {
  //typo
  color: #cccccc;
  font-size: 12px;
  font-weight: 600;

  //design
  border: solid 1px #cccccc;
  background-color: #f8f8f8;
}

button[disabled].form__button--is-invalid,
.form__button--is-invalid {
  //design
  opacity: 0.25;
  background-color: #0071bc;
}

.form__button--is-hidden {
  //layout
  display: none;
}

.form__button--is-sso-azure {
  @include standard-icon('icons/azure-ad-logo', 10px center);

  //design
  background-color: #00bdf3;
}

.form__button--is-sso-okta {
  @include standard-icon('icons/okta-logo', 10px center);

  //design
  background-color: #3E59E4;
  background-size: 30px;
}

.form__datepicker-wrapper {
  //layout
  position: relative;

  //design
  background-color: #ffffff;

  .form__input {
    //design
    background-color: transparent;
  }
}

  .form__item--is-44h {
    //layout
    min-height: 44px;

    //typo
    line-height: 44px;
  }

  .form__item--is-single-line {
    //layout
    display: flex;
    align-items: center;
    justify-content: space-between;

    .form__label {
      //layout
      margin: 0;
    }
  }

  .form__item__list-header,
  .form__item__list-item {
    //layout
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    //design
    box-shadow: inset 0 -1px 0 0 rgba(199, 199, 199, 0.5);
    background-color: #f8f8f8;
  }

  .form__item__list-header {
    //layout
    padding: 0 17px;

    .form__item__list-header-wrapper {
      //layout
      width: 90%;
      margin: auto;
      display: flex;
      justify-content: space-between;
    }
  }

  .form__item__list-item {
    //layout
    width: 90%;
    margin: auto;
    padding: 7px 15px;

    &:last-child {
      //design
      box-shadow: none;
    }
  }

  .form__item__list-header {
    //layout
    position: relative;
    //design
    box-shadow: 0 2px 2px 0 rgba(199, 199, 199, 0.5);
    background-color: #ffffff;

    .form__label {
      //layout
      margin: 0;
    }
  }

  .form__item__list-header--is-disabled {
    //design
    opacity: 0.5;
  }

  .form__item__list-item--is-active {
    //typo
    color: #2167a3;
  }

  .form__item__list-item--is-disabled {
    //design
    opacity: 0.5;
  }

.form__datepicker-text {
  //layout
  top: 10px;
  left: 10px;
  right: auto;
  bottom: 10px;
  position: absolute;

  //typo
  color: #8c8c8c;
  font-size: 12px;
  line-height: 24px;

  //design
  background-color: transparent;
}

.form__datepicker-text--is-appointment {
  //typo
  color: #4a4a4a;
}

.form__input--is-transparent {
  //typo
  color: transparent;
  font-size: 0;
  text-indent: -15px;
}

.form__checkbox-wrapper {
  //layout
  display: block;
}

.form__checkbox {
  //layout
  width: 24px;
  height: 24px;
  margin: 0px 10px 0 0px;
  display: block;
  overflow: hidden;

  //design
  border: 1px solid #979797;
  border-radius: 2px;
  background-color: #ffffff;
}

  .form__checkbox--is-product {
    //layout
    position: absolute;

    //design
    border-color: #e1e1e1;
  }

  .form__checkbox--is-login {
    //layout
    top: 50%;
    right: 0;
    width: 20px;
    height: 20px;
    margin: 0;
    display: inline-block;
    position: absolute;

    //animation
    transform: translateY(-50%);

    &.form__checkbox--is-checked {
      //design
      background-size: 12px 12px !important;
    }
  }

  .form__checkbox--is-checked {
    @include retina('forms/checkmark');

    //design
    border: 2px solid #0070bb;
    background-color: #0070bb;
  }

.form__image-ctn {
  //layout
  min-height: 60px;

  //design
  border: 1px solid #979797;
  border-radius: 2px;
  background-color: #ffffff;
}

.form__image-ctn__item {
  //layout
  margin: 30px auto;
  display: block;
  max-width: 200px;
}

.form__auto-complete,
.form__paragraph {
  //typo
  font-size: 12px;
}

  .form__auto-complete__item {
    //layout
    padding: 0 10px;

    //typo
    line-height: 44px;

    //design
    border-bottom: 1px solid #cccccc;
  }

    .form__auto-complete__label {
      @extend .form__auto-complete;

      //layout
      max-width: 42%;

      //typo
      font-weight: 700;
    }

      .form__auto-complete__label--is-empty {
        //typo
        color: #9b9b9b;
        font-weight: normal;
      }

    .form__auto-complete__value {
      @extend .form__auto-complete;

      //layout
      float: right;
      max-width: 56%;

      //typo
      text-align: right;
      font-weight: 400;
    }

    .form__auto-complete__label,
    .form__auto-complete__value {
      //layout
      display: inline-block;
      overflow: hidden;
      vertical-align: middle;

      //typo
      color: #4a4a4a;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

.form__field {
  //layout
  position: relative;
  margin-bottom: 20px;
}

.form__add-entry-button {
  //layout
  display: flex;
  justify-content: flex-end;

  .form__label__action {
    //layout
    position: initial;
  }

  .form__icon {
    //layout
    width: 20px;
    height: 20px;
    position: initial;
    margin-left: 10px;
  }
}

  .form__add-entry-button--is-link {
    //layout
    justify-content: flex-start;

    .form__icon {
      //layout
      display: none;
    }
  }

.attributes--has-separator {
  //layout
  padding: 24px 0;

  //design
  box-shadow: inset 0 -1px 0 0 rgba(199, 199, 199, 0.5);

  &:last-child {
    //design
    box-shadow: none;
  }
}

.pre-plaintext {
  //design
  border: 0px;
  background-color: transparent;
}

@media screen and (max-height: 548px) {
  .form__container--is-rep-details {
    .form__textarea {
      //layout
      height: 92px;
    }
  }
}

@media screen and (min-width: 375px) {
  .form__auto-complete__label {
    //layout
    max-width: 36%;
  }

  .form__auto-complete__value {
    //layout
    max-width: 64%;
  }

  .form__section--is-bio {
    //layout
    margin-top: 60px;
  }
}

@media screen and (device-aspect-ratio: 40/71) {
  .form__textarea--is-tasks {
    //layout
    height: 150px;
  }
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    .form__btn-wrapper--is-sticky {
      //layout
      position: absolute;
    }

    .form__button--is-outline-profilepic {
      //layout
      width: 30%;
    }

    .form__button--is-profilepic,
    .form__button--is-specialties {
      //layout
      width: 30%;

      //design
      border-radius: 4px;
      background: #008DEB;

      &:hover {
        //design
        background: #0766A4;
      }
    }

    .form__button--is-invalid {
      //typo
      color: #788893;

      //design
      background: #DCE3E8;
    }

    .form__button--is-lookbook.form__button--is-invalid {
      //typo
      color: #ffffff;
    }
  }

  .form__button--is-invalid {
    //events
    pointer-events: none;
  }

  .footer--is-mobile {
    .form__button {
      //design
      background: #008DEB;

      &:hover {
        //design
        background: #0766A4;
      }
    }
  }

  .form__button--is-outline-profilepic {
    //typo
    color: #008DEB;

    //design
    border: 1px solid #008DEB;
    background: transparent;
    border-radius: 4px;

    &:hover {
      //design
      background: #EAF7FF;
    }
  }

  .form__tag,
  .form__link,
  .form__icon,
  .form__label__link,
  .form__radio-button,
  .form__label__action,
  .form__checkbox--is-login {
    &:hover {
      //events
      cursor: pointer;
    }
  }

  .form__select,
  .form__image-wrapper,
  .form__select--is-hidden,
  .form__select--is-set-appointment-time {
    //events
    cursor: pointer;
  }

  .form__input--is-special {
    .form__input__icon {
      //events
      cursor: pointer;
    }
  }

  .form__input__icon--is-selected {
    //events
    pointer-events: none;
  }

  .form__select > option,
  .form__select--is-hidden > option
  .form__select--is-set-appointment-time > option {
    //events
    cursor: pointer;
  }
}
