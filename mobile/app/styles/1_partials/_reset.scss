@import '2_mixins/media-queries';

html,
body {
  //layout
  width: 100%;
  height: 100vh;
  margin: 0;
  overflow: auto;
  position: relative;

  //typo
  color: #4a4a4a;
  font-family: 'Open Sans', sans-serif !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  //design
  background: #f8f8f8;
}

h1, h2, h3, h4, h5, h6 {
  //typo
  font-family: 'Open Sans', sans-serif !important;
}

* {
  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

*:focus {
  //design
  outline: none;
}

ul {
  //layout
  margin: 0;
  padding: 0;

  //design
  list-style: none;
}

input, textarea {
  //event
  -webkit-user-select: auto;

  &::placeholder {
    //events
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }
}

#root,
.full-page {
  //layout
  height: 100%;
}

.device--is-desktop {
  /* Restore selection ability on desktop */
  * {
    //events
    -webkit-user-select: text;
    -webkit-touch-callout: default;
  }
}

.device--is-desktop,
.device--is-tablet-md-landscape {
  @include respond-to($breakpoint-sm, min-width) {
    //layout
    margin: 0 auto;
    max-width: 1440px;
    box-shadow: 0px 0px 5px 1px rgba(199, 199, 199, 0.5);

    #root {
      //layout
      height: 0;
    }
  }
}
