<cta-footer
  is-scrolling="!taskViewIsEmbedded && isScrollingBottom"
  show-shadow="!taskViewIsEmbedded && true"
  show-footer="isActive() && !$root.isLoading"
  footer-style="taskViewIsEmbedded && 'task-view-embedded'"
  first-button-style="'outline'"
  first-button-action="taskDismiss()"
  first-button-text="'Dismiss'"
  second-button-style="resolveBtnIsDisabled() && 'disabled'"
  second-button-action="resolveTask()"
  second-button-text="'Resolve'"
  is-contained="false",
  is-flex="true"
>
</cta-footer>