<div class="container container--is-flex">
  <div ng-if="!taskViewIsEmbedded" class="main-header main-header--is-flex">
    <a class="main-header__link--is-left-arrow" ng-click="clearValidateAndGoBack()">{{'Back' | i18next}}</a>
    <a class="main-header__link--is-right" ng-show="isActive() && !$root.isLoading && !task.readonly" ng-click="goToEditTask()"><span class="main-header__button">{{'Edit' | i18next}}</span></a>
    <h3 class="main-header__title">{{'Task' | i18next:i18nOpts}}</h3>
  </div>

  <div
    class="main-wrapper--is-task-view scroller"
  >
    <main-header-listing
      ng-if="taskViewIsEmbedded"
      button-right-class="main-header-listing__button--is-outline"
      title-text="{{'Task Details' | i18next:i18nOpts}}"
      button-right-label="Edit"
      show-button-left="false"
      button-right-visible="isActive() && !$root.isLoading && !task.readonly"
      on-button-right-click="goToEditTask()"
    >
    </main-header-listing>

    <action-buttons
      ng-if="taskIsActionable()"
      user-data="user"
      has-email="user.email || isCorporateTask()"
      has-phone="user.phone"
      has-text="user.phone || (isCorporateTask() && features.canSendSmsToMultiple())"
      has-task="false"
      task-id="taskId"
      has-share="userCanShareTask()"
      is-from-corp-task="isCorporateTask()"
      is-from-group-task="isGroupTask()"
      task-share-date-time="corporateTask.sharedAt"
      is-asset-available="isAssetAvailable()"
      i18n-opts="i18nOpts"
      alt-text-message-action="createDraftTextFromTask()"
    >
    </action-buttons>

    <div class="main-wrapper--is-1010" ng-class="{'main-wrapper--is-0010': isGroupTask() && (!user.ID || !user.id) }">
      <div
        class="contact-item"
        ng-click="(taskIsActionable()) && goToViewContact(user)"
        ng-if="user.ID"
        ng-class="{
          'contact-item--has-multiple-lines': (user.first_name || user.last_name) && (user.email || user.phone),
          'icon-arrow-list': taskIsActionable(),
        }"
      >
        <div
          class="contact-item__title contact-item__title--is-name"
          ng-if="!!user.first_name || !!user.last_name"
          ng-class="{
            'contact-item__title--is-readonly': !taskIsActionable()
          }"
        >
          {{getContactName(user)}}
        </div>
        <div class="contact-item__information" ng-if="user.email || user.phone" ng-class="{'contact-item__information--has-sibling': user.first_name || user.last_name}">
          <span ng-if="user.email" class="contact-item__information__element">{{user.email}}</span>
          <span ng-if="user.phone" class="contact-item__information__element">{{user.phone}}</span>
        </div>
      </div>
      <div>
        <view-mode-switcher
          ng-if="isGroupTask()"
          class="mode-switcher mode-switcher--is-flex"
          first-tab-label="{{'Task Details' | i18next}}"
          second-tab-label="{{'Activity Log' | i18next}}"
          is-first-tab-mode="!activityLogMode"
          is-second-tab-mode="activityLogMode"
          set-first-tab-mode="setActivityLogMode(false)"
          set-second-tab-mode="setActivityLogMode(true)"
        >
      </div>
      <div
        ng-if="isGroupTask() && activityLogMode"
        class="task-activity-log-container"
        ng-include="'views/tasks/activity-log.html'"
      >
      </div>
      <div
        class="task-view-container main-wrapper--is-1000"
        ng-if="!isGroupTask() || !activityLogMode"
      >
        <div class="contact-item__title contact-item__title--is-outside" ng-if="displayTaskTitle()">{{getTaskTitle()}}</div>

        <request-owner
          ng-if="task.showOwner"
          rep="{{task.repName}}"
          store="{{task.storeName}}"
        >
        </request-owner>

        <div class="contact-item contact-item--is-separate" ng-if="getBeautifulReminderDate()">
          <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Reminder Date and Time' | i18next:i18nOpts}}</div>
          <div class="contact-item__value">{{getBeautifulReminderDate()}}</div>
        </div>

        <div class="contact-item contact-item--is-separate" ng-if="getBeautifulAutoDismissDate()">
          <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Auto-Dismiss on' | i18next:i18nOpts}}</div>
          <div class="contact-item__value">{{getBeautifulAutoDismissDate()}}</div>
        </div>

        <div class="contact-item contact-item--is-separate" ng-if="task.details.length">
          <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Task Details' | i18next:i18nOpts}}</div>
          <div class="contact-item__value">{{task.details}}</div>
        </div>

        <div class="contact-item contact-item--is-separate">
          <div class="contact-item__multiple-ctn" ng-if="displayTaskSuggestedSubject()">
            <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Suggested Subject Line' | i18next:i18nOpts}}</div>
            <div class="contact-item__value">{{getTaskSuggestedSubject()}}</div>
          </div>

          <div class="contact-item__multiple-ctn" ng-if="displayTaskSuggestedBody()">
            <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Suggested Copy' | i18next:i18nOpts}}</div>
            <div class="contact-item__value" ng-bind-html="getTaskSuggestedBodyAsHtml()"></div>
          </div>
        </div>

        <div class="contact-item contact-item--is-separate" ng-if="task.notes.length">
          <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Task Notes' | i18next:i18nOpts}}</div>
          <div class="contact-item__value">{{task.notes}}</div>
        </div>

        <div class="contact-item contact-item--is-separate contact-item--is-title" ng-if="displayProducts()">
          <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Products' | i18next:i18nOpts}}</div>
        </div>
        <products-list
          type="'product'"
          can-edit="false"
          hide-handle="true"
          has-comment="false"
          hide-favorite="true"
          products-data="getProductsData()"
          prevent-select="true"
          hide-view-link="true"
        >
        </products-list>

        <div class="contact-item contact-item--is-separate contact-item--is-title" ng-if="displayAssets()">
          <div class="contact-item__title contact-item__title--is-generic contact-item__title--is-readonly">{{'Asset' | i18next:i18nOpts}}</div>
          <div class="contact-item__value" ng-if="getStartAndEndDate()">{{getStartAndEndDate()}}</div>
        </div>
        <products-list
          type="'asset'"
          can-edit="false"
          hide-handle="true"
          has-comment="false"
          hide-favorite="true"
          products-data="getAssetsData()"
          prevent-select="true"
          hide-view-link="true"
          hide-bottom-divider="true"
        >
        </products-list>
        <transaction-list transaction-data="transactionData" modifier-class="contact-item contact-item--is-separate"></transaction-list>
        <div ng-include="'views/tasks/notes.html'"></div>
      </div>
    </div>
  </div>
  <div ng-if="!task.readonly || isGroupTask()" ng-include="'views/tasks/task-footer.html'"></div>
</div>
