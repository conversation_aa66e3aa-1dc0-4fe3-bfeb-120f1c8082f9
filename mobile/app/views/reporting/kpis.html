<div class="kpis-switch" ng-if="canViewTabs">
  <div
    class="kpis-switch__btn"
    ng-class="{'kpis-switch__btn--is-active': kpiReportType == 'store'}"
    ng-click="changeReportType('store')"
  >
    {{ 'Store KPIs' | i18next:i18nOpts }}
  </div>
  <div
    class="kpis-switch__btn"
    ng-class="{'kpis-switch__btn--is-active': kpiReportType == 'rep'}"
    ng-click="changeReportType('rep')"
  >
    {{ 'Individual KPIs' | i18next:i18nOpts }}
  </div>
</div>

<div class="empty-state" ng-if="isKpisUnavailable">
  <div class="empty-state__text" ng-bind-html="emptyKpisMessage"></div>
</div>

<div ng-if="!isKpisUnavailable">
  <div class="kpis-cards-wrapper kpis-cards-wrapper--is-kpis">
    <div
      ng-if="kpi.enabled"
      class="kpis-card kpis-card--is-kpis-main"
      ng-repeat="kpi in kpisData.kpis track by $index"
      ng-click="onSwitchExtraKpis($index)"
      ng-class="{'kpis-card--is-active': activeExtraKpisSection === $index}"
    >
      <div class="kpis-card__content">
        <div class="kpis-card__title">{{ kpi.sectionTitle }}</div>
        <div class="kpis-main-kpis-wrapper">
          <div
            class="kpis-main-item"
            ng-class="{'kpis-main-item--is-total': item.total}"
            ng-if="item.enabled"
            id="{{item.id_label}}"
            ng-repeat="item in kpi.items | orderBy:'-value'"
            ng-init="hasTotal = kpi.items[0].total && kpi.items[0].stat > 0"
          >
            <div class="kpis-main-item__title">{{item.title}}</div>
            <div class="kpis-main-item__value">
              {{item.stat === '-' ? '\u2014' : item.stat}}
            </div>
            <div ng-if="hasTotal" class="kpis-main-item__bar">
              <div
                ng-style="{width: item.percentage + '%'}"
                ng-if="item.percentage !== 0"
              ></div>
            </div>
          </div>
        </div>
        <div
          ng-if="kpi.extras.length > 0"
          class="kpis-extra-kpis-wrapper"
          ng-class="{
            'kpis-extra-kpis-wrapper--is-collapsed': !expandedSections.includes($index),
            'kpis-extra-kpis-wrapper--is-displayed': activeExtraKpisSection === $index
          }"
        >
          <div class="kpis-card kpis-card--is-kpis-extra" ng-if="!isKpisUnavailable">
            <div class="kpis-card__content kpis-card__content--is-kpis-extra">
              <div class="kpis-card__title kpis-card__title--is-kpis-extra">{{ kpi.sectionTitle }}</div>
              <div
                class="kpis-extra-card"
                ng-if="extra.enabled"
                ng-repeat="extra in kpi.extras"
              >
                <div class="kpis-extra-card__title">
                  {{extra.sectionTitle || "&nbsp;"}}
                </div>
                <div class="kpis-extra-card__items">
                  <div
                    class="kpis-extra-item"
                    id="{{item.id_label}}"
                    ng-repeat="item in extra.items"
                    ng-if="item.enabled"
                  >
                    <div class="kpis-extra-item__title">{{item.title}}</div>
                    <div class="kpis-extra-item__value">{{item.stat}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div ng-if="kpi.extras.length > 0" class="kpis-card__more-less">
        <div ng-click="onShowMoreLess($event, $index)">
          {{ expandedSections.includes($index) ? ('Show Less KPIs' | i18next:i18nOpts)  : ('Show More KPIs' | i18next:i18nOpts) }}
        </div>
      </div>
    </div>
  </div>
</div>