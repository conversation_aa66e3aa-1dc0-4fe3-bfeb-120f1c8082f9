<div class="container">
  <div class="main-header" ng-hide="contacts">
    <a class="main-header__link--is-left" ng-if="!fromDashboard && !fromAction && !fromMainMenu" ng-click="cancelMessage()">{{'Cancel' | i18next}}</a>
    <a class="main-header__link--is-left-arrow" ng-if="fromDashboard || fromMainMenu" ng-click="cancelMessage()">{{'Home' | i18next}}</a>
    <a class="main-header__link--is-left-arrow" ng-if="fromAction" ng-click="cancelMessage()">{{'Back' | i18next}}</a>
    <h3 class="main-header__title" ng-if="mode === 'customer_request'">{{'Reply to request' | i18next:i18nOpts}}</h3>
    <h3 class="main-header__title" ng-if="mode !== 'customer_request'">{{'New Email' | i18next:i18nOpts}}</h3>

    <div class="main-header__link">
      <a class="main-header__link main-header__link--is-right" ng-if="isDraftSystemEnabled && hasContentFlag" ng-click="resetComposeEmailForm()"><span class="main-header__button">{{'Reset' | i18next:i18nOpts}}</span></a>
    </div>
  </div>
  <div class="main-wrapper main-wrapper--is-scrollable" ng-hide="contacts">
    <form class="form__container form__container--is-compose" name="composeForm" ng-class="{'form__container--has-train': data.messages.train.length > 0}">

      <message-language-selector
        ng-show="!isRequest()"
        loading="$root.isLoading"
        contacts="selectedContacts"
        template-lang="currentLangTemplate"
        on-language-change="clearContactsData()"
      >
      </message-language-selector>

      <div class="message-compose" ng-class="{'message-compose--with-train': data.messages.train.length > 0}">

        <div class="form__section">
          <label class="form__label">{{'To' | i18next:i18nOpts}}</label>
          <tags-input
              ng-model="selectedContacts"
              ng-attr-placeholder="{{placeholder}}"
              on-tag-adding="validateEmail($tag)"
              add-on-space="true"
              add-on-comma="true"
              add-on-paste="true"
              replace-spaces-with-dashes="false"
              placeholder=""
              class="form__input form__input--is-special"
              autocapitalize="none"
              autocorrect="off"
              spellcheck="false"
          >
            <auto-complete
              source="searchContacts($query)"
              template="views/directives/auto-complete-item.html"
              min-length="2"
              select-first-match="false"
              debounce-delay="200"
              class="form__auto-complete"
            >
            </auto-complete>
          </tags-input>
          <button type="button" class="form__input__button form__input__button--is-addressbook" ng-click="openAddressbook()"></button>
        </div>
        <label class="form__label">{{'Subject' | i18next}}</label>
        <input class="form__input form__input--is-special" type="text" placeholder="" ng-disabled="mode === 'customer_request'" ng-model="compose.title" ng-model-options="{ debounce: 300 }"/>

        <!-- WYSIWYG -->
        <div class="form__section" data-testid="message-wysiwyg" ng-if="isWysiwygEnabled()">
          <label class="form__label">{{'Message' | i18next}}</label>
          <div class="form__textarea-wrapper" ng-class="{'form__textarea-wrapper--is-focused': isFocused}">
            <sun-editor
              html-content="compose.message"
              bind-controls="bindWysiwygControls(controls)"
              placeholder="'Compose message' | i18next:i18nOpts"
              on-focus="focusTextarea(true)"
              on-blur="focusTextarea(false)"
              config="sunEditorConfig"
            ></sun-editor>
            <insert-link-button on-select="insertLink(message)" template-lang="currentLangTemplate"></insert-link-button>
            <ai-messaging-button ng-if="isAiMessagingEnabled" ng-click="onClickAiMessaging()"></ai-messaging-button>
          </div>
        </div>

        <!-- Plaintext -->
        <div class="form__section" data-testid="message-plaintext" ng-if="!isWysiwygEnabled()">
          <label class="form__label">{{'Message' | i18next}}</label>
          <div class="form__textarea-wrapper" ng-class="{'form__textarea-wrapper--is-focused': isFocused}">
            <highlight-textarea
              model="compose.message"
              class-modifier="wrapped"
              on-focus="focusTextarea(true)"
              on-blur="focusTextarea(false)"
              debounce="300"
              autocomplete="true"
              spellcheck="true"
            ></highlight-textarea>
          </div>
          <insert-link-button on-select="insertLink(message)" template-lang="currentLangTemplate"></insert-link-button>
          <ai-messaging-button ng-if="isAiMessagingEnabled" ng-click="onClickAiMessaging()"></ai-messaging-button>
        </div>

        <email-template-selector
          ng-if="isMultipleEmailTemplatesEnabled"
          template-email="templateEmail"
          on-change="handleEmailTemplateChange(template)"
          is-disabled="isGroupedProductsToggleEnabled"
        >
        </email-template-selector>


        <share-separator title="Add"></share-separator>

        <share-attachments
          photos="attachment.content"
          asset="compose.assets[0]"
          products="compose.products"
          uploading="uploading"
          is-photo-disabled="isPhotoDisabled()"
          is-asset-disabled="isAssetDisabled()"
          is-product-disabled="isProductDisabled()"
          is-grouped-products-toggle-enabled="isGroupedProductsToggleEnabled"
          is-grouped-products-feature-enabled="isGroupedProductsFeatureEnabled"
          attach-photo="attachPhoto(inputFiles)"
          attach-asset="attachAsset()"
          enable-all-items="true"
          attach-product="attachProduct()"
          remove-photo="removePhotoReadyForShare(index)"
          remove-asset="removeAssetReadyForShare(index)"
          remove-product="removeProductReadyForShare(index)"
          reorder-photos="reorderPhotos"
          reorder-products="reorderProducts"
          on-toggle-grouped-products="onToggleGroupedProducts(statusValue)"
          grouped-products-send-styled-link-label="groupedProductsSendStyledLinkLabel"
        >
        </share-attachments>
        <button type="button" ng-click="sendMessage()" class="form__button footer--is-mobile" ng-class="{'form__button--is-invalid': !validate()}" ng-disabled="$root.isLoading">{{shareBtnText}}</button>

      </div>
    </form>
    <div ng-include="'views/messages-store/messages-train.html'" ng-if="data.messages.train.length && !isForwardedMessage"></div>
    <div class="footer footer--is-desktop">
      <a class="footer__link">
        <span class="footer__button" ng-class="{'footer__link--is-disabled': !validate()}" ng-click="sendMessage()">
          {{shareBtnText}}
        </span>
      </a>
    </div>
  </div>
  <!-- compose -->

  <!-- addressbook -->
  <div ng-if="contacts" class="main-header">
    <a class="main-header__link--is-left" ng-click="cancelBook()">{{'Cancel' | i18next}}</a>
    <a class="main-header__link--is-right" ng-click="addContacts()" ><span class="main-header__button" ng-class="{'main-header__button--is-disabled': !isContactSelected()}">{{'Done' | i18next}}</span></a>
  </div>


  <div class="action-search__wrapper action-search__wrapper--no-toggle" ng-show="contacts" ng-class="{'action-search__wrapper--has-shadow': isScrolling}">
    <div ng-include="'views/contact-list/contacts-filter.html'"></div>
  </div>

  <div ng-show="contacts" class="main-wrapper--is-compose-contacts scroller" id="addressbookList">
    <div class="empty-state" ng-if="!contactData.length && !$root.isLoading">
      <div class="empty-state__wrapper">
        <div class="empty-state__icon empty-state__icon--is-contact" ng-if="!isSearch"></div>
        <div class="empty-state__text" ng-if="!isSearch">{{"You Don't Have Any Contacts" | i18next}}</div>
        <div class="empty-state__text" ng-if="isSearch">{{"There are no contacts matching your search criteria" | i18next}}</div>
      </div>
    </div>

    <div
      class="contacts-list"
      infinite-scroll="onScrollContactsList()"
      infinite-scroll-container="'#addressbookList'"
      infinite-scroll-distance="0.5"
      infinite-scroll-disabled="!pagination.next_page"
      infinite-scroll-immediate-check="false"
    >
      <div class="share-separator">
        <span class="share-separator__text share-separator__text--is-left">{{'Contacts' | i18next}}</span>
        <span class="share-separator__text share-separator__text--is-right" ng-if="canSendEmailToMultipleRecipients" ng-i18next="[html:i18next]({ns:'messages',numContactsSelected:potentialContacts.length + '\u002f20'})_numContactsSelected_ Selected"></span>
      </div>
      <div
        class="contacts-list__item"
        ng-repeat="contact in contactData"
        ng-click="togglePotentialContact(contact)"
        ng-class="indexOfPotentialContact(contact) < 0 ? '' : 'contacts-list__item--is-active'"
        ng-if="contact.email.length"
      >
        <div class="contacts-list__item__checkbox" ng-if="contact"></div>
        <span class="contacts-list__item__label">{{contact.text}}</span>
        <span class="contacts-list__item--is-favorite-icon" ng-show="contact.is_favorite_contact"></span>
      </div>
    </div>
  </div>

  <div ng-if="contacts" class="footer">
    <a href class="footer__link" ng-click="deselectAllContacts()"><span class="footer__button" ng-class="{'footer__link--is-disabled': !isContactSelected()}">{{'Select none' | i18next}}</span></a>
  </div>
  <!-- compose -->
</div>
