<div class="container" ng-controller="ContactsCreateView">
  <div class="main-header">
    <a class="main-header__link--is-left-arrow" ng-click="goBackToContact()"></a>
    <h3 class="main-header__title">{{isContactOrLinkedCustomer() ? 'Contact info' : 'Customer info'  | i18next:i18nOpts}}</h3>
    <toggle-button
      ng-if="!loaderService.isLoading() && isFromContact()"
      button-state="user.is_favorite_contact"
      disabled-state="isToggleFavoriteDisabled"
      classes-name="main-header__link main-header__link--is-right"
      disabled-class-name="main-header__link__star--is-disabled"
      active-class-name="main-header__link__star--is-selected"
      default-class-name="main-header__link__star--is-unselected"
      on-toggle="onToggleFavoriteContact(value)"
    >
    </toggle-button>
  </div>
  <div class="main-wrapper scroller">
    <action-buttons
      ng-if="displayActionButtons()"
      user-data="user"
      has-email="user.email"
      has-phone="user.phone"
      has-text="user.phone"
      has-task="true"
      can-add-to-contacts="false"
      task-id="taskId"
    >
    </action-buttons>
    <div id="contactsList" class="main-wrapper--is-1010" ng-show="!$root.isLoading">
      <div class="contact-item contact-item--is-separate">
        <div class="contact-item__title contact-item__title--is-contact">{{'Contact' | i18next}}</div>
        <div class="contact-item__title-link" ng-if="isFromContact()" ng-click="enterEditMode()">{{'Edit' | i18next}}</div>

        <div class="contact-item__container">
          <div class="contact-item__sub-title" ng-if="getCustomerName(user)">{{getCustomerName(user)}}</div>
          <div class="contact-item__element" ng-if="user.email.length">
          <div class="contact-item__inline-container">
            <div class="contact-item__element__label">{{(user.label_email || user.email_label || 'Email') | i18next:i18nOpts}} ({{'Default' | i18next}})</div>
            <div ng-if="user.contact_preference === Email" class="contact-item__preferred">{{'Preferred' | i18next}}</div>
          </div>
            <div class="contact-item__element__value">{{user.email}}</div>
            <div class="contact-item__element__label--is-marketing" ng-if="user.hasSubscriptionFlag">{{getMarketingText('Emails')}}</div>
            <div
              ng-if="user.hasSubscriptionFlag"
              class="contact-item__element__value--is-tag contact-item__element__value--is-{{getSubscriptionText(user.subscriptionStatus).classModifier}}"
            >
              {{getSubscriptionText(user.subscriptionStatus).tagText | i18next:i18nOpts}}
            </div>
          </div>

          <div class="contact-item__element contact-item__element--is-additional-email" ng-repeat="email in user.additionalEmails" ng-class="{'contact-item__element--is-first': $first}">
            <div class="contact-item__element__label">{{(email.label || 'Email') | i18next:i18nOpts}}</div>
            <div class="contact-item__element__value">{{email.value}}</div>
          </div>

          <div class="contact-item__element" ng-if="user.phone.length" ng-class="{'contact-item__element--has-separator': user.email.length}">
            <div class="contact-item__inline-container">
              <div class="contact-item__element__label">{{(user.label_phone || user.phone_label || 'Phone') | i18next:i18nOpts}} ({{'Default' | i18next}})</div>
              <div ng-if="user.contact_preference === TextMessage || user.contact_preference === Phone" class="contact-item__preferred">{{'Preferred' | i18next}}</div>
            </div>
            <div class="contact-item__element__value">{{user.phone}}</div>
            <div class="contact-item__element__label--is-marketing" ng-if="user.hasSMSSubscriptionFlag">{{getMarketingText('Text Messages')}}</div>
            <div
              ng-if="user.hasSMSSubscriptionFlag"
              class="contact-item__element__value--is-tag contact-item__element__value--is-{{getSubscriptionText(user.smsSubscriptionStatus).classModifier}}"
            >
              {{getSubscriptionText(user.smsSubscriptionStatus).tagText | i18next:i18nOpts}}
            </div>
          </div>

          <div class="contact-item__element" ng-repeat="phone in user.additionalPhones">
            <div class="contact-item__element__label">{{(phone.label || 'Phone') | i18next:i18nOpts}}</div>
            <div class="contact-item__element__value">{{phone.value}}</div>
          </div>

          <div class="contact-item__element" ng-if="features.hasMultilang && isFromContact() && user.locale">
            <div class="contact-item__element__label">{{'Preferred Language' | i18next}}</div>
            <div class="contact-item__element__value" id="AtContactPreferredLanguage">{{user.locale | i18next:i18nOpts}}</div>
          </div>
        </div>
      </div>

      <div
        class="contact-item contact-item--is-tags icon-arrow-list"
        ng-click="goToTags()"
        ng-if="user && features.retailerHasCustomerTags"
        ng-class="{
          'contact-item--has-tags': user.customer_tags.length,
        }"
      >
        <div class="contact-item__title contact-item__title--is-tags">{{'Tags' | i18next:i18nOpts}}</div>
        <contact-tags-list tags-data="user.customer_tags" />
      </div>

      <div class="contact-item contact-item--is-separate">
        <div class="contact-item__title contact-item__title--is-addresses">{{'Addresses' | i18next}}</div>
        <div class="contact-item__title-link" ng-if="isFromContact()" ng-click="addAddress()">{{'Add' | i18next}}</div>

        <div class="contact-item__container" ng-if="user.addresses.length">
          <div class="contact-item__element" ng-repeat="address in user.addresses" ng-class="{'contact-item__element--has-separator': !$first}">
            <div class="contact-item__element__label">{{(address.label || 'Address') | i18next:i18nOpts}} <span ng-if="address.is_default === '1'">({{'Default' | i18next}})</span>
              <div class="contact-item__element__label-link" ng-if="isFromContact() && !isPiiObfuscationEnabled" ng-click="editAddress(address)">{{'Edit' | i18next}}</div>
            </div>
            <div>
              <contact-address address-data="address" />
            </div>
          </div>
        </div>
      </div>

      <div class="contact-item contact-item--is-separate">
        <div class="contact-item__title contact-item__title--is-socials">{{'Social Media' | i18next:i18nOpts}}</div>
        <div class="contact-item__title-link" ng-if="isFromContact()" ng-click="editSocial()">{{(user.socials.length ? 'Edit' : 'Add') | i18next}}</div>

        <div class="contact-item__container" ng-if="user.socials.length">
          <div class="contact-item__element contact-item__element--is-{{social.network.name}}" ng-repeat="social in user.socials">
            <div class="contact-item__element__value--is-link" ng-click="openSocial(social.network.url, social.username)"><span ng-if="social.network.name !== 'other'">{{social.network.url}}</span>{{social.username}}</div>
          </div>
        </div>
      </div>

      <div class="contact-item contact-item--is-separate">
        <div class="contact-item__title contact-item__title--is-events">{{'Event' | i18next}}</div>
        <div class="contact-item__title-link" ng-if="isFromContact()" ng-click="editEvent()">{{user.events.length ? 'Edit' : 'Add' | i18next}}</div>

        <div class="contact-item__container" ng-if="user.events.length">
          <div class="contact-item__element contact-item__element--is-events" ng-repeat="event in user.events">
            <span class="contact-item__element__label">{{getEventLabel(event)}}</span>
            <span class="contact-item__element__value">{{getEventDate(event)}}</span>
          </div>
        </div>
      </div>

      <div
        class="contact-item contact-item--is-separate"
        ng-repeat="panel in user.attributePanels"
      >
        <contact-attribute-panel
          title="{{panel.label}}"
          attributes="panel.attributes"
          action-label="{{isFromContact() ? (isPanelEmpty(panel.attributes) ? 'Add' : 'Edit') : '' | i18next}}"
          on-click="editAttributes(panel)"
        >
        </contact-attribute-panel>
      </div>
    </div>
  </div>
</div>
