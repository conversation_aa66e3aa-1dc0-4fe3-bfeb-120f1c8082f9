<action-buttons
  ng-if="displayActionButtons()"
  user-data="user"
  has-email="user.email && isFromContact()"
  has-phone="displayPhoneOrText(user)"
  has-text="displayPhoneOrText(user)"
  has-task="isFromContact()"
  can-add-to-contacts="canAddCustomerToContacts()"
  add-to-my-contacts-action="addToMyContacts()"
  task-id="taskId"
>
</action-buttons>

<div class="empty-state" ng-if="isLimitedVisibility">
  <div class="empty-state__wrapper empty-state__wrapper--is-contacts">
    <div class="empty-state__icon empty-state__icon--is-profile"></div>
    <div class="empty-state__text">{{'Restricted Profile' | i18next:i18nOpts}}</div>
  </div>
</div>

<div class="main-wrapper--is-1010">
  <div class="contact-item contact-item--is-separate contact-item--is-contact-info" ng-click="goToContactEdit(user)">
    <div class="contact-item__title" ng-class="{'icon-arrow-list': !isLimitedVisibility}">{{isContactOrLinkedCustomer() ? 'Contact info' : 'Customer info'  | i18next:i18nOpts}}</div>
    <div class="contact-item__value contact-item__value--is-empty" ng-if="!hasContactInfo()">{{'None defined' | i18next:i18nOpts}}</div>
    <div ng-if="hasContactInfo()" class="contact-item__details">
      <div ng-if="user.email" class="contact-item__element">
        <div class="contact-item__element__label">{{'Email' | i18next:i18nOpts}}</div>
        <div class="contact-item__element__value-container">
          <div class="contact-item__element__value">{{user.email}}</div>
          <div
            ng-if="user.hasSubscriptionFlag && !isLimitedVisibility"
            class="contact-item__element__value--is-tag contact-item__element__value--is-{{getSubscriptionText(user.subscriptionStatus).classModifier}}"
          >
            {{getSubscriptionText(user.subscriptionStatus).tagText | i18next:i18nOpts}}
          </div>
        </div>
      </div>
      <div ng-if="user.phone" class="contact-item__element">
        <div class="contact-item__element__label">{{'Phone' | i18next:i18nOpts}}</div>
        <div class="contact-item__element__value-container">
          <div class="contact-item__element__value">{{user.phone}}</div>
          <div
            ng-if="user.hasSMSSubscriptionFlag && !isLimitedVisibility"
            class="contact-item__element__value--is-tag contact-item__element__value--is-{{getSubscriptionText(user.smsSubscriptionStatus).classModifier}}"
          >
            {{getSubscriptionText(user.smsSubscriptionStatus).tagText | i18next:i18nOpts}}
          </div>
        </div>
      </div>
      <div ng-if="getDefaultAddress()" class="contact-item__element">
        <div class="contact-item__element__label">{{'Address' | i18next}}</div>
        <div class="contact-item__element__value">
          <contact-address address-data="getDefaultAddress()" />
        </div>
      </div>
      <div
        class="contact-item__element"
        ng-if="user.customer_tags.length && retailerHasCustomerTags"
      >
        <div class="contact-item__element__label">{{'Tags' | i18next:i18nOpts}}</div>
        <div class="contact-item__element__value contact-item__element__value--is-contact-tags">
          <contact-tags-list tags-data="user.customer_tags" />
        </div>
      </div>
    </div>
  </div>

  <contact-stats-panels
   ng-if="features.hasClienteling() && !isLimitedVisibility"
   panels="contactStats.panels"
   user="user"
  >
  </contact-stats-panels>

  <contact-associate-relationships
    ng-if="displayAssociateRelationships()"
    retailer-customer-id="retailerCustomerId"
  >
  </contact-associate-relationships>

  <contact-section-link
    ng-repeat="section in contactSectionLinks | filter : displaySection"
    label="section.label"
    count="section.getCount()"
    on-click="section.onClick()"
    is-first="$first"
    is-last="$last"
    loading="$root.isLoading"
    ng-if="!isLimitedVisibility"
  >
  </contact-section-link>
</div>
