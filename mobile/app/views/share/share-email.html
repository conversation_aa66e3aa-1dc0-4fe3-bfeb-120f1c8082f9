<div ng-controller="ShareEmail">
  <div class="tag-filter">
    <div class="tag-filter__container">
      <span class="tag-filter__container__text tag-filter__container__text--is-alert" ng-if="!emailRecip">
        {{'No Email Recipients' | i18next:i18nOpts}}
      </span>
      <span class="tag-filter__container__text" ng-if="emailRecip">
        {{'You have' | i18next:i18nOpts}} <span class="tag-filter__container__text--is-important">{{emailRecip}}</span> {{emailRecipients}}
      </span>
      <filter-button ng-if="true" is-selected="filterSelected" on-click="toggleSubscribedContactsFacetFilter()"></filter-button>
    </div>
  </div>

  <div class="form__section">
    <input class="form__input form__input--is-special form__input--is-new" ng-model="share.subject" name="email-subject" placeholder="{{'Subject' | i18next:i18nOpts}}" autocomplete="true" spellcheck="true"></input>
  </div>

  <!-- WYSIWYG -->
  <div class="form__section" data-testid="message-wysiwyg" ng-if="isWysiwygEnabled()">
    <div class="form__textarea-wrapper" ng-class="{'form__textarea-wrapper--is-focused': isFocused}">
      <sun-editor
        html-content="share.message"
        bind-controls="bindWysiwygControls(controls)"
        placeholder="'Compose message' | i18next:i18nOpts"
        on-focus="focusTextarea(true)"
        on-blur="focusTextarea(false)"
        config="sunEditorConfig"
      >
      </sun-editor>
      <insert-link-button on-select="insertLink(message)" template-lang="currentLangTemplate"></insert-link-button>
      <ai-messaging-button ng-if="isAiMessagingEnabled" ng-click="onClickAiMessaging()"></ai-messaging-button>
    </div>
  </div>

  <!-- Plaintext -->
  <div class="form__section" data-testid="message-plaintext" ng-if="!isWysiwygEnabled()">
    <div class="form__textarea-wrapper" ng-class="{'form__textarea-wrapper--is-focused': isFocused}">
      <highlight-textarea
        model="share.message"
        class-modifier="wrapped"
        on-focus="focusTextarea(true)"
        on-blur="focusTextarea(false)"
        debounce="300"
        textarea-name="email-message"
        textarea-placeholder="{{'Compose message' | i18next:i18nOpts}}"
        autocomplete="true"
        spellcheck="true"
      ></highlight-textarea>
     </div>
     <insert-link-button
       on-select="insertLink(message)"
       template-lang="currentLangTemplate">
     </insert-link-button>
     <ai-messaging-button ng-if="isAiMessagingEnabled" ng-click="onClickAiMessaging()"></ai-messaging-button>
  </div>

  <email-template-selector
    ng-if="isMultipleEmailTemplatesEnabled"
    template-email="templateEmail"
    on-change="handleEmailTemplateChange(template)"
    is-disabled="isGroupedProductsToggleEnabledFlag">
  </email-template-selector>

  <share-separator title="Add"></share-separator>

  <share-attachments
    photos="share.photos"
    asset="share.asset"
    products="share.products"
    uploading="uploading"
    is-photo-disabled="isPhotoDisabled()"
    is-asset-disabled="isAssetDisabled()"
    is-product-disabled="isProductDisabled()"
    is-grouped-products-toggle-enabled="isGroupedProductsToggleEnabledFlag"
    is-grouped-products-feature-enabled="isGroupedProductsFeatureEnabled"
    enable-all-items="true"
    attach-photo="attachPhoto(inputFiles)"
    attach-asset="attachAsset()"
    attach-product="attachProduct()"
    remove-photo="removePhotoReadyForShare(index)"
    remove-asset="removeAssetReadyForShare(index)"
    remove-product="removeProductReadyForShare(index)"
    reorder-photos="reorderPhotos"
    reorder-products="reorderProducts"
    on-toggle-grouped-products="onToggleGroupedProducts(statusValue)"
    grouped-products-send-styled-link-label="groupedProductsSendStyledLinkLabel"
  >
  </share-attachments>

  <button type="button" ng-click="sendMessageThrottled()" class="form__button form__button--is-contained footer--is-mobile" ng-class="{'form__button--is-invalid': !validate()}">{{shareBtnText}}</button>
  <div class="footer footer--is-desktop">
    <a class="footer__link">
      <span class="footer__button" ng-class="{'footer__link--is-disabled': !validate()}" ng-click="sendMessageThrottled()">
        {{shareBtnText}}
      </span>
    </a>
  </div>
</div>
