<div class="container">
  <div class="main-header">
    <a class="main-header__link main-header__link--is-left-arrow" ng-click="cancelChanges()">{{'Back' | i18next}}</a>
    <a class="main-header__link--is-right" ng-click="products.length > 0 && attachSelectedProducts()"><span class="main-header__button" ng-class="{'main-header__button--is-disabled': products.length < 1}">{{'Attach' | i18next}}</span></a>
    <h3 class="main-header__title">{{getShareProductsTitle()}}</h3>
  </div>

  <div class="action-search__wrapper" ng-hide="hideFilterSearch() || ($root.isLoading && !isFromBrowse) || isFavorite() || detailsPageVisible" ng-class="{'action-search__wrapper--has-shadow': isScrolling}">
  <div ng-include="'views/share/partial-search-bar.html'"></div>
</div>

  <div class="empty-state empty-state--with-header" ng-if="!result.length && !$root.isLoading">
    <div class="empty-state__wrapper">
      <div class="empty-state__text" ng-if="!isFavorite()" ng-i18next="[html:i18next](i18nOpts)There are no products matching your search criteria."></div>
      <div class="empty-state__text" ng-if="isFavorite()">{{'You have no favorites' | i18next:i18nOpts}}</div>
    </div>
  </div>

  <div
    id="shareProductListing"
    ng-init="onInitShareProductListingElem()"
    class="main-wrapper"
    ng-class="{
      'main-wrapper--has-products-drawer': products.length,
      'main-wrapper--has-minimized-products-drawer': products.length && productsDrawerIsMinimized,
      'main-wrapper--is-share-products': !$root.isLoading && !isFavorite(),
      'main-wrapper--has-no-search-form': hideFilterSearch(),
      'scroller': isScrollable && !detailsPageVisible
    }"
  >
    <div class="breadcrumbs breadcrumbs--is-products" ng-if="!isFavorite() && !$root.isLoading">
      <a
        class="breadcrumbs__link"
        ng-click="!isDepartmentAll() ? goToPreviousBreadCramsCategory('all') : goBack()"
      >
        {{'Categories' | i18next:i18nOpts}}
      </a>
      <a
        class="breadcrumbs__link"
        ng-if="department_id"
        ng-click="!isDepartmentAll() && goToPreviousBreadCramsCategory('department')"
      >
        {{breadcrumbs.department.name | i18next:i18nOpts}}
      </a>
      <a
        class="breadcrumbs__link"
        ng-if="breadcrumbs.category.display"
        ng-click="(subCategoryId || hasChildren(category.children)) && goToPreviousBreadCramsCategory('category')"
      >
        {{breadcrumbs.category.name | i18next:i18nOpts}}
      </a>
      <a
        class="breadcrumbs__link"
        ng-if="breadcrumbs.subCategory.display"
        ng-click="(subCategoryChildId || hasChildren(subCategory.children)) && goToPreviousBreadCramsCategory('subCategory')"
      >
        {{breadcrumbs.subCategory.name | i18next:i18nOpts}}
      </a>
      <a class="breadcrumbs__link" ng-if="breadcrumbs.subCategoryChild.display">{{breadcrumbs.subCategoryChild.name | i18next:i18nOpts}}</a>
    </div>

    <div
      class="main-wrapper--is-products-list"
      infinite-scroll="infiniteScrollDown()"
      infinite-scroll-container="infiniteScrollContainer"
      infinite-scroll-distance="1.5"
      infinite-scroll-disabled="!hasMorePages"
    >
      <products-list
        type="'product'"
        loading = "$root.isLoading"
        products-data="result"
        selected-products="products"
        toggle-scrollable="toggleScrollable()"
        force-details-button="true"
        on-product-details-click="onProductDetailsClick(product)"
      >
      </products-list>
    </div>
  </div>

  <products-drawer
    ng-show="!detailsPageVisible"
    type="'product'"
    propose-options="isFromBrowse"
    selected-products="products"
  >
  </products-drawer>
  <div class="main-wrapper--is-filter" ng-show="facetPageVisible" ng-include="'views/share/partial-product-facets.html'"></div>
  <div class="main-wrapper--is-details" ng-if="detailsPageVisible" ng-include="'views/share/product-details-view.html'" ng-controller="ProductDetails"></div>
  <bottom-drawer is-open="isProducSortVisible" on-backdrop-click="toggleProductSort()">
    <product-sort-options value="selectedProductSortOption" on-change="onProductSortChange(value)"></product-sort-options>
  </bottom-drawer>
</div>
