<div
  class="sortable-list products-list products-list--is-{{getModifierClass()}}"
>
  <div
    class="products-list__element"
    ng-class="{
      'products-list__element--is-selected': isProductSelected(product),
      'products-list__element--is-removed': isProductRemoved(product),
      'products-list__element--is-unavailable': isProductUnavailable(product),
      'products-list__element--is-disabled': isAssetDisabled(product)
    }"
    ng-repeat="product in getProducts(productsData) | filter: getProductsFilter track by (allowDuplicates ? $index : getProductSku(product))"
  >
    <div class="products-list__inner-wrapper">
      <div
        class="products-list__button"
        ng-class="{
          'products-list__button--is-favorite': !product.favorite,
          'products-list__button--is-favorite-selected': product.favorite,
        }"
        ng-if="!canEdit && isProduct() && !hideFavorite"
        ng-click="toggleFavorite(product)"
      ></div>
      <div
        class="products-list__button products-list__button--is-handle handle"
        ng-if="canEdit && !hideHandle"
      ></div>
      <div
        class="products-list__button products-list__button--is-remove"
        ng-click="removeProduct({product: product})"
        ng-if="canEdit"
      ></div>
      <div
        class="products-list__button products-list__button--is-variant"
        ng-if="hasMoreThanOneVariant(product) && !canEdit"
      ></div>
      <div
        class="products-list__click-wrapper"
        ng-click="handleProductClick(product)"
      >
        <div class="products-list__image-ctn">
          <img class="products-list__image" ng-src="{{getImage(product)}}" />
          <div
            class="products-list__tag"
            ng-class="{'products-list__tag--is-unavailable': isProductUnavailable(product), 'products-list__tag--is-sale': isProductOnSale(product) && !isProductUnavailable(product)}"
          >
            <span ng-if="isProductUnavailable(product)"
              >{{'Out of stock (Online)' | i18next}}</span
            >
            <span
              ng-if="isProductOnSale(product) && !isProductUnavailable(product)"
              >{{'On Sale' | i18next}}</span
            >
          </div>
        </div>
        <h2
          class="products-list__title"
          ng-bind="isAsset() ? getDescription(product) : getBrand(product)"
          ng-if="isAsset() || productsHaveBrand"
        ></h2>
        <p
          class="products-list__description"
          ng-if="!isAsset()"
          ng-bind="getDescription(product)"
        ></p>
        <div class="products-list__asset-meta-section" ng-if="isAsset()">
          <div class="products-list__asset-meta">
            <span class="products-list__label">{{'Start' | i18next}}: </span>
            <span class="products-list__value">{{getFormattedStartDate(product)}}</span>
          </div>
          <div class="products-list__asset-meta">
            <span class="products-list__label">{{'End' | i18next}}: </span>
            <span class="products-list__value">{{getFormattedEndDate(product)}}</span>
          </div>
          <div class="products-list__asset-meta">
            <span class="products-list__label">{{ 'Target' | i18next }}: </span>
            <span class="products-list__value">{{ getTarget(product) }}</span>
          </div>
        </div>
      </div>
      <div class="products-list__bottom-ctn" ng-if="!hideBottomDivider">
        <div class="products-list__comment-ctn" ng-if="hasComment">
          <p
            class="products-list__comment"
            ng-class="{'products-list__comment--is-edit': canEdit, 'products-list__comment--is-autoselected': isProductAutoselected(product)}"
            ng-bind="getComment(product)"
            ng-click="goToComment({product: product, type: type})"
          ></p>
        </div>
        <p
          class="products-list__price"
          ng-if="isProduct()"
          ng-click="addProductToSelected(product)"
        >
          {{getPrice(product)}}
        </p>
        <a
          class="products-list__view"
          target="_blank"
          ng-if="isAsset()"
          ng-href="{{getUrl(product)}}"
        >
          {{'View' | i18next}}
        </a>
        <a
          class="products-list__view"
          ng-if="!isAsset() && !hideViewLink && !showDetailsButton(product)"
          ng-class="{'products-list__view--is-single': !isProduct()}"
          ng-click="openModal(product)"
        >
          {{'View' | i18next}}
        </a>
        <a
          class="products-list__details"
          ng-if="showDetailsButton(product)"
          ng-click="showProductDetails(product)"
        >
          {{'Details' | i18next}}
        </a>
      </div>
    </div>
  </div>
</div>
