<?xml version="1.0" encoding="UTF-8"?>

<!-- config.xml reference: https://build.phonegap.com/docs/config-xml -->
<widget xmlns         = "http://www.w3.org/ns/widgets"
        xmlns:android = "http://schemas.android.com/apk/res/android"
        xmlns:gap     = "http://phonegap.com/ns/1.0"
        id            = "{{getParam 'certificate'}}"
        android-versionCode="*********"
        ios-CFBundleVersion="2507.01.02"
        version       = "{{getParam 'appVersion'}}">

    <name>{{getParam 'appName'}}</name>

    <description>
        A mobile app to access your Salesfloor account
    </description>

    <author href="http://www.salesfloor.com" email="<EMAIL>">
        Salesfloor Team
    </author>

    <engine name="ios" spec="7.1.1" />
    <engine name="android" spec="13.0.0" />

    <preference name="GradlePluginGoogleServicesEnabled" value="true" />
    <preference name="android-targetSdkVersion" value="34" />
    <preference name="GradleVersion" value="7.5.1" />
    <preference name="AndroidGradlePluginVersion" value="7.3.0" />
    <preference name="GradlePluginGoogleServicesVersion" value="4.3.14" />
    <preference name="GradlePluginKotlinVersion" value="1.6.20" />
    <preference name="deployment-target" value="13.0" />

    <content src="index.html" />
    <access origin="https://sentry.io" />
    <access origin="https://*" launch-external="yes" />
    <allow-navigation href="*://*lookbooks*" />


    <preference name="AndroidXEnabled" value="true" />
    <!--
        Enable individual API permissions here.
        The "device" permission is required for the 'deviceready' event.
    -->

        <!-- to enable individual permissions use the following examples -->
    <feature name="http://api.phonegap.com/1.0/camera"/>
    <feature name="Contacts">
        <param name="ios-package" value="CDVContacts" />
        <param name="android-package" value="org.apache.cordova.contacts.ContactManager" />
    </feature>
    <feature name="http://api.phonegap.com/1.0/file"/>
    <feature name="http://api.phonegap.com/1.0/media"/>
    <feature name="http://api.phonegap.com/1.0/notification"/>
    <feature name="http://api.phonegap.com/1.0/device" />

    <feature name="InAppBrowser">
        <param name="android-package" value="org.apache.cordova.InAppBrowser"/>
    </feature>
    <feature name="InAppBrowser">
        <param name="ios-package" value="org.apache.cordova.InAppBrowser" />
    </feature>

    <feature name="StatusBar">
        <param name="ios-package" value="CDVStatusBar" onload="true" />
    </feature>

    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#008DEB" />



    <!--
        If you do not want any permissions to be added to your app, add the
        following tag to your config.xml; you will still have the INTERNET
        permission on your app, which PhoneGap requires.
    -->

    <!-- Customize your app and platform with the preference element. -->

    <preference name="phonegap-version"           value="cli-9.0.0" />
    <preference name="orientation"                value="default" />        <!-- all: default means both landscape and portrait are enabled -->
    <preference name="target-device"              value="universal" />      <!-- all: possible values handset, tablet, or universal -->
    <preference name="fullscreen"                 value="false" />           <!-- all: hides the status bar at the top of the screen -->
    <preference name="webviewbounce"              value="false" />           <!-- ios: control whether the screen 'bounces' when scrolled beyond the top -->
    <preference name="DisallowOverscroll"         value="true" />           <!-- ios: control whether the screen 'bounces' when scrolled beyond the top -->
    <preference name="exit-on-suspend"            value="false" />          <!-- ios: if set to true, app will -->

    <preference name="android-windowSoftInputMode" value="stateHidden|adjustResize" />

    <!-- Needed for Android to properly build plugins -->
    <preference name="android-build-tool" value="gradle" />

    <!-- Plugins can also be added here. -->
    <!--
        <gap:plugin name="Example" />
        A list of available plugins are available at https://build.phonegap.com/docs/plugins
    -->
    <plugin name="branch-cordova-sdk" spec="5.2.0" />
    <branch-config>
      <!-- Those 4 values are configured in src/common/scripts/configs/configs.*.js -->
      <branch-key value="{{getParam 'branchKey'}}" />
      <link-domain value="{{getParam 'branchDomain'}}" />
      <ios-team-debug value="{{getParam 'iosTeamId'}}" />
      <ios-team-release value="{{getParam 'iosTeamId'}}" />
      <uri-scheme value="salesfloor" />
      <android-prefix value="/WSuf" />
    </branch-config>

  <!--
        The files used for the icons should always remain in the same folder
        for each type (ios/android), otherwise it doesn't work for some reason,
        even when following phonegap documentation.

        example:
        images/appicons/ios/... or images/appicons/android/drawable-****/launcher.png

        Not following these instructions will result in no Salesfloor icon and the app
        will use the default cordova icon instead.

        It is always a good idea to reboot the device to clear it's cached logos
        when modifying anything related to that.
    -->

    {{!-- cordova-plugin-file --}}
    <preference name="AndroidPersistentFileLocation" value="Internal" />

    <platform name="android">
        <icon src="resources/appicons/android/drawable-ldpi/launcher.png"    density="ldpi"    />
        <icon src="resources/appicons/android/drawable-mdpi/launcher.png"    density="mdpi"    />
        <icon src="resources/appicons/android/drawable-hdpi/launcher.png"    density="hdpi"    />
        <icon src="resources/appicons/android/drawable-xhdpi/launcher.png"   density="xhdpi"   />
        <icon src="resources/appicons/android/drawable-xxhdpi/launcher.png"  density="xxhdpi"  />
        <icon src="resources/appicons/android/drawable-xxxhdpi/launcher.png" density="xxxhdpi" />

        <resource-file src="resources/appicons/android/drawable/ic_notify.png" target="app/src/main/res/drawable/ic_notify.png" />
        <resource-file src="resources/appicons/android/drawable-ldpi/ic_notify.png" target="app/src/main/res/drawable-ldpi/ic_notify.png" />
        <resource-file src="resources/appicons/android/drawable-mdpi/ic_notify.png" target="app/src/main/res/drawable-mdpi/ic_notify.png" />
        <resource-file src="resources/appicons/android/drawable-hdpi/ic_notify.png" target="app/src/main/res/drawable-hdpi/ic_notify.png" />
        <resource-file src="resources/appicons/android/drawable-xhdpi/ic_notify.png" target="app/src/main/res/drawable-xhdpi/ic_notify.png" />
        <resource-file src="www/assets/popcorn.wav" target="app/src/main/res/raw/popcorn.wav" />
        <resource-file src="google-services.json" target="app/google-services.json" />

        <!-- HACK: copied from the plugin.xml of the imagepicker, otherwise it won't be added to AndroidManifest.xml. Cordova 9 bug? -->
        <config-file target="AndroidManifest.xml" parent="/manifest/application">
          <activity android:label="@string/multi_app_name" android:name="com.synconset.MultiImageChooserActivity" android:theme="@style/Theme.AppCompat.Light" />
        </config-file>

        <preference name="AndroidLaunchMode" value="singleTask" />

        {{!-- allow background calls --}}
        <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
        <uses-permission android:name="android.permission.WAKE_LOCK" />
        <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> 
    </platform>

    {{#unless (getParam 'skipIos')}}
    <platform name="ios">
      {{!-- <hook type="after_prepare" src="./after-prepare-hook.js" /> // we do not strip architecture.--}}
      <preference name="UseSwiftLanguageVersion" value="4.2" /> {{!-- To specify swift version in our case v4.2 required RTC --}}
      <feature name="CDVWKWebViewEngine">
        <param name="ios-package" value="CDVWKWebViewEngine" />
      </feature>
      <preference name="CordovaWebViewEngine" value="CDVWKWebViewEngine" />
      <preference name="WKWebViewOnly" value="true" />
      <preference name="KeychainAccessibility" value="WhenUnlockedThisDeviceOnly"/>
      <preference name="AllowInlineMediaPlayback" value="true" />


      <preference name="scheme" value="app" />
      <preference name="hostname" value="localhost"  />

      <icon src="resources/appicons/ios/icon.png" width="57" height="57" />
      <icon src="resources/appicons/ios/<EMAIL>" width="114" height="114" />

        <!-- iPhone 6 / 6+ -->
      <icon src="resources/appicons/ios/<EMAIL>" width="180" height="180" />

        <!-- iPhone / iPod Touch  -->
      <icon src="resources/appicons/ios/icon-60.png" width="60" height="60" />
      <icon src="resources/appicons/ios/<EMAIL>" width="120" height="120" />

        <!-- iPad OS 5,6 -->
      <icon src="resources/appicons/ios/icon-72.png" width="72" height="72" />
      <icon src="resources/appicons/ios/<EMAIL>" width="144" height="144" />
      <icon src="resources/appicons/ios/icon-50.png" width="50" height="50" />
      <icon src="resources/appicons/ios/<EMAIL>" width="100" height="100" />

        <!-- appstore -->
      <icon src="resources/appicons/ios/<EMAIL>" width="1024" height="1024" />

        <!-- iPad -->
      <icon src="resources/appicons/ios/icon-76.png" width="76" height="76" />
      <icon src="resources/appicons/ios/<EMAIL>" width="152" height="152" />
      <icon src="resources/appicons/ios/<EMAIL>" width="167" height="167" />

        <!-- Settings Icon -->
      <icon src="resources/appicons/ios/icon-small.png" width="29" height="29" />
      <icon src="resources/appicons/ios/<EMAIL>" width="58" height="58" />
      <icon src="resources/appicons/ios/<EMAIL>" width="87" height="87" />

        <!-- Spotlight Icon -->
      <icon src="resources/appicons/ios/icon-40.png" width="40" height="40" />
      <icon src="resources/appicons/ios/<EMAIL>" width="80" height="80" />
      <icon src="resources/appicons/ios/<EMAIL>" width="120" height="120" />


      <edit-config target="NSPhotoLibraryUsageDescription" file="*-Info.plist" mode="merge">
        <string>Salesfloor allows sales associates to send library photos.</string>
      </edit-config>

      <edit-config target="NSCameraUsageDescription" file="*-Info.plist" mode="merge">
        <string>Salesfloor allows sales associates to send photos and video chat from their camera.</string>
      </edit-config>

      <edit-config target="NSMicrophoneUsageDescription" file="*-Info.plist"  mode="merge">
        <string>Salesfloor allows sales associates to video chat using their microphone.</string>
      </edit-config>

      <edit-config target="NSContactsUsageDescription" file="*-Info.plist"  mode="merge">
        <string>Import your existing business contacts to your Salesfloor Address book so you can send an email or text message to a known customer.</string>
      </edit-config>

      <edit-config target="NSCalendarsUsageDescription" file="*-Info.plist"  mode="merge">
        <string>Salesfloor allows sales associates to save events to their calendars.</string>
      </edit-config>

      <edit-config target="NSBluetoothPeripheralUsageDescription" file="*-Info.plist"  mode="merge">
          <string>Salesfloor allows sales associates to video chat using their bluetooth as a microphone.></string>
      </edit-config>

      <edit-config target="NSMotionUsageDescription" file="*-Info.plist"  mode="merge">
        <string>Salesfloor allows sales associates to share their location.</string>
      </edit-config>

      <edit-config target="NSLocationWhenInUseUsageDescription" file="*-Info.plist"  mode="merge">
        <string>Salesfloor allows sales associates to share their location.</string>
      </edit-config>

      <edit-config target="NSBluetoothAlwaysUsageDescription" file="*-Info.plist"  mode="merge">
        <string>Salesfloor allows sales associates to video chat using their bluetooth as a microphone.</string>
      </edit-config>

      <edit-config target="CFBundleAllowMixedLocalizations" file="*-Info.plist"  mode="merge">
        <true/>
      </edit-config>

      <config-file parent="aps-environment" platform="ios" target="*.entitlements">
        <string>{{getApsEnvironment}}</string>
      </config-file>
    </platform>
    {{/unless}}

    <!-- Splashscreens -->
    <preference name="AutoHideSplashScreen" value="true" />
    <preference name="ShowSplashScreenSpinner" value="false" />

    <platform name="android">
        <preference name="AndroidWindowSplashScreenAnimatedIcon" value="res/screen/android/splashscreen.xml" />
        <preference name="AndroidWindowSplashScreenBackground" value="#f2f2f2" />
        {{!-- The design guidelines recommend against using a branding image. --}}
        {{!-- AndroidWindowSplashScreenBrandingImage --}}

        {{!-- to strip dublicated permissions from different plugins from Manifest.xml --}}
        <hook type="before_build" src="hooks/strip_dup_permission.js"/>
    </platform>

    {{#unless (getParam 'skipIos')}}
    <platform name="ios">
        <splash src="res/screen/ios/Default@2x~universal~anyany.png" />
        <splash src="res/screen/ios/Default@2x~universal~comany.png" />
        <splash src="res/screen/ios/Default@2x~universal~comcom.png" />
        <splash src="res/screen/ios/Default@3x~universal~anyany.png" />
        <splash src="res/screen/ios/Default@3x~universal~anycom.png" />
        <splash src="res/screen/ios/Default@3x~universal~comany.png" />
        <splash src="res/screen/ios/Default@3x~universal~comcom.png" />

        <edit-config file="*-Info.plist" mode="merge" target="NSAppTransportSecurity">
          <dict>
            <key>NSAllowsArbitraryLoads</key>
            <false/>
            <key>NSExceptionDomains</key>
            <dict>
              <key>oauth.io</key>
              <dict/>
            </dict>
          </dict>
        </edit-config>
    </platform>
    {{/unless}}
</widget>
