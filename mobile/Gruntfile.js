/*eslint no-console: off*/

/*
 * SF-18933 Check that a mobile build exists before moving tickets to QA Ready
 */

'use strict';

var fs = require('fs'),
  child_process = require('child_process'),
  path = require('path'),
  aws = require('aws-sdk');

const { WebClient } = require('@slack/web-api');
// TODO Read from the environment variables instead
const apiToken = '*******************************************************';
const web = new WebClient(apiToken);

aws.config.apiVersions = {
  s3: '2006-03-01'
};

var projDir = path.resolve(__dirname);

const CERTIFICATE_DEV = 'com.salesfloor.salesfloor';

function slackSuccess(msg, callback) {
  // Given some known conversation ID (representing a public channel, private channel, DM or group DM)
  const conversationId = '#ci-mobile-build-links';

  (async () => {
    // Post a message to the channel, and await the result.
    // Find more arguments and details of the response: https://api.slack.com/methods/chat.postMessage
    try {
      const result = await web.chat.postMessage({
        text: msg,
        username: 'Salesfloor Mobile Build',
        icon_emoji: ':iphone:',
        channel: conversationId
      });

      // The result contains an identifier for the message, `ts`.
      console.log(`Successfully send message ${result.ts} in conversation ${conversationId}`);
    } catch (error) {
      console.log(error);
    }

    // This is mostly to call done() for grunt async.
    if (callback) {
      callback();
    }
  })();
}

function getDownloadPage(pkgUrl) {
  return 'https://elguntors-stg.salesfloor.net/mobileapp/package.php?key=' + pkgUrl.split('/').pop();
}

// Get configs
function getConfigs(grunt, targetEnv) {
  targetEnv = targetEnv || 'dev';
  const { configs } = require(`./${grunt.config.get('dirs').common}/scripts/configs/configs.${targetEnv}.js`);

  configs.appVersion = getVersion(grunt);
  configs.configEnv = grunt.option('configEnv') || '';

  grunt.buildConfigs = configs;
  return configs;
}

function getVersion(grunt) {
  // if appVersion is a number, version becomes a number and .match() fails
  var version = (grunt.option('appVersion') || 'dev').toString();
  // https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingMetadata.html
  // we remove dashes and fancy chars
  console.log('version: ' + version);
  if (version.match(/[^a-zA-Z0-9_.-]/)) {
    grunt.fail.fatal('appVersion can only contain letters, digits, dashes, underscores and periods');
  }
  return version;
}

function getMessage(grunt) {
  var msg = grunt.option('msg') || '';
  if (msg) {
    msg = ' - ' + msg;
  }
  return msg;
}


module.exports = function (grunt) {
  var s3Bucket = 'mobile-app-version';
  var s3 = new aws.S3();

  // Load grunt tasks automatically
  require('load-grunt-tasks')(grunt);

  // Time how long tasks take. Can help when optimizing build times
  require('time-grunt')(grunt);

  var getPkgKey = function (version, extension) {
    var c = grunt.buildConfigs,
      isProd = grunt.option('production');

    if (!extension.match(/^\./)) {
      extension = '.' + extension;
    }

    return c.certificate + (isProd ? '.prd' : '') + '-' + c.appName.replace(/[\s-]+/g, '_') + '-' + version + extension;
  };

  /**
   * void getCommitId(onSuccess)
   *
   * Calls onSuccess with the commit id of the repository's HEAD.
   */
  var getCommitId = function (onSuccess) {
    child_process.exec('git rev-parse HEAD', function (err, stdout) {
      if (err) {
        grunt.fail.fatal('git err: ' + err);
      }
      onSuccess(stdout.trim());
    });
  };

  var uploadPackage = function (version, extension, pkgFile, onSuccess, onError) {
    var logMsg = grunt.option('msg');
    var megabytes = 0;

    var __uploadPackage = function (onSuccess) {
      if ('app' === extension) {
        // .app is a directory. tarball it
        grunt.log.writeln('Packaging emulator app');

        var cmds = [];
        cmds.push('set -e');
        cmds.push('cd \'' + path.dirname(pkgFile) + '\'');
        var bn = path.basename(pkgFile);
        cmds.push('rm -f \'' + bn + '.tar\'*');
        cmds.push('tar -f- -c \'' + bn + '\' >\'' + bn + '.tar\'');
        cmds.push('gzip -9 \'' + bn + '.tar\'');

        pkgFile = pkgFile + '.tar.gz';

        child_process.execSync(cmds.join('\n'), {shell:'/bin/sh'}, function (err) {
          if (err) {
            grunt.fail.fatal('failed to tarball app: ' + err);
          }
        });
        onSuccess(pkgFile, 'app.tar.gz');
      } else {
        onSuccess(pkgFile, extension);
      }
    };

    __uploadPackage(function (pkgFile, extension) {
      var pkgKey = getPkgKey(version, extension);

      grunt.log.writeln('uploading ' + pkgFile + ' to ' + s3Bucket + '/' + pkgKey);

      getCommitId(function (commitId) {
        s3.upload({
          Bucket: s3Bucket,
          ACL: 'public-read',
          Key: pkgKey,
          Body: fs.createReadStream(pkgFile),
          Metadata: {
            'commit-id': commitId,
          }
        }).on('httpUploadProgress', function (progress) {
          var loaded = Math.floor(progress.loaded / 500000);
          if (loaded > megabytes) {
            megabytes = loaded;
            grunt.log.write('.');
          }
        }).send(function (err, ret) {
          grunt.log.writeln('!');
          if (err) {
            if (logMsg) {
              grunt.log.error(err);
              onError('mobile upload FAIL - ' + pkgFile + ' - ' + logMsg);
            } else {
              grunt.fail.fatal('failed upload: ' + err);
            }
          } else {
            fs.unlinkSync(pkgFile);
            onSuccess(ret.Location);
          }
        });
      });
    });
  };

  const targetApp = process.env.APP || grunt.option('app') || 'mobile';
  const env = grunt.option('env') || 'dev';

  const distDirs = {
    mobile: 'cordova/www',
    backoffice: 'dist'
  };

  // Define the configuration for all the tasks
  grunt.initConfig({

    targetApp,

    // Project settings
    dirs: {
      // configurable paths
      proj: projDir,
      src: 'src',
      cordova: 'cordova',
      vendor: 'vendor',

      common: 'app',
      mobile: 'src/mobile',
      sfapp: 'sf-app',
      backoffice: 'src/backoffice',
      app: `src/${targetApp}`,

      build: '.tmp',
      dist: distDirs[targetApp],
    },

    // Watches files for changes and runs tasks based on the changed files
    watch: {
      options: {
        livereload: true
      },
      js: {
        files: [
          '<%= dirs.app %>/**/*.js',
          '<%= dirs.common %>/scripts/**/*.js'
        ],
        tasks: ['babel']
      },
      react: {
        files: ['<%= dirs.common %>/mobile-react/app/js/**/*.js'],
        tasks: ['exec:buildMobileReact']
      },
      sfmobile: {
        files: ['<%= dirs.sfapp %>/src/**/*.*'],
        tasks: ['exec:buildSfMobile']
      },
      sfmobileclasses: {
        files: ['<%= dirs.sfapp %>/src/classes/*.*'],
        tasks: ['exec:buildSfMobileClasses']
      },
      compass: {
        files: [
          '<%= dirs.common %>/**/*.{scss,sass}',
          '<%= dirs.src %>/**/*.{scss,sass}'
        ],
        tasks: ['compass']
      },
      gruntfile: {
        files: ['Gruntfile.js']
      },
      locales: {
        files: ['<%= dirs.common %>/locales/**/*.json'],
      },
      indexhtml: {
        files: ['<%= dirs.app %>/index.html'],
        tasks: ['copy:indexhtml']
      },
      assets: {
        files: [
          '<%= dirs.common %>/**/*.html',
          '<%= dirs.src %>/**/*.html',
          '<%= dirs.build %>/styles/**/*.css',
          '<%= dirs.src %>/**/*.{png,jpg,jpeg,gif,webp,svg}'
        ]
      }
    },

    // The actual grunt server settings
    connect: {
      options: {
        hostname: '0.0.0.0',
        port: parseInt(grunt.option('port')) || 9000,
        livereload: parseInt(grunt.option('livereloadPort')) || 35729,
        open: false,
        base: [
          '.',
          '<%= dirs.dist %>',
          '<%= dirs.build %>',
          '<%= dirs.app %>',
          '<%= dirs.common %>',
          '<%= dirs.src %>'
        ]
      },
      livereload: {
      },
      https: {
        options: {
          protocol: 'https',
          port: parseInt(grunt.option('port')) || 443,
        }
      }
    },

    // Empties folders to start fresh
    clean: [
      '<%= dirs.build %>',
      '<%= dirs.dist %>'
    ],

    // Copies files to places other tasks can use
    copy: {
      common: {
        files: [{
          expand: true,
          cwd: '<%= dirs.common %>',
          dest: '<%= dirs.dist %>',
          src: [
            'fonts/**/*',
            'locales/**/*',
            'mobile-react/bundle/**/*'
          ]
        }, {
          expand: true,
          cwd: '<%= dirs.app %>',
          dest: '<%= dirs.dist %>',
          src: ['index.html', 'assets/**/*']
        }, {
          src: '<%= dirs.vendor %>/analytics.js',
          dest: '<%= dirs.dist %>/scripts/analytics.js'
        }, {
          src: './node_modules/webrtc-adapter/out/adapter.js',
          dest: '<%= dirs.dist %>/scripts/adapter.js'
        }]
      },
      indexhtml: {
        files: [{
          src: '<%= dirs.app %>/index.html',
          dest: '<%= dirs.dist %>/index.html'
        }]
      },
      mobile: {
        files: [{
          expand: true,
          cwd: '<%= dirs.app %>/assets',
          src: ['popcorn.wav', 'mute.caf'],
          dest: '<%= dirs.dist %>'
        }]
      },
      assets: {
        files: [{
          expand: true,
          cwd: '<%= dirs.dist %>/styles',
          src: ['**'],
          dest: '<%= dirs.sfapp %>/public/styles'
        }, {
          expand: true,
          cwd: '<%= dirs.dist %>/fonts',
          src: ['**'],
          dest: '<%= dirs.sfapp %>/public/fonts'
        }, {
          expand: true,
          cwd: '<%= dirs.dist %>/images',
          src: ['**'],
          dest: '<%= dirs.sfapp %>/public/images'
        }] 
        //TODO: add locales as well ?
      },
      backoffice: {
        files: []
      },
    },


    // Reads HTML for usemin blocks to enable smart builds that automatically
    // concat, minify and revision files. Creates configurations in memory so
    // additional tasks can operate on them
    useminPrepare: {
      html: '<%= dirs.dist %>/index.html',
      options: {
        root: '<%= dirs.build %>',
        dest: '<%= dirs.dist %>',
        flow: {
          steps: {
            js: ['concat'],
            css: ['concat', 'cssmin']
          },
          post: {}
        }
      }
    },

    // Performs rewrites based on rev and the useminPrepare configuration
    usemin: {
      html: '<%= dirs.dist %>/index.html',
    },

    // The following *-min tasks produce minified files in the dist folder
    imagemin: {
      dist: {
        files: [{
          expand: true,
          cwd: '<%= dirs.common %>/images',
          src: '**/*.{png,jpg,jpeg,gif}',
          dest: '<%= dirs.dist %>/images'
        }, {
          expand: true,
          cwd: '<%= dirs.app %>/assets/images',
          src: '**/*.{png,jpg,jpeg,gif}',
          dest: '<%= dirs.dist %>/images'
        }, {
          expand: true,
          cwd: '<%= dirs.vendor %>/jquery-ui/images',
          src: '*.png',
          dest: '<%= dirs.dist %>/styles/images'
        }]
      }
    },
    svgmin: {
      dist: {
        files: [{
          expand: true,
          cwd: '<%= dirs.common %>/images',
          src: '**/*.svg',
          dest: '<%= dirs.dist %>/images'
        }, {
          expand: true,
          cwd: '<%= dirs.app %>/assets/images',
          src: '**/*.svg',
          dest: '<%= dirs.dist %>/images'
        }]
      }
    },
    htmlmin: {
      dist: {
        options: {
          collapseWhitespace: true,
          removeComments: true,
          removeCommentsFromCDATA: true,
          removeOptionalTags: true
        },
        files: [{
          expand: true,
          cwd: '<%= dirs.common %>',
          src:'views/**/*.html',
          dest: '<%= dirs.dist %>'
        }, {
          expand: true,
          cwd: '<%= dirs.src %>',
          src: `${targetApp}/app/**/*.html`,
          dest: '<%= dirs.dist %>'
        }, {
          src: '<%= dirs.dist %>/index.html',
          dest: '<%= dirs.dist %>/index.html'
        }]
      }
    },

    // Compiles Sass to CSS and generates necessary files if requested
    compass: {
      options: {
        sassDir: '<%= dirs.app %>/styles',
        cssDir: '<%= dirs.dist %>/styles',
        specify: '<%= dirs.app %>/styles/main.scss',
        importPath: [
          '<%= dirs.proj %>',
          '<%= dirs.src %>',
          '<%= dirs.common %>/styles',
        ],
        imagesDir: '<%= dirs.dist %>/images',
        sourcemap: true,
        outputStyle: 'compressed'
      },
      dist: {}
    },

    // Add vendor prefixed styles
    autoprefixer: {
      options: {
        browsers: ['last 1 version']
      },
      dist: {
        files: [{
          expand: true,
          cwd: '<%= dirs.dist %>/styles',
          src: '**/*.css',
          dist: '<%= dirs.dist %>/styles',
        }]
      }
    },

    exec: {
      buildMobileReact: {
        cmd: 'npm run build',
        cwd: '<%= dirs.common %>/mobile-react'
      },
      // New  React mobile
      devSfMobile: {
        cmd: 'npm run dev',
        cwd: '<%= dirs.sfapp %>'
      },
      buildSfMobile: {
        cmd: env === 'dev' ? 'npm run build' : 'npm run build:ci',
        cwd: '<%= dirs.sfapp %>'
      },
      buildSfMobileClasses: {
        cmd: 'npm run buildClasses',
        cwd: '<%= dirs.sfapp %>'
      },
      buildSfMobileClassesBo: {
        cmd: 'npm run buildClassesBo',
        cwd: '<%= dirs.sfapp %>'
      },
      minMobileClasses: {
        cmd: 'npm run minClasses',
        cwd: '<%= dirs.sfapp %>'
      }
    },

    writefile: {
      options: {
        data: {},  // read template data from JSON file
        helpers: {
          getParam(name) {
            return grunt.buildConfigs[name];
          },
          getApsEnvironment() {
            const { certificate } = grunt.buildConfigs;
            return certificate === CERTIFICATE_DEV ? 'development' : 'production';
          }
        },
        paths: {
          // provide directory contents to template
        },
      },
      configxml: {
        files: [{
          src: '<%= dirs.cordova %>/config.xml.hbs',
          dest: '<%= dirs.cordova %>/config.xml',
        }]
      },
      configenvjs: {
        files: [{
          src: '<%= dirs.src %>/common/config-env.js.hbs',
          dest: '<%= dirs.build %>/<%= targetApp %>/app/config-env.js',
        }]
      }
    },

    cordovacli: {
      options: {
        path: '<%= dirs.cordova %>',
        cli: '<%= dirs.proj %>/<%= dirs.cordova %>/node_modules/cordova',
      },
      add_platform_ios: {
        options: {
          command: 'platform',
          action: 'add',
          platforms: ['ios@7.1.1'],
          args: ['--verbose'],
        }
      },
      add_platform_android: {
        options: {
          command: 'platform',
          action: 'add',
          platforms: ['android@13.0.0'],
        }
      },
      remove_platform_ios: {
        options: {
          command: 'platform',
          action: 'remove',
          platforms: ['ios'],
        }
      },
      remove_platform_android: {
        options: {
          command: 'platform',
          action: 'remove',
          platforms: ['android'],
        }
      },
      build_ios: {
        options: {
          command: 'build',
          platforms: ['ios'],
        }
      },
      build_android: {
        options: {
          command: 'build',
          platforms: ['android'],
          args: ['--', '--gradleArg=-PcdvCompileSdkVersion=34'],
        }
      },
      prepare_ios: {
        options: {
          command: 'prepare',
          platforms: ['ios'],
        }
      },
      prepare_android: {
        options: {
          command: 'prepare',
          platforms: ['android'],
        }
      },
      archive_ios_emulator: {
        options: {
          command: 'build',
          platforms: ['ios'],
          args: ['--emulator'],
        }
      },
      archive_android_emulator: {
        options: {
          command: 'build',
          platforms: ['android'],
          args: ['--emulator', '--', '--gradleArg=-PcdvCompileSdkVersion=34'],
        }
      },
      archive_ios_debug: {
        options: {
          command: 'build',
          platforms: ['ios'],
          args: ['--device', '--debug'],
        }
      },
      archive_android_debug: {
        options: {
          command: 'build',
          platforms: ['android'],
          args: ['--device', '--', '--gradleArg=-PcdvCompileSdkVersion=34'],
        }
      },
      release_ios: {
        options: {
          command: 'build',
          platforms: ['ios'],
          args: ['--device', '--release', '--verbose'],
        }
      },
      release_android: {
        options: {
          command: 'build',
          platforms: ['android'],
          args: ['--device', '--release', '--', '--packageType=apk', '--gradleArg=-PcdvCompileSdkVersion=34'],
        }
      },
      release_ios_enterprise: {
        options: {
          command: 'build',
          platforms: ['ios'],
          args: ['--device', '--release', '--verbose', '--buildConfig=buildEnterprise.json'],
        }
      },
      release_android_enterprise: {
        options: {
          command: 'build',
          platforms: ['android'],
          args: ['--device', '--release', '--buildConfig=buildEnterprise.json', '--', '--packageType=apk', '--gradleArg=-PcdvCompileSdkVersion=34'],
        }
      },
      release_ios_businessmanager: {
        options: {
          command: 'build',
          platforms: ['ios'],
          args: ['--device', '--release', '--buildConfig=buildBusinessmanager.json'],
        }
      },
      release_android_businessmanager: {
        options: {
          command: 'build',
          platforms: ['android'],
          args: ['--device', '--release', '--buildConfig=buildBusinessmanager.json', '--', '--packageType=apk', '--gradleArg=-PcdvCompileSdkVersion=34'],
        }
      },
    },

    babel: {
      options: {
        sourceMap: true
      },
      dist: {
        files: [{
          expand: true,
          cwd: '<%= dirs.common %>',
          src: 'scripts/**/*.js',
          dest: '<%= dirs.build %>/common'
        }, {
          expand: true,
          cwd: '<%= dirs.src %>',
          src: 'common/**/*.js',
          dest: '<%= dirs.build %>'
        }, {
          expand: true,
          cwd: '<%= dirs.src %>',
          src: `${targetApp}/app/**/*.js`,
          dest: '<%= dirs.build %>'
        }]
      }
    },

    uglify: {
      app: {
        options: {
          sourceMap: true
        },
        files: {
          '<%= dirs.dist %>/scripts/app.js': '<%= dirs.dist %>/scripts/app.js'
        }
      }
    },

    eslint: {
      all: [
        'Gruntfile.js',
        '<% dirs.app %>/scripts/**/*.js',
        '<%= dirs.app %>/mobile-react/app/js/**/*.js',
      ],
      fix: {
        options: {
          fix: true
        },
        src: [
          'Gruntfile.js',
          '<%= dirs.app %>/scripts/**/*.js',
          '<%= dirs.app %>/mobile-react/app/js/**/*.js',
        ]
      }
    }
  });

  grunt.loadNpmTasks('grunt-exec');
  grunt.loadNpmTasks('grunt-writefile');
  grunt.loadNpmTasks('grunt-cordovacli');

  grunt.registerTask('expand-version', '', function () {
    const done = this.async();
    const version = getVersion(grunt);
    const dirs = grunt.config.get('dirs');

    const inFile = `${dirs.common}/scripts/services/version.js.in`;
    const outFile = `${dirs.app}/app/version.js`;

    child_process.exec(`sed -e 's|@appVersion@|${version}|' ${inFile} > ${outFile}`,
      (error) => {
        if (error) {
          grunt.fail.fatal(`Failed to create ${outFile}: ${error}`);
        }
        done();
      }
    );
  });

  // Build
  grunt.registerTask('build', (targetEnv) => {
    getConfigs(grunt, targetEnv);
    grunt.task.run([
      'clean',
      'expand-version',
      'writefile:configenvjs',
      'babel',
      ...(targetApp !== 'backoffice' ? ['exec:buildMobileReact'] : []),
      'copy:common',
      `copy:${targetApp}`,
      ...(targetApp !== 'backoffice' ? ['exec:buildSfMobile', 'exec:buildSfMobileClasses'] : ['exec:buildSfMobileClassesBo']),
      'imagemin',
      'svgmin',
      'compass',
      'useminPrepare',
      'concat',
      'uglify',
      'cssmin',
      'usemin',
      'htmlmin'
    ]);
  });

  // Serve
  grunt.registerTask('serve', () => {
    getConfigs(grunt);
    grunt.task.run([
      'clean',
      'expand-version',
      'writefile:configenvjs',
      'babel',
      'exec:buildMobileReact',
      'copy:common',
      `copy:${targetApp}`,
      ...(targetApp !== 'backoffice' ? ['exec:buildSfMobile', 'exec:buildSfMobileClasses'] : ['exec:buildSfMobileClassesBo']),
      'imagemin',
      'svgmin',
      'compass',
      `connect:${grunt.option('connectType') || 'livereload'}`,
      'copy:assets',
      'watch'
    ]);
  });

  grunt.registerTask('default', [
    'build'
  ]);

  const debugTargets = [
    'dev',
    'stg',
    'test1',
    'test2',
    'test3',
  ];
  const releaseTargets = [
    'appstore',
    'businessmanager',
    'enterprise',
  ];

  const getCordovaPackageFile = (path) => {
    const cordovaDir = grunt.config.get('dirs').cordova;
    return grunt.file.expand(`${cordovaDir}/platforms/${path}`).pop();
  };

  grunt.registerTask('publish-emu-pkg', 'Publish emulator package to S3', function () {
    const done = this.async();

    const version = getVersion(grunt);
    if (!version) {
      grunt.fail.fatal('appVersion is required');
    }

    const pkgFile = getCordovaPackageFile('ios/build/**/*.app');
    uploadPackage(version, 'app', pkgFile, (pkgUrl) => {
      slackSuccess('Uploaded ' + pkgUrl, done);
    }, msg => slackSuccess(msg, done));
  });

  grunt.registerTask('publish-local-pkg', 'Publish locally-built package to S3', function (platform, target) {
    if (target === 'dev') {
      console.log('Skip package uploading for dev');
      return;
    }

    const done = this.async();

    const version = getVersion(grunt);
    if (!version) {
      grunt.fail.fatal('appVersion is required');
    }

    if (!platform) {
      grunt.fail.fatal('platform is required');
    }

    const pkgExt = platform === 'ios' ? 'ipa' : 'apk';
    const pkgPath = platform === 'ios'
      ? 'ios/build/**/*.ipa'
      : `android/app/build/outputs/apk/${releaseTargets.includes(target) ? 'release/app' : 'debug/app-debug'}.apk`;
    const pkgFile = getCordovaPackageFile(pkgPath);

    uploadPackage(version, pkgExt, pkgFile, (pkgUrl) => {
      slackSuccess('Uploaded ' + getDownloadPage(pkgUrl), done);
    }, msg => slackSuccess(msg, done));
  });

  // Cordova config.xml
  grunt.registerTask('configxml', 'set variables and write to config.xml', (targetEnv) => {
    getConfigs(grunt, targetEnv);
    grunt.task.run('writefile:configxml');
  });

  grunt.registerTask('sign-apk', (platform, target) => {
    if (platform !== 'android' || !releaseTargets.includes(target)) {
      console.log(`Skip APK signing: ${platform}, ${target}`);
      return;
    }
    const inFile = getCordovaPackageFile('android/app/build/outputs/apk/release/*.apk');
    const outFile = `${path.dirname(inFile)}/app.apk`;
    const cmdSign = `apksigner sign --ks ${process.env.KEYSTORE} --ks-pass env:KEYSTORE_PASSWORD --out ${outFile} ${inFile}`;
    console.log('Signing APK...');
    console.log(cmdSign);
    try {
      child_process.execSync(cmdSign);
    } catch (error) {
      grunt.fail.fatal(error);
    }
  });

  grunt.registerTask('cordova-build', (platform, buildType, targetEnv) => {
    targetEnv = targetEnv || buildType;
    const pkgType = buildType === 'emu' ? 'emu' : 'local';

    const getPlatformTasks = (platform) => {
      const buildCmds = {
        debug: `archive_${platform}_debug`,
        appstore: `release_${platform}`,
        enterprise: `release_${platform}_enterprise`,
        businessmanager: `release_${platform}_businessmanager`,
        emu: `archive_${platform}_emulator`,
      };
      return [
        `configxml:${platform === 'android' ? targetEnv.replace('appstore', 'playstore') : targetEnv}`,
        `cordovacli:remove_platform_${platform}`,
        `cordovacli:add_platform_${platform}`,
        `cordovacli:${buildCmds[buildType]}`,
        `sign-apk:${platform}:${targetEnv}`,
        `publish-${pkgType}-pkg:${platform}:${targetEnv}`,
      ];
    };

    let tasks = [
      `build:${targetEnv}`,
    ];
    if (platform === 'all') {
      tasks = tasks.concat(getPlatformTasks('ios'), getPlatformTasks('android'));
    } else {
      tasks = tasks.concat(getPlatformTasks(platform));
    }

    grunt.task.run(tasks);
  });

  debugTargets.forEach((target) => {
    grunt.registerTask(`local-${target}`, `cordova-build:all:debug:${target}`);
    grunt.registerTask(`local-${target}-ios`, `cordova-build:ios:debug:${target}`);
    grunt.registerTask(`local-${target}-android`, `cordova-build:android:debug:${target}`);
  });
  releaseTargets.forEach((target) => {
    grunt.registerTask(`local-${target}`, `cordova-build:all:${target}`);
    grunt.registerTask(`local-${target}-ios`, `cordova-build:ios:${target}`);
    grunt.registerTask(`local-${target}-android`, `cordova-build:android:${target}`);
  });
  grunt.registerTask('emu-stg', 'cordova-build:all:emu:stg');
  grunt.registerTask('emu-stg-ios', 'cordova-build:ios:emu:stg');
  grunt.registerTask('emu-stg-android', 'cordova-build:android:emu:stg');

  // Cordova tasks
  grunt.registerTask('cordova:config', [
    'configxml:dev'
  ]);

  grunt.registerTask('cordova:build', [
    'build:stg',
  ]);

  grunt.registerTask('cordova:platform:add:ios', [
    'cordovacli:add_platform_ios',
  ]);

  grunt.registerTask('cordova:platform:remove:ios', [
    'cordovacli:remove_platform_ios',
  ]);

  grunt.registerTask('cordova:platform:add:android', [
    'cordovacli:add_platform_android',
  ]);

  grunt.registerTask('cordova:prepare:ios', [
    'cordovacli:prepare_ios',
  ]);

  grunt.registerTask('cordova:prepare:android:stg', [
    'cordovacli:prepare_android',
  ]);

  grunt.registerTask('cordova:build:stg', [
    'build:stg',
    'cordovacli:archive_ios_debug',
  ]);
  grunt.registerTask('cordova:stg', [
    'cordova:build:stg',
    'publish-local-pkg:ios'
  ]);
  grunt.registerTask('slacknotification', '', function () {
    const done = this.async();
    slackSuccess(grunt.buildConfigs.appName + ' - build OK - v' + getVersion(grunt) + getMessage(grunt), done);
  });

  grunt.registerTask('sfmobile:copy:assets', '', function () {
    grunt.task.run(['copy:assets']);
  });

  grunt.registerTask('sfmobile:dev', '', function () {
    grunt.task.run(['exec:devSfMobile']);
  });

  grunt.registerTask('sfmobile:build', '', function () {
    grunt.task.run(['exec:buildSfMobile']);
  });
};
