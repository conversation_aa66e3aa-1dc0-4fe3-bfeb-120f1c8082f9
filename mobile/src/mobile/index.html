<!DOCTYPE html>
<html class="no-js">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <meta
      http-equiv="Content-Security-Policy"
      content="default-src * 'self' data: gap: 'unsafe-inline' 'unsafe-eval';style-src * 'self' 'unsafe-inline' 'unsafe-eval' gap:;script-src * 'self' 'unsafe-inline' 'unsafe-eval' gap:;frame-src *;"
    />

    <title>Salesfloor Mobile</title>

    <meta name="description" content="Salesfloor Mobile" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, height=device-height, maximum-scale=1, user-scalable=no, viewport-fit=cover"
    />

    <!-- overwright default cordova icon -->
    <link rel="icon" href="data:," />
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->

    <!-- build:css(.) styles/vendor.css -->
    <link
      rel="stylesheet"
      href="vendor/sass-bootstrap/dist/css/bootstrap.css"
    />
    <link rel="stylesheet" href="vendor/ng-tags-input/ng-tags-input.min.css" />
    <link rel="stylesheet" href="vendor/croppie/ng-croppie.min.css" />
    <link
      rel="stylesheet"
      href="node_modules/suneditor/dist/css/suneditor.min.css"
    />
    <!-- endbuild -->

    <link rel="stylesheet" href="styles/main.css" />
  </head>
  <body
    ng-class="{
    'device--is-android': isAndroid(),
    'device--is-desktop': sfApp === 'desktop',
    'device--is-tablet-md-landscape': isTabletInLandscapeMode,
    'device--is-orientation-unlocked': !screenOrientationIsLocked
    }"
  >
    <flash-message></flash-message>
    <div id="root"></div>
    <main-menu
      ng-class="{
        fixed: mainMenuVariant === 'fixed',
        compact: mainMenuVariant === 'compact'
      }"
      ng-if="loggedIn"
      show="openMainMenu"
      variant="mainMenuVariant"
    ></main-menu>
    <iframe id="sf-app" class="sf-app__container"></iframe>
    <div
      class="container container--is-main"
      ng-class="{
        'container--is-compact': loggedIn && mainMenuVariant === 'compact',
        'container--is-fixed': loggedIn && mainMenuVariant === 'fixed'
      }"
      ng-view=""
    ></div>
    <feedback></feedback>
    <product-modal></product-modal>
    <facet-filter></facet-filter>
    <loader></loader>

    <!-- build:js(.) scripts/vendor.js -->
    <script src="vendor/twilio/twilio-video.min.js"></script>
    <script src="node_modules/chart.js/dist/chart.umd.js"></script>
    <script src="node_modules/systemjs/dist/s.min.js"></script>
    <script src="node_modules/systemjs/dist/extras/named-register.min.js"></script>
    <script src="node_modules/@babel/polyfill/dist/polyfill.min.js"></script>
    <script src="node_modules/unorm/lib/unorm.js"></script>
    <script src="node_modules/jquery/dist/jquery.min.js"></script>
    <script src="node_modules/lodash/lodash.min.js"></script>
    <script src="node_modules/angular/angular.js"></script>
    <script src="node_modules/aws-sdk/dist/aws-sdk.min.js"></script>
    <script src="node_modules/cloudinary-jquery-file-upload/cloudinary-jquery-file-upload.min.js"></script>
    <script src="node_modules/intl-tel-input/build/js/intlTelInput-jquery.min.js"></script>
    <script src="node_modules/intl-tel-input/build/js/utils.js"></script>
    <script src="node_modules/moment/min/moment-with-locales.min.js"></script>
    <script src="node_modules/moment-timezone/builds/moment-timezone-with-data.min.js"></script>
    <script src="node_modules/i18next/i18next.min.js"></script>
    <script src="node_modules/i18next-xhr-backend/i18nextXHRBackend.min.js"></script>
    <script src="node_modules/hammerjs/hammer.min.js"></script>
    <script src="node_modules/croppie/croppie.min.js"></script>
    <script src="node_modules/classnames/index.js"></script>
    <script src="node_modules/signature_pad/dist/signature_pad.umd.min.js"></script>
    <script src="node_modules/trim-canvas/build/index.js"></script>
    <script src="vendor/croppie/ng-croppie.js"></script>
    <script src="vendor/firebase/firebase.js"></script>
    <script src="vendor/sass-bootstrap/dist/js/bootstrap.min.js"></script>
    <script src="vendor/jquery-ui/jquery-ui.min.js"></script>
    <script src="vendor/jquery.ui.touch-punch.js"></script>
    <script src="vendor/tz-detect.js"></script>
    <script src="vendor/captureconsole.min.js"></script>
    <script src="node_modules/algoliasearch/dist/algoliasearch.angular.min.js"></script>
    <script src="node_modules/angular-animate/angular-animate.min.js"></script>
    <script src="node_modules/angular-cookies/angular-cookies.min.js"></script>
    <script src="node_modules/angular-hammer/angular.hammer.min.js"></script>
    <script src="node_modules/angular-local-storage/dist/angular-local-storage.min.js"></script>
    <script src="node_modules/angular-resource/angular-resource.min.js"></script>
    <script src="node_modules/angular-route/angular-route.min.js"></script>
    <script src="node_modules/angular-sanitize/angular-sanitize.min.js"></script>
    <script src="node_modules/angular-touch/angular-touch.min.js"></script>
    <script src="node_modules/angularfire/dist/angularfire.min.js"></script>
    <script src="node_modules/angularjs-scroll-glue/src/scrollglue.js"></script>
    <script src="node_modules/ng-i18next/dist/ng-i18next.min.js"></script>
    <script src="node_modules/ng-infinite-scroll/build/ng-infinite-scroll.js"></script>
    <script src="vendor/ng-intl-tel-input.min.js"></script>
    <script src="node_modules/ng-onload/release/ng-onload.min.js"></script>
    <script src="vendor/ng-tags-input/ng-tags-input.min.js"></script>
    <script src="node_modules/salesfloor-fetch-jsonapi/dist/salesfloor-fetch-jsonapi.js"></script>
    <script src="node_modules/qrcodejs/qrcode.min.js"></script>
    <script src="vendor/suneditor.fixed.min.js"></script>
    <script src="node_modules/lz-string/libs/lz-string.min.js"></script>
    <script src="node_modules/suneditor/src/lang/fr.js"></script>
    <script src="node_modules/react/umd/react.development.js"></script>
    <script src="node_modules/react-dom/umd/react-dom.development.js"></script>
    <!-- endbuild -->

    <!-- build:js scripts/app.js -->
    <script src="common/utils/permission-utils.js"></script>

    <script src="common/scripts/app-configs.js"></script>

    <script src="common/scripts/utils.js"></script>
    <script src="common/scripts/deeplink.js"></script>
    <script src="common/scripts/deviceready.js"></script>
    <script src="common/scripts/login-utils.js"></script>
    <script src="common/scripts/moment.js"></script>
    <script src="common/scripts/app.js"></script>

    <script src="common/scripts/secureStorage.js"></script>
    <script src="common/scripts/filters/custom-filter.js"></script>
    <script src="common/scripts/filters/live-request-title.js"></script>
    <script src="common/scripts/services/activityLogService.js"></script>
    <script src="common/scripts/services/connectivity.js"></script>
    <script src="common/scripts/services/api.js"></script>
    <script src="common/scripts/services/login.js"></script>
    <script src="common/scripts/services/apiCache.js"></script>
    <script src="common/scripts/services/auth.js"></script>
    <script src="common/scripts/services/calendar.js"></script>
    <script src="common/scripts/services/chat/chat.js"></script>
    <script src="common/scripts/services/chat/chatUtils.js"></script>
    <script src="common/scripts/services/chat/chatEvents.js"></script>
    <script src="common/scripts/services/chat/chatApi.js"></script>
    <script src="common/scripts/services/chat/chatPresence.js"></script>
    <script src="common/scripts/services/chat/chatRequest.js"></script>
    <script src="common/scripts/services/chat/videoChat.js"></script>
    <script src="common/scripts/services/moderation.js"></script>
    <script src="common/scripts/services/cans.js"></script>
    <script src="common/scripts/services/draft.js"></script>
    <script src="common/scripts/services/logger.js"></script>
    <script src="common/scripts/services/config.js"></script>
    <script src="common/scripts/services/contact.js"></script>
    <script src="common/scripts/services/contactFacetFilter.js"></script>
    <script src="common/scripts/services/corporateTask.js"></script>
    <script src="common/scripts/services/groupTask.js"></script>
    <script src="common/scripts/services/address.js"></script>
    <script src="common/scripts/services/device.js"></script>
    <script src="common/scripts/services/emailTemplateValidator.js"></script>
    <script src="common/scripts/services/events.js"></script>
    <script src="common/scripts/services/facetFilter.js"></script>
    <script src="common/scripts/services/featuresEnabled.js"></script>
    <script src="common/scripts/services/feedback.js"></script>
    <script src="common/scripts/services/file.js"></script>
    <script src="common/scripts/services/firebase.js"></script>
    <script src="common/scripts/services/flashMessage.js"></script>
    <script src="common/scripts/services/i18n.js"></script>
    <script src="common/scripts/services/intentService.js"></script>
    <script src="common/scripts/services/jsonapi.js"></script>
    <script src="common/scripts/services/linkService.js"></script>
    <script src="common/scripts/services/loader.js"></script>
    <script src="common/scripts/services/logoService.js"></script>
    <script src="common/scripts/services/lookbooks.js"></script>
    <script src="common/scripts/services/messages.js"></script>
    <script src="common/scripts/services/misc.js"></script>
    <script src="common/scripts/services/navService.js"></script>
    <script src="common/scripts/services/permissions.js"></script>
    <script src="common/scripts/services/products/product.js"></script>
    <script src="common/scripts/services/products/products.js"></script>
    <script src="common/scripts/services/products/productsDrawer.js"></script>
    <script src="common/scripts/services/plainTextFormatter.js"></script>
    <script src="common/scripts/services/pushNotification.js"></script>
    <script src="common/scripts/services/repUploadedPhotos.js"></script>
    <script src="common/scripts/services/reports.js"></script>
    <script src="common/scripts/services/reps.js"></script>
    <script src="common/scripts/services/requests.js"></script>
    <script src="common/scripts/services/requestsPolling.js"></script>
    <script src="common/scripts/services/scrollPosition.js"></script>
    <script src="common/scripts/services/share.js"></script>
    <script src="common/scripts/services/shareToExternalApp.js"></script>
    <script src="common/scripts/services/storageCleanupService.js"></script>
    <script src="common/scripts/services/socialshop.js"></script>
    <script src="common/scripts/services/tasks.js"></script>
    <script src="common/scripts/services/textMessaging.js"></script>
    <script src="common/scripts/services/timezoneService.js"></script>
    <script src="common/scripts/services/tracking.js"></script>
    <script src="common/scripts/services/debounce.js"></script>
    <script src="common/scripts/services/directHttp.js"></script>
    <script src="common/scripts/services/dashboard.js"></script>
    <script src="common/scripts/services/oauth.js"></script>
    <script src="common/scripts/services/associateRelationships.js"></script>
    <script src="common/scripts/services/utils.js"></script>
    <script src="common/scripts/services/assetManagement.js"></script>
    <script src="common/scripts/services/feedValidation.js"></script>
    <script src="common/scripts/services/stringManipulation.js"></script>
    <script src="common/scripts/services/barcodeScanner.js"></script>
    <script src="common/scripts/services/storeAppointmentsHours.js"></script>
    <script src="common/scripts/services/groupedProductsValidator.js"></script>
    <script src="common/scripts/directives/charts/gauge-graph.js"></script>
    <script src="common/scripts/directives/charts/area-graph.js"></script>
    <script src="common/scripts/services/user.js"></script>
    <script src="common/scripts/services/amplitudeTracking.js"></script>
    <script src="common/scripts/directives/datepicker.js"></script>
    <script src="common/scripts/directives/datetimepicker.js"></script>
    <script src="common/scripts/directives/angular-ellipsis.js"></script>
    <script src="common/scripts/directives/input-enter.js"></script>
    <script src="common/scripts/directives/event-message.js"></script>
    <script src="common/scripts/directives/filters.js"></script>
    <script src="common/scripts/directives/facetFilter.js"></script>
    <script src="common/scripts/directives/focusOn.js"></script>
    <script src="common/scripts/directives/onTyping.js"></script>
    <script src="common/scripts/directives/scroller.js"></script>
    <script src="common/scripts/directives/sortable-list.js"></script>
    <script src="common/scripts/directives/feedback.js"></script>
    <script src="common/scripts/directives/productModal.js"></script>
    <script src="common/scripts/directives/flashMessage.js"></script>
    <script src="common/scripts/directives/loader.js"></script>
    <script src="common/scripts/directives/scrollToHere.js"></script>
    <script src="common/scripts/directives/listPagination.js"></script>
    <script src="common/scripts/directives/insertLinkButton.js"></script>
    <script src="common/scripts/directives/highlightTextarea.js"></script>
    <script src="common/scripts/directives/messageLanguageSelector.js"></script>
    <script src="common/scripts/directives/emailTemplateSelector.js"></script>
    <script src="common/scripts/directives/liveLangSwitcher.js"></script>
    <script src="common/scripts/directives/transactionList.js"></script>
    <script src="common/scripts/directives/share/shareAttachments.js"></script>
    <script src="common/scripts/directives/share/sharePreviewItem.js"></script>
    <script src="common/scripts/directives/share/shareSeparator.js"></script>
    <script src="common/scripts/directives/contactTagsList.js"></script>
    <script src="common/scripts/directives/statGraph.js"></script>
    <script src="common/scripts/directives/filterButton.js"></script>
    <script src="common/scripts/directives/autocomplete.js"></script>
    <script src="common/scripts/directives/advancedSearch.js"></script>
    <script src="common/scripts/directives/actionButtons.js"></script>
    <script src="common/scripts/directives/ctaFooter.js"></script>
    <script src="common/scripts/directives/ctaFooterFlexible.js"></script>
    <script src="common/scripts/directives/productsList.js"></script>
    <script src="common/scripts/directives/productsDrawer.js"></script>
    <script src="common/scripts/directives/productsCarousel.js"></script>
    <script src="common/scripts/directives/longpressToCopy.js"></script>
    <script src="common/scripts/directives/selector.js"></script>
    <script src="common/scripts/directives/cardsList.js"></script>
    <script src="common/scripts/directives/invalidVariantMask.js"></script>
    <script src="common/scripts/directives/scrollShrink.js"></script>
    <script src="common/scripts/directives/productsDrawerList.js"></script>
    <script src="common/scripts/directives/clipboardCopyButton.js"></script>
    <script src="common/scripts/directives/checkboxField.js"></script>
    <script src="common/scripts/directives/floatingButton.js"></script>
    <script src="common/scripts/directives/previewButton.js"></script>
    <script src="common/scripts/directives/emptyState.js"></script>
    <script src="common/scripts/directives/searchField.js"></script>
    <script src="common/scripts/directives/pagination.js"></script>
    <script src="common/scripts/directives/reactBridge.js"></script>
    <script src="common/scripts/directives/productHeader.js"></script>
    <script src="common/scripts/directives/product-library/productLibrary.js"></script>
    <script src="common/scripts/directives/product-library/productSearchField.js"></script>
    <script src="common/scripts/directives/product-library/productSearchInfo.js"></script>
    <script src="common/scripts/directives/product-library/productSearchCategories.js"></script>
    <script src="common/scripts/directives/product-library/productSortOptions.js"></script>
    <script src="common/scripts/directives/chatQuickMenu.js"></script>
    <script src="common/scripts/directives/chatCompose.js"></script>
    <script src="common/scripts/directives/chatHistory.js"></script>
    <script src="common/scripts/directives/messagesStore.js"></script>
    <script src="common/scripts/directives/chatRoom.js"></script>
    <script src="common/scripts/directives/bottomDrawer.js"></script>
    <script src="common/scripts/directives/contactAddress.js"></script>
    <script src="common/scripts/directives/dualRangeSlider.js"></script>
    <script src="common/scripts/directives/restrictInput.js"></script>
    <script src="common/scripts/directives/piiEmailValidator.js"></script>
    <script src="common/scripts/directives/pinchZoom.js"></script>
    <script src="common/scripts/directives/clickOutside.js"></script>
    <script src="common/scripts/directives/taskView.js"></script>
    <script src="common/scripts/directives/mainHeaderListing.js"></script>
    <script src="common/scripts/directives/lookbookCreateView.js"></script>
    <script src="common/scripts/directives/lookView.js"></script>
    <script src="common/scripts/directives/textMessageDetails.js"></script>
    <script src="common/scripts/directives/contactDetails.js"></script>
    <script src="common/scripts/directives/aiMessagingButton.js"></script>

    <script src="common/scripts/controllers/connect.js"></script>
    <script src="common/scripts/controllers/login.js"></script>
    <script src="common/scripts/controllers/login/auto.js"></script>
    <script src="common/scripts/controllers/login/sso.js"></script>
    <script src="common/scripts/controllers/login/mfa.js"></script>

    <script src="common/scripts/controllers/share/share.js"></script>
    <script src="common/scripts/controllers/share/share-email.js"></script>
    <script src="common/scripts/controllers/share/share-instagram.js"></script>
    <script src="common/scripts/controllers/share/share-pinterest.js"></script>
    <script src="common/scripts/controllers/share/share-facebook.js"></script>
    <script src="common/scripts/controllers/share/share-twitter.js"></script>
    <script src="common/scripts/controllers/share/product-details.js"></script>
    <script src="common/scripts/controllers/share/product-availability.js"></script>
    <script src="common/scripts/controllers/share/socialshop-list.js"></script>
    <script src="common/scripts/controllers/share/socialshop-post.js"></script>
    <script src="common/scripts/controllers/share/socialshop-settings.js"></script>
    <script src="common/scripts/controllers/share/asset-library.js"></script>
    <script src="common/scripts/controllers/storefront-events.js"></script>
    <script src="common/scripts/controllers/messages-store.js"></script>
    <script src="common/scripts/controllers/messages/messages-compose.js"></script>
    <script src="common/scripts/controllers/messages/messages-train.js"></script>
    <script src="common/scripts/controllers/contacts/contacts.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-create.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-create-view.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-create-edit.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-filter.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-notes.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-import.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-looks.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-address-edit.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-social.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-tags.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-activity-log.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-events.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-pre-customer-actions.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-transactions.js"></script>
    <script src="common/scripts/controllers/contacts/associate-relationships.js"></script>
    <script src="common/scripts/controllers/contacts/look-details.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-section.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-products.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-attributes-edit.js"></script>
    <script src="common/scripts/controllers/requests/requests-add-note.js"></script>
    <script src="common/scripts/controllers/live.js"></script>
    <script src="common/scripts/controllers/reports.js"></script>
    <script src="common/scripts/controllers/chat/chat-thread.js"></script>
    <script src="common/scripts/controllers/chat/chat-history.js"></script>
    <script src="common/scripts/controllers/chat/chat-history-list.js"></script>
    <script src="common/scripts/controllers/chat/chat-transfer-to-cs.js"></script>
    <script src="common/scripts/controllers/storefront.js"></script>
    <script src="common/scripts/controllers/scan.js"></script>
    <script src="common/scripts/controllers/lookbooks.js"></script>
    <script src="common/scripts/controllers/looks.js"></script>
    <script src="common/scripts/controllers/logout.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-compose.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-thread.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-list.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-request-message.js"></script>
    <script src="common/scripts/controllers/text-messaging/compose-message.js"></script>
    <script src="common/scripts/controllers/text-messaging/attachment-dialog.js"></script>
    <script src="common/scripts/controllers/text-messaging/address-book.js"></script>
    <script src="common/scripts/controllers/tasks/tasks-list.js"></script>
    <script src="common/scripts/controllers/tasks/tasks.js"></script>
    <script src="common/scripts/controllers/virtual/virtual-appointment.js"></script>

    <script src="common/scripts/controllers/settings/store-appointments-hours.js"></script>
    <script src="common/scripts/controllers/settings/store-appointments-booking-settings.js"></script>

    <script src="mobile/app/version.js"></script>
    <script src="mobile/app/config-env.js"></script>
    <script src="mobile/app/app.js"></script>

    <script src="mobile/app/common/components/WithPermissions.js"></script>
    <script src="mobile/app/common/components/ArrowPagination.js"></script>
    <script src="mobile/app/common/components/PageContainer.js"></script>
    <script src="mobile/app/common/components/PageContent.js"></script>
    <script src="mobile/app/common/components/PageFooter.js"></script>
    <script src="mobile/app/common/components/PageHeader.js"></script>
    <script src="mobile/app/common/components/AddFormFieldButton.js"></script>
    <script src="mobile/app/common/components/CharacterCounter.js"></script>
    <script src="mobile/app/common/components/CheckboxField.js"></script>
    <script src="mobile/app/common/components/ContentLoadingPlaceholder.js"></script>
    <script src="mobile/app/common/components/EmptyState.js"></script>
    <script src="mobile/app/common/components/Card.js"></script>
    <script src="mobile/app/common/components/SectionCard.js"></script>
    <script src="mobile/app/common/components/LinkButton.js"></script>
    <script src="mobile/app/common/components/ProductCard.js"></script>
    <script src="mobile/app/common/components/RepAvatar.js"></script>
    <script src="mobile/app/common/components/RequestOwner.js"></script>
    <script src="mobile/app/common/components/SignPad.js"></script>
    <script src="mobile/app/common/components/StatusTag.js"></script>
    <script src="mobile/app/common/components/Tabbar.js"></script>
    <script src="mobile/app/common/components/TabbarItem.js"></script>
    <script src="mobile/app/common/components/ToggleNav.js"></script>
    <script src="mobile/app/common/components/ToggleNavTab.js"></script>
    <script src="mobile/app/common/components/ToggleSwitch.js"></script>
    <script src="mobile/app/common/components/PopupMenu.js"></script>
    <script src="mobile/app/common/components/icons/IconRefresh.js"></script>
    <script src="mobile/app/common/components/ViewModeSwitcher.js"></script>
    <script src="mobile/app/common/components/TimeSelector.js"></script>
    <script src="mobile/app/common/components/DatePicker.js"></script>
    <script src="mobile/app/common/components/InputField.js"></script>
    <script src="mobile/app/common/components/ToggleButton.js"></script>
    <script src="mobile/app/common/components/QuickActionMenu.js"></script>
    <script src="mobile/app/common/directives/addFormFieldButton.js"></script>
    <script src="mobile/app/common/directives/arrow-pagination.js"></script>
    <script src="mobile/app/common/directives/toggle-switch.js"></script>
    <script src="mobile/app/common/directives/characterCounter.js"></script>
    <script src="mobile/app/common/directives/product-card.js"></script>
    <script src="mobile/app/common/directives/requestOwner.js"></script>
    <script src="mobile/app/common/directives/viewModeSwitcher.js"></script>
    <script src="mobile/app/common/services/appUpgradeNotification.js"></script>
    <script src="mobile/app/common/services/permissions.js"></script>
    <script src="mobile/app/common/directives/toggleButton.js"></script>
    <script src="mobile/app/common/directives/sunEditor.js"></script>

    <script src="mobile/app/dashboard/dashboard.js"></script>
    <script src="mobile/app/dashboard/components/Dashboard.js"></script>
    <script src="mobile/app/dashboard/components/Header.js"></script>
    <script src="mobile/app/dashboard/components/LiveChatButton.js"></script>
    <script src="mobile/app/dashboard/components/ReturnToChatButton.js"></script>
    <script src="mobile/app/dashboard/components/Tabbar.js"></script>
    <script src="mobile/app/dashboard/components/TabbarItem.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconHub.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconLogout.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconSettings.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconStoreFront.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconSubMenu.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconSupport.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/IconQRCode.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/MainMenu.js"></script>
    <script src="mobile/app/dashboard/components/main-menu/MainMenuQuickActionMenu.js"></script>
    <script src="mobile/app/dashboard/components/modules/common/MessageLoadingPlaceholder.js"></script>
    <script src="mobile/app/dashboard/components/modules/common/MessageListEmptyState.js"></script>
    <script src="mobile/app/dashboard/components/modules/common/Module.js"></script>
    <script src="mobile/app/dashboard/components/modules/common/panel.js"></script>
    <script src="mobile/app/dashboard/components/modules/appointments/Appointment.js"></script>
    <script src="mobile/app/dashboard/components/modules/appointments/Appointments.js"></script>
    <script src="mobile/app/dashboard/components/modules/kpis/IconRankStar.js"></script>
    <script src="mobile/app/dashboard/components/modules/kpis/KPIsLoadingPlaceholder.js"></script>
    <script src="mobile/app/dashboard/components/modules/kpis/KPICard.js"></script>
    <script src="mobile/app/dashboard/components/modules/kpis/KPIRank.js"></script>
    <script src="mobile/app/dashboard/components/modules/kpis/KPIs.js"></script>
    <script src="mobile/app/dashboard/components/modules/messages/Message.js"></script>
    <script src="mobile/app/dashboard/components/modules/messages/Messages.js"></script>
    <script src="mobile/app/dashboard/components/modules/requests/IconRequestAppointment.js"></script>
    <script src="mobile/app/dashboard/components/modules/requests/IconRequestContactMe.js"></script>
    <script src="mobile/app/dashboard/components/modules/requests/IconRequestShopper.js"></script>
    <script src="mobile/app/dashboard/components/modules/requests/CustomerRequest.js"></script>
    <script src="mobile/app/dashboard/components/modules/requests/Lead.js"></script>
    <script src="mobile/app/dashboard/components/modules/requests/Requests.js"></script>
    <script src="mobile/app/dashboard/components/modules/tasks/TasksLoadingPlaceholder.js"></script>
    <script src="mobile/app/dashboard/components/modules/tasks/TaskCount.js"></script>
    <script src="mobile/app/dashboard/components/modules/tasks/Task.js"></script>
    <script src="mobile/app/dashboard/components/modules/tasks/Tasks.js"></script>
    <script src="mobile/app/dashboard/directives/dashboard.js"></script>
    <script src="mobile/app/dashboard/components/HubCircle.js"></script>
    <script src="mobile/app/dashboard/directives/hubCircle.js"></script>
    <script src="mobile/app/dashboard/directives/mainMenu.js"></script>

    <!--MFA-->
    <script src="mobile/app/login/directives/mfaVerification.js"></script>
    <script src="mobile/app/login/components/MFAVerification.js"></script>

    <script src="mobile/app/appointments/appointments.js"></script>
    <script src="mobile/app/appointments/create.js"></script>
    <script src="mobile/app/appointments/cancel.js"></script>
    <script src="mobile/app/appointments/components/RadioButtonField.js"></script>
    <script src="mobile/app/appointments/directives/radioButtonField.js"></script>

    <script src="mobile/app/chat/components/ConnectButton.js"></script>
    <script src="mobile/app/chat/directives/connectButton.js"></script>
    <script src="mobile/app/chat/components/ConditionsOverlay.js"></script>
    <script src="mobile/app/chat/directives/conditionsOverlay.js"></script>
    <script src="mobile/app/chat/components/VideoChat.js"></script>
    <script src="mobile/app/chat/directives/videoChat.js"></script>

    <script src="mobile/app/events/events.js"></script>
    <script src="mobile/app/events/create.js"></script>
    <script src="mobile/app/events/view.js"></script>
    <script src="mobile/app/events/directives/eventCard.js"></script>
    <script src="mobile/app/events/directives/eventDetailsCards.js"></script>
    <script src="mobile/app/events/components/EventCard.js"></script>
    <script src="mobile/app/events/components/EventSectionCard.js"></script>
    <script src="mobile/app/events/components/EventDetailsCards.js"></script>
    <script src="mobile/app/events/components/MarketingSectionCard.js"></script>
    <script src="mobile/app/events/components/ImageSectionCard.js"></script>
    <script src="mobile/app/events/components/AddressSectionCard.js"></script>
    <script src="mobile/app/events/components/VideoSectionCard.js"></script>
    <script src="mobile/app/events/components/SpecialtiesSectionCard.js"></script>
    <script src="mobile/app/events/components/EventParticipantsCard.js"></script>

    <script src="mobile/app/settings/components/StoreAppointmentsHours.js"></script>
    <script src="mobile/app/settings/components/StoreAppointmentsHoursEdit.js"></script>
    <script src="mobile/app/settings/components/StoreAppointmentsDateOverride.js"></script>
    <script src="mobile/app/settings/components/CreateStoreAppointmentsDateOverride.js"></script>
    <script src="mobile/app/settings/components/StoreAppointmentsBookingSlotsSettings.js"></script>
    <script src="mobile/app/settings/directives/storeAppointmentsHoursEdit.js"></script>
    <script src="mobile/app/settings/directives/storeAppointmentsHours.js"></script>
    <script src="mobile/app/settings/directives/storeAppointmentsDateOverride.js"></script>
    <script src="mobile/app/settings/directives/createStoreAppointmentsDateOverride.js"></script>
    <script src="mobile/app/settings/directives/storeAppointmentsBookingSlotsSettings.js"></script>

    <script src="mobile/app/contacts/components/ContactItem.js"></script>
    <script src="mobile/app/contacts/components/ContactItemTitle.js"></script>
    <script src="mobile/app/contacts/components/ContactItemValue.js"></script>
    <script src="mobile/app/contacts/components/ContactAttributePanel.js"></script>
    <script src="mobile/app/contacts/components/ContactSectionLink.js"></script>
    <script src="mobile/app/contacts/components/ContactAssociateRelationships.js"></script>
    <script src="mobile/app/contacts/components/AssociateRelationship.js"></script>
    <script src="mobile/app/contacts/components/AssociateRelationships.js"></script>
    <script src="mobile/app/contacts/components/AssociateRelationshipsContactItem.js"></script>
    <script src="mobile/app/contacts/components/ContactStatsData.js"></script>
    <script src="mobile/app/contacts/components/ContactStatsPanels.js"></script>
    <script src="mobile/app/contacts/components/LookDetails.js"></script>
    <script src="mobile/app/contacts/components/ProductLooks.js"></script>
    <script src="mobile/app/contacts/components/ProductTransaction.js"></script>
    <script src="mobile/app/contacts/components/SubscriptionConsent.js"></script>
    <script src="mobile/app/contacts/components/ContactAttributeEdit.js"></script>
    <script src="mobile/app/contacts/directives/contactAttributePanel.js"></script>
    <script src="mobile/app/contacts/directives/contactSectionLink.js"></script>
    <script src="mobile/app/contacts/directives/contactAssociateRelationships.js"></script>
    <script src="mobile/app/contacts/directives/associateRelationships.js"></script>
    <script src="mobile/app/contacts/directives/contactStatsPanels.js"></script>
    <script src="mobile/app/contacts/directives/productLooks.js"></script>
    <script src="mobile/app/contacts/directives/lookDetails.js"></script>
    <script src="mobile/app/contacts/directives/subscriptionConsent.js"></script>
    <script src="mobile/app/contacts/directives/contactAttributeEdit.js"></script>

    <script src="mobile/app/shop/shop-capture-payment.js"></script>
    <script src="mobile/app/shop/shop-cart.js"></script>
    <script src="mobile/app/shop/shop-customer-details.js"></script>
    <script src="mobile/app/shop/shop-fulfillment-method.js"></script>
    <script src="mobile/app/shop/shop-home.js"></script>
    <script src="mobile/app/shop/shop-order-confirmation.js"></script>
    <script src="mobile/app/shop/shop-order-review.js"></script>
    <script src="mobile/app/shop/shop-orders.js"></script>
    <script src="mobile/app/shop/shop-process-payment.js"></script>
    <script src="mobile/app/shop/shop-product-display.js"></script>
    <script src="mobile/app/shop/shop-products-scan.js"></script>
    <script src="mobile/app/shop/shop-products.js"></script>
    <script src="mobile/app/shop/shop-shipping-address.js"></script>
    <script src="mobile/app/shop/shop-shipping-options.js"></script>
    <script src="mobile/app/shop/components/ShopOrderConfirmation.js"></script>
    <script src="mobile/app/shop/components/ShopTabbar.js"></script>
    <script src="mobile/app/shop/directives/cart-extended-menu.js"></script>
    <script src="mobile/app/shop/directives/cart-item.js"></script>
    <script src="mobile/app/shop/directives/grouped-order-list.js"></script>
    <script src="mobile/app/shop/directives/shopTabbar.js"></script>
    <script src="mobile/app/shop/services/order.js"></script>
    <script src="mobile/app/qr-code/qr-code.js"></script>
    <script src="mobile/app/qr-code/directives/qrCodeStorefrontCard.js"></script>
    <script src="mobile/app/qr-code/components/QRCodeCard.js"></script>
    <script src="mobile/app/qr-code/components/QRCodeStorefrontCard.js"></script>
    <script src="mobile/app/qr-code/components/QRCodeImage.js"></script>
    <!-- endbuild -->
    <!--
         Use the sfx bundle for real build
         We need to find a way to easily switch from the import
         to the bundle each time we package the app
    -->
    <script src="mobile-react/bundle/mobile-react.min.js"></script>
    <script>
      (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        (i[r] =
          i[r] ||
          function () {
            (i[r].q = i[r].q || []).push(arguments);
          }),
          (i[r].l = 1 * new Date());
        (a = s.createElement(o)), (m = s.getElementsByTagName(o)[0]);
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m);
      })(window, document, 'script', 'scripts/analytics.js', 'ga');
    </script>

    <script src="./sf-mobile/services.min.js"></script>
    <script>
      // oauth.js is expecting `window.jQuery`
      if (!window.jQuery && window.$ && window.$.fn && window.$.fn.jquery) {
        window.jQuery = window.$;
      }

      const sfAppTypes = {
        mobile: 'mobile',
        desktop: 'desktop',
      };

      const storageTypes = {
        [sfAppTypes.mobile]: 'localStorage',
        [sfAppTypes.desktop]: 'sessionStorage',
      };

      window.name = 'angular-mobile';
      window.sfAppFrame = document.getElementById('sf-app');

      // Specify all the services which was moved to the new react app and should be loaded in angularjs as well
      function loadServices() {
        return Promise.all([
          System.import('logger'),
          System.import('sound'),
          System.import('storage'),
          System.import('sessionManager'),
          System.import('mediaQuery'),
        ]);
      }

      function bootstrapApp(options) {
        loadServices()
          .then(([logger, sound, storage, sessionManager, mediaQuery]) => {
            window.globalStorageService = storage.default.Instance(
              storageTypes[window.sfApp],
            );
            angular
              .module('sfmobileApp')
              .factory('loggerService', [
                '$rootScope',
                '$window',
                'deviceService',
                'flashMessageService',
                (a, b, c, d) => new logger.default(a, b, c, d),
              ])
              .factory('soundService', [
                '$window',
                'deviceService',
                (a, b) => new sound.default(a, b),
              ])
              .factory('storageService', [
                () => storage.default.Instance(storageTypes[window.sfApp]),
              ])
              .factory('sessionManagerService', [
                'directHttp',
                a => new sessionManager.default(a),
              ])
              .factory('mediaQueryService', [() => new mediaQuery.default()]);
            angular.bootstrap(document, ['sfmobileApp'], options || {});
          })
          .catch(err => console.error(err));
      }

      function UrlExists(url) {
        var http = new XMLHttpRequest();
        http.open('HEAD', url, false);
        http.send();
        return http.status != 404;
      }

      function loadScript(src) {
        return new Promise((resolve, reject) => {
          const newScript = document.createElement('script');
          newScript.src = src;
          newScript.type = 'text/javascript';
          newScript.onload = () => resolve(src);
          newScript.onerror = () => reject(new Error(`Failed to load ${src}`));
          document.currentScript.parentNode.insertBefore(
            newScript,
            document.currentScript,
          );
        });
      }

      function initializeAmplitude() {
        window.amplitude.add(window.sessionReplay.plugin({ sampleRate: 1 }));
        window.amplitude.init('6809abfe8a30ce5b0c68165214f08f29', {
          fetchRemoteConfig: true,
          autocapture: false,
        });
      }

      if (!UrlExists('cordova.js')) {
        loadScript(
          'https://cdn.amplitude.com/script/6809abfe8a30ce5b0c68165214f08f29.js',
        )
          .then(() => initializeAmplitude())
          .catch(error => console.error('[index.html]', error));
        window.sfApp = sfAppTypes.desktop;
        sfAppFrame.src =
          window.location.origin +
          window.location.pathname +
          'sf-mobile/index.html';
        sfAppFrame.onload = () => bootstrapApp({ strictDi: true });
      } else {
        window.sfApp = sfAppTypes.mobile;
        sfAppFrame.src =
          window.location.origin + '/sf-mobile' + window.location.pathname;
        loadScript('cordova.js')
          .then(() =>
            document.addEventListener('deviceready', bootstrapApp, false),
          )
          .catch(error => console.error('[index.html]', error));
      }
    </script>
  </body>
</html>
