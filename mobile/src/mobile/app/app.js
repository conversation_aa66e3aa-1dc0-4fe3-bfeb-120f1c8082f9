angular.module('sfmobileApp')
  .factory('$exceptionHandler', ['$log', function ($log) {
    return function sfExceptionHandler(exception) {
      $log.warn(exception);
    };
  }])
  .config(($routeProvider, $locationProvider, $compileProvider) => {
    $compileProvider.imgSrcSanitizationWhitelist(/^\s*(local|http|https|app|tel|ftp|file|blob|content|ms-appx|x-wmapp0|cdvfile):|data:image\//);
    $locationProvider.html5Mode(false);
    $locationProvider.hashPrefix('');
    const resolves = {
      currentLocale(i18nService) {
        'ngInject';
        return i18nService.currentLocale();
      },
    };

    const resolvesAuth = angular.extend({
      auth($q, loginService) {
        'ngInject';
        return $q.when(loginService.isAuthenticated());
      },
    }, resolves);

    const resolvesChat = angular.extend({
      chatInit(chatService) {
        'ngInject';
        return chatService.defer.promise;
      },
    }, resolvesAuth);

    const resolvesShop = angular.extend({
      activeOrder(orderService) {
        'ngInject';
        return orderService.getActiveOrder();
      },
    }, resolvesAuth);

    $routeProvider
      .when('/login', {
        templateUrl: 'views/auth/login.html',
        controller: 'LoginCtrl',
        resolve: resolves,
      })
      // Connect screen
      .when('/connect', {
        templateUrl: 'views/auth/connect-screen.html',
        controller: 'connect',
        resolve: resolves
      })
      .when('/login/sso', {
        templateUrl: 'views/auth/sso.html',
        controller: 'SSOLoginCtrl',
        resolve: resolves,
      })
      .when('/autologin', {
        templateUrl: 'views/auth/blank.html',
        controller: 'AutologinCtrl',
        resolve: resolves
      })
      .when('/login/mfa', {
        templateUrl: 'mobile/app/login/mfa-login.html',
        controller: 'MFALoginCtrl',
        resolve: resolves
      })
      .when('/logout/:refresh', {
        templateUrl: 'views/auth/logout.html',
        controller: 'logout',
        resolve: resolvesAuth
      })
      .when('/logout', {
        templateUrl: 'views/auth/logout.html',
        controller: 'logout',
        resolve: resolvesAuth
      })
      .when('/dashboard', {
        templateUrl: 'mobile/app/dashboard/dashboard.html',
        controller: 'Dashboard',
        resolve: resolvesAuth
      })
      // Reset password screen
      .when('/password-reset', {
        templateUrl: 'views/auth/password-reset.html',
        controller: 'LoginCtrl',
        resolve: resolves
      })
      // ### Share an update
      // Share an update screen
      .when('/share', {
        templateUrl: 'views/share/share.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // Share - select search mode
      .when('/share/search', {
        templateUrl: 'views/share/share-search.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // Share - select search mode
      .when('/share/search/:page', {
        templateUrl: 'views/share/share-search.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // Favorites screen
      .when('/share/favorites', {
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // Share an update screen
      .when('/share/products/:id', {
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // Search products
      .when('/share/products/:page', {
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      .when('/share/products/:department/:page', {    //L1
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      .when('/share/products/:department/:category/:page', { //L2
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      .when('/share/products/:department/:category/:subCategoryId/:page', { //L3
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      .when('/share/products/:department/:category/:subCategoryId/:subCategoryChildId/:page', { //L4
        templateUrl: 'views/share/share-products.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // product details screen
      .when('/share/product/:productId', {
        templateUrl: 'views/share/product-details-view.html',
        controller: 'ProductDetails',
        resolve: resolvesAuth
      })
      .when('/share/product/availability/:productId', {
        templateUrl: 'views/share/product-availability.html',
        controller: 'ProductAvailability',
        resolve: resolvesAuth
      })
      .when('/share/assets/:id', {
        templateUrl: 'views/share/share-assets.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // Share an update screen
      .when('/share/:subview/:id?', {
        templateUrl: 'views/share/share-dept.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      .when('/share/:subview/:department/:id/:level?', { // inside the L2 of product categories
        templateUrl: 'views/share/share-dept.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      .when('/share/:subview/:department/:subCategoryId/:id/:level?', { // inside the L3 of product categories
        templateUrl: 'views/share/share-dept.html',
        controller: 'share',
        resolve: resolvesAuth
      })
      // ### Contacts
      // Contact list screen
      .when('/contacts', {
        templateUrl: 'views/contacts.html',
        controller: 'contacts',
        resolve: resolvesAuth
      })
      // Single contact screen
      .when('/contacts/view/:id', {
        templateUrl: 'views/contacts-single.html',
        controller: 'contacts',
        reloadOnSearch: false,
        resolve: resolvesAuth
      })
      // Associate Relationships
      .when('/contacts/associate-relationships/:id', {
        templateUrl: 'views/contacts/associate-relationships.html',
        controller: 'AssociateRelationships',
        resolve: resolvesAuth
      })
      // Single contact transaction history screen
      .when('/contacts/transactions/:view/:id', {
        templateUrl: 'views/contacts/transactions.html',
        controller: 'contacts-transactions',
        resolve: resolvesAuth
      })
      // Creation a contact screen
      .when('/contacts/create', {
        templateUrl: 'views/contacts/contacts-create.html',
        controller: 'ContactsCreate',
        resolve: resolvesAuth
      })
      // Import device contacts
      .when('/contacts/import', {
        templateUrl: 'views/contacts/import.html',
        controller: 'contacts-import',
        resolve: resolvesAuth
      })
      // Edit contact screen
      .when('/contacts/create/:id', {
        templateUrl: 'views/contacts/contacts-create.html',
        controller: 'ContactsCreate',
        resolve: resolvesAuth
      })
      // Notes listing
      .when('/contacts/notes/:id', {
        templateUrl: 'views/contacts/notes-list.html',
        controller: 'contacts-notes',
        resolve: resolvesAuth
      })
      // Single contact notes
      .when('/contacts/notes/edit/:id', {
        templateUrl: 'views/contacts/notes-create.html',
        controller: 'contacts-notes',
        resolve: resolvesAuth
      })
      // Contact add note
      .when('/contacts/notes/create/:id', {
        templateUrl: 'views/contacts/notes-create.html',
        controller: 'contacts-notes',
        resolve: resolvesAuth
      })
      // Single contact address create
      .when('/contacts/address/:customer_id/create', {
        templateUrl: 'views/contacts/address-edit.html',
        controller: 'contacts-address-edit',
        resolve: resolvesAuth
      })
      // Single contact address edit/view
      .when('/contacts/address/:customer_id/edit/:address_id', {
        templateUrl: 'views/contacts/address-edit.html',
        controller: 'contacts-address-edit',
        resolve: resolvesAuth
      })
      // Single contact socials
      .when('/contacts/social/:customer_id', {
        templateUrl: 'views/contacts/social.html',
        controller: 'contacts-social',
        resolve: resolvesAuth
      })
      // Single contact events
      .when('/contacts/events/:customer_id', {
        templateUrl: 'views/contacts/events.html',
        controller: 'contacts-events',
        resolve: resolvesAuth
      })
      // Contact tags screen
      .when('/contacts/tags/:customer_id', {
        templateUrl: 'views/contacts/tags.html',
        controller: 'contacts-tags',
        resolve: resolvesAuth
      })
      // Contact activity log screen
      .when('/contacts/activity-log/:customer_id', {
        templateUrl: 'views/contacts/activity-log.html',
        controller: 'contacts-activity-log',
        resolve: resolvesAuth
      })
      // Contact activity log screen
      .when('/contacts/pre-customer-actions', {
        templateUrl: 'views/contacts/pre-customer-actions.html',
        controller: 'contacts-pre-customer-actions',
        resolve: resolvesAuth
      })
      // Contact attributs edit screen
      .when('/contacts/attributes', {
        templateUrl: 'views/contacts/contacts-attributes-edit.html',
        controller: 'contacts-attributes-edit',
        resolve: resolvesAuth
      })

      // ### Store Msg
      .when('/store-messages', {
        templateUrl: 'views/messages-store/messages.html',
        controller: 'messages-store',
        resolve: resolvesAuth
      })
      .when('/store-messages/:subview', {
        templateUrl: 'views/messages-store/messages-compose.html',
        controller: 'messages-compose',
        resolve: resolvesAuth
      })
      .when('/store-messages/:subview/:id', {
        templateUrl: 'views/messages-store/messages-single-default.html',
        controller: 'messages-store',
        resolve: resolvesAuth
      })

      // ### Store Req
      .when('/store-requests', {
        templateUrl: 'views/messages-store/messages.html',
        controller: 'messages-store',
        resolve: resolvesAuth
      })
      .when('/store-requests/add-note/:subview/:id', {
        templateUrl: 'views/messages-store/add-note.html',
        controller: 'requests-add-note',
        resolve: resolvesAuth
      })
      .when('/store-requests/new-time/:subview/:id', {
        templateUrl: 'views/messages-store/propose-new-time.html',
        controller: 'messages-store',
        resolve: resolvesAuth
      })
      .when('/store-requests/reply-request/:subview/:id', {
        templateUrl: 'views/messages-store/messages-compose.html',
        controller: 'messages-compose',
        resolve: resolvesAuth
      })
      .when('/store-requests/:subview/:id', {
        templateUrl: 'views/messages-store/messages-single-request.html',
        controller: 'messages-store',
        resolve: resolvesAuth
      })
      .when('/new-lead/:subview/:type/:id', {
        templateUrl: 'views/messages-store/messages-single-request.html',
        controller: 'messages-store',
        resolve: resolvesAuth
      })
      // ### Chat
      .when('/chat/room/:id', {
        templateUrl: 'views/chat/chat-thread.html',
        controller: 'chatThread',
        resolve: resolvesChat
      })
      .when('/chat-history', {
        templateUrl: 'views/chat/chat-history-list.html',
        controller: 'chatHistoryList',
        resolve: resolvesAuth
      })
      .when('/chat-history/room/:id', {
        templateUrl: 'views/chat/chat-thread.html',
        controller: 'chatHistory',
        resolve: resolvesAuth
      })
      .when('/chat-transfer-to-cs', {
        templateUrl: 'views/chat/chat-transfer-to-cs.html',
        controller: 'chat-transfer-to-cs',
        resolve: resolvesAuth
      })
      // ### Live req
      // Live requests screen
      .when('/live', {
        templateUrl: 'views/live.html',
        controller: 'live',
        resolve: resolvesAuth
      })

      // ### Other
      // Reports screen
      .when('/reporting', {
        templateUrl: 'views/reporting/main.html',
        controller: 'reports',
        resolve: resolves
      })

      // Events screen
      .when('/event/create', {
        templateUrl: 'views/events/event-view.html',
        controller: 'storefront-events',
        resolve: resolves
      })
      .when('/storefront-events', {
        templateUrl: 'views/events/event-list.html',
        controller: 'storefront-events',
        resolve: resolves,
      })
      .when('/event/view/:id', {
        templateUrl: 'views/events/event-view.html',
        controller: 'storefront-events',
        resolve: resolves
      })
      .when('/event/edit/:id', {
        templateUrl: 'views/events/event-view.html',
        controller: 'storefront-events',
        resolve: resolves
      })

      .when('/storefront', {
        templateUrl: 'views/storefront.html',
        controller: 'storefront',
        resolve: resolves
      })

      .when('/storefront/:type', {
        templateUrl: 'views/storefront.html',
        controller: 'storefront',
        resolve: resolves
      })

      .when('/storefront/comments/:sku', {
        templateUrl: 'views/storefront-comments.html',
        controller: 'storefront',
        resolve: resolves
      })

      .when('/scan', {
        templateUrl: 'views/scan.html',
        controller: 'scan',
        resolve: resolves
      })

      // Lookbooks
      .when('/lookbooks', {
        templateUrl: 'views/lookbooks/lookbooks.html',
        controller: 'lookbooks',
        resolve: resolves
      })

      .when('/lookbooks/selection/:lookId', {
        templateUrl: 'views/lookbooks/lookbooks.html',
        controller: 'lookbooks',
        resolve: resolves
      })

      .when('/lookbooks/create', {
        templateUrl: 'views/lookbooks/create.html',
        controller: 'lookbooks',
        resolve: resolves
      })

      .when('/lookbooks/create/:lookbookId', {
        templateUrl: 'views/lookbooks/create.html',
        controller: 'lookbooks',
        resolve: resolves
      })

      // Look
      .when('/looks', {
        templateUrl: 'views/lookbooks/looks.html',
        controller: 'looks',
        resolve: resolves
      })

      .when('/looks/selection/:lookbookId', {
        templateUrl: 'views/lookbooks/looks.html',
        controller: 'looks',
        resolve: resolves
      })

      .when('/looks/:lookId', {
        templateUrl: 'views/lookbooks/look.html',
        controller: 'looks',
        resolve: resolves
      })

      .when('/looks/create', {
        templateUrl: 'views/lookbooks/look.html',
        controller: 'looks',
        resolve: resolves
      })

      .when('/looks/comments/:sku', {
        templateUrl: 'views/lookbooks/product-comments.html',
        controller: 'looks',
        resolve: resolves
      })

      .when('/product-search', {
        templateUrl: 'views/product-search/filters.html',
        controller: 'product-search',
        resolve: resolves
      })

      .when('/address-book', {
        templateUrl: 'views/address-book/list.html',
        controller: 'address-book',
        resolve: resolves
      })

      // Text messaging
      .when('/text-messaging', {
        templateUrl: 'views/text-messaging/list.html',
        controller: 'text-messaging-list',
        resolve: resolves
      })

      .when('/text-messaging/compose', {
        templateUrl: 'views/text-messaging/compose.html',
        controller: 'text-messaging-compose',
        resolve: resolves
      })

      .when('/text-messaging/thread/:threadId', {
        templateUrl: 'views/text-messaging/thread.html',
        controller: 'text-messaging-thread',
        resolve: resolves
      })

      .when('/text-messaging/thread', {
        templateUrl: 'views/text-messaging/thread.html',
        controller: 'text-messaging-thread',
        resolve: resolves
      })

      // Tasks
      .when('/tasks', {
        templateUrl: 'views/tasks/list.html',
        controller: 'tasks-list',
        resolve: resolves
      })
      .when('/group-tasks', {
        templateUrl: 'views/tasks/list.html',
        controller: 'tasks-list',
        resolve: resolves
      })
      .when('/tasks/create', {
        templateUrl: 'views/tasks/create.html',
        controller: 'tasks',
        resolve: resolves
      })
      .when('/tasks/edit/:taskId', {
        templateUrl: 'views/tasks/create.html',
        controller: 'tasks',
        resolve: resolves
      })
      .when('/tasks/view/:taskId', {
        templateUrl: 'views/tasks/view.html',
        controller: 'tasks',
        resolve: resolves
      })

      // Virtual Appointment
      .when('/virtual-appointment/:virtualId', {
        templateUrl: 'views/chat/chat-thread.html',
        controller: 'virtualAppointment',
        resolve: resolves
      })

      // SocialShop
      .when('/socialshop', {
        templateUrl: 'views/socialshop/list.html',
        controller: 'socialshopList',
        resolve: resolves
      })
      .when('/socialshop/settings', {
        templateUrl: 'views/socialshop/settings.html',
        controller: 'socialshopSettings',
        resolve: resolves
      })
      .when('/socialshop/:postId', {
        templateUrl: 'views/socialshop/post.html',
        controller: 'socialshopPost',
        resolve: resolves
      })

      .when('/settings/schedule', {
        templateUrl: 'views/settings/store-appointments-hours.html',
        controller: 'storeAppointmentsHours',
        resolve: resolves
      })

      .when('/settings/schedule/create', {
        templateUrl: 'views/settings/create-store-appointments-override.html',
        controller: 'storeAppointmentsHours',
        resolve: resolves
      })

      .when('/settings/schedule/settings', {
        templateUrl: 'views/settings/store-appointments-booking-settings.html',
        controller: 'storeAppointmentsBookingSettings',
        resolve: resolves
      })

      .when('/rep-onboarding/:page', {})
      .when('/settings/:page', {})
      .when('/settings', {})

      .when('/requests/appointments', {
        templateUrl: 'mobile/app/appointments/appointments.html',
        controller: 'appointments',
        resolve: resolves
      })
      .when('/requests/appointments/create', {
        templateUrl: 'mobile/app/appointments/create.html',
        controller: 'AppointmentsCreate',
        resolve: resolves
      })
      .when('/requests/appointments/cancel', {
        templateUrl: 'mobile/app/appointments/cancel.html',
        controller: 'AppointmentsCancel',
        resolve: resolves
      })
      .when('/manager/store-requests', {
        templateUrl: 'views/messages-store/messages.html',
        controller: 'messages-store',
        resolve: resolves
      })
      .when('/manager/tasks', {
        templateUrl: 'views/tasks/list.html',
        controller: 'tasks-list',
        resolve: resolves
      })
      .when('/manager/appointments', {
        templateUrl: 'mobile/app/appointments/appointments.html',
        controller: 'appointments',
        resolve: resolves
      })
      .when('/manager/events', {
        templateUrl: 'mobile/app/events/events.html',
        controller: 'Events',
        resolve: resolves
      })
      .when('/events/create', {
        templateUrl: 'mobile/app/events/create.html',
        controller: 'EventsCreate',
        resolve: resolves
      })
      .when('/events/edit/:id', {
        templateUrl: 'mobile/app/events/create.html',
        controller: 'EventsCreate',
        resolve: resolves
      })
      .when('/events/view/:id', {
        templateUrl: 'mobile/app/events/view.html',
        controller: 'EventsView',
        resolve: resolves
      })
      .when('/qr-code', {
        templateUrl: 'mobile/app/qr-code/qr-code.html',
        controller: 'QRCode',
        resolve: resolves
      })
      .when('/shop', {
        redirectTo: '/shop/home'
      })
      .when('/shop/home', {
        templateUrl: 'mobile/app/shop/shop-home.html',
        controller: 'ShopHome',
        resolve: resolvesShop
      })
      .when('/shop/products/scan', {
        templateUrl: 'mobile/app/shop/shop-products-scan.html',
        controller: 'scan',
        resolve: resolvesShop
      })
      .when('/shop/products', {
        templateUrl: 'mobile/app/shop/shop-products.html',
        controller: 'ShopProducts',
        resolve: resolvesShop
      })
      .when('/shop/product/:productId', {
        templateUrl: 'mobile/app/shop/shop-product-display.html',
        controller: 'ShopProductDisplay',
        resolve: resolvesShop
      })
      .when('/shop/customers', {
        templateUrl: 'mobile/app/shop/shop-customers.html',
        controller: 'contacts',
        resolve: resolvesShop,
      })
      .when('/shop/customers/:id', {
        templateUrl: 'mobile/app/shop/shop-customer-details.html',
        controller: 'contacts',
        resolve: resolvesShop,
      })
      .when('/shop/orders', {
        templateUrl: 'mobile/app/shop/shop-orders.html',
        controller: 'ShopOrders',
        resolve: resolvesShop,
      })
      .when('/shop/shipping-address', {
        templateUrl: 'mobile/app/shop/shop-shipping-address.html',
        controller: 'ShopShippingAddress',
        resolve: resolvesShop
      })
      .when('/shop/fulfilment-method', {
        templateUrl: 'mobile/app/shop/shop-fulfillment-method.html',
        controller: 'ShopFulfilmentMethod',
        resolve: resolvesShop
      })
      .when('/shop/shipping-options', {
        templateUrl: 'mobile/app/shop/shop-shipping-options.html',
        controller: 'ShopShippingOptions',
        resolve: resolvesShop
      })
      .when('/shop/cart', {
        templateUrl: 'mobile/app/shop/shop-cart.html',
        controller: 'ShopCart',
        resolve: resolvesShop
      })
      .when('/shop/order-review', {
        templateUrl: 'mobile/app/shop/shop-order-review.html',
        controller: 'ShopOrderReview',
        resolve: resolvesShop
      })
      .when('/shop/orders/:id', {
        templateUrl: 'mobile/app/shop/shop-order-review.html',
        controller: 'ShopOrderReview',
        resolve: resolvesShop
      })
      .when('/shop/capture-payment', {
        templateUrl: 'mobile/app/shop/shop-capture-payment.html',
        controller: 'ShopCapturePayment',
        resolve: resolvesShop
      })
      .when('/shop/process-payment', {
        templateUrl: 'mobile/app/shop/shop-process-payment.html',
        controller: 'ShopProcessPayment',
        resolve: resolvesShop
      })
      .when('/shop/order-confirmation', {
        templateUrl: 'mobile/app/shop/shop-order-confirmation.html',
        controller: 'ShopOrderConfirmation',
        resolve: resolvesShop
      })
      .when('/contacts/products', {
        templateUrl: 'views/contacts/products-sections.html',
        controller: 'contacts-products',
        resolve: resolves
      })
      .when('/contacts/looks/:id', {
        templateUrl: 'views/contacts/contacts-looks.html',
        controller: 'contacts-looks',
        resolve: resolves
      })
      .when('/contacts/look', {
        templateUrl: 'views/contacts/look-details.html',
        controller: 'look-details',
        resolve: resolves
      })
      .when('/contacts/sections/:id', {
        templateUrl: 'views/contacts/contacts-section.html',
        controller: 'contacts-section',
        resolve: resolves
      })
      .otherwise({
        redirectTo: '/dashboard'
      });
  })
  .run(($rootScope, navService) => {
    //If the route change failed due to authentication error, redirect them out
    $rootScope.$on('$routeChangeError', (_event, _current, _previous, rejection) => {
      if (rejection === '401') {
        navService.goTo('connect');
      }
    });
  })
  .constant('CONST_SHOP', {
    HOME_PAGE_ORDERS_COUNT: 5
  })
  .constant('PAGINATION_TEMPLATE_URL',
    'mobile/app/common/directives/pagination.html'
  )
  .constant('_', window._)
  .run((
    $http,
    $route,
    $interval,
    $location,
    $window,
    $timeout,
    $rootScope,
    deviceService,
    apiService,
    configService,
    secureStorageService,
    permissionService,
    featuresEnabledService,
    chatService,
    pushNotificationService,
    requestsPollingService,
    connectivityService,
    deeplinkService,
    feedbackService,
    loggerService,
    shareService,
    i18nService,
    navService,
    loaderService,
    trackingService,
    chatPresenceService,
    CANSService,  
    localStorageService,
    storageService,
    firebaseService,
    intentService,
    sessionManagerService,
    mediaQueryService,
    CONST_HTTP,
    CONST_STORAGE,
    CONST_MEDIA,
    CONST_SF_APP
  ) => {

    $http.defaults.headers.common[CONST_HTTP.HEADER_SF_APP_ID] = $rootScope.appId;
    $http.defaults.headers.common[CONST_HTTP.HEADER_SF_APP_VERSION] = $rootScope.appVersion;

    const initCloudinary = () => $.cloudinary.config({
      cloud_name: $rootScope.conf('CloudinaryCloudName'),
      api_key   : $rootScope.conf('CloudinaryApiKey')
    });

    // default - typical mobile view
    // fixed - current desktop view
    // compact - ipad view in landscape mode
    $rootScope.mainMenuVariant = 'default';
    $rootScope.openMainMenu = false;
    $rootScope.isTabletInLandscapeMode = false; // adds/removes class to the body

    const applyViewChange = (menuVariant, menuIsShown, isTabletLandscapeMode) => {
      $rootScope.isTabletInLandscapeMode = isTabletLandscapeMode;
      if ($rootScope.mainMenuVariant !== menuVariant) {
        $rootScope.mainMenuVariant = menuVariant;
        $rootScope.openMainMenu = menuIsShown;
      }
      $rootScope.$apply();
    };

    const detectView = (breakpoints) => {
      // should apply only to the tablet in landscape mode 
      // with width greater than sm 
      // with height not less than CONST_MEDIA.BREAKPOINTS.HEIGHT 
      if (deviceService.isMobile()) {
        if (breakpoints.xs || breakpoints.sm ) {
          window.screen.orientation.lock('portrait');
          $rootScope.screenOrientationIsLocked = true;
        } else {
          window.screen.orientation.unlock();
          $rootScope.screenOrientationIsLocked = false;
        }
        const minTabletHeight = window.matchMedia(`(min-height: ${CONST_MEDIA.BREAKPOINTS.HEIGHT}px)`);
        if (
          (breakpoints.md || breakpoints.lg || breakpoints.xl) &&
          breakpoints.orientation === CONST_MEDIA.ORIENTATION.LANDSCAPE &&
          minTabletHeight.matches
        ) {
          applyViewChange('compact', true, true);
        } else {
          applyViewChange('default', false, false);
        }
      } else {
        if (breakpoints.xs || breakpoints.sm ) {
          applyViewChange('default', false, false);
        } else {
          applyViewChange('fixed', true, false);
        }
      }
    };
    mediaQueryService.registerListener(detectView, true);

    // Expose method to react components (settings/specialties)
    $rootScope.updateSpecialtiesInCANS = (specialties) => {
      CANSService
        .setAssociateAvailability(
          chatPresenceService.isAvailable(),
          {
            associate: {
              specialtyIds: specialties
            }
          });
    };

    /**
     * Wait For Init Promise
     * Create a promise to wait until initPromise is defined and resolved
     * @returns {promise}
     * @resolves {undefined} When init is complete
     * @rejects {Error} If initPromise is rejected
     */
    $rootScope.waitForInitPromise = () => (new Promise((resolve, reject) => {
      const pollInterval = 100; // milliseconds
      const timer = setInterval(() => {
        // wait for initPromise to be defined
        // prevent auto-resolve if initPromise is falsey primitive value
        if ($rootScope.initPromise !== undefined && $rootScope.initPromise !== null) {
          clearInterval(timer);
          // wait for initPromise to resolve
          Promise.resolve($rootScope.initPromise)
            .then(resolve)
            .catch(reject);
        }
      }, pollInterval);
    }));

    $rootScope.init = () => {
      if (!$rootScope.initPromise) {
        $rootScope.initPromise = new Promise((resolve) => {
          sessionManagerService.init({
            headers: {
              [CONST_HTTP.HEADER_SF_APP_ID]: $rootScope.appId,
              [CONST_HTTP.HEADER_SF_APP_VERSION]: $rootScope.appVersion
            },
            apiUrl: configService.get('SfApiUrl'),
            token: $rootScope.accessToken.replace('Bearer ', ''),
            onInit: () => {
              $rootScope.initPromise = i18nService.init()
                .then($rootScope.checkCurrentUser)
                .then(user => i18nService.setLocaleForUser(user).catch(() => {}))
                .then(i18nService.currentLocale)
                .then(chatService.init)
                .then(initCloudinary)
                .then(pushNotificationService.register)
                .then(requestsPollingService.init)
                .then(loggerService.init)
                .then(() => shareService.init(i18nService.currentLocale(true)))
                .then(firebaseService.init)
                .then(() => {
                  if (window.sfApp === CONST_SF_APP.DESKTOP) {
                    const locale = storageService.getItem('ls.currentLocale', CONST_STORAGE.TYPE.LOCAL).replace(/"/g, '');
                    $rootScope.postMessageSfApp({
                      source: 'sf-app',
                      payload: {
                        locale: locale && locale !== 'null' ? locale : 'en_US',
                        token: $rootScope.accessToken,
                        user: $rootScope.currentUser,
                        config: configService.getConfig(),
                        headers: {
                          sfAppId: $rootScope.appId,
                          sfAppVersion: $rootScope.appVersion,
                        }
                      }
                    });
                    pushNotificationService.requestNotificationPermission()
                      .then((permission) => {
                        if (permission === 'granted') {
                          $rootScope.postMessageSfApp(
                            {
                              source: 'sf-app',
                              payload: {
                                action: 'registerFirebaseCloudMessaging',
                              },
                            }
                          );
                        }
                      });
                  }
                })
                .then(resolve);
            },
            onUpdate: (newToken) => {
              requestsPollingService.start(); // resume polling service
              $rootScope.setAuthHeaderWithAccessToken(newToken);
              $rootScope.setAccessToken(newToken);
            },
            onError: (err) => {
              requestsPollingService.stop();
              if (err.status === 401) {
                $location.path('/logout');
                $timeout(() => feedbackService.showError('Your session has expired.'), 2000);
                return;
              }
              if (connectivityService.isOnline()) {
                let interval;
                let countdown = {
                  timeLeft: 30
                };
                feedbackService.showFeedback({
                  type: 'modal',
                  template: 'views/directives/refresh-countdown.html',
                  context: countdown,
                  allowDismissFeedback: false,
                  nuke: true,
                  buttons: {
                    close: 'Retry now'
                  }
                }).then(() => {
                  $interval.cancel(interval);
                  sessionManagerService.refreshInstantly();
                });
                interval = $interval(() => {
                  countdown.timeLeft--;
                  if (countdown.timeLeft <= 0) {
                    $interval.cancel(interval);
                    feedbackService.closeFeedback();
                    sessionManagerService.refreshInstantly();
                  }
                }, 1000, 0);
              } else {
                const refreshOnReconnect = () => {
                  window.removeEventListener('online', refreshOnReconnect);
                  sessionManagerService.refreshInstantly();
                };
                window.addEventListener('online', refreshOnReconnect);
              }
            }
          });
        });
      }
      return $rootScope.initPromise;      
    };
  
    window.WithPermissions.setup(permissionService, featuresEnabledService);
    trackingService.setupTracking();
    connectivityService.init();

    // init basic services
    $rootScope.chatService = chatService;
    $rootScope.feedbackService = feedbackService;
    $rootScope.navService = navService;
    $rootScope.loaderService = loaderService;
    $rootScope.secureStorageService = secureStorageService;
    $rootScope.apiService = apiService;
    window.angularScope = $rootScope;
    deeplinkService.deeplink.init();
    secureStorageService.init();


    // ****************************
    // new sf-app frame interaction
    // ****************************
    $rootScope.postMessageSfApp = (message) => {
      window.sfAppFrame.contentWindow.postMessage(message);
    };
    
    const showFrame = (path, options) => {
      const config = JSON.parse(storageService.getItem('ls.Configuration_data', CONST_STORAGE.TYPE.LOCAL));
      const configId = storageService.getItem('ls.Configuration_configId', CONST_STORAGE.TYPE.LOCAL).replace(/"/g, '');
      const locale = storageService.getItem('ls.currentLocale', CONST_STORAGE.TYPE.LOCAL).replace(/"/g, '');
      const user = $rootScope.currentUser;
      secureStorageService
        .getItem(CONST_STORAGE.ACCESS_TOKEN)
        .then((token) => {
          $rootScope.postMessageSfApp({
            source: 'sf-app',
            payload: {
              appVariant: window.sfApp,
              locale: locale && locale !== 'null' ? locale : 'en_US',
              path,
              token,
              user,
              config: config[configId],
              headers: {
                sfAppId: $rootScope.appId,
                sfAppVersion: $rootScope.appVersion,
              },
              options
            }
          });
          window.sfAppFrame.style.display = 'block';
        });
    };

    $rootScope.hideNewUI = () => {
      window.sfAppFrame.style.zIndex = 1000;
      window.sfAppFrame.style.display = 'none';
    };

    // subscribe for messages from iframe
    window.addEventListener('message', (event) => {
      const payload = event.data.payload;
      if (event.data.source === 'sf-app') {
        switch (payload.action) {
        case 'close':
          $rootScope.hideNewUI();
          break;
        case 'navigate':
          navService.goTo(payload.redirect);
          break;
        case 'execute':
          switch (payload.params.name) {
          case 'showBackdrop':
            window.sfAppFrame.style.zIndex = 1002; // show frame on top of the menu
            break;          
          case 'hideBackdrop':
            window.sfAppFrame.style.zIndex = 1000; // set back frame behind menu
            break;          
          case 'setLocale': 
            i18nService
              .setLocale(payload.params.options, (err) => {
                if (err === null) {
                  $route.reload();
                }
              });
            break;
          case 'registerDeviceWithTokenForPush':
            pushNotificationService.onRegistration({ registrationId: payload.params.options });
            break;
          case 'onPushNotification':
            pushNotificationService.onNotification(payload.params.options);
            break;
          case 'updateSpecialtiesInCANS':
            $rootScope
              .resetCurrentUser()
              .then(() => {
                $rootScope.updateSpecialtiesInCANS(payload.params.options);
              });
            break;
          case 'insertAiMessage':
            $rootScope.$broadcast('insertAiMessage', {
              message: payload.params.options
            });
            break;
          }
          break;
        }
      }
    }, false);

    $rootScope.handleAppSwitch = (path, payload = {}) => {
      if (path === '/assistant') {
        window.sfAppFrame.style.zIndex = 1002;
        showFrame(path, payload);  
        return;
      }
      // redirect to dashboard before open settings page.
      if ($location.path() !== '/dashboard') {
        navService.goTo('/dashboard', {}, false);
      }
      
      showFrame(path, payload);
    };

    window.addEventListener('popstate', () => {
      if (window.sfAppFrame.style.display === 'flex') {
        window.sfAppFrame.style.display = 'none';
      }
    });

    if (window.sfApp === CONST_SF_APP.DESKTOP) {
      // *********************************** //
      // ********** SSO desktop ************ //
      // *********************************** //
      let code = localStorageService.get('code');
      let state = localStorageService.get('state');
      if (code && state) {
        localStorageService.remove('code');
        localStorageService.remove('state');
        intentService.stash({code, state});
        navService.goTo('login/sso');
      } else {
        const url = new URL($location.absUrl());
        ({ code, state } = Object.fromEntries(url.searchParams.entries()));
        // strip code & state from URL
        if (code && state) {
          url.searchParams.delete('code');
          url.searchParams.delete('state');
          url.searchParams.delete('session_state');
          // store temporary in storage and reload url without params
          localStorageService.set('code', code);
          localStorageService.set('state', state);
          $window.location = url;
        }
      }
      // *********************************** //
      // ******** End SSO desktop ********** //
      // *********************************** //        
    }
  });
