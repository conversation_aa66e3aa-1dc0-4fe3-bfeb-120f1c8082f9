angular.module('sfmobileApp').directive('sunEditor', ($timeout, i18nService) => ({
  restrict: 'E',
  scope: {
    htmlContent: '=',
    placeholder: '=',
    bindControls: '&',
    onBlur: '&',
    onFocus: '&',
    config: '=?'
  },
  link(scope, element) {
    const SUNEDITOR = window.SUNEDITOR;
    const SUNEDITOR_DEFAULT_PLUGINS = window.SUNEDITOR_DEFAULT_PLUGINS;
    const SUNEDITOR_LANG = window.SUNEDITOR_LANG;

    const CURSOR_MARKER_TAG = 'cursorpos';
    const PLACEHOLDER_MARKER_TAG = 'mark';

    // ------------------------------------------------------
    // Define functions used in configuring editor
    // ------------------------------------------------------
    /**
     * Exclude plugins from the array output
     * Object to array conversion excluding certain keys
     * @param {object} originalObject
     * @param {array} dontIncludeKeys
     * @returns {array}
     */
    const excludePluginsByKey = (originalObject, dontIncludeKeys) => {
      const result = [];
      dontIncludeKeys = dontIncludeKeys || [];
      for (let key in originalObject) {
        if (originalObject.hasOwnProperty(key) && !dontIncludeKeys.includes(key)) {
          result.push(originalObject[key]);
        }
      }

      return result;
    };

    /**
     * Get language from locale
     * @param {string} locale 
     * @returns {string} language
     */
    const getLanguageFromLocale = (locale) => {
      const match = String(locale).match(/^([a-zA-Z]+)_[a-zA-Z]+$/);
      if (match) {
        return match[1].toLowerCase();
      }
      return 'en';
    };

    let insertHtml; // used inside plugins
    
    // Placeholder translations
    const placeholderTranslations = {
      en: {
        placeholder: '(customer-name)',
        label: '+ Customer Name',
      },
      fr: {
        placeholder: '(nom-du-client)',
        label: '+ Nom du Client',
      },
    };
    const placeholderTexts = Object.values(placeholderTranslations).map(item => item.placeholder);

    // get current language
    const language = getLanguageFromLocale(i18nService.currentLocale(true));
    let sunEditorTransalation;
    let insertNameLabel, insertNamePlaceholder;
    switch (language) {
    case 'fr':
      insertNameLabel = placeholderTranslations['fr'].label;
      insertNamePlaceholder = placeholderTranslations['fr'].placeholder;
      sunEditorTransalation = SUNEDITOR_LANG['fr'];
      break;
    case 'en':
    default:
      insertNameLabel = placeholderTranslations['en'].label;
      insertNamePlaceholder = placeholderTranslations['en'].placeholder;
      // sunEditor does not require english translation
    }

    // ---------------------------
    // Editor Configs
    // ---------------------------
    const wrappingElementStyle = 'color:black; font-size:13px; font-family:Arial,Helvetica,Verdana,sans-serif;';
    const tagWhiteList = `${CURSOR_MARKER_TAG}|${PLACEHOLDER_MARKER_TAG}|p|span|div|strong|u|em|del|h1|h2|h3|h4|h5|h6|ul|ol|li|blockquote|pre|table|thead|tbody|tfoot|tr|td|th|br`;
    const tagBlackList = 'html|head|title|base|link|meta|style|body|script|noscript|template|slot|iframe|frame|embed|object|param|picture|img|source|track|canvas|map|area|svg|path|math|audio|video|form|input|button|select|textarea|option|optgroup|datalist|fieldset|legend|output|a';
    const formats = ['p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    const plugins = excludePluginsByKey(SUNEDITOR_DEFAULT_PLUGINS, ['image', 'video', 'audio']);
    const placeholderCustomerNameIcon = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="15px" height="15px" viewBox="0 0 15 15" version="1.1"><g id="surface1"><path style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 14.21875 13.15625 C 14.136719 12.96875 9.738281 10.351562 9.734375 10.347656 C 9.113281 9.894531 9.039062 9.449219 9.539062 9.195312 C 9.96875 8.976562 10.335938 8.273438 10.617188 7.519531 C 10.664062 7.542969 10.722656 7.546875 10.773438 7.53125 C 11.941406 7.191406 11.964844 4.832031 11.183594 5.25 C 11.914062 -1.125 3.089844 -1.125 3.816406 5.25 C 3.035156 4.832031 3.058594 7.191406 4.226562 7.53125 C 4.277344 7.546875 4.335938 7.542969 4.382812 7.523438 C 4.664062 8.273438 5.03125 8.976562 5.460938 9.195312 C 5.964844 9.449219 5.867188 9.871094 5.253906 10.359375 C 5.046875 10.519531 2.199219 12.109375 1.398438 12.644531 L 1.394531 12.648438 C 1.0625 12.871094 0.824219 13.054688 0.78125 13.15625 C 0.550781 13.683594 0.46875 14.53125 0.46875 14.53125 L 14.53125 14.53125 C 14.53125 14.53125 14.449219 13.683594 14.21875 13.15625 "></path></g></svg>';
    const placeholderPlugin = {
      name: 'name_placeholder',
      display: 'command',
      title: insertNameLabel,
      buttonClass: '',
      innerHTML: `<div class="se-btn-module">
                    <button type="button" class="se-btn se-btn-module" title="${insertNameLabel}">
                      <span class="se-btn-name-placeholder">${placeholderCustomerNameIcon}</span>
                    </button>
                  </div>`,
      add: () => {},
      action: () => {
        insertHtml(insertNamePlaceholder);
      }
    };
    plugins.push(placeholderPlugin);
    const sunEditorConfigs = {
      width: 'auto',
      maxWidth: 'auto',
      height: 'auto',
      showPathLabel: false,
      charCounter: false,
      resizingBar: false,
      defaultStyle: wrappingElementStyle, // same as emails
      fontSize : [8, 10, 13, 18, 24, 36],  
      addTagsWhitelist: tagWhiteList,
      pasteTagsWhitelist: tagWhiteList,
      tagsBlacklist: tagBlackList,
      imageFileInput: false,
      videoFileInput: false,
      formats: formats,
      buttonList: [
        ['bold', 'italic', 'underline', 'strike', 'fontColor', 'hiliteColor', 'name_placeholder', 'outdent', 'indent', 'align', 'list', 'table', 'formatBlock', 'fontSize'],
      ],
      plugins: plugins,
      ...(scope.config || {})
    };
    if (scope.placeholder) {
      sunEditorConfigs.placeholder = scope.placeholder;
    }

    // Set Lanugage of editor
    if (sunEditorTransalation) {
      sunEditorConfigs.lang = sunEditorTransalation;
    }

    // ---------------------------
    // Create Editor Instance
    // ---------------------------
    const sunEditor = SUNEDITOR.create(element[0], sunEditorConfigs);

    // ------------------------------------------------------
    // Define functions that makes use of the editor instance
    // ------------------------------------------------------
    /**
     * Get last modified dom element
     * @returns {HTMLElement}
     */
    const getLastChild = () => {
      const core = sunEditor.core;
      const editorNode = core.context.element.wysiwyg;
      return editorNode.lastChild;
    };

    /**
     * Insert HTML at current cursor location
     * @param {string} html
     * @returns {undefined}
     */
    insertHtml = html => sunEditor.core.sfInsertHtml(html);

    /**
     * Clean HTML of invalid tags
     * @param {string} htmlString
     * @returns {string}
     */
    const cleanHtml = htmlString => sunEditor.core.cleanHTML(htmlString, tagWhiteList, tagBlackList);

    /**
     * Replace the style attr in a HTML tag
     * @param {string} the starting html tag <td style="oldStyle" colspan="2">
     * @param {regex} pattern 
     * @param {string} elementStyle 
     * @returns {string} resulting tag <td style="elementStyle" colspan="2">
     */
    const replaceStyle = (text, pattern, elementStyle) => text.replace(pattern, (str) => {
      const stylePattern = /\s?style="([^"]*)"/gi;
      const styleAttr = ' style="' + elementStyle + '"';
      if (str.match(stylePattern)) {
        // replace existing style if exists
        str = str.replace(stylePattern, styleAttr);
      } else {
        // add style attr
        str = str.replace('>', styleAttr + '>');
      }
      str = str.replace('  ', ' ');
      return str;
    });
    
    /**
     * Apply styles using selectors
     * @param {string} htmlString
     * @param {array} stylesToApply [[selector, styleString], ... ]
     * @returns {string} html result with styles applied
     */
    const applyStyles = (htmlString, stylesToApply) => {
      // Create a new DOM parser to parse the HTML string
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, 'text/html');

      // Apply style to elements
      for (let i = 0; i < stylesToApply.length; i++) {
        const selector = stylesToApply[i][0];
        const style = stylesToApply[i][1];
        const elements = doc.querySelectorAll(selector);
        applyStyleToElements(elements, style);
      }

      return doc.body.innerHTML;
    };

    /**
     * Merge style attributes to element
     * @param {array} elements array of dom elements
     * @param {string} newStyle CSS style to apply
     */
    const applyStyleToElements = (elements, newStyle) => {
      // Apply style to each element in list
      for (let i = 0; i < elements.length; i++) {
        const el = elements[i];

        // Build a map of all CSS properties
        const stylesMap = {};

        // Accumulate existing styles
        const existingStylesArray = splitStyleAttributeValue(el.getAttribute('style') || '');
        existingStylesArray.forEach(styleAttribute => addToStylePropertyMap(stylesMap, styleAttribute));

        // Add new styles to map overriding existing
        const newStylesArray = splitStyleAttributeValue(newStyle);
        newStylesArray.forEach(styleAttribute => addToStylePropertyMap(stylesMap, styleAttribute));

        // Convert map back to array
        const combinedStyleArray = [];
        for (let key in stylesMap) {
          if (stylesMap.hasOwnProperty(key)) {
            combinedStyleArray.push(key + ': ' + stylesMap[key]);
          }
        }

        // Apply combined style if not empty, otherwise remove the style attribute
        if (combinedStyleArray.length > 0) {
          el.setAttribute('style', combinedStyleArray.join('; ') + ';');
        } else {
          el.removeAttribute('style');
        }
      }
    };

    /**
     * Split CSS properties
     * produces an array of string for each css key-value pair
     * @param {string} styleStr value of the style attribute
     * @returns {array} split CSS properties
     */
    const splitStyleAttributeValue = (styleStr) => {
      if (typeof(styleStr) !== 'string') {
        return [];
      }
      return styleStr.split(';')
        .map(s => s.trim())
        .filter(s => s);
    };

    /**
     * Extract CSS property from string and add key-value pair to CSS map
     * @param {object} stylesMap Map of css values
     * @param {string} style CSS key-value pair EX "display: block"
     */
    const addToStylePropertyMap = (stylesMap, style) => {
      if (typeof(style) === 'string') {
        const parts = style.split(':');
        if (parts.length === 2) {
          stylesMap[parts[0].trim()] = parts[1].trim();
        }
      }
    };

    /**
     * Format the message before saving
     * applies missing styles and removes extra whitespace and tags in HTML string
     * @param {string} contents HTML string from WYSIWYG
     * @returns {string} HTML with styles applied
     */
    const formatHtmlBeforeSend = (contents) => {
      // remove any non-white listed tags
      let result = cleanHtml(contents);

      const tableWithStyle100 = 'width: 100%; border-spacing: 0px; border-collapse: collapse; margin: 0 0 10px;';
      const tableWithStyleAuto = 'width: auto; border-spacing: 0px; border-collapse: collapse; margin: 0 0 10px;';
      const theadStyle = 'border-bottom: 2px solid #333; background-color: #f3f3f3;';
      const cellStyle = 'border: 1px solid #e1e1e1; padding: .4em;';
      const alphaOrderedList = 'list-style-type: lower-alpha;';
      const romanOrderedList = 'list-style-type: upper-roman;';
      const blockquoteStyle = [
        'display: block;',
        'font-family: inherit;',
        'font-size: inherit;',
        'color: #999;',
        'margin-block-start: 1em;',
        'margin-block-end: 1em;',
        'margin-inline-start: 0;',
        'margin-inline-end: 0;',
        'padding: 0 5px 0 20px;',
        'border: solid #b1b1b1;',
        'border-width: 0 0 0 5px;',
        'margin: 13px 0px;',
      ].join(' ');
      const preStyle = [
        'display: block;',
        'padding: 8px;',
        'margin: 0 0 10px;',
        'font-family: monospace;',
        'color: #666;',
        'line-height: 1.45;',
        'background-color: #f9f9f9;',
        'border: 1px solid #e1e1e1;',
        'border-radius: 2px;',
        'white-space: pre-wrap !important;',
        'word-wrap: break-word;',
        'overflow: visible;',
      ].join(' ');
      
      // Apply missing table styles to html
      const tablePattern = /<table\b([^>]*)>/gi;
      if (result.match(tablePattern)) {
        result = result.replace(tablePattern, (str) => {
          const autoWidthClassPattern = /class="[^"]*(se-table-size-auto)[^"]*"/gi;
          if (str.match(autoWidthClassPattern)) {
            str = replaceStyle(str, tablePattern, tableWithStyleAuto);
          } else {
            str = replaceStyle(str, tablePattern, tableWithStyle100);
          }
          return str;
        });
        result = replaceStyle(result, /<thead\b([^>]*)>/gi, theadStyle);
        result = replaceStyle(result, /<th\b([^>]*)>/gi, cellStyle);
        result = replaceStyle(result, /<td\b([^>]*)>/gi, cellStyle);
      }
      // Apply other styles
      result = replaceStyle(result, /<blockquote\b([^>]*)>/gi, blockquoteStyle);
      result = replaceStyle(result, /<pre\b([^>]*)>/gi, preStyle);

      // clean blank space which would add extra spacing
      result = result.replace(/(<blockquote\b[^>]*>)\s*/gi, '$1');
      result = result.replace(/(<pre\b[^>]*>)\s*/gi, '$1');

      // remove tags we dont want in result
      result = result.replace(/<mark>(.*?)<\/mark>/gi, '$1');
      result = result.replace(/<cursorpos>(.*?)<\/cursorpos>/gi, '$1');

      // Apply more complicated rules that requires selectors and dom parsing
      result = applyStyles(result, [
        // order matters
        ['ol ol', alphaOrderedList],
        ['ol ol ol', romanOrderedList],
      ]);

      // Add wrapping element to provide baseline style
      result = `<div style="${wrappingElementStyle}">${result}</div>`;
      
      return result;
    };

    // ---------------------------
    // Placeholder logic
    // ---------------------------

    /**
     * Handle Placeholder Display
     * @param {HTMLElement} element editor DOM element
     * @param {array} textsToHighlight array of placeholders to look for
     */
    const handlePlaceholderDisplay = (element, textsToHighlight) => {
      // Build regex to detect placeholders
      const escapedTexts = textsToHighlight
        .map(text => text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
        .join('|');
      const regex = new RegExp(`(${escapedTexts})`, 'g');

      // Move cursor after placeholder so the <cursorpos> element is not in the middle
      // This will allow us to correctly detect the placeholder when editing the middle of the placeholder
      moveTextCursorAfterPlaceholderIfInMiddle(element, regex);

      // Insert a <cursorpos> element to preserve the cursor position
      // This is done because following methods may replace DOM nodes and interupt the cursor's location
      insertCursorPosElement();

      // Replace <mark> tag contents which are not exact match to placeholder
      replaceInvalidMarkTags(element, regex);

      // Normalize the element to merge text nodes
      element.normalize();

      // Add Mark tags to placeholders not yet marked
      addMarkTags(element, regex);

      // Restore the cursor position using the <cursorpos> element
      removeCursorPosElementAndPlaceCursor(element);
    };

    /**
     * If text cursor is in middle of placeholder
     * move it to just after the placeholder
     * since we use the element <cursorpos> to mark the cursor position
     * it may interfere with the regex if not moved outside the text.
     * @param {HTMLElement} element containing editor contents
     * @param {regex} regex for placeholder detection
     */
    const moveTextCursorAfterPlaceholderIfInMiddle = (element, regex) => {
      let selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const offset = range.startOffset;
        const currentNode = range.startContainer;

        // Check if the cursor is inside a placeholder match
        let cursorPosition = getGlobalOffset(currentNode, offset, element);

        // Find placeholders in normalized text
        let match;
        regex.lastIndex = 0;
        while ((match = regex.exec(element.textContent)) !== null) {
          const startIndex = match.index;
          const endIndex = match.index + match[0].length;

          // Check if the cursor is in the middle of this placeholder
          if (cursorPosition > startIndex && cursorPosition < endIndex) {
            // Ensure the match is within the same block element
            if (isStartAndEndOfPlaceholderInSameParent(element, startIndex, endIndex)) {
              // Move the cursor to the end of the placeholder
              mapCursorToMergedElement(element, endIndex);
            }
            break; // Stop processing further matches
          }
        }
      }
    };

    /**
     * Calculate offset of the cursor in parent element between multiple elements
     * @param {HTMLElement} currentNode
     * @param {int} offset
     * @param {HTMLElement} parentElement
     * @returns {int}
     */
    const getGlobalOffset = (currentNode, offset, parentElement) => {
      let charIndex = 0;
      const walker = document.createTreeWalker(parentElement, NodeFilter.SHOW_TEXT, null, false);
      while (walker.nextNode()) {
        if (walker.currentNode === currentNode) {
          return charIndex + offset;
        }
        charIndex += walker.currentNode.textContent.length;
      }
      return 0;
    };

    /**
     * After merging text nodes, position the cursor
     * @param {HTMLElement} element containing editor contents
     * @param {int} endIndex
     */
    const mapCursorToMergedElement = (element, endIndex) => {
      let charIndex = 0;
      let targetOffset = 0;
      const updatedWalker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, null, false);
      let updatedTargetNode = null;
      while (updatedWalker.nextNode()) {
        const nodeText = updatedWalker.currentNode.textContent;
        if (charIndex + nodeText.length >= endIndex) {
          updatedTargetNode = updatedWalker.currentNode;
          targetOffset = endIndex - charIndex;
          break;
        }
        charIndex += nodeText.length;
      }
      if (updatedTargetNode) {
        const newRange = document.createRange();
        newRange.setStart(updatedTargetNode, targetOffset);
        newRange.setEnd(updatedTargetNode, targetOffset);

        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    };

    /**
     * Ensure start and end of the placeholder are in the same block element
     * @param {HTMLElement} element The main editor element
     * @param {int} startIndex Start index of the placeholder in the combined text
     * @param {int} endIndex End index of the placeholder in the combined text
     * @returns {boolean}
     */
    const isStartAndEndOfPlaceholderInSameParent = (element, startIndex, endIndex) => {
      let charIndex = 0;
      let matchStartNode = null;
      let matchEndNode = null;
      const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, null, false);

      while (walker.nextNode()) {
        const nodeText = walker.currentNode.textContent;

        // Find the start node
        if (charIndex <= startIndex && startIndex < charIndex + nodeText.length) {
          matchStartNode = walker.currentNode;
        }

        // Find the end node
        if (charIndex <= endIndex && endIndex <= charIndex + nodeText.length) {
          matchEndNode = walker.currentNode;
        }

        charIndex += nodeText.length;

        // Break if both start and end nodes are found
        if (matchStartNode && matchEndNode) {
          break;
        }
      }

      // Ensure both nodes exist and have the same block parent
      if (matchStartNode && matchEndNode) {
        return matchStartNode.parentNode === matchEndNode.parentNode;
      }

      return false;
    };

    /**
     * Replace mark contents which should not be in a mark tag
     * @param {HTMLElement} element containing editor contents
     * @param {Regex} regex for placeholder detection
     */
    const replaceInvalidMarkTags = (element, regex) => {
      // Select all <mark> tags within the element
      element.querySelectorAll(PLACEHOLDER_MARKER_TAG).forEach((mark) => {
        // Build a new fragment to replace the existing element
        const parent = mark.parentNode;
        const fragment = document.createDocumentFragment();
        Array.from(mark.childNodes).forEach((childNode) => {
          if (childNode.nodeType === Node.TEXT_NODE) {
            // Handle text nodes: split text into placeholders and non-placeholder parts
            let lastIndex = 0;
            let match;
            regex.lastIndex = 0;
            while ((match = regex.exec(childNode.nodeValue)) !== null) {
              let matchedText = match[0]; // Extract the matched placeholder
              let offset = match.index; // Position of the match
              if (offset > lastIndex) {
                // Add any text that occurs before placeholder to the fragment
                fragment.appendChild(document.createTextNode(childNode.nodeValue.slice(lastIndex, offset)));
              }
              // Wrap the matched placeholder in a new <mark> tag
              const newMark = document.createElement(PLACEHOLDER_MARKER_TAG);
              newMark.textContent = matchedText;
              fragment.appendChild(newMark);
              lastIndex = offset + matchedText.length; // Move past the match
            }
            // Add remaining text after the last match
            if (lastIndex < childNode.nodeValue.length) {
              fragment.appendChild(document.createTextNode(childNode.nodeValue.slice(lastIndex)));
            }
          } else {
            // Preserve non-text nodes (like <cursorpos>)
            fragment.appendChild(childNode.cloneNode(true));
          }
        });

        // Replace the <mark> with the new contents
        parent.replaceChild(fragment, mark);
        parent.normalize(); // Merge adjacent text nodes
      });
    };

    /**
     * Wrap placeholders in mark tags
     * @param {HTMLElement} element
     * @param {Regex} regex
     */
    const addMarkTags = (element, regex) => {
      const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, null, false);

      // Collect text nodes first to avoid skipping due to DOM changes
      let node;
      const nodesToProcess = [];
      while ((node = walker.nextNode())) {
        nodesToProcess.push(node);
      }

      // Build new fragment with mark tag and remaining text
      nodesToProcess.forEach((node) => {
        const parent = node.parentNode;

        if (parent.tagName !== String(PLACEHOLDER_MARKER_TAG).toUpperCase()) { // Ensure we don't wrap inside <mark> tags
          const text = node.nodeValue;
          const fragment = document.createDocumentFragment();
          let detectedText = false;
          let lastIndex = 0;

          // Reset regex state for each node
          regex.lastIndex = 0;
          let match;
          while ((match = regex.exec(text)) !== null) {
            const matchedText = match[0];
            const offset = match.index;
            detectedText = true;

            // Append text before the match
            if (offset > lastIndex) {
              const textBefore = text.slice(lastIndex, offset);
              fragment.appendChild(document.createTextNode(textBefore));
            }

            // Create <mark> element for the match
            const mark = document.createElement('mark');
            mark.textContent = matchedText;
            fragment.appendChild(mark);

            lastIndex = offset + matchedText.length;
          }

          // Append any remaining text after the last match
          if (lastIndex < text.length) {
            const textAfter = text.slice(lastIndex);
            fragment.appendChild(document.createTextNode(textAfter));
          }

          // Replace node with fragment if placeholder detected
          if (detectedText) {
            parent.replaceChild(fragment, node);
          }
        }
      });
    };

    /**
    * Insert <cursorpos> element at cursor position
    * this is so we can restore the location after the dom manipulation
    */
    const insertCursorPosElement = () => {
      let selection, range;
      selection = window.getSelection();
      if (selection.rangeCount > 0) {
        range = selection.getRangeAt(0);
        const cursorPosMarker = document.createElement('cursorpos');
        range.insertNode(cursorPosMarker);
      }
    };

    /**
    * Remove <cursorpos> element and restore cursor
    * @param {HTMLElement} element
    */
    const removeCursorPosElementAndPlaceCursor = (element) => {
      const cursorPosMarker = element.querySelector('cursorpos');
      if (cursorPosMarker) {
        const range = document.createRange();
        const selection = window.getSelection();

        range.setStartAfter(cursorPosMarker);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);

        // Remove the marker
        cursorPosMarker.parentNode.removeChild(cursorPosMarker);
      }
    };


    // ---------------------------
    // Attach callbacks
    // ---------------------------
    sunEditor.onChange = () => {
      const core = sunEditor.core;
      const editorNode = core.context.element.wysiwyg;
      handlePlaceholderDisplay(editorNode, placeholderTexts);
      $timeout(() => scope.htmlContent = core.context.element.wysiwyg.innerHTML, 0);
    };

    sunEditor.onFocus = (event, core) => {
      if (typeof(scope.onFocus) === 'function') {
        scope.onFocus({
          event,
          core,
        });
      }
    };

    sunEditor.onBlur = (event, core, contents) => {
      if (typeof(scope.onBlur) === 'function') {
        scope.onBlur({
          event,
          core,
          contents,
        });
      }
    };

    // Watch for changes in htmlContent and update the editor
    scope.$watch('htmlContent', (newValue) => {
      if (newValue !== sunEditor.getContents()) {
        sunEditor.setContents((newValue || ''));
      }
    });

    scope.$watch('placeholder', newValue => sunEditor.setOptions({placeholder: newValue}));

    scope.$onDestroy = () => sunEditor.destroy();

    // ---------------------------
    // Init the editor
    // ---------------------------
    if (typeof(scope.bindControls) === 'function') {
      scope.bindControls({ 
        controls: {
          insertHtml,
          formatHtmlBeforeSend,
          getLastChild,
          getEditor: () => sunEditor,
          handlePlaceholderDisplay,
        }
      });
    }
  }
}));
