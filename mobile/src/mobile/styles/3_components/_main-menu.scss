@import '2_mixins/media-queries';
@import '2_mixins/misc';

main-menu {
  position: fixed;
  flex-flow: column;
  top: 0;
  left: 0;
  width: auto;
  height: 100vh;
  z-index: 1001;

  .device--is-desktop & {
    position: absolute;
  }

  &.fixed {
    width: 280px;
  }

  &.compact {
    width: auto;
  }

  .page-header {
    padding-top: 0;
    width: 100%;
    transition: opacity 0.4s;

    &--is-default {
      width: 100vw;
    }

    &--is-compact {
      width: 280px;

      &.collapsed {
        width: 60px;
      }
    }

    &--is-hidden {
      opacity: 0;
    }
  }

  .page-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;

    &--is-compact {
      position: relative;
      width: 60px;
      height: calc(100% - 150px);
      display: flex;
      background-color: transparent;
    }

  }
}

.main-menu {
  //layout
  left: -100%;
  width: 100%;
  height: 100vh;
  display: flex;
  padding: 90px 0 0 0;
  position: absolute;
  flex-flow: column;
  overflow-y: auto;
  padding-bottom: 75px;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 55px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 55px);

  //design
  opacity: 0;
  transform: translate3d(0, 0, 0);
  background-color: #008deb;

  //animation
  transition: 100ms ease-in-out;

  //scrollbar (Firefox)
  scrollbar-width: auto;
  scrollbar-color: rgba(255, 255, 255, 0.4) #008deb;

  //scrollbar (Safari, Chrome)
  &::-webkit-scrollbar {
    //layout
    width: 10px;
  }

  &::-webkit-scrollbar-thumb {
    //design
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-track {
    //design
    background-color: #008deb;
  }

  &--is-compact {
    width: 280px;
    padding-bottom: 0;

    &.collapsed {
      width: 60px;
      min-width: auto;
      padding: 20px 0 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      .tabbar__menu-toggle {
        transform: scale(0.8);
        cursor: pointer;
        span {
          background-color: #fff;
        }
      }

      .main-menu__list {
        display: none;
      }
    }
  }

  &__overlay {
    position: fixed;
    top: 0;
    left: 280px;
    width: calc(100% - 280px);
    height: 100vh;
    background: #000;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1000;

    &--is-activated {
      opacity: 0.6;
    }
  }

  &__quick-action-menu {
    display: none;
    position: absolute;
    z-index: 1001;
    cursor: pointer;
    transition: left 0.3;

    &--is-compact {
      left: 14px;
      bottom: calc(env(safe-area-inset-bottom) + 10px);

      &.collapsed {
        display: block;
      }
    }

    &--is-fixed {
      left: calc(100% - 50px);
      bottom: calc(env(safe-area-inset-bottom) + 75px);
      display: block;
    }
  }
}

.main-menu--is-shown {
  //layout
  left: 0;

  //design
  opacity: 1;
}

.main-menu--with-action-bar {
  //layout
  padding-top: 135px;
}

.main-menu__list--is-pos-items {
  //layout
  margin-top: 22px;

  .main-menu__list-item {
    &:last-of-type {
      //design
      border-color: #006eb8;
    }
  }
}

.main-menu__list--is-action-items {
  //layout
  flex: 1;
  padding-bottom: 20px;

  //design
  background-color: #0082d9;
}

.main-menu__list-item {
  //typo
  color: #fff;

  //design
  border-bottom: 1px solid rgba(0, 113, 188, 0.35);
}

.main-menu__list-item--is-activated {
  //typo
  color: #008deb;

  //design
  border-color: #0083da;
  background-color: #fff;

  path {
    //design
    fill: #008deb;
  }

  &.main-menu__list-item--has-submenu {
    .main-menu__menu-item__icon {
      //design
      transform: rotate(90deg);
    }
  }
}

.main-menu__menu-item {
  //layout
  display: flex;
  align-items: center;
  padding-left: 36px;
  cursor: pointer;
}

.main-menu__menu-item__icon {
  //layout
  display: flex;
  align-items: center;
  justify-content: center;

  //animation
  transition: transform 200ms;

  svg {
    transform: scale(1.2);
  }
}

.main-menu__menu-item__label {
  //layout
  height: 44px;
  margin-left: 15px;

  //typo
  font-size: 14px;
  font-weight: 600;
  line-height: 44px;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.main-menu__sub-menu {
  //layout
  overflow: hidden;
  max-height: 0;

  //animation
  transition: max-height 200ms;

  .main-menu__list {
    //layout
    margin-top: 0;

    //design
    background-color: #0082d9;
  }

  .main-menu__list-item {
    //design
    border-bottom: none;
  }

  .main-menu__menu-item__label {
    //layout
    margin-left: 36px;

    //typo
    font-size: 12px;
  }
}

.main-menu__sub-menu--is-open {
  //layout
  max-height: 500px;
}

.quick-action-menu__overlay {
  @include set-margin-max-width(1440px, auto);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  position: fixed;
  background-color: #000000;
  transition: opacity 0.3s;
}

.quick-action-menu__overlay--is-activated {
  opacity: 0.6;
}

.quick-action-menu__wrapper {
  position: relative;
}
