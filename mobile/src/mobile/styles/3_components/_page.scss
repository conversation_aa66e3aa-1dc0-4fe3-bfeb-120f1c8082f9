@import '2_mixins/media-queries';

.page-container {
  //layout
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  flex-direction: column;
}

  .page-container--is-dashboard {
    //layout
    position: relative;

    .page-header {
      //layout
      width: 100%;
      z-index: 1;
      position: absolute;
      padding-top: 0;

      //design
      opacity: 1;

      //animation
      transition: opacity 300ms, z-index 0s 300ms;
    }

    .page-header--with-action-bar {
      //layout
      top: 57px;
    }

    .page-content {
      //layout
      padding-top: 120px;

      .container--is-fixed &,
      .container--is-compact & {
        padding-top: 10px;
        margin-bottom: env(safe-area-inset-bottom);
      }
    }

    .page-footer {
      //layout
      z-index: 1;
      position: relative;
    }
  }

.page-header {
  //layout
  margin: 0;
  padding: 10px 15px 10px 5px;

  //design
  border: none;
}

.page-content {
  //layout
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}

.page-footer {
  //layout
  height: 55px;
  height: calc(constant(safe-area-inset-bottom) + 55px);
  height: calc(env(safe-area-inset-bottom) + 55px);
  background-color: #FFF;
}

@media only screen and (max-width : 767px) {
  .page-header {
    //layout
    padding: 10px;
  }
}
