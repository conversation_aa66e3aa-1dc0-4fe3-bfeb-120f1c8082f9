@import '2_mixins/retina';
@import '2_mixins/standard-icon';
@import '2_mixins/media-queries';

.dashboard__header-fill {
  //layout
  width: 100%;
  height: 10px;

  //design
  background-color: #f8f8f8;
}

.dashboard__header-fill--is-transparent {
  //design
  background: none;
}

.dashboard__header {
  //layout
  position: relative;
  width: 100%;
  height: 81px;
  display: flex;
  padding: 0 20px;
  align-items: center;
  justify-content: space-between;

  //design
  box-shadow: 0 20px 15px -10px rgba(178, 178, 178, 0.5);
  border-radius: 8px;
  background-color: #008deb;

  &--is-compact {
    &.collapsed {
      flex-direction: column;
      box-shadow: none;
      margin-top: 50px;
  
      .dashboard__rep-info {
        display: none;
      }
  
      .dashboard__live-chat-button {
        transform: scale(0.8);
      }
    }
  }
}

.dashboard__header--with-dark-background {
  //design
  box-shadow: 0 20px 15px -10px rgba(0, 111, 185, 0.5);
}

.dashboard__rep-info {
  //layout
  display: flex;
  max-width: calc(100% - 80px);
  align-items: center;
}

.dashboard__rep-avatar {
  //layout
  height: 38px; //34px + border 2px
  position: relative;

  //design
  border: 2px solid #ffffff;
  border-radius: 100%;
}

.dashboard__rep-avatar__image {
  //layout
  width: auto;
  height: 100%;
  min-width: 34px;

  //design
  border-radius: 100%;
}

.dashboard__rep-name {
  //layout
  margin-left: 10px;

  //typo
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  line-height: 14px;
  text-transform: capitalize;
}

.dashboard__store-name {
  //typo
  font-size: 10px;
  font-weight: normal;
  line-height: 14px;
}

.dashboard__rep-name,
.dashboard__store-name {
  //layout
  overflow: hidden;

  //typo
  white-space: nowrap;
  text-overflow: ellipsis;
}

.dashboard__live-chat-button {
  //layout
  top: 0;
  width: 60px;
  height: 30px;
  position: relative;

  //design
  background: rgba(0, 113, 188, 0.4);
  border-radius: 15px;
  background-image: linear-gradient(
    to bottom,
    rgba(0, 113, 188, 0.4),
    rgba(0, 113, 188, 0.4)
  );
  background-blend-mode: multiply;
}

.dashboard__live-chat-button__slider {
  //layout
  top: 0;
  left: 0;
  width: 30px;
  height: 30px;
  content: '';
  position: absolute;

  //design
  @include standard-icon('icons/chat-status-offline');
  box-shadow: none;
  border-radius: 15px;
  background-color: #fa4100;

  //animation
  transition: 0.4s;
}

// The loader implementation is from https://codepen.io/rbv912/pen/dYbqLQ
.dashboard__live-chat-button__loader {
  //layout
  top: -3px;
  left: -3px;
  width: 36px;
  height: 36px;
  opacity: 0;
  position: relative;

  //animation
  -webkit-animation: loader-container-keyframes 4.8s linear infinite;
  animation: loader-container-keyframes 4.8s linear infinite;
  transition-delay: 0.4s;

  span {
    //layout
    top: 0;
    left: 0;
    clip: rect(0, 36px, 36px, 18px);
    right: 0;
    width: 36px;
    bottom: 0;
    margin: auto;
    height: 36px;
    display: block;
    position: absolute;

    //animation
    -webkit-animation: loader-inner-keyframes 1.2s linear infinite;
    animation: loader-inner-keyframes 1.2s linear infinite;

    &::after {
      //layout
      top: 0;
      left: 0;
      clip: rect(0, 36px, 36px, 18px);
      right: 0;
      width: 36px;
      bottom: 0;
      margin: auto;
      height: 36px;
      content: '';
      position: absolute;

      //design
      border: 3px solid #fff;
      border-radius: 50%;

      //animation
      -webkit-animation: loader-shape-keyframes 1.2s
        cubic-bezier(0.77, 0, 0.175, 1) infinite;
      animation: loader-shape-keyframes 1.2s cubic-bezier(0.77, 0, 0.175, 1)
        infinite;
    }
  }
}

.dashboard__live-chat-button__loader--is-shown {
  //layout
  opacity: 1;
}

@-webkit-keyframes loader-container-keyframes {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes loader-container-keyframes {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes loader-inner-keyframes {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(220deg);
  }
}
@keyframes loader-inner-keyframes {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(220deg);
  }
}

@-webkit-keyframes loader-shape-keyframes {
  0% {
    -webkit-transform: rotate(-140deg);
  }
  50% {
    -webkit-transform: rotate(-160deg);
  }
  100% {
    -webkit-transform: rotate(140deg);
  }
}
@keyframes loader-shape-keyframes {
  0% {
    transform: rotate(-140deg);
  }
  50% {
    transform: rotate(-160deg);
  }
  100% {
    transform: rotate(140deg);
  }
}

.dashboard__live-chat-button--is-online {
  .dashboard__live-chat-button__slider {
    //layout
    left: 30px;

    //design
    @include standard-icon('icons/chat-status-online');
    box-shadow: 0px 0px 0px 4px rgba(109, 212, 0, 0.5);
    background-color: #6dd400;
  }
}

.dashboard__module {
  //layout
  display: flex;
  flex-direction: column;

  + .dashboard__module {
    //layout
    margin-top: 20px;
  }
}

.dashboard__module--is-kpis {
  .dashboard__module__footer {
    //design
    border: none;
  }
}

.dashboard__module__header {
  //layout
  padding: 0 0 10px 4px;
}

.dashboard__module__title {
  //typo
  color: #008deb;
  font-size: 12px;
  font-weight: 700;
}

.dashboard__module__container {
  //layout
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;

  //design
  border: 1px solid #d6d6d6;
  box-shadow: 0 5px 10px 5px #d6d6d6;
  border-radius: 8px;
  background-color: #fff;
}

.dashboard__module__panels {
  //layout
  display: flex;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.dashboard__module__panel {
  //layout
  flex: 1;
  padding: 12px 5px;
  display: flex;
  overflow: hidden;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  //typo
  color: #8c8c8c;
  font-size: 12px;
  font-weight: 700;

  //design
  background-image: linear-gradient(
    to right,
    #f0f0f0,
    #ebebeb 83%,
    #e2e2e2 100%
  );

  &:first-child {
    //design
    background-image: linear-gradient(
      to right,
      #f0f0f0,
      #ebebeb 83%,
      #e2e2e2 100%
    );
  }

  &:last-child {
    //design
    background-image: linear-gradient(
      to right,
      #e2e2e2 0%,
      #ebebeb 17%,
      #f0f0f0
    );
  }
}

.dashboard__module__panel--is-active {
  //design
  background-color: #fff;
  background-image: none !important;

  //typo
  color: #4a4a4a;

  + .dashboard__module__panel {
    //design
    background-image: linear-gradient(
      to right,
      #e2e2e2 0%,
      #ebebeb 17%,
      #f0f0f0
    );
  }

  .dashboard__module__badge {
    //design
    opacity: 1;
  }
}

.dashboard__module__label {
  //layout
  overflow: hidden;
  text-align: center;

  //typo
  text-overflow: ellipsis;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.dashboard__module__badge {
  //layout
  width: 18px;
  height: 18px;
  padding: 1px;
  display: flex;
  box-sizing: content-box;
  margin-left: 5px;
  align-items: center;
  justify-content: center;

  //typo
  color: #fff;
  font-size: 9px;
  font-weight: 700;

  //design
  opacity: 0.8;
  border-radius: 100%;
  background-color: #fa4100;

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.dashboard__module__content {
  //layout
  flex: 1;
  position: relative;
}

.dashboard__module__content--is-empty {
  //layout
  display: flex;
  align-items: center;
}

.dashboard__module__footer {
  //layout
  display: flex;

  //design
  border-top: 1px solid #f0f0f0;
}

.dashboard__module__action {
  //layout
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  //typo
  color: #008deb;
  font-size: 12px;
  font-weight: 700;

  .dashboard__module__icon {
    //layout
    width: 12px;
    height: 12px;
    margin: 5px;

    path {
      fill: #008deb;
    }
  }
}

.dashboard__module__action--is-disabled {
  //typo
  color: #8c8c8c;

  .dashboard__module__icon {
    path {
      fill: #8c8c8c;
    }
  }

  .dashboard__module__icon--is-refresh {
    animation: 1s linear infinite spin-refresh-icon;
  }
}

@keyframes spin-refresh-icon {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

.dashboard__module__count {
  //layout
  display: inline-block;
  margin-left: 3px;
}

.dashboard__task-list__item,
.dashboard__message-list__item,
.dashboard__request-list__item {
  //layout
  padding: 6px;

  //design
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
}

.dashboard__task-list__item {
  //layout
  padding-left: 12px;
}

.dashboard__task-list--is-full,
.dashboard__message-list--is-full,
.dashboard__request-list--is-full {
  .dashboard__task-list__item,
  .dashboard__message-list__item,
  .dashboard__request-list__item {
    &:last-of-type {
      //design
      border-bottom: none;
    }
  }
}

.dashboard__message-list__item {
  //layout
  padding-left: 12px;
}

.dashboard__message,
.dashboard__request,
.dashboard__task {
  //layout
  padding-right: 4px;

  //typo
  color: #4a4a4a;
  font-size: 11px;
  font-weight: 600;
}

.dashboard__task {
  //typo
  font-size: 12px;
}

.dashboard__request {
  //layout
  display: flex;
}

.dashboard__request--is-appointment {
  //layout
  align-items: center;

  &.dashboard__request--has-owner {
    .dashboard__request__row {
      //layout
      line-height: 1.55;
    }
  }
}

.dashboard__message__date,
.dashboard__request__date,
.dashboard__task__date {
  //typo
  color: #8c8c8c;
  font-weight: 600;
}

.dashboard__task__date,
.dashboard__task__customer-name {
  //typo
  font-weight: 400;
}

.dashboard__task__date--is-alert {
  //typo
  color: #d0021b;
}

.dashboard__message--is-unread,
.dashboard__request--is-unread {
  .dashboard__message__name,
  .dashboard__request__name {
    //layout
    position: relative;
    padding-left: 11px;

    &:before {
      //layout
      content: '';
      top: 50%;
      left: 0;
      width: 6px;
      height: 6px;
      position: absolute;

      //design
      border-radius: 50%;
      background-color: #008deb;

      //animation
      transform: translateY(-50%);
    }
  }
}

.dashboard__request__icon {
  //layout
  margin: 6px 8px 0 0;

  //design
  opacity: 1;

  svg * {
    //design
    fill: #8c8c8c;
  }
}

.dashboard__request__date--is-appointment {
  //layout
  width: 44px;
  margin-right: 6px;

  //typo
  font-size: 9px;
  text-align: center;
  font-weight: 700;
  text-transform: uppercase;
}

.dashboard__request__day {
  //layout
  left: -1px;
  position: relative;

  //typo
  color: #4a4a4a;
  font-size: 18px;
  letter-spacing: -1px;
}

.dashboard__request__container {
  //layout
  flex: 1;
  max-width: calc(100% - 38px);
}

.dashboard__task__container {
  //layout
  position: relative;
  padding-bottom: 5px;
}

.dashboard__task__row,
.dashboard__message__row,
.dashboard__request__row {
  //layout
  display: flex;
  line-height: 2;
  align-items: center;
  justify-content: space-between;
}

.dashboard__task__row {
  //typo
  line-height: 20px;
}

.dashboard__request__row--is-left-aligned {
  //layout
  justify-content: flex-start;
}

.dashboard__request__assoc--is-right-aligned {
  //layout
  margin-left: auto;
}

.dashboard__message__name,
.dashboard__request__name,
.dashboard__task__details {
  //layout
  max-width: calc(100% - 100px);
  overflow-x: hidden;
  text-overflow: ellipsis;

  //typo
  font-size: 12px;
  font-weight: 700;
}

.dashboard__task__details {
  //layout
  width: 50%;
  white-space: nowrap;
}

.dashboard__task__details--is-standalone {
  //layout
  width: 90%;
  max-width: 100%;
}

.dashboard__task__details--has-reply {
  //layout
  margin-left: 25px;

  &::before {
    @include retina('tasks-list/customer-reply-arrows');
    //layout
    left: 0;
    width: 25px;
    height: 20px;
    content: '';
    position: absolute;
  }
}

.dashboard__task__owner {
  //layout
  display: flex;
  padding-right: 15px;
  justify-content: space-between;
}

.dashboard__task__owner-rep,
.dashboard__task__owner-store {
  //typo
  color: #656565;
  font-size: 11px;
  font-weight: 400;
}

.dashboard__message__count {
  //layout
  margin-left: 5px;
}

.dashboard__request__name--is-appointment {
  //typo
  color: #4a4a4a;
  font-weight: 700;
}

.dashboard__message__arrow,
.dashboard__task__arrow,
.dashboard__request__arrow {
  //layout
  display: inline-block;
  margin-left: 5px;

  //typo
  color: #a8a8a8;
  font-size: 8px;
}

.dashboard__task__container-is-right-aligned {
  //layout
  top: 40%;
  right: 0;
  width: 40%;
  display: flex;
  position: absolute;
  align-items: center;
  justify-content: end;
}

.dashboard__task__container-is-right-aligned--is-small {
  //layout
  width: 10%;
}

.dashboard__message__title,
.dashboard__message__content,
.dashboard__request__title,
.dashboard__task__preferred-user,
.dashboard__task__customer-name {
  //layout
  overflow: hidden;
  white-space: nowrap;

  //typo
  line-height: 2;
  text-overflow: ellipsis;
}

.dashboard__message__content,
.dashboard__request__title,
.dashboard__task__preferred-user {
  //typo
  color: #8c8c8c;
}

.dashboard__task__preferred-user {
  //layout
  width: 50%;
  display: flex;
  position: relative;
  align-items: center;
  line-height: 20px;
  justify-content: flex-end;
}

.dashboard__task__preferred-user-icon {
  @include retina('tasks-list/assoc-rep-book');
  //layout
  width: 20px;
  height: 20px;
}

.dashboard__task__customer-name {
  //typo
  color: #0070bb;
}

.dashboard__request__status {
  //layout
  margin-right: 13px;

  //typo
  color: #0070bb;
  text-transform: uppercase;

  //design
  opacity: 0.7;
}

.dashboard__request__status--is-new {
  //typo
  color: #16b64d;
}

.dashboard__request__status--is-pending {
  //typo
  color: #efa503;
}

.dashboard__request__status--is-cs {
  //typo
  color: #5900b3;
}

.dashboard__request__status--is-resolved {
  //typo
  color: #8c8c8c;
}

.dashboard__request__today-label {
  //layout
  margin-right: 5px;

  //typo
  color: #fa4100;
  text-transform: uppercase;
}

.dashboard__request__appointment-time {
  //layout
  margin-right: 5px;
}

.dashboard__task-count-list {
  //layout
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard__task-count-list--is-empty {
  //design
  border-bottom: 1px solid #f0f0f0;
}

.dashboard__task-count {
  //typo
  color: #8c8c8c;
  text-align: center;
}

.dashboard__task-count__title {
  //typo
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.dashboard__task-count__count {
  //layout
  height: 30px;
  line-height: 30px;
  margin-top: 2px;

  //typo
  font-size: 22px;
  font-weight: 700;
}

.dashboard__task-count__count--is-due {
  //typo
  color: #4a4a4a;
}

.dashboard__task-count__count--is-overdue {
  //typo
  color: #fa4100;
}

.dashboard__task-count__count--is-upcoming {
  //typo
  color: #16b64d;
}

.dashboard__task-count__count--is-empty {
  //typo
  color: #8c8c8c;
}

.dashboard__task-list {
  //layout
  border-top: 1px solid #f0f0f0;
}

.dashboard__kpis {
  //layout
  padding: 10px 10px 0 10px;
}

.dashboard__kpis__header {
  //layout
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  //typo
  color: #8c8c8c;
  font-size: 12px;
  font-weight: 600;
}

.dashboard__kpis__cards {
  //layout
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.dashboard__kpi-card {
  //layout
  width: calc((100% - 10px) / 2);
  padding: 10px;
  margin-top: 10px;

  //design
  border: 1px solid #e9e9e9;
  box-shadow: 0 10px 15px -5px #f0f0f0;
  border-radius: 8px;
}

.dashboard__kpi-card__title {
  //layout
  height: 22px;

  //typo
  color: #8c8c8c;
  font-size: 11px;
  font-weight: 600;
  line-height: 11px;
}

.dashboard__kpi-card__content {
  //layout
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.dashboard__kpi-card__value {
  //typo
  color: #4a4a4a;
  font-size: 16px;
  font-weight: 700;
}

.dashboard__kpi-card__rank {
  //typo
  color: #16b64d;
  font-size: 13px;
}

.dashboard__kpi-rank__value {
  //layout
  margin: 0 4px;

  //typo
  color: #008deb;
}

.dashboard__kpi-rank__icon-star {
  //layout
  top: 1px;
  display: inline-block;
  position: relative;
  margin-right: 2px;
}

.dashboard__placeholder__item {
  //layout
  padding: 6px;

  //design
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
}

.dashboard__message-loading-placeholder {
  //layout
  width: 100%;
  height: 58px;
}

.dashboard__message-loading-placeholder__mask-1 {
  //layout
  top: 14px;
  left: 0;
  right: 0;
  height: 8px;
}

.dashboard__message-loading-placeholder__mask-2 {
  //layout
  top: 36px;
  left: 0;
  right: 0;
  height: 8px;
}

.dashboard__message-loading-placeholder__mask-3 {
  //layout
  top: 14px;
  width: 20px;
  right: 0;
  bottom: 0;
}

.dashboard__message-loading-placeholder__mask-4 {
  //layout
  top: 36px;
  width: 40px;
  right: 0;
  bottom: 0;
}

.dashboard__kpis-loading-placeholder {
  //layout
  width: 100%;
  height: 165px;
}

.dashboard__kpis-loading-placeholder__mask-1 {
  //layout
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
}

.dashboard__kpis-loading-placeholder__mask-2 {
  //layout
  top: 10px;
  left: 30%;
  right: 30%;
  height: 17px;
}

.dashboard__kpis-loading-placeholder__mask-3 {
  //layout
  top: 27px;
  left: 0;
  right: 0;
  height: 10px;
}

.dashboard__kpis-loading-placeholder__mask-4 {
  //layout
  top: 82px;
  left: 0;
  right: 0;
  height: 10px;
}

.dashboard__kpis-loading-placeholder__mask-5 {
  //layout
  left: 0;
  right: 0;
  bottom: 0;
  height: 10px;
}

.dashboard__kpis-loading-placeholder__mask-6 {
  //layout
  top: 0;
  left: 0;
  width: 10px;
  bottom: 0;
}

.dashboard__kpis-loading-placeholder__mask-7 {
  //layout
  top: 0;
  left: calc((100% - 30px) / 2 + 10px);
  width: 10px;
  bottom: 0;
}

.dashboard__kpis-loading-placeholder__mask-8 {
  //layout
  top: 0;
  right: 0;
  width: 10px;
  bottom: 0;
}

.dashboard__tasks-loading-placeholder {
  //layout
  width: 100%;
  height: 87px;
}

.dashboard__tasks-loading-placeholder__mask-1 {
  //layout
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
}

.dashboard__tasks-loading-placeholder__mask-2 {
  //layout
  left: 0;
  right: 0;
  bottom: 0;
  height: 20px;
}

.dashboard__tasks-loading-placeholder__mask-3 {
  //layout
  top: 0;
  left: 0;
  width: 20px;
  bottom: 0;
}

.dashboard__tasks-loading-placeholder__mask-4 {
  //layout
  top: 0;
  right: 0;
  width: 20px;
  bottom: 0;
}

.dashboard__tasks-loading-placeholder__mask-5 {
  //layout
  top: 0;
  left: calc((100% - 80px) / 3 + 20px);
  bottom: 0;
  width: 20px;
}

.dashboard__tasks-loading-placeholder__mask-6 {
  //layout
  top: 0;
  right: calc((100% - 80px) / 3 + 20px);
  bottom: 0;
  width: 20px;
}

.dashboard__return-to-chat-button {
  //layout
  z-index: 2;
}
