@import '2_mixins/standard-icon';

.arrow-pagination {
  //layout
  height: 56px;
  padding: 10px;

  //typo
  display: flex;
  justify-content: center;
}

  .arrow-pagination__label {
    //layout
    margin: 0 5px;
    display: inline-block;
    min-width: 56px;

    //typo
    color: #4a4a4a;
    font-size: 12px;
    text-align: center;
    font-weight: 600;
    line-height: 36px;
  }

  .arrow-pagination__button {
    //layout
    width: 48px;
    height: 36px;
    margin: 0 1.5px;
    vertical-align: top;

    //typo
    color: #ffffff;
    font-size: 12px;
    text-align: center;
    font-weight: 600;

    //design
    border: none;
    border-radius: 2px;
    background-color: #0071bc;

    //events
    -webkit-user-select: none;
    -webkit-touch-callout: none;

    &:disabled {
      //design
      background-color: #d3e1ee;
    }
  }

    .arrow-pagination__button--is-left-arrow {
      @include standard-icon('icons/arrow-pagination-left-white');

      //layout
      width: 36px;
    }

    .arrow-pagination__button--is-right-arrow {
      @include standard-icon('icons/arrow-pagination-right-white');

      //layout
      width: 36px;
    }
