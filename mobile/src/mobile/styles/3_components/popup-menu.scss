.popup-menu {
  //layout
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  position: absolute;
}

.popup-menu--is-tabbar {
  //layout
  left: -100%;
  position: fixed;

  //animation
  transition: left 0.3s step-end;
}
.popup-menu--is-main-menu {
  //layout
  z-index: 0;
}

.popup-menu__overlay {
  //layout
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;

  //design
  opacity: 0;
  background-color: #000000;

  //animation
  transition: opacity 0.3s;
}

.popup-menu__list {
  //layout
  left: 0;
  width: 200px;
  height: auto;
  bottom: 0;
  z-index: 1;
  position: absolute;

  //design
  opacity: 0;
  border-radius: 8px;
  background-color: #008deb;
}

.popup-menu__list--is-tabbar {
  //layout
  left: 50%;
  bottom: 55px;

  //animation
  transform: translateX(-50%);
  transition: opacity 0.1s, bottom 0.1s;
}

.popup-menu__list--is-main-menu {
  //layout
  visibility: hidden;
  left: calc(100% + 25px);

  //animation
  transition: opacity 0.3s, visibility 0.3s;
}

.popup-menu__list__item {
  //layout
  height: 44px;
  padding: 0 25px 0 20px;
  overflow: hidden;
  position: relative;
  max-width: 100%;
  white-space: nowrap;

  //typo
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  line-height: 44px;
  text-overflow: ellipsis;

  //design
  border-bottom: 1px solid rgba(0, 113, 188, 0.3);

  //events
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &:last-child {
    //design
    border: none;
  }

  &::before {
    //layout
    top: 50%;
    right: 10px;
    display: flex;
    position: absolute;
    align-items: center;
    justify-content: center;

    //typo
    font-size: 9px;

    //design
    opacity: 0.4;

    //animation
    transform: translateY(-50%);
  }
}

.popup-menu--is-activated {
  //layout
  left: 0;

  //animation
  transition: left 0.3s step-start;

  .popup-menu__overlay {
    //design
    opacity: 0.6;
  }

  .popup-menu__list {
    //design
    opacity: 1;
  }

  .popup-menu__list--is-tabbar {
    //layout
    bottom: 75px;
    bottom: calc(constant(safe-area-inset-bottom) + 75px);
    bottom: calc(env(safe-area-inset-bottom) + 75px);

    //animation
    transition: opacity 0.3s, bottom 0.3s;
  }

  .popup-menu__list--is-main-menu {
    //layout
    visibility: visible;
  }
}
