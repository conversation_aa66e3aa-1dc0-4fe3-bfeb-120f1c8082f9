<!DOCTYPE html>
<html class="no-js">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Salesfloor</title>
    <meta name="description" content="Salesfloor Mobile" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />

    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->

    <!-- build:css(.) styles/vendor.css -->
    <link
      rel="stylesheet"
      href="vendor/sass-bootstrap/dist/css/bootstrap.css"
    />
    <link rel="stylesheet" href="vendor/ng-tags-input/ng-tags-input.min.css" />
    <link rel="stylesheet" href="vendor/croppie/ng-croppie.min.css" />
    <link rel="stylesheet" href="vendor/jquery-ui/jquery-ui.min.css" />
    <!-- endbuild -->

    <link rel="stylesheet" href="styles/main.css" />
  </head>
  <body>
    <flash-message></flash-message>
    <div class="container container--is-main">
      <ng-view></ng-view>
    </div>
    <feedback></feedback>
    <product-modal></product-modal>
    <slideout></slideout>

    <!-- build:js(.) scripts/vendor.js -->
    <script src="node_modules/systemjs/dist/s.min.js"></script>
    <script src="node_modules/systemjs/dist/extras/named-register.min.js"></script>
    <script src="node_modules/@babel/polyfill/dist/polyfill.min.js"></script>
    <script src="node_modules/unorm/lib/unorm.js"></script>
    <script src="node_modules/jquery/dist/jquery.min.js"></script>
    <script src="node_modules/lodash/lodash.min.js"></script>
    <script src="node_modules/angular/angular.js"></script>
    <script src="node_modules/aws-sdk/dist/aws-sdk.min.js"></script>
    <script src="node_modules/cloudinary-jquery-file-upload/cloudinary-jquery-file-upload.min.js"></script>
    <script src="node_modules/intl-tel-input/build/js/intlTelInput-jquery.min.js"></script>
    <script src="node_modules/intl-tel-input/build/js/utils.js"></script>
    <script src="node_modules/moment/min/moment-with-locales.min.js"></script>
    <script src="node_modules/moment-timezone/builds/moment-timezone-with-data.min.js"></script>
    <script src="node_modules/i18next/i18next.min.js"></script>
    <script src="node_modules/i18next-xhr-backend/i18nextXHRBackend.min.js"></script>
    <script src="node_modules/hammerjs/hammer.min.js"></script>
    <script src="node_modules/croppie/croppie.min.js"></script>
    <script src="vendor/croppie/ng-croppie.js"></script>
    <script src="vendor/firebase/firebase.js"></script>
    <script src="vendor/sass-bootstrap/dist/js/bootstrap.min.js"></script>
    <script src="vendor/jquery-ui/jquery-ui.min.js"></script>
    <script src="vendor/jquery.ui.touch-punch.js"></script>
    <script src="vendor/tz-detect.js"></script>

    <script src="node_modules/algoliasearch/dist/algoliasearch.angular.min.js"></script>
    <script src="node_modules/angular-animate/angular-animate.min.js"></script>
    <script src="node_modules/angular-cookies/angular-cookies.min.js"></script>
    <script src="node_modules/angular-hammer/angular.hammer.min.js"></script>
    <script src="node_modules/angular-local-storage/dist/angular-local-storage.min.js"></script>
    <script src="node_modules/angular-resource/angular-resource.min.js"></script>
    <script src="node_modules/angular-route/angular-route.min.js"></script>
    <script src="node_modules/angular-sanitize/angular-sanitize.min.js"></script>
    <script src="node_modules/angular-touch/angular-touch.min.js"></script>
    <script src="node_modules/angularfire/dist/angularfire.min.js"></script>
    <script src="node_modules/angularjs-scroll-glue/src/scrollglue.js"></script>
    <script src="node_modules/ng-i18next/dist/ng-i18next.min.js"></script>
    <script src="node_modules/ng-infinite-scroll/build/ng-infinite-scroll.js"></script>
    <script src="vendor/ng-intl-tel-input.min.js"></script>
    <script src="node_modules/ng-onload/release/ng-onload.min.js"></script>
    <script src="vendor/ng-tags-input/ng-tags-input.min.js"></script>
    <script src="node_modules/salesfloor-fetch-jsonapi/dist/salesfloor-fetch-jsonapi.js"></script>
    <script src="node_modules/lz-string/libs/lz-string.min.js"></script>

    <script src="node_modules/react/umd/react.production.min.js"></script>
    <script src="node_modules/react-dom/umd/react-dom.production.min.js"></script>
    <!-- endbuild -->

    <!-- build:js scripts/app.js -->
    <script src="common/utils/permission-utils.js"></script>

    <script src="common/scripts/app-configs.js"></script>

    <script src="common/scripts/utils.js"></script>
    <script src="common/scripts/deeplink.js"></script>
    <script src="common/scripts/deviceready.js"></script>
    <script src="common/scripts/login-utils.js"></script>
    <script src="common/scripts/moment.js"></script>
    <script src="common/scripts/app.js"></script>

    <script src="common/scripts/services/api.js"></script>
    <script src="common/scripts/services/login.js"></script>
    <script src="common/scripts/services/apiCache.js"></script>
    <script src="common/scripts/services/config.js"></script>
    <script src="common/scripts/services/messages.js"></script>
    <script src="common/scripts/services/contact.js"></script>
    <script src="common/scripts/services/share.js"></script>
    <script src="common/scripts/services/events.js"></script>
    <script src="common/scripts/services/activityLogService.js"></script>
    <script src="common/scripts/services/connectivity.js"></script>
    <script src="common/scripts/services/misc.js"></script>
    <script src="common/scripts/services/auth.js"></script>
    <script src="common/scripts/services/reports.js"></script>
    <script src="common/scripts/services/lookbooks.js"></script>
    <script src="common/scripts/services/chat/chat.js"></script>
    <script src="common/scripts/services/chat/chatUtils.js"></script>
    <script src="common/scripts/services/chat/chatEvents.js"></script>
    <script src="common/scripts/services/chat/chatApi.js"></script>
    <script src="common/scripts/services/chat/chatPresence.js"></script>
    <script src="common/scripts/services/chat/chatRequest.js"></script>
    <script src="common/scripts/services/chat/videoChat.js"></script>
    <script src="common/scripts/services/moderation.js"></script>
    <script src="common/scripts/services/cans.js"></script>
    <script src="common/scripts/services/draft.js"></script>
    <script src="common/scripts/services/logger.js"></script>
    <script src="common/scripts/services/repUploadedPhotos.js"></script>
    <script src="common/scripts/services/intentService.js"></script>
    <script src="common/scripts/services/textMessaging.js"></script>
    <script src="common/scripts/services/shareToExternalApp.js"></script>
    <script src="common/scripts/services/pushNotification.js"></script>
    <script src="common/scripts/services/i18n.js"></script>
    <script src="common/scripts/services/tasks.js"></script>
    <script src="common/scripts/services/permissions.js"></script>
    <script src="common/scripts/services/featuresEnabled.js"></script>
    <script src="common/scripts/services/feedback.js"></script>
    <script src="common/scripts/services/firebase.js"></script>
    <script src="common/scripts/services/tracking.js"></script>
    <script src="common/scripts/services/requestsPolling.js"></script>
    <script src="common/scripts/services/flashMessage.js"></script>
    <script src="common/scripts/services/file.js"></script>
    <script src="common/scripts/services/navService.js"></script>
    <script src="common/scripts/services/loader.js"></script>
    <script src="common/scripts/services/products/products.js"></script>
    <script src="common/scripts/services/products/productsDrawer.js"></script>
    <script src="common/scripts/services/products/product.js"></script>
    <script src="common/scripts/services/plainTextFormatter.js"></script>
    <script src="common/scripts/services/requests.js"></script>
    <script src="common/scripts/services/jsonapi.js"></script>
    <script src="common/scripts/services/linkService.js"></script>
    <script src="common/scripts/services/socialshop.js"></script>
    <script src="common/scripts/services/logoService.js"></script>
    <script src="common/scripts/services/scrollPosition.js"></script>
    <script src="common/scripts/services/corporateTask.js"></script>
    <script src="common/scripts/services/groupTask.js"></script>
    <script src="common/scripts/services/address.js"></script>
    <script src="common/scripts/services/device.js"></script>
    <script src="common/scripts/services/facetFilter.js"></script>
    <script src="common/scripts/services/calendar.js"></script>
    <script src="common/scripts/services/debounce.js"></script>
    <script src="common/scripts/services/dashboard.js"></script>
    <script src="common/scripts/services/utils.js"></script>
    <script src="common/scripts/services/stringManipulation.js"></script>
    <script src="common/scripts/secureStorage.js"></script>
    <script src="common/scripts/services/user.js"></script>
    <script src="common/scripts/filters/live-request-title.js"></script>
    <script src="common/scripts/filters/custom-filter.js"></script>
    <script src="common/scripts/directives/datetimepicker.js"></script>
    <script src="common/scripts/directives/angular-ellipsis.js"></script>
    <script src="common/scripts/directives/input-enter.js"></script>
    <script src="common/scripts/directives/event-message.js"></script>
    <script src="common/scripts/directives/filters.js"></script>
    <script src="common/scripts/directives/facetFilter.js"></script>
    <script src="common/scripts/directives/focusOn.js"></script>
    <script src="common/scripts/directives/onTyping.js"></script>
    <script src="common/scripts/directives/scroller.js"></script>
    <script src="common/scripts/directives/sortable-list.js"></script>
    <script src="common/scripts/directives/feedback.js"></script>
    <script src="common/scripts/directives/productModal.js"></script>
    <script src="common/scripts/directives/flashMessage.js"></script>
    <script src="common/scripts/directives/scrollToHere.js"></script>
    <script src="common/scripts/directives/listPagination.js"></script>
    <script src="common/scripts/directives/insertLinkButton.js"></script>
    <script src="common/scripts/directives/highlightTextarea.js"></script>
    <script src="common/scripts/directives/messageLanguageSelector.js"></script>
    <script src="common/scripts/directives/emailTemplateSelector.js"></script>
    <script src="common/scripts/services/device.js"></script>
    <script src="common/scripts/services/assetManagement.js"></script>
    <script src="common/scripts/services/feedValidation.js"></script>
    <script src="common/scripts/directives/liveLangSwitcher.js"></script>
    <script src="common/scripts/directives/transactionList.js"></script>
    <script src="common/scripts/directives/share/shareAttachments.js"></script>
    <script src="common/scripts/directives/share/sharePreviewItem.js"></script>
    <script src="common/scripts/directives/share/shareSeparator.js"></script>
    <script src="common/scripts/directives/contactTagsList.js"></script>
    <script src="common/scripts/directives/statGraph.js"></script>
    <script src="common/scripts/directives/filterButton.js"></script>
    <script src="common/scripts/directives/autocomplete.js"></script>
    <script src="common/scripts/directives/advancedSearch.js"></script>
    <script src="common/scripts/directives/actionButtons.js"></script>
    <script src="common/scripts/directives/ctaFooter.js"></script>
    <script src="common/scripts/directives/ctaFooterFlexible.js"></script>
    <script src="common/scripts/directives/productsList.js"></script>
    <script src="common/scripts/directives/productsDrawer.js"></script>
    <script src="common/scripts/directives/productsCarousel.js"></script>
    <script src="common/scripts/directives/longpressToCopy.js"></script>
    <script src="common/scripts/directives/selector.js"></script>
    <script src="common/scripts/directives/cardsList.js"></script>
    <script src="common/scripts/directives/invalidVariantMask.js"></script>
    <script src="common/scripts/directives/scrollShrink.js"></script>
    <script src="common/scripts/directives/productsDrawerList.js"></script>
    <script src="common/scripts/directives/clipboardCopyButton.js"></script>
    <script src="common/scripts/directives/checkboxField.js"></script>
    <script src="common/scripts/directives/floatingButton.js"></script>
    <script src="common/scripts/directives/previewButton.js"></script>
    <script src="common/scripts/directives/collapsiblePanel.js"></script>
    <script src="common/scripts/directives/emptyState.js"></script>
    <script src="common/scripts/directives/asset-library/assetLibrary.js"></script>
    <script src="common/scripts/directives/searchField.js"></script>
    <script src="common/scripts/directives/pagination.js"></script>
    <script src="common/scripts/directives/reactBridge.js"></script>
    <script src="common/scripts/directives/productHeader.js"></script>
    <script src="common/scripts/directives/product-library/productLibrary.js"></script>
    <script src="common/scripts/directives/product-library/productSearchCategories.js"></script>
    <script src="common/scripts/directives/product-library/productSearchField.js"></script>
    <script src="common/scripts/directives/product-library/productSearchInfo.js"></script>
    <script src="common/scripts/directives/chatQuickMenu.js"></script>
    <script src="common/scripts/directives/chatCompose.js"></script>
    <script src="common/scripts/directives/chatRoom.js"></script>
    <script src="common/scripts/directives/dualRangeSlider.js"></script>
    <script src="common/scripts/directives/restrictInput.js"></script>
    <script src="common/scripts/directives/piiEmailValidator.js"></script>
    <script src="common/scripts/directives/pinchZoom.js"></script>
    <script src="common/scripts/directives/clickOutside.js"></script>
    <script src="common/scripts/controllers/login.js"></script>
    <script src="common/scripts/controllers/login/auto.js"></script>
    <script src="common/scripts/controllers/login/sso.js"></script>
    <script src="common/scripts/controllers/login/mfa.js"></script>
    <script src="common/scripts/directives/aiMessagingButton.js"></script>

    <script src="common/scripts/controllers/share/share.js"></script>
    <script src="common/scripts/controllers/share/share-email.js"></script>
    <script src="common/scripts/controllers/share/share-instagram.js"></script>
    <script src="common/scripts/controllers/share/share-pinterest.js"></script>
    <script src="common/scripts/controllers/share/share-facebook.js"></script>
    <script src="common/scripts/controllers/share/share-twitter.js"></script>
    <script src="common/scripts/controllers/share/product-details.js"></script>
    <script src="common/scripts/controllers/share/socialshop-list.js"></script>
    <script src="common/scripts/controllers/share/socialshop-post.js"></script>
    <script src="common/scripts/controllers/share/socialshop-settings.js"></script>
    <script src="common/scripts/controllers/storefront-events.js"></script>
    <script src="common/scripts/controllers/messages-store.js"></script>
    <script src="common/scripts/controllers/messages/messages-compose.js"></script>
    <script src="common/scripts/controllers/messages/messages-train.js"></script>
    <script src="common/scripts/controllers/contacts/contacts.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-filter.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-notes.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-import.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-address-edit.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-social.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-tags.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-activity-log.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-events.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-pre-customer-actions.js"></script>
    <script src="common/scripts/controllers/contacts/contacts-transactions.js"></script>
    <script src="common/scripts/controllers/requests/requests-add-note.js"></script>
    <script src="common/scripts/controllers/live.js"></script>
    <script src="common/scripts/controllers/reports.js"></script>
    <script src="common/scripts/controllers/chat/chat-thread.js"></script>
    <script src="common/scripts/controllers/chat/chat-history.js"></script>
    <script src="common/scripts/controllers/chat/chat-history-list.js"></script>
    <script src="common/scripts/controllers/chat/chat-transfer-to-cs.js"></script>
    <script src="common/scripts/controllers/storefront.js"></script>
    <script src="common/scripts/controllers/scan.js"></script>
    <script src="common/scripts/controllers/lookbooks.js"></script>
    <script src="common/scripts/controllers/looks.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-compose.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-thread.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-list.js"></script>
    <script src="common/scripts/controllers/text-messaging/text-messaging-request-message.js"></script>
    <script src="common/scripts/controllers/text-messaging/compose-message.js"></script>
    <script src="common/scripts/controllers/text-messaging/attachment-dialog.js"></script>
    <script src="common/scripts/controllers/text-messaging/address-book.js"></script>
    <script src="common/scripts/controllers/tasks/tasks-list.js"></script>
    <script src="common/scripts/controllers/tasks/tasks.js"></script>

    <!--serivces scripts-->
    <script src="backoffice/app/app.js"></script>
    <script src="backoffice/app/version.js"></script>
    <script src="backoffice/app/config-env.js"></script>
    <script src="backoffice/app/common/services/backofficeModal.js"></script>
    <script src="backoffice/app/common/services/backofficeListing.js"></script>
    <script src="backoffice/app/common/services/permissions.js"></script>
    <script src="backoffice/app/common/components/NavBar.js"></script>
    <script src="backoffice/app/common/directives/navbar.js"></script>
    <script src="backoffice/app/common/directives/menubar.js"></script>
    <script src="backoffice/app/common/directives/slideout.js"></script>
    <script src="backoffice/app/common/directives/boSwitchToggle.js"></script>
    <script src="backoffice/app/common/directives/boBreadcrumbs.js"></script>
    <script src="backoffice/app/common/directives/datepicker.js"></script>
    <script src="backoffice/app/common/directives/product-library/productSearchAutoComplete.js"></script>
    <script src="backoffice/app/common/directives/product-library/productSearchSelectionStatus.js"></script>
    <script src="backoffice/app/menu/directives/menu.js"></script>
    <script src="backoffice/app/menu/directives/menuItem.js"></script>
    <script src="backoffice/app/login/connect.js"></script>
    <script src="backoffice/app/logout/logout.js"></script>
    <script src="backoffice/app/corporate-task/corporate-task-list.js"></script>
    <script src="backoffice/app/corporate-task/corporate-task-create.js"></script>
    <script src="backoffice/app/corporate-task/corporate-task-view.js"></script>
    <script src="backoffice/app/user-management/user-management-list.js"></script>
    <script src="backoffice/app/user-management/invited-users-list.js"></script>
    <script src="backoffice/app/user-management/services/userManagement.js"></script>
    <script src="backoffice/app/user-management/services/userManagementTracking.js"></script>
    <script src="backoffice/app/library-panel/library-panel-specialty.js"></script>
    <script src="backoffice/app/library-panel/library-panel-target-stores.js"></script>
    <script src="backoffice/app/library-panel/library-panel-filter.js"></script>
    <script src="backoffice/app/library-panel/library-panel-user-details.js"></script>
    <script src="backoffice/app/library-panel/library-panel-create-user.js"></script>
    <script src="backoffice/app/library-panel/library-panel-update-active-user.js"></script>
    <script src="backoffice/app/library-panel/library-panel-users-filter.js"></script>
    <script src="backoffice/app/asset-management/asset-management-list.js"></script>
    <script src="backoffice/app/asset-management/asset-management-view.js"></script>
    <script src="backoffice/app/asset-management/asset-management-create.js"></script>
    <script src="backoffice/app/feed-validation/feed-validation-list.js"></script>
    <script src="backoffice/app/feed-validation/feed-validation-create.js"></script>
    <script src="backoffice/app/feed-validation/feed-validation-view.js"></script>
    <script src="backoffice/app/asset-management/directives/assetInfoPanel.js"></script>
    <script src="backoffice/app/asset-management/directives/assetRelatedTasksPanel.js"></script>
    <script src="backoffice/app/dynamic-routing/dynamic-routing-view.js"></script>
    <script src="backoffice/app/library-panel/library-panel-asset-management-filter.js"></script>
    <script src="backoffice/app/library-panel/library-panel-feed-validation-filter.js"></script>

    <!-- endbuild -->

    <script src="./sf-mobile/services.min.js"></script>

    <script>
      // oauth.js is expecting `window.jQuery`
      if (!window.jQuery && window.$ && window.$.fn && window.$.fn.jquery) {
        window.jQuery = window.$;
      }
    </script>

    <script>
      (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        (i[r] =
          i[r] ||
          function () {
            (i[r].q = i[r].q || []).push(arguments);
          }),
          (i[r].l = 1 * new Date());
        (a = s.createElement(o)), (m = s.getElementsByTagName(o)[0]);
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m);
      })(window, document, 'script', 'scripts/analytics.js', 'ga');
    </script>

    <script>
      const sfAppTypes = {
        backoffice: 'backoffice',
      };

      window.sfApp = sfAppTypes.backoffice;

      function loadServices() {
        return Promise.all([
          System.import('sound'),
          System.import('storage'),
          System.import('sessionManager'),
        ]);
      }

      function bootstrapApp(options) {
        loadServices()
          .then(([sound, storage, sessionManager]) => {
            angular
              .module('sfmobileApp')
              .factory('soundService', [
                '$window',
                'deviceService',
                (a, b) => new sound.default(a, b),
              ])
              .factory('storageService', [() => storage.default.Instance()])
              .factory('sessionManagerService', [
                '$http',
                a => new sessionManager.default(a),
              ]);
            angular.bootstrap(document, ['sfmobileApp'], options || {});
          })
          .catch(err => console.error(err));
      }

      bootstrapApp({ strictDi: true });
    </script>
  </body>
</html>
