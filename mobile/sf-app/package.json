{"name": "sf-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build --mode=development", "build:ci": "tsc && vite build --mode=production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest --colors --passWithNoTests", "test:ci": "jest --passWithNoTests --coverage --ci --coverageReporters=json-summary lcov", "testWithCoverage": "jest --colors --passWithNoTests --collectCoverage", "buildClasses": "npm run compileClasses && npm run minifyClasses", "compileClasses": "tsc --build ./src/classes/tsconfig.json", "minifyClasses": "./node_modules/.bin/esbuild /tmp/sf-mobile-services.js --minify --outfile=../cordova/www/sf-mobile/services.min.js", "buildClassesBo": "npm run compileClassesBo && npm run minifyClassesBo", "compileClassesBo": "tsc --build ./src/classes/tsconfig.json", "minifyClassesBo": "./node_modules/.bin/esbuild /tmp/sf-mobile-services.js --minify --outfile=../dist/sf-mobile/services.min.js", "storybook": "storybook dev --no-open -p 6006"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^6.1.2", "@mui/material": "^6.1.2", "@mui/x-date-pickers": "^7.22.3", "@reduxjs/toolkit": "^2.2.5", "@types/zxcvbn": "^4.4.5", "axios": "^1.7.2", "broadcast-channel": "^7.0.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "firebase": "^11.3.1", "i18next": "^23.11.5", "i18next-http-backend": "^2.5.2", "immutable": "^4.3.7", "micromark": "^4.0.2", "react": "^18.3.1", "react-avatar-editor": "^13.0.2", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-i18next": "^14.1.2", "react-redux": "^9.1.2", "react-router": "^7.1.5", "zod": "^3.23.8", "zxcvbn": "^4.4.2"}, "devDependencies": {"@chromatic-com/storybook": "^1.9.0", "@joshwooding/vite-plugin-react-docgen-typescript": "^0.4.1", "@storybook/addon-a11y": "^8.2.9", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "@storybook/blocks": "^8.2.9", "@storybook/builder-vite": "^8.3.4", "@storybook/react": "^8.2.9", "@storybook/react-vite": "^8.2.9", "@storybook/test": "^8.2.9", "@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@types/cordova": "^11.0.3", "@types/jest": "^29.5.12", "@types/node": "^22.13.5", "@types/react": "^18.3.3", "@types/react-avatar-editor": "^13.0.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.3.1", "cordova-plugin-camera": "^7.0.0", "cordova-plugin-file": "^8.1.3", "cordova-plugin-media": "^7.0.0", "esbuild": "^0.25.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-storybook": "^0.8.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "react-docgen-typescript": "^2.2.2", "storybook": "^8.2.9", "storybook-addon-remix-react-router": "^3.0.1", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "typescript": "^5.5.2", "vite": "^6.2.0", "vite-plugin-static-copy": "^2.2.0"}}