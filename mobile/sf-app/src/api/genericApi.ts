import http from '@/services/http';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const logAnalyticsEvent = ({
  event,
  event_data,
}: {
  event: string;
  event_data?: Record<string, unknown>;
}) => {
  http.post('/v1/analytics-events', {
    data: {
      type: 'analytics-events',
      attributes: {
        event,
        event_data,
      },
    },
  });
};

interface IUploadImageResp {
  type: string;
  id: string;
  attributes: {
    origin: string;
    destination: string;
    cdn: string;
    user_id: string;
    is_moderator_flagged: boolean;
  };
}

export const uploadImage = createAsyncThunk<
  IUploadImageResp,
  {
    pictureBase64: string;
  }
>('generic/uploadImage', async ({ pictureBase64 }, { rejectWithValue }) => {
  const body = {
    data: {
      type: 'image_upload',
      attributes: {
        origin: 'compose',
        source: pictureBase64,
      },
    },
  };
  const response = await http.post('/images/upload', body);
  if (response.status === 'ok') {
    return response.data as IUploadImageResp;
  }
  return rejectWithValue(response);
});
