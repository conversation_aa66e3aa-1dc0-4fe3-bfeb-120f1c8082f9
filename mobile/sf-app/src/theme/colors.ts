export const colors = {
  primary: {
    100: '#0071BC',
    50: '#008DEB',
    20: '#62BAF3',
    10: '#9ED8FD',
    5: '#C8E9FF',
    0: '#EAF7FF',
  },
  grey: {
    100: '#333F46',
    80: '#58666F',
    60: '#788893',
    40: '#ADBBC5',
    10: '#F8F8F8',
    5: '#DCE3E8',
    0: '#F3F6F9',
  },
  success: {
    100: '#398067',
    50: '#30B787',
    0: '#80E3C0',
  },
  warning: {
    100: '#C77A07',
    50: '#DEAC60',
    0: '#FCE4C0',
  },
  error: {
    100: '#9B3E29',
    50: '#CA4C31',
    0: '#FFC2B5',
  },
};

declare module '@mui/material/styles' {
  interface Palette {
    outline: Partial<Palette['primary']> & Partial<Palette['action']>;
    border: Partial<Palette['primary']> & Partial<Palette['action']>;
  }

  interface PaletteOptions {
    outline?: Partial<PaletteOptions['primary']> & Partial<Palette['action']>;
    border?: Partial<Palette['primary']> & Partial<Palette['action']>;
  }
}
