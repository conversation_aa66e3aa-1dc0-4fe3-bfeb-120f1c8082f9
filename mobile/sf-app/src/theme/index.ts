import { createTheme, alpha } from '@mui/material';
import { colors } from './colors';

declare module '@mui/material/styles' {
  interface TypographyVariants {
    label: React.CSSProperties;
    regular: React.CSSProperties;
    listHeader: React.CSSProperties;
    html: React.CSSProperties;
  }

  // allow configuration using `createTheme()`
  interface TypographyVariantsOptions {
    label?: React.CSSProperties;
    regular?: React.CSSProperties;
    listHeader?: React.CSSProperties;
    html?: React.CSSProperties;
  }
}

// Update the Typography's variant prop options
declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    label: true;
    regular: true;
    listHeader: true;
    html: true;
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    link: true;
    navigation: true;
  }
}

let theme = createTheme({
  breakpoints: {
    values: {
      xs: 320,
      sm: 767,
      md: 1024,
      lg: 1280,
      xl: 1440,
    },
  },
  palette: {
    primary: {
      main: colors.primary[50],
      light: colors.primary[20],
      dark: colors.primary[100],
      contrastText: '#FFF',
    },
    grey: {
      900: colors.grey[100],
      700: colors.grey[80],
      500: colors.grey[60],
      300: colors.grey[40],
      200: colors.grey[10],
      100: colors.grey[5],
      50: colors.grey[0],
    },
    error: {
      main: colors.error[50],
      light: colors.error[0],
      dark: colors.error[100],
    },
    success: {
      main: colors.success[50],
      light: colors.success[0],
      dark: colors.success[100],
    },
    warning: {
      main: colors.warning[50],
      light: colors.warning[0],
      dark: colors.warning[100],
    },
    text: {
      primary: colors.grey[100],
      secondary: colors.grey[80],
      disabled: colors.grey[40],
    },
    background: {
      paper: '#fff',
      default: 'transparent', //used in CssBaseline to set background to the body
    },
    // TODO: define colors for actions
    action: {
      // active: '',
      // hover: '',
      // hoverOpacity: 0.04,
      selected: colors.primary[50],
      selectedOpacity: 0.08,
      // disabled: '',
      // disabledBackground: '',
      // disabledOpacity: 0.38,
      // focus: '',
      // focusOpacity: 0.12,
      // activatedOpacity: 0.12,
    },
    outline: {
      main: colors.grey[40],
    },
    border: {
      main: '#c7c7c7', // TODO: find similar color in palette or add this one.
    },
  },
  typography: {
    fontFamily: 'Open Sans, sans-serif',
    fontWeightLight: 400,
    fontWeightRegular: 500,
    fontWeightMedium: 600,
  },
  shape: {
    borderRadius: 2,
  },
});

theme = createTheme(theme, {
  shadows: [
    ...theme.shadows.with(
      1,
      `0px 1px 2px 0px ${alpha(theme.palette.grey[900], 0.2)}`,
    ),
  ],
  typography: {
    h1: {
      fontSize: '1rem', //16px
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
    },
    h2: {
      fontSize: '0.9375rem', //15px
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
    },
    h3: {
      fontSize: '0.875rem',
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
    },
    h4: {
      fontSize: '0.8125rem',
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
    },
    h5: {
      fontSize: '0.75rem',
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
    },
    h6: {
      fontSize: '0.6875rem',
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
    },
    label: {
      fontSize: 12,
      color: theme.palette.grey[500],
    },
    regular: {
      fontSize: 12,
      color: theme.palette.text.primary,
    },
    body1: {
      fontSize: 13,
      color: theme.palette.text.primary,
    },
    body2: {
      fontSize: 14,
    },
    subtitle1: {
      fontSize: '0.75rem',
      color: theme.palette.grey[500],
      fontWeight: theme.typography.fontWeightLight,
    },
    listHeader: {
      fontSize: 13,
      color: '#8c8c8c', //TODO: use standard palette
      textTransform: 'uppercase',
    },
    html: {
      fontSize: 14,
      fontWeight: theme.typography.fontWeightLight,
      color: theme.palette.text.primary,
      '& ul': {
        paddingLeft: 20,
        margin: 0,
        listStyleType: 'disc',
      },
    },
  },
  components: {
    MuiTypography: {
      defaultProps: {
        variantMapping: {
          label: 'p',
          regular: 'p',
          listHeader: 'span',
          html: 'p',
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          fontWeight: theme.typography.fontWeightRegular,
          textTransform: 'none',
          '&.Mui-selected': {
            color: theme.palette.primary.dark,
          },
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: theme.shape.borderRadius * 2,
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        message: {
          variants: [
            {
              props: { variant: 'filled' },
              style: {
                color: theme.palette.primary.contrastText,
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                fontSize: 12,
              },
            },
          ],
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          variants: [
            {
              props: { variant: 'link' },
              style: {
                textTransform: 'none',
                transition: 'none',
                background: 'none',
                color: theme.palette.primary.dark,
                fontSize: 12,
                fontWeight: theme.typography.fontWeightRegular,
                padding: 0,
                textDecoration: 'underline',
              },
            },
            {
              props: { variant: 'text' },
              style: {
                textTransform: 'none',
                transition: 'none',
                background: 'none',
                color: theme.palette.primary.main,
                fontWeight: theme.typography.fontWeightLight,
                padding: 0,
                minWidth: 'unset',
                '&.Mui-disabled': {
                  opacity: theme.palette.grey[40],
                },
                '&:hover': {
                  boxShadow: 'none',
                },
              },
            },
            {
              props: { variant: 'contained' },
              style: {
                textTransform: 'none',
                fontSize: '1rem',
                boxShadow: 'none',
                transition: 'none',
                fontWeight: theme.typography.fontWeightRegular,
                backgroundColor: theme.palette.primary.main,
                borderRadius: theme.shape.borderRadius * 2,
                '&.Mui-disabled': {
                  opacity: 0.25,
                  color: theme.palette.common.white,
                  backgroundColor: theme.palette.primary.main,
                },
                '&:hover': {
                  boxShadow: 'none',
                  backgroundColor: theme.palette.primary.dark,
                },
                [theme.breakpoints.down('sm')]: {
                  width: '100%',
                  backgroundColor: theme.palette.primary.dark,
                  borderRadius: theme.shape.borderRadius,
                },
              },
            },
            {
              props: { variant: 'outlined' },
              style: {
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: theme.typography.fontWeightRegular,
                color: theme.palette.primary.dark,
                BorderColor: theme.palette.primary.dark,
                boxShadow: 'none',
                '&:hover': {
                  boxShadow: 'none',
                },
                [theme.breakpoints.down('sm')]: {
                  width: '100%',
                  transition: 'none',
                },
              },
            },
            {
              props: { variant: 'navigation' },
              style: {
                textTransform: 'none',
                transition: 'none',
                background: 'none',
                padding: 0,
                color: theme.palette.common.black,
                fontSize: '1rem',
                '&.Mui-disabled': {
                  opacity: 0.25,
                },
                '&:hover': {
                  boxShadow: 'none',
                },
                '& .MuiButton-startIcon': {
                  margin: '0 -5px 0 0',
                },
                [theme.breakpoints.down('sm')]: {
                  color: theme.palette.common.white,
                },
              },
            },
          ],
        },
      },
    },
    MuiMultiSectionDigitalClockSection: {
      styleOverrides: {
        item: {
          '&.Mui-disabled': {
            display: 'none',
          },
        },
      },
    },
  },
});

export default theme;
