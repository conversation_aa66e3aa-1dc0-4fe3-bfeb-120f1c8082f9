import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  IconButton,
  Typography,
  List,
  ListItem,
  Collapse,
  Divider,
  Button
} from '@mui/material';
import { IAssistantMessagingSavedPrompt } from '@/app/features/assistant/api';

import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

interface PromptListItemProps {
  item: IAssistantMessagingSavedPrompt;
  onDeleteItem: (id: string) => void;
  onSelectItem: (prompt: string) => void;
}
interface PromptListProps {
  items: IAssistantMessagingSavedPrompt[];
  title: string;
  subtitle?: string;
  onDeleteItem?: (id: string) => void;
  onSelectItem: (prompt: string) => void;
  maxVisibleItems: number;
  maxListHeight?: number;
}

function PromptListItem({ item, onDeleteItem, onSelectItem }: PromptListItemProps) {
  const { t } = useTranslation([
    'ai-messaging'
  ]);

  return (
    <ListItem
      data-testid={`assistant-messaging-list-item-${item.id}`}
      aria-label={`list item ${item.id}`}
      key={item.id}
      sx={{
        px: 1.25,
        py: 1,
        display: 'flex',
        justifyContent: 'space-between'
      }}
      disablePadding
    >
      <Button
        data-testid='assistant-messaging-select-list-item-btn'
        aria-label='select list item button'
        variant='text'
        disableRipple
        onClick={() => onSelectItem(item.attributes.prompt)}
        sx={{
          display: 'flex',
          flexGrow: 1,
          justifyContent: 'start',
          textAlign: 'left',
          minWidth: 0
        }}
      >
        <Box
          sx={{
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis'
          }}
        >
          {t(item.attributes.title)}
        </Box>
      </Button>
      {onDeleteItem &&
        <IconButton
          data-testid='assistant-messaging-delete-list-item-btn'
          aria-label='delete list item button'
          onClick={() => onDeleteItem(item.id)}
          size='small'
        >
          <DeleteOutlineOutlinedIcon
            sx={theme => ({
              color: theme.palette.grey[300]
            })}
            fontSize='small'
          />
        </IconButton>
      }
    </ListItem>
  )
}

export function PromptsList({
  items,
  title,
  subtitle=null,
  onDeleteItem=null,
  onSelectItem,
  maxVisibleItems,
  maxListHeight
}: PromptListProps) {
  const { t } = useTranslation([
    'ai-messaging'
  ]);
  const [open, setOpen] = useState(false);

  const handleToggleAdditionList = () => {
    setOpen(!open);
  };

  const primaryItems = items.slice(0, maxVisibleItems);
  const additionItems = items.slice(maxVisibleItems);

  return (
    <>
      <Box
        sx={{
          px: 1.25,
          mb: 1.25
        }}
      >
        <Typography
          data-testid='assistant-messaging-prompts-list-title'
          aria-label={`${title} title`}
          variant='h3'
        >
          {t(title)}
        </Typography>
        {subtitle &&
          <Typography
            data-testid='assistant-messaging-prompts-list-subtitle'
            aria-label={`${subtitle} subtitle`}
            variant='subtitle1'
          >
            {t(subtitle)}
          </Typography>
        }
      </Box>

      <Box
        sx={theme => ({
          borderRadius: theme.shape.borderRadius,
          backgroundColor: theme.palette.common.white,
          boxShadow: 1
        })}
      >
        <List
          sx={{
            overflow: 'auto',
            maxHeight: maxListHeight ? `${maxListHeight}px` : 'none'
          }} 
          disablePadding
        >
          {primaryItems.map((item: IAssistantMessagingSavedPrompt, index: number) => {
            const lastItem = index === primaryItems.length - 1;
            return (
              <>
                <PromptListItem
                  item={item}
                  onDeleteItem={onDeleteItem}
                  onSelectItem={onSelectItem}
                />
                {(!lastItem || additionItems.length > 0) &&
                  <Divider
                    variant="fullWidth"
                    component="li"
                    sx={{ mx: 1.25 }}
                  />
                }
              </>
            )
          })}
          <Collapse in={open} timeout="auto" unmountOnExit>
            <List disablePadding>
              {additionItems.map((item: IAssistantMessagingSavedPrompt) => {
                return (
                  <>
                    <PromptListItem
                      item={item}
                      onDeleteItem={onDeleteItem}
                      onSelectItem={onSelectItem}
                    />
                    <Divider
                      variant="fullWidth"
                      component="li"
                      sx={{ mx: 1.25 }}
                    />
                  </>
                )
              })}
            </List>
          </Collapse>
          {additionItems.length > 0 &&
            <Button
              data-testid='assistant-messaging-toggle-addition-list-items-btn'
              aria-label='toggle addition list items button'
              variant='text'
              disableRipple
              onClick={handleToggleAdditionList}
              sx={{
                px: 1.25,
                py: 1,
                display: 'flex',
                flexGrow: 1,
                justifyContent: 'start'
              }}
              fullWidth
            >
              {open ? t('Show Less') : t('Show More...')}
            </Button>
          }
        </List>
      </Box>
    </>
  )
}