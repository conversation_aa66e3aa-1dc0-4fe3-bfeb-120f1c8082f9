import { RouteObject } from 'react-router';

import ProtectedRoute from '@shared/components/ProtectedRoute';
import RootBoundary from '@shared/components/RootBoundary';

import Assistant from './pages/Assistant';
import AssistantMessaging from './pages/AssistantMessaging';

const AssistantRoutes: RouteObject = {
  path: 'assistant',
  element: (
    <ProtectedRoute>
      <Assistant />
    </ProtectedRoute>
  ),
  errorElement: <RootBoundary />,
  children: [
    {
      index: true,
      element: <AssistantMessaging />,
    },
  ],
};

export default AssistantRoutes;
