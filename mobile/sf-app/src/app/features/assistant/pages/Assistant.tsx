import { Backdrop, Box, Fade } from '@mui/material';
import { Outlet } from 'react-router';

function Assistant() {
  return (
    <Fade in={true} timeout={500}>
      <Backdrop aria-hidden={false} open={true}>
        <Box
          sx={{
            display: 'flex',
            flexGrow: 1,
            height: '100%',
            justifyContent: 'end',
          }}
        >
          <Outlet />
        </Box>
      </Backdrop>
    </Fade>
  );
}

export default Assistant;
