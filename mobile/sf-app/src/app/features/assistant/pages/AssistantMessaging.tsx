import {
  useState,
  useEffect,
  ChangeEvent,
  KeyboardEvent,
  useCallback,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router';
import { micromark } from 'micromark';

import {
  Box,
  IconButton,
  Slide,
  Typography,
  CircularProgress,
  Button,
  Stack,
  Theme,
  SxProps,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DriveFileRenameOutlineOutlinedIcon from '@mui/icons-material/DriveFileRenameOutlineOutlined';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined';
import SaveOutlinedIcon from '@mui/icons-material/SaveOutlined';
import SendRoundedIcon from '@mui/icons-material/SendRounded';

import { sxGlobal, sxSpread } from '@/theme/sxGlobal';
import { postMessage } from '@/utils/generic';
import { logAnalyticsEvent } from '@/api/genericApi';
import { useAppDispatch, useAppSelector } from '@/hooks/store';
import { getConfig, IAiOutreachRecommendedPrompts } from '@store/configSlice';
import I18N from '@services/i18n';
import SFTextField from '@shared/components/SFTexField';
import useDialog from '@/hooks/dialog';

import {
  fetchMessagePrompt,
  improveMessagePrompt,
  fetchAIPrompts,
  saveAIPrompt,
  deleteAIPrompt,
  AssistantMessagingType,
  IAssistantMessagingSavedPrompt,
} from '../api';
import { getPrompts } from '../store/aiSlice';
import { PromptsList } from '../components/PromptsList';

enum viewTypes {
  MAIN = 'main',
  PREVIEW = 'preview',
}

const MAX_SAVED_PROMPTS = 5;

const sxLocal: Record<string, SxProps<Theme> | undefined> = {
  sectionWrapper: {
    p: 1.25,
    display: 'flex',
    boxShadow: 1,
    alignItems: 'flex-start',
    borderRadius: theme => theme.shape.borderRadius,
    flexDirection: 'column',
    backgroundColor: theme => theme.palette.common.white,
  },
  actionButton: {
    px: 1.25,
    py: 0.75,
    border: theme => `1px solid ${theme.palette.primary.main}`,
    fontSize: '0.875rem',
    fontWeight: theme => theme.typography.fontWeightMedium,
    borderRadius: theme => theme.shape.borderRadius,
  },
};

function AssistantMessaging() {
  const { t } = useTranslation(['ai-messaging']);

  const [visible, setVisible] = useState(true);
  const [prompt, setPrompt] = useState('');
  const [view, setView] = useState<viewTypes>(viewTypes.MAIN);
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [recommendedPrompts, setRecommendedPrompts] = useState<
    IAssistantMessagingSavedPrompt[]
  >([]);
  const location = useLocation();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { showNotification } = useDialog();

  const savedPrompts = useAppSelector(getPrompts);
  const config = useAppSelector(getConfig);

  const logEvent = (event: string) => {
    logAnalyticsEvent({
      event,
      event_data: { feature: 'ai-outreach' },
    });
  };

  const startLoading = () => {
    setView(viewTypes.PREVIEW);
    setIsLoading(true);
    setContent('');
  };

  const handleBackBtnClick = () => {
    setContent('');
    setView(viewTypes.MAIN);
    setPrompt('');
  };

  const handleCloseAssistant = () => {
    setVisible(false);
    setTimeout(() => navigate('/'), 500);
  };

  const showErrorNotification = useCallback(
    ({ message = 'Something went wrong, please try again.', error }) => {
      showNotification({
        content: (
          <Typography
            sx={{ fontSize: '0.875rem' }}
            component='span'
            dangerouslySetInnerHTML={{
              __html: t(message),
            }}
          />
        ),
      });
      console.log(
        '[AssistantMessaging.tsx] Something went wrong when submit AI prompt',
        error,
      );
    },
    [t, showNotification],
  );

  const executePromptRequest = (
    promptText: string,
    actionButton: 'improve' | 'submitPrompt' = 'submitPrompt',
  ) => {
    if (!promptText.trim()) return;

    const action =
      actionButton === 'submitPrompt'
        ? fetchMessagePrompt
        : improveMessagePrompt;

    startLoading();
    dispatch(
      action({
        prompt: promptText,
        content_type: AssistantMessagingType.EMAIL,
      }),
    )
      .unwrap()
      .then(resp => {
        setContent(micromark(resp.attributes.content));
      })
      .catch(error => {
        showErrorNotification({
          message: `Something went wrong and I couldn't generate your message. Please double-check your input or give it another try`,
          error,
        });
        setView(viewTypes.MAIN);
      })
      .finally(() => setIsLoading(false));
  };

  const handleSubmitPrompt = (suppressLog: boolean = false) => {
    if (!suppressLog) {
      logEvent('ai-outreach_submit-prompt');
    }
    executePromptRequest(prompt);
  };

  const handleImproveBtnClick = (suppressLog: boolean = false) => {
    if (!suppressLog) {
      logEvent('ai-outreach_improve-message-button');
    }
    setPrompt('');
    executePromptRequest(location.state?.message, 'improve');
  };

  const handleRewriteBtnClick = () => {
    logEvent('ai-outreach_rewrite');
    if (prompt) {
      handleSubmitPrompt(true);
    } else if (location.state?.message) {
      handleImproveBtnClick(true);
    } else {
      console.warn(
        '[AssistantMessaging.tsx] No prompt or message to improve available',
      );
    }
  };

  const handleInsertBtnClick = () => {
    logEvent('ai-outreach_insert-message');
    postMessage({
      source: 'sf-app',
      payload: {
        action: 'execute',
        params: {
          name: 'insertAiMessage',
          options: content,
        },
      },
    });
    handleCloseAssistant();
  };

  const handleSavePromptBtn = () => {
    logEvent('ai-outreach_save-prompt');
    dispatch(
      saveAIPrompt({
        title: prompt,
        prompt,
      }),
    )
      .then(() => {
        showNotification({
          title: (
            <Typography
              sx={theme => ({
                fontSize: '1rem',
                fontWeight: theme.typography.fontWeightMedium,
              })}
            >
              {t('Prompt Saved')}
            </Typography>
          ),
          content: (
            <Typography
              sx={{ fontSize: '0.875rem' }}
              component='span'
              dangerouslySetInnerHTML={{
                __html: t(
                  `Your prompt was successfully saved. You can save up to 5 prompts`,
                ),
              }}
            />
          ),
        });
      })
      .catch(error =>
        // TODO: to confirm feedback message for now will be displayed the generic
        showErrorNotification({
          error,
        }),
      );
  };

  const handleDeletePromptBtn = (id: string) => {
    dispatch(deleteAIPrompt(id))
      // TODO: to confirm feedback message for now will be displayed the generic
      .catch(error =>
        showErrorNotification({
          error,
        }),
      );
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setPrompt(e.target.value);
  };

  const handleInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitPrompt();
    }
  };

  const isPromptDuplicated = (
    prompt: string,
    promptLists: IAssistantMessagingSavedPrompt[],
  ): boolean => {
    return promptLists.some(p => p.attributes.prompt === prompt);
  };

  const maxListHeight = (numOfItems: number, itemHeight: number): number =>
    numOfItems * itemHeight;

  useEffect(() => {
    logEvent('ai-outreach_assistant-button');
    dispatch(fetchAIPrompts()).catch(error =>
      // TODO: to confirm feedback message for now will be displayed the generic
      showErrorNotification({
        error,
      }),
    );

    if (config?.AiOutreachRecommendedPrompts) {
      try {
        const parsed = JSON.parse(config.AiOutreachRecommendedPrompts);

        if (parsed && typeof parsed === 'object') {
          const rawPrompts = parsed as IAiOutreachRecommendedPrompts;
          const localePrompts = rawPrompts[I18N.beLocale] ?? [];

          setRecommendedPrompts(
            localePrompts
              .filter(prompt => prompt.active)
              .map(({ id, title, prompt }) => ({
                type: '',
                id: id,
                attributes: {
                  title: title,
                  prompt: prompt,
                },
              })),
          );
        } else {
          console.warn(
            '[AssistantMessaging.tsx] Parsed AiOutreachRecommendedPrompts is not a valid object',
          );
        }
      } catch (error) {
        // TODO: to confirm feedback message for now will be displayed the generic
        showErrorNotification({
          error,
        });
      }
    }
  }, [showErrorNotification, dispatch, config]);

  return (
    <Slide
      direction={'up'}
      timeout={500}
      in={visible}
      mountOnEnter
      unmountOnExit
    >
      <Box
        data-testid='assistant-messaging'
        sx={[
          theme => ({
            display: 'flex',
            flexGrow: 1,
            flexDirection: 'column',
            justifyContent: 'end',
            width: '100vw',
            [theme.breakpoints.up('sm')]: {
              maxWidth: 400,
            },
          }),
          ...sxSpread(sxGlobal.safeInsetBottom),
        ]}
      >
        <Box
          sx={theme => ({
            background: theme.palette.grey[200],
            p: 1.25,
            display: 'flex',
            borderTopLeftRadius: theme.shape.borderRadius * 5,
            borderTopRightRadius: theme.shape.borderRadius * 5,
          })}
        >
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              flexGrow: 1,
              flexDirection: 'column',
            }}
          >
            {/* HEADER */}
            <Box
              sx={{
                px: 1.25,
                mb: 1.25,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              {/* Header title */}
              {view === viewTypes.MAIN && (
                <Typography
                  data-testid='assistant-messaging-main-header'
                  aria-label='message assistant header'
                  variant='h1'
                >
                  {t('Message Assistant')}
                </Typography>
              )}

              {/* Header back button */}
              {view === viewTypes.PREVIEW && (
                <Button
                  data-testid='assistant-messaging-back-btn'
                  aria-label='back button'
                  sx={theme => ({
                    color: theme.palette.text.primary,
                    fontSize: '1rem',
                    fontWeight: theme.typography.fontWeightMedium,
                  })}
                  component='label'
                  startIcon={<ArrowBackIosIcon fontSize='small' />}
                  onClick={handleBackBtnClick}
                >
                  {t('Back')}
                </Button>
              )}

              {/* Header close button */}
              <IconButton
                data-testid='assistant-messaging-close-btn'
                aria-label='close assistant button'
                sx={theme => ({
                  color: theme.palette.text.primary,
                })}
                onClick={handleCloseAssistant}
                size='small'
              >
                <CloseIcon fontSize='small' />
              </IconButton>
            </Box>

            {/* MAIN VIEW */}
            {view === viewTypes.MAIN && (
              <>
                <Box sx={{ mb: 1.25 }}>
                  {/* Improve my message button */}
                  <Box
                    sx={theme => ({
                      p: 1.25,
                      display: 'flex',
                      flexGrow: 1,
                      boxShadow: 1,
                      backgroundColor: theme.palette.common.white,
                      borderRadius: theme.shape.borderRadius,
                    })}
                  >
                    <Button
                      data-testid='assistant-messaging-improve-my-message-btn'
                      aria-label='improve my message button'
                      disabled={!location.state?.message}
                      onClick={() => handleImproveBtnClick()}
                      variant='text'
                      disableRipple
                    >
                      <DriveFileRenameOutlineOutlinedIcon
                        fontSize='small'
                        sx={{ mr: 0.5 }}
                      />
                      {t('Improve my message')}
                    </Button>
                  </Box>

                  {/* Saved Prompts List*/}
                  {savedPrompts.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <PromptsList
                        items={savedPrompts}
                        title='Saved Prompts'
                        subtitle='You can save up to 5 prompts'
                        onDeleteItem={handleDeletePromptBtn}
                        onSelectItem={(prompt: string) => {
                          logEvent('ai-outreach_insert-prompt');
                          setPrompt(prompt);
                        }}
                        maxVisibleItems={5}
                      />
                    </Box>
                  )}

                  {/* Recommended Prompts List*/}
                  {recommendedPrompts.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <PromptsList
                        items={recommendedPrompts}
                        title='Recommended Prompts'
                        onSelectItem={(prompt: string) => {
                          logEvent('ai-outreach_predefined-prompt');
                          setPrompt(prompt);
                        }}
                        maxVisibleItems={3}
                        maxListHeight={maxListHeight(5, 40)}
                      />
                    </Box>
                  )}
                </Box>

                {/* Input prompt */}
                <Box
                  sx={theme => ({
                    display: 'flex',
                    border: `1px solid ${theme.palette.border.main}`,
                    borderRadius: theme.shape.borderRadius * 0.5,
                    flexDirection: 'column',
                    backgroundColor: `${theme.palette.common.white}`,
                  })}
                >
                  <SFTextField
                    data-testid='assistant-prompt-input'
                    aria-label='prompt input'
                    slotProps={{
                      htmlInput: {
                        border: 'none',
                        boxShadow: 'none',
                        fontSize: '0.875rem',
                      },
                    }}
                    multiline
                    maxRows={4}
                    fullWidth
                    value={prompt}
                    onChange={handleInputChange}
                    onKeyDown={handleInputKeyDown}
                    placeholder={t('Write your message here...')}
                  />
                  <IconButton
                    data-testid='assistant-prompt-submit-btn'
                    aria-label='prompt submit'
                    sx={{
                      alignSelf: 'flex-end',
                    }}
                    onClick={() => handleSubmitPrompt()}
                    disabled={isLoading}
                  >
                    <SendRoundedIcon />
                  </IconButton>
                </Box>
              </>
            )}

            {/* PREVIEW VIEW */}
            {view === viewTypes.PREVIEW && (
              <Box sx={sxLocal.sectionWrapper}>
                {prompt && (
                  <Box
                    sx={theme => ({
                      px: 0.75,
                      pb: 1.5,
                      width: '100%',
                      borderBottom: `1px solid ${theme.palette.grey[100]}`,
                    })}
                  >
                    {/* Submit prompt title */}
                    <Typography
                      data-testid='assistant-messaging-prompt-title'
                      aria-label='submitted prompt title'
                      variant='h3'
                      noWrap
                    >
                      {prompt}
                    </Typography>
                  </Box>
                )}

                {isLoading && (
                  <Box
                    sx={{
                      py: 2.5,
                      margin: 'auto',
                    }}
                  >
                    {/* Loading spinner */}
                    <CircularProgress
                      data-testid='assistant-messaging-loading-spinner'
                      aria-label='loading spinner'
                      size={56}
                      sx={theme => ({
                        color: theme.palette.primary.dark,
                      })}
                    />
                  </Box>
                )}

                {/* Response content */}
                {content && (
                  <>
                    <Box
                      sx={{
                        p: 0.75,
                        mb: 1.25,
                      }}
                    >
                      <Typography
                        data-testid='assistant-messaging-response-content'
                        aria-label='assistant messaging response content'
                        variant='html'
                        sx={{
                          overflow: 'auto',
                          maxHeight: 476,
                        }}
                        dangerouslySetInnerHTML={{
                          __html: content,
                        }}
                      ></Typography>
                    </Box>

                    {/* Action buttons */}
                    <Stack spacing={1} direction='row'>
                      <Button
                        data-testid='assistant-messaging-insert-btn'
                        aria-label='insert proposed message button'
                        sx={sxLocal.actionButton}
                        variant='outlined'
                        endIcon={<DriveFileRenameOutlineOutlinedIcon />}
                        onClick={handleInsertBtnClick}
                      >
                        {t('Insert')}
                      </Button>
                      <Button
                        data-testid='assistant-messaging-rewrite-btn'
                        aria-label='rewrite proposed message button'
                        sx={sxLocal.actionButton}
                        variant='outlined'
                        endIcon={<LoopOutlinedIcon />}
                        onClick={handleRewriteBtnClick}
                      >
                        {t('Rewrite')}
                      </Button>
                      {prompt &&
                        !isPromptDuplicated(prompt, [
                          ...savedPrompts,
                          ...recommendedPrompts,
                        ]) && (
                          <Button
                            data-testid='assistant-messaging-save-message-btn'
                            aria-label='save prompt button'
                            sx={sxLocal.actionButton}
                            variant='outlined'
                            endIcon={<SaveOutlinedIcon />}
                            onClick={handleSavePromptBtn}
                            disabled={savedPrompts.length >= MAX_SAVED_PROMPTS}
                          >
                            {t('Save Prompt')}
                          </Button>
                        )}
                    </Stack>
                  </>
                )}
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Slide>
  );
}

export default AssistantMessaging;
