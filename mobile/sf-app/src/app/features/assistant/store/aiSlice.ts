import { createSlice } from '@reduxjs/toolkit';
import {
  IAssistantMessagingSavedPrompt,
  fetchAIPrompts,
  saveAIPrompt,
  deleteAIPrompt,
} from '@/app/features/assistant/api';

interface IAiState {
  isLoading: boolean;
  error: string | null;
  prompts: IAssistantMessagingSavedPrompt[];
  history?: string[]; // Optional, if you want to keep track of the history of prompts
}

const initialState: IAiState = {
  isLoading: false,
  error: null,
  prompts: [],
};

const aiSlice = createSlice({
  name: 'ai',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchAIPrompts.fulfilled, (state, action) => ({
        ...state,
        ...{ prompts: action.payload },
      }))
      .addCase(saveAIPrompt.fulfilled, (state, action) => {
        const newPrompts = [action.payload, ...state.prompts];
        return {
          ...state,
          prompts: newPrompts,
        };
      })
      .addCase(deleteAIPrompt.fulfilled, (state, action) => {
        const newPrompts = state.prompts.filter(
          prompt => prompt.id !== action.payload,
        );
        return {
          ...state,
          prompts: newPrompts,
        };
      });
  },
});

export const getPrompts = (state: { ai: IAiState }) => state.ai.prompts;

export default aiSlice;
