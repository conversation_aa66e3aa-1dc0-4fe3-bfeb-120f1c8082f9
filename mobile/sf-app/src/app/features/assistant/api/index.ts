import { createAsyncThunk } from '@reduxjs/toolkit';
import http from '@services/http';

export enum AssistantMessagingType {
  EMAIL = 'email',
  SMS = 'sms',
  TEXT = 'text',
}

export interface IAssistantMessaging {
  type: string;
  id: string;
  attributes: {
    prompt: string;
    content: string;
    content_type: AssistantMessagingType;
    campaign_type: string;
    recommended_subject: string;
  };
}

export interface IAssistantMessagingSavedPrompt {
  type: string;
  id: string;
  attributes: {
    title: string;
    prompt: string;
  }
}

export interface IAssistantThunkPayload {
  prompt: string;
  content_type: AssistantMessagingType;
}

export const fetchMessagePrompt = createAsyncThunk<
  IAssistantMessaging,
  IAssistantThunkPayload
>(
  'assistant/fetchMessagePrompt',
  async ({ prompt, content_type }, { rejectWithValue }) => {
    const body = {
      data: {
        type: 'aimessage',
        attributes: {
          prompt,
          content_type,
        },
      },
    };
    const response = await http.post('/ai/message', body);
    if (response.status === 'ok') {
      return response.data as IAssistantMessaging;
    }
    return rejectWithValue(response);
  },
);

export const improveMessagePrompt = createAsyncThunk<
  IAssistantMessaging,
  IAssistantThunkPayload
>(
  'assistant/improveMessagePrompt',
  async ({ prompt, content_type }, { rejectWithValue }) => {
    const body = {
      data: {
        type: 'aimessage',
        attributes: {
          prompt,
          content_type,
        },
      },
    };
    const response = await http.post('/ai/message/refinement', body);
    if (response.status === 'ok') {
      return response.data as IAssistantMessaging;
    }
    return rejectWithValue(response);
  },
);

export const fetchAIPrompts = createAsyncThunk<
  IAssistantMessagingSavedPrompt[],
  void
>('assistant/fetchAIPrompts', async (_, { rejectWithValue }) => {
  const response = await http.get('/ai/prompt');
  if (response.status === 'ok') {
    return response.data as IAssistantMessagingSavedPrompt[];
  }
  return rejectWithValue(response);
});

export const saveAIPrompt = createAsyncThunk<
  IAssistantMessagingSavedPrompt,
  { title: string; prompt: string }
>('assistant/saveAIPrompt', async ({ title, prompt }, { rejectWithValue }) => {
  const body = {
    data: {
      type: 'aiprompt',
      attributes: {
        title,
        prompt,
      },
    },
  };
  const response = await http.post('/ai/prompt', body);
  if (response.status === 'ok') {
    return response.data as IAssistantMessagingSavedPrompt;
  }
  return rejectWithValue(response);
});

export const deleteAIPrompt = createAsyncThunk<string, string>(
  'assistant/deleteAIPrompt',
  async (id, { rejectWithValue }) => {
    const response = await http.delete(`/ai/prompt/${id}`);
    if (response.status === 'ok') {
      return id;
    }
    return rejectWithValue(response);
  },
);
