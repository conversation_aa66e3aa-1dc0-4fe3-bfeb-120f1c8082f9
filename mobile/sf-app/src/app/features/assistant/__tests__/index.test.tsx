// TODO: update jest config to fix the error: SyntaxError: Cannot use import statement outside a module
//

// import '@testing-library/jest-dom';
// import { render } from '@testing-library/react';
// import { Provider } from 'react-redux';
// import { RouterProvider, createMemoryRouter } from 'react-router';
// import { store } from '@/store';
// import AuthContext from '@/providers/AuthContext';
// import AuthProvider from '@/providers/AuthProvider';
// import ConfirmationDialogProvider from '@/providers/DialogProvider';
// import LoadingProvider from '@/providers/LoadingProvider';
// import { IUser } from '@/store/userSlice';
// import DeviceProvider from '@/providers/DeviceProvider';
// import AssistantRoutes from '..';
// import { ThemeProvider } from '@mui/material/styles';
// import theme from '@/theme';

test.skip('Dummy test', () => {});

// test('load default assistant route when not authenticated', async () => {
//   const router = createMemoryRouter([AssistantRoutes], {
//     initialEntries: ['/assistant'],
//   });

//   const { queryByTestId } = render(
//     <ThemeProvider theme={theme}>
//       <AuthProvider>
//         <Provider store={store}>
//           <RouterProvider router={router} />
//         </Provider>
//       </AuthProvider>
//     </ThemeProvider>,
//   );

//   expect(queryByTestId('assistant-messaging')).not.toBeInTheDocument();
// });

// test('load default assistant route when authenticated', async () => {
//   const router = createMemoryRouter([AssistantRoutes], {
//     initialEntries: ['/assistant'],
//   });

//   const user: Partial<IUser> = {
//     ID: 115,
//     username: 'reggie',
//   };
//   const login = jest.fn();
//   const logOut = jest.fn();
//   const featureEnabled = jest.fn();
//   const hasGroupPermission = jest.fn();

//   const { getByTestId } = render(
//     <ThemeProvider theme={theme}>
//       <DeviceProvider>
//         <AuthContext.Provider
//           value={{ user, login, logOut, featureEnabled, hasGroupPermission }}
//         >
//           <ConfirmationDialogProvider>
//             <Provider store={store}>
//               <LoadingProvider>
//                 <RouterProvider router={router} />
//               </LoadingProvider>
//             </Provider>
//           </ConfirmationDialogProvider>
//         </AuthContext.Provider>
//       </DeviceProvider>
//     </ThemeProvider>
//   );

//   expect(getByTestId('assistant-messaging')).toBeInTheDocument();
// });
