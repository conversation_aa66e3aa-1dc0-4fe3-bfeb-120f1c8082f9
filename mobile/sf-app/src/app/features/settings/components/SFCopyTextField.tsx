import {
  useTheme,
  Box,
  FormControl,
  IconButton,
  Typography,
} from '@mui/material';
import SFInputLabel from '@/app/shared/components/SFInputLabel';
import FlashMessage from '@/services/flashMessage';
import { useTranslation } from 'react-i18next';

function SFCopyTextField({ label, text }: { label: string; text: string }) {
  const theme = useTheme();
  const { t } = useTranslation();
  const onClickHandler = () => {
    //TODO: uninstall cordova clipboard plugin
    //cordova.plugins['clipboard'].copy(text);
    navigator.clipboard.writeText(text);
    FlashMessage.Success(t('Your selection has been copied to the clipboard'));
  };
  return (
    <FormControl
      sx={{
        marginBottom: '5px',
        borderTop: `1px solid ${theme.palette.grey[50]}`,
      }}
      fullWidth
    >
      <SFInputLabel
        sx={{
          top: '8px',
        }}
      >
        {label}
      </SFInputLabel>
      <Box
        display={'flex'}
        justifyContent={'space-between'}
        alignItems={'center'}
        mt={'22px'}
      >
        <Typography variant='body1' sx={{userSelect: 'text'}}>{text}</Typography>
        <IconButton onClick={onClickHandler}>
          <img src='/images/misc/copy.svg'></img>
        </IconButton>
      </Box>
    </FormControl>
  );
}

export default SFCopyTextField;
