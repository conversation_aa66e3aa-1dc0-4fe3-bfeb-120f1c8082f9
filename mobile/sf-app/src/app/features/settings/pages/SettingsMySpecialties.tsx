import { ChangeEvent, ReactNode, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  debounce,
  IconButton,
  InputAdornment,
} from '@mui/material';
import { Clear } from '@mui/icons-material';

import { useAppDispatch, useAppSelector } from '@hooks/store';
import { useDevice } from '@/hooks/device';
import { setRep, getRep } from '@store/repSlice';
import { updateRep } from '@/api/repApi';
import { ISpecialty } from '@store/retailerSlice';
import { fetchSpecialties } from '@api/retailerApi';
import FlashMessage from '@/services/flashMessage';

import SFPageLayout from '@shared/components/SFPageLayout';
import SFPageHeader from '@shared/components/SFPageHeader';
import SFHeaderBackNavigationButton from '@shared/components/SFHeaderBackNavigationButton';
import SFTextField from '@/app/shared/components/SFTexField';

import SFSelectableListItem from '../components/SFSelectableListItem';
import useDialog from '@/hooks/dialog';
import { sxGlobal } from '@/theme/sxGlobal';

interface IUserSpecialty extends ISpecialty {
  selected: boolean;
  visible: boolean;
}

function SettingsMySpecialties() {
  const { t } = useTranslation(['settings']);
  const dispatch = useAppDispatch();
  const { showConfirmation } = useDialog();
  const device = useDevice();
  const rep = useAppSelector(getRep);
  const filterInputRef = useRef<HTMLDivElement>();
  const [specialties, setSpecialties] = useState<IUserSpecialty[]>([]);
  const [filter, setFilter] = useState('');
  const [allSelected, setAllSelected] = useState(false);

  const updateAllSelected = (specialties: IUserSpecialty[]) => {
    const selectedSpecialties = specialties.filter(s => s.selected);
    setAllSelected(selectedSpecialties.length === specialties.length);
  };

  const handleSpecialtyChange = ({ id, selected }) => {
    setSpecialties(prevState => {
      const newState = prevState.map(s =>
        s.category_id === id ? { ...s, selected: selected } : s,
      );
      updateAllSelected(newState);
      return newState;
    });
  };

  const handleSelectAllClick = () => {
    setSpecialties(prevState =>
      prevState.map(s => ({ ...s, selected: allSelected ? false : true })),
    );
    setAllSelected(prevState => !prevState);
  };

  const clearFiltering = () => {
    setFilter('');
    setSpecialties(prevState => prevState.map(s => ({ ...s, visible: true })));
  };

  const handleFilterSpecialtiesChange = debounce(
    (e: ChangeEvent<HTMLInputElement>) => {
      setSpecialties(newValue => {
        const filterValue = e.target.value;
        setFilter(filterValue);
        return newValue.map(s => {
          let visible = false;
          if (
            filterValue === '' ||
            s.name.toLocaleLowerCase().includes(filterValue.toLowerCase())
          ) {
            visible = true;
          }
          return {
            ...s,
            visible,
          };
        });
      });
    },
    500,
  );

  const onConfirmUpdateSpecialties = () => {
    const payload = {
      specialties: specialties.filter(s => s.selected).map(s => s.category_id),
    };

    const promise = dispatch(updateRep(payload));
    promise
      .unwrap()
      .then(() => {
        FlashMessage.Success(t('Your specialties have been changed'));
        // update store with selected specialties
        dispatch(setRep({ specialties: specialties.filter(s => s.selected) }));

        //TODO: remove when migrated and replace with migrated methods.
        window.parent.postMessage({
          source: 'sf-app',
          payload: {
            action: 'execute',
            params: {
              name: 'updateSpecialtiesInCANS',
              options: payload.specialties,
            },
          },
        });
      })
      .catch(() => {
        FlashMessage.Error(t('Changes could not be saved'));
      });
  };

  const onSaveClick = () => {
    const selectedSpecialties = specialties.filter(s => s.selected);
    if (!selectedSpecialties.length) {
      FlashMessage.Error(t('Please select at least one category', { ns: 'rep-onboarding' }));
      return;
    }

    showConfirmation({
      content: t(
        'Modifying your specialties may change the products displayed.',
      ),
      onConfirm: onConfirmUpdateSpecialties,
    });
  };

  useEffect(() => {
    const promise = dispatch(fetchSpecialties());
    promise.unwrap().then(specialties => {
      const userSpecialties = specialties.map(specialty => {
        const isSelected = rep.specialties.find(
          s => s.category_id === specialty.category_id,
        );
        return {
          ...specialty,
          selected: isSelected !== undefined,
          visible:
            filter === '' ||
            specialty.name.toLocaleLowerCase().includes(filter.toLowerCase()),
        };
      });
      setSpecialties(userSpecialties);
      updateAllSelected(userSpecialties);
    });
    return () => {
      promise.abort();
    };
  }, [dispatch, filter, rep]);

  return (
    <SFPageLayout
      data-testid='settings-my-specialties'
      header={
        <>
          <SFPageHeader
            title={t('My Specialties')}
            startAdorment={<SFHeaderBackNavigationButton route='/settings' />}
            endAdornment={
              <Button
                onClick={handleSelectAllClick}
                sx={{
                  display: () => ({
                    xs: 'block',
                    sm: device.isMobile() ? 'block' : 'none',
                  }),
                }}
              >
                {t(allSelected ? 'Select None' : 'Select All')}
              </Button>
            }
          ></SFPageHeader>
          <Box sx={{ pt: 2, pb: 2, pl: 3, pr: 3 }}>
            <SFTextField
              ref={input => (filterInputRef.current = input)}
              name='filterSpecialties'
              defaultValue=''
              onChange={handleFilterSpecialtiesChange}
              // placeholder='Type something to filter specilaties' //TODO: ask for placeholder string
              slotProps={{
                input: {
                  endAdornment: ((): ReactNode => {
                    const inputEl =
                      filterInputRef?.current?.querySelector('input');
                    return (
                      <InputAdornment position='end'>
                        <IconButton
                          color='primary'
                          size='small'
                          aria-label='clear field'
                          disabled={inputEl?.value === ''}
                          onClick={() => {
                            inputEl.value = '';
                            clearFiltering();
                          }}
                        >
                          <Clear fontSize='small' />
                        </IconButton>
                      </InputAdornment>
                    );
                  })(),
                },
              }}
              fullWidth
            />
          </Box>
        </>
      }
      footer={
        <>
          <Button
            data-testid='specialties-select-all-none-btn'
            variant={'contained'}
            onClick={handleSelectAllClick}
            sx={{
              mr: 1,
              display: () => ({
                xs: 'none',
                sm: device.isMobile() ? 'none' : 'inline-block',
              }),
            }}
          >
            {t(allSelected ? 'Select None' : 'Select All')}
          </Button>
          <Button
            data-testid='specialties-save-btn'
            variant={'contained'}
            onClick={onSaveClick}
            sx={sxGlobal.safeInsetBottom}
          >
            {t('Save Changes')}
          </Button>
        </>
      }
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          pl: 3,
          pr: 3,
        }}
      >
        {specialties.map(specialty => (
          <SFSelectableListItem
            id={specialty.category_id}
            label={specialty.name}
            selected={specialty.selected}
            visible={specialty.visible}
            onChange={handleSpecialtyChange}
          />
        ))}
      </Box>
    </SFPageLayout>
  );
}

export default SettingsMySpecialties;
