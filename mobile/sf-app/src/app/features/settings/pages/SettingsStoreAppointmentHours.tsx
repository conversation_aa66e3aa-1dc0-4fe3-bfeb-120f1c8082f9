import {
  SyntheticEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  IconButton,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import dayjs, { Dayjs } from 'dayjs';

import { sxGlobal } from '@/theme/sxGlobal';
import { useAppDispatch, useAppSelector } from '@hooks/store';
import { getUser } from '@store/userSlice';
import FlashMessage from '@services/flashMessage';

import { BoolAsNumstring } from '@shared/types/Generic';
import SFPageLayout from '@shared/components/SFPageLayout';
import SFPageHeader from '@shared/components/SFPageHeader';
import SFHeaderBackNavigationButton from '@shared/components/SFHeaderBackNavigationButton';
import SFLabeledSwitch from '@shared/components/SFLabeledSwitch';
import SFTimePicker from '@shared/components/SFTimePicker';

import {
  IStoreAppointmentTimeSlot,
  ICustomAvailabilityStatus,
  fetchCustomAvailabilityStatus,
  saveCustomAvailabilityStatus,
  IStoreAppointmentHours,
  fetchStoreAppointmentsHours,
  IStoreAppointmentHoursOverrides,
  fetchStoreAppointmentsHoursOverrides,
  deleteStoreAppointmentsHoursOverrides,
  updateStoreAppointmentsHours,
} from '../api/storeAppintmentHours';
import useLoading from '@hooks/loading';
import useDialog from '@/hooks/dialog';
import useDataShare from '@hooks/dataShare';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <Box id={`tabpanel-${index}`} hidden={value !== index} {...other}>
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function SettingsStoreAppointmentHours() {
  const { t } = useTranslation(['settings']);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { state } = useLocation();
  const { setSharedData } = useDataShare();
  const { loading } = useLoading();
  const { showConfirmation } = useDialog();
  const user = useAppSelector(getUser);
  const [activeTab, setActiveTab] = useState(state?.tab || 0);
  const [customAvailabilityStatus, setCustomAvailabilityStatus] =
    useState<ICustomAvailabilityStatus>();
  const [storeAppointmentHours, setStoreAppointmentHours] = useState<
    IStoreAppointmentHours[]
  >([]);
  const cacheStoreAppointmentHours = useRef<IStoreAppointmentHours[]>([]);
  const [invalidTimeSlots, setInvalidTimeSlots] = useState<{
    [key: string]: number[];
  }>({});
  const [overlappedTimeSlots, setOverlappedTimeSlots] = useState<{
    [key: string]: number[];
  }>({});
  const [
    customAvailabilityHoursOverrides,
    setCustomAvailabilityHoursOverrides,
  ] = useState<IStoreAppointmentHoursOverrides[]>([]);

  const isTruthty = useCallback((value: BoolAsNumstring) => value === '1', []);

  const formatDefaultTime = useCallback(
    (time: string) => dayjs(`1970-01-01 ${time}`).format('LT'),
    [],
  );

  const handleTabChange = (_event: SyntheticEvent, newValue: number) => {
    if (hasChanges()) {
      showConfirmation({
        content: t('Discard your changes?'),
        onConfirm: () => {
          setStoreAppointmentHours(cacheStoreAppointmentHours.current);
          setActiveTab(newValue);
        },
      });

      return;
    }
    setActiveTab(newValue);
  };

  const handleCustomAvailabilityStatusChange = (value: boolean) => {
    const status: ICustomAvailabilityStatus = {
      ...customAvailabilityStatus,
      is_enabled: value ? '1' : '0',
    };
    setCustomAvailabilityStatus(status);
    const promise = dispatch(saveCustomAvailabilityStatus(status));
    promise.unwrap().then(() => {
      const promise = dispatch(
        fetchStoreAppointmentsHours({ storeId: user.store.id }),
      ).unwrap();
      promise.then((storeAppointmentHours: IStoreAppointmentHours[]) => {
        setStoreAppointmentHours(storeAppointmentHours);
      });
    });
  };

  const handleBtnSaveClick = () => {
    dispatch(
      updateStoreAppointmentsHours({
        statusId: customAvailabilityStatus.id,
        storeHoursData: storeAppointmentHours,
      }),
    )
      .unwrap()
      .then(() => {
        FlashMessage.Success(t('Your changes have been saved'));
        cacheStoreAppointmentHours.current = structuredClone(
          storeAppointmentHours,
        );
      })
      .catch(({ title }) => {
        FlashMessage.Error(title || t('There was an error, please try again.'));
      });
  };

  const handleDaySelectionChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    item: IStoreAppointmentHours,
  ) => {
    const newStoreAppointmentHours: IStoreAppointmentHours[] =
      storeAppointmentHours.map(availabilityHours => {
        if (
          availabilityHours.attributes.day_of_week ===
          item.attributes.day_of_week
        ) {
          const newAvailabilityHours = structuredClone(availabilityHours);
          newAvailabilityHours.attributes.is_available = event.target.checked
            ? '1'
            : '0';
          return newAvailabilityHours;
        }
        return availabilityHours;
      });
    setStoreAppointmentHours(newStoreAppointmentHours);
  };

  const hasChanges = useCallback(() => {
    return (
      JSON.stringify(cacheStoreAppointmentHours.current) !==
      JSON.stringify(storeAppointmentHours)
    );
  }, [storeAppointmentHours]);

  const isNoneSelected = useCallback(() => {
    return (
      storeAppointmentHours.find(
        item => item.attributes.is_available === '1',
      ) === undefined
    );
  }, [storeAppointmentHours]);

  const isTimeSlotInvalid = useCallback(
    (dayOfTheWeek: string, slotIndex: number) => {
      return (invalidTimeSlots[dayOfTheWeek] ?? []).includes(slotIndex);
    },
    [invalidTimeSlots],
  );

  const isTimeSlotHasOverlaps = useCallback(
    (dayOfTheWeek: string, slotIndex: number) => {
      return (overlappedTimeSlots[dayOfTheWeek] ?? []).includes(slotIndex);
    },
    [overlappedTimeSlots],
  );

  const hasOverlaps = useCallback(
    (day: string) => {
      return Object.prototype.hasOwnProperty.call(overlappedTimeSlots, day);
    },
    [overlappedTimeSlots],
  );

  const validateTimeslots = ({
    timeStart,
    timeEnd,
    day,
    slotIndex,
  }: {
    timeStart: Dayjs;
    timeEnd: Dayjs;
    day: string;
    slotIndex: number;
  }) => {
    const invalidSots = structuredClone(invalidTimeSlots);
    if (timeStart.isAfter(timeEnd) || timeStart.isSame(timeEnd)) {
      if (!invalidSots[day]) {
        invalidSots[day] = [];
      }
      if (!invalidSots[day].includes(slotIndex)) {
        invalidSots[day].push(slotIndex);
      }
    } else {
      if (invalidSots[day] && invalidSots[day].length) {
        const index = invalidSots[day].indexOf(slotIndex);
        if (index > -1) {
          invalidSots[day].splice(index, 1);
        }
      }
    }
    setInvalidTimeSlots(invalidSots);
  };

  const validateTimeslotsOverlap = useCallback(
    (timeslots: IStoreAppointmentTimeSlot[]) => {
      const overlaps = [];
      timeslots.forEach(({ end_time, start_time }, i) => {
        // i !== j => Make sure the compared slots are not the same one
        // slot.end_time > start_time => Find a slot where the end_time overlapses the start_time of another slot
        // slot.start_time < end_time => Find a slot where the start_time overlapses the end_time of another slot
        if (
          timeslots.find(
            (slot, j) =>
              i !== j &&
              slot.end_time > start_time &&
              slot.start_time < end_time,
          )
        ) {
          overlaps.push(i);
        }
      });
      return overlaps;
    },
    [],
  );

  const updateOverlaps = (day: string, overlaps: number[]) => {
    const newOvelapTimeslots = structuredClone(overlappedTimeSlots);
    if (overlaps.length) {
      newOvelapTimeslots[day] = overlaps;
    } else {
      if (newOvelapTimeslots[day]) {
        delete newOvelapTimeslots[day];
      }
    }
    setOverlappedTimeSlots(newOvelapTimeslots);
  };

  const onTimeSlotChange = (
    value: Dayjs,
    otherValue: Dayjs,
    day: string,
    slotIndex: number,
    slot: 'start_time' | 'end_time',
  ) => {
    let overlaps: number[];
    const newStoreAppointmentHours: IStoreAppointmentHours[] =
      storeAppointmentHours.map(storeAppointmentHoursItem => {
        if (storeAppointmentHoursItem.attributes.day_of_week === day) {
          const newStoreAppointmentHoursItem = structuredClone(
            storeAppointmentHoursItem,
          );
          newStoreAppointmentHoursItem.attributes.timeslots[slotIndex][slot] =
            value.format('HH:mm');
          overlaps = validateTimeslotsOverlap(
            newStoreAppointmentHoursItem.attributes.timeslots,
          );
          return newStoreAppointmentHoursItem;
        }
        return storeAppointmentHoursItem;
      });

    validateTimeslots({
      timeStart: slot === 'start_time' ? value : otherValue,
      timeEnd: slot === 'start_time' ? otherValue : value,
      day,
      slotIndex,
    });
    updateOverlaps(day, overlaps);
    setStoreAppointmentHours(newStoreAppointmentHours);
  };

  const onTimeSlotRemoveClick = (
    timeslotIndex: number,
    storeAppointmentHoursItem: IStoreAppointmentHours,
  ) => {
    const {
      attributes: { day_of_week },
    } = storeAppointmentHoursItem;
    let overlaps: number[];
    const newStoreAppointmentHours: IStoreAppointmentHours[] =
      storeAppointmentHours.map(storeAppointmentHours => {
        if (storeAppointmentHours.attributes.day_of_week === day_of_week) {
          const newStoreAppointmentHoursItem = structuredClone(
            storeAppointmentHours,
          );
          newStoreAppointmentHoursItem.attributes.timeslots.splice(
            timeslotIndex,
            1,
          );
          overlaps = validateTimeslotsOverlap(
            newStoreAppointmentHoursItem.attributes.timeslots,
          );

          return newStoreAppointmentHoursItem;
        }
        return storeAppointmentHours;
      });

    // remove index from validation error list if it's there
    const invalidSots = structuredClone(invalidTimeSlots);
    const invalidDayOfTheWeekIndexes = invalidSots[day_of_week] ?? [];
    if (invalidDayOfTheWeekIndexes.includes(timeslotIndex)) {
      const index = invalidSots[day_of_week].indexOf(timeslotIndex);
      if (index > -1) {
        invalidSots[day_of_week].splice(index, 1);
      }
      setInvalidTimeSlots(invalidSots);
    }
    updateOverlaps(day_of_week, overlaps);
    setStoreAppointmentHours(newStoreAppointmentHours);
  };

  const onTimeSlotAddClick = (
    storeAppointmentHoursItem: IStoreAppointmentHours,
  ) => {
    const {
      attributes: { day_of_week },
    } = storeAppointmentHoursItem;
    let overlaps: number[];
    const newStoreAppointmentHours: IStoreAppointmentHours[] =
      storeAppointmentHours.map(storeAppointmentHours => {
        if (storeAppointmentHours.attributes.day_of_week === day_of_week) {
          //The start_time of a new slot will be an hour after the end_time of the previous slot
          const newStoreAppointmentHoursItem = structuredClone(
            storeAppointmentHours,
          );
          const { end_time } = structuredClone(
            newStoreAppointmentHoursItem.attributes.timeslots,
          ).pop();
          const [endHours, endMinutes] = end_time.split(':');
          const time = dayjs()
            .hour(parseInt(endHours))
            .minute(parseInt(endMinutes));
          let startTime = time.add(1, 'hour').format('HH:mm:ss');
          let endTime = time.add(2, 'hour').format('HH:mm:ss');
          //if end_time of the previous slot is 22:00 or 22:30 the start and end times are: 23:00 and 23:30
          if (endHours === '22') {
            startTime = '23:00:00';
            endTime = '23:30:00';
          }
          newStoreAppointmentHoursItem.attributes.timeslots.push({
            start_time: startTime,
            end_time: endTime,
          });
          overlaps = validateTimeslotsOverlap(
            newStoreAppointmentHoursItem.attributes.timeslots,
          );

          return newStoreAppointmentHoursItem;
        }
        return storeAppointmentHours;
      });
    updateOverlaps(day_of_week, overlaps);
    setStoreAppointmentHours(newStoreAppointmentHours);
  };

  const onTimeSlotOverrideRemoveClick = (dateOverrideId: string) => {
    showConfirmation({
      content: t('Are you sure you want to delete this Date Override?'),
      onConfirm: () => {
        const promise = dispatch(
          deleteStoreAppointmentsHoursOverrides({ dateOverrideId }),
        ).unwrap();
        promise
          .then(() => {
            setCustomAvailabilityHoursOverrides(
              customAvailabilityHoursOverrides.filter(
                override => override.id !== dateOverrideId,
              ),
            );
          })
          .catch(({ title }) => {
            FlashMessage.Error(
              title || t('There was an error, please try again.'),
            );
          });
      },
    });
  };

  useEffect(() => {
    Promise.all([
      dispatch(
        fetchCustomAvailabilityStatus({ storeId: user.store.id }),
      ).unwrap(),
      dispatch(
        fetchStoreAppointmentsHours({ storeId: user.store.id }),
      ).unwrap(),
      dispatch(
        fetchStoreAppointmentsHoursOverrides({ storeId: user.store.id }),
      ).unwrap(),
    ]).then(resp => {
      cacheStoreAppointmentHours.current = structuredClone(resp[1]);
      setCustomAvailabilityStatus(resp[0]);
      setStoreAppointmentHours(resp[1]);
      setCustomAvailabilityHoursOverrides(resp[2]);
    });
    return () => {
      //promise.abort();
    };
  }, [dispatch, user]);

  return (
    <SFPageLayout
      data-testid='settings-my-store-appointment-settings'
      header={
        <>
          <SFPageHeader
            title={t('Store Appointment Hours')}
            startAdorment={
              <SFHeaderBackNavigationButton
                route='/settings'
                showPrompt={hasChanges()}
              />
            }
          ></SFPageHeader>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant='fullWidth'
            TabIndicatorProps={{
              style: { height: 1 },
            }}
            sx={{
              boxShadow: '0 0 4px 1px rgba(0, 0, 0, 0.1)',
            }}
          >
            <Tab id='tab-0' label={t('Store Schedule')} />
            <Tab
              id='tab-1'
              label={t('Date Override')}
              disabled={!isTruthty(customAvailabilityStatus?.is_enabled)}
            />
          </Tabs>
          {activeTab === 0 && (
            <SFLabeledSwitch
              label={t('Custom Availability')}
              value={isTruthty(customAvailabilityStatus?.is_enabled)}
              onChange={handleCustomAvailabilityStatusChange}
            />
          )}
        </>
      }
      footer={
        <>
          {isTruthty(customAvailabilityStatus?.is_enabled) &&
            activeTab === 0 && (
              <Button
                loading={loading}
                variant='contained'
                disabled={loading || isNoneSelected() || !hasChanges()}
                onClick={handleBtnSaveClick}
                sx={sxGlobal.safeInsetBottom}
              >
                {t('Save')}
              </Button>
            )}
        </>
      }
    >
      <Box>
        <TabPanel value={activeTab} index={0}>
          {!isTruthty(customAvailabilityStatus?.is_enabled) && (
            <Box sx={{ p: 3 }}>
              {storeAppointmentHours.map(
                ({
                  attributes: {
                    day_of_week,
                    is_available,
                    timeslots: [{ end_time, start_time }],
                  },
                }) => (
                  <Box mb={4}>
                    <Typography variant='regular' fontWeight='fontWeightMedium'>
                      {t(day_of_week)}
                    </Typography>
                    <Typography variant='regular'>
                      {!isTruthty(is_available) && t('Unavailable')}
                      {isTruthty(is_available) &&
                        t('_storeHoursOpen_ to _storeHoursClose_', {
                          storeHoursOpen: formatDefaultTime(start_time),
                          storeHoursClose: formatDefaultTime(end_time),
                        })}
                    </Typography>
                  </Box>
                ),
              )}
            </Box>
          )}
          {isTruthty(customAvailabilityStatus?.is_enabled) && (
            <Box sx={{ p: 3 }}>
              {storeAppointmentHours.map(storeAppointmentHoursItem => {
                const {
                  attributes: { is_available, day_of_week, timeslots },
                } = storeAppointmentHoursItem;
                const isAvailable = isTruthty(is_available);
                return (
                  <Box mb={isAvailable ? 2 : 0}>
                    <Box display='flex' alignItems='center'>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={isAvailable}
                            onChange={e =>
                              handleDaySelectionChange(
                                e,
                                storeAppointmentHoursItem,
                              )
                            }
                            size='small'
                          />
                        }
                        label={
                          <Typography
                            variant='regular'
                            fontWeight='fontWeightMedium'
                          >
                            {t(day_of_week)}
                          </Typography>
                        }
                      />
                      {!isAvailable && (
                        <Typography
                          variant='regular'
                          fontWeight='fontWeightNormal'
                          sx={theme => ({ color: theme.palette.grey[500] })}
                        >
                          {t('Unavailable')}
                        </Typography>
                      )}
                    </Box>
                    {isAvailable &&
                      (timeslots || [{}]).map(
                        (
                          { start_time = '00:00:00', end_time = '23:30:00' },
                          slotsIndex: number,
                        ) => {
                          const [startHours, startMinutes] =
                            start_time.split(':');
                          const [endHours, endMinutes] = end_time.split(':');
                          const dayjsStartHours = dayjs()
                            .set('hour', parseInt(startHours))
                            .set('minute', parseInt(startMinutes));
                          const dayjsEndHours = dayjs()
                            .set('hour', parseInt(endHours))
                            .set('minute', parseInt(endMinutes));
                          return (
                            <>
                              <Box display='flex' pl={3} pb={0.75}>
                                <SFTimePicker
                                  views={['hours', 'minutes']}
                                  minutesStep={30}
                                  sx={{ width: '80px', mr: 2 }}
                                  value={dayjsStartHours}
                                  onChange={value => {
                                    onTimeSlotChange(
                                      value,
                                      dayjsEndHours,
                                      day_of_week,
                                      slotsIndex,
                                      'start_time',
                                    );
                                  }}
                                  error={
                                    isTimeSlotInvalid(
                                      day_of_week,
                                      slotsIndex,
                                    ) ||
                                    isTimeSlotHasOverlaps(
                                      day_of_week,
                                      slotsIndex,
                                    )
                                  }
                                />
                                <SFTimePicker
                                  views={['hours', 'minutes']}
                                  minutesStep={30}
                                  sx={{ width: '80px', mr: 1 }}
                                  value={dayjsEndHours}
                                  onChange={value => {
                                    onTimeSlotChange(
                                      value,
                                      dayjsStartHours,
                                      day_of_week,
                                      slotsIndex,
                                      'end_time',
                                    );
                                  }}
                                  error={
                                    isTimeSlotInvalid(
                                      day_of_week,
                                      slotsIndex,
                                    ) ||
                                    isTimeSlotHasOverlaps(
                                      day_of_week,
                                      slotsIndex,
                                    )
                                  }
                                />
                                {slotsIndex > 0 && (
                                  <IconButton
                                    onClick={() => {
                                      onTimeSlotRemoveClick(
                                        slotsIndex,
                                        storeAppointmentHoursItem,
                                      );
                                    }}
                                  >
                                    <RemoveCircleOutlineIcon />
                                  </IconButton>
                                )}
                              </Box>
                              {isTimeSlotInvalid(day_of_week, slotsIndex) && (
                                <Typography pl={3} pb={1} color='error'>
                                  {t(
                                    'Choose an end time later than the start time.',
                                  )}
                                </Typography>
                              )}
                            </>
                          );
                        },
                      )}
                    {isAvailable && hasOverlaps(day_of_week) && (
                      <Typography pl={3} pb={1} color='error'>
                        {t(
                          'Time interval cannot overlap another time interval.',
                        )}
                      </Typography>
                    )}
                    {isAvailable && timeslots.length < 12 && (
                      <Button
                        variant='link'
                        disableRipple
                        sx={{ ml: 3 }}
                        onClick={() => {
                          onTimeSlotAddClick(storeAppointmentHoursItem);
                        }}
                      >
                        {t('Add a new interval')}
                      </Button>
                    )}
                  </Box>
                );
              })}
              {isNoneSelected() && (
                <Typography color='error'>
                  {t('Please add at least one available set of times')}
                </Typography>
              )}
            </Box>
          )}
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          <Box sx={{ p: 3 }}>
            {customAvailabilityHoursOverrides.length === 0 && (
              <Typography
                variant='regular'
                sx={theme => ({ color: theme.palette.grey[500], pb: 1 })}
              >
                {t('You currently do not have any date overrides')}
              </Typography>
            )}
            <Button
              variant='link'
              disableRipple
              onClick={() => {
                setSharedData({
                  override: null,
                  overrides: customAvailabilityHoursOverrides,
                  storeHours: storeAppointmentHours,
                });
                navigate('/settings/store-appointment-hours-override');
              }}
            >
              {t('Add a new interval')}
            </Button>
            {customAvailabilityHoursOverrides.map(
              ({ id, date, is_available, timeslots }, i: number) => {
                const d = dayjs(date, 'YYYY-MM-DD');
                return (
                  <Box
                    pt={2}
                    pb={2}
                    sx={theme => ({
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      borderTop:
                        i > 0 ? `1px solid ${theme.palette.grey[400]}` : 'none',
                    })}
                  >
                    <Box
                      sx={{ cursor: 'pointer' }}
                      onClick={() => {
                        setSharedData({
                          override: {
                            id,
                            attributes: { date, is_available, timeslots },
                          },
                          overrides: customAvailabilityHoursOverrides,
                          storeHours: storeAppointmentHours,
                        });
                        navigate('/settings/store-appointment-hours-override');
                      }}
                    >
                      <Typography
                        variant='regular'
                        fontWeight='fontWeightMedium'
                      >
                        {d.format('LL')} - {d.format('dddd')}
                      </Typography>
                      {!isTruthty(is_available) && (
                        <Typography
                          variant='regular'
                          fontWeight='fontWeightNormal'
                        >
                          {t('Unavaialble')}
                        </Typography>
                      )}
                      {isTruthty(is_available) &&
                        timeslots.map(({ start_time, end_time }) => (
                          <Typography
                            variant='regular'
                            fontWeight='fontWeightNormal'
                          >
                            {t('_storeHoursOpen_ to _storeHoursClose_', {
                              storeHoursOpen: formatDefaultTime(start_time),
                              storeHoursClose: formatDefaultTime(end_time),
                            })}
                          </Typography>
                        ))}
                    </Box>
                    <IconButton
                      onClick={() => {
                        onTimeSlotOverrideRemoveClick(id);
                      }}
                    >
                      <RemoveCircleOutlineIcon />
                    </IconButton>
                  </Box>
                );
              },
            )}
          </Box>
        </TabPanel>
      </Box>
    </SFPageLayout>
  );
}

export default SettingsStoreAppointmentHours;
