import { useEffect } from 'react';
import { Outlet } from 'react-router';
import { Box } from '@mui/material';
import DataShareProvider from '@/providers/DataShareProvider';
import { useAppSelector, useAppDispatch } from '@hooks/store';
import { useDevice } from '@/hooks/device';
import { getUser } from '@store/userSlice';
import { fetchRep, IRepQueryParameters } from '@api/repApi';

function Settings() {
  const dispatch = useAppDispatch();
  const user = useAppSelector(getUser);
  const device = useDevice();

  useEffect(() => {
    const queryParams: IRepQueryParameters = {
      fields: ['avatar', 'specialties'].join(','),
    };

    const promise = dispatch(
      fetchRep({ repId: user ? user.ID.toString() : null, queryParams }),
    );
    return () => {
      promise?.abort();
    };
  }, [dispatch, user]);
  return (
    <DataShareProvider>
      <Box>
        <Box
          sx={theme => ({
            marginLeft: 0,
            background: theme.palette.grey[200],
            [theme.breakpoints.up('sm')]: {
              marginLeft: () => (!device.isMobile() ? '280px' : '0'),
            },
            [`${theme.breakpoints.up(
              'md',
            )} and (orientation: landscape) and ((min-height: 703px))`]: {
              marginLeft: () => (!device.isMobile() ? '280px' : '60px'),
            },
          })}
        >
          <Outlet />
        </Box>
      </Box>
    </DataShareProvider>
  );
}

export default Settings;
