import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { RouterProvider, createMemoryRouter } from 'react-router';
import { store } from '@/store';
import AuthContext from '@/providers/AuthContext';
import AuthProvider from '@/providers/AuthProvider';
import ConfirmationDialogProvider from '@/providers/DialogProvider';
import LoadingProvider from '@/providers/LoadingProvider';
import SettingsRoutes from '..';
import { IUser } from '@/store/userSlice';
import DeviceProvider from '@/providers/DeviceProvider';

test('load default settings route when not authenticated', async () => {
  const router = createMemoryRouter([SettingsRoutes], {
    initialEntries: ['/settings'],
  });

  const { queryByTestId } = render(
    <AuthProvider>
      <Provider store={store}>
        <RouterProvider router={router} />
      </Provider>
    </AuthProvider>,
  );

  expect(queryByTestId('settings-list')).not.toBeInTheDocument();
});

test('load default settings route when authenticated', async () => {
  const router = createMemoryRouter([SettingsRoutes], {
    initialEntries: ['/settings'],
  });

  const user: Partial<IUser> = {
    ID: 115,
    username: 'reggie',
  };
  const login = jest.fn();
  const logOut = jest.fn();
  const featureEnabled = jest.fn();
  const hasGroupPermission = jest.fn();

  const { getByTestId } = render(
    <DeviceProvider>
      <AuthContext.Provider
        value={{ user, login, logOut, featureEnabled, hasGroupPermission }}
      >
        <ConfirmationDialogProvider>
          <Provider store={store}>
            <LoadingProvider>
              <RouterProvider router={router} />
            </LoadingProvider>
          </Provider>
        </ConfirmationDialogProvider>
      </AuthContext.Provider>
    </DeviceProvider>,
  );

  expect(getByTestId('settings-list')).toBeInTheDocument();
});
