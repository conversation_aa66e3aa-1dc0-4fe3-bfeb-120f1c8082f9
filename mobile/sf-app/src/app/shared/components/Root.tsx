import { Suspense, useEffect, useCallback, useMemo, useRef } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch } from '@hooks/store';
import I18N from '@services/i18n';
import { IUser, setUser, clearUser } from '@store/userSlice';
import { clearRep } from '@store/repSlice';
import {
  IConfig,
  IEnabledFeatures,
  IPages,
  IOverride,
  setConfig,
} from '@store/configSlice';
import http from '@services/http';

import FlashMessage from './FlashMessage';
import GlobalLoadingIndicator from './GlobalLoadingIndicator';

import Immutable from 'immutable';
import FirebaseCloudMessaging from '@/classes/firebaseCloudMessaging';
import { postMessage } from '@/utils/generic';
import { useAuth } from '@hooks/auth';
import { AppVariant } from '../types/Generic';
import { useDevice } from '@/hooks/device';

const retrieveConfigsFromAttributes = (
  configs: IEnabledFeatures,
  user: IUser,
): IEnabledFeatures => {
  const { group = '1', selling_mode = '1', creation_source = 'invite' } = user;
  const { pages: originalPages, overrides } = configs;
  const pages = ((
    initialPages: IPages,
    overrides: IOverride[],
    group: string,
    selling_mode: string,
    creation_source: string,
  ): IPages => {
    // need to use these in the body function over wise they are not evaluated in runtime
    if (!(group && selling_mode && creation_source)) {
      return;
    }
    const { override = {} } =
      Immutable.List(overrides)
        .filter(obj => eval(obj.rules) === true)
        .last() || {};
    return { ...initialPages, ...override };
  })(originalPages, overrides, group, selling_mode, creation_source);
  return { ...configs, ...{ pages } };
};

interface MessagePayloadMobileApp {
  source: string;
  payload: {
    appVariant: AppVariant;
    token: string | null;
    locale: string;
    user: IUser | null;
    config: IConfig | null;
    path?: string;
    action?: string;
    headers?: {
      sfAppId: string;
      sfAppVersion: string;
    };
    options?: unknown;
  };
}

function Root() {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const auth = useAuth();
  const device = useDevice();
  const configRef = useRef<IConfig>();
  const fcmInstanceRef = useRef<FirebaseCloudMessaging>();

  const close = useCallback(() => {
    dispatch(clearUser());
    dispatch(clearRep());
    postMessage({
      source: 'sf-app',
      payload: {
        action: 'close',
      },
    });
  }, [dispatch]);

  const registerDeviceWithTokenForPush = useCallback((token: string) => {
    postMessage({
      source: 'sf-app',
      payload: {
        action: 'execute',
        params: {
          name: 'registerDeviceWithTokenForPush',
          options: token,
        },
      },
    });
  }, []);

  const onPushNotification = useCallback(payload => {
    postMessage({
      source: 'sf-app',
      payload: {
        action: 'execute',
        params: {
          name: 'onPushNotification',
          options: payload,
        },
      },
    });
  }, []);

  const registerFirebaseCloudMessaging = useCallback(async () => {
    const { FirebaseWebAppConfiguration, FirebaseWebAppVapidKey } =
      configRef.current || {};
    fcmInstanceRef.current = new FirebaseCloudMessaging(
      FirebaseWebAppConfiguration,
      FirebaseWebAppVapidKey,
      payload => {
        onPushNotification(payload);
      },
    );
    const token = await fcmInstanceRef.current.register();
    if (token) {
      // TODO: We need mechanism retry attempts
      registerDeviceWithTokenForPush(token);
    }
  }, [onPushNotification, registerDeviceWithTokenForPush]);

  const unregisterFirebaseCloudMessaging = useCallback(async () => {
    fcmInstanceRef.current && (await fcmInstanceRef.current.unregister());
  }, []);

  const messageHandlers = useMemo(
    () => ({
      registerFirebaseCloudMessaging: registerFirebaseCloudMessaging,
      unregisterFirebaseCloudMessaging: unregisterFirebaseCloudMessaging,
    }),
    [registerFirebaseCloudMessaging, unregisterFirebaseCloudMessaging],
  );

  const messageListener = useCallback(
    (event: MessageEvent<MessagePayloadMobileApp>) => {
      if (event.data.source === 'sf-app') {
        const {
          appVariant,
          user,
          config,
          path,
          locale = 'en-US',
          token,
          action,
          headers,
          options,
        } = event.data.payload;

        if (path === '/') {
          close();
          return;
        }

        device.setDeviceType(appVariant);

        if (user && config) {
          const enabledFeaturesOverrides = JSON.parse(
            config.Specificities || '{}',
          ) as IEnabledFeatures;

          const { pages } = retrieveConfigsFromAttributes(
            enabledFeaturesOverrides,
            user,
          );

          const enabledFeatures = Immutable.Map(pages)
            .map(page => page !== 'false')
            .toObject();

          // configure http headers
          http.setBaseUrl(config.SfApiUrl);
          http.setHeader('Authorization', token);
          http.setHeader('Sf-app-id', headers.sfAppId); //'Salesfloor Mobile App'
          http.setHeader('sf-app-version', headers.sfAppVersion); //'dev'
          http.setHeader('sf-origin', 'mobile'); // 'mobile'

          // TODO: separate enabled feature and permissions from config
          // for now will pas the whole config to auth provider
          // pass data to auth provider
          auth.login({
            user,
            features: config,
          });

          dispatch(setUser(user));
          dispatch(setConfig({ ...config, ...{ enabledFeatures } }));
          configRef.current = config;
        }

        if (action && messageHandlers[action]) {
          messageHandlers[action]();
        }

        // set locale will trow the exception if the locale === currentLocale
        I18N.setLocale(locale).catch(() => {});

        if (path === '/assistant') {
          const message =
            options && options['message'] ? options['message'] : '';
          navigate(path, { state: { message } });
          return;
        }

        navigate(path);
      }
    },
    [dispatch, navigate, close, messageHandlers, auth, device],
  );

  useEffect(() => {
    const isAngularMobile = window?.top?.name === 'angular-mobile';
    if (isAngularMobile) {
      window.addEventListener('message', messageListener, false);
    }
    return () => {
      if (isAngularMobile) {
        window.removeEventListener('message', messageListener, false);
      }
    };
  }, [messageListener]);

  useEffect(() => {
    // notify parent frame about location change
    if (window?.top?.name === 'angular-mobile' && location.pathname === '/') {
      close();
    }
  }, [location.pathname, close]);

  return (
    <>
      <FlashMessage />
      <GlobalLoadingIndicator />
      <Suspense fallback={<>...</>}>
        <Outlet />
      </Suspense>
    </>
  );
}

export default Root;
