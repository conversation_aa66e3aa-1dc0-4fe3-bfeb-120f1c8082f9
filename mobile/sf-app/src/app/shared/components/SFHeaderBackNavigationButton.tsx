import { Button } from '@mui/material';
import { ArrowBackIos } from '@mui/icons-material';
import { NavigateOptions, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import useDialog from '@/hooks/dialog';

interface SFHeaderBackNavigationButtonProps {
  label?: string;
  route?: string;
  options?: Pick<NavigateOptions, 'state'>;
  showPrompt?: boolean;
  promptMessage?: string;
}

const SFHeaderBackNavigationButton = ({
  label = 'Back',
  route = '/',
  options = null,
  showPrompt = false,
  promptMessage,
  ...rest
}: SFHeaderBackNavigationButtonProps) => {
  const { t } = useTranslation(['rep-onboarding']);
  const navigate = useNavigate();
  const { showConfirmation } = useDialog();

  const navigateConfirmed = () => {
    navigate(route, options);
  };
  return (
    <>
      <Button
        component='label'
        role={undefined}
        variant='navigation'
        tabIndex={-1}
        startIcon={<ArrowBackIos fontSize='small' />}
        onClick={() => {
          if (showPrompt) {
            showConfirmation({
              content: promptMessage || t('Discard your changes?'),
              onConfirm: navigateConfirmed,
            });
          } else {
            navigateConfirmed();
          }
        }}
        {...rest}
      >
        {t(label)}
      </Button>
    </>
  );
};

export default SFHeaderBackNavigationButton;
