import { forwardRef, Ref, useState } from 'react';
import {
  styled,
  TextField,
  Box,
  InputAdornment,
  IconButton,
  TextFieldProps,
  mergeSlotProps,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { Control, Controller, FieldValues } from 'react-hook-form';

import SFInput from './SFInput';
import SFInputLabel from './SFInputLabel';

//TODO: create a separate type password component
const SFTextFieldStyled = styled(
  forwardRef(
    (
      {
        errorText = '',
        unhidePasswordButton = false,
        slotProps = {},
        type,
        error,
        ...props
      }: TextFieldProps & {
        unhidePasswordButton?: boolean;
        errorText?: string;
      },
      ref: Ref<HTMLDivElement>,
    ) => {
      const [showPassword, setShowPassword] = useState(false);
      const handleMouseDownPassword = () => setShowPassword(!showPassword);
      return (
        <TextField
          data-testid={props.name}
          ref={ref}
          type={showPassword ? 'text' : type}
          slots={{
            inputLabel: ({ children, ...rest }) => {
              return (
                <SFInputLabel
                  {...rest}
                  sx={{
                    width: '100%',
                  }}
                >
                  <Box component={'span'}>
                    {children}
                    {error && errorText && ` (${errorText})`}
                  </Box>
                  {slotProps &&
                    (slotProps.htmlInput as HTMLTextAreaElement)?.maxLength && (
                      <Box component={'span'}>{`${
                        props.value ? (props.value as string).length : '0'
                      } of ${
                        (slotProps?.htmlInput as HTMLTextAreaElement).maxLength
                      } chars`}</Box>
                    )}
                </SFInputLabel>
              );
            },
            input: SFInput,
          }}
          error={error}
          slotProps={{
            ...slotProps,
            inputLabel: mergeSlotProps(slotProps?.inputLabel, {
              focused: false,
            }),
            input: mergeSlotProps(slotProps?.input, {
              disableUnderline: true,
              ...(unhidePasswordButton && {
                endAdornment: (
                  <InputAdornment position='end'>
                    <IconButton
                      aria-label='toggle password visibility'
                      onMouseDown={handleMouseDownPassword}
                    >
                      {showPassword ? (
                        <Visibility color='primary' fontSize='small' />
                      ) : (
                        <VisibilityOff color='primary' fontSize='small' />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }),
            }),
          }}
          {...props}
        />
      );
    },
  ),
)(() => ({}));

const SFTextField = forwardRef(
  (
    {
      name,
      control,
      onChange,
      ...rest
    }: TextFieldProps & {
      control?: Control<FieldValues>;
      unhidePasswordButton?: boolean;
      errorText?: string;
    },
    ref: Ref<HTMLDivElement>,
  ) => {
    if (control) {
      return (
        <Controller
          name={name}
          control={control}
          render={({
            field: { onChange: fieldOnChange, ...restField },
            fieldState: { error, invalid },
          }) => (
            <SFTextFieldStyled
              {...restField}
              onChange={e => {
                if (onChange instanceof Function) {
                  onChange(e);
                }
                fieldOnChange(e);
              }}
              error={invalid}
              errorText={error?.message ?? null}
              {...rest}
            />
          )}
        />
      );
    }
    return (
      <SFTextFieldStyled ref={ref} name={name} onChange={onChange} {...rest} />
    );
  },
);

export default SFTextField;
