import { alpha, styled, Input } from '@mui/material';

const SFInput = styled(Input)(({ theme, inputProps }) => ({
  padding: 0,
  'label + &': {
    marginTop: '22px',
  },
  '& .MuiInputBase-input': {
    ...{
      position: 'relative',
      backgroundColor: theme.palette.common.white,
      border: `1px solid ${theme.palette.grey[500]}`,
      borderRadius: 2,
      fontSize: '0.75rem',
      color: theme.palette.text.primary,
      padding: '12px 10px',
      transition: theme.transitions.create(['border-color', 'box-shadow']),
      boxShadow: `${alpha(theme.palette.common.black, 0.1)} 0 1px 2px 0`,
      '&:focus': {
        borderColor: theme.palette.primary.dark,
      },
      ...inputProps,
    },
  },
  '& .MuiInputAdornment-root': {
    position: 'absolute',
    '&.MuiInputAdornment-positionEnd': {
      right: '5px',
    },
  },
  '&.Mui-error .MuiInputBase-input': {
    borderColor: theme.palette.error.main,
  },
}));

export default SFInput;
