import { useState, useEffect, useCallback } from 'react';
import { BroadcastChannel } from 'broadcast-channel';
import { Alert, AlertColor, Snackbar } from '@mui/material';
import { useDevice } from '@/hooks/device';

function FlashMessage() {
  const [display, setDisplay] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<AlertColor>();
  const [backdrop, setBackdrop] = useState(false);
  const [autohideDuration, setAutohideDuration] = useState(null);
  const [modal, setModal] = useState(false);

  const device = useDevice();

  const handleCloseFlashMessage = useCallback(() => {
    if (!modal) {
      setDisplay(false);
      setMessage('');
      setBackdrop(false);
    }
  }, [modal]);

  const handleBroadcastMessage = useCallback(
    (params: {
      message: string;
      type: string;
      delay: number;
      modal: boolean;
      backdrop: boolean;
    }) => {
      setMessage(params.message);
      setType(params.type as AlertColor);
      setBackdrop(params.backdrop);
      setModal(params.modal);
      if (params.delay) {
        setAutohideDuration(params.delay);
      }
      setDisplay(true);
    },
    [],
  );

  useEffect(() => {
    // TODO: convert to hook
    const channel = new BroadcastChannel('flash-message');
    channel.addEventListener('message', handleBroadcastMessage);
    return () => {
      channel.removeEventListener('message', handleBroadcastMessage);
      channel.close();
    };
  }, [handleBroadcastMessage]);

  return (
    <>
      {/* TODO: Use material ui Backdrop instead ? */}
      {display && backdrop && modal && <div className='flash-backdrop'></div>}
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        open={display}
        autoHideDuration={autohideDuration}
        onClose={handleCloseFlashMessage}
        sx={theme => ({
          left: 0,
          width: '100%',
          paddingLeft: theme.spacing(2),
          paddingRight: theme.spacing(2),
          [theme.breakpoints.up('md')]: {
            left: 'auto',
            right: 0,
            width: 'calc(100% - 280px)',
          },
          [`${theme.breakpoints.up(
            'md',
          )} and (orientation: landscape) and ((min-height: 703px))`]: {
            width: !device.isMobile()
              ? 'calc(100% - 280px)'
              : 'calc(100% - 60px)',
          },
        })}
      >
        <Alert
          icon={false}
          severity={type}
          variant='filled'
          sx={{ width: '100%' }}
        >
          {message}
        </Alert>
      </Snackbar>
    </>
  );
}

export default FlashMessage;
