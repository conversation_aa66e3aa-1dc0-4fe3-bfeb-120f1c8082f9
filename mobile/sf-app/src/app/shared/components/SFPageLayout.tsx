import { Box, Fade } from '@mui/material';

interface ISFPageLayoutProps {
  header?: string | JSX.Element;
  children?: string | JSX.Element | JSX.Element[];
  footer?: string | JSX.Element;
}

function SFPageLayout({
  header = null,
  children,
  footer = null,
  ...rest
}: ISFPageLayoutProps) {
  return (
    <Fade in={true} timeout={500}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh'
        }}
        {...rest}
      >
        {header && <Box>{header}</Box>}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flexGrow: 1,
            overflowY: 'auto',
          }}
        >
          {children}
        </Box>
        {footer && (
          <Box
            data-testid='footer'
            sx={theme => ({
              p: theme.spacing(1, 3, 1, 3),
              [theme.breakpoints.up('sm')]: {
                backgroundColor: theme.palette.common.white,
              },
            })}
          >
            {footer}
          </Box>
        )}
      </Box>
    </Fade>
  );
}

export default SFPageLayout;
