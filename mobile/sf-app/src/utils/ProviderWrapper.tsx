import { Provider } from 'react-redux';
import { BrowserRouter, MemoryRouter } from 'react-router-dom';
import { makeStore } from '../store';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactNode } from 'react';
import ConfirmationDialogProvider from '@/providers/DialogProvider';
import AuthProvider from '@/providers/AuthProvider';
import LoadingProvider from '@/providers/LoadingProvider';
import DeviceProvider from '@/providers/DeviceProvider';

export function renderWithProviders(ui: React.ReactNode) {
  const store = makeStore();
  return {
    store,
    userEvent: userEvent.setup(),
    screen,
    ...render(ui, {
      wrapper: ({ children }) => (
        <Provider store={store}>
          <DeviceProvider>
            <AuthProvider>
              <ConfirmationDialogProvider>
                <LoadingProvider>
                  <MemoryRouter>{children}</MemoryRouter>
                </LoadingProvider>
              </ConfirmationDialogProvider>
            </AuthProvider>
          </DeviceProvider>
        </Provider>
      ),
    }),
  };
}

export function renderWithRouter(ui: ReactNode, { route = '/' } = {}) {
  window.history.pushState({}, '', route);
  return {
    userEvent: userEvent.setup(),
    screen,
    ...render(ui, { wrapper: () => <BrowserRouter /> }),
  };
}
