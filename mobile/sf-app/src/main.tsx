import ReactDOM from 'react-dom/client';
import { RouterProvider, createHashRouter } from 'react-router';
import { Provider } from 'react-redux';

import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import theme from './theme';

import ConfirmationDialogProvider from './providers/DialogProvider';
import LoadingProvider from './providers/LoadingProvider';
import AuthProvider from './providers/AuthProvider';
import AppRoutes from './routes';
import { store } from './store';

import './services/i18n';
import DeviceProvider from './providers/DeviceProvider';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <ThemeProvider theme={theme}>
    <CssBaseline />
    <Provider store={store}>
      <DeviceProvider>
        <AuthProvider>
          <ConfirmationDialogProvider>
            <LoadingProvider>
              <RouterProvider router={createHashRouter(AppRoutes)} />
            </LoadingProvider>
          </ConfirmationDialogProvider>
        </AuthProvider>
      </DeviceProvider>
    </Provider>
  </ThemeProvider>,
);
