import type { Action, ThunkAction } from '@reduxjs/toolkit';
import { combineSlices, configureStore } from '@reduxjs/toolkit';
import globalSlice from './globalSlice';
import retailerSlice from './retailerSlice';
import userSlice from './userSlice';
import repSlice from './repSlice';
import configSlice from './configSlice';
import aiSlice from '../app/features/assistant/store/aiSlice';

// `combineSlices` automatically combines the reducers using
// their `reducerPath`s, therefore we no longer need to call `combineReducers`.
const rootReducer = combineSlices(
  globalSlice,
  configSlice,
  retailerSlice,
  userSlice,
  repSlice,
  aiSlice,
);

// Infer the `RootState` type from the root reducer
export type RootState = ReturnType<typeof rootReducer>;

// The store setup is wrapped in `makeStore` to allow reuse
// when setting up tests that need the same store config
export function makeStore(preloadedState?: Partial<RootState>) {
  // configure listeners using the provided defaults
  // optional, but required for `refetchOnFocus`/`refetchOnReconnect` behaviors
  // use with RTK queryies
  // setupListeners(store.dispatch)

  return configureStore({
    reducer: rootReducer,
    preloadedState,
  });
}

export const store = makeStore();

// Infer the type of `store`
export type AppStore = typeof store;

// Infer the `AppDispatch` type from the store itself
export type AppDispatch = AppStore['dispatch'];

export type AppThunk<ThunkReturnType = void> = ThunkAction<
  ThunkReturnType,
  RootState,
  unknown,
  Action
>;
