import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from './index';
import { BoolAsString, Locale } from '../app/shared/types/Generic';
import * as configApi from '../api/configApi';

export interface IConfig {
  AdvancedSearchTransactionMaxAmount: number;
  AlgoliaApiKey: string;
  AlgoliaAppId: string;
  AlgoliaIndexKey: string;
  ApiCacheExpiration: number;
  AppDownloadUrls: {
    ios: string;
    android: string;
  };
  AppVersionUrls: string | null;
  AssetTargetChannelLabels: { identifier: string; label: string }[];
  AvailableLabelOptions: {
    email: string[];
    phone: string[];
  };
  BaseUrl: string;
  BrandNeedsPadding: string;
  CANSApiVersion: string;
  CANSUrl: string;
  CameraMaxHeight: number;
  CameraMaxPhotos: number;
  CameraMaxWidth: number;
  CameraQuality: number;
  CanRepViewAllAppointments: boolean;
  ChatExtraQuickResponses: {
    full: {
      en_US: string;
      fr_CA: string;
    };
    short: {
      en_US: string;
      fr_CA: string;
    };
  }[];
  ChatQuickResponses: {
    full: {
      en_US: string;
      fr_CA: string;
    };
    short: {
      en_US: string;
      fr_CA: string;
    };
  }[];
  ChatTransferToCs: boolean;
  ChatTransferToCsTextRequired: boolean;
  Clienteling: boolean;
  ClientelingBlackout: boolean;
  ClientelingBlackoutPeriod: number;
  CloudinaryApiKey: string;
  CloudinaryCloudName: string;
  ContactsBarcodeScanner: boolean;
  ContextualWidgetEventsRecordingEnabled: boolean;
  CookieShowPrefix: boolean;
  CustomerTagsAreReadOnly: boolean;
  EmailMeLabel: string;
  FirebaseMaxReconnectionTime: number;
  FirebaseShowDisconnectMessage: boolean;
  FirebaseToken: string;
  FirebaseUrl: string;
  FirebaseWebAppVapidKey: string;
  FirebaseWebAppConfiguration: FirebaseConfig;
  GroupTasksIsEnabled: boolean;
  GroupTasksLabel: string;
  GroupedProductsIsEnabled: boolean;
  GroupedProductsKpiStyledLinkLabel: string;
  GroupedProductsMaxProducts: number;
  GroupedProductsMinProducts: number;
  GroupedProductsSendStyledLinkLabel: string;
  GroupedProductsURL: string;
  LoaderDelay: number;
  LoaderMessageInterval: number;
  MenuLogoPath: string;
  MobileAppUpgradeNotificationEnabled: boolean;
  MobileAppUpgradeNotificationMessage: {
    en_US: string;
    fr_CA: string;
    ja_JP: string;
  };
  MobileAppUpgradeNotificationTimeout: number;
  NameTemplate: {
    MobileDashboard: string;
    MobileContact: string;
    ChatRepName: string;
    GroupTaskRepName: string;
    ChatCustomerName: string;
  };
  Oauth2AccessTokenUrl: string;
  Oauth2AuthorizationUrl: string;
  OnboardingLogoPath: string;
  OutOfOfficeMessage: string;
  PasswordPolicyMinimumStrength: number;
  PasswordPolicyType: string;
  PermissionsMap: {
    'create-user': string;
  };
  PiiObfuscationIsEnabled: boolean;
  ProductApiUrl: string;
  ProductBarcodeScanner: boolean;
  ProductsExpandedVariantsEnabled: boolean;
  ProductsHasModal: boolean;
  RetailerAlternateBrand: string | null;
  RetailerBeginWeek: number;
  RetailerBrandName: string;
  RetailerCanAddContacts: boolean;
  RetailerCanAddCustomerToContacts: boolean;
  RetailerCanBrowseLibrary: boolean;
  RetailerCanChangeCommunicationConsent: boolean;
  RetailerCanChangeRetailerId: boolean;
  RetailerCanEditEvents: boolean;
  RetailerCanImportContacts: boolean;
  RetailerCanSendEmailToMultipleRecipients: boolean;
  RetailerCanSendSmsToMultipleRecipients: boolean;
  RetailerCanShareAnUpdate: boolean;
  RetailerCanShareFromBrowseLibrary: boolean;
  RetailerChatDelay: {
    queue: number;
    broadcast: number;
    dynamic: number;
  };
  RetailerChatMode: [];
  RetailerChatRoutingMode: 'dynamic' | 'broadcast' | 'queue';
  RetailerContactsConsentRequired: boolean;
  RetailerCorpEmailRequired: boolean;
  RetailerCountriesApplicationAvailable: string[];
  RetailerCountriesApplicationPreferred: string[];
  RetailerCurrentBrand: string | null;
  RetailerDefaultCountry: string;
  RetailerEmailTemplatesLayouts: string;
  RetailerGoogleAnalyticsUID: string;
  RetailerGoogleAnalyticsWebBOUID: string;
  RetailerHasAppointmentRequests: boolean;
  RetailerHasAppointments: boolean;
  RetailerHasAssetManagement: boolean;
  RetailerHasAssociateRelationships: boolean;
  RetailerHasAutomatedTasks: boolean;
  RetailerHasChat: boolean;
  RetailerHasChatAppointment: boolean;
  RetailerHasCorporateTasks: boolean;
  RetailerHasCustomerActivityFeed: boolean;
  RetailerHasCustomerTags: boolean;
  RetailerHasDeals: boolean;
  RetailerHasExtendedAttributes: boolean;
  RetailerHasFeedValidation: boolean;
  RetailerHasInventoryLookup: boolean;
  RetailerHasLimitedVisibility: boolean;
  RetailerHasLookbooks: boolean;
  RetailerHasMultipleEmailTemplates: boolean;
  RetailerHasNewArrivals: boolean;
  RetailerHasPersonalShopper: boolean;
  RetailerHasPhoneCallEnabled: boolean;
  RetailerHasProductsFeed: boolean;
  RetailerHasServicesAppointmentSaveToDevice: boolean;
  RetailerHasSpecialties: boolean;
  RetailerHasStorefront: boolean;
  RetailerHasTasks: boolean;
  RetailerHasTextMessageChannel: boolean;
  RetailerHomepageUrl: {
    en_US: string;
    fr_CA: string;
  };
  RetailerId: string;
  RetailerLogoPath: string;
  RetailerLongName: string;
  RetailerMFAEnabled: boolean;
  RetailerMaximumSmsRecipients: number;
  RetailerPasswordUpdateEnabled: boolean;
  RetailerPrefixNumericUsernames: boolean;
  RetailerPrettyName: string;
  RetailerPricePrecision: number;
  RetailerPricePrecisionHideEmptyDecimals: boolean;
  RetailerServicesAppointmentManagementAllCustomerIsEnabled: boolean;
  RetailerServicesAppointmentManagementIsEnabled: boolean;
  RetailerServicesAppointmentReassignmentIsEnabled: boolean;
  RetailerServicesAppointmentTypes: {
    is_default: boolean;
    is_enabled: boolean;
    position: number;
    type: 'virtual' | 'store' | 'phone';
    use_alternate_label: boolean;
  }[];
  RetailerShortName: string;
  RetailerShowProductsBrandName: boolean;
  RetailerShowProductColorAsUpc: boolean;
  RetailerStartDate: string;
  RetailerStorepageMode: 'rep' | 'store';
  S3AccessKeyId: string;
  S3Region: string;
  S3SecretAccessKey: string;
  SMSQuickChatResponses: []; // TODO: define
  ServiceAppointmentLabel: string;
  ServiceAppointmentRequestLabel: string;
  ServiceChatLabel: string;
  ServiceChatRequestLabel: string;
  ServiceEmailMeLabel: string;
  ServiceEmailMeReportsLabel: string;
  ServiceEmailMeReportsRequestLabel: string;
  ServiceEmailMeRequestChatHandoffLabel: string;
  ServiceEmailMeRequestLabel: string;
  ServicePersonalShopperLabel: string;
  ServicePersonalShopperRequestLabel: string;
  SfApiUrl: string;
  SfMposProxyUrl: string;
  SfWpApiUrl: string;
  ShareConnectedServicesEnabled: boolean;
  ShareEmailEnabled: boolean;
  ShareFacebookEnabled: boolean;
  ShareInstagramEnabled: boolean;
  SharePinterestEnabled: boolean;
  Specificities: string;
  Stack: string;
  StoreAppointmentHours: []; //TODO: define
  StoreAppointmentHoursFirstDayOfWeek: number;
  StoreAppointmentHoursGroupPermissions: number[];
  StoreAppointmentHoursIsEnabled: boolean;
  StoreHours: []; //TODO: define
  StoreTextHours: []; //TODO: define
  StorefrontMaxProductCounts: {
    deals: number;
    myLooks: number;
    topPicks: number;
    newArrivals: number;
    recommendations: number;
  };
  StorefrontMinProductCounts: {
    recommendations: number;
  };
  StorefrontTrendingRecommendationsMode: boolean;
  SubscriptionEmailCheckboxLabel: string;
  SubscriptionSMSCheckboxLabel: string;
  TaskAutoDismissEnabled: boolean;
  TaskAutoDismissSettings: []; //TODO: define
  TaskQueryCutOffDaysBack: number;
  TextMessaging: boolean;
  UsePriorityVariants: boolean;
  algoliaSearchableAttributes: string[];
  defaultLocale: string;
  globalLocales: Locale[];
  i18nIsEnabled: boolean;
  isMfaEnabled: boolean;
  isMobileCheckoutEnabled: boolean;
  isOauth2Enabled: boolean;
  isOutfitsEnabled: boolean;
  isShopFeedEnabled: boolean;
  isUserStoresEnabled: boolean;
  isVideoChatEnabled: boolean;
  isVirtualVideoChatEnabled: boolean;
  outfitsSectionLabel: string;
  outfitsTransactionsPerPage: number;
  retailerChatFindNearbyStores: boolean;
  AiOutreachRecommendedPrompts: string;
}

export interface IPages {
  'choose-specialties': BoolAsString;
  congrats: BoolAsString;
  'connect-social': {
    twitter: BoolAsString;
  };
  'create-user': {
    confirm: BoolAsString;
    password: BoolAsString;
    username: string;
  };
  'enter-email': {
    alias: {
      disabled: BoolAsString;
      value: string;
    };
    email: string;
    phone: string;
    storefront: string;
  };
  'enter-token': {
    token: BoolAsString;
  };
  'import-contact': BoolAsString;
  'out-of-store': BoolAsString;
  'personal-info': {
    firstname: string;
    lastname: string;
  };
  'pick-store': {
    store: string;
    introduction: string;
  };
  'take-picture': BoolAsString;
}

// TODO: verify if we defined them all correctly
export interface IOverride {
  override: {
    'choose-specialties': BoolAsString;
    'connect-social': BoolAsString | { twitter: BoolAsString };
    details: BoolAsString;
    'enter-email': BoolAsString | { alias: BoolAsString };
    'import-contact': BoolAsString;
    'out-of-store': BoolAsString;
    'pick-store': BoolAsString;
    'take-picture': BoolAsString;
  };
  rules: string;
}

export interface IEnabledFeatures {
  overrides: IOverride[];
  pages: IPages;
}

export interface FirebaseConfig {
  apiKey: string,
  authDomain: string,
  databaseURL?: string,
  projectId: string,
  storageBucket: string,
  messagingSenderId: string,
  appId: string,
  measurementId: string
}

export interface IAiOutreachRecommendedPrompts {
  [localeCode: string]: {
    id: string;
    title: string;
    description: string;
    prompt: string;
    category: string;
    active: boolean;
  }[];
}

interface IConfigSlice extends IConfig {
  test: unknown;
  enabledFeatures: {
    pages?: undefined;
    'choose-specialties'?: boolean;
    details?: boolean;
    congrats?: boolean;
    'connect-social'?: boolean;
    'create-user'?: boolean;
    'enter-email'?: boolean;
    'enter-token'?: boolean;
    'import-contact'?: boolean;
    'out-of-store'?: boolean;
    'personal-info'?: boolean;
    'pick-store'?: boolean;
    'take-picture'?: boolean;
  };
}

// Define the initial state using that type
const initialState = {} as IConfigSlice;

const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {
    setConfig: (state, action: PayloadAction<Partial<IConfigSlice>>) => ({
      ...state,
      ...action.payload,
    }),
  },
  extraReducers: builder => {
    builder
      .addCase(configApi.fetchRetailerConfig.fulfilled, (state, action) => {
        const test = action.payload;
        return { ...state, test };
      })
      .addCase(configApi.fetchRetailerConfig.rejected, (_state, action) => {
        // handle store update on error if needed
        console.log('THUNK ERROR: ', action);
      });
  },
});

export const { setConfig } = configSlice.actions;

// Other code such as selectors can use the imported `RootState` type
export function getConfig(state: RootState) {
  return state.config;
}

export default configSlice;
