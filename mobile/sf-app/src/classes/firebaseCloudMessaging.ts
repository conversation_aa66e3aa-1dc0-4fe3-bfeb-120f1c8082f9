import { initializeApp, FirebaseApp } from 'firebase/app';
import { getMessaging, onMessage, getToken, deleteToken, Messaging, MessagePayload } from 'firebase/messaging';
import { FirebaseConfig } from '@store/configSlice';

interface OnMessageCallback {
  (payload: MessagePayload) : void;
}
class FirebaseCloudMessaging {
  private messaging: Messaging;
  private firebaseConfig: FirebaseConfig;
  private vapidKey: string;
  private onMessageCallback: OnMessageCallback;
  private serviceWorkerRegistration: ServiceWorkerRegistration;
  private channel: BroadcastChannel;

  public constructor(firebaseConfiguration: FirebaseConfig, vapidKey: string, onMessageCallback: OnMessageCallback) {
    this.firebaseConfig = firebaseConfiguration;
    this.vapidKey = vapidKey;
    this.onMessageCallback = onMessageCallback;

    const app: FirebaseApp = initializeApp(this.firebaseConfig);
    this.messaging = getMessaging(app);
  }

  private delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async registerServiceWorker(scriptURL: string): Promise<ServiceWorkerRegistration> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register(`${scriptURL}?config=${JSON.stringify(this.firebaseConfig)}`);
        await navigator.serviceWorker.ready;
        this.channel = new BroadcastChannel('firebase-messaging-sw-channel')
        return registration;
      } catch (error) {
        throw new Error(`[firebaseCloudMessaging.ts] Service worker ${scriptURL} registration failed ${error}`);
      }
    } else {
      throw new Error(`[firebaseCloudMessaging.ts] 'Service Worker is not supported`);
    }
  }

  private async getFcmToken(serviceWorkerRegistration: ServiceWorkerRegistration): Promise<string | null> {
      const retries: number = 3;
      const delayMs: number = 1000;
      let attempt: number = 0;

      while (attempt < retries) {
        try {
          const token = await getToken(this.messaging, { vapidKey: this.vapidKey, serviceWorkerRegistration })
          if (token) {
            return token;
          } else {
            console.log('[firebaseCloudMessaging.ts] No FCM token found.')
            return null;
          }
        } catch (error) {
          attempt++;
          console.log(`[firebaseCloudMessaging.ts] Error getting FCM token`, error);
          if (attempt < retries) {
            console.log(`[firebaseCloudMessaging.ts] Retrying getting FCM token... Attempt ${attempt + 1}`);
            await this.delay(delayMs);
          } else {
            throw new Error('[firebaseCloudMessaging.ts] All retry getting FCM token attempts failed.');
          }
        }
      }
    return null;
  }

  private async deleteFcmToken(): Promise<void> {
    try {
      await deleteToken(this.messaging);
    } catch (error) {
      throw new Error(`[firebaseCloudMessaging.ts] Error deleting FCM token: ${error}`);
    }
  }

  private serviceWorkerBroadcastListener = (event: MessageEvent) => this.onMessageCallback(event.data);
  
  private registerIncomingMessageListeners(): void {
    onMessage(this.messaging, ({ data, ...rest }) => {
      const pushNotificationData = { ...rest, additionalData: { ...data, foreground: true, playSound: true, showPrompt: true } };
      this.onMessageCallback(pushNotificationData);
    });

    this.channel.onmessage = this.serviceWorkerBroadcastListener;
  }
    
  private unregisterIncomingMessageListeners(): void {
    this.channel.close();
  }

  public async register(): Promise<string | null>  {
    try {
      this.serviceWorkerRegistration = await this.registerServiceWorker('../firebase-messaging-sw.js');
      const token = await this.getFcmToken(this.serviceWorkerRegistration);
      this.registerIncomingMessageListeners();
      return token;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  public async unregister(): Promise<void> {
    try {
      await this.deleteFcmToken();
      this.serviceWorkerRegistration.unregister();
      this.unregisterIncomingMessageListeners();
    } catch (error) {
      console.log(error);
    }
  }
}

export default FirebaseCloudMessaging;