class Sound {
  private static _instance: Sound;
  public PUSH_SOUND_FILE_NAME = 'popcorn';
  private RING_SOUND_FILE_NAME = 'www/popcorn.wav';
  private RING_SOUND_WEB_FILE_NAME = 'https://cdn.salesfloor.net/salesfloor-assets/salesfloor/popcorn.mp3';
  private media: Media | null = null;
  private audio: HTMLAudioElement | null = null;
  private mediaState: number;

  private constructor() {}

  public static Instance() {
    return this._instance || (this._instance = new this());
  }

  /**
   * Returns the path to the phonegap resources.
   */
  private getPhoneGapPath = () => {
    let path: string = '';
    if (cordova?.file) {
      path = cordova.file.applicationDirectory.replace(/file:\/\//, '');
    } else {
      path = window.location.pathname.substr(0, path.length - 'www/index.html'.length);
    }
    return decodeURIComponent(path);
  };

  public play = (src: string) => {
    this.stop(); // stop previous played media file
    if (window.Media) {
      const mediaSource = this.getPhoneGapPath() + (src || this.RING_SOUND_FILE_NAME);
      this.media = new Media(
        mediaSource,
        () => {},
        err => console.error('[sound.ts] Media error:', err),
        state => (this.mediaState = state),
      );
      this.media.play();
      // set max volume (in IOS if volume is not set it doesn'tring)
      this.media.setVolume(1.0);
    } else {
      // Web environment: use HTMLAudioElement      
      if (!this.audio) {
        this.audio = new Audio();
      }

      if (this.audio.src !== src) {
        this.audio.src = src || this.RING_SOUND_WEB_FILE_NAME;
        this.audio.load();
      }

      this.audio.play().catch((error) => {
        console.error('[sound.ts] Error playing audio:', error);
      });
    }
  };

  public stop = () => {
    if (this.isPlaying()) {
      if (this.media !== null) {
        // Cordova environment: use Media object
        this.media.stop();
        this.media.release();
        this.media = null;
      } else {
        // Web environment: use HTMLAudioElement
        this.audio.pause();
        this.audio.currentTime = 0;
      }
    }
  };

  public isPlaying = () => 
    this.media && Media.MEDIA_RUNNING === this.mediaState || this.audio && !this.audio.paused;
}

export default Sound;
