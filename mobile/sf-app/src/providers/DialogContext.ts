import { createContext, ReactNode } from 'react';

export interface IConfirmationDialogParams {
  title?: ReactNode;
  content: ReactNode;
  onConfirm: () => void;
}

export interface INotificationDialogParams {
  title?: ReactNode;
  content: ReactNode;
  container?: Element | (() => Element | null);
  onClose?: () => void;
}

export interface IDialogContext {
  showConfirmation: (params: IConfirmationDialogParams) => void;
  showNotification: (params: INotificationDialogParams) => void;
}

const DialogContext = createContext<IDialogContext>(null);

export default DialogContext;
