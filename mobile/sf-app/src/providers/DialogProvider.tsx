import { ReactNode, useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import DialogContext, {
  IConfirmationDialogParams,
  INotificationDialogParams,
} from './DialogContext';

enum DIALOG_TYPE {
  CONFIRMATION,
  NOTIFICATION,
}

const ConfiramtionDialogProvider = ({ children }) => {
  const { t } = useTranslation();
  const [dialogType, setDialogType] = useState<DIALOG_TYPE>(null);
  const [open, setOpen] = useState(false);
  const [container, setContainer] = useState(null);
  const [title, setTitle] = useState<ReactNode>(null);
  const [content, setContent] = useState<ReactNode>(null);
  const [onConfirmCallback, setOnConfirmCallback] = useState(null);

  const showConfirmation = ({
    title = null,
    content,
    onConfirm,
  }: IConfirmationDialogParams) => {
    setDialogType(DIALOG_TYPE.CONFIRMATION);
    setTitle(title);
    setContent(content);
    setOnConfirmCallback(() => onConfirm);
    window.parent.postMessage({
      source: 'sf-app',
      payload: {
        action: 'execute',
        params: {
          name: 'showBackdrop',
        },
      },
    });
    setOpen(true);
  };

  const showNotification = ({
    container = null,
    title = null,
    content,
    onClose = () => {},
  }: INotificationDialogParams) => {
    setDialogType(DIALOG_TYPE.NOTIFICATION);
    setContainer(container);
    setTitle(title);
    setContent(content);
    setOnConfirmCallback(() => onClose);
    setOpen(true);
  };

  const closeDialog = () => {
    setTitle(null);
    setContainer(null);
    setContent(null);
    setOnConfirmCallback(null);
    setOpen(false);
  };

  const onConfirm = () => {
    if (onConfirmCallback instanceof Function) {
      onConfirmCallback();
    }
    closeDialog();
  };

  return (
    <DialogContext.Provider value={{ showConfirmation, showNotification }}>
      <Dialog
        disablePortal={true}
        sx={{ '& .MuiDialog-paper': { width: '80%', maxHeight: 435 } }}
        maxWidth='xs'
        closeAfterTransition={false}
        open={open}
        slots={{
          container,
        }}
      >
        {title && <DialogTitle>{title}</DialogTitle>}
        <DialogContent>
          <Typography variant='body2' sx={{ textAlign: 'center' }}>
            {content}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          {dialogType === DIALOG_TYPE.CONFIRMATION && (
            <>
              <Button onClick={closeDialog} variant='outlined'>
                {t('Cancel')}
              </Button>
              <Button onClick={onConfirm} variant='contained'>
                {t('Confirm')}
              </Button>
            </>
          )}
          {dialogType === DIALOG_TYPE.NOTIFICATION && (
            <Button onClick={closeDialog} variant='contained'>
              {t('OK')}
            </Button>
          )}
        </DialogActions>
      </Dialog>
      {children}
    </DialogContext.Provider>
  );
};

export default ConfiramtionDialogProvider;
