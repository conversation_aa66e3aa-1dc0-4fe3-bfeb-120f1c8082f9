/* global firebase, clients, importScripts: true */
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  event.waitUntil(
    clients.matchAll({includeUncontrolled: true, type: 'window'})
      .then((clientsList) => {
        const swOrigin = self.location.origin;
        const localhost = 'http://localhost:9000';
        const isLocalhost = swOrigin.startsWith(localhost);
        // TODO: if multiple tab opened, it may focus to the wrong tab.
        // Currently it doesnt support multiple tabs.
        const client = clientsList.find(({ url }) => url.startsWith(localhost) || url.startsWith(`${swOrigin}/app`));
        
        if (client) {
          client.focus();
        } else {
          clients.openWindow(isLocalhost ? swOrigin : `${swOrigin}/app`);
        }
      })
      .catch((error) => {
        console.log('firebase-messaging-sw.js: Error fetching client list:', error);
      })
  );
});

// Import necessary Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/11.3.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.3.1/firebase-messaging-compat.js');

const config = new URL(location.href).searchParams.get('config');
// Initialize Firebase
firebase.initializeApp(JSON.parse(config));

// Get Firebase Messaging instance
const messaging = firebase.messaging();

self.addEventListener('install', (event) => {
  event.waitUntil(self.skipWaiting());
});

self.addEventListener('activate', (event) => {
  event.waitUntil(self.clients.claim());
});

const channel = new BroadcastChannel('firebase-messaging-sw-channel');

// Handle background messages
messaging.onBackgroundMessage(({ data, ...rest }) => {
  data.foreground = false;
  const notificationTitle = data.title;
  const notificationOptions = {
    body: data.message,
    data: { ...rest, additionalData: data }
  };
 
  channel.postMessage({ ...rest, additionalData: { ...data, playSound: true, showPrompt: true } });
  self.registration.showNotification(notificationTitle, notificationOptions);
});